{"_id": "@isaacs/balanced-match", "_rev": "5206512-684bde4167627409cfc2d8fb", "dist-tags": {"latest": "4.0.1"}, "name": "@isaacs/balanced-match", "time": {"created": "2025-06-13T08:16:01.222Z", "modified": "2025-06-13T08:16:02.244Z", "4.0.1": "2025-06-12T20:05:52.863Z", "4.0.0": "2025-06-12T17:59:55.554Z"}, "versions": {"4.0.1": {"name": "@isaacs/balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/balanced-match.git"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "type": "module", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.2", "@types/node": "^24.0.0", "mkdirp": "^3.0.1", "prettier": "^3.3.2", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.5"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "license": "MIT", "engines": {"node": "20 || >=22"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "_id": "@isaacs/balanced-match@4.0.1", "gitHead": "b99b03c567c1e8b7e0b0e27d22e0a1f128c978dd", "bugs": {"url": "https://github.com/isaacs/balanced-match/issues"}, "homepage": "https://github.com/isaacs/balanced-match#readme", "_nodeVersion": "22.14.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ==", "shasum": "3081dadbc3460661b751e7591d7faea5df39dd29", "tarball": "https://registry.npmmirror.com/@isaacs/balanced-match/-/balanced-match-4.0.1.tgz", "fileCount": 13, "unpackedSize": 18204, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC3e2Fcqz1RxwCaM5Q7y8ar47C4JTkTLUJL9/bAt44bjwIhAJHqs0/6nLWovtTzgvEjHHG4Ft82B2mJHyE1+JW1UTxz"}], "size": 4438}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/balanced-match_4.0.1_1749758752669_0.7048331630381846"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-12T20:05:52.863Z", "publish_time": 1749758752863, "_source_registry_name": "default"}, "4.0.0": {"name": "@isaacs/balanced-match", "version": "4.0.0", "keywords": ["match", "regexp", "test", "balanced", "parse"], "license": "MIT", "_id": "@isaacs/balanced-match@4.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/balanced-match", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "dist": {"shasum": "219eaa47d91b818b5801100cf9a37f0ce2200c3c", "tarball": "https://registry.npmmirror.com/@isaacs/balanced-match/-/balanced-match-4.0.0.tgz", "fileCount": 12, "integrity": "sha512-DsUNZaSbXah0FwksEbeS/0I1bdLLcWnI4RUB/YctEcnzO96fcgAmHhvFmu6UtlKfTIYHO2ogM4JzWP73aQbcdg==", "signatures": [{"sig": "MEQCIGFbH8TfRtDW1p4AjFdAOjQJkigokeIbUu62QsZhfW8jAiAIHUNDbAYERRGfsXtoEuN2sP1mSKt0i1nb4/9EZBFiCA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 19316, "size": 6760}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "gitHead": "666d35f9e3015a1dac69d7d7c5de330cbd5fc39a", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/juliangruber/balanced-match.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "Match balanced character pairs, like \"{\" and \"}\"", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^21.1.0", "tshy": "^3.0.2", "mkdirp": "^3.0.1", "typedoc": "^0.28.5", "prettier": "^3.3.2", "@types/node": "^24.0.0", "@types/brace-expansion": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/balanced-match_4.0.0_1749751195355_0.9008624103978009", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-12T17:59:55.554Z", "publish_time": 1749751195554, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/balanced-match/issues"}, "description": "Match balanced character pairs, like \"{\" and \"}\"", "homepage": "https://github.com/isaacs/balanced-match#readme", "keywords": ["match", "regexp", "test", "balanced", "parse"], "license": "MIT", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# @isaacs/balanced-match\n\nA hybrid CJS/ESM TypeScript fork of\n[balanced-match](http://npm.im/balanced-match).\n\nMatch balanced string pairs, like `{` and `}` or `<b>` and `</b>`. Supports regular expressions as well!\n\n[![CI](https://github.com/juliangruber/balanced-match/actions/workflows/ci.yml/badge.svg)](https://github.com/juliangruber/balanced-match/actions/workflows/ci.yml)\n[![downloads](https://img.shields.io/npm/dm/balanced-match.svg)](https://www.npmjs.org/package/balanced-match)\n\n## Example\n\nGet the first matching pair of braces:\n\n```js\nimport { balanced } from '@isaacs/balanced-match'\n\nconsole.log(balanced('{', '}', 'pre{in{nested}}post'))\nconsole.log(balanced('{', '}', 'pre{first}between{second}post'))\nconsole.log(balanced(/\\s+\\{\\s+/, /\\s+\\}\\s+/, 'pre  {   in{nest}   }  post'))\n```\n\nThe matches are:\n\n```bash\n$ node example.js\n{ start: 3, end: 14, pre: 'pre', body: 'in{nested}', post: 'post' }\n{ start: 3,\n  end: 9,\n  pre: 'pre',\n  body: 'first',\n  post: 'between{second}post' }\n{ start: 3, end: 17, pre: 'pre', body: 'in{nest}', post: 'post' }\n```\n\n## API\n\n### const m = balanced(a, b, str)\n\nFor the first non-nested matching pair of `a` and `b` in `str`, return an\nobject with those keys:\n\n- **start** the index of the first match of `a`\n- **end** the index of the matching `b`\n- **pre** the preamble, `a` and `b` not included\n- **body** the match, `a` and `b` not included\n- **post** the postscript, `a` and `b` not included\n\nIf there's no match, `undefined` will be returned.\n\nIf the `str` contains more `a` than `b` / there are unmatched pairs, the first match that was closed will be used. For example, `{{a}` will match `['{', 'a', '']` and `{a}}` will match `['', 'a', '}']`.\n\n### const r = balanced.range(a, b, str)\n\nFor the first non-nested matching pair of `a` and `b` in `str`, return an\narray with indexes: `[ <a index>, <b index> ]`.\n\nIf there's no match, `undefined` will be returned.\n\nIf the `str` contains more `a` than `b` / there are unmatched pairs, the first match that was closed will be used. For example, `{{a}` will match `[ 1, 3 ]` and `{a}}` will match `[0, 2]`.\n", "repository": {"type": "git", "url": "git://github.com/isaacs/balanced-match.git"}, "_source_registry_name": "default"}