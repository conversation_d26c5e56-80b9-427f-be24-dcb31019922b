{"_attachments": {}, "_id": "chalk", "_rev": "176-61f1441e23990e8a812e2238", "description": "Terminal string styling done right", "dist-tags": {"latest": "5.4.1", "next": "3.0.0-beta.2"}, "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "name": "chalk", "readme": "<h1 align=\"center\">\n\t<br>\n\t<br>\n\t<img width=\"320\" src=\"media/logo.svg\" alt=\"Chalk\">\n\t<br>\n\t<br>\n\t<br>\n</h1>\n\n> Terminal string styling done right\n\n[![Coverage Status](https://codecov.io/gh/chalk/chalk/branch/main/graph/badge.svg)](https://codecov.io/gh/chalk/chalk)\n[![npm dependents](https://badgen.net/npm/dependents/chalk)](https://www.npmjs.com/package/chalk?activeTab=dependents)\n[![Downloads](https://badgen.net/npm/dt/chalk)](https://www.npmjs.com/package/chalk)\n\n![](media/screenshot.png)\n\n## Info\n\n- [Why not switch to a smaller coloring package?](https://github.com/chalk/chalk?tab=readme-ov-file#why-not-switch-to-a-smaller-coloring-package)\n- See [yoctocolors](https://github.com/sindresorhus/yoctocolors) for a smaller alternative\n\n## Highlights\n\n- Expressive API\n- Highly performant\n- No dependencies\n- Ability to nest styles\n- [256/Truecolor color support](#256-and-truecolor-color-support)\n- Auto-detects color support\n- Doesn't extend `String.prototype`\n- Clean and focused\n- Actively maintained\n- [Used by ~115,000 packages](https://www.npmjs.com/browse/depended/chalk) as of July 4, 2024\n\n## Install\n\n```sh\nnpm install chalk\n```\n\n**IMPORTANT:** Chalk 5 is ESM. If you want to use Chalk with TypeScript or a build tool, you will probably want to use Chalk 4 for now. [Read more.](https://github.com/chalk/chalk/releases/tag/v5.0.0)\n\n## Usage\n\n```js\nimport chalk from 'chalk';\n\nconsole.log(chalk.blue('Hello world!'));\n```\n\nChalk comes with an easy to use composable API where you just chain and nest the styles you want.\n\n```js\nimport chalk from 'chalk';\n\nconst log = console.log;\n\n// Combine styled and normal strings\nlog(chalk.blue('Hello') + ' World' + chalk.red('!'));\n\n// Compose multiple styles using the chainable API\nlog(chalk.blue.bgRed.bold('Hello world!'));\n\n// Pass in multiple arguments\nlog(chalk.blue('Hello', 'World!', 'Foo', 'bar', 'biz', 'baz'));\n\n// Nest styles\nlog(chalk.red('Hello', chalk.underline.bgBlue('world') + '!'));\n\n// Nest styles of the same type even (color, underline, background)\nlog(chalk.green(\n\t'I am a green line ' +\n\tchalk.blue.underline.bold('with a blue substring') +\n\t' that becomes green again!'\n));\n\n// ES2015 template literal\nlog(`\nCPU: ${chalk.red('90%')}\nRAM: ${chalk.green('40%')}\nDISK: ${chalk.yellow('70%')}\n`);\n\n// Use RGB colors in terminal emulators that support it.\nlog(chalk.rgb(123, 45, 67).underline('Underlined reddish color'));\nlog(chalk.hex('#DEADED').bold('Bold gray!'));\n```\n\nEasily define your own themes:\n\n```js\nimport chalk from 'chalk';\n\nconst error = chalk.bold.red;\nconst warning = chalk.hex('#FFA500'); // Orange color\n\nconsole.log(error('Error!'));\nconsole.log(warning('Warning!'));\n```\n\nTake advantage of console.log [string substitution](https://nodejs.org/docs/latest/api/console.html#console_console_log_data_args):\n\n```js\nimport chalk from 'chalk';\n\nconst name = 'Sindre';\nconsole.log(chalk.green('Hello %s'), name);\n//=> 'Hello Sindre'\n```\n\n## API\n\n### chalk.`<style>[.<style>...](string, [string...])`\n\nExample: `chalk.red.bold.underline('Hello', 'world');`\n\nChain [styles](#styles) and call the last one as a method with a string argument. Order doesn't matter, and later styles take precedent in case of a conflict. This simply means that `chalk.red.yellow.green` is equivalent to `chalk.green`.\n\nMultiple arguments will be separated by space.\n\n### chalk.level\n\nSpecifies the level of color support.\n\nColor support is automatically detected, but you can override it by setting the `level` property. You should however only do this in your own code as it applies globally to all Chalk consumers.\n\nIf you need to change this in a reusable module, create a new instance:\n\n```js\nimport {Chalk} from 'chalk';\n\nconst customChalk = new Chalk({level: 0});\n```\n\n| Level | Description |\n| :---: | :--- |\n| `0` | All colors disabled |\n| `1` | Basic color support (16 colors) |\n| `2` | 256 color support |\n| `3` | Truecolor support (16 million colors) |\n\n### supportsColor\n\nDetect whether the terminal [supports color](https://github.com/chalk/supports-color). Used internally and handled for you, but exposed for convenience.\n\nCan be overridden by the user with the flags `--color` and `--no-color`. For situations where using `--color` is not possible, use the environment variable `FORCE_COLOR=1` (level 1), `FORCE_COLOR=2` (level 2), or `FORCE_COLOR=3` (level 3) to forcefully enable color, or `FORCE_COLOR=0` to forcefully disable. The use of `FORCE_COLOR` overrides all other color support checks.\n\nExplicit 256/Truecolor mode can be enabled using the `--color=256` and `--color=16m` flags, respectively.\n\n### chalkStderr and supportsColorStderr\n\n`chalkStderr` contains a separate instance configured with color support detected for `stderr` stream instead of `stdout`. Override rules from `supportsColor` apply to this too. `supportsColorStderr` is exposed for convenience.\n\n### modifierNames, foregroundColorNames, backgroundColorNames, and colorNames\n\nAll supported style strings are exposed as an array of strings for convenience. `colorNames` is the combination of `foregroundColorNames` and `backgroundColorNames`.\n\nThis can be useful if you wrap Chalk and need to validate input:\n\n```js\nimport {modifierNames, foregroundColorNames} from 'chalk';\n\nconsole.log(modifierNames.includes('bold'));\n//=> true\n\nconsole.log(foregroundColorNames.includes('pink'));\n//=> false\n```\n\n## Styles\n\n### Modifiers\n\n- `reset` - Reset the current style.\n- `bold` - Make the text bold.\n- `dim` - Make the text have lower opacity.\n- `italic` - Make the text italic. *(Not widely supported)*\n- `underline` - Put a horizontal line below the text. *(Not widely supported)*\n- `overline` - Put a horizontal line above the text. *(Not widely supported)*\n- `inverse`- Invert background and foreground colors.\n- `hidden` - Print the text but make it invisible.\n- `strikethrough` - Puts a horizontal line through the center of the text. *(Not widely supported)*\n- `visible`- Print the text only when Chalk has a color level above zero. Can be useful for things that are purely cosmetic.\n\n### Colors\n\n- `black`\n- `red`\n- `green`\n- `yellow`\n- `blue`\n- `magenta`\n- `cyan`\n- `white`\n- `blackBright` (alias: `gray`, `grey`)\n- `redBright`\n- `greenBright`\n- `yellowBright`\n- `blueBright`\n- `magentaBright`\n- `cyanBright`\n- `whiteBright`\n\n### Background colors\n\n- `bgBlack`\n- `bgRed`\n- `bgGreen`\n- `bgYellow`\n- `bgBlue`\n- `bgMagenta`\n- `bgCyan`\n- `bgWhite`\n- `bgBlackBright` (alias: `bgGray`, `bgGrey`)\n- `bgRedBright`\n- `bgGreenBright`\n- `bgYellowBright`\n- `bgBlueBright`\n- `bgMagentaBright`\n- `bgCyanBright`\n- `bgWhiteBright`\n\n## 256 and Truecolor color support\n\nChalk supports 256 colors and [Truecolor](https://github.com/termstandard/colors) (16 million colors) on supported terminal apps.\n\nColors are downsampled from 16 million RGB values to an ANSI color format that is supported by the terminal emulator (or by specifying `{level: n}` as a Chalk option). For example, Chalk configured to run at level 1 (basic color support) will downsample an RGB value of #FF0000 (red) to 31 (ANSI escape for red).\n\nExamples:\n\n- `chalk.hex('#DEADED').underline('Hello, world!')`\n- `chalk.rgb(15, 100, 204).inverse('Hello!')`\n\nBackground versions of these models are prefixed with `bg` and the first level of the module capitalized (e.g. `hex` for foreground colors and `bgHex` for background colors).\n\n- `chalk.bgHex('#DEADED').underline('Hello, world!')`\n- `chalk.bgRgb(15, 100, 204).inverse('Hello!')`\n\nThe following color models can be used:\n\n- [`rgb`](https://en.wikipedia.org/wiki/RGB_color_model) - Example: `chalk.rgb(255, 136, 0).bold('Orange!')`\n- [`hex`](https://en.wikipedia.org/wiki/Web_colors#Hex_triplet) - Example: `chalk.hex('#FF8800').bold('Orange!')`\n- [`ansi256`](https://en.wikipedia.org/wiki/ANSI_escape_code#8-bit) - Example: `chalk.bgAnsi256(194)('Honeydew, more or less')`\n\n## Browser support\n\nSince Chrome 69, ANSI escape codes are natively supported in the developer console.\n\n## Windows\n\nIf you're on Windows, do yourself a favor and use [Windows Terminal](https://github.com/microsoft/terminal) instead of `cmd.exe`.\n\n## FAQ\n\n### Why not switch to a smaller coloring package?\n\nChalk may be larger, but there is a reason for that. It offers a more user-friendly API, well-documented types, supports millions of colors, and covers edge cases that smaller alternatives miss. Chalk is mature, reliable, and built to last.\n\nBut beyond the technical aspects, there's something more critical: trust and long-term maintenance. I have been active in open source for over a decade, and I'm committed to keeping Chalk maintained. Smaller packages might seem appealing now, but there's no guarantee they will be around for the long term, or that they won't become malicious over time.\n\nChalk is also likely already in your dependency tree (since 100K+ packages depend on it), so switching won’t save space—in fact, it might increase it. npm deduplicates dependencies, so multiple Chalk instances turn into one, but adding another package alongside it will increase your overall size.\n\nIf the goal is to clean up the ecosystem, switching away from Chalk won’t even make a dent. The real problem lies with packages that have very deep dependency trees (for example, those including a lot of polyfills). Chalk has no dependencies. It's better to focus on impactful changes rather than minor optimizations.\n\nIf absolute package size is important to you, I also maintain [yoctocolors](https://github.com/sindresorhus/yoctocolors), one of the smallest color packages out there.\n\n*\\- [Sindre](https://github.com/sindresorhus)*\n\n### But the smaller coloring package has benchmarks showing it is faster\n\n[Micro-benchmarks are flawed](https://sindresorhus.com/blog/micro-benchmark-fallacy) because they measure performance in unrealistic, isolated scenarios, often giving a distorted view of real-world performance. Don't believe marketing fluff. All the coloring packages are more than fast enough.\n\n## Related\n\n- [chalk-template](https://github.com/chalk/chalk-template) - [Tagged template literals](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#tagged_templates) support for this module\n- [chalk-cli](https://github.com/chalk/chalk-cli) - CLI for this module\n- [ansi-styles](https://github.com/chalk/ansi-styles) - ANSI escape codes for styling strings in the terminal\n- [supports-color](https://github.com/chalk/supports-color) - Detect whether a terminal supports color\n- [strip-ansi](https://github.com/chalk/strip-ansi) - Strip ANSI escape codes\n- [strip-ansi-stream](https://github.com/chalk/strip-ansi-stream) - Strip ANSI escape codes from a stream\n- [has-ansi](https://github.com/chalk/has-ansi) - Check if a string has ANSI escape codes\n- [ansi-regex](https://github.com/chalk/ansi-regex) - Regular expression for matching ANSI escape codes\n- [wrap-ansi](https://github.com/chalk/wrap-ansi) - Wordwrap a string with ANSI escape codes\n- [slice-ansi](https://github.com/chalk/slice-ansi) - Slice a string with ANSI escape codes\n- [color-convert](https://github.com/qix-/color-convert) - Converts colors between different models\n- [chalk-animation](https://github.com/bokub/chalk-animation) - Animate strings in the terminal\n- [gradient-string](https://github.com/bokub/gradient-string) - Apply color gradients to strings\n- [chalk-pipe](https://github.com/LitoMore/chalk-pipe) - Create chalk style schemes with simpler style strings\n- [terminal-link](https://github.com/sindresorhus/terminal-link) - Create clickable links in the terminal\n\n*(Not accepting additional entries)*\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n", "time": {"created": "2022-01-26T12:52:46.789Z", "modified": "2024-12-21T17:05:57.938Z", "5.0.0": "2021-11-26T09:57:35.917Z", "4.1.2": "2021-07-30T12:02:52.839Z", "4.1.1": "2021-04-21T08:54:18.124Z", "4.1.0": "2020-06-09T07:43:42.525Z", "4.0.0": "2020-04-02T08:20:33.785Z", "3.0.0": "2019-11-09T06:59:09.065Z", "3.0.0-beta.2": "2019-10-08T09:32:47.141Z", "3.0.0-beta.1": "2019-09-27T05:08:09.440Z", "2.4.2": "2019-01-05T15:45:52.349Z", "2.4.1": "2018-04-26T05:15:51.877Z", "2.4.0": "2018-04-17T04:28:37.857Z", "2.3.2": "2018-03-02T17:43:52.786Z", "2.3.1": "2018-02-11T13:18:28.596Z", "2.3.0": "2017-10-24T04:12:55.953Z", "2.2.2": "2017-10-24T03:20:46.238Z", "2.2.0": "2017-10-18T03:15:41.898Z", "2.1.0": "2017-08-07T03:56:43.217Z", "2.0.1": "2017-06-30T03:26:46.721Z", "2.0.0": "2017-06-29T23:49:22.932Z", "1.1.3": "2016-03-29T00:16:44.512Z", "1.1.2": "2016-03-28T23:32:04.003Z", "1.1.1": "2015-08-19T20:10:58.495Z", "1.1.0": "2015-07-01T13:32:13.906Z", "1.0.0": "2015-02-23T07:41:35.421Z", "0.5.1": "2014-07-09T20:24:36.498Z", "0.5.0": "2014-07-04T21:23:48.003Z", "0.4.0": "2013-12-13T19:30:32.742Z", "0.3.0": "2013-10-19T15:58:20.344Z", "0.2.1": "2013-08-29T14:15:49.234Z", "0.2.0": "2013-08-03T16:48:31.308Z", "0.1.1": "2013-08-03T01:38:53.881Z", "0.1.0": "2013-08-03T00:21:59.499Z", "5.0.1": "2022-03-08T18:44:36.269Z", "5.1.0": "2022-10-05T14:46:20.465Z", "5.1.1": "2022-10-12T09:37:44.826Z", "5.1.2": "2022-10-12T16:31:51.839Z", "5.2.0": "2022-12-08T18:46:27.169Z", "5.3.0": "2023-06-29T10:58:11.887Z", "5.4.0": "2024-12-18T18:00:41.792Z", "5.4.1": "2024-12-21T17:04:53.006Z"}, "versions": {"5.0.0": {"name": "chalk", "version": "5.0.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.47.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "4d5c4795ad24c326ae16bfe0c39c826c732716a9", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-/duVOqst+luxCQRKEo4bNxinsOQtMP80ZYm7mMqzuh5PociNL0PvmHFvREJ9ueYL2TxlHjBcmLCdmocx9Vg+IQ==", "shasum": "bd96c6bb8e02b96e08c0c3ee2a9d90e050c7b832", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.0.0.tgz", "fileCount": 12, "unpackedSize": 41306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoK+PCRA9TVsSAnZWagAAX+wP/jaVhQWZpfCaDjmJsHuN\n5JQ1XlOLFdbzRfyvuZ2XAn96myIf/1UAa96Pi9gKXjVV1cnRdFW9TPgBMIzt\n42mqC2T8/qJe0a2VFIvlRlSFBMyThbyr3WnDCHvrgjfy8pp6EN10uHwSqnWn\nYL5NemSFQSc2EZ1LxulrJC8bzj3CzcB6yJXDMKUE3BfSFL0r5YxG2FLjktDw\nlL9hm/JxylEPgUIrzKNabO0W77Lcvg2nwJzxRZpDCkLC6NSLx3UV8nDyzoXT\nlvBiR1pxzjWrWbOPvTGO1+5q1HdVTOGqLWA/XjSBBpHZ7SQDgkh9f8pwP823\nep7Q3/2j5BvgleeVzOPQqwGXOq7qbAcBFibHTLd2Zi0xXBFMHtIVaw8odDVV\nAsWqTV4qAgRrefe8Q7wIzUrO42Z5ndRfqHSI6t9bJ4AeH/ZL3vrNNmTFIoLx\nJdWEu1WXSnkrFdCWkxHIjU0wyVJAIAOtK/vQw7LPmOQhEeHLLos/t7fBP9tC\nMi5UoMNZYYE41d5MENeySPAHXZ+ZVIROdAqN7mCCwfAg8lygiNfbNLiPeo9o\nld5H9hYFw4eW31fwvlX8Q6MclVR5k9755fpZUHcyDqgN6hgLJU4j0PHSvnhI\nFrB0wwi7SNJ4DiqIlKtpeNaRSwy1cnaiML3wFeeYKf0zZEAp26bf4CNbZ9XX\ndcEG\r\n=qfhV\r\n-----END PGP SIGNATURE-----\r\n", "size": 13293, "noattachment": false}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.0.0_1637920655740_0.2838492953241636"}, "_hasShrinkwrap": false, "publish_time": 1637920655917, "_cnpm_publish_time": 1637920655917, "_cnpmcore_publish_time": "2021-12-13T06:35:17.941Z"}, "4.1.2": {"name": "chalk", "version": "4.1.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^4.0.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^15.0.0", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.28.2"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off", "@typescript-eslint/member-ordering": "off", "no-redeclare": "off", "unicorn/string-content": "off", "unicorn/better-regex": "off"}}, "gitHead": "95d74cbe8d3df3674dec1445a4608d3288d8b73c", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@4.1.2", "_nodeVersion": "12.22.1", "_npmVersion": "7.5.4", "dist": {"shasum": "aac4e2b7734a740867aeb16bf02aad556a1e7a01", "size": 11577, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_4.1.2_1627646572658_0.5829286157088185"}, "_hasShrinkwrap": false, "publish_time": 1627646572839, "_cnpm_publish_time": 1627646572839, "_cnpmcore_publish_time": "2021-12-13T06:35:18.198Z"}, "4.1.1": {"name": "chalk", "version": "4.1.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^4.0.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^15.0.0", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.28.2"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off", "@typescript-eslint/member-ordering": "off", "no-redeclare": "off", "unicorn/string-content": "off", "unicorn/better-regex": "off"}}, "gitHead": "89e9e3a5b0601f4eda4c3a92acd887ec836d0175", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@4.1.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.5.4", "dist": {"shasum": "c80b3fab28bf6371e6863325eee67e618b77e6ad", "size": 11521, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-4.1.1.tgz", "integrity": "sha512-diHzdDKxcU+bAsUboHLPEDQiw0qEe0qd7SYUn3HgcFlWgbDcfLGswOHYeGrHKzG9z6UYf01d9VFMfZxPM1xZSg=="}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_4.1.1_1618995257952_0.8528874341897312"}, "_hasShrinkwrap": false, "publish_time": 1618995258124, "_cnpm_publish_time": 1618995258124, "_cnpmcore_publish_time": "2021-12-13T06:35:18.563Z"}, "4.1.0": {"name": "chalk", "version": "4.1.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^4.0.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^15.0.0", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.28.2"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off", "@typescript-eslint/member-ordering": "off", "no-redeclare": "off", "unicorn/string-content": "off", "unicorn/better-regex": "off"}}, "gitHead": "4c3df8847256f9f2471f0af74100b21afc12949f", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@4.1.0", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4e14870a618d9e2edd97dd8345fd9d9dc315646a", "size": 11068, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-4.1.0.tgz", "integrity": "sha512-qwx12AxXe2Q5xQ43Ac//I6v5aXTipYrSESdOgzrN+9XjgEpyjpKuvSGaN4qE93f7TQTlerQQ8S+EQ0EyDoVL1A=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_4.1.0_1591688622018_0.2681914768782716"}, "_hasShrinkwrap": false, "publish_time": 1591688622525, "_cnpm_publish_time": 1591688622525, "_cnpmcore_publish_time": "2021-12-13T06:35:18.859Z"}, "4.0.0": {"name": "chalk", "version": "4.0.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^4.0.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^15.0.0", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.28.2"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off", "@typescript-eslint/member-ordering": "off", "no-redeclare": "off", "unicorn/string-content": "off", "unicorn/better-regex": "off"}}, "gitHead": "31fa94208034cb7581a81b06045ff2cf51057b40", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@4.0.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6e98081ed2d17faab615eb52ac66ec1fe6209e72", "size": 10970, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-4.0.0.tgz", "integrity": "sha512-N9oWFcegS0sFr9oh1oz2d7Npos6vNoWW9HvtCg5N1KRFpUhaAhvTv5Y58g880fZaEYSNm3qDz8SU1UrGvp+n7A=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_4.0.0_1585815633587_0.14399738942582263"}, "_hasShrinkwrap": false, "publish_time": 1585815633785, "_cnpm_publish_time": 1585815633785, "_cnpmcore_publish_time": "2021-12-13T06:35:19.213Z"}, "3.0.0": {"name": "chalk", "version": "3.0.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "main": "source", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^3.2.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^14.1.1", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.25.3"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off"}}, "gitHead": "20002d8bd1dfd6f68bfa8bdacba520ff6027a450", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@3.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.13.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3f73c2bf526591f574cc492c51e2456349f844e4", "size": 10923, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-3.0.0.tgz", "integrity": "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_3.0.0_1573282748902_0.03099002657701666"}, "_hasShrinkwrap": false, "publish_time": 1573282749065, "_cnpm_publish_time": 1573282749065, "_cnpmcore_publish_time": "2021-12-13T06:35:19.574Z"}, "3.0.0-beta.2": {"name": "chalk", "version": "3.0.0-beta.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "main": "source", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.5", "execa": "^2.0.3", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^14.1.1", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.25.3"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off"}}, "readmeFilename": "readme.md", "gitHead": "4de1841129cf3d0a1db7a5d6638402b7828e1731", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@3.0.0-beta.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d6a422d12828314a6f2ecad6863c68b706598a22", "size": 10784, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-3.0.0-beta.2.tgz", "integrity": "sha512-NKGY083nqioebgPmjRTGY/bRq34dgkXkmHmWgwDXbPs0YEkN7ZTcimhnv1KnHGW80QPta1QZiJDbQv7s+e+uaQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_3.0.0-beta.2_1570527167016_0.8251191799502275"}, "_hasShrinkwrap": false, "publish_time": 1570527167141, "_cnpm_publish_time": 1570527167141, "_cnpmcore_publish_time": "2021-12-13T06:35:19.951Z"}, "3.0.0-beta.1": {"name": "chalk", "version": "3.0.0-beta.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "main": "source", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.5", "execa": "^2.0.3", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^14.1.1", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.25.3"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off"}}, "readmeFilename": "readme.md", "gitHead": "48905d08052aad4c8ba53bbd9fbcd8a9faf4f6e5", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@3.0.0-beta.1", "_nodeVersion": "8.16.1", "_npmVersion": "6.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "328a0eccc051d6e50787fe112cbcf85b5d5e4d88", "size": 10694, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-3.0.0-beta.1.tgz", "integrity": "sha512-f32K9VcIM5XJjpPHkqbrg+xN4vQVzEFNmPTgn1Ai3RBbtWT6ggFkUwZmB3/JTk0tdtjH1sl7fepaB9Vj8uVdUw=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_3.0.0-beta.1_1569560889303_0.3449501496211118"}, "_hasShrinkwrap": false, "publish_time": 1569560889440, "_cnpm_publish_time": 1569560889440, "_cnpmcore_publish_time": "2021-12-13T06:35:20.341Z"}, "2.4.2": {"name": "chalk", "version": "2.4.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "flow-bin": "^0.68.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "gitHead": "9776a2ae5b5b1712ccf16416b55f47e575a81fb9", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.4.2", "_npmVersion": "6.5.0", "_nodeVersion": "10.13.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cd42541677a54333cf541a49108c1432b44c9424", "size": 9865, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_2.4.2_1546703152138_0.5501232329596948"}, "_hasShrinkwrap": false, "publish_time": 1546703152349, "_cnpm_publish_time": 1546703152349, "_cnpmcore_publish_time": "2021-12-13T06:35:20.679Z"}, "2.4.1": {"name": "chalk", "version": "2.4.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts", "index.js.flow"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "flow-bin": "^0.68.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "gitHead": "48ba5b0b9beadcabd9fc406ac4d9337d8fa6b36d", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "18c49ab16a037b6eb0152cc83e3471338215b66e", "size": 9918, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.4.1.tgz", "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "<EMAIL>", "name": "unicorn"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_2.4.1_1524719751701_0.7780175911150953"}, "_hasShrinkwrap": false, "publish_time": 1524719751877, "_cnpm_publish_time": 1524719751877, "_cnpmcore_publish_time": "2021-12-13T06:35:21.043Z"}, "2.4.0": {"name": "chalk", "version": "2.4.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts", "index.js.flow"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "flow-bin": "^0.68.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "gitHead": "af8b3657e96a0a6ca5190fb0d0a1345797148320", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a060a297a6b57e15b61ca63ce84995daa0fe6e52", "size": 9947, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.4.0.tgz", "integrity": "sha512-Wr/w0f4o9LuE7K53cD0qmbAMM+2XNLzR29vFn5hqko4sxGlUsyy363NvmyGIyk5tpe9cjTr9SJYbysEyPkRnFw=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "<EMAIL>", "name": "unicorn"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_2.4.0_1523939317754_0.3039215958746819"}, "_hasShrinkwrap": false, "publish_time": 1523939317857, "_cnpm_publish_time": 1523939317857, "_cnpmcore_publish_time": "2021-12-13T06:35:21.449Z"}, "2.3.2": {"name": "chalk", "version": "2.3.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"]}, "gitHead": "84f27d4bd86f7f482a32652ae536cd996ad204bd", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.3.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "250dc96b07491bfd601e648d66ddf5f60c7a5c65", "size": 9349, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.3.2.tgz", "integrity": "sha512-ZM4j2/ld/YZDc3Ma8PgN7gyAk+kHMMMyzLNryCPGhWrsfAuDVeuid5bpRFTDgMH9JBK2lA4dyyAkkZYF/WcqDQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "<EMAIL>", "name": "unicorn"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_2.3.2_1520012630405_0.9222977073145247"}, "_hasShrinkwrap": false, "publish_time": 1520012632786, "_cnpm_publish_time": 1520012632786, "_cnpmcore_publish_time": "2021-12-13T06:35:21.865Z"}, "2.3.1": {"name": "chalk", "version": "2.3.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.0", "escape-string-regexp": "^1.0.5", "supports-color": "^5.2.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"]}, "gitHead": "ae8a03f2c5c49896adeb3dd4ec5350e4ab9449a2", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.3.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "523fe2678aec7b04e8041909292fe8b17059b796", "size": 9340, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.3.1.tgz", "integrity": "sha512-QUU4ofkDoMIVO7hcx1iPTISs88wsO8jA92RQIm4JAwZvFGGAV2hSAA1NX7oVj2Ej2Q6NDTcRDjPTFrMCRZoJ6g=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "<EMAIL>", "name": "unicorn"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_2.3.1_1518355108425_0.3816906865374552"}, "_hasShrinkwrap": false, "publish_time": 1518355108596, "_cnpm_publish_time": 1518355108596, "_cnpmcore_publish_time": "2021-12-13T06:35:22.298Z"}, "2.3.0": {"name": "chalk", "version": "2.3.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.8.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"]}, "gitHead": "14e0aa97727019b22f0a003fdc631aeec5e2e24c", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.3.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b5ea48efc9c1793dccc9b4767c93914d3f2d52ba", "size": 9276, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.3.0.tgz", "integrity": "sha512-Az5zJR2CBujap2rqXGaJKaPHyJ0IrUimvYNX+ncCy8PJP4ltOGTrHUIo097ZaL2zMeKYpiCdqDvS6zdrTFok3Q=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "unicorn"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk-2.3.0.tgz_1508818375657_0.9021007190458477"}, "directories": {}, "publish_time": 1508818375953, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508818375953, "_cnpmcore_publish_time": "2021-12-13T06:35:22.716Z"}, "2.2.2": {"name": "chalk", "version": "2.2.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.8.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"]}, "gitHead": "e1177ec3628f6d0d37489c1e1accd2c389a376a8", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.2.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "4403f5cf18f35c05f51fbdf152bf588f956cf7cb", "size": 9239, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.2.2.tgz", "integrity": "sha512-LvixLAQ4MYhbf7hgL4o5PeK32gJKvVzDRiSNIApDofQvyhl8adgG2lJVXn4+ekQoK7HL9RF8lqxwerpe0x2pCw=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "unicorn"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk-2.2.2.tgz_1508815246099_0.3707860491704196"}, "directories": {}, "publish_time": 1508815246238, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508815246238, "_cnpmcore_publish_time": "2021-12-13T06:35:23.271Z"}, "2.2.0": {"name": "chalk", "version": "2.2.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.8.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"]}, "gitHead": "d86db88e778fa856f4d6f5f68c588750ca06b822", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.2.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "477b3bf2f9b8fd5ca9e429747e37f724ee7af240", "size": 9121, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.2.0.tgz", "integrity": "sha512-0BMM/2hG3ZaoPfR6F+h/oWpZtsh3b/s62TjSM6MGCJWEbJDN1acqCXvyhhZsDSVFklpebUoQ5O1kKC7lOzrn9g=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "unicorn"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk-2.2.0.tgz_1508296541817_0.8590951061341912"}, "directories": {}, "publish_time": 1508296541898, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508296541898, "_cnpmcore_publish_time": "2021-12-13T06:35:23.722Z"}, "2.1.0": {"name": "chalk", "version": "2.1.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "devDependencies": {"ava": "*", "coveralls": "^2.11.2", "execa": "^0.7.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^3.0.0", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}, "gitHead": "38f641a222d7ee0e607e4e5209d3931d2af1e409", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "ac5becf14fa21b99c6c92ca7a7d7cfd5b17e743e", "size": 8447, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.1.0.tgz", "integrity": "sha512-LUHGS/dge4ujbXMJrnihYMcL4AoOweGnw9Tp3kQuqy1Kx5c1qKjqvMJZ6nVJPMWJtKCTN72ZogH3oeSO9g9rXQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "unicorn"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk-2.1.0.tgz_1502078203099_0.6595528507605195"}, "directories": {}, "publish_time": 1502078203217, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502078203217, "_cnpmcore_publish_time": "2021-12-13T06:35:24.267Z"}, "2.0.1": {"name": "chalk", "version": "2.0.1", "description": "Terminal string styling done right. Much color", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc mocha", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "mocha": "*", "nyc": "^11.0.2", "resolve-from": "^3.0.0", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}, "gitHead": "5827081719944a2f903b52a88baeec1ec8581f82", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.0.1", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "dbec49436d2ae15f536114e76d14656cdbc0f44d", "size": 8538, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.0.1.tgz", "integrity": "sha512-Mp+FX<PERSON>+FrwY/XYV45b2YD3E8i3HwnEAoFcM0qlZzq/RZ9RwWitt2Y/c7cqRAz70U7hfekqx6qNYthuKFO6K0g=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "unicorn"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk-2.0.1.tgz_1498793206623_0.8611406192649156"}, "directories": {}, "publish_time": 1498793206721, "_hasShrinkwrap": false, "deprecated": "Please upgrade to Chalk 2.1.0 - template literals in this version (2.0.1) are quite buggy.", "_cnpm_publish_time": 1498793206721, "_cnpmcore_publish_time": "2021-12-13T06:35:24.749Z"}, "2.0.0": {"name": "chalk", "version": "2.0.0", "description": "Terminal string styling done right. Much color", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc mocha", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "mocha": "*", "nyc": "^11.0.2", "resolve-from": "^3.0.0", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}, "gitHead": "3fca6150e23439e783409f5c8f948f767c2ddc5a", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@2.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c25c5b823fedff921aa5d83da3ecb5392e84e533", "size": 8523, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-2.0.0.tgz", "integrity": "sha512-7jy/5E6bVCRhLlvznnsbVPjsARuVC9HDkBjUKVaOmUrhsp6P3ExUUcW09htM7/qieRH+D2lHVpNbuYh7GjVJ0g=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "unicorn"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk-2.0.0.tgz_1498780161964_0.21432337583974004"}, "directories": {}, "publish_time": 1498780162932, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498780162932, "_cnpmcore_publish_time": "2021-12-13T06:35:25.280Z"}, "1.1.3": {"name": "chalk", "version": "1.1.3", "description": "Terminal string styling done right. Much color.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "matcha": "^0.6.0", "mocha": "*", "nyc": "^3.0.0", "require-uncached": "^1.0.2", "resolve-from": "^1.0.0", "semver": "^4.3.3", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}, "gitHead": "0d8d8c204eb87a4038219131ad4d8369c9f59d24", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@1.1.3", "_shasum": "a8115c55e4a702fe4d150abd3872822a7e09fc98", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "a8115c55e4a702fe4d150abd3872822a7e09fc98", "size": 5236, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-1.1.3.tgz", "integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/chalk-1.1.3.tgz_1459210604109_0.3892582862172276"}, "directories": {}, "publish_time": 1459210604512, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459210604512, "_cnpmcore_publish_time": "2021-12-13T06:35:25.861Z"}, "1.1.2": {"name": "chalk", "version": "1.1.2", "description": "Terminal string styling done right. Much color.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "supports-color": "^3.1.2"}, "devDependencies": {"coveralls": "^2.11.2", "matcha": "^0.6.0", "mocha": "*", "nyc": "^5.2.0", "require-uncached": "^1.0.2", "resolve-from": "^2.0.0", "semver": "^5.1.0", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}, "gitHead": "a838948dcbf2674dd28adfbb78e791900ae741e9", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@1.1.2", "_shasum": "53e9f9e7742f7edf23065c29c0219175a7869155", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "53e9f9e7742f7edf23065c29c0219175a7869155", "size": 5210, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-1.1.2.tgz", "integrity": "sha512-QBKX51aavmpKcCkgrJXhjS5b3rCgH2Wn99BYqUV2H1FjTP7Mm4KTcskSxuKrfhQKt69mBn9jH4Kb2xnchvEaOw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/chalk-1.1.2.tgz_1459207923607_0.6091341155115515"}, "deprecated": "chalk@1.1.2 introduces breaking changes. Please use 1.1.3 or above.", "directories": {}, "publish_time": 1459207924003, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459207924003, "_cnpmcore_publish_time": "2021-12-13T06:35:26.459Z"}, "1.1.1": {"name": "chalk", "version": "1.1.1", "description": "Terminal string styling done right. Much color.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^2.1.0", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "matcha": "^0.6.0", "mocha": "*", "nyc": "^3.0.0", "require-uncached": "^1.0.2", "resolve-from": "^1.0.0", "semver": "^4.3.3", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}, "gitHead": "8b554e254e89c85c1fd04dcc444beeb15824e1a5", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@1.1.1", "_shasum": "509afb67066e7499f7eb3535c77445772ae2d019", "_from": ".", "_npmVersion": "2.13.5", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "509afb67066e7499f7eb3535c77445772ae2d019", "size": 5242, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-1.1.1.tgz", "integrity": "sha512-W10W+QfIxJlTm3VRtg8eafwUBkDfUPFvRvPv4jCD9vF4+HzlAyXJ7P3Y5yw/r+gJ1TzFEU6oFqMgp1dIVpYr0A=="}, "directories": {}, "publish_time": 1440015058495, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440015058495, "_cnpmcore_publish_time": "2021-12-13T06:35:26.973Z"}, "1.1.0": {"name": "chalk", "version": "1.1.0", "description": "Terminal string styling done right. Much color.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/chalk"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^2.1.0", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "matcha": "^0.6.0", "mocha": "*", "nyc": "^3.0.0", "require-uncached": "^1.0.2", "resolve-from": "^1.0.0", "semver": "^4.3.3"}, "gitHead": "e9bb6e6000b1c5d4508afabfdc85dd70f582f515", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk", "_id": "chalk@1.1.0", "_shasum": "09b453cec497a75520e4a60ae48214a8700e0921", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "09b453cec497a75520e4a60ae48214a8700e0921", "size": 5163, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-1.1.0.tgz", "integrity": "sha512-pn7bzDYUIrL0KRp/KK5B+sej6uYtzQ5hYOdLU+L3MVWHCgoYi4aUYdh2/R2rsdURIoOK/ptZi5FDtLdjvKYQ7g=="}, "directories": {}, "publish_time": 1435757533906, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435757533906, "_cnpmcore_publish_time": "2021-12-13T06:35:27.632Z"}, "1.0.0": {"name": "chalk", "version": "1.0.0", "description": "Terminal string styling done right. Much color.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/chalk"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "bench": "matcha benchmark.js"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^2.0.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^1.0.3", "strip-ansi": "^2.0.1", "supports-color": "^1.3.0"}, "devDependencies": {"matcha": "^0.6.0", "mocha": "*"}, "gitHead": "8864d3563313ed15574a38dd5c9d5966080c46ce", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "homepage": "https://github.com/sindresorhus/chalk", "_id": "chalk@1.0.0", "_shasum": "b3cf4ed0ff5397c99c75b8f679db2f52831f96dc", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b3cf4ed0ff5397c99c75b8f679db2f52831f96dc", "size": 4687, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-1.0.0.tgz", "integrity": "sha512-1TE3hpADga5iWinlcCpyhC7fTl9uQumLD8i2jJoJeVg7UbveY5jj7F6uCq8w0hQpSeLhaPn5QFe8e56toMVP1A=="}, "directories": {}, "publish_time": 1424677295421, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424677295421, "_cnpmcore_publish_time": "2021-12-13T06:35:28.451Z"}, "0.5.1": {"name": "chalk", "version": "0.5.1", "description": "Terminal string styling done right. Created because the `colors` module does some really horrible things.", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "bench": "matcha benchmark.js"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^1.1.0", "escape-string-regexp": "^1.0.0", "has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "supports-color": "^0.2.0"}, "devDependencies": {"matcha": "^0.5.0", "mocha": "*"}, "gitHead": "994758f01293f1fdcf63282e9917cb9f2cfbdaac", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "homepage": "https://github.com/sindresorhus/chalk", "_id": "chalk@0.5.1", "_shasum": "663b3a648b68b55d04690d49167aa837858f2174", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "663b3a648b68b55d04690d49167aa837858f2174", "size": 3334, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.5.1.tgz", "integrity": "sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg=="}, "directories": {}, "publish_time": 1404937476498, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404937476498, "_cnpmcore_publish_time": "2021-12-13T06:35:29.005Z"}, "0.5.0": {"name": "chalk", "version": "0.5.0", "description": "Terminal string styling done right. Created because the `colors` module does some really horrible things.", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "bench": "matcha benchmark.js"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^1.1.0", "escape-string-regexp": "^1.0.0", "has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "supports-color": "^0.2.0"}, "devDependencies": {"matcha": "^0.5.0", "mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "homepage": "https://github.com/sindresorhus/chalk", "_id": "chalk@0.5.0", "_shasum": "375dfccbc21c0a60a8b61bc5b78f3dc2a55c212f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "375dfccbc21c0a60a8b61bc5b78f3dc2a55c212f", "size": 3134, "noattachment": false, "tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.5.0.tgz", "integrity": "sha512-rTCcbF0wrwC+kKzA/3SpBc6PrcOx/+PRQVtS3PEDw5tGzqycpB48dRS8ByxFDd8Ij5E1RtafZ34R1X9VLI/vUQ=="}, "directories": {}, "publish_time": 1404509028003, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404509028003, "_cnpmcore_publish_time": "2021-12-13T06:35:29.638Z"}, "0.4.0": {"name": "chalk", "version": "0.4.0", "description": "Terminal string styling done right. Created because the `colors` module does some really horrible things.", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~1.0.0", "strip-ansi": "~0.1.0"}, "devDependencies": {"mocha": "~1.x"}, "readmeFilename": "readme.md", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "homepage": "https://github.com/sindresorhus/chalk", "_id": "chalk@0.4.0", "dist": {"tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.4.0.tgz", "shasum": "5199a3ddcd0c1efe23bc08c1b027b06176e0c64f", "size": 2534, "noattachment": false, "integrity": "sha512-sQfYDlfv2DGVtjdoQqxS0cEZDroyG8h6TamA6rvxwlrU5BaSLDx9xhatBYl2pxZ7gmpNaPFVwBtdGdu5rQ+tYQ=="}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386963032742, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386963032742, "_cnpmcore_publish_time": "2021-12-13T06:35:30.262Z"}, "0.3.0": {"name": "chalk", "version": "0.3.0", "description": "Terminal string styling done right", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["chalk.js"], "main": "chalk", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk.git"}, "scripts": {"test": "mocha"}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "readmeFilename": "readme.md", "_id": "chalk@0.3.0", "dist": {"tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.3.0.tgz", "shasum": "1c98437737f1199ebcc1d4c48fd41b9f9c8e8f23", "size": 2296, "noattachment": false, "integrity": "sha512-OcfgS16PHpCu2Q4TNMtk0aZNx8PyeNiiB+6AgGH91fhT9hJ3v6pIIJ3lxlaOEDHlTm8t3wDe6bDGamvtIokQTg=="}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1382198300344, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382198300344, "_cnpmcore_publish_time": "2021-12-13T06:35:30.858Z"}, "0.2.1": {"name": "chalk", "version": "0.2.1", "description": "Terminal string styling done right", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["chalk.js"], "main": "chalk", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk.git"}, "scripts": {"test": "mocha"}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "readmeFilename": "readme.md", "_id": "chalk@0.2.1", "dist": {"tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.2.1.tgz", "shasum": "7613e1575145b21386483f7f485aa5ffa8cbd10c", "size": 2189, "noattachment": false, "integrity": "sha512-nmVapomwGksziCuynboy7I+dtW4ytIdqXPlrfY/ySx8l8EqFRGHyA04q6NMNpOri8XliGUGwXyfScVl48zFHbw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377785749234, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377785749234, "_cnpmcore_publish_time": "2021-12-13T06:35:31.615Z"}, "0.2.0": {"name": "chalk", "version": "0.2.0", "description": "Terminal string styling done right", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["chalk.js"], "main": "chalk", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk.git"}, "scripts": {"test": "mocha"}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "readmeFilename": "readme.md", "_id": "chalk@0.2.0", "dist": {"tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.2.0.tgz", "shasum": "47270e80edce0e219911af65479d17db525ff5db", "size": 2183, "noattachment": false, "integrity": "sha512-CHq4xplBE+jhsJKGmh8AegFpEsC84kQNPMeL2mjrD5ojPc1LqNV1q5opCBU7BcRxWbpX+S8s+q4LFaqjP1rZmg=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375548511308, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375548511308, "_cnpmcore_publish_time": "2021-12-13T06:35:32.382Z"}, "0.1.1": {"name": "chalk", "version": "0.1.1", "description": "Terminal string styling done right", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["chalk.js"], "main": "chalk", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk.git"}, "scripts": {"test": "mocha"}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "readmeFilename": "readme.md", "_id": "chalk@0.1.1", "dist": {"tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.1.1.tgz", "shasum": "fe6d90ae2c270424720c87ed92d36490b7d36ea0", "size": 2144, "noattachment": false, "integrity": "sha512-NJbznmWlxmS5Co0rrLJYO0U3QW6IzWw2EuojeOFn4e8nD1CYR5Ie60CEEmHrF8DXtfd83pdF0xYWVCXbRysrDQ=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375493933881, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375493933881, "_cnpmcore_publish_time": "2021-12-13T06:35:32.961Z"}, "0.1.0": {"name": "chalk", "version": "0.1.0", "description": "Terminal string styling done right", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["chalk.js"], "main": "chalk", "repository": {"type": "git", "url": "git://github.com/sindresorhus/chalk.git"}, "scripts": {"test": "mocha"}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "readmeFilename": "readme.md", "_id": "chalk@0.1.0", "dist": {"tarball": "https://registry.npmmirror.com/chalk/-/chalk-0.1.0.tgz", "shasum": "69afbee2ffab5e0db239450767a6125cbea50fa2", "size": 2138, "noattachment": false, "integrity": "sha512-E1+My+HBCBHA6fBUZlbPnrOMrGKnc3QAXGEvCk/lpEG/ZKowZFg01dXt6RCYJMvTWYgxHWTyZQ6qkCrVPKJ2YQ=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375489319499, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375489319499, "_cnpmcore_publish_time": "2021-12-13T06:35:33.615Z"}, "5.0.1": {"name": "chalk", "version": "5.0.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.47.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "bccde97f8a1bb125d4fe99e8fd355182101ff4f2", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.0.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-Fo07WOYGqMfCWHOzSXOt2CxDbC6skS/jO9ynEcmpANMoPrD+W1r1K6Vx7iNm+AQmETU1Xr2t+n8nzkV9t6xh3w==", "shasum": "ca57d71e82bb534a296df63bbacc4a1c22b2a4b6", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.0.1.tgz", "fileCount": 12, "unpackedSize": 41336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ6QUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR4hAAkZ1Pzo7shU7iLgAUqhpUI79vsAQoqkKYBv9iC+Z4ogWUlcd0\r\nRmauMjugcrKH6fsokqp9jDuJLiZSAbj7nwbmWDoBR9XRrxedjsB2H0eANVlC\r\nTAecFxGdN4gxEBzE6yVOfJelpNuw2qWstXKwOZmLwZkTlqE9LETYWO7xmzfs\r\nUW42alsTOP78ZYRnUWwkeZr3Z8yt7thGUwIi9q+QFsDIt0VwE86uTLeLvv+6\r\npNI04MyKzNkKRG+PF8uW+O+j5PYIr9BPYY++g+f+rayFdwSYcoQjutF0BHdj\r\nG+7DGTrk+dhzCJ5xLSZY0rljcgyY9/KMSj+3rO7fUJalPIpqbwLClmMtfJQU\r\n2MmdM+dJfsXAfoZA5rMxP3GoOC8LC6RaScliVivockrCSJgd9Tp9KzRt+66I\r\nqNiuztSypB5SRB7TC41S8jvIHDw5PNNnNP5C7E/uBjU35yc6aX/2pyxUeXYm\r\nAOPLmVWqBcroBiE4zi4OmG1fB2Izw9DJsK31i2U98lkMLfkVaUa4tIBgVwbU\r\nkB62F3BA86yYuWucYeZL3hp9H1TG5+WBhs9+lHrb0+mH3b6ab2lZu/uCWvH6\r\nSedXquHHQxyM9JcuxDrDtmELqR87B+h0iAKzBf7KYtmyrqrxpHE0FiteQsV9\r\nUJbfD3SPAaX/IBAryNeP4Q4e/oSQDy3YUuI=\r\n=ZFqY\r\n-----END PGP SIGNATURE-----\r\n", "size": 13294}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.0.1_1646765076079_0.31760153530326884"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-08T18:45:22.314Z"}, "5.1.0": {"name": "chalk", "version": "5.1.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.52.4", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "92c55db46f2396c18764e55e6a52dcb49884a42b", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.1.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-56zD4khRTBoIyzUYAFgDDaPhUMN/fC/rySe6aZGqbj/VWiU2eI3l6ZLOtYGFZAV5v02mwPjtpzlrOveJiz5eZQ==", "shasum": "c4b4a62bfb6df0eeeb5dbc52e6a9ecaff14b9976", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.1.0.tgz", "fileCount": 12, "unpackedSize": 41604, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGiB2UgkPRG76rTZOqtyvgHW3GZdkr1l1grc4mtEcDuQIhAJuhTuvzptwKcBUWBFc4+2YHWu3vqzlDzAAfqkac2pZ1"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPZi8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY0xAAiHomYe49a9lY0i07r0BEJZGVmegNRGYPJAekN+UaRE/UOx67\r\n6EzdIa7v74VYkhVD4rHBg/O7Uqe+O0U517do414JWVWVXbkB/t5RGjaGtH1s\r\n8OQ85DCyTmIhD15M107/v3qSKWLMhE8lca1KnFYKtCrGFfmEsib8hAFsqS2F\r\nWnrEM152LuQeXnUoUwCaI5yUefG0uLe/yuKhtyWcKToxjmK6HH4ORlG+1o+K\r\nsyXX6Lfv3m40LAIWayP8KE49RjZ1kM0sFK+hasPg8hoxMQgSyM2URnjcUPVN\r\n9wi8ReRUHuaWyuPEPWfRRnYpK8IQnTo24eFaSz4Jz5Z/yVLDYhmOuvzeqXbm\r\nkv74Tnn4HlD7iju3erQQjolvl5D+mKh9ObGjl92nekeyg8G/A/w2EfMpd8FR\r\nJfFBr9Ty+vtNEYbl5iKgzjrl5uLEHKmOGfXXogLsahWB0EXuX2AV8dB5OMlX\r\ngmr68f1jvWYZmxiaKHkXVsLAW/TXl1zWOotSdeYDVDpZQ3SOKtZCez+QKnBQ\r\n1GFqIj/nBmkSGtP++w9Hk9PGAXM7cW/7pLgwT8qGLEeT2SrwnEEAUkImPNJe\r\nnLHSyaS7l7nA267jR5XQQ2SP0wR1alYrE4MKL0nw5wHau8bxLMb1ZFjxE58V\r\n/LYxUMGQDJqfgNtyYmtq8CycQRUv3kbxsHg=\r\n=0Hlp\r\n-----END PGP SIGNATURE-----\r\n", "size": 13265}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.1.0_1664981180277_0.8837577780411197"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-05T14:46:31.498Z"}, "5.1.1": {"name": "chalk", "version": "5.1.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.52.4", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "1b4cd21fb15ca441ab8ff1fc4ce9fcd1365e4b7d", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.1.1", "_nodeVersion": "16.15.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-OItMegkSDU3P7OJRWBbNRsQsL8SzgwlIGXSZRVfHCLBYrDgzYDuozwDMwvEDpiZdjr50tdOTbTzuubirtEozsg==", "shasum": "546fb2b8fc5b7dad0991ef81b2a98b265fa71e02", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.1.1.tgz", "fileCount": 12, "unpackedSize": 43787, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCe9oT/CKTmjgzKIXm3S+DVkuwywIySphHQrSYz9xWtGAIhAJNOTCunCt5qIDhrg5pWHvdPHorZeO13auuylbrUjTq/"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRoroACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotkg//SSk78lf5lQWol2k17UO4dnj+LpYx4gNYLk8uptAPbsRLyhx2\r\nhkmiejrW9Komrzx1FO58JwocXNS5Q2I3mUqG3WisGokfvTWI6kDVlx9l4M0a\r\n0lPo/ENgk9lPN1WUMVx60zCneP/NDxG/sJerE8cM/UnWdc/YHKDiHQEU/hzX\r\nkGAn5juElGo0ae4PAL1M15UsMg3Mk4lyhTRsoztWxYfAI1pNIAxI6ovTaVWg\r\nG/dT6b59DsG6U8dM94cwGxFGtOsnzaBfgupH5Y1T1GvwVQCgeFiLlWWXLAqc\r\nsqP67fUDJtMY1qAJCKhFm80g4eCr6NOvfZ8yovn87Hw8RRmglyOrA46MpQ70\r\nYdJbJBruOToWfR9C9VpcO7BzcPJP79ynfyX70ULe1uP6MZ6R4nJr5FfKanm6\r\ndde4ZTNvLr7ZDWhbW9n4Fr7cVMsT9Jr3f1R/YJLqBNpp0cQBP7UeeJfi+yRj\r\nAsoD1oaDgdDIjcjRBUVAKirU4EwNZ/tk7ejQg710KBK2ccgsiav5rm69TeDd\r\nlCLJdiE856wWK6ZRqY/aXdydoglVJb/L8rG0UK20gaEw4U/BGkLoyvWke/PG\r\nz8VUy2ih3MlSRNVBgXW5U15jT8ku5pnf0jT2yiNT9ke+Vk79Ke7TCPqUj+AU\r\n8htjOcAsRtRTJnQa15nytGTSRtQmQzGqVzg=\r\n=Tswy\r\n-----END PGP SIGNATURE-----\r\n", "size": 13475}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.1.1_1665567464573_0.20876003648435004"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-12T10:01:50.952Z"}, "5.1.2": {"name": "chalk", "version": "5.1.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.52.4", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "158bf4429ee5c40fd23d45b7d43e5cbbbdf6795e", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.1.2", "_nodeVersion": "16.16.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-E5CkT4jWURs1Vy5qGJye+XwCkNj7Od3Af7CP6SujMetSMkLs8Do2RWJK5yx1wamHV/op8Rz+9rltjaTQWDnEFQ==", "shasum": "d957f370038b75ac572471e83be4c5ca9f8e8c45", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.1.2.tgz", "fileCount": 12, "unpackedSize": 43787, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHW+BiaXhxpezvF8SWNxOCnLYZEqgpN7iUr+4sfRrJ1NAiEA45MmChdnALfbsbbqaI0RqSrU6koyuZjSI/FwkJrgCf4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRuv3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXDg//SHD6IFmJB0Dq/i5hFI0gxlnD9aTFqVEQAatIyxOJvXvEyS7M\r\n+JD3/dOU44ePZ4zH7aYT0YrUnY1oHpijO1elbyL4mhgLxauHuNpYmvgH7dyE\r\nuSKSbvCmvtB/lzeTZR5VDDvq/CYRZ4CTL3N9oZmRMV4b3yw6JGZVa7iTUYvS\r\nz5WdJ89aGNmtH8wvmR1HOfDQOeBEV438VpGhBp2lKy6WYYJVEJgddR/FutMd\r\nwJ4Ptm810A5bNMlhfCQRmjYwEgEz9EipavaM+sS6RWKxLGps+tvTRz1Ukgt5\r\n4yRaEjDpYywtB7ujl/dA2mXKjnJo92uLmirmQkSJHHjSJ8r+M8lx9y4CYdo7\r\nlpzHovkYnDacd3F9rKMZoFZZR7a/VWGCBQQM1P7xabPqCJV88Pyqv1jsjSrE\r\ne/mNaEqSUFomZG//23pavE3UAe2aCfX9V3aZay2rYDdeAmLebCAhxaNqUscL\r\nok0aOwSJG2c0c2RxceQl/7o27CY/5F5iJqg6Dcvk5MbuF0z5GIoMoIRYx4Rs\r\nouwEKeu+7ei4Du7qs55GsjQuSUdR52L5s/UtT1u/RUEvPgoonP33LSr7UxZD\r\nOuN8BdmCdD9MMfiZjf/RUJFrApOa4nCyWB4p9wwKTQc2DZ4TErJJIWwQxyz9\r\nKAkARhOYA8iaWDtpBRlVbeEI/xnbezSuqrA=\r\n=ibOO\r\n-----END PGP SIGNATURE-----\r\n", "size": 13472}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.1.2_1665592311613_0.6166299016608878"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-12T17:17:13.435Z"}, "5.2.0": {"name": "chalk", "version": "5.2.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.53.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "a370f468a43999e4397094ff5c3d17aadcc4860e", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.2.0", "_nodeVersion": "14.21.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-ree3Gqw/nazQAPuJJEy+avdl7QfZMcUvmHIKgEZkGL+xOBzRvup5Hxo6LHuMceSxOabuJLJm5Yp/92R9eMmMvA==", "shasum": "249623b7d66869c673699fb66d65723e54dfcfb3", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.2.0.tgz", "fileCount": 12, "unpackedSize": 43568, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGpkIV4DWbErlKu9VI8Xrd6zVKzVBcJP1P4uiwhcauWeAiBCaqZ8GWGM91SUt7da69Vy7lSbP1Qsfns18NVexta3Jw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkjEDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV7g//SkAHVpwaXi9OSemLPPViDVvDgWITSR6UOiBP2kt/IAYeuRw+\r\nBJPNm3iIrJPK55H5xFW8hOTasUdt6pltGY6lBLkNYytOGRKx6Hj4+6gMUbvI\r\nHhrRUb3IJyGAkxQeQ1yqSpjGpubi5T0Xe7q5WFgEm+0cibDPqOBBAl2/Y2mL\r\npHW5cL2zyzTo76TW6m33PjA51ima7eSlXx76ZBYE74NZ69mS30mkxGx7/KW4\r\nN83QnYpIvZi7cfFqcMQQhnD5LK9vm1bE9wbdVvwj3r6tFt+W97egZ3qiBU4g\r\nownwJUfj7tSP0Vi0XQeLhImIh9efdxqvRK5HJjaEWDQhnyAZIw9S22aesGD/\r\nwjL6ehZRawG7TAHvMh4KC/8Yv4Wubx5dJgvGuwOjgv1+45bt+r7xfKPvnBBb\r\nFt+s8YfMvGMv685020Ht9TGmH3DwypB5RltnZoOnmvBpWRfP93uTkLZiweek\r\nbRfzqfjK1kj8V4qqphCfJYU71d9s1q7A5fnRob+MTAJ1opRyK2v30du2D/P0\r\n3IBJ6fynWJUAMHyWg7BZRDfsNWN/pYkW1UP1fMy8V93dXBa2WGuMuVwpRBxK\r\nIzrXhc52uxi1jIZDaZ2RbhcdIxtWLy+5ikYx34nqdMFquh0WWkhXsus1t3A4\r\ndI5gxKg37xdA8NYh9kwQGmZ6HsjrmqMbKUs=\r\n=tvom\r\n-----END PGP SIGNATURE-----\r\n", "size": 13351}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.2.0_1670525187006_0.6054411598751666"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T19:09:27.470Z"}, "5.3.0": {"name": "chalk", "version": "5.3.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.53.0", "yoctodelay": "^2.0.0"}, "sideEffects": false, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off", "unicorn/expiring-todo-comments": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "gitHead": "72c742d4716b1f94bb24bbda86d96fbb247ca646", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_id": "chalk@5.3.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "shasum": "67c20a7ebef70e7f3970a01f90fa210cb6860385", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.3.0.tgz", "fileCount": 12, "unpackedSize": 43736, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7jtsJDO883o5OSOE4ygbH48k0Q4SciRc0MhlEjWvJRAiAi1pflMpbvv+4KrVGVN3ZhppLjF45dpLmLJ3dk1VtZug=="}], "size": 13397}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chalk_5.3.0_1688036291769_0.9519443644747436"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-29T10:58:11.887Z", "publish_time": 1688036291887, "_source_registry_name": "default"}, "5.4.0": {"name": "chalk", "version": "5.4.0", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "sideEffects": false, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.57.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off", "unicorn/expiring-todo-comments": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "_id": "chalk@5.4.0", "gitHead": "83acfcf8cb17437b63beceb027180399da74f0a7", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-ZkD35Mx92acjB2yNJgziGqT9oKHEOxjTBTDRpOsRWtdecL/0jM3z5kM/CTzHWvHIen1GvkM85p6TuFfDGfc8/Q==", "shasum": "846fdb5d5d939d6fa3d565cd5545697b6f8b6923", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.4.0.tgz", "fileCount": 12, "unpackedSize": 44129, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF9O9rt/0qVcL415fsimTeAtarR3ClkFa2x24fPx4VzsAiAyfeEfT32AN36Dl+1Wt4T8dyAGqZc0ktD3+hgIiSNnGQ=="}], "size": 13722}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/chalk_5.4.0_1734544841544_0.4959266765700201"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-18T18:00:41.792Z", "publish_time": 1734544841792, "_source_registry_name": "default"}, "5.4.1": {"name": "chalk", "version": "5.4.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "sideEffects": false, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.57.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off", "unicorn/expiring-todo-comments": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "_id": "chalk@5.4.1", "gitHead": "5dbc1e2633f3874f43c144fa4919934bc934c495", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==", "shasum": "1b48bf0963ec158dce2aacf69c093ae2dd2092d8", "tarball": "https://registry.npmmirror.com/chalk/-/chalk-5.4.1.tgz", "fileCount": 12, "unpackedSize": 44242, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ5wDikSytgziyeCEmOhw4VJznYEkpJZXRQgJr4o+SUQIhAK3MBAa2qCkGdIpM+MMML0rPG+R12h6M+pqFXKxYO9Yq"}], "size": 13753}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/chalk_5.4.1_1734800692821_0.33369100320867306"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T17:04:53.006Z", "publish_time": 1734800693006, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "_source_registry_name": "default"}