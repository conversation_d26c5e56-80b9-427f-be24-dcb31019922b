{"_attachments": {}, "_id": "cfb", "_rev": "71362-61f18537ab210505a88a95e3", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "dist-tags": {"latest": "1.2.2"}, "license": "Apache-2.0", "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "name": "cfb", "readme": "# Container File Blobs\n\nPure JS implementation of various container file formats, including ZIP and CFB.\n\n[![Build Status](https://travis-ci.org/SheetJS/js-cfb.svg?branch=master)](https://travis-ci.org/SheetJS/js-cfb)\n[![Coverage Status](http://img.shields.io/coveralls/SheetJS/js-cfb/master.svg)](https://coveralls.io/r/SheetJS/js-cfb?branch=master)\n[![Dependencies Status](https://david-dm.org/sheetjs/js-cfb/status.svg)](https://david-dm.org/sheetjs/js-cfb)\n[![NPM Downloads](https://img.shields.io/npm/dt/cfb.svg)](https://npmjs.org/package/cfb)\n[![Analytics](https://ga-beacon.appspot.com/***********-1/SheetJS/js-cfb?pixel)](https://github.com/SheetJS/js-cfb)\n\n## Installation\n\nIn the browser:\n\n```html\n<script src=\"dist/cfb.min.js\" type=\"text/javascript\"></script>\n```\n\nWith [npm](https://www.npmjs.org/package/cfb):\n\n```bash\n$ npm install cfb\n```\n\nThe `xlscfb.js` file is designed to be embedded in [js-xlsx](http://git.io/xlsx)\n\n\n## Library Usage\n\nIn node:\n\n```js\nvar CFB = require('cfb');\n```\n\nFor example, to get the Workbook content from an Excel 2003 XLS file:\n\n```js\nvar cfb = CFB.read(filename, {type: 'file'});\nvar workbook = CFB.find(cfb, 'Workbook');\nvar data = workbook.content;\n```\n\n\n## Command-Line Utility Usage\n\nThe [`cfb-cli`](https://www.npmjs.com/package/cfb-cli) module ships with a CLI\ntool for manipulating and inspecting supported files.\n\n\n## JS API\n\nTypeScript definitions are maintained in `types/index.d.ts`.\n\nThe CFB object exposes the following methods and properties:\n\n`CFB.parse(blob)` takes a nodejs Buffer or an array of bytes and returns an\nparsed representation of the data.\n\n`CFB.read(blob, opts)` wraps `parse`.\n\n`CFB.find(cfb, path)` performs a case-insensitive match for the path (or file\nname, if there are no slashes) and returns an entry object or null if not found.\n\n`CFB.write(cfb, opts)` generates a file based on the container.\n\n`CFB.writeFile(cfb, filename, opts)` creates a file with the specified name.\n\n### Parse Options\n\n`CFB.read` takes an options argument.  `opts.type` controls the behavior:\n\n| `type`     | expected input                                                  |\n|------------|:----------------------------------------------------------------|\n| `\"base64\"` | string: Base64 encoding of the file                             |\n| `\"binary\"` | string: binary string (byte `n` is `data.charCodeAt(n)`)        |\n| `\"buffer\"` | nodejs Buffer                                                   |\n| `\"file\"`   | string: path of file that will be read (nodejs only)            |\n| (default)  | buffer or array of 8-bit unsigned int (byte `n` is `data[n]`)   |\n\n\n### Write Options\n\n`CFB.write` and `CFB.writeFile` take options argument.\n\n`opts.type` controls the behavior:\n\n| `type`     | output                                                          |\n|------------|:----------------------------------------------------------------|\n| `\"base64\"` | string: Base64 encoding of the file                             |\n| `\"binary\"` | string: binary string (byte `n` is `data.charCodeAt(n)`)        |\n| `\"buffer\"` | nodejs Buffer                                                   |\n| `\"file\"`   | string: path of file that will be created (nodejs only)         |\n| (default)  | buffer if available, array of 8-bit unsigned int otherwise      |\n\n`opts.fileType` controls the output file type:\n\n| `fileType`         | output                  |\n|:-------------------|:------------------------|\n| `'cfb'` (default)  | CFB container           |\n| `'zip'`            | ZIP file                |\n| `'mad'`            | MIME aggregate document |\n\n`opts.compression` enables DEFLATE compression for ZIP file type.\n\n\n## Utility Functions\n\nThe utility functions are available in the `CFB.utils` object.  Functions that\naccept a `name` argument strictly deal with absolute file names:\n\n- `.cfb_new(?opts)` creates a new container object.\n- `.cfb_add(cfb, name, ?content, ?opts)` adds a new file to the `cfb`.\n  Set the option `{unsafe:true}` to skip existence checks (for bulk additions)\n- `.cfb_del(cfb, name)` deletes the specified file\n- `.cfb_mov(cfb, old_name, new_name)` moves the old file to new path and name\n- `.use_zlib(require(\"zlib\"))` loads a nodejs `zlib` instance.\n\nBy default, the library uses a pure JS inflate/deflate implementation.  NodeJS\n`zlib.InflateRaw` exposes the number of bytes read in versions after `8.11.0`.\nIf a supplied `zlib` does not support the required features, a warning will be\ndisplayed in the console and the pure JS fallback will be used.\n\n\n## Container Object Description\n\nThe objects returned by `parse` and `read` have the following properties:\n\n- `.FullPaths` is an array of the names of all of the streams (files) and\n  storages (directories) in the container.  The paths are properly prefixed from\n  the root entry (so the entries are unique)\n\n- `.FileIndex` is an array, in the same order as `.FullPaths`, whose values are\n  objects following the schema:\n\n```typescript\ninterface CFBEntry {\n  name: string; /** Case-sensitive internal name */\n  type: number; /** 1 = dir, 2 = file, 5 = root ; see [MS-CFB] 2.6.1 */\n  content: Buffer | number[] | Uint8Array; /** Raw Content */\n  ct?: Date; /** Creation Time */\n  mt?: Date; /** Modification Time */\n  ctype?: String; /** Content-Type (for MAD) */\n}\n```\n\n\n## License\n\nPlease consult the attached LICENSE file for details.  All rights not explicitly\ngranted by the Apache 2.0 License are reserved by the Original Author.\n\n\n## References\n\n - `MS-CFB`: Compound File Binary File Format\n - ZIP `APPNOTE.TXT`: .ZIP File Format Specification\n - RFC1951: https://www.ietf.org/rfc/rfc1951.txt\n - RFC2045: https://www.ietf.org/rfc/rfc2045.txt\n - RFC2557: https://www.ietf.org/rfc/rfc2557.txt\n\n", "time": {"created": "2022-01-26T17:30:31.376Z", "modified": "2023-07-28T06:37:26.302Z", "1.2.1": "2021-09-06T20:09:52.968Z", "1.2.0": "2020-07-09T06:26:47.267Z", "1.1.4": "2020-03-13T06:00:48.126Z", "1.1.3": "2019-08-04T20:09:50.781Z", "1.1.2": "2019-07-20T22:29:50.757Z", "1.1.1": "2019-06-10T12:49:20.094Z", "1.1.0": "2018-09-04T07:52:32.616Z", "1.0.8": "2018-07-08T08:24:36.609Z", "1.0.7": "2018-04-28T18:17:31.065Z", "1.0.6": "2018-04-09T06:47:54.280Z", "1.0.5": "2018-03-05T04:12:50.567Z", "1.0.4": "2018-02-15T00:19:58.186Z", "1.0.3": "2018-02-12T07:59:06.366Z", "1.0.2": "2018-01-19T04:23:02.144Z", "1.0.1": "2017-11-27T05:53:43.991Z", "1.0.0": "2017-11-05T17:05:24.672Z", "0.13.2": "2017-10-20T21:02:22.686Z", "0.13.1": "2017-09-21T00:10:13.698Z", "0.13.0": "2017-09-14T21:36:04.952Z", "0.12.1": "2017-08-09T07:14:54.250Z", "0.12.0": "2017-07-28T18:43:45.062Z", "0.11.1": "2017-03-30T21:52:16.952Z", "0.11.0": "2017-02-24T08:18:48.544Z", "0.10.3-a": "2017-02-24T07:27:33.502Z", "0.10.3": "2014-11-03T06:55:14.085Z", "0.10.2-a": "2014-11-03T05:53:30.747Z", "0.10.2": "2014-11-03T05:42:35.139Z", "0.10.1": "2014-07-05T16:22:01.148Z", "0.10.0": "2014-06-24T04:10:20.732Z", "0.9.1": "2014-05-20T17:45:14.630Z", "0.8.1": "2013-12-06T18:23:10.433Z", "1.2.2": "2022-04-06T06:59:33.572Z"}, "versions": {"1.2.1": {"name": "cfb", "version": "1.2.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0", "printj": "~1.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "1e70aae159e29df32d420f4242efb19edb05fc61", "_id": "cfb@1.2.1", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "209429e4c68efd30641f6fc74b2d6028bd202402", "size": 108366, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.2.1.tgz", "integrity": "sha512-wT2ScPAFGSVy7CY+aauMezZBnNrfnaLSrxHUHdea+Td/86vrk6ZquggV+ssBR88zNs0OnBkL2+lf9q0K+zVGzQ=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.2.1_1630958992811_0.7692838319103692"}, "_hasShrinkwrap": false, "publish_time": 1630958992968, "_cnpm_publish_time": 1630958992968, "_cnpmcore_publish_time": "2021-12-16T10:35:56.059Z"}, "1.2.0": {"name": "cfb", "version": "1.2.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "crc-32": "~1.2.0", "printj": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "76e4603fa5e693171d133877e96e692dc39fe9c6", "_id": "cfb@1.2.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "6a4d0872b525ed60349e1ef51fb4b0bf73eca9a8", "size": 102821, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.2.0.tgz", "integrity": "sha512-sXMvHsKCICVR3Naq+J556K+ExBo9n50iKl6LGarlnvuA2035uMlGA/qVrc0wQtow5P1vJEw9UyrKLCbtIKz+TQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.2.0_1594276007132_0.7777077650654309"}, "_hasShrinkwrap": false, "publish_time": 1594276007267, "_cnpm_publish_time": 1594276007267, "_cnpmcore_publish_time": "2021-12-16T10:35:56.349Z"}, "1.1.4": {"name": "cfb", "version": "1.1.4", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "commander": "^2.16.0", "crc-32": "~1.2.0", "printj": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7c1980741a3a07aa218721cee0f5355ffb143303", "_id": "cfb@1.1.4", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "81fd35ede4c919d8f0962a94582e1dfaf7051e2a", "size": 92510, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.1.4.tgz", "integrity": "sha512-rwFkl3aFO3f+ljR27YINwC0x8vPjyiEVbYbrTCKzspEf7Q++3THdfHVgJYNUbxNcupJECrLX+L40Mjm9hm/Bgw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.1.4_1584079247970_0.3540657374005258"}, "_hasShrinkwrap": false, "publish_time": 1584079248126, "_cnpm_publish_time": 1584079248126, "_cnpmcore_publish_time": "2021-12-16T10:35:56.876Z"}, "1.1.3": {"name": "cfb", "version": "1.1.3", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "commander": "^2.16.0", "crc-32": "~1.2.0", "printj": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "aff6d67444074f8f2d0689da89483a5f1815d89f", "_id": "cfb@1.1.3", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"shasum": "05de6816259c8e8bc32713aba905608ee385df66", "size": 88526, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.1.3.tgz", "integrity": "sha512-joXBW0nMuwV9no7UTMiyVJnQL6XIU3ThXVjFUDHgl9MpILPOomyfaGqC290VELZ48bbQKZXnQ81UT5HouTxHsw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.1.3_1564949390674_0.8966314322524249"}, "_hasShrinkwrap": false, "publish_time": 1564949390781, "_cnpm_publish_time": 1564949390781, "_cnpmcore_publish_time": "2021-12-16T10:35:57.175Z"}, "1.1.2": {"name": "cfb", "version": "1.1.2", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "commander": "^2.16.0", "crc-32": "~1.2.0", "printj": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8756cc9b4159e1b726222a3fc5d204d607e89fbf", "_id": "cfb@1.1.2", "_shasum": "5dfff9813eb37757597272dae3da3b37b3edcc44", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.16.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5dfff9813eb37757597272dae3da3b37b3edcc44", "size": 89157, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.1.2.tgz", "integrity": "sha512-FAEjGHtrAF027TFkhDmTDNbRf97C33jBXRa9ODeabZRqYDglsq/F5uRpJfV5BitCH4yiyplHNBKx2xOhQb12PA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.1.2_1563661790628_0.8833589119355454"}, "_hasShrinkwrap": false, "publish_time": 1563661790757, "_cnpm_publish_time": 1563661790757, "_cnpmcore_publish_time": "2021-12-16T10:35:57.467Z"}, "1.1.1": {"name": "cfb", "version": "1.1.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "commander": "^2.16.0", "crc-32": "~1.2.0", "printj": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8d85fb6e74e3b84e440a3a107b01350bc2b30464", "_id": "cfb@1.1.1", "_shasum": "7f399f4c541954715d0ced9b1cdecc52b554290a", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.16.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "7f399f4c541954715d0ced9b1cdecc52b554290a", "size": 89077, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.1.1.tgz", "integrity": "sha512-eov9L63s9/uMiiDvWCiTYbK3AcM5x5AYo4NL1q75G0AZBrD2X0+Lm9hubk94qy1MJP7pYFuM6/xn0h3grAvBXQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.1.1_1560170959927_0.21836682646790928"}, "_hasShrinkwrap": false, "publish_time": 1560170960094, "_cnpm_publish_time": 1560170960094, "_cnpmcore_publish_time": "2021-12-16T10:35:57.757Z"}, "1.1.0": {"name": "cfb", "version": "1.1.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "commander": "^2.16.0", "crc-32": "~1.2.0", "printj": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.9.0", "@types/node": "^8.10.25", "blanket": "~1.2.3", "dtslint": "~0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d9b99d68a767f1a1e6586e4932086e8cc8051018", "_id": "cfb@1.1.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "44fb1b30eee014fa5633a0ed5f26c87fd765799a", "size": 89412, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.1.0.tgz", "integrity": "sha512-ZqfxNGWTMKhd0a/n6YKJLq8hWbd5kR3cA4kXwUj9vVEdHlwJ09werR8gN15Z7Y1FTXqdD6dE3GGCxv4uc28raA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.1.0_1536047552468_0.304297610649628"}, "_hasShrinkwrap": false, "publish_time": 1536047552616, "_cnpm_publish_time": 1536047552616, "_cnpmcore_publish_time": "2021-12-16T10:35:58.086Z"}, "1.0.8": {"name": "cfb", "version": "1.0.8", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.14.1", "printj": "~1.1.2"}, "devDependencies": {"crc-32": "~1.2.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8ee792f34390bf0cde6282c2c66ce9c09a3818ce", "_id": "cfb@1.0.8", "_shasum": "77f213493d697d754fd9c0f5511eab5ad72d02cf", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "77f213493d697d754fd9c0f5511eab5ad72d02cf", "size": 45719, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.8.tgz", "integrity": "sha512-oA7VomcgZRWTo8V20UYLlXu4ZOCFEAfwwrcxE8PcVzXW12WOhsi38PVnymb6Xoj8y7ghoZQOOOVRBMdLJ4jCjg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.0.8_1531038276490_0.036468331535607135"}, "_hasShrinkwrap": false, "publish_time": 1531038276609, "_cnpm_publish_time": 1531038276609, "_cnpmcore_publish_time": "2021-12-16T10:35:58.366Z"}, "1.0.7": {"name": "cfb", "version": "1.0.7", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.14.1", "printj": "~1.1.2"}, "devDependencies": {"crc-32": "~1.2.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "c88a98104d77a5bd01339624b27317cbf593f0a0", "_id": "cfb@1.0.7", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "ccb615eb1bb0a039e7829ab0b2ad4ea564fbaa78", "size": 50515, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.7.tgz", "integrity": "sha512-KjjZFR+a/e8RDdDTr4PwR0P/HIFRI3sxArFQttml0pFkhIO4TnvS/1+dqtGXPqe5/0MHp2IzjFx1JTzmohHT+w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.0.7_1524939450972_0.05214823218277931"}, "_hasShrinkwrap": false, "publish_time": 1524939451065, "_cnpm_publish_time": 1524939451065, "_cnpmcore_publish_time": "2021-12-16T10:35:58.664Z"}, "1.0.6": {"name": "cfb", "version": "1.0.6", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.14.1", "printj": "~1.1.2"}, "devDependencies": {"crc-32": "~1.2.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "35d59c57bf881cb5df0c1413c8d6300bd78661cb", "_id": "cfb@1.0.6", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "d0107fa579a8d79c53a31074a755abcb898017f0", "size": 50786, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.6.tgz", "integrity": "sha512-HDWN8FEiZ5JbVZZqtlrR6K3aaC8FTO0benJrRtE93R2zY3nxNOb+Oda+fUnXFkibmY62+lUvb/Us/eDgkVWSZQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.0.6_1523256473974_0.35684267970395456"}, "_hasShrinkwrap": false, "publish_time": 1523256474280, "_cnpm_publish_time": 1523256474280, "_cnpmcore_publish_time": "2021-12-16T10:35:58.970Z"}, "1.0.5": {"name": "cfb", "version": "1.0.5", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.14.1", "printj": "~1.1.2"}, "devDependencies": {"crc-32": "~1.2.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "17b1a8de5a305342bd23cf02567df1fe99eff59b", "_id": "cfb@1.0.5", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5f7cf2fcb385dd41db271cf1f28a83fcd705bf06", "size": 50395, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.5.tgz", "integrity": "sha512-z1BN+JkopTE4vYu0sx25da2ZFurcN8gUKcBpT2ThCDlNFtWBozId8AHMs4OS7jTPLCJYK30Ud7QgcioyGkkkbg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.0.5_1520223170435_0.1627487819912985"}, "_hasShrinkwrap": false, "publish_time": 1520223170567, "_cnpm_publish_time": 1520223170567, "_cnpmcore_publish_time": "2021-12-16T10:35:59.735Z"}, "1.0.4": {"name": "cfb", "version": "1.0.4", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.12.1", "printj": "~1.1.1"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "98e9d2e64197d63d05053010207cb1d8ad2e55de", "_id": "cfb@1.0.4", "_shasum": "d7253209aa20c026fb83225725b677506d1ee4cb", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "d7253209aa20c026fb83225725b677506d1ee4cb", "size": 44615, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.4.tgz", "integrity": "sha512-Tg8BgDL31vUhp/0oiTXro1bmpJzV3AacF5P3EEcWqxQ+N803R6lDZz7lzxrZp6cM4mx7C4wPmJ07vIy3vLZrkw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.0.4_1518653997060_0.3173849094368122"}, "_hasShrinkwrap": false, "publish_time": 1518653998186, "_cnpm_publish_time": 1518653998186, "_cnpmcore_publish_time": "2021-12-16T10:36:00.024Z"}, "1.0.3": {"name": "cfb", "version": "1.0.3", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.12.1", "printj": "~1.1.1"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "25688e28fe0ed357ee0d974f48ad7a27afb1b77c", "_id": "cfb@1.0.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "3fd20b16f92b4dae8196363261f6a841b9732acf", "size": 48922, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.3.tgz", "integrity": "sha512-KJ6/+rU5ghC0p7YSpM9i4yyMJIAeHOMJ2VACMysGbI63MQxEcHX+Zc7TrFdSSUPxSjxxMlK3qhQiCGAiwyrguA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.0.3_1518422345396_0.13379696943027475"}, "_hasShrinkwrap": false, "publish_time": 1518422346366, "_cnpm_publish_time": 1518422346366, "_cnpmcore_publish_time": "2021-12-16T10:36:00.257Z"}, "1.0.2": {"name": "cfb", "version": "1.0.2", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.12.1", "printj": "~1.1.1"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b0e076f00311a2cee49ac0fb1178882e8bdef318", "_id": "cfb@1.0.2", "_shasum": "f51d9bbe5b5d1e75700581d68222f2ef46ec2142", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "f51d9bbe5b5d1e75700581d68222f2ef46ec2142", "size": 44540, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.2.tgz", "integrity": "sha512-jHaNYIzG/OzbBxwOsODZlusZtYCiJnyNSy544V3FMz00BBRI+mlpjDivCB/sdRKY1gCXpagde14yqoag5pfIcQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-1.0.2.tgz_1516335781896_0.13949824636802077"}, "directories": {}, "publish_time": 1516335782144, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516335782144, "_cnpmcore_publish_time": "2021-12-16T10:36:00.562Z"}, "1.0.1": {"name": "cfb", "version": "1.0.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "^2.12.1", "printj": "~1.1.0"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "~0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8cd01668f0c3ba1101b2e92f9bdd6c0cc15084e2", "_id": "cfb@1.0.1", "_shasum": "557bb24f67dae78803785a1595d365bc82c5fab4", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "557bb24f67dae78803785a1595d365bc82c5fab4", "size": 44322, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.1.tgz", "integrity": "sha512-R35kSFdb7MK8DD806sIELzfNt3zEg9c/FGSxwzN5f1/+YQdVNHGoCzTTPVa/cY7RFD98WZgHULjNByEiUrYuUg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-1.0.1.tgz_1511762022859_0.6434657925274223"}, "directories": {}, "publish_time": 1511762023991, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511762023991, "_cnpmcore_publish_time": "2021-12-16T10:36:00.821Z"}, "1.0.0": {"name": "cfb", "version": "1.0.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"printj": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "46719dfe3cbfc015ab0ebf71cfb961b8ec090d2a", "_id": "cfb@1.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "ab8be69563ce2cc1cb70819026d0c7c9b76d5c73", "size": 48582, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.0.0.tgz", "integrity": "sha512-nDzEYhfR9kqwcRrnAPQnPbisPorodhkJTkMS2QG12y9kHNXnF+8ByWFLyqxbVfry1jWuXlT1PCK11+MOS8uGKg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-1.0.0.tgz_1509901523532_0.7862392889801413"}, "directories": {}, "publish_time": 1509901524672, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509901524672, "_cnpmcore_publish_time": "2021-12-16T10:36:01.098Z"}, "0.13.2": {"name": "cfb", "version": "0.13.2", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"printj": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9e22a4425e1a51715de8f21ac1e6800cc5bf5c02", "_id": "cfb@0.13.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5c2cbf5a54d8561c14c34c9537730ec62bbafadf", "size": 45115, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.13.2.tgz", "integrity": "sha512-fn3HqoRFh2qJrU6F5iJh/npunXjSO7jSWmfo9QjQAUAuFXfowWKf46QNlPuN1uZodODHEWqc5M/Tf+wuyg9juQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-0.13.2.tgz_1508533341637_0.46940799057483673"}, "directories": {}, "publish_time": 1508533342686, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508533342686, "_cnpmcore_publish_time": "2021-12-16T10:36:01.403Z"}, "0.13.1": {"name": "cfb", "version": "0.13.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"printj": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "cfb@0.13.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "6506e0ab9b6846be7ef25c758932fe16f64c9c71", "size": 42286, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.13.1.tgz", "integrity": "sha512-XG+CMeWTJo1clHKxxtMjXq8ttBdpueXr5MLakYkyCf+sc8Va4PD0bt2YhGsGwVr0vS8sHDDbLC8TXIF5dD2kEA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-0.13.1.tgz_1505952612636_0.49062300380319357"}, "directories": {}, "publish_time": 1505952613698, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505952613698, "_cnpmcore_publish_time": "2021-12-16T10:36:01.736Z"}, "0.13.0": {"name": "cfb", "version": "0.13.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"printj": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"crc-32": "~1.1.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d784f9b1f2b2cd62cfe2a317debedbb0f13f02ad", "_id": "cfb@0.13.0", "_shasum": "47e9b79eb6eaa256181d0c09ee78d4604b64d166", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "47e9b79eb6eaa256181d0c09ee78d4604b64d166", "size": 41607, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.13.0.tgz", "integrity": "sha512-jUnzeGp/1PeEwiWCPnWamumRTiO+mO2WX5Ql0spbTP8UBpAWy3ZIbTcWazd8pp08tilx5AEjHD0tWtYMzuJnZg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-0.13.0.tgz_1505424963969_0.907111007720232"}, "directories": {}, "publish_time": 1505424964952, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505424964952, "_cnpmcore_publish_time": "2021-12-16T10:36:02.431Z"}, "0.12.1": {"name": "cfb", "version": "0.12.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"printj": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "2d0c71ac88689b645f7fb6b21d58b794bbd4db2a", "_id": "cfb@0.12.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "57fadfa9fc31409b2ca58a4109ecaaac5ad1a4ec", "size": 20454, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.12.1.tgz", "integrity": "sha512-cP+4A0tTqtyza5gJwNwDetZ8FPjl0gPLE7mIxGKyUzOS6HkM23WaAWW/l3t7jIQSMqVXroa09Ey0lo7gV8LNxw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-0.12.1.tgz_1502262893236_0.067834249464795"}, "directories": {}, "publish_time": 1502262894250, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502262894250, "_cnpmcore_publish_time": "2021-12-16T10:36:02.649Z"}, "0.12.0": {"name": "cfb", "version": "0.12.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "bin/", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "17f35153f81e6380bd8f3cbd7a11dfebe4b563de", "_id": "cfb@0.12.0", "_shasum": "0db23660eba7a4d21d3e34974b95816e92da6c22", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "0db23660eba7a4d21d3e34974b95816e92da6c22", "size": 19601, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.12.0.tgz", "integrity": "sha512-sLjZ+h2dUC/Wru7kYtz4IIDVXBJEg0w+F+wbAcMAwu0dmjUUH8p3lMno2GnAPx6/5ma12DrbOGVedmf5wTaUeg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb-0.12.0.tgz_1501267424052_0.574402411468327"}, "directories": {}, "publish_time": 1501267425062, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501267425062, "_cnpmcore_publish_time": "2021-12-16T10:36:02.886Z"}, "0.11.1": {"name": "cfb", "version": "0.11.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js", "xlscfb.flow.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "16eebaeab45fcf8d4d68a99d56f32337e9329116", "_id": "cfb@0.11.1", "_shasum": "a96db8f272a6c3fb99dbbb23ef41223f48be1ea7", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "a96db8f272a6c3fb99dbbb23ef41223f48be1ea7", "size": 17445, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.11.1.tgz", "integrity": "sha512-1GEqpcO365hTRpP+GzHXNiUF5SB7qmY5aVYwrJm8ISx27HzHpaFlTQhnOCMNhqP0WPkHR0OGE9WDSqtksV4anw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cfb-0.11.1.tgz_1490910735008_0.5643791323527694"}, "directories": {}, "publish_time": 1490910736952, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490910736952, "_cnpmcore_publish_time": "2021-12-16T10:36:03.162Z"}, "0.11.0": {"name": "cfb", "version": "0.11.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js", "xlscfb.flow.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b5c5f05e32f41e24af5c080baa2ee374c9200adf", "homepage": "https://github.com/SheetJS/js-cfb#readme", "_id": "cfb@0.11.0", "_shasum": "3ab793e72ebaf16a4b8704133b6bed5a355ea6e2", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "3ab793e72ebaf16a4b8704133b6bed5a355ea6e2", "size": 17330, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.11.0.tgz", "integrity": "sha512-ZMeKF+a9h3haO+rwXNTM+xE0a6KdYD4HHmQgxm/NV2TYRc8xFsFgLy1ptvlqYhDLlcnDwLtyidHhV40GWlEsuw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cfb-0.11.0.tgz_1487924324036_0.08157537039369345"}, "directories": {}, "publish_time": 1487924328544, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487924328544, "_cnpmcore_publish_time": "2021-12-16T10:36:03.376Z"}, "0.10.3-a": {"name": "cfb", "version": "0.10.3-a", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js", "xlscfb.flow.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "87677f0ae0274249040ff5fbafa6317ed1c0612b", "homepage": "https://github.com/SheetJS/js-cfb#readme", "_id": "cfb@0.10.3-a", "_shasum": "75265d682fe2e99e9bb817957d3aafd93baef326", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "0.12.18", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "75265d682fe2e99e9bb817957d3aafd93baef326", "size": 17334, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.10.3-a.tgz", "integrity": "sha512-WPZnPdlUDHZ5XfLZh25URbC52R3TwP93FFp19fh8ihHcUGHhLBQmOQ7+l18dopLqgFf8GSfWFpzQJiPyAP47OA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cfb-0.10.3-a.tgz_1487921251515_0.6927942216861993"}, "directories": {}, "publish_time": 1487921253502, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487921253502, "_cnpmcore_publish_time": "2021-12-16T10:36:03.663Z"}, "0.10.3": {"name": "cfb", "version": "0.10.3", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "0ef16e1ad15de6a4ff7ded45e15391eca18917e0", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.10.3", "_shasum": "3b0be7151d09ba028894aa25f8eb83f0dbbe8ad9", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "3b0be7151d09ba028894aa25f8eb83f0dbbe8ad9", "size": 15608, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.10.3.tgz", "integrity": "sha512-JH52FmQ03o2Fst5z0GjRd6eHDkPwInJZP32bf42W3YWeaBTpiyY1scFe3DItSF5XJ8mCLWe7PelHrUsZlFzmZQ=="}, "directories": {}, "publish_time": 1414997714085, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414997714085, "_cnpmcore_publish_time": "2021-12-16T10:36:03.958Z"}, "0.10.2-a": {"name": "cfb", "version": "0.10.2-a", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb.njs"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "0ef16e1ad15de6a4ff7ded45e15391eca18917e0", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.10.2-a", "_shasum": "18b4bb451a8ec986d60c1fb7c819175fcd3f4732", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "18b4bb451a8ec986d60c1fb7c819175fcd3f4732", "size": 15536, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.10.2-a.tgz", "integrity": "sha512-nA4E63DKfaNyOHXd2mQHQrbScGR4unO5RzL87hPDDA4ISOZWMZ5oIfIuimALALfimzj4AN2SK/5cKAiX0eEH3w=="}, "directories": {}, "publish_time": 1414994010747, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414994010747, "_cnpmcore_publish_time": "2021-12-16T10:36:04.156Z"}, "0.10.2": {"name": "cfb", "version": "0.10.2", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "fcc05e3567a233e7d1f3275ae05bab6752ac23ab", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.10.2", "_shasum": "562629137afc8046c77b90b68d76201cbc6671b0", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "562629137afc8046c77b90b68d76201cbc6671b0", "size": 15530, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.10.2.tgz", "integrity": "sha512-Xh3USBJ1wt/mMheK7nmy4Bd8Uo9TUz9rnnCKPGSsU4ufmNYw15dQI92EbNnONXwgAzqrvaSoiq+RQY2BdNGI6Q=="}, "directories": {}, "publish_time": 1414993355139, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414993355139, "_cnpmcore_publish_time": "2021-12-16T10:36:04.641Z"}, "0.10.1": {"name": "cfb", "version": "0.10.1", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb"}, "main": "./cfb", "files": ["LICENSE", "README.md", "bin/", "dist/", "cfb.js"], "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "597de28cf55d7b880b95be08d71ec525dbf38a68", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.10.1", "_shasum": "1719ed00431c6106d16df5d80f965aaca2c4f4c6", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "1719ed00431c6106d16df5d80f965aaca2c4f4c6", "size": 14826, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.10.1.tgz", "integrity": "sha512-XN61KZfEpxI6e31PfoQ1xh35zc5QrZQR5qpRDmVEUG0sUps9hNwaHIlUuVAubdtF0UfAmu2ICnFaSfy9ud/CYA=="}, "directories": {}, "publish_time": 1404577321148, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404577321148, "_cnpmcore_publish_time": "2021-12-16T10:36:04.833Z"}, "0.10.0": {"name": "cfb", "version": "0.10.0", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb"}, "main": "./cfb", "dependencies": {"commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test"}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5c7df1f3dbde33a145774b4e5c804186f159f2f4", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.10.0", "_shasum": "06cd1e7bfe753fe12cb775df688bf74ea346b78a", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "06cd1e7bfe753fe12cb775df688bf74ea346b78a", "size": 25829, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.10.0.tgz", "integrity": "sha512-U+BQF2PA/0aRI18211aIA3Qw8JWl2gDQ7S1cMPWLiwMzIL1dgYSQ+BFkrKAi4sxgjBfE2zwWM4FcXg3gRvbAjQ=="}, "directories": {}, "publish_time": 1403583020732, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403583020732, "_cnpmcore_publish_time": "2021-12-16T10:36:05.114Z"}, "0.9.1": {"name": "cfb", "version": "0.9.1", "author": {"name": "SheetJS"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb"}, "main": "./cfb", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"test": "make test"}, "dependencies": {"commander": ""}, "config": {"blanket": {"pattern": "cfb.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache 2.0", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.9.1", "dist": {"shasum": "746ecedd2e011fcec25d862530d49de57c4b3f72", "size": 22976, "noattachment": false, "tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.9.1.tgz", "integrity": "sha512-zlNgrFREA5cSHMmfvpfZGQ74J51lVgt3ZDPiGFnYOr3InZnOrc1ZNRxxelAQoaVtMHJVx4NRApftC3ZE05xNYA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1400607914630, "_hasShrinkwrap": false, "_cnpm_publish_time": 1400607914630, "_cnpmcore_publish_time": "2021-12-16T10:36:05.335Z"}, "0.8.1": {"name": "cfb", "version": "0.8.1", "author": {"name": "SheetJS"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "bin": {"cfb": "./bin/cfb"}, "main": "./cfb", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"test": "make test"}, "dependencies": {"commander": ""}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache 2.0", "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/js-cfb", "_id": "cfb@0.8.1", "dist": {"tarball": "https://registry.npmmirror.com/cfb/-/cfb-0.8.1.tgz", "shasum": "efe46440d96157f7fd917f754c9ca62e99a9abe2", "size": 11588, "noattachment": false, "integrity": "sha512-/Ou1PV0vuroSSYkmadPWH3kZowy4RhRHgL8mUQvv3TvgWcG+VEovt/Mn1er+VGzHSwtEoBQ4y+kxh4Dz0QdJ5A=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386354190433, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386354190433, "_cnpmcore_publish_time": "2021-12-16T10:36:05.754Z"}, "1.2.2": {"name": "cfb", "version": "1.2.2", "author": {"name": "sheetjs"}, "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.10.25", "acorn": "7.4.1", "alex": "8.1.1", "blanket": "~1.2.3", "dtslint": "~0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jscs": "3.0.7", "jshint": "2.13.4", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "ad93f76a36f893362efe96f738a327c290c1f2a9", "_id": "cfb@1.2.2", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==", "shasum": "94e687628c700e5155436dac05f74e08df23bc44", "tarball": "https://registry.npmmirror.com/cfb/-/cfb-1.2.2.tgz", "fileCount": 12, "unpackedSize": 370889, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+8+bGCnSPdmRl4813s3nGxrjN2o5wiQKsb2J0BqMBvgIgQgdwaQlQu1MQfAf1f1x45wugrrNpjhe6sHjNpBZiUlw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTTpVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Tg/+MfFDC972AbMjcNWIO3lWUeVU4pUjkNPaVi4qGL0Y1nQU2oi+\r\nKdmvRO7zYiN83HGOTl6/HGAZkHMUJW15lYsz23OVLOU2Ig/oVQmVx8dCaYVA\r\nXCEHPtZVugFgEsTtacj7FUCgU2cl5sfYoL3K3U8Ldilhn4ZcZXtC/zb84aQa\r\n1EmKf3eQx+pd5RvSOgyxEtZg/2yoHrRPt4UQdcNpGzI2/LkxHI6jqGMKwGb0\r\nYUyDOtAMvJkjOUv+vAL2g8s5vGXMUgypuOeLEOjld9D7ekXytV9AOfoknAMA\r\nlriTaxh3fpkXFfr7gKbr6i3e3Mvw6aur5o4A4AovayLj6VD9ZyRpldTO1pSQ\r\nTgxjlMTTbTLUx/VNeJvbmKV1LoXKa3Hh7OOq0LpEExrbFfe18ZBzoTAWTibQ\r\nFCw3ftHxXH+g0h+DrFDeFvpUoZlVSdZIkPbEleYXRWt+Fpe/+TVch4+vIs/L\r\nV11im8tWG6BGhylRmPsPmW5JeR9Ljg2BGQp8y3drYMzvmXDnxeOHflnH+k/y\r\na1RtLuL84VOhUUnXwwfCW9wHpzRA5YeNesWyGwhgdE6+VFgN4eIpbpOPAGYP\r\nw21iEYZvXfMr1nND9kZG0dGLho1OEq2eyyPMK3+JwIxnig0ywUc2xnPmNmj5\r\nMp+xN1yVkqVJEbdp1m3BCi4+fhj4PudP0XU=\r\n=jt3i\r\n-----END PGP SIGNATURE-----\r\n", "size": 109812}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cfb_1.2.2_1649228373447_0.24938194638013766"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-06T12:33:45.821Z"}}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "homepage": "http://sheetjs.com/", "keywords": ["cfb", "compression", "office"], "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "_source_registry_name": "default"}