import axios from 'axios';

// 创建axios实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_URL, // 从环境变量获取API基础URL
  timeout: 10000, // 设置超时时间为10秒
});

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bear<PERSON> ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    // 处理全局错误
    if (error.response && error.response.status === 401) {
      // 处理未授权的情况，例如跳转到登录页面
      window.location.href = '/login';
    } else if (error.response && error.response.status === 500) {
      console.error('服务器内部错误:', error);
      alert('服务器内部错误，请稍后再试');
    }
    return Promise.reject(error);
  }
);

// 用户相关API
export const login = (username, password) => {
  return apiClient.post('/users/login', {
    username,
    password,
  });
};

export const logout = () => {
  return apiClient.post('/users/logout');
};

// 获取用户信息
export const getUserInfo = () => {
  return apiClient.get('/users/info');
};

// 修改密码
export const changePassword = (oldPassword, newPassword) => {
  return apiClient.post('/users/change-password', {
    oldPassword,
    newPassword,
  });
};
