{"_attachments": {}, "_id": "ssf", "_rev": "71366-61f18537bc29700608411313", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "dist-tags": {"latest": "0.11.2"}, "license": "Apache-2.0", "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "name": "ssf", "readme": "# [SheetJS SSF](http://sheetjs.com)\n\nssf (SpreadSheet Format) is a pure JS library to format data using ECMA-376\nspreadsheet format codes (used in popular spreadsheet software packages).\n\nThis is the community version.  We also offer a pro version with additional\nfeatures like international support as well as dedicated support.\n\n## Installation\n\nWith [npm](https://www.npmjs.org/package/ssf):\n\n```bash\n$ npm install ssf\n```\n\nIn the browser:\n\n```html\n<script src=\"ssf.js\"></script>\n```\n\nThe browser exposes a variable `SSF`\n\nWhen installed globally, npm installs a script `ssf` that renders the format\nstring with the given arguments.  Running the script with `-h` displays help.\n\nThe script will manipulate `module.exports` if available .  This is not always\ndesirable.  To prevent the behavior, define `DO_NOT_EXPORT_SSF`.\n\n## Usage\n\n`SSF.format(fmt, val, opts)` formats `val` using the format `fmt`.\n\nIf `fmt` is a string, it will be parsed and evaluated.  If `fmt` is a `number`,\nthe actual format will be the corresponding entry in the internal format table.\nFor a raw numeric format like `000`, the value should be passed as a string.\n\nDate arguments are interpreted in the local time of the JS client.\n\nThe options argument may contain the following keys:\n\n| Option Name | Default | Description                                          |\n| :---------- | :-----: | :--------------------------------------------------- |\n| `date1904`  | false   | Use 1904 date system if true, 1900 system if false   |\n\n### Manipulating the Internal Format Table\n\nBinary spreadsheet formats store cell formats in a table and reference by index.\nThis library uses a global table:\n\n`SSF._table` is the underlying object, mapping numeric keys to format strings.\n\n`SSF.load(fmt:string, idx:?number):number` assigns the format to the specified\nindex and returns the index.  If the index is not specified, SSF will search the\nspace for an available format slot pick an unused slot.  For compatibility with\nthe XLS and XLSB file formats, custom indices should be in the valid ranges\n`5-8`, `23-26`, `41-44`, `63-66`, `164-382` (see `[MS-XLSB] 2.4.655 BrtFmt`)\n\n`SSF.get_table()` gets the internal format table (number to format mapping).\n\n`SSF.load_table(table)` sets the internal format table.\n\n### Other Utilities\n\n`SSF.parse_date_code(val:number, opts:?any)` parses `val`, returning an object:\n\n```typescript\ntype SSFDate = {\n  D:number; /* number of whole days since relevant epoch, 0 <= D */\n  y:number; /* integral year portion, epoch_year <= y */\n  m:number; /* integral month portion, 1 <= m <= 12 */\n  d:number; /* integral day portion, subject to gregorian YMD constraints */\n  q:number; /* integral day of week (0=Sunday .. 6=Saturday) 0 <= q <= 6 */\n\n  T:number; /* number of seconds since midnight, 0 <= T < 86400 */\n  H:number; /* integral number of hours since midnight, 0 <= H < 24 */\n  M:number; /* integral number of minutes since the last hour, 0 <= M < 60 */\n  S:number; /* integral number of seconds since the last minute, 0 <= S < 60 */\n  u:number; /* sub-second part of time, 0 <= u < 1 */\n}\n```\n\n`SSF.is_date(fmt:string):boolean` returns `true` if `fmt` encodes a date format.\n\n## Examples\n\n- [Basic Demo](http://oss.sheetjs.com/ssf/)\n- [Custom Formats Builder](https://customformats.com)\n\n## Related Packages\n\n[`ssf-cli`](https://www.npmjs.com/package/ssf-cli) is a simple NodeJS command\nline tool for formatting numbers.\n\n## License\n\nPlease consult the attached LICENSE file for details.  All rights not explicitly\ngranted by the Apache 2.0 license are reserved by the Original Author.\n\n## References\n\n - `ECMA-376`: Office Open XML File Formats\n - `MS-XLS`: Excel Binary File Format (.xls) Structure Specification\n - `MS-XLSB`: Excel (.xlsb) Binary File Format\n\n## Badges\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/ssfjs.svg)](https://saucelabs.com/u/ssfjs)\n\n[![Build Status](https://travis-ci.org/SheetJS/ssf.svg?branch=master)](https://travis-ci.org/SheetJS/ssf)\n\n[![Coverage Status](http://img.shields.io/coveralls/SheetJS/ssf/master.svg)](https://coveralls.io/r/SheetJS/ssf?branch=master)\n\n[![NPM Downloads](https://img.shields.io/npm/dt/ssf.svg)](https://npmjs.org/package/ssf)\n\n[![Dependencies Status](https://david-dm.org/sheetjs/ssf/status.svg)](https://david-dm.org/sheetjs/ssf)\n\n[![Analytics](https://ga-beacon.appspot.com/***********-1/SheetJS/ssf?pixel)](https://github.com/SheetJS/ssf)\n", "time": {"created": "2022-01-26T17:30:31.703Z", "modified": "2023-07-27T22:05:08.482Z", "0.11.2": "2020-06-27T22:56:33.923Z", "0.11.1": "2020-06-17T05:36:02.573Z", "0.11.0": "2020-05-09T05:00:34.894Z", "0.10.3": "2020-03-08T22:34:15.977Z", "0.10.2": "2018-02-21T03:50:41.613Z", "0.10.1": "2017-08-01T03:04:47.559Z", "0.10.0": "2017-07-28T21:16:49.148Z", "0.9.4": "2017-06-09T01:35:43.513Z", "0.9.3": "2017-05-16T19:50:28.122Z", "0.9.2": "2017-05-08T04:33:32.625Z", "0.9.1": "2017-04-30T06:55:42.972Z", "0.9.0": "2017-03-21T07:55:13.533Z", "0.8.2": "2014-07-05T17:08:18.534Z", "0.8.1": "2014-06-25T00:18:35.547Z", "0.8.0": "2014-06-13T18:09:44.942Z", "0.7.1": "2014-06-05T00:30:14.713Z", "0.7.0": "2014-05-22T04:43:50.929Z", "0.6.5": "2014-04-25T18:28:07.834Z", "0.6.4": "2014-04-03T16:36:52.440Z", "0.6.3": "2014-04-03T05:44:24.967Z", "0.6.2": "2014-03-29T09:46:32.777Z", "0.6.1": "2014-03-28T21:37:42.194Z", "0.6.0": "2014-03-27T19:52:29.706Z", "0.5.11": "2014-03-25T08:50:36.200Z", "0.5.10": "2014-03-25T07:46:13.348Z", "0.5.9": "2014-03-19T04:07:59.717Z", "0.5.8": "2014-02-17T08:31:30.337Z", "0.5.7": "2014-02-11T19:30:04.180Z", "0.5.6": "2014-02-11T05:47:15.453Z", "0.5.5": "2014-01-30T01:44:22.141Z", "0.5.4": "2014-01-29T00:53:36.684Z", "0.5.3": "2014-01-23T04:26:24.805Z", "0.5.2": "2014-01-20T08:43:57.055Z", "0.5.1": "2014-01-12T08:34:47.139Z", "0.5.0": "2014-01-10T19:11:22.662Z", "0.4.1": "2013-12-31T16:39:14.888Z", "0.4.0": "2013-12-26T23:14:24.719Z", "0.3.1": "2013-12-17T00:03:17.944Z"}, "versions": {"0.11.2": {"name": "ssf", "version": "0.11.2", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "./ssf", "types": "types", "dependencies": {"frac": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "ssf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b00ee9b286e58e0e066b498da47ceca41790a306", "_id": "ssf@0.11.2", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "0b99698b237548d088fc43cdf2b70c1a7512c06c", "size": 27805, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz", "integrity": "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf_0.11.2_1593298593765_0.6117465155084638"}, "_hasShrinkwrap": false, "publish_time": 1593298593923, "_cnpm_publish_time": 1593298593923, "_cnpmcore_publish_time": "2021-12-16T10:35:59.140Z"}, "0.11.1": {"name": "ssf", "version": "0.11.1", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "bin/ssf.njs"}, "main": "./ssf", "types": "types", "dependencies": {"frac": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "ssf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "885b27fda56249e3b859c48176874d434788cdd2", "_id": "ssf@0.11.1", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "da48e57d0485e4b24c206fa34ff029c32a8f9e99", "size": 54983, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.11.1.tgz", "integrity": "sha512-98w1+VzON56fN2sZUoFUDKjmac+qxWKOeuS6LY0sGyHci5gK9seooyPwZqkTNhBtxCHdnmfOb2nOoEnBavmgVA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf_0.11.1_1592372162370_0.6500793924988224"}, "_hasShrinkwrap": false, "publish_time": 1592372162573, "_cnpm_publish_time": 1592372162573, "_cnpmcore_publish_time": "2021-12-16T10:35:59.488Z"}, "0.11.0": {"name": "ssf", "version": "0.11.0", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "bin/ssf.njs"}, "main": "./ssf", "types": "types", "dependencies": {"frac": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "ssf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b00f11ce336a214a3c43037276058247ed7cb62b", "_id": "ssf@0.11.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "a2aba8514358898f80ca88e29da6ce321c9fc75a", "size": 52466, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.11.0.tgz", "integrity": "sha512-+0aEdWvTqCD7zSgqFld5zI8s/EGmg5Uupwo8MfWOweXChY3A9TRQeEmaVRQsjejgn3zAAvBX5ec65dDEUN9Y+g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf_0.11.0_1589000434787_0.757786384484028"}, "_hasShrinkwrap": false, "publish_time": 1589000434894, "_cnpm_publish_time": 1589000434894, "_cnpmcore_publish_time": "2021-12-16T10:35:59.806Z"}, "0.10.3": {"name": "ssf", "version": "0.10.3", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "bin/ssf.njs"}, "main": "./ssf", "types": "types", "dependencies": {"frac": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "ssf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9eaba424933df3fc860dd6978584ba13a6d8e30a", "_id": "ssf@0.10.3", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "8eae1fc29c90a552e7921208f81892d6f77acb2b", "size": 24365, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.10.3.tgz", "integrity": "sha512-pRuUdW0WwyB2doSqqjWyzwCD6PkfxpHAHdZp39K3dp/Hq7f+xfMwNAWIi16DyrRg4gg9c/RvLYkJTSawTPTm1w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf_0.10.3_1583706855814_0.3671887995134224"}, "_hasShrinkwrap": false, "publish_time": 1583706855977, "_cnpm_publish_time": 1583706855977, "_cnpmcore_publish_time": "2021-12-16T10:36:00.050Z"}, "0.10.2": {"name": "ssf", "version": "0.10.2", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "types": "types", "dependencies": {"frac": "~1.1.2"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f6de1799c41c7db90ff830cc898e20a0c96306f5", "_id": "ssf@0.10.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "65b2b4fcdfd967bc8e8383a41349009893115976", "size": 22828, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.10.2.tgz", "integrity": "sha512-rDhAPm9WyIsY8eZEKyE8Qsotb3j/wBdvMWBUsOhJdfhKGLfQidRjiBUV0y/MkyCLiXQ38FG6LWW/VYUtqlIDZQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf_0.10.2_1519185041552_0.4503359431453198"}, "_hasShrinkwrap": false, "publish_time": 1519185041613, "_cnpm_publish_time": 1519185041613, "_cnpmcore_publish_time": "2021-12-16T10:36:00.436Z"}, "0.10.1": {"name": "ssf", "version": "0.10.1", "author": {"name": "sheetjs"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"frac": "~1.1.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "71f827c4faa89475996acb2458161bac2bbd4280", "_id": "ssf@0.10.1", "_shasum": "f23d82b63792ef56089089c1cd0c848e911cdba6", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "f23d82b63792ef56089089c1cd0c848e911cdba6", "size": 21994, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.10.1.tgz", "integrity": "sha512-aujMSKx9hILS7zenrlR06TJ1wEzpBgJJH08Tr2+U7cFjVMUg0ezm3JuuNejxMeudqyT7StVv9hrt2gBINGUQ0g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf-0.10.1.tgz_1501556686407_0.928666056599468"}, "directories": {}, "publish_time": 1501556687559, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501556687559, "_cnpmcore_publish_time": "2021-12-16T10:36:00.725Z"}, "0.10.0": {"name": "ssf", "version": "0.10.0", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"voc": "~1.0.0", "frac": "~1.0.6"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "80c1a0fec756eec3aac637791843ccdef6cc1c1a", "_id": "ssf@0.10.0", "_shasum": "772d4fe3e8c43639992d789f132c57b2763d79e6", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "772d4fe3e8c43639992d789f132c57b2763d79e6", "size": 19904, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.10.0.tgz", "integrity": "sha512-7Ru9sqPapIjJG/XiYvwfSCN4myxVquUclRDnCJB5AOcBZqPpzelHmFCARWLv1a8BwwxAZbOG57Ec54bsv5a81A=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf-0.10.0.tgz_1501276608027_0.27288642758503556"}, "directories": {}, "publish_time": 1501276609148, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501276609148, "_cnpmcore_publish_time": "2021-12-16T10:36:00.934Z"}, "0.9.4": {"name": "ssf", "version": "0.9.4", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "~1.0.6"}, "devDependencies": {"mocha": "", "@sheetjs/uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "aa25491492df4a2f681684eb9f3d4d1a4343768b", "_id": "ssf@0.9.4", "_shasum": "8e57a98c19dbbf1edd53f0f8c9e7fd524b0f6c9c", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "8e57a98c19dbbf1edd53f0f8c9e7fd524b0f6c9c", "size": 19228, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.9.4.tgz", "integrity": "sha512-PGlTb3r+W8MneGKfN5B+5TFcaA4HUQcpYrVvTW+gMiM5dsqv1Y5h47OQu0okhVLzuQSRoZQbrP7g6zEcP+UrTg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ssf-0.9.4.tgz_1496972142470_0.2801917064934969"}, "directories": {}, "publish_time": 1496972143513, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496972143513, "_cnpmcore_publish_time": "2021-12-16T10:36:01.261Z"}, "0.9.3": {"name": "ssf", "version": "0.9.3", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "~1.0.6"}, "devDependencies": {"mocha": "", "@sheetjs/uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5bcd966b227c1ab3ff23b3b118586759c19c0629", "_id": "ssf@0.9.3", "_shasum": "42da2bdf99fcbde1b62b3ca58f00aa1f097a764e", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "42da2bdf99fcbde1b62b3ca58f00aa1f097a764e", "size": 19222, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.9.3.tgz", "integrity": "sha512-K+h3KK5gSnYo6TSBfVKv2P0EyIuujJL/LADZFm8YgBkxTepa3sfXZkK6y94p3iUxG4BA4KG64RIHXhWaP37Ldg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ssf-0.9.3.tgz_1494964226468_0.6785808934364468"}, "directories": {}, "publish_time": 1494964228122, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494964228122, "_cnpmcore_publish_time": "2021-12-16T10:36:01.582Z"}, "0.9.2": {"name": "ssf", "version": "0.9.2", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "~1.0.6"}, "devDependencies": {"mocha": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "ssf@0.9.2", "_shasum": "b1969526cffef0d30b467be0b0bd3b3174b3722d", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "b1969526cffef0d30b467be0b0bd3b3174b3722d", "size": 34000, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.9.2.tgz", "integrity": "sha512-gkPHdo9Yx/hGugRe4KZ1MkD8TKvvoETWoKyx1FxdjOFxluCCtFmKjwPotB9s8keaqp8FbC0mZnPwM9/J7NboVQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ssf-0.9.2.tgz_1494218010556_0.7907115654088557"}, "directories": {}, "publish_time": 1494218012625, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494218012625, "_cnpmcore_publish_time": "2021-12-16T10:36:01.869Z"}, "0.9.1": {"name": "ssf", "version": "0.9.1", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "~1.0.6"}, "devDependencies": {"mocha": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "ssf@0.9.1", "_shasum": "38a3d1619837a2b5b7bf754375c7d3a9ee4bc99a", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "38a3d1619837a2b5b7bf754375c7d3a9ee4bc99a", "size": 33762, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.9.1.tgz", "integrity": "sha512-dJ9nWdb/0l1Sz8OUVBOJlw5XorwHDiWuP9HThpuow5b0ijQWDpLMeGeWvIZHxCFCTvGM2jmmCbPYQnSwnG8TlQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ssf-0.9.1.tgz_1493535342310_0.7422827633563429"}, "directories": {}, "publish_time": 1493535342972, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493535342972, "_cnpmcore_publish_time": "2021-12-16T10:36:02.098Z"}, "0.9.0": {"name": "ssf", "version": "0.9.0", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "./ssf", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "0.3.1"}, "devDependencies": {"mocha": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "ssf.js"}}, "homepage": "https://oss.sheetjs.com/ssf", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "354f2bce4ff4831ef0347924e8c323c7bddc2c53", "_id": "ssf@0.9.0", "_shasum": "eab445edc8c85bc1b637eaa62c3cf0ace92b7148", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "eab445edc8c85bc1b637eaa62c3cf0ace92b7148", "size": 34169, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.9.0.tgz", "integrity": "sha512-5HovhIk/dNmDMa3plH8eQ91GgSKa3muwrb7Ag3lzMY9IbkdrD0WDeQd6nfqC10i5ZpqVOpX/MHfyLcYQ0PTP4g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ssf-0.9.0.tgz_1490082912956_0.7045138981193304"}, "directories": {}, "publish_time": 1490082913533, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490082913533, "_cnpmcore_publish_time": "2021-12-16T10:36:02.348Z"}, "0.8.2": {"name": "ssf", "version": "0.8.2", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "bin": {"ssf": "./bin/ssf.njs"}, "main": "ssf.js", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "3a2d0b2d19fd7301504ff8612035776f9f4c93b1", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.8.2", "_shasum": "b9d4dc6a1c1bcf76f8abfa96d7d7656fb2abecd6", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "b9d4dc6a1c1bcf76f8abfa96d7d7656fb2abecd6", "size": 26688, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.8.2.tgz", "integrity": "sha512-+ZkFDAG+ImJ48DcZvabx6YTrZ67DKkM0kbyOOtH73mbUEvNhQWWgRZrHC8+k7GuGKWQnACYLi7bj0eCt1jmosQ=="}, "directories": {}, "publish_time": 1404580098534, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404580098534, "_cnpmcore_publish_time": "2021-12-16T10:36:02.578Z"}, "0.8.1": {"name": "ssf", "version": "0.8.1", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b4221c28808c53c8a21d060cbd12aa93884df856", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.8.1", "_shasum": "e5cf5a6a59a39678aac35be5d5874f3f17e05b14", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "e5cf5a6a59a39678aac35be5d5874f3f17e05b14", "size": 38107, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.8.1.tgz", "integrity": "sha512-3/XkLADxjIecfvciq1fWtC0mDKb71IwLpEmMsuLMD2vE4EX4iX8kRIbc2cQFPqicczGjIURCTqP8gplOshXsmQ=="}, "directories": {}, "publish_time": 1403655515547, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403655515547, "_cnpmcore_publish_time": "2021-12-16T10:36:02.854Z"}, "0.8.0": {"name": "ssf", "version": "0.8.0", "author": {"name": "SheetJS"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "0.6.2", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "e32a2894c41153d44add39b4978951f91c67e683", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.8.0", "_shasum": "d549406ad10253af248b19067f31fb6eb65afe16", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "d549406ad10253af248b19067f31fb6eb65afe16", "size": 38105, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.8.0.tgz", "integrity": "sha512-Z/3etAKdQROF20H3JEL5u9wMja2odRQtChXNt4kCV7uyvWEhL3cYKLBByH8eMZY7OFv9rFAo89WsUnmU2OzxDw=="}, "directories": {}, "publish_time": 1402682984942, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402682984942, "_cnpmcore_publish_time": "2021-12-16T10:36:03.167Z"}, "0.7.1": {"name": "ssf", "version": "0.7.1", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.7.1", "_shasum": "f78e38aab58f8fedaf4782c52acc0a5382305257", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "f78e38aab58f8fedaf4782c52acc0a5382305257", "size": 34104, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.7.1.tgz", "integrity": "sha512-R1vXE16+05WUU0i7GiUWzvr2q+G5+nNEiWo8CfPRR4AHepeE0Sge4FjKvZ2e55KFCJayOBlMVrzlvdeAM3bWvw=="}, "directories": {}, "publish_time": 1401928214713, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401928214713, "_cnpmcore_publish_time": "2021-12-16T10:36:03.471Z"}, "0.7.0": {"name": "ssf", "version": "0.7.0", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.7.0", "dist": {"shasum": "9188f06f8db4caf21aeb26642bc1caa0a51851d1", "size": 33665, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.7.0.tgz", "integrity": "sha512-02sIitXqPtMpVvxc0m6QkALwz82bQkWn4+yL4mdjSMeYfKz4KcRLXSfmNk+oKjXpwKg5/ujmrzaWDbh890qSsw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1400733830929, "_hasShrinkwrap": false, "_cnpm_publish_time": 1400733830929, "_cnpmcore_publish_time": "2021-12-16T10:36:04.310Z"}, "0.6.5": {"name": "ssf", "version": "0.6.5", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.6.5", "dist": {"shasum": "81cb53f1357a57b3e4a60546179f7cc4f3fa0f88", "size": 32037, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.6.5.tgz", "integrity": "sha512-3G29p4zNZjHZ9kFSRMaySoppSbfuI7N/Xv3h66V4ZoPf68cmFIpPbLGWInsgE0gQtj+P9bNlXxuvsADoeB2BbA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398450487834, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398450487834, "_cnpmcore_publish_time": "2021-12-16T10:36:04.888Z"}, "0.6.4": {"name": "ssf", "version": "0.6.4", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.6.4", "dist": {"shasum": "84f3674d036c71b5f541044d57629f744997eb2d", "size": 31885, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.6.4.tgz", "integrity": "sha512-AD172Canu93xKRQt+jAp6RwtnqBput8meDLOxuVkRgRSdlHMyFyQaRN7J7X6tL6NbLv+goUOSe5AluW4+rwghQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396543012440, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396543012440, "_cnpmcore_publish_time": "2021-12-16T10:36:05.185Z"}, "0.6.3": {"name": "ssf", "version": "0.6.3", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.6.3", "dist": {"shasum": "1862265871582bedbd101a7010a4b8e7b748a217", "size": 31813, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.6.3.tgz", "integrity": "sha512-Ok6skITv6dud2ooWqYvo6K4dmEgaz880/BwZ7yc9QVC5uamWrAkFk9Q5zmIeJevd9OfhX9UZHjduinR20zzerQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396503864967, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396503864967, "_cnpmcore_publish_time": "2021-12-16T10:36:05.549Z"}, "0.6.2": {"name": "ssf", "version": "0.6.2", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.6.2", "dist": {"shasum": "0de10c3a2a89f2e36ae5dc06287d69ef346fa442", "size": 31396, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.6.2.tgz", "integrity": "sha512-dAQ9uS9Zxrq13iDsoR7zGLc1qHMhVNgWMKPtjgzTxwcjJd3Uptr5Xy19C0xjQdsE29lf8GTK0SHv4E+nCc/SRA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396086392777, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396086392777, "_cnpmcore_publish_time": "2021-12-16T10:36:05.765Z"}, "0.6.1": {"name": "ssf", "version": "0.6.1", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.6.1", "dist": {"shasum": "87e8c98db63e789beac571f1ab64f2829e39bec5", "size": 30325, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.6.1.tgz", "integrity": "sha512-V8dMELKnrjs8RXSPZrsrHftfffsK2bJw0ao0wkc2mf78H6Kvqb3trMyRctSeEbTQkOub0rfHHYN70XmBDXPfQQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396042662194, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396042662194, "_cnpmcore_publish_time": "2021-12-16T10:36:06.050Z"}, "0.6.0": {"name": "ssf", "version": "0.6.0", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.6.0", "dist": {"shasum": "3050d14cfab3d57ad9f14064c885574ab186e019", "size": 29850, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.6.0.tgz", "integrity": "sha512-XMiZpf77PyBYWy1KkwL8P1mJiJ/3flSMFPOiEcI99X1KI9tAX4aviSI/ea/c36EDZscJ+amTaWod4TmHOM7xdw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395949949706, "_hasShrinkwrap": false, "_cnpm_publish_time": 1395949949706, "_cnpmcore_publish_time": "2021-12-16T10:36:06.280Z"}, "0.5.11": {"name": "ssf", "version": "0.5.11", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.11", "dist": {"shasum": "8c32c5efc27cd6f14f73b13618768b862da1117c", "size": 29237, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.11.tgz", "integrity": "sha512-XHiVHxfpDVniyOKVElxL9q2DUclTbtYBFNCmI3aB9K34kwikvcTEs9yWSDerO+mo4Sw144wFgJjIzeeLePPbvA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395737436200, "_hasShrinkwrap": false, "_cnpm_publish_time": 1395737436200, "_cnpmcore_publish_time": "2021-12-16T10:36:06.490Z"}, "0.5.10": {"name": "ssf", "version": "0.5.10", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.10", "dist": {"shasum": "bef7db8791fe339eb123b4ba439eb2dc51562b29", "size": 29170, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.10.tgz", "integrity": "sha512-dSmrSJlUU0sw/ihLzQCCh8OjIxe6ElSPmEzw0OTPf0zMV4BKWk6ndf9B9Ltcbq4iRPhXBOMWZIresR6rgXUGLA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395733573348, "_hasShrinkwrap": false, "_cnpm_publish_time": 1395733573348, "_cnpmcore_publish_time": "2021-12-16T10:36:06.691Z"}, "0.5.9": {"name": "ssf", "version": "0.5.9", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.9", "dist": {"shasum": "b6bdb4f44622d67017047706ad71dc38fac4069b", "size": 28922, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.9.tgz", "integrity": "sha512-OTrcgYWX8LgGbFj3rglBLkr3jnTesH3iQCUqdssANhEGTe2i6ilYfsMwxXyqLd5Dm1liFd60qvuWEcuiPe+BCw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395202079717, "_hasShrinkwrap": false, "_cnpm_publish_time": 1395202079717, "_cnpmcore_publish_time": "2021-12-16T10:36:07.002Z"}, "0.5.8": {"name": "ssf", "version": "0.5.8", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.8", "dist": {"shasum": "a8794ecb8ccf3d77bd47520c7f2842455b2f32c3", "size": 28792, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.8.tgz", "integrity": "sha512-aJpPXtmnQ75ImAB22LAZGR5jIQs0Ig3HL2LfA13iou7KZGud7PjayLEwDm8V3j9ZYehyufxOr7ZqLy7kTnSHQA=="}, "_from": ".", "_npmVersion": "1.3.26", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392625890337, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392625890337, "_cnpmcore_publish_time": "2021-12-16T10:36:07.284Z"}, "0.5.7": {"name": "ssf", "version": "0.5.7", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.7", "dist": {"shasum": "b934a46e6888efefc00a6ae8f6be99e03be88e25", "size": 28755, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.7.tgz", "integrity": "sha512-mTo1+1U6BWLVKGGbGvXIz0nGgDO7M3T0anUR/3rcyuUr0eP+Q3/TAAR0i48r5wB3yvuTYfKnHxYYMVNXKyqweg=="}, "_from": ".", "_npmVersion": "1.3.26", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392147004180, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392147004180, "_cnpmcore_publish_time": "2021-12-16T10:36:07.574Z"}, "0.5.6": {"name": "ssf", "version": "0.5.6", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.6", "dist": {"shasum": "79e6bc2f9d916caf0bd2f682f3e058b28e59e8ec", "size": 28591, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.6.tgz", "integrity": "sha512-MRH2ueJQW9Calemz+1Zeo/KX5fjiQsMQnzqHt+Jhs3d+NMjg4l7Z80MV+hSeQsr5kSJ6yk27RRpucnooPTzoEA=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392097635453, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392097635453, "_cnpmcore_publish_time": "2021-12-16T10:36:07.801Z"}, "0.5.5": {"name": "ssf", "version": "0.5.5", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.5", "dist": {"shasum": "02ae4d495607cfa276755ca54337fe9c0ec0426c", "size": 22451, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.5.tgz", "integrity": "sha512-8LrnFdEa4tozg8QwySRjfSdak1Q/0uhf8HREbeonygGA7c652quwDQbIa4ceaTxNuEM3gMa59QRBjEGzJBsLLw=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391046262141, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391046262141, "_cnpmcore_publish_time": "2021-12-16T10:36:08.073Z"}, "0.5.4": {"name": "ssf", "version": "0.5.4", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.4", "dist": {"shasum": "ff86ea4667044d10829ec7cecf0c28c3da80a50f", "size": 22326, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.4.tgz", "integrity": "sha512-mH16lEyvVGO0rquyGON3JzHs6d1227IdERuHwVZanMFtF2sTjQJqX68mu4exO1tSmtZLf0s51+P0eRdY/mbouA=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390956816684, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390956816684, "_cnpmcore_publish_time": "2021-12-16T10:36:08.357Z"}, "0.5.3": {"name": "ssf", "version": "0.5.3", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.3", "dist": {"shasum": "a1674c5686987a7f8c583e1f4654f5221208cee8", "size": 22286, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.3.tgz", "integrity": "sha512-VJAewYZCmMoubfNgNKaIF+BTcKmQmyWfYo8lWXHtavUoTikgXLj5+nNiTh/faaoRo2VjJBuurtJM+uSwThSssw=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390451184805, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390451184805, "_cnpmcore_publish_time": "2021-12-16T10:36:08.610Z"}, "0.5.2": {"name": "ssf", "version": "0.5.2", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "config": {"blanket": {"pattern": "ssf.js"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.2", "dist": {"shasum": "02ae3ef993f1469b9e7652d2d36f6e778ea0773f", "size": 21689, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.2.tgz", "integrity": "sha512-DTh3qVhzF9hOfmt3JXzGQCO/V07xX2Hjo1lX2YL8+xQYPXeeLqTWvtuzQtVK1R3gdU4lC+7KewQUb7yIWCro1w=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390207437055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390207437055, "_cnpmcore_publish_time": "2021-12-16T10:36:09.268Z"}, "0.5.1": {"name": "ssf", "version": "0.5.1", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": "", "frac": "0.3.1"}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.1", "dist": {"shasum": "c3183c02d1e2975a0e761274772d66157e0b0dba", "size": 19393, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.1.tgz", "integrity": "sha512-wvB1x3jp7qYnxTfvnA/3R8TWo2zw2kLJ3TdKMG5FRcQoRQk3Ctef2cdj2v0JPLk7kcjI6KghbzU17h/Sg2UTNw=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389515687139, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389515687139, "_cnpmcore_publish_time": "2021-12-16T10:36:09.541Z"}, "0.5.0": {"name": "ssf", "version": "0.5.0", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf.js", "dependencies": {"voc": "", "colors": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bin": {"ssf": "./bin/ssf.njs"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.5.0", "dist": {"shasum": "25963e52d1ca123655ceefa84ef0e5f77360ce08", "size": 17272, "noattachment": false, "tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.5.0.tgz", "integrity": "sha512-jaVbU+fzVQun+hAzeF+Mz8yiODCw9yRr47fRyYFWZvCka7+SofEnGuNajPKPQMjV5TIaIqrJ+l7Iw3oi3Gb+rg=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389381082662, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389381082662, "_cnpmcore_publish_time": "2021-12-16T10:36:09.771Z"}, "0.4.1": {"name": "ssf", "version": "0.4.1", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf_node.js", "dependencies": {"voc": "", "colors": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.4.1", "dist": {"tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.4.1.tgz", "shasum": "21e1d0e2e27e04ee9bbc3cd07903fc12d5d80340", "size": 16674, "noattachment": false, "integrity": "sha512-nEq1KI0o58/eA6qdXX618yl2/fRcH8Pby/LhleLNtWbIh6b9Q0i6k8Wq/Lcpd/Xseqkwkp9un58SmeTXq2UYQw=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1388507954888, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388507954888, "_cnpmcore_publish_time": "2021-12-16T10:36:10.016Z"}, "0.4.0": {"name": "ssf", "version": "0.4.0", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf_node.js", "dependencies": {"voc": "", "colors": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.4.0", "dist": {"tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.4.0.tgz", "shasum": "d317a82fdfd0326fd4165bae7b5880861600482b", "size": 16620, "noattachment": false, "integrity": "sha512-WZgrrCiwgEwHzRCLtubU2TMo0bDurM7cTftN4YHBUXI9Sd43ybvQU1JWd1zIZnjrFYOZ8+40nJaitbL/auJyKQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1388099664719, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388099664719, "_cnpmcore_publish_time": "2021-12-16T10:36:10.657Z"}, "0.3.1": {"name": "ssf", "version": "0.3.1", "author": {"name": "SheetJS"}, "description": "pure-JS library to format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "ssf_node.js", "dependencies": {"voc": "", "colors": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "mocha -R spec"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/ssf", "_id": "ssf@0.3.1", "dist": {"tarball": "https://registry.npmmirror.com/ssf/-/ssf-0.3.1.tgz", "shasum": "976f142091a2f6dd54cc4de8a112bc62b92b932a", "size": 15157, "noattachment": false, "integrity": "sha512-51lsWsMMhjxtPbnCPiRFuLbArqrqYyDy2awPNzgf0SxX5lTZL8MTWR5Vev80Ws7zaWWd2N7nBsxtDWOQaYa/vw=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1387238597944, "_hasShrinkwrap": false, "_cnpm_publish_time": 1387238597944, "_cnpmcore_publish_time": "2021-12-16T10:36:10.878Z"}}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "homepage": "http://sheetjs.com/", "keywords": ["format", "sprintf", "spreadsheet"], "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "_source_registry_name": "default"}