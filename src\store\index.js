import { createStore } from 'vuex';
import { getUserInfo } from '@/utils/api';

export default createStore({
  state: {
    userInfo: null,
    token: localStorage.getItem('token') || null,
  },

  mutations: {
    setUser(state, userInfo) {
      state.userInfo = userInfo;
    },
    setToken(state, token) {
      state.token = token;
      if (token) {
        localStorage.setItem('token', token);
      } else {
        localStorage.removeItem('token');
      }
    },
    clearUser(state) {
      state.userInfo = null;
      state.token = null;
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
    },
  },

  actions: {
    // 获取用户信息
    async fetchUserInfo({ commit }) {
      try {
        const response = await getUserInfo();
        if (response.success) {
          commit('setUser', response.data);
          return response.data;
        }
        return null;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        return null;
      }
    },

    // 登出操作
    async logout({ commit }) {
      commit('clearUser');
    },
  },

  getters: {
    isAuthenticated: state => !!state.token,
    currentUser: state => state.userInfo,
  },
});
