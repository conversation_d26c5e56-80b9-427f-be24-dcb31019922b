{"_attachments": {}, "_id": "combined-stream", "_rev": "1811-61f147bb830fd08f52a27a44", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "description": "A stream that emits multiple other streams one after another.", "dist-tags": {"latest": "1.0.8", "next": "1.0.6-rc1"}, "license": "MIT", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "name": "combined-stream", "readme": "# combined-stream\n\nA stream that emits multiple other streams one after another.\n\n**NB** Currently `combined-stream` works with streams version 1 only. There is ongoing effort to switch this library to streams version 2. Any help is welcome. :) Meanwhile you can explore other libraries that provide streams2 support with more or less compatibility with `combined-stream`.\n\n- [combined-stream2](https://www.npmjs.com/package/combined-stream2): A drop-in streams2-compatible replacement for the combined-stream module.\n\n- [multistream](https://www.npmjs.com/package/multistream): A stream that emits multiple other streams one after another.\n\n## Installation\n\n``` bash\nnpm install combined-stream\n```\n\n## Usage\n\nHere is a simple example that shows how you can use combined-stream to combine\ntwo files into one:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create();\ncombinedStream.append(fs.createReadStream('file1.txt'));\ncombinedStream.append(fs.createReadStream('file2.txt'));\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\nWhile the example above works great, it will pause all source streams until\nthey are needed. If you don't want that to happen, you can set `pauseStreams`\nto `false`:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create({pauseStreams: false});\ncombinedStream.append(fs.createReadStream('file1.txt'));\ncombinedStream.append(fs.createReadStream('file2.txt'));\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\nHowever, what if you don't have all the source streams yet, or you don't want\nto allocate the resources (file descriptors, memory, etc.) for them right away?\nWell, in that case you can simply provide a callback that supplies the stream\nby calling a `next()` function:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create();\ncombinedStream.append(function(next) {\n  next(fs.createReadStream('file1.txt'));\n});\ncombinedStream.append(function(next) {\n  next(fs.createReadStream('file2.txt'));\n});\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\n## API\n\n### CombinedStream.create([options])\n\nReturns a new combined stream object. Available options are:\n\n* `maxDataSize`\n* `pauseStreams`\n\nThe effect of those options is described below.\n\n### combinedStream.pauseStreams = `true`\n\nWhether to apply back pressure to the underlaying streams. If set to `false`,\nthe underlaying streams will never be paused. If set to `true`, the\nunderlaying streams will be paused right after being appended, as well as when\n`delayedStream.pipe()` wants to throttle.\n\n### combinedStream.maxDataSize = `2 * 1024 * 1024`\n\nThe maximum amount of bytes (or characters) to buffer for all source streams.\nIf this value is exceeded, `combinedStream` emits an `'error'` event.\n\n### combinedStream.dataSize = `0`\n\nThe amount of bytes (or characters) currently buffered by `combinedStream`.\n\n### combinedStream.append(stream)\n\nAppends the given `stream` to the combinedStream object. If `pauseStreams` is\nset to `true, this stream will also be paused right away.\n\n`streams` can also be a function that takes one parameter called `next`. `next`\nis a function that must be invoked in order to provide the `next` stream, see\nexample above.\n\nRegardless of how the `stream` is appended, combined-stream always attaches an\n`'error'` listener to it, so you don't have to do that manually.\n\nSpecial case: `stream` can also be a String or Buffer.\n\n### combinedStream.write(data)\n\nYou should not call this, `combinedStream` takes care of piping the appended\nstreams into itself for you.\n\n### combinedStream.resume()\n\nCauses `combinedStream` to start drain the streams it manages. The function is\nidempotent, and also emits a `'resume'` event each time which usually goes to\nthe stream that is currently being drained.\n\n### combinedStream.pause();\n\nIf `combinedStream.pauseStreams` is set to `false`, this does nothing.\nOtherwise a `'pause'` event is emitted, this goes to the stream that is\ncurrently being drained, so you can use it to apply back pressure.\n\n### combinedStream.end();\n\nSets `combinedStream.writable` to false, emits an `'end'` event, and removes\nall streams from the queue.\n\n### combinedStream.destroy();\n\nSame as `combinedStream.end()`, except it emits a `'close'` event instead of\n`'end'`.\n\n## License\n\ncombined-stream is licensed under the MIT license.\n", "time": {"created": "2022-01-26T13:08:11.979Z", "modified": "2023-07-28T03:02:27.304Z", "1.0.8": "2019-05-12T17:49:45.337Z", "1.0.7": "2018-09-18T16:32:24.107Z", "1.0.6": "2018-02-13T16:31:05.240Z", "1.0.6-rc1": "2018-02-12T16:33:42.335Z", "1.0.5": "2015-06-15T03:19:17.202Z", "1.0.4": "2015-06-14T15:09:18.379Z", "1.0.3": "2015-05-19T12:43:10.026Z", "1.0.2": "2015-05-09T14:25:06.378Z", "1.0.1": "2015-05-05T01:13:50.245Z", "1.0.0": "2015-05-04T21:29:45.965Z", "0.0.7": "2014-10-31T10:18:38.784Z", "0.0.5": "2014-06-24T08:39:18.882Z", "0.0.4": "2013-02-02T18:29:01.654Z", "0.0.3": "2011-05-28T13:49:43.181Z", "0.0.2": "2011-05-27T13:46:53.900Z", "0.0.1": "2011-05-24T09:46:38.486Z", "0.0.0": "2011-05-24T06:36:15.190Z"}, "versions": {"1.0.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.8", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "5298bece5aba2cf4d2f5ec84c4fc128572c41cee", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.8", "_nodeVersion": "11.10.1", "_npmVersion": "6.7.0", "dist": {"shasum": "c3d45a8b34fd730631a110a8a2520682b31d5a7f", "size": 4068, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.8_1557683385167_0.16390415386060164"}, "_hasShrinkwrap": false, "publish_time": 1557683385337, "_cnpm_publish_time": 1557683385337, "_cnpmcore_publish_time": "2021-12-13T14:07:08.808Z"}, "1.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.7", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "licenseText": "Copyright (c) 2011 Debuggable Limited <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "combined-stream@1.0.7", "dist": {"shasum": "2d1d24317afb8abe95d6d2c0b07b57813539d828", "size": 3969, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.7.tgz", "integrity": "sha512-brWl9y6vOB1xYPZcpZde3N9zDByXTosAeMDo4p1wzo6UMOX4vumB+TP1RZ76sfE6Md68Q0NJSrE/gbezd4Ul+w=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.7_1537288343882_0.48309285138952385"}, "_hasShrinkwrap": false, "publish_time": 1537288344107, "_cnpm_publish_time": 1537288344107, "_cnpmcore_publish_time": "2021-12-13T14:07:09.077Z"}, "1.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.6", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "licenseText": "Copyright (c) 2011 Debuggable Limited <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "combined-stream@1.0.6", "dist": {"shasum": "723e7df6e801ac5613113a7e445a9b69cb632818", "size": 3966, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.6.tgz", "integrity": "sha512-cN6NJ9NnPLDiP/CpmVC1knLFqNjD9Hi1vPsacL/WQP3v8cqVbZpbpX6NHmYJo2fR4B80CgE4cEgPWiDauAQzPw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.6_1518539465161_0.17330394935691018"}, "_hasShrinkwrap": false, "publish_time": 1518539465240, "_cnpm_publish_time": 1518539465240, "_cnpmcore_publish_time": "2021-12-13T14:07:09.355Z"}, "1.0.6-rc1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.6-rc1", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "readmeFilename": "Readme.md", "licenseText": "Copyright (c) 2011 Debuggable Limited <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "combined-stream@1.0.6-rc1", "dist": {"shasum": "aca80f422f9778ddc1dc05980838189e5b34b913", "size": 3968, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.6-rc1.tgz", "integrity": "sha512-OZGx7sV9JGMi4vmwkBYDE+Sm40kKoWSDnvrNh/iPRwYnZUjDpJiKIRi9bEW3F6p7uHcesA2HqEFhwiW09eQXOg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.6-rc1_1518453222275_0.3377146156635127"}, "_hasShrinkwrap": false, "publish_time": 1518453222335, "_cnpm_publish_time": 1518453222335, "_cnpmcore_publish_time": "2021-12-13T14:07:09.683Z"}, "1.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.5", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "cfc7b815d090a109bcedb5bb0f6713148d55a6b7", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.5", "_shasum": "938370a57b4a51dea2c77c15d5c5fdf895164009", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "938370a57b4a51dea2c77c15d5c5fdf895164009", "size": 3675, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.5.tgz", "integrity": "sha512-JgSRe4l4UzPwpJuxfcPWEK1SCrL4dxNjp1uqrQLMop3QZUVo+hDU8w9BJKA4JPbulTWI+UzrI2UA3tK12yQ6bg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1434338357202, "_hasShrinkwrap": false, "_cnpm_publish_time": 1434338357202, "_cnpmcore_publish_time": "2021-12-13T14:07:10.046Z"}, "1.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.4", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "ffea6d5444d708a4e457286cc80b546c81af4756", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.4", "_shasum": "f988793ed1a59c5e5c9e1e395c4207d409527c8b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "f988793ed1a59c5e5c9e1e395c4207d409527c8b", "size": 3670, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.4.tgz", "integrity": "sha512-BX6ms9qZiZnVTxIo+CLS8q9V3w5FGQJPb/3tuwEQWdtLd7FDC6F78A6k8zhJabHXyT3jffZhGA/1QaSYnAoATA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1434294558379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1434294558379, "_cnpmcore_publish_time": "2021-12-13T14:07:10.361Z"}, "1.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.3", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "f1a12682aed63acb3cd66857104202a7e7ca5565", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.3", "_shasum": "c224cc35d3cb98e25dead532472a18e8f75df5ab", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "c224cc35d3cb98e25dead532472a18e8f75df5ab", "size": 3662, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.3.tgz", "integrity": "sha512-ti+lCx2J0cWkksupsP2xxjb9XgXVX+6Zgf+fsKqcbNHHPoEOZkPgwU4kziJtbPUrJLsrp1RWXA+u0a7KwcgmzQ=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432039390026, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432039390026, "_cnpmcore_publish_time": "2021-12-13T14:07:10.703Z"}, "1.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.2", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "e73d8849659fc4c382f952986d88e8842f345fa5", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.2", "_shasum": "6ae58023aef4deae6b0993948ab9a0d0af2b4b72", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "6ae58023aef4deae6b0993948ab9a0d0af2b4b72", "size": 3651, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.2.tgz", "integrity": "sha512-7LwwHCXWJGfsfu7DiXuYoIgqnRSls4MKXzwQbchsvX7A/xPbU0qwNbBCMeocRLmHqJP7rTaFBUmRPgqh27s6Nw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431181506378, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431181506378, "_cnpmcore_publish_time": "2021-12-13T14:07:11.070Z"}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.1", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "69b8207c14d7ff95b7e69c1cc00b496ebdca0644", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.1", "_shasum": "d29988ca26d4a0fb85d384650ba8db5948d2f41b", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "d29988ca26d4a0fb85d384650ba8db5948d2f41b", "size": 3507, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.1.tgz", "integrity": "sha512-JS5TtJcwW51TyE/kVEAuBi9Kl6XIdho4VEfqXjg7gMthb1/13TOauaX7ZkmtyVTb5WEZrhCCuw/wjgHyOyEgaQ=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1430788430245, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430788430245, "_cnpmcore_publish_time": "2021-12-13T14:07:11.429Z"}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.0", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "8bb2526b074ba7da9ce430e4158b4b77dd89ecd8", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.0", "_shasum": "d451d5a5f94340b3480e49b38bfd1811ab1b2110", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "d451d5a5f94340b3480e49b38bfd1811ab1b2110", "size": 3506, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.0.tgz", "integrity": "sha512-+cWz+97tOTM7FKdAWAIr4hViY2GJKjTDaXAv7Vh/bqP8ZHkZ9fIb5jEt0+mXsesS1i0LvL32z7xbALPl2VQeww=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1430774985965, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430774985965, "_cnpmcore_publish_time": "2021-12-13T14:07:11.815Z"}, "0.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.7", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}, "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@0.0.7", "dist": {"shasum": "0137e657baa5a7541c57ac37ac5fc07d73b4dc1f", "size": 3501, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "felix<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1414750718784, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414750718784, "_cnpmcore_publish_time": "2021-12-13T14:07:12.197Z"}, "0.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.5", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "19d9bdd4c20f6806c2ae8adb00a53fb6fd154740", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@0.0.5", "_shasum": "29ed76e5c9aad07c4acf9ca3d32601cce28697a2", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "29ed76e5c9aad07c4acf9ca3d32601cce28697a2", "size": 3468, "noattachment": false, "tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.5.tgz", "integrity": "sha512-5iibGSlnX9jIyz9F0eSgaoazkVo+7+pQTPS9gJmrP9FcyCaxxaIRb8OLiu1nYHxDeFFTWkkLGe/bkvZdzhza+g=="}, "directories": {}, "publish_time": 1403599158882, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403599158882, "_cnpmcore_publish_time": "2021-12-13T14:07:12.700Z"}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.4", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.4", "dist": {"tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.4.tgz", "shasum": "2d1a43347dbe9515a4a2796732e5b88473840b22", "size": 5624, "noattachment": false, "integrity": "sha512-RVtYNBtwuVyncYQwuTnrsCIfsmsVPnGj1RI8xNeqhmuWFLfNUfaGxGbLWx4N10fNd+MnNVh+SO4f60/7ghV3fg=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "celer", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1359829741654, "_hasShrinkwrap": false, "_cnpm_publish_time": 1359829741654, "_cnpmcore_publish_time": "2021-12-13T14:07:13.062Z"}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.3", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.3", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.3.tgz", "shasum": "a1d6223c463a000b21c9937c4b15ef41ba001f78", "size": 5761, "noattachment": false, "integrity": "sha512-obT/nnZll8VTnjKTUGtov1vYMCWaTLfrkIHmfioG1wzLMtG5dfMXenNH/wQ5aoxpPaDnJtuYFixDUxDl8V84yQ=="}, "scripts": {}, "directories": {}, "publish_time": 1306590583181, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306590583181, "_cnpmcore_publish_time": "2021-12-13T14:07:13.447Z"}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.2", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.2.tgz", "shasum": "6c1691577cfb5f842f7ac1fecd8515a13cb99c9c", "size": 5749, "noattachment": false, "integrity": "sha512-k8<PERSON><PERSON><PERSON>liZ8WYzOSRw4et5c9GZBQsMXpowpEfidipCQt8XDP3bEM89Jghpfl/H6mqOnap4PODDTCzL1dob5997YA=="}, "scripts": {}, "directories": {}, "publish_time": 1306504013900, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306504013900, "_cnpmcore_publish_time": "2021-12-13T14:07:13.846Z"}, "0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.1", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.1.tgz", "shasum": "dff7a316813a9ec58ef03bde5c1fc7133a35f944", "size": 5029, "noattachment": false, "integrity": "sha512-e+lVCV60dvpBOTToTv42ygWML9zxzlNkjwKkFO1bjbK9vGvY1NF/2GBkDRuAOTG/NLCP8/zcv1Oif202ly9jDQ=="}, "scripts": {}, "directories": {}, "publish_time": 1306230398486, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306230398486, "_cnpmcore_publish_time": "2021-12-13T14:07:14.258Z"}, "0.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.0", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "_id": "combined-stream@0.0.0", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.0.tgz", "shasum": "45550d8a25ee3b42de817cf675690732240e45d7", "size": 30284, "noattachment": false, "integrity": "sha512-LdFWln+/shOpBLCd2VfHUxEwP9m/kJ75dgBWKVLJ+Ye4PKOFbFE3c8ZnKDXQyu3qf+pfNWb/vtciWAmrcK+5Sg=="}, "scripts": {}, "directories": {}, "publish_time": 1306218975190, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306218975190, "_cnpmcore_publish_time": "2021-12-13T14:07:14.717Z"}}, "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "_source_registry_name": "default"}