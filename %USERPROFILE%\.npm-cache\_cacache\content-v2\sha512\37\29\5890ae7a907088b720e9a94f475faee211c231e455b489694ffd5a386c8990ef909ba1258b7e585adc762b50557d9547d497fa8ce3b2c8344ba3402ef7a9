{"_attachments": {}, "_id": "@vue/devtools-api", "_rev": "7652-61f1609eb77ea98a74948bf2", "author": {"name": "webfansplz"}, "description": "> Plugins API for easier DevTools integrations.", "dist-tags": {"latest": "7.7.7", "next": "7.4.4"}, "license": "MIT", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "name": "@vue/devtools-api", "readme": "# @vue/devtools-api\n\n> Plugins API for easier DevTools integrations.\n\n## Getting Started\n\nPlease follow the documentation at [devtools.vuejs.org](https://devtools.vuejs.org/plugins/api).\n", "time": {"created": "2022-01-26T14:54:22.408Z", "modified": "2025-06-16T05:51:33.427Z", "6.0.0-beta.20.1": "2021-11-10T16:36:34.087Z", "6.0.0-beta.20": "2021-11-03T13:07:58.583Z", "6.0.0-beta.19": "2021-10-04T19:39:33.370Z", "6.0.0-beta.18": "2021-09-22T23:06:29.613Z", "6.0.0-beta.17": "2021-09-19T08:09:33.695Z", "6.0.0-beta.16": "2021-09-18T21:41:55.316Z", "6.0.0-beta.15": "2021-06-28T17:03:45.790Z", "6.0.0-beta.14": "2021-06-09T11:48:27.680Z", "6.0.0-beta.13": "2021-06-07T22:54:59.133Z", "6.0.0-beta.12": "2021-05-29T06:16:17.409Z", "6.0.0-beta.11": "2021-05-21T16:45:20.334Z", "6.0.0-beta.10": "2021-05-12T23:58:50.594Z", "6.0.0-beta.9": "2021-05-06T12:53:13.244Z", "6.0.0-beta.8": "2021-04-23T21:05:48.209Z", "6.0.0-beta.7": "2021-02-19T10:05:41.071Z", "6.0.0-beta.6": "2021-02-02T18:31:07.366Z", "6.0.0-beta.5": "2021-02-01T01:41:03.387Z", "6.0.0-beta.4": "2021-02-01T01:09:01.510Z", "6.0.0-beta.3": "2020-12-23T23:17:32.454Z", "6.0.0-beta.2": "2020-09-02T20:50:42.242Z", "6.0.0-beta.21": "2021-12-20T13:34:49.790Z", "6.0.0-beta.21.1": "2021-12-20T18:47:10.357Z", "6.0.0": "2022-02-07T09:35:34.527Z", "6.0.1": "2022-02-07T16:53:09.710Z", "6.0.2": "2022-02-08T12:11:00.129Z", "6.0.3": "2022-02-09T14:44:33.597Z", "6.0.4": "2022-02-09T17:33:55.251Z", "6.0.5": "2022-02-09T22:37:27.732Z", "6.0.6": "2022-02-11T01:30:07.953Z", "6.0.7": "2022-02-11T17:03:47.082Z", "6.0.8": "2022-02-11T23:09:45.530Z", "6.0.9": "2022-02-14T11:05:03.453Z", "6.0.10": "2022-02-14T13:49:41.228Z", "6.0.11": "2022-02-14T18:26:41.061Z", "6.0.12": "2022-02-15T19:10:53.739Z", "6.0.13": "2022-03-09T17:34:14.129Z", "6.1.0": "2022-03-11T17:40:47.481Z", "6.1.1": "2022-03-14T15:34:59.634Z", "6.1.2": "2022-03-14T16:55:48.129Z", "6.1.3": "2022-03-14T17:11:59.662Z", "6.1.4": "2022-03-29T21:16:49.583Z", "6.2.0": "2022-07-01T02:44:06.743Z", "6.2.1": "2022-07-13T01:08:50.243Z", "6.3.0": "2022-09-24T23:26:05.474Z", "6.4.0": "2022-09-26T20:51:45.777Z", "6.4.1": "2022-09-26T21:02:12.665Z", "6.4.2": "2022-09-29T10:28:10.883Z", "6.4.3": "2022-10-03T09:17:25.557Z", "6.4.4": "2022-10-08T09:17:34.451Z", "6.4.5": "2022-10-18T13:18:50.126Z", "6.5.0": "2023-01-20T15:39:19.364Z", "6.5.1": "2023-10-10T09:53:49.858Z", "7.0.0": "2023-12-29T08:45:12.226Z", "7.0.1": "2023-12-29T16:08:12.251Z", "7.0.2": "2023-12-29T16:59:54.064Z", "7.0.3": "2024-01-02T15:03:07.489Z", "7.0.4": "2024-01-03T12:55:24.602Z", "7.0.5": "2024-01-04T15:47:15.967Z", "7.0.6": "2024-01-06T08:17:02.298Z", "7.0.7": "2024-01-09T11:55:35.382Z", "7.0.8": "2024-01-11T13:09:02.756Z", "7.0.9": "2024-01-14T06:02:07.732Z", "7.0.10": "2024-01-14T07:54:51.989Z", "7.0.11": "2024-01-18T12:25:12.308Z", "7.0.12": "2024-01-25T14:24:06.218Z", "7.0.13": "2024-01-26T04:26:10.624Z", "7.0.14": "2024-02-01T14:47:33.061Z", "6.6.1": "2024-02-15T18:10:03.957Z", "7.0.15": "2024-02-16T14:55:31.140Z", "7.0.16": "2024-03-02T04:06:06.890Z", "7.0.17": "2024-03-12T14:05:55.107Z", "7.0.18": "2024-03-17T07:13:24.826Z", "7.0.19": "2024-03-19T04:58:41.271Z", "7.0.20": "2024-03-20T15:48:47.389Z", "7.0.21": "2024-03-25T17:03:38.428Z", "7.0.22": "2024-03-26T12:31:19.069Z", "7.0.23": "2024-03-26T15:31:31.366Z", "7.0.24": "2024-03-27T04:38:43.841Z", "7.0.25": "2024-03-28T04:48:54.399Z", "7.0.26": "2024-04-10T04:48:52.441Z", "7.0.27": "2024-04-10T13:00:44.005Z", "7.1.0": "2024-04-23T14:12:41.798Z", "7.1.1": "2024-04-23T14:37:21.947Z", "7.1.2": "2024-04-23T14:49:12.016Z", "7.1.3": "2024-04-26T14:15:49.651Z", "7.2.0": "2024-05-15T12:40:04.191Z", "7.2.1": "2024-05-19T15:59:30.159Z", "6.6.2": "2024-05-31T13:39:53.941Z", "6.6.3": "2024-06-04T09:55:50.038Z", "7.3.0-beta.1": "2024-06-05T15:09:37.750Z", "7.3.0-beta.2": "2024-06-06T13:00:34.252Z", "7.3.0-beta.3": "2024-06-06T15:57:20.506Z", "7.3.0": "2024-06-16T10:37:15.124Z", "7.3.1": "2024-06-18T04:51:56.408Z", "7.3.2": "2024-06-19T13:51:32.736Z", "7.3.3": "2024-06-21T17:31:02.139Z", "7.3.4": "2024-06-22T13:37:09.482Z", "7.3.5": "2024-06-28T12:45:17.319Z", "7.3.6": "2024-07-14T15:48:19.781Z", "7.3.7": "2024-07-23T15:03:32.333Z", "7.3.8": "2024-08-12T14:57:40.917Z", "7.3.9": "2024-08-25T09:07:46.118Z", "7.4.0": "2024-09-03T16:36:15.577Z", "7.4.1": "2024-09-04T04:59:15.210Z", "7.4.2": "2024-09-04T05:09:12.633Z", "7.4.3": "2024-09-04T05:39:01.886Z", "7.4.4": "2024-09-04T14:27:35.399Z", "6.6.4": "2024-09-09T12:41:03.053Z", "7.4.5": "2024-09-11T11:19:02.645Z", "7.4.6": "2024-09-23T14:43:13.978Z", "7.5.0": "2024-10-16T12:33:24.209Z", "7.5.1": "2024-10-16T12:50:43.119Z", "7.5.2": "2024-10-16T13:55:36.436Z", "7.5.3": "2024-10-22T14:28:17.438Z", "7.5.4": "2024-10-24T13:25:07.458Z", "7.5.5": "2024-10-29T13:49:15.428Z", "7.5.6": "2024-10-29T14:23:11.343Z", "7.6.0": "2024-10-30T17:09:58.676Z", "7.6.1": "2024-10-30T21:28:16.312Z", "7.6.2": "2024-10-31T16:48:05.630Z", "7.6.3": "2024-11-05T13:15:04.524Z", "7.6.4": "2024-11-11T14:33:26.970Z", "7.6.5": "2024-11-27T13:16:29.024Z", "7.6.6": "2024-11-28T13:37:52.415Z", "7.6.7": "2024-11-28T15:02:28.693Z", "7.6.8": "2024-12-11T11:27:59.839Z", "7.7.0": "2025-01-07T13:33:45.533Z", "7.7.1": "2025-01-24T00:04:46.784Z", "7.7.2": "2025-02-12T13:47:38.475Z", "7.7.3": "2025-04-16T13:34:12.738Z", "7.7.4": "2025-04-16T15:54:06.089Z", "7.7.5": "2025-04-16T16:37:57.023Z", "7.7.6": "2025-04-28T14:27:21.015Z", "7.7.7": "2025-06-15T11:11:41.559Z"}, "versions": {"6.0.0-beta.20.1": {"name": "@vue/devtools-api", "version": "6.0.0-beta.20.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "gitHead": "838f1f235184e8371843e51f7548001d1d670149", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.20.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-R2rfiRY+kZugzWh9ZyITaovx+jpU4vgivAEAiz80kvh3yviiTU3CBuGuyWpSwGz9/C7TkSWVM/FtQRGlZ16n8Q==", "shasum": "5b499647e929c35baf2a66a399578f9aa4601142", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.20.1.tgz", "fileCount": 37, "unpackedSize": 29017, "size": 6383, "noattachment": false}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.20.1_1636562193932_0.25828735105855816"}, "_hasShrinkwrap": false, "publish_time": 1636562194087, "_cnpm_publish_time": 1636562194087, "_cnpmcore_publish_time": "2021-12-16T16:47:58.749Z"}, "6.0.0-beta.20": {"name": "@vue/devtools-api", "version": "6.0.0-beta.20", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.20", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "b5405c9a84fb44687d05e7c4c7854b1639141106", "size": 6383, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.20.tgz", "integrity": "sha512-21u2jFOk8jbAneeGpDwZQ0W66RJa0IBDUyVl6SgKnn2cRFjLWzKj+ukXjpLhYr1KASyCe5E5U4jXwChVo0YUAw=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.20_1635944878459_0.9234170142537794"}, "_hasShrinkwrap": false, "publish_time": 1635944878583, "_cnpm_publish_time": 1635944878583, "_cnpmcore_publish_time": "2021-12-16T16:47:58.973Z"}, "6.0.0-beta.19": {"name": "@vue/devtools-api", "version": "6.0.0-beta.19", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.19", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "f8e88059daa424515992426a0c7ea5cde07e99bf", "size": 6363, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.19.tgz", "integrity": "sha512-ObzQhgkoVeoyKv+e8+tB/jQBL2smtk/NmC9OmFK8UqdDpoOdv/Kf9pyDWL+IFyM7qLD2C75rszJujvGSPSpGlw=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.19_1633376373159_0.20313628441185472"}, "_hasShrinkwrap": false, "publish_time": 1633376373370, "_cnpm_publish_time": 1633376373370, "_cnpmcore_publish_time": "2021-12-16T16:47:59.171Z"}, "6.0.0-beta.18": {"name": "@vue/devtools-api", "version": "6.0.0-beta.18", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.18", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "84c0aff9289a57294cb97490811f69e8a0a67f8a", "size": 6288, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.18.tgz", "integrity": "sha512-56vRhO7nXWWFYTx520BQSDlQH5VYpwy62hFDEqi2yHHEBpEqseOP5WYQusq7BEW3DXSY9E9cfPVR5CFtJbKuMg=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.18_1632351989471_0.451197724661647"}, "_hasShrinkwrap": false, "publish_time": 1632351989613, "_cnpm_publish_time": 1632351989613, "_cnpmcore_publish_time": "2021-12-16T16:47:59.367Z"}, "6.0.0-beta.17": {"name": "@vue/devtools-api", "version": "6.0.0-beta.17", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.17", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "d4b9eb02c670d39a4532f4bb3c8517abce3fcb82", "size": 6288, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.17.tgz", "integrity": "sha512-hwGY4Xxc2nl34OyNH7l2VO8/ja3R78B8bcbaBQnZljSju5Z0Bm9HTt+/fQao+TUrs3gfNrrQrY3euWqiaG8chw=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.17_1632038973528_0.7809340166132366"}, "_hasShrinkwrap": false, "publish_time": 1632038973695, "_cnpm_publish_time": 1632038973695, "_cnpmcore_publish_time": "2021-12-16T16:47:59.575Z"}, "6.0.0-beta.16": {"name": "@vue/devtools-api", "version": "6.0.0-beta.16", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.16", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "a114f113974507f5a0d922def7407e3be8da7855", "size": 6305, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.16.tgz", "integrity": "sha512-pDkJZhNIeh59ezxQoTou7i7RW8gvOIW16l5oiC+/+Ye0X7cYv7HjJbDLlZrlFpVVe/EDVX6iVkfbyXTue58/Ag=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.16_1632001315150_0.6970197642824525"}, "_hasShrinkwrap": false, "publish_time": 1632001315316, "_cnpm_publish_time": 1632001315316, "_cnpmcore_publish_time": "2021-12-16T16:47:59.752Z"}, "6.0.0-beta.15": {"name": "@vue/devtools-api", "version": "6.0.0-beta.15", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.15", "_nodeVersion": "16.3.0", "_npmVersion": "7.15.1", "dist": {"shasum": "ad7cb384e062f165bcf9c83732125bffbc2ad83d", "size": 4738, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.15.tgz", "integrity": "sha512-quBx4Jjpexo6KDiNUGFr/zF/2A4srKM9S9v2uHgMXSU//hjgq1eGzqkIFql8T9gfX5ZaVOUzYBP3jIdIR3PKIA=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.15_1624899825645_0.06899054444330299"}, "_hasShrinkwrap": false, "publish_time": 1624899825790, "_cnpm_publish_time": 1624899825790, "_cnpmcore_publish_time": "2021-12-16T16:48:00.010Z"}, "6.0.0-beta.14": {"name": "@vue/devtools-api", "version": "6.0.0-beta.14", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.14", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "6ed2d6f8d66a9256c9ad04bfff08309ba87b9723", "size": 4769, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.14.tgz", "integrity": "sha512-44fPrrN1cqcs6bFkT0C+yxTM6PZXLbR+ESh1U1j8UD22yO04gXvxH62HApMjLbS3WqJO/iCNC+CYT+evPQh2EQ=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.14_1623239307321_0.8548625122254243"}, "_hasShrinkwrap": false, "publish_time": 1623239307680, "_cnpm_publish_time": 1623239307680, "_cnpmcore_publish_time": "2021-12-16T16:48:00.229Z"}, "6.0.0-beta.13": {"name": "@vue/devtools-api", "version": "6.0.0-beta.13", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.13", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "ff31e552ae3e99b5f2c0af0ca81f0889dbfb2155", "size": 4769, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.13.tgz", "integrity": "sha512-oZ0n/N4UWpkMvbR1OrBtu+YhaVADo+bYX5lxo9tou7h10p0+v2K9yzzaZATVr0lqHb7iY1wALfO8yojwg0MTHw=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.13_1623106499016_0.35901417975450234"}, "_hasShrinkwrap": false, "publish_time": 1623106499133, "_cnpm_publish_time": 1623106499133, "_cnpmcore_publish_time": "2021-12-16T16:48:00.495Z"}, "6.0.0-beta.12": {"name": "@vue/devtools-api", "version": "6.0.0-beta.12", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.12", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"shasum": "693ffc77bfb66b080e5c9576abb5786c85470a32", "size": 4766, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.12.tgz", "integrity": "sha512-PtHmAxFmCyCElV7uTWMrXj+fefwn4lCfTtPo9fPw0SK8/7e3UaFl8IL7lnugJmNFfeKQyuTkSoGvTq1uDaRF6Q=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.12_1622268977168_0.3527571542406247"}, "_hasShrinkwrap": false, "publish_time": 1622268977409, "_cnpm_publish_time": 1622268977409, "_cnpmcore_publish_time": "2021-12-16T16:48:00.698Z"}, "6.0.0-beta.11": {"name": "@vue/devtools-api", "version": "6.0.0-beta.11", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.11", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "4fb4161ee41ba75f3f5198d4bfd80e4ffb7f2462", "size": 4651, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.11.tgz", "integrity": "sha512-vpw61AkW9U8c2upjJCljHq9eh1KkD4FJ7DYbRzIETpj9WAw2VESudJZosAk4M+7npBo1Zu1jNQY03HUMVO/czQ=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.11_1621615520201_0.114711135140531"}, "_hasShrinkwrap": false, "publish_time": 1621615520334, "_cnpm_publish_time": 1621615520334, "_cnpmcore_publish_time": "2021-12-16T16:48:00.904Z"}, "6.0.0-beta.10": {"name": "@vue/devtools-api", "version": "6.0.0-beta.10", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.10", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "f39da7618cee292e39c7274227c34163e30eb3ca", "size": 4628, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.10.tgz", "integrity": "sha512-nktQYRnIFrh4DdXiCBjHnsHOMZXDIVcP9qlm/DMfxmjJMtpMGrSZCOKP8j7kDhObNHyqlicwoGLd+a4hf4x9ww=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.10_1620863930454_0.9085572469120102"}, "_hasShrinkwrap": false, "publish_time": 1620863930594, "_cnpm_publish_time": 1620863930594, "_cnpmcore_publish_time": "2021-12-16T16:48:01.102Z"}, "6.0.0-beta.9": {"name": "@vue/devtools-api", "version": "6.0.0-beta.9", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.9", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "7231f173c9b91cb2f3d4a209c7c0e380a7f5e417", "size": 4598, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.9.tgz", "integrity": "sha512-Qo5OXPt/+k26ffg/ZbyR3Ldlmp5uttfPP3B1G3Uo1Ha/DmnqeUlvwebVGDT3EXM6RNL5SoW2gvVLyFq58+8nZg=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.9_1620305593132_0.12280205935269861"}, "_hasShrinkwrap": false, "publish_time": 1620305593244, "_cnpm_publish_time": 1620305593244, "_cnpmcore_publish_time": "2021-12-16T16:48:01.304Z"}, "6.0.0-beta.8": {"name": "@vue/devtools-api", "version": "6.0.0-beta.8", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.8", "_nodeVersion": "15.13.0", "_npmVersion": "7.7.6", "dist": {"shasum": "0dbb2d21b13818c04b7f70b1b0c3f7aae95e4fb4", "size": 4528, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.8.tgz", "integrity": "sha512-I2QYmUYvuJnMMX/4/i+fbCf2nlAkjfw58siw0whFlUT7LTvTc5Xv7jcLP5XS3+8LkHd7UtVhU7EBGMfA7jkXXg=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.8_1619211948027_0.7845855637784389"}, "_hasShrinkwrap": false, "publish_time": 1619211948209, "_cnpm_publish_time": 1619211948209, "_cnpmcore_publish_time": "2021-12-16T16:48:01.592Z"}, "6.0.0-beta.7": {"name": "@vue/devtools-api", "version": "6.0.0-beta.7", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.7", "_nodeVersion": "14.15.4", "_npmVersion": "7.5.2", "dist": {"shasum": "1d306613c93b9a837a3776b1b9255502662f850f", "size": 4432, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.7.tgz", "integrity": "sha512-mIfqX8ZF6s2ulelIzfxGk9sFoigpoeK/2/DlWrtBGWfvwaK3kR1P2bxNkZ0LbJeuKHfcRP6hGZtGist7nxUN9A=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.7_1613729140863_0.16136414371167884"}, "_hasShrinkwrap": false, "publish_time": 1613729141071, "_cnpm_publish_time": 1613729141071, "_cnpmcore_publish_time": "2021-12-16T16:48:01.806Z"}, "6.0.0-beta.6": {"name": "@vue/devtools-api", "version": "6.0.0-beta.6", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.6", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "bceabad6631156c57bdf790bfbbd5df27545e2a2", "size": 4391, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.6.tgz", "integrity": "sha512-BRQOUjncqTRmYRXH76TWYYzi3O+Xhto2/wD6GN30pbFoYimgJHCwfYaeL8iSZ2aBTNHgO9Rd73qXt1kn9p3S8g=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.6_1612290667214_0.3776193974252313"}, "_hasShrinkwrap": false, "publish_time": 1612290667366, "_cnpm_publish_time": 1612290667366, "_cnpmcore_publish_time": "2021-12-16T16:48:02.006Z"}, "6.0.0-beta.5": {"name": "@vue/devtools-api", "version": "6.0.0-beta.5", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.5", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "722296bb0b93c4d0018b6a2beda1e781a3ff8789", "size": 4388, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.5.tgz", "integrity": "sha512-Xik8/zF+cu1lQmhJy2djGudZ2OmSGGB25mjtTIGhMsNN7jvvwnn44kD6qSN22+PdnX6sAK+2EQ51WDCXWmitwg=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.5_1612143663251_0.6306073025852934"}, "_hasShrinkwrap": false, "publish_time": 1612143663387, "_cnpm_publish_time": 1612143663387, "_cnpmcore_publish_time": "2021-12-16T16:48:02.245Z"}, "6.0.0-beta.4": {"name": "@vue/devtools-api", "version": "6.0.0-beta.4", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.4", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "10fdb3ed98593ccc3ef45d2d66d4d9a983e75936", "size": 4388, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.4.tgz", "integrity": "sha512-cIpWHDwyAnbElILI4jdv1G2koRjTbTVr7yluqY1mB7+xjT8HxUmPd/JD+cFfWDSEqvdA+BAKcXRVUtg90gBCfQ=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.4_1612141741376_0.6999065956805088"}, "_hasShrinkwrap": false, "publish_time": 1612141741510, "_cnpm_publish_time": 1612141741510, "_cnpmcore_publish_time": "2021-12-16T16:48:02.496Z"}, "6.0.0-beta.3": {"name": "@vue/devtools-api", "version": "6.0.0-beta.3", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm", "build:cjs": "tsc --module commonjs --outDir lib/cjs -d", "build:watch": "yarn tsc --module commonjs --outDir lib/cjs -d -w --sourceMap", "ts": "yarn build:esm"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.3", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"shasum": "5a66cc8beed688fe18c272ee7a8bd8ed7e35a54c", "size": 4366, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.3.tgz", "integrity": "sha512-iJQhVyWzWIJxYIMjbZpljZQfU4gL2IMD5YQm3HXO8tQRU7RqqnD3f1WHn+vrqvrSvM8Qw2BeNugwdBBmbK8Oxg=="}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.3_1608765452329_0.24429479641944596"}, "_hasShrinkwrap": false, "publish_time": 1608765452454, "_cnpm_publish_time": 1608765452454, "_cnpmcore_publish_time": "2021-12-16T16:48:02.712Z"}, "6.0.0-beta.2": {"name": "@vue/devtools-api", "version": "6.0.0-beta.2", "description": "Interact with the Vue devtools from the page", "main": "./lib/index.js", "types": "./lib/index.d.ts", "author": "", "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn ts", "build:watch": "yarn ts -w", "ts": "tsc -d -outDir lib"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^3.8.3"}, "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.2", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "833ad3335f97ae9439e26247d97f9baf7b5a6116", "size": 6226, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.2.tgz", "integrity": "sha512-5k0A8ffjNNukOiceImBdx1e3W5Jbpwqsu7xYHiZVu9mn4rYxFztIt+Q25mOHm7nwvDnMHrE7u5KtY2zmd+81GA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.2_1599079842098_0.6628039664072316"}, "_hasShrinkwrap": false, "publish_time": 1599079842242, "_cnpm_publish_time": 1599079842242, "_cnpmcore_publish_time": "2021-12-16T16:48:02.956Z"}, "6.0.0-beta.21": {"name": "@vue/devtools-api", "version": "6.0.0-beta.21", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "1a32ba2d819006e71baa2893147bf8c078ec29c5", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.21", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-97C2evr7bKxtXEmgMiSuidKyW0fjdGJ6UBIAnUIH7vu5AHgp15u5h4USkeT1qj/jxl0wyOOfcnvevPapyDGAmQ==", "shasum": "74d78f15d61e7da31b3ed918e955c42aa94fbf6d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.21.tgz", "fileCount": 37, "unpackedSize": 29414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwIZ5CRA9TVsSAnZWagAAPrwQAJERrizoxQOy9VrdHRaN\nqq5Ui3w5OdUlcxxehIoFRxh17noIUCT9djtiV0b1IruZwInqwBh2kCEVRDUB\niUYIoOICm8wwzL15vgeQHqcaKMmo/MEKr8ry1D9TMyy632zEaRwNTEnfw9fy\nzbw2rzDj30QmVhShFvr/A+ZcO5lGEOMCaVHLRCsfUAm2uSpPejv8jicGdowt\nYiaO4HZ8N9K+dvb9NQbOmBh3tBgQSnfi/+ZGDYa83Z+yrbbXcZkuREbIj65j\nTJVL+/HEHEi2UQJr55HGooHtuELu1GbQzYiRhtLaw1S5t8l4eFO/oFh6C2pt\nteDXorBn6pCC33sDGn0RCwY0aOB5f4ftikDBaW8ULNAlpMiafeKbWoMoTrTO\nIsReEenU0M4qkafyOMnZR0EIq6lH6tIHU+btl/if0xnp7xnfMwvANtUMjeL3\nEWaziIx/ox/bHto2WXuSzGj4TPHMwK6avIm6fRaFndtvzqFD1D6k7Rydze4m\nIFngL8wgZn5D4+QlzCk99HromQ0lzbJ1B9jNdQ9f7iTo0wdiPz3P160pw/kQ\niDPthJstC9eoD4DWAtr5SNT4icxevx9/fGVtSs239MPFT1eFuTM9z4uPcfeU\n9PsKyenSzukMhv93MEhnp2BM9XorIrsEINuCXq30XfXDnzIY5HwesISc8F1b\ngTQK\r\n=TlaY\r\n-----END PGP SIGNATURE-----\r\n", "size": 6467}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.21_1640007289641_0.4412726314066864"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-20T13:44:58.468Z"}, "6.0.0-beta.21.1": {"name": "@vue/devtools-api", "version": "6.0.0-beta.21.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "09352751921540500cb15dea26e7b634523ed70c", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0-beta.21.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-FqC4s3pm35qGVeXRGOjTsRzlkJjrBLriDS9YXbflHLsfA9FrcKzIyWnLXoNm+/7930E8rRakXuAc2QkC50swAw==", "shasum": "f1410f53c42aa67fa3b01ca7bdba891f69d7bc97", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0-beta.21.1.tgz", "fileCount": 37, "unpackedSize": 29190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwM+uCRA9TVsSAnZWagAAmVcQAIxPmvmKYxzK+qxO53LO\n5qW0CS0iqtMMlTvNXrKl7URLb7VNj89TFKkDhYQVqDgFZeLD89oWj9Pj0ufZ\nFo9081b1A73hxoTxhqQxS+GBPggsLP3wsSBkGFwJsGsSvn8m6T/SUihwPxxP\nec00r4rHYaN3Mu9QI/6SmnP47y1c/Q6ATsA+K/Y/liQclAIl+p7SHm9OpwRo\nrHD5Pm9+jvoOZ27PFm8e4QsMdBBTF32uLiSni39h3H4vVUu5R+QpIgMc+Ycm\nQBtKwmYfPjpbqgztTEfJBBYwh/RtlfDzL7GugqLBtM+Tzl6iWwPJrU1eMOpa\nXaPr7Vj/RtLo4rpTFUlzfYvUlPcH+IA/lDHzh/igd8gw/98Ha1wXv0bGmeZT\nAovnd7ZVKXMeYF6aEEx0xNhOrvtG/nNNB8NBaCtNFHRG8fzV2l5cCj8EdhyE\nRVXN2G2sR7b2UDDIxfbcOzEzDBoKon2nCTbboVoQJLrdeeUs+C8okf950xqt\ntS+AXps8ELNaeXVFHVwjbluqK5lar3hUKQK6A7FX6m7UqOcPZjrzUqmkCskm\nLCwfSIdCoVzi0iijdCe+7GuOQhbPPV6B3Ejn1qiQNffdm6MyXl/OFNyFBMXQ\nIe0DrBsZFFtfbg0KaCIbN3k20/q8gpAal6r5GdoXGJ11j5wYrIKd0xrR1tfj\nPj16\r\n=2V3Z\r\n-----END PGP SIGNATURE-----\r\n", "size": 6436}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0-beta.21.1_1640026030186_0.8938510819052317"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-20T18:47:42.068Z"}, "6.0.0": {"name": "@vue/devtools-api", "version": "6.0.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "3820c8a4a75bc0df34c6c428cd3cee5f55d826d6", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-v6i9kWtUZ3iiOGOj43tkisp0o5RJ9jOG3/tDA48J51TZt31DG4sr49mjaZOG8CkWFVE52bkzyozZmwcNrGNviQ==", "shasum": "f690a9776ab3c19f5605f4b10daf31a10221a405", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.0.tgz", "fileCount": 37, "unpackedSize": 29180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAOfmCRA9TVsSAnZWagAAQ5QP/A5j15MzqqEo0pw8YEan\nHJ76iFwgY/9VVMW/6XC1amLrimlu4pm4eKM0hfEKTF/I1okHy232yfDX4Hcb\nzYrmCxKMto8uaR+z1x/qOsPGczao/OXoOF2sF4pjXdbL8VRKY84iE4OqkMRw\nPq+izrhopFv1hbAf65YimFIPIO48CYxrmC3gQtYw7HrW6RVCDi3M97cSj5IM\nbYHNgTLZeCsoFuin9m0b89oqa5EkPY08wTrhdpIzpgfgviqbX631GFy5TPrS\nF7SArdJpWsytq27UDe0YPM3/hWF83MSyGzsaDRmEvPIneSw69ULvcyOeS0Of\nYpU+EDmLwRvO8nxWBHZ1z61r47pS2b7nSWjwsqD28WbAH4hRJwPRHuA4JVEx\nO2kqoTGrDX9bsCwDo5C3zCrhJRjzDaxi2JJaEuDQiuUjTE7X7O6ju7FyEFHh\nVY+0hWkVZnLfGUM2cW7L62EtHgzCIBu5F2xRYt6pbeuSW/Kr1M9RA6OaCUza\nqpFGliEQyD2zaSVwOOdiMqRA8LPdXjy+2CRNBpDEizXdqVBnesxWtI6P8cXK\nWVv+8gREOII7Sf7WZAJtomYkrZf++aeoVzvC+D6kfzzqhKr1A3zMAZ5XKRG0\nayLvxcyUlvE6iMSviUoTEyhNBAzmHjq1/7SGOCAVpcdVDkh4BLh7eQB+XSmP\nRA05\r\n=iLMt\r\n-----END PGP SIGNATURE-----\r\n", "size": 6427}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.0_1644226534401_0.27295738447398765"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-07T09:35:42.334Z"}, "6.0.1": {"name": "@vue/devtools-api", "version": "6.0.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "085585b5ed0f2ae7569207378ae82146cdb07eca", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-V2BKGa9pHf/sY2oBUr4uO8yF5MtgL2X96uJq2cBPxPqEUEkLfhJrbpU7t34JRjnanp2tkDJQrQsrsoMltHnFNQ==", "shasum": "c198200b9f84c7b6f7c4976b0206cbe1e7f61dc9", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.1.tgz", "fileCount": 37, "unpackedSize": 29180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAU51CRA9TVsSAnZWagAAwGoQAKDSugd5eGBg8VHPQJP/\nD2MUSMAKaN2QPFJR8p+5YdEskvA18YJJ52NeP4nGQx488omeKyXF17ER+FSd\n9NwjI+swHh58XPyDefMlHjXF7dlMqywiUmSmfGAYwJWCeMVc3JAv8j0Oo79W\nkqIauGLqHvj6Q7Vaq39ybsPxGHiMzfWcMRd25uYD8B6/AtMpROtFB18ulTm5\nr0bGcLk68/LhCMOma2ORW+u4fjGQBX5SJ6BB6vZwAdksfHwxGOvuzvvE7NVa\nMNoe+IZLjiWNylsMnL3YGVho1bgaNEyheTaxhdpq3zqEUi3AKGSMnu1VYjWt\nmEVXEVXZAsW5yeHOc/VJQZX3yMM36kEm9hvM+hlOJO+d8Vlj8wxWs9h0nyQs\nniRKjkCLk2fZIH+vUIru742fN98HpKzgSIBIf/AVUv741wSkoQKtvatQLPdF\n7KS7dXnTCMbCvr+JoBUTaSkrLoWik2MP/V12hTgRjatmGRuoenSx0l65j0ha\nDczOwVLWcpZllf2/GGsKJ7Jv3OokzeZ9sI2yU9lIsBGfmNRABblgtRBoAhYu\nH/PFEXkLoDoskL9Lj1WziS1xycNEFsiBBuBf/R4joOmSdVQyULScyL0rItD3\nMNUk08AEElB/uDpHLQVJUvl0mbvHkpk81oHpAAAFXdMeMwSBHJsSADdQ897d\nQ62j\r\n=CkSI\r\n-----END PGP SIGNATURE-----\r\n", "size": 6426}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.1_1644252789562_0.5690842935501539"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-07T16:53:18.702Z"}, "6.0.2": {"name": "@vue/devtools-api", "version": "6.0.2", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "daee8840d869043fae5223e15fefc7008ffc7a0b", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.2", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-1W8ylfudTFepNgpgY1Dp29iVPlseIy+0+4xXoMkxKmdnxWx8vm8dNgz4F83zML46hHb9aP382JURFfqLuJcWYQ==", "shasum": "92267911dd2772c5a7641d2889051c6193a7c6aa", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.2.tgz", "fileCount": 37, "unpackedSize": 29182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAl3UCRA9TVsSAnZWagAAeTIP/Rl3j2PAUY3CWgEHvHY9\ngnbBs7tCIkoyr6oNlSEI3HUthDTanT4Y3Q/jexhK6ct3XtN3Ug6dCsynlju8\nHG3Xyp7XFO2M//HKPa7rpWWplv4XW9Al6JHpMQkKgieNxFoa/t5nG/4IOQfX\nY0z22CneITV6gW8GP0A7ho2y60NeVNKeFI/HVLPs3Sf8G+PcSawS4ZI3Xc13\nwYI4c5UPtyjE06Zm0zeYr2oSS8j0Dj3kYpGd3GeMLaUXQ6tz5h3FommejYi8\nJQMeSp4LqFlpoJFQzXmihyWMucqx294UGxUKvdtadceHBfwNFDt62K0D3e4g\nXeLeS+Yn3JYrjqbW6Js2kDwPwuF4p10fofZybJEyfI+qjc2Eoulxkef+GD99\nPPRlLDtbzh9a6KK9GL/KkJbAamUNSC6KNhi/6m/ABjLkY0w7SPQttVcijBIP\nOKq4qjt7rIBqkS2032OxPH15NIxKRHRhuvY/iGFTFyrTfyA3Mrzhq03Lctnj\nq3N3RSgVC9aUcjFVoLczXB3EL+tUPqF0KCxWl/PAeuVJU48Da0rY0+jfYY5U\np+2Erhz079wsDIUDPprGMVgoYV/anrrcw0YWXcUrhOlf+02LD/n5Y9M0bIGA\n4PiPkIIGtImmujYDZGpE5wqsJqxi/Kxg5Vi3Q+ITuF36/BDd5i+dj98S0pyv\nLxnl\r\n=i5bl\r\n-----END PGP SIGNATURE-----\r\n", "size": 6421}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.2_1644322259999_0.5266066422881404"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-08T12:11:15.094Z"}, "6.0.3": {"name": "@vue/devtools-api", "version": "6.0.3", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "d35a998fc0edfc2545484048162be955531ddc30", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.3", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-KSVo6rqmaVCdzMBE3ZLz7AFNnWrqAwk7BJ3uYuzFi8kQqwuaGWY6QH8ToVw7QlqFrqkUbMq2kyvzRURrQZN0Iw==", "shasum": "490354134a39474ee110738355a4acd946b21180", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.3.tgz", "fileCount": 37, "unpackedSize": 29182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA9NRCRA9TVsSAnZWagAAJTsQAKIzMP7X48SvpW6TCTDT\n31F4HtWhZHnyJdrKQaJUClMVippBHwej7C2vfy8MQLldG+/jzQiy2Gttj+Rk\nfFXS4qZCo1rwKQF3XIs7R191KkTR+PctDK52mogJyBBSs68hZnVa0BYnTvBW\nkbpIimo/fx6yq+5xxhPHyCrpCJJC9PxLY+ixMyXxF0Rz4m5tCbxtIrD2/HJ2\nZOe5iVGO3/tEE9Uy6FCqcFyGiMqiJXBzL2bLjIoSSCFQ2iXYBUXx1OBVLKoU\ngyJwRWbgrzZayNylRrjeUQezaPdbBLqMwqLnh+E8vnWDQwo+yM5Txauwn/pn\nJkP9Qrb3BJfOinyp+M9N3fw/f58BpBU7IY85eU9o7/F2O5wv+HYv1QADTiNR\n/Vf24jEjeKSxHHAfFkesEdCwg3S7rSh81tudJp3da4dVsgQbp/UMDdtRkWyp\nHz1DscL8Px4q4aMH7c1VR2cWMHA135En2iabErntsO223xkxMcKh/bDDkQKB\nHOD7gn5/GCPYpezfgOdUVtTy9NK7q88M0Bs7gwV6h1gj48zySKhSUfWJAqgS\nkBSydQLeK81RNxt1ENVXvBv+SI1c8vzRSL5OzsNyRhpub3zxnx7So9C9pBeW\nZT1Ol71DVD+HBJuOcuwBY5+mAiMlXRx9n4b9WQ3qs7eRVtgU022opWFlDswE\nR504\r\n=FU1l\r\n-----END PGP SIGNATURE-----\r\n", "size": 6422}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.3_1644417873462_0.6732845159231271"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-09T14:44:42.877Z"}, "6.0.4": {"name": "@vue/devtools-api", "version": "6.0.4", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "dc0f5de11c73c953140fade6aa97bf278d6315b3", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.4", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-vk+gHlZCyMwLVwon4gnk8HY1dTU6xyNmxuv36QvVfNxw2sOMof6lD7yKH0KTelPpxCypMAQ3DxxHsEl9zEMYUA==", "shasum": "d183c513a26d72178218717efa70f7f981956bf5", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.4.tgz", "fileCount": 37, "unpackedSize": 29182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA/sDCRA9TVsSAnZWagAAl1MP/R9xnHdo3Ezb57MwJayw\nZZYKr2cIQVqxkNLTc7sJ4wV6jR61fcuL5EbqP4EiiVnunLXPBdGi3VZSqpMm\nxDf697dfDlGJ3Zwu6DQv1nz//sWNGMoVWwprZCYUuY5bzbO8H/WiGAYLTOhL\nkOw9e69PDQEZVkoY5IjbLAGEQpZ632F+qCZustWkPEf5gZujYeJds7M5izNn\nCPdtXALOn9IXtjWj4NKW5Vy7RsdPyL79v5VU4/jE5WOroXJ8OXjiNOqdjfki\nPiZ0DC9SzpMza305DYEitXBS7/HADP5GCkad7lil8lOVDUmg4VzBdlXryW89\nTkBI/DCINOUEgvcnuHi6HZ0D/ahjjBPyxtI+AMUTjDLs2si+NBoXzYL0EcJ+\n7rg7JRPkuJSpljpwxcRJ8J+ALGWE5/yWMrIw9teIAYluBBgZOjhlKFL+Sorz\np86a4V21tPhgT2LZ0EaZqbHKqGNJlmWdrQD5Oa8zxP/PJ1tL/NP+QOiW8825\nVaTiSf/Ardboz+cQs2PgB5kqBeMxYWUhSUUJqZISXzOTzhQcEZ2KpWx6/DWp\nOH2uWRY7f3NeJdJmMulSpZLg4k+4eActn08vV2CFVZzTzxZ0nkib2rHC5yPm\nCQUYVmQJg8vi/xxgs7xOCSCMu5Otrr0QNq+uXTuIc3CSnFFTY8RGUEpM9nb0\nZB6a\r\n=Pc0J\r\n-----END PGP SIGNATURE-----\r\n", "size": 6422}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.4_1644428035094_0.3111612160678958"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-09T17:34:04.183Z"}, "6.0.5": {"name": "@vue/devtools-api", "version": "6.0.5", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "8aaef261a0a42a3b532247b2d341baac48641134", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.5", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-2nM84dzo3B63pKgxwoArlT1d/yqSL0y2lG2GiyyGhwpyPTwkfIuJHlCNbputCoSCNnT6MMfenK1g7nv7Mea19A==", "shasum": "7e35cfee4f44ada65cde0d19341fbaeb0ae353f4", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.5.tgz", "fileCount": 37, "unpackedSize": 29182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBEInCRA9TVsSAnZWagAAeYUQAJQHmi6jxyDlI76kicY5\ncUDi7iGauNEtN/7BsT3lVCkTLMvI5CjFV1UNDAmiNUlg0Gfurw3hxpvlkgPf\nOzzBYqrA1RIx0MEEx5sWjO4TgGopnHxB4JmH8fg5/Vhbx9hn+/UkvPBt5Xg0\na4crIw6Cx+s+lE4ETqWurqkgipM9ALRnb9H7r2r724q8IXFtEVWZfgE4DW39\ny5Ok5MCVtJi3+wE93UFAW12jU3kpORLbz6VGJEZ/s0zlssKY71IdIjkFcq2U\nY4YAd/gBMULHu14WDwH3ddwZO1vvZMwRgFTZlpICITVrmfFS2v/yHnIK+1ob\nhK2YoZ0Q3SGJdpHR5o99RBJphfLDZGiqjR/buFqydqICwAid/0uvafMN7alD\n1lrY0eYnohpI+MRHZ0mErWAKEKzTi71tB/tyqxX8bQSMvXJWhiuJMwu2heXk\nhN8fscp5AlTLckw8m/n90JaCwQkWwHV0yaGppzWHXfPJYbHVaOVDOc7Nhsbu\nq6TI+1xkFzeNDHplnHX5mzyF45oQHShdq80niZwVUc55wP0WgovCzvD4pCML\nttmjESEBkWw4ZubsyCnjUQZ/ePPRKcEH48r1CmKEK6XFW70nSNOrIziPriox\nQvT6yYGXfDF2XWl3YFOpYYNaPtFF3B3Sc330XCgOlGegajMVRMvf3TNRt3QW\noHit\r\n=4ipD\r\n-----END PGP SIGNATURE-----\r\n", "size": 6422}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.5_1644446247566_0.7996756084226779"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-09T22:37:36.008Z"}, "6.0.6": {"name": "@vue/devtools-api", "version": "6.0.6", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "3cce6f3bfd61b7840d6ba9f9446379a70d0b6c53", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.6", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-QxGATXEuANiVhnc+8PrddyZsyOjMa8ftBXJohl9X7fG0CnlUK2kmKaRfHsKagmkfEpBg74xMkqrkVTEMJYdAWQ==", "shasum": "4bf8bfe8f14f73c614d9baeaa275fe3cd1c799d6", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.6.tgz", "fileCount": 37, "unpackedSize": 30298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBbwfCRA9TVsSAnZWagAAHSYP/2+UAhXsvz4xq0ZYxwaZ\nlraLL+7eHfkROoU6CPRuWnFUHjQGOZOZKb4NXhEdB8l+S/sVIk7Arq1x1RIn\nChDSh+2FmaNP3pLVchNdCFtLyW4WNUo+w1ccI8OUllVI+uuoTd6u50roDKPH\nqLsiQ+T4vJFInXXuup04rAg4xFQy5M7VVhqdzDdp/3otJW0yUkwdjev/Gexs\nEDkMTMzcvq1W3kjfWX2UC0eqvlW66nHH4RzWj1Z9aJYxonyo+bxHK34eEkhl\nPxWxRIrGt5n0GwWyFqbZIYbHJDUb4ocTE/JUP6GGW4+So3FjBip3485tBmIB\niTDzyziLAyI7bLcKqBaSs4bkrc4gI54nBb8/au24rh2ClUJxsmfwfnocar64\nlWRBbyguhd2KwiShR0jG9DzKAkUc567hKYAbJzWtvB9DDrBFgNrJktn3ziGx\nJnsXzcq4YBm46JKTVrDxqk0eyDqcuY6BUDcINt9mlCjMBr4hcGIhX2/3mECM\n5OD3H9V91pX+5ixRd5jAVrQi6ixGgGYXvYKpihnImN1QHgIoyMQLBbSXEJbM\nGFhkr6GkAWBhwC531blZfz/3zj/bZqxnf5w+DxZuItoP7ykop+cQ3lXke2Uk\n4rcOVWOCPwoi5P0SgtrcCIAG+YE/pMjk+q4+mzB3CwkPmbwf3uao1z/2LRDU\nZ2tY\r\n=RLJg\r\n-----END PGP SIGNATURE-----\r\n", "size": 6727}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.6_1644543007837_0.23840582871193794"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-11T01:30:26.077Z"}, "6.0.7": {"name": "@vue/devtools-api", "version": "6.0.7", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "a7bde71073796ed938d4b793f4ab838320a07820", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.7", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-i7vgWvkzP7n2L9yCH7hh+JZOYZYylrd6Pw4mwzZyk8CVYzy8Vm/Kl3bEmurgFg7n2kGV8pnm/qgegNIak8UomQ==", "shasum": "a54b3b99b19ab71e96d40d005e9b1cec19eae5f1", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.7.tgz", "fileCount": 37, "unpackedSize": 30298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBpbzCRA9TVsSAnZWagAAz30P/2sFR8vgHEufZCYt+MMd\nHHmlFCMnaBoLU8P3mlbE7TgH7QGaWIpFLP0dl+XwrCBT+zk6kVhM/sdnpFOh\nh2XLZUYFieWwgTFlr/Hqe5LJIgg1pEpeM5NHNodIgYG4gxxaCP5QF306L6WL\n/nxfq0rHhnaHR4NnBdOyuVT2U3BdKJJzfvt33er1fzCQ8tFZP8f0u+3oaX8i\n7Nn+wlBDX6DdzDwN6KteOMqs9kspAK6ARWlxxyWSKKDUINe4Te7jA4SVY2zE\nutLOQsl6GYUkKCcTZWsI7dyriyR4RORYaGqMDXRtCP3q//iv9Q+UKangF3S7\n37TT3eN2ZlapHHyzzuXBPudRrRSEehkOteIK4ogrtK6Br7ndREC8dWLpgADe\n2hq75OCN0laEzLJh9MPsGLnynIkkWyedsQwk8iM4KFEABR9kgWal3qDHuhbA\nYuBNkNt3YQQVdEEHJobo7+RtQl8OYPF4Kn51yU2CL9DFYCE7lLa0x+PbVr5Y\nFxC8vDa7Ta1/w3rmtYtjLAwPf0zKRyEVE/QiwxrVClFNoOw1pQQ7lqxc+0jd\nsXsbYXj6V1vTZCjYc8VyugZ7JfJlzNtTvLCJAv0ubFixnaPqg7ssRlJeU53Q\nZXHEKIeavbqQqnv0DWcFPVT9m3MNp0CfoSCVX34U9cVjRvdxUuLuMc9+E12k\nSZz0\r\n=04HK\r\n-----END PGP SIGNATURE-----\r\n", "size": 6727}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.7_1644599026942_0.3500988929128621"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-11T17:03:53.051Z"}, "6.0.8": {"name": "@vue/devtools-api", "version": "6.0.8", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "3674f29495dd1c3b3703fe90f9324ac58a3cd1f1", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.8", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-bzei608bPVQE2yq9Ghrjn/dnpf27mWfrr0q0ZQiuZoO3LQqFG0T1xSGz+9vw1j9KAZM2Cu9vdQMkddDxqzZGNg==", "shasum": "78708d2d437093224f00b775f8808e2c35d024a3", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.8.tgz", "fileCount": 37, "unpackedSize": 30298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBuy5CRA9TVsSAnZWagAA5DUP/0z11TiQLx5ID/qhqhMc\n+oqObCrQiJNPOGkLi6g5MtDaBavjzv2a+buvJYJZQAEPg+fNrdXXgvVvBXDn\n8Y20krE6NfyRMyM/pSz5/tKO8QMYPKRM+XmDEdB09pFHblkZoQ974f7UtHl/\nWXZuQr5RWjCj22mHIpVYZcTv3avEZgzefQw1AbdDcGz/ixLVEUiuTilV4Buw\nRG/tePkWq7e6bw/6Z+cvzRLQFMRsveTeDKFz2dC9B5/7sZDnHA61aekt+i5n\nbyu+4Y1gaJHlD9jN8YIYNmjB/JBYC5NXSWIr4cDMp5lmjjndbTxG4c4Rjcz8\nBW8qeClygqmRpSiJV/t923tNv9cE2Iljb90JtOf1WsCjZf9utMStzf2lI4Uj\n3Cx2B/P4RXMuXPwMQtmy5EgwAS80XI/DHEewJnScH5APnZDG+IiQ2bssIP+/\n59GD9/S/qBVzC+H5HdI5b1culDlPgKBfbHcy6RpPw/dmdMUfp8+tJIINCuBw\nO2eGBTseUzus3jzXOxRy3bx+EmNfxyCFE07xGDpwoEsZYbNA3MFePpXBB+XW\nrMptNvVq+yQo4psyi9U5gF4EPa8Xzd8IZ6srofnGBhX9vQkDdbu6fxV4ymGH\noKj/fH+gZpA6cZH0wcjBOUwNP6+Gy0xqhvk6kHiMOSxEVZQOL5MSOhr8xy4T\nVQol\r\n=JIEy\r\n-----END PGP SIGNATURE-----\r\n", "size": 6727}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.8_1644620985392_0.5108555777118995"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-11T23:09:52.196Z"}, "6.0.9": {"name": "@vue/devtools-api", "version": "6.0.9", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "77f4cff01ced77793b22d286f145dd842a69f6e9", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.9", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-O9tAMBMNMAMzgvbSS3OZlTpfgKCGjvjo5LTjWNtQ/M6A/SKWghJfvoMKuQL/vykLb6rjB/AkaQVFg9yDHprN6w==", "shasum": "d2a82ece1ed2e66970c0611e47ca8af9875a3cc2", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.9.tgz", "fileCount": 37, "unpackedSize": 30298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCjdfCRA9TVsSAnZWagAAr8EP/RMuDAp5AqNQq2KL25VU\ny3EAzCq5eV9CfdfXzX9LxHDRA5BgwmcvjeA3FRZHBH8QXI/TrHd6rqhGyK/7\npZr86RSydQiBL2Lf03Oox/BNKr/bvQzOKSEOg2BAvILnngfBxXI6BGciqiX3\nh6bime3KXPOk0UGcrWutvkFS9mdeDSB6G6CfnzoAe7BCB/dBA58Cpxodg+8q\nLVz+vSTJ98jc+LtguzPxR2Ls0Q3VLVW9Mx1YH7w3ERKoiP9rj4ehl8LDvnys\nTNZcFRx0NpYAMelbtoo/soJIDb3MTfwPKevCz4YZQKfZmcwXncQLfULHwsNE\nng19ys+O5CO5MNyZT3XKhGnPZr3c2zjruKxoGgbZzclwwiokgp0/jZtOfb4j\ng7y74EbOnYKN+w0LDyQvYbh8dvlVAEe/e/DqlCVUVDueu00Sa/lvQxWJI5I+\nco22KOYiAtVhxVyqQTaz0guvbUno35dRgpDr6JSPCc77AMlvcU1tpqH23reN\n8W8avrjOsfE4r5DLb/Rcs2mZxYKnz71ir2dF/O4qL+8OeOAahr6aQeylrwME\nA/u1f3w3H2KKcrjJFuFr7mZ8RW3dnDe5iluXVvGoI0qjK/5Jm3ZlSvKG0120\nGKb1Q+9TuHn5Y24oQjR/uGQL86e/MFtdHTaGkF+vpRENwnddM71Sph4NBzLO\ndhLE\r\n=RHJE\r\n-----END PGP SIGNATURE-----\r\n", "size": 6727}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.9_1644836703268_0.2310480582947565"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-14T11:05:12.975Z"}, "6.0.10": {"name": "@vue/devtools-api", "version": "6.0.10", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "56f57e4f09f273300e0def47de5d941cd08c7124", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.10", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-PxZDEDYOHX7rEdrZohA+6yUv0AOL/7IRzW4nbpWDlnwsmcyP5nAfV1yODYaConjL8HBxB/tIC6QmHu1ApW2muA==", "shasum": "097cbc453e76e37f5ae2c5b508058113f9855571", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.10.tgz", "fileCount": 37, "unpackedSize": 30299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCl31CRA9TVsSAnZWagAA/OMP/09ACiAJHKwX8teS0L1m\n6ny06J7TbsfKRvz26ufmxaKHB+68al9+ouL0xgEVzvjd/z81oJY8v0tYlcpv\nXyTtz2GziTsLBLmqxefRR760JZCaXYejzqBMQ2O2j3GIhxP1LR8L9O1Lr0zi\n0B7D5J3Ez//D05IQVn7WJ+ThV9O7MXVF2b7mYsRI+hWJFQkSrylEbVn/Wq9m\n8NTMfVyvtBGbt46AsGqIWrjXWCbORmab5qfbDBF1200w21Zf4ZshfdyWhgC6\n5uolLKobnmDPVzrjuVNIlpowT3bY73L4DjXjDxUFLNAbQ3SHEItRrfd8xD1w\ne4LFi1u246m6J8lUWQR8qIisRTtIOYcoSkmO/omZhr14JlwIhNtVfVb9216H\nynCHGzEuin53mUhPyh+sLuCijX+cUFE3XLKEwLDAs2K8F1uGpyXPnj+U2bNU\n64fPvmLevYI6OEewSrkg35VwoDqXUByTEUpIudLdjwr1BmhZQUoCc2jtpBMr\nyRZccyq0+4Y1A7eouxkXHIG1JZ3fpU27yniNpiiufL9Pg7EpYsK+y9zae0s+\n4iA0JwT/TTBRNFR1cklFXBruBMk+CcwzfoRG09rselWx48aqWldPqKTS44y5\neRx3cTmgmgfHkthMmXGzbPRllaCa4tBZpMpfkKbgvE1TxDK7D9MBfjYAOBSM\n+8EE\r\n=euH4\r\n-----END PGP SIGNATURE-----\r\n", "size": 6728}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.10_1644846581048_0.5775285029697996"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-14T13:49:47.589Z"}, "6.0.11": {"name": "@vue/devtools-api", "version": "6.0.11", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "384901ce3d77b29cb3c9d1e4273e7a28f43e70f6", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.11", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-0X7/3plmtdBFAG0dS5u1utBsA2++E/TJxWQlnlUydP/21q71CCn/F4b0ir49us/Cn8mzemednIL8ZMjX/boiQw==", "shasum": "f855e49fb4dc8b4f368488d300dde793f434dcbe", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.11.tgz", "fileCount": 37, "unpackedSize": 30299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCp7hCRA9TVsSAnZWagAAA7UP/0QNCRbNlr2WZTmVAHuq\nzyxuGA9Y6G7LzIXqCrIyWKJ0VOl/Uby+2JLcCyhq8a7mQcvnFRpA/2oMKj/v\nQZ9g7d8solEbOL3i9oJFvZ+SwuKJ2I1a+2CVbFP6A+Xrrf0bgShno0CDY1zB\nH5i5JEJfFZG5zfY0PJovc923rwfF7kO1/NeVGbM+GPIvLYQT7OsyrS3yACAV\nM82FFDLznjfzfvAtxvqKf9qhqaO/+8DdjwBv0i3i04d4L2oZpo6R7GbyAk8A\nZ1q4KwMMDJE79iF2yzILMKtxhqsr7dCPlxfl1n5GnV9a3A7Rs2BBX4o83iYL\n4EpUkRmZecX5eBZh7eYI4poVi8oBlQ/2HpAYhA1Y4y/XrqS0roOoozzKzsRv\nUsm7ohwGjIZTQ5ec266hwfoi/T0bTfVxkvN3weutGyReGhc/a6Tk6MMPFAnF\n33sJLhYQ4O88lGHJeOL9GuknZ3jPDTTRY216OyKhHC77ZPHql8sKNnh5+F+P\nW/zsIjX5sJJznLXLdUd4LVaDEJsh0F9PDvD6LTAH/N5MW/zQXtxg+J/Eevnw\nVdKOelLIjOoweI2cnhaZ0EnXTzp8AGiyv9x+xYKwlIGkX98nZ7X8QgHCWDF4\n0uXnMSvawRhYhnnwJANI64gX8hzMiGUX4rVrbFNr+nVa9eZUPRRu2O4eLU+p\nNfWb\r\n=adrO\r\n-----END PGP SIGNATURE-----\r\n", "size": 6728}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.11_1644863200917_0.3178243200448576"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-14T18:26:56.806Z"}, "6.0.12": {"name": "@vue/devtools-api", "version": "6.0.12", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "33211b33564bbfc0b3da7ab464adac41b7940382", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.12", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-iO/4FIezHKXhiDBdKySCvJVh8/mZPxHpiQrTy+PXVqJZgpTPTdHy4q8GXulaY+UKEagdkBb0onxNQZ0LNiqVhw==", "shasum": "7b57cce215ae9f37a86984633b3aa3d595aa5b46", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.12.tgz", "fileCount": 37, "unpackedSize": 30299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/q9CRA9TVsSAnZWagAAB9UP/R2NX2Etr3dPrjgp+4EN\npeQQleOh2CTbNM7XfJhx9uf9cr2i6kufT+pWczBN0c6W1kAxZ1BS5OjpUBi+\nw6r63ecfIaKl/g3NOR/JqmWReI1yNIpkueqpB9OvIT8uHVM9X7SPgUmEhptE\nXNk04bp6cO02iZrWkhApHDQ9LBCfIT9ccZesWgKWVNsAwKa3A5SI/IDMnLQn\nma2DX6TWDWU66zDSdOO0o6oyW/P+l8RM2+XLNC9wMbDuK5SKliH4EdHaW/mR\nf+Mke4cG80lMC3EDn2tm9LrdJiLebQyPa/7s+ArFfXq3252WjASOEQQdId16\nRtCHZjCoBrbrDTqTCIwQBBjVUyL9yPQhUdWqH8xzscnke7/i7cFFBa8I94M+\nM7KuWPBbuJ9/iAmhbb9NW6VooX8n9j5x4eDmvlOdT7PQcmCnUiFtz3MmvV0g\nOVgjR+Ms3LX+sLlIm6ge0uryg6GPfmA2Wx9wf671oJf9iZDroYjqc2xWvPQ6\nlqS4DOTMTLl5pMFXqQlYtjeVVwn8u1GGFjD+95sKKMbNAOneXkTc/Sk7z/DM\nucK9Ms1xl58OzXXT/wSgVmS63tjM69KcDFbc/vUq0bpWg//Uk7HrfKDmEWPB\nHrcySBgTzXEudda14+R9df69hRemHJBrf3oUKOArfkBZCeqj/I3F0xuC9bDn\nBPdZ\r\n=Khqb\r\n-----END PGP SIGNATURE-----\r\n", "size": 6728}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.12_1644952253589_0.5052115161775541"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-15T19:11:00.079Z"}, "6.0.13": {"name": "@vue/devtools-api", "version": "6.0.13", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "859d2b265bb2c0f0d6d745f445d4fac5a1ed4374", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.0.13", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-T34EjcArVqzANedEZe2kXQ+AZsld2z1ptJlkOGm87+blk+s6udnP4ze/NYqV8lz1o9AIivimN0xxteLlWiWQdg==", "shasum": "33f8debe2d0239903b6fc8af10ace45ed3a4fab1", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.0.13.tgz", "fileCount": 37, "unpackedSize": 30347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKOUWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHkA/9GIG6eC2/S+PtMCnlk0Lxtl4CqCsi4RK0YUbgCAtQgMtzb/tb\r\nD6hROFx/Q6UyhLsCbk3DHJntKwVGIf8VaAZdDNXiw9ItDQwPMw1rtoT7K9tZ\r\n3sn16v/8N0Qc6DYSWWnsiVzcaqQGElWk3c02L0AtLICFABXJst8fm5KWw8D8\r\nG8T+xe62tNC7+ow2Ezus6gLxluOJuI6OjTjuZItBXMLG69L9s8jTyr9a98iX\r\nxcjiIjsGgZRxteRh1JWgxL1W+s8/MsAkffkyHDNJZrtiw69CVlaks35ULeMi\r\nW4ZdOixfXXV0lWJlGpLrXGByRYzMEe5tAygIYmHkU8nGqr9dcazl1Lmh6CTg\r\nXfeUAMRpb49XzQVhBSdM7AweSN30Xw+Vm/JMgFohoQSrsZEo5+K76PEgqlW1\r\n7GrH9e8hMEVsda1OT1bQBbHr4/QnAQkD1iZ5Xey7Qd6cfQ914l4thKxU07Xq\r\n4Xe3EGRFr6lDqdWrIZbztfEXzn0rE4TORyYMW7CiQjhjZMjTkU+DyqiIFazJ\r\nanc99v5TiWBRIiCvuNSoEs+865uWB5b7AuP+01gE3bCKByhPxEof+GZfLskg\r\nX9RuP+ALtEiDaHUFNFsm63HkgBiIsnlLpHJrVt1Cs5vEafXWfLo/UrYhkKG7\r\n0fwyil6yL/YEHzbFonp+q8BksUOnwgrzyeY=\r\n=acvl\r\n-----END PGP SIGNATURE-----\r\n", "size": 6741}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.0.13_1646847253975_0.15095741502488713"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-09T17:34:44.718Z"}, "6.1.0": {"name": "@vue/devtools-api", "version": "6.1.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "cded0ce1603883c1b27b3f040a0d92e9d3acce03", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.1.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-1FtbxEEHN70WGJl1b/h8nLmyN+tOHONNsNLvgVEXF/L/vBrRqQZ0kF+dev1YAz3OtxsQ1sV/vPLKwRlq1axrgg==", "shasum": "5b7945e656bc0f85b1774c9aca1dbe075bead3c0", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.0.tgz", "fileCount": 40, "unpackedSize": 31578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiK4mfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRvBAAo+pR+graRZYe+ApzfRgrxW6/Ha1Y8QQKbm5uHv/YJ8BZrbJU\r\nu2vBc34Q2SqxaDeW2Ow2ItndsTBkmA5azD7qBhsN2SkJawpsL71cRXualm0z\r\nMmInGYGCWy09Y4Wftishv21P8fh/5FgnT4eJi09NbEjIZMNeici/clDa795A\r\nWt553q//liR9uYuQdmxCRSc2gkbfh0VzYQdENTTlfj58J+8lalu+EmPH+KmN\r\nWKFkOVMAYmhjv63Xl4jaXaoeJF3xi8b2TGO+/qGE+/nePt5MaphgiMR9KV3X\r\nZMqM3GffxBucATbbJjasYrWA7WserAIBWxDcKwB0wLStnFv07mLvYPZST7bp\r\n1t8EupjaNIOv1a6T1K44UjXgBtviAgWrj02T2695x2HIQVbb+5zWEUybLTGZ\r\nRDaSQ/GAonwSFK3dvJHEjGklUqevWA2jVbmlXWFJVC73rNV7Q37xrrSGVt9d\r\nfUbWBnX5ngLqrtTUFm7HCW4hPMaNC9KyfHIa9b3C4vs7omgnCMAzcKkN7KGs\r\nVhs/H6KjmJSXJiEXp2cf0ITGf2HjMVzNcOUP8V9UC0Y8Nc3yrJlMqgTpIzqm\r\nvPF7o/y2VGqjjTKhvG4KberJfOvemXFzi5lj1HMh+Z7Ui7tmdctjZmsEeZiA\r\nlUXlC/RwNIMtswoj7sl74ndaJbXPzkMao6I=\r\n=WEnw\r\n-----END PGP SIGNATURE-----\r\n", "size": 7006}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.1.0_1647020447286_0.30105035046979944"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-11T17:40:55.525Z"}, "6.1.1": {"name": "@vue/devtools-api", "version": "6.1.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "ae4918fc76ed568b6d3dc9ae131dd724dcd6a974", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.1.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-VeqwqAOtI42AWeiAIHaoigw+0Km8MVwzDwICgsX95hy6R4UaHnLtTdF0a5JFy4T5vacTeRnuWxZtQ0B/Cytq4g==", "shasum": "10fd2ca8d5b3cbcde4d6aec5c3fcc7113fa913d6", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.1.tgz", "fileCount": 40, "unpackedSize": 32214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL2CjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowhhAAli9cZ7uR3OviCPeF7jd0RvmhB/X3s8rjPCAECjwBFlqTGoTK\r\nlqyM+RTaWDG9UgeLGonXyURITSgXzXYuTOlPGRvvbiBEmdPmDvq2gwbmsLo3\r\nh+QnypkRnLUYaRgN9b+LVrQqLETgbHMzBIedF1o6Z8ws/QR/ncFQ+YrqwP1E\r\nh1UxKd6jbMTHpPtzJ1l8vd/L/YhMvMWfthrapDm58HAZ9Ty7J18YyuhsH4c3\r\nw4PFZ/ufe8aKQVwscuoinosMa7MmfgR4h8sb3p9KdwFhKtC2Jt8LvxES/gkC\r\nAgSZK2XyMpIDcafCPqyo1so0ONuqtFARZQ7Pq2hqG2ZTLHEEsjcyLRvp2vHt\r\nbRFq5EG+TfIhsqlQhriLbQlw/bd9NzuKbSUmo2RFdzRV3bnE2RMoa6brAutw\r\n+RbMZ79dBBDrvNB6lxOEyZcTZcvBLTz03mL1owKINTMbHP3irtmUz5dnnK+q\r\nuhMvKwDAs2jD+UV/Yc/Qh8z1/EkqKJHuNZ2NQl46K+rX78apssy98TyV6xe1\r\nG/3Z6yuoun6jK3tnMBeiCmMB4haHq9g6iUOmKa3XSK+S/s9aIYhbD31W4gUl\r\nvrZgxvTXDm5mYcqwziq2/8LzDoG2p6ERBDFu0q5ogFp9OvSl+8m2R41VL7Hg\r\nAp20X/rNRcDRCQrOq9GvqeJ2Ri1il8vTvCQ=\r\n=wR17\r\n-----END PGP SIGNATURE-----\r\n", "size": 7097}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.1.1_1647272099468_0.687429859726304"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-14T15:35:04.391Z"}, "6.1.2": {"name": "@vue/devtools-api", "version": "6.1.2", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "30fd3c8cb6b83db785b815b0624bb058c2eebd4f", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.1.2", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-/fCjHQUVDEAzWWoCxLOl7JVwj5IyYMD8I+DrnR0s2az+OzaBexSES43KymAmnZ78MyMBKdif/pLkkUPK/fEgRw==", "shasum": "1bcebad5e6b8b5b3f5c381c58423c75b97a88342", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.2.tgz", "fileCount": 40, "unpackedSize": 32214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3OUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe+RAAoSyXaS8oc7Di89VE3LYgvF7StbCvhBtzYWioj2p4Qz1blsGv\r\n68oy5ibzovLqI1PcrNwV1BuMiQeioiBpE+raAYP/sDEgyvNoGZ6egJpvPwOE\r\nSOM47WDQU0I0LL7mWLsyc4b+q/mEtKiFCr4Qwa/kBn5j+rc0agbNVEXZSFND\r\nw8N3jqtjBUlpy+9e8wuF4WlUSbgNPt5b7lVjKrpf6j8p+02JmmZ0OsfDWLcX\r\n8Je/+jw0GZAwSArfO0aJwBfrb6NBP5JfFD5eN8QA3mIPGeI/QUDPqHcxvlo0\r\nogJe7kmBzAsA8+uuiBinnBwraEaTzB7llc5v7fTx00Qn+fKkdlY64gsgn9Z4\r\n4V071Knie4ttYOv15kCWxg6Kq+WCdegQkrDicUvWKM4URK8Ngdie79xBTU0k\r\njza89JNgK9ZA0gUHXHpLIocrQax67UqOt9wnu3lV5Y3FiiPkW3XR7bUWsSOO\r\nrsJqH+VHCx6dAlgh1hUlo9KGEhr5769RKsdXfLGKijoJjtBjHMtAbaznCkNC\r\nzs/ePuUvVESp9hfvHmUaVeNBkzV6t6cwxQ23ckquUkL/io4M3JLOPVLaZNdi\r\nL5hgWPadcVAYOtWt1l7O1U3HQ/PGRgGheQH6gsXN0AsFK2v3daAOMo/rkWF0\r\n3lprdGaWdh0dtyHVGLMznoQ+AAZ/6E722ko=\r\n=WpcB\r\n-----END PGP SIGNATURE-----\r\n", "size": 7099}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.1.2_1647276947850_0.6569770065688774"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-14T16:56:48.336Z"}, "6.1.3": {"name": "@vue/devtools-api", "version": "6.1.3", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "c35bbbf5017d5da932a76b1175f42fde9268472b", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.1.3", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-79InfO2xHv+WHIrH1bHXQUiQD/wMls9qBk6WVwGCbdwP7/3zINtvqPNMtmSHXsIKjvUAHc8L0ouOj6ZQQRmcXg==", "shasum": "a44c52e8fa6d22f84db3abdcdd0be5135b7dd7cf", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.3.tgz", "fileCount": 40, "unpackedSize": 32214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3dfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUNg/+LM7nXCUvgdPdD/gMJFHH4VVG0fLtmiN5gY3/+2FpX/bIqjnp\r\ncmI7G79NHddH0Yl8ChMtb2AEZNQk6z3Jmyjib98StRVsvik5FNrsjRawfrP+\r\noNLFD8P1l98KN9cwGQY9iIdW6VTXcpQ7QgBIdscocHVX4wUQGcTe3p8aVznU\r\n19mEGcf55rrQJFZuxuCoqGaVkKpJ+DMFJLH/ZE/6134PMPz7/1BpU8n5/EJz\r\nRe2vE2ONQa9fyYP73JV0ZBdt/wvGUe+ZAsOoe9C+gUkyxbFK64HMJG1vlw/G\r\n3t7VfnGRR7lxnqtI5zFMGsxoY5XjHx1NrlelMMCGTkmXKDLrZVzy3V0TvwVu\r\nhDJGUEO9W/nYdKFSUgO27RllcZpJ0wFrNHU9tHURmZeHeRNVWreSGqoJ6bWo\r\nQmI9k8YU1QlQ/mlw00TTZRDwEknEEO9Tgu5mB+7G/Dh3rwSg56862VO6fZXy\r\nP82WaWyfb1Ref9aa6bvvvsnumjLY2CDSmb7xYh64sJ/D5dzbBGb5qddPXPrQ\r\nOx18uJrgZA9w7U6Ys1SyOfF0KDi+rkn7eVVfRoVGW+4Vp8edwh5oLiv2dick\r\n9N1tPndRNpc5brb9YtKeOujpxc6YNHtWVx0Ik1GGqOSvozhTnGd3qTk33Xlf\r\nFTM5FJjBTG3ycjNcINvFh8uQQtCt9zuaczI=\r\n=L5lK\r\n-----END PGP SIGNATURE-----\r\n", "size": 7099}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.1.3_1647277919506_0.607580544468298"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-14T17:12:03.971Z"}, "6.1.4": {"name": "@vue/devtools-api", "version": "6.1.4", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "b738d682f0367b176a35211024e8022fabb51f5b", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.1.4", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-IiA0SvDrJEgXvVxjNkHPFfDx6SXw0b/TUkqMcDZWNg9fnCAHbTpoo59YfJ9QLFkwa3raau5vSlRVzMSLDnfdtQ==", "shasum": "b4aec2f4b4599e11ba774a50c67fa378c9824e53", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.4.tgz", "fileCount": 40, "unpackedSize": 32685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQ3dBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBMBAAo3Nvk71GH02rCK8j0S7WT2s1x6A+HWaOha4kEeMXnnBt72wo\r\nSgQVbl4WcjChimWzdWrfm+7lnhYKHHJ24nxqjjZiT2ZHgZJVc25uiW9WTLKG\r\nkcP1YtARVOpRaSLMD41HVr28OgZUpo6VsHlVDmqNpllMIbi9RjkBFE5wFcqB\r\nqzruzzX/2GLRLh9zOv6+EsTKy2b9jmcUmlPZrgoGJ3yDhXYD385BrcjmTgNz\r\nI/dxlcJG9DuVNERhOUETSdaLXZxF/BLUvEKSQk6yrrGd6iBmvHjhLL0/yOBa\r\n7Bvz8EYOZSMJzE83TtkPzIOC5IyVy+kVAwDEMA0IO0vMKgC6iD8jdOwZ3Knn\r\nCkCs89KOh4iIrxKWkdNt1825yCFqXoBb0oUyM2KCOuAiTaRdcW8j3jeKPfe+\r\nBUL86sT8G1+BOfUESHQv9ci83zZyies/Gnn/d3pRhjAudlu4RWhzJ9YZ6wnD\r\nIntEH73px9/+234lXjIxjpBcRDw/cWR9tvQp7uiZzS/0B3ZTCdwNglFqoRlf\r\n7hUOTsdk9crLvs0/EToPIObwIma4/R0a3z+oBaursSXtCxeGBI1NTvCBUK9p\r\nUtY2V1ggrt5SbEfxF61ZHXSI8SdaFvUT4zmqeuBf/9Lvex5o3S+VtFOuYW+6\r\nE8vfYXbvv+1vNk7uWEfiSwt+ZuSuXJLAj94=\r\n=nzvs\r\n-----END PGP SIGNATURE-----\r\n", "size": 7173}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.1.4_1648588609449_0.9148919370247328"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-29T21:16:53.544Z"}, "6.2.0": {"name": "@vue/devtools-api", "version": "6.2.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "ea87e458043593beaedc2a2d1f965f43a0539e62", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.2.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.4.1", "dist": {"integrity": "sha512-pF1G4wky+hkifDiZSWn8xfuLOJI1ZXtuambpBEYaf7Xaf6zC/pM29rvAGpd3qaGXnr4BAXU1Pxz/VfvBGwexGA==", "shasum": "e3dc98a0cce8e87292745e2d24c9ee8c274a023b", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.2.0.tgz", "fileCount": 40, "unpackedSize": 33021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfgAmMqWnH3CHiJOuqwy5v/Ddiw7nrCJSWebg2fKTOHQIgXBTXh8MCfErQjn90W2ftamhhs2L/NZcPUVNT/XviKf8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivl92ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoc3RAAk5GsDOVsqjgy4mOBcLUlRpGUXBKEo/jh75/0Q36yy8Pu0pQS\r\nv5hx2QoqevhPAGpVPJxIYvm/IVLew6UhigdrmYkfwDfpxQ8w6UD37aXeGnks\r\nqEEUxlNnDhT94MlIltOyIEh638sFrl+H4LSNNjdy+SD5xZ59KznED6F4fFDg\r\n3QgtQtSObbXT80QhY76frzfYmasmgDgQRzASptDrCkqrsm0zV5pC56qGX3e8\r\nTCjZrrwYiiX1HiPvnMp2is9j8pMbaBqMWbU11S8p0qsFTjJwn7FfBib4/YtP\r\nhbYuBwnb7Z87yRelHdDxin6BkM8XldddTSCczg7fnNpGoM2iLDageXoQtWzh\r\nn8uM/9xr3od0JmhhZFUkGjhaEgUSA6OorsNLTUGkKtx+d0l/nRcb1lRNNiuh\r\nlCuTmCQ49RMlMWIKJ0xeHs5zR2FgbZyxRnjnpMSOhRA+qykkMfY8XWbRDW2M\r\nlLBPUEDnkEhFfs/9dFwGu9cBAQlFWABI6T4kFjP9veSRce6raSdhoTzluYGS\r\nDAv2gHWpcaK8mi58StLX8yE7vY3KxD14pnc+DsCpkzFQDja+gSTKM5aYlSml\r\nOhdeErQvo+D1PMzWMHaFxCPfF3Ki1cbRU4arCZo6JF2zdlwqC55tZ5QrP8Ux\r\nrFyAQnWVspdj4oKcGrhx3JPIAW9hZK1JzB0=\r\n=8f5Q\r\n-----END PGP SIGNATURE-----\r\n", "size": 7228}, "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.2.0_1656643446571_0.9619344553460791"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-01T15:19:49.609Z"}, "6.2.1": {"name": "@vue/devtools-api", "version": "6.2.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "0e946bf49d47cb9a8e3cb5211dd9705d71279964", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.2.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.4.1", "dist": {"integrity": "sha512-OEgAMeQXvCoJ+1x8WyQuVZzFo0wcyCmUR3baRVLmKBo1LmYZWMlRiXlux5jd0fqVJu6PfDbOrZItVqUEzLobeQ==", "shasum": "6f2948ff002ec46df01420dfeff91de16c5b4092", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.2.1.tgz", "fileCount": 40, "unpackedSize": 33021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBXR0097c1ranfVogWukDH9sB1uVc6dGN/LHZKhRe63gAiAlcZ+KEhhjhIfrSKs7s6sN5Vn2Ng/eGpjd12Uc1aahhw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizhsiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruUg/+KN/ijrc3s/AOPZdyvUENN3P2iKT8Ohu+mjfUks3YTi7U02a6\r\np72/Y6vMJhm6fVzytB3the7gKE1Gj6Y1pWoEHOW7CqxwwJbBzhpRY4WI98y5\r\nJBKPPi58CZNykKDe4cLAAonpklh3lpNAiVthxZvThpEEy4F+QwkYJOOj458p\r\nZkJw2SszmvFdN1JScSvZhX7T+v21HS3cooXyhm6MGT+0rpKzcMOsiVCtXWHM\r\nsZg9mahTseauWT06dV1B8fW1l6qFY/ypF9KtiTtFiNIdvVQ5oMoAHhzje/sQ\r\nK3Q8h2+n6XU6RUiEc4ga4XDrR4MUzunmzF5EMz+amhrjyTtf1bM32+pJvtL4\r\ncN2vnrVhNIEj4qDjqtiqec9PIaTt3eg0pl7t4ySpBXkojkxJNUMnPs0KHCn0\r\nIbxRNhHmkgr7kVPI0MXx1B6Ye+s+QDWd2citRXSRQUQcn21k3cHQIf8Ulhsu\r\nKMmpkN2yzApdNF/y81BoVpB3eGFhbjbheq45f35LLC5K0a2dFFDxAlwtVn7i\r\nRF4zYBMjzCMU/0J5fPnjQdeiHU/xfXCsKGIwSdFnbQUdWigMtiI+3yfU8P0t\r\nNe1ORd+1/A2o3gKnLAEzlxfZUZwBKIgzFGOS03MaY7E+6WvXp3KYbFVXsSkw\r\nq3uJrRUpfcuYnn7v2rqMkfNE0iArDYjYMHQ=\r\n=RqBh\r\n-----END PGP SIGNATURE-----\r\n", "size": 7227}, "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.2.1_1657674530098_0.6509496866362985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-13T01:09:04.904Z"}, "6.3.0": {"name": "@vue/devtools-api", "version": "6.3.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "5881800d73e78f8d983eb646e9885606a1ba7d2d", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.3.0", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-OfjtreoF3LtHmte3TrWSoZcyL4XWBL5+dTnCARuJZzTCYuaaO29PGMKCKdmXi4CZ0SiN0Exz1IGSo2S5BgDwEQ==", "shasum": "2cb4cbf012e704f467f5ba7e4092f09fc141edac", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.3.0.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBwyn4SmddV5O8ZYtYD58ijAQVfZ8hREMbZWWaQouj++AiAByfS+Si3SsxY2YK91XfcuEbq39vKIk0uUT8xHSXjCnQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjL5INACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9DRAAoJT0kGILcSZ/9BYgmILlAGEiXixMHTjRt1dg4cN2Jk/mcj91\r\npMNskbcopnzS4PDSbgYSDkBsMXRClRKdIKMNLZ8p84y9tXQZ4dun3CLM5RIn\r\nn5oo1hDQMBtj0T86KGsVTdUAGfKgjID1yTCuas045kdJaGHWTOTITmAMBzLk\r\nHGT2OoIb+ZYmzcf8tmuIe6TWAKnZKWNTMV12UIu9R2U56B07zHDElj/VPe5D\r\nwKLKgOOY5tjSBgjh/154Qe5/yAqJmAdno1srCHTYTRxoWIOybJJgKlEd0Ca5\r\nNpa/XVxUZ22dK/C6lugsoVaxJtIikaQC8vNwe1Do0qcqjrNqMb8QW0k7KLgQ\r\nyugpDt1U8zeM1eYhZ4Siho51YoBFdSl1dJqLK9Z0Ep/qiZd1TDKWoFKy2wHd\r\nj0hztu5HaH/xgTMxDPsZRF+iX0BYT43Ipydzes4EvuR/QBV3bY0SO4bmH1Q5\r\nSb3plJSGRVG4SUKGYjMoHPFiScOaR7eGkBdgYnmXPigi4D/l1rk3mpVJowgg\r\n5qC9+keD5BuyPehL0mtS3wQ53dEmu+xrSzgD2GjDEH9hw/J7xWbOpiHfbkvH\r\nL/HEmbn/YeuxRcGmkKGLgfV+PK42c53zNRNxyLm7CYjFN8kboxzWGo/ms72f\r\nLUXWyZihQnZRK7aQzHs2kkg/86cAhPaJZa4=\r\n=Zz+8\r\n-----END PGP SIGNATURE-----\r\n", "size": 7251}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.3.0_1664061965305_0.22751946715088156"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-24T23:26:18.426Z"}, "6.4.0": {"name": "@vue/devtools-api", "version": "6.4.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "33e596b64306395068fc6982ca1b183a3cf4320e", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.4.0", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-BBjYI/gcYyby6moAt4GAqhHLrG17cB4aocWlQCkoaO4laSBkNOboKd43r93g5F3vRStrO0dt7La2SbitPSQEIA==", "shasum": "065be1a07609d1856873e6dbaf02fa3ace1f604f", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.4.0.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFvn7IKv2tmAL5Gw8k/ZCB1CmUgwK8o6V1Py8rwJYqmmAiBmiQhSuQAtrsQu4ShV+JFoX8zhyratOtnFOtCFUoTH/Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMhDhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotkw//R/X3J5QC1C54k4yHMsdC7jtj3rvYC+1CheRLr0icZmfSeoYk\r\n77efjOrKwb1nUf/EFwyUzvMN8LN/At0EkuJdPipoGSpcOK20uYDlvnrczIis\r\n8fSdavZldQE3xGaSx3a8OG4toiYkKvt4B2AOJ2LOC63EAFr+RrM3pUM65V39\r\nAEWxSltwFNutzYMIxQQoHZLLIOy3Dzj0a+d2tmmvRGiDd7LT3jEGDwGHj7En\r\nquKhQTBtksFMqXJnvIdUGuTBittxbMEOKliGfyZR/GidJus4BqHzPk+/GzLP\r\ny8KjUTnpo2Se4fcyKjJI8erM56nAMp2PtZRyj7z8ngyu7uMOpDZx026xCNY8\r\nimfUJZdUFTTzzV6rUty1Q0p5fh8Z97d+DOJ1dp1TTMFZnPc5/FK84NKzt2TI\r\nMfs1WpRdsP9pOC7x76+IClDMOQlQbFCDCUT5TLvO6uDeHQJ5C8zA8pMYn5+T\r\nd0tp9Lz8HYSy5kmJjVoT71ExHL7eZl69BmiphUr88ZwR3C4zKfOfM42B2FJf\r\n0jdlBtOsOuR08xM4KcCPUt6ndk7ZTqvvrlAE70YDo3gUXNn3NKcXtYrsuukX\r\nBU3CP1ZmHGFNzrV+ZKSlDNLDBXa8edUwZkbPCh7LFTXqO+m+IrdKCy0shWIV\r\nE0wpkzckkj/lSS1/agAZSvKfqyqWTMaFK98=\r\n=UJiq\r\n-----END PGP SIGNATURE-----\r\n", "size": 7252}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.4.0_1664225505581_0.47297173694980943"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-26T20:52:03.277Z"}, "6.4.1": {"name": "@vue/devtools-api", "version": "6.4.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "7899b732d4f98c29d1922076245f18d638bb12d1", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.4.1", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-tY5m7kwu0R+9GWHSncsE40rCX9ou4HhjhlbgdEMci8j08BE7pLlOpHRcyv6eEP0VYrW1JV0zFh6AoWsoHrVyFw==", "shasum": "9def459f5c78ca8d0976e9e29187942e09776b19", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.4.1.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzFwCREp2ooh8Tj/LkvDl+00p0sNJce9GiLwQfzTyuSAIgZXev7CX79KfSpf567yIYI8+1KwhdZj3pwtr+447sRCA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMhNUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroWg//bKZOp4+Ep95GTWHtQ78LF2lxQHZKY6LofEotfJhwI1YvBFS7\r\nS1oqXUbu+AImqZ6vlPXPs6rRr1jcv1vdKFCeXJwhXEQW7RgH/ae9O+RLV380\r\nJWiOEbv2R2g3U484ZQtM8I2s+S5oM9jxdeBOfBTHdsIq3bp+nZ3064rAlV4g\r\nIpJfPCANmHhm5iIkK0/JWLS5ybxW0EUV7uOcaCsHhkqCOIO/3x6FRmJSlnpZ\r\nlbZ1tdEt88oHnouRIopdTPD+yqLs3VFc8Aw4Tfd3xtDx2O0DY6GZZi4PMnpO\r\negCk2sGO8jLu1R/7eZAaQabrc6+WHwBeiMe7RiOFJ1Iai5iaLZMLRRS0lE8t\r\nHpSAGP/yltfJwhDlaHakvXtTJYIxasCGfintfZpvDnwA5CCVxAntdCD58YJj\r\nLiGm2RTjOGfELuD3kEV5QKvEK+/MkauDDz25iNH1Gyt5sp8m2NjOO074B6E8\r\npr6R4Wtyp/tr0YcXg+0z6S4AnTCW72U1DPt5rfb9oMu+iLUJbVUh8IiqMbTw\r\no9cI1dzAboJDh4dwD3S9PcNJ2OYMBn+Q/lltQ3iarZ4oxbRHZSFsvG5fzXCw\r\nPaEeu0gp0gT+tkFcFKdahmUPfg1ohXrczyRD7hy5lyK7j6rpbAeDJXj/+tQz\r\nzV9XHF3LFe+OYG2graxCdTxpITZu7vNbhx8=\r\n=nBj2\r\n-----END PGP SIGNATURE-----\r\n", "size": 7250}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.4.1_1664226132466_0.5802362578367364"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-26T21:06:16.248Z"}, "6.4.2": {"name": "@vue/devtools-api", "version": "6.4.2", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "536165ccfae5ec0d361d2337c2e93bc940251c48", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.4.2", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-6hNZ23h1M2Llky+SIAmVhL7s6BjLtZBCzjIz9iRSBUsysjE7kC39ulW0dH4o/eZtycmSt4qEr6RDVGTIuWu+ow==", "shasum": "faf303a70cd9f8662896d663a195df41a8af1e53", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.4.2.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDKRR9RbDvubYGBFpru0R4tKhGhHl58X+tjQt3MKM+LzAiEA4IKgk6+ll/RNXmAmUynu7dRXbcn7xUUFnoONU3Y+wIQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNXM6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQtQ/9G6QO/EZ+UtXbBX80k/31qUvWcBFlrFP75PD/B8/47FOkaCqz\r\nQiHmWPjlY2q85p8nCKQ17FH6eu82M1BxdF+rqrtaVNxt5Ety2pRY2/LSVYp8\r\nZE8FDii5JW9aNj+qOxA5z5A/IKMswcO3NOD74F1H2K0k2Fa3tGcNDMPRfAGa\r\nIJpK9EDxd97UYl21RwQveejp5MnBwqllSuJxQNH6CY7nZJz6vVRHYaYX4NrB\r\nMkgLTIVXGLZGXWHlkkeQ8+yq4Wp6azSICf2i9VRyIHEiAGdADfl8ZHNkstOe\r\npJ+1gLFDMBrnqJCuZIZBhosywRea53eqJ3t5xv9x89om2Lds2OuvpOFamryI\r\ntiJDH/tnZ8iEF10CEX8LmmeMjP2kPDqBNw7zKkBh3YqNhShrXESKG9ayuVn0\r\nnqz340GVb2Lv4HaFGVu4t88RkLBypBMtUlGFBrLcNZ8iXzUMXslUjz27GeOC\r\nlZIL+tb34QeuCDlHLu64Hc5ekJjRuJ9yjR4EnNQmH7+7ouSw1QQW8tsdQZNZ\r\n7lYpJa8OYN1863MbmdMQNNPaQjbL1xweaEkBDADIzEqAqC416uhzCARKEFdM\r\nCEvE+fP+QOpcAtuSHhBnb4FU8WUm4rZxG/PHTv42ysKHzGJa3v8LBzZuJwk+\r\nsDBUzq+mWktgUMNDndnXb+H8cUdtSaV14DY=\r\n=mxzF\r\n-----END PGP SIGNATURE-----\r\n", "size": 7252}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.4.2_1664447290660_0.19849919321383003"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-29T21:09:57.596Z"}, "6.4.3": {"name": "@vue/devtools-api", "version": "6.4.3", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "30cea9f737366da74a5980817cddf43e1c5b9cf1", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.4.3", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-9WCRwdROJvWcHAdyrR7SZMM/qUvllDZnpndHXokThkUsjnJ2xe4/pvsH9FZrxFe22L+JmDKczL79HjLJ7DK9rg==", "shasum": "784aa9e397adde727ca892c3574f5f1cfb2bc1c2", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.4.3.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAh/mRIK19W8WmD7nEexLkK8vRA3jhHlsBvXThZxWcsSAiEAs9GfRLI4Or5mn6UMMS3oEfEsX/SgyEbALcHFXduM3Qo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOqilACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrmcg/8D/1l3izKgH5uPEkyHrFGu8xjVTslFIEgE/AxzyMRO0a61xRs\r\nIHvvUh0GBrJfXeiPpCZGnscDT/wQ3rgGuEXDrJLYl8xMgAxz4O9kO/ucFPTV\r\n7WbJUq6gOEzEanwt3QzYprk1rEW74NlbUtr9nnuWlG842qcQEalOVZYvoIRa\r\nJDpqNMmzs3CV4Hh6jh2a3qvDhERHNMgEmE47ekSjeaMcY+l5J6pGTzpVRoyy\r\n4KYcVfhS0cQIJyRSmfzLXxQ2+fmWqJRIeWx0/hLvDyEn7vkZGUjLzVVLewe1\r\no7bQB1ejZwGenh1pyNzPwBGeDp0Z9JpMFA10UjBpCKk5AIily2Kfr9ehyawy\r\nsE+y0Szg/Q5rW1+CkbJEVs3NaKBdiub9MrTrM1LPruv5Kys1+xPzUR5swENk\r\nQkv3N+eBsu3U+SncbDeWtyoOTVkWJMgsd2GuH+O4eD+ftLS46n62IyyJZ99e\r\nd1gus6nbP/Q1Nbx8W0CKiTmYCTLyrDJd1gdu5AvW7UKC+SAPzrmfopJTk2Kh\r\nmTAJjdyz639eNqm+932mZaq2djhBdAUJgeHhtNmr3eOpRuDEnDhI3ZYQLmyj\r\nxQ5JSoj4j9QWbQID3OhILEM1Gjh/iY6bSPWYP61Q8m4A1nhcvQXyNQQlZlqZ\r\nIeyCh7UF8T9QF/sUa7tcUD4UORl5GHZi1Nk=\r\n=rFn7\r\n-----END PGP SIGNATURE-----\r\n", "size": 7251}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.4.3_1664788645386_0.9455682553323559"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-03T09:18:08.585Z"}, "6.4.4": {"name": "@vue/devtools-api", "version": "6.4.4", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "67580d188aa3a08135922408161d4824feb30ea6", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.4.4", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-Ku31WzpOV/8cruFaXaEZKF81WkNnvCSlBY4eOGtz5WMSdJvX1v1WWlSMGZeqUwPtQ27ZZz7B62erEMq8JDjcXw==", "shasum": "0b024fc8ca91bb4b6035abaf53c5aecc17119b3b", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.4.4.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxwCCtEOL9vnriYshZGL84C0IFR8nND/iFjwOzXIJb2QIhAJeMEhJLuERvHHciTmA7FYUpXriZ3pG3y9QstgQLNTui"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQUAuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMag/+NQNjFcPMMeaWvhVZrNMsogJ6sg5gCdhm0DQ6e71DAYRVOI+K\r\nBIAa9jfUnEA1q2DucBvq2sxHdsJJMazmI5snp3VmHsK5Qh08zPbhEjngfIq7\r\n2OHKGv2hy2lHc6LRT7oT9HvhV5M7AZHGH2UjlT8seXOkt98RVuI/m/3rsh6n\r\n/yb+4bGiRR/9ZWW/sUKgSPbWuCYssNLdtAub1n9JD3w2b5rvBDYB8udoIREA\r\njxBgg2iBmhWer9OH/PWVIZ+fbiUkVyueyZs8Jkp0F03iEsfg3eBvQaR4DzaK\r\nicOl6pV0j8v1veNKfMr6PjPOdm8Fhdn7NJVXlPS1bVa00XCyYafzljRvyjhU\r\nKLsbTpCitYzzDNgGIRnTai4XHPQTiwzNW3yNSJ1ZhYjtTDOuu+SbcwV/p4QE\r\nnrKXvyXBy7eGbKe8GZ4FClasKFqeIxtj1zpRlB28XAmQ5F5+INONwx2ywrg5\r\nK3hN+HTPfCreE6Jc/evZfszLLEwLlb/14sY8DWnkDIw29QKsKb4ER6+EHUg9\r\nKdsSDFKsoTEO5fHBqSMKkQ/O/3q5TWcSgtSdj6Zer4gKJXmLoPl0BefEIRmg\r\nWhX+2d1ptZ3TQN/cQv/Hik6Gqs/nPqp8S0wxfgN/BoVe3vqD7eNmCw8NdMWG\r\nnqa37SVRkpWbp204AdSwsE2jw5IDUycf6nM=\r\n=qybP\r\n-----END PGP SIGNATURE-----\r\n", "size": 7252}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.4.4_1665220654279_0.019139942247942354"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-08T09:17:48.857Z"}, "6.4.5": {"name": "@vue/devtools-api", "version": "6.4.5", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "812410f7567670c345295a9d7d9b27b3a5f809ef", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.4.5", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-JD5fcdIuFxU4fQyXUu3w2KpAJHzTVdN+p4iOX2lMWSHMOoQdMAcpFLZzm9Z/2nmsoZ1a96QEhZ26e50xLBsgOQ==", "shasum": "d54e844c1adbb1e677c81c665ecef1a2b4bb8380", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.4.5.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCH7YmowbmSAmAg9QNb7InazsBzlySefuq3d5n4xq6OAiB+Jq458Hk/G6u0hNNIiIKHqNa2iq0YtLmufxGumSAj7A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTqe6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqz5xAApHEuNo7R4ke4ej/qVsxQq8ze3aTorKlzWRwp9EBmKfWX+ID2\r\nepdy0DENNjei+oJxAT8tuSVkHGjmXC6OjillhsIDUgtdYw1ksJyadx23EL3B\r\nAqh6dfZ9fZgjYSAInXjDO9dGsG0P8Ik8PiUP1iH92vJOxUcn1A6dlbxJVatu\r\nsswJZ0CyT0Jdx0LdnC8BEkt2ylQNiCOXl42BTUGpsWUg7Pi2fZsNrFEJLzRM\r\ntP/8F9kVBiwH/OHOzx+sbdZyE1Qn9SMxhEUYgQ2mHozQnC4CG94VZEWpmp0Q\r\nbXIPEjwxkdMvOMVR1dyYZRkvjg+78HdL4u61wXUsJXfr/WStlbRvhC2MINpV\r\nalfkdLCdux2hfGVwxoeCevuVJExCjiGATcsaFS/liHe3Rq+Lu3m1DFmRt8kf\r\n6Opu+2O3XNsx0VanDdqd19ESy9hyJOq7S7maaz0VHKFZSdWB5wFLJ/gCGm7V\r\nKOxz7+Jyc7N2BEiqNRenZSh+ce0zCF8kOfLMmP9fgMSCkBAhgCQOxoWfp8/4\r\nObydkJLk2ZY33nuZg+xG+QscnYukoY3aPHUtk3kYLDdUZsrxCbFzwtfOgQsS\r\nw/lBVu7tXPI3xE4VraF4PBk8D5ieKAMfYBM43jAIB4MY5jmoCG7FZSqYhZ0S\r\nOaavk9i+fhvBKzEBQstMmxZskrxndE2y29Y=\r\n=wrUY\r\n-----END PGP SIGNATURE-----\r\n", "size": 7251}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.4.5_1666099129932_0.10259273256371326"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-19T07:51:12.238Z"}, "6.5.0": {"name": "@vue/devtools-api", "version": "6.5.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "gitHead": "01e7c2135336fa25de11a31749aadd50b2dee94a", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_id": "@vue/devtools-api@6.5.0", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-o9KfBeaBmCKl10usN4crU53fYtC1r7jJwdGKjPT24t348rHxgfpZ0xL3Xm/gLUYnc0oTp8LAmrxOeLyu6tbk2Q==", "shasum": "98b99425edee70b4c992692628fa1ea2c1e57d07", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.0.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOb+ja0e1tPdIPNjfPcvkdtp2ToFHI7hTpTmN2IbPYaAiA2YmPCRrmiyqOmMz+wFdV2yzd2Ny9QSY3bSvLILBQ+Bg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyrWnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtKw//QDz5aOXb2fBPnKchbEub98trX3WDBK+/GYcnpk7vX+7pH8tj\r\nmokUlaAJBI6rDR1VuEKI5UNVPeLECQvphVyRS7aAfRR9l5irGbbQvddBaaz3\r\nJrFewadC2zJXNFx7WZ49uE/B57wvt2A9pd3nFk6wRJbAfSsqqTtiqINBBx5m\r\nNUbXU2jjgUmEl+9+Hul77dsvfaZMDNM2fc23xzPnP6An+WfqlhsawJTf+4/d\r\nUN0iZAfIVn8EYUGnVTnY/Lwek+ufhKpe5uAJysKG9Los1aw/5GchFDE6ama8\r\nw8i6+LNvgiffCH9L1gE+EfiUSz9jc7VW9Y0qQjVzFaIFiCsEnpu7w5ksm6bI\r\ng2oQyCEsux+zq2GWZ6IFYGdBj+ZU8BNCvGIiRO7Zhd4FhIN7wALy41RmcXjB\r\nWFWulEPdzrFkIsWL0hiqNPTK/V3kPuQGjOA44fx3dl9yxrYVPrVK8JSup7be\r\niF0D0X1zHfSY+aHfwg97ewE8wBeS/qjt7tvHbbrxM4fx/XspUPF6O6mtYlbU\r\n/skNzwH6kpeGpBmoynYSomTxdwrrP3tAJEBgixhs3hsl2rMwUtEso14hnGZ9\r\nR3HEFv8Vidxps42YmqGuUCPEi62D4baX5bYdDPUKKh9LqUdBZW7SYyQi6vw/\r\nyLlZPeX2/26MjJl50nCEjXgc4hjQ9pG3K6o=\r\n=ZJ38\r\n-----END PGP SIGNATURE-----\r\n", "size": 7251}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.5.0_1674229159166_0.09140567844812542"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-20T15:39:19.364Z", "publish_time": 1674229159364}, "6.5.1": {"name": "@vue/devtools-api", "version": "6.5.1", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}, "_id": "@vue/devtools-api@6.5.1", "gitHead": "3444bdd89c49092a37715f2706caca3e695591a5", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_nodeVersion": "20.6.1", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-+KpckaAQyfbvshdDW5xQylLni1asvNSGme1JFs8I1+/H5pHEhqUKMEQD/qn3Nx5+/nycBq11qAEi8lk+LXI2dA==", "shasum": "7f71f31e40973eeee65b9a64382b13593fdbd697", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.1.tgz", "fileCount": 40, "unpackedSize": 33074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC8fzuWvK4yuUf8qrHElH9B/m4Amor+jUJlwqwIB2OnKAiEAl+M1bFUGrAyXFolOUyC6XxRfagoCPo+2rZLDhykXezk="}], "size": 7249}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.5.1_1696931629697_0.260884127706247"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-10T09:53:49.858Z", "publish_time": 1696931629858, "_source_registry_name": "default"}, "7.0.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.0"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.0", "_integrity": "sha512-BQdPSC6gEm4FYdtMEs0tia4WGCPYq45nTiz+VjuxYP7sy0gUENjmtedgBcvpuY+W3HAjXDDoQfW4zdpxKFmI6A==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/5711cdaa3f37ce7a9d7199cff344f58d/vue-devtools-api-7.0.0.tgz", "_from": "file:vue-devtools-api-7.0.0.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-BQdPSC6gEm4FYdtMEs0tia4WGCPYq45nTiz+VjuxYP7sy0gUENjmtedgBcvpuY+W3HAjXDDoQfW4zdpxKFmI6A==", "shasum": "b5c8816f0e0c2d48dfa4edec45b345d5cd0503b6", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.0.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDpRXO6lZR3g0J8tt5G4YC1ZXZgIvd0Wxw8z/96ckyr1AiEA2KCLaX7zp6iKFy9DpP0cbaDpMsnIjVgK7rrUBRthFpU="}], "size": 1955}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.0_1703839512053_0.44170375735076606"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-29T08:45:12.226Z", "publish_time": 1703839512226, "_source_registry_name": "default"}, "7.0.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.1"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.1", "_integrity": "sha512-JR72hghfmkAN8SrnIupvfHmuIvXhVF95aDYX+pUCYLCy+FMwW1caKeU0vAamI+2LvjWqOLKldhG39UmAKVhtbA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/49b9e54b55c32b95402c83aef11f1519/vue-devtools-api-7.0.1.tgz", "_from": "file:vue-devtools-api-7.0.1.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-JR72hghfmkAN8SrnIupvfHmuIvXhVF95aDYX+pUCYLCy+FMwW1caKeU0vAamI+2LvjWqOLKldhG39UmAKVhtbA==", "shasum": "3bf29acc7a1014f67da2225fecb5bc3ec3edf1cf", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.1.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHbdSv0uN5x48vnAOrqeCkdeyZ4Dal0YdTiH7GsEpTrgIgfeKrWLQWYHfqjQC+P31DlDutxhoxfSOvLn3a21EJGM0="}], "size": 1955}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.1_1703866091999_0.8356505070831324"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-29T16:08:12.251Z", "publish_time": 1703866092251, "_source_registry_name": "default"}, "7.0.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.2"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.2", "_integrity": "sha512-P13ZQCDIcoAbKfXslqVPP9EmGxT+Iz2eCcUJrxNNJH57XfAePDjRfpH3oYR8SykLCO5kV1ttv5Yl9FhMm8mdlw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/f60c0ba5ae76ca13dba79bd180a35f23/vue-devtools-api-7.0.2.tgz", "_from": "file:vue-devtools-api-7.0.2.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-P13ZQCDIcoAbKfXslqVPP9EmGxT+Iz2eCcUJrxNNJH57XfAePDjRfpH3oYR8SykLCO5kV1ttv5Yl9FhMm8mdlw==", "shasum": "163f8c7ac053a34d778a5f97b0617b5e10e70846", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.2.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH9fRAnA8oamCS21c1XGKznRCqreXiR/Gv+RxpvFRkXvAiAuot3QqI3dMAiyaD7xYtItJ54HzfKYqWHgPDLj0XjQ7A=="}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.2_1703869193888_0.10441098324700593"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-29T16:59:54.064Z", "publish_time": 1703869194064, "_source_registry_name": "default"}, "7.0.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.3"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.3", "_integrity": "sha512-OYGclXkK++Csb4Ip/tYQS+anogTVP8pzo8RkPYNwAm8FDncgbkBnNhy16Kd+2FLRvbEj+JTYyJ3g8fr2lojYeA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/c4aa82bc0e1ceb864007289a5be0b3bc/vue-devtools-api-7.0.3.tgz", "_from": "file:vue-devtools-api-7.0.3.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-OYGclXkK++Csb4Ip/tYQS+anogTVP8pzo8RkPYNwAm8FDncgbkBnNhy16Kd+2FLRvbEj+JTYyJ3g8fr2lojYeA==", "shasum": "83ec8797ddb283a1b4dd70805e0885c20efbb9f1", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.3.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEmh4sxccXN4JBzRCsBFZHr2BUyAmUuaOe1/wW1S0W3rAiBYk+xCwJl+cxLyTyTKElmKFAgi/90FCGXvvCqoPZBxaQ=="}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.3_1704207787257_0.9646925154972712"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-02T15:03:07.489Z", "publish_time": 1704207787489, "_source_registry_name": "default"}, "7.0.4": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.4", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.4"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.4", "_integrity": "sha512-KF1pQtJosZcvKVJsNkc1qp1qjEOqmzYY7medrRpjM71YWP8EnLesg6nrXEzoWSNXbGHZohZiGRNXT1KuISIDuA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/2e8491a8236dc993366fd4eb6e226648/vue-devtools-api-7.0.4.tgz", "_from": "file:vue-devtools-api-7.0.4.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-KF1pQtJosZcvKVJsNkc1qp1qjEOqmzYY7medrRpjM71YWP8EnLesg6nrXEzoWSNXbGHZohZiGRNXT1KuISIDuA==", "shasum": "b200c8097607a0618a735e7455632a586f69e462", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.4.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqZRK2+u53Bv8Kpy9qpmuCx4zM6R6xLrqpKkwD0CW08wIgCjAHQZsSPVvdhP1F81Jb/bC6ykKW11YJIP4fS6/KPas="}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.4_1704286524427_0.9620724238723755"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-03T12:55:24.602Z", "publish_time": 1704286524602, "_source_registry_name": "default"}, "7.0.5": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.5", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.5"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.5", "_integrity": "sha512-pP0hj2srK/XmsEdVph4Ff7WoIKAVaRGKtS4UXo6li/mzL+4HCLlwIOVTPpnrPikcsPZCOU42R7B44p991o1Zag==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/339b2379198f8ae69fdfb411707b2a2b/vue-devtools-api-7.0.5.tgz", "_from": "file:vue-devtools-api-7.0.5.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-pP0hj2srK/XmsEdVph4Ff7WoIKAVaRGKtS4UXo6li/mzL+4HCLlwIOVTPpnrPikcsPZCOU42R7B44p991o1Zag==", "shasum": "d3f201cce892142a7ee9ccb7c42c5b6c73048a30", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.5.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGo8TbEMyYTnm6j5lfqMo3eDHHC7GKu8NeAJrkuXmB9rAiEAkkjCQtcKVrQ9HZwEVd5cNOsuvT0xOCsTiJICYcE/HBE="}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.5_1704383235818_0.25086133596252536"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-04T15:47:15.967Z", "publish_time": 1704383235967, "_source_registry_name": "default"}, "7.0.6": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.6", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.6"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.6", "_integrity": "sha512-P+Mag4VF7ihDPOLj29uWHhmeUrxnJTIub2N6jqtUfGW876ov43llpIUU7EjDtjKTs6xRQ1L9KnJRlV6LqK2m6g==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/8f42591a020b5a8f108b068d9e953fda/vue-devtools-api-7.0.6.tgz", "_from": "file:vue-devtools-api-7.0.6.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-P+Mag4VF7ihDPOLj29uWHhmeUrxnJTIub2N6jqtUfGW876ov43llpIUU7EjDtjKTs6xRQ1L9KnJRlV6LqK2m6g==", "shasum": "35c5cc3e8c9a7212a01d49573a4919effbfd39be", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.6.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIANq2+mL/q6TlqeGX4T+3aSx4DBhi4SZlp78ZQO9RSgCAiEAt2bNIQOrNdzb1wVLLAXFNwYKzBs0neCkiReQ+DxzkvE="}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.6_1704529022110_0.12516768550345625"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-06T08:17:02.298Z", "publish_time": 1704529022298, "_source_registry_name": "default"}, "7.0.7": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.7", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.7"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.7", "_integrity": "sha512-cOA+ba+ZFFFMxr2kp0S2leWyc0+FMcPhgiwMMpNhclYGLPsu6gGvy4TsRDuSs1FGrra3quZioeyc7HxaG//sfg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/4158ff27098587d5213471b50d10e474/vue-devtools-api-7.0.7.tgz", "_from": "file:vue-devtools-api-7.0.7.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-cOA+ba+ZFFFMxr2kp0S2leWyc0+FMcPhgiwMMpNhclYGLPsu6gGvy4TsRDuSs1FGrra3quZioeyc7HxaG//sfg==", "shasum": "81fd98f92e95df6bcd17ccc2a120fc01f7a0b4ee", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.7.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDW6IL6YqwJ0wlLCHGzmVS4eaG0d2bKYsxbtOE/rWoT0AIhAJLCt1c6d7l+lG38TVMtK8VsotSqwaLF7GM9asFjmYw8"}], "size": 1955}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.7_1704801335209_0.22765431535212843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-09T11:55:35.382Z", "publish_time": 1704801335382, "_source_registry_name": "default"}, "7.0.8": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.8", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.8"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.8", "_integrity": "sha512-SJGTqioBlsjsE6Ax2ZSeERSaJ1O40XlhO2s/Wt+GHIvp4wG+QjoGAMBATSPbDVp4KMqMLU/JV6uPlhReeOMuiA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/48a609da36fe2f8347baee5fe8f8bd3a/vue-devtools-api-7.0.8.tgz", "_from": "file:vue-devtools-api-7.0.8.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-SJGTqioBlsjsE6Ax2ZSeERSaJ1O40XlhO2s/Wt+GHIvp4wG+QjoGAMBATSPbDVp4KMqMLU/JV6uPlhReeOMuiA==", "shasum": "28348a6bd0ea8369aaa8877cfbd0dc2709768b18", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.8.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCK13zc9D/XGjCsN460XBwjw4P562j1RF7OeiaMT6vQPwIgJSKNWnWmnW4BQCgB+9U0QF5mmvDk5JiY7+F2rwZi0g8="}], "size": 1957}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.8_1704978542575_0.8107840216811075"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-11T13:09:02.756Z", "publish_time": 1704978542756, "_source_registry_name": "default"}, "7.0.9": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.9", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.9"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.9", "_integrity": "sha512-B3ppEIn8q44N93ksxKUB+/h9uAp19TSjM0uVqyQrZ+HfQYiXi8XdW3Q8TC9r93fdR81KkfTM4NObZfllSNE7CQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/5ff048c0863f28c46c8b0821f87d20d8/vue-devtools-api-7.0.9.tgz", "_from": "file:vue-devtools-api-7.0.9.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-B3ppEIn8q44N93ksxKUB+/h9uAp19TSjM0uVqyQrZ+HfQYiXi8XdW3Q8TC9r93fdR81KkfTM4NObZfllSNE7CQ==", "shasum": "0e6f948baaff57bce18c03f258e5b509b65eeaea", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.9.tgz", "fileCount": 7, "unpackedSize": 4265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAlZHLvhb6brru3NKiNEkmAy918U5uOW4ok36ypeROmgIhALv9I8iuSIzlYpO8u1kWT8mwVw9jqxbtse4r9LKGuQnC"}], "size": 1957}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.9_1705212127538_0.7369053294145531"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-14T06:02:07.732Z", "publish_time": 1705212127732, "_source_registry_name": "default"}, "7.0.10": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.10", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.10"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.10", "_integrity": "sha512-ahyPg7WfNChvFBBoXuksPWYetbfKQq+9s+1HFFtAW3ZT/KrHw+********************************/3rw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/f4b92fec03f5753a3232198014736d6f/vue-devtools-api-7.0.10.tgz", "_from": "file:vue-devtools-api-7.0.10.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-ahyPg7WfNChvFBBoXuksPWYetbfKQq+9s+1HFFtAW3ZT/KrHw+********************************/3rw==", "shasum": "11c4bb6adf586545c7cad2a8fa2b66e16249d2d4", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.10.tgz", "fileCount": 7, "unpackedSize": 4267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCO/kD5Cq1f2TWyT7nryUIq3l5FJmvhdnkIH4cGjD252wIhALT0U+9d+QqDXeX/HUsMxE8OIlLZlEtDgBjyvA2cJcul"}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.10_1705218891835_0.7771233650640994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-14T07:54:51.989Z", "publish_time": 1705218891989, "_source_registry_name": "default"}, "7.0.11": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.11", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.11"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.11", "_integrity": "sha512-cNgfi8gDPp7ArlCuhTQ2LVSUKzODmBSdyKtkrhjxpJLANMA6GGnL9VubNBhhiUtx94ub3kbyEJaK0252369xmg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/5ff84d27ff86f71a3b4315004cacc628/vue-devtools-api-7.0.11.tgz", "_from": "file:vue-devtools-api-7.0.11.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-cNgfi8gDPp7ArlCuhTQ2LVSUKzODmBSdyKtkrhjxpJLANMA6GGnL9VubNBhhiUtx94ub3kbyEJaK0252369xmg==", "shasum": "76a8022e08f9360f9b5d36e542852cecbb27bd44", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.11.tgz", "fileCount": 7, "unpackedSize": 4267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKtqgZJVK/w81tOkd+g55jsaj3vGyaE9tFI+a1bM/+MgIhALpXI4TPgDquNHYMAcCYSAkC/7WM5LGDLLrNTEFC1Wbw"}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.11_1705580712143_0.373243031086655"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-18T12:25:12.308Z", "publish_time": 1705580712308, "_source_registry_name": "default"}, "7.0.12": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.12", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.12"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.12", "_integrity": "sha512-GuLEE+J+/wcDId0OxI7RzjY5bCkRoWY4uGo4Y4wFkxC5jqaZKLDEs4AeYa7JeGefM3d0YTzX3ACi63QU3+PctQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/1ffc8efe455c8cf0d33e893825c2fd98/vue-devtools-api-7.0.12.tgz", "_from": "file:vue-devtools-api-7.0.12.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-GuLEE+J+/wcDId0OxI7RzjY5bCkRoWY4uGo4Y4wFkxC5jqaZKLDEs4AeYa7JeGefM3d0YTzX3ACi63QU3+PctQ==", "shasum": "e237b7f28764ed1272f58982f3bd98143d90db1e", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.12.tgz", "fileCount": 7, "unpackedSize": 4267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFvjSDdJV8LI39M9HpSTd42XsQ301dSGjTJxElZ3hX3NAiAlYFfaQhntqqW017XF42F8VMH+WXOuk7bo4tXJlJ5s7g=="}], "size": 1956}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.12_1706192646052_0.1439593313894263"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-25T14:24:06.218Z", "publish_time": 1706192646218, "_source_registry_name": "default"}, "7.0.13": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.13", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"@vue/devtools-kit": "^7.0.13"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.13", "_integrity": "sha512-7JStHeUC8Qekf0nVx4MhPnrzMPvN03ejAt1R++rH8yZvu9DdFOjTY8CRXb6kpIlMBuIVOsVECb3jciWZisrl9w==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/9d2bb00ccf6d2cad52a943d333738eed/vue-devtools-api-7.0.13.tgz", "_from": "file:vue-devtools-api-7.0.13.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-7JStHeUC8Qekf0nVx4MhPnrzMPvN03ejAt1R++rH8yZvu9DdFOjTY8CRXb6kpIlMBuIVOsVECb3jciWZisrl9w==", "shasum": "45591c8db9d9d13c2439fea226322552d0e2b71c", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.13.tgz", "fileCount": 7, "unpackedSize": 4267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxkCJBKppMEFqF5SfWauxP9SjgzMotuuEV/eUyBUDIJQIhAP3Rapvwc684mHqwJhwPfKDl16hc4yqTv8EWAe795eb6"}], "size": 1957}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.13_1706243170482_0.5533672549068014"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-26T04:26:10.624Z", "publish_time": 1706243170624, "_source_registry_name": "default"}, "7.0.14": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.14", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.14"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.14", "_integrity": "sha512-TluWR9qZ6aO11bwtYK8+fzXxBqLfsE0mWZz1q/EQBmO9k82Cm6deieLwNNXjNFJz7xutazoia5Qa+zTYkPPOfw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/99da0200c1fdb4b3a2499ac18dcc9d68/vue-devtools-api-7.0.14.tgz", "_from": "file:vue-devtools-api-7.0.14.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-TluWR9qZ6aO11bwtYK8+fzXxBqLfsE0mWZz1q/EQBmO9k82Cm6deieLwNNXjNFJz7xutazoia5Qa+zTYkPPOfw==", "shasum": "27eedda650f09d991718741268b7cef823dd08a6", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.14.tgz", "fileCount": 7, "unpackedSize": 4233, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICe/J9VLwUNwHvdHRuNEr4hYF9tl7sS8RDXgo4eBRyzXAiEAr7Ln2cj25VXDRY48AsUuaoUm39+gJv2r8CoFRU6QDpg="}], "size": 1960}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.14_1706798852885_0.9778390360954083"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-01T14:47:33.061Z", "publish_time": 1706798853061, "_source_registry_name": "default"}, "6.6.1": {"name": "@vue/devtools-api", "version": "6.6.1", "description": "Interact with the Vue devtools from the page", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "sideEffects": false, "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^20.11.16", "@types/webpack-env": "^1.15.1", "typescript": "^5.3.3"}, "_id": "@vue/devtools-api@6.6.1", "gitHead": "6f645b83400a8d7a894e0cc9777bf276e1a5ebbf", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_nodeVersion": "20.8.1", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-LgPscpE3Vs0x96PzSSB4IGVSZXZBZHpfxs+ZA1d+VEPwHdOXowy/Y2CsvCAIFrf+ssVU1pD1jidj505EpUnfbA==", "shasum": "7c14346383751d9f6ad4bea0963245b30220ef83", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.1.tgz", "fileCount": 40, "unpackedSize": 33264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhgiNlHutiKjtTEkuHli3RiP2Kq8t7QeekcwYmKrSoZAiBIvlKLCYP+sbQ/ST7sAurbciM0ynR6WXhzCVbdFkcXaQ=="}], "size": 7297}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.6.1_1708020603568_0.4673635050765761"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-15T18:10:03.957Z", "publish_time": 1708020603957, "_source_registry_name": "default"}, "7.0.15": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.15", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.15"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.15", "_integrity": "sha512-kgEYWosDyWpS1vFSuJNNWUnHkP+VkL3Y+9mw+rf7ex41SwbYL/WdC3KXqAtjiSrEs7r/FrHmUTh0BkINJPFkbA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/b010e0d42a57c19d5fc652f10e28dfbc/vue-devtools-api-7.0.15.tgz", "_from": "file:vue-devtools-api-7.0.15.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-kgEYWosDyWpS1vFSuJNNWUnHkP+VkL3Y+9mw+rf7ex41SwbYL/WdC3KXqAtjiSrEs7r/FrHmUTh0BkINJPFkbA==", "shasum": "875a228fb5154fa4a6678e8cbc4c894089d87cde", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.15.tgz", "fileCount": 7, "unpackedSize": 4233, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqR9ZQOciKBtBrJpMvuIA747tHqkoGNbpc2eux7xN0+QIgefZ96u1IkcY2Aye0uL57xDXlM3tn+rRoIOxcd8KPVy8="}], "size": 1965}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.15_1708095330991_0.5727341511414943"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-16T14:55:31.140Z", "publish_time": 1708095331140, "_source_registry_name": "default"}, "7.0.16": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.16", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.16"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.16", "_integrity": "sha512-fZG2CG8624qphMf4aj59zNHckMx1G3lxODUuyM9USKuLznXCh66TP+tEbPOCcml16hA0GizJ4D8w6F34hrfbcw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/98020a3997efa04382d30b96445ed64a/vue-devtools-api-7.0.16.tgz", "_from": "file:vue-devtools-api-7.0.16.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-fZG2CG8624qphMf4aj59zNHckMx1G3lxODUuyM9USKuLznXCh66TP+tEbPOCcml16hA0GizJ4D8w6F34hrfbcw==", "shasum": "aa199ebb597bab697876d9555feb35c98415a782", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.16.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFi0swmie/CdQ+ikhkcm/g4TqCPnMd/gwEhZplySNSwSAiEAzPyOltPjzP6IK43OavAB7MMjPNHzrlOFeRrbm78/8y8="}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.16_1709352366723_0.9217402807844131"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-02T04:06:06.890Z", "publish_time": 1709352366890, "_source_registry_name": "default"}, "7.0.17": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.17", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.17"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.17", "_integrity": "sha512-UWU9tqzUBv+ttUxYLaQcL5IxSSdF+i6yheFiEtz7mh88YZUYkxpEmT43iKBs3YsC54ROwPD2iZIndnju6PWfOQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/eab62976842294f45b3ec6e4c563488f/vue-devtools-api-7.0.17.tgz", "_from": "file:vue-devtools-api-7.0.17.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-UWU9tqzUBv+ttUxYLaQcL5IxSSdF+i6yheFiEtz7mh88YZUYkxpEmT43iKBs3YsC54ROwPD2iZIndnju6PWfOQ==", "shasum": "6251541c3a9f6a4e0cbdef56b2c17d762aa115b0", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.17.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHy8KGG+r+4RCBsS6zchuk4oDp1hw9RGeu51JAkAp6hQIgEFurOHMnngfOaQz5CYIaF9xwGdUah4uDH0z0kTNZcjU="}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.17_1710252354922_0.6460045600259026"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-12T14:05:55.107Z", "publish_time": 1710252355107, "_source_registry_name": "default"}, "7.0.18": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.18", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.18"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.18", "_integrity": "sha512-rNLQ8A2eGmaXAen/xARLY6MVz7tzXFTDlrWJrpTD1VSU7vqmBn9qPIPjRkCdb8/2/En6RGsRebNSqfqh1Ck4pg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/94224183de9cc05fd51fa07077eb6d15/vue-devtools-api-7.0.18.tgz", "_from": "file:vue-devtools-api-7.0.18.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-rNLQ8A2eGmaXAen/xARLY6MVz7tzXFTDlrWJrpTD1VSU7vqmBn9qPIPjRkCdb8/2/En6RGsRebNSqfqh1Ck4pg==", "shasum": "20a9e8583ec1db7637a09bb6285b5e1e51b6c799", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.18.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwrIJ1hedAcCIslhNhMEiM/46FV8DHf38OqJsptyIu+QIhAKVfI0TRul8XvjTjtK1ltZhxbqksMOl49h159Je4x+2n"}], "size": 1992}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.18_1710659604676_0.7756800673935942"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-17T07:13:24.826Z", "publish_time": 1710659604826, "_source_registry_name": "default"}, "7.0.19": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.19", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.19"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.19", "_integrity": "sha512-RlNAMDLEFPi44I79t3OAWStjED0PS4havAYevkgHRPqZxgX+cNvQqTqgl6DVJBsmkuOcnWev4ukQaCuuWrNgzw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/a6f81657be1f8cdfe891ed63c1ef7000/vue-devtools-api-7.0.19.tgz", "_from": "file:vue-devtools-api-7.0.19.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-RlNAMDLEFPi44I79t3OAWStjED0PS4havAYevkgHRPqZxgX+cNvQqTqgl6DVJBsmkuOcnWev4ukQaCuuWrNgzw==", "shasum": "c4206c961937f5b1291a8ef6cf3b3e78920ec041", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.19.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKc3D7GxR1cwS/bNbt2RdIobSyp38O3nwvb627lrlD4gIhAP7/PNE8d9T7sr1xVDXgQyeaa5WkDks3NAMJ/At4bdHE"}], "size": 1992}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.19_1710824321135_0.3484676705242511"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-19T04:58:41.271Z", "publish_time": 1710824321271, "_source_registry_name": "default"}, "7.0.20": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.20", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.20"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.20", "_integrity": "sha512-DGEIdotTQFll4187YGc/0awcag7UGJu9M6rE1Pxcs8AX/sGm0Ikk7UqQELmqYsyPzTT9s6OZzSPuBc4OatOXKA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/2fc7ce62fecb086ef3c58270e9cf19f6/vue-devtools-api-7.0.20.tgz", "_from": "file:vue-devtools-api-7.0.20.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-DGEIdotTQFll4187YGc/0awcag7UGJu9M6rE1Pxcs8AX/sGm0Ikk7UqQELmqYsyPzTT9s6OZzSPuBc4OatOXKA==", "shasum": "fd79229b917f0284e7341e6d4e1ae403aac9232a", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.20.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0wyP29SL9sXkotbiPemAlw45PBFqvLuPrH2p6ADzJLQIgJtIHARv5ydwwiAXw2N0USFX3WEotFxqzsz516t/CkIs="}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.20_1710949727224_0.5962234928661003"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-20T15:48:47.389Z", "publish_time": 1710949727389, "_source_registry_name": "default"}, "7.0.21": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.21", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.21"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.21", "_integrity": "sha512-hIkqpU3I9BM/qNUGhb/RAfEkRtFZTM4QFrBBH5gxGYubBIRctqgQk4ukLAkhaW+VmOw5DAPxQHXH4W6Oh36L6g==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/851b1bb8e5cd5683b5cc1131fac9893d/vue-devtools-api-7.0.21.tgz", "_from": "file:vue-devtools-api-7.0.21.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-hIkqpU3I9BM/qNUGhb/RAfEkRtFZTM4QFrBBH5gxGYubBIRctqgQk4ukLAkhaW+VmOw5DAPxQHXH4W6Oh36L6g==", "shasum": "c357e988002059e56fb55c45b8dfc9ac3543c3a2", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.21.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChl7Lq8JdXp9Qw1TDUOm1geQ2ln30K9JdiTW0DbGTWkAIhALNYgpTfLzhyLDchUqhCFxqJerEWxSgHIFjHaC+G4pXD"}], "size": 1987}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.21_1711386218119_0.06182325474696104"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-25T17:03:38.428Z", "publish_time": 1711386218428, "_source_registry_name": "default"}, "7.0.22": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.22", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.22"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.22", "_integrity": "sha512-Fy3eMNft2h/tm5IDSlKzjcKo4WzIG9vrzn48gRlyMWkiR5SvHOvTmEGDyaKwZRtrQ0FGGuDG0kGE1Cl/yQwUWw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/fa989476084b78becfda791512844177/vue-devtools-api-7.0.22.tgz", "_from": "file:vue-devtools-api-7.0.22.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-Fy3eMNft2h/tm5IDSlKzjcKo4WzIG9vrzn48gRlyMWkiR5SvHOvTmEGDyaKwZRtrQ0FGGuDG0kGE1Cl/yQwUWw==", "shasum": "6088b9f9a4db122762821abd983b1124a15071b4", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.22.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICG38F/rekeV0UHj/0JuelIFF761zaw0Y5twM6LCu7W1AiAFquXvrQ0cZB6u4sl0WWrXFizx+/Wc/Q5kWk6JyrzrYQ=="}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.22_1711456278902_0.7193766345205881"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-26T12:31:19.069Z", "publish_time": 1711456279069, "_source_registry_name": "default"}, "7.0.23": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.23", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.23"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.23", "_integrity": "sha512-CjJ3vZ00Wt/6aLWMY6GuWYzbmxkGQ1axoPqQEZQ/61e4pqOQijYgTU2xLYOB77qjZu32pJnGCMtpRAFPoRiuXg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/c721e0dc02f2a8232f4b39c4da4c756e/vue-devtools-api-7.0.23.tgz", "_from": "file:vue-devtools-api-7.0.23.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-CjJ3vZ00Wt/6aLWMY6GuWYzbmxkGQ1axoPqQEZQ/61e4pqOQijYgTU2xLYOB77qjZu32pJnGCMtpRAFPoRiuXg==", "shasum": "5afb71310770c042b65bab586b0c4dceecf97e17", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.23.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTB9BhvzmdqXUFb0aM0EQqFmiBmaToi7zCp7lWjt8kkAIgGyOPHn571cQJmjDQNcW53bBaHMWp4bjsM0twTs8suMQ="}], "size": 1992}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.23_1711467091135_0.95549666461449"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-26T15:31:31.366Z", "publish_time": 1711467091366, "_source_registry_name": "default"}, "7.0.24": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.24", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.24"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.24", "_integrity": "sha512-4dsRfL+7CnRZ9TgkmFNHRc7fKSE6v74GNojwxop+F5lotUTxNDbN7HTYg/vJ7DfJtwKFYGQjMFbHxEDZQ4Sahg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/ca087fab3474eb571f1c9544aea6e6ee/vue-devtools-api-7.0.24.tgz", "_from": "file:vue-devtools-api-7.0.24.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-4dsRfL+7CnRZ9TgkmFNHRc7fKSE6v74GNojwxop+F5lotUTxNDbN7HTYg/vJ7DfJtwKFYGQjMFbHxEDZQ4Sahg==", "shasum": "4c10190b642347191d49de4c5573cc6b152e187d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.24.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFKmoC7boTQEKKkbX/5T8360jIEz+r8j8AAchsLlkZT5AiEA2zJklLIwS26x+ACF83WsXX8Np0iKiQFEf5ZuwD+KL4A="}], "size": 1987}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.24_1711514323663_0.717589349377751"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-27T04:38:43.841Z", "publish_time": 1711514323841, "_source_registry_name": "default"}, "7.0.25": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.25", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.25"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.25", "_integrity": "sha512-fL6DlRp4MSXCLYcqYvKU7QhQZWE3Hfu7X8pC25BS74coJi7uJeSWs4tmrITcwFihNmC9S5GPiffkMdckkeWjzg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/5960bb2c48f8224cc3d26db82c2a63de/vue-devtools-api-7.0.25.tgz", "_from": "file:vue-devtools-api-7.0.25.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-fL6DlRp4MSXCLYcqYvKU7QhQZWE3Hfu7X8pC25BS74coJi7uJeSWs4tmrITcwFihNmC9S5GPiffkMdckkeWjzg==", "shasum": "240bff9ee5bbe684e0c7b29696e98b497179368d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.25.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvnKSKw/vluOo24zny0aCf7KTuk4BdwdAeb3pTsbDMQwIhAP5XlJWjoKpgNbv+dSeVvgDIccogB1ecSdomBTp3gUFP"}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.25_1711601334232_0.8458333093871488"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-28T04:48:54.399Z", "publish_time": 1711601334399, "_source_registry_name": "default"}, "7.0.26": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.26", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.26"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.26", "_integrity": "sha512-KnHZBP7IIhp5IUBXkyDmfuLKSht4BLhuZEgKaZXwgzFJRSTM9FfcZe5nWIrD33n28ClsIycne3b3kWYq5XnaYg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/c36918560630854ff666ed05508e7d75/vue-devtools-api-7.0.26.tgz", "_from": "file:vue-devtools-api-7.0.26.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-KnHZBP7IIhp5IUBXkyDmfuLKSht4BLhuZEgKaZXwgzFJRSTM9FfcZe5nWIrD33n28ClsIycne3b3kWYq5XnaYg==", "shasum": "016649580a25caa13ce877b8d2b394d3589088f5", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.26.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDbh7hglgA6GnOAcvHw0SzaOLj+ZuYB/aC5zPH+ZSWLpAiB//EFp5rDDSjxZb4s5k4FfsMwKaE1RHI/CejgYB9UKUg=="}], "size": 1987}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.26_1712724532277_0.4694475593191205"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-10T04:48:52.441Z", "publish_time": 1712724532441, "_source_registry_name": "default"}, "7.0.27": {"name": "@vue/devtools-api", "type": "module", "version": "7.0.27", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.0.27"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.0.27", "_integrity": "sha512-BFCFCusSDcw2UcOFD/QeK7OxD1x2C/m+uAN30Q7jLKECSW53hmz0urzJmX834GuWDZX/hIxkyUKnLLfEIP1c/w==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/78baf407defe7636c0dceea908ab4897/vue-devtools-api-7.0.27.tgz", "_from": "file:vue-devtools-api-7.0.27.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-BFCFCusSDcw2UcOFD/QeK7OxD1x2C/m+uAN30Q7jLKECSW53hmz0urzJmX834GuWDZX/hIxkyUKnLLfEIP1c/w==", "shasum": "bfbbfb1d632bdb71b7a9b5e3ed4314dab26a5440", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.0.27.tgz", "fileCount": 7, "unpackedSize": 4508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDejKcNOu8miRVIAdl1xSuFT/HpjpZ0+T2XBGS/2lJySgIhAIXwevxyy23oS7/OWaNZbAsyO0ZMwTywtBToHRVh7C8v"}], "size": 1987}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.0.27_1712754043843_0.8034856805547317"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-10T13:00:44.005Z", "publish_time": 1712754044005, "_source_registry_name": "default"}, "7.1.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.1.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.1.0"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.1.0", "_integrity": "sha512-ufuqwGwg3+WIZhe0PC4d2pF4W5Xei+OBQOm47lbPLNfzl/FwyWEmtAHDKSyykTUSdGuD4aaGE5YlsbQwkRXbZw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/e21bfa43757e4cdd92a9511cbab6f269/vue-devtools-api-7.1.0.tgz", "_from": "file:vue-devtools-api-7.1.0.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-ufuqwGwg3+WIZhe0PC4d2pF4W5Xei+OBQOm47lbPLNfzl/FwyWEmtAHDKSyykTUSdGuD4aaGE5YlsbQwkRXbZw==", "shasum": "ef3fa3974257f94bc19432bbb1983e0cbbee148e", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.1.0.tgz", "fileCount": 7, "unpackedSize": 4506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDRQkJ0QIyE0bl6xC/P5pyqhSkTrqjjROQ9gstH9Xvy0AiBfVJrgyHTsmpjcU4AXtCscWSAIsU+4oJp3+Ks+n/RVJA=="}], "size": 1985}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.1.0_1713881561635_0.0036145667577398566"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T14:12:41.798Z", "publish_time": 1713881561798, "_source_registry_name": "default"}, "7.1.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.1.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.1.1"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.1.1", "_integrity": "sha512-tqipKQrxEw+ddipuuLnFgeOhpR0DMhUgxHpzs50JnnCdGrfiqcwlESx542BbdbvJUn+kUldd4VQlWYfhAXFtTg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/c1b3e99149dbaf1ff3cea0b2254e0689/vue-devtools-api-7.1.1.tgz", "_from": "file:vue-devtools-api-7.1.1.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-tqipKQrxEw+ddipuuLnFgeOhpR0DMhUgxHpzs50JnnCdGrfiqcwlESx542BbdbvJUn+kUldd4VQlWYfhAXFtTg==", "shasum": "75202c6bb4c3f6a9f9b0fbf7ba5d1f823f281878", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.1.1.tgz", "fileCount": 7, "unpackedSize": 4506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDz6TQYrVz7iwf7+np6u4NQyr0aSDTq3xfSB3/HN0m+aAiEA3QpJVFYC5KDzJ7y6WsF5XKxYPtVPk/vyKeBpTh1CXnM="}], "size": 1985}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.1.1_1713883041808_0.4413342781297658"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T14:37:21.947Z", "publish_time": 1713883041947, "_source_registry_name": "default"}, "7.1.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.1.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.1.2"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.1.2", "_integrity": "sha512-AKd49cN3BdRgttmX5Aw8op7sx6jmaPwaILcDjaa05UKc1yIHDYST7P8yGZs6zd2pKFETAQz40gmyG7+b57slsQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/b279bc982fc18c69a2def66b1118ff54/vue-devtools-api-7.1.2.tgz", "_from": "file:vue-devtools-api-7.1.2.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-AKd49cN3BdRgttmX5Aw8op7sx6jmaPwaILcDjaa05UKc1yIHDYST7P8yGZs6zd2pKFETAQz40gmyG7+b57slsQ==", "shasum": "8808b0f008842b756bf1e9c30788837abb62ab3a", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.1.2.tgz", "fileCount": 7, "unpackedSize": 4506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJvYOM45A/yZprt8etOh3uIIfB4Xoz4iLvtZdqybbrWwIgCF0W1VYm4LFt8YBMi7mQZU11d06NkYH9Ezr9WwZt/NU="}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.1.2_1713883751851_0.8465556484765835"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T14:49:12.016Z", "publish_time": 1713883752016, "_source_registry_name": "default"}, "7.1.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.1.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.1.3"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.1.3", "_integrity": "sha512-W8IwFJ/o5iUk78jpqhvScbgCsPiOp2uileDVC0NDtW38gCWhsnu9SeBTjcdu3lbwLdsjc+H1c5Msd/x9ApbcFA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/74f824d3b94f679131dcb672ceca75dd/vue-devtools-api-7.1.3.tgz", "_from": "file:vue-devtools-api-7.1.3.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-W8IwFJ/o5iUk78jpqhvScbgCsPiOp2uileDVC0NDtW38gCWhsnu9SeBTjcdu3lbwLdsjc+H1c5Msd/x9ApbcFA==", "shasum": "b1cc9050025022193204ad804a9669e384fee8b0", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.1.3.tgz", "fileCount": 7, "unpackedSize": 4506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDMLMIKv5rTT8YUiuQns1I3CB209fhGjgJP4fkwzBwT+AiEAjlYfAPb1GwfBBGbpGs4Y11EbVNLW3sbRBZs/2WJOCXo="}], "size": 1985}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.1.3_1714140949486_0.6318988261984522"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-26T14:15:49.651Z", "publish_time": 1714140949651, "_source_registry_name": "default"}, "7.2.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.2.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.2.0"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.2.0", "_integrity": "sha512-92RsjyH9WKNFO6U/dECUMakq4dm2CeqEDJYLJ8wZ81AnCifpXE7d4jPIjK34ENsPaapA6BSfIZdH/qzLOHiepA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/dc64ee39b3d6169d3f71bb7335a22579/vue-devtools-api-7.2.0.tgz", "_from": "file:vue-devtools-api-7.2.0.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-92RsjyH9WKNFO6U/dECUMakq4dm2CeqEDJYLJ8wZ81AnCifpXE7d4jPIjK34ENsPaapA6BSfIZdH/qzLOHiepA==", "shasum": "7e16c3788a17a8f812369064eb706a7576346daf", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.2.0.tgz", "fileCount": 7, "unpackedSize": 4506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCG0xXitEeMxmN2XSI3k78rExEM7iSCqNGBU+kjWa4f1AIgXJVxvePh2+CRt7e5nyNTncc6qHR7vTDncGbuot2I1ko="}], "size": 1985}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.2.0_1715776803848_0.5510275876297368"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T12:40:04.191Z", "publish_time": 1715776804191, "_source_registry_name": "default"}, "7.2.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.2.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.2.1"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.2.1", "_integrity": "sha512-6oNCtyFOrNdqm6GUkFujsCgFlpbsHLnZqq7edeM/+cxAbMyCWvsaCsIMUaz7AiluKLccCGEM8fhOsjaKgBvb7g==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/b1370bbe031f424ee6c52ef25aa9d550/vue-devtools-api-7.2.1.tgz", "_from": "file:vue-devtools-api-7.2.1.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-6oNCtyFOrNdqm6GUkFujsCgFlpbsHLnZqq7edeM/+cxAbMyCWvsaCsIMUaz7AiluKLccCGEM8fhOsjaKgBvb7g==", "shasum": "1eb3d33c85b76306106d5804bafa0d13178e9224", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.2.1.tgz", "fileCount": 7, "unpackedSize": 4506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRutHE5L2HuWbYHVSyAXofHEJytT/mfkzj5SsPSWBrEAiEAlUIfHZIMhFcmyhWPbljZ8RPQY2aylP4Q1O1zQew84iM="}], "size": 1986}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.2.1_1716134369990_0.43863896315999695"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-19T15:59:30.159Z", "publish_time": 1716134370159, "_source_registry_name": "default"}, "6.6.2": {"name": "@vue/devtools-api", "version": "6.6.2", "description": "Interact with the Vue devtools from the page", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "sideEffects": false, "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^20.11.16", "@types/webpack-env": "^1.15.1", "typescript": "^5.3.3"}, "_id": "@vue/devtools-api@6.6.2", "gitHead": "042c73ec2a239f96bbb346e862b04e77e671d227", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-134clD8u7cBBXdmBbXI282gHGF7T/eAbD/G7mAK2llQF62IbI4ny28IVamZVMoJSvfImC2Xxnj732hXkJvUj6g==", "shasum": "a5a17377ca810c7f0153232ff1dcfa25bd5694be", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.2.tgz", "fileCount": 40, "unpackedSize": 33264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0k+Lsg4+xwbHxFICTAdvO7hnclFn2hMNjaJuWag5pyAiEAgkIanGoKUvqlwueccR+EA2AIQxLAB3tuK8kSO0AR7dg="}], "size": 7317}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.6.2_1717162793803_0.4595047263901215"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-31T13:39:53.941Z", "publish_time": 1717162793941, "_source_registry_name": "default"}, "6.6.3": {"name": "@vue/devtools-api", "version": "6.6.3", "description": "Interact with the Vue devtools from the page", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "sideEffects": false, "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^20.11.16", "@types/webpack-env": "^1.15.1", "typescript": "^5.3.3"}, "_id": "@vue/devtools-api@6.6.3", "gitHead": "f6b006231fdba08c942bf158292c9b7f49298979", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-0MiMsFma/HqA6g3KLKn+AGpL1kgKhFWszC9U29NfpWK5LE7bjeXxySWJrOJ77hBz+TBrBQ7o4QJqbPbqbs8rJw==", "shasum": "b23a588154cba8986bba82b6e1d0248bde3fd1a0", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.3.tgz", "fileCount": 40, "unpackedSize": 33264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIynYADGTCec8LEHt6ZOODsH9TZI4Fyds6sD99sFctlAiEAhjCylPsrDlG+64XVvH5rq/nalY/c6RRpUrZTj2918lk="}], "size": 7317}, "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.6.3_1717494949880_0.7757866849025581"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-04T09:55:50.038Z", "publish_time": 1717494950038, "_source_registry_name": "default"}, "7.3.0-beta.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.0-beta.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.0-beta.1"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.0-beta.1", "_integrity": "sha512-LpG8hlh4AW8mPDjFpg7iNZlv12FdcwtfgOiSA0KJjNPGlj+LW83nQH8JVhjD1Bk5xBh7Na/E9Yct5iTeqkbbjg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/fa26e88bb6f9d7327760198e8d89aa7f/vue-devtools-api-7.3.0-beta.1.tgz", "_from": "file:vue-devtools-api-7.3.0-beta.1.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-LpG8hlh4AW8mPDjFpg7iNZlv12FdcwtfgOiSA0KJjNPGlj+LW83nQH8JVhjD1Bk5xBh7Na/E9Yct5iTeqkbbjg==", "shasum": "e01bd7470aa02bd4e6a00ab9b87827b1f20f825a", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.0-beta.1.tgz", "fileCount": 7, "unpackedSize": 4564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVGETP4GcMYLu1Hsp7E8xG9VuiWJVHBj4rWJxJVy289AIhAIQmMcZqEbieJcOI0cBESx4q+rU42iVtm/srcTSSbWPp"}], "size": 2008}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.0-beta.1_1717600177606_0.35921978685037215"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T15:09:37.750Z", "publish_time": 1717600177750, "_source_registry_name": "default"}, "7.3.0-beta.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.0-beta.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.0-beta.2"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.0-beta.2", "_integrity": "sha512-bxr3OoAu5RAhpI3QMFLezjwvWkzBQAd+hMSROkPD+pDq3UF19OJaVc3MjqB100i+/15lzRRMxNY3y6GnwMjCzw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/cedd04f57923413dff24a276c3848c1c/vue-devtools-api-7.3.0-beta.2.tgz", "_from": "file:vue-devtools-api-7.3.0-beta.2.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-bxr3OoAu5RAhpI3QMFLezjwvWkzBQAd+hMSROkPD+pDq3UF19OJaVc3MjqB100i+/15lzRRMxNY3y6GnwMjCzw==", "shasum": "723a5b35afab0381603e9bc5652ea09f8a78a3f8", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.0-beta.2.tgz", "fileCount": 7, "unpackedSize": 4564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoFYWemILZr61LgWFtwOoMQZFGRsas0ElWWpODLCI45gIhANqBF2jLcfhX37rREgTWBu+GwB36zzreryVinvggom2M"}], "size": 2008}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.0-beta.2_1717678834063_0.1228971623894679"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-06T13:00:34.252Z", "publish_time": 1717678834252, "_source_registry_name": "default"}, "7.3.0-beta.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.0-beta.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.0-beta.3"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.0-beta.3", "_integrity": "sha512-fBGNlRedaGn6Q17SOO13SMsjjbQ4GyhiYbbvEToHUbFRuTF5y4fg0aPUchq/BCBUmfDd3/xxFbEKVWajpSxDkA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/e6aa0b7f50a08acf6c51736dc4fa2a7b/vue-devtools-api-7.3.0-beta.3.tgz", "_from": "file:vue-devtools-api-7.3.0-beta.3.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-fBGNlRedaGn6Q17SOO13SMsjjbQ4GyhiYbbvEToHUbFRuTF5y4fg0aPUchq/BCBUmfDd3/xxFbEKVWajpSxDkA==", "shasum": "6a67d59329dc7d13ffe6fe74890fd8cde43da188", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.0-beta.3.tgz", "fileCount": 7, "unpackedSize": 4564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHEVxG3gtN4ZWSYFdiWUDbJvu9FCLJdjgIi5++1gu/l/AiEAwGFKvp0LMytXpEFArrw412qkeyqbFgO0CDylAOqOoHo="}], "size": 2008}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.0-beta.3_1717689440367_0.825464233824889"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-06T15:57:20.506Z", "publish_time": 1717689440506, "_source_registry_name": "default"}, "7.3.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.0"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.0", "_integrity": "sha512-EQ6DIm9AuL9q6IzjjnxeHWgzHzZTI+0ZGyLyG6faLN1e0tzLWPut58OtvFbLP/hbEhE5zPlsdUsH1uFr7RVFYw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/84ccc29ec5e3c1e3da9fea1fe79f824f/vue-devtools-api-7.3.0.tgz", "_from": "file:vue-devtools-api-7.3.0.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-EQ6DIm9AuL9q6IzjjnxeHWgzHzZTI+0ZGyLyG6faLN1e0tzLWPut58OtvFbLP/hbEhE5zPlsdUsH1uFr7RVFYw==", "shasum": "55623f9d0b956e718d5234ec39f254a820f5d832", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.0.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcLjJAqsSITYfc+kIh4/Vi/FHEgdgb9i8bc1lR5TEgdgIhALOkb5ni6ibP2H4BRNvFxN3hrnJixk6uJcnfbwRhUOfZ"}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.0_1718534234978_0.24179462080205538"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-16T10:37:15.124Z", "publish_time": 1718534235124, "_source_registry_name": "default"}, "7.3.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.1"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.1", "_integrity": "sha512-yg2<PERSON>Qcnuez906E6/gDROLbUwI554PH5SWJMkomvOfGcPYjNpkK8LEy1pM1wxBtSVSlQbZKqiNgGQFp3Zw+0Jog==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/ff571232f2d07d581aa93a98774dd0d9/vue-devtools-api-7.3.1.tgz", "_from": "file:vue-devtools-api-7.3.1.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-yg2<PERSON>Qcnuez906E6/gDROLbUwI554PH5SWJMkomvOfGcPYjNpkK8LEy1pM1wxBtSVSlQbZKqiNgGQFp3Zw+0Jog==", "shasum": "71bdf61211bef04f937ee7a235a45c5253553294", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.1.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC1VYaMHENcPOrD+z+P0ayJCL1Z9gFLx6JhXI4NfflBHAiEA5YP7WUH5spV2kWNeuXIE1fl18Uif5PeZMdzx2QzNQag="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.1_1718686316248_0.43224143192296505"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-18T04:51:56.408Z", "publish_time": 1718686316408, "_source_registry_name": "default"}, "7.3.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.2"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.2", "_integrity": "sha512-qFCm12te9rG0XWLCHm3x8TiZLULEP5s7Ruaadi5jAogwZ5qF7QH7tKc6yXZGV96uM+y1FUlbK+QwVbWgMfXEhQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/45bf94c05e26c66398bf2c9d714d0637/vue-devtools-api-7.3.2.tgz", "_from": "file:vue-devtools-api-7.3.2.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-qFCm12te9rG0XWLCHm3x8TiZLULEP5s7Ruaadi5jAogwZ5qF7QH7tKc6yXZGV96uM+y1FUlbK+QwVbWgMfXEhQ==", "shasum": "65ff03b235a61cafc39b17322ee79551f8d2c88d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.2.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOFgQQOO2ueBhSW6yvxBlkgC3gBqmYF94m4UyeIpC3kQIgXWuOAnkSeTh209XL/lQx2rqDtNtx6xCq25t3opGtl3c="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.2_1718805092552_0.16511662530151838"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-19T13:51:32.736Z", "publish_time": 1718805092736, "_source_registry_name": "default"}, "7.3.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.3"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.3", "_integrity": "sha512-Q9SpC5puX+VZ8mAixoVHAYRgLij4e/MCEfXze4gScdtxQuemHq7/zEE0EsgiwSrISaxwNGooymU8UO78W2yA7Q==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/982cc40f711cd2eed1f598bcd6dfacd8/vue-devtools-api-7.3.3.tgz", "_from": "file:vue-devtools-api-7.3.3.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Q9SpC5puX+VZ8mAixoVHAYRgLij4e/MCEfXze4gScdtxQuemHq7/zEE0EsgiwSrISaxwNGooymU8UO78W2yA7Q==", "shasum": "301eb4c1fe0153d7f5ffd9f99464356fa93ac67b", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.3.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIDWKRs0wcEsExg3omATkuggPpufRdtR2LaTZfKhBflcnAh9eRKOzQJ2z4zrnFlq/InoKs4sF5e4rpkYTqqqBUSIF"}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.3_1718991061950_0.1638350650703586"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-21T17:31:02.139Z", "publish_time": 1718991062139, "_source_registry_name": "default"}, "7.3.4": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.4", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.4"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.4", "_integrity": "sha512-E5dJlLW+NgGb+WS33y99ioOJL2OXpVhje6VwXGJ/q5fNizJDpe67Ml0GBSrlYOKNSjZs2mwcZd7B3e12th3Q0g==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/613e15768f9cffb8f22535190394ec63/vue-devtools-api-7.3.4.tgz", "_from": "file:vue-devtools-api-7.3.4.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-E5dJlLW+NgGb+WS33y99ioOJL2OXpVhje6VwXGJ/q5fNizJDpe67Ml0GBSrlYOKNSjZs2mwcZd7B3e12th3Q0g==", "shasum": "24a0537f706a200e93e4da2d6012d207a3ad0520", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.4.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBQtEDINP7cBC6px1er4gsycEW0fNY4dCWofu0HkdNZ4AiB2DipnCFoMXgbTu8kMgGRp2SlTfkwvPGUgCRYuiFEmAA=="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.4_1719063429308_0.10860093582974306"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-22T13:37:09.482Z", "publish_time": 1719063429482, "_source_registry_name": "default"}, "7.3.5": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.5", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.5"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.5", "_integrity": "sha512-BSdBBu5hOIv+gBJC9jzYMh5bC27FQwjWLSb8fVAniqlL9gvsqvK27xTgczMf+hgctlszMYQnRm3bpY/j8vhPqw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/c28ac1fd8394f143c1e43b94caba9bf7/vue-devtools-api-7.3.5.tgz", "_from": "file:vue-devtools-api-7.3.5.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-BSdBBu5hOIv+gBJC9jzYMh5bC27FQwjWLSb8fVAniqlL9gvsqvK27xTgczMf+hgctlszMYQnRm3bpY/j8vhPqw==", "shasum": "afd9f3bca50cfff96aebeea3cc3853fd127267f7", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.5.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIIIM4HK3ULE44cpcpIjmieNfZyMFLoVzkhy+FWjdgbAiBL4hzex0YZcuB66xKwq+X6+uGXo03sigloPQzZqEAmVw=="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.5_1719578717176_0.8848891387691691"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-28T12:45:17.319Z", "publish_time": 1719578717319, "_source_registry_name": "default"}, "7.3.6": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.6", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.6"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.6", "_integrity": "sha512-z6cKyxdXrIGgA++eyGBfquj6dCplRdgjt+I18fJx8hjWTXDTIyeQvryyEBMchnfZVyvUTjK3QjGjDpLCnJxPjw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/858d6fb56e03fc001e225e0ee22bc08e/vue-devtools-api-7.3.6.tgz", "_from": "file:vue-devtools-api-7.3.6.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-z6cKyxdXrIGgA++eyGBfquj6dCplRdgjt+I18fJx8hjWTXDTIyeQvryyEBMchnfZVyvUTjK3QjGjDpLCnJxPjw==", "shasum": "165f676d959db2c1dec1a035a781394bb6833777", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.6.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICE72wyI7+bCB/svEXnog6ceW39HP25y4L8wflUvGJZyAiBFNR2pyrSE7EdToYf8r3jUY/uVLJQgJ3e99czRlXM8Vg=="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.6_1720972099645_0.3416880394182764"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-14T15:48:19.781Z", "publish_time": 1720972099781, "_source_registry_name": "default"}, "7.3.7": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.7", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.7"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.7", "_integrity": "sha512-kvjQ6nmsqTp7SrmpwI2G0MgbC4ys0bPsgQirHXJM8y1m7siQ5RnWQUHJVfyUrHNguCySW1cevAdIw87zrPTl9g==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/0fba5068c6587b30b3c6f7e6795b925c/vue-devtools-api-7.3.7.tgz", "_from": "file:vue-devtools-api-7.3.7.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-kvjQ6nmsqTp7SrmpwI2G0MgbC4ys0bPsgQirHXJM8y1m7siQ5RnWQUHJVfyUrHNguCySW1cevAdIw87zrPTl9g==", "shasum": "78f46e9d7af206f06392b4139045e6538cef0a19", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.7.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICD4uDfbekWC22NvveFMkcMPWiJS2x+QnrGNgkWXY/Z9AiEA50RVn8Edma2zAy1xMv1LSCdAOrHZff+K4A5UeQTWYi8="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.7_1721747012145_0.7276212252969245"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-23T15:03:32.333Z", "publish_time": 1721747012333, "_source_registry_name": "default"}, "7.3.8": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.8", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.8"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.8", "_integrity": "sha512-NURFwmxz4WukFU54IHgyGI2KSejdgHG5JC4xTcWmTWEBIc8aelj9fBy4qsboObGHFp3JIdRxxANO9s2wZA/pVQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/9f41c4713da9b775168f3349b2872bfe/vue-devtools-api-7.3.8.tgz", "_from": "file:vue-devtools-api-7.3.8.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-NURFwmxz4WukFU54IHgyGI2KSejdgHG5JC4xTcWmTWEBIc8aelj9fBy4qsboObGHFp3JIdRxxANO9s2wZA/pVQ==", "shasum": "a1cbdc0adfe212aef8fb21a9645adb5ef96c765a", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.8.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC14pl4LgC0yJ6lgb2t8GfhR3mit8ViE5MJqlX0Q1Br8AiBLR04ilh80300TwG3eufBO4YW5uo7Olosp5DSce7z+TA=="}], "size": 2004}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.8_1723474660711_0.9915754954624625"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-12T14:57:40.917Z", "publish_time": 1723474660917, "_source_registry_name": "default"}, "7.3.9": {"name": "@vue/devtools-api", "type": "module", "version": "7.3.9", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.3.9"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.3.9", "_integrity": "sha512-D+GTYtFg68bqSu66EugQUydsOqaDlPLNmYw5oYk8k81uBu9/bVTUrqlAJrAA9Am7MXhKz2gWdDkopY6sOBf/Bg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/0a2815a2fd73e2330319ace8cd8902c9/vue-devtools-api-7.3.9.tgz", "_from": "file:vue-devtools-api-7.3.9.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-D+GTYtFg68bqSu66EugQUydsOqaDlPLNmYw5oYk8k81uBu9/bVTUrqlAJrAA9Am7MXhKz2gWdDkopY6sOBf/Bg==", "shasum": "952824f7bd4444718e283c995ee64995299c748a", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.3.9.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDq0d3F66IVzGU/4HqKMZtzUN84QP4T/51XsbTcFH12cgIgFQyUsUYiiHQcavwh4nEfXaag9i0GzfIZW5QPznOQla4="}], "size": 2004}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.3.9_1724576865972_0.32666067404586774"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-25T09:07:46.118Z", "publish_time": 1724576866118, "_source_registry_name": "default"}, "7.4.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.4.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.4.0"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.4.0", "_integrity": "sha512-bUCBiknVv1QEaEPyth7FeeTyPY1AkKnCNAjeR6AJ1C0djqIurcVvBn8jD8E3r/MTmhzlKv0auDxzxRf9w5wCLA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/ee3ac28bb6425d96211b616cd48efcff/vue-devtools-api-7.4.0.tgz", "_from": "file:vue-devtools-api-7.4.0.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-bUCBiknVv1QEaEPyth7FeeTyPY1AkKnCNAjeR6AJ1C0djqIurcVvBn8jD8E3r/MTmhzlKv0auDxzxRf9w5wCLA==", "shasum": "cbcf98507aa6405a22c21fe25ba6cd21203b427e", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.0.tgz", "fileCount": 7, "unpackedSize": 4550, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbg6N+/eZP/rlGxpxKdsbqsue7qBRw+2ArwoOXlVKQCwIgP4XRR7EqtVEP9rxEH0W13Cyn7e/XdqCXkZaFKTtMz4k="}], "size": 2003}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.4.0_1725381375293_0.2698508480775208"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-03T16:36:15.577Z", "publish_time": 1725381375577, "_source_registry_name": "default"}, "7.4.1": {"name": "@vue/devtools-api", "version": "7.4.1", "author": {"name": "webfansplz"}, "license": "MIT", "_id": "@vue/devtools-api@7.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "homepage": "https://github.com/vuejs/devtools-next#readme", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "dist": {"shasum": "a13e8410f13dcfc285103d370293e70c47c7e327", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.1.tgz", "fileCount": 7, "integrity": "sha512-EWTSObDPmo8mhg/EO01M4brAMQ85IGYFHLyX64QKsI1HW3rHXs91zC3jnhJu5BgbXuXVA7pldYD2UT6720qxBQ==", "signatures": [{"sig": "MEUCIDJI0BQ/roBjTB8/E0r/n+O3K1G7CthepvUK16RV3faFAiEAofDD/61JEY6ZkgRd0fLEQdkyIFIOe4DYSzs8E+NLkQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4540, "size": 2004}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vue-devtools-api-7.4.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"stub": "tsup --watch --onSuccess 'tsup --dts-only'", "build": "tsup --clean", "prepare:type": "tsup --dts-only"}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/50ffa4a9e08f675e8c7d65102b6f3724/vue-devtools-api-7.4.1.tgz", "_integrity": "sha512-EWTSObDPmo8mhg/EO01M4brAMQ85IGYFHLyX64QKsI1HW3rHXs91zC3jnhJu5BgbXuXVA7pldYD2UT6720qxBQ==", "repository": {"url": "git+https://github.com/vuejs/devtools-next.git", "type": "git", "directory": "packages/devtools-api"}, "_npmVersion": "10.5.0", "description": "> Plugins API for easier DevTools integrations.", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"@vue/devtools-kit": "^7.4.1"}, "publishConfig": {"tag": "next"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/devtools-api_7.4.1_1725425955038_0.88987584517052", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-09-04T04:59:15.210Z", "publish_time": 1725425955210, "_source_registry_name": "default"}, "7.4.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.4.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.4.2"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.4.2", "_integrity": "sha512-Bn9dWSd8RV4kXFdnC4D4E0briLlTz6OS3jLNOo/Kup343afCzi5gRCXEjlB6dk5gmpGE10pSETmO1UYVCZpOrw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/a345e9df1cdefd6566c13ca61e8d6dca/vue-devtools-api-7.4.2.tgz", "_from": "file:vue-devtools-api-7.4.2.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Bn9dWSd8RV4kXFdnC4D4E0briLlTz6OS3jLNOo/Kup343afCzi5gRCXEjlB6dk5gmpGE10pSETmO1UYVCZpOrw==", "shasum": "7e13db82cde429ef8b3af4138064bb5e61bca873", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.2.tgz", "fileCount": 7, "unpackedSize": 4540, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBWBVlPFodVfvQAXZgJzcn8fhkz8tnWDNreFJA+moDnwIgWPGBo00fz/z7Oo6HuEb4FFyFymafUbLdO0FthYjJN88="}], "size": 2004}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.4.2_1725426552502_0.7791568993963569"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-04T05:09:12.633Z", "publish_time": 1725426552633, "_source_registry_name": "default"}, "7.4.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.4.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.4.3"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.4.3", "_integrity": "sha512-V5o2j0ZakZh2BQgB7+juV7CK0PSR+kZ/PaPvQRPbBeecC9+oqxEzvhMfTS/hOyHeTPPQd9VOXf2cMFe0QoXsMQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/dfb9460ee34cac683235d4c5125b971f/vue-devtools-api-7.4.3.tgz", "_from": "file:vue-devtools-api-7.4.3.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-V5o2j0ZakZh2BQgB7+juV7CK0PSR+kZ/PaPvQRPbBeecC9+oqxEzvhMfTS/hOyHeTPPQd9VOXf2cMFe0QoXsMQ==", "shasum": "cc9e2ac1add3bfd8f7bf187e7108aa336536e919", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.3.tgz", "fileCount": 7, "unpackedSize": 4540, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDGfTpIPfmMCXF+WibA5alVo5ZehN2cl6Np8Q4i+YYFAiBQim8tgWcQz9zu/cXZ/hYtU1ux/RQDkNgBXFZODNCY2g=="}], "size": 2004}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.4.3_1725428341746_0.3967503591947763"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-04T05:39:01.886Z", "publish_time": 1725428341886, "_source_registry_name": "default"}, "7.4.4": {"name": "@vue/devtools-api", "type": "module", "version": "7.4.4", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.4.4"}, "publishConfig": {"tag": "next"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_id": "@vue/devtools-api@7.4.4", "_integrity": "sha512-Iqqy9yBFWBbPb/jHlJzU/OrU+iHSJ/e9p/v5pZhm/L5pUCX26z32bvvjPa28vMXxRehbAZTgX8zovOeqBTnhdg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/142edd992dc17c3372a718a63854df19/vue-devtools-api-7.4.4.tgz", "_from": "file:vue-devtools-api-7.4.4.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Iqqy9yBFWBbPb/jHlJzU/OrU+iHSJ/e9p/v5pZhm/L5pUCX26z32bvvjPa28vMXxRehbAZTgX8zovOeqBTnhdg==", "shasum": "12bcdd742cd6e0c372a0b3d50627fc5c5fdaff13", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.4.tgz", "fileCount": 7, "unpackedSize": 4540, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6a9Ef+evLl1PIfrjWV6GrmsKQSNyC5A7bfUgkpRH5vQIgPRZHSzoSohf0hV9+6xfikGWaYyTV0lyDdISlDdpjkXc="}], "size": 2004}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.4.4_1725460055209_0.2910494292624972"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-04T14:27:35.399Z", "publish_time": 1725460055399, "_source_registry_name": "default"}, "6.6.4": {"name": "@vue/devtools-api", "version": "6.6.4", "description": "Interact with the Vue devtools from the page", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "sideEffects": false, "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^20.11.16", "@types/webpack-env": "^1.15.1", "typescript": "^5.3.3"}, "_id": "@vue/devtools-api@6.6.4", "gitHead": "df6ab6bb7791a7a525a97990de73b3ea5e9a1941", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "homepage": "https://github.com/vuejs/vue-devtools#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==", "shasum": "cbe97fe0162b365edc1dba80e173f90492535343", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "fileCount": 40, "unpackedSize": 33264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA71mhVgaBot+n/ndE12n/wLWUhG41hQLudksggOp948AiBAAuf8HlqZXFSLENGKUiB54K+q9rHPKNy/AkRR0l6RSg=="}], "size": 7317}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_6.6.4_1725885662903_0.29753596282551054"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-09T12:41:03.053Z", "publish_time": 1725885663053, "_source_registry_name": "default"}, "7.4.5": {"name": "@vue/devtools-api", "type": "module", "version": "7.4.5", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.4.5"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.4.5", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_integrity": "sha512-PX9uXirHOY2P99kb1cP3DxWZojFW3acNMqd+l4i5nKcqY59trXTOfwDZXt2Qifu0OU1izAQb76Ur6NPVldF2KQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/7bb097a75cb132d764b6c3ce73b15abb/vue-devtools-api-7.4.5.tgz", "_from": "file:vue-devtools-api-7.4.5.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PX9uXirHOY2P99kb1cP3DxWZojFW3acNMqd+l4i5nKcqY59trXTOfwDZXt2Qifu0OU1izAQb76Ur6NPVldF2KQ==", "shasum": "4b6f0a780aa187ee281a8ad45f121004f1a264e7", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.5.tgz", "fileCount": 7, "unpackedSize": 4496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfmuMPvHVpHQ43HlddSSENhmAjY27+1T/YD+T35bZifQIgOLJhYZvfn/6jxWijAAQcidX4Ie79bjB36R0SBWBsOsE="}], "size": 1969}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.4.5_1726053542472_0.49320782376125005"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-11T11:19:02.645Z", "publish_time": 1726053542645, "_source_registry_name": "default"}, "7.4.6": {"name": "@vue/devtools-api", "type": "module", "version": "7.4.6", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.4.6"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.4.6", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_integrity": "sha512-XipBV5k0/IfTr0sNBDTg7OBUCp51cYMMXyPxLXJZ4K/wmUeMqt8cVdr2ZZGOFq+si/jTyCYnNxeKoyev5DOUUA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/836eaae8fcf55d39cbfd1fa702b0f292/vue-devtools-api-7.4.6.tgz", "_from": "file:vue-devtools-api-7.4.6.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-XipBV5k0/IfTr0sNBDTg7OBUCp51cYMMXyPxLXJZ4K/wmUeMqt8cVdr2ZZGOFq+si/jTyCYnNxeKoyev5DOUUA==", "shasum": "5e9249d6de3cee58624e511fdc727837b1f2d273", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.4.6.tgz", "fileCount": 7, "unpackedSize": 4496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBsNxpsW2EBAzjfABydLnAYcu20F3HPRl1IO+1KV5VKyAiEAqn6lithZHPPNwjXvk6h7vPiiqlaEYyZF0DrLAqbRVvA="}], "size": 1969}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.4.6_1727102593822_0.5684609184540503"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-23T14:43:13.978Z", "publish_time": 1727102593978, "_source_registry_name": "default"}, "7.5.0": {"name": "@vue/devtools-api", "version": "7.5.0", "author": {"name": "webfansplz"}, "license": "MIT", "_id": "@vue/devtools-api@7.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "homepage": "https://github.com/vuejs/devtools-next#readme", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "dist": {"shasum": "e3b5545b0501f23005a753d078d0a2cd985daa56", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.0.tgz", "fileCount": 7, "integrity": "sha512-0ofcCO285muzvpTyVM8liVAZEGHKzJdcwGFNfG2n26tptKoFsgHg715eeMXiqkHgoLpsDgEX3Ko16itjHiDPNg==", "signatures": [{"sig": "MEUCIQCbS+ejnXCX1LJdqnvGgiOz7EG4jZ6UyP4TDzR9Iq6VMAIgIDPxbZ9KNJsZXw8yJ9oK3fIVL8pCS9biMfgPc9mH9Xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4496, "size": 1969}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vue-devtools-api-7.5.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"stub": "tsup --watch --onSuccess 'tsup --dts-only'", "build": "tsup --clean", "prepare:type": "tsup --dts-only"}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/98ce57c320d27b025e150123826d755a/vue-devtools-api-7.5.0.tgz", "_integrity": "sha512-0ofcCO285muzvpTyVM8liVAZEGHKzJdcwGFNfG2n26tptKoFsgHg715eeMXiqkHgoLpsDgEX3Ko16itjHiDPNg==", "repository": {"url": "git+https://github.com/vuejs/devtools-next.git", "type": "git", "directory": "packages/devtools-api"}, "_npmVersion": "10.8.2", "description": "> Plugins API for easier DevTools integrations.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@vue/devtools-kit": "^7.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/devtools-api_7.5.0_1729082004036_0.3026211108960475", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-10-16T12:33:24.209Z", "publish_time": 1729082004209, "_source_registry_name": "default"}, "7.5.1": {"name": "@vue/devtools-api", "version": "7.5.1", "author": {"name": "webfansplz"}, "license": "MIT", "_id": "@vue/devtools-api@7.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "homepage": "https://github.com/vuejs/devtools-next#readme", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "dist": {"shasum": "bdbe34ebe865e2c36c5069e08b17555e0b5da44f", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.1.tgz", "fileCount": 7, "integrity": "sha512-2UqRIfUE6/GuBSUpud7r1yFtXOTz6Qn+3dBOL1JG4BdwGjC9gQlV/Nl1+BTLFlYlKnDA7EppPFNvRnWeCtBrqQ==", "signatures": [{"sig": "MEUCIQDYwQt+/xaR7uWhY9Xx55umMNKn/sIGFs/kCu38bmdITAIgCuOqfYKxRSdfBLbq8RMnfXxTpI1qZXLZGx/ThST1oeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4496, "size": 1969}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vue-devtools-api-7.5.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"stub": "tsup --watch --onSuccess 'tsup --dts-only'", "build": "tsup --clean", "prepare:type": "tsup --dts-only"}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/a4dac962f3d225d4c5ba74c2959924ac/vue-devtools-api-7.5.1.tgz", "_integrity": "sha512-2UqRIfUE6/GuBSUpud7r1yFtXOTz6Qn+3dBOL1JG4BdwGjC9gQlV/Nl1+BTLFlYlKnDA7EppPFNvRnWeCtBrqQ==", "repository": {"url": "git+https://github.com/vuejs/devtools-next.git", "type": "git", "directory": "packages/devtools-api"}, "_npmVersion": "10.8.2", "description": "> Plugins API for easier DevTools integrations.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@vue/devtools-kit": "^7.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/devtools-api_7.5.1_1729083042944_0.5164033435752595", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-10-16T12:50:43.119Z", "publish_time": 1729083043119, "_source_registry_name": "default"}, "7.5.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.5.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.5.2"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.5.2", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_integrity": "sha512-VxPbAQxJrYSIkoGVvQ2oOoKW8u4CMpvRLySTxhoJA38z8bQEGy9GO33eoRY/DulJbSFRfjZFNvH+dh8B4qpesQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/51631f24e8a1a1c0231bf632a25be714/vue-devtools-api-7.5.2.tgz", "_from": "file:vue-devtools-api-7.5.2.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VxPbAQxJrYSIkoGVvQ2oOoKW8u4CMpvRLySTxhoJA38z8bQEGy9GO33eoRY/DulJbSFRfjZFNvH+dh8B4qpesQ==", "shasum": "2d3f8da97a56b966572c02249b7bcf777aa105cd", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.2.tgz", "fileCount": 7, "unpackedSize": 4496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwve38S3dhbVRv1bI7CKiSofW7TcVT61UCPr2bMx81jQIhAMaQuBfZZeZr1dEoowBMzXQDeWaErR7Yv+ZvWBYQb53o"}], "size": 1969}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.5.2_1729086936233_0.5156010948937977"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-16T13:55:36.436Z", "publish_time": 1729086936436, "_source_registry_name": "default"}, "7.5.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.5.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.5.3"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.5.3", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_integrity": "sha512-nwz45qBxHOUdZzaYP9V3E1aFOgPpoMmNlBcGn0dsUxizlws4wJ4V6P6849yt28p5NSQ/2E3V87JXFAuk3N9Inw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/a18e109492a062dadb5e7c7efa64443a/vue-devtools-api-7.5.3.tgz", "_from": "file:vue-devtools-api-7.5.3.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-nwz45qBxHOUdZzaYP9V3E1aFOgPpoMmNlBcGn0dsUxizlws4wJ4V6P6849yt28p5NSQ/2E3V87JXFAuk3N9Inw==", "shasum": "577f191f419ceef6c5c8e5366169cc8b6755a645", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.3.tgz", "fileCount": 7, "unpackedSize": 4496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEfACF8bPEf3bcde6DfKkUJLU2Yx1EVmqdk1qdQFjYDAiAPozW1w1Lw7l8XFc8XjbcYYCtxQu2lCAVvYx+ZzW9Ddw=="}], "size": 1969}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.5.3_1729607297252_0.6451002581179983"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-22T14:28:17.438Z", "publish_time": 1729607297438, "_source_registry_name": "default"}, "7.5.4": {"name": "@vue/devtools-api", "type": "module", "version": "7.5.4", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.5.4"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.5.4", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "homepage": "https://github.com/vuejs/devtools-next#readme", "_integrity": "sha512-j9UC/KeYUNZ6AyCJxBROBCbogB5YHW6PZv9VnCNp2ntE4rq426Lfc8WP5B9V+rXBwqWmrgZTGYBa31CBSxdAUg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/ef0e0e08eeffd0fbc3bdac31edf745ac/vue-devtools-api-7.5.4.tgz", "_from": "file:vue-devtools-api-7.5.4.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-j9UC/KeYUNZ6AyCJxBROBCbogB5YHW6PZv9VnCNp2ntE4rq426Lfc8WP5B9V+rXBwqWmrgZTGYBa31CBSxdAUg==", "shasum": "41f23528b850bcebb8fb351e6561b577499184d4", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.4.tgz", "fileCount": 7, "unpackedSize": 4496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHXFF9WHYAzbzxQLFUUdPcdfhMjsrnZBBVgNT0Z1X77vAiEAkwcMA73uIqfi/cIJDuS4wWgHL0dn0TtlybgYVF0ca5A="}], "size": 1969}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.5.4_1729776307281_0.8408785633036746"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-24T13:25:07.458Z", "publish_time": 1729776307458, "_source_registry_name": "default"}, "7.5.5": {"name": "@vue/devtools-api", "version": "7.5.5", "author": {"name": "webfansplz"}, "license": "MIT", "_id": "@vue/devtools-api@7.5.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "homepage": "https://github.com/vuejs/devtools#readme", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "dist": {"shasum": "9940e0575c957ef4ebe48528176cc4e1627abb23", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.5.tgz", "fileCount": 7, "integrity": "sha512-zo2Nj1Pl/T9Q2um2se2188z4D9Fkod/8rqXeD6+EUMLyww3E/nF6cT6yuGJ7tY1mcfDr11ymARsb89CvyGapng==", "signatures": [{"sig": "MEUCIQD4CjlbC5CmGQRE3gJC4YvSAiXpAHPIOr6U637LayCTIAIgfEhQ29T2mEt1anAZUdp5qCcCn4eTNwbPMrtga8IMoW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4491, "size": 1967}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vue-devtools-api-7.5.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"stub": "tsup --watch --onSuccess 'tsup --dts-only'", "build": "tsup --clean", "prepare:type": "tsup --dts-only"}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/03043525a8dc0cd661a5541499604198/vue-devtools-api-7.5.5.tgz", "_integrity": "sha512-zo2Nj1Pl/T9Q2um2se2188z4D9Fkod/8rqXeD6+EUMLyww3E/nF6cT6yuGJ7tY1mcfDr11ymARsb89CvyGapng==", "repository": {"url": "git+https://github.com/vuejs/devtools.git", "type": "git", "directory": "packages/devtools-api"}, "_npmVersion": "10.8.2", "description": "> Plugins API for easier DevTools integrations.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@vue/devtools-kit": "^7.5.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/devtools-api_7.5.5_1730209755269_0.16652788856419232", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-10-29T13:49:15.428Z", "publish_time": 1730209755428, "_source_registry_name": "default"}, "7.5.6": {"name": "@vue/devtools-api", "type": "module", "version": "7.5.6", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.5.6"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.5.6", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-/7ov2ioU80fYcYENEJXp88l88gX1PJCGJdMtQmUV3VQmGgQvKrpeUoPWgkpXPkUxmAquh6PZnVtXeDpTX5mmLg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/7ebadb2ff4e42d85b2f7a4fbfcd5a6db/vue-devtools-api-7.5.6.tgz", "_from": "file:vue-devtools-api-7.5.6.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/7ov2ioU80fYcYENEJXp88l88gX1PJCGJdMtQmUV3VQmGgQvKrpeUoPWgkpXPkUxmAquh6PZnVtXeDpTX5mmLg==", "shasum": "a8127adbb94791815139d436345ad511a2e5b548", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.5.6.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGkXsP6py+rXYXfpZmKPpeTTMOH3/uJ5Gdu6gRlL5v8/AiBZAq6o1hbJqnQC0l24xlXez6ny4PSqylAa4WWqAKO8fA=="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.5.6_1730211791116_0.8411945974580499"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T14:23:11.343Z", "publish_time": 1730211791343, "_source_registry_name": "default"}, "7.6.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.0"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.0", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-FGxX7jatS0ZcsglIBcdxAQciYSUpb/eXt610x0YDVBdIJaH0x6iDg0vs4MhbzSIrRCywjFmW+ZwpYus/eFIS8Q==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/fa7e068d7d6529c9c94d8db20a88707a/vue-devtools-api-7.6.0.tgz", "_from": "file:vue-devtools-api-7.6.0.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FGxX7jatS0ZcsglIBcdxAQciYSUpb/eXt610x0YDVBdIJaH0x6iDg0vs4MhbzSIrRCywjFmW+ZwpYus/eFIS8Q==", "shasum": "8ed216c2c38e49529627fe90a830cfde86cde8fd", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.0.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICoHAkRxThQN+w4ChaKRFMGdm3D42lj4wJ6tdGf3xfM/AiBhFcqT36VA1CEMtlsn7yvTt0X20XRiMLmI5XpgHTLcpQ=="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.0_1730308198503_0.9481079823529912"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-30T17:09:58.676Z", "publish_time": 1730308198676, "_source_registry_name": "default"}, "7.6.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.1"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.1", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-W99uw1nJKWeG4V2Bgp/pR1vnIREfixmnYW71wjtID7Gn/XnHH+nhfJmNy/0DjxcXOM14POVBDkl9JGlsOx1UjQ==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/a76999a7abce230464080273676c3d5c/vue-devtools-api-7.6.1.tgz", "_from": "file:vue-devtools-api-7.6.1.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-W99uw1nJKWeG4V2Bgp/pR1vnIREfixmnYW71wjtID7Gn/XnHH+nhfJmNy/0DjxcXOM14POVBDkl9JGlsOx1UjQ==", "shasum": "424ab75e102a3a07af52c3627bd4166d76ffa64b", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.1.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJ5pzLaMMdBFOlsIgQrT/e05FqMgL4yYUo0JGyC1rAoAiAiDG7Mg+ywPoXfAv0pkCYRRTwEfyYN1z2ol8x+Hy9d4A=="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.1_1730323696074_0.4527864511830273"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-30T21:28:16.312Z", "publish_time": 1730323696312, "_source_registry_name": "default"}, "7.6.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.2"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.2", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-NCT0ujqlwAhoFvCsAG7G5qS8w/A/dhvFSt2BhmNxyqgpYDrf9CG1zYyWLQkE3dsZ+5lCT6ULUic2VKNaE07Vzg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/13e8fe87554a6fd01587ca61cbbbd886/vue-devtools-api-7.6.2.tgz", "_from": "file:vue-devtools-api-7.6.2.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NCT0ujqlwAhoFvCsAG7G5qS8w/A/dhvFSt2BhmNxyqgpYDrf9CG1zYyWLQkE3dsZ+5lCT6ULUic2VKNaE07Vzg==", "shasum": "9beb95e3faba24aa89b8237f4ac713210a28b6f7", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.2.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8kQU5iK5/82oRVNYAQpfPrk8UD/7eQF0Gc6EyDD+iUgIgZuU1eB3scwdMu9q2QwJ/WRWknC8+Yw05NLpm/d+7jdI="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.2_1730393285399_0.5650661411712452"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-31T16:48:05.630Z", "publish_time": 1730393285630, "_source_registry_name": "default"}, "7.6.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.3"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.3", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-H2TRzFA9hNezdtM6I0y3RCMhIg5T3gib/p9qI2IAS8gB9tvkAv4JZHAZZl5BZHhO7btuHkvHzU5qpO/vdsjYMg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/d646813bfebe5dbe70de3e61eab1c2c5/vue-devtools-api-7.6.3.tgz", "_from": "file:vue-devtools-api-7.6.3.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-H2TRzFA9hNezdtM6I0y3RCMhIg5T3gib/p9qI2IAS8gB9tvkAv4JZHAZZl5BZHhO7btuHkvHzU5qpO/vdsjYMg==", "shasum": "dba719d43119c048b9d640673dec9424bf2bb460", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.3.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFxo1zEt9CpP5gyPj0u5ok2cgUneDaJQBa9ZRxH18a+NAiBBzf7FemZ7eRTVT4YWUlnEu5J6fy/G22XBIqLZy345Ng=="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.3_1730812504341_0.26294926689762077"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-05T13:15:04.524Z", "publish_time": 1730812504524, "_source_registry_name": "default"}, "7.6.4": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.4", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.4"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.4", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-5AaJ5ELBIuevmFMZYYLuOO9HUuY/6OlkOELHE7oeDhy4XD/hSODIzktlsvBOsn+bto3aD0psj36LGzwVu5Ip8w==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/bc988713fc3e8faee49035481b0e7d76/vue-devtools-api-7.6.4.tgz", "_from": "file:vue-devtools-api-7.6.4.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-5AaJ5ELBIuevmFMZYYLuOO9HUuY/6OlkOELHE7oeDhy4XD/hSODIzktlsvBOsn+bto3aD0psj36LGzwVu5Ip8w==", "shasum": "024bf0ecf543395844f4d97cff0a84f8f759b29d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.4.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHbSpzFj7YFBvaKsQLiWHu6Znz0sYE6Llo/uPKV3adFwIgQ8NQ6vbnbZvZhzs8kZm0+IfrLiYoUhZBVoS1t8yFpPI="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.4_1731335606807_0.24330975009601818"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-11T14:33:26.970Z", "publish_time": 1731335606970, "_source_registry_name": "default"}, "7.6.5": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.5", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.5"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.5", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-HHvbX7X85k7cqWV6pkcphzlEBbV+kRDzGKumhk9WnDIQuKKw5u2FPvk9icM8v4Wk5VRU1jiMoSFcFBuMqNL6eA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/4dd017a6274910ec402dda55f66b82e5/vue-devtools-api-7.6.5.tgz", "_from": "file:vue-devtools-api-7.6.5.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-HHvbX7X85k7cqWV6pkcphzlEBbV+kRDzGKumhk9WnDIQuKKw5u2FPvk9icM8v4Wk5VRU1jiMoSFcFBuMqNL6eA==", "shasum": "ea31207c594565900710db44a1073b5ca357cf7f", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.5.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9hr7RvASIBiGTugFcJ751T1f2Gz2S7B09Cue5+LKpkQIhAMiT5kuGJq8/f1rjCDyxpEaAVcKVcSEmn2+8VgfyW6po"}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.5_1732713388836_0.3938043959039581"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-27T13:16:29.024Z", "publish_time": 1732713389024, "_source_registry_name": "default"}, "7.6.6": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.6", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.6"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.6", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-BIiWzP0mvT/Whmz6WWcJyspC9TliVY5HC25A87aUwzu1TYiQUvv0WaLdkP97FE58YkjCtZmCrjZwagkdIdPihw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/61b7a82e4b434f61cfb5dec289e1f90c/vue-devtools-api-7.6.6.tgz", "_from": "file:vue-devtools-api-7.6.6.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-BIiWzP0mvT/Whmz6WWcJyspC9TliVY5HC25A87aUwzu1TYiQUvv0WaLdkP97FE58YkjCtZmCrjZwagkdIdPihw==", "shasum": "a1a7f426e3eed1288a0c7cb5c4464588be31cd5d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.6.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyB9f1QAgLI4zwRTEVEcy9BzzhyB35ICkYdu4nhVFWWQIhAOis6dSzJFGHuqhIVFTxbqWYvX7YXYmpNt74t4OhVTu3"}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.6_1732801072257_0.08757803743847115"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-28T13:37:52.415Z", "publish_time": 1732801072415, "_source_registry_name": "default"}, "7.6.7": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.7", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.7"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.7", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-PV4I31WaV2rfA8RGauM+69uFEzWkqtP561RiLU2wK+Ce85u3zyKW3aoESlLCNzkc4y0JaJyskH6zAE3xWOP8+Q==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/ebfe877a2975525245cc87db041d0dff/vue-devtools-api-7.6.7.tgz", "_from": "file:vue-devtools-api-7.6.7.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PV4I31WaV2rfA8RGauM+69uFEzWkqtP561RiLU2wK+Ce85u3zyKW3aoESlLCNzkc4y0JaJyskH6zAE3xWOP8+Q==", "shasum": "2e5a8ae02df8a6c5518d15192e5964cbd40a2aa1", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.7.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAMdW8AExBYKxEzl150cBRuv5PexX+NdUSeRkIQt87JeAiEAk73N8nMRlaIFDSkrP8/NNcesVGsskJ2XBsmRis5ywxo="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/devtools-api_7.6.7_1732806148503_0.3904381608737151"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-28T15:02:28.693Z", "publish_time": 1732806148693, "_source_registry_name": "default"}, "7.6.8": {"name": "@vue/devtools-api", "type": "module", "version": "7.6.8", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.6.8"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.6.8", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-ma6dY/sZR36zALVsV1W7eC57c6IJPXsy8SNgZn1PLVWU4z4dPn5TIBmnF4stmdJ4sQcixqKaQ8pwjbMPzEZwiA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/2a400fa571bb0a176c095074b7d41400/vue-devtools-api-7.6.8.tgz", "_from": "file:vue-devtools-api-7.6.8.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ma6dY/sZR36zALVsV1W7eC57c6IJPXsy8SNgZn1PLVWU4z4dPn5TIBmnF4stmdJ4sQcixqKaQ8pwjbMPzEZwiA==", "shasum": "8cd39eb9846d81eccbffe41c1f1ca33ad195d691", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.6.8.tgz", "fileCount": 7, "unpackedSize": 4491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFbelxkVcmeQL4sUto0OjRvpF3OMXp67I41Za1i+IENSAiEA4kCCCrJUK2wm5NQHKxd3+2SgtC6J2BO2JxPQrcmj7hY="}], "size": 1967}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.6.8_1733916479654_0.22621298600251238"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-11T11:27:59.839Z", "publish_time": 1733916479839, "_source_registry_name": "default"}, "7.7.0": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.0", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.0"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.0", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-bHEv6kT85BHtyGgDhE07bAUMAy7zpv6nnR004nSTd0wWMrAOtcrYoXO5iyr20Hkf5jR8obQOfS3byW+I3l2CCA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/d7ee5fe6ced26d79711a9c04c4f4c2a2/vue-devtools-api-7.7.0.tgz", "_from": "file:vue-devtools-api-7.7.0.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bHEv6kT85BHtyGgDhE07bAUMAy7zpv6nnR004nSTd0wWMrAOtcrYoXO5iyr20Hkf5jR8obQOfS3byW+I3l2CCA==", "shasum": "2fcc1fd8800a3fd3942ca6716156c0cbd403dbd4", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.0.tgz", "fileCount": 7, "unpackedSize": 4497, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDutGa9H8b34Yww8XRMnYnGcFGk202KtNYaZJr4TRLsJAIhAKCqa8swajQxDwukEuRYpw/B/zafmkWdfndC1yPFtata"}], "size": 1966}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.0_1736256825342_0.4805431378363041"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-07T13:33:45.533Z", "publish_time": 1736256825533, "_source_registry_name": "default"}, "7.7.1": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.1", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.1"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.1", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-Cexc8GimowoDkJ6eNelOPdYIzsu2mgNyp0scOQ3tiaYSb9iok6LOESSsJvHaI+ib3joRfqRJNLkHFjhNuWA5dg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/08d4c38a1fd1db9059c79fc1626f8659/vue-devtools-api-7.7.1.tgz", "_from": "file:vue-devtools-api-7.7.1.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Cexc8GimowoDkJ6eNelOPdYIzsu2mgNyp0scOQ3tiaYSb9iok6LOESSsJvHaI+ib3joRfqRJNLkHFjhNuWA5dg==", "shasum": "6a44bf03ce27ba0230461171812d9ae98aeb2458", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.1.tgz", "fileCount": 7, "unpackedSize": 4497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD9AwqtytAkHEdbfOPB/Xzifb/te+EsX/L8BYGoW6y25gIgU3Z0dMRzP5Khmr6j6ORxb+nmVKDizq2ftfG2YxX/YPA="}], "size": 1966}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.1_1737677086587_0.8573052084940791"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-24T00:04:46.784Z", "publish_time": 1737677086784, "_source_registry_name": "default"}, "7.7.2": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.2", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.2"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.2", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-1syn558KhyN+chO5SjlZIwJ8bV/bQ1nOVTG66t2RbG66ZGekyiYNmRO7X9BJCXQqPsFHlnksqvPhce2qpzxFnA==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/6b9161a0ac6395479296d0d9501a49bf/vue-devtools-api-7.7.2.tgz", "_from": "file:vue-devtools-api-7.7.2.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1syn558KhyN+chO5SjlZIwJ8bV/bQ1nOVTG66t2RbG66ZGekyiYNmRO7X9BJCXQqPsFHlnksqvPhce2qpzxFnA==", "shasum": "49837eae6f61fc43a09f5d6c2d3210f9f73a0d09", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.2.tgz", "fileCount": 7, "unpackedSize": 4521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCPtNfOAprntOwFLrTq3uXAHk21616LEfU12lFmeB/DgAIgG/kTPryGiox976sTAIYZdYWxwuoWmfTFtW85qdM74Rc="}], "size": 1980}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.2_1739368058312_0.9168533898980329"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-12T13:47:38.475Z", "publish_time": 1739368058475, "_source_registry_name": "default"}, "7.7.3": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.3", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.3"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.3", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-LZTp7ga+7KWY0sW+B0p/GFo5l0S58iV0aXcf693LpCm7gSWqEgeFRoQZEY/lc7foSM3chEdG00zL0EwRSi4v0w==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/0da1f5930a580b927758f348de80dcb8/vue-devtools-api-7.7.3.tgz", "_from": "file:vue-devtools-api-7.7.3.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-LZTp7ga+7KWY0sW+B0p/GFo5l0S58iV0aXcf693LpCm7gSWqEgeFRoQZEY/lc7foSM3chEdG00zL0EwRSi4v0w==", "shasum": "5c7c2fff24139ee2fd7b842e12ca12019fe8aaef", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.3.tgz", "fileCount": 7, "unpackedSize": 4521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFGIS53u422ZlfOpnDkxmQU6UBlUUbRpwu+c++BUyYovAiBT4D2PVhj8pXIPSwIHF8VSTdl80xEh6RQFVjxoIBgrlA=="}], "size": 1980}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.3_1744810452558_0.014049204742457189"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-16T13:34:12.738Z", "publish_time": 1744810452738, "_source_registry_name": "default"}, "7.7.4": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.4", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.4"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.4", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-u4bNkUZsR1gbgXaLAhsQ7tfSMCdSUN0wNpJoKWtQ9IcsnX5MzV2ZY/U0LOyHDvnMrKvNiPK8K1HF6vS/wG1ybg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/e67d2afadfde7738d8f75b5c36c034e2/vue-devtools-api-7.7.4.tgz", "_from": "file:vue-devtools-api-7.7.4.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-u4bNkUZsR1gbgXaLAhsQ7tfSMCdSUN0wNpJoKWtQ9IcsnX5MzV2ZY/U0LOyHDvnMrKvNiPK8K1HF6vS/wG1ybg==", "shasum": "7823e259a36958bc0878bdedbbaa24aaae72997d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.4.tgz", "fileCount": 7, "unpackedSize": 4521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHA+82FWJKmPxXd7VTnYQ05oXWOXIE5CE5FbiGh40rKDAiALt+o42lrpzCvE3857cufUoySdFigAhkNELp5M8KIqkg=="}], "size": 1980}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.4_1744818845930_0.8675788541560392"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-16T15:54:06.089Z", "publish_time": 1744818846089, "_source_registry_name": "default"}, "7.7.5": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.5", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.5"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.5", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-HYV3tJGARROq5nlVMJh5KKHk7GU8Au3IrrmNNqr978m0edxgpHgYPDoNUGrvEgIbObz09SQezFR3A1EVmB5WZg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/48cc91fd923d18f7c47a521223900bad/vue-devtools-api-7.7.5.tgz", "_from": "file:vue-devtools-api-7.7.5.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-HYV3tJGARROq5nlVMJh5KKHk7GU8Au3IrrmNNqr978m0edxgpHgYPDoNUGrvEgIbObz09SQezFR3A1EVmB5WZg==", "shasum": "1e6c3d72c1a77419c1940bc94ee12d2949334aaf", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.5.tgz", "fileCount": 7, "unpackedSize": 4521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCxe+w54l81xB7cwpcTObuB/zwy3COT41YB9SMcmvRzEwIgMYe57AsWNdkZCxGRGKqAJLHZ2gY8Jh7cz0NxHMlZXqE="}], "size": 1980}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.5_1744821476846_0.4487688651366897"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-16T16:37:57.023Z", "publish_time": 1744821477023, "_source_registry_name": "default"}, "7.7.6": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.6", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.6"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.6", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-b2Xx0KvXZObePpXPYHvBRRJLDQn5nhKjXh7vUhMEtWxz1AYNFOVIsh5+HLP8xDGL7sy+Q7hXeUxPHB/KgbtsPw==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/ae1bf962af02b568b3edee5b97852292/vue-devtools-api-7.7.6.tgz", "_from": "file:vue-devtools-api-7.7.6.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-b2Xx0KvXZObePpXPYHvBRRJLDQn5nhKjXh7vUhMEtWxz1AYNFOVIsh5+HLP8xDGL7sy+Q7hXeUxPHB/KgbtsPw==", "shasum": "4af5dbc77bcc8543f0a8e6f029f598ed978d6c7d", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.6.tgz", "fileCount": 7, "unpackedSize": 4521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCX0HPS1QYiqPdAcexqheWO6Z0OUQ/AqXtRigoodiGjqQIhAJpWHKMEL3OCJvX98IqWVRH/O5Peql67a2FkhL4smwLk"}], "size": 1980}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.6_1745850440833_0.8736632262191248"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-28T14:27:21.015Z", "publish_time": 1745850441015, "_source_registry_name": "default"}, "7.7.7": {"name": "@vue/devtools-api", "type": "module", "version": "7.7.7", "author": {"name": "webfansplz"}, "license": "MIT", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "dependencies": {"@vue/devtools-kit": "^7.7.7"}, "scripts": {"build": "tsup --clean", "prepare:type": "tsup --dts-only", "stub": "tsup --watch --onSuccess 'tsup --dts-only'"}, "_id": "@vue/devtools-api@7.7.7", "types": "./dist/index.d.ts", "description": "> Plugins API for easier DevTools integrations.", "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "_integrity": "sha512-lwOnNBH2e7x1fIIbVT7yF5D+YWhqELm55/4ZKf45R9T8r9dE2AIOy8HKjfqzGsoTHFbWbr337O4E0A0QADnjBg==", "_resolved": "/private/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/f06a1c2e94d4dbdf2102f0fd47791860/vue-devtools-api-7.7.7.tgz", "_from": "file:vue-devtools-api-7.7.7.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lwOnNBH2e7x1fIIbVT7yF5D+YWhqELm55/4ZKf45R9T8r9dE2AIOy8HKjfqzGsoTHFbWbr337O4E0A0QADnjBg==", "shasum": "5ef5f55f60396220725a273548c0d7ee983d5d34", "tarball": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.7.tgz", "fileCount": 7, "unpackedSize": 4521, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCAyq1443V1NyvjSU7ra5rljBB0kaBHBuYD46q+OB3kEAIhAJp/fBoLDk8FqWW2UBe+TMWrz4C0UP8zp8Xtl1VzxUOV"}], "size": 1980}, "_npmUser": {"name": "webfansplz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}, {"name": "webfansplz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/devtools-api_7.7.7_1749985901375_0.26100778920364354"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-15T11:11:41.559Z", "publish_time": 1749985901559, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/vuejs/devtools/issues"}, "homepage": "https://github.com/vuejs/devtools#readme", "repository": {"directory": "packages/devtools-api", "type": "git", "url": "git+https://github.com/vuejs/devtools.git"}, "_source_registry_name": "default"}