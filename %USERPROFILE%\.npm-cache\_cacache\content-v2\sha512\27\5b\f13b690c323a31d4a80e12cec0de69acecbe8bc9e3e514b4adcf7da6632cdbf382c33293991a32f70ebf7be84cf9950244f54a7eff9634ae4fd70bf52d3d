{"_attachments": {}, "_id": "async-validator", "_rev": "7833-61f161c7b677e08f51166534", "description": "validate form asynchronous", "dist-tags": {"latest": "4.2.5"}, "license": "MIT", "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "async-validator", "readme": "# async-validator\n\n[![NPM version][npm-image]][npm-url]\n[![build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![node version][node-image]][node-url]\n[![npm download][download-image]][download-url]\n[![npm bundle size (minified + gzip)][bundlesize-image]][bundlesize-url]\n\n[npm-image]: https://img.shields.io/npm/v/async-validator.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/async-validator\n[travis-image]:https://app.travis-ci.com/yiminghe/async-validator.svg?branch=master\n[travis-url]: https://app.travis-ci.com/github/yiminghe/async-validator\n[coveralls-image]: https://img.shields.io/coveralls/yiminghe/async-validator.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/yiminghe/async-validator?branch=master\n[node-image]: https://img.shields.io/badge/node.js-%3E=4.0.0-green.svg?style=flat-square\n[node-url]: https://nodejs.org/download/\n[download-image]: https://img.shields.io/npm/dm/async-validator.svg?style=flat-square\n[download-url]: https://npmjs.org/package/async-validator\n[bundlesize-image]: https://img.shields.io/bundlephobia/minzip/async-validator.svg?label=gzip%20size\n[bundlesize-url]: https://bundlephobia.com/result?p=async-validator\n\nValidate form asynchronous. A variation of https://github.com/freeformsystems/async-validate\n\n## Install\n\n```bash\nnpm i async-validator\n```\n\n## Usage\n\nBasic usage involves defining a descriptor, assigning it to a schema and passing the object to be validated and a callback function to the `validate` method of the schema:\n\n```js\nimport Schema from 'async-validator';\nconst descriptor = {\n  name: {\n    type: 'string',\n    required: true,\n    validator: (rule, value) => value === 'muji',\n  },\n  age: {\n    type: 'number',\n    asyncValidator: (rule, value) => {\n      return new Promise((resolve, reject) => {\n        if (value < 18) {\n          reject('too young');  // reject with error message\n        } else {\n          resolve();\n        }\n      });\n    },\n  },\n};\nconst validator = new Schema(descriptor);\nvalidator.validate({ name: 'muji' }, (errors, fields) => {\n  if (errors) {\n    // validation failed, errors is an array of all errors\n    // fields is an object keyed by field name with an array of\n    // errors per field\n    return handleErrors(errors, fields);\n  }\n  // validation passed\n});\n\n// PROMISE USAGE\nvalidator.validate({ name: 'muji', age: 16 }).then(() => {\n  // validation passed or without error message\n}).catch(({ errors, fields }) => {\n  return handleErrors(errors, fields);\n});\n```\n\n## API\n\n### Validate\n\n```js\nfunction(source, [options], callback): Promise\n```\n\n* `source`: The object to validate (required).\n* `options`: An object describing processing options for the validation (optional).\n* `callback`: A callback function to invoke when validation completes (optional).\n\nThe method will return a Promise object like:\n* `then()`，validation passed\n* `catch({ errors, fields })`，validation failed, errors is an array of all errors, fields is an object keyed by field name with an array of errors per field\n\n### Options\n\n* `suppressWarning`: Boolean, whether to suppress internal warning about invalid value.\n\n* `first`: Boolean, Invoke `callback` when the first validation rule generates an error,\nno more validation rules are processed.\nIf your validation involves multiple asynchronous calls (for example, database queries) and you only need the first error use this option.\n\n* `firstFields`: Boolean|String[], Invoke `callback` when the first validation rule of the specified field generates an error,\nno more validation rules of the same field are processed.  `true` means all fields.\n\n### Rules\n\nRules may be functions that perform validation.\n\n```js\nfunction(rule, value, callback, source, options)\n```\n\n* `rule`: The validation rule in the source descriptor that corresponds to the field name being validated. It is always assigned a `field` property with the name of the field being validated.\n* `value`: The value of the source object property being validated.\n* `callback`: A callback function to invoke once validation is complete. It expects to be passed an array of `Error` instances to indicate validation failure. If the check is synchronous, you can directly return a ` false ` or ` Error ` or ` Error Array `.\n* `source`: The source object that was passed to the `validate` method.\n* `options`: Additional options.\n* `options.messages`: The object containing validation error messages, will be deep merged with defaultMessages.\n\nThe options passed to `validate` or `asyncValidate` are passed on to the validation functions so that you may reference transient data (such as model references) in validation functions. However, some option names are reserved; if you use these properties of the options object they are overwritten. The reserved properties are `messages`, `exception` and `error`.\n\n```js\nimport Schema from 'async-validator';\nconst descriptor = {\n  name(rule, value, callback, source, options) {\n    const errors = [];\n    if (!/^[a-z0-9]+$/.test(value)) {\n      errors.push(new Error(\n        util.format('%s must be lowercase alphanumeric characters', rule.field),\n      ));\n    }\n    return errors;\n  },\n};\nconst validator = new Schema(descriptor);\nvalidator.validate({ name: 'Firstname' }, (errors, fields) => {\n  if (errors) {\n    return handleErrors(errors, fields);\n  }\n  // validation passed\n});\n```\n\nIt is often useful to test against multiple validation rules for a single field, to do so make the rule an array of objects, for example:\n\n```js\nconst descriptor = {\n  email: [\n    { type: 'string', required: true, pattern: Schema.pattern.email },\n    { \n      validator(rule, value, callback, source, options) {\n        const errors = [];\n        // test if email address already exists in a database\n        // and add a validation error to the errors array if it does\n        return errors;\n      },\n    },\n  ],\n};\n```\n\n#### Type\n\nIndicates the `type` of validator to use. Recognised type values are:\n\n* `string`: Must be of type `string`. `This is the default type.`\n* `number`: Must be of type `number`.\n* `boolean`: Must be of type `boolean`.\n* `method`: Must be of type `function`.\n* `regexp`: Must be an instance of `RegExp` or a string that does not generate an exception when creating a new `RegExp`.\n* `integer`: Must be of type `number` and an integer.\n* `float`: Must be of type `number` and a floating point number.\n* `array`: Must be an array as determined by `Array.isArray`.\n* `object`: Must be of type `object` and not `Array.isArray`.\n* `enum`: Value must exist in the `enum`.\n* `date`: Value must be valid as determined by `Date`\n* `url`: Must be of type `url`.\n* `hex`: Must be of type `hex`.\n* `email`: Must be of type `email`.\n* `any`: Can be any type.\n\n#### Required\n\nThe `required` rule property indicates that the field must exist on the source object being validated.\n\n#### Pattern\n\nThe `pattern` rule property indicates a regular expression that the value must match to pass validation.\n\n#### Range\n\nA range is defined using the `min` and `max` properties. For `string` and `array` types comparison is performed against the `length`, for `number` types the number must not be less than `min` nor greater than `max`.\n\n#### Length\n\nTo validate an exact length of a field specify the `len` property. For `string` and `array` types comparison is performed on the `length` property, for the `number` type this property indicates an exact match for the `number`, ie, it may only be strictly equal to `len`.\n\nIf the `len` property is combined with the `min` and `max` range properties, `len` takes precedence.\n\n#### Enumerable\n\n> Since version 3.0.0 if you want to validate the values `0` or `false` inside `enum` types, you have to include them explicitly.\n\nTo validate a value from a list of possible values use the `enum` type with a `enum` property listing the valid values for the field, for example:\n\n```js\nconst descriptor = {\n  role: { type: 'enum', enum: ['admin', 'user', 'guest'] },\n};\n```\n\n#### Whitespace\n\nIt is typical to treat required fields that only contain whitespace as errors. To add an additional test for a string that consists solely of whitespace add a `whitespace` property to a rule with a value of `true`. The rule must be a `string` type.\n\nYou may wish to sanitize user input instead of testing for whitespace, see [transform](#transform) for an example that would allow you to strip whitespace.\n\n\n#### Deep Rules\n\nIf you need to validate deep object properties you may do so for validation rules that are of the `object` or `array` type by assigning nested rules to a `fields` property of the rule.\n\n```js\nconst descriptor = {\n  address: {\n    type: 'object',\n    required: true,\n    fields: {\n      street: { type: 'string', required: true },\n      city: { type: 'string', required: true },\n      zip: { type: 'string', required: true, len: 8, message: 'invalid zip' },\n    },\n  },\n  name: { type: 'string', required: true },\n};\nconst validator = new Schema(descriptor);\nvalidator.validate({ address: {} }, (errors, fields) => {\n  // errors for address.street, address.city, address.zip\n});\n```\n\nNote that if you do not specify the `required` property on the parent rule it is perfectly valid for the field not to be declared on the source object and the deep validation rules will not be executed as there is nothing to validate against.\n\nDeep rule validation creates a schema for the nested rules so you can also specify the `options` passed to the `schema.validate()` method.\n\n```js\nconst descriptor = {\n  address: {\n    type: 'object',\n    required: true,\n    options: { first: true },\n    fields: {\n      street: { type: 'string', required: true },\n      city: { type: 'string', required: true },\n      zip: { type: 'string', required: true, len: 8, message: 'invalid zip' },\n    },\n  },\n  name: { type: 'string', required: true },\n};\nconst validator = new Schema(descriptor);\n\nvalidator.validate({ address: {} })\n  .catch(({ errors, fields }) => {\n    // now only errors for street and name    \n  });\n```\n\nThe parent rule is also validated so if you have a set of rules such as:\n\n```js\nconst descriptor = {\n  roles: {\n    type: 'array',\n    required: true,\n    len: 3,\n    fields: {\n      0: { type: 'string', required: true },\n      1: { type: 'string', required: true },\n      2: { type: 'string', required: true },\n    },\n  },\n};\n```\n\nAnd supply a source object of `{ roles: ['admin', 'user'] }` then two errors will be created. One for the array length mismatch and one for the missing required array entry at index 2.\n\n#### defaultField\n\nThe `defaultField` property can be used with the `array` or `object` type for validating all values of the container.\nIt may be an `object` or `array` containing validation rules. For example:\n\n```js\nconst descriptor = {\n  urls: {\n    type: 'array',\n    required: true,\n    defaultField: { type: 'url' },\n  },\n};\n```\n\nNote that `defaultField` is expanded to `fields`, see [deep rules](#deep-rules).\n\n#### Transform\n\nSometimes it is necessary to transform a value before validation, possibly to coerce the value or to sanitize it in some way. To do this add a `transform` function to the validation rule. The property is transformed prior to validation and returned as promise result or callback result when pass validation.\n\n```js\nimport Schema from 'async-validator';\nconst descriptor = {\n  name: {\n    type: 'string',\n    required: true,\n    pattern: /^[a-z]+$/,\n    transform(value) {\n      return value.trim();\n    },\n  },\n};\nconst validator = new Schema(descriptor);\nconst source = { name: ' user  ' };\n\nvalidator.validate(source)\n  .then((data) => assert.equal(data.name, 'user'));\n\nvalidator.validate(source,(errors, data)=>{\n  assert.equal(data.name, 'user'));\n});\n```\n\nWithout the `transform` function validation would fail due to the pattern not matching as the input contains leading and trailing whitespace, but by adding the transform function validation passes and the field value is sanitized at the same time.\n\n\n#### Messages\n\nDepending upon your application requirements, you may need i18n support or you may prefer different validation error messages.\n\nThe easiest way to achieve this is to assign a `message` to a rule:\n\n```js\n{ name: { type: 'string', required: true, message: 'Name is required' } }\n```\n\nMessage can be any type, such as jsx format.\n\n```js\n{ name: { type: 'string', required: true, message: '<b>Name is required</b>' } }\n```\n\nMessage can also be a function, e.g. if you use vue-i18n:\n```js\n{ name: { type: 'string', required: true, message: () => this.$t( 'name is required' ) } }\n```\n\nPotentially you may require the same schema validation rules for different languages, in which case duplicating the schema rules for each language does not make sense.\n\nIn this scenario you could just provide your own messages for the language and assign it to the schema:\n\n```js\nimport Schema from 'async-validator';\nconst cn = {\n  required: '%s 必填',\n};\nconst descriptor = { name: { type: 'string', required: true } };\nconst validator = new Schema(descriptor);\n// deep merge with defaultMessages\nvalidator.messages(cn);\n...\n```\n\nIf you are defining your own validation functions it is better practice to assign the message strings to a messages object and then access the messages via the `options.messages` property within the validation function.\n\n#### asyncValidator\n\nYou can customize the asynchronous validation function for the specified field:\n\n```js\nconst fields = {\n  asyncField: {\n    asyncValidator(rule, value, callback) {\n      ajax({\n        url: 'xx',\n        value: value,\n      }).then(function(data) {\n        callback();\n      }, function(error) {\n        callback(new Error(error));\n      });\n    },\n  },\n\n  promiseField: {\n    asyncValidator(rule, value) {\n      return ajax({\n        url: 'xx',\n        value: value,\n      });\n    },\n  },\n};\n```\n\n#### validator\n\nYou can custom validate function for specified field:\n\n```js\nconst fields = {\n  field: {\n    validator(rule, value, callback) {\n      return value === 'test';\n    },\n    message: 'Value is not equal to \"test\".',\n  },\n\n  field2: {\n    validator(rule, value, callback) {\n      return new Error(`${value} is not equal to 'test'.`);\n    },\n  },\n \n  arrField: {\n    validator(rule, value) {\n      return [\n        new Error('Message 1'),\n        new Error('Message 2'),\n      ];\n    },\n  },\n};\n```\n\n## FAQ\n\n### How to avoid global warning\n\n```js\nimport Schema from 'async-validator';\nSchema.warning = function(){};\n```\n\nor\n```js\nglobalThis.ASYNC_VALIDATOR_NO_WARNING = 1;\n```\n\n### How to check if it is `true`\n\nUse `enum` type passing `true` as option.\n\n```js\n{\n  type: 'enum',\n  enum: [true],\n  message: '',\n}\n```\n\n## Test Case\n\n```bash\nnpm test\n```\n\n## Coverage\n\n```bash\nnpm run coverage\n```\n\nOpen coverage/ dir\n\n## License\n\nEverything is [MIT](https://en.wikipedia.org/wiki/MIT_License).\n", "time": {"created": "2022-01-26T14:59:19.016Z", "modified": "2023-07-28T02:09:48.690Z", "4.0.7": "2021-10-18T03:57:58.917Z", "4.0.6": "2021-10-18T02:29:25.036Z", "4.0.5": "2021-10-15T11:01:22.382Z", "4.0.4": "2021-10-15T10:56:55.968Z", "4.0.3": "2021-08-31T06:59:36.610Z", "4.0.2": "2021-08-17T02:32:51.169Z", "4.0.1": "2021-08-11T03:59:50.252Z", "4.0.0": "2021-08-11T03:40:26.223Z", "3.5.2": "2021-04-30T04:11:05.677Z", "3.5.1": "2020-11-19T02:08:39.879Z", "3.5.0": "2020-11-12T09:31:57.556Z", "3.4.1": "2020-11-12T02:24:36.789Z", "3.4.0": "2020-08-05T10:59:36.018Z", "3.3.0": "2020-05-07T11:04:39.403Z", "3.2.4": "2020-03-10T08:00:01.417Z", "3.2.3": "2019-12-06T08:21:45.093Z", "3.2.2": "2019-11-11T10:11:25.241Z", "3.2.1": "2019-11-04T08:11:26.644Z", "3.2.0": "2019-10-16T03:34:00.247Z", "3.1.0": "2019-09-09T11:12:38.848Z", "3.0.4": "2019-08-22T06:40:44.708Z", "3.0.3": "2019-08-13T08:36:34.498Z", "3.0.2": "2019-08-09T05:33:51.150Z", "3.0.1": "2019-08-07T08:01:47.584Z", "2.0.1": "2019-07-31T11:22:37.640Z", "2.0.0": "2019-07-29T11:10:32.366Z", "1.12.2": "2019-07-29T11:08:31.314Z", "1.12.1": "2019-07-29T02:51:27.752Z", "1.12.0": "2019-07-26T08:55:51.652Z", "1.11.5": "2019-07-17T08:31:05.598Z", "1.11.4": "2019-07-17T08:24:55.987Z", "1.11.3": "2019-06-28T05:07:14.683Z", "1.11.2": "2019-04-23T10:37:46.891Z", "1.11.1": "2019-04-22T07:52:58.501Z", "1.11.0": "2019-03-22T07:31:38.319Z", "1.10.1": "2018-12-18T10:03:38.684Z", "1.10.0": "2018-10-17T08:24:17.553Z", "1.9.0": "2018-10-10T07:29:29.900Z", "1.8.5": "2018-07-25T12:20:27.533Z", "1.8.4": "2018-07-18T02:53:22.353Z", "1.8.2": "2017-12-08T04:02:12.014Z", "1.8.1": "2017-09-06T06:26:50.779Z", "1.8.0": "2017-08-16T07:05:10.937Z", "1.7.1": "2017-06-09T16:46:38.961Z", "1.6.9": "2017-04-24T03:21:38.985Z", "1.6.8": "2017-01-04T10:50:43.076Z", "1.6.7": "2016-12-14T03:35:31.508Z", "1.6.6": "2016-08-18T10:27:36.089Z", "1.6.5": "2016-08-15T12:18:33.431Z", "1.6.4": "2016-08-15T09:57:18.855Z", "1.6.3": "2016-07-08T06:23:44.621Z", "1.6.2": "2016-04-19T12:28:03.716Z", "1.6.1": "2016-03-30T11:56:09.706Z", "1.6.0": "2016-03-30T04:12:26.631Z", "1.5.1": "2016-03-01T10:27:06.104Z", "1.5.0": "2016-02-02T08:15:24.401Z", "1.4.2": "2016-01-21T06:58:31.476Z", "1.4.1": "2016-01-21T06:25:36.872Z", "1.4.0": "2016-01-13T06:50:13.875Z", "1.3.7": "2015-12-11T08:26:09.862Z", "1.3.5": "2015-11-20T06:15:16.125Z", "1.3.4": "2015-11-11T05:59:33.970Z", "1.3.3": "2015-09-01T08:52:11.465Z", "1.3.2": "2015-09-01T08:47:32.107Z", "1.3.1": "2015-08-27T17:23:58.553Z", "1.3.0": "2015-08-27T15:33:09.749Z", "1.2.1": "2015-08-27T06:40:41.493Z", "1.2.0": "2015-08-27T06:30:53.830Z", "1.1.6": "2015-08-06T16:27:06.914Z", "1.1.5": "2015-06-10T08:20:52.724Z", "1.1.4": "2015-06-10T04:29:20.952Z", "1.1.3": "2015-06-10T04:22:20.857Z", "1.1.2": "2015-06-10T03:58:47.828Z", "1.1.1": "2015-03-18T11:55:38.485Z", "1.1.0": "2015-03-18T11:50:35.607Z", "1.0.3": "2015-03-10T08:26:11.240Z", "1.0.1": "2015-03-10T07:44:49.031Z", "1.0.0": "2015-03-10T04:44:49.570Z", "0.0.1": "2015-03-10T03:07:57.002Z", "4.0.8": "2022-04-24T17:42:47.758Z", "4.0.9": "2022-04-25T10:33:13.654Z", "4.1.0": "2022-04-25T10:33:58.163Z", "4.1.1": "2022-04-26T01:47:28.260Z", "4.2.3": "2022-06-17T11:06:08.338Z", "4.2.4": "2022-06-17T11:27:00.396Z", "4.2.5": "2022-06-17T11:35:56.530Z"}, "versions": {"4.0.7": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.7", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.7", "_nodeVersion": "14.17.5", "_npmVersion": "7.21.0", "dist": {"shasum": "034a0fd2103a6b2ebf010da75183bec299247afe", "size": 59722, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.7.tgz", "integrity": "sha512-Pj2IR7u8hmUEDOwB++su6baaRi+QvsgajuFB9j95foM1N2gy5HM4z60hfusIO0fBPG5uLAEl6yCJr1jNSVugEQ=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.7_1634529478764_0.487837547079905"}, "_hasShrinkwrap": false, "publish_time": 1634529478917, "_cnpm_publish_time": 1634529478917, "_cnpmcore_publish_time": "2021-12-15T16:53:27.213Z"}, "4.0.6": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.6", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {"url-regex": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.6", "_nodeVersion": "14.17.5", "_npmVersion": "7.21.0", "dist": {"shasum": "292e84250f04c7690e60d34cedbf984112afb87b", "size": 58991, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.6.tgz", "integrity": "sha512-GlDzivUkhAebHWJBA7Sr0lwouTX6KzaZTWt/xEptaDSNDa8n3S7F6GuPFaXTdXWg/yBRxGdedu+Z3DlcsYesaQ=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.6_1634524164871_0.6495777130877591"}, "_hasShrinkwrap": false, "publish_time": 1634524165036, "_cnpm_publish_time": 1634524165036, "_cnpmcore_publish_time": "2021-12-15T16:53:27.459Z"}, "4.0.5": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.5", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {"url-regex": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.5", "_nodeVersion": "14.17.5", "_npmVersion": "7.21.0", "dist": {"shasum": "7e60dcebbecbe72ddc659f3972bf81181b824559", "size": 50820, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.5.tgz", "integrity": "sha512-xh1AFR/fde9XaPIRxxX4sPjX7JbNZrmiUmhh7vaicl8GdVS9N20EczB+NeCFYy545E21/xW6gBb2xPFVB0Qg0w=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.5_1634295682213_0.9585734448139962"}, "_hasShrinkwrap": false, "publish_time": 1634295682382, "_cnpm_publish_time": 1634295682382, "_cnpmcore_publish_time": "2021-12-15T16:53:27.721Z"}, "4.0.4": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.4", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {"url-regex": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.4", "_nodeVersion": "14.17.5", "_npmVersion": "7.21.0", "dist": {"shasum": "130bc7e2a8eabdb2db21be99add3bccaeebd3c04", "size": 50763, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.4.tgz", "integrity": "sha512-VHW2MkJ4VrIJgXod5eoL3DOD9RJ5hzccx/p1sYmyDsIn4Jeb9WJI6DQ/f4u8udhRXkB9AHF9cxi4U3eF8iM2Ow=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.4_1634295415772_0.8679667508616908"}, "_hasShrinkwrap": false, "publish_time": 1634295415968, "_cnpm_publish_time": 1634295415968, "_cnpmcore_publish_time": "2021-12-15T16:53:27.966Z"}, "4.0.3": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.3", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.3", "_nodeVersion": "14.17.5", "_npmVersion": "7.21.0", "dist": {"shasum": "c8ecd6b28fc8e18b14be4ec7c704641f999d377d", "size": 59539, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.3.tgz", "integrity": "sha512-LVoIbJNHPKsO7FMLamo88uxdrvayGkF3vLTMTeiN3CqAbP3qSafLRc6yx3Sq9lHkiEOLNpoA2jwwnfGDdu1SMQ=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.3_1630393176468_0.920553093709175"}, "_hasShrinkwrap": false, "publish_time": 1630393176610, "_cnpm_publish_time": 1630393176610, "_cnpmcore_publish_time": "2021-12-15T16:53:28.163Z"}, "4.0.2": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.2", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.2", "_nodeVersion": "14.15.1", "_npmVersion": "7.18.1", "dist": {"shasum": "f8089628ff8a95f7c8c58e1b8d3c3cd9de186996", "size": 59207, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.2.tgz", "integrity": "sha512-wPFnOgf9uIu/7uvptlX7PepSf2ArGt60Wng0bYrQ08eZVFG65LRLQpHKQebWEyAYtJcdPN31kndy4nS0jVnf0Q=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.2_1629167570961_0.8259523109147571"}, "_hasShrinkwrap": false, "publish_time": 1629167571169, "_cnpm_publish_time": 1629167571169, "_cnpmcore_publish_time": "2021-12-15T16:53:28.456Z"}, "4.0.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.1", "_nodeVersion": "14.15.1", "_npmVersion": "7.18.1", "dist": {"shasum": "5cf3525036efe5a74c0d4167207659829fd7b11c", "size": 59193, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.1.tgz", "integrity": "sha512-jA1y7iZeT5vm9MM4RepKRJPTxKGPOSFijdoGRXMm6kLMgj9xiXn53TYlLquLKawK+m8/5zukzL0X3HwdXR9b3A=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.1_1628654390068_0.017069870307280688"}, "_hasShrinkwrap": false, "publish_time": 1628654390252, "_cnpm_publish_time": 1628654390252, "_cnpmcore_publish_time": "2021-12-15T16:53:28.704Z"}, "4.0.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.0", "_nodeVersion": "14.15.1", "_npmVersion": "7.18.1", "dist": {"shasum": "d5868ef67c9e670decd6b77ea50a797d4c56433a", "size": 59585, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.0.tgz", "integrity": "sha512-DHTFxTI5PgDZMetB/I0edjbDyrOPMnxvEBsrON48O6W4UB3uf7F+Dz1uw6LemMLjWHSL9lfM/Rz+67AKRjl3dA=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.0_1628653226039_0.6352224288586243"}, "_hasShrinkwrap": false, "publish_time": 1628653226223, "_cnpm_publish_time": 1628653226223, "_cnpmcore_publish_time": "2021-12-15T16:53:28.920Z"}, "3.5.2": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.5.2", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.5.2", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.10", "dist": {"shasum": "68e866a96824e8b2694ff7a831c1a25c44d5e500", "size": 55480, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.5.2.tgz", "integrity": "sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.5.2_1619755865300_0.7587164268975253"}, "_hasShrinkwrap": false, "publish_time": 1619755865677, "_cnpm_publish_time": 1619755865677, "_cnpmcore_publish_time": "2021-12-15T16:53:29.209Z"}, "3.5.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.5.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.5.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "cd62b9688b2465f48420e27adb47760ab1b5559f", "size": 55457, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.5.1.tgz", "integrity": "sha512-DDmKA7sdSAJtTVeNZHrnr2yojfFaoeW8MfQN8CeuXg8DDQHTqKk9Fdv38dSvnesHoO8MUwMI2HphOeSyIF+wmQ=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.5.1_1605751719713_0.9864467335614133"}, "_hasShrinkwrap": false, "publish_time": 1605751719879, "_cnpm_publish_time": 1605751719879, "_cnpmcore_publish_time": "2021-12-15T16:53:29.515Z"}, "3.5.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.5.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.5.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "f6791ee7217cde8036941591bc3754f7c26bbf89", "size": 55427, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.5.0.tgz", "integrity": "sha512-jMDcDHrH618eznoO4/3afJG5+I4HE/ipQd7y4mhPJmCaoHCSPOJfjpWgjFoxma2h8irL+zGe+qwyptDrR37Vhg=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.5.0_1605173517412_0.9565017617142375"}, "_hasShrinkwrap": false, "publish_time": 1605173517556, "_cnpm_publish_time": 1605173517556, "_cnpmcore_publish_time": "2021-12-15T16:53:29.820Z"}, "3.4.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.4.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.4.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "ce1d72593925d55964b254a952d77a90f6e31697", "size": 55350, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.4.1.tgz", "integrity": "sha512-Nq4Bh/XniZzPCmyXEs36R7FYg9erEspNDPOjSOeX9Axz49mWZaFgBF7TrvLlVOKb5waxmjC86i05ZunfiZ/t2Q=="}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.4.1_1605147876599_0.2826711101446753"}, "_hasShrinkwrap": false, "publish_time": 1605147876789, "_cnpm_publish_time": 1605147876789, "_cnpmcore_publish_time": "2021-12-15T16:53:30.610Z"}, "3.4.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.4.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.4.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"shasum": "871b3e594124bf4c4eb7bcd1a9e78b44f3b09cae", "size": 55338, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.4.0.tgz", "integrity": "sha512-VrFk4eYiJAWKskEz115iiuCf9O0ftnMMPXrOFMqyzGH2KxO7YwncKyn/FgOOP+0MDHMfXL7gLExagCutaZGigA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.4.0_1596625175844_0.08043683457770912"}, "_hasShrinkwrap": false, "publish_time": 1596625176018, "_cnpm_publish_time": 1596625176018, "_cnpmcore_publish_time": "2021-12-15T16:53:30.846Z"}, "3.3.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.3.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.3.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "1d92193bbe60d6d6c8b246692c7005e9ed14a8ee", "size": 55440, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.3.0.tgz", "integrity": "sha512-cAHGD9EL8aCqWXjnb44q94MWiDFzUo1tMhvLb2WzcpWqGiKugsjWG9cvl+jPgkPca7asNbsBU3fa0cwkI/P+Xg=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.3.0_1588849479159_0.18827026429978733"}, "_hasShrinkwrap": false, "publish_time": 1588849479403, "_cnpm_publish_time": 1588849479403, "_cnpmcore_publish_time": "2021-12-15T16:53:31.120Z"}, "3.2.4": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.2.4", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.8.7", "@pika/pack": "^0.5.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.8.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.2.4", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "4e773a1d0d741016b455b7995b469a47cce0dbe0", "size": 53648, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.2.4.tgz", "integrity": "sha512-mTgzMJixkrh+5t2gbYoua8MLy11GHkQqFE6tbhY5Aqc4jEDGsR4BWP+sVQiYDHtzTMB8WIwI/ypObTVPcTZInw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.2.4_1583827201220_0.12577839864843843"}, "_hasShrinkwrap": false, "publish_time": 1583827201417, "_cnpm_publish_time": 1583827201417, "_cnpmcore_publish_time": "2021-12-15T16:53:31.541Z"}, "3.2.3": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.2.3", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.2.3", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "b38b72f9c08c1d28548df13bb260b6908448ca49", "size": 53122, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.2.3.tgz", "integrity": "sha512-yMJ4i3x5qEGVgEMowZiBkx+rjDrsXf64BWdHENCtHLgyPiEE+2r8jvqMF1cghCgdGo4sWVLJ7MDwPQgGSPDCcw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.2.3_1575620504851_0.6238149067197851"}, "_hasShrinkwrap": false, "publish_time": 1575620505093, "_cnpm_publish_time": 1575620505093, "_cnpmcore_publish_time": "2021-12-15T16:53:31.831Z"}, "3.2.2": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.2.2", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.2.2", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "91f6314d2dc7f03fd90940bcb577afc8ee5466ac", "size": 53141, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.2.2.tgz", "integrity": "sha512-NT5efhGkksDqODAsARaTAlkPshMgmpWw80ijM2MEr1TrDczBETaNRS3GDd1jsVRK5YSfVW10Zscab98rDYkaBA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.2.2_1573467085017_0.02955823279058367"}, "_hasShrinkwrap": false, "publish_time": 1573467085241, "_cnpm_publish_time": 1573467085241, "_cnpmcore_publish_time": "2021-12-15T16:53:32.083Z"}, "3.2.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.2.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.2.1", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "19ac8655c1296a5331b00c75f2492f0d33cae1f8", "size": 53137, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.2.1.tgz", "integrity": "sha512-yc96RhAthww0n52m9osoI1uDQbbyd/N2xwPWS1gVvngSWOsKerpBFCulvmhp8GfNwUay41TWskNTd3swQM1XMA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.2.1_1572855086463_0.6173190830135178"}, "_hasShrinkwrap": false, "publish_time": 1572855086644, "_cnpm_publish_time": 1572855086644, "_cnpmcore_publish_time": "2021-12-15T16:53:32.303Z"}, "3.2.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.2.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.2.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "fcbd644e7b5b7c9304d29a4752c3f06214ef0d56", "size": 53129, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.2.0.tgz", "integrity": "sha512-QBuW7Qrg8wbh7Wtqw1QdN162GUmXDs9gayxFaXcCOf3bCqHJ/TQep0H4I63iVk7Q3kIGWU4wbAr/C0Uj64JiMw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.2.0_1571196840080_0.6731922058681568"}, "_hasShrinkwrap": false, "publish_time": 1571196840247, "_cnpm_publish_time": 1571196840247, "_cnpmcore_publish_time": "2021-12-15T16:53:32.564Z"}, "3.1.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.1.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-build-types": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@3.1.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "447db5eb003cbb47e650f040037a29fc3881ce92", "size": 52839, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.1.0.tgz", "integrity": "sha512-XyAHGwtpx3Y3aHIOaGXXFo4tiulnrh+mXBU9INxig6Q8rtmtmBxDuCxb60j7EIGbAsQg9cxfJ2jrUZ+fIqEnBQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.1.0_1568027558734_0.27978711913300214"}, "_hasShrinkwrap": false, "publish_time": 1568027558848, "_cnpm_publish_time": 1568027558848, "_cnpmcore_publish_time": "2021-12-15T16:53:32.864Z"}, "3.0.4": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.0.4", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pika-plugin-clean-dist-src": "^0.1.1", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@3.0.4", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "515c55bafee40b1366f477a79fe678753c8828e3", "size": 51926, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.0.4.tgz", "integrity": "sha512-CIKCZ3wyJWpxN8VMGXAjD1FqZ5FnB/asHMsLM8DvFlBxgJIb88MZJc5dg4Q4P91jI9iq+YBncUhy1/SKNNTPuQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.0.4_1566456044516_0.4479341655121385"}, "_hasShrinkwrap": false, "publish_time": 1566456044708, "_cnpm_publish_time": 1566456044708, "_cnpmcore_publish_time": "2021-12-15T16:53:33.104Z"}, "3.0.3": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.0.3", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@3.0.3", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "3eb8cee8bcb8517ee55feefb848da16d48205b35", "size": 57783, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.0.3.tgz", "integrity": "sha512-jbYHtp7+CIPJPlqWvhqVwwqHr+2AJKfpYocUve8kC1CPWhin8OKFrQxlLL4cvnJwFtWkw2K+Mk35vjA5lu7rbw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.0.3_1565685394363_0.9295473664144274"}, "_hasShrinkwrap": false, "publish_time": 1565685394498, "_cnpm_publish_time": 1565685394498, "_cnpmcore_publish_time": "2021-12-15T16:53:33.377Z"}, "3.0.2": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.0.2", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.6.0", "@pika/plugin-standard-pkg": "^0.6.0", "@pika/types": "^0.6.0", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.6.0", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@3.0.2", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "cb85a448fb7e0099a8c3fa10a1a62ab73d40e6ed", "size": 60726, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.0.2.tgz", "integrity": "sha512-FDwBNYJIegEAWJ/y1hl75MuluUPFqj0Wps4RJ8tS3fyUEgKFgHmq2JEUxZmEPLLWEt/R8qxsHs58Uf0LqUD4Vw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.0.2_1565328830979_0.3679319131280123"}, "_hasShrinkwrap": false, "publish_time": 1565328831150, "_cnpm_publish_time": 1565328831150, "_cnpmcore_publish_time": "2021-12-15T16:53:33.623Z"}, "3.0.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "3.0.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/plugin-build-node": "^0.5.1", "@pika/plugin-build-web": "^0.5.1", "@pika/plugin-standard-pkg": "^0.5.1", "@pika/types": "^0.5.1", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@3.0.1", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "0b46683588918f7c9933b0076deeff2ea1872d34", "size": 23524, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-3.0.1.tgz", "integrity": "sha512-oloIa72uQRSJMOUZXuW9WjUxXa/d7jjeOW2T0zH2KjrtOir4aUVdDbNCpuNLcej9EYe7dJiH+L+o9PHJh4jM7Q=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_3.0.1_1565164907439_0.8048648254545032"}, "_hasShrinkwrap": false, "publish_time": 1565164907584, "_cnpm_publish_time": 1565164907584, "_cnpmcore_publish_time": "2021-12-15T16:53:33.845Z"}, "2.0.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "2.0.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/plugin-build-node": "^0.5.1", "@pika/plugin-build-web": "^0.5.1", "@pika/plugin-standard-pkg": "^0.5.1", "@pika/types": "^0.5.1", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "np": "^5.0.3", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@2.0.1", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "2ab03b1062b5bc727de9229aeeba4dc43f36de24", "size": 23320, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-2.0.1.tgz", "integrity": "sha512-sQkJ7Vjgvu9pGCRtDw9CIPuMFstOPtMiMoHqDI3m5xUQhHnHrn/YPBp583IYUaLZi9jW8icGjLlodH0BHSkzeg=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_2.0.1_1564572157497_0.545504934359647"}, "_hasShrinkwrap": false, "publish_time": 1564572157640, "_cnpm_publish_time": 1564572157640, "_cnpmcore_publish_time": "2021-12-15T16:53:34.068Z"}, "2.0.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "2.0.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/plugin-build-node": "^0.5.1", "@pika/plugin-build-web": "^0.5.1", "@pika/plugin-standard-pkg": "^0.5.1", "@pika/types": "^0.5.1", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@2.0.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "ed977501c49ea76a8c91b9dfa3959540387bc53a", "size": 23309, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-2.0.0.tgz", "integrity": "sha512-YhPgfndEuZPQQIRSIzeiyxWzpCbAirXmEA8BkzQ3nm0KWqsX4L/KsrvQqhzYVznHMJWbXOWYWj7AeEnGE5kuEQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_2.0.0_1564398632128_0.14208559174411572"}, "_hasShrinkwrap": false, "publish_time": 1564398632366, "_cnpm_publish_time": 1564398632366, "_cnpmcore_publish_time": "2021-12-15T16:53:34.278Z"}, "1.12.2": {"name": "async-validator", "version": "1.12.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "_id": "async-validator@1.12.2", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "beae671e7174d2938b7b4b69d2fb7e722b7fd72c", "size": 22289, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.12.2.tgz", "integrity": "sha512-57EETfCPFiB7M4QscvQzWSGNsmtkjjzZv318SK1CBlstk+hycV72ocjriMOOM48HjvmoAoJGpJNjC7Z76RlnZA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.12.2_1564398511155_0.6644476887128656"}, "_hasShrinkwrap": false, "publish_time": 1564398511314, "_cnpm_publish_time": 1564398511314, "_cnpmcore_publish_time": "2021-12-15T16:53:34.492Z"}, "1.12.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "1.12.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.5.0", "@pika/plugin-build-node": "^0.5.1", "@pika/plugin-build-web": "^0.5.1", "@pika/plugin-standard-pkg": "^0.5.1", "@pika/types": "^0.5.1", "babel-jest": "^24.8.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@1.12.1", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "d987bf2dee74766e48d6e199d5e30e294ab2f7ba", "size": 23311, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.12.1.tgz", "integrity": "sha512-dWOdw3BJZZ9uhfEQ/IguCPz96+FJuKp+Zd4Dnpfk600CoounSOv2Wp142oaL+d10uWdfsV39TSWDl+JjYE62Sw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.12.1_1564368687620_0.8696582438852021"}, "_hasShrinkwrap": false, "publish_time": 1564368687752, "_cnpm_publish_time": 1564368687752, "_cnpmcore_publish_time": "2021-12-15T16:53:34.707Z"}, "1.12.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "1.12.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/runtime": "^7.5.5", "@pika/plugin-build-node": "^0.5.1", "@pika/plugin-build-types": "^0.5.1", "@pika/plugin-build-web": "^0.5.1", "@pika/plugin-standard-pkg": "^0.5.1", "@pika/types": "^0.5.1", "babel-jest": "^24.8.0", "babel-plugin-module-resolver": "^3.2.0", "babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^24.8.0", "lint-staged": "^7.2.0", "pre-commit": "^1.2.2", "prettier": "^1.11.1"}, "esnext": "dist-src/index.js", "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "_id": "async-validator@1.12.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "ff3c5bc74a1607fdae551f1781220f299b469cc8", "size": 25191, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.12.0.tgz", "integrity": "sha512-obbl0gHT3m7tVqtsTtGmPwKTJ5samGs98Wu1z8e0/q0C2q95PKKy/VNR8OL9Gbe4Mf0EMaW4m9aWh88lp30aSQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.12.0_1564131351465_0.048400443825049155"}, "_hasShrinkwrap": false, "publish_time": 1564131351652, "_cnpm_publish_time": 1564131351652, "_cnpmcore_publish_time": "2021-12-15T16:53:34.961Z"}, "1.11.5": {"name": "async-validator", "version": "1.11.5", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "gitHead": "61fb066b84bd2cb466e01f1937b4940109e139ae", "_id": "async-validator@1.11.5", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "9d43cf49ef6bb76be5442388d19fb9a6e47597ea", "size": 22287, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.5.tgz", "integrity": "sha512-XNtCsMAeAH1pdLMEg1z8/Bb3a8cdCbui9QbJATRFHHHW5kT6+NPI3zSVQUXgikTFITzsg+kYY5NTWhM2Orwt9w=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.11.5_1563352265417_0.8407595470538207"}, "_hasShrinkwrap": false, "publish_time": 1563352265598, "_cnpm_publish_time": 1563352265598, "_cnpmcore_publish_time": "2021-12-15T16:53:35.148Z"}, "1.11.4": {"name": "async-validator", "version": "1.11.4", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "71bb931c41ed16220a75980de79bdde39d653bbb", "_id": "async-validator@1.11.4", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "8ce1ef82fc3a25a5846f716dd6faf809a34de156", "size": 22369, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.4.tgz", "integrity": "sha512-8D47Q95iMYFsMASAhxfIlyidlHGC9Upm1bj3YZky8vcpMexaBCm0Iige5kKkG1kZKY6A+4O4fJiNg73mRSxw1g=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.11.4_1563351895826_0.5041425024804946"}, "_hasShrinkwrap": false, "publish_time": 1563351895987, "_cnpm_publish_time": 1563351895987, "_cnpmcore_publish_time": "2021-12-15T16:53:35.360Z"}, "1.11.3": {"name": "async-validator", "version": "1.11.3", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "e5dba0e927b580d079f88f5ce3b77396bf703e31", "_id": "async-validator@1.11.3", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "23703b19740721d88edbcb6310f6d745da9ec109", "size": 22376, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.3.tgz", "integrity": "sha512-<PERSON>eyt+fpqTSYeC++J/M/KkBq8UEGiAkjjKTirKhvkR9M9q+iZNCsv6ffVWNySllAuNPZ+SqzKMgBuvWHILjHatg=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.11.3_1561698434475_0.09627001382328437"}, "_hasShrinkwrap": false, "publish_time": 1561698434683, "_cnpm_publish_time": 1561698434683, "_cnpmcore_publish_time": "2021-12-15T16:53:35.582Z"}, "1.11.2": {"name": "async-validator", "version": "1.11.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "08d67b8300153783fe61d542119baa84e51b4407", "_id": "async-validator@1.11.2", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "ca5e2ba31c15e9319cb2d873c2b137b67a198ead", "size": 22299, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.2.tgz", "integrity": "sha512-K<PERSON>bL4jU6ZRJA82D69oOCoyKqr99emuKNvG0axH3XpGZh0xy1wDHpdH4cGzvwJ9nXAM0j7k/waBId16CQVI8dlw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.11.2_1556015866690_0.757126733441781"}, "_hasShrinkwrap": false, "publish_time": 1556015866891, "_cnpm_publish_time": 1556015866891, "_cnpmcore_publish_time": "2021-12-15T16:53:35.772Z"}, "1.11.1": {"name": "async-validator", "version": "1.11.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "49ceefeab19cab3e7967666fa7d2b366fece38b7", "_id": "async-validator@1.11.1", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "946a68b9f042718a9b601afed6a0aab687d83812", "size": 22224, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.1.tgz", "integrity": "sha512-ODR9kD5CzCHs8Ta1VM0EKfnH6fIx+Aim3QwBg4p3jECQ+AgTnnDJMcfbkGSDLy2bD4kJh5GS3N6l8sLNn8hQEg=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.11.1_1555919578342_0.8342422428416996"}, "_hasShrinkwrap": false, "publish_time": 1555919578501, "_cnpm_publish_time": 1555919578501, "_cnpmcore_publish_time": "2021-12-15T16:53:35.990Z"}, "1.11.0": {"name": "async-validator", "version": "1.11.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "coveralls": "^2.13.1", "jest": "^23.6.0", "pre-commit": "1.x", "rc-tools": "7.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "8b420ef0b6463be6530409ea71f122817b0ea32e", "_id": "async-validator@1.11.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.7.0", "dist": {"shasum": "f62fd14bcca336fcda96e6dd53dbd1ddd3fc3198", "size": 22144, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.11.0.tgz", "integrity": "sha512-3ROlrxLee6KB1aEj9eENBLB0pPCPCb+hc+aGify63HBHptq5FnEFNAQOAkhbx7xBlVh5XXi2w+QEecWpOdigpw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.11.0_1553239898208_0.49900271285413345"}, "_hasShrinkwrap": false, "publish_time": 1553239898319, "_cnpm_publish_time": 1553239898319, "_cnpmcore_publish_time": "2021-12-15T16:53:36.191Z"}, "1.10.1": {"name": "async-validator", "version": "1.10.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "c262a2b284929ece0939596a23d6b2fc65e35d95", "_id": "async-validator@1.10.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "41e9b0b9f8e719e6edf946372f018a958c2e70f3", "size": 20376, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.10.1.tgz", "integrity": "sha512-VLiLKZuJc8VIeAMC3YobVsZov8XPNhbwyIkKjhPW5cFnhZXH+HHJpkE270YMD/6zJIOJXUN/Cq0t3fR7XPwaDQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.10.1_1545127418556_0.7547116648709378"}, "_hasShrinkwrap": false, "publish_time": 1545127418684, "_cnpm_publish_time": 1545127418684, "_cnpmcore_publish_time": "2021-12-15T16:53:36.394Z"}, "1.10.0": {"name": "async-validator", "version": "1.10.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "def5a89bed084bbbb76c0222f1592acb6db9822f", "_id": "async-validator@1.10.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "59a392a1b48565c2eb43faddb4e54d7d86dce293", "size": 20312, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.10.0.tgz", "integrity": "sha512-tjkUJ3OXURZbm1nrlU2QtH0XJe4YvhN1J9AYiKFN9ODBqt0AFIE6YZdZByrWG2SidPUOOK5KIAsqskqFj/43ZQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.10.0_1539764657378_0.7558484388747573"}, "_hasShrinkwrap": false, "publish_time": 1539764657553, "_cnpm_publish_time": 1539764657553, "_cnpmcore_publish_time": "2021-12-15T16:53:36.628Z"}, "1.9.0": {"name": "async-validator", "version": "1.9.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "e7deb94a4557a533dd973e92d8afc1c4b4ecf8f9", "_id": "async-validator@1.9.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2dac22f43157b7fd4296ff6d7ad6429718fbd4d4", "size": 20695, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.9.0.tgz", "integrity": "sha512-3iNZJ2b1K77Qcdt/JHCpxz/RW7HCmXjGkdbST6sTudbQZREvq3+IGF5+4kLQrHZd1+XlN/D6qQCVJZgZMzDuAA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.9.0_1539156569709_0.8481553580179615"}, "_hasShrinkwrap": false, "publish_time": 1539156569900, "_cnpm_publish_time": 1539156569900, "_cnpmcore_publish_time": "2021-12-15T16:53:36.880Z"}, "1.8.5": {"name": "async-validator", "version": "1.8.5", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "8bc241c968546d5e229dae1f95e4c2ad81f3f32e", "_id": "async-validator@1.8.5", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dc3e08ec1fd0dddb67e60842f02c0cd1cec6d7f0", "size": 20282, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.8.5.tgz", "integrity": "sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.8.5_1532521227433_0.29327594872295415"}, "_hasShrinkwrap": false, "publish_time": 1532521227533, "_cnpm_publish_time": 1532521227533, "_cnpmcore_publish_time": "2021-12-15T16:53:37.074Z"}, "1.8.4": {"name": "async-validator", "version": "1.8.4", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "062470d0ed1fef6aa6de4c343e92e9ce329f06fc", "_id": "async-validator@1.8.4", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "74c3a324161e6207f5c56cc1c66725b5c597e506", "size": 20193, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.8.4.tgz", "integrity": "sha512-9M6Q6Q3iqFKSdyhliLG8gUH9E73p/TQU1XNH/qiybX5eFIgwB++IIZ/wcPM1f+x9WeqemxGkm0CCx69Mkx3EEg=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_1.8.4_1531882402194_0.27350797057749254"}, "_hasShrinkwrap": false, "publish_time": 1531882402353, "_cnpm_publish_time": 1531882402353, "_cnpmcore_publish_time": "2021-12-15T16:53:37.289Z"}, "1.8.2": {"name": "async-validator", "version": "1.8.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "e782748f0345b462d84e96a582c0dd38db2de666", "_id": "async-validator@1.8.2", "_shasum": "b77597226e96242f8d531c0d46ae295f62422ba4", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b77597226e96242f8d531c0d46ae295f62422ba4", "size": 19694, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.8.2.tgz", "integrity": "sha512-o2Y8fHzLml/2nENmgKHN318VrZ3gjdiUQMFNug5Esl3IcOc/3ODm7467SwwhlZMEVE72s3o4/CUxsGb5BZRGRQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator-1.8.2.tgz_1512705731548_0.5838209073990583"}, "directories": {}, "publish_time": 1512705732014, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512705732014, "_cnpmcore_publish_time": "2021-12-15T16:53:37.504Z"}, "1.8.1": {"name": "async-validator", "version": "1.8.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "935d4b595da6d156d9fd3f8af91b771660c24107", "_id": "async-validator@1.8.1", "_shasum": "6665788ca39269af770e5ee02f0e557f2438d2ca", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6665788ca39269af770e5ee02f0e557f2438d2ca", "size": 19458, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.8.1.tgz", "integrity": "sha512-gTLONCqmaAOI/hE8yhyJ3q0f9Oy9FhuH8hCjFxM4pAnVCwn87TJfjWa1mcrUGg2yi9JGhNIlNNVnKRJypFzA+A=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator-1.8.1.tgz_1504679210687_0.21227537072263658"}, "directories": {}, "publish_time": 1504679210779, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504679210779, "_cnpmcore_publish_time": "2021-12-15T16:53:37.830Z"}, "1.8.0": {"name": "async-validator", "version": "1.8.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "c26eb70150723d5a7f8dff6c99808ec77397e7d5", "_id": "async-validator@1.8.0", "_shasum": "22775e9b4f48f726472e60823281502161b6b143", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "22775e9b4f48f726472e60823281502161b6b143", "size": 19438, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.8.0.tgz", "integrity": "sha512-h93nrRDAMSwxC4JN4iOswW6gRCdEeKVo71d0UPh6s+T2cLNmLt9YowbjQYXP9xQTNT6qgmPjea7ZL9PIaMUeSA=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator-1.8.0.tgz_1502867110673_0.12054537632502615"}, "directories": {}, "publish_time": 1502867110937, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502867110937, "_cnpmcore_publish_time": "2021-12-15T16:53:38.022Z"}, "1.7.1": {"name": "async-validator", "version": "1.7.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib", "es"], "main": "./lib/index", "module": "./es/index", "jest": {"collectCoverageFrom": ["src/*"], "transform": {"\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub --babel-runtime", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"jest": "20.x", "pre-commit": "1.x", "rc-tools": "6.x"}, "pre-commit": ["lint"], "dependencies": {"babel-runtime": "6.x"}, "gitHead": "b5994294534fdc60715b7f38e89c93b8d4ddea8f", "_id": "async-validator@1.7.1", "_shasum": "89d3d7a384ca5d05e0f07bf51754d591e2cfec61", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "89d3d7a384ca5d05e0f07bf51754d591e2cfec61", "size": 19319, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.7.1.tgz", "integrity": "sha512-6hFNu0uiGcHhBYWxLrRqgiCmTH5W7kj59gG55rzvhXdvKIvPtdGEPkmDDkYEoyxQIx1Zg1JPhwWOpWZ65C+ACw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator-1.7.1.tgz_1497026798798_0.656300273258239"}, "directories": {}, "publish_time": 1497026798961, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497026798961, "_cnpmcore_publish_time": "2021-12-15T16:53:38.262Z"}, "1.6.9": {"name": "async-validator", "version": "1.6.9", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "7b4ee0aa361fcd1167c1c87b2d4b9207982cb0e4", "_id": "async-validator@1.6.9", "_shasum": "a8309daa8b83421cdbd4628e026d6abb25192d34", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a8309daa8b83421cdbd4628e026d6abb25192d34", "size": 12489, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.9.tgz", "integrity": "sha512-g1V4UwD7er75tfyC3MhfeQ214tdXBto+lDwPgSOvkaLJIIS1cQZlthAmm1iNKFDVqj7p/615xMzrCfqxXj7KpQ=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.9.tgz_1493004096890_0.22322611324489117"}, "directories": {}, "publish_time": 1493004098985, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493004098985, "_cnpmcore_publish_time": "2021-12-15T16:53:38.477Z"}, "1.6.8": {"name": "async-validator", "version": "1.6.8", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "4c02e015c89dd54fefa80ff804d9d4b06be31028", "_id": "async-validator@1.6.8", "_shasum": "fbaaa9002b41066fdf3ba21d8a4ca8b1179ad36b", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fbaaa9002b41066fdf3ba21d8a4ca8b1179ad36b", "size": 12399, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.8.tgz", "integrity": "sha512-9bgpG28pLspU0uRSr6F/8wIde1xZ2iq3x26zaZ4m4RnTYW6a+PvmA4uKCMuqBWCcQPqovHSmUQohfwgBybHGaw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.8.tgz_1483527041140_0.6837994067464024"}, "directories": {}, "publish_time": 1483527043076, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483527043076, "_cnpmcore_publish_time": "2021-12-15T16:53:38.689Z"}, "1.6.7": {"name": "async-validator", "version": "1.6.7", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "e8c5d43f854421ff5bff2a8041f63795cd2563a5", "_id": "async-validator@1.6.7", "_shasum": "345162bdb54f3d653f07cd1ac076bbb517e53a2e", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "345162bdb54f3d653f07cd1ac076bbb517e53a2e", "size": 11687, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.7.tgz", "integrity": "sha512-evrNvd2Stp7yZ3mj0VrotcKzHRqTSZvd4d+/txHJKA14XaoN3v2AMRncYP6pgnrVJ0fGBhKy1aneJe+Sw68KZw=="}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.7.tgz_1481686530787_0.9550799627322704"}, "directories": {}, "publish_time": 1481686531508, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481686531508, "_cnpmcore_publish_time": "2021-12-15T16:53:38.958Z"}, "1.6.6": {"name": "async-validator", "version": "1.6.6", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "95f0d39ee3f2cf16d71dfe74fce8747695b19105", "_id": "async-validator@1.6.6", "_shasum": "12e895ffaaadd5b9a8faf8be1b07cdd930674fa2", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "12e895ffaaadd5b9a8faf8be1b07cdd930674fa2", "size": 11651, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.6.tgz", "integrity": "sha512-qwkgPp9ee5//br6Ibg0H03c++JxnR0LWp38DXU8G1F0u4xmS/AFjGuwl+8qiQcKeHmMtc43JDVMy8brFReK6gg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.6.tgz_1471516055121_0.5374817268457264"}, "directories": {}, "publish_time": 1471516056089, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471516056089, "_cnpmcore_publish_time": "2021-12-15T16:53:39.164Z"}, "1.6.5": {"name": "async-validator", "version": "1.6.5", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "6fc2367153781ec22511e894b6e7daa64db582b8", "_id": "async-validator@1.6.5", "_shasum": "92c3495bd90c47a9dd5583c357c186ffa721dd42", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "92c3495bd90c47a9dd5583c357c186ffa721dd42", "size": 11648, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.5.tgz", "integrity": "sha512-sZare1FDh9PKQbT4qSBxnha2yI+6FXz0KRWgYqnfH7kMgL6yAPIHUA9aQIQQHOSpp0iviWnwS3Xa3ViVfacELg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.5.tgz_1471263511600_0.9974322440102696"}, "directories": {}, "publish_time": 1471263513431, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471263513431, "_cnpmcore_publish_time": "2021-12-15T16:53:39.386Z"}, "1.6.4": {"name": "async-validator", "version": "1.6.4", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "8c1de764eccfc77e00ed54c8a29550c73ca77854", "_id": "async-validator@1.6.4", "_shasum": "d45747cee16f4e91b8934ce4351c8a00aeeff85c", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d45747cee16f4e91b8934ce4351c8a00aeeff85c", "size": 11528, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.4.tgz", "integrity": "sha512-9tNw3dMhV01tr2Wjcr7G1KGqZqSsJtavrlNDMJD3RWUuqZRT3W8cZhBT/6RB84nm4necXOA5E4HWYaa8Ws70Xw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.4.tgz_1471255037037_0.21241059806197882"}, "directories": {}, "publish_time": 1471255038855, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471255038855, "_cnpmcore_publish_time": "2021-12-15T16:53:39.606Z"}, "1.6.3": {"name": "async-validator", "version": "1.6.3", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "gitHead": "9cfd4606e26aefe63b3143a5a02cc8f6669ee151", "_id": "async-validator@1.6.3", "_shasum": "29daab543dc4b2555230a37695fae000a56d6804", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "29daab543dc4b2555230a37695fae000a56d6804", "size": 11528, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.3.tgz", "integrity": "sha512-/pkJ0fJZhwZVKB0l5+YMzSYhmOX1O/9ZERWfM4xp4soAyZGp5mRQWWZCKtg1kq5hi3kaqwAKGy4qQTCWGsBjZg=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.3.tgz_1467959022187_0.15846293652430177"}, "directories": {}, "publish_time": 1467959024621, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467959024621, "_cnpmcore_publish_time": "2021-12-15T16:53:39.890Z"}, "1.6.2": {"name": "async-validator", "version": "1.6.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "dependencies": {"lodash.mergewith": "4.x"}, "gitHead": "029fadf8c0b24c82db3d643ee618589c467c57cb", "_id": "async-validator@1.6.2", "_shasum": "e1168f48fcffaca6ec013e6eb664f28e795bf6d7", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e1168f48fcffaca6ec013e6eb664f28e795bf6d7", "size": 11507, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.2.tgz", "integrity": "sha512-s8EUNhJtbKYJlzc4WAFe5Mep8JFon1UI6Ru7nCn/gAy57QvDc8GjQgfCIIB+FNY/RnJL4BnFZLV2emjxtBPq5A=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.2.tgz_1461068881225_0.8974000504240394"}, "directories": {}, "publish_time": 1461068883716, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461068883716, "_cnpmcore_publish_time": "2021-12-15T16:53:40.138Z"}, "1.6.1": {"name": "async-validator", "version": "1.6.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "dependencies": {"lodash.mergewith": "4.x"}, "gitHead": "99e6247de3c04700804556bdd3935dc39a3b8315", "_id": "async-validator@1.6.1", "_shasum": "54b4887efea359b02a72ea289db47a020d78123e", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "54b4887efea359b02a72ea289db47a020d78123e", "size": 11371, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.1.tgz", "integrity": "sha512-6L00jlUsiiriuYzJ53keBYvZf3mY8a/OP54hlhHVcDV+8YcUSaOSDuRQm75PXSUaitHR0jKvWmBpgWpmnYyYow=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.1.tgz_1459338967375_0.5495510927867144"}, "directories": {}, "publish_time": 1459338969706, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459338969706, "_cnpmcore_publish_time": "2021-12-15T16:53:40.470Z"}, "1.6.0": {"name": "async-validator", "version": "1.6.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "test": "rc-tools run test", "chrome-test": "rc-tools run chrome-test", "coverage": "rc-tools run coverage"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-tools": "5.x"}, "pre-commit": ["lint"], "dependencies": {"lodash.mergewith": "4.x"}, "gitHead": "bd12e5af99ce6b3e141c2bf944042ff390916b05", "_id": "async-validator@1.6.0", "_shasum": "73b205dbbfe9ead73e87b0d3e7ca4be3f01bc3c1", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "73b205dbbfe9ead73e87b0d3e7ca4be3f01bc3c1", "size": 11191, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.6.0.tgz", "integrity": "sha512-xDZdIYWS3qz3mX1llHdQ+fRv4kSmph7r3BpgmzP5IqFPix7lSKrSra0GR0Cu0y9rImIxqkXbT5kx/vpV0xgP8g=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/async-validator-1.6.0.tgz_1459311146196_0.6425307986792177"}, "directories": {}, "publish_time": 1459311146631, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459311146631, "_cnpmcore_publish_time": "2021-12-15T16:53:40.678Z"}, "1.5.1": {"name": "async-validator", "version": "1.5.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "dependencies": {"lodash.mergewith": "^4.0.3"}, "gitHead": "59827b6ac12e40f3c3e89ab5cd80e5ade55c3567", "_id": "async-validator@1.5.1", "_shasum": "748dd26ac62ba3a55a8f49c73c15dee7a5db023d", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "748dd26ac62ba3a55a8f49c73c15dee7a5db023d", "size": 10760, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.5.1.tgz", "integrity": "sha512-IEO/Ztuwdu2wFfL2h8ZYeOea4nIZycdxw07x2KIysTEfoos7CTpr0TP4JeDU6iTiGhW6+pPQcmn9w8cAkDrgcA=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/async-validator-1.5.1.tgz_1456828024125_0.2078237673267722"}, "directories": {}, "publish_time": 1456828026104, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456828026104, "_cnpmcore_publish_time": "2021-12-15T16:53:40.916Z"}, "1.5.0": {"name": "async-validator", "version": "1.5.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "dependencies": {"lodash.mergewith": "^4.0.3"}, "gitHead": "ba076c091d9665b34839527271decd1576927f3f", "_id": "async-validator@1.5.0", "_shasum": "ec24bfbbfc87886efffce9a619468306266723c4", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ec24bfbbfc87886efffce9a619468306266723c4", "size": 10752, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.5.0.tgz", "integrity": "sha512-XS1P7assa5Cx9PMkw3fqKYWfp06ezyW9vjoXl/E7uPn5dPnvfN5F66QqkiU1y0BjeFa/99EtYRtk6d3mEKLbgA=="}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/async-validator-1.5.0.tgz_1454400923713_0.8684745586942881"}, "directories": {}, "publish_time": 1454400924401, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454400924401, "_cnpmcore_publish_time": "2021-12-15T16:53:41.122Z"}, "1.4.2": {"name": "async-validator", "version": "1.4.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "gitHead": "f5ba4274b6d5daf1c4491c3bb518f2a331699816", "_id": "async-validator@1.4.2", "_shasum": "3b40e475bd9768df493ad37b70902760d5f73451", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3b40e475bd9768df493ad37b70902760d5f73451", "size": 10527, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.4.2.tgz", "integrity": "sha512-U2XgtsQgUXFWkQClYiWIqxODU6Z4mcNE+eHl1mGblwWa0od/YOUpEztsh6y/tNe7aydtnIie5vmlXY2jg6sjYQ=="}, "directories": {}, "publish_time": 1453359511476, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453359511476, "_cnpmcore_publish_time": "2021-12-15T16:53:41.324Z"}, "1.4.1": {"name": "async-validator", "version": "1.4.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "gitHead": "4f4d5e8320177869f7b596d66674ea4478e75e43", "_id": "async-validator@1.4.1", "_shasum": "2e3658895716fe8c33b346657b859ce3b5f97b12", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2e3658895716fe8c33b346657b859ce3b5f97b12", "size": 10465, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.4.1.tgz", "integrity": "sha512-JRgRqZ+IVzkxGD2NweEna/xTvOHI7syfbckm7r5s5NJJrYxMnS/9dwj7j3a0YoxnY316rvdHHj3Sfnz4a/SdDA=="}, "directories": {}, "publish_time": 1453357536872, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453357536872, "_cnpmcore_publish_time": "2021-12-15T16:53:41.564Z"}, "1.4.0": {"name": "async-validator", "version": "1.4.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "gitHead": "17c04ef0d462a1ed0289d69c2e118bbfdbdbd162", "_id": "async-validator@1.4.0", "_shasum": "ebd1926fe2272e4ca3b665734066ec8758ace505", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ebd1926fe2272e4ca3b665734066ec8758ace505", "size": 10450, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.4.0.tgz", "integrity": "sha512-HvssFbDn5son7VxK/InANUFkkkeQXAbPY6gF9JggzbkrrTH3zvdUfD2llnW59n1nrzM+z6eZmQfAfLrXQ9r37A=="}, "directories": {}, "publish_time": 1452667813875, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452667813875, "_cnpmcore_publish_time": "2021-12-15T16:53:41.803Z"}, "1.3.7": {"name": "async-validator", "version": "1.3.7", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "gitHead": "2760850ed536573f4ec3065d45b152e00322cabe", "_id": "async-validator@1.3.7", "_shasum": "316d082b978ce1bffac943c92f96e0be0048a3d4", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "316d082b978ce1bffac943c92f96e0be0048a3d4", "size": 10078, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.7.tgz", "integrity": "sha512-h1K<PERSON>Zum4sw9SsFISxgCs9Q9OK94/tPKg5/5EAD9+ZxH5G9LafUKsXZXb0izY9+Wc3uen7zC+GyVvDoKH5/DCg=="}, "directories": {}, "publish_time": 1449822369862, "_hasShrinkwrap": false, "_cnpm_publish_time": 1449822369862, "_cnpmcore_publish_time": "2021-12-15T16:53:42.049Z"}, "1.3.5": {"name": "async-validator", "version": "1.3.5", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "gitHead": "facfc55d12ef20b2d483ba3a769d6d0f4c458141", "_id": "async-validator@1.3.5", "_shasum": "5c57edd4d5fbef611126e18a986c8aeb79771dd0", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5c57edd4d5fbef611126e18a986c8aeb79771dd0", "size": 9940, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.5.tgz", "integrity": "sha512-ZZ3mcXJwqQbyt/SxEn5Rc7mB/iBledi/ep4qPUpVEZTlEY9rIJE9iy4Nr6/L9UYXMVsfGVFw0ZEiMWUIqsdZ0Q=="}, "directories": {}, "publish_time": 1448000116125, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448000116125, "_cnpmcore_publish_time": "2021-12-15T16:53:42.303Z"}, "1.3.4": {"name": "async-validator", "version": "1.3.4", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "pre-commit": "1.x", "rc-server": "3.x", "rc-tools": "4.x"}, "pre-commit": ["lint"], "gitHead": "a0d95675ee50956fe4f9e7a4c9a5a42551c28efc", "_id": "async-validator@1.3.4", "_shasum": "9e0cbbdaaa5346949aa4ec24990da2d62816f90d", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9e0cbbdaaa5346949aa4ec24990da2d62816f90d", "size": 9908, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.4.tgz", "integrity": "sha512-C90hQFX4VYNlSLqUSFr45r69XGDtKTE+E1QZ1MWXgSr7x4MXZbsOVMoRVX3/NY0dSevFo0Fl88LK85gQWAG2og=="}, "directories": {}, "publish_time": 1447221573970, "_hasShrinkwrap": false, "_cnpm_publish_time": 1447221573970, "_cnpmcore_publish_time": "2021-12-15T16:53:42.498Z"}, "1.3.3": {"name": "async-validator", "version": "1.3.3", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "node --harmony node_modules/.bin/rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "4.x", "react": "~0.13.3"}, "precommit": ["lint"], "gitHead": "2dcf7d54fea46e9d3429c828c1185eb26d581a9c", "_id": "async-validator@1.3.3", "_shasum": "32d44c7b8c3b978dc678ef78f736fadf11d39049", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "32d44c7b8c3b978dc678ef78f736fadf11d39049", "size": 9953, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.3.tgz", "integrity": "sha512-xnz0Zvcjjc42vjJPMfFpRzf04r3Cqk6qBIw5asYtwiQGR05ZYTo8fz9blGHoCGTTxmjLiYuZoovGBeemc/3IBA=="}, "directories": {}, "publish_time": 1441097531465, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441097531465, "_cnpmcore_publish_time": "2021-12-15T16:53:42.732Z"}, "1.3.2": {"name": "async-validator", "version": "1.3.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "node --harmony node_modules/.bin/rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "4.x", "react": "~0.13.3"}, "precommit": ["lint"], "gitHead": "cdc49ea226003e5c602d89e8c34af2655c61b1bb", "_id": "async-validator@1.3.2", "_shasum": "4062c77ac4fe062482e8c51e75ce6e5b99a6e4d6", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4062c77ac4fe062482e8c51e75ce6e5b99a6e4d6", "size": 4687, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.2.tgz", "integrity": "sha512-mAS28Hv4kf8Luo8ygK5vfQpLLJYaYH9CFeUb9FX+9tehj9OYqdAfXP+olOHE7FR9RksobxYi9MLK6U1nmDPgKA=="}, "directories": {}, "publish_time": 1441097252107, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441097252107, "_cnpmcore_publish_time": "2021-12-15T16:53:42.992Z"}, "1.3.1": {"name": "async-validator", "version": "1.3.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "node --harmony node_modules/.bin/rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "4.x", "react": "~0.13.3"}, "precommit": ["lint"], "gitHead": "23f670e88cafabc84fdcff77f1327a7272040259", "_id": "async-validator@1.3.1", "_shasum": "bc334219154cef5a57fabe845ffcb2059d6b9884", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bc334219154cef5a57fabe845ffcb2059d6b9884", "size": 9911, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.1.tgz", "integrity": "sha512-dtcU64t8EHu49aQoGahSxlL/qfvVjejcNQrPaulMg1NpPL1u6SxQI1t+x8zKu6VHWzZLzKwLplDf98Q+df7AxA=="}, "directories": {}, "publish_time": 1440696238553, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440696238553, "_cnpmcore_publish_time": "2021-12-15T16:53:43.201Z"}, "1.3.0": {"name": "async-validator", "version": "1.3.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "files": ["lib"], "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "node --harmony node_modules/.bin/rc-server", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "karma": "rc-tools run karma", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "4.x", "react": "~0.13.3"}, "precommit": ["lint"], "gitHead": "e10e15811a338f5c5cce1500873fc2209e2a83c0", "_id": "async-validator@1.3.0", "_shasum": "3dece6d0998011fc8ecc2eba39047061b0b81807", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3dece6d0998011fc8ecc2eba39047061b0b81807", "size": 9889, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.3.0.tgz", "integrity": "sha512-NxVbooUlM9uGBZp04IRx9ZUeDJXDX9qeoCnB525gFhGF2eyALtAIU9e6YKYDcj2/ADzAW1kQ5xVWBG1GgyObtQ=="}, "directories": {}, "publish_time": 1440689589749, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440689589749, "_cnpmcore_publish_time": "2021-12-15T16:53:43.408Z"}, "1.2.1": {"name": "async-validator", "version": "1.2.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "2424bd9f69627784c744b16666373960de2535f7", "_id": "async-validator@1.2.1", "_shasum": "560d84f598ddca6bb8c2c42ee73bde6aa158cdf3", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "560d84f598ddca6bb8c2c42ee73bde6aa158cdf3", "size": 9604, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.2.1.tgz", "integrity": "sha512-djL1GkxeniTHJvgKvofqh/57PmuAMEQTCLY7sBkywQ3DtJZnfF2/qPE4YGpvzHpKIrOSecHn0D84VWMl8QrBrA=="}, "directories": {}, "publish_time": 1440657641493, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440657641493, "_cnpmcore_publish_time": "2021-12-15T16:53:43.641Z"}, "1.2.0": {"name": "async-validator", "version": "1.2.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "4f2bb74cb0e116878729cb146f88945c031cfdd9", "_id": "async-validator@1.2.0", "_shasum": "e217600b47ed935d1350863447779dc665eea93c", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e217600b47ed935d1350863447779dc665eea93c", "size": 9596, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.2.0.tgz", "integrity": "sha512-MV3crLUQU4CqV+JyB6WpU7z1FnB2Ol3vL2f/wLqG5vcBYJVEhPmPkQrMnjKFFBmuwt3aP+8Rh9edURH1rnL5iw=="}, "directories": {}, "publish_time": 1440657053830, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440657053830, "_cnpmcore_publish_time": "2021-12-15T16:53:43.864Z"}, "1.1.6": {"name": "async-validator", "version": "1.1.6", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "67b9e6fc32256597b008e4dfc7b2438f7c9d65cb", "_id": "async-validator@1.1.6", "_shasum": "039f65c1ab552e751f17512ad39ba83d11939c4b", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "039f65c1ab552e751f17512ad39ba83d11939c4b", "size": 9510, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.6.tgz", "integrity": "sha512-LpDHu5PMGUMdaFO8di8r/ZCcTDtVIzjmYAT4C8RpzCSeL/hjHx+r4KYPEHE98fa90gd81hIirGDyWMWe9tdbhw=="}, "directories": {}, "publish_time": 1438878426914, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438878426914, "_cnpmcore_publish_time": "2021-12-15T16:53:44.081Z"}, "1.1.5": {"name": "async-validator", "version": "1.1.5", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "89d5a9548270545b79dcd66c6cd55884ef2c18b9", "_id": "async-validator@1.1.5", "_shasum": "af1888eae7fcc4ad1c742084fda9e6ec4862300b", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "af1888eae7fcc4ad1c742084fda9e6ec4862300b", "size": 9529, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.5.tgz", "integrity": "sha512-c55abUyFNb1LjyvKWJittw9ZTfHyUZ8olWEYDD1eh/82S+CpKGstvepuwhK28g4uD65I0b990Aw6dVAYuqfFgQ=="}, "directories": {}, "publish_time": 1433924452724, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433924452724, "_cnpmcore_publish_time": "2021-12-15T16:53:44.257Z"}, "1.1.4": {"name": "async-validator", "version": "1.1.4", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "f8f92c31c429519b833afe57f471cd5377a38653", "_id": "async-validator@1.1.4", "_shasum": "02406872047540a0fab634dd112d9b53ba3e42f5", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "02406872047540a0fab634dd112d9b53ba3e42f5", "size": 9543, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.4.tgz", "integrity": "sha512-/pdS8QkVeuRW2IagRTuXvTJfJt7xYNivot5rXzAqErP1N47Av3EX0T7ROa6dFo3m/nnF0NMkacgBGyoEgdBI9w=="}, "directories": {}, "publish_time": 1433910560952, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433910560952, "_cnpmcore_publish_time": "2021-12-15T16:53:44.496Z"}, "1.1.3": {"name": "async-validator", "version": "1.1.3", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "2ee1eab1e4d73ef065db071ddff8b04c613c6dd9", "_id": "async-validator@1.1.3", "_shasum": "787dd2d9b4ce20fa66ddb09bc9c4e211e6ae20d9", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "787dd2d9b4ce20fa66ddb09bc9c4e211e6ae20d9", "size": 9543, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.3.tgz", "integrity": "sha512-33rvTEmeFGwlYs090pt17e3AEQ08codlek2uZEltswIYu8pNNaSs0QUIl++8jlDcDxdPEzNo6xjZjmeNlWH6Dw=="}, "directories": {}, "publish_time": 1433910140857, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433910140857, "_cnpmcore_publish_time": "2021-12-15T16:53:44.741Z"}, "1.1.2": {"name": "async-validator", "version": "1.1.2", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "main": "./lib/index", "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "config": {"port": 8010}, "scripts": {"build": "rc-tools run build", "precommit": "rc-tools run precommit", "less": "rc-tools run less", "gh-pages": "rc-tools run gh-pages", "history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag", "lint": "rc-tools run lint", "saucelabs": "node --harmony node_modules/.bin/rc-tools run saucelabs", "browser-test": "node --harmony node_modules/.bin/rc-tools run browser-test", "browser-test-cover": "node --harmony node_modules/.bin/rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "3.x", "rc-tools": "3.x", "react": "~0.13.3"}, "precommit": ["precommit"], "gitHead": "92bdc02bc5377c960751ed7f07431c2bc8514c2e", "_id": "async-validator@1.1.2", "_shasum": "599cd9fc66f1ba9571a4f3dcbe392104cc2384df", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "599cd9fc66f1ba9571a4f3dcbe392104cc2384df", "size": 9540, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.2.tgz", "integrity": "sha512-HrQ0wWILisFH75vepuKjw6JAr8z8xL6Va0VjmUt8X7wbxocnH0ZLk7fH/8hbDT+XGG2CyaRkp5DFIHm8sYaWWQ=="}, "directories": {}, "publish_time": 1433908727828, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433908727828, "_cnpmcore_publish_time": "2021-12-15T16:53:44.999Z"}, "1.1.1": {"name": "async-validator", "version": "1.1.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "spm": {}, "config": {"port": 8010}, "scripts": {"history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag && spm publish", "lint": "rc-tools run lint", "test": "", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "^2.0.0", "rc-tools": "^1.1.0"}, "precommit": ["lint"], "gitHead": "84c9ace4bf277b85b6e668fd5c69738e5db34395", "_id": "async-validator@1.1.1", "_shasum": "862264bc217b5cd3fcc4e489484af0f5fdc55131", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "862264bc217b5cd3fcc4e489484af0f5fdc55131", "size": 10817, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.1.tgz", "integrity": "sha512-gs6ss3GqUK8KwJoPaBrqlp2997NTAusjasr6AsGtkcNfBY7gp6jbEWs4Y4+yXD1fMgEXLb5XwryVh9e0Xe7XLQ=="}, "directories": {}, "publish_time": 1426679738485, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426679738485, "_cnpmcore_publish_time": "2021-12-15T16:53:45.205Z"}, "1.1.0": {"name": "async-validator", "version": "1.1.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "spm": {}, "config": {"port": 8010}, "scripts": {"history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag && spm publish", "lint": "rc-tools run lint", "test": "", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "^2.0.0", "rc-tools": "^1.1.0"}, "precommit": ["lint"], "gitHead": "e7fe8a654ae56da78ffe99e631d1bd52459780ee", "_id": "async-validator@1.1.0", "_shasum": "8a35e193d44899232d9aa90dfb8afe0ac10bba71", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8a35e193d44899232d9aa90dfb8afe0ac10bba71", "size": 10813, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.1.0.tgz", "integrity": "sha512-hXIQhDVriOMJyFooAZ9uCbaNJa8PZZmdXN736zTXVVrq5NTe2suvjiB1qpI9V98rTGMXHikTjcLNXsAl96daxg=="}, "directories": {}, "publish_time": 1426679435607, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426679435607, "_cnpmcore_publish_time": "2021-12-15T16:53:45.506Z"}, "1.0.3": {"name": "async-validator", "version": "1.0.3", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "spm": {}, "config": {"port": 8010}, "scripts": {"history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag && spm publish", "lint": "rc-tools run lint", "test": "", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "^2.0.0", "rc-tools": "^1.1.0"}, "precommit": ["lint"], "gitHead": "b20137e455d93d43ee259a0120a1afb012417e8f", "_id": "async-validator@1.0.3", "_shasum": "4ab790bb68303013aaf22edda4739b7449dc3b4c", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ab790bb68303013aaf22edda4739b7449dc3b4c", "size": 10748, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.0.3.tgz", "integrity": "sha512-ZnIjbze87/XlUIBRu+NLGg8xdVXTFREEGiJv2K0hhmtLOHTWtl8GNrClpHQtbiSyq2JG/094g5szPWnvuwPf8A=="}, "directories": {}, "publish_time": 1425975971240, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425975971240, "_cnpmcore_publish_time": "2021-12-15T16:53:45.745Z"}, "1.0.1": {"name": "async-validator", "version": "1.0.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "spm": {}, "config": {"port": 8010}, "scripts": {"history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag && spm publish", "lint": "rc-tools run lint", "test": "", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "^2.0.0", "rc-tools": "^1.1.0"}, "precommit": ["lint"], "gitHead": "7d17f0b051d5797a13d3fcb682b204dc397cac39", "_id": "async-validator@1.0.1", "_shasum": "63b0a37288a35c812b729c7c3571d77221d4c16a", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "63b0a37288a35c812b729c7c3571d77221d4c16a", "size": 10843, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.0.1.tgz", "integrity": "sha512-HyDOmpwFPUDf8U4kv8ZF3JiyDm5Q7pGkVg0VUQ8BvOefqoQNyVjap5bFPwfbEcQb7d09fWv7e03K/XWp+UqUaw=="}, "directories": {}, "publish_time": 1425973489031, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425973489031, "_cnpmcore_publish_time": "2021-12-15T16:53:46.020Z"}, "1.0.0": {"name": "async-validator", "version": "1.0.0", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "spm": {}, "config": {"port": 8010}, "scripts": {"history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag && spm publish", "lint": "rc-tools run lint", "test": "", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "^2.0.0", "rc-tools": "^1.1.0"}, "precommit": ["lint"], "gitHead": "4b45a733ca7cac31f4a5c1f1492224ea305e546a", "_id": "async-validator@1.0.0", "_shasum": "07617cc4dea540be09e3967ee4d2b59e5c194cb1", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "07617cc4dea540be09e3967ee4d2b59e5c194cb1", "size": 7642, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-1.0.0.tgz", "integrity": "sha512-6GYvyfsLMy3Iz7XdXPntdMhw4I5YgfrIjaqSq4EgBxo1PDmTazs1wERKQPR+tDZH6YF56V3uDjQQ63f6r6g11A=="}, "directories": {}, "publish_time": 1425962689570, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425962689570, "_cnpmcore_publish_time": "2021-12-15T16:53:46.216Z"}, "0.0.1": {"name": "async-validator", "version": "0.0.1", "description": "validate form asynchronous", "keywords": ["validator", "validate", "async"], "homepage": "http://github.com/yiminghe/async-validator", "author": {"name": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "bugs": {"url": "http://github.com/yiminghe/async-validator/issues"}, "licenses": "MIT", "spm": {}, "config": {"port": 8010}, "scripts": {"history": "rc-tools run history", "start": "node --harmony node_modules/.bin/rc-server", "publish": "rc-tools run tag && spm publish", "lint": "rc-tools run lint", "test": "", "saucelabs": "rc-tools run saucelabs", "browser-test": "rc-tools run browser-test", "browser-test-cover": "rc-tools run browser-test-cover"}, "devDependencies": {"expect.js": "~0.3.1", "precommit-hook": "^1.0.7", "rc-server": "^2.0.0", "rc-tools": "^1.1.0"}, "precommit": ["lint"], "gitHead": "bd72163e7bb09e20bb79b67f701b43dccb021b2f", "_id": "async-validator@0.0.1", "_shasum": "e94e425bb7f01349f37eeb9a72a24dc2151c9dba", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e94e425bb7f01349f37eeb9a72a24dc2151c9dba", "size": 1580, "noattachment": false, "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-0.0.1.tgz", "integrity": "sha512-M5TpbDrtJC/nXdniIm6hWksDqMzA65cpDphkjVRuCdwic3f/tXLznAxcfboQ2hQTLpB0kfvarTu304Ai3yjwYg=="}, "directories": {}, "publish_time": 1425956877002, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425956877002, "_cnpmcore_publish_time": "2021-12-15T16:53:46.429Z"}, "4.0.8": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.8", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "gitHead": "e93a901624b21e36b92c065572a3a7f9dd1e89cb", "_id": "async-validator@4.0.8", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-vx1kyOCuSJqB5IVatlybUkV/e22sdx+V0XohCWbBfbbzbiLFt6fHxfWg6UEBhFw0gVgZtaSLTJtugmP4rdGQAQ==", "shasum": "35a5abbc819a6eae7de0a92a4f46b7424627e49b", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.8.tgz", "fileCount": 34, "unpackedSize": 255647, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjJ7J2oBFvn2F8dhh6Cl2BEXfyJDmGjMIlehw13/0IcAiBbyVgt3RBnMl33aQm7nYk1G173JSohTmilad4VqTzpBg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZYwXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAyA//Q8+IMA83Fqddub/cFJeVFfaiWQoCtZaomv5eX9uLKp5oCQJ1\r\nSwk2UEMu9YG6lVYhre4+EzWbgEp6/MJObviOrotQbS9xUIF1BhK8N6rQb4k2\r\n7X4jXUOYhDxq/pXMi+86wFkPBVfHWrAtCd++m13Eac6L+KjCq+1c85GMu5Sf\r\nEQ4J03HKxYDOxORwtfjWfx8JrKMnA9kPizYcCgmDW7T6uOvWbiUp4lPVR5yG\r\nkcxNw7LHu25kmAtbjNQULTFVJfrUsWH1UJBDsMeVckCngXH/tw70YShJ0J8B\r\nwSvf9xOM9tTtXVqK9mHRo+9t+JDhCbQwc/ShiMLelxSOdISCuLbSvyA1Ycks\r\ndqahWZDvve5UgTIy3C10SDab6nx2AvQ7FopWM1EjEt1/alcMjlKvC0CS8zRn\r\nIXleapi+Qf06kzSQr8dvpF+CtVTTcrIpbKlt9jXUuThQW/S0G7owS/ZLNpbM\r\nn7onO/l2kKFQYC8OfFzPnCgpFZZW/Xi/UJ8x6iH00Iv9VPkvyd3CTxOUKChQ\r\nvCaP05gL3PWd/296Qu97XUSitGWfPn57G950PKxjyIVWU9wjGrX+OLm/x1Fn\r\nZFOHYAggwzrZEI7562URCS2m1MhFxO3mfgUxzWpWnAyO11/jy0r5bNzZwK8X\r\nJnZoVgmksxdnc6u+jxLK8rsLSOmev0ujeTw=\r\n=L4BJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 59834}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.8_1650822167539_0.646029941863485"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-24T17:50:53.990Z"}, "4.0.9": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.0.9", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "_id": "async-validator@4.0.9", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-RYicfB2qgd+bp+4kmkvvLRstndz2vkdLlxkMod/XEhyUALV3E2C/9uFYNx+By3+O7Vh4A/iRRDhss/EIMC6+qA==", "shasum": "22a81e78f2300cf13b5f0b06681f055b4c98578c", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.9.tgz", "fileCount": 34, "unpackedSize": 255094, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHfvCmypMC5KjDBVxvxmFLBi+s4h3xd0hLofINMGqzg9AiAF8cb60YTX2RP/X/8EgKsrrJxh+n355kkbdfWe18SHag=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZnjpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmquyg/9Fk+JPmgyZUhPWf/r+riyjHsWblEPJun+DXidCrb3MBg9Ax9/\r\nA9NZFn6juDS5fI0JL6gKUpQZZGEv1mDjzJS3T46Y+Y3fogc+lUMZ2WXXwL+n\r\nFMDdgXL5UDaP8i7x1lDK/6lXQqH8SnINzb1mtTZ8OGMreMRmS3idgPEnnd2V\r\nPwBPkxAV0X6loIIauq/uVhLQfWxWNOHumeIeeVspTuPpR0i0w7UqT1xUgzgT\r\nyIT0m8TOh3S8YQI4lkChTyWMzRg+qLaXg5xMDivfxmzGLUmQuAw1bRwcwDkl\r\nefreTuP0V5EVo6I1be/FLKDCoueePZ0RXEqJ/ONh6k1gNh4dU/+VlfeFZNpL\r\nUN7w2BRQAcKuLVeaNlDITBdgMSyEKDvfCvTjO6HXQExlDNTJ9dqqGG37Iwxi\r\nUMFHCYqXcsrdU6+LqNBRuRi2KOyuQtTQqKah1vEGe6oGPLqduPiUXbC0NZIA\r\nPtcVrXVbKf+yDiKH9nS6faYmIzOgRlmVeNwUueOnd0zMcM6+TlF1tuuL5CN7\r\nG4Dj6xjtKnokd/T0AwGvxsspS2tr+P0jsKgRLV/WRQo7gQRJOuXHop8allr7\r\n0+oMH3SzTj1xkE2VPzkLBfC6oKgBxQwimqPKyLmy2H66MwR1w/1I3I66LOkB\r\nLD+E5wdB8RoNUZ5jGdjq3S2IRwHSDV1O5i8=\r\n=RiEs\r\n-----END PGP SIGNATURE-----\r\n", "size": 59722}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.0.9_1650882793500_0.6986813759075994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-25T10:46:19.388Z"}, "4.1.0": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.1.0", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "gitHead": "4522d7127a732df9d449bf9cdc04151e525e2228", "_id": "async-validator@4.1.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-FjmiC/asUPkfsi3taDPtpRVhxxCIiS4EWeR6OjOjppKaHsh3l4J13s7uQ++1zfl84mRaOFxjz7UDIxwy9l9zAQ==", "shasum": "e551fe10016a0163d07d61eb56e39b996cb1be69", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.1.0.tgz", "fileCount": 34, "unpackedSize": 255647, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCa4XL/+okx5hP/vkBzYMdhNHPI4hzCENiPNN+StaOFdgIgD8CD73/sFAk3JCcYU7ThmjAj+u+lTqlZ3msVHgGMfQc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZnkWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMjBAAjWj1bvRxk0GRCoLtCVqFQTCfiNTCI91rWQ8ZXLYcNfUjiwgZ\r\npBh4JvORAqbzh9y4T27GqHxYNsLCpZEazSckYkdZxoXYll/twUfxS6/d+glh\r\nkNsxFzFl6pUqC9HsraKM/TYOZfmoHkSzUw1APOQUbnnBGHvVTrUViU2m5g69\r\nGuX0NGU9gCDl5TR7iSAZno9DPtdLfYLKiMJOxXBPdxWq8QMbQFCjFp/Gya8C\r\noslBMF45SZ0UNqcOp+1CnB0jnNPjf+/Pv8DjJx3+TKSEQS7y5+dVvfATSlLD\r\nmDp6+f3E4Y4Eqf+Y6z/CxasY7qrj7qWQADbazkJP0KvgW+HcmJmrnyuRNFeN\r\n59/xds1K4q6Wa2d6qmTjvCIFMK8SbzGxyRu7u3JdmxLPDGXdb3uHY+fLk3xk\r\n9F5sFCHFxAMuzWmxCL0ERVMZDHmgSnYo7dkPYOl5qy2aKeG05T7pUdgvGRpN\r\n39niqZ4nTZG5SA+uofooiJsqjzhJoZtXmxhSYLvkepsCb489jC2qMtBP0TCD\r\ng7ip23CAMEYjrNQ8xMBJd1NBAn5Eqeah2GGC6gDiOQw5z6iaBnbmWEYpztA4\r\n6QI8UzV8ClTwVp8BmkhLeiMCf7dUXbhIP7rzgpGfwLYQfTJITWWb1U3W1xsh\r\n5WfKLMosZ92aUJSu4npvWR2usMCU55TR44o=\r\n=0syB\r\n-----END PGP SIGNATURE-----\r\n", "size": 59832}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.1.0_1650882838026_0.34886158532991374"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-25T10:46:19.527Z"}, "4.1.1": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.1.1", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "gitHead": "cc126d62084bfce8b0a84dff652348d5ec72d03f", "_id": "async-validator@4.1.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-p4DO/JXwjs8klJyJL8Q2oM4ks5fUTze/h5k10oPPKMiLe1fj3G1QMzPHNmN1Py4ycOk7WlO2DcGXv1qiESJCZA==", "shasum": "3cd1437faa2de64743f7d56649dd904c946a18fe", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.1.1.tgz", "fileCount": 34, "unpackedSize": 256611, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdYyXEeEufIIFLD3LrlSCsfm9nTbEpHMAvcHI4OVf0OwIhAK95Whoi5jDb5UGNsiBHtgQMOheKloMywbfNTwDsv+Ok"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ08wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6OxAAjtIUAWflqbBxKIqZU8MdeFU6lHxHuLUqjAEhbmXkCgNpQgiR\r\nCJAbVJqiCddYRMvpiurrJYaET9cSVZWthWVAz7R9aFth22wKc7H1b5/D0NXG\r\ngOHpvbu1TfhBMt/naghq7syJe/ZfU2EEzH/kxO5F4Z1k+2gZ5F0LhYBoxtXh\r\n4emcGCaoaF+VcbptgIMKmF2bHlsuW3ThTi11RdWz5KQ6yXgf1yIJjW9iDH+z\r\nyrQcmkDDa5V12kazN9vP34zicCG9OXkPAWtn8CY9m5ki0caYBlbefTX6Wb5h\r\njr7OkFgbM29zdHl0+JBaSh6973HyB2PsZvu8sni3dSpHlKceWF9ZP7LDycfj\r\njs9PTM7d7GiYyhPJkLlzrcrUpFgzJXBAtSzXOicVKri2AVcRGFI8zsFgzTCO\r\nSkv5W/r9o1Q82r42thmGt/vQbL7/6+927lWOH/zj6wjy1NAqCttIRfY/lrSK\r\n6mwaHfyz0soS7TENrKlKStzu3ue/JPO6HgDYdPH/uz9wAwOCm6iHAI64/ZWZ\r\nl4sC5BHFA6dCHoe4lJNF1NT4oqeZLvtzITeeiuxUDKyGn1nhsbfGKBdrutmt\r\nC/NhF5JStLiNa7J56lkiL1RET1+Ior5X8WFz5KJL8XMry8bSTNRT5xD8xPvi\r\nenm4M2Jx3b46Z7ux2TTFScngZOnTTedBWTw=\r\n=CUvH\r\n-----END PGP SIGNATURE-----\r\n", "size": 60155}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.1.1_1650937648115_0.9705147331178059"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-26T01:49:50.861Z"}, "4.2.3": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.2.3", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "^5.0.3", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "ts-node": "^10.8.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "gitHead": "70faa9f9d9366b1092c6ce381c48772b8cd2dc6e", "_id": "async-validator@4.2.3", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-W69vDeiwVy3ofpvm0loKZfoM3cY7hvx4mOeSXIHLsaTx+fcO0A5rFjwgh2Db8RRC6+E8Kc6ApdaVXLZqe3McMg==", "shasum": "cb0104424add0bff135ef40dbed2f7704748325b", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.3.tgz", "fileCount": 35, "unpackedSize": 283921, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsADSbEd7qNMQNUR3vZZaqWMseZ7qm8l3VRwSuVoo4ZQIhAPwn/KINM4+PEq4qW/7saeRzLj2rjnAf1Q8XmqhYIKhq"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirGAgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3HA//V0CIB4jKG1W4p/2JJE7leTBAyOXDBhsqXM+YeXGZLXaPe2h5\r\nHCrc6NySnU9lBSl+tC+P3qG0rupqDjI7TEBpluRMCOIClhx79/TR2rnpjxoF\r\nQf+B0/bxOGeX1qDeBCYepsCiIwEdibjjrt6ML+vcIf8O2Vp0H5H0XGgExvTT\r\n0K117p/fHt+Y59nVAR02TVVtxffOH53xlvrHDldYParWYdBYYe6NhVWazqdC\r\nkyeyPBYi0MIYrwXaN1ja2FHH9TheByDTA5P71y/OMd0xMe9WR5oYjn3c+0rE\r\nTUG5TZDIjbWTcA89cuSbGaJViv0JptZuCjbsiV36OrlG9p9NB3eUjmEE3+RM\r\nsAp7fUpnES3pcxORoQC1KQBCE9aje+PVTAN6aBSB7OJ3FX7VTYgNZko0pdO4\r\nHWDLxbPxAntzekb+KXy7nHauCP+oiS5nhtBXTx77D9muQGa7EBXyNTASbcnp\r\nvnMJap3zegZifhNW921kMVInXkWSx7RjLyreb9/H9Z5ZrGjRKChnZ0DHXan8\r\nWuKd9HNMLtp5jl28QqHnJatW8Czsshdn00kduarJwHp22dpxdOHyF8P0Zrj1\r\nADRrYh15k81s77vSmIuzqATenfyKK4KWXQzXaoS1reRv2hEBHC+Wj7g9Jp5q\r\nzKI4Ow5Qm/Pf7xjQBcicpCgK4PHxnFU1qb4=\r\n=bRSa\r\n-----END PGP SIGNATURE-----\r\n", "size": 66973}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.2.3_1655463968110_0.5883367550324659"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-17T11:28:33.811Z"}, "4.2.4": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.2.4", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "7.x", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "ts-node": "^10.8.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "gitHead": "06611b0c846a6f1192ec55fd48678d3f40203b3b", "_id": "async-validator@4.2.4", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-MGbzNMCOY6TFjoy58TPR+cS5L5iZr8POTgnH2+ybK3AG430Tr2ukM3fBtFxO5KtzEA1l/AHh2WTXFJX2GhAt+w==", "shasum": "c710411d2714a98c1ad4c906be09a48a6e80ad1d", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.4.tgz", "fileCount": 35, "unpackedSize": 284406, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBwROP96QLmFkzzxTtJM5u1b/9Ot0P1RoTrHyl1F9SW5AiEA2q9SFib2/eN3UZbky9g1zXi+Nzaa7IndhScfDgiYga8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirGUEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmphuw/+KpYEv2dLrziBmSE36n2EFa4hgl4Za0xJtTPhJ0IRnQTde2Bh\r\njCgEdyAs4b8zQ5h3KE6KXCmfEUqTI7snwHawGVow4MhrqLbjWAnCjaGTSyLN\r\n2pmd1i9F9uV9a6RMVFczWmilwzkzfNq7mttnvUTkoA4pOP/MlwABNXo/50Px\r\ng8EuGCXzU7kKCB558nkHkaRgMtgZyWpI7E0uC77GcoPm6AHKOJGCj4EOb31J\r\nFhCiBG5oZy16B4LgrRa4gXRzxCoBp9kR5Jo/vgG6u1HTzlZdbScxk+U4qyta\r\nQvTc4JaXA9cgs3G+8cDNa1CxT7MtJoaDxM8ssQJEYkoAeRlzqhkGJp4l4BRP\r\nU69eYC1RjZnBNoV5q74oFu3xPp57N9FzO6AgByJ1cpaAdAf85/D8Jgsq0csb\r\n7YdhjLiboureRZduL89FEtCVvAoh2EVVw+P2QnjzL7WCcuDmLrnMZlkrJvlq\r\ntDmOEt6niEcq7tei77Y4WUQ9r9CEljg32FqBq+u5Yvmnad7ifoXlU5Dpn6uE\r\njqFIy+5j1/FpyxoVGdyEQJ6//a6dQkiR9Cx+LW1Qe3tKdJ72VEOUZ64fIl5J\r\npIaHxo/BrSADhbpgG7C7poq+c+AJl0cL6h50QPSsZwde378U9bSmn6PpbbgX\r\nx9OC8Dqj62txlhAs7MR4dEgDaCtJeILsmNY=\r\n=HfMk\r\n-----END PGP SIGNATURE-----\r\n", "size": 67105}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.2.4_1655465220246_0.762381722343678"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-17T11:28:33.913Z"}, "4.2.5": {"name": "async-validator", "description": "validate form asynchronous", "version": "4.2.5", "license": "MIT", "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "7.x", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "ts-node": "^10.8.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js", "gitHead": "a6d0eaf87d65511da85b9485f3f780036ddc5675", "_id": "async-validator@4.2.5", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==", "shasum": "c96ea3332a521699d0afaaceed510a54656c6339", "tarball": "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz", "fileCount": 35, "unpackedSize": 285053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHAjVyBW3/CNP8lmNbQdH+e627gK0SRZR5hp6oOIftpQIgRBHxEE6nF1MTkxMzkwdkGYtiOZAjFxJxWoORkqBE/YU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirGccACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovcA//aX2PTpCigRkVerZcHeIqam0i6pt8fvT0ZJGXcKgDDlQgx47h\r\nBXdKN59UMc+oDSCt2IOXCf2KDD81hkTUzyfRzXh0F+0QvFljW365PPKdgUqw\r\nteKYq+YaQza+px4goVpqXRrAUBaVYDFtb7eQSOAqHwjIvVDFCV4sYyrucwh4\r\nAtQkYpsZL/0Toq9Ap3P9+WKhXiDIlu3ckQB50UaKPXM8MXvKHUsbao/xhYDs\r\nX6x/6rOCRjAv9eYzHMd6wVqSqLNShXIdXUwS4H97hqWCwv7Bj+7Rsu+Gpl0K\r\nnKIVhsh7BWHsO8hiZ6g4mjHENnOHuVJFIQM2iJNw2hz9t8HEosUH4nSIBd9h\r\n/12A5OAp5QpdbPtfAClxCOWSAyo2/55LVCnWi28c5yGDT9dsY+B7YD0+baV7\r\nkEYAEIH5R+drXfTgA0pt7N2Q9zeSSB4uJi+n0+rxF1efp19w5GMQKgi4a3n3\r\n25L3SyyBW4h6mqtuIuzukd3gRyHO7iysQrOPVuDDoe8IbAKLBkIxm5VwIF3h\r\n34lipWy4wXrBFN+hlInW8vB5sFD+B2NO5YLYEo6Xd5Oj1vzK8HiWtNOnSoRK\r\n8rSj/A9gIllkIvcJGvTLNofSo59/4aIBJfANvsVd9GJ1UJfOLOOtUy/skbEx\r\nBUebNBnTmNR00gjcJ8D5yV3tdzdUVPbkc6o=\r\n=ECG1\r\n-----END PGP SIGNATURE-----\r\n", "size": 67204}, "_npmUser": {"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "yim<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async-validator_4.2.5_1655465756363_0.7776738049526379"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-17T11:46:09.967Z"}}, "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "homepage": "https://github.com/yiminghe/async-validator", "keywords": ["validator", "validate", "async"], "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "_source_registry_name": "default"}