{"_attachments": {}, "_id": "jiti", "_rev": "299560-61f1ce5c6bd61f0607ae5cc6", "description": "Runtime typescript and ESM support for Node.js", "dist-tags": {"1x": "1.21.7", "2x": "2.0.0-rc.1", "latest": "2.4.2"}, "license": "MIT", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "name": "jiti", "readme": "# jiti\n\n<!-- automd:badges color=F0DB4F bundlephobia -->\n\n[![npm version](https://img.shields.io/npm/v/jiti?color=F0DB4F)](https://npmjs.com/package/jiti)\n[![npm downloads](https://img.shields.io/npm/dm/jiti?color=F0DB4F)](https://npmjs.com/package/jiti)\n[![bundle size](https://img.shields.io/bundlephobia/minzip/jiti?color=F0DB4F)](https://bundlephobia.com/package/jiti)\n\n<!-- /automd -->\n\n> This is the active development branch. Check out [jiti/v1](https://github.com/unjs/jiti/tree/v1) for legacy v1 docs and code.\n\n## 🌟 Used in\n\n[Docusaurus](https://docusaurus.io/), [ESLint](https://github.com/eslint/eslint), [FormKit](https://formkit.com/), [<PERSON><PERSON>](https://histoire.dev/), [<PERSON>ni<PERSON>](https://knip.dev/), [<PERSON><PERSON>](https://nitro.unjs.io/), [Nuxt](https://nuxt.com/), [PostCSS loader](https://github.com/webpack-contrib/postcss-loader), [Rsbuild](https://rsbuild.dev/), [Size Limit](https://github.com/ai/size-limit), [Slidev](https://sli.dev/), [Tailwindcss](https://tailwindcss.com/), [Tokenami](https://github.com/tokenami/tokenami), [UnoCSS](https://unocss.dev/), [WXT](https://wxt.dev/), [Winglang](https://www.winglang.io/), [Graphql code generator](https://the-guild.dev/graphql/codegen), [Lingui](https://lingui.dev/), [Scaffdog](https://scaff.dog/), [Storybook](https://storybook.js.org), [...UnJS ecosystem](https://unjs.io/), [...60M+ npm monthly downloads](https://npm.chart.dev/jiti), [...6M+ public repositories](https://github.com/unjs/jiti/network/dependents).\n\n## ✅ Features\n\n- Seamless TypeScript and ESM syntax support for Node.js\n- Seamless interoperability between ESM and CommonJS\n- Asynchronous API to replace `import()`\n- Synchronous API to replace `require()` (deprecated)\n- Super slim and zero dependency\n- Custom resolve aliases\n- Smart syntax detection to avoid extra transforms\n- Node.js native `require.cache` integration\n- Filesystem transpile with hard disk caches\n- ESM Loader support\n- JSX support (opt-in)\n\n> [!IMPORTANT]\n> To enhance compatibility, jiti `>=2.1` enabled [`interopdefault`](#interopdefault) using a new Proxy method. If you migrated to `2.0.0` earlier, this might have caused behavior changes. In case of any issues during the upgrade, please [report](https://github.com/unjs/jiti/issues) so we can investigate to solve them. 🙏🏼\n\n## 💡 Usage\n\n### CLI\n\nYou can use `jiti` CLI to quickly run any script with TypeScript and native ESM support!\n\n```bash\nnpx jiti ./index.ts\n```\n\n### Programmatic\n\nInitialize a jiti instance:\n\n```js\n// ESM\nimport { createJiti } from \"jiti\";\nconst jiti = createJiti(import.meta.url);\n\n// CommonJS (deprecated)\nconst { createJiti } = require(\"jiti\");\nconst jiti = createJiti(__filename);\n```\n\nImport (async) and resolve with ESM compatibility:\n\n```js\n// jiti.import(id) is similar to import(id)\nconst mod = await jiti.import(\"./path/to/file.ts\");\n\n// jiti.esmResolve(id) is similar to import.meta.resolve(id)\nconst resolvedPath = jiti.esmResolve(\"./src\");\n```\n\nIf you need the default export of module, you can use `jiti.import(id, { default: true })` as shortcut to `mod?.default ?? mod`.\n\n```js\n// shortcut to mod?.default ?? mod\nconst modDefault = await jiti.import(\"./path/to/file.ts\", { default: true });\n```\n\nCommonJS (sync & deprecated):\n\n```js\n// jiti() is similar to require(id)\nconst mod = jiti(\"./path/to/file.ts\");\n\n// jiti.resolve() is similar to require.resolve(id)\nconst resolvedPath = jiti.resolve(\"./src\");\n```\n\nYou can also pass options as the second argument:\n\n```js\nconst jiti = createJiti(import.meta.url, { debug: true });\n```\n\n### Register global ESM loader\n\nYou can globally register jiti using [global hooks](https://nodejs.org/api/module.html#initialize). (Important: Requires Node.js > 20)\n\n```js\nimport \"jiti/register\";\n```\n\nOr:\n\n```bash\nnode --import jiti/register index.ts\n```\n\n## 🎈 `jiti/native`\n\nYou can alias `jiti` to `jiti/native` to directly depend on runtime's [`import.meta.resolve`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import.meta/resolve) and dynamic [`import()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import) support. This allows easing up the ecosystem transition to runtime native support by giving the same API of jiti.\n\n## ⚙️ Options\n\n### `debug`\n\n- Type: Boolean\n- Default: `false`\n- Environment variable: `JITI_DEBUG`\n\nEnable verbose logging. You can use `JITI_DEBUG=1 <your command>` to enable it.\n\n### `fsCache`\n\n- Type: Boolean | String\n- Default: `true`\n- Environment variable: `JITI_FS_CACHE`\n\nFilesystem source cache (enabled by default)\n\nBy default (when is `true`), jiti uses `node_modules/.cache/jiti` (if exists) or `{TMP_DIR}/jiti`.\n\n**Note:** It is recommended that this option be enabled for better performance.\n\n### `moduleCache`\n\n- Type: String\n- Default: `true`\n- Environment variable: `JITI_MODULE_CACHE`\n\nRuntime module cache (enabled by default).\n\nDisabling allows editing code and importing the same module multiple times.\n\nWhen enabled, jiti integrates with Node.js native CommonJS cache-store.\n\n### `transform`\n\n- Type: Function\n- Default: Babel (lazy loaded)\n\nTransform function. See [src/babel](./src/babel.ts) for more details\n\n### `sourceMaps`\n\n- Type: Boolean\n- Default `false`\n- Environment variable: `JITI_SOURCE_MAPS`\n\nAdd inline source map to transformed source for better debugging.\n\n### `interopDefault`\n\n- Type: Boolean\n- Default: `true`\n- Environment variable: `JITI_INTEROP_DEFAULT`\n\nJiti combines module exports with the `default` export using an internal Proxy to improve compatibility with mixed CJS/ESM usage. You can check the current implementation [here](https://github.com/unjs/jiti/blob/main/src/utils.ts#L105).\n\n### `alias`\n\n- Type: Object\n- Default: -\n- Environment variable: `JITI_ALIAS`\n\nYou can also pass an object to the environment variable for inline config. Example: `JITI_ALIAS='{\"~/*\": \"./src/*\"}' jiti ...`.\n\nCustom alias map used to resolve IDs.\n\n### `nativeModules`\n\n- Type: Array\n- Default: ['typescript']\n- Environment variable: `JITI_NATIVE_MODULES`\n\nList of modules (within `node_modules`) to always use native `require()` for them.\n\n### `transformModules`\n\n- Type: Array\n- Default: []\n- Environment variable: `JITI_TRANSFORM_MODULES`\n\nList of modules (within `node_modules`) to transform them regardless of syntax.\n\n### `importMeta`\n\nParent module's [`import.meta`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import.meta) context to use for ESM resolution. (only used for `jiti/native` import).\n\n### `tryNative`\n\n- Type: Boolean\n- Default: Enabled if bun is detected\n- Environment variable: `JITI_TRY_NATIVE`\n\nTry to use native require and import without jiti transformations first.\n\n### `jsx`\n\n- Type: Boolean | {options}\n- Default: `false`\n- Environment Variable: `JITI_JSX`\n\nEnable JSX support using [`@babel/plugin-transform-react-jsx`](https://babeljs.io/docs/babel-plugin-transform-react-jsx).\n\nSee [`test/fixtures/jsx`](./test/fixtures/jsx) for framework integration examples.\n\n## Development\n\n- Clone this repository\n- Enable [Corepack](https://github.com/nodejs/corepack) using `corepack enable`\n- Install dependencies using `pnpm install`\n- Run `pnpm dev`\n- Run `pnpm jiti ./test/path/to/file.ts`\n\n## License\n\n<!-- automd:contributors license=MIT author=\"pi0\" -->\n\nPublished under the [MIT](https://github.com/unjs/jiti/blob/main/LICENSE) license.\nMade by [@pi0](https://github.com/pi0) and [community](https://github.com/unjs/jiti/graphs/contributors) 💛\n<br><br>\n<a href=\"https://github.com/unjs/jiti/graphs/contributors\">\n<img src=\"https://contrib.rocks/image?repo=unjs/jiti\" />\n</a>\n\n<!-- /automd -->\n\n<!-- automd:with-automd -->\n", "time": {"created": "2022-01-26T22:42:36.282Z", "modified": "2024-12-17T21:28:59.428Z", "1.12.15": "2022-01-28T12:07:04.238Z", "1.12.9": "2021-10-18T13:36:27.856Z", "1.12.8": "2021-10-18T12:51:50.022Z", "1.12.7": "2021-10-12T12:54:19.180Z", "1.12.6": "2021-10-02T13:20:55.164Z", "1.12.5": "2021-09-29T14:50:10.881Z", "1.12.4": "2021-09-29T14:05:25.336Z", "1.12.3": "2021-09-21T18:20:24.077Z", "1.12.2": "2021-09-21T18:00:54.139Z", "1.12.1": "2021-09-21T16:37:42.015Z", "1.12.0": "2021-09-13T10:43:02.982Z", "1.11.0": "2021-07-26T11:23:22.576Z", "1.10.1": "2021-05-28T11:07:59.764Z", "1.10.0": "2021-05-28T10:05:46.032Z", "1.9.2": "2021-05-11T09:42:39.052Z", "1.9.1": "2021-04-09T10:39:53.309Z", "1.9.0": "2021-04-09T10:32:19.955Z", "1.8.0": "2021-04-09T09:30:38.487Z", "1.7.0": "2021-04-09T08:23:07.576Z", "1.6.4": "2021-03-11T20:18:42.948Z", "1.6.3": "2021-03-06T20:39:03.730Z", "1.6.2": "2021-03-05T16:12:05.257Z", "1.6.1": "2021-03-05T12:20:08.619Z", "1.6.0": "2021-03-03T20:43:20.915Z", "1.5.0": "2021-03-03T19:41:10.019Z", "1.4.0": "2021-03-01T10:33:12.185Z", "1.3.0": "2021-01-21T10:07:15.093Z", "1.2.1": "2021-01-20T12:08:39.483Z", "1.2.0": "2021-01-14T12:40:16.451Z", "1.1.0": "2021-01-13T11:42:09.931Z", "1.0.0": "2021-01-12T15:51:17.941Z", "0.1.20": "2021-01-12T15:46:21.565Z", "0.1.19": "2020-12-30T11:10:57.523Z", "0.1.18": "2020-12-22T19:13:49.360Z", "0.1.17": "2020-11-27T22:43:03.519Z", "0.1.16": "2020-11-23T11:10:17.603Z", "0.1.15": "2020-11-22T02:02:16.796Z", "0.1.14": "2020-11-21T21:17:03.071Z", "0.1.13": "2020-11-21T21:07:57.545Z", "0.1.12": "2020-11-01T21:17:40.615Z", "0.1.11": "2020-06-19T14:56:49.584Z", "0.1.10": "2020-06-19T14:52:56.336Z", "0.1.9": "2020-06-12T17:56:56.036Z", "0.1.8": "2020-06-12T12:24:28.396Z", "0.1.7": "2020-06-11T21:14:44.214Z", "0.1.6": "2020-06-11T21:12:51.352Z", "0.1.5": "2020-06-11T19:47:26.889Z", "0.1.4": "2020-06-11T14:13:20.219Z", "0.1.3": "2020-06-07T17:31:46.198Z", "0.1.2": "2020-06-07T17:03:46.516Z", "0.1.1": "2020-06-06T23:42:28.708Z", "0.1.0": "2020-06-06T23:34:55.098Z", "0.0.0": "2020-06-06T19:19:56.653Z", "1.12.10": "2022-01-25T14:50:33.462Z", "1.12.11": "2022-01-25T15:35:02.038Z", "1.12.12": "2022-01-25T15:50:07.716Z", "1.12.13": "2022-01-25T16:04:01.922Z", "1.12.14": "2022-01-26T11:47:57.442Z", "1.13.0": "2022-02-18T17:22:09.893Z", "1.14.0": "2022-06-20T12:58:45.607Z", "1.15.0": "2022-09-06T09:48:24.140Z", "1.16.0": "2022-09-19T09:54:49.953Z", "1.16.1": "2023-01-03T13:04:02.350Z", "1.16.2": "2023-01-10T11:10:23.241Z", "1.17.0": "2023-02-08T21:43:01.379Z", "1.17.1": "2023-02-17T00:25:27.505Z", "1.17.2": "2023-03-06T19:24:54.176Z", "1.18.0": "2023-03-15T15:14:28.750Z", "1.18.1": "2023-03-15T15:16:21.554Z", "1.18.2": "2023-03-15T15:18:04.541Z", "1.19.0": "2023-07-04T12:21:24.461Z", "1.19.1": "2023-07-04T13:49:48.769Z", "1.19.2": "2023-08-18T11:14:39.747Z", "1.19.3": "2023-08-18T12:01:12.332Z", "1.20.0": "2023-09-07T10:28:45.294Z", "1.21.0": "2023-10-30T17:33:36.397Z", "1.21.1": "2024-06-05T00:22:35.129Z", "1.21.2": "2024-06-05T10:32:36.101Z", "1.21.3": "2024-06-05T11:10:41.961Z", "1.21.4": "2024-06-10T13:36:06.075Z", "1.21.5": "2024-06-10T14:33:43.642Z", "1.21.6": "2024-06-10T15:00:31.192Z", "2.0.0-beta.1": "2024-06-28T15:18:17.795Z", "2.0.0-beta.2": "2024-07-01T19:48:52.765Z", "2.0.0-beta.3": "2024-07-02T10:54:08.386Z", "2.0.0-rc.1": "2024-09-19T11:26:30.030Z", "2.0.0": "2024-09-25T18:21:54.238Z", "2.1.0": "2024-10-01T21:39:42.589Z", "2.1.1": "2024-10-03T07:49:36.774Z", "2.1.2": "2024-10-03T14:34:57.620Z", "2.2.0": "2024-10-04T16:47:05.676Z", "2.2.1": "2024-10-04T17:15:31.745Z", "2.3.0": "2024-10-05T10:44:42.843Z", "2.3.1": "2024-10-05T11:05:15.695Z", "2.3.2": "2024-10-07T11:45:06.120Z", "2.3.3": "2024-10-07T17:13:34.652Z", "2.4.0": "2024-11-01T11:08:28.244Z", "2.4.1": "2024-11-29T10:15:21.499Z", "1.21.7": "2024-12-17T21:20:49.254Z", "2.4.2": "2024-12-17T21:23:12.058Z"}, "versions": {"1.12.15": {"name": "jiti", "version": "1.12.15", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest run"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "80f616f1a86c207736b5e37235509b5751f5225d", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.15", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-/+K89y6KJA2nISbWrlc/773XdpDgSQq/LdQ+ZZyw2jRxUNyquPtbsDCCCMRzzNORUgroUGc4nAXxJEnQvpViCA==", "shasum": "8f6a141c06524ab32e05d5e3c9b33eeda54ae775", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.15.tgz", "fileCount": 13, "unpackedSize": 1732012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh89xoCRA9TVsSAnZWagAA1UkP/RFgBTjIWqTQ4XhmeqkT\ng3UGLP5Hz8KpfaMTXcEyP8aHRZStVhCiJlLKQnp41M7VDzr7Zmod+Xh96VAj\n7nFZfuZE1mDkWQXMTDyfNXITIlS+c0pjbnpbNRBE6j+FZiXmjrZvIMuf6twn\nWFSjMGIOp77hVI1IYs1ol6jDOFgt1RHqrg/wHOPB4JC8Nz1RKyaEFZHZKxz9\nqQ3o5wdaVUtGjoHE2iqwcDGu+JTZUQ31lO4fBEET3Nf6Rmx4aRfFPQlukugO\nZARkPBc2P/b5DzSxSF94VnOIM9Vem+0jDl5iiaJEOGZor8HlSmjIjRuBQErf\nP3YkP/wej25jFmKdTav1OluGnLQavx+zp2DejtDBkR5bchPO6DIwYtM7ZCk6\ncPmqQIFf+i8s6jzqRkoQ0cxU9hyGjTURTXr9p9MfDjUTY2rUmvbrWGt4bao1\ncV7hW771+tOKCQfoE/hSSm/baBTZMZNA3IVA174ePlQznUyM7moQVz5W425F\nYd+93pA+75/2xWC3vRSJpWJRIhf+R8fAu0s6mVEDIDUEAocOpFYkS54RXVPb\ntKh2R1mmj9Ujh0BIyiZNolqKqqYiW/IP5VssLbGLPBNWs8W5KQgBP78dsqS+\nTw5168iP+GNYAG/mgGvp1Sw4u6UjoE0CH4I42dmtkEXFshFbJca50RVJuyj1\nrH/B\r\n=+9an\r\n-----END PGP SIGNATURE-----\r\n", "size": 364267}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.15_1643371623838_0.09633712731021182"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-28T12:07:11.688Z"}, "1.12.9": {"name": "jiti", "version": "1.12.9", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "^7", "esm": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "d52165239c8d2b5809e6b9dac386d4a3de38194f", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.9", "_nodeVersion": "14.18.0", "_npmVersion": "8.0.0", "dist": {"shasum": "2ce45b265cfc8dc91ebd70a5204807cf915291bc", "size": 274607, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.9.tgz", "integrity": "sha512-TdcJywkQtcwLxogc4rSMAi479G2eDPzfW0fLySks7TPhgZZ4s/tM6stnzayIh3gS/db3zExWJyUx4cNWrwAmoQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.9_1634564187655_0.29534641160416397"}, "_hasShrinkwrap": false, "publish_time": 1634564187856, "_cnpm_publish_time": 1634564187856, "_cnpmcore_publish_time": "2021-12-16T13:28:36.988Z"}, "1.12.8": {"name": "jiti", "version": "1.12.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "^7", "esm": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "ccc149357c16342b0a146b0e8f4fac398c1818d0", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.8", "_nodeVersion": "14.18.0", "_npmVersion": "8.0.0", "dist": {"shasum": "f9310bb49e290c29e1492e8002a3945c29e1c6c4", "size": 274608, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.8.tgz", "integrity": "sha512-sq9fK0JmDlcwfLkKv+lvqjOetzV4TzwmD2El89UyqjEvVFr3F6I3+2sUqYowRouZ6P7/Oybd8gPXUX1wMA+hxw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.8_1634561509830_0.31349544648051153"}, "_hasShrinkwrap": false, "publish_time": 1634561510022, "deprecated": "please use 1.12.9+", "_cnpm_publish_time": 1634561510022, "_cnpmcore_publish_time": "2021-12-16T13:28:38.031Z"}, "1.12.7": {"name": "jiti", "version": "1.12.7", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "mlly": "^0.2.6", "object-hash": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "0a654723fb46121fb9d9c4860bf407bfb791738f", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.7", "_nodeVersion": "14.18.0", "_npmVersion": "8.0.0", "dist": {"shasum": "745074d5a6f88c29152b32a99f4a2de23bd22a3f", "size": 274187, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.7.tgz", "integrity": "sha512-2v5iYsJp5l7iX6ettW/hD7A9qZtsib3gMBfxbQxASszzOpZ0dFZBZAUQGKKIQ780XGR3sGEp1L/8t1JyyPq5Fg=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.7_1634043258943_0.6257258450938465"}, "_hasShrinkwrap": false, "publish_time": 1634043259180, "_cnpm_publish_time": 1634043259180, "_cnpmcore_publish_time": "2021-12-16T13:28:38.735Z"}, "1.12.6": {"name": "jiti", "version": "1.12.6", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "997a02d75b50b781881b01c5c691291af23b8415", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.6", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "8884d53a10bd571e0e85787994d97cfcc48ac6f9", "size": 279822, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.6.tgz", "integrity": "sha512-drQ/qnYriF9KiU47sRF0rTvfQmJo4JEmFMhCk2SJIsUj+hGnQaxkwaKfyvK9KenX20JNTQmVfJOz7VWe0cSntw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.6_1633180854886_0.2333978080822603"}, "_hasShrinkwrap": false, "publish_time": 1633180855164, "_cnpm_publish_time": 1633180855164, "_cnpmcore_publish_time": "2021-12-16T13:28:39.190Z"}, "1.12.5": {"name": "jiti", "version": "1.12.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "5d953595b665bf1ebeacc1a786406eb48b8b7368", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.5", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "67ee49e95299ed6c18ffdcc2cfe0d2f65432d22b", "size": 279975, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.5.tgz", "integrity": "sha512-+K46njcZW6E/OPWWHImm3G1u+fYVcKU7vqc8b36oVtfi9l3moaSGtsPydaRf2gPuVxh13G1KSCVlW0DfyYUjPQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.5_1632927010613_0.8652986600719177"}, "_hasShrinkwrap": false, "publish_time": 1632927010881, "_cnpm_publish_time": 1632927010881, "_cnpmcore_publish_time": "2021-12-16T13:28:40.019Z"}, "1.12.4": {"name": "jiti", "version": "1.12.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "0f121e9bea3eef58c42f4674d8fb0897a973b122", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.4", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "b91782de8e9441fc2b96f6477850bc298287c917", "size": 279926, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.4.tgz", "integrity": "sha512-i6V6PRT0Rc7QcWYgjz1Lpvn3IYW5kc4j/u869G9sQBBD4NqfMLzMDK+UA5ftC940umFS6c0ITwYVvVoHVGGpjg=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.4_1632924325025_0.11211254420750372"}, "_hasShrinkwrap": false, "publish_time": 1632924325336, "deprecated": "please upgrade to 1.12.5+", "_cnpm_publish_time": 1632924325336, "_cnpmcore_publish_time": "2021-12-16T13:28:40.659Z"}, "1.12.3": {"name": "jiti", "version": "1.12.3", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "168284b2cdb2d516940f5485b8d9e643cc81677e", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.3", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "fe6f9cb066aa2c37981231dffb1d3f04ab4ebdb2", "size": 280862, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.3.tgz", "integrity": "sha512-p88jl9WzvZYekMS5ZOB61bJ1SPV69o7nEpAU+mFpGzXErqLEg3WvNz3jeXylAiSfLZzvqZssrAu08N3AuvaqwQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.3_1632248423838_0.01230269020872754"}, "_hasShrinkwrap": false, "publish_time": 1632248424077, "_cnpm_publish_time": 1632248424077, "_cnpmcore_publish_time": "2021-12-16T13:28:41.104Z"}, "1.12.2": {"name": "jiti", "version": "1.12.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "710a1940849c06df48f4536e6264183b102d3f76", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.2", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "2a5e874f8a63c758ab2a598762f9a972e1de42f3", "size": 280794, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.2.tgz", "integrity": "sha512-1P4ujZHhzdGMRwVJt0p8+caSbGtsMgIncHFq93CVieQzJ8MgRiNKA8IhqN3PNbRjXmBL9w0VvtDYOMB0/d/wZQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.2_1632247253918_0.6386417184901365"}, "_hasShrinkwrap": false, "publish_time": 1632247254139, "_cnpm_publish_time": 1632247254139, "_cnpmcore_publish_time": "2021-12-16T13:28:42.026Z"}, "1.12.1": {"name": "jiti", "version": "1.12.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "a59b5f17cd2b11a88bcb88741ce14b144b7c941f", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.1", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "ef5cee2a3086fb6044ae782ee73032a2017e4ac9", "size": 280727, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.1.tgz", "integrity": "sha512-fev4NxeNRaIlvAuGbb9GHsg86KdDKCHCvtMVnuO6sWB4g9PUIYECfaH6E9SWLl4e8y2RWGPg0AWDsngE3EpRfA=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.1_1632242261728_0.8429439726073484"}, "_hasShrinkwrap": false, "publish_time": 1632242262015, "_cnpm_publish_time": 1632242262015, "_cnpmcore_publish_time": "2021-12-16T13:28:43.282Z"}, "1.12.0": {"name": "jiti", "version": "1.12.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "1fac75386f82f0e0b49484b320d6e70fed07544f", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.0", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "6f6e1908f9425075626400692f0b6d902db262e4", "size": 280635, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.0.tgz", "integrity": "sha512-0yGfdPjYZ+RkYR9HRo9cbeS7UiOleg+1Wg0QNk0vOjeSaXNw0dKp7fz+JeqEpHjmFuTN48eh7bY0FsizSYOLDQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.0_1631529782799_0.47864960396812406"}, "_hasShrinkwrap": false, "publish_time": 1631529782982, "_cnpm_publish_time": 1631529782982, "_cnpmcore_publish_time": "2021-12-16T13:28:44.054Z"}, "1.11.0": {"name": "jiti", "version": "1.11.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "67adddfbba6f9434e694c963ebf424e61fdcf943", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.11.0", "_nodeVersion": "14.17.3", "_npmVersion": "6.14.13", "dist": {"shasum": "64120a30d97b9bf37b8b032cf4564dfadc28984c", "size": 273849, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.11.0.tgz", "integrity": "sha512-/2c7e61hxxTIN34UeHBB0LCJ5Tq64kgJDV7GR+++e8XRxCKRIKmB8tH6ww1W+Z6Kgd6By+C3RSCu1lXjbPT68A=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.11.0_1627298602415_0.06427342435275851"}, "_hasShrinkwrap": false, "publish_time": 1627298602576, "_cnpm_publish_time": 1627298602576, "_cnpmcore_publish_time": "2021-12-16T13:28:44.943Z"}, "1.10.1": {"name": "jiti", "version": "1.10.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "57f0fbf05d27f98d6c369354b3f47d29f5732782", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.10.1", "_nodeVersion": "14.16.1", "_npmVersion": "7.12.1", "dist": {"shasum": "bc2a175b9435274dc8659d3d9a121a91c6b3a1af", "size": 269045, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.10.1.tgz", "integrity": "sha512-qux9juDtAC8HlZxAk/fku73ak4TWNLigRFTNzFShE/kw4bXVFsVu538vLXAxvNyPszXgpX4YxkXfwTYEi+zf5A=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.10.1_1622200079525_0.6454317524615913"}, "_hasShrinkwrap": false, "publish_time": 1622200079764, "_cnpm_publish_time": 1622200079764, "_cnpmcore_publish_time": "2021-12-16T13:28:45.604Z"}, "1.10.0": {"name": "jiti", "version": "1.10.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "ceb9bee90a7d1a62d2daf761366726681a88480b", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.10.0", "_nodeVersion": "14.16.1", "_npmVersion": "7.12.1", "dist": {"shasum": "00ecf6582471bdb654d0b59b7e0ddf9d70ad6ba3", "size": 269046, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.10.0.tgz", "integrity": "sha512-+fyyZJyRnZ4ek5HdWfFGVdXmdnLuyEL+i7vVFvB+KDlU1jfubpFMebJgK/Swgo+xCNev2zHCo9ZR4g2pciiL2g=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.10.0_1622196345866_0.2992138994233642"}, "_hasShrinkwrap": false, "publish_time": 1622196346032, "_cnpm_publish_time": 1622196346032, "_cnpmcore_publish_time": "2021-12-16T13:28:46.601Z"}, "1.9.2": {"name": "jiti", "version": "1.9.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "2062b36292908f7b4324be3bfc8d02f4f96dd83d", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.9.2", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "2ee44830883dbb1b2e222adc053c3052d0bf3b61", "size": 274840, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.9.2.tgz", "integrity": "sha512-wymUBR/YGGVNVRAxX52yvFoZdUAYKEGjk0sYrz6gXLCvMblnRvJAmDUnMvQiH4tUHDBtbKHnZ4GT3R+m3Hc39A=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.9.2_1620726158747_0.7424131898625117"}, "_hasShrinkwrap": false, "publish_time": 1620726159052, "_cnpm_publish_time": 1620726159052, "_cnpmcore_publish_time": "2021-12-16T13:28:47.438Z"}, "1.9.1": {"name": "jiti", "version": "1.9.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "9b76886b6ebc643cbdedc1618ac1ef0ae79e266a", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.9.1", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "d9e267fa050ddc52191f17d8af815d49a38ebafd", "size": 275734, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.9.1.tgz", "integrity": "sha512-AhYrAxJ/IW2257nHkJasUjtxHhmYIUEHEjsofJtGYsPWk8pTjqjbPFlJfOwfY+WX8YBiKHM1l0ViDC/mye2SWg=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.9.1_1617964793182_0.66393357873833"}, "_hasShrinkwrap": false, "publish_time": 1617964793309, "_cnpm_publish_time": 1617964793309, "_cnpmcore_publish_time": "2021-12-16T13:28:48.836Z"}, "1.9.0": {"name": "jiti", "version": "1.9.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "01169c38f0b1a6441e049c6217c0f916e2a84628", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.9.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "7b39fd870929171af1267ec4c0ac57787060a581", "size": 275713, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.9.0.tgz", "integrity": "sha512-89yTCLjy7zRF+oLIKM1bF/vX1kxl7OvdXCq55u3Bl5h+wjFG3jzb9tBY7uG9CI8jjx5MbpRNSGmclHDMfFSskw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.9.0_1617964339753_0.6468238950974103"}, "_hasShrinkwrap": false, "publish_time": 1617964339955, "_cnpm_publish_time": 1617964339955, "_cnpmcore_publish_time": "2021-12-16T13:28:49.904Z"}, "1.8.0": {"name": "jiti", "version": "1.8.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "^2.1.0", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "object-hash": "^2.1.1", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "0828f673e7210ecda48b1a3c4f2910a2ee0ac15e", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.8.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "8ddec5722b5785eb98cbe4298c0d8080ff5814d1", "size": 275611, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.8.0.tgz", "integrity": "sha512-qtwVKO+1YbKIssnbnGN/V6m9g0XvNTCKwk3mJWJD+kEgkxHTozhSZiV7Vb8nkjzKU7asL7m1xOowXBJvjwlZ+g=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.8.0_1617960638240_0.5215727303589703"}, "_hasShrinkwrap": false, "publish_time": 1617960638487, "_cnpm_publish_time": 1617960638487, "_cnpmcore_publish_time": "2021-12-16T13:28:51.118Z"}, "1.7.0": {"name": "jiti", "version": "1.7.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "1e0d2cf8e94a75699139850297622f1650a3b3d0", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.7.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "e16426366376db9143e573b717572d6f2803b59e", "size": 273497, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.7.0.tgz", "integrity": "sha512-lntRupmBO+y6sHmlfQwiZDPPEPyG48Ky6VqtS2D80ToSgerlw1/j9+/bQNdmyZd5LO5XLi/G+1vpWju69GWQcw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.7.0_1617956587439_0.2317740398697914"}, "_hasShrinkwrap": false, "publish_time": 1617956587576, "_cnpm_publish_time": 1617956587576, "_cnpmcore_publish_time": "2021-12-16T13:28:51.969Z"}, "1.6.4": {"name": "jiti", "version": "1.6.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "6ed82103cfa7ab884f6e47f511eb36ab47a1a128", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.6.4", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "63453b602d0234f8bd7ce638f03f0e74ef99be12", "size": 278850, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.6.4.tgz", "integrity": "sha512-ICUtP0/rAyT/GaaDG0vj6fmWzx5yjFc7v+L1MAEARGl1+lrdJ8wtJNChr+ZGEdPoOhFwdhtcDO5VM2TNNgPpjQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.6.4_1615493922782_0.7717516782394624"}, "_hasShrinkwrap": false, "publish_time": 1615493922948, "_cnpm_publish_time": 1615493922948, "_cnpmcore_publish_time": "2021-12-16T13:28:52.812Z"}, "1.6.3": {"name": "jiti", "version": "1.6.3", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "d41682b2882b02b2d1356e29f2a2ca4b46cb4f6d", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.6.3", "_nodeVersion": "14.15.0", "_npmVersion": "7.5.4", "dist": {"shasum": "722fb45ee03b01af87314f80aeae07762f9ffb7e", "size": 278578, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.6.3.tgz", "integrity": "sha512-4oOM9K5MgVx9kiv8zo2B/8SIpKQ/rkaREskE+H8ymvF08mC8PAKnd0bbZ1FV5DTyoOOto9mapMpvZWA06reWKw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.6.3_1615063143576_0.21811166680107985"}, "_hasShrinkwrap": false, "publish_time": 1615063143730, "_cnpm_publish_time": 1615063143730, "_cnpmcore_publish_time": "2021-12-16T13:28:53.568Z"}, "1.6.2": {"name": "jiti", "version": "1.6.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "@types/semver": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "5d5ee2895d6670044bda368449557a6a131f7439", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.6.2", "_nodeVersion": "14.15.0", "_npmVersion": "7.5.4", "dist": {"shasum": "a04b768fad5506fe4e17e1397a8f535feba27536", "size": 278496, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.6.2.tgz", "integrity": "sha512-7/w7Oi5IC1WAT5Ej7KnooJuezBmhHMtva4VbJ2RP4ZCFCqZTvJawcPypPIvIiIksCkJYPwMxU8bBF/xNPBBR2w=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.6.2_1614960725060_0.9106026414178949"}, "_hasShrinkwrap": false, "publish_time": 1614960725257, "_cnpm_publish_time": 1614960725257, "_cnpmcore_publish_time": "2021-12-16T13:28:54.651Z"}, "1.6.1": {"name": "jiti", "version": "1.6.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest", "@types/semver": "latest"}, "gitHead": "fe0d35c813c05c4442e1dbcc8ca5f8bf968b946e", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.6.1", "_nodeVersion": "14.15.0", "_npmVersion": "7.5.4", "dist": {"shasum": "9fc0fcfba4690add0cf830c92c0f92503842bc17", "size": 278362, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.6.1.tgz", "integrity": "sha512-nVY9O+o6LGLy7Z8n6HQn2T2gveccYBhJF4UbYYJDMPawWByvGKhk7suPJggc/u1JMjti7fh7o92cbUfXfXV0bA=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.6.1_1614946808449_0.40625043614437106"}, "_hasShrinkwrap": false, "publish_time": 1614946808619, "_cnpm_publish_time": 1614946808619, "_cnpmcore_publish_time": "2021-12-16T13:28:55.432Z"}, "1.6.0": {"name": "jiti", "version": "1.6.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest", "@types/semver": "latest"}, "gitHead": "d12413a15b69dbf8fdca419fd83bd5d30ffa0718", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.6.0", "_nodeVersion": "14.15.0", "_npmVersion": "7.5.4", "dist": {"shasum": "8888bf15ee18a810163b9ed2d085e5332965070e", "size": 485634, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.6.0.tgz", "integrity": "sha512-wZS9llKo0cjuWfUWhk11/y7Ihy1l7q1bTPCmIJo+MqWDy+Wf03rIlE+ZJHQBJcyjuE/ZyQfyPps/GH5EbDmF+A=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.6.0_1614804200737_0.191141412682581"}, "_hasShrinkwrap": false, "publish_time": 1614804200915, "_cnpm_publish_time": 1614804200915, "_cnpmcore_publish_time": "2021-12-16T13:28:56.342Z"}, "1.5.0": {"name": "jiti", "version": "1.5.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.13.8", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "^7.3.4", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "dependencies": {"@types/semver": "^7.3.4"}, "gitHead": "a6534a0186fadb9ece73e048eca1468e1a1909aa", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.5.0", "_nodeVersion": "14.15.0", "_npmVersion": "7.5.4", "dist": {"shasum": "f7b351c7c321be23a08191c63eab900376688f16", "size": 485088, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.5.0.tgz", "integrity": "sha512-7cvYWEJ9E3+664sLtKL1O4AsH90iKvZz/3ku1pcTRbuHaT8QdSNGwuBfSskGksQivVlmCe8LcNV1xms27sy1Gg=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.5.0_1614800469857_0.07518458350805157"}, "_hasShrinkwrap": false, "publish_time": 1614800470019, "_cnpm_publish_time": 1614800470019, "_cnpmcore_publish_time": "2021-12-16T13:28:57.758Z"}, "1.4.0": {"name": "jiti", "version": "1.4.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.13.8", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "semver": "^7.3.4", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "dependencies": {"@types/semver": "^7.3.4"}, "gitHead": "2882308e660b7ed0657d21025cd0e1e0e97ebd25", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.4.0", "_nodeVersion": "14.15.0", "_npmVersion": "7.5.4", "dist": {"shasum": "2cc3df19003785cd700f5f5fd0292dc6ef1c2751", "size": 484834, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.4.0.tgz", "integrity": "sha512-riTFltg08xtufghxm4Ve6ITghk+rtg7gqD4YvH0LyjqMSxREG9sJmXGbJ2spNkUPdpl37QqjeQ0w0BtS70yNPA=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.4.0_1614594791987_0.6194113086162671"}, "_hasShrinkwrap": false, "publish_time": 1614594792185, "_cnpm_publish_time": 1614594792185, "_cnpmcore_publish_time": "2021-12-16T13:28:58.998Z"}, "1.3.0": {"name": "jiti", "version": "1.3.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "pirates": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "e956a85c3e0b2e2264a37b0b1d76da22028ebf52", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.3.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "9c6b8bde5744f732f33c1aa27108fd03b9a49d0d", "size": 262005, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.3.0.tgz", "integrity": "sha512-CrRtGy3v7PE5dGWtMBe+FMaViXX2eUa7I+Cyo0WmyggVm61lfpEs1orBQalHx9KDIp0Kl5mNaXGHZCMgTpNDXw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.3.0_1611223634916_0.9426293740853267"}, "_hasShrinkwrap": false, "publish_time": 1611223635093, "_cnpm_publish_time": 1611223635093, "_cnpmcore_publish_time": "2021-12-16T13:28:59.624Z"}, "1.2.1": {"name": "jiti", "version": "1.2.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "a93e5926aead72f4a502b058c6fe7f7d88a6b946", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.2.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "e8d31d27e28b66db160da656b7189ee404f406f5", "size": 261023, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.2.1.tgz", "integrity": "sha512-boJtKVr/lTSMcRMf845TXfX3EOXbnrWiINgarCeOIifwrjs76g5iq398Qh8dAJiv0BGaHb8AmWjJRAVSuJ0mUQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.2.1_1611144519181_0.5340568021059313"}, "_hasShrinkwrap": false, "publish_time": 1611144519483, "_cnpm_publish_time": 1611144519483, "_cnpmcore_publish_time": "2021-12-16T13:29:01.011Z"}, "1.2.0": {"name": "jiti", "version": "1.2.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "70911d7cd582307fd07608b06e224db26529482c", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.2.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "15187ac6c49fb5ee4e04700bc05c1d5caed58546", "size": 260877, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.2.0.tgz", "integrity": "sha512-9YR5zusNf4qmOZAIMqja5pTeuojl5mG2sE4IGogwBoxyclX+Wm2yBZDo2nvuwkUOzUg/nmXwiI2Hz7d/d1UCyw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.2.0_1610628016290_0.039922332765012"}, "_hasShrinkwrap": false, "publish_time": 1610628016451, "_cnpm_publish_time": 1610628016451, "_cnpmcore_publish_time": "2021-12-16T13:29:01.775Z"}, "1.1.0": {"name": "jiti", "version": "1.1.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "e95220cdc32b38291472aac2bfea194a7b24d4bb", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.1.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "01b2d1b9ea01e80adcbafb08a7027d4681c6b270", "size": 260747, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.1.0.tgz", "integrity": "sha512-bNQ+heLBltC/62UQnFY3qYSHx8bJ1+gwEk4j/c9KAM/S1xzS/hzakuZPQztkzU69imuTVrihJRzg6utaqd7pUA=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.1.0_1610538129712_0.4416236184343485"}, "_hasShrinkwrap": false, "publish_time": 1610538129931, "_cnpm_publish_time": 1610538129931, "_cnpmcore_publish_time": "2021-12-16T13:29:02.508Z"}, "1.0.0": {"name": "jiti", "version": "1.0.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "a2e48dc2d7beb8dfd69cce97a2530c5ab696afb2", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@1.0.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "508dcacb5f6cdc6b2248f00492db2b749684bf21", "size": 260619, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.0.0.tgz", "integrity": "sha512-TIU7sNC4Iuo6YgjOUjYFME1JR1He4j8da8jIyB0K1B+oNfzbLn9pnI7YTGcR8xTQ8w5wiukmSelo4qeUki01jQ=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.0.0_1610466677813_0.8537529841698845"}, "_hasShrinkwrap": false, "publish_time": 1610466677941, "_cnpm_publish_time": 1610466677941, "_cnpmcore_publish_time": "2021-12-16T13:29:03.426Z"}, "0.1.20": {"name": "jiti", "version": "0.1.20", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "bc7c8624bd258b63ca2dfdc8e8e9fd815b038867", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.20", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "5fc8d4793f0e04e552f35c4a058ea62023e1bd36", "size": 260606, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.20.tgz", "integrity": "sha512-nlsuibooCG5yEjmGSVqxhjULy3rO1Gl0LDP+HpUMbzOSLcz5s1Gf5cPnjvHiei0JCG3SXX761HQArDzNIfdz4Q=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.20_1610466381352_0.7184578625275453"}, "_hasShrinkwrap": false, "publish_time": 1610466381565, "_cnpm_publish_time": 1610466381565, "_cnpmcore_publish_time": "2021-12-16T13:29:04.243Z"}, "0.1.19": {"name": "jiti", "version": "0.1.19", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "a72e0f4ef6532de38e11436572749aabda218d41", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.19", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "455f93e0c92f26c9cea0af48def3eb4f567bbbe3", "size": 260507, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.19.tgz", "integrity": "sha512-ElDW0w/0ATX6IQwReNYMVrYkHu8PqIOO5t3lluceP/saq4SYz4D3uSMpU8pbn/RX3H6VYVPc0PnKn2WfQ5kiwg=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.19_1609326657342_0.028519463439000248"}, "_hasShrinkwrap": false, "publish_time": 1609326657523, "_cnpm_publish_time": 1609326657523, "_cnpmcore_publish_time": "2021-12-16T13:29:05.248Z"}, "0.1.18": {"name": "jiti", "version": "0.1.18", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "64df06d25f69a5879ebffd65dd8688818d2761cf", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.18", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "1120c59f538667ab128ae9d7edde96ab837d8f33", "size": 260694, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.18.tgz", "integrity": "sha512-UUoooMMbuKJivBJL8tH+Tkf4gdF4CcwdaswrBW+HJxme0L1NWjXEV8A4wxgHW2s5rJnDM5NoVmuUYT6PNSgKmw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.18_1608664429172_0.878937904944572"}, "_hasShrinkwrap": false, "publish_time": 1608664429360, "_cnpm_publish_time": 1608664429360, "_cnpmcore_publish_time": "2021-12-16T13:29:05.854Z"}, "0.1.17": {"name": "jiti", "version": "0.1.17", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "resolve": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "2e753c4bb0d812a3c04017cc86bb4f4fc0ef7b9d", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.17", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "b693a29c94d0ca4f82a4624b40dd9915527416be", "size": 266510, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.17.tgz", "integrity": "sha512-IlUGuEHKA44dqJoSqpv1poIRyyi31ciEmpLlRZCmo9TasVSZhwfmaVUuQVs26EHuwYdx+NirOm41+wbykH/+9Q=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.17_1606516983277_0.9445353124690461"}, "_hasShrinkwrap": false, "publish_time": 1606516983519, "_cnpm_publish_time": 1606516983519, "_cnpmcore_publish_time": "2021-12-16T13:29:06.387Z"}, "0.1.16": {"name": "jiti", "version": "0.1.16", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "resolve": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "2084232246f66df5165e2a155e59e4485361f06f", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.16", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "f8c7d73b51153edfb43d3e5a4984537198771650", "size": 266075, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.16.tgz", "integrity": "sha512-fyMkReB81k8jzf9V97W/aUs8FQP/c4+jcP1+y/7yG8K/I6yhNbKVK9yI/GqUAn9WGLIMqLK95UgtQPHX+j3M8Q=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.16_1606129817449_0.2641875293887954"}, "_hasShrinkwrap": false, "publish_time": 1606129817603, "_cnpm_publish_time": 1606129817603, "_cnpmcore_publish_time": "2021-12-16T13:29:07.381Z"}, "0.1.15": {"name": "jiti", "version": "0.1.15", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "babel-plugin-dynamic-import-node": "latest", "create-require": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "resolve": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "30f2d36f64bd3a8670c473435a002415fde4dabb", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.15", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "4ab80092b5e14e6b4d41e645940e6b818de549df", "size": 265531, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.15.tgz", "integrity": "sha512-H2xOyAnmrHcl1KaJ5fDvcKgXbps852FSTMp9v0F5nBpX0NET1xgkLFUFHNHs4i83u4ZSBXLB0qDwvrV8/lOAdw=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.15_1606010536559_0.5825856134594611"}, "_hasShrinkwrap": false, "publish_time": 1606010536796, "_cnpm_publish_time": 1606010536796, "_cnpmcore_publish_time": "2021-12-16T13:29:07.938Z"}, "0.1.14": {"name": "jiti", "version": "0.1.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/resolve": "latest", "create-require": "latest", "eslint": "latest", "esm": "latest", "mkdirp": "latest", "resolve": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "v8-compile-cache": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "7838c5eb37c4433511f781a3cef6fda0c37a3483", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.14", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "ed77008bebcdc8f21245021746ae56b5bd4452c9", "size": 265349, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.14.tgz", "integrity": "sha512-xr4fW9wNEIpo6uK6Xyq3pc26SNGlVH1HKNInWwQTIDaOVFXgsQZkUVWKLaZmbHuC59rABLQNwwNlZ4cfJaeh7g=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.14_1605993422851_0.5416791901279863"}, "_hasShrinkwrap": false, "publish_time": 1605993423071, "_cnpm_publish_time": 1605993423071, "_cnpmcore_publish_time": "2021-12-16T13:29:08.615Z"}, "0.1.13": {"name": "jiti", "version": "0.1.13", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.13", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "8266fe36c19a3f327e8b297dba98ee8ed4b07b3b", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.13", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "4c5cdc23f35c0dfc128c700cefe7a3ce64a3f38b", "size": 260907, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.13.tgz", "integrity": "sha512-jU0cGhWMMMB3gRW2J2F/yJ8LU/i0HUvPLfXKEiwRiMklIo8SvtJOssqEfqDQYGgWh0BU54EnsL1w6rRKGZh3Ew=="}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.13_1605992877237_0.7818800766233363"}, "_hasShrinkwrap": false, "publish_time": 1605992877545, "_cnpm_publish_time": 1605992877545, "_cnpmcore_publish_time": "2021-12-16T13:29:09.985Z"}, "0.1.12": {"name": "jiti", "version": "0.1.12", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.13", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "4a17862cc87242dfd152f2b8bd7ca89e77911a30", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.12", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "ffe4a09266d739b77721170fb73115c50b680330", "size": 260660, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.12.tgz", "integrity": "sha512-rWtqsaABBGNzTzfQkfP7DUvE9KGEIQUd6s0nITw5rDW1L9OwwtBQ5dz9hMq4YrMZqrSJr8ArrMvZ1mV1/7S3Fg=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.12_1604265460391_0.5687354050312827"}, "_hasShrinkwrap": false, "publish_time": 1604265460615, "_cnpm_publish_time": 1604265460615, "_cnpmcore_publish_time": "2021-12-16T13:29:10.905Z"}, "0.1.11": {"name": "jiti", "version": "0.1.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.13", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "51309a1a5aab733fe98d4ecc50b76f1da5e27544", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.11", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "8b27b92e4c0866b3c8c91945c55a99a1db17a782", "size": 260705, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.11.tgz", "integrity": "sha512-zSPegl+ageMLSYcq1uAZa6V56pX2GbNl/eU3Or7PFHu10a2YhLAXj5fnHJGd6cHZTalSR8zXGH8WmyuyufMhLA=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.11_1592578609463_0.4546926647182583"}, "_hasShrinkwrap": false, "publish_time": 1592578609584, "_cnpm_publish_time": 1592578609584, "_cnpmcore_publish_time": "2021-12-16T13:29:11.535Z"}, "0.1.10": {"name": "jiti", "version": "0.1.10", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.13", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "3dcdfca6e69f4db3b50f75e1e19f93edefeda575", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.10", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "28be9dde1a2f6a06ddb536b0a8ae0c4c33a9b776", "size": 260613, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.10.tgz", "integrity": "sha512-y5cWkAiTN/3hwZ0cd2Bhv8nMKQ6q9lXXcmKe8p3XbDUJ7UPzb55Icz4ZORwj05Plz+n1fMGcHMpWC5YgzIFCmw=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.10_1592578376184_0.26948515958574193"}, "_hasShrinkwrap": false, "publish_time": 1592578376336, "_cnpm_publish_time": 1592578376336, "_cnpmcore_publish_time": "2021-12-16T13:29:12.329Z"}, "0.1.9": {"name": "jiti", "version": "0.1.9", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./index.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.13", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "14dd2e75ec32de24748476895bd6e1f40fac7ad2", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.9", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "2567bb84fa101582716387f1d53b88dc03f0e1a4", "size": 260491, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.9.tgz", "integrity": "sha512-HR7z/lkCbx7VUdjWaAUVMU5gSDMg9DOB2NoKWAoN8oYMurosG7UlCVvmYCt1ADn+Uf+wvO7s9LsfiuPYzHMx9Q=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.9_1591984615871_0.7140184785216817"}, "_hasShrinkwrap": false, "publish_time": 1591984616036, "_cnpm_publish_time": 1591984616036, "_cnpmcore_publish_time": "2021-12-16T13:29:13.010Z"}, "0.1.8": {"name": "jiti", "version": "0.1.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./inde.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.13", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "24a9e8dbee3435f4fa2a7f646b40d18632b1f000", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.8", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "43af51cf305509d703252aa09438d3ab174e5947", "size": 260424, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.8.tgz", "integrity": "sha512-tb4Tsi7TEZqdMGrqSiuY0l+f+w+sEXVPKvJYnpeDjhyjpQhpTW5lF0ZDXuooLB0zvg7SseLfw/0uJ2Fots4+HA=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.8_1591964668232_0.002652584789114565"}, "_hasShrinkwrap": false, "publish_time": 1591964668396, "_cnpm_publish_time": 1591964668396, "_cnpmcore_publish_time": "2021-12-16T13:29:13.590Z"}, "0.1.7": {"name": "jiti", "version": "0.1.7", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/nuxt-contrib/jiti.git"}, "license": "MIT", "main": "./inde.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.11", "@types/resolve": "^1.17.1", "create-require": "^1.0.1", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "8ce40e9f40174e35a37f6a01e8b83ed77a02b416", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "homepage": "https://github.com/nuxt-contrib/jiti#readme", "_id": "jiti@0.1.7", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "7d28d29ae69c91ff5e2cd3184f2ccbe0607e93ba", "size": 260245, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.7.tgz", "integrity": "sha512-Xo17OKUtmnevMRkUoyRZo6l3x8QOW58keRb2kOAF5rePnMTK+UmmQUejmEOxT/cUAeabfUzPRrjjFZoepKWngw=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.7_1591910083977_0.6032067476896357"}, "_hasShrinkwrap": false, "publish_time": 1591910084214, "_cnpm_publish_time": 1591910084214, "_cnpmcore_publish_time": "2021-12-16T13:29:14.583Z"}, "0.1.6": {"name": "jiti", "version": "0.1.6", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "license": "MIT", "main": "./inde.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/mkdirp": "^1.0.1", "@types/node": "^14.0.11", "@types/resolve": "^1.17.1", "create-require": "^1.0.1", "eslint": "^7.2.0", "esm": "^3.2.25", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "v8-compile-cache": "^2.1.1", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "23a8e9fd460e2c207d81475820dbf3f01eddb3ef", "_id": "jiti@0.1.6", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "fc05680c932457ae575850030dd42ce0c4bcbd62", "size": 260163, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.6.tgz", "integrity": "sha512-lax0D0Mo1OTV7v3UXrBL3+kop7TUcjHx3FJaxZfmrwKQobCaF1qiETdRNxXhDEQ6tUdizQ3rGS5Eein+vKL5ww=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.6_1591909971161_0.2489744494133448"}, "_hasShrinkwrap": false, "publish_time": 1591909971352, "_cnpm_publish_time": 1591909971352, "_cnpmcore_publish_time": "2021-12-16T13:29:15.459Z"}, "0.1.5": {"name": "jiti", "version": "0.1.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "license": "MIT", "main": "./inde.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/node": "^14.0.11", "create-require": "^1.0.1", "eslint": "^7.2.0", "esm": "^3.2.25", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "7c45e86179506f431c583a3b6aa2cf60431eef27", "_id": "jiti@0.1.5", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "eaffddb53c6c0fa7f282cff5124f58d16ffe4bfe", "size": 255912, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.5.tgz", "integrity": "sha512-ebhBUr7KP69dsH2+RLoAHuFj9NcFenAwibyyZA6ftmCX0oJgCNwgpgGBkc73j1OHuPkO27OgulbLXSS9y1ih0g=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.5_1591904846736_0.39742116664701155"}, "_hasShrinkwrap": false, "publish_time": 1591904846889, "_cnpm_publish_time": 1591904846889, "_cnpmcore_publish_time": "2021-12-16T13:29:17.451Z"}, "0.1.4": {"name": "jiti", "version": "0.1.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "license": "MIT", "main": "dist/jiti.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/node": "^14.0.11", "create-require": "^1.0.1", "eslint": "^7.2.0", "esm": "^3.2.25", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "d0dd67630b397ac3d5ed4434d3e239a57dfb7abc", "_id": "jiti@0.1.4", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "1cfcd2c3be69cabe36b424a56715f93ba38cff19", "size": 263842, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.4.tgz", "integrity": "sha512-p3kyV6vNnIknQidFxPxHnjVMSWnnrMo5jh2JQWz1Wgvsd+893i7wvJ4nF5PfxbZLFHcc6e6i9Xz4sFehdToIbw=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.4_1591884800063_0.6773379351877007"}, "_hasShrinkwrap": false, "publish_time": 1591884800219, "_cnpm_publish_time": 1591884800219, "_cnpmcore_publish_time": "2021-12-16T13:29:18.398Z"}, "0.1.3": {"name": "jiti", "version": "0.1.3", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "license": "MIT", "main": "dist/jiti.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/node": "^14.0.11", "create-require": "^1.0.1", "eslint": "^7.2.0", "esm": "^3.2.25", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "0c944571a560bc39b204e69af14f765636c204bc", "_id": "jiti@0.1.3", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "7d3722f542b7ea1678e2ea96af633fabdc92a31b", "size": 263568, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.3.tgz", "integrity": "sha512-tsWnOUChON4mJwhIii0W6NvJm1E4soSGW8ImzqQwPwt6rRih0AERBRSEF3QCaShkJuuI6IPNsE9MHtVEWGNqrQ=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.3_1591551106069_0.1681022624437003"}, "_hasShrinkwrap": false, "publish_time": 1591551106198, "_cnpm_publish_time": 1591551106198, "_cnpmcore_publish_time": "2021-12-16T13:29:19.057Z"}, "0.1.2": {"name": "jiti", "version": "0.1.2", "description": "Just-in-time compiler for typescript and esm files for CommonJS environments.", "license": "MIT", "main": "dist/jiti.js", "types": "dist/jiti.d.ts", "scripts": {"build": "yarn clean && NODE_ENV=production yarn webpack", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@types/babel__core": "^7.1.8", "@types/node": "^14.0.11", "create-require": "^1.0.1", "eslint": "^7.2.0", "esm": "^3.2.25", "resolve": "^1.17.0", "standard-version": "^8.0.0", "ts-loader": "^7.0.5", "tslib": "^2.0.0", "typescript": "^3.9.5", "webpack": "^5.0.0-beta.17", "webpack-cli": "^3.3.11"}, "gitHead": "b2da5406497f08088dcb3a096123a5eb4085133d", "_id": "jiti@0.1.2", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "661d89ce0de40b2a3d6d9b21eff52ea1c5466673", "size": 262787, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.2.tgz", "integrity": "sha512-KtiYAg7mMBqY2AS26vzSYWnGwKnXRSeAW+/ZYQddW+fPK2yhxPrEIef0lHJE1+wicN6JCAkC/qF/ViBGUlMYkQ=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.2_1591549426336_0.6837492685053765"}, "_hasShrinkwrap": false, "publish_time": 1591549426516, "_cnpm_publish_time": 1591549426516, "_cnpmcore_publish_time": "2021-12-16T13:29:19.738Z"}, "0.1.1": {"name": "jiti", "version": "0.1.1", "description": "Require with just-in-time compiler for typescript and esm files", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "yarn build:transform && yarn build:jit", "dev": "yarn build:jit --watch", "build:jit": "rollup -c", "build:transform": "ncc build src/transform.ts -t -m -o dist/transform", "lint": "eslint --ext .ts,.js .", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-env": "^7.10.2", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@rollup/plugin-commonjs": "^13.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^8.0.1", "@types/babel__core": "^7.1.8", "@types/node": "^14.0.11", "@zeit/ncc": "^0.22.3", "create-require": "^1.0.1", "eslint": "^7.2.0", "resolve": "^1.17.0", "rollup": "^2.13.1", "rollup-plugin-typescript2": "^0.27.1", "tslib": "^2.0.0", "typescript": "^3.9.5"}, "gitHead": "a886f61083a13174046a52677a779f4082c1ba2d", "_id": "jiti@0.1.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "633f6bdba768683d65c54c00b841c192e73b21c7", "size": 416272, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.1.tgz", "integrity": "sha512-UHwZfDjNTnBEjruX7w/kJ/MH2lkZrZ3rDS0emt6Fr+KGJgH6FfsoMFuzVkWrcV9lF8Uoq2N9CUHraR9b5pyHYg=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.1_1591486948555_0.6259184768087931"}, "_hasShrinkwrap": false, "publish_time": 1591486948708, "_cnpm_publish_time": 1591486948708, "_cnpmcore_publish_time": "2021-12-16T13:29:20.517Z"}, "0.1.0": {"name": "jiti", "version": "0.1.0", "description": "Require with just-in-time compiler for typescript and esm files", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "yarn build:transform && yarn build:jit", "dev": "yarn build:jit --watch", "build:jit": "rollup -c", "build:transform": "ncc build src/transform.ts -t -m -o dist/transform", "lint": "eslint --ext .ts,.js .", "test": "yarn lint"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-env": "^7.10.2", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@rollup/plugin-commonjs": "^13.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^8.0.1", "@types/babel__core": "^7.1.8", "@types/node": "^14.0.11", "@zeit/ncc": "^0.22.3", "create-require": "^1.0.1", "eslint": "^7.2.0", "resolve": "^1.17.0", "rollup": "^2.13.1", "rollup-plugin-typescript2": "^0.27.1", "tslib": "^2.0.0", "typescript": "^3.9.5"}, "gitHead": "cd992c31799b781c74dd7bd4c75083f1b91ceea0", "_id": "jiti@0.1.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "ed422d2920562fbca8dc13ea51f457cd765face1", "size": 412606, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.1.0.tgz", "integrity": "sha512-jqLJ/Qho9qmZyPMJlNzfRZ09luGCF7G1l753tCmMn5f1wR9orTlF5xUH3vXHDdyYe/KCtVK4kd8O78F7436uXg=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.1.0_1591486494931_0.9737097520851894"}, "_hasShrinkwrap": false, "publish_time": 1591486495098, "_cnpm_publish_time": 1591486495098, "_cnpmcore_publish_time": "2021-12-16T13:29:21.547Z"}, "0.0.0": {"version": "0.0.0", "name": "jiti", "license": "mit", "_id": "jiti@0.0.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "0be2f7857919ff102b49f3063f1fbfc608d74e89", "size": 153, "noattachment": false, "tarball": "https://registry.npmmirror.com/jiti/-/jiti-0.0.0.tgz", "integrity": "sha512-lFGrsDfufUj57y+vSf7rOIuDIxVF6KHB1kk06yLTEzKYIoVAWG3WFGnjfF/EZWDxJOkp8psqhRJKJlysp9TNDA=="}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_0.0.0_1591471196555_0.5130964863905125"}, "_hasShrinkwrap": false, "publish_time": 1591471196653, "_cnpm_publish_time": 1591471196653, "_cnpmcore_publish_time": "2021-12-16T13:29:21.749Z"}, "1.12.10": {"name": "jiti", "version": "1.12.10", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest run"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "06fd2d4869caf6ab7fe3d8f07918b6f3f2909339", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.10", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-sbaR90YFFOebPWUyqMjh2l/KlceE0UAFOqpoQzfeweXhm49Zm15Ug097JOnKgTSC4v5CF7kToFgyUPssRzpslQ==", "shasum": "61bb48c002077f7146e3b77d0ef767e4ff597561", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.10.tgz", "fileCount": 13, "unpackedSize": 1301767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8A45CRA9TVsSAnZWagAAORwP+gJ7cKt23aQZq8YU3Q5n\nsvJuEII1iGISb7Ah6C4eOQYfox4iCuLIORoaBCLBOlET7rAUudncc3bTLXoJ\nycnchoDEOZvJJJq2hnx5Mbk8DeB5jF2nj+3nUjpHcqnZBFcnr+HKDIlLZZDA\nXJujb1oQE+URUoSH+HgUO9ZkjLLAMXg6VxAvs4iY3WxrJqV9uRWR5ZxuXhEp\n/vAJ1NT6dx3IpBHvdxtn/xLrR4YmLrHj5WWxcn4Zf/XHQ29+n1VCiuIETecb\nst8s6Jpm7YkCBzOLxv4EWT+q94NSKm3d5RZZwmeMwaZCWsMoClkF6yun+pBz\nB8NoleLpEwTzKhiQUldyFJnwnQ4majexz/W2wXPEUIdgKst3N+LfuCgK7d/Q\nBzWH/n6X8Zxz/ZOnKu0/37Qs8+k6nCO8fvhqyaWfvKgt9yDrsq3lcUdlfttZ\nh/VyWtkIFiK4VpkUCtqAmMnrkfuSDynEsSKQN3qpr4pJuiWMp0iAYQhPH8GL\nYjPTFQwtjZoOM/uV818MqW0WGO78aHuM/3P3LGy8Fvx7DUp5uTsoY3tOOmQh\nti7EB1tDtPXGbZhiJFk6rcYDcXMLkK1HMr+cPan2YUJjbUO4FDWI2Fj9t6ZH\nqSstQt2qMpAio8M8EB9TUE7ZkvgT9x3bk2lVIEtsjqxjoFYHIK6Ok/L/rjSE\nsw86\r\n=kB2j\r\n-----END PGP SIGNATURE-----\r\n", "size": 311810}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.10_1643122233241_0.6668551206288387"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T14:50:56.237Z"}, "1.12.11": {"name": "jiti", "version": "1.12.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest run"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "e0db5f7406583bffa724f1e4217b89a83a95c52b", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.11", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-1QGzY8y2fPqvWHNu3Y8tvhNSddSYwEoJC0MYvGDW5w22srN28OpyTWo2j7SbKFMEjtH0jkikemDTigH5H9cRXw==", "shasum": "b556410fe009293ceb68bf80b05f49c8e472af9b", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.11.tgz", "fileCount": 13, "unpackedSize": 1731511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8BimCRA9TVsSAnZWagAAbe4P/iKSZV1IHJ4at08W3htr\nfplrfnmoruBjY4lsu1Ko7tFyTJgyW/p7DFeXw1gQVXSdgAdLbHudFbyjXts/\nD8JKDOpf7XUh5V2i70+ehkx1JzupmB8lK2BSFYQ9XP8bf4BynVRNRjZDjbCs\nLGG+NXZ4Icr9/DAkI5AwYHq9qlYjDBeImVHALl8oPYpWHgCY2asWBQqSmZjp\n4/uBheRbyJ+4+0QHphV2GyUYT5iZEHtoIwzgzbWsxXNtrt8GN8ZlUSxexnh5\n/K6eFS5e2TsuN64Fz8FKlhMXL8nv+qTSk3S25sANscJSS0TRMu5bk2k5eSnt\nHbnDM6Yn64mlJSoOCcgEZl64Jm6gU+WRZKs6NWz7CyVJP/UeVwNZAvXsI4bn\nleVcjF7Itbh47K4aDo990PWvhlHekBEdFFzslqrRcwn/itIFbibY2M/QxSZv\ntawXxNNCr1HKoqceXKPJwSJ6Q5y8HdZpkNgFb3m6FEqIXzNPpEAIuR4CNWJy\nQKoZazJQA5GoMIOompjzIsOi2dJZAo3IlGh5rmx/V/JUXDnlyHVGBEGshFx4\nVkvx8MR9AtXNS10DMRNF5rsaWKyJON7QM0AW4oGM/Bb/gXWEUM99mZR+1CJt\nEYv9WBMdqjx8FKDqvvAoQf+yEw5Gh8MBHVpXWm3JkN/gWzmUCKl3kLRT3nOc\n8nPa\r\n=XsVT\r\n-----END PGP SIGNATURE-----\r\n", "size": 364147}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.11_1643124901712_0.6601054939415001"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T15:45:13.809Z"}, "1.12.12": {"name": "jiti", "version": "1.12.12", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest run"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "9868198bca6565a65d55d9822d3cfccf1c0c7fa8", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.12", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-7gdxH+yhHKzzfQj2Q38KsjRjt97HszKZE8XbE4JngUEnBq7T8eOpqE4h/wKD+TsdUCwSMw7hPlGMMaZbHFxfkw==", "shasum": "9d4992c9abdf669ad49b235d79b5c92893fb294b", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.12.tgz", "fileCount": 13, "unpackedSize": 1731550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8BwvCRA9TVsSAnZWagAAazEP/36hjSmtRujgtVxv/T/K\nHKPLkHkJNHV95ZIMnjBsHnnyqANulq9UYG5+f1jTrwSo6A+a95rXVPj9EiA5\n5Z3Kn/PyZVrQsnBv1RwLabCRWuUP4/gG5v/b+qk/om7pthUOKbE3nhXy8+7g\nfzmVZObLSfH3/BUHh1g+NNg9vEecBcQLpJEU6bhTPJGsE7erUNZ4KKqLwxrL\n9Qm+F/13BRP83FAotKNurh83oc8ihDTtF5H2JQ73XmHHB+1ITyBR00vkYJM1\nq3fsNslXgNB16YhjQbotqJ6MN+xmm590WMAlmzXc0/lvv5xlkrQvWo3AIQMX\n+75b6pWhSz9CSyRlhoserflrM5+4iVcL7gMYqCpYvhq+L2X/AP+Kh/Gti7BL\ndMp6j3mPpgMBqqGVCKNE6S/E9ljPg7zYRWcO1rDE6jfzeHS6HzgO+tj/wKFm\nxiPrH2kyR7g4pP6ZjlxOcsrg2Y0+n4GsIiITJXlP9xLYhamxWYjqxI+oP79N\nqbhifxzqW6c1hExbf/ct+2O2tSYHsbV9SB4QcvEFqPk2F3XiH2gQe8lOOqEu\nOZP8Gu3wcl/2uVzdjTaB9DXHXyf08vVzJIZoapeU2bCO9L4UrHXO+3rFGXp8\nF9NWUsu7fgnSCDrK+xN6VdoH3Cu6Y0xnTVSYSeh5BO6Q6zUwGlTOeKtFdCPH\nHhmn\r\n=f++y\r\n-----END PGP SIGNATURE-----\r\n", "size": 364157}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.12_1643125807416_0.699954423423089"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T15:50:37.436Z"}, "1.12.13": {"name": "jiti", "version": "1.12.13", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest run"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "0ea19c8f03f4b72edc29ff238bf6c374a6c10de2", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.13", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-OhsYlXc2HLjuHZML6Ay68/l0KT9lXMw9zo1+TMIaxW8c0TS+kJnxgx+he4yBPFiRHi7UIsG2ANZjHl2zU86j2g==", "shasum": "652a018606d1971e219eb122e26bdbdbac497a53", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.13.tgz", "fileCount": 13, "unpackedSize": 1731677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8B9xCRA9TVsSAnZWagAAm/UP/1Ns3Qe0l7SQ7AiQRZoY\nyXGbTvqEDFT4FA26apzVfC96vXzJorlqjU/BkbYkXZixgjOOfAcj7FsxqwI6\nfWgRqDi9Hvv3MhpiNJUfaARs9c9hWsbtiVsUUDNz15cOh5tkUt8sHZ5SJLV7\nTGpzZUhhuKScu+XcmUPsmrmhb12YO9eN6MN9YC3jMdUnPgG0thCkXlp8Pg25\nqdg8j+tw1j7CHkNL8j4Lrv6YEn2BZT2sJ2mC/uKFtQtmPqRY5CFGQR5aZhhU\nOZMTlvBgCYuqThA4CTOjomefDknsIs1BJZ0FWIdy+Q8B5w99mHbf1l8NsfC/\nUdmmZFcNEmw4gSwyYf9qyfPaok22uoqJ4HT7jUtwB5ZuL9waITIZu1l9CZAu\n7o/6rFkvjq6NuxD8XkWTTVSYhkzmTO8EUcN6drJuwmRoDB6Sb0mlNNFKOwKP\ndDWgmpLrkUrpww0HhFpTmOvQIFWX7nATlwN8I/WMNS1LVNloOMqAkKl3lZNc\nFLQFLFDs1Eg146euW1ry4SQyVnIgYEWJ+vrx4+ZuFyYTOcgP2jRNkCSQei35\nMlI7QJth+VKGvkq/RqyW1usyrO4bpQEQi6eQxTZNtIMvGDV0NCxG/VbxTFnc\nHXrLajZI2nD5JStHeJXu6qX90X9kNnR/LaAjbee9Ua4j3ZPtQGBiWrnJCZBC\njzX5\r\n=5Qa3\r\n-----END PGP SIGNATURE-----\r\n", "size": 364171}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.13_1643126641692_0.19089824218940699"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T16:14:09.868Z"}, "1.12.14": {"name": "jiti", "version": "1.12.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest run"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "a78949bdd3921e3bbbfc39ed90f2d6a9337c30b5", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.12.14", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-rR+9GYXtYiIod1x+EHFYcTyqIPG4cAHG3WYGHkk71MVjzPCJwOI4E27BPOXQhUf1viglcLtqj3WotFIOJQwM+g==", "shasum": "001e583136fce21d62585491aab96c9be1ea1f16", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.12.14.tgz", "fileCount": 13, "unpackedSize": 1731969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8TTtCRA9TVsSAnZWagAABxMP/2IPMQqHJZwwiPAm0YmE\n0GctWUxn5RoZQWU1PFSuXxLQClRALgXB4X6H6FH9rnEO77CWqQ7zsQ2Z1CAD\njtS/xwy/wXrXK18Q3C7F8mv8JThDGBAeIUetUrivjLz7VbW6gserndOUUTU2\nq0mSwk6epvYwpjpB7oYX9cFbv6m6/Gqd6hEYXOMSYtFHfW/8aHMrWLbHGkmf\nrFY06PbrJuoloKxnfvY5pSl5JEGPrP6cTwio9hWc8SfhTHqHvQKETkOAB+Z6\nZLHrh1OvTlTGNv7AE7/7jT5BHcUY/yJv8Y+6eSCLQLD1jB4iz8Q1oUmLSJq1\nbsx6e1fcR/5VJxzEtGGIdwTv0EvqXsuuHdYJcykpGf+9sySlj1si29Ts121m\nVFNFaro+EJ++EJNL1r4OuInbglhANeJfKzdkYLSODhs0fabVioMfxs4x2m1m\nMbn4Aa2zgkJdCqldsDeJQXVnbxYF/0Gwo1aBEK9mIFU8wasD3SZcdaTW1ZVS\n/HWnRHN30M70eWVSd8dMBT0v59JwPRo1sF5G6oNwwVZ3q8ETNxBV1zCm8Hot\nGbaTQLrrEs62wlainLLUrKzJJEoqv1jkemag7rIZ9f6obvQ/7H9ewbROjPgE\nUvYjgMvlClOKHtICdqTh645386nXEfu7KCtWm4Sq6LYMxoebfvFyffFridLf\nBPJX\r\n=b+wN\r\n-----END PGP SIGNATURE-----\r\n", "size": 364256}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.12.14_1643197677136_0.6753797021675614"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-26T12:00:39.417Z"}, "1.13.0": {"name": "jiti", "version": "1.13.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "test": "vitest"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "babel-plugin-transform-import-meta": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "gitHead": "95476f32aee10e464c5cb7db3e59fc06354593db", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.13.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-/n9mNxZj/HDSrincJ6RP+L+yXbpnB8FybySBa+IjIaoH9FIxBbrbRT5XUbe8R7zuVM2AQqNMNDDqz0bzx3znOQ==", "shasum": "3cdfc4e651ca0cca4c62ed5e47747b5841d41a8e", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.13.0.tgz", "fileCount": 13, "unpackedSize": 1732608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD9XBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH2BAAlCyCTMgfAIypCAg2c+oG0E+tn3gj91pqLvzIVaZbD5Cnk7Xg\r\nFOFqWc86x3LfN3FexcICSU5IZ4kp8uoY1R64FcIi5VlBsCg0BTR2YixfW3cG\r\n72yrSHbHz5Z547ez2twL1XL+5QEKVqazykVaIdgbZ12lLwumFexvIctHKkW3\r\nKYXb/Z0tb17MCEW/O91Y0Hhs3+QNVsXDkma4I6J3Fw7LFIpRbSaDKW68k/EO\r\nkdafIC3+Cq0sCyylLE0AhDMfV/OLK+XNv478Qk95aq8Mw9qapYEDD9e/vEVA\r\nJrn49VQvy8xn1sdzIYauQpwzvMjViN2OuOe6O3A3A0NF8KuWxMfZK+NegCgP\r\n3tgU6QhEddjrhaBl1Y7k66YpN7sdkuBoKa/GiZlfkk5YK40Qh8nq89LEAzcw\r\ndPGPczJl83mmC1jxi6sHV5kT6zcTBCQdRlUTV+ywMi1Z6OxtySB9TL5Bva1D\r\n8spkf2wFnyZkfmW3cAIhatzdO9VSBnIMWgGNykoGc7MBUkcLsqkGatWsD54n\r\nJuu7N8g8BHpRZuKYiRcLrQW5ET2kz8TwDW/Gl6r660a0pbVi+qOLnYX9XPHa\r\nQMoAFujqCuYaNqTXlQaFLnwrhEO/epv7jCIpQlLn7Wghm3/YqL24hL0/xujd\r\nci+qY6p7x02Y3cw+bU/J4x/hRSIld0qUjI8=\r\n=5QdM\r\n-----END PGP SIGNATURE-----\r\n", "size": 364434}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.13.0_1645204929668_0.40097228440532295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-18T17:22:17.746Z"}, "1.14.0": {"name": "jiti", "version": "1.14.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "devDependencies": {"@babel/core": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/preset-typescript": "latest", "@babel/template": "latest", "@babel/types": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@types/babel__core": "latest", "@types/babel__template": "latest", "@types/mkdirp": "latest", "@types/node": "latest", "@types/object-hash": "latest", "@types/resolve": "latest", "@types/semver": "latest", "acorn": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "create-require": "latest", "cross-env": "latest", "destr": "latest", "eslint": "latest", "esm": "latest", "estree-walker": "latest", "execa": "latest", "fast-glob": "latest", "mkdirp": "latest", "mlly": "latest", "object-hash": "latest", "pathe": "latest", "pirates": "latest", "pkg-types": "latest", "semver": "latest", "standard-version": "latest", "terser-webpack-plugin": "latest", "ts-loader": "latest", "tslib": "latest", "typescript": "latest", "vitest": "latest", "webpack": "latest", "webpack-cli": "latest"}, "packageManager": "pnpm@7.3.0", "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "pnpm test && pnpm build && pnpm standard-version && git push --follow-tags && pnpm publish", "test": "vitest"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.14.0", "_integrity": "sha512-4IwstlaKQc9vCTC+qUXLM1hajy2ImiL9KnLvVYiaHOtS/v3wRjhLlGl121AmgDgx/O43uKmxownJghS5XMya2A==", "_resolved": "/tmp/afedbb6e79c3e7bf1c0df0a0c56526b6/jiti-1.14.0.tgz", "_from": "file:jiti-1.14.0.tgz", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-4IwstlaKQc9vCTC+qUXLM1hajy2ImiL9KnLvVYiaHOtS/v3wRjhLlGl121AmgDgx/O43uKmxownJghS5XMya2A==", "shasum": "5350fff532a4d891ca4bcd700c47c1f40e6ee326", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.14.0.tgz", "fileCount": 13, "unpackedSize": 1706457, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlXr8kXmMnx260c/y0tbwmDeKN6NJZU3+jerhCxgLiKwIgWXBX4DoYLyHNPo1tNO3/GBLzR/KsvIZlyAL8FK+UgOw="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisG8FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYzg//ZHYocyEhk7hbi5s4+H/8dkbdCCAOG0d+GIuVaV6nsbinA4Xj\r\n3VekyRHybY9lMZL0Z1XAlRIeB1gkKIPtfEblAqi7BQ+1XcnG4WVB2qof2eF4\r\nyS1+DOfyQCvNSumtcxQiq0vKFgeCudP9p5BURdgpq9xEUOWGGeNbywX2e6Hl\r\nMP1FMDRtQV/JggrWsBhCmkVoSqwGynkGgBx3vqGqhLmEVhNlnTripqiKsfJS\r\nZrDCZHUQ0BJWHsVMeQCeecRvADZS588p38FE4URK5//7uzfqkeZZMTYUbWBP\r\nxYHPRI2amas6ec0oGRlsrzfgZl8ckLYpIZe58XMlpoG8xOUmcaCHt+n3Dc+y\r\nguFa9SziAAyNuU7XnEZ3BpPUsegv8DmBPsetkxF4ClBSIX40GvjoL5zFOpMM\r\nOFVwpk8T6C2olkSnAFx5IigfjORNFz9MrHavUTpmH2gFKAcT1ZofdPQaf58P\r\nJolcpKAScaZNylIBR9jrfYuL+941YuWjjuYCEBoAlF8BAe1Sr02gnwlTOBBH\r\novoxprUg3PYEquzOfnrF7T62r8lWUw1BqEpqPVSQVy1s9MaTFUtISKOYiiqQ\r\nzHM28WUolbaiuhFq9e9KAtH3iAGwDOWDrzyWiCgOROGbaU6P4+wpKpoFyk3P\r\nCWUjc9Umv5oxFijzS9rpUhnHvhLE2xjAyvc=\r\n=KgK7\r\n-----END PGP SIGNATURE-----\r\n", "size": 352270}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.14.0_1655729925421_0.28794202233541943"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-20T13:51:56.856Z"}, "1.15.0": {"name": "jiti", "version": "1.15.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-decorators": "^7.19.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@babel/plugin-transform-typescript": "^7.19.0", "@babel/preset-typescript": "^7.18.6", "@babel/template": "^7.18.10", "@babel/types": "^7.19.0", "@nuxtjs/eslint-config-typescript": "^11.0.0", "@types/babel__core": "^7.1.19", "@types/babel__template": "^7.4.1", "@types/mkdirp": "^1.0.2", "@types/node": "^18.7.15", "@types/object-hash": "^2.2.1", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.12", "acorn": "^8.8.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "config": "^3.3.7", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.1.1", "escape-string-regexp": "^5.0.0", "eslint": "^8.23.0", "esm": "^3.2.25", "estree-walker": "^3.0.1", "execa": "^6.1.0", "fast-glob": "^3.2.11", "mkdirp": "^1.0.4", "mlly": "^0.5.14", "object-hash": "^3.0.0", "pathe": "^0.3.7", "pirates": "^4.0.5", "pkg-types": "^0.3.4", "semver": "^7.3.7", "standard-version": "^9.5.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.3.1", "tslib": "^2.4.0", "typescript": "^4.8.2", "vitest": "^0.23.1", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "packageManager": "pnpm@7.11.0", "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "pnpm test && pnpm build && pnpm standard-version && git push --follow-tags && pnpm publish", "test": "vitest"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.15.0", "_integrity": "sha512-cClBkETOCVIpPMjX3ULlivuBvmt8l2Xtz+SHrULO06OqdtV0QFR2cuhc4FJnXByjUUX4CY0pl1nph0aFh9D3yA==", "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/4d56aa3320826e424cb4d17fba4cf665/jiti-1.15.0.tgz", "_from": "file:jiti-1.15.0.tgz", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-cClBkETOCVIpPMjX3ULlivuBvmt8l2Xtz+SHrULO06OqdtV0QFR2cuhc4FJnXByjUUX4CY0pl1nph0aFh9D3yA==", "shasum": "cfa7ebfe4a60d77cf3bd4f8630cd99225b638417", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.15.0.tgz", "fileCount": 13, "unpackedSize": 2254297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEv5FmRjRZeRGjkANPKe2vhaWCEe7yIiEh0j9zbhvePqAiEAp/YnTp+L6kmExGdK/kn2EvWSuroain5G03SGRMcGPSA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFxdoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhRA/+PDjayaei6aOrlCHp5ASP3fVBPz0Qh3XPRRftuU4IfAR6zGgf\r\nACQYb41XHFnWl9B/STck6vVDBuLW95/e2Yo7sPPRRW0MWSMEx95ZobpdAXJA\r\nP5ZZSF1b2cpPiNu5pvAJVgYjCH1rUNo9RFeBoDWIwxj8DNcf6vSZ/xVBHKJk\r\nv7KugZ8FindX1kA1UyJUX33OpdfgBSvTjHfbEwQKp0Nkdlf8TsgfwcGEdPHU\r\n7nWCBj5RXEnUvJBvYHxWd9fzkf6xbRNX1lO/dbc9deCU+he08cTuSuQoQ1qs\r\nqMMlmYByEqguDURt7/AatNJ02mQf2HsU2tNI1tHaOvvv1kHyZzcLEgxvzaxU\r\nnixiRvECOfdYK1Zl9axTyL9Z6gDiXdlzSmSnUZ+78LRdlpedirlwZ9wcN4Tq\r\nSV7mV4e6PkfrL5UHUHogFxsyW+eKx50S9qOoQ1Yg7dfE5buiK0LV9KgYip+D\r\n2B/7Sva3kFnPQpxrrN8C+BecDdgHJ5MXmcmpH/dkGLiqhzBUiZdxRIW72j3O\r\nRANdXsTMfchhOTSyXj0igj0nAHLRWIATJvCcKzu3ZrI4c7ICV6njy/ct+e0K\r\nw0wC2UcPqQgUbIEuLV+Lr34FcBFtDWf5HDdNzU27/XBHCHXSqfnmCaH0VK61\r\nABmc6Lvjg6dp9V7KUjUZZ5IzBipkwPMLAfM=\r\n=eIHX\r\n-----END PGP SIGNATURE-----\r\n", "size": 476894}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.15.0_1662457703907_0.49870543709888504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-06T11:20:26.657Z"}, "1.16.0": {"name": "jiti", "version": "1.16.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "devDependencies": {"@babel/core": "^7.19.1", "@babel/plugin-proposal-decorators": "^7.19.1", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@babel/plugin-transform-typescript": "^7.19.1", "@babel/preset-typescript": "^7.18.6", "@babel/template": "^7.18.10", "@babel/types": "^7.19.0", "@nuxtjs/eslint-config-typescript": "^11.0.0", "@types/babel__core": "^7.1.19", "@types/babel__template": "^7.4.1", "@types/mkdirp": "^1.0.2", "@types/node": "^18.7.18", "@types/object-hash": "^2.2.1", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.12", "@vitest/coverage-c8": "^0.23.4", "acorn": "^8.8.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "config": "^3.3.8", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.1.1", "escape-string-regexp": "^5.0.0", "eslint": "^8.23.1", "esm": "^3.2.25", "estree-walker": "^3.0.1", "execa": "^6.1.0", "fast-glob": "^3.2.12", "mkdirp": "^1.0.4", "mlly": "^0.5.14", "object-hash": "^3.0.0", "pathe": "^0.3.8", "pirates": "^4.0.5", "pkg-types": "^0.3.5", "semver": "^7.3.7", "standard-version": "^9.5.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.0", "tslib": "^2.4.0", "typescript": "^4.8.3", "vitest": "^0.23.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "packageManager": "pnpm@7.12.0", "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run --coverage"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.16.0", "_integrity": "sha512-L3BJStEf5NAqNuzrpfbN71dp43mYIcBUlCRea/vdyv5dW/AYa1d4bpelko4SHdY3I6eN9Wzyasxirj1/vv5kmg==", "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/a1c02e9123acc36763dfe175dfc5a604/jiti-1.16.0.tgz", "_from": "file:jiti-1.16.0.tgz", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-L3BJStEf5NAqNuzrpfbN71dp43mYIcBUlCRea/vdyv5dW/AYa1d4bpelko4SHdY3I6eN9Wzyasxirj1/vv5kmg==", "shasum": "f72065954446ad1866fa8d6bcc3bed3cc1cebdaa", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.16.0.tgz", "fileCount": 13, "unpackedSize": 2379603, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHxWmrhw0Gl9Swoj2gAnHXdU6U9kejmLp1+9xi+RJy+AiAnjOE//wbtUN8oVbz4SPQMiE1wdzYb0fc7LpqxyoFdZA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKDxpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+aw//budimbay9s1p5xbfOzjk2fxWaR10QJdvX81GkrvHdPXRIX4X\r\nxUAHt9mOr52LtRRbGXnPRcNo0sQTLpVpj2DPqUoTkr0iXtyxkASGhaIVXvIs\r\nAdU8wGhOqPzaszbMidBJPAssR22fPYN171zjXPAXPnrWwb7+WiRmuWyBuscP\r\n/GuWBNzi/iXIAJv5BRf0ypa8p72HVQq6tgc3gkR1bjxeXuRRs7rXGDbtfsVy\r\ndy61Qvi1HfpsCir86C6TUSgG0KEoUoalKAPgfhkWsP5aEI1Vx8+6/eK9Q3qN\r\nbvzT0KFyflWqm+h15kfvRmnRWDcvfOo23hMOy6l0dLRitHF0dYsQd8NwKuZN\r\nNFcyQTckI2VTlhW+Nx3M83tlvI02bQR2i8ZvQxYiW6hi0XDeF0oRgDMLXw93\r\n2Cnu2Ot8n8mFllqVXEWVjyMkN2KUUYkcGkLEKjqxI2CA9syqwtNLdnqcqhVj\r\n+uaBtYhrjCd9BSVY3n9k1dm/4hr3iEmh7EtOvadT0jWlx11xmTHzMEZjFPP3\r\nSAKv77BZQ/j0b7RF61VqaqmRwxPkEAuoquG1ksQtE2QE51hRx5LdImd5hlGI\r\n9KnOpXFcFhlKhH8260Ff7biJcekTg7Cswoa21LqllLwB2NaZuDekROzvLjqz\r\n+mPNWIFnsS6E8NhSa1eLKft3yhGZ7qryEOU=\r\n=hfnK\r\n-----END PGP SIGNATURE-----\r\n", "size": 504411}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.16.0_1663581289697_0.4902727775633162"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-19T10:24:56.203Z"}, "1.16.1": {"name": "jiti", "version": "1.16.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "devDependencies": {"@babel/core": "^7.20.7", "@babel/plugin-proposal-decorators": "^7.20.7", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-transform-typescript": "^7.20.7", "@babel/preset-typescript": "^7.18.6", "@babel/template": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__core": "^7.1.20", "@types/babel__template": "^7.4.1", "@types/mkdirp": "^1.0.2", "@types/node": "^18.11.18", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.26.3", "acorn": "^8.8.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "config": "^3.3.8", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.31.0", "eslint-config-unjs": "^0.0.3", "esm": "^3.2.25", "estree-walker": "^3.0.1", "execa": "^6.1.0", "fast-glob": "^3.2.12", "mkdirp": "^1.0.4", "mlly": "^1.0.0", "object-hash": "^3.0.0", "pathe": "^1.0.0", "pirates": "^4.0.5", "pkg-types": "^1.0.1", "prettier": "^2.8.1", "semver": "^7.3.8", "standard-version": "^9.5.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "tslib": "^2.4.1", "typescript": "^4.9.4", "vitest": "^0.26.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.21.0", "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c .", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run --coverage"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.16.1", "_integrity": "sha512-kJUp4Bj44uTaZAwG6R2/GjbodOWHULn8Swue0B7tY8v5BpTkUvDR+zBM5tsbC4x/jCeYDZ+mAdrUIScwIo4oPw==", "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/5a8a102bb7d42aec7c3915ac98726847/jiti-1.16.1.tgz", "_from": "file:jiti-1.16.1.tgz", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-kJUp4Bj44uTaZAwG6R2/GjbodOWHULn8Swue0B7tY8v5BpTkUvDR+zBM5tsbC4x/jCeYDZ+mAdrUIScwIo4oPw==", "shasum": "48529b29c974f374a433f3a8a2760c2471e076ab", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.16.1.tgz", "fileCount": 13, "unpackedSize": 1901593, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPXNgxrhamdJ/uIqXiZomWIFgmNWcJoV8/BjgzCpiJvAiBKr633wfWu0hM+iWfqqMH85DKRZ5QDjeBwAEmw8HToTA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtCfCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1VQ//Xjap5jiJReCUwA4L/y6PsqIn71/Jpx1INsL8TnHAO2NNs7qi\r\nEhShjVv0rDikDB4dizZP+Tp7Id+kQ881/YTLFfqDDpvoK3mQzTyIzKfcnLRn\r\nJ7jB2JT65/eUooFzZgYTVyD+FSmKhQHKWRGOzMHJtf3n5cl58jolmu9bxhM5\r\nbdnFD5BGAM9Md5tsckD4ghicLp8LPhqHUuFE49Bj6ipRfK+h6MXIzZLUVlH3\r\n6PlAxgT9lJKOKBn+gS8li/BQ2YJk8DLcbBgFhwwAQRPsp00yjbxfW5AbX7Lp\r\nqvSrRx0WuXwkKyYzvByorqi5J+oQpaBIVoiK6BY0hgDFJnM2XsgVu9EUnh5l\r\nsP4t+CAzKBI37x2l0Jn4jSTCdT+L+9059zlfh07Z+liWy3LsFWPB6x2RgXaY\r\n7IKdzzi7uL1ti7KV/UjkFFVX6x++YHmGw2EAKiVMrU0elZLqZbpqb77YpOS0\r\nwoXRZyL0MU7MrXMY92b8s0o0M7QwLMvec7tPsE7G2G0rLZ7IWsJ3eTEHsZRX\r\nOA0XENxGPJkMbzPoGZqKNka9yIEZ+CzeikSrnJAhUPr6SlBkOIdwGelShVgJ\r\nf5RdoPJxc7xR84Fe6/j8k4q4HVu/+Br1hlR9A1enytAS+7FWKG3coXIOXDZG\r\n/14GofegQng6zs5+obKjOn8J+2RH9bnBkDM=\r\n=yb5Z\r\n-----END PGP SIGNATURE-----\r\n", "size": 399924}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.16.1_1672751042141_0.7176412288962482"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-03T13:04:18.846Z"}, "1.16.2": {"name": "jiti", "version": "1.16.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "devDependencies": {"@babel/core": "^7.20.7", "@babel/plugin-proposal-decorators": "^7.20.7", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-transform-typescript": "^7.20.7", "@babel/preset-typescript": "^7.18.6", "@babel/template": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__core": "^7.1.20", "@types/babel__template": "^7.4.1", "@types/mkdirp": "^1.0.2", "@types/node": "^18.11.18", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.26.3", "acorn": "^8.8.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "config": "^3.3.8", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.31.0", "eslint-config-unjs": "^0.0.3", "esm": "^3.2.25", "estree-walker": "^3.0.1", "execa": "^6.1.0", "fast-glob": "^3.2.12", "mkdirp": "^1.0.4", "mlly": "^1.0.0", "object-hash": "^3.0.0", "pathe": "^1.0.0", "pirates": "^4.0.5", "pkg-types": "^1.0.1", "prettier": "^2.8.1", "semver": "^7.3.8", "standard-version": "^9.5.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "tslib": "^2.4.1", "typescript": "^4.9.4", "vitest": "^0.26.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.21.0", "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c .", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run --coverage"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.16.2", "_integrity": "sha512-OKBOVWmU3FxDt/UH4zSwiKPuc1nihFZiOD722FuJlngvLz2glX1v2/TJIgoA4+mrpnXxHV6dSAoCvPcYQtoG5A==", "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/c042f899d0a6fdd719867b1f8f3b6526/jiti-1.16.2.tgz", "_from": "file:jiti-1.16.2.tgz", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-OKBOVWmU3FxDt/UH4zSwiKPuc1nihFZiOD722FuJlngvLz2glX1v2/TJIgoA4+mrpnXxHV6dSAoCvPcYQtoG5A==", "shasum": "75f7a0a8fde4a0e57e576f7d329491d588db89cf", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.16.2.tgz", "fileCount": 13, "unpackedSize": 1901573, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/05fiM5tRD+NqV+GDjDXWVR1+CvnO3pigECvVyFgIaAIhAOimd9oAS4Eyt3Uwr70G2oJffDB+dXAnFNO2SU0udrYG"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvUefACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmQw/8DO9tVtxadvIm9ZmOme8l4JTGItQWq4q25P8kZbnhlz4dz9pC\r\nbbBO2qXBWOdohJ9U2kB5MDct6Tt8ZzcykehFlWhUE8gXzyqJldIq1gXXMaJj\r\nYvIcJU8Xj6WLaUrRnjrm+VtySeBXdaSuQGNPRBXcSqprgqE7NSTZVD3xZkvk\r\nPR09oykUJsmSeIt448BKX7ivau9625ANiyT/O+EmSm4/o06gvda6w4dTRr0f\r\ntqj4I6hsonpdw8OR9DYVsmuIBluNfs5ZE9A2s+2h0zKsYQCfLclDioB0h+mg\r\nYw869F6xhIFnLVpUwCxv9R5aue2s7PoOVkfk/adFxy/LHSRbFD4Wx+Q9gLsW\r\nh19s7RFLemzdKWsPwAX9wklaog83vKfg/7JIDny0vOLaNps1VzIUolL828EL\r\ndUfBhloVMELq2K/XPp8sqxz/CvAjdU3to7BU+N7yyInvRO7/8qCgfU9cFYnT\r\niENRH9riP0nT7G0bW6zWvvrabzbKMcFfrVmSXf3mygTs0MPx78OoE+m2/447\r\n78F2BXs196eR9mQ22XatS/TQjP5eLgo4aXmWrOWvkUdIXX2nCoQHY7lMH4J1\r\niUQatBpX47+3DNSuHQefemd0AeEeEMD02YWKR86ihdMsRMTOA7fEWh1Iw8aX\r\nyduj1jdfdRSxwgwD1Q2TfC03CqKtJh+w9T4=\r\n=Wfwx\r\n-----END PGP SIGNATURE-----\r\n", "size": 399918}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.16.2_1673349022993_0.30084561987813907"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-10T11:10:23.241Z", "publish_time": 1673349023241}, "1.17.0": {"name": "jiti", "version": "1.17.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/plugin-proposal-decorators": "^7.20.13", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-transform-typescript": "^7.20.13", "@babel/preset-typescript": "^7.18.6", "@babel/template": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__core": "^7.20.0", "@types/babel__template": "^7.4.1", "@types/mkdirp": "^1.0.2", "@types/node": "^18.11.18", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.28.3", "acorn": "^8.8.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "config": "^3.3.9", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.33.0", "eslint-config-unjs": "^0.1.0", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^6.1.0", "fast-glob": "^3.2.12", "mkdirp": "^1.0.4", "mlly": "^1.1.0", "object-hash": "^3.0.0", "pathe": "^1.1.0", "pirates": "^4.0.5", "pkg-types": "^1.0.1", "prettier": "^2.8.3", "semver": "^7.3.8", "standard-version": "^9.5.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vitest": "^0.28.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.26.2", "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run --coverage"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.17.0", "_integrity": "sha512-CByzPgFqYoB9odEeef7GNmQ3S5THIBOtzRYoSCya2Sv27AuQxy2jgoFjQ6VTF53xsq1MXRm+YWNvOoDHUAteOw==", "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/2a073d9d4e68a90412a387f9f601dbf9/jiti-1.17.0.tgz", "_from": "file:jiti-1.17.0.tgz", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-CByzPgFqYoB9odEeef7GNmQ3S5THIBOtzRYoSCya2Sv27AuQxy2jgoFjQ6VTF53xsq1MXRm+YWNvOoDHUAteOw==", "shasum": "9a4e1787b9d83e594a5ad27cdf9c9ab555112ac1", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.17.0.tgz", "fileCount": 14, "unpackedSize": 1915798, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCH8OeQ/0rUjn6UqdlyCzthnv2QUcZ7qmYSjE4PwiLegIhALoKDUBeeVA2/KqIDDYZbRD8ETzSnfplBL74UnDJ+FFx"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5BdlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMJRAAjB+upvZbhrHBBCo0D+O8s+l+FpZcH9n2IBprOjvVcgNtj27a\r\nauuABZzd5HmcRkLAsLtwJQYel8S2jSFvuTuc6uAwKXZLDS/e3WCS20DWviuv\r\n2qQOxxnm3R2wzl1VUEtGUhu3nqf81zBYQGL3v2dW3gY1k9WRneWvd4FOEM5M\r\n9mE4HAMXE4dJi1jve6r44QdP/RYLdh0fCIZXypnE18/5HQjT38neJdGm9fAM\r\nwjCPT84Ss0JpbOiU5BGrBKdRGTFdaa7yIW8BgFksnKGUA+G+41nAdT3GkDy6\r\nqtTu0+ZITYp2wkhQ9emNKl8BGawWXkQ5QzA4TN91MIaP4pSFq52MQTcM9CKF\r\nglMQ1jdcfNZ4bHLQfWeA2ixsZEbeGEQ2f2fJDoJMvZql2ScIOrmSLjSY0UlW\r\nxr7cMXI4Ywdcgr/w6grEOB7Mmn6fw+lQs26mgQmVIY/I2+Ov+woJFvXyKeOJ\r\nra6sNb0s9DBUTL0L32seKTDB+o69dyHiIPQNUU/A2wLY2J1WACAVrbYaFvd9\r\nAVdUXi4fMB4roPNVpiEw/7TZYGJo62Qa1DpCwQYCJckCWA6G0Oy+egy9YlBP\r\nTun70YZgPtfDtWP5up8C7Ksxqt65UwDvV9MMKSaM0ouoi0eJD62z4x+ee5Dm\r\nFnT1rjKUUTqmpsyQy62cZadL3mHQ/SM32Lc=\r\n=0Cob\r\n-----END PGP SIGNATURE-----\r\n", "size": 402969}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.17.0_1675892581109_0.15215048165070977"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-08T21:43:01.379Z", "publish_time": 1675892581379}, "1.17.1": {"name": "jiti", "version": "1.17.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/plugin-proposal-decorators": "^7.20.13", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-transform-typescript": "^7.20.13", "@babel/preset-typescript": "^7.18.6", "@babel/template": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__core": "^7.20.0", "@types/babel__template": "^7.4.1", "@types/node": "^18.13.0", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.28.5", "acorn": "^8.8.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.4.1", "config": "^3.3.9", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.34.0", "eslint-config-unjs": "^0.1.0", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.0.0", "fast-glob": "^3.2.12", "mlly": "^1.1.1", "object-hash": "^3.0.0", "pathe": "^1.1.0", "pirates": "^4.0.5", "pkg-types": "^1.0.2", "prettier": "^2.8.4", "semver": "^7.3.8", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vitest": "^0.28.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.27.0", "gitHead": "9b698b70392371486b988c1177ee598563874dfc", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.17.1", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-NZIITw8uZQFuzQimqjUxIrIcEdxYDFIe/0xYfIlVXTkiBjjyBEvgasj5bb0/cHtPRD/NziPbT312sFrkI5ALpw==", "shasum": "264daa43ee89a03e8be28c3d712ccc4eb9f1e8ed", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.17.1.tgz", "fileCount": 13, "unpackedSize": 1909689, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIel0JVx6LcvL0f1AwyyfYqh2kaf+0deBXvBzYdGD2wgIhAOZj4dsIJzkumDvc/hvL1fuTpSVIuhppvzoZ3GSCcNG2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7sl3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo8g/9EH6QWx/lqQlag6yybI+W52GRxS+szePOpP8AJgHy7778fwrc\r\nBus7hUzzOF1ZWX6dGm4T9TvsSg7yIbxt/sjUP5e1pnVAuhDdBS6Od8hD+IdK\r\njn6xbqnywhSz98ecWZD8Rxp+E50mZGunihXqsZzXOrkzlRH+Nt0LyVUw+FT7\r\nyqpNzpeoNj0dMdG2+Z/aqhQ7ynB+aQ8jGn4BStZ5JpYWaDoLj2i1gyEbJbww\r\n6zP9+ZfMObNCV7LdH7vLbVePY0mS4HwBZt2D18dwWyfKn088EDJXlPPmF/Ty\r\n/kgdxGWNg8ljYdlOHXCANkan8T67rVYvgG9dyM0GLTkf/pDHQy9swZ15COIC\r\nM4ygrnpPypOWeWO3QLc4FZc9k90EIK6vUIyK+TUEkTWTK8+odZyQpIS1dgAi\r\nxyB62KXdoDTgULn97zR0B/h1Mc6kNBvjQTB2g99oJtPuEyFx/tosi6Uc9X+8\r\n8KVpg1s6JpAfKJ4Y/r+YMHFF1jNzwDRcNlF/tAWQryTyDPw07cB4Jx5qA+hD\r\nfaFp0daCKKI3nPHf9auiLnAEWoXs6Uc1k21Ec7LORNhnpgjigz7btBNecAn1\r\nS7GzEpJH3GwXqnXycW8hNa00wR/eAWmY8I1scB3LAkgEtCc08AFotFcWaZ9q\r\nzq0wU9rzCLlAmirfFaiI19G75vG3HndXM+I=\r\n=OyCm\r\n-----END PGP SIGNATURE-----\r\n", "size": 399555}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.17.1_1676593527252_0.39247597506202636"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-17T00:25:27.505Z", "publish_time": 1676593527505}, "1.17.2": {"name": "jiti", "version": "1.17.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-typescript": "^7.21.0", "@babel/preset-typescript": "^7.21.0", "@babel/template": "^7.20.7", "@babel/types": "^7.21.2", "@types/babel__core": "^7.20.0", "@types/babel__template": "^7.4.1", "@types/node": "^18.14.6", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.29.2", "acorn": "^8.8.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.1", "config": "^3.3.9", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.35.0", "eslint-config-unjs": "^0.1.0", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.0.0", "fast-glob": "^3.2.12", "mlly": "^1.1.1", "object-hash": "^3.0.0", "pathe": "^1.1.0", "pirates": "^4.0.5", "pkg-types": "^1.0.2", "prettier": "^2.8.4", "reflect-metadata": "^0.1.13", "semver": "^7.3.8", "std-env": "^3.3.2", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vite": "^4.1.4", "vitest": "^0.29.2", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.29.0", "gitHead": "671d06904a8198e379bb46b3723282571c672fd1", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.17.2", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-Xf0nU8+8wuiQpLcqdb2HRyHqYwGk2Pd+F7kstyp20ZuqTyCmB9dqpX2NxaxFc1kovraa2bG6c1RL3W7XfapiZg==", "shasum": "9e193d171c76b1c518a46c243c410c0462fe22d1", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.17.2.tgz", "fileCount": 13, "unpackedSize": 1947070, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDy12YvSNP6bTjcpk8SMRBbB+bCy4sg+DW5ORRf/OJkogIgPUL2iXF7DXMVvgtmyrXZI/u0EFzBcjZKFwVK1s7vhvM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBj4GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQLQ//dX5o7Dt4icRLtx4M9jkmv9FixkQM0V/5tcpmAXq1zsKJIKpS\r\nSL7MN9YEAlDPFG1z8CaRyKKMWTZlWHhgF72pdkBmMZ00O7gcBLMh8/BiMeXN\r\nHx8Vr2TClP2KE34f6ikCkwQmPl0PA0VLtA0URxX9D9vaXPFEwmIW9zndkqAV\r\nySl7hPLnyy4+GF+2VHvsFNJlVcbYFVshc6foneLFn0XEkPoYVt5Z/A2nSTpz\r\ndmwL1w2JNltjcyddhy6+3B71nhOsU3ki3ZzNgpVE5WZ7BqbWHjF4OLiEEOXx\r\nf89D4bk5sGb4XtW6Y357HAMXIx4iLjJuTICeFa1LMTbJLaRhMHkjb6eF6T/U\r\nAcu++DLD+E6jgPpMHMhQvtJcculKd/Fda1eZckXTm+mTZlSQfEeqJQdr4EBk\r\nRPvdyTPcs9Dj98+lA/rWmZ/jvFobGPTcQzQ32H4gZ44zNt4mb6wFxG6oDCWb\r\nzngqafvS0o1Q/fGn2DwxywsJEFDf4Vh5ODdENAoWPOwdzHkvkNi9mwuheSfY\r\nRkaMNdllTLufUTjsz2MgpM3ynDgQ1djtg1I5xsG/qH5vmNqmtxvb941vjNpD\r\ngpPOFN3EVc/cNfB8va0yji50wve9r2b1lmT2nHKpeYxjLbWDccx1x+SXL0jF\r\nM0Ne5HolNlm612QGSawdI2jwmdExfq3PJd0=\r\n=cf46\r\n-----END PGP SIGNATURE-----\r\n", "size": 406356}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.17.2_1678130693984_0.41488722831240454"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-06T19:24:54.176Z", "publish_time": 1678130694176}, "1.18.0": {"name": "jiti", "version": "1.18.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/preset-typescript": "^7.21.0", "@babel/template": "^7.20.7", "@babel/types": "^7.21.3", "@types/babel__core": "^7.20.0", "@types/babel__template": "^7.4.1", "@types/node": "^18.15.3", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.29.2", "acorn": "^8.8.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.1", "config": "^3.3.9", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.36.0", "eslint-config-unjs": "^0.1.0", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.1.1", "fast-glob": "^3.2.12", "mlly": "^1.2.0", "object-hash": "^3.0.0", "pathe": "^1.1.0", "pirates": "^4.0.5", "pkg-types": "^1.0.2", "prettier": "^2.8.4", "reflect-metadata": "^0.1.13", "semver": "^7.3.8", "std-env": "^3.3.2", "terser-webpack-plugin": "^5.3.7", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vite": "^4.1.4", "vitest": "^0.29.2", "webpack": "^5.76.1", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.29.3", "gitHead": "c65d92cb7ce84d3536ef56da970fb3dcc5624976", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.18.0", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-82fU0lIX1aWCJjZSbqjWlS8HlI286ARMmbx7yrgVzakqA2L9dlj7KDeSsTmz1zDWEF88kmCzU0YHScfJZYyMqg==", "shasum": "87ce91460a807a38279ee1cc26a489ea313bd75c", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.18.0.tgz", "fileCount": 13, "unpackedSize": 1946406, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuAHrZBxsTK207qcjJ5Q58IcWnEmw+MSpKSses6PhbaAIgEpZIxnbeAb312pYjUeh+t37AphttoCypHiAPMLCjk7M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEeDUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG4Q//eK25bePddOdqj2mj0jXG0/eCAGbzvWBJnWZS2G2MC3isaPqk\r\nZ0fIHffTBgCeFdORkFHfzV8Pp6Yf4MSGBq84bbM2Td5FkWr9incn1xHtAwni\r\nJBRuduP88c1o7K9GL9D0ahl6mQLTJO81nmbzeqMvcb9o02w3Tma7HvduCuub\r\nXwIreXBgiYYYj/sJdFytBCgW9UXvzpU2kuQf8AS49SzMAf/kEYYQ9yZj4EBC\r\n3TaD7iqc9zquMCaxWDvYD+RlTm22/Me9nqZFNBWi7d6FMWhnTZX+OP2sbrYQ\r\ncfkUc9jm77Pt8qcG/U4en2py9MtQwW/Mew9JbgUyr0doTiKChQBX5M3GY6fn\r\nZsw+jfq/e0H+3pjJdtIg7r0163JbrgBXBrS53FZn56x7Rs+KfoLia4I3Bg72\r\nNAw5X11uyGyWeWaMl3Nm/01pRiHM/TSLs44K5EI/izHnw7uoLNBDV6FpIBEd\r\nQJY4Ez4+/uvr75CSSlRZE5spxzBwMt16yjDhRdVUd1shMYI29ZdBd36G0w3j\r\ncwOe2Jb59u7lQ9tA8op8E/VE7AzN6/Xk3CUO5OmC3A8HTFB1s2aKzBIgH7EI\r\n+46LBw9OgnKdFYul2Qut9ZoQGbF9dy1/J3/H/kM6ybCnv5AZPcZr7Xn8PN7z\r\nFXsWJPvTM0OjUGnwpjCOoufu8P1r9kpMJac=\r\n=rVrb\r\n-----END PGP SIGNATURE-----\r\n", "size": 406497}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.18.0_1678893268508_0.07444152211380972"}, "_hasShrinkwrap": false, "deprecated": "please use 1.18.2 or later", "_cnpmcore_publish_time": "2023-03-15T15:14:28.750Z", "publish_time": 1678893268750}, "1.18.1": {"name": "jiti", "version": "1.18.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/preset-typescript": "^7.21.0", "@babel/template": "^7.20.7", "@babel/types": "^7.21.3", "@types/babel__core": "^7.20.0", "@types/babel__template": "^7.4.1", "@types/node": "^18.15.3", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.29.2", "acorn": "^8.8.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.1", "config": "^3.3.9", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.36.0", "eslint-config-unjs": "^0.1.0", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.1.1", "fast-glob": "^3.2.12", "mlly": "^1.2.0", "object-hash": "^3.0.0", "pathe": "^1.1.0", "pirates": "^4.0.5", "pkg-types": "^1.0.2", "prettier": "^2.8.4", "reflect-metadata": "^0.1.13", "semver": "^7.3.8", "std-env": "^3.3.2", "terser-webpack-plugin": "^5.3.7", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vite": "^4.1.4", "vitest": "^0.29.2", "webpack": "^5.76.1", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.29.3", "gitHead": "93512f97a92a40450c4908ec3870bbb579a7a3f1", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.18.1", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-3YoLqT3pzjfwhVaD3nvBKb2T7897c/X1IHctAX7Qpm5pHlaTmdhvXc8lfz2xH4dNIHRungp9CwTk+bnwBU6CQQ==", "shasum": "fb2e9033cc60de257926054fd3f0780893fb9e6b", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.18.1.tgz", "fileCount": 14, "unpackedSize": 2706150, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBf72dV6TC5zMapSZzIcx6FaIz/xQAZJwP/R1zhEMJOOAiEAtkRPNF7cYYx9VS1xm6A2/iMxZK+ykdow151lICsv8ZQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEeFFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovrRAAjIqpxRflEP3bGTAMyLa2iAKCb5+2UCY+iv3VQ/DB7999bf/a\r\nnEGRuH/njs5PNwjdIEDT/0g+L+wOOMXB5I8o73u3j9VZ03MYfwN9YwCGDS65\r\nfX2Z3AWUU0Jn3+6BCafOcbBMWs/c7p7d3+mfgAgW8e6P0DFAT1icI2TCK5B9\r\nvT72cVVPFAyueQZT+2LRSX/Ll7QWkDxImMKWVyx0NjM0A8jAZSHp7r67UGfT\r\njzLX1LWh3nNZ9ZbHjWxMSGelCCKrbAYt37I0RUkyyFAzMIFgC0sq0P8NVBHO\r\n7VnYrdwcoDRPqyW9+xKaDFZsNpXqtwfLCy9vznMgF/MGxRq2EtM/R9Nmj4NG\r\nBF8M5iLB7q6YonfAPebAbJVufYeQ/V33+EBxAGui3g7z2zUOuc3ict0MEl71\r\n+QsFIoUjqdCSpxzD5UEY8UIGtCB90oATVBLjHKngIgprE9ExJEKJ0XQWfWlh\r\nKbVjIZD0Ia0mM8/ffE0Fc0VJcviKzM1fPIEWcrdEIkLJN+oTzugNkzmQb6tc\r\nsNDFR2tTz+n9KBtPMnMmeI+xdi96LmteJb7E08W5oBbW0qp+LKwxrjaosO1L\r\nOnqe7C4bX4h7W2dL6iv8NoWTxGq1TYkXLlQ/vHonSF8J7cwDUgrCuvP+I9f8\r\n1e/7AaUcgwuFrNCfnV61U795VI8PTAhw9ec=\r\n=xKS2\r\n-----END PGP SIGNATURE-----\r\n", "size": 490274}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.18.1_1678893381383_0.5618911374679363"}, "_hasShrinkwrap": false, "deprecated": "please use 1.18.2 or later", "_cnpmcore_publish_time": "2023-03-15T15:16:21.554Z", "publish_time": 1678893381554}, "1.18.2": {"name": "jiti", "version": "1.18.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/preset-typescript": "^7.21.0", "@babel/template": "^7.20.7", "@babel/types": "^7.21.3", "@types/babel__core": "^7.20.0", "@types/babel__template": "^7.4.1", "@types/node": "^18.15.3", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.3.13", "@vitest/coverage-c8": "^0.29.2", "acorn": "^8.8.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.1", "config": "^3.3.9", "create-require": "^1.1.1", "cross-env": "^7.0.3", "destr": "^1.2.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.36.0", "eslint-config-unjs": "^0.1.0", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.1.1", "fast-glob": "^3.2.12", "mlly": "^1.2.0", "object-hash": "^3.0.0", "pathe": "^1.1.0", "pirates": "^4.0.5", "pkg-types": "^1.0.2", "prettier": "^2.8.4", "reflect-metadata": "^0.1.13", "semver": "^7.3.8", "std-env": "^3.3.2", "terser-webpack-plugin": "^5.3.7", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^4.9.5", "vite": "^4.1.4", "vitest": "^0.29.2", "webpack": "^5.76.1", "webpack-cli": "^5.0.1"}, "packageManager": "pnpm@7.29.3", "gitHead": "ce030f6fb98bbbb484c45f5a7fb1bf0745aa822b", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.18.2", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-QAdOptna2NYiSSpv0O/BwoHBSmz4YhpzJHyi+fnMRTXFjp7B8i/YG5Z8IfusxB1ufjcD2Sre1F3R+nX3fvy7gg==", "shasum": "80c3ef3d486ebf2450d9335122b32d121f2a83cd", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.18.2.tgz", "fileCount": 14, "unpackedSize": 1948699, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDEgpdxWh6gkWAX+eVccqGqEfXBIw8WL6UlzHDr8MZoJAiEAjYh569x9cAkYJ8WAQwlX/v8ZwGdsTc44rW0xnw1luKE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEeGsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpd5A//XqmTvtVsx6BAt3n/1r6qXIrAjmEfZm3T4ju9z9oytsNlpAGj\r\nwK8BAg2vtT1XUYPzMYer7Brh3qkVMtyMhtvxAN+rZU/zgK3pd81yyYz3/nDD\r\nP1L0ZrM1eKV5INzmLgk0gnva3l+fX9SiB7hO8VQGsoKMR1ol2ByKDPhCTOOS\r\nyjFmd67Ev7TOEC8neQuK0JhmLmq9wA1Yqg7g9E8rsJaaUxSww6v4z6oP6pjm\r\nXvKH4hY7w7wEB3olW6qezPA4aHsIs84tXngXZXd4owA9ZmSMLw0sGcKkP320\r\nKSw8Na5ByUWi8tHkBC+cRlC81pTPAhReZMgUKDI6+1Wri5r/ftXNvqVVwhmX\r\nHpfvNW1Zfw1MbMZ+F9MyRn80+knazeGDeGd+OHT8gsGUX/YOPwVe9DbkkP2d\r\nQorxTRvsx73NC081PoV6Zl1XC39JCDt5LqqeHydSQLX7whTL3wuBxcI0YCJ7\r\nHNKKEg5MbrnvBgQOtK0I6uz+3tpvMBR8N3ss3cia0UREt3ruLVZhK+p1GaQ8\r\nvDidQ+vKqquRTg5l98uhR7wYkaj4p08/DRNUQocjXHDdfZ4GYU4xlArnZ3yk\r\nKgdgAfM2impQnsFdcPRJcWzMRu+1OwzsOq0Ymeke1KPp/WZP/2GpZz9s7san\r\nVB6gOqEPUmEOIVhDf1QCR5sC3y7JGxtyqzg=\r\n=ASn6\r\n-----END PGP SIGNATURE-----\r\n", "size": 407517}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.18.2_1678893484300_0.2993525407626698"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-15T15:18:04.541Z", "publish_time": 1678893484541}, "1.19.0": {"name": "jiti", "version": "1.19.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-proposal-decorators": "^7.22.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@babel/template": "^7.22.5", "@babel/types": "^7.22.5", "@types/babel__core": "^7.20.1", "@types/babel__template": "^7.4.1", "@types/node": "^20.3.3", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.0", "@vitest/coverage-v8": "^0.32.4", "acorn": "^8.9.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.4", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.0", "escape-string-regexp": "^5.0.0", "eslint": "^8.44.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.1.1", "fast-glob": "^3.3.0", "mlly": "^1.4.0", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^2.8.8", "reflect-metadata": "^0.1.13", "semver": "^7.5.3", "std-env": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "tslib": "^2.6.0", "typescript": "^5.1.6", "vite": "^4.3.9", "vitest": "^0.32.4", "webpack": "^5.88.1", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.6.6", "gitHead": "adcde06eec80a7aa9df2b95f9a448953d2a7f5f6", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.19.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-93OnYCvFp88SbkJstwqwGOxU1POpIqImAlGKZLAXuZRJrvfRtKUrcAwFaGuTcrlglkQXrV7QvHkLFizFWeRU5g==", "shasum": "9bf45a47670f0f7d0c9c53325c8216f90353cd2f", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.19.0.tgz", "fileCount": 14, "unpackedSize": 1918881, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvpwXM1QpjXqBECqb2yF6t6elwlLvoikWJ3hvaA4q7IAIhAJ+BaQYOLYlX02fyi1vIuDsq01uHjtCur+bOzyOZC1Zz"}], "size": 399117}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.19.0_1688473284222_0.3719814179268235"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-04T12:21:24.461Z", "publish_time": 1688473284461, "_source_registry_name": "default", "deprecated": "please use jiti@1.19.1 or later"}, "1.19.1": {"name": "jiti", "version": "1.19.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-proposal-decorators": "^7.22.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@babel/template": "^7.22.5", "@babel/types": "^7.22.5", "@types/babel__core": "^7.20.1", "@types/babel__template": "^7.4.1", "@types/node": "^20.3.3", "@types/object-hash": "^3.0.2", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.0", "@vitest/coverage-v8": "^0.32.4", "acorn": "^8.9.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.4", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.0", "escape-string-regexp": "^5.0.0", "eslint": "^8.44.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.1.1", "fast-glob": "^3.3.0", "mlly": "^1.4.0", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^2.8.8", "reflect-metadata": "^0.1.13", "semver": "^7.5.3", "std-env": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "tslib": "^2.6.0", "typescript": "^5.1.6", "vite": "^4.3.9", "vitest": "^0.32.4", "webpack": "^5.88.1", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.6.6", "gitHead": "79eeafb67b1768531010d41b59fcda3d46f8f730", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.19.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-oVhqoRDaBXf7sjkll95LHVS6Myyyb1zaunVwk4Z0+WPSW4gjS0pl01zYKHScTuyEhQsFxV5L4DR5r+YqSyqyyg==", "shasum": "fa99e4b76a23053e0e7cde098efe1704a14c16f1", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.19.1.tgz", "fileCount": 14, "unpackedSize": 1918888, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG+j0X+94AbTTqmiHaJhktt8s9W91EF0XT0sS9ryOBdtAiBQKhTcBYpJ6xU9p0V2KePqSrZvtvDGDwH4qXD5AZCnrA=="}], "size": 399118}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.19.1_1688478588583_0.5306607167510518"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-04T13:49:48.769Z", "publish_time": 1688478588769, "_source_registry_name": "default"}, "1.19.2": {"name": "jiti", "version": "1.19.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/plugin-proposal-decorators": "^7.22.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.10", "@babel/preset-typescript": "^7.22.5", "@babel/template": "^7.22.5", "@babel/types": "^7.22.10", "@types/babel__core": "^7.20.1", "@types/babel__template": "^7.4.1", "@types/node": "^20.5.0", "@types/object-hash": "^3.0.3", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.0", "@vitest/coverage-v8": "^0.34.2", "acorn": "^8.10.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.4", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.1", "escape-string-regexp": "^5.0.0", "eslint": "^8.47.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.2.0", "fast-glob": "^3.3.1", "mlly": "^1.4.0", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^3.0.2", "reflect-metadata": "^0.1.13", "semver": "^7.5.4", "std-env": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "tslib": "^2.6.1", "typescript": "^5.1.6", "vite": "^4.4.9", "vitest": "^0.34.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.6.12", "gitHead": "035c879724f6bad821099dfe8aff4e9141703a3b", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.19.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-UgPQy6IN/a/5DRAVkNRgoyVQBC4BqjN/ALFKnTlCR6mJIBtEj0/X52QCMckszSNbY7RbSNa1PzhhxQzROiGHcA==", "shasum": "1bd97b4f40feb7810a14b820c2ceea62adc06cb5", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.19.2.tgz", "fileCount": 14, "unpackedSize": 1918236, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFWiv55hTZH86aeARigVkxtqNIy2y9CBx+kgnxjDFpHIAiA5sVSmpINDWDomzCoS72dVxG9qJJ1qCWx0/9aShdkkKQ=="}], "size": 399352}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.19.2_1692357279470_0.3931637834114208"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-18T11:14:39.747Z", "publish_time": 1692357279747, "_source_registry_name": "default"}, "1.19.3": {"name": "jiti", "version": "1.19.3", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/plugin-proposal-decorators": "^7.22.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.10", "@babel/preset-typescript": "^7.22.5", "@babel/template": "^7.22.5", "@babel/types": "^7.22.10", "@types/babel__core": "^7.20.1", "@types/babel__template": "^7.4.1", "@types/node": "^20.5.0", "@types/object-hash": "^3.0.3", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.0", "@vitest/coverage-v8": "^0.34.2", "acorn": "^8.10.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.4", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.1", "escape-string-regexp": "^5.0.0", "eslint": "^8.47.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^7.2.0", "fast-glob": "^3.3.1", "mlly": "^1.4.0", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^3.0.2", "reflect-metadata": "^0.1.13", "semver": "^7.5.4", "std-env": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "tslib": "^2.6.1", "typescript": "^5.1.6", "vite": "^4.4.9", "vitest": "^0.34.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.6.12", "gitHead": "4ae58425027343b283a71a08cf964e4d7356c9fd", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.19.3", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-5eEbBDQT/jF1xg6l36P+mWGGoH9Spuy0PCdSr2dtWRDGC6ph/w9ZCL4lmESW8f8F7MwT3XKescfP0wnZWAKL9w==", "shasum": "ef554f76465b3c2b222dc077834a71f0d4a37569", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.19.3.tgz", "fileCount": 14, "unpackedSize": 1918270, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+ZweO12NXvxTgGUdce5U3mRGUwWfA4glyeNmvXhWy1AiAsXdKzXfoH43dLaZjQ1VpIrgDMquetm/eAzTu9nOXCmg=="}], "size": 399354}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.19.3_1692360071877_0.06302320475706646"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-18T12:01:12.332Z", "publish_time": 1692360072332, "_source_registry_name": "default"}, "1.20.0": {"name": "jiti", "version": "1.20.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.22.15", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-export-namespace-from": "^7.18.9", "@babel/plugin-transform-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-transform-optional-chaining": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.15", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/preset-typescript": "^7.22.15", "@babel/template": "^7.22.15", "@babel/types": "^7.22.15", "@types/babel__core": "^7.20.1", "@types/babel__template": "^7.4.1", "@types/node": "^20.5.9", "@types/object-hash": "^3.0.4", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.1", "@vitest/coverage-v8": "^0.34.3", "acorn": "^8.10.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.1", "escape-string-regexp": "^5.0.0", "eslint": "^8.48.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^8.0.1", "fast-glob": "^3.3.1", "mlly": "^1.4.2", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^3.0.3", "reflect-metadata": "^0.1.13", "semver": "^7.5.4", "std-env": "^3.4.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^4.4.9", "vitest": "^0.34.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.7.4", "gitHead": "2f7ccf29c384d67a1bb131b7b599269dda77f2ca", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.20.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-3TV69ZbrvV6U5DfQimop50jE9Dl6J8O1ja1dvBbMba/sZ3YBEQqJ2VZRoQPVnhlzjNtU1vaXRZVrVjU4qtm8yA==", "shasum": "2d823b5852ee8963585c8dd8b7992ffc1ae83b42", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.20.0.tgz", "fileCount": 14, "unpackedSize": 1899788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHXAJ++G4BqNKeo8uTV5Tx9wiiSGhNhdCZ7K04lGk9DxAiB/QGFeIiTqkVgimYofeVqx/mGh96VoqjJPgYMowzuNgA=="}], "size": 399726}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.20.0_1694082524956_0.18578748470117912"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-07T10:28:45.294Z", "publish_time": 1694082525294, "_source_registry_name": "default"}, "1.21.0": {"name": "jiti", "version": "1.21.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-export-namespace-from": "^7.22.11", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.11", "@babel/plugin-transform-optional-chaining": "^7.23.0", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/preset-typescript": "^7.23.2", "@babel/template": "^7.22.15", "@babel/types": "^7.23.0", "@types/babel__core": "^7.20.3", "@types/babel__template": "^7.4.3", "@types/node": "^20.8.9", "@types/object-hash": "^3.0.5", "@types/resolve": "^1.20.4", "@types/semver": "^7.5.4", "@vitest/coverage-v8": "^0.34.6", "acorn": "^8.11.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.52.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^8.0.1", "fast-glob": "^3.3.1", "mlly": "^1.4.2", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^3.0.3", "reflect-metadata": "^0.1.13", "semver": "^7.5.4", "std-env": "^3.4.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.5.0", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.8.0", "gitHead": "16d41590db10a5c8b941478f84be6e98b08b4f76", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_id": "jiti@1.21.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==", "shasum": "7c97f8fe045724e136a397f7340475244156105d", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.0.tgz", "fileCount": 14, "unpackedSize": 1913525, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH8Zmp+KX72oGZBvtRfazTMLJcx5YuWR6xax4oNZ3zS7AiA4J+BahmRXMgTkdeuquNPQwgdUCICygUanbBXhW1khRA=="}], "size": 402841}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.21.0_1698687215893_0.18731224621416098"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-30T17:33:36.397Z", "publish_time": 1698687216397, "_source_registry_name": "default"}, "1.21.1": {"name": "jiti", "version": "1.21.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.1", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1", "@babel/plugin-transform-optional-chaining": "^7.24.5", "@babel/plugin-transform-typescript": "^7.24.5", "@babel/preset-typescript": "^7.24.1", "@babel/template": "^7.24.0", "@babel/types": "^7.24.5", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.12.8", "@types/object-hash": "^3.0.6", "@types/resolve": "^1.20.6", "@types/semver": "^7.5.8", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.11.3", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.11", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.2.0", "eslint-config-unjs": "^0.3.2", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^9.1.0", "fast-glob": "^3.3.2", "mlly": "^1.7.0", "object-hash": "^3.0.0", "pathe": "^1.1.2", "pirates": "^4.0.6", "pkg-types": "^1.1.0", "prettier": "^3.2.5", "reflect-metadata": "^0.2.1", "semver": "^7.6.0", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "tslib": "^2.6.2", "typescript": "^5.4.5", "vite": "^5.2.11", "vitest": "^1.6.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@9.1.4", "_id": "jiti@1.21.1", "gitHead": "9727cf0b5f655c2a2154d8062a77d566c7325f69", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-KMXpzEJMsOFyRj6ZpDTnnlJrdr9umUY+eut5vlRvjVixohitnRFIRTFw9MEu9zPlBxTHZo6xD5ftKYiQZuJYQw==", "shasum": "84639a2debaececa42fcb2a313b5b80716c894ab", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.1.tgz", "fileCount": 14, "unpackedSize": 1949031, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqNCqbT/pVOrVcbMi7HKO21rxRkFFSLz7jQ5Fgd9FTiAiEAhDv5UlR2J59QVsNT1yDHXE1ovKLKcy/Z/iQsHsl4Mlw="}], "size": 411811}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.21.1_1717546954868_0.9799424202303704"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T00:22:35.129Z", "publish_time": 1717546955129, "_source_registry_name": "default"}, "1.21.2": {"name": "jiti", "version": "1.21.2", "license": "MIT", "_id": "jiti@1.21.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "262ea386ac29b6d9e74ed0329ed6880845e1d25e", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.2.tgz", "fileCount": 14, "integrity": "sha512-U<PERSON><PERSON><PERSON>yi4HPsFoxcsJ0uV4qDVBDpPWVI7D0JOaeCUnhcEpjWFQIeSu8Ecpu2mAZS6ee3QGtFewR3UOp60YiveZQ==", "signatures": [{"sig": "MEQCIHLDyg5/DvD+6TR8EEgHlCmXvc0d24Pc6ijoLDHgQN+KAiBBzBU9lFu7vS8ww+GAF0rC5SCdcqf/JqYBtQbDYE4THA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1948248, "size": 411589}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "9ca70a1d129f6a6af4d08f27faedbd40b5a4eb30", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "resolutions": {"mlly": "1.4.2"}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.4", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.0", "vite": "^5.2.11", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.2", "config": "^3.3.11", "eslint": "^9.2.0", "semver": "^7.6.0", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.2.5", "fast-glob": "^3.3.2", "pkg-types": "^1.1.0", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.5", "@types/node": "^20.12.8", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.5", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.0", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-typescript": "^7.24.5", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-optional-chaining": "^7.24.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.2_1717583555902_0.2303907850205864", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-05T10:32:36.101Z", "publish_time": 1717583556101, "_source_registry_name": "default"}, "1.21.3": {"name": "jiti", "version": "1.21.3", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.1", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1", "@babel/plugin-transform-optional-chaining": "^7.24.5", "@babel/plugin-transform-typescript": "^7.24.5", "@babel/preset-typescript": "^7.24.1", "@babel/template": "^7.24.0", "@babel/types": "^7.24.5", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.12.8", "@types/object-hash": "^3.0.6", "@types/resolve": "^1.20.6", "@types/semver": "^7.5.8", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.11.3", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.11", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.2.0", "eslint-config-unjs": "^0.3.2", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^9.1.0", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "object-hash": "^3.0.0", "pathe": "^1.1.2", "pirates": "^4.0.6", "pkg-types": "^1.1.0", "prettier": "^3.2.5", "reflect-metadata": "^0.2.1", "semver": "^7.6.0", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "tslib": "^2.6.2", "typescript": "^5.4.5", "vite": "^5.2.11", "vitest": "^1.6.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@9.1.4", "_id": "jiti@1.21.3", "gitHead": "c7872b20b768d94a21c9959ab4b2c7aa45279eb0", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-uy2bNX5zQ+tESe+TiC7ilGRz8AtRGmnJH55NC5S0nSUjvvvM2hJHmefHErugGXN4pNv4Qx7vLsnNw9qJ9mtIsw==", "shasum": "b2adb07489d7629b344d59082bbedb8c21c5f755", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.3.tgz", "fileCount": 14, "unpackedSize": 1949115, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEWCJxBMhn5V+kmXjaajJZPthGB12YS3dbPTGVd2lysdAiBQGE59d+HgSGfhpHVPINQe3/LgLnd3Ux2+VQX6QW1keQ=="}], "size": 411836}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.21.3_1717585841765_0.2716802687553339"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T11:10:41.961Z", "publish_time": 1717585841961, "_source_registry_name": "default"}, "1.21.4": {"name": "jiti", "version": "1.21.4", "license": "MIT", "_id": "jiti@1.21.4", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "69e9988f32054bfbaf6d60495eb3f9fce0662922", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.4.tgz", "fileCount": 14, "integrity": "sha512-DUP4hpE2Of384xS46IiRm1JK3r4Ix4ADQI5k65y4joiZF7Oeo2ySdYG7PbqioljFalu1ndzto0Sb5IfExYliYA==", "signatures": [{"sig": "MEYCIQCFq6xIHPPH19NUP5OngNzYE4kp3eHWfurb9NiZXoSUxwIhALlE6iGkr0KLv1ewUgtC6cnfTr1CZ7zj2nHEStgrsFPx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1947746, "size": 411727}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "7faee9bb18d03f21c0a95d4133c1bbf50594ca73", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.2.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.2.12", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.3", "config": "^3.3.11", "eslint": "^9.4.0", "semver": "^7.6.2", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.7", "@types/node": "^20.14.2", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.7", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.4_1718026565817_0.7661058661250486", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-10T13:36:06.075Z", "publish_time": 1718026566075, "_source_registry_name": "default"}, "1.21.5": {"name": "jiti", "version": "1.21.5", "license": "MIT", "_id": "jiti@1.21.5", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "1b22e744691081f333ff9077773d1f3545b7e5b0", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.5.tgz", "fileCount": 14, "integrity": "sha512-JmvHYAZK3v0BifQ3fk+kOhuCeni40Ehqx1qdFJsYUeFZVL3kKeyWPRQ4NEY0rjklqgVZzLzqNHktzQRirst15Q==", "signatures": [{"sig": "MEUCIQDPHtwdXz7KBHrNge2jJ9CBwDIDQEovBRYKZGm3EnOn6QIgeop9NcSDf3mQ+9G2c1nElwV1DZymuKEBmmk6omcQ60g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1947717, "size": 411718}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "6ea0aa388a6a7016a0f31288cc1b04729d7848db", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.2.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.2.12", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.3", "config": "^3.3.11", "eslint": "^9.4.0", "semver": "^7.6.2", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.7", "@types/node": "^20.14.2", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.7", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.5_1718030023381_0.0692330930455447", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-10T14:33:43.642Z", "publish_time": 1718030023642, "_source_registry_name": "default"}, "1.21.6": {"name": "jiti", "version": "1.21.6", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.24.7", "@babel/types": "^7.24.7", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.14.2", "@types/object-hash": "^3.0.6", "@types/resolve": "^1.20.6", "@types/semver": "^7.5.8", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.11.3", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.11", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.4.0", "eslint-config-unjs": "^0.3.2", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^9.1.0", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "object-hash": "^3.0.0", "pathe": "^1.1.2", "pirates": "^4.0.6", "pkg-types": "^1.1.1", "prettier": "^3.3.1", "reflect-metadata": "^0.2.1", "semver": "^7.6.2", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "tslib": "^2.6.3", "typescript": "^5.4.5", "vite": "^5.2.12", "vitest": "^1.6.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@9.2.0", "_id": "jiti@1.21.6", "gitHead": "c091661aff55784bbe694cb65652637c3a9f9988", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==", "shasum": "6c7f7398dd4b3142767f9a168af2f317a428d268", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.6.tgz", "fileCount": 14, "unpackedSize": 1947802, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIET4hMIEcoMI9NJu0oISsexfGvUL95m0kmK0z6fACNigAiBxzEZk87vHkSGaocM1Gl55C3P8anusAWiA41VgUfl54w=="}], "size": 411741}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_1.21.6_1718031630994_0.3925504902551509"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-10T15:00:31.192Z", "publish_time": 1718031631192, "_source_registry_name": "default"}, "2.0.0-beta.1": {"name": "jiti", "version": "2.0.0-beta.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "exports": {".": {"types": "./dist/jiti.d.ts", "default": "./lib/index.js"}}, "main": "./lib/index.js", "types": "./dist/jiti.d.ts", "bin": {"jiti": "bin/jiti.js"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-simple-access": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.24.7", "@babel/types": "^7.24.7", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.14.9", "@types/object-hash": "^3.0.6", "@types/resolve": "^1.20.6", "@types/semver": "^7.5.8", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.12.0", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.12", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.5.0", "eslint-config-unjs": "^0.3.2", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^9.3.0", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "object-hash": "^3.0.0", "pathe": "^1.1.2", "pirates": "^4.0.6", "pkg-types": "^1.1.1", "prettier": "^3.3.2", "reflect-metadata": "^0.2.1", "semver": "^7.6.2", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "tslib": "^2.6.3", "typescript": "^5.5.2", "vite": "^5.3.1", "vitest": "^1.6.0", "webpack": "^5.92.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@9.4.0", "_id": "jiti@2.0.0-beta.1", "readmeFilename": "README.md", "gitHead": "863c7ea86e563a438f7c568b518e9502105bdb57", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "20.15.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-HcvE7lgB3gSkV40gbxMhDmd1oGs47+0yXZD5o8McaYT54dT9Bf2dbwRlzGxlkINX9dVlLqn5PkQwHgWI6oii/w==", "shasum": "f0f3577227e7452bbfece7026e3d9e676cc96dcd", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.0.0-beta.1.tgz", "fileCount": 24, "unpackedSize": 1904804, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYQ3Sb9o4R+xToaHkD6lwi5Oxveb3YfDmn2dfK+aFRAQIgFXIZsjCMRd5wNTtk4aQlN5bwVaSFbzoWs5OCGilSJrQ="}], "size": 405517}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.0.0-beta.1_1719587897574_0.3293016482090756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-28T15:18:17.795Z", "publish_time": 1719587897795, "_source_registry_name": "default"}, "2.0.0-beta.2": {"name": "jiti", "version": "2.0.0-beta.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.mts", "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 ./lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "test": "pnpm lint && vitest run --coverage && pnpm test:register && pnpm test:bun", "test:register": "node ./test/register-test.mjs", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-simple-access": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.24.7", "@babel/types": "^7.24.7", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.14.9", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.12.0", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.12", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.5.0", "eslint-config-unjs": "^0.3.2", "estree-walker": "^3.0.3", "execa": "^9.3.0", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "pathe": "^1.1.2", "pkg-types": "^1.1.1", "prettier": "^3.3.2", "reflect-metadata": "^0.2.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "typescript": "^5.5.2", "vitest": "^1.6.0", "webpack": "^5.92.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.4.2"}, "packageManager": "pnpm@9.4.0", "_id": "jiti@2.0.0-beta.2", "readmeFilename": "README.md", "gitHead": "4490a8bd038f676987b3de521a220b398a56b4c4", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "20.15.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-c+<PERSON><PERSON><PERSON><PERSON>akiQuMKbnhvrjZUvrK6E/AfmTOf4P+E3Y4FNVHcNMX9e/XrnbEvO+m4wS6ZjsvhHh/POQTlfy8uXFc0A==", "shasum": "5316b4adaa6f2e7ced330ab475bc6f35cfe51aaa", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.0.0-beta.2.tgz", "fileCount": 13, "unpackedSize": 1895302, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmem3o9/4/gMwnz9zDJDX3tidGRSAxFL8c3IpgxBU9swIhAJDetMMydUn+bLqCYZ0+gKd88OEMdZNKamHiFL67034B"}], "size": 402387}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.0.0-beta.2_1719863332536_0.7420122525103274"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-01T19:48:52.765Z", "publish_time": 1719863332765, "_source_registry_name": "default"}, "2.0.0-beta.3": {"name": "jiti", "version": "2.0.0-beta.3", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.mts", "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 ./lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "test": "pnpm lint && vitest run --coverage && pnpm test:register && pnpm test:bun", "test:register": "node ./test/register-test.mjs", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-simple-access": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.24.7", "@babel/types": "^7.24.7", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.14.9", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.12.0", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.12", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.5.0", "eslint-config-unjs": "^0.3.2", "estree-walker": "^3.0.3", "execa": "^9.3.0", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "pathe": "^1.1.2", "pkg-types": "^1.1.1", "prettier": "^3.3.2", "reflect-metadata": "^0.2.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "typescript": "^5.5.2", "vitest": "^1.6.0", "webpack": "^5.92.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.4.2", "yoctocolors": "^2.1.0"}, "packageManager": "pnpm@9.4.0", "_id": "jiti@2.0.0-beta.3", "readmeFilename": "README.md", "gitHead": "fdd6f8a407745cadab0b394da456b6a81595efd3", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "20.15.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-pmfRbVRs/7khFrSAYnSiJ8C0D5GvzkE4Ey2pAvUcJsw1ly/p+7ut27jbJrjY79BpAJQJ4gXYFtK6d1Aub+9baQ==", "shasum": "8b97dcb268a7409c3fbda9242453486564a11c36", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.0.0-beta.3.tgz", "fileCount": 13, "unpackedSize": 1897312, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfO3YX6vJUWsAghhhqk+GozMIBwaZqyoLJWvNUhZnAOAiBoh7+Mp4rqiACI0OBt1CnJmNdiPJJfb7/oWbw8KIasAg=="}], "size": 402867}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.0.0-beta.3_1719917648178_0.9708220284228433"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T10:54:08.386Z", "publish_time": 1719917648386, "_source_registry_name": "default"}, "2.0.0-rc.1": {"name": "jiti", "version": "2.0.0-rc.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-module-transforms": "^7.25.2", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-simple-access": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.6", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-typescript": "^7.25.2", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.6", "@babel/types": "^7.25.6", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.5.5", "@vitest/coverage-v8": "^2.1.1", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.12", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.10.0", "eslint-config-unjs": "^0.3.2", "estree-walker": "^3.0.3", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "prettier": "^3.3.3", "reflect-metadata": "^0.2.2", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.1", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1"}, "packageManager": "pnpm@9.10.0", "_id": "jiti@2.0.0-rc.1", "readmeFilename": "README.md", "gitHead": "fc98088e9a3b02707288d6302087cfe5fa5969fb", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-40BOLe5MVHVgtzjIB52uBqRxTCR07Ziecxx/LVmqRDV14TJaruFX6kKgS9iYhATGSUs04x3S19Kc8ErUKshMhA==", "shasum": "8f39e916abd5644ccf17e95fd41e132f4e5ac8a3", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.0.0-rc.1.tgz", "fileCount": 15, "unpackedSize": 1918206, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2WGDhN7I8FmM+POq/7/pgtiEaqdjob+alofRv24I3NAiBOhK0aKePXFqAshj9Cqy8SHscNGx5SCYfhm3OEVKyqjA=="}], "size": 407242}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.0.0-rc.1_1726745189777_0.9573466979187801"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-19T11:26:30.030Z", "publish_time": 1726745190030, "_source_registry_name": "default"}, "2.0.0": {"name": "jiti", "version": "2.0.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-module-transforms": "^7.25.2", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-simple-access": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.6", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@babel/plugin-transform-typescript": "^7.25.2", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.6", "@babel/types": "^7.25.6", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.5.5", "@vitest/coverage-v8": "^2.1.1", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.11.0", "eslint-config-unjs": "^0.3.2", "estree-walker": "^3.0.3", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.1", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.1", "vue": "^3.5.8", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.0.0", "gitHead": "fca09ee60ea0128adab135e7171d0e8dbed03baa", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-CJ7e7Abb779OTRv3lomfp7Mns/Sy1+U4pcAx5VbjxCZD5ZM/VJaXPpPjNKjtSvWQy/H86E49REXR34dl1JEz9w==", "shasum": "ccaab6ce73a73cbf04e187645c614b3a3d41b653", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.0.0.tgz", "fileCount": 15, "unpackedSize": 1933642, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfeWqrsMwz4KRBsj8yffPMBQ80Z/cm0/mYfvK7DW4m9QIhALv+NQIJmMJLYHg06ypaWPPK2/v/DyipS92OJ2QLHPN5"}], "size": 411250}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.0.0_1727288513813_0.7590541001612354"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-25T18:21:54.238Z", "publish_time": 1727288514238, "_source_registry_name": "default"}, "2.1.0": {"name": "jiti", "version": "2.1.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-module-transforms": "^7.25.2", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-simple-access": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.6", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@babel/plugin-transform-typescript": "^7.25.2", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.6", "@babel/types": "^7.25.6", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.1", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.11.0", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.1", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.1", "vue": "^3.5.10", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.1.0", "gitHead": "f99a1d7df0064e201df760e17470cc34bb330618", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Nftp80J8poC3u+93ZxpjstsgfQ5d0o5qyD6yStv32sgnWr74xRxBppEwsUoA/GIdrJpgGRkC1930YkLcAsFdSw==", "shasum": "54bdaa411f543554aed9bf3458351bbc1fa5acab", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.1.0.tgz", "fileCount": 15, "unpackedSize": 2084017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDgwVfiJ5BrI+LfbodIUrvrFZg2i22e+2vKbYbLPC2UFAiEA5PgzWSVQ8c2jxNDRcQCfJJPxYM+TdUz1hEKsbWogWWE="}], "size": 451991}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.1.0_1727818782288_0.6658355511138001"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-01T21:39:42.589Z", "publish_time": 1727818782589, "_source_registry_name": "default"}, "2.1.1": {"name": "jiti", "version": "2.1.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.11.1", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.1", "moment-timezone": "^0.5.45", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.1", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.10", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.1.1", "gitHead": "420787447bc3aa1c4da0d590ed82323cf1e49bed", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1BRk+NppnvjWLfEqPQtDc3JTs2eiXY9cKBM+VOk5WO+uwWHIuLeWEo3Y1LTqjguKiK9KcLDYA3IdP7gWqcbRig==", "shasum": "4c2410723e6f29c835b266783ea312ac0b5da54a", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.1.1.tgz", "fileCount": 15, "unpackedSize": 3464036, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDxKnCf+s03z+ok/IXUYDwCOXtOgbm9qvwe0rOO5iIzZAiEAniG4Jf2ZaXKMS66XWMlUWx6X9daHEoheQLIL6Ql2Rd0="}], "size": 701893}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.1.1_1727941776484_0.9059349550199787"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-03T07:49:36.774Z", "publish_time": 1727941776774, "_source_registry_name": "default"}, "2.1.2": {"name": "jiti", "version": "2.1.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.11.1", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.1", "moment-timezone": "^0.5.45", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.1", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.10", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.1.2", "gitHead": "103d165092f7d13e9243977a3f8edf9ec6e66b41", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-cYNjJus5X9J4jLzTaI8rYoIq1k6YySiA1lK4wxSnOrBRXkbVyreZfhoboJhsUmwgU82lpPjj1IoU7Ggrau8r3g==", "shasum": "2a37dbb3897d24767dc37bc29693a1033d368040", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.1.2.tgz", "fileCount": 15, "unpackedSize": 3464044, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA+JSoPwEhQQMaimd+GFNMBTJrRJ75nPJ6Q6dUzpvZNDAiEAwRhPA5VEv9xOb4WfrzSs63EnYD7lXUpmr6L5D5QRT60="}], "size": 701894}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.1.2_1727966097341_0.3897291492353765"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-03T14:34:57.620Z", "publish_time": 1727966097620, "_source_registry_name": "default"}, "2.2.0": {"name": "jiti", "version": "2.2.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.11.1", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.1", "moment-timezone": "^0.5.45", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.1", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.10", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.2.0", "gitHead": "50ac5d9874f3c6d6198becb1e9ba00d422e5004f", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-XI3aImuxq2Qt9upi/5cWxVT7aP0FUmrT5jg9LAhl87tqQsJ7WRqePfI3SBktCRB6YiaHFl1R60PZ9s03nV3fSg==", "shasum": "1b5b8934cdf8290920dc5f8cc6b836294ddc72a2", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.2.0.tgz", "fileCount": 15, "unpackedSize": 2112535, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF2fsvnp+BnlnZsfuNCGjeDk+3anS2+1ntzOenCzaZrQIgVnu8CmgI4HTYRSNpnhFNCLXXd5pa7i9fNZ0KboOK2UE="}], "size": 455881}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.2.0_1728060420117_0.7296113715924781"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-04T16:47:05.676Z", "publish_time": 1728060425676, "_source_registry_name": "default"}, "2.2.1": {"name": "jiti", "version": "2.2.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.11.1", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.1", "moment-timezone": "^0.5.45", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.1", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.10", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.2.1", "gitHead": "94b172b9794bbf16ea774b8d82b8016384bb327a", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-weIl/Bv3G0J3UKamLxEA2G+FfQ33Z1ZkQJGPjKFV21zQdKWu2Pi6o4elpj2uEl5XdFJZ9xzn1fsanWTFSt45zw==", "shasum": "0c968cd6c9b4baeb4074abd2f2f2dc42f6537a09", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.2.1.tgz", "fileCount": 15, "unpackedSize": 2112535, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSTQf/EZtFa3WJ0C/TDpwEni08WQGqkyc8qIScaKWH4QIhAN1Ru5AynUkKcuEnJvVW1+BA24PCVYDAZfDlfVuyG7qq"}], "size": 455880}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.2.1_1728062131402_0.846769461773522"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-04T17:15:31.745Z", "publish_time": 1728062131745, "_source_registry_name": "default"}, "2.3.0": {"name": "jiti", "version": "2.3.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.12.0", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.1", "moment-timezone": "^0.5.45", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.2", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.11", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.3.0", "gitHead": "a39bd7eb1687059b57c169c3f5001f9d921f4a18", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-sxPw05kUBzmPclmPVH12lMDiUPUWVPQwZdULPxKljb9GDqAu9qHc/5jF2v+I4j7Sd8UQ7yehHmlReBBmdhEE9g==", "shasum": "cf2c0cd6337be162bd5ae14dff8c653c4833e5a3", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.3.0.tgz", "fileCount": 15, "unpackedSize": 2113072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGB8k4UnkoFjey3cauDaHSBHwkw1XxkCXiI9hzeoGcvhAiEAoLk8W1WqCOAxpN9xROEMRipenmiAtg9ewus2pmlR7tA="}], "size": 456014}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.3.0_1728125082573_0.7379108960028755"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-05T10:44:42.843Z", "publish_time": 1728125082843, "_source_registry_name": "default"}, "2.3.1": {"name": "jiti", "version": "2.3.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.12.0", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.1", "moment-timezone": "^0.5.45", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.2", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.11", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.11.0", "_id": "jiti@2.3.1", "gitHead": "c4697486207fcbcb390d172da619b7c1a7bac6a2", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-xPZ6pPzUifI8XDBBxIL4OB1w1ZKmBpmNEeKwNt2d0Spn8XisAIZhWrlOHq5seBrFGTxVx9PbrWvEMyrk4IO5bA==", "shasum": "b81ff051075117768dd67829f47f5035eeceb5c7", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.3.1.tgz", "fileCount": 15, "unpackedSize": 2113181, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC47ST2BuFYhVGa//IC60B4xJ31TG8BYsHWmp9pfeXy6AiBacLPmRYae8+6EUYucMeaKYSRrHwpW2QoUv4tyFgDknA=="}], "size": 456074}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.3.1_1728126315409_0.15792466821452233"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-05T11:05:15.695Z", "publish_time": 1728126315695, "_source_registry_name": "default"}, "2.3.2": {"name": "jiti", "version": "2.3.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.12.0", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.2", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.2", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.11", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.12.0", "_id": "jiti@2.3.2", "gitHead": "c1a24e0d9f4bc077c70e1ec72f9424b6b4e3b195", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-9<PERSON><PERSON>e7kYLCyWTAP6EIyg3B4ZuHy5W0gdy6y1rgrWrAOpTrUU+vKuKa1+OXB7MBkujyvm6a2b7i0ETHQDbgY98A==", "shasum": "ea3620d59372cf4829458fa4b921b54255435819", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.3.2.tgz", "fileCount": 15, "unpackedSize": 2114372, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCa/yKbtMZEyR43AiZYjqMHj4Bwg8/R9DzKQkY1IL7KgIgBM3rwruLHDWxrifUDTveTKolAguiTD3jEmSxY2iE1Lc="}], "size": 456157}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.3.2_1728301505899_0.713551000155525"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-07T11:45:06.120Z", "publish_time": 1728301506120, "_source_registry_name": "default"}, "2.3.3": {"name": "jiti", "version": "2.3.3", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.2", "acorn": "^8.12.1", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.12.0", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.2", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.0", "preact": "^10.24.2", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.1", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vitest": "^2.1.2", "vue": "^3.5.11", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.12.0", "_id": "jiti@2.3.3", "gitHead": "544247421cce0349d58dc94d58e862a7bc12176a", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-EX4oNDwcXSivPrw2qKH2LB5PoFxEvgtv2JgwW0bU858HoLQ+kutSvjLMUqBd0PeJYEinLWhoI9Ol0eYMqj/wNQ==", "shasum": "39c66fc77476b92a694e65dfe04b294070e2e096", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.3.3.tgz", "fileCount": 15, "unpackedSize": 2114372, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpzTfxTdGvjPn7gC/VLYIlUzqDnr/hbH+Gvh1dYcdXVQIgEVDm7HK9E9eOd4VMStUhtXPrpVQUjr1cZkA+ePBU5iE="}], "size": 456156}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.3.3_1728321214260_0.34152779070578343"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-07T17:13:34.652Z", "publish_time": 1728321214652, "_source_registry_name": "default"}, "2.4.0": {"name": "jiti", "version": "2.4.0", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.26.0", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.8.6", "@vitest/coverage-v8": "^2.1.4", "acorn": "^8.14.0", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.13.0", "eslint-config-unjs": "^0.4.1", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "mime": "^4.0.4", "mlly": "^1.7.2", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.1", "preact": "^10.24.3", "preact-render-to-string": "^6.5.11", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.3", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.3", "vitest": "^2.1.4", "vue": "^3.5.12", "webpack": "^5.96.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.12.3", "_id": "jiti@2.4.0", "gitHead": "2f9c2376e70d951cdfbc06eb8ed046331deb177a", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-H5UpaUI+aHOqZXlYOaFP/8AzKsg+guWu+Pr3Y8i7+Y3zr1aXAvCvTAQ1RxSc6oVD8R8c7brgNtTVP91E7upH/g==", "shasum": "393d595fb6031a11d11171b5e4fc0b989ba3e053", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.4.0.tgz", "fileCount": 15, "unpackedSize": 2130508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZi3fTgdh1qCTN5L/VUZgSUl4CrrHdR2aPASc4AXoxEwIhAOP9x6oG3m2wg9vn3VFlywRGVoXjS1Gh2FRNd+p/ge2J"}], "size": 460627}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.4.0_1730459307933_0.028450881270881245"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-01T11:08:28.244Z", "publish_time": 1730459308244, "_source_registry_name": "default"}, "2.4.1": {"name": "jiti", "version": "2.4.1", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.26.0", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.10.1", "@vitest/coverage-v8": "^2.1.6", "acorn": "^8.14.0", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.15.0", "eslint-config-unjs": "^0.4.2", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "is-installed-globally": "^1.0.0", "mime": "^4.0.4", "mlly": "^1.7.3", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.1", "preact": "^10.25.0", "preact-render-to-string": "^6.5.11", "prettier": "^3.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.3", "std-env": "^3.8.0", "terser-webpack-plugin": "^5.3.10", "tinyexec": "^0.3.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "vitest": "^2.1.6", "vue": "^3.5.13", "webpack": "^5.96.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.14.3", "_id": "jiti@2.4.1", "gitHead": "ad6191f04624badf2112651217bc17008a9bde50", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-yPBThwecp1wS9DmoA4x4KR2h3QoslacnDR8ypuFM962kI4/456Iy1oHx2RAgh4jfZNdn0bctsdadceiBUgpU1g==", "shasum": "4de9766ccbfa941d9b6390d2b159a4b295a52e6b", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.4.1.tgz", "fileCount": 15, "unpackedSize": 2130665, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAL0/sWcenlsEQ560EGnViGz3agmWur9pxP3JdOnYr95AiAnjQKCA6hZnegjeo1JxVOkuiVXaFSARETt/U/aNJjxPQ=="}], "size": 460701}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jiti_2.4.1_1732875321306_0.41359100248920866"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-29T10:15:21.499Z", "publish_time": 1732875321499, "_source_registry_name": "default"}, "1.21.7": {"name": "jiti", "version": "1.21.7", "license": "MIT", "_id": "jiti@1.21.7", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9dd81043424a3d28458b193d965f0d18a2300ba9", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-1.21.7.tgz", "fileCount": 14, "integrity": "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==", "signatures": [{"sig": "MEUCIQCUn1w9Q5AVb/1eB028gQ6EKWqX6gZqF+IPm/zzSgsuzwIgegG6eKGXdvt8vSmDc1GHUnBYhl7aCbIXuzuO9bx7uo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2003102, "size": 422851}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "e420d48300a1d23a5468e5ea9239e2c5b96413ab", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish --tag 1x", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.0", "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.3", "vite": "^6.0.3", "acorn": "^8.14.0", "destr": "^2.0.3", "execa": "^9.5.2", "pathe": "^1.1.2", "tslib": "^2.8.1", "config": "^3.3.12", "eslint": "^9.17.0", "semver": "^7.6.3", "vitest": "^2.1.8", "pirates": "^4.0.6", "std-env": "^3.8.0", "webpack": "^5.97.1", "prettier": "^3.4.2", "fast-glob": "^3.3.2", "pkg-types": "^1.2.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "@babel/core": "^7.26.0", "@types/node": "^22.10.2", "changelogen": "^0.5.7", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.26.3", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.25.9", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^2.1.8", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.11", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.26.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-typescript": "^7.26.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-optional-chaining": "^7.25.9", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-nullish-coalescing-operator": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.7_1734470449003_0.7864185939977761", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2024-12-17T21:20:49.254Z", "publish_time": 1734470449254, "_source_registry_name": "default"}, "2.4.2": {"name": "jiti", "version": "2.4.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-typescript": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/template": "^7.25.9", "@babel/traverse": "^7.26.4", "@babel/types": "^7.26.3", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "acorn": "^8.14.0", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "is-installed-globally": "^1.0.0", "mime": "^4.0.4", "mlly": "^1.7.3", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.1", "preact": "^10.25.2", "preact-render-to-string": "^6.5.12", "prettier": "^3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.3", "std-env": "^3.8.0", "terser-webpack-plugin": "^5.3.11", "tinyexec": "^0.3.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "vue": "^3.5.13", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.24.1"}, "packageManager": "pnpm@9.15.0", "_id": "jiti@2.4.2", "gitHead": "340e2a733c35df66c85667ef254eab672f5de210", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==", "shasum": "d19b7732ebb6116b06e2038da74a55366faef560", "tarball": "https://registry.npmmirror.com/jiti/-/jiti-2.4.2.tgz", "fileCount": 15, "unpackedSize": 2131809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1Ji6WPArFC4SY3xSjRXrcDhXeLnuFIGfxphcjM7BzAQIhAPfFXfY+aeUV17O63vVSgmkx7QqwRKXz1beF61NVhjfr"}], "size": 460976}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jiti_2.4.2_1734470591808_0.9662416788350368"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-17T21:23:12.058Z", "publish_time": 1734470592058, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "_source_registry_name": "default"}