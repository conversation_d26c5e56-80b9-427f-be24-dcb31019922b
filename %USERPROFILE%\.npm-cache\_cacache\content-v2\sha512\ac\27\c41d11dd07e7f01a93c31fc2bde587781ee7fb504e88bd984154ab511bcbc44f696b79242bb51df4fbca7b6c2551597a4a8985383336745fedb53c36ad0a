{"_attachments": {}, "_id": "vscode-uri", "_rev": "6219-61f155d7830fd08f52a46317", "author": "Microsoft", "description": "The URI implementation that is used by VS Code and its extensions", "dist-tags": {"latest": "3.1.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON>xa<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "microsoft1es", "email": "<EMAIL>"}, {"name": "sbatten", "email": "<EMAIL>"}, {"name": "lszomoru", "email": "<EMAIL>"}, {"name": "vscode-bot", "email": "<EMAIL>"}], "name": "vscode-uri", "readme": "## vscode-uri\n\n[![Build Status](https://travis-ci.org/Microsoft/vscode-uri.svg?branch=master)](https://travis-ci.org/Microsoft/vscode-uri)\n\nThis module contains the URI implementation that is used by VS Code and its extensions. \nIt has support for parsing a string into `scheme`, `authority`, `path`, `query`, and\n`fragment` URI components as defined in: http://tools.ietf.org/html/rfc3986\n\n```\n  foo://example.com:8042/over/there?name=ferret#nose\n  \\_/   \\______________/\\_________/ \\_________/ \\__/\n   |           |            |            |        |\nscheme     authority       path        query   fragment\n   |   _____________________|__\n  / \\ /                        \\\n  urn:example:animal:ferret:nose\n```\n\n## Usage\n\n```js\nimport { URI } from 'vscode-uri'\n\n// parse an URI from string\n\nlet uri = URI.parse('https://code.visualstudio.com/docs/extensions/overview#frag')\n\nassert.ok(uri.scheme === 'https');\nassert.ok(uri.authority === 'code.visualstudio.com');\nassert.ok(uri.path === '/docs/extensions/overview');\nassert.ok(uri.query === '');\nassert.ok(uri.fragment === 'frag');\nassert.ok(uri.toString() === 'https://code.visualstudio.com/docs/extensions/overview#frag')\n\n\n// create an URI from a fs path\n\nlet uri = URI.file('/users/me/c#-projects/');\n\nassert.ok(uri.scheme === 'file');\nassert.ok(uri.authority === '');\nassert.ok(uri.path === '/users/me/c#-projects/');\nassert.ok(uri.query === '');\nassert.ok(uri.fragment === '');\nassert.ok(uri.toString() === 'file:///users/me/c%23-projects/')\n```\n\n## Usage: Util\n\nThis module also exports a `Utils` package which is an extension, not part of `vscode.Uri`, and useful for path-math. There is: \n\n* `Utils.joinPath(URI, paths): URI`\n* `Utils.resolvePath(URI, paths): URI`\n* `Utils.dirname(URI): string`\n* `Utils.basename(URI): string`\n* `Utils.extname(URI): string`\n\nAll util use posix path-math as defined by the node.js path module. \n\n\n## Contributing\n\nThe source of this module is taken straight from the [vscode](https://github.com/microsoft/vscode)-sources and because of that issues and pull request should be created in that repository. Thanks and Happy Coding!\n\n## Code of Conduct\n\nThis project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/). For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.\n", "time": {"created": "2022-01-26T14:08:23.720Z", "modified": "2025-07-11T23:14:22.003Z", "3.0.2": "2021-01-05T08:11:15.653Z", "3.0.1": "2020-12-18T16:23:53.818Z", "3.0.0": "2020-12-17T11:57:42.387Z", "2.1.2": "2020-05-26T14:12:37.837Z", "2.1.1": "2019-10-30T10:30:56.603Z", "2.1.0": "2019-10-24T10:23:38.609Z", "2.0.3": "2019-07-10T07:04:35.392Z", "2.0.2": "2019-06-14T10:18:05.944Z", "2.0.1": "2019-06-05T10:21:22.968Z", "2.0.0": "2019-05-29T11:03:38.899Z", "1.0.8": "2019-05-29T10:57:17.837Z", "1.0.7": "2019-05-29T10:05:50.833Z", "1.0.6": "2018-08-16T10:44:38.003Z", "1.0.5": "2018-06-06T10:01:53.756Z", "1.0.3": "2018-03-08T08:30:45.610Z", "1.0.2": "2018-03-08T08:29:22.034Z", "1.0.1": "2017-06-16T09:19:21.975Z", "1.0.0": "2016-09-26T08:52:04.279Z", "0.0.7": "2016-09-13T10:04:46.285Z", "0.0.6": "2016-09-05T13:54:37.273Z", "0.0.5": "2016-09-05T12:59:34.422Z", "0.0.4": "2016-09-05T12:51:46.055Z", "0.0.3": "2016-06-13T09:39:03.647Z", "0.0.2": "2016-06-08T07:00:45.802Z", "0.0.1": "2016-06-07T15:16:23.915Z", "3.0.3": "2021-12-21T16:20:37.457Z", "3.0.4": "2022-09-15T08:04:47.753Z", "3.0.5": "2022-09-16T14:24:54.174Z", "3.0.6": "2022-09-19T13:27:26.118Z", "3.0.7": "2022-12-13T08:29:04.909Z", "3.0.8": "2023-10-05T09:39:42.462Z", "3.1.0": "2025-02-04T18:18:11.908Z"}, "versions": {"3.0.2": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.2", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^8.0.3", "@types/node": "^10.12.21", "mocha": "8.1.3", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^8.0.13", "typescript": "^4.0.3", "webpack": "^5.11.1", "webpack-cli": "^4.3.1"}, "gitHead": "dd39d06d80c9f8d4b5ca6ab195219699b417a0b2", "_id": "vscode-uri@3.0.2", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "ecfd1d066cb8ef4c3a208decdbab9a8c23d055d0", "size": 47944, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.2.tgz", "integrity": "sha512-jkjy6pjU1fxUvI51P+gCsxg1u2n8LSt0W6KrCNQceaziKzff74GoWmjVG46KieVzybO1sttPQmYfrwSHey7GUA=="}, "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.2_1609834274852_0.48985470887088045"}, "_hasShrinkwrap": false, "publish_time": 1609834275653, "_cnpm_publish_time": 1609834275653, "_cnpmcore_publish_time": "2021-12-16T12:00:41.591Z"}, "3.0.1": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.1", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^8.0.3", "@types/node": "^10.12.21", "mocha": "8.1.3", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^8.0.4", "typescript": "^4.0.3", "webpack": "^5.11.0", "webpack-cli": "^4.2.0"}, "gitHead": "c343b054276466f96ae981d3c686019fccaeefd1", "_id": "vscode-uri@3.0.1", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "c36502fcf6fa57e63441d3804b5826018e26062e", "size": 47931, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.1.tgz", "integrity": "sha512-LnMgm97uZM2JDjX/vKbbCk+phm++Ih31e5Ao3lqokawhDRocp2ZAVMRiIhPZx6fS5Sqnquyhxh8ABn9TWCvHoA=="}, "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.1_1608308633616_0.7479734445041408"}, "_hasShrinkwrap": false, "publish_time": 1608308633818, "_cnpm_publish_time": 1608308633818, "_cnpmcore_publish_time": "2021-12-16T12:00:42.134Z"}, "3.0.0": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.0", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@purtuga/esm-webpack-plugin": "^1.4.0", "@types/mocha": "^8.0.3", "@types/node": "^10.12.21", "mocha": "8.1.3", "rimraf": "^3.0.2", "ts-loader": "^8.0.4", "typescript": "^4.0.3", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "gitHead": "2fa017b3d015e3991708a5a869bf248347bd2fd9", "_id": "vscode-uri@3.0.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "a881b16614999d97e71d5d29ea453d4f8c19499d", "size": 50824, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.0.tgz", "integrity": "sha512-boWPjBN8UPv8/9OnEGumoXgIVreVjs3RyqULtfHC3GRT1aCnaotYRKOhZERmaMsCVVt6nPmmI/ec7i1FH2kctw=="}, "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.0_1608206262193_0.4414011528978512"}, "_hasShrinkwrap": false, "publish_time": 1608206262387, "_cnpm_publish_time": 1608206262387, "_cnpmcore_publish_time": "2021-12-16T12:00:42.500Z"}, "2.1.2": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.1.2", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^3.6.4"}, "gitHead": "7c094c53581a8b1b7631fbc5c4265dea2beaf303", "_id": "vscode-uri@2.1.2", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "c8d40de93eb57af31f3c715dd650e2ca2c096f1c", "size": 11678, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.1.2.tgz", "integrity": "sha512-8TEXQxlldWAuIODdukIb+TR5s+9Ds40eSJrw+1iDDA9IFORPjMELarNQE3myz5XIkWWpdprmJjm1/SxMlWOC8A=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.1.2_1590502357661_0.03265937402256003"}, "_hasShrinkwrap": false, "publish_time": 1590502357837, "_cnpm_publish_time": 1590502357837, "_cnpmcore_publish_time": "2021-12-16T12:00:42.691Z"}, "2.1.1": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.1.1", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^3.6.4"}, "gitHead": "95e03c06f87d38f25eda1ae3c343fe5b7eec3f52", "_id": "vscode-uri@2.1.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.0", "dist": {"shasum": "5aa1803391b6ebdd17d047f51365cf62c38f6e90", "size": 10993, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.1.1.tgz", "integrity": "sha512-eY9jmGoEnVf8VE8xr5znSah7Qt1P/xsCdErz+g8HYZtJ7bZqKH5E3d+6oVNm1AC/c6IHUDokbmVXKOi4qPAC9A=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.1.1_1572431456431_0.8803296990665133"}, "_hasShrinkwrap": false, "publish_time": 1572431456603, "_cnpm_publish_time": 1572431456603, "_cnpmcore_publish_time": "2021-12-16T12:00:42.884Z"}, "2.1.0": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.1.0", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^3.6.4"}, "gitHead": "3849c4efe461a0d3b272293fe23c9779e29db68a", "_id": "vscode-uri@2.1.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.0", "dist": {"shasum": "475a4269e63edbc13914b40c84bc1416e3398156", "size": 11585, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.1.0.tgz", "integrity": "sha512-3voe44nOhb6OdKlpZShVsmVvY2vFQHMe6REP3Ky9RVJuPyM/XidsjH6HncCIDdSmbcF5YQHrTC/Q+Q2loJGkOw=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.1.0_1571912618410_0.629226272854188"}, "_hasShrinkwrap": false, "publish_time": 1571912618609, "_cnpm_publish_time": 1571912618609, "_cnpmcore_publish_time": "2021-12-16T12:00:43.152Z"}, "2.0.3": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.0.3", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "413805221cc6ed167186ab3103d3248d6f7161f2", "_id": "vscode-uri@2.0.3", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.0", "dist": {"shasum": "25e5f37f552fbee3cec7e5f80cef8469cefc6543", "size": 11039, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.0.3.tgz", "integrity": "sha512-4D3DI3F4uRy09WNtDGD93H9q034OHImxiIcSq664Hq1Y1AScehlP3qqZyTkX/RWxeu0MRMHGkrxYqm2qlDF/aw=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.0.3_1562742274930_0.1596921412833472"}, "_hasShrinkwrap": false, "publish_time": 1562742275392, "_cnpm_publish_time": 1562742275392, "_cnpmcore_publish_time": "2021-12-16T12:00:43.431Z"}, "2.0.2": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.0.2", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "885ff5685579389fb03f5b46e67f33a553c20b31", "_id": "vscode-uri@2.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "cbc7982525e1cd102d2da6515e6f103650cb507c", "size": 14316, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.0.2.tgz", "integrity": "sha512-VebpIxm9tG0fG2sBOhnsSPzDYuNUPP1UQW4K3mwthlca4e4f3d6HKq3HkITC2OPFomOaB7pHTSjcpdFWjfYTzg=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.0.2_1560507485770_0.2331589487994501"}, "_hasShrinkwrap": false, "publish_time": 1560507485944, "_cnpm_publish_time": 1560507485944, "_cnpmcore_publish_time": "2021-12-16T12:00:43.686Z"}, "2.0.1": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.0.1", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "3cc5fd3791d0502a2e7c5de00dd19a708a845e1e", "_id": "vscode-uri@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "5448e4f77d21d93ffa34b96f84c6c5e09e3f5a9b", "size": 14313, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.0.1.tgz", "integrity": "sha512-s/k0zsYr6y+tsocFyxT/+G5aq8mEdpDZuph3LZ+UmCs7LNhx/xomiCy5kyP+jOAKC7RMCUvb6JbPD1/TgAvq0g=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.0.1_1559730082834_0.7439719089830974"}, "_hasShrinkwrap": false, "publish_time": 1559730082968, "_cnpm_publish_time": 1559730082968, "_cnpmcore_publish_time": "2021-12-16T12:00:43.955Z"}, "2.0.0": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "2.0.0", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "453872f41a79f32af0b9fca1f12cfc72d1386bb4", "_id": "vscode-uri@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "2df704222f72b8a71ff266ba0830ed6c51ac1542", "size": 10858, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-2.0.0.tgz", "integrity": "sha512-lWXWofDSYD8r/TIyu64MdwB4FaSirQ608PP/TzUyslyOeHGwQ0eTHUZeJrK1ILOmwUHaJtV693m2JoUYroUDpw=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_2.0.0_1559127818740_0.12774620425889238"}, "_hasShrinkwrap": false, "publish_time": 1559127818899, "_cnpm_publish_time": 1559127818899, "_cnpmcore_publish_time": "2021-12-16T12:00:44.152Z"}, "1.0.8": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.8", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "58355f662eab0d64600433728decf4d570bf9e59", "_id": "vscode-uri@1.0.8", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "9769aaececae4026fb6e22359cb38946580ded59", "size": 10868, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.8.tgz", "integrity": "sha512-obtSWTlbJ+a+TFRYGaUumtVwb+InIUVI0Lu0VBUAPmj2cU5JutEXg3xUE0c2J5Tcy7h2DEKVJBFi+Y9ZSFzzPQ=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_1.0.8_1559127437724_0.4187886652676305"}, "_hasShrinkwrap": false, "publish_time": 1559127437837, "_cnpm_publish_time": 1559127437837, "_cnpmcore_publish_time": "2021-12-16T12:00:44.762Z"}, "1.0.7": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.7", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "77666df7ac20d72b7ebcc0de934f4eb488aa3e10", "_id": "vscode-uri@1.0.7", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "2ed22e87c6f0a2dbeb5fbadd16b4663213e83f0b", "size": 10868, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.7.tgz", "integrity": "sha512-dnF9z9v1cFtNYeQciCZET3hGf8thk8m/34YCU5vlZQR/ru7NwHCj0JuBcEDS9tnFW7giriMOpRGIi77ZrAWNjg=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_1.0.7_1559124350591_0.4333255906271902"}, "_hasShrinkwrap": false, "publish_time": 1559124350833, "_cnpm_publish_time": 1559124350833, "_cnpmcore_publish_time": "2021-12-16T12:00:45.038Z"}, "1.0.6": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.6", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "b1d3221579f97f28a839b6f996d76fc45e9964d8", "_id": "vscode-uri@1.0.6", "_npmVersion": "6.4.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "6b8f141b0bbc44ad7b07e94f82f168ac7608ad4d", "size": 10185, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.6.tgz", "integrity": "sha512-sLI2L0uGov3wKVb9EB+vIQBl9tVP90nqRvxSoJ35vI3NjxE8jfsE5DSOhWgSunHSZmKS4OCi2jrtfxK7uyp2ww=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_1.0.6_1534416277894_0.5560834344124208"}, "_hasShrinkwrap": false, "publish_time": 1534416278003, "_cnpm_publish_time": 1534416278003, "_cnpmcore_publish_time": "2021-12-16T12:00:45.281Z"}, "1.0.5": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.5", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "a52018b38fd4e96947a398430898a00030397cdf", "_id": "vscode-uri@1.0.5", "_shasum": "3b899a8ef71c37f3054d79bdbdda31c7bf36f20d", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "8.9.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "3b899a8ef71c37f3054d79bdbdda31c7bf36f20d", "size": 9351, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.5.tgz", "integrity": "sha512-hNZQfWfVbjjvI3sNCusqqjkZXOn7I4aDPQpiCCI/YxfYo4HHR9C4OdWZAdMRiCsegU7/Ei0LJnmZ7bU87FPKcw=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_1.0.5_1528279313503_0.7015618772164711"}, "_hasShrinkwrap": false, "publish_time": 1528279313756, "_cnpm_publish_time": 1528279313756, "_cnpmcore_publish_time": "2021-12-16T12:00:45.543Z"}, "1.0.3": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.3", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "d8c94c631279a8ff8523419b0d406071ae721b90", "_id": "vscode-uri@1.0.3", "_shasum": "631bdbf716dccab0e65291a8dc25c23232085a52", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "8.9.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "631bdbf716dccab0e65291a8dc25c23232085a52", "size": 7861, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.3.tgz", "integrity": "sha512-UU2mUJfedXt88AuhOj8vSkyWYiusf+zVdaFTme1C6l4k3ja8teTYpRt7C6wToIq7Jnph1fGYYmsaLQuqI+B46g=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_1.0.3_1520497845535_0.13625193702514093"}, "_hasShrinkwrap": false, "publish_time": 1520497845610, "_cnpm_publish_time": 1520497845610, "_cnpmcore_publish_time": "2021-12-16T12:00:46.137Z"}, "1.0.2": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.2", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "scripts": {"compile": "tsc -p ./src && tsc -p ./src/tsconfig.esm.json"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "3d09fc9241c62f6ae4d061657ad54188857cddfc", "_id": "vscode-uri@1.0.2", "_shasum": "0f96da24c43cd706e17469b9b9fde6a2c0f9da5c", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "8.9.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "0f96da24c43cd706e17469b9b9fde6a2c0f9da5c", "size": 6351, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.2.tgz", "integrity": "sha512-HQeFCrj1KZpDuxlkh25rGcd1D5WT+CPoQ3GETq1ZAAGEkqZTYLe4QJtlcDEsSrgeRtKoR1IA8Zp9WN7v4hv9Cg=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_1.0.2_1520497761953_0.83014397744242"}, "_hasShrinkwrap": false, "publish_time": 1520497762034, "_cnpm_publish_time": 1520497762034, "_cnpmcore_publish_time": "2021-12-16T12:00:46.377Z"}, "1.0.1": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.1", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "typings": "./lib/index", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"typescript": "^2.0.3"}, "gitHead": "2255aa25f205d1364666a6b6f9dcc51e46d403bf", "_id": "vscode-uri@1.0.1", "_shasum": "11a86befeac3c4aa3ec08623651a3c81a6d0bbc8", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.11.0", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "11a86befeac3c4aa3ec08623651a3c81a6d0bbc8", "size": 6112, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.1.tgz", "integrity": "sha512-KyJ9thZO85/DII4VWBdXdkGv1W6nA6DigdsTfsM7+linLqD/B6CPCg8SOPbLNVwVm/ea/5hTZve8Ss5QT2Be8w=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri-1.0.1.tgz_1497604760997_0.06592794577591121"}, "directories": {}, "publish_time": 1497604761975, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497604761975, "_cnpmcore_publish_time": "2021-12-16T12:00:46.602Z"}, "1.0.0": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "1.0.0", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "typings": "./lib/index", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "^2.0.3"}, "gitHead": "f1dbeccc10996681377a59dbbf72253828273a38", "_id": "vscode-uri@1.0.0", "_shasum": "07369e2d096bfe42b406bd21860b5aa4fe7a7ad0", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "07369e2d096bfe42b406bd21860b5aa4fe7a7ad0", "size": 5853, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-1.0.0.tgz", "integrity": "sha512-/xXdy33O982DRX08foToMwZa13EPyrP5fSOptBR8MVfZwjdAo8dB63cE2tYDpq9ewp6MtEm4rkoPq0dYL9NJ5g=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/vscode-uri-1.0.0.tgz_1474879923066_0.6516176562290639"}, "directories": {}, "publish_time": 1474879924279, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474879924279, "_cnpmcore_publish_time": "2021-12-16T12:00:46.800Z"}, "0.0.7": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.7", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "typings": "./lib/index", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "1.8.10"}, "gitHead": "5e9adde6292e4258fbc3e13e9419632461b72a7d", "_id": "vscode-uri@0.0.7", "_shasum": "43e18cdd2ab2377f08c77554c3575786f3e66138", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "43e18cdd2ab2377f08c77554c3575786f3e66138", "size": 5863, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.7.tgz", "integrity": "sha512-jy6u58uu0lyLKF8byiwex6qzxLMmI/mjF/7eOWX3tI+DlxmkplcsTKh6FZNkcpISw/A+MlWue5J1GRXqeD5IFQ=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.7.tgz_1473761084273_0.1066384690348059"}, "directories": {}, "publish_time": 1473761086285, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473761086285, "_cnpmcore_publish_time": "2021-12-16T12:00:47.371Z"}, "0.0.6": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.6", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "typings": "./lib/index", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "1.8.10"}, "gitHead": "d2e8c575c2d0b247c452d2187a3a9d5fd30f574d", "_id": "vscode-uri@0.0.6", "_shasum": "25de885c74fe0bb2aad692c989e450b382a40e5e", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "25de885c74fe0bb2aad692c989e450b382a40e5e", "size": 5843, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.6.tgz", "integrity": "sha512-q2UAyP2AOWTHTOxWsAvoLlkZ+i5Dv72P7PP1jOMbhzA0SYkjD0Jh8hWIadkfjwuSZb1fF6hNaHyeihC+Sl3abA=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.6.tgz_1473083674820_0.6469899534713477"}, "directories": {}, "publish_time": 1473083677273, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473083677273, "_cnpmcore_publish_time": "2021-12-16T12:00:47.683Z"}, "0.0.5": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.5", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "typings": "./lib/index", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "1.8.10"}, "gitHead": "d2e8c575c2d0b247c452d2187a3a9d5fd30f574d", "_id": "vscode-uri@0.0.5", "_shasum": "8b690279e93dc5f98d50a3f1f03cbda84d0d0957", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "8b690279e93dc5f98d50a3f1f03cbda84d0d0957", "size": 5593, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.5.tgz", "integrity": "sha512-GHlcs0E09WeTd0mMK+Azq8j5qbrUNrJvJPJ9kpxmADiKblOIyZcqzLyfiXJUhThAXgInYLkyTWM5mdJOCam1fQ=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.5.tgz_1473080372606_0.41552305105142295"}, "directories": {}, "publish_time": 1473080374422, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473080374422, "_cnpmcore_publish_time": "2021-12-16T12:00:47.919Z"}, "0.0.4": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.4", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "typings": "./lib/index", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "next"}, "gitHead": "da6e0384e8f667fcd10d629cdb9cdee353e7a3b8", "_id": "vscode-uri@0.0.4", "_shasum": "74223c90a99e0fa6557b6c6d82d9287009df1245", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "74223c90a99e0fa6557b6c6d82d9287009df1245", "size": 5598, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.4.tgz", "integrity": "sha512-dlfUwgI8mzfg+psXj4vTMHazir+awJeiiQySUsF/j2nTuhL8JoDWaOfvOdol8yOussOAqvXvTNH51xsx7ds57A=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.4.tgz_1473079903621_0.7160273538902402"}, "directories": {}, "publish_time": 1473079906055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473079906055, "_cnpmcore_publish_time": "2021-12-16T12:00:48.123Z"}, "0.0.3": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.3", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "^1.8.10"}, "gitHead": "8b303f81b29ac984fe80fc72ec0fed4c8204ecea", "_id": "vscode-uri@0.0.3", "_shasum": "04d2ac7796c270e25fba5c62dba44d34963082a3", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "04d2ac7796c270e25fba5c62dba44d34963082a3", "size": 5334, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.3.tgz", "integrity": "sha512-1+yMJwUigWM7ofuXctFBZwzyoJwqMl2fEnhh/T6LTqAl+FWOwNXe53gbNheTotyT4tZyJI6wy0V6TUtNe+yU7Q=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.3.tgz_1465810741297_0.5368822107557207"}, "directories": {}, "publish_time": 1465810743647, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465810743647, "_cnpmcore_publish_time": "2021-12-16T12:00:48.341Z"}, "0.0.2": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.2", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "scripts": {"compile": "tsc && tsc -p spec/tsconfig.json", "test": "npm run compile && mocha --ui tdd spec/", "prepublish": "npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "devDependencies": {"mocha": "^2.5.3", "typescript": "^1.8.10"}, "gitHead": "0857ddca174457cf6833e3704ed7f9e9b3c69040", "_id": "vscode-uri@0.0.2", "_shasum": "c6e788ccc160f8733f52855910d8928d07d4e3c2", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "c6e788ccc160f8733f52855910d8928d07d4e3c2", "size": 5180, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.2.tgz", "integrity": "sha512-McsNQsMvgc5ZNX5IaARltb41WplqazvT5cCjjCRxU3NiEkLqOwSw1GJXKDbKKpaN3LtD7Bq+6k4QOHHrD/vDEg=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.2.tgz_1465369243789_0.7840987844392657"}, "directories": {}, "publish_time": 1465369245802, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465369245802, "_cnpmcore_publish_time": "2021-12-16T12:00:48.538Z"}, "0.0.1": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "0.0.1", "description": "The URI implementation that is used by VS Code and its extensions", "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/vscode-uri/issues"}, "homepage": "https://github.com/Microsoft/vscode-uri#readme", "dependencies": {"typescript": "^1.8.10"}, "gitHead": "33fdb7b078b955e316908b473046382199d39604", "_id": "vscode-uri@0.0.1", "_shasum": "7e4a9009e90f47f1395ebd9ff70a3ba65e83d585", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "dist": {"shasum": "7e4a9009e90f47f1395ebd9ff70a3ba65e83d585", "size": 4572, "noattachment": false, "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-0.0.1.tgz", "integrity": "sha512-aRzSBF/hELD5djU8mRrIKFRxg9H/tybzQO0LGstuXZ7AVQxeTGVP1yXvY28XVaEZnQiUxGnE/hQDRqgtfRkPfw=="}, "maintainers": [{"name": "egamma", "email": "<EMAIL>"}, {"name": "j<PERSON>ken", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vscode-uri-0.0.1.tgz_1465312581712_0.2044140943326056"}, "directories": {}, "publish_time": 1465312583915, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465312583915, "_cnpmcore_publish_time": "2021-12-16T12:00:48.806Z"}, "3.0.3": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.3", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^8.0.3", "@types/node": "^10.12.21", "mocha": "8.1.3", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^8.0.13", "typescript": "^4.5.4", "webpack": "^5.11.1", "webpack-cli": "^4.3.1"}, "gitHead": "48d2d95cf1f793b66f349401050c48b8fc73637e", "_id": "vscode-uri@3.0.3", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-EcswR2S8bpR7fD0YPeS7r2xXExrScVMxg4MedACaWHEtx9ftCF/qHG1xGkolzTPcEmjTavCQgbVzHUIdTMzFGA==", "shasum": "a95c1ce2e6f41b7549f86279d19f47951e4f4d84", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.3.tgz", "fileCount": 13, "unpackedSize": 174876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwf7VCRA9TVsSAnZWagAA5EgP/RelXF8G3NukqdnotoUx\nCmEtvqTZpqWwZvosnH8UIFm+2wkUT5D7Cn6vOC1l8qn4Q3LBOs4w7vv8rE3V\nz7FutxO8N0IYeygqcq0hD2nlYpAtzOA0JxHATPAQRedFFYjsjEsHbnwLiRd7\nxtOkIoLN5Cg33/JJqkrDl6sZ7djdOG/mDCvmIlNs646P+vgNOIORQp2MH8dk\nMkBH7xmwecVNyVsQd60Jd5csxj50K12bM7RqBO7C65/j/vFgh25TV7swzs51\nFOf0w61Jr9lW0MqQRCJaPgHtfB7Zo/nAzfS6DzhB+WPptvEugQLZQGSJirUC\nd7p8IcZPWETrxyRevUQLJ3khMwQtlxHK7T2uwPldpnxu45Z9HL6aTd+1hpWW\nv5miKtCA1yntX001DmPYSzezWDXN2XxmnvjcUStIWDLW3zpy5Ws00ne7IUSM\ncWo7qZSxbgAAl2EeUxWwUYm1yt10DfQZD4qJ6YeCQ8YYCGYiyl/boq0a4wgM\n6zoR0oLuj1fp/p8V4fdBXpXR0807OJEHJALy81xFMM8dY8pLW1wvL66+BGxe\nIA8h0N1ySezM0WynMlVhwERAs4UZvEeRujt67c2FGgInXH59FJxjbNJjhlZC\nCPTYVSZ7egu10YtfzY4Z2VoKI4FsEdj8eD5EXR/Q00WtNnRFE3o/A1G9QeBe\nhz3f\r\n=Pq6N\r\n-----END PGP SIGNATURE-----\r\n", "size": 49466}, "_npmUser": {"name": "j<PERSON>ken", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON>ken", "email": "<EMAIL>"}, {"name": "egamma", "email": "<EMAIL>"}, {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.3_1640103637290_0.7757531914602547"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T16:24:00.027Z"}, "3.0.4": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.4", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "16.x", "mocha": "^10.0.0", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^9.3.1", "typescript": "^4.8.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "gitHead": "ee9df1be9d5c88d5243fbc4fcef96f9a8b3a49db", "_id": "vscode-uri@3.0.4", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-aEmKD6H8Sg8gaQAUrnadG0BMeWXtiWhRsj1a94n2FYsMkDpgnK7BRVzZjOUYIvkv2B+bp5Bmt4ImZCpYbnJwkg==", "shasum": "703c6dd7c0b727ee1c34a1287434138fb52d054f", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.4.tgz", "fileCount": 14, "unpackedSize": 174595, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtEuOCrRbTkZ+CSgmo9SAorKnad2N/mVk07lTlFa7rewIhAI6bjncnXKcOVTEdHFqr2sWnrea6xxP1lKP0BJWXR6r2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjItyfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfZg//eoBeejx28n/isIEY2Zx68ncSN0SaOoN/jfCzmzSEqCBGF/rl\r\nKMBW2oH4StrFaw71nQkWO2dTpQaKr/TFHq+nXyKzixZeZTGmmFtp+YG+6aVD\r\nELik3v6H05jSPvLrkZDrqZA1xiMIxVvSyfunMv/0LQee8P2k4I4y1Gy1d4N/\r\nGMNgNZQX2Kb8JO5NWRBw9j9oT41HyMdV2juuC2YfLsZm/ukxWKpJFRNTIKZD\r\n+67twPtyz5x4WKas4MDM4ifml7u7ycF8C6PA+p9FkD6vRtivINlHkPlWu1nn\r\n1TEIU6ggLPf1smJxSdTqMIloUb3r7XJiDjQL8T61n2FYlXDtqmaMFtwHgOxO\r\nUFxCZOIc8nHA8iGKA6x035d6gDSqkeL3ApAJyXzz4yg/UYKVwb83IJg5Rf/i\r\nrBOfNGFvGIQzvEtmFxLPSToxWTpxjP5cKQ/eP2UX+FF0sZqrAqHjXK4G52XQ\r\nqoJ4r/xGCYsQCjVkVtHYty0sE5lAnRsqn9PfRkXmJp2LjTJO5XkjJfZ5QWS+\r\nXY8W+Rx5GvwDF0ceikITJ/L0agIdRcOMw+omZEwuwx1v5c7rBd+xy/9rNmbp\r\nuw45SAHEyWNMKmXcXoMCbqm0hu6H2b48ZJ4Zl3uAQv4uUMVV6L9Fg0m0rFed\r\no1Czoj/LPzMtMGpWXR8/wS1NPa+s10roF9g=\r\n=ycn4\r\n-----END PGP SIGNATURE-----\r\n", "size": 48919}, "_npmUser": {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON>ken", "email": "<EMAIL>"}, {"name": "egamma", "email": "<EMAIL>"}, {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.4_1663229087553_0.07247197435515762"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-15T08:23:17.742Z"}, "3.0.5": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.5", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "16.x", "mocha": "^10.0.0", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^9.3.1", "typescript": "^4.8.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "gitHead": "be2c3de41709c7523b3cee89513c9e20d99da03b", "_id": "vscode-uri@3.0.5", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-bBp2pi1o6ynwlnGL8Tt6UBL1w3VsVZtHCU/Sl73bRfqjno3jMcVSCybdY+hj+31A8FQOELZJWwY+shLVLtcNew==", "shasum": "3dd5a9e154e7c9a40de5eaa450a0ce59b435e584", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.5.tgz", "fileCount": 14, "unpackedSize": 174792, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICNlAX0/pqfmrdXmHC8EKbjqFQscZt4NA2HKQPB8ArE+AiB6vyqDhKvSTOW9xbpYpoCCaul568a6geT3MupCxobTow=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJIc2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVUg//eH+y8dyolg9DtEGRI9E6Zp5NEmMZJl68xWQayuyY8kJRaG9x\r\n3YcVQrr6YFhP5qaQ3PYMKE4cg5c4EWZ52KDJvQsNXivBGYYOVXlzgRdZFVhV\r\nTcvVSKY/VpQBSCw4+j9yyhuEpG7VFmLehSJVID/M13aAIfYqxjMT8RAa7cCZ\r\nheZY/i3XTLdf6019J7AKcUfifL8bFvdlj8OlPRCjzxGdxWh6GPf2paM6Ee2d\r\n9potEFdM0Uysfn5lLU5vPNuhK1q+uVOD6wvcQkyMkaCPlpI+T52t8pIAg+v3\r\n86EYpRWeT6B+DBX8W/q6uTH6mOr/MiWFKa8AhS3WV/3NzCMWmKMu8kxkvA8F\r\n6IErIK9o0Mr919S75UxuSw+7KlqsstQwLkB9AWE2p5lXyBw+SERA9GbycN4H\r\n5t8pAKZoDlGxgNbKaPqmUQU3IjLe2T+S49GR8VrOB8wxkOlO18LVTYYUufMj\r\nWxGr6ClvyTk4//k33yzNI7k6L1EHWaVzU5iKD07aFBYnMwUIJNbgf4NEAb9b\r\nt9LbGEjYF6wOQ215a51+jmd90XC1QZLl6R67yLihrZSCERAsifz9xFSJVuVD\r\n9O0vhHkEXH8q5Atw7y1tSfN5MVrJhEjRzRiN0REvqXyaDfHsSwyLb/LipyJZ\r\nnx63y3km3WNYb/9O9Zi6KAvPPY9wPIvn5qc=\r\n=Jur2\r\n-----END PGP SIGNATURE-----\r\n", "size": 48937}, "_npmUser": {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON>ken", "email": "<EMAIL>"}, {"name": "egamma", "email": "<EMAIL>"}, {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.5_1663338293975_0.6393500053500909"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-16T14:25:11.208Z"}, "3.0.6": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.6", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "prepublish": "npm run test && npm run clean && npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "16.x", "mocha": "^10.0.0", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^9.3.1", "typescript": "^4.8.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "gitHead": "16c3a80ba3eaeacdd875d7c39a2daa7a6879fddf", "_id": "vscode-uri@3.0.6", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-fmL7V1eiDBFRRnu+gfRWTzyPpNIHJTc4mWnFkwBUmO9U3KPgJAmTx7oxi2bl/Rh6HLdU7+4C9wlj0k2E4AdKFQ==", "shasum": "5e6e2e1a4170543af30151b561a41f71db1d6f91", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.6.tgz", "fileCount": 14, "unpackedSize": 174617, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBy6QP/0AODf1CgwAmtZra/XmKihkGbckP/Hd/m+Qmc/AiAe1zZTIvvs1kyMceWQeIZuRVWGWoOry7lhys+Mk7V3fA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKG4+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmplvw//UPB9qZur5NWIovUcU23M6MZq1T8RrILd+3RjAAaMFSjzl2PP\r\nzsLWbbagZOl1MM0Sf+5/CI5wUIkN7zjZYBRSl/JKgqx9ONy1RrWePXAZWnwn\r\n1eRgHLhWm+vApefwLnpQkQiMw4bHHrJMqV2fK9twUXOKqhl6fR0QTHmgbK1J\r\nvWq5HzzCwMiGVrS0+BkuVUitT+ONOsf43wvZrTUCz1kNwVAXj2azOoNeLRwH\r\n2i3Q9igHaxghX1FwJkH1YvjaeENwiLpZG5MmYlUcDCumsdJvwqhKmJvyo3kO\r\nN0S2xMxfiq50f/XF9KvwfB9mMOKHq+KHUHQaoKMfMHZF9wY84N2/GH1n/XZF\r\nAKmnH0lCfisXSdox7zNsbh+Zyby5YakySEdJaoUYzJMx0XlF3HRKlyAR7yg0\r\neXMf46xQlK3ax/gl2wv3VGvivkgfzvCF2inzxZ416TBBp2y5GEejPHij5wie\r\njdsq2iD1N5tkSRLQSct17YT/JiKvk1ZLYzR0Akm2ttdSBMIm/CvClksN6sSP\r\nz/H8Fo7A237dqBRq3jvkx5ANI8WYMl5JXG/dm8Dpv5x3H0q1TBwzWp+eeqhu\r\ndH/jx3WO1leAmB6+Z8Gnas1FNUftM3S2826+W23so7o3qwZLPb8VwB4xAuFw\r\nCChCvLsHwaFWSfT5FqtH/K0GAcTrb4wPCgM=\r\n=dZs/\r\n-----END PGP SIGNATURE-----\r\n", "size": 48926}, "_npmUser": {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON>ken", "email": "<EMAIL>"}, {"name": "egamma", "email": "<EMAIL>"}, {"name": "a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.6_1663594045912_0.6474359781169607"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-19T13:29:50.898Z"}, "3.0.7": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.7", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "module": "./lib/esm/index.js", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "compile": "tsc -p ./src", "prepublish": "npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "16.x", "mocha": "^10.0.0", "path-browserify": "^1.0.1", "rimraf": "^3.0.2", "ts-loader": "^9.3.1", "typescript": "^4.8.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "_id": "vscode-uri@3.0.7", "_integrity": "sha512-eOpPHogvorZRobNqJGhapa0JdwaxpjVvyBp0QIUMRMSf8ZAlqOdEquKuRmw9Qwu0qXtJIWqFtMkmvJjUZmMjVA==", "_resolved": "/mnt/vss/_work/1/vscode-uri/vscode-uri-3.0.7.tgz", "_from": "file:vscode-uri-3.0.7.tgz", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-eOpPHogvorZRobNqJGhapa0JdwaxpjVvyBp0QIUMRMSf8ZAlqOdEquKuRmw9Qwu0qXtJIWqFtMkmvJjUZmMjVA==", "shasum": "6d19fef387ee6b46c479e5fb00870e15e58c1eb8", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.7.tgz", "fileCount": 19, "unpackedSize": 207447, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2E58fcHSXnRxkS9g0pkUeHywTTPuPG09GxagtfYeifAIhANKIpL9M6FI2y6Kp36TjH35T2sykUp/mTP1hyE3Sl/YQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmDfQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5tQ/9EUncASKmrfbOOo3iNUtOP4/PhgJPwFDcDbhElNZ1WI7UFdSB\r\nOfnZZzxP4jm3fXTG/orZmJZFOp2QAdijIj79DMuOdA9hVT+FbtkpgTl63Wuz\r\n81WkhXhs7wdG22UFALtqWYsSV67qqQ6Uu5uW7wVE7H0QqixhDiFA8Cq+kko2\r\nhfRsFr/XNqD66AaiPAufe/XEcJbmc6VE6e23Xncv1R+89MorqNjnzT4o1npJ\r\nSrABcAmzzqCnD63a1RR+/2AJnCiTVyb1DfEk/f9boGy2g8BQ464fEj3bM1xa\r\nVU+Wf5ny8i/N25lR3SYIGe1p+g7KbGtoP2I/reuchfmB72cIOvY0yswFMeFc\r\nXn0CpqXQnEWUcplWra+VeoEP1jdXMFgFUCxkhOi82hhhiKtx6Xw++/DkSg+C\r\nVrkswHUHyzHUXWvPAshytikqm0IJML8xAMshDFGg2RZlUHY0MV06A+Irq3Y6\r\nCAdwz8TJLGVbhMNCEX9SssDvziGiflQ0F+grG62b9la+Zk1H3qhkVd3+I5ia\r\niCRAz2ScsWt3vCnSeUsEsOLIRkqtjilWvy6NNWon0QcikJFXCSwecfbsxR6t\r\nusSVHwp/ejF7watsmUk+HL07FU3vmJNupME/CKfBm9TQY1shy9pTz7snCtUO\r\nktHXHuronZcL4SSIpQwaF/fmpfWHlfgI85w=\r\n=lbT0\r\n-----END PGP SIGNATURE-----\r\n", "size": 56654}, "_npmUser": {"name": "vscode-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>xa<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sbatten", "email": "<EMAIL>"}, {"name": "microsoft1es", "email": "<EMAIL>"}, {"name": "lszomoru", "email": "<EMAIL>"}, {"name": "vscode-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.7_1670920144640_0.44527592522888826"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-13T08:30:37.193Z"}, "3.0.8": {"name": "vscode-uri", "author": {"name": "Microsoft"}, "version": "3.0.8", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "exports": {"import": "./lib/esm/index.mjs", "require": "./lib/umd/index.js", "types": "./lib/umd/index.d.ts", "browser": "./lib/esm/index.mjs"}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "compile": "tsc -p ./src", "prepublish": "npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^10.0.2", "@types/node": "16.x", "mocha": "^10.2.0", "path-browserify": "^1.0.1", "rimraf": "^5.0.5", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "_id": "vscode-uri@3.0.8", "_integrity": "sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==", "_resolved": "/mnt/vss/_work/1/vscode-uri/vscode-uri-3.0.8.tgz", "_from": "file:vscode-uri-3.0.8.tgz", "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==", "shasum": "1770938d3e72588659a172d0fd4642780083ff9f", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.0.8.tgz", "fileCount": 17, "unpackedSize": 204488, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgddfnXDUm1jmbYwXwzbnIKnlQBHu7XwZfSUfy0jNmQwIhAIfKrab7nxb0yYem/AC8K6ydqV965mk90IBFjGfeJDpC"}], "size": 59270}, "_npmUser": {"name": "vscode-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>xa<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sbatten", "email": "<EMAIL>"}, {"name": "microsoft1es", "email": "<EMAIL>"}, {"name": "lszomoru", "email": "<EMAIL>"}, {"name": "vscode-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vscode-uri_3.0.8_1696498782223_0.5117300543437384"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-05T09:39:42.462Z", "publish_time": 1696498782462, "_source_registry_name": "default"}, "3.1.0": {"name": "vscode-uri", "author": "Microsoft", "version": "3.1.0", "description": "The URI implementation that is used by VS Code and its extensions", "main": "./lib/umd/index.js", "typings": "./lib/umd/index", "exports": {"types": "./lib/umd/index.d.ts", "import": "./lib/esm/index.mjs", "require": "./lib/umd/index.js", "browser": "./lib/esm/index.mjs"}, "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "pack-production": "webpack --mode=production", "pack-dev": "webpack", "compile": "tsc -p ./src", "prepublish": "npm run pack-production", "test": "tsc -p ./src && npm run pack-dev && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "devDependencies": {"@types/mocha": "^10.0.2", "@types/node": "20.x", "mocha": "^11.1.0", "path-browserify": "^1.0.1", "rimraf": "^5.0.5", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "_id": "vscode-uri@3.1.0", "_integrity": "sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==", "_resolved": "M:\\SvcFab\\_App\\MS.Ess.PackageManager.Publisher_App2\\temp\\1122b676-b422-4b85-93f4-ceaa471e4f43\\vscode-uri-3.1.0.tgz", "_from": "file:M:\\SvcFab\\_App\\MS.Ess.PackageManager.Publisher_App2\\temp\\1122b676-b422-4b85-93f4-ceaa471e4f43\\vscode-uri-3.1.0.tgz", "tag": "latest", "_nodeVersion": "18.5.0", "dist": {"integrity": "sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==", "shasum": "dd09ec5a66a38b5c3fffc774015713496d14e09c", "tarball": "https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.1.0.tgz", "fileCount": 17, "unpackedSize": 204476, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDK6qxAFnwUNfEdmEHnVC505cy7tCPcWrYG4rXtsNEWFQIgUf9tIZDXXXexFD6nddbPiY0gmONx1ScTyfBS1VOYviY="}], "size": 59768}, "_npmUser": {"name": "microsoft1es", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>xa<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sbatten", "email": "<EMAIL>"}, {"name": "microsoft1es", "email": "<EMAIL>"}, {"name": "lszomoru", "email": "<EMAIL>"}, {"name": "vscode-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vscode-uri_3.1.0_1738693091587_0.6720759414063389"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-04T18:18:11.908Z", "publish_time": 1738693091908, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/microsoft/vscode-uri/issues"}, "homepage": "https://github.com/microsoft/vscode-uri#readme", "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-uri.git"}, "_source_registry_name": "default"}