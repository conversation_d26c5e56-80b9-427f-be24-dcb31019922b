{"_id": "@rollup/rollup-win32-arm64-msvc", "_rev": "3616653-64c80974cfb30bb281e095f5", "dist-tags": {"beta": "4.33.0-0", "latest": "4.45.0"}, "name": "@rollup/rollup-win32-arm64-msvc", "time": {"created": "2023-07-31T19:20:20.693Z", "modified": "2025-07-12T05:54:27.705Z", "4.0.0-0": "2023-07-31T19:17:54.259Z", "4.0.0-1": "2023-08-01T04:48:58.136Z", "4.0.0-2": "2023-08-01T11:16:31.094Z", "4.0.0-3": "2023-08-04T08:16:52.930Z", "4.0.0-4": "2023-08-04T11:36:35.688Z", "4.0.0-5": "2023-08-20T06:56:47.845Z", "4.0.0-6": "2023-08-20T07:51:37.043Z", "4.0.0-7": "2023-08-20T10:33:24.107Z", "4.0.0-8": "2023-08-20T11:22:15.348Z", "4.0.0-9": "2023-08-20T14:29:01.774Z", "4.0.0-10": "2023-08-21T15:29:59.660Z", "4.0.0-11": "2023-08-23T10:15:47.876Z", "4.0.0-12": "2023-08-23T14:40:22.126Z", "4.0.0-13": "2023-08-24T15:48:32.762Z", "4.0.0-14": "2023-09-15T12:34:24.977Z", "4.0.0-15": "2023-09-15T13:06:52.042Z", "4.0.0-16": "2023-09-15T14:17:14.546Z", "4.0.0-17": "2023-09-15T14:59:05.626Z", "4.0.0-18": "2023-09-15T16:09:57.202Z", "4.0.0-19": "2023-09-15T18:50:56.415Z", "4.0.0-20": "2023-09-24T06:10:37.075Z", "4.0.0-21": "2023-09-24T17:22:23.293Z", "4.0.0-22": "2023-09-26T16:17:18.268Z", "4.0.0-23": "2023-09-26T20:14:22.052Z", "4.0.0-24": "2023-10-03T05:12:45.045Z", "4.0.0-25": "2023-10-05T14:12:43.378Z", "4.0.0": "2023-10-05T15:14:30.170Z", "4.0.1": "2023-10-06T12:36:38.933Z", "4.0.2": "2023-10-06T14:18:39.981Z", "4.1.0": "2023-10-14T05:52:12.995Z", "4.1.1": "2023-10-15T06:31:46.732Z", "4.1.3": "2023-10-15T17:48:25.413Z", "4.1.4": "2023-10-16T04:34:08.071Z", "4.1.5": "2023-10-28T09:23:30.089Z", "4.1.6": "2023-10-31T05:45:12.880Z", "4.2.0": "2023-10-31T08:10:39.058Z", "4.3.0": "2023-11-03T20:13:01.119Z", "4.3.1": "2023-11-11T07:57:53.402Z", "4.4.0": "2023-11-12T07:49:53.088Z", "4.4.1": "2023-11-14T05:25:08.131Z", "4.5.0": "2023-11-18T05:52:11.149Z", "4.5.1": "2023-11-21T20:13:08.293Z", "4.5.2": "2023-11-24T06:29:46.605Z", "4.6.0": "2023-11-26T13:39:12.964Z", "4.6.1": "2023-11-30T05:23:05.984Z", "4.7.0": "2023-12-08T07:57:59.448Z", "4.8.0": "2023-12-11T06:24:52.398Z", "4.9.0": "2023-12-13T09:24:15.898Z", "4.9.1": "2023-12-17T06:26:10.095Z", "4.9.2": "2023-12-30T06:23:26.758Z", "4.9.3": "2024-01-05T06:20:45.826Z", "4.9.4": "2024-01-06T06:38:59.065Z", "4.9.5": "2024-01-12T06:16:12.667Z", "4.9.6": "2024-01-21T05:52:18.060Z", "4.10.0": "2024-02-10T05:58:40.484Z", "4.11.0": "2024-02-15T06:09:36.756Z", "4.12.0": "2024-02-16T13:32:14.611Z", "4.12.1": "2024-03-06T06:03:32.472Z", "4.13.0": "2024-03-12T05:28:31.652Z", "4.13.1-1": "2024-03-24T07:39:19.926Z", "4.13.1": "2024-03-27T10:27:38.448Z", "4.13.2": "2024-03-28T14:13:33.899Z", "4.14.0": "2024-04-03T05:22:50.470Z", "4.14.1": "2024-04-07T07:35:38.046Z", "4.14.2": "2024-04-12T06:23:38.284Z", "4.14.3": "2024-04-15T07:18:30.488Z", "4.15.0": "2024-04-20T05:37:11.543Z", "4.16.0": "2024-04-21T04:41:50.657Z", "4.16.1": "2024-04-21T18:29:56.812Z", "4.16.2": "2024-04-22T15:19:13.519Z", "4.16.3": "2024-04-23T05:12:33.316Z", "4.16.4": "2024-04-23T13:15:06.116Z", "4.17.0": "2024-04-27T11:29:50.741Z", "4.17.1": "2024-04-29T04:57:53.503Z", "4.17.2": "2024-04-30T05:00:43.448Z", "4.18.0": "2024-05-22T05:03:42.401Z", "4.18.1": "2024-07-08T15:25:10.939Z", "4.19.0": "2024-07-20T05:46:13.771Z", "4.19.1": "2024-07-27T04:54:00.447Z", "4.19.2": "2024-08-01T08:32:52.875Z", "4.20.0": "2024-08-03T04:48:50.287Z", "4.21.0": "2024-08-18T05:55:34.070Z", "4.21.1": "2024-08-26T15:54:12.865Z", "4.21.2": "2024-08-30T07:04:25.676Z", "4.21.3": "2024-09-12T07:05:49.160Z", "4.22.0": "2024-09-19T04:55:31.105Z", "4.22.1": "2024-09-20T08:21:53.172Z", "4.22.2": "2024-09-20T09:33:44.742Z", "4.22.3-0": "2024-09-20T14:47:57.146Z", "4.22.3": "2024-09-21T05:03:08.733Z", "4.22.4": "2024-09-21T06:11:21.956Z", "4.22.5": "2024-09-27T11:48:15.492Z", "4.23.0": "2024-10-01T07:10:06.266Z", "4.24.0": "2024-10-02T09:37:19.700Z", "4.24.1": "2024-10-27T06:43:02.743Z", "4.24.2": "2024-10-27T15:40:07.904Z", "4.25.0-0": "2024-10-29T06:15:10.503Z", "4.24.3": "2024-10-29T14:14:07.677Z", "4.24.4": "2024-11-04T08:47:09.121Z", "4.25.0": "2024-11-09T08:37:22.287Z", "4.26.0": "2024-11-13T06:44:59.566Z", "4.27.0-0": "2024-11-13T07:03:14.160Z", "4.27.0-1": "2024-11-14T06:33:11.015Z", "4.27.0": "2024-11-15T10:40:36.197Z", "4.27.1-0": "2024-11-15T13:28:07.844Z", "4.27.1-1": "2024-11-15T15:38:03.382Z", "4.27.1": "2024-11-15T16:07:42.003Z", "4.27.2": "2024-11-15T17:20:03.555Z", "4.27.3": "2024-11-18T16:39:36.407Z", "4.27.4": "2024-11-23T07:00:21.698Z", "4.28.0": "2024-11-30T13:15:47.859Z", "4.28.1": "2024-12-06T11:44:56.897Z", "4.29.0-0": "2024-12-16T06:39:53.358Z", "4.29.0-1": "2024-12-19T06:37:32.055Z", "4.29.0-2": "2024-12-20T06:56:03.306Z", "4.29.0": "2024-12-20T18:37:25.058Z", "4.29.1": "2024-12-21T07:16:02.859Z", "4.30.0-0": "2024-12-21T07:17:15.337Z", "4.30.0-1": "2024-12-30T06:52:17.065Z", "4.29.2": "2025-01-05T12:07:43.602Z", "4.30.0": "2025-01-06T06:36:41.603Z", "4.30.1": "2025-01-07T10:35:53.302Z", "4.31.0-0": "2025-01-14T05:57:42.900Z", "4.31.0": "2025-01-19T12:56:47.836Z", "4.32.0": "2025-01-24T08:27:35.866Z", "4.33.0-0": "2025-01-28T08:30:09.507Z", "4.32.1": "2025-01-28T08:33:18.309Z", "4.33.0": "2025-02-01T07:11:59.808Z", "4.34.0": "2025-02-01T08:40:24.070Z", "4.34.1": "2025-02-03T06:58:14.244Z", "4.34.2": "2025-02-04T08:10:02.853Z", "4.34.3": "2025-02-05T09:22:05.218Z", "4.34.4": "2025-02-05T21:31:11.562Z", "4.34.5": "2025-02-07T08:53:02.736Z", "4.34.6": "2025-02-07T16:32:08.106Z", "4.34.7": "2025-02-14T09:53:59.463Z", "4.34.8": "2025-02-17T06:26:25.882Z", "4.34.9": "2025-03-01T07:32:37.070Z", "4.35.0": "2025-03-08T06:24:44.703Z", "4.36.0": "2025-03-17T08:35:44.165Z", "4.37.0": "2025-03-23T14:57:07.923Z", "4.38.0": "2025-03-29T06:29:06.074Z", "4.39.0": "2025-04-02T04:49:32.369Z", "4.40.0": "2025-04-12T08:39:35.968Z", "4.40.1": "2025-04-28T04:35:24.474Z", "4.40.2": "2025-05-06T07:26:53.317Z", "4.41.0": "2025-05-18T05:33:33.495Z", "4.41.1": "2025-05-24T06:14:31.783Z", "4.41.2": "2025-06-06T11:40:32.620Z", "4.42.0": "2025-06-06T14:48:13.088Z", "4.43.0": "2025-06-11T05:22:38.072Z", "4.44.0": "2025-06-19T06:22:57.281Z", "4.44.1": "2025-06-26T04:34:16.917Z", "4.44.2": "2025-07-04T12:56:08.839Z", "4.45.0": "2025-07-12T05:54:03.804Z"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-0", "os": ["win32"], "cpu": ["arm64"], "main": "native/rollup.win32-arm64-msvc.node", "description": "Next-generation ES module bundler", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-0", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-TzG3kpfIKdQOAq+gURHANATZj5Do6T6323V+F/ATkNWiG1cWR5wCtMfYfuNcVXm26z5r2cI3pi5EiOM42jpmDQ==", "shasum": "d1aa9e9bb7d036898cce66e2a628866fa9496a1d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-0.tgz", "fileCount": 2, "unpackedSize": 735, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt50YvfzSJ2wa315v4QEYU5qQF+UGR7g20eiMIxdUNygIhAPnAWcmTlomgLiPSoiHO1gXnTtkIdci5gwg2wYY7bbhs"}], "size": 497}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-0_1690831074113_0.6666528336610902"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-31T19:17:54.259Z", "publish_time": 1690831074259, "_source_registry_name": "default"}, "4.0.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-1", "os": ["win32"], "cpu": ["arm64"], "main": "rollup.win32-arm64-msvc.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-1", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-fjfp9qaJha1W4Zvcns7csAMPIlJMDvNid2dLFwBKKFppGV2NVS1mTq2q5Z2YC4HG9bOT8shPmYnq0mN99HuE1w==", "shasum": "5104a39c0aeda01935065498ad16c0c941e91689", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-1.tgz", "fileCount": 3, "unpackedSize": 2744880, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5mfDwYTiuJUqDKqfwQfLNXlOXcgCGncYI9QpsP6ubUwIgPfTRNr1tCeAOzkU8HewerwJOT88kwaBYIJCMomNH474="}], "size": 1002052}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-1_1690865337886_0.02801411964037781"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-01T04:48:58.136Z", "publish_time": 1690865338136, "_source_registry_name": "default"}, "4.0.0-2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-2", "os": ["win32"], "cpu": ["arm64"], "main": "rollup.win32-arm64-msvc.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-2", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-U298hzXxAo3rMxXA0fLpzFgGvHRSBLXw+YAEAfHATizCqUO+6x0AWooIqHTp3EpE0LROSOX96k306A7wlXq1Sg==", "shasum": "8ea8ad6a50ea492651e303ecc9d4a703cf8e057a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-2.tgz", "fileCount": 3, "unpackedSize": 2744880, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFpc5jWHa7BVZKVo9IzdVvtsu1IjT/1lWmvu0CL0N1SRAiBkZX9FpIZV4l4QMmWpILhSsHxuB985dBj2AyxtJGGZPA=="}], "size": 1002052}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-2_1690888590833_0.5699681687683182"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-01T11:16:31.094Z", "publish_time": 1690888591094, "_source_registry_name": "default"}, "4.0.0-3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-3", "os": ["win32"], "cpu": ["arm64"], "main": "rollup.win32-arm64-msvc.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "readmeFilename": "README.md", "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-3", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-hHNMqTLtPRa7lkJYIRivR/K6kXPN3NiSaZoZUb6y9yYaCDYNZUufE04fIqjJDCDLLI+EbRrAQEcv+6pvUw9CXw==", "shasum": "09bda3dca670917f19ff54e4f0676f3dd528d2fb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-3.tgz", "fileCount": 3, "unpackedSize": 2739248, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXnzoLS4nbEGPIJ/hoD6ksEZ4xMigU91x5aESD5P68CgIhAKl6No+qlpMB2lOwIoLy7+QZBxRwc0So1RFNEWUzOafn"}], "size": 1006578}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-3_1691137012771_0.39221954694134276"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-04T08:16:52.930Z", "publish_time": 1691137012930, "_source_registry_name": "default"}, "4.0.0-4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-4", "os": ["win32"], "cpu": ["arm64"], "main": "rollup.win32-arm64-msvc.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "readmeFilename": "README.md", "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-4", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-FIsLT66CcQr24+<PERSON><PERSON><PERSON><PERSON>ai8d4cGyDBowoKLad56MUEfvo05+9CZLhr8f+j9lqxdhgnN2L5MrIV8yJE93Ro/vlg==", "shasum": "09b586105ba65b114f8b9a08554833cf9b06494d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-4.tgz", "fileCount": 3, "unpackedSize": 2739760, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAAxta9dBHj3NNjbxoQ6QM+MZWS8taRzFqfwdDTkALKgIgP29B5QXwcwff6nuhfOgxloToNBLl8H1w59DjIi1N8A4="}], "size": 1006462}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-4_1691148995499_0.8626498580790858"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-04T11:36:35.688Z", "publish_time": 1691148995688, "_source_registry_name": "default"}, "4.0.0-5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-5", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.win32-arm64-msvc.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-5", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-2G7SH+MbZ1wjiilx/3FGDffGZi28KD//0t2dVQOewA9kbiZbE0zQFEgtrsiCvGstYCMr6Om5sexad7eCiiRF/A==", "shasum": "37a0be7063c9fbd049603ab9f10feec55cd30715", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-5.tgz", "fileCount": 3, "unpackedSize": 3130019, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFAuIT1IBxq66JIxd4iFW2NNeA5Sl3mcyISkFLxdT7ZyAiEA0R8RmjpjR4ccbrNvQpc6DiFmg/BlQTEYH1Cb3qyaQF8="}], "size": 1157323}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-5_1692514607616_0.8612684052391522"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T06:56:47.845Z", "publish_time": 1692514607845, "_source_registry_name": "default"}, "4.0.0-6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-6", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.win32-arm64-msvc.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-6", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-z08+47Yh6tp2dZBeR6PxkbTjJLaIdXFiBY/S8U3Q3ya0Jx7UQApEP7azvspJdcIRdD7JbW5J1JRZ05GNlBBaJw==", "shasum": "f782c9f04732cfb9fa11671e280543405c543972", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-6.tgz", "fileCount": 3, "unpackedSize": 3130019, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuXS6sLwGKYwYG9vM2GhYmykWgCEvOwzdS0L2vsBiRogIhANRvWe3uSuFGch0uOkeQPUr9UXU6v5OsLNE3Zg4MknW3"}], "size": 1157324}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-6_1692517896757_0.22756201309832091"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T07:51:37.043Z", "publish_time": 1692517897043, "_source_registry_name": "default"}, "4.0.0-7": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-7", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.win32-arm64-msvc.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-7", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-s9UNX4tod5DCDtOt0R1fAjpwZelflRulbuS6Z53IA6Xns5BdlSfdCZpGLFmlUx7c0WCy2R5bxyhY09shI2VAlw==", "shasum": "fa08a29e2ba3b71a82f9436a47f8f34a6d84b42a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-7.tgz", "fileCount": 7, "unpackedSize": 8584941, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFi+/BSC3r9fXm0bjAPtKJyTd+LJqYYpzx5dqin5+3fHAiEAlCSvdfO/OZyL0jFS/iWB/4ZqjAI5+ziJC9sVEv33LTw="}], "size": 2416966}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-7_1692527603923_0.2084538362547219"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T10:33:24.107Z", "publish_time": 1692527604107, "_source_registry_name": "default"}, "4.0.0-8": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-8", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.win32-arm64-msvc.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-8", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-mdlbEf6ElLzjevxjTYH8OaiKUNQoffZC2YYbKfVEzwPSQbZOe3BNutoW9Yvy3z8niOm90C6LTJOUxA3Oag66Gw==", "shasum": "25d6dcfc9e689e1f9a792bcc47710afb7fd97e0a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-8.tgz", "fileCount": 7, "unpackedSize": 8584941, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNAJqBXe6Op2rVJoEr2NNoZ1KHbVu2Y7KoPCXSVRqluwIgSSSdRyePJJP/hdJZx/JOON1p5tgLsktQ9sRfJo3umh0="}], "size": 2416799}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-8_1692530535010_0.9231160738893072"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T11:22:15.348Z", "publish_time": 1692530535348, "_source_registry_name": "default"}, "4.0.0-9": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-9", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.win32-arm64-msvc.node", "default": "./wasm-node/bindings_wasm.js"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-9", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-23dVTBJyMjrXfYSHoFrq4x7xMmjE2aU/6JfVpIof29/MHzRzn3OHHiRdg8GTHl05SYK2WZBAq8SS/8zprrVOuQ==", "shasum": "5479a0d763d46ebbe681654f9e41ec232e0d9bc2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-9.tgz", "fileCount": 7, "unpackedSize": 8584941, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4iY3e7A8bHpHeNlC6uUBwP533nucp98em0vk7rJFdGQIhALFJzwOpJrs/IyUnIQz+nKiPszEHW0Qyflb9fgDPoJ/E"}], "size": 2416838}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-9_1692541741461_0.6956883954924955"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T14:29:01.774Z", "publish_time": 1692541741774, "_source_registry_name": "default"}, "4.0.0-10": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-10", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.win32-arm64-msvc.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-10", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-ytyIBfpA5BmU616Q+NXeliapb4PSIILgUjwhs8xaI6GyXCsdpcestoLazjHcqlT9PGNsrR2iitsn4wi+cgVvjQ==", "shasum": "0e214f90b79b50a5cdf018d29473e4f286974033", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-10.tgz", "fileCount": 7, "unpackedSize": 8593353, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGBnBGSUhhKXzaCrF9px4sKU1Xycc92dENI3+52ig+DVAiEAoiFlJNhNfWKJwsj+0nz7sLmL02ShMBxOMOfSCwX+FpA="}], "size": 2420457}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-10_1692631799393_0.10567629667551381"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-21T15:29:59.660Z", "publish_time": 1692631799660, "_source_registry_name": "default"}, "4.0.0-11": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-11", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-11", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-XioucLV16m1PR6FVxIva8t9k1UpqCqs+w7QGjs5pIp4qGMLTtW8U1HqYdfIh7O8Hn1qxYTrG1osT4rLJX8kc7g==", "shasum": "7700d703b1dc82fd5e78623e02b5450c75af4375", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-11.tgz", "fileCount": 3, "unpackedSize": 3148340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5fIFVkHaCgoi52+jPr8ekEWsuHkHF3PNDXiL1xTSHQwIhAIvTBgeiqsBT+ifszBNsUha+nzjVMWOD5UceuQRTfHy1"}], "size": 1164641}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-11_1692785747614_0.24166119912177608"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T10:15:47.876Z", "publish_time": 1692785747876, "_source_registry_name": "default"}, "4.0.0-12": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-12", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-12", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-CAnKKJkAKIHR0yK/fFpXtb4EP0gTkXtV8l0PpLVdZmucJl5vK7aHpsGorq+FZ+9RiYU6MBorxdU/Uqzf/+Akaw==", "shasum": "76d66a2da668bafae61fe9a492bf881750603a48", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-12.tgz", "fileCount": 3, "unpackedSize": 3148340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMlYAfkkY+F9+IUOQ3gtVt6+kk61O+YphTw3fpNNCdiAIhANgt8AbvL1IM+Z9owp6Y9tr4j2a06kH5Qeu0LruIa19q"}], "size": 1164618}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-12_1692801621887_0.1654772356158989"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T14:40:22.126Z", "publish_time": 1692801622126, "_source_registry_name": "default"}, "4.0.0-13": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-13", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-13", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-ZMLLhPv6lrlPtBTCB6WI0DFZkNTusuEO114Vou+ZcJTKvRP2gdrDzntXBTNfiph+Lgju0mrHH7Mal8Qlf/1Pig==", "shasum": "d2e12c4fb51c9ade7ed738fb84f08161276c4bfd", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-13.tgz", "fileCount": 3, "unpackedSize": 3112500, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyYp8Cuqz2Yjdovmbwz2uR+L8wK31FNdz1S9RadT4R9QIgWabxZxFd9wRXZen8PpkmQiBPHVYnDN4VdPJn6N7ykGA="}], "size": 1152493}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-13_1692892112465_0.20887324354676973"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-24T15:48:32.762Z", "publish_time": 1692892112762, "_source_registry_name": "default"}, "4.0.0-14": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-14", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-14", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-0m6xFB7qd0WYLUGvHcZeZ7LBVoLegXaMssD0GZ2RSeOwJjkRnRagJgsm+bJN07H3INshVC9zPlrJcgBG4v26dQ==", "shasum": "008045105bcab665f22c029ab8622cb8b48a94a3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-14.tgz", "fileCount": 3, "unpackedSize": 3127860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPQ9kDUv4Gw1OGltZyJBpKNMzWMYL/Ccn6QkTOT4SrvAiEAh0+H0QR05sefH3nzb2W3UM6d4iN7VZ1aXKVLNzcLGRQ="}], "size": 1145535}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-14_1694781264646_0.9267406390701576"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T12:34:24.977Z", "publish_time": 1694781264977, "_source_registry_name": "default"}, "4.0.0-15": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-15", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-15", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-vPH0NYQbt3D8/tv1QiOwFgdD8qaGAyHJDmh3m4FpDUdaWElIvPgnHqSz3SVLkCSJrBfAfBuwteXt8LqHORt94g==", "shasum": "d3ca1dc364ef0f8a1faa5f982b514d04c99918df", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-15.tgz", "fileCount": 3, "unpackedSize": 3127860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrGYLyxVziEXvTCY6d4SUxd4NAh/BvuNb3AsBq6vnkbQIgHGIfftYfff/HVhQqjkLM8RREAvDEoPLgCVB6HKIdAU8="}], "size": 1145535}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-15_1694783211801_0.5260590330212929"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T13:06:52.042Z", "publish_time": 1694783212042, "_source_registry_name": "default"}, "4.0.0-16": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-16", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-16", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-pEanYAzKkT1qPn6375AhIyPnpOG35R+3wDboyiXPsLuynAmLjUc1YiPNtIwvCGteNAFWS8iGwof3j0jJBwHTjA==", "shasum": "7f990c044b77597d39d266fddab420987e70202f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-16.tgz", "fileCount": 3, "unpackedSize": 3127860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnSzRGMeizeJb8FaedTx0ehWuBAM9ziDYUCfTHhehA8QIgA5HYRD30MCn/w1ASh0yl/uNQMa0CiBF1k/NXjEVf5zY="}], "size": 1145534}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-16_1694787434317_0.884666219383546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T14:17:14.546Z", "publish_time": 1694787434546, "_source_registry_name": "default"}, "4.0.0-17": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-17", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-17", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-9B730yqHhwEvlwZKmkhjXU9Tmzprd0Mxw4s2FBD5XBT2boNIhVm6qWm2FfItwyuahHiXABZxZ2tgMG4eImAKJw==", "shasum": "1b24a2a758aa8a01475edd88f16303d821bb4bdf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-17.tgz", "fileCount": 3, "unpackedSize": 3127860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDI+R54IgQ7PiGvAA3mpZFk4dhCNe2Nf3rxTrU3qh9BfAIhAI5enSsddHzmYfQ1aTYa+dfqf9jYtzkRZO/3q3GprOlb"}], "size": 1145531}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-17_1694789945341_0.07104057242221984"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T14:59:05.626Z", "publish_time": 1694789945626, "_source_registry_name": "default"}, "4.0.0-18": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-18", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-18", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-a2IAxT9MzKpqi30jkxL5rRuaSKlxr4a1LaY6UoN75Vclx+luWarRo9DVCPiAEzmosVDrWJWSvsWsNcMPL5wn4Q==", "shasum": "7056b7903c8290a07238aa1416cc38afee63c099", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-18.tgz", "fileCount": 3, "unpackedSize": 3127860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3hyXcm+pqmaVgeEP7OjgnVQHfkBe8QQQRThlHOWyvIgIhAN3j5ju6bSlT6O4RzBogUPGOCPUwWDLvQKizk0+l6D6L"}], "size": 1145535}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-18_1694794196912_0.6857637771201748"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T16:09:57.202Z", "publish_time": 1694794197202, "_source_registry_name": "default"}, "4.0.0-19": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-19", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-19", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-/XykBfL7naa1xS19Uj9LdkkzyXxFniRVHfsfGnI9AxzmZPz32hJVFdT5nLU86UixmHaJPt+xydwacnZ6yxs+Bw==", "shasum": "9523dab0ff53e8367f4ede40fdc00889ba2a1ff4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-19.tgz", "fileCount": 3, "unpackedSize": 3127860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGUMaGCFobi24aqCRUR/ZY26ebm8L4wmL04T+Tl6ULE/AiEAiJGdQsyTNCVe5GfBb2+1S6eZJDxKMQ4ebPn9juoe3Vo="}], "size": 1145535}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-19_1694803856166_0.5905525803763616"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T18:50:56.415Z", "publish_time": 1694803856415, "_source_registry_name": "default"}, "4.0.0-20": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-20", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-20", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-4lh051uRJPTKXjqRaW3zWlYKuR0RmI+bssKgaEQkO0crh6Bzv4aG2qToDe4nT43CLU5TZT1LY1z7DihgANWvvQ==", "shasum": "aa61a4efa56f31f8ce9e9d4c5dd80572dfaaf484", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-20.tgz", "fileCount": 3, "unpackedSize": 3119602, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKrzFGYZ1gWlBcwR+AdYLuej818XSSnHXnrWk4cDsKvwIgcf7ZFV2FcsB79oCmuwsgp6E28oowsy9tQxzQrZaXxlQ="}], "size": 1142997}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-20_1695535836769_0.767428283922363"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-24T06:10:37.075Z", "publish_time": 1695535837075, "_source_registry_name": "default"}, "4.0.0-21": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-21", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-21", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-fjHgYLZrSrY8N4jut6SpA2hpX+2MCqZnprjJd3fIBxoarkJ1+unN+PYTwfQAmz/7bOCb+uVdWPLm5ywjPS4SGg==", "shasum": "7da95939af0b32a66c2aad991f3f89c3fc0812c8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-21.tgz", "fileCount": 3, "unpackedSize": 3119602, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHxUvQ6m+VtmtdGJKWnVRzegn24WWPP8eNn3g339VUARAiApejQwjYt/K7/XQVVSl6Fk16S8cbuGAMHWkoNMPaA1yA=="}], "size": 1142998}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-21_1695576143058_0.47884103232151065"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-24T17:22:23.293Z", "publish_time": 1695576143293, "_source_registry_name": "default"}, "4.0.0-22": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-22", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-22", "readmeFilename": "README.md", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-I5tzD1RqkJyiSrOpWwURFh2WQeICfgX+s0ZPmG2TEpo8fAQbWmNacoiEmmaotaXo7D6gCQPTMuvNpFXkr0zUhQ==", "shasum": "77a56539f65e812832facc9a9e96ba6aa6f538b6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-22.tgz", "fileCount": 3, "unpackedSize": 3124210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD65RiWyqxsDlebFqL7V0YaeaxlOQq832SBQVVQM5v3tQIgF4mhxp96kah35J6yT7zYYYfJxjjZ6BpxpqOPAvebsx0="}], "size": 1145235}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-22_1695745037942_0.23863511964677087"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T16:17:18.268Z", "publish_time": 1695745038268, "_source_registry_name": "default"}, "4.0.0-23": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-23", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "readmeFilename": "README.md", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-23", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-pEWZzHhioEt8HrM5PV7YiDbPmrZ8ceWtmoL/7xhvOgQ4xD3SCr5kych1gv1YKuWxLNKVl2S3SanARSvPQVcdog==", "shasum": "11f720784a91e69c9deb2229d626e35d7ee1bafb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-23.tgz", "fileCount": 3, "unpackedSize": 3124210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBjTVOCNsHjagwzzTxugg8rWYoBTu7XzM2mG+tep0ceDAiEAkpyN+utMW02W//B7mk72C8nudZvHm7rR1ejz1a68pqU="}], "size": 1145247}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-23_1695759261741_0.5669274319164077"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T20:14:22.052Z", "publish_time": 1695759262052, "_source_registry_name": "default"}, "4.0.0-24": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-24", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-24", "readmeFilename": "README.md", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-xT8djNMxVB84cCm1XILpQXkMcu8S+GBKjurXM4sc6eB1FQpFpcTAOsuQSg9xOhfPqm1xa7qqXA6ZpUhoUMboVQ==", "shasum": "c6ad6b8c9bad415ae7fc6077597618eb6af0a3ea", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-24.tgz", "fileCount": 3, "unpackedSize": 3309554, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbZslG++SqaICvaMxct80dseuTDqfulvAqEypwpOSZeQIgTt8t5KkkwA23w8/OThFtgMAOIgCrWobA3h3yrAAN+rg="}], "size": 1160375}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-24_1696309964747_0.8008757789154648"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-03T05:12:45.045Z", "publish_time": 1696309965045, "_source_registry_name": "default"}, "4.0.0-25": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0-25", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0-25", "readmeFilename": "README.md", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-3iXMflVqSCDjmuYKwHllmS9nLLF9qKiQB5zeZDyhlvsV2QDIqdu6hb5Nv55rmR1zGEfYVSszOL7RGtCVgbecaw==", "shasum": "ae42aec04841626b2e458c2f75b48eabd62a741d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0-25.tgz", "fileCount": 3, "unpackedSize": 3306482, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKg19jGIUZwbpdJAcRyfRfCTTA2g+6pWDamVuDc9C0JgIhAL6RisFbNzOZ7jrftnx/inB9daAzfBNp8cI5KgODBPSA"}], "size": 1159850}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0-25_1696515163112_0.041415107232930515"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-05T14:12:43.378Z", "publish_time": 1696515163378, "_source_registry_name": "default"}, "4.0.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.0", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-h2r04SsqVMbmaIRSMN3HKQLYpKewJ7rWQx1SwEZQMeXRkecWFBBNOfoB3iMlvvUfc3VUOonR/3Dm/Op6yOD2Lg==", "shasum": "98cab6ea9398adbe07573acc22a69661f1b22a67", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.0.tgz", "fileCount": 3, "unpackedSize": 3306479, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfwgAvvwoqg6qO10BWbvk9PWiP4CXGNGjaMv7RQlJG8AIgVzUXbL+DUNBwEuxLg/qyhVtPqL/Mt9giSTvU+4iOObs="}], "size": 1159850}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.0_1696518869901_0.6416132453333874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-05T15:14:30.170Z", "publish_time": 1696518870170, "_source_registry_name": "default"}, "4.0.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.1", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-AJ0NKL2X/eMq2ED7APz1zDrf70sqIJPdtD1zqgGnqKN0ipn8iDQCiNF9xRolx7ImH6CF+jA1n3/VzgD4a8eVNA==", "shasum": "d17f3f54886f38490c24eb22d465ae3cc6c7feb2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.1.tgz", "fileCount": 3, "unpackedSize": 3256303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC784qTiXlqVKH5LnNEJUu7doHJQk60+GQOql8k1/suUwIgemzPIPoK9tzzqoMp7WjQyQJTud5rnGM82/Nh/rY9wZ0="}], "size": 1150587}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.1_1696595798645_0.35549581832993615"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T12:36:38.933Z", "publish_time": 1696595798933, "_source_registry_name": "default"}, "4.0.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.0.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.0.2", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-ZkS2NixCxHKC4zbOnw64ztEGGDVIYP6nKkGBfOAxEPW71Sji9v8z3yaHNuae/JHPwXA+14oDefnOuVfxl59SmQ==", "shasum": "853acc6ec7eb573fba1161c5707d7e1d1c12b3b0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.0.2.tgz", "fileCount": 3, "unpackedSize": 3256303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHGwpN87+Sq++qxeVi1y6mtBIZADrfCX9jUg59zqKkfQAiEAuch0bRHTQJ/t+BNtRTl7f1LEdTPNFc7hHCYgpcl0reA="}], "size": 1150330}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.0.2_1696601919707_0.5564613271055543"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T14:18:39.981Z", "publish_time": 1696601919981, "_source_registry_name": "default"}, "4.1.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.0", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-7lqRlilcPpA88eCtFxl7zaeRH4nZlPy/qECsrRe9JQzqANa3wPYmjgwLQbduN8PuZ9dQ6HD09BFm7v+Pc0dNTQ==", "shasum": "09563571c801091472552f2413eef47759679d8c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.0.tgz", "fileCount": 3, "unpackedSize": 3365871, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDv5wuAGzIAlp0/rEbmJzcUq7deAlPYwTWCIc2CILYmxAIgMOtbkeUK/PvNoTaGNhDqKO458Q86+pze5cudAU5fKL4="}], "size": 1181820}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.1.0_1697262732650_0.032722337828594794"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-14T05:52:12.995Z", "publish_time": 1697262732995, "_source_registry_name": "default"}, "4.1.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.1", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-q04G+SLoMmLewIHc2ZVrsAzskXRXnzc5lUqXpOvwzgBSG+cBI1aBd+sLCGiOWezwMvcoa9yiPrkg+s5t0cZqSQ==", "shasum": "af47c951ebd197931953df735ae4fbb0d77ffce5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.1.tgz", "fileCount": 3, "unpackedSize": 3129327, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEVcXB35CSJhDpOm1a93q9sLit0nBjJEXwoCdcmfnr7zAiEA/aTsMkMbEot/n206DjOh9S+OETbnTXo+R9TxRkvRHvM="}], "size": 1178143}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.1.1_1697351506475_0.5721399132761051"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-15T06:31:46.732Z", "publish_time": 1697351506732, "_source_registry_name": "default"}, "4.1.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.3", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-rTxOCH2ZLWkbZJh621qNmS4zDjvIOnvwXdd0Zvkm8twvVoyWATbn6q/bI3bXjQeV7mEASXU1atUWzdeovTcrrw==", "shasum": "f65b2112e3fd581895d1572e278a74eeefce24e2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.3.tgz", "fileCount": 3, "unpackedSize": 3129327, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXPxsKvigh98tZWdc+6eMoGpAKpkIJGEGdGqvq0I7TBgIgTpSFQOuI9ChcghfPWCfpdB0hwnuCbzR/kSPQYF/Dwto="}], "size": 1178143}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.1.3_1697392105066_0.8369470864283883"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-15T17:48:25.413Z", "publish_time": 1697392105413, "_source_registry_name": "default"}, "4.1.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.4", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-ZY5BHHrOPkMbCuGWFNpJH0t18D2LU6GMYKGaqaWTQ3CQOL57Fem4zE941/Ek5pIsVt70HyDXssVEFQXlITI5Gg==", "shasum": "acb619a959c7b03fad63017b328fa60641b75239", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.4.tgz", "fileCount": 3, "unpackedSize": 3097583, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICLC/SgfmQ+tBZqCJEAeh9FlRCwaNk7MTqqJgTotJWmeAiEA7urtXgwwcOSQSWB78L7PsXfOv8SB/Q8nYrZDt7lTPKI="}], "size": 1172815}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.1.4_1697430847834_0.18828884575988836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-16T04:34:08.071Z", "publish_time": 1697430848071, "_source_registry_name": "default"}, "4.1.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.5", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.5", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-5oxhubo0A3J8aF/tG+6jHBg785HF8/88kl1YnfbDKmnqMxz/EFiAQDH9cq6lbnxofjn8tlq5KiTf0crJGOGThg==", "shasum": "d4a674b472b0a31d8a8f9406b79933a6ccdb9efb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.5.tgz", "fileCount": 3, "unpackedSize": 3116015, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD01hKCwUUFZSg9XkEvij1DpHDdvSNdQAcg7kCbUqL+aQIgeWWgq+nhecouL+MPStARPp9oBKWCM1oGFI/QYZ26ReU="}], "size": 1171784}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.1.5_1698485009646_0.7315548184835701"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-28T09:23:30.089Z", "publish_time": 1698485010089, "_source_registry_name": "default"}, "4.1.6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.1.6", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.1.6", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-lEpjGR/ZeX1fjj2ho9p5OT9Xc+egePdpB/S7ZPjjct9GctVpHgwfbxARAOMiCuZ8/fWkTERg+4qs1MG7Dm8cNw==", "shasum": "7ec7aa0d21395d8e54c8c67bb7b7a0bcb2990f62", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.1.6.tgz", "fileCount": 3, "unpackedSize": 3116015, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA+qFSDr/3mM4MmNzgEIqHn2wXQETieNAAa4Yw03KEAPAiEAoRNpqKGq0BXFuBLwRAsEXPqtca8uwdl4mk1Zl9QCa0c="}], "size": 1171785}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.1.6_1698731112588_0.3764098072632913"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-31T05:45:12.880Z", "publish_time": 1698731112880, "_source_registry_name": "default"}, "4.2.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.2.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.2.0", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-vYxF3tKJeUE4ceYzpNe2p84RXk/fGK30I8frpRfv/MyPStej/mRlojztkN7Jtd1014HHVeq/tYaMBz/3IxkxZw==", "shasum": "0047005faab99cdea50b594f76388ed09ed44650", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.2.0.tgz", "fileCount": 3, "unpackedSize": 3131887, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFQvJCiFzwJPiC+ozqJNmKDAaPMWZMnd6XnLLpcdVNquAiEAoN3duslYItSRt339m5vdIIp6MT4mHgkPGfnhMQ73a40="}], "size": 1179613}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.2.0_1698739838769_0.8410763806868988"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-31T08:10:39.058Z", "publish_time": 1698739839058, "_source_registry_name": "default"}, "4.3.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.3.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.3.0", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-OKGxp6kATQdTyI2DF+e9s+hB3/QZB45b6e+dzcfW1SUqiF6CviWyevhmT4USsMEdP3mlpC9zxLz3Oh+WaTMOSw==", "shasum": "1a36aba17c7efe6d61e98b8049e70b40e33b1f45", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.3.0.tgz", "fileCount": 3, "unpackedSize": 3124207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEokxloOUM8UusagTzidU7KL2KJ+OkEQMOKqPw4L3an9AiEAvYewucc85P/5EJFK+8u6SD3sKkK9COGvAxK/2YOshMo="}], "size": 1179255}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.3.0_1699042380883_0.18505058874675906"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-03T20:13:01.119Z", "publish_time": 1699042381119, "_source_registry_name": "default"}, "4.3.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.3.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.3.1", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-aYKKmlrLL7C0oY43B2Q4uMIlfF1BsSlSYf3R7q7SGB/SrK7Tkj2DHuxqBSYuFqSxuYuAP4PaHt230McvMpZg5A==", "shasum": "fae8d013b3b8ea75a7a37fa82c89ac0243bbf07c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.3.1.tgz", "fileCount": 3, "unpackedSize": 3206127, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/NDrLnINWGvLaJH0v79Kc0ppCxItQ2fC9tYqkKJU06wIgXEWHMmg3FTKhJShONN+nQdAWVFSazslRN2SNU5fVgDo="}], "size": 1159631}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.3.1_1699689473122_0.2777425092835788"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-11T07:57:53.402Z", "publish_time": 1699689473402, "_source_registry_name": "default"}, "4.4.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.4.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.4.0", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-jnoDRkg5Ve6Y1qx2m1+ehouOLQ4ddc15/iQSfFjcDUL6bqLdJJ5c4CKfUy/C6W1oCU4la+hMkveE9GG7ECN7dg==", "shasum": "acabac95fff647d666a5d649909612aad72bd3d0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.4.0.tgz", "fileCount": 3, "unpackedSize": 2856431, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYCO4J6i6QUVbjK+0/GICfnKeBefu8fc/mXX6IuoiphQIhALE5x/SS0g68ZoPofvVtrp09/uIfN3k5YnOy5qamOgI1"}], "size": 1031046}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.4.0_1699775392871_0.2329971354979481"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-12T07:49:53.088Z", "publish_time": 1699775393088, "_source_registry_name": "default"}, "4.4.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.4.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.4.1", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-Hdn39PzOQowK/HZzYpCuZdJC91PE6EaGbTe2VCA9oq2u18evkisQfws0Smh9QQGNNRa/T7MOuGNQoLeXhhE3PQ==", "shasum": "0f0d0c6b75c53643fab8238c76889a95bca3b9cc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.4.1.tgz", "fileCount": 3, "unpackedSize": 2856431, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBeR8aLAxHghcRjQUdEycrEe9QeXWcRhIE/dZG8RENgwAiA7ImVMSroKuXHY4d/vsBMB0FEIQNHFPNWhSZtPRPDEmw=="}], "size": 1031048}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.4.1_1699939507955_0.15938605462077038"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-14T05:25:08.131Z", "publish_time": 1699939508131, "_source_registry_name": "default"}, "4.5.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.5.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.5.0", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-xaOHIfLOZypoQ5U2I6rEaugS4IYtTgP030xzvrBf5js7p9WI9wik07iHmsKaej8Z83ZDxN5GyypfoyKV5O5TJA==", "shasum": "f145f10c33aa187a11fd60933465be46667e6e42", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.5.0.tgz", "fileCount": 3, "unpackedSize": 2836975, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGwKxLWz5+L8VzxFhqgzYyZ5uI4thq2CYqO1jzcPVivQIhAPGzYPtkHa/rbKd0pfb6WoZezI5ahc7lFCWAsw1K5ycz"}], "size": 1021902}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.5.0_1700286730847_0.7552241614908168"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-18T05:52:11.149Z", "publish_time": 1700286731149, "_source_registry_name": "default"}, "4.5.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.5.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.5.1", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-ihqfNJNb2XtoZMSCPeoo0cYMgU04ksyFIoOw5S0JUVbOhafLot+KD82vpKXOurE2+9o/awrqIxku9MRR9hozHQ==", "shasum": "df70597f6639549e79f0801004525d6a7a0075e4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.5.1.tgz", "fileCount": 3, "unpackedSize": 2837487, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPnccC3dPtIxnKZl/z7NMDBmdGXOYRMefbrn1VHtUlhQIgXllrRgsyep2ngf90/iuvhrqbfcAZqoMD+t2dH786BWA="}], "size": 1021967}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.5.1_1700597588065_0.6444298684692589"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-21T20:13:08.293Z", "publish_time": 1700597588293, "_source_registry_name": "default"}, "4.5.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.5.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.5.2", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-NCKuuZWLht6zj7s6EIFef4BxCRX1GMr83S2W4HPCA0RnJ4iHE4FS1695q6Ewoa6A9nFjJe1//yUu0kgBU07Edw==", "shasum": "b880eb1d349f166939ffbe27cbf1efd2e1923819", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.5.2.tgz", "fileCount": 3, "unpackedSize": 2830831, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvlbXRVR0SQPirg67h2mrLz348C6hogGWlkYYgu3w0fAiBHxxgp1cZ+Ie28TvHzYHUdhpj38wZ1C132eSi1hT/n0g=="}], "size": 1020912}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.5.2_1700807386420_0.8098900131191715"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-24T06:29:46.605Z", "publish_time": 1700807386605, "_source_registry_name": "default"}, "4.6.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.6.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.6.0", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-+MRMcyx9L2kTrTUzYmR61+XVsliMG4odFb5UmqtiT8xOfEicfYAGEuF/D1Pww1+uZkYhBqAHpvju7VN+GnC3ng==", "shasum": "9a7bfc660ac088d447858fc5223984deb979a55a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.6.0.tgz", "fileCount": 3, "unpackedSize": 2830831, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKt3KnGQJu8Cj5q5gR08KpDiKpKQtssus+epGSoO3EFAiB/syb3xnp3lLPG04eNP48LLBsbN/Z1PAaRWOFAw1RbOg=="}], "size": 1020911}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.6.0_1701005952743_0.9538580827339687"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-26T13:39:12.964Z", "publish_time": 1701005952964, "_source_registry_name": "default"}, "4.6.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.6.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.6.1", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-v2FVT6xfnnmTe3W9bJXl6r5KwJglMK/iRlkKiIFfO6ysKs0rDgz7Cwwf3tjldxQUrHL9INT/1r4VA0n9L/F1vQ==", "shasum": "b6e97fd38281667e35297033393cd1101f4a31be", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.6.1.tgz", "fileCount": 3, "unpackedSize": 2830831, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB15fD7gFfT7odtMOiO9caqsdpF6fZSZJmX/mGOkrnk9AiBG7dYMTrw5v5hFSsmLZBj0f9+R/Ssgv9+i+7igqH/Jng=="}], "size": 1020910}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.6.1_1701321785710_0.8420280506332605"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-30T05:23:05.984Z", "publish_time": 1701321785984, "_source_registry_name": "default"}, "4.7.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.7.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.7.0", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-CPtgaQL1aaPc80m8SCVEoxFGHxKYIt3zQYC3AccL/SqqiWXblo3pgToHuBwR8eCP2Toa+X1WmTR/QKFMykws7g==", "shasum": "95902325d07919e25dff32be9428acbc1b889101", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.7.0.tgz", "fileCount": 3, "unpackedSize": 2862063, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFsbLz31d7YcyYKH6XiHQH70Esw5UTJe7h23Pqries/FAiEA3iatqMDRj31RJeGX7xuFrHjJO81/GASe2WGsnJFfPx8="}], "size": 1029153}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.7.0_1702022279188_0.3407015462192724"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-08T07:57:59.448Z", "publish_time": 1702022279448, "_source_registry_name": "default"}, "4.8.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.8.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.8.0", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-ge7saUz38aesM4MA7Cad8CHo0Fyd1+qTaqoIo+Jtk+ipBi4ATSrHWov9/S4u5pbEQmLjgUjB7BJt+MiKG2kzmA==", "shasum": "7cce8efc5c9239a1bafe7ac2a52743bc5734471f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.8.0.tgz", "fileCount": 3, "unpackedSize": 2862063, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFUqKXjkSZQ21745dMdkCZt/uzgrK/3o/RioGqNglQ34AiBO8i8VmGyJWZREmRexTmc0cSKNCgAygYBC2r8Z6jeCCQ=="}], "size": 1085104}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.8.0_1702275892214_0.5193412874197734"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-11T06:24:52.398Z", "publish_time": 1702275892398, "_source_registry_name": "default"}, "4.9.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.0", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-9jPgMvTKXARz4inw6jezMLA2ihDBvgIU9Ml01hjdVpOcMKyxFBJrn83KVQINnbeqDv0+HdO1c09hgZ8N0s820Q==", "shasum": "bf2dbad350376e46cb77fab408bb398ad5f3648d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.0.tgz", "fileCount": 3, "unpackedSize": 2862063, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBWL+Ems7xH8OEbAZxdFCxc4WPowJSIxBDEvQrUKgMDwIhAJc3cQVJzzy1EEukOrwKnLHsngT6UwyhRElbTQTfH4Xo"}], "size": 1085103}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.0_1702459455579_0.45836551948245696"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-13T09:24:15.898Z", "publish_time": 1702459455898, "_source_registry_name": "default"}, "4.9.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.1", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-7XI4ZCBN34cb+BH557FJPmh0kmNz2c25SCQeT9OiFWEgf8+dL6ZwJ8f9RnUIit+j01u07Yvrsuu1rZGxJCc51g==", "shasum": "5bebc66e3a7f82d4b9aa9ff448e7fc13a69656e9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.1.tgz", "fileCount": 3, "unpackedSize": 2854895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQHyw/O8qmEFUnT7LfyHn8LHq1YsgcSlSlXomOzUkVfQIgTfFwLyxKgEgqiiIZ/q8HuqLjU38GkICbDxnv2Dcd/yw="}], "size": 1080022}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.1_1702794369798_0.6117636176861649"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-17T06:26:10.095Z", "publish_time": 1702794370095, "_source_registry_name": "default"}, "4.9.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.2", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-x1CWburlbN5JjG+juenuNa4KdedBdXLjZMp56nHFSHTOsb/MI2DYiGzLtRGHNMyydPGffGId+VgjOMrcltOksA==", "shasum": "60152948f9fb08e8c50c1555e334ca9f9f1f53aa", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.2.tgz", "fileCount": 3, "unpackedSize": 2858479, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEUYbO4Wfg/HtEOI/WZthGdEHHSXpx2SIDPximqd0/W6AiAqP/ROe1DrNPCmudStv/7MUwGyOgSdjDHBzNOLTOldjQ=="}], "size": 1082718}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.2_1703917406544_0.7609767309538868"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-30T06:23:26.758Z", "publish_time": 1703917406758, "_source_registry_name": "default"}, "4.9.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.3", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-z5uvoMvdRWggigOnsb9OOCLERHV0ykRZoRB5O+URPZC9zM3pkoMg5fN4NKu2oHqgkzZtfx9u4njqqlYEzM1v9A==", "shasum": "53fafdaca77027c12171a60c27ca249cf981a4b9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.3.tgz", "fileCount": 3, "unpackedSize": 2864111, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBNTcgm+DtyHV50HQcaoDl2L7H026tr+gN6dQFQq1/lAIgDRHJ/Sfbg1/IpvtoAq+84BmmXyzQTufu8z7/lEBYdBk="}], "size": 1089106}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.3_1704435645591_0.40080545631007025"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-05T06:20:45.826Z", "publish_time": 1704435645826, "_source_registry_name": "default"}, "4.9.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.4", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-T8Q3XHV+Jjf5e49B4EAaLKV74BbX7/qYBRQ8Wop/+TyyU0k+vSjiLVSHNWdVd1goMjZcbhDmYZUYW5RFqkBNHQ==", "shasum": "95957067eb107f571da1d81939f017d37b4958d3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.4.tgz", "fileCount": 3, "unpackedSize": 2864111, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEWb2weDPcJfPbX6lEfHRormKVLaGx6uvKH4HjdaOvfuAiBJaNJjn8H8Wus8Ef0nhG1OWP1hJNdpbS8d9tHykh9Pag=="}], "size": 1089221}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.4_1704523138843_0.8850386814122175"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-06T06:38:59.065Z", "publish_time": 1704523139065, "_source_registry_name": "default"}, "4.9.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.5", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.5", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-aHSsMnUw+0UETB0Hlv7B/ZHOGY5bQdwMKJSzGfDfvyhnpmVxLMGnQPGNE9wgqkLUs3+gbG1Qx02S2LLfJ5GaRQ==", "shasum": "422661ef0e16699a234465d15b2c1089ef963b2a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.5.tgz", "fileCount": 3, "unpackedSize": 2847215, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB4WVwAhsiZcBrlcJlKuF0/idj2qudvAx78U2MED6PmwAiBEV+wOREBwxM0ZP+oJ55gfUlYm5m8fnMtMqMKw4vH4JA=="}], "size": 1082779}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.5_1705040172465_0.03090970766433432"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-12T06:16:12.667Z", "publish_time": 1705040172667, "_source_registry_name": "default"}, "4.9.6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.9.6", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.9.6", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-VD6qnR99dhmTQ1mJhIzXsRcTBvTjbfbGGwKAHcu+52cVl15AC/kplkhxzW/uT0Xl62Y/meBKDZvoJSJN+vTeGA==", "shasum": "1cc3416682e5a20d8f088f26657e6e47f8db468e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.9.6.tgz", "fileCount": 3, "unpackedSize": 2871791, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCc6t9XqxBXw3nAogNj9Uo0sGXTb1R4gXdah58mdolwmwIhAOoBFI5oPw6exMLWoS1//7nVP6MccKJIZN1vbr5lr31m"}], "size": 1090896}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.9.6_1705816337858_0.9481162215041194"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-21T05:52:18.060Z", "publish_time": 1705816338060, "_source_registry_name": "default"}, "4.10.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.10.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.10.0", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-NrR6667wlUfP0BHaEIKgYM/2va+Oj+RjZSASbBMnszM9k+1AmliRjHc3lJIiOehtSSjqYiO7R6KLNrWOX+YNSQ==", "shasum": "6ad0d4fb0066f240778ee3f61eecf7aa0357f883", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.10.0.tgz", "fileCount": 3, "unpackedSize": 2950640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFy0RUQ9sc4ULIOPmnTbrb7Bo4awUL7XZ1KjHA6jr+/gAiB6am/X2iaAjWIE5iYxcHlL3+53G9MZcD2JDTfm55cRWw=="}], "size": 1115090}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.10.0_1707544720261_0.8058607284155408"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-10T05:58:40.484Z", "publish_time": 1707544720484, "_source_registry_name": "default"}, "4.11.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.11.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.11.0", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-LVXo9dDTGPr0nezMdqa1hK4JeoMZ02nstUxGYY/sMIDtTYlli1ZxTXBYAz3vzuuvKO4X6NBETciIh7N9+abT1g==", "shasum": "18e49376786def75843e605bdc8059a301c11dad", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.11.0.tgz", "fileCount": 3, "unpackedSize": 2950640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/qcDB7qACamZD4AfL0NpKmfYqNn/lGU3eGNfrT/AY2AiAb8ETHApgMEBeyCysUF/vAnTAYhEsp1V7ri3ppfmzYqg=="}], "size": 1115089}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.11.0_1707977376542_0.6897934836840123"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-15T06:09:36.756Z", "publish_time": 1707977376756, "_source_registry_name": "default"}, "4.12.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.12.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.12.0", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-JPDxovheWNp6d7AHCgsUlkuCKvtu3RB55iNEkaQcf0ttsDU/JZF+iQnYcQJSk/7PtT4mjjVG8N1kpwnI9SLYaw==", "shasum": "68d233272a2004429124494121a42c4aebdc5b8e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.12.0.tgz", "fileCount": 3, "unpackedSize": 2925040, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHmQ150e/sH7lLVjloXZmv1Q1T//ESHyK3g7teiAzSSiAiEAoSR7rIyvXnhUYe/IbzZFrGoJ8COqX6NoVlUg8o8pSiw="}], "size": 1107843}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.12.0_1708090334292_0.34068323726685645"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-16T13:32:14.611Z", "publish_time": 1708090334611, "_source_registry_name": "default"}, "4.12.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.12.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.12.1", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-<PERSON><PERSON><PERSON>64bnICG42UPL7TrhIwsJW4QcKkIt9gGlj21gq3VV0LL6XNb1yAdHVp1pIi9gkts9gGcT3OfUYHjGP7ETAiw==", "shasum": "27977d91f5059645ebb3b7fbf4429982de2278d3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.12.1.tgz", "fileCount": 3, "unpackedSize": 2961392, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEQhMshNi5zeJQEwpoC/Q5739VX09q40QDkZgJQb9P0/AiBtaSE/9ptp41HTQyAXID1UGmUXUwKxjZKghRd9zzZg5A=="}], "size": 1121266}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.12.1_1709705012264_0.8075603002413951"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-06T06:03:32.472Z", "publish_time": 1709705012472, "_source_registry_name": "default"}, "4.13.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.0", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-46ue8ymtm/5PUU6pCvjlic0z82qWkxv54GTJZgHrQUuZnVH+tvvSP0LsozIDsCBFO4VjJ13N68wqrKSeScUKdA==", "shasum": "8ffecc980ae4d9899eb2f9c4ae471a8d58d2da6b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.0.tgz", "fileCount": 3, "unpackedSize": 2970608, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID6eVE5rXxLYtG0bht4RTlCOYTO3n+6i3YKFq8+Rn6MfAiAi+TA/0sYW+dojYNnzPw0KAMsErPTvBRB/V4F+rha9uQ=="}], "size": 1123362}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.13.0_1710221311390_0.18954860526592743"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-12T05:28:31.652Z", "publish_time": 1710221311652, "_source_registry_name": "default"}, "4.13.1-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.1-1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.1-1", "readmeFilename": "README.md", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-ycwUDKpoaafBCk7QiiG0C24L1HAhzLaq6QeVzWJzGX6nPGsyFu0STYIxyaKwLVI/X5360c8EmebsTFZy6ORzNg==", "shasum": "454e1da64c7bb528a3fba4015204381805e69faa", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.1-1.tgz", "fileCount": 3, "unpackedSize": 2961394, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWTM+u+hV6k65s2YgeizQtxotvJ6NMrfW6wH9yupRLJgIhAPuMaCPiQriTwROVyPuE+u7/gn2J316xnVZ1zmFYpoyS"}], "size": 1119386}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.13.1-1_1711265959720_0.5439210873367608"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-24T07:39:19.926Z", "publish_time": 1711265959926, "_source_registry_name": "default"}, "4.13.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.1", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-L+hX8Dtibb02r/OYCsp4sQQIi3ldZkFI0EUkMTDwRfFykXBPptoz/tuuGqEd3bThBSLRWPR6wsixDSgOx/U3Zw==", "shasum": "6f9359bbec6cb4a2c002642c63e3704b0b5e68b7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.1.tgz", "fileCount": 3, "unpackedSize": 2961392, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIElOi0XQE9KVkxIL3RBiEZM34t4od/SEyRYsC1qv52fPAiEAm4L2e/DnsXwFaTIcpaA23M4DsDHwg/7gfPu+jW3EGTI="}], "size": 1119386}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.13.1_1711535258199_0.6317919843056932"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-27T10:27:38.448Z", "publish_time": 1711535258448, "_source_registry_name": "default"}, "4.13.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.13.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.13.2", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-2YWwoVg9KRkIKaXSh0mz3NmfurpmYoBBTAXA9qt7VXk0Xy12PoOP40EFuau+ajgALbbhi4uTj3tSG3tVseCjuA==", "shasum": "f8b65a4a7e7a6b383e7b14439129b2f474ff123c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.13.2.tgz", "fileCount": 3, "unpackedSize": 2961392, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb257ZQhNptO0Tz6/RPIDPWc8k6WS9c+OocfN5ai9+uQIhAJDSIoQzK/tWCPOTeL7zHa28WpP/mHuopmHBgUmf6LtB"}], "size": 1119381}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.13.2_1711635213694_0.6253024408310945"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-28T14:13:33.899Z", "publish_time": 1711635213899, "_source_registry_name": "default"}, "4.14.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.0", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-Fq52EYb0riNHLBTAcL0cun+rRwyZ10S9vKzhGKKgeD+XbwunszSY0rVMco5KbOsTlwovP2rTOkiII/fQ4ih/zQ==", "shasum": "269023332297051d037a9593dcba92c10fef726b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.0.tgz", "fileCount": 3, "unpackedSize": 2944496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMsTuHg05Jd/NfXMF1+abMgJStf5h3DJ8Hff2fzcc8AAIgaLj4dMnyGlWVKTZiJZNZ1ph+vL0jk2MNrhcgl6i+4z4="}], "size": 1108441}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.14.0_1712121770182_0.14500400962084847"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-03T05:22:50.470Z", "publish_time": 1712121770470, "_source_registry_name": "default"}, "4.14.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.1", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-ryS22I9y0mumlLNwDFYZRDFLwWh3aKaC72CWjFcFvxK0U6v/mOkM5Up1bTbCRAhv3kEIwW2ajROegCIQViUCeA==", "shasum": "84d48c55740ede42c77373f76e85f368633a0cc3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.1.tgz", "fileCount": 3, "unpackedSize": 2937328, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGE4XrDVmFKEr90eklDLBtzu4MdDm/LnoPpoOqpAXDtoAiAprhni/A3kBrqa44v1q7i1FjYbLquyEqa8ZNuzTcZHCA=="}], "size": 1111013}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.14.1_1712475337811_0.34765709260683586"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-07T07:35:38.046Z", "publish_time": 1712475338046, "_source_registry_name": "default"}, "4.14.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.2", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-H4s8UjgkPnlChl6JF5empNvFHp77Jx+Wfy2EtmYPe9G22XV+PMuCinZVHurNe8ggtwoaohxARJZbaH/3xjB/FA==", "shasum": "10f608dfc1e5bb96aca18c7784cc4a94d890c03c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.2.tgz", "fileCount": 3, "unpackedSize": 2954224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqcrKR3khrY7Pwudl+hkh5u67/cx/4/wosWXxW2hP6XAiEAwHmdBUEz/uc16o9NEEB1/Lj8Ig4OPDZSV3t9NYY3hVA="}], "size": 1119541}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.14.2_1712903018041_0.00661428094615224"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T06:23:38.284Z", "publish_time": 1712903018284, "_source_registry_name": "default"}, "4.14.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.14.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.14.3", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+4h2WrGOYsOumDQ5S2sYNyhVfrue+9tc9XcLWLh+Kw3UOxAvrfOrSMFon60KspcDdytkNDh7K2Vs6eMaYImAZg==", "shasum": "a648122389d23a7543b261fba082e65fefefe4f6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.14.3.tgz", "fileCount": 3, "unpackedSize": 2941424, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA5z2BEx3jVgQm28GdgLJ8ZyNAaVB+/L+xmRmQLli9vXAiBVETzz1yfPEcSiwUHJET95iYL479tzWeX6nP2I6qqXKQ=="}], "size": 1111449}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.14.3_1713165510239_0.10114258887794225"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-15T07:18:30.488Z", "publish_time": 1713165510488, "_source_registry_name": "default"}, "4.15.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.15.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.15.0", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-nEtaFBHp1OnbOf+tz66DtID579sNRHGgMC23to8HUyVuOCpCMD0CvRNqiDGLErLNnwApWIUtUl1VvuovCWUxwg==", "shasum": "95dae687b645a25aab3a082d987556f58274ffbe", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.15.0.tgz", "fileCount": 3, "unpackedSize": 3046384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUF+nivjj44VEZJehN1iQh7b42ckI6qEuWy0AuZFupmAiAVcqY2KZ2w8kFq/hzMZQ6Z5lJk57ApFf9sC0j062lAgA=="}], "size": 1149925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.15.0_1713591431348_0.5694270984601386"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-20T05:37:11.543Z", "publish_time": 1713591431543, "_source_registry_name": "default"}, "4.16.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.0", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+wtkF+z2nw0ZwwHji01wOW0loxFl24lBNxPtVAXtnPPDL9Ew0EhiCMOegXe/EAH3Zlr8Iw9tyPJXB3DltQLEyw==", "shasum": "e9117384c4e340370777c5b5974c5cebf84c8807", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.0.tgz", "fileCount": 3, "unpackedSize": 3046384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDImFVSJQ0BWusY/cfDhY4mAkm7PG/I6rc3CQBHe3RUywIhAJMqBbPITHC01HCQ0ti1cmYvwxtOqRuqMpFJvlB3dyAx"}], "size": 1149926}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.16.0_1713674510435_0.1394710243424122"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-21T04:41:50.657Z", "publish_time": 1713674510657, "_source_registry_name": "default"}, "4.16.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.1", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-1vIP6Ce02L+qWD7uZYRiFiuAJo3m9kARatWmFSnss0gZnVj2Id7OPUU9gm49JPGasgcR3xMqiH3fqBJ8t00yVg==", "shasum": "af113ad682fc13d1f870242c5539031f8cc27cf1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.1.tgz", "fileCount": 3, "unpackedSize": 3046384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFkhINiSGS0DCKYsK3CXfZzDsVF/5ae4ygvMUuugstOAIgV/kwdYIqZkwM6MksWekk0jA0TS07SprC7nccZfTeWMM="}], "size": 1149925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.16.1_1713724196623_0.8925901025179472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-21T18:29:56.812Z", "publish_time": 1713724196812, "_source_registry_name": "default"}, "4.16.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.2", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Wg7ANh7+hSilF0lG3e/0Oy8GtfTIfEk1327Bw8juZOMOoKmJLs3R+a4JDa/4cHJp2Gs7QfCDTepXXcyFD0ubBg==", "shasum": "edd352302e3fa6a2d612447590b0a0887cdbf762", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.2.tgz", "fileCount": 3, "unpackedSize": 3046384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFO3ZjpSNY5t48utor1Nc+ikGeMuafFARJ+Oy54rpz09AiBOZBb69iL86hPp4Tmp+J0gkHG5J3Nv2WjWaZHUILgRPA=="}], "size": 1149926}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.16.2_1713799153270_0.7270838723544637"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T15:19:13.519Z", "publish_time": 1713799153519, "_source_registry_name": "default"}, "4.16.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.3", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+rxD3memdkhGz0NhNqbYHXBoA33MoHBK4uubZjF1IeQv1Psi6tqgsCcC6vwQjxBM1qoCqOQQBy0cgNbbZKnGUg==", "shasum": "1f24ac280a7e984a62c81b30c26e17314eed4b5f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.3.tgz", "fileCount": 3, "unpackedSize": 3046384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIENgqY/RIOYygUt5HCTHHPU6Mbqhe6JyuAwyu9Le7N1yAiEA1WtiWZDfL3Kv4utngh/KiVT/mwuZsGRlaDb6+TxXoS4="}], "size": 1149925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.16.3_1713849153075_0.7628762762167252"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T05:12:33.316Z", "publish_time": 1713849153316, "_source_registry_name": "default"}, "4.16.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.16.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.16.4", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Ma4PwyLfOWZWayfEsNQzTDBVW8PZ6TUUN1uFTBQbF2Chv/+sjenE86lpiEwj2FiviSmSZ4Ap4MaAfl1ciF4aSA==", "shasum": "6cc0db57750376b9303bdb6f5482af8974fcae35", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.4.tgz", "fileCount": 3, "unpackedSize": 3046384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICziaDaogCl5BEDEtARjOCyrzPgIk1MGOvaJOoyoDe2wAiEA34qZ2dJWYzE6o30pMe91Jd9EKsbhT7yUKVPvFEdx6IY="}], "size": 1149926}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.16.4_1713878105878_0.0691828061519848"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T13:15:06.116Z", "publish_time": 1713878106116, "_source_registry_name": "default"}, "4.17.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.17.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.17.0", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-uL7UYO/MNJPGL/yflybI+HI+n6+4vlfZmQZOCb4I+z/zy1wisHT3exh7oNQsnL6Eso0EUTEfgQ/PaGzzXf6XyQ==", "shasum": "a0403ef24fe50d28b7c18dcf97d88d882950bbd8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.17.0.tgz", "fileCount": 3, "unpackedSize": 3033072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhqCYhrCoJkm8aLsJFSJ35XitGpD+ey/7xRkaX9VoiZQIgLaE+da37MewHB47e3zZjkBJEBKOM3IkEDO+3VGTT8RA="}], "size": 1147769}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.17.0_1714217390526_0.07229835233996806"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-27T11:29:50.741Z", "publish_time": 1714217390741, "_source_registry_name": "default"}, "4.17.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.17.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.17.1", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-J0d3NVNf7wBL9t4blCNat+d0PYqAx8wOoY+/9Q5cujnafbX7BmtYk3XvzkqLmFECaWvXGLuHmKj/wrILUinmQg==", "shasum": "f1b28caca6d97beab3e3a5e623b97610a423bea5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.17.1.tgz", "fileCount": 3, "unpackedSize": 3033072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBYSpgaaJe38vaUzSwuYcRXQeT6wpPkQ2HPvmeahp+k4AiBmOwzTywj5la9osk3HY5+EyAr5r/r8kTx3JLWZHvik/g=="}], "size": 1147769}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.17.1_1714366673287_0.05451779386638966"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-29T04:57:53.503Z", "publish_time": 1714366673503, "_source_registry_name": "default"}, "4.17.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.17.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.17.2", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-tmdtXMfKAjy5+IQsVtDiCfqbynAQE/TQRpWdVataHmhMb9DCoJxp9vLcCBjEQWMiUYxO1QprH/HbY9ragCEFLA==", "shasum": "27f65a89f6f52ee9426ec11e3571038e4671790f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.17.2.tgz", "fileCount": 3, "unpackedSize": 3033072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIDOMDqPWPNq5Ql9C3xwiN2fDi9Cm6R1ph4qOGI8h7VQIgFDWFSoWKrkF2pM6RyeCJ3nvCdVcwxqoO3e6CqNaZV/Q="}], "size": 1147769}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.17.2_1714453243191_0.06014036904632869"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-30T05:00:43.448Z", "publish_time": 1714453243448, "_source_registry_name": "default"}, "4.18.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.18.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.18.0", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-7J6TkZQFGo9qBKH0pk2cEVSRhJbL6MtfWxth7Y5YmZs57Pi+4x6c2dStAUvaQkHQLnEQv1jzBUW43GvZW8OFqA==", "shasum": "ed6603e93636a96203c6915be4117245c1bd2daf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.18.0.tgz", "fileCount": 3, "unpackedSize": 3055088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZymwh6BI/XVRxSiK+sxT10UF9VFsfsyRybO0gWmvXEAiAYdXBd87OAxjlO7E5sk1oKuB5qROY4UNf1XQNBtu8LTA=="}], "size": 1157338}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.18.0_1716354222160_0.4623045650353932"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-22T05:03:42.401Z", "publish_time": 1716354222401, "_source_registry_name": "default"}, "4.18.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.18.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.18.1", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.3", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-W2ZNI323O/8pJdBGil1oCauuCzmVd9lDmWBBqxYZcOqWD6aWqJtVBQ1dFrF4dYpZPks6F+xCZHfzG5hYlSHZ6g==", "shasum": "4a5dcbbe7af7d41cac92b09798e7c1831da1f599", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.18.1.tgz", "fileCount": 3, "unpackedSize": 2904560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCG5AItzPsrDySHTEl8Wu2noFUMRRX8bJa7nx08aNifJgIga/IhajS3kpImAGQfqoIhuf+R57OT7/YsEgnNa19lktQ="}], "size": 1123728}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.18.1_1720452310708_0.6712877791837246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-08T15:25:10.939Z", "publish_time": 1720452310939, "_source_registry_name": "default"}, "4.19.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.19.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.19.0", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-HxDMKIhmcguGTiP5TsLNolwBUK3nGGUEoV/BO9ldUBoMLBssvh4J0X8pf11i1fTV7WShWItB1bKAKjX4RQeYmg==", "shasum": "1eed24b91f421c2eea8bb7ca8889ba0c867e1780", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.0.tgz", "fileCount": 3, "unpackedSize": 2893808, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC1ew6LteTRZkVMMCAJYwCcYQIRVe+haezmdVih0DUlYAiEAkrLVvfD2pZKBEk0vuXJwojX+1eUL8OVp0GXRpkhNXfg="}], "size": 1104511}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.19.0_1721454373546_0.740393938986827"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-20T05:46:13.771Z", "publish_time": 1721454373771, "_source_registry_name": "default"}, "4.19.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.19.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.19.1", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-88brja2vldW/76jWATlBqHEoGjJLRnP0WOEKAUbMcXaAZnemNhlAHSyj4jIwMoP2T750LE9lblvD4e2jXleZsA==", "shasum": "21ac5ed84d914bc31821fec3dd909f7257cfb17b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.1.tgz", "fileCount": 3, "unpackedSize": 2833904, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKSw90JHDghiOwBs/tIQGpK3beSM/NFaJIlWOqVdlhtAiB8lZbsLM3I0PQFKlHhlhMNdZm+Oq0f4A5O84dGYuhHBg=="}], "size": 1092633}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.19.1_1722056040139_0.3905244759104194"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-27T04:54:00.447Z", "publish_time": 1722056040447, "_source_registry_name": "default"}, "4.19.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.19.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.19.2", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ayVstadfLeeXI9zUPiKRVT8qF55hm7hKa+0N1V6Vj+OTNFfKSoUxyZvzVvgtBxqSb5URQ8sK6fhwxr9/MLmxdA==", "shasum": "bada17b0c5017ff58d0feba401c43ff5a646c693", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.2.tgz", "fileCount": 3, "unpackedSize": 2833904, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApPF7zNQwBvA9FamDdOlK2kX20vLepQwawatA/OXjETAiEAw8vYKCFRFAOVev92x7S9SQfspmLrTLRaUXdSvGp/Wvk="}], "size": 1092633}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.19.2_1722501172725_0.4406872380445286"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-01T08:32:52.875Z", "publish_time": 1722501172875, "_source_registry_name": "default"}, "4.20.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.20.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.20.0", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-psegMvP+Ik/Bg7QRJbv8w8PAytPA7Uo8fpFjXyCRHWm6Nt42L+JtoqH8eDQ5hRP7/XW2UiIriy1Z46jf0Oa1kA==", "shasum": "86a221f01a2c248104dd0defb4da119f2a73642e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.20.0.tgz", "fileCount": 3, "unpackedSize": 2833392, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnfOrGrrnDYmZO3qZ+ejNgbjHQ8w1139cqWAjyiijEBQIgKeWt8ZCK4PKxbS4ssJ8qDK6GhEMhNEKbxROuMFPb1V8="}], "size": 1090426}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.20.0_1722660530075_0.8047893918399636"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-03T04:48:50.287Z", "publish_time": 1722660530287, "_source_registry_name": "default"}, "4.21.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.0", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-s5oFkZ/hFcrlAyBTONFY1TWndfyre1wOMwU+6KCpm/iatybvrRgmZVM+vCFwxmC5ZhdlgfE0N4XorsDpi7/4XQ==", "shasum": "2fef1a90f1402258ef915ae5a94cc91a5a1d5bfc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.0.tgz", "fileCount": 3, "unpackedSize": 2741232, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCI0uOkU+8922ORVTt/60xIRKfOLQA5MVoLO1xfcI2CYwIhAL0WGHr7rVOnrPi5sfXfxEbPl0jTp9R+r2dEeEehubCe"}], "size": 1053574}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.21.0_1723960533896_0.7736036598135376"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-18T05:55:34.070Z", "publish_time": 1723960534070, "_source_registry_name": "default"}, "4.21.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.1", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-3Q3brDgA86gHXWHklrwdREKIrIbxC0ZgU8lwpj0eEKGBQH+31uPqr0P2v11pn0tSIxHvcdOWxa4j+YvLNx1i6g==", "shasum": "18349db8250559a5460d59eb3575f9781be4ab98", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.1.tgz", "fileCount": 3, "unpackedSize": 2738672, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzlI8EJWmBgcYRbk+/KIjDI/dsjI7/Edndf40HmjJAHAiEAu7uD+cFvSHUuM6/PqdkgisrTZHQGTk7vufcYAKaI4qI="}], "size": 1052953}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.21.1_1724687652633_0.8004691705718407"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-26T15:54:12.865Z", "publish_time": 1724687652865, "_source_registry_name": "default"}, "4.21.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.2", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-9rRero0E7qTeYf6+rFh3AErTNU1VCQg2mn7CQcI44vNUWM9Ze7MSRS/9RFuSsox+vstRt97+x3sOhEey024FRQ==", "shasum": "a0ca0c5149c2cfb26fab32e6ba3f16996fbdb504", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.2.tgz", "fileCount": 3, "unpackedSize": 2729456, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDksdicDjAZ9Yo8HwCxX7Xy6SEg82LMThxKnmkdJ03lHAIgK2ifEmruBVgo/P567jk2BLMJcsc4TFQgD2JI/sm1P5A="}], "size": 1050830}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.21.2_1725001465436_0.9959006777512065"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-30T07:04:25.676Z", "publish_time": 1725001465676, "_source_registry_name": "default"}, "4.21.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.21.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.21.3", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-9isNzeL34yquCPyerog+IMCNxKR8XYmGd0tHSV+OVx0TmE0aJOo9uw4fZfUuk2qxobP5sug6vNdZR6u7Mw7Q+Q==", "shasum": "fbb6ef5379199e2ec0103ef32877b0985c773a55", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.21.3.tgz", "fileCount": 3, "unpackedSize": 2755056, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjmXXvy5MZag7X/bk09LLi+NYGLFwvQuhOfKVlPPx4NQIgHLgpjxDJ8+qwVbQ8M8SvYs+2AlTQCOuaIGgZUY8nU6o="}], "size": 1044944}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.21.3_1726124748949_0.9384773973054874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-12T07:05:49.160Z", "publish_time": 1726124749160, "_source_registry_name": "default"}, "4.22.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.0", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-7LB+Bh+Ut7cfmO0m244/asvtIGQr5pG5Rvjz/l1Rnz1kDzM02pSX9jPaS0p+90H5I1x4d1FkCew+B7MOnoatNw==", "shasum": "6bd66c198f80c8e7050cfd901701cfb9555d768a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.0.tgz", "fileCount": 3, "unpackedSize": 2746864, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1QaRaiR0LLM5rM4EjmipceCKfHfsYXePR6YoRLIuqLwIgeJHv/i8pLSoQLBwIPqLEcGrtd6eAVKFvn3azUXPJ790="}], "size": 1041554}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.0_1726721730894_0.8506786655802039"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-19T04:55:31.105Z", "publish_time": 1726721731105, "_source_registry_name": "default"}, "4.22.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.1", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-jDS/ShZxlA3HKtgm25CcbApOVsr/0Zkdu/E+3xK4UO0PT912yqyh7jNpTmZZJAiPDQoSDI9FOqrjSbnlpW6IFg==", "shasum": "df2283dabb7ebc54ef6bd2e0e5415d8dc04c5a8f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.1.tgz", "fileCount": 3, "unpackedSize": 2743280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcd4w7JU8BLwuHuUp/Jo+T8/ku4+r+LNk1Ucl6etrtEwIgC/btzD4diI/OyDufwgxWzzVEccgpqCKbT/F2susFaK0="}], "size": 1042440}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.1_1726820512924_0.4229877732127416"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T08:21:53.172Z", "publish_time": 1726820513172, "_source_registry_name": "default"}, "4.22.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.2", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-t/YgCbZ638R/r7IKb9yCM6nAek1RUvyNdfU0SHMDLOf6GFe/VG1wdiUAsxTWHKqjyzkRGg897ZfCpdo1bsCSsA==", "shasum": "8ae561401b92acb8ca7a842ffadececb22a2247e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.2.tgz", "fileCount": 3, "unpackedSize": 2743280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+AL75v01tzqmzf9g623jH/Sp3jvHS6b6xuNbVeQIfhQIgd4ZzQA2WU3KmztRTfcmtF24izBCosfqsxPE6m5qCqmM="}], "size": 1042441}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.2_1726824824572_0.782851378262718"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T09:33:44.742Z", "publish_time": 1726824824742, "_source_registry_name": "default"}, "4.22.3-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.3-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.3-0", "readmeFilename": "README.md", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-QjWy6iX2PrHzghSjNEJpkoU3w4LqD+fHWxdwt5ebe3Pr/DX7OxeGO5lC05fiAZv9mR/ZuZ2UVPDQSb98SIT4yg==", "shasum": "a43036b6ba8b93446127b4c8c33d9caf1f958338", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.3-0.tgz", "fileCount": 3, "unpackedSize": 2743282, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFlscjgK87lFKix7NKTjULJP7oxMoMMDQPSxhSjjO8owIhAMU8cOrsjVyxnrrPbpzQPhcT6b6jTxn91twcumxb0L5H"}], "size": 1042440}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.3-0_1726843676781_0.49938132216912057"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T14:47:57.146Z", "publish_time": 1726843677146, "_source_registry_name": "default"}, "4.22.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.3", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-IWxGO/k2gysJPXDmUAqmpcayaX6P+RB10JXJrV4QjcN7ipKWFcHYfTDG1EGyXLEZhHMQe3Ed0OjvPe4JYZvINA==", "shasum": "57ed663fb87588718d3f367d36aaca94fade66e0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.3.tgz", "fileCount": 3, "unpackedSize": 2743280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGDCJaCaWclChzcqLwsd1tCWdEP+DpNxlgZr0El969TSAiEAxvwogNWlN5S7aWJRywIwCB9L4dtjcs/ZrwmkACAnyCo="}], "size": 1042440}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.3_1726894988330_0.3639983980922825"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-21T05:03:08.733Z", "publish_time": 1726894988733, "_source_registry_name": "default"}, "4.22.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.4", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-BjI+NVVEGAXjGWYHz/vv0pBqfGoUH0IGZ0cICTn7kB9PyjrATSkX+8WkguNjWoj2qSr1im/+tTGRaY+4/PdcQw==", "shasum": "4349482d17f5d1c58604d1c8900540d676f420e0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.4.tgz", "fileCount": 3, "unpackedSize": 2743280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCz/+2qytEh2hLSyrier/YnOCjLBZdYKUfGdEKMRmU5yQIgTxJWjpWGfm8E97e/31yo7yFg955jSYAzjm1L7Twx7jE="}], "size": 1042441}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.4_1726899081723_0.02110964868255305"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-21T06:11:21.956Z", "publish_time": 1726899081956, "_source_registry_name": "default"}, "4.22.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.22.5", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.22.5", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-RXT8S1HP8AFN/Kr3tg4fuYrNxZ/pZf1HemC5Tsddc6HzgGnJm0+Lh5rAHJkDuW3StI0ynNXukidROMXYl6ew8w==", "shasum": "12ee49233b1125f2c1da38392f63b1dbb0c31bba", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.5.tgz", "fileCount": 3, "unpackedSize": 2751472, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLFx/XT8UD9vj/Y0F3VDV3XWrBTH1q+oAgiJoy9QNCcwIhAIA5MSqAkO+hNqu63HoExpM+eLc+IHTOpQAXiM5LmE5D"}], "size": 1045100}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.22.5_1727437695188_0.7685111192913245"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-27T11:48:15.492Z", "publish_time": 1727437695492, "_source_registry_name": "default"}, "4.23.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.23.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.23.0", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-+5Ky8dhft4STaOEbZu3/NU4QIyYssKO+r1cD3FzuusA0vO5gso15on7qGzKdNXnc1gOrsgCqZjRw1w+zL4y4hQ==", "shasum": "9e09307dd0656a63db9ef86a6004679f56d9ddcf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.23.0.tgz", "fileCount": 3, "unpackedSize": 2751472, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGsA9BvyR9XMHeJ8NB6YpC+d37MOgFn4dSFLHKuV/WfwIgQWpItMcow0q0aDta6zMC8KARxIa83h2UoEMfdqnFZyg="}], "size": 1045102}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.23.0_1727766606032_0.46299889021671503"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-01T07:10:06.266Z", "publish_time": 1727766606266, "_source_registry_name": "default"}, "4.24.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.0", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-VXBrnPWgBpVDCVY6XF3LEW0pOU51KbaHhccHw6AS6vBWIC60eqsH19DAeeObl+g8nKAz04QFdl/Cefta0xQtUQ==", "shasum": "71fa3ea369316db703a909c790743972e98afae5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.0.tgz", "fileCount": 3, "unpackedSize": 2760176, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG2vSRT+fBauaTopRN9DIYcQqis9EkjPfX+JgJEDWU3qAiBO7bQzElhZrF+8KH0J1PtrGxwDj9jT7DQveb+FivEO/Q=="}], "size": 1048356}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.24.0_1727861839460_0.30083771304548423"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-02T09:37:19.700Z", "publish_time": 1727861839700, "_source_registry_name": "default"}, "4.24.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.1", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-wexHPBkBa2/tPhbGcxLqOM2AFZ7BQsZ0pk3dVxRL5Ec0SsXnkpcMucZ4j4woyoD5DbRdFP6Roptd9TRsGVTvUA==", "shasum": "bc7c6677060c84213938f4f0cbe3a78ddf846ab4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.1.tgz", "fileCount": 3, "unpackedSize": 2759664, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOfLRtCeaXE3vCh6ovUzfGuO+8PuaPOUW5P89rKxfP/AIgXX/bIaBpNMCr74yW5D/NrLooPGD9Tpgawnrh6fLYia0="}], "size": 1048101}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.24.1_1730011382458_0.5632957502883698"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T06:43:02.743Z", "publish_time": 1730011382743, "_source_registry_name": "default"}, "4.24.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.2", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-A+JAs4+EhsTjnPQvo9XY/DC0ztaws3vfqzrMNMKlwQXuniBKOIIvAAI8M0fBYiTCxQnElYu7mLk7JrhlQ+HeOw==", "shasum": "c80e2c33c952b6b171fa6ad9a97dfbb2e4ebee44", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.2.tgz", "fileCount": 3, "unpackedSize": 2759664, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFvuGsCu3C7wuArtKCLPkTvt5bHBqnkrvrqS+vVXTIMTAiBanZJyx/4DDyXrJTppDXGZtnx7USwUETsOeNdm/scj4w=="}], "size": 1048101}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.24.2_1730043607643_0.02999956542064397"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T15:40:07.904Z", "publish_time": 1730043607904, "_source_registry_name": "default"}, "4.25.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.25.0-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.25.0-0", "readmeFilename": "README.md", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-8pPB123mQiJiQ5SBv8vqhkUA2s0UPnInttZruPpG41SzDr4ku8EJQqxMNPnox9iikeggmcX9kQK+8UOjcOovOg==", "shasum": "05ec6c0a9b27c4822207f135a3a18e6b95c65856", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.25.0-0.tgz", "fileCount": 3, "unpackedSize": 2759666, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDn5oeIDgVbkF02PG4vF1KCDJ3KKnkWCsKuJ+lDg434aQIgTiVrvWzcCAWg+XEmQJCMWa+asvSNG3WeueJb46Ieuik="}], "size": 1048101}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.25.0-0_1730182510094_0.06290339064036976"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T06:15:10.503Z", "publish_time": 1730182510503, "_source_registry_name": "default"}, "4.24.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.3", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-2lg1CE305xNvnH3SyiKwPVsTVLCg4TmNCF1z7PSHX2uZY2VbUpdkgAllVoISD7JO7zu+YynpWNSKAtOrX3AiuA==", "shasum": "11d6a59f651a3c2a9e5eaab0a99367b77a29c319", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.3.tgz", "fileCount": 3, "unpackedSize": 2759664, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXmwKfEfkTPVvXiJI9EjvQjxst8Ms4sGMDRXP9YViHMQIgJ2OQDOZ/we8f5EsoIzg6O7iCwj1+mnatsHEILgKBBPg="}], "size": 1048101}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.24.3_1730211247364_0.640490804504958"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T14:14:07.677Z", "publish_time": 1730211247677, "_source_registry_name": "default"}, "4.24.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.24.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.24.4", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ku2GvtPwQfCqoPFIJCqZ8o7bJcj+Y54cZSr43hHca6jLwAiCbZdBUOrqE6y29QFajNAzzpIOwsckaTFmN6/8TA==", "shasum": "e48e78cdd45313b977c1390f4bfde7ab79be8871", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.24.4.tgz", "fileCount": 3, "unpackedSize": 2751984, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUv5zFK9NqFV1VD9e0FfTIUWCtYLaFkVX1sTsmuohtiwIhANXeW6B3HSubJyqFpynQgrk4lGNm0rfJltmV3Cpz17UD"}], "size": 1043916}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.24.4_1730710028919_0.6932387941578353"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-04T08:47:09.121Z", "publish_time": 1730710029121, "_source_registry_name": "default"}, "4.25.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.25.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.25.0", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-JT8tcjNocMs4CylWY/CxVLnv8e1lE7ff1fi6kbGocWwxDq9pj30IJ28Peb+Y8yiPNSF28oad42ApJB8oUkwGww==", "shasum": "f4ec076579634f780b4e5896ae7f59f3e38e0c60", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.25.0.tgz", "fileCount": 3, "unpackedSize": 2730992, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1YROmaasmurTM6hiaM+pi0yXVg9/una4iqvFrJcQC2gIgZzNfYFSxjJkq3aIZUgTVD+nHUyapdueu7P6mJj5RfY0="}], "size": 1038317}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.25.0_1731141442093_0.316088161729964"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-09T08:37:22.287Z", "publish_time": 1731141442287, "_source_registry_name": "default"}, "4.26.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.26.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.26.0", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-WUQzVFWPSw2uJzX4j6YEbMAiLbs0BUysgysh8s817doAYhR5ybqTI1wtKARQKo6cGop3pHnrUJPFCsXdoFaimQ==", "shasum": "f4b4e0747710ba287eb2e2a011538ee2ed7f74d3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.26.0.tgz", "fileCount": 3, "unpackedSize": 2730992, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGvVc8guYvnab+mqOqqeloZ9tb72lD4iqRgSe/ixr+72AiEAj6oqqTc6POpmFgxRj0I/VdAli+sazj42Cm2c9ubXv8A="}], "size": 1038318}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.26.0_1731480299356_0.023605070170603426"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-13T06:44:59.566Z", "publish_time": 1731480299566, "_source_registry_name": "default"}, "4.27.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.0-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.0-0", "readmeFilename": "README.md", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-furEmp7NutOcj9fpe1kUhtZUzgzJH4m5cTzq+w3vkqhNn5TtgoE1t1yEShSQQl8CvPkBX2aQcD3/DCRLjA0Yaw==", "shasum": "8c79bf8d4fdec83896e1625d087a98d4fb36d7a9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.0-0.tgz", "fileCount": 3, "unpackedSize": 2730994, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCI4Em3HF5kPgotvwm1VTkvsGZdA+xoiJ0HbQsJwjcdsAIhAO46tMPD+7fUg6Xfusb+aOIir4LtVqZz1H+8jAMSA4B5"}], "size": 1038318}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.0-0_1731481393860_0.5716545981366179"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-13T07:03:14.160Z", "publish_time": 1731481394160, "_source_registry_name": "default"}, "4.27.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.0-1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.0-1", "readmeFilename": "README.md", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ol2hOOigawL5L4JPj40Jrhgxbltj3ty6REYwcEu5quhE052p/FE5Oyxt3jnhgf/K/MsZm/TJnSvqXmASOMk8hA==", "shasum": "6650a958ab63187bc5b95e3c3eabc49d6ff17a10", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.0-1.tgz", "fileCount": 3, "unpackedSize": 2730994, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFr0BXFxs53h4NRkJV360eDeE1BAWJqZQuVbrSh1qefhAiEAs12GGtQAN7lMyZoSxDYqAOoNLdAV1kxPcUwVuv58tSE="}], "size": 1038319}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.0-1_1731565990724_0.3028024961225093"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-14T06:33:11.015Z", "publish_time": 1731565991015, "_source_registry_name": "default"}, "4.27.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.0", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VrYQHY5+Y71OU/uOSRE9lLhph16bbuWGrMwGwZDPxCUXUW5NgLA+K+q0kv7rafHRlnrsZSVcMOkZskzTNnR3ZQ==", "shasum": "efe48aa0820420f71b9b54eccea9924a59429671", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.0.tgz", "fileCount": 3, "unpackedSize": 2761200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAu3wri5lG9jBxqXJQAeE7L0H1AhAHSmIwuavlwFCSVQIhAJHG1r6bkmGcVfceIPErV1VulhP5noUGEKoopxVmDC8y"}], "size": 1048576}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.0_1731667235965_0.889495344625767"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T10:40:36.197Z", "publish_time": 1731667236197, "_source_registry_name": "default"}, "4.27.1-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.1-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.1-0", "readmeFilename": "README.md", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NP6bgGeWN524Urogt11q/tWVecrkL2tyT4Ct559//AiW9UYgQ4xEGCLjo5LY5VZDj+RdiJwRiWibxpYt340xRg==", "shasum": "576e78bf6db69e35b59cc7e9160e0abc0fdca658", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.1-0.tgz", "fileCount": 3, "unpackedSize": 2761202, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmt49/O/6HlNFG4oyezrtXHeghrjvEDpldzFAhvQX73wIgPceCDjhQ2t6dEkQqGVE8G8d34ua/hdKXanYBODtGBPc="}], "size": 1048579}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.1-0_1731677287581_0.019509376229615016"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T13:28:07.844Z", "publish_time": 1731677287844, "_source_registry_name": "default"}, "4.27.1-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fa040fd37e05007c0bca4270f688ba4df543613f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-3nvzHq2GU/NAkj+FDslPJfSomLbk7VLlNa8v3Xik8IJ8uPWY6QZEGnHK7C1BMfJwTl14cVsTLG1TEyIUNcayqQ==", "signatures": [{"sig": "MEUCIG3yNTmxJar8sZFicY/eLJQmbmCobzCknR9h0XyIwntcAiEA8zMgxG2q6+WDjU7qQVETx2TED86xAxgKNkPeZB7RuiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761202, "size": 1048579}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.27.1-1_1731685083063_0.4865424263887359", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-11-15T15:38:03.382Z", "publish_time": 1731685083382, "_source_registry_name": "default"}, "4.27.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.1", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-AcQsa9FF6T9FrHXWXGAqJ6Kjcae2lYEDZA7wRQmK/3Bvv/0hH38tJL51CYclTY90fRf1mtKuwpC4LRcMkZuV1w==", "shasum": "928391f58dd298d7c2a69170fcf5984d788dec63", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.1.tgz", "fileCount": 3, "unpackedSize": 2761200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDml8NTTnZbRLacdVhZ9ppolc3bfL2zJ+6jFHauku52UQIhALKvTlUcfrMMc/H3AzT0FecwPjxaF+qB0l/5Zgtjo1S2"}], "size": 1048577}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.1_1731686861754_0.9699401634618721"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T16:07:42.003Z", "publish_time": 1731686862003, "_source_registry_name": "default"}, "4.27.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.2", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-euMIv/4x5Y2/ImlbGl88mwKNXDsvzbWUlT7DFky76z2keajCtcbAsN9LUdmk31hAoVmJJYSThgdA0EsPeTr1+w==", "shasum": "3ccf1f8a7e74ca22ad9cf258d31077320cfac19e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.2.tgz", "fileCount": 3, "unpackedSize": 2761200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEncpYBqeQKp6/is3ueWcQBvEvWc/6s/YcLFZScvw9joAiEAq4v+blLfMVqEC0114/tf4gwwrMB7duT9cic8gO0ROx0="}], "size": 1048580}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.2_1731691203286_0.3782801489659764"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T17:20:03.555Z", "publish_time": 1731691203555, "_source_registry_name": "default"}, "4.27.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.3", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ogfbEVQgIZOz5WPWXF2HVb6En+kWzScuxJo/WdQTqEgeyGkaa2ui5sQav9Zkr7bnNCLK48uxmmK0TySm22eiuw==", "shasum": "3f2db9347c5df5e6627a7e12d937cea527d63526", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.3.tgz", "fileCount": 3, "unpackedSize": 2761200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF22/DvizJPhNhPx/RhVJwirQHDYnoASkEm/7xZTCcJ5AiA2UASUM0+CqTdqgWW5owo4Rsh5syPx3fibjiFAspSb3Q=="}], "size": 1048644}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.3_1731947976161_0.4458770440249713"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-18T16:39:36.407Z", "publish_time": 1731947976407, "_source_registry_name": "default"}, "4.27.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.27.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.27.4", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-yOpVsA4K5qVwu2CaS3hHxluWIK5HQTjNV4tWjQXluMiiiu4pJj4BN98CvxohNCpcjMeTXk/ZMJBRbgRg8HBB6A==", "shasum": "030b6cc607d845da23dced624e47fb45de105840", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.27.4.tgz", "fileCount": 3, "unpackedSize": 2751472, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBKm0lQeG0jf2257l2dbWH2PrLRSo2cDvxHDOfhKsEBFAiEAg5EVdoqClWgqYYCBtaK5bShIMAmykhF80316ZnsWrsU="}], "size": 1043590}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.27.4_1732345221430_0.4128535177123134"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-23T07:00:21.698Z", "publish_time": 1732345221698, "_source_registry_name": "default"}, "4.28.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.28.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.28.0", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Vi+WR62xWGsE/Oj+mD0FNAPY2MEox3cfyG0zLpotZdehPFXwz6lypkGs5y38Jd/NVSbOD02aVad6q6QYF7i8Bg==", "shasum": "69682a2a10d9fedc334f87583cfca83c39c08077", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.28.0.tgz", "fileCount": 3, "unpackedSize": 2748912, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmse+Ou79r0wladdACQkVS4+QzNQM5UmW2X5tK8xpPaQIgdjVDAKk1h+0NAgYrma6l8cAO43vANCuDqfA9iD78sZA="}], "size": 1044143}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.28.0_1732972547625_0.2812330032091377"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-30T13:15:47.859Z", "publish_time": 1732972547859, "_source_registry_name": "default"}, "4.28.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.28.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.28.1", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-wSXmDRVupJstFP7elGMgv+2HqXelQhuNf+IS4V+nUpNVi/GUiBgDmfwD0UGN3pcAnWsgKG3I52wMOBnk1VHr/A==", "shasum": "631ffeee094d71279fcd1fe8072bdcf25311bc11", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.28.1.tgz", "fileCount": 3, "unpackedSize": 2751472, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFoVLsTG8wXYb+gSf/WIzdoyzw0abvwrYj0K+cmtc11jAiBn/GyodH11zRXh8qnWeE9OHZoMB7w3yIh946jDDbxezQ=="}], "size": 1045213}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-win32-arm64-msvc_4.28.1_1733485496652_0.34680105562472496"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T11:44:56.897Z", "publish_time": 1733485496897, "_source_registry_name": "default"}, "4.29.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0-0", "readmeFilename": "README.md", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UaTUHv9yk9PPTvd3q7dXepvC3rhL45XeMmDHnZe0KrzBOXlady2Q9BdOtcKe6uvDpyZoqIwjlNhnYWoQjcueQA==", "shasum": "7e48cd880183efe32db4ff9d9742e8b2d21b221c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0-0.tgz", "fileCount": 3, "unpackedSize": 2748914, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhsnyP6D7RaPB8j1im1NNt1JjemNoLz37EzfyvzAFFDwIgJO7lZZY+mccHdOyio2NeTzgWaRS6gBqxBZP/llZPkM8="}], "size": 1044077}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.29.0-0_1734331193145_0.5082836522475351"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-16T06:39:53.358Z", "publish_time": 1734331193358, "_source_registry_name": "default"}, "4.29.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0-1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0-1", "readmeFilename": "README.md", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-E3AqECbYWNAlCS9OelgGqAgbdPr8/cQGNcpY/IdkXdPuNwWXZLdI76aM+xXmYWTsY1RQjHhI2qKnt3bUxDniWA==", "shasum": "9fcbcf9e5835b57c16a7619ef6d870cce93e8a52", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0-1.tgz", "fileCount": 3, "unpackedSize": 2748914, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUu/GvOw3jkODXdtD9U2YGcVUJF/8buI6lyAm+4Dnq/AIhALrdFBfnCZeq0Ym3Uy2MT1NGsDDqJmcq7fbvCv7H9ZOa"}], "size": 1044078}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.29.0-1_1734590251871_0.7860825709170882"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-19T06:37:32.055Z", "publish_time": 1734590252055, "_source_registry_name": "default"}, "4.29.0-2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0-2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0-2", "readmeFilename": "README.md", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-09oF/8AHlVErs/4ZuX2tMfkr9uHd/inXSXb+AqfnNXvJx2nrbPUWi+SZpwzHp3v3eGHATzpg8GFYP653Opxh2A==", "shasum": "c548fd1a70da1efbc9d471228b244e9fb1ea51ca", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0-2.tgz", "fileCount": 3, "unpackedSize": 2748914, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDuqOgVCy/iXDug6Lw7vR48r4zfcCipBUKBY4xFHMJTnAiB7Ys/DrZOf6IDwTslVrTraRb186Qxbqv30UBs3qGduJQ=="}], "size": 1044078}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.29.0-2_1734677763079_0.762975030054557"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T06:56:03.306Z", "publish_time": 1734677763306, "_source_registry_name": "default"}, "4.29.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.0", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jA4+oxG7QTTtSQxwSHzFVwShcppHO2DpkbAM59pfD5WMG/da79yQaeBtXAfGTI+ciUx8hqK3RF3H2KWByITXtQ==", "shasum": "d7b388fc20d421db007e3078b934f3a40d8c75c5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.0.tgz", "fileCount": 3, "unpackedSize": 2743792, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+PY8mo5zBVE9LWoU/GtOcYDKzRWNZgBfcMa9LRgvfZgIhAJyKB7EtkP3/2Ouy8Ai3BUJzkQhXGY/qsU5ecDMH8tAh"}], "size": 1042340}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.29.0_1734719844821_0.7627809578941189"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T18:37:25.058Z", "publish_time": 1734719845058, "_source_registry_name": "default"}, "4.29.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.1", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-F2OiJ42m77lSkizZQLuC+jiZ2cgueWQL5YC9tjo3AgaEw+KJmVxHGSyQfDUoYR9cci0lAywv2Clmckzulcq6ig==", "shasum": "7f12efb8240b238346951559998802722944421e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.1.tgz", "fileCount": 3, "unpackedSize": 2743792, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8hbDocSaxs/3EtWBWSo+ZG7Y1VVWmt0YxBGJGXoufQQIgUC+7VrjJ4CtMjsDykolHCfTqAwqdvf/1eXS5SV7CmzQ="}], "size": 1042339}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.29.1_1734765362613_0.9498926054212233"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:16:02.859Z", "publish_time": 1734765362859, "_source_registry_name": "default"}, "4.30.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.0-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.0-0", "readmeFilename": "README.md", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-nLm/SeSV7fs63mwIaFlW7NmHjf1uyin2aSdQHBC1nFJFd0wYYnDTYpvR0RSCDdspCbyaYUrmylfO4IEZ8nv3Yg==", "shasum": "557cd145b94c4d6e860f7decc522ffd0edf94013", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.0-0.tgz", "fileCount": 3, "unpackedSize": 2743794, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFggzyBqu+bglPdFZx8tsTIfWUdET9ZCOQ9gg+wNfCfAIgBvbeNPQ4OwDf4ThtkZjKMS8a5QDpDZmmvSvkS/r5k5s="}], "size": 1042337}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.30.0-0_1734765435114_0.6277029123323348"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:17:15.337Z", "publish_time": 1734765435337, "_source_registry_name": "default"}, "4.30.0-1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.0-1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.0-1", "readmeFilename": "README.md", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VupblZvQfSf1BFjn48GH9xBjGwns3fSXUNsXCEbbr8QqXig7nMEaWHuSpmaWOzDVNKqtkPwFRk8HNU7ktfizzQ==", "shasum": "f91b791eb4f7c58d7a900c3c8040903d046da963", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.0-1.tgz", "fileCount": 3, "unpackedSize": 2746866, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTwYO8c+OlashlnXHUDRWLVekuYcwNaCFZkaxdRpo2ZwIhAKV8a3JIBBo8He10TOG7aZjFifnmZKxz1HPnMd6ePL6p"}], "size": 1041152}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.30.0-1_1735541536855_0.9315339342112761"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-30T06:52:17.065Z", "publish_time": 1735541537065, "_source_registry_name": "default"}, "4.29.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.29.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.29.2", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-S2V0LlcOiYkNGlRAWZwwUdNgdZBfvsDHW0wYosYFV3c7aKgEVcbonetZXsHv7jRTTX+oY5nDYT4W6B1oUpMNOg==", "shasum": "7c56efd576618db251909e21818d473cbcf96786", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.29.2.tgz", "fileCount": 3, "unpackedSize": 2746864, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxDwcharRwaWGXdzt7lljAcfTxqUXv5i+5scxbyOU8UgIgGJxg+41WuALNfJkw8SlXp+eonxauMrN8fZAzENeDDeM="}], "size": 1041149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.29.2_1736078863278_0.29150204780232425"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-05T12:07:43.602Z", "publish_time": 1736078863602, "_source_registry_name": "default"}, "4.30.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.0", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jROwnI1+wPyuv696rAFHp5+6RFhXGGwgmgSfzE8e4xfit6oLRg7GyMArVUoM3ChS045OwWr9aTnU+2c1UdBMyw==", "shasum": "6b968f5b068469db16eac743811ee6c040671042", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.0.tgz", "fileCount": 3, "unpackedSize": 2746864, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7T3/JMXezfEk3awkxdFVvvq9oCllyMS4G/nR7muMLOAiBR2vw6wg3VhCp+KvXmOi8TbbUACOov4rlpvdjvPeGqqw=="}], "size": 1041149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.30.0_1736145401353_0.7053314552499155"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-06T06:36:41.603Z", "publish_time": 1736145401603, "_source_registry_name": "default"}, "4.30.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.30.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.30.1", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WabtHWiPaFF47W3PkHnjbmWawnX/aE57K47ZDT1BXTS5GgrBUEpvOzq0FI0V/UYzQJgdb8XlhVNH8/fwV8xDjw==", "shasum": "51cad812456e616bfe4db5238fb9c7497e042a52", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.30.1.tgz", "fileCount": 3, "unpackedSize": 2746864, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYocZ/iLBO8iDwmc+sjvVyVNwS55dcyf0CT88QDxkHbgIgB05fimlIqo43+neUsaYAuyhhUw4+Alb1voQAjN8sLMc="}], "size": 1041149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.30.1_1736246153032_0.9144180177453756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-07T10:35:53.302Z", "publish_time": 1736246153302, "_source_registry_name": "default"}, "4.31.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.31.0-0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.31.0-0", "readmeFilename": "README.md", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-I5sBb+c7mL3qQ0WzpVo9WnoWBtH6VBTJntsnJHupXZlvK0ZVkCn7hQdYEs6b05gBf9oHo7g9eCUIrHHQYl38hw==", "shasum": "2356c25427553c3cab3fe89b8b9259a9f290a453", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.31.0-0.tgz", "fileCount": 3, "unpackedSize": 2760690, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmMY15ryShPIKO00FlxQPL5hj4xATVniHfZZBUTt0UeAiEAsw5Wte60OtZ89rde8HVtJN79XCObhEw2jOUBD1ICaHI="}], "size": 1047539}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.31.0-0_1736834262629_0.9682330132528654"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-14T05:57:42.900Z", "publish_time": 1736834262900, "_source_registry_name": "default"}, "4.31.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.31.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.31.0", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-JuhN2xdI/m8Hr+aVO3vspO7OQfUFO6bKLIRTAy0U15vmWjnZDLrEgCZ2s6+scAYaQVpYSh9tZtRijApw9IXyMw==", "shasum": "32ed85810c1b831c648eca999d68f01255b30691", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.31.0.tgz", "fileCount": 3, "unpackedSize": 2787312, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGd1V/6aPLtv4pB3KvRH3p1VjxR/inGEUbMkqRxmqfUIAiAdH+85pvxcubE5zzGMB96KAc17FjFLCJ2w/pKoNnxiJg=="}], "size": 1051123}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.31.0_1737291407599_0.20957758282836214"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-19T12:56:47.836Z", "publish_time": 1737291407836, "_source_registry_name": "default"}, "4.32.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.32.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.32.0", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-pFDdotFDMXW2AXVbfdUEfidPAk/OtwE/Hd4eYMTNVVaCQ6Yl8et0meDaKNL63L44Haxv4UExpv9ydSf3aSayDg==", "shasum": "6534a09fcdd43103645155cedb5bfa65fbf2c23f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.32.0.tgz", "fileCount": 3, "unpackedSize": 2800112, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCrL29LmKec44AwrYK14PnZ+caFlriypftcORpcLUabogIhAJv3GX9T/VyyrSei4lkYd6SuVE8rTG4YH+DC+PSd5n0Y"}], "size": 1056133}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.32.0_1737707255614_0.3002173333110183"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-24T08:27:35.866Z", "publish_time": 1737707255866, "_source_registry_name": "default"}, "4.33.0-0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bd39ed7307a4e6471212d36cd420d19caad5c885", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-IEbgraO79IG9UF8cMymmtoxd14Xkd/HJF6faJ/A2pM25R02ABkHvTIUXGBuFOAgG/4531rTJrq+YMwh1RpH0cQ==", "signatures": [{"sig": "MEUCIQDCliWi7pgBaw9ab2Ae26caQcl8T2zfYAK/DAPcbfPSUQIgZs1WKitCrtMXb834UY08mvKZs5vIhphnD2OnwBLR6xk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2800114, "size": 1056137}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.33.0-0_1738053009250_0.09175165469654978", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-01-28T08:30:09.507Z", "publish_time": 1738053009507, "_source_registry_name": "default"}, "4.32.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.32.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.32.1", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-w2l3UnlgYTNNU+Z6wOR8YdaioqfEnwPjIsJ66KxKAf0p+AuL2FHeTX6qvM+p/Ue3XPBVNyVSfCrfZiQh7vZHLQ==", "shasum": "fa106304818078f9d3fc9005642ad99f596eed2d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.32.1.tgz", "fileCount": 3, "unpackedSize": 2800112, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF4J6dNNgSVbKXX4FEKSbVFiqp3Oso6dMCVB245hjf4VAiB5l8MIrMH8c2Q7+LPDttPdz5TRY32gDBBxbySAFlX1vg=="}], "size": 1056135}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.32.1_1738053198074_0.4278804042514486"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-28T08:33:18.309Z", "publish_time": 1738053198309, "_source_registry_name": "default"}, "4.33.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-win32-arm64-msvc@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e52d57829bac75ed398c6a38cd1114c275eb45e1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-Wmr+r26PU9fu2BMGA2Rb7FJf6yNUcnWrPvDHf0h15iOsY3iMxSCk98/Ez/QQOjLP6F0hTCCP7hWUuruoPbjAgQ==", "signatures": [{"sig": "MEYCIQDvdDcSH3t3w3ukeuJP8Iap1dDfUS4npJnYElczcZkKSwIhAOW1rAD2wWxYBO+DuNK5ouH2GPrWPuzqOoB2soE/upNA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2744816, "size": 1049464}, "main": "./rollup.win32-arm64-msvc.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-win32-arm64-msvc_4.33.0_1738393919566_0.323759864087223", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-02-01T07:11:59.808Z", "publish_time": 1738393919808, "_source_registry_name": "default"}, "4.34.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.0", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Vmg0NhAap2S54JojJchiu5An54qa6t/oKT7LmDaWggpIcaiL8WcWHEN6OQrfTdL6mQ2GFyH7j2T5/3YPEDOOGA==", "shasum": "e60db53ff6b3bd00baf13b04f91fddb43740077f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.0.tgz", "fileCount": 3, "unpackedSize": 2744816, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGL3XSVHiL7Xqap/536Q3bs/XK+BEzqMBNucra5OM5lGAiEApoEnqtezjxM0mTPu6JRkQe40QCJwZ47nPVaKIfSJZZU="}], "size": 1049464}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.0_1738399223861_0.37401692525846797"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-01T08:40:24.070Z", "publish_time": 1738399224070, "_source_registry_name": "default"}, "4.34.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.1", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-RnHy7yFf2Wz8Jj1+h8klB93N0NHNHXFhNwAmiy9zJdpY7DE01VbEVtPdrK1kkILeIbHGRJjvfBDBhnxBr8kD4g==", "shasum": "125f09f40719ea7a02a76607428a0366ff9dca4d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.1.tgz", "fileCount": 3, "unpackedSize": 2744816, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCVW96coRzkW3qJz2lDUb1qqSPiXt3UcvuiPXBWtyeoBQIhAMG24+EJ6XmeAUwhDlohUW2Jb9A3JFBScVXSKrcPDV76"}], "size": 1049464}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.1_1738565894056_0.3450857713524653"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-03T06:58:14.244Z", "publish_time": 1738565894244, "_source_registry_name": "default"}, "4.34.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.2", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-LQRkCyUBnAo7r8dbEdtNU08EKLCJMgAk2oP5H3R7BnUlKLqgR3dUjrLBVirmc1RK6U6qhtDw29Dimeer8d5hzQ==", "shasum": "8bbf8dfb84aac3a013baaa15c1d5340a84326cea", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.2.tgz", "fileCount": 3, "unpackedSize": 2744816, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBK4yJWwmeQ+jf+uuDA4/FKpM5GDWDRZo9UEvWmFaj6qAiEAwIAh5T5r2uKOz86F+ekrmRp4VZ2/l/xCHhbb8ic8ml4="}], "size": 1049467}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.2_1738656602596_0.12303251794350611"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-04T08:10:02.853Z", "publish_time": 1738656602853, "_source_registry_name": "default"}, "4.34.3": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.3", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.3", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ojFOKaz/ZyalIrizdBq2vyc2f0kFbJahEznfZlxdB6pF9Do6++i1zS5Gy6QLf8D7/S57MHrmBLur6AeRYeQXSA==", "shasum": "183fb4b849accdf68d430894ada2b88eea95a140", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.3.tgz", "fileCount": 3, "unpackedSize": 2744816, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHFo+caCHy3GcJYLXlARPDEp7kkiLxTYTAbiSTFaEihfAiEA/kvjooTfI/gk0jXfRoCviIRrnsjR7yKi4uxVJzJknE4="}], "size": 1049465}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.3_1738747324987_0.5241975258207028"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T09:22:05.218Z", "publish_time": 1738747325218, "_source_registry_name": "default"}, "4.34.4": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.4", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.4", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qORc3UzoD5UUTneiP2Afg5n5Ti1GAW9Gp5vHPxzvAFFA3FBaum9WqGvYXGf+c7beFdOKNos31/41PRMUwh1tpA==", "shasum": "33423f0b5e763aa79d9ef14aed9e22c3217a5051", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.4.tgz", "fileCount": 3, "unpackedSize": 2744816, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCM8ym/aVZHPwiHt6f8BIqwWe7NwAOJMOFFTYjNaQC6DgIhAPu09C4kkbbVaHebqcngjAddouT8/oxnkQw+6YSeYWFf"}], "size": 1049465}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.4_1738791071287_0.9276114509964968"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T21:31:11.562Z", "publish_time": 1738791071562, "_source_registry_name": "default"}, "4.34.5": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.5", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.5", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PUbWd+h/h6rUowalDYIdc9S9LJXbQDMcJe0BjABl3oT3efYRgZ8aUe8ZZDSie7y+fz6Z+rueNfdorIbkWv5Eqg==", "shasum": "d9f4a433addf4d36264ddc15130144ee8506665a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.5.tgz", "fileCount": 3, "unpackedSize": 2719728, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBxiKw517wnZUM0YJrALqTJ6uK53rvVrlYEdJc7310DDAiEAzwmbsZVSnzsfza9VwRKABsnb24mcw+h4QUo+DWuhwdQ="}], "size": 1040379}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.5_1738918382453_0.12346437631214946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T08:53:02.736Z", "publish_time": 1738918382736, "_source_registry_name": "default"}, "4.34.6": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.6", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.6", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3/q1qUsO/tLqGBaD4uXsB6coVGB3usxw3qyeVb59aArCgedSF66MPdgRStUd7vbZOsko/CgVaY5fo2vkvPLWiA==", "shasum": "c6c5bf290a3a459c18871110bc2e7009ce35b15a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.6.tgz", "fileCount": 3, "unpackedSize": 2715120, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEtaCpY/CPKWXhX1VOOH3j5Xn/Y+wvZPAnmf2RU67z1VAiByvjxcBkEdbZHpb/yn3wAT+CdJWDaQIq0BrvDi2MZ5Qg=="}], "size": 1037936}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.6_1738945927750_0.8458255386846991"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T16:32:08.106Z", "publish_time": 1738945928106, "_source_registry_name": "default"}, "4.34.7": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.7", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.7", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-MN7aaBC7mAjsiMEZcsJvwNsQVNZShgES/9SzWp1HC9Yjqb5OpexYnRjF7RmE4itbeesHMYYQiAtUAQaSKs2Rfw==", "shasum": "06cedc0ef3cbf1cbd8abcf587090712e40ae6941", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.7.tgz", "fileCount": 3, "unpackedSize": 2715632, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB+bREGL1ic5PANehRYrlQIvLy6IlJLbs8O/pxBzH6ByAiBHOWtwFRQZYrintvNEssCCnQsLg8VmKMxi8ryP9o7oog=="}], "size": 1039407}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.7_1739526839251_0.14370273314171067"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T09:53:59.463Z", "publish_time": 1739526839463, "_source_registry_name": "default"}, "4.34.8": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.8", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.8", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YHYsgzZgFJzTRbth4h7Or0m5O74Yda+hLin0irAIobkLQFRQd1qWmnoVfwmKm9TXIZVAD0nZ+GEb2ICicLyCnQ==", "shasum": "cbfee01f1fe73791c35191a05397838520ca3cdd", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.8.tgz", "fileCount": 3, "unpackedSize": 2715632, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFuwE385P1d3ThPic2IIAPMM6rCPO41laE8nvKI3zByMAiEArO3BK/b3ATfv/DDiiJBhapjhuIsVCiUwZPjzk3vAFLU="}], "size": 1039406}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.8_1739773585687_0.05214642674013281"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-17T06:26:25.882Z", "publish_time": 1739773585882, "_source_registry_name": "default"}, "4.34.9": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.34.9", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.34.9", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==", "shasum": "89427dcac0c8e3a6d32b13a03a296a275d0de9a9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz", "fileCount": 3, "unpackedSize": 2893296, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDFYu5d9OUYKOZLjityoWlkvwVKEVLo7HXoHpfYeBwflQIgI19NyfSAF+kSE5A8xuSCVTUKESg01cEKP99JQaVvt74="}], "size": 1107678}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.34.9_1740814356805_0.6405027723184651"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-01T07:32:37.070Z", "publish_time": 1740814357070, "_source_registry_name": "default"}, "4.35.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.35.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.35.0", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-OUOlGqPkVJCdJETKOCEf1mw848ZyJ5w50/rZ/3IBQVdLfR5jk/6Sr5m3iO2tdPgwo0x7VcncYuOvMhBWZq8ayg==", "shasum": "b0b37e2d77041e3aa772f519291309abf4c03a84", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.35.0.tgz", "fileCount": 3, "unpackedSize": 2897904, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH2QmGv0Fn7rQBVwoTPQwVFHI9i7qELA6EdIU9A7BdJIAiEAnw+KDD93dpS7R6ddcTeSM2jcAh/CRio0MHDSVF+p33g="}], "size": 1112990}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.35.0_1741415084511_0.5060878016712071"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-08T06:24:44.703Z", "publish_time": 1741415084703, "_source_registry_name": "default"}, "4.36.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.36.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.36.0", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qbqt4N7tokFwwSVlWDsjfoHgviS3n/vZ8LK0h1uLG9TYIRuUTJC88E1xb3LM2iqZ/WTqNQjYrtmtGmrmmawB6A==", "shasum": "bfda7178ed8cb8fa8786474a02eae9fc8649a74d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.36.0.tgz", "fileCount": 3, "unpackedSize": 2909680, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCID33kfalP91EcRKJlmK871948ROdTAYy59aDLF4Yw/MqAiEAkW9Ls07VzPFWWbIaja5C1bTCUAq3k9sLgE8zNGKpzx4="}], "size": 1115712}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.36.0_1742200543997_0.5140434835469121"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-17T08:35:44.165Z", "publish_time": 1742200544165, "_source_registry_name": "default"}, "4.37.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.37.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.37.0", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Jm7biMazjNzTU4PrQtr7VS8ibeys9Pn29/1bm4ph7CP2kf21950LgN+BaE2mJ1QujnvOc6p54eWWiVvn05SOBg==", "shasum": "8063d5f8195dd1845e056d069366fbe06a424d09", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.37.0.tgz", "fileCount": 3, "unpackedSize": 2914800, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFERDq8Be2HxG5NX0hPx0rRkMDP6un+aZwEnS9KGTjsZAiBcuQUehgi2vi41KyiSH9ASYVPid6JLEpP8dokm4iidZA=="}], "size": 1115107}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.37.0_1742741827681_0.4526816623725194"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-23T14:57:07.923Z", "publish_time": 1742741827923, "_source_registry_name": "default"}, "4.38.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.38.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.38.0", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-u/Jbm1BU89Vftqyqbmxdq14nBaQjQX1HhmsdBWqSdGClNaKwhjsg5TpW+5Ibs1mb8Es9wJiMdl86BcmtUVXNZg==", "shasum": "d27ab565009357014c9f2d6393ee58bd63a63cb8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.38.0.tgz", "fileCount": 3, "unpackedSize": 2908144, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDF7fLD2TjUp2ZGD3uEO9U2AieKzRXK850Bk2s7XKOswwIgP/xzGKpkYYXsxy/BNdULyDsbZwX2aSo//oLwm0oUYD4="}], "size": 1115535}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.38.0_1743229745804_0.06656309954132267"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-29T06:29:06.074Z", "publish_time": 1743229746074, "_source_registry_name": "default"}, "4.39.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.39.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.39.0", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jDrLm6yUtbOg2TYB3sBF3acUnAwsIksEYjLeHL+TJv9jg+TmTwdyjnDex27jqEMakNKf3RwwPahDIt7QXCSqRQ==", "shasum": "3a3f421f5ce9bd99ed20ce1660cce7cee3e9f199", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.39.0.tgz", "fileCount": 3, "unpackedSize": 2908144, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICC6u8p+cDlhxslCMH0gdOHbpAy3xvmA8i46g8p3iua/AiBYNeZLPxe9WbOM1E1VXW5qe3Nh9A+EzshCrk6GDIVCxQ=="}], "size": 1115534}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.39.0_1743569372148_0.9770852055294168"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T04:49:32.369Z", "publish_time": 1743569372369, "_source_registry_name": "default"}, "4.40.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.40.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.40.0", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UtZQQI5k/b8d7d3i9AZmA/t+Q4tk3hOC0tMOMSq2GlMYOfxbesxG4mJSeDp0EHs30N9bsfwUvs3zF4v/RzOeTQ==", "shasum": "c5bee19fa670ff5da5f066be6a58b4568e9c650b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.0.tgz", "fileCount": 3, "unpackedSize": 2922480, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGvszmj+a0odADjIB/GyYQay5WCY+SzK/JfMyZ82iG8AAiEAzHul/fSaMhHO44UviQ7+MZjj4cCYsiCu6aUCSynr5T8="}], "size": 1117639}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.40.0_1744447175768_0.24776486361186922"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-12T08:39:35.968Z", "publish_time": 1744447175968, "_source_registry_name": "default"}, "4.40.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.40.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.40.1", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-b2bcNm9Kbde03H+q+Jjw9tSfhYkzrDUf2d5MAd1bOJuVplXvFhWz7tRtWvD8/ORZi7qSCy0idW6tf2HgxSXQSg==", "shasum": "c7724c386eed0bda5ae7143e4081c1910cab349b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.1.tgz", "fileCount": 3, "unpackedSize": 2993136, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGa0dFvBN4Fhpfb/pDfPzZnaTpAgSjOSqEhVOZefuQLSAiBXLTYNP3SyxoWWINE76JKmZM0QzGw12mD4EX9OzVha2w=="}], "size": 1139727}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.40.1_1745814924236_0.3838260235665385"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-28T04:35:24.474Z", "publish_time": 1745814924474, "_source_registry_name": "default"}, "4.40.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.40.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.40.2", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Bjv/HG8RRWLNkXwQQemdsWw4Mg+IJ29LK+bJPW2SCzPKOUaMmPEppQlu/Fqk1d7+DX3V7JbFdbkh/NMmurT6Pg==", "shasum": "6397e1e012db64dfecfed0774cb9fcf89503d716", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.2.tgz", "fileCount": 3, "unpackedSize": 2997744, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAtc4krQC8c2DMsZ6b73wRvMkXWJt6ZKMDnTJIjmIH0ZAiAD3IaIcWa0+ze0xDzGYwUCHKYJJdou0HJWPUJNE4fSkg=="}], "size": 1138657}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.40.2_1746516413105_0.5282701789678799"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T07:26:53.317Z", "publish_time": 1746516413317, "_source_registry_name": "default"}, "4.41.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.41.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.41.0", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4yodtcOrFHpbomJGVEqZ8fzD4kfBeCbpsUy5Pqk4RluXOdsWdjLnjhiKy2w3qzcASWd04fp52Xz7JKarVJ5BTg==", "shasum": "3b7bbd9f43f1c380061f306abce6f3f64de20306", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.0.tgz", "fileCount": 3, "unpackedSize": 3107824, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDv4oNXEwMD46eKqGUKT0gueG5HYfg6DhgY4XBwHYaZLgIgbZLfJrbsPjGyDx9pS6tcIhfJC558lS7Tz0fgAClW3ZY="}], "size": 1181773}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.41.0_1747546413268_0.4718916182828139"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-18T05:33:33.495Z", "publish_time": 1747546413495, "_source_registry_name": "default"}, "4.41.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.41.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.41.1", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lZkCxIrjlJlMt1dLO/FbpZbzt6J/A8p4DnqzSa4PWqPEUUUnzXLeki/iyPLfV0BmHItlYgHUqJe+3KiyydmiNQ==", "shasum": "7eeada98444e580674de6989284e4baacd48ea65", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.1.tgz", "fileCount": 3, "unpackedSize": 3267568, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDwvhnEGjhHmesMBvFY50scMDg0uZZeN9gAxDZRYoGr9AiAe5pby2CV359ieMblV2wcFzMa4k4a8BeZgct9/LnWyhw=="}], "size": 1234111}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.41.1_1748067271557_0.42276067949263796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-24T06:14:31.783Z", "publish_time": 1748067271783, "_source_registry_name": "default"}, "4.41.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.41.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.41.2", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-LOXSg8GprvL36erslsrNEUirlxy28JcuyTH5PYSBj8wwa0gDQlR8sZricFRbZGCzLhFixvmW2ozj7Mi+j023sg==", "shasum": "eeea3d6b73f18f3fdb065f86fe028f190e36e4b6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.2.tgz", "fileCount": 3, "unpackedSize": 3271152, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGRDjNLuQ1euh2EVmJw0VjKKKDrmAUmgQBMHoKGxMmfTAiBfp/NIp+rPPC9s/k/bqY9yyWUQJxXoB9AuxSK3JKSn+A=="}], "size": 1237900}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.41.2_1749210032389_0.8163569975324525"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T11:40:32.620Z", "publish_time": 1749210032620, "_source_registry_name": "default"}, "4.42.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.42.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.42.0", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-+axkdyDGSp6hjyzQ5m1pgcvQScfHnMCcsXkx8pTgy/6qBmWVhtRVlgxjWwDp67wEXXUr0x+vD6tp5W4x6V7u1A==", "shasum": "395ad8b6b6372a3888d2e96bf6c45392be815f4d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.42.0.tgz", "fileCount": 3, "unpackedSize": 3271152, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDwsCZYC3J0epdxCT4Vxdhb9x9kRvJa8azXj5sD3BuMXgIgF0G3YluElnFjGqxK/3CI4VLJzX2VnHgvXgNXce7DPGg="}], "size": 1237899}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.42.0_1749221292839_0.6323625923722607"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T14:48:13.088Z", "publish_time": 1749221293088, "_source_registry_name": "default"}, "4.43.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.43.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.43.0", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==", "shasum": "d6d84aace2b211119bf0ab1c586e29d01e32aa01", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz", "fileCount": 3, "unpackedSize": 3271152, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFluJvPLlPjn7wsVlORzgRHTVov7/XIy6vv5Cx90PUFfAiEA1h3ZSRX0mIdKhMfNKQV4veUkt/g0YDPWAjcwoGBvS9c="}], "size": 1237900}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.43.0_1749619357839_0.06672660166916211"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T05:22:38.072Z", "publish_time": 1749619358072, "_source_registry_name": "default"}, "4.44.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.44.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-M0CpcHf8TWn+4oTxJfh7LQuTuaYeXGbk0eageVjQCKzYLsajWS/lFC94qlRqOlyC2KvRT90ZrfXULYmukeIy7w==", "shasum": "41ffab489857987c75385b0fc8cccf97f7e69d0a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.0.tgz", "fileCount": 3, "unpackedSize": 3244528, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD4LhHKvWVDIINvxk3BEyrEanBU8mDY/2sbKD0GOYLr6wIhAJ2y8/AZkHQNarkF+i9VCfh3O3nE1PsuodAmd9GF4LM1"}], "size": 1232044}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.44.0_1750314177023_0.6760716989748929"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-19T06:22:57.281Z", "publish_time": 1750314177281, "_source_registry_name": "default"}, "4.44.1": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.44.1", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.44.1", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NtSJVKcXwcqozOl+FwI41OH3OApDyLk3kqTJgx8+gp6On9ZEt5mYhIsKNPGuaZr3p9T6NWPKGU/03Vw4CNU9qg==", "shasum": "d4aae38465b2ad200557b53c8c817266a3ddbfd0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.1.tgz", "fileCount": 3, "unpackedSize": 3244528, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDSUAagUCRvXndxP/RM/QI+pEiHxVaYxEJ9fdSuRAP8hAIhAMmCL7d217ZXkgBxGiXCuh2HEbrL6X3g6vnHJwM5XaB0"}], "size": 1232046}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.44.1_1750912456659_0.11578225673583598"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-26T04:34:16.917Z", "publish_time": 1750912456917, "_source_registry_name": "default"}, "4.44.2": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.44.2", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VfU0fsMK+rwdK8mwODqYeM2hDrF2WiHaSmCBrS7gColkQft95/8tphyzv2EupVxn3iE0FI78wzffoULH1G+dkw==", "shasum": "fcf3e62edd76c560252b819f69627685f65887d7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.2.tgz", "fileCount": 3, "unpackedSize": 3258864, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFkkl5flpj+aMLOOGoXGuoiDnZ/4vPSGee0vpb5M8VCsAiEA8sMBoSpZGM+jcBM1cHy/hV+80HEnOACilAQmf/d5Vu4="}], "size": 1229590}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.44.2_1751633768551_0.30178059724223427"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-04T12:56:08.839Z", "publish_time": 1751633768839, "_source_registry_name": "default"}, "4.45.0": {"name": "@rollup/rollup-win32-arm64-msvc", "version": "4.45.0", "os": ["win32"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.win32-arm64-msvc.node", "_id": "@rollup/rollup-win32-arm64-msvc@4.45.0", "gitHead": "b7c7c1159f70ebe8ad6f94c942ebab2fa59c7982", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-B+IJgcBnE2bm93jEW5kHisqvPITs4ddLOROAcOc/diBgrEiQJJ6Qcjby75rFSmH5eMGrqJryUgJDhrfj942apQ==", "shasum": "42e08bf3ea4fc463fc9f199c4f0310a736f03eb1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.45.0.tgz", "fileCount": 3, "unpackedSize": 3258864, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAfE1ln1WddrHpKJ7EujaTqupacHOa9+/HHBCFCyj78/AiBf4AviyZKU4JJi/pgJ27RqAAak6nMA+dvNac+7eTfx4g=="}], "size": 1229592}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-win32-arm64-msvc_4.45.0_1752299643579_0.48954132622602575"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-12T05:54:03.804Z", "publish_time": 1752299643804, "_source_registry_name": "default"}}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "description": "Native bindings for Rollup", "homepage": "https://rollupjs.org/", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "license": "MIT", "maintainers": [{"name": "lukastaegert", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "shellscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "readme": "# `@rollup/rollup-win32-arm64-msvc`\n\nThis is the **aarch64-pc-windows-msvc** binary for `rollup`\n", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "_source_registry_name": "default"}