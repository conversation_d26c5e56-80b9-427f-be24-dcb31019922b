{"_attachments": {}, "_id": "sortablejs", "_rev": "7579-61f16053830fd08f52a4f2b8", "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "dist-tags": {"latest": "1.15.6"}, "license": "MIT", "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "name": "sortablejs", "readme": "# Sortable &nbsp; [![Financial Contributors on Open Collective](https://opencollective.com/Sortable/all/badge.svg?label=financial+contributors)](https://opencollective.com/Sortable) [![CircleCI](https://circleci.com/gh/SortableJS/Sortable.svg?style=svg)](https://circleci.com/gh/SortableJS/Sortable) [![DeepScan grade](https://deepscan.io/api/teams/3901/projects/5666/branches/43977/badge/grade.svg)](https://deepscan.io/dashboard#view=project&tid=3901&pid=5666&bid=43977) [![](https://data.jsdelivr.com/v1/package/npm/sortablejs/badge)](https://www.jsdelivr.com/package/npm/sortablejs) [![npm](https://img.shields.io/npm/v/sortablejs.svg)](https://www.npmjs.com/package/sortablejs)\r\n\r\nSortable is a JavaScript library for reorderable drag-and-drop lists.\r\n\r\nDemo: http://sortablejs.github.io/Sortable/\r\n\r\n[<img width=\"250px\" src=\"https://raw.githubusercontent.com/SortableJS/Sortable/HEAD/st/saucelabs.svg?sanitize=true\">](https://saucelabs.com/)\r\n\r\n## Features\r\n\r\n * Supports touch devices and [modern](http://caniuse.com/#search=drag) browsers (including IE9)\r\n * Can drag from one list to another or within the same list\r\n * CSS animation when moving items\r\n * Supports drag handles *and selectable text* (better than voidberg's html5sortable)\r\n * Smart auto-scrolling\r\n * Advanced swap detection\r\n * Smooth animations\r\n * [Multi-drag](https://github.com/SortableJS/Sortable/tree/master/plugins/MultiDrag) support\r\n * Support for CSS transforms\r\n * Built using native HTML5 drag and drop API\r\n * Supports\r\n   * [Meteor](https://github.com/SortableJS/meteor-sortablejs)\r\n   * Angular\r\n     * [2.0+](https://github.com/SortableJS/angular-sortablejs)\r\n     * [1.&ast;](https://github.com/SortableJS/angular-legacy-sortablejs)\r\n   * React\r\n     * [ES2015+](https://github.com/SortableJS/react-sortablejs)\r\n     * [Mixin](https://github.com/SortableJS/react-mixin-sortablejs)\r\n   * [Knockout](https://github.com/SortableJS/knockout-sortablejs)\r\n   * [Polymer](https://github.com/SortableJS/polymer-sortablejs)\r\n   * [Vue](https://github.com/SortableJS/Vue.Draggable)\r\n   * [Ember](https://github.com/SortableJS/ember-sortablejs)\r\n * Supports any CSS library, e.g. [Bootstrap](#bs)\r\n * Simple API\r\n * Support for [plugins](#plugins)\r\n * [CDN](#cdn)\r\n * No jQuery required (but there is [support](https://github.com/SortableJS/jquery-sortablejs))\r\n * Typescript definitions at `@types/sortablejs`\r\n\r\n\r\n<br/>\r\n\r\n\r\n### Articles\r\n\r\n * [Dragging Multiple Items in Sortable](https://github.com/SortableJS/Sortable/wiki/Dragging-Multiple-Items-in-Sortable) (April 26, 2019)\r\n * [Swap Thresholds and Direction](https://github.com/SortableJS/Sortable/wiki/Swap-Thresholds-and-Direction) (December 2, 2018)\r\n * [Sortable v1.0 — New capabilities](https://github.com/SortableJS/Sortable/wiki/Sortable-v1.0-—-New-capabilities/) (December 22, 2014)\r\n * [Sorting with the help of HTML5 Drag'n'Drop API](https://github.com/SortableJS/Sortable/wiki/Sorting-with-the-help-of-HTML5-Drag'n'Drop-API/) (December 23, 2013)\r\n\r\n<br/>\r\n\r\n### Getting Started\r\n\r\nInstall with NPM:\r\n```bash\r\nnpm install sortablejs --save\r\n```\r\n\r\nInstall with Bower:\r\n```bash\r\nbower install --save sortablejs\r\n```\r\n\r\nImport into your project:\r\n```js\r\n// Default SortableJS\r\nimport Sortable from 'sortablejs';\r\n\r\n// Core SortableJS (without default plugins)\r\nimport Sortable from 'sortablejs/modular/sortable.core.esm.js';\r\n\r\n// Complete SortableJS (with all plugins)\r\nimport Sortable from 'sortablejs/modular/sortable.complete.esm.js';\r\n```\r\n\r\nCherrypick plugins:\r\n```js\r\n// Cherrypick extra plugins\r\nimport Sortable, { MultiDrag, Swap } from 'sortablejs';\r\n\r\nSortable.mount(new MultiDrag(), new Swap());\r\n\r\n\r\n// Cherrypick default plugins\r\nimport Sortable, { AutoScroll } from 'sortablejs/modular/sortable.core.esm.js';\r\n\r\nSortable.mount(new AutoScroll());\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n### Usage\r\n```html\r\n<ul id=\"items\">\r\n\t<li>item 1</li>\r\n\t<li>item 2</li>\r\n\t<li>item 3</li>\r\n</ul>\r\n```\r\n\r\n```js\r\nvar el = document.getElementById('items');\r\nvar sortable = Sortable.create(el);\r\n```\r\n\r\nYou can use any element for the list and its elements, not just `ul`/`li`. Here is an [example with `div`s](https://jsbin.com/visimub/edit?html,js,output).\r\n\r\n\r\n---\r\n\r\n\r\n### Options\r\n```js\r\nvar sortable = new Sortable(el, {\r\n\tgroup: \"name\",  // or { name: \"...\", pull: [true, false, 'clone', array], put: [true, false, array] }\r\n\tsort: true,  // sorting inside list\r\n\tdelay: 0, // time in milliseconds to define when the sorting should start\r\n\tdelayOnTouchOnly: false, // only delay if user is using touch\r\n\ttouchStartThreshold: 0, // px, how many pixels the point should move before cancelling a delayed drag event\r\n\tdisabled: false, // Disables the sortable if set to true.\r\n\tstore: null,  // @see Store\r\n\tanimation: 150,  // ms, animation speed moving items when sorting, `0` — without animation\r\n\teasing: \"cubic-bezier(1, 0, 0, 1)\", // Easing for animation. Defaults to null. See https://easings.net/ for examples.\r\n\thandle: \".my-handle\",  // Drag handle selector within list items\r\n\tfilter: \".ignore-elements\",  // Selectors that do not lead to dragging (String or Function)\r\n\tpreventOnFilter: true, // Call `event.preventDefault()` when triggered `filter`\r\n\tdraggable: \".item\",  // Specifies which items inside the element should be draggable\r\n\r\n\tdataIdAttr: 'data-id', // HTML attribute that is used by the `toArray()` method\r\n\r\n\tghostClass: \"sortable-ghost\",  // Class name for the drop placeholder\r\n\tchosenClass: \"sortable-chosen\",  // Class name for the chosen item\r\n\tdragClass: \"sortable-drag\",  // Class name for the dragging item\r\n\r\n\tswapThreshold: 1, // Threshold of the swap zone\r\n\tinvertSwap: false, // Will always use inverted swap zone if set to true\r\n\tinvertedSwapThreshold: 1, // Threshold of the inverted swap zone (will be set to swapThreshold value by default)\r\n\tdirection: 'horizontal', // Direction of Sortable (will be detected automatically if not given)\r\n\r\n\tforceFallback: false,  // ignore the HTML5 DnD behaviour and force the fallback to kick in\r\n\r\n\tfallbackClass: \"sortable-fallback\",  // Class name for the cloned DOM Element when using forceFallback\r\n\tfallbackOnBody: false,  // Appends the cloned DOM Element into the Document's Body\r\n\tfallbackTolerance: 0, // Specify in pixels how far the mouse should move before it's considered as a drag.\r\n\r\n\tdragoverBubble: false,\r\n\tremoveCloneOnHide: true, // Remove the clone element when it is not showing, rather than just hiding it\r\n\temptyInsertThreshold: 5, // px, distance mouse must be from empty sortable to insert drag element into it\r\n\r\n\r\n\tsetData: function (/** DataTransfer */dataTransfer, /** HTMLElement*/dragEl) {\r\n\t\tdataTransfer.setData('Text', dragEl.textContent); // `dataTransfer` object of HTML5 DragEvent\r\n\t},\r\n\r\n\t// Element is chosen\r\n\tonChoose: function (/**Event*/evt) {\r\n\t\tevt.oldIndex;  // element index within parent\r\n\t},\r\n\r\n\t// Element is unchosen\r\n\tonUnchoose: function(/**Event*/evt) {\r\n\t\t// same properties as onEnd\r\n\t},\r\n\r\n\t// Element dragging started\r\n\tonStart: function (/**Event*/evt) {\r\n\t\tevt.oldIndex;  // element index within parent\r\n\t},\r\n\r\n\t// Element dragging ended\r\n\tonEnd: function (/**Event*/evt) {\r\n\t\tvar itemEl = evt.item;  // dragged HTMLElement\r\n\t\tevt.to;    // target list\r\n\t\tevt.from;  // previous list\r\n\t\tevt.oldIndex;  // element's old index within old parent\r\n\t\tevt.newIndex;  // element's new index within new parent\r\n\t\tevt.oldDraggableIndex; // element's old index within old parent, only counting draggable elements\r\n\t\tevt.newDraggableIndex; // element's new index within new parent, only counting draggable elements\r\n\t\tevt.clone // the clone element\r\n\t\tevt.pullMode;  // when item is in another sortable: `\"clone\"` if cloning, `true` if moving\r\n\t},\r\n\r\n\t// Element is dropped into the list from another list\r\n\tonAdd: function (/**Event*/evt) {\r\n\t\t// same properties as onEnd\r\n\t},\r\n\r\n\t// Changed sorting within list\r\n\tonUpdate: function (/**Event*/evt) {\r\n\t\t// same properties as onEnd\r\n\t},\r\n\r\n\t// Called by any change to the list (add / update / remove)\r\n\tonSort: function (/**Event*/evt) {\r\n\t\t// same properties as onEnd\r\n\t},\r\n\r\n\t// Element is removed from the list into another list\r\n\tonRemove: function (/**Event*/evt) {\r\n\t\t// same properties as onEnd\r\n\t},\r\n\r\n\t// Attempt to drag a filtered element\r\n\tonFilter: function (/**Event*/evt) {\r\n\t\tvar itemEl = evt.item;  // HTMLElement receiving the `mousedown|tapstart` event.\r\n\t},\r\n\r\n\t// Event when you move an item in the list or between lists\r\n\tonMove: function (/**Event*/evt, /**Event*/originalEvent) {\r\n\t\t// Example: https://jsbin.com/nawahef/edit?js,output\r\n\t\tevt.dragged; // dragged HTMLElement\r\n\t\tevt.draggedRect; // DOMRect {left, top, right, bottom}\r\n\t\tevt.related; // HTMLElement on which have guided\r\n\t\tevt.relatedRect; // DOMRect\r\n\t\tevt.willInsertAfter; // Boolean that is true if Sortable will insert drag element after target by default\r\n\t\toriginalEvent.clientY; // mouse position\r\n\t\t// return false; — for cancel\r\n\t\t// return -1; — insert before target\r\n\t\t// return 1; — insert after target\r\n\t\t// return true; — keep default insertion point based on the direction\r\n\t\t// return void; — keep default insertion point based on the direction\r\n\t},\r\n\r\n\t// Called when creating a clone of element\r\n\tonClone: function (/**Event*/evt) {\r\n\t\tvar origEl = evt.item;\r\n\t\tvar cloneEl = evt.clone;\r\n\t},\r\n\r\n\t// Called when dragging element changes position\r\n\tonChange: function(/**Event*/evt) {\r\n\t\tevt.newIndex // most likely why this event is used is to get the dragging element's current index\r\n\t\t// same properties as onEnd\r\n\t}\r\n});\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `group` option\r\nTo drag elements from one list into another, both lists must have the same `group` value.\r\nYou can also define whether lists can give away, give and keep a copy (`clone`), and receive elements.\r\n\r\n * name: `String` — group name\r\n * pull: `true|false|[\"foo\", \"bar\"]|'clone'|function` — ability to move from the list. `clone` — copy the item, rather than move. Or an array of group names which the elements may be put in. Defaults to `true`.\r\n * put: `true|false|[\"baz\", \"qux\"]|function` — whether elements can be added from other lists, or an array of group names from which elements can be added.\r\n * revertClone: `boolean` — revert cloned element to initial position after moving to a another list.\r\n\r\n\r\nDemo:\r\n - https://jsbin.com/hijetos/edit?js,output\r\n - https://jsbin.com/nacoyah/edit?js,output — use of complex logic in the `pull` and` put`\r\n - https://jsbin.com/bifuyab/edit?js,output — use `revertClone: true`\r\n\r\n\r\n---\r\n\r\n\r\n#### `sort` option\r\nAllow sorting inside list.\r\n\r\nDemo: https://jsbin.com/jayedig/edit?js,output\r\n\r\n\r\n---\r\n\r\n\r\n#### `delay` option\r\nTime in milliseconds to define when the sorting should start.\r\nUnfortunately, due to browser restrictions, delaying is not possible on IE or Edge with native drag & drop.\r\n\r\nDemo: https://jsbin.com/zosiwah/edit?js,output\r\n\r\n\r\n---\r\n\r\n\r\n#### `delayOnTouchOnly` option\r\nWhether or not the delay should be applied only if the user is using touch (eg. on a mobile device). No delay will be applied in any other case. Defaults to `false`.\r\n\r\n\r\n---\r\n\r\n\r\n#### `swapThreshold` option\r\nPercentage of the target that the swap zone will take up, as a float between `0` and `1`.\r\n\r\n[Read more](https://github.com/SortableJS/Sortable/wiki/Swap-Thresholds-and-Direction#swap-threshold)\r\n\r\nDemo: http://sortablejs.github.io/Sortable#thresholds\r\n\r\n\r\n---\r\n\r\n\r\n#### `invertSwap` option\r\nSet to `true` to set the swap zone to the sides of the target, for the effect of sorting \"in between\" items.\r\n\r\n[Read more](https://github.com/SortableJS/Sortable/wiki/Swap-Thresholds-and-Direction#forcing-inverted-swap-zone)\r\n\r\nDemo: http://sortablejs.github.io/Sortable#thresholds\r\n\r\n\r\n---\r\n\r\n\r\n#### `invertedSwapThreshold` option\r\nPercentage of the target that the inverted swap zone will take up, as a float between `0` and `1`. If not given, will default to `swapThreshold`.\r\n\r\n[Read more](https://github.com/SortableJS/Sortable/wiki/Swap-Thresholds-and-Direction#dealing-with-swap-glitching)\r\n\r\n\r\n---\r\n\r\n\r\n#### `direction` option\r\nDirection that the Sortable should sort in. Can be set to `'vertical'`, `'horizontal'`, or a function, which will be called whenever a target is dragged over. Must return `'vertical'` or `'horizontal'`.\r\n\r\n[Read more](https://github.com/SortableJS/Sortable/wiki/Swap-Thresholds-and-Direction#direction)\r\n\r\n\r\nExample of direction detection for vertical list that includes full column and half column elements:\r\n\r\n```js\r\nSortable.create(el, {\r\n\tdirection: function(evt, target, dragEl) {\r\n\t\tif (target !== null && target.className.includes('half-column') && dragEl.className.includes('half-column')) {\r\n\t\t\treturn 'horizontal';\r\n\t\t}\r\n\t\treturn 'vertical';\r\n\t}\r\n});\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `touchStartThreshold` option\r\nThis option is similar to `fallbackTolerance` option.\r\n\r\nWhen the `delay` option is set, some phones with very sensitive touch displays like the Samsung Galaxy S8 will fire\r\nunwanted touchmove events even when your finger is not moving, resulting in the sort not triggering.\r\n\r\nThis option sets the minimum pointer movement that must occur before the delayed sorting is cancelled.\r\n\r\nValues between 3 to 5 are good.\r\n\r\n\r\n---\r\n\r\n\r\n#### `disabled` options\r\nDisables the sortable if set to `true`.\r\n\r\nDemo: https://jsbin.com/sewokud/edit?js,output\r\n\r\n```js\r\nvar sortable = Sortable.create(list);\r\n\r\ndocument.getElementById(\"switcher\").onclick = function () {\r\n\tvar state = sortable.option(\"disabled\"); // get\r\n\r\n\tsortable.option(\"disabled\", !state); // set\r\n};\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `handle` option\r\nTo make list items draggable, Sortable disables text selection by the user.\r\nThat's not always desirable. To allow text selection, define a drag handler,\r\nwhich is an area of every list element that allows it to be dragged around.\r\n\r\nDemo: https://jsbin.com/numakuh/edit?html,js,output\r\n\r\n```js\r\nSortable.create(el, {\r\n\thandle: \".my-handle\"\r\n});\r\n```\r\n\r\n```html\r\n<ul>\r\n\t<li><span class=\"my-handle\">::</span> list item text one\r\n\t<li><span class=\"my-handle\">::</span> list item text two\r\n</ul>\r\n```\r\n\r\n```css\r\n.my-handle {\r\n\tcursor: move;\r\n\tcursor: -webkit-grabbing;\r\n}\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `filter` option\r\n\r\n\r\n```js\r\nSortable.create(list, {\r\n\tfilter: \".js-remove, .js-edit\",\r\n\tonFilter: function (evt) {\r\n\t\tvar item = evt.item,\r\n\t\t\tctrl = evt.target;\r\n\r\n\t\tif (Sortable.utils.is(ctrl, \".js-remove\")) {  // Click on remove button\r\n\t\t\titem.parentNode.removeChild(item); // remove sortable item\r\n\t\t}\r\n\t\telse if (Sortable.utils.is(ctrl, \".js-edit\")) {  // Click on edit link\r\n\t\t\t// ...\r\n\t\t}\r\n\t}\r\n})\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `ghostClass` option\r\nClass name for the drop placeholder (default `sortable-ghost`).\r\n\r\nDemo: https://jsbin.com/henuyiw/edit?css,js,output\r\n\r\n```css\r\n.ghost {\r\n  opacity: 0.4;\r\n}\r\n```\r\n\r\n```js\r\nSortable.create(list, {\r\n  ghostClass: \"ghost\"\r\n});\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `chosenClass` option\r\nClass name for the chosen item  (default `sortable-chosen`).\r\n\r\nDemo: https://jsbin.com/hoqufox/edit?css,js,output\r\n\r\n```css\r\n.chosen {\r\n  color: #fff;\r\n  background-color: #c00;\r\n}\r\n```\r\n\r\n```js\r\nSortable.create(list, {\r\n  delay: 500,\r\n  chosenClass: \"chosen\"\r\n});\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n#### `forceFallback` option\r\nIf set to `true`, the Fallback for non HTML5 Browser will be used, even if we are using an HTML5 Browser.\r\nThis gives us the possibility to test the behaviour for older Browsers even in newer Browser, or make the Drag 'n Drop feel more consistent between Desktop , Mobile and old Browsers.\r\n\r\nOn top of that, the Fallback always generates a copy of that DOM Element and appends the class `fallbackClass` defined in the options. This behaviour controls the look of this 'dragged' Element.\r\n\r\nDemo: https://jsbin.com/sibiput/edit?html,css,js,output\r\n\r\n\r\n---\r\n\r\n\r\n#### `fallbackTolerance` option\r\nEmulates the native drag threshold. Specify in pixels how far the mouse should move before it's considered as a drag.\r\nUseful if the items are also clickable like in a list of links.\r\n\r\nWhen the user clicks inside a sortable element, it's not uncommon for your hand to move a little between the time you press and the time you release.\r\nDragging only starts if you move the pointer past a certain tolerance, so that you don't accidentally start dragging every time you click.\r\n\r\n3 to 5 are probably good values.\r\n\r\n\r\n---\r\n\r\n\r\n#### `dragoverBubble` option\r\nIf set to `true`, the dragover event will bubble to parent sortables. Works on both fallback and native dragover event.\r\nBy default, it is false, but Sortable will only stop bubbling the event once the element has been inserted into a parent Sortable, or *can* be inserted into a parent Sortable, but isn't at that specific time (due to animation, etc).\r\n\r\nSince 1.8.0, you will probably want to leave this option as false. Before 1.8.0, it may need to be `true` for nested sortables to work.\r\n\r\n\r\n---\r\n\r\n\r\n#### `removeCloneOnHide` option\r\nIf set to `false`, the clone is hidden by having it's CSS `display` property set to `none`.\r\nBy default, this option is `true`, meaning Sortable will remove the cloned element from the DOM when it is supposed to be hidden.\r\n\r\n\r\n---\r\n\r\n\r\n#### `emptyInsertThreshold` option\r\nThe distance (in pixels) the mouse must be from an empty sortable while dragging for the drag element to be inserted into that sortable. Defaults to `5`. Set to `0` to disable this feature.\r\n\r\nDemo: https://jsbin.com/becavoj/edit?js,output\r\n\r\nAn alternative to this option would be to set a padding on your list when it is empty.\r\n\r\nFor example:\r\n```css\r\nul:empty {\r\n  padding-bottom: 20px;\r\n}\r\n```\r\n\r\nWarning: For `:empty` to work, it must have no node inside (even text one).\r\n\r\nDemo:\r\nhttps://jsbin.com/yunakeg/edit?html,css,js,output\r\n\r\n---\r\n### Event object ([demo](https://jsbin.com/fogujiv/edit?js,output))\r\n\r\n - to:`HTMLElement` — list, in which moved element\r\n - from:`HTMLElement` — previous list\r\n - item:`HTMLElement` — dragged element\r\n - clone:`HTMLElement`\r\n - oldIndex:`Number|undefined` — old index within parent\r\n - newIndex:`Number|undefined` — new index within parent\r\n - oldDraggableIndex: `Number|undefined` — old index within parent, only counting draggable elements\r\n - newDraggableIndex: `Number|undefined` — new index within parent, only counting draggable elements\r\n - pullMode:`String|Boolean|undefined` — Pull mode if dragging into another sortable (`\"clone\"`, `true`, or `false`), otherwise undefined\r\n\r\n\r\n#### `move` event object\r\n - to:`HTMLElement`\r\n - from:`HTMLElement`\r\n - dragged:`HTMLElement`\r\n - draggedRect:`DOMRect`\r\n - related:`HTMLElement` — element on which have guided\r\n - relatedRect:`DOMRect`\r\n - willInsertAfter:`Boolean` — `true` if will element be inserted after target (or `false` if before)\r\n\r\n\r\n---\r\n\r\n\r\n### Methods\r\n\r\n\r\n##### option(name:`String`[, value:`*`]):`*`\r\nGet or set the option.\r\n\r\n\r\n\r\n##### closest(el:`HTMLElement`[, selector:`String`]):`HTMLElement|null`\r\nFor each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\r\n\r\n\r\n##### toArray():`String[]`\r\nSerializes the sortable's item `data-id`'s (`dataIdAttr` option) into an array of string.\r\n\r\n\r\n##### sort(order:`String[]`, useAnimation:`Boolean`)\r\nSorts the elements according to the array.\r\n\r\n```js\r\nvar order = sortable.toArray();\r\nsortable.sort(order.reverse(), true); // apply\r\n```\r\n\r\n\r\n##### save()\r\nSave the current sorting (see [store](#store))\r\n\r\n\r\n##### destroy()\r\nRemoves the sortable functionality completely.\r\n\r\n\r\n---\r\n\r\n\r\n<a name=\"store\"></a>\r\n### Store\r\nSaving and restoring of the sort.\r\n\r\n```html\r\n<ul>\r\n\t<li data-id=\"1\">order</li>\r\n\t<li data-id=\"2\">save</li>\r\n\t<li data-id=\"3\">restore</li>\r\n</ul>\r\n```\r\n\r\n```js\r\nSortable.create(el, {\r\n\tgroup: \"localStorage-example\",\r\n\tstore: {\r\n\t\t/**\r\n\t\t * Get the order of elements. Called once during initialization.\r\n\t\t * @param   {Sortable}  sortable\r\n\t\t * @returns {Array}\r\n\t\t */\r\n\t\tget: function (sortable) {\r\n\t\t\tvar order = localStorage.getItem(sortable.options.group.name);\r\n\t\t\treturn order ? order.split('|') : [];\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * Save the order of elements. Called onEnd (when the item is dropped).\r\n\t\t * @param {Sortable}  sortable\r\n\t\t */\r\n\t\tset: function (sortable) {\r\n\t\t\tvar order = sortable.toArray();\r\n\t\t\tlocalStorage.setItem(sortable.options.group.name, order.join('|'));\r\n\t\t}\r\n\t}\r\n})\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n<a name=\"bs\"></a>\r\n### Bootstrap\r\nDemo: https://jsbin.com/visimub/edit?html,js,output\r\n\r\n```html\r\n<!-- Latest compiled and minified CSS -->\r\n<link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css\"/>\r\n\r\n\r\n<!-- Latest Sortable -->\r\n<script src=\"http://SortableJS.github.io/Sortable/Sortable.js\"></script>\r\n\r\n\r\n<!-- Simple List -->\r\n<ul id=\"simpleList\" class=\"list-group\">\r\n\t<li class=\"list-group-item\">This is <a href=\"http://SortableJS.github.io/Sortable/\">Sortable</a></li>\r\n\t<li class=\"list-group-item\">It works with Bootstrap...</li>\r\n\t<li class=\"list-group-item\">...out of the box.</li>\r\n\t<li class=\"list-group-item\">It has support for touch devices.</li>\r\n\t<li class=\"list-group-item\">Just drag some elements around.</li>\r\n</ul>\r\n\r\n<script>\r\n    // Simple list\r\n    Sortable.create(simpleList, { /* options */ });\r\n</script>\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n### Static methods & properties\r\n\r\n\r\n\r\n##### Sortable.create(el:`HTMLElement`[, options:`Object`]):`Sortable`\r\nCreate new instance.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.active:`Sortable`\r\nThe active Sortable instance.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.dragged:`HTMLElement`\r\nThe element being dragged.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.ghost:`HTMLElement`\r\nThe ghost element.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.clone:`HTMLElement`\r\nThe clone element.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.get(element:`HTMLElement`):`Sortable`\r\nGet the Sortable instance on an element.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.mount(plugin:`...SortablePlugin|SortablePlugin[]`)\r\nMounts a plugin to Sortable.\r\n\r\n\r\n---\r\n\r\n\r\n##### Sortable.utils\r\n* on(el`:HTMLElement`, event`:String`, fn`:Function`) — attach an event handler function\r\n* off(el`:HTMLElement`, event`:String`, fn`:Function`) — remove an event handler\r\n* css(el`:HTMLElement`)`:Object` — get the values of all the CSS properties\r\n* css(el`:HTMLElement`, prop`:String`)`:Mixed` — get the value of style properties\r\n* css(el`:HTMLElement`, prop`:String`, value`:String`) — set one CSS properties\r\n* css(el`:HTMLElement`, props`:Object`) — set more CSS properties\r\n* find(ctx`:HTMLElement`, tagName`:String`[, iterator`:Function`])`:Array` — get elements by tag name\r\n* bind(ctx`:Mixed`, fn`:Function`)`:Function` — Takes a function and returns a new one that will always have a particular context\r\n* is(el`:HTMLElement`, selector`:String`)`:Boolean` — check the current matched set of elements against a selector\r\n* closest(el`:HTMLElement`, selector`:String`[, ctx`:HTMLElement`])`:HTMLElement|Null` — for each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree\r\n* clone(el`:HTMLElement`)`:HTMLElement` — create a deep copy of the set of matched elements\r\n* toggleClass(el`:HTMLElement`, name`:String`, state`:Boolean`) — add or remove one classes from each element\r\n* detectDirection(el`:HTMLElement`)`:String` — automatically detect the [direction](https://github.com/SortableJS/Sortable/wiki/Swap-Thresholds-and-Direction#direction) of the element as either `'vertical'` or `'horizontal'`\r\n* index(el`:HTMLElement`, selector`:String`)`:Number` — index of the element within its parent for a selected set of elements\r\n* getChild(el`:HTMLElement`, childNum`:Number`, options`:Object`, includeDragEl`:Boolean`):`HTMLElement` — get the draggable element at a given index of draggable elements within a Sortable instance\r\n* expando`:String` — expando property name for internal use, sortableListElement[expando] returns the Sortable instance of that elemenet\r\n---\r\n\r\n\r\n### Plugins\r\n#### Extra Plugins (included in complete versions)\r\n - [MultiDrag](https://github.com/SortableJS/Sortable/tree/master/plugins/MultiDrag)\r\n - [Swap](https://github.com/SortableJS/Sortable/tree/master/plugins/Swap)\r\n\r\n#### Default Plugins (included in default versions)\r\n - [AutoScroll](https://github.com/SortableJS/Sortable/tree/master/plugins/AutoScroll)\r\n - [OnSpill](https://github.com/SortableJS/Sortable/tree/master/plugins/OnSpill)\r\n\r\n\r\n---\r\n\r\n\r\n<a name=\"cdn\"></a>\r\n### CDN\r\n\r\n```html\r\n<!-- jsDelivr :: Sortable :: Latest (https://www.jsdelivr.com/package/npm/sortablejs) -->\r\n<script src=\"https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js\"></script>\r\n```\r\n\r\n\r\n---\r\n\r\n\r\n### Contributing (Issue/PR)\r\n\r\nPlease, [read this](CONTRIBUTING.md).\r\n\r\n\r\n---\r\n\r\n\r\n## Contributors\r\n\r\n### Code Contributors\r\n\r\nThis project exists thanks to all the people who contribute. [[Contribute](CONTRIBUTING.md)].\r\n<a href=\"https://github.com/SortableJS/Sortable/graphs/contributors\"><img src=\"https://opencollective.com/Sortable/contributors.svg?width=890&button=false\" /></a>\r\n\r\n### Financial Contributors\r\n\r\nBecome a financial contributor and help us sustain our community. [[Contribute](https://opencollective.com/Sortable/contribute)]\r\n\r\n#### Individuals\r\n\r\n<a href=\"https://opencollective.com/Sortable\"><img src=\"https://opencollective.com/Sortable/individuals.svg?width=890\"></a>\r\n\r\n#### Organizations\r\n\r\nSupport this project with your organization. Your logo will show up here with a link to your website. [[Contribute](https://opencollective.com/Sortable/contribute)]\r\n\r\n<a href=\"https://opencollective.com/Sortable/organization/0/website\"><img src=\"https://opencollective.com/Sortable/organization/0/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/1/website\"><img src=\"https://opencollective.com/Sortable/organization/1/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/2/website\"><img src=\"https://opencollective.com/Sortable/organization/2/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/3/website\"><img src=\"https://opencollective.com/Sortable/organization/3/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/4/website\"><img src=\"https://opencollective.com/Sortable/organization/4/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/5/website\"><img src=\"https://opencollective.com/Sortable/organization/5/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/6/website\"><img src=\"https://opencollective.com/Sortable/organization/6/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/7/website\"><img src=\"https://opencollective.com/Sortable/organization/7/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/8/website\"><img src=\"https://opencollective.com/Sortable/organization/8/avatar.svg\"></a>\r\n<a href=\"https://opencollective.com/Sortable/organization/9/website\"><img src=\"https://opencollective.com/Sortable/organization/9/avatar.svg\"></a>\r\n\r\n## MIT LICENSE\r\nPermission is hereby granted, free of charge, to any person obtaining\r\na copy of this software and associated documentation files (the\r\n\"Software\"), to deal in the Software without restriction, including\r\nwithout limitation the rights to use, copy, modify, merge, publish,\r\ndistribute, sublicense, and/or sell copies of the Software, and to\r\npermit persons to whom the Software is furnished to do so, subject to\r\nthe following conditions:\r\n\r\nThe above copyright notice and this permission notice shall be\r\nincluded in all copies or substantial portions of the Software.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\r\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\r\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\r\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\r\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\r\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\r\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\r\n", "time": {"created": "2022-01-26T14:53:07.977Z", "modified": "2025-07-14T05:56:00.640Z", "1.14.0": "2021-07-04T18:35:08.598Z", "1.13.0": "2021-01-08T02:18:16.409Z", "1.12.0": "2020-09-21T03:29:58.064Z", "1.11.2-alpha.4": "2020-09-18T14:36:55.727Z", "1.11.2-alpha.3": "2020-09-14T23:16:57.381Z", "1.11.2-alpha.2": "2020-09-14T23:07:25.117Z", "1.10.2": "2019-12-25T19:43:41.553Z", "1.10.1": "2019-10-01T21:48:42.462Z", "1.10.0": "2019-09-15T18:55:02.543Z", "1.10.0-rc3": "2019-06-27T00:54:43.240Z", "1.10.0-rc2": "2019-06-07T01:57:08.071Z", "1.10.0-rc1": "2019-06-06T01:07:36.825Z", "1.9.0": "2019-04-20T01:44:19.559Z", "1.8.4": "2019-03-11T19:53:38.589Z", "1.8.3": "2019-02-11T22:47:28.797Z", "1.8.2": "2019-02-10T20:57:29.598Z", "1.8.1": "2019-01-16T00:27:07.562Z", "1.8.0": "2019-01-15T01:56:32.485Z", "1.8.0-rc1": "2018-12-15T22:39:47.044Z", "1.7.0": "2017-11-06T11:58:06.020Z", "1.6.1": "2017-08-28T07:35:41.038Z", "1.6.0": "2017-06-02T09:19:32.397Z", "1.5.1": "2017-02-24T06:43:32.225Z", "1.5.0": "2017-02-19T18:11:30.481Z", "1.5.0-rc1": "2016-11-14T19:26:38.117Z", "1.4.2": "2015-10-21T08:53:48.330Z", "1.4.1": "2015-10-14T18:38:52.553Z", "1.4.0": "2015-10-13T07:47:31.789Z", "1.3.0": "2015-09-22T19:29:36.871Z", "1.2.1": "2015-09-01T06:54:05.093Z", "1.2.0": "2015-05-30T10:53:50.088Z", "1.1.1": "2015-02-20T05:56:08.295Z", "1.1.0": "2015-02-13T19:20:02.933Z", "1.0.1": "2015-01-26T19:11:53.160Z", "1.0.0": "2014-12-22T06:06:42.727Z", "0.7.2": "2014-12-08T06:53:05.484Z", "0.7.1": "2014-12-05T11:59:36.201Z", "0.7.0": "2014-12-03T20:08:02.739Z", "1.15.0": "2022-03-20T16:30:34.826Z", "1.15.1": "2023-11-30T22:07:22.872Z", "1.15.2": "2024-01-14T00:27:49.728Z", "1.15.3": "2024-09-01T22:55:30.201Z", "1.15.4": "2024-11-24T10:31:41.749Z", "1.15.5": "2024-11-27T23:13:54.713Z", "1.15.6": "2024-11-28T18:21:49.918Z"}, "versions": {"1.14.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.14.0", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "NODE_ENV=umd rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "4684991aabfdf5cab989427fe204814d99520509", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.14.0", "_nodeVersion": "16.3.0", "_npmVersion": "7.6.0", "dist": {"shasum": "6d2e17ccbdb25f464734df621d4f35d4ab35b3d8", "size": 133703, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.14.0.tgz", "integrity": "sha512-pBXvQCs5/33fdN1/39pPL0NZF20LeRbLQ5jtnheIPN9JQAaufGjKdWduZn4U7wCtVuzKhmRkI0DFYHYRbB2H1w=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.14.0_1625423708435_0.7124649901697102"}, "_hasShrinkwrap": false, "publish_time": 1625423708598, "_cnpm_publish_time": 1625423708598, "_cnpmcore_publish_time": "2021-12-15T17:41:31.197Z"}, "1.13.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.13.0", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "NODE_ENV=umd rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "8a987c007cff0535edf65cd19a1a0a70cd880a07", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.13.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.14.10", "dist": {"shasum": "3ab2473f8c69ca63569e80b1cd1b5669b51269e9", "size": 131242, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.13.0.tgz", "integrity": "sha512-RBJirPY0spWCrU5yCmWM1eFs/XgX2J5c6b275/YyxFRgnzPhKl/TDeU2hNR8Dt7ITq66NRPM4UlOt+e5O4CFHg=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.13.0_1610072296244_0.40777058852138737"}, "_hasShrinkwrap": false, "publish_time": 1610072296409, "_cnpm_publish_time": 1610072296409, "_cnpmcore_publish_time": "2021-12-15T17:41:31.930Z"}, "1.12.0": {"name": "sortablejs", "version": "1.12.0", "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices", "main": "dist/sortable.umd.js", "module": "modular/sortable.complete.esm.js", "scripts": {"build": "npm-run-all --parallel build:*", "build:umd": "microbundle -f umd -i src/umd.ts -o dist/sortable.umd.js --name Sortable", "build:modular-core": "microbundle -f esm -i src/modular-core.ts -o modular/sortable.core.esm.js", "build:modular-default": "microbundle -f esm -i src/modular-default.ts -o modular/sortable.esm.js", "build:modular-complete": "microbundle -f esm -i src/modular-complete.ts -o modular/sortable.complete.esm.js", "prepublishOnly": "yarn build"}, "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "devDependencies": {"colorette": "^1.2.1", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "typescript": "^3.9.7"}, "gitHead": "e4dc8b0fb9fa5af4ea0864a7023e55c0b026f16f", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.12.0", "_nodeVersion": "14.10.1", "_npmVersion": "lerna/3.22.1/node@v14.10.1+x64 (linux)", "dist": {"shasum": "ee6d7ece3598c2af0feb1559d98595e5ea37cbd6", "size": 258800, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.12.0.tgz", "integrity": "sha512-bPn57rCjBRlt2sC24RBsu40wZsmLkSo2XeqG8k6DC1zru5eObQUIPPZAQG7W2SJ8FZQYq+BEJmvuw1Zxb3chqg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "wayne<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.12.0_1600658997886_0.4384934642351106"}, "_hasShrinkwrap": false, "publish_time": 1600658998064, "deprecated": "This version has deprecated in favor of a rollback to version 1.10.2", "_cnpm_publish_time": 1600658998064, "_cnpmcore_publish_time": "2021-12-15T17:41:32.834Z"}, "1.11.2-alpha.4": {"name": "sortablejs", "version": "1.11.2-alpha.4", "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices", "main": "dist/sortable.js", "browser": "dist/sortable.umd.js", "module": "dist/sortable.module.js", "scripts": {"build": "npm-run-all --parallel build:*", "build:main": "microbundle -f cjs -i src/default-named.ts -o dist/sortable.js", "build:umd": "microbundle -f umd -i src/complete-umd.ts -o dist/sortable.umd.js --name Sortable", "build:modular-core": "microbundle -f esm -i src/core-named.ts -o modular/sortable.core.esm.js", "build:modular-default": "microbundle -f esm -i src/default-named.ts -o modular/sortable.esm.js", "build:modular-complete": "microbundle -f esm -i src/complete-named.ts -o modular/sortable.complete.esm.js", "prepublishOnly": "yarn build"}, "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "devDependencies": {"colorette": "^1.2.1", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "typescript": "^3.9.7"}, "gitHead": "b3d470c40f770c11c67a04a4615924a7e973f47e", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.11.2-alpha.4", "_nodeVersion": "14.10.1", "_npmVersion": "lerna/3.22.1/node@v14.10.1+x64 (linux)", "dist": {"shasum": "5ad3f6bdc963dc69b18fdd22904de23129962acb", "size": 322355, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.11.2-alpha.4.tgz", "integrity": "sha512-96jThPR99NBLk8FDHA++/diZYn15vZ0GLoMBzr4zsUHGGxKNuc3/NhyiSEhr9hXgVvP0i5ZitcT6IzybPtTuzQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "wayne<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.11.2-alpha.4_1600439815575_0.4809144794561442"}, "_hasShrinkwrap": false, "publish_time": 1600439815727, "deprecated": "This version has deprecated in favor of a rollback to version 1.10.2", "_cnpm_publish_time": 1600439815727, "_cnpmcore_publish_time": "2021-12-15T17:41:34.256Z"}, "1.11.2-alpha.3": {"name": "sortablejs", "version": "1.11.2-alpha.3", "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices", "main": "dist/sortable.js", "browser": "dist/sortable.umd.js", "module": "dist/sortable.module.js", "scripts": {"build": "npm-run-all --parallel build:*", "build:umd": "microbundle -f umd -i src/complete-umd.ts -o dist/sortable.umd.js --name Sortable", "build:modular-core": "microbundle -f esm -i src/core-named.ts -o modular/sortable.core.esm.js", "build:modular-default": "microbundle -f esm -i src/default-named.ts -o modular/sortable.esm.js", "build:modular-complete": "microbundle -f esm -i src/complete-named.ts -o modular/sortable.complete.esm.js", "prepublishOnly": "yarn build"}, "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "devDependencies": {"colorette": "^1.2.1", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "typescript": "^3.9.7"}, "gitHead": "a798fcff3aeb6d6d8ae818168a073f75ce1e7ef4", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.11.2-alpha.3", "_nodeVersion": "14.10.1", "_npmVersion": "lerna/3.22.1/node@v14.10.1+x64 (linux)", "dist": {"shasum": "b61c59c8793c8762532f03c5c84d88d71f8e042c", "size": 258831, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.11.2-alpha.3.tgz", "integrity": "sha512-sX/cttekU7IQDvUGNsRIKDB1MXhd0OfR2OOsluusrZHIPwAKS8OSeGlNXONjcd3vsc3o6t8VakT0KpwnKMm6PQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "wayne<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.11.2-alpha.3_1600125417193_0.3149380812415892"}, "_hasShrinkwrap": false, "publish_time": 1600125417381, "deprecated": "This version has deprecated in favor of a rollback to version 1.10.2", "_cnpm_publish_time": 1600125417381, "_cnpmcore_publish_time": "2021-12-15T17:41:35.073Z"}, "1.11.2-alpha.2": {"name": "sortablejs", "version": "1.11.2-alpha.2", "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices", "main": "dist/sortable.js", "browser": "dist/sortable.umd.js", "module": "dist/sortable.module.js", "scripts": {"build": "npm-run-all --parallel build:*", "build:umd": "microbundle -f umd -i src/complete-umd.ts -o dist/sortable.umd.js --name Sortable", "build:modular-core": "microbundle -f esm -i src/core-named.ts -o modular/sortable.core.esm.js", "build:modular-default": "microbundle -f esm -i src/default-named.ts -o modular/sortable.esm.js", "build:modular-complete": "microbundle -f esm -i src/complete-named.ts -o modular/sortable.complete.esm.js", "prepublishOnly": "yarn build"}, "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "devDependencies": {"colorette": "^1.2.1", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "typescript": "^3.9.7"}, "gitHead": "6cd93be2e4ffc520375f12b0069915834318f2d1", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.11.2-alpha.2", "_nodeVersion": "14.10.1", "_npmVersion": "lerna/3.22.1/node@v14.10.1+x64 (linux)", "dist": {"shasum": "1eb3888b490cf4e2aa4548dc13c1cf3bea17a83e", "size": 66384, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.11.2-alpha.2.tgz", "integrity": "sha512-0Z0bUNaDSWc02bGbl74ruT0JeVb/Q+8jUovgnOSO4zmQenX84YaIzSDRXBc5EEkDwpDblq86A9z8FxptN7A/Zw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "wayne<PERSON><PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.11.2-alpha.2_1600124844975_0.2656835686000487"}, "_hasShrinkwrap": false, "publish_time": 1600124845117, "deprecated": "This version has deprecated in favor of a rollback to version 1.10.2", "_cnpm_publish_time": 1600124845117, "_cnpmcore_publish_time": "2021-12-15T17:41:35.326Z"}, "1.10.2": {"name": "sortablejs", "exportName": "Sortable", "version": "1.10.2", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "NODE_ENV=umd rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "2addddd67387b6e4b6b5e51806eb698f0a3eee88", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.10.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.13.2", "dist": {"shasum": "6e40364d913f98b85a14f6678f92b5c1221f5290", "size": 130544, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.2.tgz", "integrity": "sha512-YkPGufevysvfwn5rfdlGyrGjt7/CRHwvRPogD/lC+TnvcN29jDpCifKP+rBqf+LRldfXSTh+0CGLcSg0VIxq3A=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.10.2_1577303021422_0.16251767245149829"}, "_hasShrinkwrap": false, "publish_time": 1577303021553, "_cnpm_publish_time": 1577303021553, "_cnpmcore_publish_time": "2021-12-15T17:41:36.041Z"}, "1.10.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.10.1", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "NODE_ENV=umd rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "8d7cb8840cc90cd5026c467a5a9c392ba9be547c", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.10.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.1", "dist": {"shasum": "3d52b00f871be00f00f84d99a60d120bf3dfe52c", "size": 129894, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.1.tgz", "integrity": "sha512-N6r7GrVmO8RW1rn0cTdvK3JR0BcqecAJ0PmYMCL3ZuqTH3pY+9QyqkmJSkkLyyDvd+AJnwaxTP22Ybr/83V9hQ=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.10.1_1569966522294_0.8633336637568805"}, "_hasShrinkwrap": false, "publish_time": 1569966522462, "_cnpm_publish_time": 1569966522462, "_cnpmcore_publish_time": "2021-12-15T17:41:36.690Z"}, "1.10.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.10.0", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "NODE_ENV=umd rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "82ce396042600f03e3cc63dd6f12a4966e417cad", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.10.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.1", "dist": {"shasum": "0ebc054acff2486569194a2f975b2b145dd5e7d6", "size": 129904, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.0.tgz", "integrity": "sha512-+e0YakK1BxgEZpf9l9UiFaiQ8ZOBn1p/4qkkXr8QDVmYyCrUDTyDRRGm0AgW4E4cD0wtgxJ6yzIRkSPUwqhuhg=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.10.0_1568573702410_0.541857966416236"}, "_hasShrinkwrap": false, "publish_time": 1568573702543, "_cnpm_publish_time": 1568573702543, "_cnpmcore_publish_time": "2021-12-15T17:41:37.888Z"}, "1.10.0-rc3": {"name": "sortablejs", "exportName": "Sortable", "version": "1.10.0-rc3", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c build/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c build/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c build/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c build/esm-build.js", "minify": "node build/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "e1d3de84df1676fc5f1cb0dce84631475861a78f", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.10.0-rc3", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "2fe63463a38b5cd12ec914fc3e03583048496f42", "size": 127519, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.0-rc3.tgz", "integrity": "sha512-uw8vZqwI3nkIeAqdrP6N/GDxFW3dY7yz3/rK0GLLoe8aJ2RZALmo6mAwOi+uA7RYuqfz2lm7AACr4ms6gXcb6w=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.10.0-rc3_1561596882915_0.800501849224055"}, "_hasShrinkwrap": false, "publish_time": 1561596883240, "_cnpm_publish_time": 1561596883240, "_cnpmcore_publish_time": "2021-12-15T17:41:38.436Z"}, "1.10.0-rc2": {"name": "sortablejs", "exportName": "Sortable", "version": "1.10.0-rc2", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c build/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c build/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c build/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c build/esm-build.js", "minify": "node build/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "28a2444ca130b54b8a3b2ba5f5794d189e500fbe", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.10.0-rc2", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "a3d4e55b1ddfbc28ca36bc06ee2765d8518f2f6f", "size": 127584, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.0-rc2.tgz", "integrity": "sha512-162MzYM7acfIAwWh3GpoLwEiB3dVChljDRNOcIlzLsIS2MOwgzv0iifiGuSG/M8AzqrkfubrX93uxm/x0SrWxw=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.10.0-rc2_1559872627886_0.4206398223020771"}, "_hasShrinkwrap": false, "publish_time": 1559872628071, "_cnpm_publish_time": 1559872628071, "_cnpmcore_publish_time": "2021-12-15T17:41:39.105Z"}, "1.10.0-rc1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.10.0-rc1", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c build/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c build/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c build/esm-build.js", "minify": "node build/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "5c0a7eccd72b5e7e9967332bad0f4b24112a1bac", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.10.0-rc1", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "4c173acfe443d53f8233b224c09e1fd743c2c109", "size": 127608, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.10.0-rc1.tgz", "integrity": "sha512-YciPioKaTdNuXYOqj8YHUOfS5JIKUpyBxf6DYhc+bt9wIQCOlMEOafQ/5P7tD3xYyQ+DuDrCx2+JsNedh/lPSg=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.10.0-rc1_1559783256626_0.4778455252474474"}, "_hasShrinkwrap": false, "publish_time": 1559783256825, "_cnpm_publish_time": 1559783256825, "_cnpmcore_publish_time": "2021-12-15T17:41:39.814Z"}, "1.9.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.9.0", "devDependencies": {"gulp": "^4.0.0", "gulp-each": "^0.5.0", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "http-server": "^0.9.0", "pump": "^3.0.0"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"http-server": "http-server -s ./", "prepublish": "gulp build"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "2f00519da6918876433df81c548d87682477022c", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.9.0", "_nodeVersion": "9.6.1", "_npmVersion": "6.9.0", "dist": {"shasum": "2d1e74ae6bac2cb4ad0622908f340848969eb88d", "size": 35021, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.9.0.tgz", "integrity": "sha512-Ot6bYJ6PoqPmpsqQYXjn1+RKrY2NWQvQt/o4jfd/UYwVWndyO5EPO8YHbnm5HIykf8ENsm4JUrdAvolPT86yYA=="}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.9.0_1555724659061_0.9813537127019791"}, "_hasShrinkwrap": false, "publish_time": 1555724659559, "_cnpm_publish_time": 1555724659559, "_cnpmcore_publish_time": "2021-12-15T17:41:40.105Z"}, "1.8.4": {"name": "sortablejs", "exportName": "Sortable", "version": "1.8.4", "devDependencies": {"gulp": "^4.0.0", "gulp-each": "^0.5.0", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "http-server": "^0.9.0", "pump": "^3.0.0"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"http-server": "http-server -s ./", "prepublish": "gulp build"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "971f76a2742343312f115aba5316def05e83a0a3", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.8.4", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "dist": {"shasum": "1bbfeafa96d399b83f28e25d8e49d4fbfd867f30", "size": 35065, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.8.4.tgz", "integrity": "sha512-Brqnzelu1AhFuc0Fn3N/qFex1tlIiuQIUsfu2J8luJ4cRgXYkWrByxa+y5mWEBlj8A0YoABukflIJwvHyrwJ6Q=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.8.4_1552334018457_0.42910520424193255"}, "_hasShrinkwrap": false, "publish_time": 1552334018589, "_cnpm_publish_time": 1552334018589, "_cnpmcore_publish_time": "2021-12-15T17:41:40.355Z"}, "1.8.3": {"name": "sortablejs", "exportName": "Sortable", "version": "1.8.3", "devDependencies": {"gulp": "^4.0.0", "gulp-each": "^0.5.0", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "http-server": "^0.9.0", "pump": "^3.0.0"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"http-server": "http-server -s ./", "prepublish": "gulp build"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "047f94e2c4af73ed1c68c6cb162151db58ed20de", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.8.3", "_nodeVersion": "9.6.1", "_npmVersion": "6.7.0", "dist": {"shasum": "5ae908ef96300966e95440a143340f5dd565a0df", "size": 33335, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.8.3.tgz", "integrity": "sha512-AftvD4hdKcR5QlGi7L/JST506zGNGrysE8/QohDpwKXJarHWqCt+TUlrtoMk/wkECB607Q019/OZlJViyWiD6A=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.8.3_1549925248601_0.3434635070352443"}, "_hasShrinkwrap": false, "publish_time": 1549925248797, "_cnpm_publish_time": 1549925248797, "_cnpmcore_publish_time": "2021-12-15T17:41:40.731Z"}, "1.8.2": {"name": "sortablejs", "exportName": "Sortable", "version": "1.8.2", "devDependencies": {"gulp": "^4.0.0", "gulp-each": "^0.5.0", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "http-server": "^0.9.0", "pump": "^3.0.0"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"http-server": "http-server -s ./", "prepublish": "gulp build"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "890f378e68d152b03bbc9e8444e0fe99e2fe273d", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.8.2", "_nodeVersion": "9.6.1", "_npmVersion": "6.7.0", "dist": {"shasum": "5ffcfdaf7307b21c0770586186d1938c6c1a028a", "size": 33335, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.8.2.tgz", "integrity": "sha512-Opa3q1nVMBM/XVrh9pCDh/BJFYCNuoFLLzT4FOVzgXocGrb4QpC/u0E4WVpUoIfhUfMSvP6c5SA5nK22vPqbFA=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.8.2_1549832249471_0.36413902492986927"}, "_hasShrinkwrap": false, "publish_time": 1549832249598, "_cnpm_publish_time": 1549832249598, "_cnpmcore_publish_time": "2021-12-15T17:41:41.530Z"}, "1.8.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.8.1", "devDependencies": {"gulp": "^4.0.0", "gulp-each": "^0.5.0", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "http-server": "^0.9.0", "pump": "^3.0.0"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"http-server": "http-server -s ./", "prepublish": "gulp build"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "10e4a0bc7fe1ab0df7a16becd37e649036addd28", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.8.1", "_npmVersion": "6.5.0", "_nodeVersion": "9.6.1", "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "dist": {"shasum": "d19b66cf10aab9521a565b79766f2f3291c18278", "size": 33094, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.8.1.tgz", "integrity": "sha512-r0bKxZYGUFgv/6DQSWW8TVJBBtupUn6TPnDABd4rlqNq+u94ct5+barZmYauPAQNCKwy56C1kg9Y9msAN7UPag=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.8.1_1547598427449_0.19187258107064808"}, "_hasShrinkwrap": false, "publish_time": 1547598427562, "_cnpm_publish_time": 1547598427562, "_cnpmcore_publish_time": "2021-12-15T17:41:42.177Z"}, "1.8.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.8.0", "devDependencies": {"gulp": "^4.0.0", "gulp-each": "^0.5.0", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "http-server": "^0.9.0", "pump": "^3.0.0"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"http-server": "http-server -s ./", "prepublish": "gulp build"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "629872b938cd5ea39b27f20ce0fb5fca13741cc2", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.8.0", "_npmVersion": "6.5.0", "_nodeVersion": "9.6.1", "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "dist": {"shasum": "df6edea68ab4e8ff851753f3fdf24f09feb52ca2", "size": 33261, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.8.0.tgz", "integrity": "sha512-m381wr79JDqyv3BTXopBd5UKkPGNZUGAgmhLsRV3m3F3nc4wJXaprHiZaLWhMiw0wu4kV9JjIsXQOovCFStVlg=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.8.0_1547517392359_0.7171730536642402"}, "_hasShrinkwrap": false, "publish_time": 1547517392485, "_cnpm_publish_time": 1547517392485, "_cnpmcore_publish_time": "2021-12-15T17:41:42.449Z"}, "1.8.0-rc1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.8.0-rc1", "devDependencies": {"grunt": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*", "grunt-testcafe": "^0.15.0", "grunt-version": "*", "http-server": "^0.9.0", "testcafe": "^0.16.0"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "./node_modules/grunt/bin/grunt", "http-server": "http-server -s ./"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "6e99cb3c198585e499a436061d19914754a276a3", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.8.0-rc1", "_npmVersion": "6.5.0", "_nodeVersion": "9.6.1", "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "dist": {"shasum": "fe4731b2cb3ccb4730a26a8cdde2f74a634bb259", "size": 27945, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.8.0-rc1.tgz", "integrity": "sha512-umyNbQVDwRgc0SZvUB+FRUIUqACnu5vCCmK0zv/xWA3eDSOh+IZsg3GHdWvEOcUBwnykqyk760+YPgVa8HfxFg=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.8.0-rc1_1544913586882_0.4184248738907268"}, "_hasShrinkwrap": false, "publish_time": 1544913587044, "_cnpm_publish_time": 1544913587044, "_cnpmcore_publish_time": "2021-12-15T17:41:42.737Z"}, "1.7.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.7.0", "devDependencies": {"grunt": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*", "grunt-testcafe": "^0.15.0", "grunt-version": "*", "http-server": "^0.9.0", "testcafe": "^0.16.0"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "./node_modules/grunt/bin/grunt", "prepublish": "./node_modules/grunt/bin/grunt", "http-server": "http-server -s ./"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "files": ["Sortable.js", "Sortable.min.js"], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "64e4491ce87675a20b3f58d90bf8a97b96572229", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.7.0", "_shasum": "80a2b2370abd568e1cec8c271131ef30a904fa28", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "80a2b2370abd568e1cec8c271131ef30a904fa28", "size": 22396, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.7.0.tgz", "integrity": "sha512-4z/P2iyY/BElEvKALqpng7wlgdP9pww+r7i7/uUXwX2pDHGLcKMsXsl2NDgZnFinrV4kOLHKLfx89LeCqSxHkQ=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs-1.7.0.tgz_1509969484992_0.32195718213915825"}, "directories": {}, "publish_time": 1509969486020, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509969486020, "_cnpmcore_publish_time": "2021-12-15T17:41:43.106Z"}, "1.6.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.6.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "./node_modules/grunt/bin/grunt", "prepublish": "./node_modules/grunt/bin/grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "files": ["Sortable.js", "Sortable.min.js"], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "b818a9639de5e4ad6639a486ff4eb848be9a2f06", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.6.1", "_shasum": "d120d103fbb9f60c7db27814a1384072e6c6e083", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "d120d103fbb9f60c7db27814a1384072e6c6e083", "size": 21531, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.6.1.tgz", "integrity": "sha512-uBW46rDJZ8213V5dyzMlaEwsjm0EknCug1JG/Nefp+ktUha/3WZaACEXLEaOVqJBiiU6Q4Y1l1R9IQiSEDQNCw=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs-1.6.1.tgz_1503905740024_0.3772503458894789"}, "directories": {}, "publish_time": 1503905741038, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503905741038, "_cnpmcore_publish_time": "2021-12-15T17:41:43.435Z"}, "1.6.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.6.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "./node_modules/grunt/bin/grunt", "prepublish": "./node_modules/grunt/bin/grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "files": ["Sortable.js", "Sortable.min.js"], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "91edeac12b9f1d84c0868d19d7e762f27f69e846", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.6.0", "_shasum": "097cc19267218af2ca1cd455ed858f380136ab68", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "097cc19267218af2ca1cd455ed858f380136ab68", "size": 21502, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.6.0.tgz", "integrity": "sha512-g26kClbzGYGC1dXlkCgxNefcyD1Qkej3/ZUx7iQo/Vbdh/vfirDMVM2qUfWf+8MoqYsiU4LcoSQqB77Q9IN72w=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs-1.6.0.tgz_1496395171255_0.22604197706095874"}, "directories": {}, "publish_time": 1496395172397, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496395172397, "_cnpmcore_publish_time": "2021-12-15T17:41:43.708Z"}, "1.5.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.5.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "./node_modules/grunt/bin/grunt", "prepublish": "./node_modules/grunt/bin/grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "files": ["Sortable.js", "Sortable.min.js"], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "473bd8fecfd2f2834e4187fb033dfa6912eb3b98", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.5.1", "_shasum": "efae13b84c677f3b3d3f4f8e46c7e05fc6acb525", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "efae13b84c677f3b3d3f4f8e46c7e05fc6acb525", "size": 21342, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.5.1.tgz", "integrity": "sha512-dmEjElWUUuI05Mgr+yEFhndBcM9wRNc0aL1dC69aGEk45Pzy6LtF3+J6z/kn78l9bpkaztzWnGEihya3Qm/8zQ=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/sortablejs-1.5.1.tgz_1487918611605_0.8055508281104267"}, "directories": {}, "publish_time": 1487918612225, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487918612225, "_cnpmcore_publish_time": "2021-12-15T17:41:44.029Z"}, "1.5.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.5.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "./node_modules/grunt/bin/grunt", "prepublish": "./node_modules/grunt/bin/grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "files": ["Sortable.js", "Sortable.min.js"], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "3a99522d4bb5499a696a7e791a235235faea6b9a", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.5.0", "_shasum": "7f17e1ffed61a77803ead20922d45fa50cc2a442", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "7f17e1ffed61a77803ead20922d45fa50cc2a442", "size": 21312, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.5.0.tgz", "integrity": "sha512-Y88RMm4ZrkaIgonxjwYsDsiwcFBsBDnkTu2oWOQ7YArqvCktoM42x7bEi482HgQ95MDCIaKQDGpPrh5zw7mGUw=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sortablejs-1.5.0.tgz_1487527888521_0.7268973065074533"}, "directories": {}, "publish_time": 1487527890481, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487527890481, "_cnpmcore_publish_time": "2021-12-15T17:41:44.307Z"}, "1.5.0-rc1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.5.0-rc1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "8e48c2aa1e4f23b7f061caa3a6a276216fff0b67", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.5.0-rc1", "_shasum": "c3407e9a6fc4a54b4d0804926a2e2993544d91b3", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "c3407e9a6fc4a54b4d0804926a2e2993544d91b3", "size": 71711, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.5.0-rc1.tgz", "integrity": "sha512-b56F9akCezdHFnIPK1L2Kd4uxQTqjsBWUCYzlci/m757C6TjucAZqcVlxCFqlKXz8bXxQe+kqTh5yKd9pPuu1Q=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sortablejs-1.5.0-rc1.tgz_1479151595948_0.02265141112729907"}, "directories": {}, "publish_time": 1479151598117, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479151598117, "_cnpmcore_publish_time": "2021-12-15T17:41:44.631Z"}, "1.4.2": {"name": "sortablejs", "exportName": "Sortable", "version": "1.4.2", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "51337f72792525e438680695e223b327b1648e0a", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.4.2", "_shasum": "10df66054e1d1dcb538d7eaedaeaf692232c8d0d", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "10df66054e1d1dcb538d7eaedaeaf692232c8d0d", "size": 90561, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.4.2.tgz", "integrity": "sha512-t6bhlC48KKAvWDCxvQQkQe4u5iE5zFZecxEd5vA6vTaNseXjMmYKPB6Tu+0prfwPH8Hnq9xvsw5ovdR5X4BO1w=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445417628330, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445417628330, "_cnpmcore_publish_time": "2021-12-15T17:41:45.119Z"}, "1.4.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.4.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "40a2f605459687e0ee20e4d121006bca5b24fcab", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.4.1", "_shasum": "68ea791c37cff52b7a232e5181edf93744c3eac4", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "4.1.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "dist": {"shasum": "68ea791c37cff52b7a232e5181edf93744c3eac4", "size": 90586, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.4.1.tgz", "integrity": "sha512-kBp4MD83/IRYwX+OZbobvjL8MPca+mQ4e5e/VAGI2ftLxhptKZHZk9Tch0SOHl68lQC6gKHW7Srga5xtDLNKGg=="}, "directories": {}, "publish_time": 1444847932553, "_hasShrinkwrap": false, "_cnpm_publish_time": 1444847932553, "_cnpmcore_publish_time": "2021-12-15T17:41:45.585Z"}, "1.4.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.4.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "a793a5e3951a0812a7241aab52a4d9bb56eb598b", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.4.0", "_shasum": "9e1bc8410a852a0560606cb88fdbf85a6565befa", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "9e1bc8410a852a0560606cb88fdbf85a6565befa", "size": 90506, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.4.0.tgz", "integrity": "sha512-vUjbBBsoDUTQg4gLkPk7mh4qwGofUy5w/7qFNKv7G7X3vvZaQNepClomdjG77tYtrxajtgjNSIItqk9LRDNflA=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1444722451789, "_hasShrinkwrap": false, "_cnpm_publish_time": 1444722451789, "_cnpmcore_publish_time": "2021-12-15T17:41:46.028Z"}, "1.3.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.3.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "385f03e5d0d82ea8f839929c1d447102f795b363", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.3.0", "_shasum": "7dbfd7d88e879e03d380f577e70c7586b9b20920", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "4.1.0", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "dist": {"shasum": "7dbfd7d88e879e03d380f577e70c7586b9b20920", "size": 100439, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.3.0.tgz", "integrity": "sha512-1zl2y2m/yr4TW19IsLQIjJlSyFQ3OB/cx2KtM2NhtAYy276W84AOGitxn6IWLo1FxwEmOJeOrAN87QtOm+0h9g=="}, "directories": {}, "publish_time": 1442950176871, "_hasShrinkwrap": false, "_cnpm_publish_time": 1442950176871, "_cnpmcore_publish_time": "2021-12-15T17:41:46.475Z"}, "1.2.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.2.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "gitHead": "e912d00edb70f0da82dc8351c8d21e358f0df7d9", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable#readme", "_id": "sortablejs@1.2.1", "_shasum": "946001e11d646d49f5a66c613019075ccd678d9d", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "dist": {"shasum": "946001e11d646d49f5a66c613019075ccd678d9d", "size": 86549, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.2.1.tgz", "integrity": "sha512-yHg/h+Ve5NQQu4lOe6taKLXY92euM66EwKSiawRLT5wQrOrGt1wQsuj7Ho9h/DyHERrRPLOyz0pjIbcr7lUNJA=="}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441090445093, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441090445093, "_cnpmcore_publish_time": "2021-12-15T17:41:47.368Z"}, "1.2.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.2.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-srotable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.2.0", "dist": {"shasum": "0089c77744769e39d57479f23bde7b9c184078a9", "size": 84695, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.2.0.tgz", "integrity": "sha512-umRhLmTYUgFOGPTF+Lq8k6GRPO5MHzsfP6GQf3p4pwLXGs+kWoTtrggzOwQCu4q00h42xalcFSS8tVGkrOAbpA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432983230088, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432983230088, "_cnpmcore_publish_time": "2021-12-15T17:41:47.962Z"}, "1.1.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.1.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-srotable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.1.1", "dist": {"shasum": "eb43fb573ffb1a2f350fa7e68f13dc9f99226323", "size": 80377, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.1.1.tgz", "integrity": "sha512-zl2WYMFLBrJXeWPfvuKMincI3e0I5tLSMFzIOfSbV9r+qR3DEl8i2wKrEiIFXqrm5NH0izjVOA9o/OdOV/j6gg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1424411768295, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424411768295, "_cnpmcore_publish_time": "2021-12-15T17:41:48.473Z"}, "1.1.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.1.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-srotable", "react", "mixin"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.1.0", "dist": {"shasum": "4a89e66301e571866d288f8c1a0884779235ed28", "size": 80411, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.1.0.tgz", "integrity": "sha512-wrxAlprK3v/4FyPg1fv8czqci+H3vUTvOIJiqurk2SqioicXwVc/rDzizEAF3tKT20ktqeODH7ySgdQr246MqQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1423855202933, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423855202933, "_cnpmcore_publish_time": "2021-12-15T17:41:49.006Z"}, "1.0.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.0.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "ng-srotable", "angular"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "spm": {"main": "Sortable.js", "ignore": ["meteor", "st"]}, "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.0.1", "dist": {"shasum": "6c71ecc558f08740fc6afc8042c1c0dfd45c1eb7", "size": 77664, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.0.1.tgz", "integrity": "sha512-lm8lQb1D5gAyejpRCCFKIm34o5TgJCbVsOccBJJiKcupKUFR2IRNCxDo9dCpzxZXcakXInOwq8yp3n4k762e7Q=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1422299513160, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422299513160, "_cnpmcore_publish_time": "2021-12-15T17:41:49.415Z"}, "1.0.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.0.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-exec": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*", "spacejam": "*"}, "description": "Minimalist JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery. Supports AngularJS and any CSS library, e.g. Bootstrap.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@1.0.0", "dist": {"shasum": "73c330cff67a413e6161b21d5799b2414b859d59", "size": 75430, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.0.0.tgz", "integrity": "sha512-8a+tbrs8HhY2lhNoM/XCwbWl6jBj4+ajoy6UUFzliMsVWJVkbLS199FvBJyMIupU2BY2CRXj7stopB7A+31FVw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1419228402727, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419228402727, "_cnpmcore_publish_time": "2021-12-15T17:41:49.863Z"}, "0.7.2": {"name": "sortablejs", "exportName": "Sortable", "version": "0.7.2", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-shell": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*"}, "description": "Minimalist library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@0.7.2", "dist": {"shasum": "1a316be5f4fc953d956d847914bde8c6d694ff26", "size": 53980, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-0.7.2.tgz", "integrity": "sha512-HJkSSnBQ88MVsIFRQs2qtigxUUb3MtxWCvRybRrgTCjgk/uR8QEEJmPCQG4hRXk2uyH4tz8H40t3fKzXXorJUQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1418021585484, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418021585484, "_cnpmcore_publish_time": "2021-12-15T17:41:50.195Z"}, "0.7.1": {"name": "sortablejs", "exportName": "Sortable", "version": "0.7.1", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-shell": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*"}, "description": "Minimalist library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@0.7.1", "dist": {"shasum": "0e0c7fdbf14be50713817017f3a1723551c36880", "size": 53618, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-0.7.1.tgz", "integrity": "sha512-FISUsr44hCq9wNcOX2TRSCXj4mxkWQqQTIvfu51/E4oBXblDr0Qb6u3CgQL3JOoRyBwGXLSG8vNLZseEL2j6zA=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1417780776201, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417780776201, "_cnpmcore_publish_time": "2021-12-15T17:41:50.482Z"}, "0.7.0": {"name": "sortablejs", "exportName": "Sortable", "version": "0.7.0", "devDependencies": {"grunt": "*", "grunt-version": "*", "grunt-shell": "*", "grunt-contrib-jshint": "0.9.2", "grunt-contrib-uglify": "*"}, "description": "Minimalist library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery.", "main": "Sortable.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/rubaxa/Sortable.git"}, "keywords": ["sortable", "reorder", "drag"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rubaxa/Sortable/issues"}, "homepage": "https://github.com/rubaxa/Sortable", "_id": "sortablejs@0.7.0", "dist": {"shasum": "b058056924240c9ebef7b089c3130fb17c8f0576", "size": 53165, "noattachment": false, "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-0.7.0.tgz", "integrity": "sha512-z9g034lZtq+zmYRyitOjJNkQdMAHhavngn5s2DCJr8u3WYXvVAFIeatpAHPjkuEpRffwz+wWYFoBlgJfx2qgqw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rubaxa", "email": "<EMAIL>"}, "maintainers": [{"name": "owenm", "email": "<EMAIL>"}, {"name": "rubaxa", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1417637282739, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417637282739, "_cnpmcore_publish_time": "2021-12-15T17:41:50.918Z"}, "1.15.0": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.0", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "babf6ab85fdfc5e13be1027827cecfbba2fb0d1e", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.0", "_nodeVersion": "16.3.0", "_npmVersion": "7.6.0", "dist": {"integrity": "sha512-bv9qgVMjUMf89wAvM6AxVvS/4MX3sPeN0+agqShejLU5z5GX4C75ow1O2e5k4L6XItUyAK3gH6AxSbXrOM5e8w==", "shasum": "53230b8aa3502bb77a29e2005808ffdb4a5f7e2a", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.0.tgz", "fileCount": 8, "unpackedSize": 550699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiN1aqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ0Q//dNSpZekJ8xytWIjUQ7fEAi0Vh33uEi0TelJ458L2SLCcwASt\r\nXjOuPobn+QAhYlHBIZPBQ9/ZEQSunWqwRWsRxDQPSYSGh7gkzI6JeFQScath\r\nLmpJcUVr5v6Lrg/RrZ84IRRav1EqTGqwmNaM03ZLHL5/V4FjotzHhlEyWQe8\r\nqmY34uYKyhTQWeKXxoxwjdE8sg9qQ9+Ofa5sitqWEgOmDr46NXCJvro43dLE\r\nNlBkoA7/pJMEI3Z8E9LsFw2Wp+0tyXvgysFKcKzWNAfbhpXA6aNR7SpZMoD1\r\nkmzFu6zmSg5XVUXa4Xv6U9i+s4tfAU8f37Uqw+ZKk3ayxKlHj7z+MEtMx7RG\r\nvauNTHxvRjAZyyXHap2y45nTG+hORgYxYFaRye/En4jWotkPciekgZE5WuF2\r\niRGsHjWDsCZwQNrvee/rjkuxEWp1XwivQcwWGauNI01om+pdAgfkymfSsMW9\r\nHevk7dYOq1K9PSuV/wHZtuAqZQj7/LyirMEAOuylovdsYbKvkZUbWZOwzDXW\r\nsSywX3fRFfdUxJCRF2aDHWCk6D93AQs5lPlL0bLNaD/UalN4vylK0lyw4rXw\r\nchNR0wEAyCMvDS2DRCsuEgGk+qTobWcgNrzOHTv1A2+7eqlTuQBgmCU9kMjf\r\nxOwrr7+yFOyi0FlozAiaHpJ3gIbu9nE2MLI=\r\n=FR/R\r\n-----END PGP SIGNATURE-----\r\n", "size": 134132}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.0_1647793834611_0.4167578357771933"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-20T16:30:38.933Z"}, "1.15.1": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.1", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "1b7575f6f9d3f0a9e857786be5fdfe1e6ab73037", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.1", "_nodeVersion": "16.17.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-P5Cjvb0UG1ZVNiDPj/n4V+DinttXG6K8n7vM/HQf0C25K3YKQTQY6fsr/sEGsJGpQ9exmPxluHxKBc0mLKU1lQ==", "shasum": "9a35f52cdff449fb42ea8ecf222f3468d76e0a47", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.1.tgz", "fileCount": 8, "unpackedSize": 554213, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAXcCot4uxBJ4+Yt4swNAvjSIsubQqVd9Db8B47y126AIgBYCXwGC4T81/8WYHP3J7HjId9CRMmipP0v9+D81h3XE="}], "size": 133865}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.1_1701382042624_0.8299296184576501"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-30T22:07:22.872Z", "publish_time": 1701382042872, "_source_registry_name": "default"}, "1.15.2": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.2", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "424eed6fa33dfbf0152cb6e882fa5cff8c65c0d8", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.2", "_nodeVersion": "16.17.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-FJF5jgdfvoKn1MAKSdGs33bIqLi3LmsgVTliuX6iITj834F+JRQZN90Z93yql8h0K2t0RwDPBmxwlbZfDcxNZA==", "shasum": "4e9f7bda4718bd1838add9f1866ec77169149809", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.2.tgz", "fileCount": 8, "unpackedSize": 555579, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBMkOcchki6Hvv2s11xkBbZJ0ArvjV1dDLHhqZIv+28QIhAN/qNf3S8ADy5SKIGb0YRAkfMilTAA7cCiL3iixOncZf"}], "size": 134273}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.2_1705192069557_0.9974529939528174"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-14T00:27:49.728Z", "publish_time": 1705192069728, "_source_registry_name": "default"}, "1.15.3": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.3", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "dcb8f9e85b972292db1e0cab2ff9d7cd359ff352", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.3", "_nodeVersion": "16.17.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-zdK3/kwwAK1cJgy1rwl1YtNTbRmc8qW/+vgXf75A7NHag5of4pyI6uK86ktmQETyWRH7IGaE73uZOOBcGxgqZg==", "shasum": "033668db5ebfb11167d1249ab88e748f27959e29", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.3.tgz", "fileCount": 14, "unpackedSize": 634329, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7wRPL0KrGKDwJ9z4o6FJiXkzMhf97C5HukFV27KPTAAiEA5pctNP8VfzQdn0Goxgvmcm0t7sjET52Q1MwByAQww7s="}], "size": 153564}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.3_1725231329921_0.8362448271771772"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-01T22:55:30.201Z", "publish_time": 1725231330201, "_source_registry_name": "default"}, "1.15.4": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.4", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "62f0498ac3585982aa44f4492bbcfcb3e8776a80", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.4", "_nodeVersion": "16.17.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-wr7G5Id/WNllca5yF9I2vsz/2wDKJebX5FJBtfUFBGGpaaIVjW4kziAnNMEcigaTZAaPLB92NYBGqWenGDH++g==", "shasum": "f5601f6aa725df1601c09a4a50a8f4ea5d95d96b", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.4.tgz", "fileCount": 14, "unpackedSize": 640262, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfvLonXtIzphQRsD10nsC0xIT9IExtpNp1yo5PiuOBwQIgUPWqgGduZZmo1GpMyD6YW+pe5QOPdQfzx/IFLaDMte8="}], "size": 154508}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.4_1732444301509_0.6425135621833593"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-24T10:31:41.749Z", "publish_time": 1732444301749, "_source_registry_name": "default"}, "1.15.5": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.5", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "104d731585f260315116eacb56542a32653ad3bb", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.5", "_nodeVersion": "16.17.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-xDJLosRJzZ+nVnjaUYmO9H/wZth0lWTRq7VzV1eQyDSKsvxmoJ69HTGcwnwGYpJG/AkJ9OWiwWH4BhIycdonWw==", "shasum": "225edc6ae153b2e5eca54f150abc2bc5f9305b29", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.5.tgz", "fileCount": 14, "unpackedSize": 640663, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAUH2HOLvjxEEXFGDjOQlyuoUJYHCHLtix10XCdcX2LgIhALctNtHTaLJ+9fkWQm/OJffJXin13B/2BUFbfeoNdqzJ"}], "size": 154547}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.5_1732749234492_0.2140059032378565"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-27T23:13:54.713Z", "publish_time": 1732749234713, "_source_registry_name": "default"}, "1.15.6": {"name": "sortablejs", "exportName": "Sortable", "version": "1.15.6", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": [{"name": "rubaxa", "email": "<EMAIL>"}, {"name": "owenm", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT", "gitHead": "63ecb31937d19fcb30aa924e82ee8a769ef34492", "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "_id": "sortablejs@1.15.6", "_nodeVersion": "16.17.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-aNfiuwMEpfBM/CN6LY0ibyhxPfPbyFeBTYJKCvzkJ2GkUpazIt3H+QIPAMHwqQ7tMKaHz1Qj+rJJCqljnf4p3A==", "shasum": "ff93699493f5b8ab8d828f933227b4988df1d393", "tarball": "https://registry.npmmirror.com/sortablejs/-/sortablejs-1.15.6.tgz", "fileCount": 14, "unpackedSize": 640877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMAo73oSfn2TQx9R2H4Ufo18PBWgts8iNYa8KFCSX0NQIgSsj7MK1ErH7SYmZa/ERnknJvrm/T1mfSbVwSgjAEb3Y="}], "size": 154705}, "_npmUser": {"name": "owenm", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sortablejs_1.15.6_1732818109699_0.9920355624051163"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-28T18:21:49.918Z", "publish_time": 1732818109918, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/SortableJS/Sortable/issues"}, "homepage": "https://github.com/SortableJS/Sortable#readme", "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "_source_registry_name": "default"}