{"_attachments": {}, "_id": "vue-echarts", "_rev": "697660-61f2598c08c71399a3bf2f1c", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "description": "Vue.js component for Apache ECharts™.", "dist-tags": {"latest": "7.0.3"}, "license": "MIT", "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "name": "vue-echarts", "readme": "<h1 align=\"center\">Vue-ECharts</h1>\n\n<p align=\"center\">Vue.js component for Apache ECharts™.</p>\n<p align=\"center\"><a href=\"https://npmjs.com/package/vue-echarts\"><img alt=\"npm version\" src=\"https://img.shields.io/npm/v/vue-echarts\"></a> <a href=\"https://vue-echarts.dev/\"><img src=\"https://img.shields.io/badge/Demo%20%C2%BB-20c3aa\" alt=\"View demo\"></a> <a href=\"./README.zh-Hans.md\"><img src=\"https://img.shields.io/badge/%E4%B8%AD%E6%96%87%E7%89%88%20%C2%BB-000\" alt=\"前往中文版\"></a></p>\n<p align=\"center\"><a href=\"https:///pr.new/ecomfe/vue-echarts\"><img alt=\"Open in Codeflow\" src=\"https://developer.stackblitz.com/img/open_in_codeflow.svg\" height=\"28\"></a> <a href=\"https://codesandbox.io/p/github/ecomfe/vue-echarts\"><img alt=\"Edit in CodeSandbox\" src=\"https://assets.codesandbox.io/github/button-edit-lime.svg\" height=\"28\"></a></p>\n\n---\n\n> Still using v6? Read v6 docs [here →](https://github.com/ecomfe/vue-echarts/tree/6.x)\n\n## Installation & Usage\n\n### npm\n\n```sh\nnpm add echarts vue-echarts\n```\n\n#### Example\n\n<details>\n<summary>Vue 3 <a href=\"https://stackblitz.com/edit/vue-echarts-vue-3?file=src%2FApp.vue\">Demo →</a></summary>\n\n```vue\n<template>\n  <v-chart class=\"chart\" :option=\"option\" />\n</template>\n\n<script setup>\nimport { use } from \"echarts/core\";\nimport { CanvasRenderer } from \"echarts/renderers\";\nimport { PieChart } from \"echarts/charts\";\nimport {\n  TitleComponent,\n  TooltipComponent,\n  LegendComponent\n} from \"echarts/components\";\nimport VChart, { THEME_KEY } from \"vue-echarts\";\nimport { ref, provide } from \"vue\";\n\nuse([\n  CanvasRenderer,\n  PieChart,\n  TitleComponent,\n  TooltipComponent,\n  LegendComponent\n]);\n\nprovide(THEME_KEY, \"dark\");\n\nconst option = ref({\n  title: {\n    text: \"Traffic Sources\",\n    left: \"center\"\n  },\n  tooltip: {\n    trigger: \"item\",\n    formatter: \"{a} <br/>{b} : {c} ({d}%)\"\n  },\n  legend: {\n    orient: \"vertical\",\n    left: \"left\",\n    data: [\"Direct\", \"Email\", \"Ad Networks\", \"Video Ads\", \"Search Engines\"]\n  },\n  series: [\n    {\n      name: \"Traffic Sources\",\n      type: \"pie\",\n      radius: \"55%\",\n      center: [\"50%\", \"60%\"],\n      data: [\n        { value: 335, name: \"Direct\" },\n        { value: 310, name: \"Email\" },\n        { value: 234, name: \"Ad Networks\" },\n        { value: 135, name: \"Video Ads\" },\n        { value: 1548, name: \"Search Engines\" }\n      ],\n      emphasis: {\n        itemStyle: {\n          shadowBlur: 10,\n          shadowOffsetX: 0,\n          shadowColor: \"rgba(0, 0, 0, 0.5)\"\n        }\n      }\n    }\n  ]\n});\n</script>\n\n<style scoped>\n.chart {\n  height: 400px;\n}\n</style>\n```\n\n</details>\n\n<details>\n<summary>Vue 2 <a href=\"https://stackblitz.com/edit/vue-echarts-vue-2?file=src%2FApp.vue\">Demo →</a></summary>\n\n```vue\n<template>\n  <v-chart class=\"chart\" :option=\"option\" />\n</template>\n\n<script>\nimport { use } from \"echarts/core\";\nimport { CanvasRenderer } from \"echarts/renderers\";\nimport { PieChart } from \"echarts/charts\";\nimport {\n  TitleComponent,\n  TooltipComponent,\n  LegendComponent\n} from \"echarts/components\";\nimport VChart, { THEME_KEY } from \"vue-echarts\";\n\nuse([\n  CanvasRenderer,\n  PieChart,\n  TitleComponent,\n  TooltipComponent,\n  LegendComponent\n]);\n\nexport default {\n  name: \"HelloWorld\",\n  components: {\n    VChart\n  },\n  provide: {\n    [THEME_KEY]: \"dark\"\n  },\n  data() {\n    return {\n      option: {\n        title: {\n          text: \"Traffic Sources\",\n          left: \"center\"\n        },\n        tooltip: {\n          trigger: \"item\",\n          formatter: \"{a} <br/>{b} : {c} ({d}%)\"\n        },\n        legend: {\n          orient: \"vertical\",\n          left: \"left\",\n          data: [\n            \"Direct\",\n            \"Email\",\n            \"Ad Networks\",\n            \"Video Ads\",\n            \"Search Engines\"\n          ]\n        },\n        series: [\n          {\n            name: \"Traffic Sources\",\n            type: \"pie\",\n            radius: \"55%\",\n            center: [\"50%\", \"60%\"],\n            data: [\n              { value: 335, name: \"Direct\" },\n              { value: 310, name: \"Email\" },\n              { value: 234, name: \"Ad Networks\" },\n              { value: 135, name: \"Video Ads\" },\n              { value: 1548, name: \"Search Engines\" }\n            ],\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: \"rgba(0, 0, 0, 0.5)\"\n              }\n            }\n          }\n        ]\n      }\n    };\n  }\n};\n</script>\n\n<style scoped>\n.chart {\n  height: 400px;\n}\n</style>\n```\n\n</details>\n\n> [!IMPORTANT]\n> We encourage manually importing components and charts from ECharts for smaller bundle size. We've built an [import code generator](https://vue-echarts.dev/#codegen) to help you with that. You can just paste in your `option` code and we'll generate the precise import code for you.\n>\n> ![](https://github.com/ecomfe/vue-echarts/assets/1726061/f9c38a06-3422-4f0e-ab8c-f242d9aea9aa)\n>\n> [Try it →](https://vue-echarts.dev/#codegen)\n\nBut if you really want to import the whole ECharts bundle without having to import modules manually, just add this in your code:\n\n```js\nimport \"echarts\";\n```\n\n### CDN\n\nDrop `<script>` inside your HTML file and access the component via `window.VueECharts`.\n\n<details>\n<summary>Vue 3 <a href=\"https://stackblitz.com/edit/vue-echarts-vue-3-global?file=index.html\">Demo →</a></summary>\n\n<!-- vue3Scripts:start -->\n```html\n<script src=\"https://cdn.jsdelivr.net/npm/vue@3.4.33\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/echarts@5.5.1\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/vue-echarts@7.0.3\"></script>\n```\n<!-- vue3Scripts:end -->\n\n```js\nconst app = Vue.createApp(...)\n\n// register globally (or you can do it locally)\napp.component('v-chart', VueECharts)\n```\n\n</details>\n\n<details>\n<summary>Vue 2 <a href=\"https://stackblitz.com/edit/vue-echarts-vue-2-global?file=index.html\">Demo →</a></summary>\n\n<!-- vue2Scripts:start -->\n```html\n<script src=\"https://cdn.jsdelivr.net/npm/vue@2.7.16\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/echarts@5.5.1\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/vue-echarts@7.0.3\"></script>\n```\n<!-- vue2Scripts:end -->\n\n```js\n// register globally (or you can do it locally)\nVue.component(\"v-chart\", VueECharts);\n```\n\n</details>\n\nSee more examples [here](https://github.com/ecomfe/vue-echarts/tree/main/src/demo).\n\n### Props\n\n- `init-options: object`\n\n  Optional chart init configurations. See `echarts.init`'s `opts` parameter [here →](https://echarts.apache.org/en/api.html#echarts.init)\n\n  Injection key: `INIT_OPTIONS_KEY`.\n\n- `theme: string | object`\n\n  Theme to be applied. See `echarts.init`'s `theme` parameter [here →](https://echarts.apache.org/en/api.html#echarts.init)\n\n  Injection key: `THEME_KEY`.\n\n- `option: object`\n\n  ECharts' universal interface. Modifying this prop will trigger ECharts' `setOption` method. Read more [here →](https://echarts.apache.org/en/option.html)\n\n  > 💡 When `update-options` is not specified, `notMerge: false` will be specified by default when the `setOption` method is called if the `option` object is modified directly and the reference remains unchanged; otherwise, if a new reference is bound to `option`, ` notMerge: true` will be specified.\n\n- `update-options: object`\n\n  Options for updating chart option. See `echartsInstance.setOption`'s `opts` parameter [here →](https://echarts.apache.org/en/api.html#echartsInstance.setOption)\n\n  Injection key: `UPDATE_OPTIONS_KEY`.\n\n- `group: string`\n\n  Group name to be used in chart [connection](https://echarts.apache.org/en/api.html#echarts.connect). See `echartsInstance.group` [here →](https://echarts.apache.org/en/api.html#echartsInstance.group)\n\n- `autoresize: boolean | { throttle?: number, onResize?: () => void }` (default: `false`)\n\n  Whether the chart should be resized automatically whenever its root is resized. Use the options object to specify a custom throttle delay (in milliseconds) and/or an extra resize callback function.\n\n- `loading: boolean` (default: `false`)\n\n  Whether the chart is in loading state.\n\n- `loading-options: object`\n\n  Configuration item of loading animation. See `echartsInstance.showLoading`'s `opts` parameter [here →](https://echarts.apache.org/en/api.html#echartsInstance.showLoading)\n\n  Injection key: `LOADING_OPTIONS_KEY`.\n\n- `manual-update: boolean` (default: `false`)\n\n  For performance critical scenarios (having a large dataset) we'd better bypass Vue's reactivity system for `option` prop. By specifying `manual-update` prop with `true` and not providing `option` prop, the dataset won't be watched any more. After doing so, you need to retrieve the component instance with `ref` and manually call `setOption` method to update the chart.\n\n### Events\n\nYou can bind events with Vue's `v-on` directive.\n\n```vue\n<template>\n  <v-chart :option=\"option\" @highlight=\"handleHighlight\" />\n</template>\n```\n\n> **Note**\n>\n> Only the `.once` event modifier is supported as other modifiers are tightly coupled with the DOM event system.\n\nVue-ECharts support the following events:\n\n- `highlight` [→](https://echarts.apache.org/en/api.html#events.highlight)\n- `downplay` [→](https://echarts.apache.org/en/api.html#events.downplay)\n- `selectchanged` [→](https://echarts.apache.org/en/api.html#events.selectchanged)\n- `legendselectchanged` [→](https://echarts.apache.org/en/api.html#events.legendselectchanged)\n- `legendselected` [→](https://echarts.apache.org/en/api.html#events.legendselected)\n- `legendunselected` [→](https://echarts.apache.org/en/api.html#events.legendunselected)\n- `legendselectall` [→](https://echarts.apache.org/en/api.html#events.legendselectall)\n- `legendinverseselect` [→](https://echarts.apache.org/en/api.html#events.legendinverseselect)\n- `legendscroll` [→](https://echarts.apache.org/en/api.html#events.legendscroll)\n- `datazoom` [→](https://echarts.apache.org/en/api.html#events.datazoom)\n- `datarangeselected` [→](https://echarts.apache.org/en/api.html#events.datarangeselected)\n- `timelinechanged` [→](https://echarts.apache.org/en/api.html#events.timelinechanged)\n- `timelineplaychanged` [→](https://echarts.apache.org/en/api.html#events.timelineplaychanged)\n- `restore` [→](https://echarts.apache.org/en/api.html#events.restore)\n- `dataviewchanged` [→](https://echarts.apache.org/en/api.html#events.dataviewchanged)\n- `magictypechanged` [→](https://echarts.apache.org/en/api.html#events.magictypechanged)\n- `geoselectchanged` [→](https://echarts.apache.org/en/api.html#events.geoselectchanged)\n- `geoselected` [→](https://echarts.apache.org/en/api.html#events.geoselected)\n- `geounselected` [→](https://echarts.apache.org/en/api.html#events.geounselected)\n- `axisareaselected` [→](https://echarts.apache.org/en/api.html#events.axisareaselected)\n- `brush` [→](https://echarts.apache.org/en/api.html#events.brush)\n- `brushEnd` [→](https://echarts.apache.org/en/api.html#events.brushEnd)\n- `brushselected` [→](https://echarts.apache.org/en/api.html#events.brushselected)\n- `globalcursortaken` [→](https://echarts.apache.org/en/api.html#events.globalcursortaken)\n- `rendered` [→](https://echarts.apache.org/en/api.html#events.rendered)\n- `finished` [→](https://echarts.apache.org/en/api.html#events.finished)\n- Mouse events\n  - `click` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.click)\n  - `dblclick` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.dblclick)\n  - `mouseover` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.mouseover)\n  - `mouseout` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.mouseout)\n  - `mousemove` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.mousemove)\n  - `mousedown` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.mousedown)\n  - `mouseup` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.mouseup)\n  - `globalout` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.globalout)\n  - `contextmenu` [→](https://echarts.apache.org/en/api.html#events.Mouse%20events.contextmenu)\n- ZRender events\n  - `zr:click`\n  - `zr:mousedown`\n  - `zr:mouseup`\n  - `zr:mousewheel`\n  - `zr:dblclick`\n  - `zr:contextmenu`\n\nSee supported events [here →](https://echarts.apache.org/en/api.html#events)\n\n#### Native DOM Events\n\nAs Vue-ECharts binds events to the ECharts instance by default, there is some caveat when using native DOM events. You need to prefix the event name with `native:` to bind native DOM events (or you can use the `.native` modifier in Vue 2, which is dropped in Vue 3).\n\n```vue\n<template>\n  <v-chart @native:click=\"handleClick\" />\n</template>\n```\n\n### Provide / Inject\n\nVue-ECharts provides provide/inject API for `theme`, `init-options`, `update-options` and `loading-options` to help configuring contextual options. eg. for `init-options` you can use the provide API like this:\n\n<details>\n<summary>Vue 3</summary>\n\n```js\nimport { THEME_KEY } from 'vue-echarts'\nimport { provide } from 'vue'\n\n// composition API\nprovide(THEME_KEY, 'dark')\n\n// options API\n{\n  provide: {\n    [THEME_KEY]: 'dark'\n  }\n}\n```\n\n</details>\n\n<details>\n<summary>Vue 2</summary>\n\n```js\nimport { THEME_KEY } from 'vue-echarts'\n\n// in component options\n{\n  provide: {\n    [THEME_KEY]: 'dark'\n  }\n}\n```\n\n> **Note**\n>\n> You need to provide an object for Vue 2 if you want to change it dynamically.\n>\n> ```js\n> // in component options\n> {\n>   data () {\n>     return {\n>       theme: { value: 'dark' }\n>     }\n>   },\n>   provide () {\n>     return {\n>       [THEME_KEY]: this.theme\n>     }\n>   }\n> }\n> ```\n\n</details>\n\n### Methods\n\n- `setOption` [→](https://echarts.apache.org/en/api.html#echartsInstance.setOption)\n- `getWidth` [→](https://echarts.apache.org/en/api.html#echartsInstance.getWidth)\n- `getHeight` [→](https://echarts.apache.org/en/api.html#echartsInstance.getHeight)\n- `getDom` [→](https://echarts.apache.org/en/api.html#echartsInstance.getDom)\n- `getOption` [→](https://echarts.apache.org/en/api.html#echartsInstance.getOption)\n- `resize` [→](https://echarts.apache.org/en/api.html#echartsInstance.resize)\n- `dispatchAction` [→](https://echarts.apache.org/en/api.html#echartsInstance.dispatchAction)\n- `convertToPixel` [→](https://echarts.apache.org/en/api.html#echartsInstance.convertToPixel)\n- `convertFromPixel` [→](https://echarts.apache.org/en/api.html#echartsInstance.convertFromPixel)\n- `containPixel` [→](https://echarts.apache.org/en/api.html#echartsInstance.containPixel)\n- `showLoading` [→](https://echarts.apache.org/en/api.html#echartsInstance.showLoading)\n- `hideLoading` [→](https://echarts.apache.org/en/api.html#echartsInstance.hideLoading)\n- `getDataURL` [→](https://echarts.apache.org/en/api.html#echartsInstance.getDataURL)\n- `getConnectedDataURL` [→](https://echarts.apache.org/en/api.html#echartsInstance.getConnectedDataURL)\n- `clear` [→](https://echarts.apache.org/en/api.html#echartsInstance.clear)\n- `dispose` [→](https://echarts.apache.org/en/api.html#echartsInstance.dispose)\n\n### Static Methods\n\nStatic methods can be accessed from [`echarts` itself](https://echarts.apache.org/en/api.html#echarts).\n\n## CSP: `style-src` or `style-src-elem`\n\nIf you are applying a CSP to prevent inline `<style>` injection, you need to use `vue-echarts/csp` instead of `vue-echarts` and include `vue-echarts/csp/style.css` manually.\n\n## Migration to v7\n\nRead the breaking changes document in the [release log](https://github.com/ecomfe/vue-echarts/releases/tag/v7.0.0-beta.0) and the migration shoud be straightforward.\n\n## Local development\n\n```sh\npnpm i\npnpm serve\n```\n\nOpen `http://localhost:8080` to see the demo.\n\n## Notice\n\nThe Apache Software Foundation [Apache ECharts, ECharts](https://echarts.apache.org/), Apache, the Apache feather, and the Apache ECharts project logo are either registered trademarks or trademarks of the [Apache Software Foundation](https://www.apache.org/).\n", "time": {"created": "2022-01-27T08:36:28.739Z", "modified": "2024-08-19T16:59:45.552Z", "6.0.0": "2021-08-02T12:31:27.700Z", "6.0.0-rc.6": "2021-06-09T17:24:07.123Z", "6.0.0-rc.5": "2021-04-30T10:45:29.870Z", "6.0.0-rc.4": "2021-03-29T11:53:16.113Z", "6.0.0-rc.3": "2021-03-09T02:53:19.454Z", "6.0.0-rc.2": "2021-03-09T02:37:04.556Z", "6.0.0-rc.1": "2021-03-09T02:32:21.251Z", "6.0.0-beta.7": "2021-03-04T17:07:49.008Z", "6.0.0-beta.6": "2021-03-03T12:36:25.672Z", "6.0.0-beta.5": "2021-03-03T10:29:30.123Z", "6.0.0-beta.4": "2021-03-02T16:27:50.181Z", "6.0.0-beta.3": "2021-03-02T03:39:43.208Z", "6.0.0-beta.2": "2021-02-28T17:31:21.950Z", "6.0.0-beta.1": "2021-02-27T18:50:07.791Z", "6.0.0-alpha.5": "2021-02-23T12:09:53.267Z", "6.0.0-alpha.4": "2021-02-23T07:52:53.972Z", "6.0.0-alpha.3": "2021-02-22T15:44:39.112Z", "6.0.0-alpha.2": "2021-02-22T11:42:12.920Z", "6.0.0-alpha.1": "2021-02-22T09:19:49.596Z", "5.0.0-beta.0": "2020-01-24T03:40:36.218Z", "4.1.0": "2019-12-06T09:11:20.191Z", "4.0.4": "2019-09-17T09:18:55.450Z", "4.0.3": "2019-05-17T02:58:11.982Z", "4.0.2": "2019-04-26T09:25:26.423Z", "4.0.1": "2019-01-22T02:26:24.045Z", "4.0.0": "2019-01-19T16:13:32.373Z", "4.0.0-beta.1": "2019-01-18T11:31:37.219Z", "4.0.0-beta.0": "2019-01-18T11:18:21.086Z", "3.1.3": "2018-09-25T09:41:43.840Z", "3.1.2": "2018-09-02T17:50:34.209Z", "3.1.1": "2018-08-24T13:27:20.870Z", "3.1.0": "2018-08-23T16:22:24.071Z", "3.0.9": "2018-06-19T07:48:33.255Z", "3.0.8": "2018-06-14T03:30:51.598Z", "3.0.7": "2018-04-01T12:52:56.158Z", "3.0.6": "2018-04-01T08:06:26.973Z", "3.0.5": "2018-03-17T05:28:50.803Z", "3.0.4": "2018-03-09T09:37:26.904Z", "3.0.3": "2018-02-22T17:48:50.231Z", "3.0.2": "2018-02-10T14:29:23.092Z", "3.0.1": "2018-01-18T02:07:11.336Z", "3.0.0": "2018-01-17T18:15:22.437Z", "2.6.0": "2017-12-18T07:37:09.532Z", "2.5.1": "2017-11-10T06:46:35.802Z", "2.5.0": "2017-11-08T04:51:58.737Z", "2.4.1": "2017-09-18T06:31:21.244Z", "2.4.0": "2017-05-27T07:55:06.261Z", "2.3.9": "2017-05-27T06:22:19.613Z", "2.3.8": "2017-05-03T04:20:25.791Z", "2.3.7": "2017-04-22T14:59:25.621Z", "2.3.6": "2017-04-21T10:32:11.333Z", "2.3.5": "2017-04-21T09:19:47.477Z", "2.3.4": "2017-04-18T07:14:06.997Z", "2.3.3": "2017-03-08T04:32:20.995Z", "2.3.2": "2017-03-07T09:29:18.509Z", "2.3.1": "2017-02-13T09:20:16.903Z", "2.3.0": "2017-02-09T03:48:48.802Z", "2.2.0": "2017-01-06T08:20:54.606Z", "2.1.0": "2016-12-21T09:40:19.358Z", "2.0.0": "2016-12-04T14:55:03.668Z", "1.0.0": "2016-12-04T14:52:16.614Z", "0.1.2": "2016-11-11T16:03:46.503Z", "0.1.1": "2016-06-17T10:56:11.975Z", "0.1.0": "2016-06-17T10:23:44.770Z", "0.0.0": "2016-03-22T03:14:39.629Z", "6.0.1": "2022-01-14T11:08:22.899Z", "6.0.2": "2022-01-18T10:20:17.452Z", "6.0.3": "2022-05-10T17:23:03.768Z", "6.1.0": "2022-06-15T11:28:12.424Z", "6.2.0": "2022-07-06T07:12:19.746Z", "6.2.1": "2022-07-06T08:19:21.989Z", "6.2.2": "2022-07-07T07:22:00.566Z", "6.2.3": "2022-07-12T04:26:50.974Z", "6.2.4": "2022-12-07T11:47:10.526Z", "6.3.0": "2022-12-16T17:27:45.494Z", "6.3.1": "2022-12-16T17:47:09.313Z", "6.3.2": "2022-12-18T11:40:06.969Z", "6.3.3": "2022-12-18T18:04:15.575Z", "6.4.0": "2022-12-29T10:14:53.210Z", "6.4.1": "2023-01-01T15:52:20.289Z", "6.5.0": "2023-01-02T15:42:22.552Z", "6.5.1": "2023-01-09T06:11:32.897Z", "6.5.2": "2023-01-28T15:52:44.980Z", "6.5.3": "2023-02-08T08:23:24.710Z", "6.5.4": "2023-02-08T08:32:59.442Z", "6.5.5": "2023-04-24T05:17:44.214Z", "6.6.0": "2023-06-13T12:38:25.282Z", "6.6.1": "2023-08-04T17:03:31.509Z", "6.6.2": "2023-12-04T09:15:07.628Z", "6.6.3": "2023-12-12T02:00:23.479Z", "6.6.4": "2023-12-13T03:02:59.522Z", "6.6.5": "2023-12-18T02:29:18.737Z", "6.6.6": "2023-12-26T06:55:26.450Z", "6.6.7": "2023-12-26T07:00:52.556Z", "6.6.8": "2023-12-27T07:59:27.386Z", "6.6.9": "2024-02-20T02:59:18.349Z", "6.6.10": "2024-04-22T03:01:38.396Z", "6.7.0": "2024-04-22T13:16:21.567Z", "6.7.1": "2024-04-23T11:51:03.021Z", "6.7.2": "2024-05-07T09:45:20.400Z", "6.7.3": "2024-06-04T15:12:27.273Z", "7.0.0-beta.0": "2024-07-24T10:32:44.426Z", "7.0.0": "2024-08-03T08:18:29.609Z", "7.0.1": "2024-08-03T23:39:15.201Z", "7.0.2": "2024-08-13T07:32:14.280Z", "7.0.3": "2024-08-19T15:43:03.464Z"}, "versions": {"6.0.0": {"name": "vue-echarts", "version": "6.0.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js", "prepare": "npm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.11.2"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.0-beta.1", "@vue/cli-plugin-eslint": "^5.0.0-beta.1", "@vue/cli-plugin-typescript": "^5.0.0-beta.1", "@vue/cli-service": "^5.0.0-beta.1", "@vue/compiler-sfc": "^3.1.1", "@vue/composition-api": "^1.0.5", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.1.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.6.0", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.2.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-styles": "^3.14.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "~4.1.5", "vue": "^3.1.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "gitHead": "bb9540bf4a224a0343bcc5de0cdb4903933963b9", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0", "_nodeVersion": "14.17.2", "_npmVersion": "6.14.13", "dist": {"shasum": "480263fc6ed2125b886bb1b7f05bf9273edee552", "size": 66811, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0.tgz", "integrity": "sha512-eB3fnnu8Oc+kwVTlBz95o/rliTbWIiUdxTG7vRQIIgRpINWW07WLSPm+UhlD6FZ/P8IqCoVG87pJeLRKsaHeMw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0_1627907487527_0.4018422618646189"}, "_hasShrinkwrap": false, "publish_time": 1627907487700, "_cnpm_publish_time": 1627907487700, "_cnpmcore_publish_time": "2021-12-16T14:46:12.320Z", "hasInstallScript": true}, "6.0.0-rc.6": {"name": "vue-echarts", "version": "6.0.0-rc.6", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js", "prepare": "npm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.9.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.0-beta.1", "@vue/cli-plugin-eslint": "^5.0.0-beta.1", "@vue/cli-plugin-typescript": "^5.0.0-beta.1", "@vue/cli-service": "^5.0.0-beta.1", "@vue/compiler-sfc": "^3.1.1", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.1.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.6.0", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.2.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-styles": "^3.14.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "~4.1.5", "vue": "^3.1.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "gitHead": "a77ecc726d884b8c5cc99c2b5a5cdb4bb07141d0", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-rc.6", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "c12afdbe2e858c941d434996e9bb9b7af3c8c631", "size": 66800, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-rc.6.tgz", "integrity": "sha512-wvgP77HkK2Cjm4CV/FDbnWQ9UiOvID4ayqpcED/e4MJ3UJO5E8hj8H23CMS7GiNdywBg5GQj1M4TU87zpAX9Qg=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-rc.6_1623259447009_0.2622829634216304"}, "_hasShrinkwrap": false, "publish_time": 1623259447123, "_cnpm_publish_time": 1623259447123, "_cnpmcore_publish_time": "2021-12-16T14:46:12.600Z", "hasInstallScript": true}, "6.0.0-rc.5": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-rc.5", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "prepare": "npm run build", "postinstall": "node ./scripts/postinstall.js", "docs": "node -r esm ./scripts/docs.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.4"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.11"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.11"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "dd71918d101bdb76cd40825ec02d3d8ee57d10fd", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-rc.5", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "4e06289927d03d289c368fa09b740d82e6ded528", "size": 67692, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-rc.5.tgz", "integrity": "sha512-qG7jM4tz2Ipygn5r88s2nHFftakQyWpoM2IXwpzWrQwl/UZIQ1bbqyyLHqMLtgzEU/QhD/maFG9tnMkjVR75sg=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-rc.5_1619779529755_0.3327986425961822"}, "_hasShrinkwrap": false, "publish_time": 1619779529870, "_cnpm_publish_time": 1619779529870, "_cnpmcore_publish_time": "2021-12-16T14:46:12.826Z", "hasInstallScript": true}, "6.0.0-rc.4": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-rc.4", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "prepare": "npm run build", "postinstall": "node ./scripts/postinstall.js", "docs": "node -r esm ./scripts/docs.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.4"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "23a4efd070fd0c185b7ddb9ca6cfbbbc5c5f1138", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-rc.4", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "e7d25304136db7d959f9710172c4d36f62f620e4", "size": 66905, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-rc.4.tgz", "integrity": "sha512-kkpBkAFIh7aHw2/0FEm9UktQ16LDP5EmATaCqq+CtnSKzd+vuZI6UxrfTLnOQNMCR5eKX8FoonggoKHpcjNGQg=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-rc.4_1617018795951_0.17649956281931467"}, "_hasShrinkwrap": false, "publish_time": 1617018796113, "_cnpm_publish_time": 1617018796113, "_cnpmcore_publish_time": "2021-12-16T14:46:13.041Z", "hasInstallScript": true}, "6.0.0-rc.3": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-rc.3", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "prepare": "npm run build", "postinstall": "node ./scripts/postinstall.js", "docs": "node -r esm ./scripts/docs.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "e10e588d99a8455a78f166110f50e6596c140ad1", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-rc.3", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "040471fc19025b989c96c901689f6c0d1022d1ff", "size": 66911, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-rc.3.tgz", "integrity": "sha512-B7xfwpHaOeM9+cjallrK3AW893oaeqT9Sjx7tHgwoyGdfPTq6cwDc2Hcdmjgrs/KEp+ZZXMIajXky30Wu4/TDw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-rc.3_1615258399322_0.6488439455892845"}, "_hasShrinkwrap": false, "publish_time": 1615258399454, "_cnpm_publish_time": 1615258399454, "_cnpmcore_publish_time": "2021-12-16T14:46:13.362Z", "hasInstallScript": true}, "6.0.0-rc.2": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-rc.2", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "postinstall": "node ./scripts/postinstall.js", "readme": "node -r esm ./scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "e543866b4dff3a8031b2886a51063e71adbb9c92", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-rc.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "b77b8c81562234ec836363b4b5a82fb2315abad1", "size": 67865, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-rc.2.tgz", "integrity": "sha512-oIS3AHLpgCcrJ1NddMsK3evMkcfeJOzJ7SqVJCCRwVgJw5QGABHNChHX+Gd66akXEuCKBDoEkKbUAIc+46mi1Q=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-rc.2_1615257424371_0.33816411973411475"}, "_hasShrinkwrap": false, "publish_time": 1615257424556, "_cnpm_publish_time": 1615257424556, "_cnpmcore_publish_time": "2021-12-16T14:46:13.601Z", "hasInstallScript": true}, "6.0.0-rc.1": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-rc.1", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "postinstall": "node ./scripts/postinstall.js", "readme": "node -r esm ./scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "9e14357510e8d3f58cc671826e2c79d1a5bd94e7", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-rc.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "58b77343709b7337d5b15f874b66fe43fe98e0b3", "size": 67896, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-rc.1.tgz", "integrity": "sha512-UCGGVUyCbKuxQxl3rvsG9c4L9xrV/DtTQm7m/Q8jy57EJcC4TZKkbL3vcoceDfVRiNXSTSDvOlIlrwNNDa2NCA=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-rc.1_1615257140923_0.7193495584872138"}, "_hasShrinkwrap": false, "publish_time": 1615257141251, "_cnpm_publish_time": 1615257141251, "_cnpmcore_publish_time": "2021-12-16T14:46:13.807Z", "hasInstallScript": true}, "6.0.0-beta.7": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.7", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "6526e2e43cdb8aa2f81d466da816b82b86bf5c43", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.7", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "1daf952400fcc450facf300e5119885b2f51e461", "size": 66173, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.7.tgz", "integrity": "sha512-lcXFhge5WmallSQztruCbMWQ4Yu2EqcSdVdzndQLZN5OLrrJ7CdNF1X+h9iutnQzo2Xxsllsuxu/G1JWdEigDQ=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.7_1614877668838_0.49314901688953205"}, "_hasShrinkwrap": false, "publish_time": 1614877669008, "_cnpm_publish_time": 1614877669008, "_cnpmcore_publish_time": "2021-12-16T14:46:14.098Z"}, "6.0.0-beta.6": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.6", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "7ced9421af65c217c2186eee036b0a1935d60015", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.6", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "0a65216c43faa3c39759a95133cf848e97e3f94c", "size": 65694, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.6.tgz", "integrity": "sha512-NkZNDGWjBbG+o6ZLbsmQ8haXPFz0Vh9YDbW+2HViNU82WWkqlQPH9YQALD8a2kGsFfm1he5/nj2ZOdf0fi9Dog=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.6_1614774985480_0.7121476182571262"}, "_hasShrinkwrap": false, "publish_time": 1614774985672, "_cnpm_publish_time": 1614774985672, "_cnpmcore_publish_time": "2021-12-16T14:46:14.296Z"}, "6.0.0-beta.5": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.5", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "da994a4926ecaccf0543ad60e0aceb229b7a858a", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.5", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "92354a2888e1bdea02a74a90b4b8be168be8c51f", "size": 65567, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.5.tgz", "integrity": "sha512-xgXIKRQ1CfNuVQQCaXa4lgZZaBdk7LHZlXHwLoz65UCyYqgpQP8ta+em1NX5Gtw/tKZqi5tW3rO0Nvp7FKylCA=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.5_1614767369943_0.9674448627487249"}, "_hasShrinkwrap": false, "publish_time": 1614767370123, "_cnpm_publish_time": 1614767370123, "_cnpmcore_publish_time": "2021-12-16T14:46:14.522Z"}, "6.0.0-beta.4": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.4", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "codesandbox": "^2.2.1", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "0664e6b23e0c93e903b5b471a0522f937916a2fd", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.4", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "2f78e1ad193751b792999499674dc3fdd79849a0", "size": 64861, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.4.tgz", "integrity": "sha512-uM4O46U1XEw4lGcKkI4wRqkrUgFtXs+RIl5DSBWxtqUEUMWur404vz77TULE/LnWji0GhqMLzG0f5w4DW5CweA=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.4_1614702470015_0.1723274675902977"}, "_hasShrinkwrap": false, "publish_time": 1614702470181, "_cnpm_publish_time": 1614702470181, "_cnpmcore_publish_time": "2021-12-16T14:46:14.733Z"}, "6.0.0-beta.3": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.3", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "npm run readme && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "47d3604deec295c63956c455797a2ddf6c717954", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.3", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "db4bb3e79a978f9ce088e2b2992a7e4b547c0f6f", "size": 60600, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.3.tgz", "integrity": "sha512-drTgWdNOf8O+X+2q3CYJP5zlKCxPLYQEYX6Uzr98xrYqhcKYXiVQH4ZpvpzTjpr10Ua0/ks57xUnw6VC2QB9oQ=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.3_1614656383034_0.014265643564518982"}, "_hasShrinkwrap": false, "publish_time": 1614656383208, "_cnpm_publish_time": 1614656383208, "_cnpmcore_publish_time": "2021-12-16T14:46:14.990Z"}, "6.0.0-beta.2": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.2", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run readme && npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "f458e242ec37ba4cb12c3e96454a7157fc31294d", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "b7837eb4fd399246159b777017a40eec975c9713", "size": 60528, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.2.tgz", "integrity": "sha512-/xJXSvNN43nwacpV/oodXgn/Q3bwg5kSKnxVNHYD3XMt5G7jtMg8bPrqcJkyPezfhdgnf6FyxMtnStSzOTJfuw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.2_1614533481806_0.14730957660536936"}, "_hasShrinkwrap": false, "publish_time": 1614533481950, "_cnpm_publish_time": 1614533481950, "_cnpmcore_publish_time": "2021-12-16T14:46:15.288Z"}, "6.0.0-beta.1": {"name": "vue-echarts", "description": "Vue.js component for Apache ECharts.", "version": "6.0.0-beta.1", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "license": "MIT", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run readme && npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.7.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.12 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "c400baf8d8c1f3f3317a40187030dc6f3150bca2", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.0-beta.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "9109b9e6fbd1c6169050f6b8cc0032807dc97fb6", "size": 59730, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-beta.1.tgz", "integrity": "sha512-RAUwUdR+/iwSYJkdMjdUEtvLAnzwLYnhK59j/DNEXKDkcTGhNNupniteDh5Wjlj1f/JS5lqFyl2aFfVIeuNgmA=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-beta.1_1614451807581_0.4216070144328288"}, "_hasShrinkwrap": false, "publish_time": 1614451807791, "_cnpm_publish_time": 1614451807791, "_cnpmcore_publish_time": "2021-12-16T14:46:15.540Z"}, "6.0.0-alpha.5": {"name": "vue-echarts", "version": "6.0.0-alpha.5", "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run readme && npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"core-js": "^3.6.5", "resize-detector": "^0.3.0", "vue-demi": "^0.6.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5", "vue-demi": "^0.6.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.11 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "6629540a8ed44d182a3bdaadb4f9e1ac9ef12b32", "description": "> Vue.js component for Apache ECharts.", "_id": "vue-echarts@6.0.0-alpha.5", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "4a247048bebae65260dffe086698a83cbe51da8c", "size": 58118, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-alpha.5.tgz", "integrity": "sha512-5kWxvmXnWcrdG4bKGhUoPmQgLmHXNTmmLXpfwnkANcTspwW04ooi2DdzS8x1VSHCl7bMGDBoVzZtphN5iB7eMw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-alpha.5_1614082193049_0.43677481474805235"}, "_hasShrinkwrap": false, "publish_time": 1614082193267, "_cnpm_publish_time": 1614082193267, "_cnpmcore_publish_time": "2021-12-16T14:46:15.775Z"}, "6.0.0-alpha.4": {"name": "vue-echarts", "version": "6.0.0-alpha.4", "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run readme && npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"core-js": "^3.6.5", "resize-detector": "^0.3.0", "vue-demi": "^0.6.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5", "vue-demi": "^0.6.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.11 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "39437ba90744f1c48f81ccd228388b062e7461b3", "description": "> Vue.js component for Apache ECharts.", "_id": "vue-echarts@6.0.0-alpha.4", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "4e4167062041beb9088f46673624c7f6fa5d1a05", "size": 56205, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-alpha.4.tgz", "integrity": "sha512-HXa9IA2MogQdI2F1sy+7PINfawPdyE1aIsUJAZSmRNMLvIl5hRWEYq55bj+vCBlQjP2v2Dx4iCz3hwidKy0C6Q=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-alpha.4_1614066773834_0.15834932682720226"}, "_hasShrinkwrap": false, "publish_time": 1614066773972, "_cnpm_publish_time": 1614066773972, "_cnpmcore_publish_time": "2021-12-16T14:46:16.031Z"}, "6.0.0-alpha.3": {"name": "vue-echarts", "version": "6.0.0-alpha.3", "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run readme && npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"core-js": "^3.6.5", "resize-detector": "^0.3.0", "vue-demi": "^0.6.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5", "vue-demi": "^0.6.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.11 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "b2cfcb23695cf9fc5d92bd7129d3481240990daf", "description": "> Vue.js component for Apache ECharts.", "_id": "vue-echarts@6.0.0-alpha.3", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "93ce828094fa8dbc69646d6e8e45d434cae531d1", "size": 55733, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-alpha.3.tgz", "integrity": "sha512-9ddSiWNis5/1bqUA3/d6aCxCqxlFD79rlHfUwJt/MAEwKe9am/WdVjLlmZgq6OVJmn2HDy2aBz7LdXBLIX93cw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-alpha.3_1614008678956_0.9900789195804252"}, "_hasShrinkwrap": false, "publish_time": 1614008679112, "_cnpm_publish_time": 1614008679112, "_cnpmcore_publish_time": "2021-12-16T14:46:16.753Z"}, "6.0.0-alpha.2": {"name": "vue-echarts", "version": "6.0.0-alpha.2", "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run readme && npm run build", "readme": "node -r esm scripts/readme.js"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"core-js": "^3.6.5"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "comment-mark": "^1.0.0", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "esm": "^3.2.25", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "^3.0.5", "vue-demi": "^0.6.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.11 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "9dd913abbe7e10cd172645623898a0e452a8b2b3", "description": "> Vue.js component for Apache ECharts.", "_id": "vue-echarts@6.0.0-alpha.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "01c69a3d526ac78f9f14fff6fc99471ef4dd78f2", "size": 55733, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-alpha.2.tgz", "integrity": "sha512-FRDh99WCu3WmBlXcQeLE+8hiiAEvDrOz4me6/WvmiMNYuMWK50HwLbCVxvPwNgmXgMuEX0hAEzmYGVmBJiDuJQ=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-alpha.2_1613994132765_0.7310718877763083"}, "_hasShrinkwrap": false, "publish_time": 1613994132920, "_cnpm_publish_time": 1613994132920, "_cnpmcore_publish_time": "2021-12-16T14:46:17.003Z"}, "6.0.0-alpha.1": {"name": "vue-echarts", "version": "6.0.0-alpha.1", "scripts": {"serve": "vue-cli-service serve", "build:demo": "vue-cli-service build", "build": "rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "prepare": "npm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "jsdelivr": "dist/index.umd.min.js", "types": "dist/index.d.ts", "dependencies": {"core-js": "^3.6.5"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/composition-api": "^1.0.0-rc.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "echarts": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "postcss": "^8.2.5", "postcss-loader": "^5.0.0", "postcss-nested": "^4.2.3", "prettier": "^1.19.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3", "vue": "npm:vue@^3.0.5", "vue-demi": "^0.6.0"}, "peerDependencies": {"@vue/composition-api": "1.0.0-rc.2", "echarts": "^5.0.2", "vue": "^2.6.11 || ^3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "d2f756f978c81b88b0fc887e438a62175cba6069", "description": "> Vue.js component for Apache ECharts.", "_id": "vue-echarts@6.0.0-alpha.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.14.11", "dist": {"shasum": "c80cf15a17fab20eebef1c570dd4647450dc63b5", "size": 55607, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.0-alpha.1.tgz", "integrity": "sha512-ptdIrdqasSoClVNt5c7f4BVpFGxyOHuCR/eSpJt4nbyQExJ/fbFVVdGxrZkhUhYNApfqnWlqlt+KkZcILlMKbg=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.0-alpha.1_1613985589440_0.26830979549870837"}, "_hasShrinkwrap": false, "publish_time": 1613985589596, "_cnpm_publish_time": 1613985589596, "_cnpmcore_publish_time": "2021-12-16T14:46:17.256Z"}, "5.0.0-beta.0": {"name": "vue-echarts", "version": "5.0.0-beta.0", "description": "ECharts component for Vue.js.", "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "cross-env NODE_ENV=production rollup -c", "lint": "vue-cli-service lint", "demo": "vue-cli-service build", "pub": "sh ./publish.sh"}, "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "dependencies": {"core-js": "^3.4.4", "lodash": "^4.17.15", "resize-detector": "^0.1.10"}, "devDependencies": {"@rollup/plugin-buble": "^0.21.0", "@rollup/plugin-commonjs": "^11.0.1", "@rollup/plugin-node-resolve": "^7.0.0", "@vue/cli-plugin-babel": "^4.1.2", "@vue/cli-plugin-eslint": "^4.1.2", "@vue/cli-service": "^4.1.2", "@vue/eslint-config-standard": "^5.1.0", "babel-eslint": "^10.0.3", "cross-env": "^6.0.3", "echarts": "^4.6.0", "echarts-liquidfill": "^2.0.5", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.1.2", "prettier-eslint": "^9.0.1", "promise-polyfill": "^8.1.3", "raw-loader": "^1.0.0", "rollup": "^1.29.1", "rollup-plugin-terser": "^5.2.0", "rollup-plugin-vue": "^5.1.6", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue": "^2.6.11", "vue-template-compiler": "^2.6.11", "vuex": "^3.1.2"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.4.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "700b4a25f9e2948bf041e776ddd1ed38aac01f83", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@5.0.0-beta.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.13.6", "dist": {"shasum": "438dd4b0fc5ccea281709c1f7c6321b05352bdf4", "size": 15062, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-5.0.0-beta.0.tgz", "integrity": "sha512-QZFKGXDAYFQo+F20REpzcdLx79nsl4kOorJRpN+08aYq4YiIlmtWss1Lxadm7Fo+NYyWm8nnT+h4xHv3uqWIDQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_5.0.0-beta.0_1579837236028_0.8097973225259565"}, "_hasShrinkwrap": false, "publish_time": 1579837236218, "_cnpm_publish_time": 1579837236218, "_cnpmcore_publish_time": "2021-12-16T14:46:17.464Z"}, "4.1.0": {"name": "vue-echarts", "version": "4.1.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.15", "resize-detector": "^0.1.10"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-plugin-eslint": "^3.12.1", "@vue/cli-service": "^3.12.1", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.3", "echarts": "^4.5.0", "echarts-liquidfill": "^2.0.5", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.2.3", "promise-polyfill": "^8.1.3", "raw-loader": "^1.0.0", "rollup": "^1.27.8", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.4", "rollup-plugin-terser": "^4.0.4", "rollup-plugin-vue": "^4.7.2", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue": "^2.6.10", "vue-template-compiler": "^2.6.10", "vuex": "^3.1.2"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "643447d35e3354d37667f7e505629239492eddb5", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.1.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.13.1", "dist": {"shasum": "ff4828aaa599e7aaaac95e35297d964192a3af0d", "size": 15264, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.1.0.tgz", "integrity": "sha512-am2vsAjEYGz3JqqMaSqIR35HIxr/TjxYPoKWTorgi10rzwl7+f+uBvpj7AkCJ6HVL8zR3exYpyo881aG+24Rfg=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.1.0_1575623480006_0.17195332303369248"}, "_hasShrinkwrap": false, "publish_time": 1575623480191, "_cnpm_publish_time": 1575623480191, "_cnpmcore_publish_time": "2021-12-16T14:46:17.710Z"}, "4.0.4": {"name": "vue-echarts", "version": "4.0.4", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.15", "resize-detector": "^0.1.10"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.8.0", "@vue/cli-plugin-eslint": "^3.8.0", "@vue/cli-service": "^3.8.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.3.0", "echarts-liquidfill": "^2.0.5", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.2.2", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.14.4", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.4", "rollup-plugin-terser": "^4.0.4", "rollup-plugin-vue": "^4.7.2", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.6.10", "vue-template-compiler": "^2.6.10", "vuex": "^3.1.1"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "33ac3495cd1b437c8374e7d594e94e3ffba9f443", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.4", "_nodeVersion": "10.15.0", "_npmVersion": "6.11.3", "dist": {"shasum": "25c2cc2379b6d8a20ef63ac8f5203df5d07098aa", "size": 15031, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.4.tgz", "integrity": "sha512-AemPvRAJlu5JCDKcfVfyNSQ4XipiT3gjy5v+VCLuHzNGUhxnQqBdh7ohaUKzuWBOoKBym3tZL7U9FyWIVPkPHw=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.4_1568711935318_0.957463677295237"}, "_hasShrinkwrap": false, "publish_time": 1568711935450, "_cnpm_publish_time": 1568711935450, "_cnpmcore_publish_time": "2021-12-16T14:46:18.095Z"}, "4.0.3": {"name": "vue-echarts", "version": "4.0.3", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.11", "resize-detector": "^0.1.10"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.7.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.2.0-rc.2", "echarts-liquidfill": "^2.0.4", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-terser": "^4.0.2", "rollup-plugin-vue": "^4.6.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.5.21", "vue-template-compiler": "^2.5.21", "vuex": "^3.1.0"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "54d542c4cb90d6bb2e9ac78f6e841d4fdd6d13af", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.3", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "4eee27ebadd327bb59e61a95ea35d716da8fa80d", "size": 14923, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.3.tgz", "integrity": "sha512-voU1VoQWujryfsfZIjGvW1YzoFQ6RWMFpE/LaD/jo51P5c/6QOAMwM+BytG8tgjRzhJQANLZ49kSd3J3vDvM2g=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.3_1558061891810_0.9699754897087329"}, "_hasShrinkwrap": false, "publish_time": 1558061891982, "_cnpm_publish_time": 1558061891982, "_cnpmcore_publish_time": "2021-12-16T14:46:18.298Z"}, "4.0.2": {"name": "vue-echarts", "version": "4.0.2", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.11", "resize-detector": "^0.1.9"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.2.0-rc.2", "echarts-liquidfill": "^2.0.4", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-terser": "^4.0.2", "rollup-plugin-vue": "^4.6.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.5.21", "vue-template-compiler": "^2.5.21", "vuex": "^3.1.0"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "a0b8d4def122eb15af54da9736d962897b01cccd", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.2", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "fa60bbbed90f25e26a7940fa549b034f20a8845d", "size": 14910, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.2.tgz", "integrity": "sha512-q/QGAEPdt3wrlcyy1hDDa4CAbu9d7/0+CZH8b/9hf99V/yaMGQIFe8YUCQoT3S/PcjT11ueD+/N3OQF1+Fx/3Q=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.2_1556270726320_0.8568361402016815"}, "_hasShrinkwrap": false, "publish_time": 1556270726423, "_cnpm_publish_time": 1556270726423, "_cnpmcore_publish_time": "2021-12-16T14:46:18.502Z"}, "4.0.1": {"name": "vue-echarts", "version": "4.0.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.11", "resize-detector": "^0.1.9"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.2.0-rc.2", "echarts-liquidfill": "^2.0.4", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-terser": "^4.0.2", "rollup-plugin-vue": "^4.6.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.5.21", "vue-template-compiler": "^2.5.21", "vuex": "^3.1.0"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "bbaeb92487d4476aff8d939b36b538e4e370c825", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.1", "_npmVersion": "6.5.0", "_nodeVersion": "9.11.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "3d3a14f53119701bba09545a68b17cf0689cad29", "size": 14586, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.1.tgz", "integrity": "sha512-JgvIcr/btz4bAkafVm0aLyfkAdhGf2T8VIdESd2264Mlr5w0dZ0IOG3BJCvtznIlBJMiwQXAztc27Y3KGKdF5Q=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.1_1548123983934_0.13413974517742533"}, "_hasShrinkwrap": false, "publish_time": 1548123984045, "_cnpm_publish_time": 1548123984045, "_cnpmcore_publish_time": "2021-12-16T14:46:18.710Z"}, "4.0.0": {"name": "vue-echarts", "version": "4.0.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.11", "resize-detector": "^0.1.9"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.2.0-rc.2", "echarts-liquidfill": "^2.0.4", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-terser": "^4.0.2", "rollup-plugin-vue": "^4.6.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.5.21", "vue-template-compiler": "^2.5.21", "vuex": "^3.1.0"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "3292673e2e2d80d6c4dc2fb331063926007a67b1", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "9.11.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "09baf5b1a25170149a27edbbcde11cf20b31a5cc", "size": 14564, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.0.tgz", "integrity": "sha512-5qClIdRVRFhS60g/LJLddplqtMti9uORpZ/9gXJDFSLcLBx8V2IhjhG47McltT5K167gUafvx+4zCi3iNtbs9A=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.0_1547914412229_0.39480488630984634"}, "_hasShrinkwrap": false, "publish_time": 1547914412373, "_cnpm_publish_time": 1547914412373, "_cnpmcore_publish_time": "2021-12-16T14:46:18.944Z"}, "4.0.0-beta.1": {"name": "vue-echarts", "version": "4.0.0-beta.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.11", "resize-detector": "^0.1.9"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.2.0-rc.2", "echarts-liquidfill": "^2.0.4", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-terser": "^4.0.2", "rollup-plugin-vue": "^4.6.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.5.21", "vue-template-compiler": "^2.5.21", "vuex": "^3.1.0"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "d466b792731bd7db60ae62013958c83c5fdb5998", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.0-beta.1", "_npmVersion": "6.5.0", "_nodeVersion": "9.11.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "d31450f7791dadd0cb0e31c2b05d531ad1356ac2", "size": 14730, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.0-beta.1.tgz", "integrity": "sha512-ADhyZCQj6jt4FO4c/Juom7L+X/fL5lAVszAfcSUI+tYUavwE1H0Mq4MzMp8Qh/MEsmzo2PiSQDO/iPpxTHrBgQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.0-beta.1_1547811097045_0.7606578168151823"}, "_hasShrinkwrap": false, "publish_time": 1547811097219, "_cnpm_publish_time": 1547811097219, "_cnpmcore_publish_time": "2021-12-16T14:46:19.160Z"}, "4.0.0-beta.0": {"name": "vue-echarts", "version": "4.0.0-beta.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "module": "components/ECharts.vue", "scripts": {"serve": "vue-cli-service serve", "demo": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"lodash": "^4.17.11", "resize-detector": "^0.1.9"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "echarts": "^4.2.0-rc.2", "echarts-liquidfill": "^2.0.4", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "promise-polyfill": "^8.1.0", "raw-loader": "^1.0.0", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-terser": "^4.0.2", "rollup-plugin-vue": "^4.6.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.5.21", "vue-template-compiler": "^2.5.21", "vuex": "^3.1.0"}, "peerDependencies": {"echarts": "^4.1.0", "vue": "^2.2.6"}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "gitHead": "826ae4fe881fe9ed22e3f6a7fd87b75e6c739fc0", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@4.0.0-beta.0", "_npmVersion": "6.5.0", "_nodeVersion": "9.11.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "a17a800707a1430a2e7f814feb6bc619a193707e", "size": 14727, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-4.0.0-beta.0.tgz", "integrity": "sha512-GNIA+t/WYhoV2LAPx7fSE0DYRk7QcE7K9VJVW6KttmxUTWynEKFd52P+ZmFXS5fnMiAUpUrOlEpgLVmsTLISaQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_4.0.0-beta.0_1547810300961_0.06900305617879177"}, "_hasShrinkwrap": false, "publish_time": 1547810301086, "_cnpm_publish_time": 1547810301086, "_cnpmcore_publish_time": "2021-12-16T14:46:19.378Z"}, "3.1.3": {"name": "vue-echarts", "version": "3.1.3", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.1.0", "lodash": "^4.17.10", "resize-detector": "^0.1.7"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^2.0.2", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.18.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0", "zrender": "^4.0.4"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "0887a33b455360056b45c6fb87ba4130f86c2588", "_id": "vue-echarts@3.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "a65de7fe31e2d18bf38928ccde83e144667ea9e6", "size": 260769, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.1.3.tgz", "integrity": "sha512-qAGWNnTQ8rXN/Q4/krfcOpHmmlRfAXaT8penBiG6+USnYQo8W3Misjw2RezSfjCGrDcDkrylScpufNu8dwRFRA=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.1.3_1537868503618_0.10375583241269393"}, "_hasShrinkwrap": false, "publish_time": 1537868503840, "_cnpm_publish_time": 1537868503840, "_cnpmcore_publish_time": "2021-12-16T14:46:19.913Z"}, "3.1.2": {"name": "vue-echarts", "version": "3.1.2", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.1.0", "lodash": "^4.17.10", "resize-detector": "^0.1.7"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^2.0.2", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.18.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0", "zrender": "^4.0.4"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "826550c8048fe78e0da9d47f7d225ae71fc38979", "_id": "vue-echarts@3.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "cc51c4e321d7cb1b14cfd044e04d7bae86fe17cc", "size": 665649, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.1.2.tgz", "integrity": "sha512-beqezTLQiUi90q3ELdIkQGU2PQINsGvDDgoyXdNRy95lTHoMZ00h7U4IsPw1nVhPRfAaz49wk1yFTZhklxMtbA=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.1.2_1535910633992_0.32751399359437094"}, "_hasShrinkwrap": false, "publish_time": 1535910634209, "_cnpm_publish_time": 1535910634209, "_cnpmcore_publish_time": "2021-12-16T14:46:20.219Z"}, "3.1.1": {"name": "vue-echarts", "version": "3.1.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.1.0", "lodash": "^4.17.10", "resize-detector": "^0.1.7"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^2.0.2", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.18.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0", "zrender": "^4.0.4"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "c42231993f57f34c06a680c290feb494fadbbdc2", "_id": "vue-echarts@3.1.1", "_npmVersion": "6.4.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "00568c2b16dce28f7fb442653854054e66d48adc", "size": 664898, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.1.1.tgz", "integrity": "sha512-aY+kbVQIdYLiZdtl6s0LHD07x6EefTPqzCZ6zpYV+ruHr+y6bBY8ixi8cx08xTfxejiw7AXRFdm3kHZEVAQ7Jg=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.1.1_1535117240703_0.8866503959798284"}, "_hasShrinkwrap": false, "publish_time": 1535117240870, "_cnpm_publish_time": 1535117240870, "_cnpmcore_publish_time": "2021-12-16T14:46:20.981Z"}, "3.1.0": {"name": "vue-echarts", "version": "3.1.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.1.0", "lodash": "^4.17.10", "resize-detector": "^0.1.7"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^2.0.2", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.18.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0", "zrender": "^4.0.4"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "5360450b4d9f994f9b3cd2d8cbe52fa44feccf7b", "_id": "vue-echarts@3.1.0", "_npmVersion": "6.4.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "f347a904123bcd99cbd877908f133c1f3d981bbc", "size": 664882, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.1.0.tgz", "integrity": "sha512-TOarpX1UeZtwMMmnDnHoWiZATtU3QlC7VabMpLRmOND47F12EBtVaSdSnSKRKdhuBhIQHT0rkhwwwMYf6lpdIQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.1.0_1535041343891_0.5307102270803605"}, "_hasShrinkwrap": false, "publish_time": 1535041344071, "_cnpm_publish_time": 1535041344071, "_cnpmcore_publish_time": "2021-12-16T14:46:21.306Z"}, "3.0.9": {"name": "vue-echarts", "version": "3.0.9", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.4", "lodash": "^4.17.10", "resize-detector": "^0.1.7"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.18.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "92bad85cc4b2da83d7fff461047da9a07c22d020", "_id": "vue-echarts@3.0.9", "_npmVersion": "6.1.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "bad7bdef12a1361a463f90ba57319f40afcd60ac", "size": 258651, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.9.tgz", "integrity": "sha512-kh509jyQN/fmnYIeHa2hq8Q65bgixlcQlMBDeOSeV+9kOno2kJ0ese0QEQARhCZ8cWCPqaxWfADM0/C8Cp5uzA=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.9_1529394513123_0.2578231028902238"}, "_hasShrinkwrap": false, "publish_time": 1529394513255, "_cnpm_publish_time": 1529394513255, "_cnpmcore_publish_time": "2021-12-16T14:46:21.552Z"}, "3.0.8": {"name": "vue-echarts", "version": "3.0.8", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.4", "lodash": "^4.17.4", "resize-detector": "^0.1.6"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "fd07d5531444b8d1775d61cef728201f6e1f4c60", "_id": "vue-echarts@3.0.8", "_npmVersion": "6.1.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "b4e4449bd0cc18ea7f3f59e632fc46ef0091d0f2", "size": 258607, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.8.tgz", "integrity": "sha512-cWZ/A13xB1+RPXqSu+stkz8m34OFz/DoZVptrcCdZiqVu2cYpfo04lXExXMz1rlDUS6HdgKCqYJIUdZrD6qhtQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "ecomfe-core"}, {"email": "<EMAIL>", "name": "justineo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.8_1528947051470_0.20301840790683623"}, "_hasShrinkwrap": false, "publish_time": 1528947051598, "_cnpm_publish_time": 1528947051598, "_cnpmcore_publish_time": "2021-12-16T14:46:22.283Z"}, "3.0.7": {"name": "vue-echarts", "version": "3.0.7", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.4", "lodash": "^4.17.4", "resize-detector": "^0.1.6"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "58740f29211ed82ff1839bd252b5b36ef8db08ae", "_id": "vue-echarts@3.0.7", "_npmVersion": "5.7.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "d1ce3a51156c7a83799e575ccbae53f7524eb655", "size": 258010, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.7.tgz", "integrity": "sha512-AcY29diBP39dm+uJeLdBxo/VzmQ/n5/okD83i0sbetNrShiLl24oUi1E6tiATgI6PTQd9G92HhBv9IdCgMCj3g=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.7_1522587176063_0.5728519755618795"}, "_hasShrinkwrap": false, "publish_time": 1522587176158, "_cnpm_publish_time": 1522587176158, "_cnpmcore_publish_time": "2021-12-16T14:46:22.596Z"}, "3.0.6": {"name": "vue-echarts", "version": "3.0.6", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.4", "lodash": "^4.17.4", "resize-detector": "^0.1.6"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "9c8bcab5d3454079562abc3b98cc64ba290b1cd4", "_id": "vue-echarts@3.0.6", "_npmVersion": "5.7.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "47b92d6b630ee1d1e597e2892c205370e3fa664a", "size": 257823, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.6.tgz", "integrity": "sha512-Cr4gJ7/3REeM9wyJI74cxfB2oZjzzg+D+E17sGXlIBSQ7iEhYxt9CE4zOPomTi6iDJAzfzNexr0SBVrk5ESvfA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.6_1522569986418_0.29810354696824604"}, "_hasShrinkwrap": false, "publish_time": 1522569986973, "_cnpm_publish_time": 1522569986973, "_cnpmcore_publish_time": "2021-12-16T14:46:23.046Z"}, "3.0.5": {"name": "vue-echarts", "version": "3.0.5", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.4", "lodash": "^4.17.4", "resize-detector": "^0.1.5"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "9c8bcab5d3454079562abc3b98cc64ba290b1cd4", "_id": "vue-echarts@3.0.5", "_npmVersion": "5.7.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "40dfa70e26e5aae30a59c3546760cdc48676a774", "size": 257748, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.5.tgz", "integrity": "sha512-NJUeGB56caueUnmcDKUOAPHvqpf0Pe+U2u1JZXYKdcLghwK9SVpDrsSdyKWzlZ6ViO+S1tq0sOYQf5fwa9b4hw=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.5_1521264530721_0.13174498954979263"}, "_hasShrinkwrap": false, "publish_time": 1521264530803, "_cnpm_publish_time": 1521264530803, "_cnpmcore_publish_time": "2021-12-16T14:46:23.356Z"}, "3.0.4": {"name": "vue-echarts", "version": "3.0.4", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.4", "lodash": "^4.17.4", "resize-detector": "^0.1.2"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "88456004445657f45538ae8ddf08e99b3ecd326b", "_id": "vue-echarts@3.0.4", "_npmVersion": "5.7.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "63b3ef5fc19cdf5e45e251d3f442069d0836013e", "size": 257679, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.4.tgz", "integrity": "sha512-Netpqa/w3kOWeGNJm9tlOSrt63/m0L9W9DIWUoVggxXWyaA/5Dun1VY41K5YFGDkbVKJNC9pAB7ezyesvjIQFg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.4_1520588246738_0.29959525226268013"}, "_hasShrinkwrap": false, "publish_time": 1520588246904, "_cnpm_publish_time": 1520588246904, "_cnpmcore_publish_time": "2021-12-16T14:46:23.675Z"}, "3.0.3": {"name": "vue-echarts", "version": "3.0.3", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.2", "lodash": "^4.17.4", "resize-detector": "^0.1.2"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "d1533679df9635012102df4c1491058c0aa5b09e", "_id": "vue-echarts@3.0.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "7de8d144e44ff7d9c6bc8753cc75a44873316284", "size": 255866, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.3.tgz", "integrity": "sha512-srqkiEsFIBv6i0FkdWMp9F8uTqeVgTsbW2jhUXe6w7je6swD7gYcW1zy9Mf1XX041wZqlXjVQgJsNHIQ7GUExg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.3_1519321730141_0.3528252458459449"}, "_hasShrinkwrap": false, "publish_time": 1519321730231, "_cnpm_publish_time": 1519321730231, "_cnpmcore_publish_time": "2021-12-16T14:46:24.404Z"}, "3.0.2": {"name": "vue-echarts", "version": "3.0.2", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.2", "lodash": "^4.17.4", "resize-detector": "0.0.5"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "qs": "^6.5.1", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "7f4cbfa64663d1787462c4e5549e6ee899948643", "_id": "vue-echarts@3.0.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "dist": {"shasum": "d5e33bc4e2027909f73f3e4e29ba7a36372edaae", "size": 255222, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.2.tgz", "integrity": "sha512-iZnYDeyPQ3J4wlWaHv2H1YXj2KjbkCbj+HQ7VNCM99IR4n0/SsfZqCYGBhvOzIgzBXi06sOJCCeHK2gSeqPihA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_3.0.2_1518272962733_0.48580820082185316"}, "_hasShrinkwrap": false, "publish_time": 1518272963092, "_cnpm_publish_time": 1518272963092, "_cnpmcore_publish_time": "2021-12-16T14:46:24.649Z"}, "3.0.1": {"name": "vue-echarts", "version": "3.0.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.1", "lodash": "^4.17.4", "resize-detector": "0.0.5"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "87709fafdc01c8f95e402adedadfd35d85e4fd6a", "_id": "vue-echarts@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "874171cc44ceba3ef62e3df0df2192925765d055", "size": 255184, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.1.tgz", "integrity": "sha512-kz84OYxUoiJlaI2nQvd71gfifPNHQvzIWfC4zSNGcb4VksbEcQEpnIlg3EF43lOEOjPrO4ZDgLzARSJgtfMrgQ=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-3.0.1.tgz_1516241231195_0.01801501354202628"}, "directories": {}, "publish_time": 1516241231336, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516241231336, "_cnpmcore_publish_time": "2021-12-16T14:46:24.884Z"}, "3.0.0": {"name": "vue-echarts", "version": "3.0.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^4.0.1", "lodash": "^4.17.4", "resize-detector": "0.0.5"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eslint-plugin-vue": "^4.2.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "586c0d42a2e0acbabf387bb01512fdc276852400", "_id": "vue-echarts@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "ac35b8ae170f45e3a0b982894a7d44e7f9822266", "size": 253734, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-3.0.0.tgz", "integrity": "sha512-dFgEh/8WXFZjIC8xuRz47omPqiwiShooj9/whgwKDSLguGpAVHFSIXAUjqoyf/sZZEOP8rZ6SYEPVQ9zOj7yKA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-3.0.0.tgz_1516212922279_0.3934409429784864"}, "directories": {}, "publish_time": 1516212922437, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516212922437, "_cnpmcore_publish_time": "2021-12-16T14:46:25.563Z"}, "2.6.0": {"name": "vue-echarts", "version": "2.6.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.8.5", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.1.1", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "a831be0db6335be7b818720ef308de38cfc4dcdc", "_id": "vue-echarts@2.6.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "dist": {"shasum": "64a08af57933a099c38c53b70f4cb4cd1ff6f29f", "size": 232324, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.6.0.tgz", "integrity": "sha512-2kth1/dxbHg00hd3cjXHC/zkhSmkKwxOnKdqoZb17YNL1I/oX8wii9UdCNE5gh+V4A8d3Jx8StU/KEiJiB0pFQ=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-2.6.0.tgz_1513582629345_0.29308717930689454"}, "directories": {}, "publish_time": 1513582629532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513582629532, "_cnpmcore_publish_time": "2021-12-16T14:46:26.086Z"}, "2.5.1": {"name": "vue-echarts", "version": "2.5.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.8.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.0.5", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "38f8f9602ca9d6bffdce69b8a9fbe91c1f63a741", "_id": "vue-echarts@2.5.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "7616fc6d3bde559bd76aa47790b11a3c9ff5e784", "size": 231948, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.5.1.tgz", "integrity": "sha512-+Sf0j3JLSKQBx5+9GW9V7MDnnfM4FXekjvAmXdELBIjfNQ0UvhOCPCA4OpxqaKfH7rVkqN9HBrkeJDNgoTweMw=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-2.5.1.tgz_1510296395671_0.2524506060872227"}, "directories": {}, "publish_time": 1510296395802, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510296395802, "_cnpmcore_publish_time": "2021-12-16T14:46:26.547Z"}, "2.5.0": {"name": "vue-echarts", "version": "2.5.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.7.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.0.5", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.17.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue": "^2.5.3", "vue-loader": "^13.4.0", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.5.3", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "b2451ce4b8b5938e2435511b007989cb5e4c782a", "_id": "vue-echarts@2.5.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "6eaa9d3ec576e85cd39b1738e053ee21a7fa0d32", "size": 222160, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.5.0.tgz", "integrity": "sha512-x/U2nImS4kfpo/DUzhdcFcIkpPPLHUhQTjeGN94VKaB5x9SvjulEAGHfo2DakhtdX7pkJm2SHktQlO8LhMlvHw=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-2.5.0.tgz_1510116717471_0.30918579548597336"}, "directories": {}, "publish_time": 1510116718737, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510116718737, "_cnpmcore_publish_time": "2021-12-16T14:46:26.941Z"}, "2.4.1": {"name": "vue-echarts", "version": "2.4.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.6.0", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.0.5", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^1.0.2", "rollup-plugin-vue": "^2.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue-loader": "^11.3.4", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.2.6", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "a272a3bd3bbc542cdb0c4a6df09fb2c120068879", "_id": "vue-echarts@2.4.1", "_shasum": "138ec92a03fd08e87e9cf9d2a5e71f6a907f971a", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "138ec92a03fd08e87e9cf9d2a5e71f6a907f971a", "size": 215902, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.4.1.tgz", "integrity": "sha512-YU9SfCajKGQnG3AByBQqHsbWk8qDrqI8z2Oxa58F1B0DlZqLaW+XYvRpWdlFigbsfL4PuhxQpy/Uca3OFD3MQA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-2.4.1.tgz_1505716281164_0.4464953711722046"}, "directories": {}, "publish_time": 1505716281244, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505716281244, "_cnpmcore_publish_time": "2021-12-16T14:46:27.262Z"}, "2.4.0": {"name": "vue-echarts", "version": "2.4.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.6.0", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.0.5", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^1.0.2", "rollup-plugin-vue": "^2.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue-loader": "^11.3.4", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.2.6", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "9a6d937dd2179d23dc746be8fce59898ace2170e", "_id": "vue-echarts@2.4.0", "_shasum": "28654069ea4cf9ac7426ab0f5f723c77b8cd5f36", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "dist": {"shasum": "28654069ea4cf9ac7426ab0f5f723c77b8cd5f36", "size": 215700, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.4.0.tgz", "integrity": "sha512-sc3kphGUCxUuUh2kbnSxek6HylRUgp+mTIWl6WELQiFmd+axzDMr6bCsGfihLxL1q0D1Id9sJsctrHXXMv1S0Q=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-2.4.0.tgz_1495871706145_0.7029106097761542"}, "directories": {}, "publish_time": 1495871706261, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495871706261, "_cnpmcore_publish_time": "2021-12-16T14:46:27.575Z"}, "2.3.9": {"name": "vue-echarts", "version": "2.3.9", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "pub": "sh ./publish.sh"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.6.0", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.0.5", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^1.0.2", "rollup-plugin-vue": "^2.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue-loader": "^11.3.4", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.2.6", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "9a6d937dd2179d23dc746be8fce59898ace2170e", "_id": "vue-echarts@2.3.9", "_shasum": "ffd893e4a2ad2d52121c91b3fd5fdbae1cfa5803", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "dist": {"shasum": "ffd893e4a2ad2d52121c91b3fd5fdbae1cfa5803", "size": 215452, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.9.tgz", "integrity": "sha512-7SecUebZxmu/ALZFXxA185xxvdXlLPNytSm/QcGeRxxznJ0Y3tuYLmgYueSgnMhXejPO1CasUniXNTC1CBjCtg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts-2.3.9.tgz_1495866139483_0.7088936523068696"}, "directories": {}, "publish_time": 1495866139613, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495866139613, "_cnpmcore_publish_time": "2021-12-16T14:46:27.884Z"}, "2.3.8": {"name": "vue-echarts", "version": "2.3.8", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "node build/dev-server.js", "demo": "node build/build.js", "build": "cross-env NODE_ENV=production rollup -c", "prepublish": "npm run demo && npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "echarts-liquidfill": "^1.0.4", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "friendly-errors-webpack-plugin": "^1.1.3", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "raw-loader": "^0.5.1", "rimraf": "^2.6.0", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^1.0.2", "rollup-plugin-vue": "^2.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus-loader": "^3.0.1", "url-loader": "^0.5.8", "vue-loader": "^11.3.4", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.2.6", "vuex": "^2.3.1", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHead": "8006a5d60a8c47e2ee8ab0cafb2b0c8c557ed749", "_id": "vue-echarts@2.3.8", "_shasum": "cfe7c7cb3ba51229e5ac9547bd1f616ef050da42", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.6.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "cfe7c7cb3ba51229e5ac9547bd1f616ef050da42", "size": 209817, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.8.tgz", "integrity": "sha512-1IcAYZUjU8R67uyYxc9V0kHDziZqoR8eEk9I/MC/y4/zQ+cSne2UiImu92W4z9MnNEUCVWD9WNXXKMKECAIaLw=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.8.tgz_1493785225523_0.288112070877105"}, "directories": {}, "publish_time": 1493785225791, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493785225791, "_cnpmcore_publish_time": "2021-12-16T14:46:28.363Z"}, "2.3.7": {"name": "vue-echarts", "version": "2.3.7", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "demo": "cross-env NODE_ENV=production webpack --progress --hide-modules", "build": "cross-env NODE_ENV=production rollup -c", "prepublish": "npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^1.0.2", "rollup-plugin-vue": "^2.3.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^11.0.0", "vue-template-compiler": "^2.2.6", "vuex": "^2.1.1", "webpack": "^2.3.2", "webpack-dev-server": "^2.4.2", "webpack-merge": "^0.14.1"}, "gitHead": "0d10ee7b97a04d3b7380218f2cfdb6c5f9ff68dc", "_id": "vue-echarts@2.3.7", "_shasum": "2862749950f778ffccabe74ef3f766eef05990b9", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "2862749950f778ffccabe74ef3f766eef05990b9", "size": 207735, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.7.tgz", "integrity": "sha512-Q8UEGW7O00wN2S8ToH+JIeJOa3YlmNyTC4Hoi7NTySv5YnCyR9uOsINaqaKfYz7vuYb9TRYpTMUTOpBty5msow=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.7.tgz_1492873165354_0.4607285591773689"}, "directories": {}, "publish_time": 1492873165621, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492873165621, "_cnpmcore_publish_time": "2021-12-16T14:46:29.123Z"}, "2.3.6": {"name": "vue-echarts", "version": "2.3.6", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* .", "publish": "rm -rf ./components && rm index.js"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^11.0.0", "vue-template-compiler": "^2.2.6", "vuex": "^2.1.1", "webpack": "^2.3.2", "webpack-dev-server": "^2.4.2", "webpack-merge": "^0.14.1"}, "gitHead": "0d10ee7b97a04d3b7380218f2cfdb6c5f9ff68dc", "_id": "vue-echarts@2.3.6", "_shasum": "6d9472673ca567834a21d442862b99f2c1d99057", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "6d9472673ca567834a21d442862b99f2c1d99057", "size": 189050, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.6.tgz", "integrity": "sha512-p9t8KoPoy7LDBPjzOYuS0h4ceIvvtVIlE9IEIQr46qhcVUc+wDFDKMDVM9vW/aevXXhRkrChqUB7uERlr1/qKg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.6.tgz_1492770731081_0.7796500867698342"}, "directories": {}, "publish_time": 1492770731333, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492770731333, "_cnpmcore_publish_time": "2021-12-16T14:46:29.530Z"}, "2.3.5": {"name": "vue-echarts", "version": "2.3.5", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* .", "publish": "rm -rf ./components && rm index.js"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^11.0.0", "vue-template-compiler": "^2.2.6", "vuex": "^2.1.1", "webpack": "^2.3.2", "webpack-dev-server": "^2.4.2", "webpack-merge": "^0.14.1"}, "gitHead": "0d10ee7b97a04d3b7380218f2cfdb6c5f9ff68dc", "_id": "vue-echarts@2.3.5", "_shasum": "664a389dbc7ea47db6b3c982e6ac56570e2b3395", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "664a389dbc7ea47db6b3c982e6ac56570e2b3395", "size": 189031, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.5.tgz", "integrity": "sha512-dU9V9mywl0VPk6JHb/5MHyzv7SUBpbzk6UejcSgcy0x2uaVg4obXfUmZQgZautYbNm7Wlqn8TbuXskNUhAVdlg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.5.tgz_1492766385297_0.8952726181596518"}, "directories": {}, "publish_time": 1492766387477, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492766387477, "_cnpmcore_publish_time": "2021-12-16T14:46:29.936Z"}, "2.3.4": {"name": "vue-echarts", "version": "2.3.4", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* .", "publish": "rm -rf ./components && rm index.js"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8"}, "peerDependencies": {"vue": "^2.2.6"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^11.0.0", "vue-template-compiler": "^2.2.6", "vuex": "^2.1.1", "webpack": "^2.3.2", "webpack-dev-server": "^2.4.2", "webpack-merge": "^0.14.1"}, "gitHead": "edc7f6a9915f70ea52f418d9d1c4c9de11449a22", "_id": "vue-echarts@2.3.4", "_shasum": "f58a14a76acf336fe8b935fb5793a8a4f8341210", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "f58a14a76acf336fe8b935fb5793a8a4f8341210", "size": 188982, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.4.tgz", "integrity": "sha512-q3Tj1g3tPDKuQKMfpfMk/x5uitj9eEKkwXLIVsyXNyPMxVgAaUEg1xf4I8ZrsLDkSaG2op2pIrXu/atCEhkpaw=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.4.tgz_1492499644745_0.5837817378342152"}, "directories": {}, "publish_time": 1492499646997, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492499646997, "_cnpmcore_publish_time": "2021-12-16T14:46:30.313Z"}, "2.3.3": {"name": "vue-echarts", "version": "2.3.3", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* .", "publish": "rm -rf ./components && rm index.js && rm util.js"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "vuex": "^2.1.1", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "c55f392cd813a56ce6d0a28f5cd2c88aeef3733f", "_id": "vue-echarts@2.3.3", "_shasum": "37bd2e36dfba58c4ca42b770fea155ec464a28cb", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "37bd2e36dfba58c4ca42b770fea155ec464a28cb", "size": 193806, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.3.tgz", "integrity": "sha512-6vrMdAHq61f/eHrtSLSTqZMs8TCepp/CjdQp1y8RulSzxZY27oEQHGmunVlWTfj+PEvfxcQALogkiNcKOBOVlA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.3.tgz_1488947540706_0.6142152238171548"}, "directories": {}, "publish_time": 1488947540995, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488947540995, "_cnpmcore_publish_time": "2021-12-16T14:46:30.662Z"}, "2.3.2": {"name": "vue-echarts", "version": "2.3.2", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* . && rm index.js && rm util.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "vuex": "^2.1.1", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "35ce63a7ef3d2ff71004455e47ed5dbd49bc93fb", "_id": "vue-echarts@2.3.2", "_shasum": "2d29973b8de0c1758cf8961a45054b428ffa92e2", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "2d29973b8de0c1758cf8961a45054b428ffa92e2", "size": 193166, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.2.tgz", "integrity": "sha512-Hnr3P+kTZHTQi0c3H488pqo4Kk5qdLunY8LL56FfuYPE+VWJKwj8zzgCL7nRqetI2DCexxTukMKUR4HkKeJc3g=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.2.tgz_1488878958280_0.749187842477113"}, "directories": {}, "publish_time": 1488878958509, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488878958509, "_cnpmcore_publish_time": "2021-12-16T14:46:30.980Z"}, "2.3.1": {"name": "vue-echarts", "version": "2.3.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "stylus": "^0.54.5", "stylus-loader": "^2.4.0", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "vuex": "^2.1.1", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "9b40106ceda0bebdd2640ac220430731a4452173", "_id": "vue-echarts@2.3.1", "_shasum": "c4c7d7f501e4acd58e2424b6be600a1ca65163a7", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "c4c7d7f501e4acd58e2424b6be600a1ca65163a7", "size": 193651, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.1.tgz", "integrity": "sha512-h3m8nv+2Y6s4xUYjVdd9Drywoxm9xj/TDjYWVZYKSLlYFsNQIuMbc5NX16FX4dHNxm/s/Iuv12cPVw7fnTbOCQ=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.1.tgz_1486977614331_0.9904059444088489"}, "directories": {}, "publish_time": 1486977616903, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486977616903, "_cnpmcore_publish_time": "2021-12-16T14:46:31.323Z"}, "2.3.0": {"name": "vue-echarts", "version": "2.3.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "echarts-liquidfill": "^1.0.2", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "vuex": "^2.1.1", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "42f92b96e8915816436673d8633051b7ee6187d6", "_id": "vue-echarts@2.3.0", "_shasum": "9c0a2cdf25c66e3f75f61972005b92038ea336f3", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "9c0a2cdf25c66e3f75f61972005b92038ea336f3", "size": 193257, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.3.0.tgz", "integrity": "sha512-YzYBs/9ujrwqiVLEeyzpg6BFIFVfMmELa9P7NnIFnabNgUfxW46nl5VVqMWm0y/b/B7nCzVR75l1XC8TmOF7tQ=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.3.0.tgz_1486612126618_0.6664523824583739"}, "directories": {}, "publish_time": 1486612128802, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486612128802, "_cnpmcore_publish_time": "2021-12-16T14:46:31.797Z"}, "2.2.0": {"name": "vue-echarts", "version": "2.2.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "lodash.debounce": "^4.0.8", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "json-loader": "^0.5.4", "raw-loader": "^0.5.1", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "4db9294afe81df4baa9c4a77c27e88b71bb7472e", "_id": "vue-echarts@2.2.0", "_shasum": "7110124f5c5ccd70b2f3eb1ce260e48b3ee02e55", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "7110124f5c5ccd70b2f3eb1ce260e48b3ee02e55", "size": 191940, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.2.0.tgz", "integrity": "sha512-9DaoZ0dhfopntqjsqxjSdbQvUvwwqRxQn+PNa13KA3mbS95BhwySr5PU4dR0RyfkTn32Wm7rjSAxJSpddDovww=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.2.0.tgz_1483690854384_0.3549921077210456"}, "directories": {}, "publish_time": 1483690854606, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483690854606, "_cnpmcore_publish_time": "2021-12-16T14:46:32.171Z"}, "2.1.0": {"name": "vue-echarts", "version": "2.1.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "b36665019654f6c0d4a350eb564c83560417b463", "_id": "vue-echarts@2.1.0", "_shasum": "63705dc0159cf00248530a926cf6e4993df34bac", "_from": ".", "_npmVersion": "3.8.5", "_nodeVersion": "4.0.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "63705dc0159cf00248530a926cf6e4993df34bac", "size": 190870, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.1.0.tgz", "integrity": "sha512-xbMu9N1rNChj3OQGJuZ6rfco0LejbDsRSXt+GDIwZ1zvYt88sj1HREv2wlkNVmVbS9a1xaDqXK4JKp6o7cT0IQ=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.1.0.tgz_1482313219128_0.38878077710978687"}, "directories": {}, "publish_time": 1482313219358, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482313219358, "_cnpmcore_publish_time": "2021-12-16T14:46:32.485Z"}, "2.0.0": {"name": "vue-echarts", "version": "2.0.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "prepublish": "npm run build && cp -r ./src/* . && rm index.js", "publish": "rm -rf ./components"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "dependencies": {"echarts": "^3.3.2", "vue": "^2.0.1"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "cross-env": "^3.1.1", "css-loader": "^0.25.0", "file-loader": "^0.9.0", "font-awesome": "^4.7.0", "font-awesome-svg": "^0.4.0", "vue-loader": "^10.0.2", "vue-template-compiler": "^2.1.4", "webpack": "2.1.0-beta.22", "webpack-dev-server": "2.1.0-beta.10", "webpack-merge": "^0.14.1"}, "gitHead": "b81a333905737d5e59d5d1b5d980f17c77aacf38", "_id": "vue-echarts@2.0.0", "_shasum": "71f4c48ebe7719ac763c13964368d52c3a636b72", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "71f4c48ebe7719ac763c13964368d52c3a636b72", "size": 190683, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-2.0.0.tgz", "integrity": "sha512-qY7tec40fzorUM1chSIlHJME9UlwlDtvO1DuHqmMnjebE8re3MicnGJebaOBMjxRccue2KiPrirZJzJacc41jA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-2.0.0.tgz_1480863301334_0.907468032091856"}, "directories": {}, "publish_time": 1480863303668, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480863303668, "_cnpmcore_publish_time": "2021-12-16T14:46:33.113Z"}, "1.0.0": {"name": "vue-echarts", "version": "1.0.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "webpack-dev-server --inline --hot", "build": "webpack -p"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-core": "^6.7.2", "babel-loader": "^6.2.4", "babel-plugin-transform-runtime": "^6.6.0", "babel-preset-es2015": "^6.6.0", "css-loader": "^0.23.1", "vue-hot-reload-api": "^1.3.2", "vue-html-loader": "^1.2.0", "vue-loader": "^8.2.1", "vue-style-loader": "^1.0.0", "webpack": "^1.12.14", "webpack-dev-server": "^1.14.1"}, "dependencies": {"echarts": "^3.3.2", "vue": "^1.0.17"}, "gitHead": "0f309438047086a043f363b426da141e9fb46ff7", "_id": "vue-echarts@1.0.0", "_shasum": "4379d353b3340fc2f57c9103ade9d5931f506b93", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "4379d353b3340fc2f57c9103ade9d5931f506b93", "size": 190429, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-1.0.0.tgz", "integrity": "sha512-/FgHH0gWnKvg7byx6JxmFPJnP6W5TNrXaIWWqLVqHn7TXDw3NuGExIYJY89if/3Ho7OQUqkEfESqZ26jtyFNjg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-1.0.0.tgz_1480863134070_0.5029048349242657"}, "directories": {}, "publish_time": 1480863136614, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480863136614, "_cnpmcore_publish_time": "2021-12-16T14:46:33.507Z"}, "0.1.2": {"name": "vue-echarts", "version": "0.1.2", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "webpack-dev-server --inline --hot", "build": "webpack -p"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-core": "^6.7.2", "babel-loader": "^6.2.4", "babel-plugin-transform-runtime": "^6.6.0", "babel-preset-es2015": "^6.6.0", "css-loader": "^0.23.1", "vue-hot-reload-api": "^1.3.2", "vue-html-loader": "^1.2.0", "vue-loader": "^8.2.1", "vue-style-loader": "^1.0.0", "webpack": "^1.12.14", "webpack-dev-server": "^1.14.1"}, "dependencies": {"echarts": "^3.3.1", "vue": "^1.0.17"}, "gitHead": "edc7bd114b83a36ac6aa673f6e9f1a333378e833", "_id": "vue-echarts@0.1.2", "_shasum": "7e4820d074f462de65cf81c6e13c60b8636eb71c", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "7e4820d074f462de65cf81c6e13c60b8636eb71c", "size": 187549, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-0.1.2.tgz", "integrity": "sha512-HXCsSBrZHB6efF0cvUWiSVkFqnlSXvqA+bhnA9QOL8ue7hh9mQB9KPlEYbSnpN+3/DopQ2OluEA7wGhz1UW3gA=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-echarts-0.1.2.tgz_1478880225969_0.7579219094477594"}, "directories": {}, "publish_time": 1478880226503, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478880226503, "_cnpmcore_publish_time": "2021-12-16T14:46:33.875Z"}, "0.1.1": {"name": "vue-echarts", "version": "0.1.1", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "webpack-dev-server --inline --hot", "build": "webpack -p"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-core": "^6.7.2", "babel-loader": "^6.2.4", "babel-plugin-transform-runtime": "^6.6.0", "babel-preset-es2015": "^6.6.0", "css-loader": "^0.23.1", "vue-hot-reload-api": "^1.3.2", "vue-html-loader": "^1.2.0", "vue-loader": "^8.2.1", "vue-style-loader": "^1.0.0", "webpack": "^1.12.14", "webpack-dev-server": "^1.14.1"}, "dependencies": {"echarts": "^3.1.5", "vue": "^1.0.17"}, "gitHead": "d2af62f3c598960eda204adfbfa5b03b9fee35e6", "_id": "vue-echarts@0.1.1", "_shasum": "e6ae5906cbc4dbc7b2630637e7a6a51e687e4678", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "e6ae5906cbc4dbc7b2630637e7a6a51e687e4678", "size": 336213, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-0.1.1.tgz", "integrity": "sha512-ucS3wCmAQrGcVaSE11GrFWAlsOha/Y2LXeApjmGCRjtgM7toGf/YGnyzdrHZ+JNp5fXNbBqfR33iEWRC6AH1gw=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-0.1.1.tgz_1466160971508_0.6953082147520036"}, "directories": {}, "publish_time": 1466160971975, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466160971975, "_cnpmcore_publish_time": "2021-12-16T14:46:34.677Z"}, "0.1.0": {"name": "vue-echarts", "version": "0.1.0", "description": "ECharts component for Vue.js.", "main": "dist/vue-echarts.js", "scripts": {"dev": "webpack-dev-server --inline --hot", "build": "webpack -p"}, "keywords": ["<PERSON><PERSON><PERSON>", "Vue.js"], "author": {"name": "<PERSON><PERSON>", "url": "<EMAIL>"}, "license": "MIT", "devDependencies": {"babel-core": "^6.7.2", "babel-loader": "^6.2.4", "babel-plugin-transform-runtime": "^6.6.0", "babel-preset-es2015": "^6.6.0", "css-loader": "^0.23.1", "vue-hot-reload-api": "^1.3.2", "vue-html-loader": "^1.2.0", "vue-loader": "^8.2.1", "vue-style-loader": "^1.0.0", "webpack": "^1.12.14", "webpack-dev-server": "^1.14.1"}, "dependencies": {"echarts": "^3.1.5", "vue": "^1.0.17"}, "gitHead": "14b7b3a49e2fd76f831f7bc7bc9d5bb242e9f6e8", "_id": "vue-echarts@0.1.0", "_shasum": "6c9e97053459ca311005fd95dc2ee698bec558f7", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "justineo", "email": "<EMAIL>"}, "dist": {"shasum": "6c9e97053459ca311005fd95dc2ee698bec558f7", "size": 335960, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-0.1.0.tgz", "integrity": "sha512-einThoHWFyVIgHt0PWuqVlvexX171GhwQxlBxPXmbd9nVhYaxiyD830lvbxxINjwaViRWiDXzNC/92Tfl810kg=="}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-0.1.0.tgz_1466159024333_0.7210149129386991"}, "directories": {}, "publish_time": 1466159024770, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466159024770, "_cnpmcore_publish_time": "2021-12-16T14:46:35.214Z"}, "0.0.0": {"name": "vue-echarts", "version": "0.0.0", "description": "vue echarts", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "_id": "vue-echarts@0.0.0", "_shasum": "328e57c94e86c4eeb4901328d864a613cf81c178", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "ecomfe", "email": "<EMAIL>"}], "dist": {"shasum": "328e57c94e86c4eeb4901328d864a613cf81c178", "size": 256, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-0.0.0.tgz", "integrity": "sha512-rqGXdedOsL/tCEF5NRmwQRmtHp+DKe+HIDHF2ziIC1tNsazZ2aYcZ3nBJJiXxYoX727OYmhNiOHkGIz7RLypNA=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/vue-echarts-0.0.0.tgz_1458616479153_0.6050214171409607"}, "directories": {}, "publish_time": 1458616479629, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458616479629, "_cnpmcore_publish_time": "2021-12-16T14:46:35.411Z"}, "6.0.1": {"name": "vue-echarts", "version": "6.0.1", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js", "prepare": "npm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.12.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.0-beta.1", "@vue/cli-plugin-eslint": "^5.0.0-beta.1", "@vue/cli-plugin-typescript": "^5.0.0-beta.1", "@vue/cli-service": "^5.0.0-beta.1", "@vue/compiler-sfc": "^3.1.1", "@vue/composition-api": "^1.0.5", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "echarts": "^5.2.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.6.0", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.2.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-styles": "^3.14.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "~4.1.5", "vue": "^3.2.26"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "gitHead": "cdc6ed54d69f252ff4f7775d2eab6d984302c8a9", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.1", "_nodeVersion": "14.17.2", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-MUIiYfaMSHA++WdpWlNqlzqPWdthqWotgNcGId69WZNilFDiQSQt9QTJPqvFEbaoonwUikIIz8KpHM/eKc9G7w==", "shasum": "40ec1ded52622a094652d30be5f10a975cfb32c0", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.1.tgz", "fileCount": 19, "unpackedSize": 296334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4VmmCRA9TVsSAnZWagAALyYP/RT7nJgqOhTyNz8z0Qv8\n4QgHFZHUipH88oWRysipysVWQjtio0PdDz4gfPznvaOqtEM02Bh9vStIx1VT\naItTmjeis+Gs825EjcXwqnYZXkJ1Wt5FmV1AmwBYa7KZoh+QojD8Ba64LB0D\n3mVJqYb0VX0Vvav8U7cukcqZwhT04W3kvaBO0BdZWF+92EvSSdUwZccT8Crr\npL+nOgEz4jfNItP8El+/MyS+d+P4+X/HguKkaBWG+Xr/82no7Za43XKHE/vZ\nrHi5+ot58hYocdgYkf7kr4o3OoQ/GwALejytl390VNd1P8261jNjcgLcltWR\nLCdhWy7uZgO2o7MdN51fGVDUZufdaO+snHuT8fCQWFlCLh/UUlfOuWS+d1m/\n+vu1jXhGCjA5WQkC6Ri98cy7/qPWL8SdWBgb/Q/8MQCK2PDtsl1YMW5LIB47\nNyuXUWGXABS/b6HeNoabpPt3pYNfBwRAXJWeCdMxc9jVWbXtvDQDmVXv7+eO\naZS1paZWSQvRB2+xE4PfGFs7WmTB8VmLI0LW6MZn+gMlWSxFLLiKdv5M06QH\nMTFBUirqUBlAgfL0sss8LnNwei6UwWmbWg7sa7rpSyoQ2/kRakrePqdNYQEe\n/8VJY/GRLr2OxRd2tFQw05LrIil/84sW/KWrcGLYonfmR4c31HP9c2r5rz0a\nuI2p\r\n=5tqM\r\n-----END PGP SIGNATURE-----\r\n", "size": 64493}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.1_1642158502707_0.24845122984085433"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-14T11:26:14.779Z", "hasInstallScript": true}, "6.0.2": {"name": "vue-echarts", "version": "6.0.2", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js && cp src/index.vue2.d.ts dist", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js", "prepare": "npm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.12.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.0-beta.1", "@vue/cli-plugin-eslint": "^5.0.0-beta.1", "@vue/cli-plugin-typescript": "^5.0.0-beta.1", "@vue/cli-service": "^5.0.0-beta.1", "@vue/compiler-sfc": "^3.1.1", "@vue/composition-api": "^1.0.5", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "echarts": "^5.2.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.6.0", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.2.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-styles": "^3.14.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "~4.1.5", "vue": "^3.2.26"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "gitHead": "01460d543d6a4eb89723a8eea4b4e299cbc1e9a1", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.2", "_nodeVersion": "14.17.2", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-9xDokauJtAc389MNKbwi1I0VDmp4Y6ndAJTQ8T9K7H0ffosTe1OJSJbUtkT7/fVLDFzlCcmg2TfAKaMzbpg5yQ==", "shasum": "f6d65c4b328d246da9060ded07b83556c1bef09b", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.2.tgz", "fileCount": 19, "unpackedSize": 292955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5pRhCRA9TVsSAnZWagAANo0P/AkhDooKac9PHHsYAw+3\nE1JunxAmUtoxL26nKe2HWnlGJikF8OOYFuHJl1qlwLR8HACpdRzVTr0Rs3HG\nGzCpOTxzm1ksx5XKxxdujE6XrVV0tO4GUXwl9/3igb7QBDbuinRQAotQ/fpx\nGPPF1rydBOiDVEMkI/DRW37Q68pij037hQ/mUSvDqPUHSlkRkfGmWHlZYiSX\nf43bA7ZAq03TXAvCCt7bQ9Og0QjBAZ9Rr445FIg92qrxgkArT4O4zxCXItoZ\nGAgdyov/q/mNCqciQR4Re6QUtxRLs/x44F4Vq1NXTmaYIDo6hXV1ngSyNwR4\nD+vDy21kM/Yuf+0bdwNr2Jou1yzX9m6fvqDJf7TFCBgt4GXl+HQ/OJR625sX\ntqbddGn+F80707mH2Q+Yf77eDV4yJ6SqAjwxpOUVRLTU0A0htlQG7rYR+51t\nog4TDC7M6VEIUC9/bVjLSELQjZmQTs29aAgApiG3iuWhaEqHjP6EtrrqlinB\nJT+9dDqJfKl6XFhdYez86dx2UKKAoRqQTadIMJYQoSLt1af//EGmCeeHl2ZQ\nLAAPeTFWyphjNAiOCeXwkp/ucMFbcu/47JPVlf3wz/FTKxNUyP/DaUxdE1Vn\n0pcVVzLTROnO+wXklOvfw8x7HR5rEohKkgtc82pkATy1gd0uzipl7L4G9N1s\nwu33\r\n=AeGP\r\n-----END PGP SIGNATURE-----\r\n", "size": 63987}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.2_1642501217328_0.33433480395144755"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-18T10:29:22.750Z", "hasInstallScript": true}, "6.0.3": {"name": "vue-echarts", "version": "6.0.3", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "npm run docs && rimraf dist && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js", "prepare": "npm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.12.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.1.1", "@vue/composition-api": "^1.6.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "echarts": "^5.3.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.6.0", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.2.1", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.38.5", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-styles": "^3.14.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "~4.1.5", "vue": "^3.2.33"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "gitHead": "e51adb12d010d34eac3a1996fb6589f27b917bab", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.0.3", "_nodeVersion": "14.17.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-Nu+qb+szmBFCiVmNSZclquRx2lONGxfJXeppXODBYCl+KAdsP2TIaDwO2wfEFqU5jyxaL4b/qV7IYxswESTSFw==", "shasum": "502eb33f6e41fd088831f720a96154bf0a5c4e72", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.0.3.tgz", "fileCount": 19, "unpackedSize": 293903, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDYg49pK37o6gNrp00lGgXg023J+rQ7MWwsAAjDAg789AiBng353lAGgFgr9qOhxgIpCrUAuVxWNWEj7lIjOJqVHmw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiep93ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHBg//QjdzN89u7DgKTBkPVv8A1p1o64wMVogHzAg3EFpC6Bx1K4LT\r\nq+W4ymYvoRddHmgaGgJpFdcm/iBK9ZyxiyGBt3klquJmhbXbtOczhewOrm1G\r\ntOEV2j2c6mSRiwpSoLUtOy01FGKpFFhIfHeq9s6a9rP2PD9RmHjQAarglulD\r\n7LuEnxcaQWKH/PtLaWyz5TaITl0oRxPKB9OMIC4xzMhtYAPhZYxJycfWvHXl\r\n5Pa604VSOdb3ge+XFS1qK9nJtrYJ2c6KzzfGUx7E7X1dhZ4BA2W57baI6sUJ\r\nENguvxyzazQYTfvlc9FrZ9JOxy/NdjA97qHWIaB2PSXEKPliOQFjgR5clShJ\r\nBGleZEaC4RI/XYjhcoeTPLVasHZf2n7js3VPSYwhuf6LXDCzrCznqR1SATg/\r\nJPMJ5g4FNW9ON5L+q44vNU9itQctWzvRlKFZzOkaAaITNGxZ4sOXv7cJXQMz\r\nirpKwRSS9nnoZpOf+LohXbI/1V3yCpFPJkgv36VANcKBKAMRDan0PoMPI/Nx\r\nxL36virnp5J7C5eo+/TC7nLaYLtU2HT0JIhXdrXT+U/dYpx5uTJu6IU17BDf\r\nNVid1bO51avG5mb8k4o49lC5hY82adX3ZFamIkUCkNjk0v2uRRvNM+ideNoD\r\nFGf+wspLZqHt/l52GI/TxL3hCgAlQbA2C/M=\r\n=kjaC\r\n-----END PGP SIGNATURE-----\r\n", "size": 64235}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.0.3_1652203383543_0.8205770820715508"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-10T18:30:02.903Z", "hasInstallScript": true}, "6.1.0": {"name": "vue-echarts", "version": "6.1.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.12.5"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.6.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.6.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.1.0", "_integrity": "sha512-UkIUX/QNRVgRJ/zPMmYxIT8TRcgo8LIsropb+bQhfJ4uUpHgpwjLy5B/9NVOeFniIrAT8/3SV3JthVbmF4wUqw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/b15117ef438713a9bfefc69d4de736c8/vue-echarts-6.1.0.tgz", "_from": "file:vue-echarts-6.1.0.tgz", "_nodeVersion": "14.17.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-UkIUX/QNRVgRJ/zPMmYxIT8TRcgo8LIsropb+bQhfJ4uUpHgpwjLy5B/9NVOeFniIrAT8/3SV3JthVbmF4wUqw==", "shasum": "880935551e8e2278861a29808724a4f9170ef264", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.1.0.tgz", "fileCount": 19, "unpackedSize": 392377, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKAv4MUrDVnWLFS3HNkW5+wpDWYHbc8qS5M0fg63GYAwIhAKbeRD6p/N07K8KGGlxkVvoClTLGp2KiOqvYyNCl50YQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqcJMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEtg//SkDglAYRNn9c8AwpSWTApk5vLruozuc2PhAB8kZZEaAcDzSR\r\nQXFhx2QQ7f+DkYYTVLRFGZNmE2dtW03gEO7BzVYTL1ahLfWCYgGCOl830LH2\r\n/36m/c0Lr4fairL8N4y9QHmJacLqKtu/8V04FesJrwqMovoFtb2CoDW4Z/x7\r\nGHOfUtKnLG/4w7d6N6WcfyZ2XhmIrPzvUV43K75QedPOOtqOFmEgER00Omud\r\ntdnQRRqSo/lZCdKDvylz1mNbMldsknJss65sKphL/yAVD8HcE0VqzFKQLVx8\r\ndf0BLmIS6CD/A6x1fUoJgCculkDKF079oylCVWBNBX12aG4wMUSfRefDglxt\r\nZtGbJO1MVElDtvcTvq0MYRfz2oxrwCfGO+MvaALtFkBpyecmRdP9J1BKiaGO\r\nrH3Rn29px2WsoK8XEQpEKxDvxByHKu42RJIDAS9PeHLqVAN/XiEpnEPNAEb+\r\ntBJtZf4KunSJ/w6VMH2QMVn9150PZI6hfEdQf4/JZHWfRl4SFwqtYSWoCPIZ\r\nqAEVQCstGT/3y/uX3rlm5LWr93m8XXGLTFRw0DLVa/s1hPjq6BENU+EuA/xR\r\nsKqCL9NHDryx563hD4Hz+pSgn6mF5c01GQRtyXvAAvMrH8wfYF+lxXTcM7AU\r\ndaaJt0qhBMMuzQtsj3M44mvebljFP2zNoVY=\r\n=NFOD\r\n-----END PGP SIGNATURE-----\r\n", "size": 101298}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.1.0_1655292492270_0.6463806413052733"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-15T12:32:03.234Z", "hasInstallScript": true}, "6.2.0": {"name": "vue-echarts", "version": "6.2.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.6.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.2.0", "_integrity": "sha512-/tLt58AHIa/JaLRverVdgK8RvoJE+lUJmQH4wjandYoJUtOSv4e4X2ZbacTcz3//h+Y+c3nDSfeP4aizt2G+Mg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/3fd24e71abc16261d320f02174552669/vue-echarts-6.2.0.tgz", "_from": "file:vue-echarts-6.2.0.tgz", "_nodeVersion": "14.17.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-/tLt58AHIa/JaLRverVdgK8RvoJE+lUJmQH4wjandYoJUtOSv4e4X2ZbacTcz3//h+Y+c3nDSfeP4aizt2G+Mg==", "shasum": "6f6b207269d642496efb9d191f91cd4388f17961", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.2.0.tgz", "fileCount": 19, "unpackedSize": 372264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4EqtjJAlqt649v5KpP7xRMReCX2LIihFj21o9SOtQVAiEAiGtPXxxQGpRlho0YPHpPUYy4ocaZT+rPLi/OKUchxJQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixTXTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU1g//ZBKZVek+Oa9lLzdinD5qA7IlMpmDRfZ/e8skM0eGc9fdv9pp\r\nas3RzF1y4dPOeIe/8VXQ6h5kHtbGeZofhzN4SFKz+pxh5oSKcAfODrTwTm/m\r\nvSPebSKykWAfdTYnD+Gck5zyMEv1AwzhnZYCcHOF8ZRoo097rc0y2jx1W0xo\r\no8xnugDHUFLHZMNhvCMo1lzc5wpWQTtryPNxUXwBvrxs6jvN9hOniqro3qXy\r\ntJOFbdCjoaB33oVsGDAEQ1y8k1S0GXeTX767PCslOUtrn5JIam88GVTsBnyH\r\n0ngiOITuFgIE2mZutY/z83AP8J9b0INPULI+9sP9pFOgqc5tXXkZnJD4oR9k\r\nkCtkatNxZD3n31s30KCD42YVRGQgeSjRJoQSUirfCL9RyFEjGR3KwAdjlHuc\r\nEsW3/0CgbwMhNljDyAKup5dsJmLGHUetxMlayaoe4MbPEQbU7yfCO/zd9Llx\r\nNFkdbHEfolj2kr+OCwPpdmkXL+qANONjV/GHu7mXN4qbaZxQLybx+FrpxnXh\r\nJDbYPifes+ZObOn7McyXNnHd7nhJIfeaa5SMV2ALhVNL46agunhfb925Bg9g\r\nz4vl5HJH30eoWmt7weBf5iLNDcI3ph/WMbix09PpLoEoZIOlAuL1dxhAe2lx\r\n2Qb/FLNC54FPElTBYh3MlpLbzsy5O1DxT2I=\r\n=eD/p\r\n-----END PGP SIGNATURE-----\r\n", "size": 94593}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.2.0_1657091539554_0.16767899902353833"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-06T07:16:11.365Z", "hasInstallScript": true}, "6.2.1": {"name": "vue-echarts", "version": "6.2.1", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.6.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.2.1", "_integrity": "sha512-uLRJREO14RB0K+0lQ4LWqXGlSpw6pPzh38Jx4aikM+bqCgW9EMOIAk01fcvC/PFIGv0Wbj362yPJjz5WgL83yw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/44ae175793be73291db7606b28c494f1/vue-echarts-6.2.1.tgz", "_from": "file:vue-echarts-6.2.1.tgz", "_nodeVersion": "14.17.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-uLRJREO14RB0K+0lQ4LWqXGlSpw6pPzh38Jx4aikM+bqCgW9EMOIAk01fcvC/PFIGv0Wbj362yPJjz5WgL83yw==", "shasum": "5c4a165f1098572a899070f86d5a7461c254e64b", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.2.1.tgz", "fileCount": 19, "unpackedSize": 373434, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTLZvpiWKHybOEwKlvE+TLSreftiP+7/e/305pbghqXQIgDN68+f613fTbCelipStv7eInqjQtomLFYRC97ylao60="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixUWKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ6Q/+MIdpAMNXkUzlK8CcB6mbavkrZ9Q1ReBmQqZ/CGgfkfcDZiOU\r\nh3uVscGMhE56hyEnij9J2ebHYZECn2UnYwdAmJ5pR+crarDR6i117PuG2HCj\r\n3Kz5ZncB2xDoTtZXdZVTobVGbSaDLqiDVo9A/fnbJWHbcmFqy9KZ7U8YwX5B\r\nz4rf/n1rpnaODi1CsfebQWofueZzXZ0KxLTAFKLDosNksE5ARNtVmwk8fAz8\r\nf0uBjIRP0flnDn5WH+7aB+qToGdEfPlSyHPCKnuAMECGROZpAe4jLyDHmubh\r\nswftbqBdBkzchkYgyYUh6nt4ojjRhnHXEwIKPJiCSI8H4rU2sffG7zjBKYTs\r\n/N74QX6kz1+liMGLiwlDbmTUSNfReTlFXzROZA3ZHQq9sFIPkPaEbzsavURc\r\n9QCzYWNaXErGMTeBR8U0io3Ob2qjw7V7gZlFoqkcdrPqtstM+YhiyCPne+kr\r\nixXYtYXP3vtFOMGwVBdEL+oz0dBnXeLdP49P8L8lqRLwXqy+eGpyDFCKwwoe\r\n569Twy0dI42uajnS/7LNc0/T+PcatWziJSJoHFTFdkML+rdaLfFxRwMs1Wm7\r\nTiWPIfU8JmxXKlTyJCqSlgVxkxx2xFPZnsV3Nw3W7Hd52DquDvImbFKoh9Ro\r\nKa+ur3OVCQREismcIHErYmZFrdtgIWg/KWU=\r\n=b0Bb\r\n-----END PGP SIGNATURE-----\r\n", "size": 94777}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.2.1_1657095561810_0.3082392522107802"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-06T08:40:00.096Z", "hasInstallScript": true}, "6.2.2": {"name": "vue-echarts", "version": "6.2.2", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.6.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.2.2", "_integrity": "sha512-weMJQj/guLHKaSEj7nrOV1jfdZRT0m3erex+1sjVNGLHg3NMRION3MExi++tDlwqxJZ6VkYlpfY8hJM61VoMWQ==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/a152881c88acde4d0bc603f990fab13a/vue-echarts-6.2.2.tgz", "_from": "file:vue-echarts-6.2.2.tgz", "_nodeVersion": "14.17.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-weMJQj/guLHKaSEj7nrOV1jfdZRT0m3erex+1sjVNGLHg3NMRION3MExi++tDlwqxJZ6VkYlpfY8hJM61VoMWQ==", "shasum": "2978230703795479699eb6e7fd9d1e47600454c3", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.2.2.tgz", "fileCount": 19, "unpackedSize": 373502, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAedh7jP+5ynWi5h6hhjYrDP+5On49YqTbQzFfA9vDFDAiEAh7FaW1GBIe2KqeNMBj3cQaub6jXFCt6D6fp1wPsp4c4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixomYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLJw/+K25rV6u1ShKrPpnY561qS1KVoq8Q9MK460/4MV8eLjUyaNAc\r\n9GrBR03e+Px1bAXDYRQCtsSbkNdR6+RZxnigCCUBya0+MbrLTZMHX7YZmJIL\r\nAImGG2DUa/eo8g5UdRlOtILnqXMyuDUnfJCJY3wC8aoneh6Z8MJVwnQG5Ytr\r\nhwcWvGY5QXhfRAa5F+UP70idTZv9+0wpxP/fdKAvqO0GzXpxqupS957kRFt/\r\nREv2XzhTsfAqz68rsU4PKdnfufUEUtfonfUJNlub1m1kBtUhyjlA4GhBty9Y\r\n0BjEUT3yFBHdLyIrP4WG2tqJoK3UnzaZ5k4oBDDOHiVeAgmg0/ZpiI5CAZmv\r\nghsIWRqAdiEhCAbG0fRfVaXC7SZlvTjtCrxifAmVd08QMIM6sRXrr4/15NOL\r\nxnVE7gczWFISyDYu2MNBu7S121zSIOL2CuX6RJMA1km2fTqjZ4eRysmvXK3N\r\nFJnGnAnM58VW8StqaJq87xmi0+4EagezdjYWqrUF1cXxBeEd7nx77Lo50imD\r\nS1R40+CS09x1PdyH0bF61ckml3u8MVSt+T8nZj1uGWnT58CdSmFZqOR6XWnv\r\n7HSf/jBX6h6ljB2nWkKJgCCZNA0tEb5rerWMvdwWfX+wzwyTe+fiTaLPbuQ/\r\nwJvDqdiuo5aJ8ebAgEBQRld8T9gvuDSKt5Q=\r\n=8wTQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 94750}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.2.2_1657178520049_0.440385959473375"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-07T07:30:00.575Z", "hasInstallScript": true}, "6.2.3": {"name": "vue-echarts", "version": "6.2.3", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "codesandbox": "^2.2.3", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.6.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.2.3", "_integrity": "sha512-xHzUvgsgk/asJTcNa8iVVwoovZU3iEUHvmBa3bzbiP3Y6OMxM1YXsoWOKVmVVaUusGs4ob4pSwjwNy2FemAz9w==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/bf39f42b73b416e13bdd08c15997308e/vue-echarts-6.2.3.tgz", "_from": "file:vue-echarts-6.2.3.tgz", "_nodeVersion": "14.17.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-xHzUvgsgk/asJTcNa8iVVwoovZU3iEUHvmBa3bzbiP3Y6OMxM1YXsoWOKVmVVaUusGs4ob4pSwjwNy2FemAz9w==", "shasum": "77973c417a56bca76847576ab903ab92979d75bb", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.2.3.tgz", "fileCount": 19, "unpackedSize": 374413, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAuSUm4RtILo8y/drdWjZ5ZneHFfrv15+H8md4VlbbhTAiEA0iG+Ntqe+Rqs1+M2ZXXeX/ka3ZPrIVtwZ9/JPrIlDPs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizPgLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnmA//ZYjyaKKbw7adInUgOBb/DgKPWehsJaNcGSc/icSzJhudaVrF\r\nq2X9bgbkQOPHbiEM3QrB0zGCI+NUco5KaHznE+ClhIcpQNvDUrAXBfZoY75R\r\nwGdNpz19Iu2ITI9cAGBZ+V6rAKJsf591AjLqcrZC4thyWW9fm+uSbriliKE9\r\nqK83FgpEiVuvOgAJK9ykl6UtkGFF11mjhm1iGpmQtY8eAkv88jiPhPqnwxpx\r\nvEIk3qcSiw/T/MNi2k3aVloZcTbtcwpoJQayw20JqQHRvowRZLSda1PXhAY7\r\nANuc3evWukI7ZB9YMGrTPT/lFeFHhYQur3s8xGhfoLU8LJ1Kk/m05ousd0bE\r\nS4xuUwTFnO4Xm5QIU1ZGTsypwCSNzAuvGgla38qY4qBkFwKqyPILRuk3BKzE\r\nwUWSRYCl+R1t2vBAZIxlV8/c13X37QOigXvjW0a5jLKX8RCIrmgAkSO0YvLU\r\ngct5MSmRaE2I+b5sxI4rknS+xxjm8jAPWw3jKLvE0nbLkVHXhIJX9YS/3SuZ\r\nUvJ2E95b6VjWW1VoS0HI80T4I4aLreRV2PKYMYPyLvaRviVTZliLZo37evRI\r\n6KLvitgwlRCIhQxr/3m89+UvNvPIP6rUCggHPeELS+/SdsVFDqxdMv2aM1dm\r\nSVwcXg2zbHSauXx1LZwv4scA8D46f4sjtgk=\r\n=DIub\r\n-----END PGP SIGNATURE-----\r\n", "size": 95016}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.2.3_1657600010806_0.07840593358271697"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-12T04:27:11.998Z", "hasInstallScript": true}, "6.2.4": {"name": "vue-echarts", "version": "6.2.4", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.4", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.2.4", "_integrity": "sha512-k9Bd0LsDbQxJsNQ5y4MZyYU+9A3nxmw4gIOHw41IpDRFhHpqOFyj2dkJSiVpIZJLqL7Y/22lmQ3OrTaTN6jvBg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/36bef8b50c2e5a1790080c9774f50190/vue-echarts-6.2.4.tgz", "_from": "file:vue-echarts-6.2.4.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-k9Bd0LsDbQxJsNQ5y4MZyYU+9A3nxmw4gIOHw41IpDRFhHpqOFyj2dkJSiVpIZJLqL7Y/22lmQ3OrTaTN6jvBg==", "shasum": "5553ff6e3c4b4b4c0ffad9418cbe15ea10ab30d2", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.2.4.tgz", "fileCount": 19, "unpackedSize": 371010, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICxHmNrh4ShYQIEuVktmy3da0JWIVxT3VEKAuHS3yNUFAiBkTR+9zy3aXBOfwJHD001xBTkGFHx7hmZQNw1kIZdcRQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkH0+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpd7g/6A7Z+ggnXaErdDSRoMNg7vBPQ+IpX3z7DgLm3IT+3v1uhNM7r\r\n1dI1midjMJJirEFY+8cwgg8CyiReWHuDv1hrKP8/PnH8a/rVxqAyKMlXG685\r\nXakmNfJgorEYnSg6VEbZes80Rcv1y1dC4cBKm51bqUMSDV/f2aRMbuu4g73m\r\nB0qb0BTT4nkh0dxc8Yx316FAh4MJeRH2hJD6m6p9u7H+SjVdx2JxASCImpeP\r\nT/gBMCgJqZ/PGa70+Y03bxwMdUMEph9bKoLdF1yKh8tDeX9qnt5C6FYDIon7\r\nfCFmEmoHfv0MXY8ejpCZaIYrrMv7sMwzd8GUOBdHUo9fgcj3EVIIzwbz2pDl\r\nqw5cwMk34xmkZd5QbZP753Dt1yu0BDpWpCYrxcqbt04illTiafgemmz0D4LF\r\nFExsncdK+u5UZHMXLrqDSN7hG2u+yT6ex9AUFJ5c4yFSLIyQLN3tfm4T+TCC\r\n4kuUJ5VulwgK6SF4o5Kj1KICsdFrrXkS0V5sl9F9n6eA60HtcOfPMlv4+Pmx\r\nQMUz6Z0K0wVgZ1F7sltuNkW++TdzsQajqjo//ssOXyHneAKpdvpa1C5Z2Tj4\r\n5RSMJ5OZ2LgeBjL2p8kS8/BTta8EgANVhMFFyi9WH2YHM/ez+UszQbbzeKtJ\r\nfAM9IRMQdZmx828z+XPOI9OVobC5NC+fPvA=\r\n=Y0zy\r\n-----END PGP SIGNATURE-----\r\n", "size": 93937}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.2.4_1670413630344_0.3631724039661435"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T13:04:02.674Z", "hasInstallScript": true}, "6.3.0": {"name": "vue-echarts", "version": "6.3.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.3.0", "_integrity": "sha512-niDhhPcThUoMi5xLEc1p7Psbo2YdXlzmpTkBRqvnA5GJrPHaJd8nc7i7IIrrmyy018Q3GiF+NdcHcc4UN/tFuA==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/8e7bcf863673dfc4cac185a222f540d4/vue-echarts-6.3.0.tgz", "_from": "file:vue-echarts-6.3.0.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-niDhhPcThUoMi5xLEc1p7Psbo2YdXlzmpTkBRqvnA5GJrPHaJd8nc7i7IIrrmyy018Q3GiF+NdcHcc4UN/tFuA==", "shasum": "fb0c1a453095192c5f25777a07aff1ad5e299505", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.3.0.tgz", "fileCount": 19, "unpackedSize": 376823, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqPNwb5KolbfPjfyg17AJ1mlbAxYTyikYjW/8xO7uy9AiEArY4OZHFb7RKYgwH/PQjfB5bUJ1gWqz9ypr32oOMXWiM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnKqRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1mg/6Ahr2vxcTSW4sX2WsVyStyBT31l09o8FgpGrf+CQnzkXI84y8\r\nEUdTKHQ82g0H7Wt08y7VRHR7MvzKsDZOzPCi4HV9ivguleSydmvl/MP/X6Dz\r\naptkExtF97xbqK3uwsXPApVkLQQM7lXj2VDhNXDOrJt3W5WXqWGowvzlQ1a2\r\nSghNLBlxUnsIxLwMMsD+qaRsJbtRIizDsw+H5Gv+PBGKDrY3fbUa/kDEqVnN\r\ni3CwoxDKIZhDXXwzLHSjHuQ9cPuC2aqpyXlBLhX7mgUoygXwCRn6sIxUOOtO\r\nFY46hhIE4ebx0jAH9G2SyOqeJpSfXfoklsI79RyUAIEaFI6zCFn4IYN4gzyn\r\nP0hPAL9IO+jmOqzIdozupPot9GJcd3RkWAZ1p62sZzQ50QdhW9iB3qhGxVHg\r\nVaHpawERczT41zhKq0tcYm4M/7AqsGUnMY18OPuJ3kJimxbeIYXQzBrPPMlD\r\nPQY1wY/qxDdb2TAQfyLV+J4uTI6pDgr5PyS5utbZ7cYbmK5LWy4gYuyoUCmj\r\n9iB4LxYsVf5iuUVxHzCCO9TziwCAM7UfZDaQw/AekHXkmN1gKdAkh23J9J9q\r\ni9E6//tqdgQVKTmJZqBIAUvZN4sYYeYsRbzse12Up6g34atDSGuMFkq9OUBx\r\nkgbgMrLepu2iB2/LlH9JFJMhMBN4QQGZfqM=\r\n=yHaC\r\n-----END PGP SIGNATURE-----\r\n", "size": 95703}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.3.0_1671211665344_0.05219038518137542"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-16T17:38:50.688Z", "hasInstallScript": true}, "6.3.1": {"name": "vue-echarts", "version": "6.3.1", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.3.1", "_integrity": "sha512-GGDYWgZSOj1v1pPOznlHBFiA4SV+VSRkmP5VB0zvZ6zhoAiVbSDMLYfqLW0BjjEG1CIjyNgbOogDtl2QIVQdtg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/3667d58f448342e1b1f94e1ef3aeb13c/vue-echarts-6.3.1.tgz", "_from": "file:vue-echarts-6.3.1.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-GGDYWgZSOj1v1pPOznlHBFiA4SV+VSRkmP5VB0zvZ6zhoAiVbSDMLYfqLW0BjjEG1CIjyNgbOogDtl2QIVQdtg==", "shasum": "6364f97a4f55c8f184c17a44528e58c2ccd892e3", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.3.1.tgz", "fileCount": 19, "unpackedSize": 376721, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+tD/62IC+p2vIHsLvFMb8o85fMhba+y1dBdzr6io+QAiBR/oKP1zQe6C+bfCGjQxzy6KPaSKcexUx4wLsAeYoaJw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnK8dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdtQ/9EersX3pl8m0twNbs1aX0d7Q0q8V13ICfW3u9Lgt2PFVC3GBt\r\nix9nl+6C5tgL8FNc5QHoRkCZKQpb9Er7OPfaHWrhZn5mPdG3JTNj4+FfS6/l\r\nZTjoUJhrqwYzpj3LRxAteGZwgMfh5t3sY+0RJKS6VT+bfhJZMHqq1eswloku\r\nt/ipgoL0vzPKF9lcZK1vkvCSvLuSZwmPIYoO9y7WKE4BGF+jTP9LiMI0/5zy\r\nJGPmwrM6utYTtgPd+yGoO6WL4+cpdAG+tEVDWLN4fUNHBderSBNRqvK3rf9L\r\n3u1276RaHRX4LmGQJNGxPQohL9qZ3kpsbyhl99H2K0EU6lajzLFy34pRtEyZ\r\nxyYzen2g6FGSf63wDCb8qmecfdU7Stv5nxTHfhMdAQA284mk9nFwZE1EsXK4\r\ns8HAECC/SikY9vm/biNYpiNvg06loBqfnshwsEg2bIsSjGaaOsQs+lgqSC3G\r\nbakQUGpF7a6kVmJJX33SGW1Y2Y5DIme6I2ejp/2nyUiJtnO8iiGd522xt0Jk\r\nETLULHOGzdqJI7GjXWfDxmnnbr8F8glJXswcDryEV0k0tmtHxxrHcBR0JxQF\r\nIQOKxP8DWgZjK6vua7wArsS/yagcCVmIc/M6ZbPf5I3d0+0dz4TpS/03sy3w\r\nrphtjd9iCQ0fniAAlx6cAd6dqanWSdifqHM=\r\n=D9W/\r\n-----END PGP SIGNATURE-----\r\n", "size": 95693}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.3.1_1671212828993_0.5102053770504416"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-16T17:47:55.579Z", "hasInstallScript": true}, "6.3.2": {"name": "vue-echarts", "version": "6.3.2", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.3.2", "_integrity": "sha512-groERmdAg044IbWQpBXQ2DhU7M7WWSQiIv4XAeRBEGPLaH4498zUJO/yBj0ypZd+nfAkdz/oiHP16alZEEvRGw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/6f374f36d32ecd0d1a29fc4176adc8de/vue-echarts-6.3.2.tgz", "_from": "file:vue-echarts-6.3.2.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-groERmdAg044IbWQpBXQ2DhU7M7WWSQiIv4XAeRBEGPLaH4498zUJO/yBj0ypZd+nfAkdz/oiHP16alZEEvRGw==", "shasum": "6f87699a4a04e2bff8cb571655ad96c6e6d4b680", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.3.2.tgz", "fileCount": 19, "unpackedSize": 382625, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBA1Wx4BeEXXq/K3L72c9JBTG+DHirlQ0LgD++i7m4cVAiBMZMwrU4nsG50kBKcIvh8Sojr6M2unY2a17Zp8RRPcbg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnvwXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3jw/7BoBzMgsWeK8TXkTgujzruLy0/tOkuTW9vpcggUjO0A5Dx/gN\r\nXmkFPT7UFOR3d+Z2cnp9U3YRu23lqvLV/611JVaNhR5gl67p1uEplhL+nevY\r\nrC/UiOzWzl//Sl4CUZ3DLenLcuuvWlCCqSmuxE4sdB6YnMMHTKJzp386gHlb\r\nRPObQujrAGoPNb5z540nHt8jWiYl1a3QtNTkjC8UzfFohZYtuWkPPnoFmXZj\r\n1ZkjsflMkeHCOHJoLZAgz3iU/J8STYRMeeSGJgNUEYAq44pbp71T6IKgcPTz\r\n4Do1oN9SugawBlJf0VyDIhiPqv3RK9FEuK9uhZVVTW4euHp+tZ+BNgiEUqBU\r\n2GGIxQAH0Nes3Psw1gyDqlDInGqrKpq9n8gWQ9UT0+ZeiGnMFeaqCmzz1SeA\r\nbSMcyBkfzfD/Z+Eu8CT7lDuY5nzQHqGuawdlPGetGnqcGir9DAw6k90k07Eb\r\nefmnMwQVr2+n16e0p7NubKxdQRGl3k9Bva/isR6MiKi3NebAlA36uEMfK1fD\r\ndXV7GIkJdxXyQ67yRn2cUouYQpXQQMjTcPuXn1hIu1D7yOhWSB9UDMyvFrlR\r\nZ9sTE6bmQRRZ0GQtV4Md28qZtRoOM041ytw/EqrCBmXJYYaBGnhOgFqMlhes\r\nvogAgYuPrrIP29+uqIcvB3rKOYtcfYUkcVU=\r\n=eL5S\r\n-----END PGP SIGNATURE-----\r\n", "size": 96620}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.3.2_1671363606791_0.640946573978179"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-18T11:40:11.331Z", "hasInstallScript": true}, "6.3.3": {"name": "vue-echarts", "version": "6.3.3", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.3.3", "_integrity": "sha512-kfaXxWHrQK7MegPQg9WWgLZjs6UpI5D7B5sDQC+3nN8z6Q3RjmaGI0KPwKLIk5fXZOfLF9GcfNi9BS59eeYsGQ==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/1827e3b432534ccf6846606a85bd97ea/vue-echarts-6.3.3.tgz", "_from": "file:vue-echarts-6.3.3.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-kfaXxWHrQK7MegPQg9WWgLZjs6UpI5D7B5sDQC+3nN8z6Q3RjmaGI0KPwKLIk5fXZOfLF9GcfNi9BS59eeYsGQ==", "shasum": "f2f872ab18c1cf1cb62c56219c0d21be16c41282", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.3.3.tgz", "fileCount": 19, "unpackedSize": 379789, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaOnLrzblBHSR3VRZ2F7SVsolBZkEgahzoBAIIhd3M/gIhALficent8jtdMLDANdN0GZDEhO9vvfnZXO/rdtdupNF7"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjn1YfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxOw//eFLrOkDxx3tiOeZajp5mEaeU7e/oxr1gCJO4f8cPJR8HK+4R\r\nu38WnZ+QcK+/BGrK6HCP4MEboA3NqGcEBIsvljZe/3zcuGAvq57wfucQ7LCA\r\nvTI11KnjCzuCZQgH2mYaIGHLQrjdbqwXl1Ywj+CgT0gd07c/a7jzaSg5E3u1\r\nNJnfzFkxl+df/SHTd3bmIYsHepUxPBD0Z9+FvdJI9sXH329FmTbvs0/Z1Z7N\r\ng10uB1h0ZGEk33HMvqqDkRe2TvAPZdToXbWzrB+zxAVQ7dM8k32d071ICLVh\r\nc/Bw2jRCxhhj1bQM3IWucvpl3YmgXmG+/sAfdya7+sU1jtuiVkcxzPhcB19+\r\nyeknUDzil33EFMsZu3Ng9H4BSBZeIknC+VadbAkuCHE3jHoeg08KhnEwVmML\r\nqar1b6/S9y6MmLrrJhendvsQraHQvUyIIupUtd5MU+6EapYHrSo9LDz5/zB9\r\nY1YnOs5i9bGFHvr1kZ+lB9OCrtbMQPmTYuksi87pdxEu+qPQCd+oHMvGiUuU\r\ngumqfSl4udtva3ODMG+8JmKm24Olw8mQ2VMVmiiWNARP296/COfzX48CtK7Z\r\nRvvT+qTONAXOP/uEru+ahs9ktXohb1GpyDQfo3TGOXnk876J5qqZOVxvNQNU\r\nn8/xKGyWnfCros+XPAoUihMSwpMWJSkCU1Y=\r\n=PjLw\r\n-----END PGP SIGNATURE-----\r\n", "size": 96423}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.3.3_1671386655408_0.7196159309887575"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-18T18:04:19.175Z", "hasInstallScript": true}, "6.4.0": {"name": "vue-echarts", "version": "6.4.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.4.0", "_integrity": "sha512-XQWdRoYVjQcdNQsGPuQZwjI229VH9L5X6WKMdJebP0msP6QGxnmOYAEt0F/9cBelonQ8iWMIctKm5AM5PX2Djw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/20704958230acda442e4a5bbb028b39f/vue-echarts-6.4.0.tgz", "_from": "file:vue-echarts-6.4.0.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-XQWdRoYVjQcdNQsGPuQZwjI229VH9L5X6WKMdJebP0msP6QGxnmOYAEt0F/9cBelonQ8iWMIctKm5AM5PX2Djw==", "shasum": "f960da3319fd6f732583610a549cd19fcb1f84ea", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.4.0.tgz", "fileCount": 19, "unpackedSize": 397174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFCOM16xxsF1NhK4c6G6uuZCLeFm0Q+oHK9PkHNPfAPvAiBdlLVvzxmxMbIqj3NazSDe8KbyIUUnhdE7t5ASwQyzYg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrWidACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovcRAAmK2kFfL53dtwJO0tQDPni2kAHdIrWtsctY3CihrcDwYyHb71\r\n52PiBihybjff+VBAEigEdhKFttoX4pDAhAkDkC8RkLz4nPGyd/JO6PHvaEd9\r\nn3tKV4pZTawUsZtPZWOW0Se82cD+TScRaQiH5bp1vGPzT3CvsY2MXVoqXKLe\r\nnCJnpjODtAAArxqlqnNrNTXo3aT3/H4KECFp2c1mfR2W6NSWcPhOrQqWPlU1\r\nPGTwXpG6cUGk5nC6NE0K4VC4e+tr6CYxAbBS9Mk6+gixrj0j9sD8/M3T+e8Q\r\nYizfsKVW/bsXIFdSe+3puD16XDUppUr5fdHlGE5AtYYQxQ9v1dZ/cVbkHddH\r\n8yjShxM3PPhN1hVN5q8gwI1a5G0pbQTfEPKZRzp3IsGcOPAkVeF35gGbUkWH\r\nIS+pVe40CicWvahJGnkuqojEu0gK9tIu13zjWY6mqUTS9wSsiTDq9ZOcihK+\r\nkdUlgw5+E4167wMvdfvelMegGnzHNfjFY5ewQpGhIE+PNuIElxhcL2Ib8PZw\r\nAM32pn0CzueGt5pmC0UG2ScidicWnhc7Z3YxPOau9jnwJjRY/gA16swCwZps\r\nV/ZF63eTLKXU2igUdkL+9O8g49ZDVhVd3kicxgiCqm5IJy/4aTDBLcW1Jz0t\r\nAR/Rdwr4WoYf0CVGTRr3CJTQWzQAEh75YGA=\r\n=0Hqh\r\n-----END PGP SIGNATURE-----\r\n", "size": 102153}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.4.0_1672308893032_0.8509715531322641"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-29T10:14:58.967Z", "hasInstallScript": true}, "6.4.1": {"name": "vue-echarts", "version": "6.4.1", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.1.2", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.4.1", "_integrity": "sha512-i7oknqVsnGxWUGneO+9AdZUfQipHO08CyWshe8DzfxPV1rcxfQhIxhX55c+isi336MMQ7yG+ESSAmbpAGWj6Yg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/3cadb06c75280f1aa10368bdbe5efc83/vue-echarts-6.4.1.tgz", "_from": "file:vue-echarts-6.4.1.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-i7oknqVsnGxWUGneO+9AdZUfQipHO08CyWshe8DzfxPV1rcxfQhIxhX55c+isi336MMQ7yG+ESSAmbpAGWj6Yg==", "shasum": "6132d4f8614849cbc47dc0ccb00de65bba8b8038", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.4.1.tgz", "fileCount": 19, "unpackedSize": 398506, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSXAIHMufeAk5lpsji1KUturRXo7uwjxXo3lt5pfDVaQIhAMvI3ui1Lb1nFBrmIRx1TqiONbS4s4pe0Uz/SHEaOyYH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsaw0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/uA/9FnvFtgweg3hQHGFEK9TWkQ8cOcwzFZTlOmKTlfUKlgJVJP2q\r\npbBc8QfUUj1RasMrvAXTH9P9sDkMF4Bs/9UqLwGLhSiEDpGGVJsGy3StDICx\r\nExpAqgk0Q3GhdC2zK1/tbuoI2sH4kkcmc1IfcP99sbLJzT/deWJqTIMX3iHF\r\nW91nuqGwP8MWzsYFMm9jKtKgSGWGlfsmaHv7DW5N0ewka0N4yKK4t7wLdNOg\r\nlEYDwycMbSmbXdzGAUa9HopfCvSW8Ag3gq9VF4Z+LDu4WpbLNM4zBYMXF2OO\r\nISVcrhVF3ClnAJvsEU7YDLxQ6qWR3AyYArGb8QsJAQJS7qMQznVglZ0GgJmD\r\nBf49wp5VnUrMLLCfwjMcXrVrQbA23bTMOBAt9A4B+0z2+5iUcf0FiE3VEjJD\r\na4EvJC+U4svdbgR089efvcOeOqyEONCJ4MBu5n65jIv6GEDCCdUuSG7P+12k\r\np4WSfZgMlv4dT0yfG9KmaHome32GYOBukFO1beY2bQLXUL/SiOeOWO/GqH6y\r\nnDTGpGhBPLQknz2hFSFr89kqyWywGAc2tgjo8a19eM+sip1qzAMr50E26i19\r\n/zxRp1Jovh5+uiYnlkGe64yCNVYIIxtLGbi/l+mX5hOjWDfpvJP6vvxmBJVZ\r\nME82kYao9FkdXmlh9xr+903cx09vPpyP2oc=\r\n=S0mm\r\n-----END PGP SIGNATURE-----\r\n", "size": 102341}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.4.1_1672588340089_0.38612290940671556"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-01T15:52:29.080Z", "hasInstallScript": true}, "6.5.0": {"name": "vue-echarts", "version": "6.5.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.17.10", "@rollup/plugin-node-resolve": "^11.1.1", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "@vue/cli-plugin-babel": "^5.0.4", "@vue/cli-plugin-eslint": "^5.0.4", "@vue/cli-plugin-typescript": "^5.0.4", "@vue/cli-service": "^5.0.4", "@vue/compiler-sfc": "^3.2.33", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.0.0", "core-js": "^3.23.0", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.3.0", "postcss-loader": "^5.0.0", "postcss-nested": "^5.0.5", "prettier": "^2.6.2", "qs": "^6.10.5", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.33", "vue2": "npm:vue@^2.7.14", "webpack": "^5.72.1"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.5.0", "_integrity": "sha512-WpmQVOwQm0+SfA4qq4a7/H45wEzXju2wcbNZqRC4S0ttjA23SaPISZjGbSCvEuqKfbxmsKbnmU6zI0z0PurB6A==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/7b93a2baea9c5be29db46968c49737b3/vue-echarts-6.5.0.tgz", "_from": "file:vue-echarts-6.5.0.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-WpmQVOwQm0+SfA4qq4a7/H45wEzXju2wcbNZqRC4S0ttjA23SaPISZjGbSCvEuqKfbxmsKbnmU6zI0z0PurB6A==", "shasum": "09b8719db626c93cc56ae29dda6461a26e528122", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.5.0.tgz", "fileCount": 19, "unpackedSize": 402332, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMf1d8BHATuVd/S9anWQBSQyqIqGjtWhMreq6lU7kx4wIhAMqpoEHA2pmbPXDCatzarzFBmaJb6f2kw6GiivU9bHPQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsvteACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhSw//aRvi8ZOszoQT7K9FWcjgtt3o+1dI81eJalTbk7W9fnbml1NU\r\nXw4cFlyLY/hru1HK1LM+2Kzhuh1e2tP2VoW3J8bI5qPnZeYVQn9NrXRTyXkF\r\n38ma72bpvailrxOPDR8bb8tLFkRHocOdh0y7m0eU1eKzTfkuiQKOjyRN3CYt\r\nQj5OdtpDkjFSKnEf61k8x6xX+MRpT0I8iIlii4k7MD8BWEwhBnQGDKOKUYAV\r\ngtzKwESNR2IaAkkXwawjTkzNVZIkia0GqaXkbunyfg7b/cXPGwRYjEdIneEb\r\nL+RHSo0uB44ksvt0xjlUNhUlrCP0NTG6/QXCgpY+1Ny0UOORApGt0/9NRgIe\r\nyBOdrly9GsXlPdE4AELPhSJqU56rR66Xe95K0iM00Smt0GBQRflTZZg48BvJ\r\nVr7dJIyZUmWVc8CEVlz96DExUVrxWGxYeVxqjRzEjFnkV74NUo042Xl2SGt3\r\ntxM0rB6DzQs2ktawZwX+LaCLjAWUQdOEeTUO2Z3CliES58O9h2Pavk2YDOjz\r\nzyc9UI2F6roPefKuo28s0cjR5C7vBJXWiuvwdNmq0M4ACwdg2oWAC/vnLlpV\r\nvZU/cCWxNaESYO1IHpMkyTp5qG/kYHnM2DJ7FVXf8jr5H1a0KPQpZGRhD818\r\nqxqQ9zm7/RLowCQWiJckqF9LBEVqGYYMAFU=\r\n=TsUY\r\n-----END PGP SIGNATURE-----\r\n", "size": 102430}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.5.0_1672674142369_0.1866643765462841"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-02T15:42:27.477Z", "hasInstallScript": true}, "6.5.1": {"name": "vue-echarts", "version": "6.5.1", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^5.0.7", "@vue/cli-plugin-eslint": "^5.0.7", "@vue/cli-plugin-typescript": "^5.0.7", "@vue/cli-service": "^5.0.7", "@vue/compiler-sfc": "^3.2.37", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.23.3", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.4.14", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.37", "vue2": "npm:vue@^2.7.14", "webpack": "^5.73.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.5.1", "_integrity": "sha512-vlCX65ITq83xkOljip3juL8LyMd4cHOE6Zmp+9u1nPxPrar0irEtTwV80lkFm5yQM4Ef9X9fNdMa6gmsj75xYw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/5b9d91f900c725ad98ad8d9218af0cf7/vue-echarts-6.5.1.tgz", "_from": "file:vue-echarts-6.5.1.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-vlCX65ITq83xkOljip3juL8LyMd4cHOE6Zmp+9u1nPxPrar0irEtTwV80lkFm5yQM4Ef9X9fNdMa6gmsj75xYw==", "shasum": "f7e7eb86354b0f1a461906b4b92aac456dd5dd65", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.5.1.tgz", "fileCount": 19, "unpackedSize": 400861, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYX1MVYn0KnApHMliGFIYuZNgYs9H/rpabs40VCQ6gLwIgKQPeDT/aCIPZgxPunE7LhdXRs9oL8IaeE4iA2TsApTg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju7AUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0dQ/+Mb5fnTed8bKL3+uYuLytbv0QBnxoHmy6YF6xs/kbu0zpHaOG\r\nZ6v82n9HV3NY/SFKhseTjCVShhDJcAyrzSMeaikHToEfyPm9aIVwb9s5h75r\r\n2zxdmbcbR2Yw18npvHFP6RjYgNvBpuMblcmi6dzH51doZ57Vntz1mtZM0u+B\r\nyf37LR1bDRs1EocjE8boxC/oCdII/Hh1Y8ZuHrkKZhWW9YyNj7LyCeJg3t6g\r\nml4hKcOSAdUXi3JXXyM+exTL3FNYJ9McQIWMixsL98vu18pkCFw2whJbnrfw\r\nOo1PRif9go21Z9EYsQ8VvID1NOuB86/QLbKwU0CcSksYAOtpo1EM5w2hdfTe\r\na3buBx7gIMwtSedrQPypFNpT3t6YgpHTkzBfqMyRATDLBlF4arvwjJSEvAR4\r\nPGTtY9c0wl8YOo/6ilZ/6PnffI4BgN9bi55HfOG5oj9MFPcDZSVjYp1zREe7\r\nZVggrtw5zs+yeEqtZIWHFW1LCr6mR0HcQgaoqNiUHIJyMhzz+M5jZ4zPWF6c\r\nb3R37o4AnN01LUGHNPIgTcCNamPzBPjuOgvTEhmHyNBWE9LKAsnn/R2JLT8v\r\nd7ByZD0wHulSHlgFxuUUISQVHSwDjAh8P4uKCN/g1NUCCE91L8ERUrKABEyD\r\nFHsI2Zdl4NbSlvLdyGHbKUCvCggEpQXuRKM=\r\n=xt91\r\n-----END PGP SIGNATURE-----\r\n", "size": 102384}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.5.1_1673244692743_0.3955430111934022"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-09T06:11:32.897Z", "publish_time": 1673244692897, "hasInstallScript": true}, "6.5.2": {"name": "vue-echarts", "version": "6.5.2", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^5.0.7", "@vue/cli-plugin-eslint": "^5.0.7", "@vue/cli-plugin-typescript": "^5.0.7", "@vue/cli-service": "^5.0.7", "@vue/compiler-sfc": "^3.2.37", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.23.3", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "postcss": "^8.4.14", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.37", "vue2": "npm:vue@^2.7.14", "webpack": "^5.73.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node -r esm ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.5.2", "_integrity": "sha512-8NCVTX6aGCR2VPxm4P/Nm3k4sx1rsocCTTf4m0C9d3Q17WgXxB2tcYixQYV0tQNY1f16Z3yS6iLlmc8zV0j/kg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/ca52f395fc4b4e6da6887136df32a663/vue-echarts-6.5.2.tgz", "_from": "file:vue-echarts-6.5.2.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-8NCVTX6aGCR2VPxm4P/Nm3k4sx1rsocCTTf4m0C9d3Q17WgXxB2tcYixQYV0tQNY1f16Z3yS6iLlmc8zV0j/kg==", "shasum": "68fcf80d042d2dac6a2e9409c687e7b618fb576b", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.5.2.tgz", "fileCount": 32, "unpackedSize": 717593, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICx9CcZemjoi0zBWDT4406chUqB+VQE0H28AZldNeltNAiEAwrkJlx4Z52ePb+D4giWJuLbhbaOHJFGEzbImJ80OahU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1UTNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2qQ/+JL7L0IKeM6amMIuUHBRz8rPwO6P01LcewCRc/MFRULnsP//7\r\nq/KVlGROkLe24dPa9x4vVgeAiikVZQ1qT7mhKoNcGgLBMCeklSamchZxP9Md\r\nuJsXkD3Dnk5UdCFtxp2TkRWzlM1/ya5ctlypd41ABD9en3nUCIsaGrWCvRdB\r\nMEJTIEwl96x6a9OqjeLWMq9Qk9ql5xcrwV58AONywy2+XJPhkcGJirZWl+Zs\r\nTdblbjsz+kBkKnipU1jfnU1QXg6+jSzvmRaixLSvIWDBAqibh42YiI54tEFB\r\nmNdPGFoslfL2QhCwMR8AuOIGItyULU/vJ4cxj3PV8DDxZLuEjFlsJl1E9+US\r\npPCxZNjHYWuGFOgg9v8opEHNYAo1VzkbuqWipgns9FqlNBNnnXmz0w59BP4e\r\nCGe1rhFFQq73fe+rFKpHhrDZSDOTnhqpd6irekYVExg34xk2HlVsr3RRROPX\r\nOXKq6sT+piPjDJLMl5/3jjXmKQWs7zdI9LOL9S5g8AxpRCHf5kQMu/GkCpcx\r\nwxlNb5y5OwljaT9KH92BhVxFoX37wJ97dHZM3vzYt5aB5l6BJgZt1PNUSLgv\r\nuiedyIXxlREo34CeQKV3Xwfs7Doc1jaUdwYgmYrRXedhbxSTBcSSpEKEDVpj\r\n8H+OnOI16A6PEzeHpiq91fooQljIQ/P47Xg=\r\n=rMdD\r\n-----END PGP SIGNATURE-----\r\n", "size": 176051}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.5.2_1674921164766_0.4025238403692253"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-28T15:52:44.980Z", "publish_time": 1674921164980, "hasInstallScript": true}, "6.5.3": {"name": "vue-echarts", "version": "6.5.3", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^5.0.7", "@vue/cli-plugin-eslint": "^5.0.7", "@vue/cli-plugin-typescript": "^5.0.7", "@vue/cli-service": "^5.0.7", "@vue/compiler-sfc": "^3.2.37", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.23.3", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "postcss": "^8.4.14", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.37", "vue2": "npm:vue@^2.7.14", "webpack": "^5.73.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.5.3", "_integrity": "sha512-vv74Kdr0ozXhJ/SZQAc7Qm3h6ez5oAyHq1+Yp0vkKPsOo2g8/Z9fKcFwiYixYPtGszNQzZxWicMTafrcQnPIFg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/50b3790e78febbe4716f1d32e041616d/vue-echarts-6.5.3.tgz", "_from": "file:vue-echarts-6.5.3.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-vv74Kdr0ozXhJ/SZQAc7Qm3h6ez5oAyHq1+Yp0vkKPsOo2g8/Z9fKcFwiYixYPtGszNQzZxWicMTafrcQnPIFg==", "shasum": "b69fc3bfe469349285e29878e996d9a6c3ce9d44", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.5.3.tgz", "fileCount": 32, "unpackedSize": 721363, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0bwWyM1Z0PyM5EAlg6Uldt4KL029pWV5Rjicr/HH2MAIgcSFAEdcOrtHGyUj0J66TWw7oVyTAF2+5nR1aS4/tcOE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj41v8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozsA//b3J/4I8RVDf7PD8Q/6MlYGuB9V3XADl+HU4eE7ZlLoopwtq6\r\nXNKzHVvZnXuTLnaekrvwCY2IMXx1JD4KrMTz6jPJhgX6LHMZb5tweGzk1BPK\r\nLbBnPnNvySWQlh8HHnwUPdvJJr33KnOpDLX/v7ru/vqpMFZklrs+AndERVCg\r\njhsEtF6cM42X13vTxaV3nEqLOATlb42L7cgs9SOdXzL2DNTE7OHOsL6+6ec/\r\neoNAVamrEVrdpc1pJbp616imJinf/nbHpDlM44E4W2+GeKXEbqLgkfZ1nhjt\r\nLlpCoMWXtZ8mmughIMX3IUPugrqzF/Apxc6YvCASes/K1ecBhMqqZhbCk2A9\r\nI31CK2l+o+7oxARHnoD43MWxi3eDOyfZlR/fTEotxPk0Ta0XoH5WcWyOilAk\r\n3ibr7Gw5QWlt3YFFYqWB24VEJRxJLrKbqsqCNu9AvbbnkeOX9D8twX2I/23/\r\n34X4bNTlcWOmW0bnbcJtmVZdHgu9MGBjP/n8R/b8MoWqWR/UilnLZkCiyKSz\r\n6fs6g9HXzJJPwNJpCCYvioVMYjXL3o16S97BkK1luPpvfX3v8X/inzSXbJtc\r\nQWi0wWI1YUjYmpkESVi+HEO3oRxqhoVEHHEnWl6YRQyTIJYoBJP5P7cxETbb\r\nqXLThUV1IqoN3nIu44xFtfWW2rC0bZETgzI=\r\n=Bh47\r\n-----END PGP SIGNATURE-----\r\n", "size": 177547}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.5.3_1675844604407_0.02687318066878719"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-08T08:23:24.710Z", "publish_time": 1675844604710, "hasInstallScript": true}, "6.5.4": {"name": "vue-echarts", "version": "6.5.4", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^5.0.7", "@vue/cli-plugin-eslint": "^5.0.7", "@vue/cli-plugin-typescript": "^5.0.7", "@vue/cli-service": "^5.0.7", "@vue/compiler-sfc": "^3.2.37", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.23.3", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "postcss": "^8.4.14", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.37", "vue2": "npm:vue@^2.7.14", "webpack": "^5.73.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.5.4", "_integrity": "sha512-pLJTk2hfhauoWdmiZGXByAtcO6bpfnAS+jkJqKimSBSV5I5ckh2nka9duLlIVbTvQOEb2tFa0XiXjACQNcEfpA==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/c0dad15732abbab68c4f6f3755f993c1/vue-echarts-6.5.4.tgz", "_from": "file:vue-echarts-6.5.4.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-pLJTk2hfhauoWdmiZGXByAtcO6bpfnAS+jkJqKimSBSV5I5ckh2nka9duLlIVbTvQOEb2tFa0XiXjACQNcEfpA==", "shasum": "13b75999c75a36ea78d4e66fee67d82c88417ec0", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.5.4.tgz", "fileCount": 32, "unpackedSize": 719015, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTN4j/gXfUNG01+Pi9RYiqX5rqZPfXosXmi8UKaDQXrgIgMrZh4GTIvc4IUjSqCYHFsFBMsifZDbKwkAwfUMl5BWk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4147ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2gw/+LMPejddnabTIi/Z9/t2ViFuiV5gYLTNXNugPJ2oK80N9YGdu\r\ntVgCqaCzC3XAnnOMHDFh1xCMPkslj+EBLSLcBxQ9BCPCuTI+ailFalq1BWJw\r\nPq43zBAjcJZGrzSkJFwa1vKIet6865dTO45fSWkEe8vFo7ZqYa9BdLOhNAQm\r\nHlKVxMlZVmRF6pcIY8LHI9WiIlvOFx40XHwnQH4ak0QApMnHPLgyylEaYapj\r\nOrDjV1aFLiwXk13bIfRA1Llct+Vavt2TpiXqsF6YysWhpjLbaV8PdxQHv0DK\r\nJDgryNWkLoo9lsAxVBO0+Htip8jHgS7bc19SZjXlnMzMU2MEBMLKfAPlCUge\r\nwVt9lhyfTkDtrDNIvw8MvE451XvTdamPrCu7OpGoQvyLWEwwxJqi/QVEMHGQ\r\n3C0xmmYtOcOH+L5FemZkYZYCzASaVseFevad5hauChb/vH99Jc9Q1mnsT7qD\r\n6z7H0KmMFQPD9jVyLhmESiP6jvCyfezJZ+RdHDD4wYmbjTuR5j0CcTN7sk/r\r\n2izVd855m6ItjWfL8umnLp5mVtkg8TaZDE/qeh+7vzWPyFEF1N9f8UAd2V/S\r\nLOcpSysIHpFi61KtHBToX0i/LrwKGAicE0368S8DMQmbfD4giLJnBxvvHBIH\r\nfG+Xx084F/ZvDHQztsi4NBDto8r8tvkElNQ=\r\n=dUpY\r\n-----END PGP SIGNATURE-----\r\n", "size": 176732}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.5.4_1675845179218_0.6479284482378742"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-08T08:32:59.442Z", "publish_time": 1675845179442, "hasInstallScript": true}, "6.5.5": {"name": "vue-echarts", "version": "6.5.5", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^5.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^5.0.7", "@vue/cli-plugin-eslint": "^5.0.7", "@vue/cli-plugin-typescript": "^5.0.7", "@vue/cli-service": "^5.0.7", "@vue/compiler-sfc": "^3.2.37", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.23.3", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "postcss": "^8.4.14", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.2.37", "vue2": "npm:vue@^2.7.14", "webpack": "^5.73.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.5.5", "_integrity": "sha512-KJD1MSyoMQ33q8M4hM45OYeSC+t7LzesIXQOrkRSXv4xhOi0iN+6yEjQuIuqRB9zZGCT2J4bK3eQ+1ttdz4Uag==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/264a1aa75f94baea85da50d98cac65d6/vue-echarts-6.5.5.tgz", "_from": "file:vue-echarts-6.5.5.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-KJD1MSyoMQ33q8M4hM45OYeSC+t7LzesIXQOrkRSXv4xhOi0iN+6yEjQuIuqRB9zZGCT2J4bK3eQ+1ttdz4Uag==", "shasum": "d1a5c358c24754c034e884eae7a8bd17d5f65a02", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.5.5.tgz", "fileCount": 32, "unpackedSize": 712533, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDa3HTE4hVvjts29UbWB/uAcRqvu8wdUH98metxOOkopgIhAJKEFojlwSu3dZMS4OzyPNofnxYJXxMGjmPWcjaXifAF"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRhD4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdZw//VWXpGngU+Ux4DQG4yCLfjBCFuLgrizfQwKxfLicwCh72g0aW\r\n6BEqH8lJ3K9h6bbzyqq7RFCPLCsBl12zIKg8dUiNLMGsTDlwW8HiT6AEjjkE\r\nWt9goPVIN7Ezzcma/11tI1YI4Y9BUU2uYteytVB7cGjFvyDMmnI1ksS0Q3qd\r\npu0SVHMWZCHHHK9Yg2ce+ew3V7UTh+WyyOzL2B00EobC1oWI79D0efylFoRO\r\nlo9LUOtjJrr1mwJnK2QAIvRdpxVD7UUeUs2Q87lH/o5zQB4EEhNRTLYP0uzy\r\nPE6XvATODdb6UUbzTeacmq3CrDK8HemWCDN3+mvDhTrIN/mXSrFVjnLeujCQ\r\nsEwPkgIli7Yze88E9yMC44Udt3g1BNPO0HOOEXqkZQpfcGpmorgEwFGsYbJ9\r\n/DyCD6d3+16sIKvxopAPG+VR5D0vzBqdD5X+y1++hJPOkvUzU8nQ/HBK/6qp\r\n4BOnM+ih8/RCSXgWfNdMSzeynNKr2m4IYRHFz/NgZx2IxfANczRqjKUAK55F\r\nicFydGDKtc3zl+sfKQNoBi2AEVG3Qg91r86Au7fI+AAPX/1yNM/dstGQ7+xe\r\nnjHIcnlXM2/9f2noNVh2O4TnkjgUlMAhwmoEw/Xi/Yb+pvImS8pffPEdnZyD\r\n4uph3L6CG7A4UXYTae1X39SJzX+JDNZZMms=\r\n=H4Lm\r\n-----END PGP SIGNATURE-----\r\n", "size": 175140}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.5.5_1682313463985_0.5007447681382411"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-24T05:17:44.214Z", "publish_time": 1682313464214, "hasInstallScript": true}, "6.6.0": {"name": "vue-echarts", "version": "6.6.0", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^5.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^5.0.7", "@vue/cli-plugin-eslint": "^5.0.7", "@vue/cli-plugin-typescript": "^5.0.7", "@vue/cli-service": "^5.0.7", "@vue/compiler-sfc": "^3.2.37", "@vue/composition-api": "^1.7.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.23.3", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "pinia": "^2.1.3", "postcss": "^8.4.14", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.4.0", "typescript": "4.6.4", "vue": "^3.3.4", "vue2": "npm:vue@^2.7.14", "webpack": "^5.73.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.0", "_integrity": "sha512-PpXDe1wKKzOJTLM8RM4GJxpRi+9eiC0YUJR7i44/AAa0oDatvNctJcZlfq/bUV4KV+HDsCeQzaB6JocrXEy9LA==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/d737e1523283f5a18c330a7c6ef513a3/vue-echarts-6.6.0.tgz", "_from": "file:vue-echarts-6.6.0.tgz", "_nodeVersion": "16.14.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-PpXDe1wKKzOJTLM8RM4GJxpRi+9eiC0YUJR7i44/AAa0oDatvNctJcZlfq/bUV4KV+HDsCeQzaB6JocrXEy9LA==", "shasum": "8f5088f42a4f603e8847a52c283616fbcb6630a5", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.0.tgz", "fileCount": 32, "unpackedSize": 724839, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoHqFEXTFi6PTXy1pflAsIHBusKnLWwx1Q54YrNJgtQQIgCtZPIgnFBZSw6XSHaWEzCQaM+tNW8cOmOst8q6w/2SA="}], "size": 179181}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.0_1686659905096_0.09608030907523246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-13T12:38:25.282Z", "publish_time": 1686659905282, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.1": {"name": "vue-echarts", "version": "6.6.1", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.22.9", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.3", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.0.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.4", "@vue/composition-api": "^1.7.1", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "comment-mark": "^1.1.1", "core-js": "^3.32.0", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "pinia": "^2.1.6", "postcss": "^8.4.27", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "qs": "^6.11.2", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.1", "typescript": "4.6.4", "vue": "^3.3.4", "vue2": "npm:vue@^2.7.14", "webpack": "^5.88.2"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.1", "_integrity": "sha512-EpreTzlNeJ+eaUn0AhXEmKJk98xJGecgTqAdyZovoXWnhTxnlW2HuBM0ei3y8rLw1JCUabf8/sYvxjlr8SzBKQ==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/6a2626b4982666cbd88b62d55f272c75/vue-echarts-6.6.1.tgz", "_from": "file:vue-echarts-6.6.1.tgz", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-EpreTzlNeJ+eaUn0AhXEmKJk98xJGecgTqAdyZovoXWnhTxnlW2HuBM0ei3y8rLw1JCUabf8/sYvxjlr8SzBKQ==", "shasum": "5b0398427ea98ba33175bdc80f34af4ce8f27ee4", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.1.tgz", "fileCount": 32, "unpackedSize": 804539, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHpHJtKcb12OowmaWYNMlHEbECmxwkHEq+eMfZzn/midAiA+Oh4uEhlZEbZj7SGI+JZGYPexaAYI1ieUtspIKXyovQ=="}], "size": 199765}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.1_1691168611342_0.9139579088383807"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-04T17:03:31.509Z", "publish_time": 1691168611509, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.2": {"name": "vue-echarts", "version": "6.6.2", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.2", "_integrity": "sha512-Lyb8O33W7/jk6KLeCy77kwLCVpiadNCXFjEQ20t70HRE0DxrrkZ3t6iDmvdAw2JlBy83r9UKZhHjVL6R4ov55A==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/1a9701d02b8c2703e87d447097285af1/vue-echarts-6.6.2.tgz", "_from": "file:vue-echarts-6.6.2.tgz", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-Lyb8O33W7/jk6KLeCy77kwLCVpiadNCXFjEQ20t70HRE0DxrrkZ3t6iDmvdAw2JlBy83r9UKZhHjVL6R4ov55A==", "shasum": "7c0dd9731f6f4e94c96d0c8185eb555125fbb526", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.2.tgz", "fileCount": 32, "unpackedSize": 805683, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBAtrHfOaLw+ZMmd+a0Cd/M6Pw6h8bq9v3IIey940vquAiB5tj3Vr0te8vgN07ShjcjOv+uwVcnB4p0Eg9gECMT52Q=="}], "size": 200202}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.2_1701681307348_0.8626488513503323"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-04T09:15:07.628Z", "publish_time": 1701681307628, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.3": {"name": "vue-echarts", "version": "6.6.3", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.3", "_integrity": "sha512-ogeMZ88QV62h+6Rmfi1ZAScKGX0TrCESTtoSUDNanrVH3lsvquVGNb43805J6pVhNqsgXXf4b5oweFjRw1Mqlw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/b41636483c7b8672bd70dd1cf4673db5/vue-echarts-6.6.3.tgz", "_from": "file:vue-echarts-6.6.3.tgz", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-ogeMZ88QV62h+6Rmfi1ZAScKGX0TrCESTtoSUDNanrVH3lsvquVGNb43805J6pVhNqsgXXf4b5oweFjRw1Mqlw==", "shasum": "a25575dc749c471d4754324822a684b79c6a3f17", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.3.tgz", "fileCount": 32, "unpackedSize": 806628, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfAFReO01UO5vre1jWAgZ5EBxoOCZhtN34s1vmUmpzfAiEA/dzJ8GhzV27EaVQqa1sdPvGlIULT9Aw7FNa8WT4jtck="}], "size": 200451}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.3_1702346423178_0.3759127994981777"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-12T02:00:23.479Z", "publish_time": 1702346423479, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.4": {"name": "vue-echarts", "version": "6.6.4", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.4", "_integrity": "sha512-WtTAdwcLgbCsblOlbOfOr9tOHrR6ufEfoZwPbXHp/HVRXCLlh/GaAIXtqdjGSr/YtCEQNkaW0FpR2c9ukzxVmw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/6cd79baabc72233cb69984dba23a3498/vue-echarts-6.6.4.tgz", "_from": "file:vue-echarts-6.6.4.tgz", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-WtTAdwcLgbCsblOlbOfOr9tOHrR6ufEfoZwPbXHp/HVRXCLlh/GaAIXtqdjGSr/YtCEQNkaW0FpR2c9ukzxVmw==", "shasum": "52f95e92247506683eaf541c85860f6d73b33489", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.4.tgz", "fileCount": 32, "unpackedSize": 806635, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICRVkpFTCQeksncTYl6klo+DdnynQ7JFqsFUODf3PLVaAiEAoyrzQzOBgLO50Nasc/zIHp8Omno7CrVncPUFttVqvbo="}], "size": 200449}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.4_1702436579193_0.7992299448630842"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-13T03:02:59.522Z", "publish_time": 1702436579522, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.5": {"name": "vue-echarts", "version": "6.6.5", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.5", "_integrity": "sha512-L<PERSON>+jQrBu3OZMMp1O00VOdeU1Lnc1tqY1UFmTha8G5ntOsz7NxLYs3+S3l32E53dHdax+YlEh8ZWMHMZGAuELig==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/a6350f583d817a56101e870ecf9dd83a/vue-echarts-6.6.5.tgz", "_from": "file:vue-echarts-6.6.5.tgz", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-L<PERSON>+jQrBu3OZMMp1O00VOdeU1Lnc1tqY1UFmTha8G5ntOsz7NxLYs3+S3l32E53dHdax+YlEh8ZWMHMZGAuELig==", "shasum": "2dee4472b18ac3f72692079be41dcfd82ad628b1", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.5.tgz", "fileCount": 32, "unpackedSize": 806728, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkwR5dAj82IrzGLPpZK6d2UYZ0C1ygwIQ2Bqdju3TLbgIgMsw6Ij80kch+olY0IQRTZ4FojNG/tlLN6E2YIico1G4="}], "size": 200463}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.5_1702866558473_0.2801717319322492"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-18T02:29:18.737Z", "publish_time": 1702866558737, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.6": {"name": "vue-echarts", "version": "6.6.6", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.6", "_integrity": "sha512-xbAWEIrzeRaidZ3+4ixivYbS0+ETNOpj6w4C8MIcnhE4Ca+SQ8wyvA6tO878K3+2i94FQtWA5PNmIJ3hhg7ciw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/fb6b857b9fa09d14a77df169d4b4c9e2/vue-echarts-6.6.6.tgz", "_from": "file:vue-echarts-6.6.6.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-xbAWEIrzeRaidZ3+4ixivYbS0+ETNOpj6w4C8MIcnhE4Ca+SQ8wyvA6tO878K3+2i94FQtWA5PNmIJ3hhg7ciw==", "shasum": "00fdcab4651aebb3180c87a6669b4dcdb8c61e2e", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.6.tgz", "fileCount": 32, "unpackedSize": 807237, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGpNMqQjrfZ+IJbBl7aMqkIPHerElWqW2aLq4clBKsbNAiEAhO+Cz2WkfwD7uit/xmkntp68zU6As3/tv/M1q49vf1o="}], "size": 202368}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.6_1703573726124_0.958414852345195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-26T06:55:26.450Z", "publish_time": 1703573726450, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.7": {"name": "vue-echarts", "version": "6.6.7", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.7", "_integrity": "sha512-nfPJ/n9ub14KctfPF0jYyTLb5Ll4qFcChzkHFoI2q+ONjgWI/cVVXFO+OQKVA0qKJBrKuIOrDz3pJxbEzUMwIQ==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/729355a1057c8c10021d630461112257/vue-echarts-6.6.7.tgz", "_from": "file:vue-echarts-6.6.7.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-nfPJ/n9ub14KctfPF0jYyTLb5Ll4qFcChzkHFoI2q+ONjgWI/cVVXFO+OQKVA0qKJBrKuIOrDz3pJxbEzUMwIQ==", "shasum": "841e972683a5c3aad3c98a22544e9f9d7464e646", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.7.tgz", "fileCount": 33, "unpackedSize": 810202, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgGBh6miiDk83x+v0fYGXf9hrIgpnAKn5wBsMLv6vhnAiEArDCuT2SIOKj/Fuuwh1jP0OjP9Se76NryZnrXlf0Phr8="}], "size": 202435}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.7_1703574052340_0.6234454295258076"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-26T07:00:52.556Z", "publish_time": 1703574052556, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.8": {"name": "vue-echarts", "version": "6.6.8", "description": "Vue.js component for Apache ECharts.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js", "prepublishOnly": "pnpm run build"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "_id": "vue-echarts@6.6.8", "gitHead": "dc9dccbf055bd71e4b471565830b7a892c9b7e54", "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-3EGrxKGCGjHnkhudRQQ4fkK5iJxxXNQ1fXvSWA/7mzR/oV7BBSHYvC3gDbG/WIW0A/Fcx2H8k5H3NDyWgjyi8g==", "shasum": "e00c2a0fcb39681fbb41e0afacbe88aef7e80f24", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.8.tgz", "fileCount": 33, "unpackedSize": 810243, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAxtxSslexHmUsvWHbV012f9qIkp32yFS4Y1DZ3BaWQOAiEAswcAGJ98pj/W0M0Hql9avjq3AqJWj2Hd+C6n1pUJTrA="}], "size": 202073}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.8_1703663967103_0.19342467117839246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-27T07:59:27.386Z", "publish_time": 1703663967386, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.9": {"name": "vue-echarts", "version": "6.6.9", "description": "Vue.js component for Apache ECharts™.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.9", "_integrity": "sha512-mojIq3ZvsjabeVmDthhAUDV8Kgf2Rr/X4lV4da7gEFd1fP05gcSJ0j7wa7HQkW5LlFmF2gdCJ8p4Chas6NNIQQ==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/8843a665f9b5f72a4b3fb0867431dd27/vue-echarts-6.6.9.tgz", "_from": "file:vue-echarts-6.6.9.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-mojIq3ZvsjabeVmDthhAUDV8Kgf2Rr/X4lV4da7gEFd1fP05gcSJ0j7wa7HQkW5LlFmF2gdCJ8p4Chas6NNIQQ==", "shasum": "151372ecd086db985dafeeebd3ea83c8d4d2846b", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.9.tgz", "fileCount": 33, "unpackedSize": 810671, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDRFjqJgyyb11DM2iEQwLpQaIw8X057t2dv1yq4/fLTeAiAdsQwzhtsDoMPiG50GFzcNuC0HL3oWVKc3xCNd66XwKQ=="}], "size": 202578}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.9_1708397958128_0.8787148795730388"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-20T02:59:18.349Z", "publish_time": 1708397958349, "_source_registry_name": "default", "hasInstallScript": true}, "6.6.10": {"name": "vue-echarts", "version": "6.6.10", "description": "Vue.js component for Apache ECharts™.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.23.2", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.1.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.7", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.5.0", "comment-mark": "^1.1.1", "core-js": "^3.33.2", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.2", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.31", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.3.7", "vue2": "npm:vue@^2.7.15", "webpack": "^5.89.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.6.10", "_integrity": "sha512-jLj0IJ9eQ01yR6jObprLQF0pMqarlnzsIgt4jtU39sCasebjSFOXvE9+J09ZeKLUgWWSlfl0Nb2cYbtkR9T52g==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/bff7b962b9899ad23121f5939ef58291/vue-echarts-6.6.10.tgz", "_from": "file:vue-echarts-6.6.10.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-jLj0IJ9eQ01yR6jObprLQF0pMqarlnzsIgt4jtU39sCasebjSFOXvE9+J09ZeKLUgWWSlfl0Nb2cYbtkR9T52g==", "shasum": "af3890d2455860ba697c389878a5a69e086997c2", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.6.10.tgz", "fileCount": 33, "unpackedSize": 811367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXFuzY0GG9IXzzUpwAQxR6el9gjKhYFfVmYDilsOG9RAiAC2D+s+klytamV7m5UXFF85CK5fClS4oFrEkEpPbp1iQ=="}], "size": 202745}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.6.10_1713754898147_0.04225424939322453"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T03:01:38.396Z", "publish_time": 1713754898396, "_source_registry_name": "default", "hasInstallScript": true}, "6.7.0": {"name": "vue-echarts", "version": "6.7.0", "description": "Vue.js component for Apache ECharts™.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.24.4", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.2.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.23", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.9.0", "comment-mark": "^1.1.1", "core-js": "^3.37.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.12", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.38", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.4.23", "vue2": "npm:vue@^2.7.16", "webpack": "^5.91.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.7.0", "_integrity": "sha512-dP2HKr/I1MswH5OzkZqR/JmSpFpgJmJrOH7FjWsch6SkcicmUHV0qs9h5CWKWNbs201I6CX+l8zw0F0RUKLAtA==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/f2243cfda25159418a855f063cbc727c/vue-echarts-6.7.0.tgz", "_from": "file:vue-echarts-6.7.0.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-dP2HKr/I1MswH5OzkZqR/JmSpFpgJmJrOH7FjWsch6SkcicmUHV0qs9h5CWKWNbs201I6CX+l8zw0F0RUKLAtA==", "shasum": "a8ee53b394a8ce906fb931fc2c509e558dbe13c0", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.7.0.tgz", "fileCount": 33, "unpackedSize": 840340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBwCEumzkkrIbFXtD7WIn/wfj085g+4jv0ek5/Szmu2QIhANuhOoSMAT5iVqjtnc/H3etx7rpE5F0HXhuhYLX3/1YT"}], "size": 210366}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.7.0_1713791781291_0.6327128011259322"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T13:16:21.567Z", "publish_time": 1713791781567, "_source_registry_name": "default", "hasInstallScript": true}, "6.7.1": {"name": "vue-echarts", "version": "6.7.1", "description": "Vue.js component for Apache ECharts™.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.24.4", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.2.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.24", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.9.0", "comment-mark": "^1.1.1", "core-js": "^3.37.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.12", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.38", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.4.24", "vue2": "npm:vue@^2.7.16", "webpack": "^5.91.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.7.1", "_integrity": "sha512-HYn54QBk1ILaQ+qA1PQGHwm3MsEFTD5NN9bmgk6wco8LcwKLnRC4HRM0ualYDGOJcCccTpnqD5DvIG0UH1+wDQ==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/a5561d778aa517272f922d6d851f82dc/vue-echarts-6.7.1.tgz", "_from": "file:vue-echarts-6.7.1.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-HYn54QBk1ILaQ+qA1PQGHwm3MsEFTD5NN9bmgk6wco8LcwKLnRC4HRM0ualYDGOJcCccTpnqD5DvIG0UH1+wDQ==", "shasum": "0a44912e4c6aff568f1b637cabe38efef17cca71", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.7.1.tgz", "fileCount": 33, "unpackedSize": 837859, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFcO/tZIBuqCs2emLQ9K/6xxYI49tui/iZ8zFIedh5UzAiEAlLNhGzrBJIq6w4WNXLfX2QC25dGJ+/FpqD+h3jFdXc0="}], "size": 210367}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.7.1_1713873062796_0.5597197787034123"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T11:51:03.021Z", "publish_time": 1713873063021, "_source_registry_name": "default", "hasInstallScript": true}, "6.7.2": {"name": "vue-echarts", "version": "6.7.2", "description": "Vue.js component for Apache ECharts™.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.24.4", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.2.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.24", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.9.0", "comment-mark": "^1.1.1", "core-js": "^3.37.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.12", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.38", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.4.24", "vue2": "npm:vue@^2.7.16", "webpack": "^5.91.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.7.2", "_integrity": "sha512-SG8Vmszhx24KjtySsk361DogZLRkPCyLhgoyh7iN1eH3WGJ0kyl3k0g4QiSJqK0+F1Ej0HDopq4A5OGcBlAwzw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/07595aa16b880f846c30b44227b78104/vue-echarts-6.7.2.tgz", "_from": "file:vue-echarts-6.7.2.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-SG8Vmszhx24KjtySsk361DogZLRkPCyLhgoyh7iN1eH3WGJ0kyl3k0g4QiSJqK0+F1Ej0HDopq4A5OGcBlAwzw==", "shasum": "66f3d9dcd9535f1d39a20f890b870a3e5c6598a8", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.7.2.tgz", "fileCount": 33, "unpackedSize": 837929, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEm4kE8mkHEQemeVcg1zu5K2WkDWhPszeMIbuG+BdRhWAiEA1k3d7ODGCksmVXw4SQduAuDedczkquyAF3BYvFZLf+E="}], "size": 210420}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.7.2_1715075120215_0.18895033222179203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T09:45:20.400Z", "publish_time": 1715075120400, "_source_registry_name": "default", "hasInstallScript": true}, "6.7.3": {"name": "vue-echarts", "version": "6.7.3", "description": "Vue.js component for Apache ECharts™.", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "unpkg": "dist/index.umd.min.js", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.24.4", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.2.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.24", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.9.0", "comment-mark": "^1.1.1", "core-js": "^3.37.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.12", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.38", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.4.24", "vue2": "npm:vue@^2.7.16", "webpack": "^5.91.0"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "types": "dist/index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "postinstall": "node ./scripts/postinstall.js"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@6.7.3", "_integrity": "sha512-vXLKpALFjbPphW9IfQPOVfb1KjGZ/f8qa/FZHi9lZIWzAnQC1DgnmEK3pJgEkyo6EP7UnX6Bv/V3Ke7p+qCNXA==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/3a3d21196452f474fd9f0ca1a6b97f0f/vue-echarts-6.7.3.tgz", "_from": "file:vue-echarts-6.7.3.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-vXLKpALFjbPphW9IfQPOVfb1KjGZ/f8qa/FZHi9lZIWzAnQC1DgnmEK3pJgEkyo6EP7UnX6Bv/V3Ke7p+qCNXA==", "shasum": "30efafc51a4a9de1b8117d3b63e74b0c761ff3ba", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.7.3.tgz", "fileCount": 33, "unpackedSize": 838013, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICRvaqSvk5rBWEMdm+LECar3e9xqegoYn2GuBlm4FnOxAiAdeRfEdKRGGSxbtx1nybHN61SxJ4tkC0owuCldzDOvBA=="}], "size": 210457}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_6.7.3_1717513947036_0.4068321110884088"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-04T15:12:27.273Z", "publish_time": 1717513947273, "_source_registry_name": "default", "hasInstallScript": true}, "7.0.0-beta.0": {"name": "vue-echarts", "version": "7.0.0-beta.0", "description": "Vue.js component for Apache ECharts™.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "type": "module", "main": "dist/index.js", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./csp": "./dist/csp/index.js", "./csp/style.css": "./dist/csp/style.css"}, "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}, "devDependencies": {"@babel/core": "^7.24.9", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vercel/analytics": "^1.3.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.33", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vueuse/core": "^10.11.0", "comment-mark": "^1.1.1", "core-js": "^3.37.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.23.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "highlight.js": "^11.10.0", "pinia": "^2.1.7", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "publint": "^0.2.9", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-import-css": "^3.5.0", "tslib": "^2.6.3", "typescript": "5.5.4", "vue": "^3.4.33", "vue2": "npm:vue@^2.7.16", "webpack": "^5.93.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:rollup", "build:rollup": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "publint": "publint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.mjs"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@7.0.0-beta.0", "_integrity": "sha512-DKmPak9+Al/GlIexC5OvvWIDyO39q/AmZm2aqF7+n2fTDyUq7l/dkdmg/9hBeLafZlU6Yf5J7io/BnDzzRK1+g==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/d0d52826122b631e56ac4ffcf92ac219/vue-echarts-7.0.0-beta.0.tgz", "_from": "file:vue-echarts-7.0.0-beta.0.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-DKmPak9+Al/GlIexC5OvvWIDyO39q/AmZm2aqF7+n2fTDyUq7l/dkdmg/9hBeLafZlU6Yf5J7io/BnDzzRK1+g==", "shasum": "219e331e24f13ecdee3c2e5daa1cbc6e0961cf87", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-7.0.0-beta.0.tgz", "fileCount": 15, "unpackedSize": 182422, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPJOuuqtO1Xqx5FihnR+tdI2ibxE9T2XOyR6efW4xNeAIhANxI7tOk5Ywp66CCcpTHFP5kpzKInUWTUTPtRJyM165o"}], "size": 29150}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_7.0.0-beta.0_1721817164261_0.35555478455841305"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-24T10:32:44.426Z", "publish_time": 1721817164426, "_source_registry_name": "default"}, "7.0.0": {"name": "vue-echarts", "version": "7.0.0", "description": "Vue.js component for Apache ECharts™.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "type": "module", "main": "dist/index.js", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./csp": "./dist/csp/index.js", "./csp/style.css": "./dist/csp/style.css"}, "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}, "devDependencies": {"@babel/core": "^7.24.9", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vercel/analytics": "^1.3.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.33", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vueuse/core": "^10.11.0", "comment-mark": "^1.1.1", "core-js": "^3.37.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.23.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "highlight.js": "^11.10.0", "pinia": "^2.1.7", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "publint": "^0.2.9", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-import-css": "^3.5.0", "tslib": "^2.6.3", "typescript": "5.5.4", "vue": "^3.4.33", "vue2": "npm:vue@^2.7.16", "webpack": "^5.93.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:rollup", "build:rollup": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "publint": "publint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.mjs"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@7.0.0", "_integrity": "sha512-lMSn7twd81eC/Sw4wwCsOsg9+upY8ToRe7nTA6fCfL2C/kozvpkcl5Q92NAFondSgPQaGq13P/f/fjFW7thq3w==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/73d6d008b6a53da10835345820c75c83/vue-echarts-7.0.0.tgz", "_from": "file:vue-echarts-7.0.0.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-lMSn7twd81eC/Sw4wwCsOsg9+upY8ToRe7nTA6fCfL2C/kozvpkcl5Q92NAFondSgPQaGq13P/f/fjFW7thq3w==", "shasum": "8be7f60fc5b25d6fba96a95ddbb9047f82337570", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-7.0.0.tgz", "fileCount": 15, "unpackedSize": 180014, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH0zzjSlCz39X3409A6wwNSozl0ty0YTSKUlaFeTvez5AiAVX6mEe9wYdvrlvYXG6NPuU9dLN3yWIoAh+L5/MFOsQQ=="}], "size": 28333}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_7.0.0_1722673109420_0.08352476281382937"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-03T08:18:29.609Z", "publish_time": 1722673109609, "_source_registry_name": "default"}, "7.0.1": {"name": "vue-echarts", "version": "7.0.1", "description": "Vue.js component for Apache ECharts™.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "type": "module", "main": "dist/index.js", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./csp": "./dist/csp/index.js", "./csp/style.css": "./dist/csp/style.css"}, "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}, "devDependencies": {"@babel/core": "^7.24.9", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vercel/analytics": "^1.3.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.33", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vueuse/core": "^10.11.0", "comment-mark": "^1.1.1", "core-js": "^3.37.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.23.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "highlight.js": "^11.10.0", "pinia": "^2.1.7", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "publint": "^0.2.9", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-import-css": "^3.5.0", "tslib": "^2.6.3", "typescript": "5.5.4", "vue": "^3.4.33", "vue2": "npm:vue@^2.7.16", "webpack": "^5.93.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:rollup", "build:rollup": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "publint": "publint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.mjs"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@7.0.1", "_integrity": "sha512-P+u6mwAtHuPKC3uDQ+sqIhwBBTvrvEFvgHYzV2Ba1/7+rSzRd6o3Dv/A8PuLP24hB1zPPNfu6BvNyD139X1qbg==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/b6036379cff76468cba579c8031b4a92/vue-echarts-7.0.1.tgz", "_from": "file:vue-echarts-7.0.1.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-P+u6mwAtHuPKC3uDQ+sqIhwBBTvrvEFvgHYzV2Ba1/7+rSzRd6o3Dv/A8PuLP24hB1zPPNfu6BvNyD139X1qbg==", "shasum": "888fa9abe882e67bd451bf862d67fa7d4b1620a8", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-7.0.1.tgz", "fileCount": 15, "unpackedSize": 179594, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDUfDt759g+W8/Z7prVFzk0Zn1Avht6qrAHm8yT5AXL0AiBksZxKTCGxZwa0j4DX20Z6NN8m/GJlGu1/zDvR4kaK7g=="}], "size": 28225}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_7.0.1_1722728354952_0.3769940842308699"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-03T23:39:15.201Z", "publish_time": 1722728355201, "_source_registry_name": "default"}, "7.0.2": {"name": "vue-echarts", "version": "7.0.2", "description": "Vue.js component for Apache ECharts™.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "type": "module", "main": "dist/index.js", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./csp": "./dist/csp/index.js", "./csp/style.css": "./dist/csp/style.css"}, "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}, "devDependencies": {"@babel/core": "^7.24.9", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vercel/analytics": "^1.3.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.33", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vueuse/core": "^10.11.0", "comment-mark": "^1.1.1", "core-js": "^3.37.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.23.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "highlight.js": "^11.10.0", "pinia": "^2.1.7", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "publint": "^0.2.9", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "@justfork/rollup-plugin-import-css": "^3.5.1", "tslib": "^2.6.3", "typescript": "5.5.4", "vue": "^3.4.33", "vue2": "npm:vue@^2.7.16", "webpack": "^5.93.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:rollup", "build:rollup": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "publint": "publint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.mjs"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@7.0.2", "_integrity": "sha512-7a+3yKOpmn5enVmigut1QEJKw31DgIWGhzYlGCPi8pNBK5+l1d6XGZMVD/5BgijP5w2aOH3lgEF1Bou1c1QfTw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/89e8ecd745e8f9a605e9ca148b2ab4aa/vue-echarts-7.0.2.tgz", "_from": "file:vue-echarts-7.0.2.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-7a+3yKOpmn5enVmigut1QEJKw31DgIWGhzYlGCPi8pNBK5+l1d6XGZMVD/5BgijP5w2aOH3lgEF1Bou1c1QfTw==", "shasum": "cc2a2af1a9dcc5667509f96bff9adad4698a6076", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-7.0.2.tgz", "fileCount": 15, "unpackedSize": 179664, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA3+egcBrv9jnFyc6GhdCzIm9D757Mv8ZkTTQOm2J+vDAiEAsuV8oWnlJFDM6mLVT8vVynZnfFhpML43bElA5VgmknE="}], "size": 28236}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_7.0.2_1723534334118_0.7217570180923238"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-13T07:32:14.280Z", "publish_time": 1723534334280, "_source_registry_name": "default"}, "7.0.3": {"name": "vue-echarts", "version": "7.0.3", "description": "Vue.js component for Apache ECharts™.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "type": "module", "main": "dist/index.js", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./csp": "./dist/csp/index.js", "./csp/style.css": "./dist/csp/style.css"}, "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}, "devDependencies": {"@babel/core": "^7.24.9", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vercel/analytics": "^1.3.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.33", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vueuse/core": "^10.11.0", "comment-mark": "^1.1.1", "core-js": "^3.37.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.23.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "highlight.js": "^11.10.0", "pinia": "^2.1.7", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "publint": "^0.2.9", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-import-css": "^3.5.1", "tslib": "^2.6.3", "typescript": "5.5.4", "vue": "^3.4.33", "vue2": "npm:vue@^2.7.16", "webpack": "^5.93.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "pnpm run docs && rimraf dist && pnpm run build:rollup", "build:rollup": "vue-demi-switch 3 && rollup -c rollup.config.js", "lint": "vue-cli-service lint", "publint": "publint", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.mjs"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "_id": "vue-echarts@7.0.3", "_integrity": "sha512-/jSxNwOsw5+dYAUcwSfkLwKPuzTQ0Cepz1LxCOpj2QcHrrmUa/Ql0eQqMmc1rTPQVrh2JQ29n2dhq75ZcHvRDw==", "_resolved": "/private/var/folders/gm/flzrv5v56cdfz8892g8dbly80000gn/T/596642335c9c07cc47535ed192ab73f5/vue-echarts-7.0.3.tgz", "_from": "file:vue-echarts-7.0.3.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-/jSxNwOsw5+dYAUcwSfkLwKPuzTQ0Cepz1LxCOpj2QcHrrmUa/Ql0eQqMmc1rTPQVrh2JQ29n2dhq75ZcHvRDw==", "shasum": "bf79f7ee0144bbdc6aee5610e8443fed91f6abbe", "tarball": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-7.0.3.tgz", "fileCount": 15, "unpackedSize": 179856, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJIuhfyR/3hkGlAUEl86ZGFAEFov7/0eGxvWNq1ZlPOwIhAJS4yOImD5Wlb0YyLTpNJJUcLxwHb+7qTs/fJYN6bL8g"}], "size": 28264}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-echarts_7.0.3_1724082183289_0.07725469461625423"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-19T15:43:03.464Z", "publish_time": 1724082183464, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "homepage": "https://github.com/ecomfe/vue-echarts#readme", "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "_source_registry_name": "default"}