import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';

// 导入 Element Plus
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 导入全局样式
import './styles/index.less';

const app = createApp(App);

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 挂载 Element Plus
app.use(ElementPlus);
// 挂载 Vuex store
app.use(store);
// 挂载路由
app.use(router);

// 全局错误处理
app.config.errorHandler = err => {
  console.error('全局错误:', err);
};

app.mount('#app');
