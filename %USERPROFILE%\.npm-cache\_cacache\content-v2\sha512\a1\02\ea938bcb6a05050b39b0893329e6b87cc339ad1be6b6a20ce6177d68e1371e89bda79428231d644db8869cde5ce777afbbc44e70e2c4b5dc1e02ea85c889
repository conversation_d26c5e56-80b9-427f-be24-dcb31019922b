{"_attachments": {}, "_id": "<PERSON>ler-32", "_rev": "71410-61f1853961011c8ed85baa62", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "dist-tags": {"latest": "1.3.1"}, "license": "Apache-2.0", "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "name": "<PERSON>ler-32", "readme": "# adler32\n\nSigned ADLER-32 algorithm implementation in JS (for the browser and nodejs).\nEmphasis on correctness, performance, and IE6+ support.\n\n## Installation\n\nWith [npm](https://www.npmjs.org/package/adler-32):\n\n```bash\n$ npm install adler-32\n```\n\nIn the browser:\n\n```html\n<script src=\"adler32.js\"></script>\n```\n\nThe browser exposes a variable `ADLER32`.\n\nWhen installed globally, npm installs a script `adler32` that computes the\nchecksum for a specified file or standard input.\n\nThe script will manipulate `module.exports` if available .  This is not always\ndesirable.  To prevent the behavior, define `DO_NOT_EXPORT_ADLER`.\n\n## Usage\n\nIn all cases, the relevant function takes an argument representing data and an\noptional second argument representing the starting \"seed\" (for running hash).\n\nThe return value is a signed 32-bit integer.\n\n- `ADLER32.buf(byte array or buffer[, seed])` assumes the argument is a sequence\n  of 8-bit unsigned integers (nodejs `Buffer`, `Uint8Array` or array of bytes).\n\n- `ADLER32.bstr(binary string[, seed])` assumes the argument is a binary string\n  where byte `i` is the low byte of the UCS-2 char: `str.charCodeAt(i) & 0xFF`\n\n- `ADLER32.str(string)` assumes the argument is a standard JS string and\n  calculates the hash of the UTF-8 encoding.\n\nFor example:\n\n```js\n// var ADLER32 = require('adler-32');           // uncomment if in node\nADLER32.str(\"SheetJS\")                          // 176947863\nADLER32.bstr(\"SheetJS\")                         // 176947863\nADLER32.buf([ 83, 104, 101, 101, 116, 74, 83 ]) // 176947863\n\nadler32 = ADLER32.buf([83, 104])                // 17825980  \"Sh\"\nadler32 = ADLER32.str(\"eet\", adler32)           // 95486458  \"Sheet\"\nADLER32.bstr(\"JS\", adler32)                     // 176947863  \"SheetJS\"\n\n[ADLER32.str(\"\\u2603\"),  ADLER32.str(\"\\u0003\")]  // [ 73138686, 262148 ]\n[ADLER32.bstr(\"\\u2603\"), ADLER32.bstr(\"\\u0003\")] // [ 262148,   262148 ]\n[ADLER32.buf([0x2603]),  ADLER32.buf([0x0003])]  // [ 262148,   262148 ]\n```\n\n## Testing\n\n`make test` will run the nodejs-based test.\n\nTo run the in-browser tests, run a local server and go to the `ctest` directory.\n`make ctestserv` will start a python `SimpleHTTPServer` server on port 8000.\n\nTo update the browser artifacts, run `make ctest`.\n\nTo generate the bits file, use the `adler32` function from python `zlib`:\n\n```python\n>>> from zlib import adler32\n>>> x=\"foo bar baz٪☃🍣\"\n>>> adler32(x)\n1543572022\n>>> adler32(x+x)\n-2076896149\n>>> adler32(x+x+x)\n2023497376\n```\n\nThe [`adler32-cli`](https://www.npmjs.com/package/adler32-cli) package includes\nscripts for processing files or text on standard input:\n\n```bash\n$ echo \"this is a test\" > t.txt\n$ adler32-cli t.txt\n726861088\n```\n\nFor comparison, the `adler32.py` script in the subdirectory uses python `zlib`:\n\n```bash\n$ packages/adler32-cli/bin/adler32.py t.txt\n726861088\n```\n\n## Performance\n\n`make perf` will run algorithmic performance tests (which should justify certain\ndecisions in the code).\n\nBit twiddling is much faster than taking the mod in Safari and Firefox browsers.\nInstead of taking the literal mod 65521, it is faster to keep it in the integers\nby bit-shifting: `65536 ~ 15 mod 65521` so for nonnegative integer `a`:\n\n```\n    a = (a >>> 16) * 65536 + (a & 65535)            [equality]\n    a ~ (a >>> 16) * 15    + (a & 65535) mod 65521\n```\n\nThe mod is taken at the very end, since the intermediate result may exceed 65521\n\n## Magic Number\n\nThe magic numbers were chosen so as to not overflow a 31-bit integer:\n\n```mathematica\nF[n_] := Reduce[x*(x + 1)*n/2 + (x + 1)*(65521) < (2^31 - 1) && x > 0, x, Integers]\nF[255] (* bstr:  x \\[Element] Integers && 1 <= x <= 3854 *)\nF[127] (* ascii: x \\[Element] Integers && 1 <= x <= 5321 *)\n```\n\nSubtract up to 4 elements for the Unicode case.\n\n## License\n\nPlease consult the attached LICENSE file for details.  All rights not explicitly\ngranted by the Apache 2.0 license are reserved by the Original Author.\n\n## Badges\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/adler32.svg)](https://saucelabs.com/u/adler32)\n\n[![Build Status](https://img.shields.io/github/workflow/status/sheetjs/js-adler32/Tests:%20node.js)](https://github.com/SheetJS/js-adler32/actions)\n\n[![Coverage Status](http://img.shields.io/coveralls/SheetJS/js-adler32/master.svg)](https://coveralls.io/r/SheetJS/js-adler32?branch=master)\n\n[![Analytics](https://ga-beacon.appspot.com/***********-1/SheetJS/js-adler32?pixel)](https://github.com/SheetJS/js-adler32)\n", "time": {"created": "2022-01-26T17:30:33.710Z", "modified": "2023-07-28T06:37:21.427Z", "1.3.0": "2021-04-18T17:27:43.712Z", "1.2.0": "2018-01-17T19:38:22.848Z", "1.1.0": "2017-07-28T09:06:20.828Z", "1.0.0": "2016-10-13T02:49:40.500Z", "0.4.0": "2016-06-16T17:23:15.637Z", "0.3.0": "2016-01-16T06:24:15.418Z", "0.2.1": "2014-07-06T17:00:06.590Z", "0.2.0": "2014-06-20T03:53:37.065Z", "0.1.0": "2014-06-18T18:02:14.676Z", "1.3.1": "2022-03-30T02:30:17.495Z"}, "versions": {"1.3.0": {"name": "<PERSON>ler-32", "version": "1.3.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "main": "./adler32", "types": "types", "dependencies": {"printj": "~1.2.2"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "adler32.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b7700fd5fa4c1ecfb4e67d20ed0248154940fcdb", "_id": "<PERSON><PERSON>-32@1.3.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "3cad1b71cdfa69f6c8a91f3e3615d31a4fdedc72", "size": 7781, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.0.tgz", "integrity": "sha512-f5nltvjl+PRUh6YNfUstRaXwJxtfnKEWhAWWlmKvh+Y3J2+98a0KKVYDEhz6NdKGqswLhjNGznxfSsZGOvOd9g=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/adler-32_1.3.0_1618766863534_0.5835994515984018"}, "_hasShrinkwrap": false, "publish_time": 1618766863712, "_cnpm_publish_time": 1618766863712, "_cnpmcore_publish_time": "2021-12-16T10:35:55.897Z"}, "1.2.0": {"name": "<PERSON>ler-32", "version": "1.2.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "bin": {"adler32": "./bin/adler32.njs"}, "main": "./adler32", "types": "types", "dependencies": {"printj": "~1.1.0", "exit-on-epipe": "~1.0.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "adler32.js"}}, "homepage": "http://sheetjs.com/opensource", "files": ["adler32.js", "bin/adler32.njs", "LICENSE", "README.md", "types/index.d.ts", "types/*.json"], "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d75071081036cc5648af377cec2301e873fa0836", "_id": "<PERSON><PERSON>-32@1.2.0", "_shasum": "6a3e6bf0a63900ba15652808cb15c6813d1a5f25", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "6a3e6bf0a63900ba15652808cb15c6813d1a5f25", "size": 5394, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-1.2.0.tgz", "integrity": "sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/adler-32-1.2.0.tgz_1516217901918_0.7342358850874007"}, "directories": {}, "publish_time": 1516217902848, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516217902848, "_cnpmcore_publish_time": "2021-12-16T10:35:56.201Z"}, "1.1.0": {"name": "<PERSON>ler-32", "version": "1.1.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "bin": {"adler32": "./bin/adler32.njs"}, "main": "./adler32", "dependencies": {"printj": "~1.1.0", "exit-on-epipe": "~1.0.1"}, "devDependencies": {"mocha": "~2.5.3", "codepage": "~1.10.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "files": ["adler32.js", "bin/adler32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "06fac2b06374aa2b19b9767ecd41d5dba75b228d", "homepage": "https://github.com/SheetJS/js-adler32#readme", "_id": "<PERSON><PERSON>-32@1.1.0", "_shasum": "03551a5c7f0edfbd4fc8fa12a6814978eab651c3", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "03551a5c7f0edfbd4fc8fa12a6814978eab651c3", "size": 4799, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-1.1.0.tgz", "integrity": "sha512-lRKKX9RZQBPy6CrdUqiDUsxVcZujjbkkUg++0zLLyi0EwRui+aFyEDJBXRXCqwp+pmmybdZgBNHxOAOQcgdJYg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/adler-32-1.1.0.tgz_1501232779957_0.9965898292139173"}, "directories": {}, "publish_time": 1501232780828, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501232780828, "_cnpmcore_publish_time": "2021-12-16T10:35:56.590Z"}, "1.0.0": {"name": "<PERSON>ler-32", "version": "1.0.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "bin": {"adler32": "./bin/adler32.njs"}, "main": "./adler32", "dependencies": {"concat-stream": "", "printj": "", "exit-on-epipe": ""}, "devDependencies": {"mocha": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "files": ["adler32.js", "bin/adler32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "318cb4ebd4c4fbc58cd2f369bc845fc9969c9079", "_id": "<PERSON>ler-32@1.0.0", "_shasum": "28728a71756f629666dd1653cd80793a9df18651", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "28728a71756f629666dd1653cd80793a9df18651", "size": 4698, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-1.0.0.tgz", "integrity": "sha512-kH0TVBEB75TEziSFdX+ot2jbdq52OOA4eWQSv9KVx6U4Y3eg8uebS0Zw3hszufidzA733WcAgA6BzFBw/L2cfw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/adler-32-1.0.0.tgz_1476326978547_0.30778620345517993"}, "directories": {}, "publish_time": 1476326980500, "_hasShrinkwrap": false, "_cnpm_publish_time": 1476326980500, "_cnpmcore_publish_time": "2021-12-16T10:35:57.085Z"}, "0.4.0": {"name": "<PERSON>ler-32", "version": "0.4.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "bin": {"adler32": "./bin/adler32.njs"}, "main": "./adler32", "dependencies": {"concat-stream": "", "exit-on-epipe": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "files": ["adler32.js", "bin/adler32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b692b3f7dfd2928c5e9a9544d71c2ec80cdc85d7", "_id": "<PERSON>ler-32@0.4.0", "_shasum": "248d7c00b2ecf703cbd61e553d7ea3405def56b2", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "248d7c00b2ecf703cbd61e553d7ea3405def56b2", "size": 2057, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-0.4.0.tgz", "integrity": "sha512-9n7wXCvBxw+y06NDZmjgMp0CBPmx5Py95JBB8+gc0/7UrjudOfe/JiAyD35r9yElpiiMEE43WwRKM6kng8w3Zw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/adler-32-0.4.0.tgz_1466097794542_0.7036323617212474"}, "directories": {}, "publish_time": 1466097795637, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466097795637, "_cnpmcore_publish_time": "2021-12-16T10:35:57.296Z"}, "0.3.0": {"name": "<PERSON>ler-32", "version": "0.3.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "main": "./adler32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "files": ["adler32.js", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8215562b118598df163c8e38fad8fbf2e31c8fc3", "homepage": "https://github.com/SheetJS/js-adler32", "_id": "<PERSON>ler-32@0.3.0", "_shasum": "e66de041c3b3db7f0b3fe262fa67e4e1c6aa5fe0", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.9", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "e66de041c3b3db7f0b3fe262fa67e4e1c6aa5fe0", "size": 2962, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-0.3.0.tgz", "integrity": "sha512-iRUqGF0GSYR8ZWcbDCaAmiBlNyK9THIGch/bJlDA/fhr3CKY7qTw+VaA6sE0G3+qYjbF26xowl8o5zCszVc3yQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452925455418, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452925455418, "_cnpmcore_publish_time": "2021-12-16T10:35:57.530Z"}, "0.2.1": {"name": "<PERSON>ler-32", "version": "0.2.1", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "main": "./adler32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "files": ["adler32.js", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "77228baf0a3caf1318ef5cbcf14acfa596dd7914", "homepage": "https://github.com/SheetJS/js-adler32", "_id": "<PERSON>ler-32@0.2.1", "_shasum": "1aa46a9961089688fb88e2542f112fc82cdd6496", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "1aa46a9961089688fb88e2542f112fc82cdd6496", "size": 2878, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-0.2.1.tgz", "integrity": "sha512-ywRJ47dckM2v7dKu+2Vp1lU4FBMi0c8a8ddZNuGwUnBVNliFJipcMZMSkoQpYyO7pdEYI/vjdHhPgV2gS78SwA=="}, "directories": {}, "publish_time": 1404666006590, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404666006590, "_cnpmcore_publish_time": "2021-12-16T10:35:57.749Z"}, "0.2.0": {"name": "<PERSON>ler-32", "version": "0.2.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "main": "./adler32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d0e9ff9b3dacf7dd105748104d7b4748537e1a76", "homepage": "https://github.com/SheetJS/js-adler32", "_id": "<PERSON>ler-32@0.2.0", "_shasum": "42ac2f155b7bc1b12e12ac0822246977df73812c", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "42ac2f155b7bc1b12e12ac0822246977df73812c", "size": 36547, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-0.2.0.tgz", "integrity": "sha512-ZHPdF0Zrp83v8zi4oBxZU/t3dmKFXBS36mrV253agAtvA0L9+nKOQOvsNOmKo0+dqmS4w9IgA6cakro2l9sAxA=="}, "directories": {}, "publish_time": 1403236417065, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403236417065, "_cnpmcore_publish_time": "2021-12-16T10:35:58.059Z"}, "0.1.0": {"name": "<PERSON>ler-32", "version": "0.1.0", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "main": "./adler32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "adler32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b53334db84fda12cb760494496afb8b7a85962f1", "homepage": "https://github.com/SheetJS/js-adler32", "_id": "<PERSON>ler-32@0.1.0", "_shasum": "17abef523658af846d84e6b83757f7d9ef032ab9", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "17abef523658af846d84e6b83757f7d9ef032ab9", "size": 36059, "noattachment": false, "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-0.1.0.tgz", "integrity": "sha512-cJfG59oaNoWpce1zPyzlNsQtjknCt4eDqtA/NOazPCzENunysCSSFtAaHcDJBI/JMGXZ43I29dxIYBKj1IYM/g=="}, "directories": {}, "publish_time": 1403114534676, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403114534676, "_cnpmcore_publish_time": "2021-12-16T10:35:58.338Z"}, "1.3.1": {"name": "<PERSON>ler-32", "version": "1.3.1", "author": {"name": "sheetjs"}, "description": "Pure-JS ADLER-32", "keywords": ["adler32", "checksum"], "main": "./adler32", "types": "types/index.d.ts", "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "adler32.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "40515a860d21075ddc963247ddf75c0ae0c411af", "_id": "<PERSON><PERSON>-32@1.3.1", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==", "shasum": "1dbf0b36dda0012189a32b3679061932df1821e2", "tarball": "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz", "fileCount": 7, "unpackedSize": 20386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQ8C5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmri8RAAop7oUW2h7+37qafBykZbh6FHmAVlRV5/aBhdijAjLvOoNdx6\r\nETT4p7Zb4arkBYs89OdGyR/pYFgERtSU5es5ZEf/Hk4hWxdb7hzGttUDueSr\r\nylkyAKGUa2VC5NgxO33Xg/QCXg2Nb0wwC2W4j51uqiHjoKjdFIugPzBfKGAl\r\nOcyO8cdfP7/mbwHC09LzI+XVrS2I3AE8AjFDIE0VglJ6drFU7sjH6JVx2xsg\r\njrtBP75Gmhox6v83/tcDXVSc4WjHGUF+lqMAndru3LtUna3Oeduxm/Jx1LY/\r\nqVjwATX/b2/Jy8dzRmvvbb4bNpMbJFR6Hop6pWRoobXBO7JqyldTghi94d0l\r\nbE/+DWU/Zr3kJniUndpdH1DvoPn44j2YoKiu/BZZ4pQyyHOx9sUh9iNwkZuz\r\ndKDTQ/z16VLcLmoIWek51CyUyVUNGdHmrGQBnSDcS/l8kSkPDHTeOXGw/GS6\r\ncjKsRs5YLUKULLsqBOF0ivw9mzyZI7tl3/47JXNViskzWv5G8JA4Mk0+qPN5\r\n6AZ//WHz5zZ+XuAHaPVzh7TM5ZicqCKPVIazoENoEyKpmnwN9B5n/RS4cyNk\r\nTm/lHWvCnc6azl58v1eJ4u0/x2srbWUtXSh+2Qr2Z8cmUHNkbVoFuTIOI1hP\r\nLmrI61JdoBhs3KGa6zwcEmMd9sVIKHqJQ0Y=\r\n=GzMj\r\n-----END PGP SIGNATURE-----\r\n", "size": 7763}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/adler-32_1.3.1_1648607417337_0.5143717226072202"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-30T02:30:22.978Z"}}, "bugs": {"url": "https://github.com/SheetJS/js-adler32/issues"}, "homepage": "http://sheetjs.com/opensource", "keywords": ["adler32", "checksum"], "repository": {"type": "git", "url": "git://github.com/SheetJS/js-adler32.git"}, "_source_registry_name": "default"}