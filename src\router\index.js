import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '../layout/MainLayout.vue';
import ProjectDashboard from '../views/Dashboard.vue';
import ProjectManagement from '../views/Projects.vue';
import TaskManagement from '../views/Tasks.vue';
import UserManagement from '../views/Users.vue';
import UserDashboard from '../views/UserDashboard.vue';

const routes = [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '/dashboard',
        component: ProjectDashboard,
        meta: { requiresAuth: false },
      },
      {
        path: '/projects',
        component: ProjectManagement,
        meta: { requiresAuth: true },
      },
      {
        path: '/tasks',
        component: TaskManagement,
        meta: { requiresAuth: true },
      },
      {
        path: '/users',
        component: UserManagement,
        meta: { requiresAuth: true },
      },
      {
        path: '/UserDashboard',
        component: UserDashboard,
        meta: { requiresAuth: true },
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginModal.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 添加全局前置守卫
router.beforeEach((to, _from, next) => {
  const token = localStorage.getItem('token');
  const role = localStorage.getItem('role');

  if (to.meta.requiresAuth) {
    if (!token) {
      next('/login');
    } else {
      if (to.meta.requiresAdmin && role !== 'admin') {
        next('/dashboard'); // 非管理员用户重定向到仪表盘
      } else {
        next();
      }
    }
  } else {
    if (to.path === '/login' && token) {
      next('/dashboard'); // 已登录用户访问登录页则重定向到仪表盘
    } else {
      next();
    }
  }
});

export default router;
