{"_attachments": {}, "_id": "@vue/cli-plugin-eslint", "_rev": "288669-61f1c47f61011c8ed8680ea6", "author": {"name": "<PERSON>"}, "description": "eslint plugin for vue-cli", "dist-tags": {"latest": "5.0.8", "next": "5.0.6"}, "license": "MIT", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "akryum", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "name": "@vue/cli-plugin-eslint", "readme": "# @vue/cli-plugin-eslint\n\n> eslint plugin for vue-cli\n\n## Injected Commands\n\n- **`vue-cli-service lint`**\n\n  ```\n  Usage: vue-cli-service lint [options] [...files]\n\n  Options:\n\n    --format [formatter] specify formatter (default: stylish)\n    --no-fix             do not fix errors\n    --max-errors         specify number of errors to make build failed (default: 0)\n    --max-warnings       specify number of warnings to make build failed (default: Infinity)\n    --output-file        specify file to write report to\n  ```\n\nLints and fixes files. If no specific files are given, it lints all files in `src` and `tests`, as well as all JavaScript files in the root directory (these are most often config files such as `babel.config.js` or `.eslintrc.js`).\n\nOther [ESLint CLI options](https://eslint.org/docs/user-guide/command-line-interface#options) are not supported.\n\n::: tip\n`vue-cli-service lint` will lint dotfiles `.*.js` by default. If you want to follow ESLint's default behavior instead, consider adding a `.eslintignore` file in your project.\n:::\n\n## Configuration\n\nESLint can be configured via `.eslintrc` or the `eslintConfig` field in `package.json`. See the [ESLint configuration docs](https://eslint.org/docs/user-guide/configuring) for more detail.\n\n::: tip\nThe following option is under the section of [`vue.config.js`](https://cli.vuejs.org/config/#vue-config-js). It is respected only when `@vue/cli-plugin-eslint` is installed.\n:::\n\nLint-on-save during development with `eslint-loader` is enabled by default. It can be disabled with the `lintOnSave` option in `vue.config.js`:\n\n``` js\nmodule.exports = {\n  lintOnSave: false\n}\n```\n\nWhen set to `true`, `eslint-loader` will emit lint errors as warnings. By default, warnings are only logged to the terminal and does not fail the compilation.\n\nTo make lint errors show up in the browser overlay, you can use `lintOnSave: 'error'`. This will force `eslint-loader` to always emit errors. this also means lint errors will now cause the compilation to fail.\n\nAlternatively, you can configure the overlay to display both warnings and errors:\n\n``` js\n// vue.config.js\nmodule.exports = {\n  devServer: {\n    overlay: {\n      warnings: true,\n      errors: true\n    }\n  }\n}\n```\n\nWhen `lintOnSave` is a truthy value, `eslint-loader` will be applied in both development and production. If you want to disable `eslint-loader` during production build, you can use the following config:\n\n``` js\n// vue.config.js\nmodule.exports = {\n  lintOnSave: process.env.NODE_ENV !== 'production'\n}\n```\n\n## Installing in an Already Created Project\n\n```bash\nvue add eslint\n```\n\n## Injected webpack-chain Rules\n\n- `config.module.rule('eslint')`\n- `config.module.rule('eslint').use('eslint-loader')`\n", "time": {"created": "2022-01-26T22:00:31.867Z", "modified": "2023-07-28T20:08:41.067Z", "5.0.0-rc.1": "2021-11-17T03:50:46.309Z", "5.0.0-rc.0": "2021-11-06T12:17:58.019Z", "4.5.15": "2021-10-28T12:37:56.677Z", "5.0.0-beta.7": "2021-10-26T13:49:29.707Z", "4.5.14": "2021-10-14T07:08:34.210Z", "5.0.0-beta.6": "2021-10-14T03:48:53.483Z", "5.0.0-beta.5": "2021-10-10T07:41:11.172Z", "5.0.0-beta.4": "2021-09-15T11:09:03.640Z", "5.0.0-beta.3": "2021-08-10T07:07:08.381Z", "5.0.0-beta.2": "2021-06-09T05:17:11.985Z", "5.0.0-beta.1": "2021-05-14T08:41:41.663Z", "4.5.13": "2021-05-08T00:37:17.705Z", "5.0.0-beta.0": "2021-04-25T09:20:10.030Z", "5.0.0-alpha.8": "2021-03-24T12:53:39.351Z", "4.5.12": "2021-03-17T11:49:25.411Z", "5.0.0-alpha.7": "2021-03-11T08:29:07.019Z", "5.0.0-alpha.6": "2021-03-10T12:48:58.728Z", "5.0.0-alpha.5": "2021-02-23T08:46:17.634Z", "5.0.0-alpha.4": "2021-02-18T07:46:47.864Z", "4.5.11": "2021-01-22T14:56:19.559Z", "5.0.0-alpha.3": "2021-01-22T08:22:25.374Z", "5.0.0-alpha.2": "2021-01-06T07:33:14.393Z", "5.0.0-alpha.1": "2021-01-06T07:27:15.465Z", "4.5.10": "2021-01-06T07:10:02.977Z", "5.0.0-alpha.0": "2020-12-14T11:49:53.443Z", "4.5.9": "2020-11-17T06:51:49.804Z", "4.5.8": "2020-10-19T10:40:52.523Z", "4.5.7": "2020-10-07T08:40:30.239Z", "4.5.6": "2020-09-10T13:34:23.283Z", "4.5.5": "2020-09-10T11:58:02.951Z", "4.5.4": "2020-08-18T02:12:56.767Z", "4.5.3": "2020-08-11T09:10:30.475Z", "4.5.2": "2020-08-10T06:50:12.448Z", "4.5.1": "2020-08-06T15:50:40.901Z", "4.5.0": "2020-07-24T11:43:22.551Z", "4.4.6": "2020-06-24T05:25:05.885Z", "4.4.5": "2020-06-22T12:01:06.300Z", "4.4.4": "2020-06-12T13:01:13.300Z", "4.4.3": "2020-06-12T07:54:31.423Z", "4.4.2": "2020-06-12T07:47:37.374Z", "4.4.1": "2020-05-25T08:11:08.156Z", "4.4.0": "2020-05-19T15:21:11.038Z", "4.3.1": "2020-04-07T15:41:11.785Z", "4.3.0": "2020-04-01T08:01:12.614Z", "4.2.3": "2020-02-27T14:33:26.183Z", "4.2.2": "2020-02-07T11:12:16.450Z", "4.2.1": "2020-02-07T10:11:26.260Z", "4.2.0": "2020-02-07T07:46:13.135Z", "4.1.2": "2019-12-28T12:56:02.422Z", "4.1.1": "2019-11-27T15:16:07.580Z", "4.1.0": "2019-11-27T07:00:06.736Z", "4.1.0-beta.0": "2019-11-09T13:50:52.184Z", "4.0.5": "2019-10-22T11:55:11.413Z", "3.12.1": "2019-10-18T08:37:57.729Z", "4.0.4": "2019-10-18T07:49:31.725Z", "4.0.3": "2019-10-17T08:00:27.041Z", "4.0.2": "2019-10-17T02:24:21.061Z", "4.0.1": "2019-10-16T12:34:50.070Z", "4.0.0": "2019-10-16T11:20:07.424Z", "4.0.0-rc.8": "2019-10-11T08:12:27.702Z", "3.12.0": "2019-10-10T09:01:42.315Z", "4.0.0-rc.7": "2019-10-01T15:37:10.288Z", "4.0.0-rc.6": "2019-09-30T12:29:38.332Z", "4.0.0-rc.5": "2019-09-30T10:41:21.624Z", "4.0.0-rc.4": "2019-09-25T04:32:42.218Z", "4.0.0-rc.3": "2019-09-09T16:07:07.036Z", "4.0.0-rc.2": "2019-09-08T03:54:24.432Z", "4.0.0-rc.1": "2019-09-04T08:21:09.738Z", "3.11.0": "2019-08-21T16:06:29.656Z", "4.0.0-rc.0": "2019-08-21T09:49:51.486Z", "4.0.0-beta.3": "2019-08-08T03:11:26.558Z", "3.10.0": "2019-08-03T12:08:37.757Z", "4.0.0-beta.2": "2019-07-29T16:24:07.203Z", "4.0.0-beta.1": "2019-07-25T02:41:35.586Z", "4.0.0-beta.0": "2019-07-22T14:24:34.090Z", "4.0.0-alpha.5": "2019-07-14T15:14:09.717Z", "4.0.0-alpha.4": "2019-07-06T11:52:23.926Z", "3.9.2": "2019-07-06T11:37:59.846Z", "4.0.0-alpha.3": "2019-07-04T01:10:13.026Z", "3.9.1": "2019-07-04T01:03:31.798Z", "3.9.0": "2019-07-03T08:21:57.584Z", "4.0.0-alpha.2": "2019-07-03T07:43:41.206Z", "3.8.0": "2019-05-25T15:20:58.492Z", "4.0.0-alpha.1": "2019-05-25T09:50:10.668Z", "4.0.0-alpha.0": "2019-04-30T16:21:20.879Z", "3.7.0": "2019-04-28T06:23:15.350Z", "3.6.0": "2019-04-13T15:59:17.670Z", "3.5.1": "2019-03-12T08:05:45.009Z", "3.5.0": "2019-03-08T06:42:32.310Z", "3.4.1": "2019-02-20T06:50:08.336Z", "3.4.0": "2019-01-31T18:45:53.256Z", "3.3.0": "2019-01-08T04:04:55.314Z", "3.2.2": "2018-12-30T13:19:32.526Z", "3.2.1": "2018-11-27T19:03:52.415Z", "3.2.0": "2018-11-27T18:51:52.826Z", "3.1.5": "2018-11-12T08:21:50.260Z", "3.1.4": "2018-11-02T14:56:47.016Z", "3.1.3": "2018-10-31T11:52:37.560Z", "3.1.2": "2018-10-31T11:25:49.144Z", "3.1.1": "2018-10-31T03:58:26.511Z", "3.1.0": "2018-10-30T18:20:14.693Z", "3.0.5": "2018-10-09T09:20:24.998Z", "3.0.4": "2018-09-25T08:01:14.286Z", "3.0.3": "2018-09-12T05:18:43.474Z", "3.0.2": "2018-09-11T14:46:18.748Z", "3.0.1": "2018-08-16T16:10:53.666Z", "3.0.0": "2018-08-10T15:01:44.437Z", "3.0.0-rc.12": "2018-08-09T06:23:49.853Z", "3.0.0-rc.11": "2018-08-07T15:58:11.143Z", "3.0.0-rc.10": "2018-07-30T22:19:33.133Z", "3.0.0-rc.9": "2018-07-29T03:23:56.215Z", "3.0.0-rc.8": "2018-07-27T18:03:44.805Z", "3.0.0-rc.7": "2018-07-27T02:38:39.196Z", "3.0.0-rc.6": "2018-07-26T22:12:17.046Z", "3.0.0-rc.5": "2018-07-16T13:51:53.218Z", "3.0.0-rc.4": "2018-07-13T17:11:33.056Z", "3.0.0-rc.3": "2018-06-18T14:40:09.404Z", "3.0.0-rc.2": "2018-06-14T13:45:04.276Z", "3.0.0-rc.1": "2018-06-13T20:11:29.570Z", "3.0.0-beta.16": "2018-06-08T04:51:45.564Z", "3.0.0-beta.15": "2018-05-30T17:43:08.326Z", "3.0.0-beta.14": "2018-05-29T17:27:30.554Z", "3.0.0-beta.13": "2018-05-29T17:21:44.201Z", "3.0.0-beta.12": "2018-05-29T05:29:25.657Z", "3.0.0-beta.11": "2018-05-21T19:43:30.515Z", "3.0.0-beta.10": "2018-05-11T04:03:10.517Z", "3.0.0-beta.9": "2018-04-28T02:36:08.235Z", "3.0.0-beta.8": "2018-04-27T23:41:07.569Z", "3.0.0-beta.7": "2018-04-25T14:59:00.978Z", "3.0.0-beta.6": "2018-03-06T20:30:47.235Z", "3.0.0-beta.5": "2018-03-05T23:01:17.372Z", "3.0.0-beta.4": "2018-03-05T19:42:43.617Z", "3.0.0-beta.3": "2018-03-03T03:28:35.367Z", "3.0.0-beta.2": "2018-02-28T23:44:56.669Z", "3.0.0-beta.1": "2018-02-16T10:50:15.244Z", "3.0.0-alpha.13": "2018-02-13T18:27:43.400Z", "3.0.0-alpha.12": "2018-02-12T22:11:36.064Z", "3.0.0-alpha.11": "2018-02-09T18:24:49.835Z", "3.0.0-alpha.10": "2018-02-08T22:38:15.595Z", "3.0.0-alpha.9": "2018-02-06T22:50:20.773Z", "3.0.0-alpha.8": "2018-02-04T15:56:24.344Z", "3.0.0-alpha.7": "2018-02-02T22:41:25.127Z", "3.0.0-alpha.6": "2018-02-02T08:51:49.924Z", "3.0.0-alpha.5": "2018-01-29T17:29:35.046Z", "3.0.0-alpha.4": "2018-01-26T23:34:06.655Z", "3.0.0-alpha.3": "2018-01-26T03:45:24.260Z", "3.0.0-alpha.2": "2018-01-25T17:24:07.206Z", "3.0.0-alpha.1": "2018-01-25T16:36:25.168Z", "5.0.0-rc.2": "2022-01-15T15:00:53.750Z", "5.0.0-rc.3": "2022-02-10T08:00:30.111Z", "5.0.0": "2022-02-17T11:13:59.948Z", "5.0.1": "2022-02-17T11:17:13.819Z", "5.0.2": "2022-03-15T13:07:07.950Z", "5.0.3": "2022-03-15T13:13:38.339Z", "4.5.16": "2022-03-15T13:17:47.333Z", "5.0.4": "2022-03-22T14:24:12.170Z", "4.5.17": "2022-03-23T05:55:17.363Z", "5.0.5": "2022-06-16T06:06:51.807Z", "4.5.18": "2022-06-16T14:39:51.874Z", "5.0.6": "2022-06-16T14:41:42.399Z", "4.5.19": "2022-06-28T04:18:31.388Z", "5.0.7": "2022-07-05T08:53:47.795Z", "5.0.8": "2022-07-07T10:22:07.183Z"}, "versions": {"5.0.0-rc.1": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-rc.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-rc.1", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "b0e7bf07d6d7e086985475df713b593cb42ef878", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-rc.1", "_nodeVersion": "14.18.1", "_npmVersion": "lerna/4.0.0/node@v14.18.1+x64 (darwin)", "dist": {"integrity": "sha512-CK/T00XeibQCMHzSenBWDROB11HbCL69bEc1nwT8gjXq5MMw9AB8qlaZzSuoHbbUE0PDyD272V/RDePO1ExT7w==", "shasum": "d6525834d54249ed5df5ba55614eeea3fa836812", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-rc.1.tgz", "fileCount": 16, "unpackedSize": 32786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlHwWCRA9TVsSAnZWagAAxq0QAITGVByTg0mgHvqxn+AZ\nF3lBl4E+9rRtquhWcEpBqCQfdc9HjwrrcHDfAi9TiBa7p4sp311F7Yh5Fljq\nFd6KOgt7rS6ym874HfGycmBkmkD1fg0m/UYe6A7Bmos33J5jl2S20wjAuh1H\nzfYZnjXtRtO/AnCoXfisxGPHSwJpBvHYLOrFiu0RpdCguwVMgEvPInkQBz2N\n9DZlZeFgE/H5mQ2lJ6qmlMUuAuupaSKRljzxa57rv7TRYRcA3nvDOJ3Ei8M5\nVfskjwiJ8g+p3wWx25q7o8F5IuvtLM6yGYrfYUD4MFgY6FJ7a+SUO6p8iMoG\nGn+D9+Lv+rvcZPQWCiBtzsd3NiXReNG0YcQ2EzG37KOG3Qju55auc8Oklfnw\npvOUnHjhqNhbl7aBrvttnKGFDxUzrTqq9IF50TGTndeIx+RU4Jssr8uf2Wia\nUAB+NPTmGLgyd00qp3BKEPcFPip0rwgDJ/lKa3JOJZXvOkLS5ctHL+GYQRPU\nm/ukJC/+/hT3etP0CIUGeayEoSWYgJhz8kOMugs7XNh8hnCIEYLc7d24+Rb2\n5I0Vn+B6O0GabgqlhfQ4qLKBIhRG1If90U7DjjHBQjFK7Yv0rPLahgbJw/Tk\nqcZvVAmXWM2rvtdtTxQREsYOIbEcXBRuU2Eb0JQmsV7MhlXOfDvF2XtXaNpH\nn+iD\r\n=XPx6\r\n-----END PGP SIGNATURE-----\r\n", "size": 11710, "noattachment": false}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-rc.1_1637121046150_0.31661979232548654"}, "_hasShrinkwrap": false, "publish_time": 1637121046309, "_cnpm_publish_time": 1637121046309, "_cnpmcore_publish_time": "2021-12-16T15:26:20.614Z"}, "5.0.0-rc.0": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-rc.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-rc.0", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "8eb12d6e3a5f94d2c79923c4c6586ba44cf57cf8", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-rc.0", "_nodeVersion": "14.18.1", "_npmVersion": "lerna/4.0.0/node@v14.18.1+x64 (darwin)", "dist": {"shasum": "d3d1ff6a62312418058e15c354756a8d4c341627", "size": 11693, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-rc.0.tgz", "integrity": "sha512-yd4lLrKQEHUncMa6kN4TcuoBUvYYtbpttAmAaCWV8qiZvIi3s6d+PAqNFNGwkZG9M7QaVNR4U8DOXDqGOGgn6A=="}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-rc.0_1636201077879_0.24516537384604153"}, "_hasShrinkwrap": false, "publish_time": 1636201078019, "_cnpm_publish_time": 1636201078019, "_cnpmcore_publish_time": "2021-12-16T15:26:20.852Z"}, "4.5.15": {"name": "@vue/cli-plugin-eslint", "version": "4.5.15", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.15", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "22a8a788e84cacec0c07407214f87a0819399ebf", "_id": "@vue/cli-plugin-eslint@4.5.15", "_nodeVersion": "14.18.1", "_npmVersion": "lerna/3.22.1/node@v14.18.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "5781824a941f34c26336a67b1f6584a06c6a24ff", "size": 10804, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.15.tgz", "integrity": "sha512-/2Fl6wY/5bz3HD035oSnFRMsKNxDxU396KqBdpCQdwdvqk4mm6JAbXqihpcBRTNPeTO6w+LwGe6FE56PVbJdbg=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.15_1635424676519_0.7372350581214415"}, "_hasShrinkwrap": false, "publish_time": 1635424676677, "_cnpm_publish_time": 1635424676677, "_cnpmcore_publish_time": "2021-12-16T15:26:21.228Z"}, "5.0.0-beta.7": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.7", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "3f1346e97374f128f1d10f89ebb2b9b381355556", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.7", "_nodeVersion": "16.12.0", "_npmVersion": "lerna/4.0.0/node@v16.12.0+x64 (darwin)", "dist": {"shasum": "4978907bf63a5d2446d70183e4c4a0fdeea95ad7", "size": 11716, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.7.tgz", "integrity": "sha512-ACKuxOI5XpuXZBIHwSI2RwLAOMikxFMKBPBICse/qNDmihU2kXfDcGMrqQOh7HbqQE4XdpwhaUvARHz80psxEg=="}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.7_1635256169588_0.696496558387909"}, "_hasShrinkwrap": false, "publish_time": 1635256169707, "_cnpm_publish_time": 1635256169707, "_cnpmcore_publish_time": "2021-12-16T15:26:21.460Z"}, "4.5.14": {"name": "@vue/cli-plugin-eslint", "version": "4.5.14", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.14", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "f128c0c5ecd004e3d532fdcfc8c6b16ac34b107b", "_id": "@vue/cli-plugin-eslint@4.5.14", "_nodeVersion": "14.18.0", "_npmVersion": "lerna/3.22.1/node@v14.18.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7fdea4cae783f696521fd635d264fee52b0b836a", "size": 10802, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.14.tgz", "integrity": "sha512-8leK9mZ4Ia4hARWMfVAbcgPBFKjdeOW9S0nG+pt6OBnnwK+V1jf/C7ytfXH+H086KgisU8R9nz1xNaz+9QET0g=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.14_1634195314045_0.30915673878177174"}, "_hasShrinkwrap": false, "publish_time": 1634195314210, "_cnpm_publish_time": 1634195314210, "_cnpmcore_publish_time": "2021-12-16T15:26:21.706Z"}, "5.0.0-beta.6": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.6", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "c0794c716de0ee863973be400a070638fe7041ac", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.6", "_nodeVersion": "14.18.0", "_npmVersion": "lerna/4.0.0/node@v14.18.0+x64 (darwin)", "dist": {"shasum": "b1eb0e1f3d01b3e219a7a77d387843b572efad13", "size": 11717, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.6.tgz", "integrity": "sha512-kCoz/1UvN3yGNhHysIY3v2cxJq4JQuTLi8ftezrkm53jykKtZo9pGhvs77W4q89i5thZ08jj408nQUCwdq5KQw=="}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.6_1634183333274_0.7349104250208203"}, "_hasShrinkwrap": false, "publish_time": 1634183333483, "_cnpm_publish_time": 1634183333483, "_cnpmcore_publish_time": "2021-12-16T15:26:21.917Z"}, "5.0.0-beta.5": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.5", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "a8b74b498025dbf9f02e4ecf5f8bd95e4dea9be1", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.5", "_nodeVersion": "14.18.0", "_npmVersion": "lerna/4.0.0/node@v14.18.0+x64 (darwin)", "dist": {"shasum": "65d659cf5df8293f4df8dfcbcffdf03ec587535c", "size": 11715, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.5.tgz", "integrity": "sha512-KcLcGNS9D6v6iN9IVI40t58c7KwzbBe4yx0/jyG8FChily/EuJMCXkXx6l/Ye0hukcLrWZsWZg7Kh0ADCz1aYA=="}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.5_1633851671045_0.7705219973364648"}, "_hasShrinkwrap": false, "publish_time": 1633851671172, "_cnpm_publish_time": 1633851671172, "_cnpmcore_publish_time": "2021-12-16T15:26:22.109Z"}, "5.0.0-beta.4": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.4", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "8b53487ac8411206c95faded412b9268dc029999", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.4", "_nodeVersion": "14.17.6", "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "dist": {"shasum": "5f81cc280f227ab3101e23fa45c6dd5eb9fe7567", "size": 11177, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.4.tgz", "integrity": "sha512-qTbO3Wze9EwoSa02NAiBf9pWEKw2JUbvWv6ncZhDWjScUIo11I6IBP4IynEf7PrNiE1fOkomAa3CbjA7AOgweA=="}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.4_1631704143470_0.22167650832597663"}, "_hasShrinkwrap": false, "publish_time": 1631704143640, "_cnpm_publish_time": 1631704143640, "_cnpmcore_publish_time": "2021-12-16T15:26:22.299Z"}, "5.0.0-beta.3": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.3", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "c1bf3d874692637648a5cee39c8ea27126272d35", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.3", "_nodeVersion": "14.17.3", "_npmVersion": "lerna/3.22.1/node@v14.17.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "ec48ace6b62443bbe92bf440865b33e15a634777", "size": 11180, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.3.tgz", "integrity": "sha512-HJwktX9IYYWH+lYyfRg9kcQt3AdfP1B7O0CkNykUCC2zVKUSVl+NNPnys86WA0kf2feiQXMC9BSdYQgON7R7Qw=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.3_1628579228191_0.3701222163506672"}, "_hasShrinkwrap": false, "publish_time": 1628579228381, "_cnpm_publish_time": 1628579228381, "_cnpmcore_publish_time": "2021-12-16T15:26:22.509Z"}, "5.0.0-beta.2": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.2", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "ae7c5a57f0165cfad1161b2884ea9ebece96f4b3", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.2", "_nodeVersion": "14.17.0", "_npmVersion": "lerna/3.22.1/node@v14.17.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7325bf9c49badbf792e04ff2e52697102b651602", "size": 11175, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.2.tgz", "integrity": "sha512-qDmvHq1PO4MeII8stAtIcU0PQ3swmE2mN1yTnO4rATdtelkRtpgKGkc+f1TeLiAcGptGigPeFvWpzsovo0qVaA=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.2_1623215831829_0.4210734265984506"}, "_hasShrinkwrap": false, "publish_time": 1623215831985, "_cnpm_publish_time": 1623215831985, "_cnpmcore_publish_time": "2021-12-16T15:26:22.799Z"}, "5.0.0-beta.1": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.1", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "e6262a4fb5490a4e272d7134b44903afa0ef6eb5", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.1", "_nodeVersion": "12.22.1", "_npmVersion": "lerna/3.22.1/node@v12.22.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "2091192bb4d45125bc01f15c67a048f3e08c4f80", "size": 11178, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.1.tgz", "integrity": "sha512-Ac0bcQSA5o/rLbuic+qQiP8eHzl7zlQm1w/hB5YDLYOxremLDzoliRd/3T4iKxepbpPqaHNVrfzRrYTjEVLGsQ=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.1_1620981701531_0.5981570956509463"}, "_hasShrinkwrap": false, "publish_time": 1620981701663, "_cnpm_publish_time": 1620981701663, "_cnpmcore_publish_time": "2021-12-16T15:26:22.998Z"}, "4.5.13": {"name": "@vue/cli-plugin-eslint", "version": "4.5.13", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.13", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "6e0d846b9a707f7f7c0c7a26435f467e1b4d42fe", "_id": "@vue/cli-plugin-eslint@4.5.13", "_nodeVersion": "12.22.1", "_npmVersion": "lerna/3.22.1/node@v12.22.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "8baf22d0d96d76720c7506646b96f4f62c05bdfa", "size": 10801, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.13.tgz", "integrity": "sha512-yc2uXX6aBiy3vEf5TwaueaDqQbdIXIhk0x0KzEtpPo23jBdLkpOSoU5NCgE06g/ZiGAcettpmBSv73Hfp4wHEw=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.13_1620434237583_0.5024281944778808"}, "_hasShrinkwrap": false, "publish_time": 1620434237705, "_cnpm_publish_time": 1620434237705, "_cnpmcore_publish_time": "2021-12-16T15:26:23.211Z"}, "5.0.0-beta.0": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-beta.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-beta.0", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "2d3116ed50de185bee584d98d48cf7223782367d", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-beta.0", "_nodeVersion": "12.22.1", "_npmVersion": "lerna/3.22.1/node@v12.22.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "838e2599ae2db1bd7a819873c9fa92761460cf6e", "size": 11177, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-beta.0.tgz", "integrity": "sha512-FqBlFlJ/8C+uR3nzyKdp/eyRYSpvDtvLaoDUtTl7eeGBdu+q4J6hINTDJ1f62ROcYPBBdkfVG9Q3xmLZv8yVyA=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-beta.0_1619342409867_0.5870847596281965"}, "_hasShrinkwrap": false, "publish_time": 1619342410030, "_cnpm_publish_time": 1619342410030, "_cnpmcore_publish_time": "2021-12-16T15:26:23.750Z"}, "5.0.0-alpha.8": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.8", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^8.0.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "89af6c50c2e559487d5ac24071a1f8589f9c026b", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.8", "_nodeVersion": "14.16.0", "_npmVersion": "lerna/3.22.1/node@v14.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "c0c20a6bf514a29ef8740c60dd0235bf6b92e677", "size": 11185, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.8.tgz", "integrity": "sha512-UquXP+jX/H6/ypVSfRotz+GqpVZecQ+KATm5Vui/OtZeMmVo8v/XDP5LV07EWf+UeTgvAdY68bVI8JXTTWHTXg=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.8_1616590419195_0.8597272510033442"}, "_hasShrinkwrap": false, "publish_time": 1616590419351, "_cnpm_publish_time": 1616590419351, "_cnpmcore_publish_time": "2021-12-16T15:26:23.980Z"}, "4.5.12": {"name": "@vue/cli-plugin-eslint", "version": "4.5.12", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.12", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "b0de229e78ae860c6ebc1e53f4bb481d7549cde7", "_id": "@vue/cli-plugin-eslint@4.5.12", "_nodeVersion": "14.16.0", "_npmVersion": "lerna/3.22.1/node@v14.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7fc2a1d0a490fa300ef4e94518c2cc49ba7a292f", "size": 10804, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.12.tgz", "integrity": "sha512-nbjGJkWxo/xdD32DwvnEAUwkWYsObpqNk9NuU7T62ehdzHPzz58o3j03YZ7a7T7Le8bYyOWMYsdNfz63F+XiZQ=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.12_1615981765263_0.10666565597619004"}, "_hasShrinkwrap": false, "publish_time": 1615981765411, "_cnpm_publish_time": 1615981765411, "_cnpmcore_publish_time": "2021-12-16T15:26:24.167Z"}, "5.0.0-alpha.7": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.7", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^7.1.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "e591bfa5eb965d8015caca297cdd24b2dfbf500c", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.7", "_nodeVersion": "14.16.0", "_npmVersion": "lerna/3.22.1/node@v14.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "b6fa8f927aa9c19c45760efa3be6e7aafc239c02", "size": 11183, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.7.tgz", "integrity": "sha512-HywWY4xPoY6UvkTZHhHngElEm5cGSeNHHIbhtqWiBQ4Zcuebh5077BD29ZYz4TvESagbF1iROsqSAsDsdn8dqw=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.7_1615451346852_0.7118833859378362"}, "_hasShrinkwrap": false, "publish_time": 1615451347019, "_cnpm_publish_time": 1615451347019, "_cnpmcore_publish_time": "2021-12-16T15:26:24.378Z"}, "5.0.0-alpha.6": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.6", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^7.1.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "40e3de2c7c0fa37a164378fb8e990b671b36aa7e", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.6", "_nodeVersion": "14.16.0", "_npmVersion": "lerna/3.22.1/node@v14.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "90ba6bfc5c08015033c6ad7a6f2bbf123de21fc0", "size": 11181, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.6.tgz", "integrity": "sha512-+PuhZXuGTyZKQtNcskKlTPc3aMrFoiBLdCQatYlhPKhLZ+Gh2PlfDrUumYjH+nX58EZ8VmdTR9xYS754A9xR0g=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.6_1615380538491_0.5906222219732622"}, "_hasShrinkwrap": false, "publish_time": 1615380538728, "_cnpm_publish_time": 1615380538728, "_cnpmcore_publish_time": "2021-12-16T15:26:24.979Z"}, "5.0.0-alpha.5": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.5", "eslint-webpack-plugin": "2.4.3", "globby": "^11.0.2", "inquirer": "^7.1.0", "webpack": "^5.22.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "6f88bd5ff4daf45808ec7f2d9e798cd1dd60476f", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.5", "_nodeVersion": "12.20.2", "_npmVersion": "lerna/3.22.1/node@v12.20.2+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "3c6ed35b6c09d03b1666c2772e33aeba4c757606", "size": 11182, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.5.tgz", "integrity": "sha512-7cHubGFdXoskZOXJWZRb3npQ6qDRpd1++lrMrn/nJcNUFgcmWStXFmOlDD5JeThuUaq93DoyS02YZ+JmpLdg0A=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.5_1614069977525_0.22892451663760904"}, "_hasShrinkwrap": false, "publish_time": 1614069977634, "_cnpm_publish_time": 1614069977634, "_cnpmcore_publish_time": "2021-12-16T15:26:25.309Z"}, "5.0.0-alpha.4": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.4", "eslint-webpack-plugin": "^2.4.1", "globby": "^11.0.1", "inquirer": "^7.1.0", "webpack": "^5.10.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "cf3d80193429b67ebfcfd941981163e087bd7812", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.4", "_nodeVersion": "10.23.3", "_npmVersion": "lerna/3.22.1/node@v10.23.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "42b81df8c75982cebe01bf277855222464c9b54e", "size": 11171, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.4.tgz", "integrity": "sha512-7clXFdnAKpf4IkV48TzlKl3t+IzsUJnT13l4cIDlPWmTDgY2KhGZdNnhInh8zymVla0Y+YtvfCoxXpB8g+/x4A=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.4_1613634407720_0.5122267560034819"}, "_hasShrinkwrap": false, "publish_time": 1613634407864, "_cnpm_publish_time": 1613634407864, "_cnpmcore_publish_time": "2021-12-16T15:26:25.516Z"}, "4.5.11": {"name": "@vue/cli-plugin-eslint", "version": "4.5.11", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.11", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "cf7b46adc4c5f181d9e3eb03decbeb4cfb792bc8", "_id": "@vue/cli-plugin-eslint@4.5.11", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "660eb7f8077a022c93bfad7b1cfb81e70a8be142", "size": 10810, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.11.tgz", "integrity": "sha512-6XrF3A3ryjtqoPMYL0ltZaP0631HS2a68Ye34KIkz111EKXtC5ip+gz6bSPWrH5SbhinU3R8cstA8xVASz9kwg=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.11_1611327379442_0.7428181327083265"}, "_hasShrinkwrap": false, "publish_time": 1611327379559, "_cnpm_publish_time": 1611327379559, "_cnpmcore_publish_time": "2021-12-16T15:26:25.701Z"}, "5.0.0-alpha.3": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.3", "eslint-webpack-plugin": "^2.4.1", "globby": "^11.0.1", "inquirer": "^7.1.0", "webpack": "^5.10.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "3d80810a55d912e146d41235549b96e82b4e7931", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.3", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "601954f1a348999766cacc282c24c827b86972b8", "size": 11044, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.3.tgz", "integrity": "sha512-E/zCfRHqsbxAqtiFLMSTY+4u46entMdMNadGzdScvycw4jVqU+cKmwvcqKG6sRBUHQNXddrVY6cpo1oRN7gHkA=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.3_1611303745225_0.9181479119216844"}, "_hasShrinkwrap": false, "publish_time": 1611303745374, "_cnpm_publish_time": 1611303745374, "_cnpmcore_publish_time": "2021-12-16T15:26:26.008Z"}, "5.0.0-alpha.2": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.2", "eslint-webpack-plugin": "^2.4.1", "globby": "^11.0.1", "inquirer": "^7.1.0", "webpack": "^5.10.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "48d6afd73d3a239af01c27e6cae05eca900ec6d4", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.2", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "b28364eaf2daa18fedb9a562ab0fb54c1527844e", "size": 11040, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.2.tgz", "integrity": "sha512-LDUdRYotife3w7ajYVLntkDpRchA+P8n21hytJ0rU+8l1ZSJqT5nWE9oEH03BHwPr/yzgxZzID3MlaGuDP/nOA=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.2_1609918394207_0.9383256526178305"}, "_hasShrinkwrap": false, "publish_time": 1609918394393, "_cnpm_publish_time": 1609918394393, "_cnpmcore_publish_time": "2021-12-16T15:26:26.262Z"}, "5.0.0-alpha.1": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.1", "eslint-webpack-plugin": "^2.4.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^5.10.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "104f9bcc719351575eb89c52e87d2b761489fc8b", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.1", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "9be5a30b836606c023d8a26b5e11a339320ae783", "size": 11045, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.1.tgz", "integrity": "sha512-BxGQ742NjV2zIbzJ7uM47/tAA9MMVfERp1T2GTl32BgIxB9FcKnIK9sYPCaF/qR9/JvvtsKvuMemxeW9VwGG0Q=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.1_1609918035284_0.7079660251917399"}, "_hasShrinkwrap": false, "publish_time": 1609918035465, "_cnpm_publish_time": 1609918035465, "_cnpmcore_publish_time": "2021-12-16T15:26:26.469Z"}, "4.5.10": {"name": "@vue/cli-plugin-eslint", "version": "4.5.10", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.10", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "5bc8c80deda476f54b177c0b09c036425ad3ce46", "_id": "@vue/cli-plugin-eslint@4.5.10", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "ba150292d7d51c96ce1a87f2782f05f644a0eb4b", "size": 10808, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.10.tgz", "integrity": "sha512-2ud8lurlMJCtcErjhYBcTWhu5eN79sCBGz5dHBAmtLP0k7p7xZq7/1mo2ahnZioUskYrfz94Vo9i+D3pOUMuMQ=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.10_1609917002768_0.5599071142483465"}, "_hasShrinkwrap": false, "publish_time": 1609917002977, "_cnpm_publish_time": 1609917002977, "_cnpmcore_publish_time": "2021-12-16T15:26:26.685Z"}, "5.0.0-alpha.0": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-alpha.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-alpha.0", "eslint-webpack-plugin": "^2.4.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^5.10.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "7139e4093ab1c9651e9775486c41f87b3736fe06", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-alpha.0", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "f7f7d66a73923cb6b9a8e8d037330fa96543e34e", "size": 11044, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-alpha.0.tgz", "integrity": "sha512-22+Rjj6WSCg4Ahb4T7gQx8uqGFmPuYFU+o/BaxbqZatZHluaWBdLbcCPWmcipIVa0UOYO5LHXvrij0eLltKfEQ=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-alpha.0_1607946593245_0.8087808056189278"}, "_hasShrinkwrap": false, "publish_time": 1607946593443, "_cnpm_publish_time": 1607946593443, "_cnpmcore_publish_time": "2021-12-16T15:26:26.883Z"}, "4.5.9": {"name": "@vue/cli-plugin-eslint", "version": "4.5.9", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.9", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "3fd9cac88c4594384672d4126754c265094c4d9d", "_id": "@vue/cli-plugin-eslint@4.5.9", "_nodeVersion": "12.19.0", "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7d7f8a48b8e73e6b19fe2f54b39c78850a9ab33d", "size": 10813, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.9.tgz", "integrity": "sha512-wTsWRiRWPW5ik4bgtlh4P4h63Zgjsyvqx2FY0kcj+bSAnQGPJ3bKUOMU9KQP5EyNH6pAXMVGh2LEXK9WwJMf1w=="}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.9_1605595909667_0.17797609881611898"}, "_hasShrinkwrap": false, "publish_time": 1605595909804, "_cnpm_publish_time": 1605595909804, "_cnpmcore_publish_time": "2021-12-16T15:26:27.095Z"}, "4.5.8": {"name": "@vue/cli-plugin-eslint", "version": "4.5.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.8", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "d06a1e89957e7eb2b4875f9c4dc7f0051dd2f240", "_id": "@vue/cli-plugin-eslint@4.5.8", "_nodeVersion": "14.13.1", "_npmVersion": "lerna/3.22.1/node@v14.13.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "fce1ed659dbb50de7477592591348cf29f3353ff", "size": 10811, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.8.tgz", "integrity": "sha512-1lg3K2D/harXbN4FvRoA1b0X9947H+G4Ql/43rxJY39OqswfK/d1ck438Fo9M4l1+zhBSUNDmcjn7Q2EH6qWmA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.8_1603104052412_0.2285923886496406"}, "_hasShrinkwrap": false, "publish_time": 1603104052523, "_cnpm_publish_time": 1603104052523, "_cnpmcore_publish_time": "2021-12-16T15:26:27.293Z"}, "4.5.7": {"name": "@vue/cli-plugin-eslint", "version": "4.5.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.7", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "597b4b64363e320ee4e248db152444a34c6e3ab3", "_id": "@vue/cli-plugin-eslint@4.5.7", "_nodeVersion": "12.18.4", "_npmVersion": "lerna/3.22.1/node@v12.18.4+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "e66c0011f8d58bd86ee525f2062c6dab2c4272da", "size": 10805, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.7.tgz", "integrity": "sha512-6fWob1xh2W0uif2++YhNiBWITDBsAEktdgnLRgIgM/UqUg9oFpz9tqs0i85PQwjUDIn/erMT2ID3hnOncYTxxQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.7_1602060030157_0.5666950242518414"}, "_hasShrinkwrap": false, "publish_time": 1602060030239, "_cnpm_publish_time": 1602060030239, "_cnpmcore_publish_time": "2021-12-16T15:26:27.490Z"}, "4.5.6": {"name": "@vue/cli-plugin-eslint", "version": "4.5.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.6", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "6cac3af2dffbb3a770c8d89f1ac1c9b5f84f7fdb", "_id": "@vue/cli-plugin-eslint@4.5.6", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "d6721bc96f797b9d978e13bd0afd39999f92caf1", "size": 10804, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.6.tgz", "integrity": "sha512-maG3dy64pGVT9mMQq7KvP6kbBK6TeVgcj1aa1QwzT5yrw65E2So8bKMrEMEjy53b88bgR9jZ7gshOks00jrYsg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.6_1599744863112_0.3032826295369666"}, "_hasShrinkwrap": false, "publish_time": 1599744863283, "_cnpm_publish_time": 1599744863283, "_cnpmcore_publish_time": "2021-12-16T15:26:27.723Z"}, "4.5.5": {"name": "@vue/cli-plugin-eslint", "version": "4.5.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.5", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "f77b4d35a57a49efb4a297f5dc58371aba79829a", "_id": "@vue/cli-plugin-eslint@4.5.5", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "ffadf5f42f0ed423388265f04c6fd48e03a3496b", "size": 10805, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.5.tgz", "integrity": "sha512-eze5iyLOCZU0vBMPQmfjMXoxPaJsGTy1LwxIRVVh3ddwL7NqIgvsewUJS/kAOP/28Fn2kwfYxKnIG413D6BeZQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.5_1599739082817_0.4826642975152551"}, "_hasShrinkwrap": false, "publish_time": 1599739082951, "_cnpm_publish_time": 1599739082951, "_cnpmcore_publish_time": "2021-12-16T15:26:27.947Z"}, "4.5.4": {"name": "@vue/cli-plugin-eslint", "version": "4.5.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.4", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "ff24edb66361a504446a141525f9981bf34e8931", "_id": "@vue/cli-plugin-eslint@4.5.4", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "0f1f307abfe1e4ad67dcb97693640942b15fae76", "size": 10807, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.4.tgz", "integrity": "sha512-mWuhKtxMiAM70nPW/NnoWtf32YJoOPPt7SyNmsAjBKSRPcje+16Egl7BD8yuPKoF1MTkvs5CM/e7gp3AnSTFzQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.4_1597716776653_0.9290095557117324"}, "_hasShrinkwrap": false, "publish_time": 1597716776767, "_cnpm_publish_time": 1597716776767, "_cnpmcore_publish_time": "2021-12-16T15:26:28.231Z"}, "4.5.3": {"name": "@vue/cli-plugin-eslint", "version": "4.5.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.3", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "a1ee583ede9e9feb85ab2a44927da49422d48e18", "_id": "@vue/cli-plugin-eslint@4.5.3", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "edac801bf05001e1a7fccd58b20c1a6cfe52701a", "size": 10806, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.3.tgz", "integrity": "sha512-zSOLvLGI2gXdYTlkTOOgll1PbeXj7ka1mTKKHaFl/nNQhc76CiJ0Y/OzZlWqJOgk2lRlbL86KdioDh2FzZxFiw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.3_1597137030246_0.9425391296450893"}, "_hasShrinkwrap": false, "publish_time": 1597137030475, "_cnpm_publish_time": 1597137030475, "_cnpmcore_publish_time": "2021-12-16T15:26:28.475Z"}, "4.5.2": {"name": "@vue/cli-plugin-eslint", "version": "4.5.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.2", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "395212598aa243d56159c6048372b358fed812fe", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.5.2", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "2aaf9dee417bef936e9910d41e6e6cd1e1b499cb", "size": 10807, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.2.tgz", "integrity": "sha512-CTb3CaFYXLmAae0NTIV6qChmFyiMvur5YQEw4m6pcLFfOor8spaeyWFqA9t4K5qFyAmEtFqle3hgxJfKllu1fQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.2_1597042212336_0.03338879764600189"}, "_hasShrinkwrap": false, "publish_time": 1597042212448, "_cnpm_publish_time": 1597042212448, "_cnpmcore_publish_time": "2021-12-16T15:26:28.681Z"}, "4.5.1": {"name": "@vue/cli-plugin-eslint", "version": "4.5.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.1", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "09adc41408f6528554fcf9801b5bae169d5051a9", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.5.1", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "8103e30e803728b97399bd9af67af9ead82c1460", "size": 10811, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.1.tgz", "integrity": "sha512-/wNS48h8GWy95yTwPPWfMltRyGC1FFgiVt1pKgdXA5Cj8JafdOxv1Ps5D/qdYFD2qnGxYdY3lqR1CbaTUkbrKA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.1_1596729040704_0.4935965039159853"}, "_hasShrinkwrap": false, "publish_time": 1596729040901, "_cnpm_publish_time": 1596729040901, "_cnpmcore_publish_time": "2021-12-16T15:26:28.890Z"}, "4.5.0": {"name": "@vue/cli-plugin-eslint", "version": "4.5.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.0", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "56348259db4412428d9b5ab49e4bc91191c24fff", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.5.0", "_nodeVersion": "12.18.3", "_npmVersion": "lerna/3.22.1/node@v12.18.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "de2a36872e932d28bcc371ee76eeddffded1d19a", "size": 10807, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.0.tgz", "integrity": "sha512-jlQuU738duqJZ5j9nwncMVeg6JeoLjXteDpnFDYYW5kNAWQPr6C1x7EayQ98puaC6TukitOtt1fvUdlyxj+TOg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.0_1595591002454_0.6071011864032083"}, "_hasShrinkwrap": false, "publish_time": 1595591002551, "_cnpm_publish_time": 1595591002551, "_cnpmcore_publish_time": "2021-12-16T15:26:29.101Z"}, "4.4.6": {"name": "@vue/cli-plugin-eslint", "version": "4.4.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.6", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "e311b062cb388f4d6230ea45cdc3bd3512b88b61", "_id": "@vue/cli-plugin-eslint@4.4.6", "_nodeVersion": "12.18.1", "_npmVersion": "lerna/3.22.0/node@v12.18.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "442d62a70dd93e4a549ff9164d2d10f4e97a58f1", "size": 10704, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.6.tgz", "integrity": "sha512-3a9rVpOKPQsDgAlRkhmBMHboGobivG/47BbQGE66Z8YJxrgF/AWikP3Jy67SmxtszRkyiWfw4aJFRV9r3MzffQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.6_1592976305726_0.5158420432435042"}, "_hasShrinkwrap": false, "publish_time": 1592976305885, "_cnpm_publish_time": 1592976305885, "_cnpmcore_publish_time": "2021-12-16T15:26:29.256Z"}, "4.4.5": {"name": "@vue/cli-plugin-eslint", "version": "4.4.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.5", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "b66bc8c6e6e628f03cb2545871211c00795b266d", "_id": "@vue/cli-plugin-eslint@4.4.5", "_nodeVersion": "12.18.1", "_npmVersion": "lerna/3.22.0/node@v12.18.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "11eb77da1dba8f78302edb939d4a875fc7dded26", "size": 10703, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.5.tgz", "integrity": "sha512-2igWVdzU367F/EZNNeTkEsd7F+jxVj3lq184Sip89yZHZsULk5BnCEsTJPBNAPfSRky3LF2Pj85t58erGdrHvw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.5_1592827266205_0.03391620525413863"}, "_hasShrinkwrap": false, "publish_time": 1592827266300, "_cnpm_publish_time": 1592827266300, "_cnpmcore_publish_time": "2021-12-16T15:26:29.444Z"}, "4.4.4": {"name": "@vue/cli-plugin-eslint", "version": "4.4.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.4", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "78374b717ee3d7a4f3bc9e40d70cb946baedacab", "_id": "@vue/cli-plugin-eslint@4.4.4", "_nodeVersion": "12.17.0", "_npmVersion": "lerna/3.22.0/node@v12.17.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "19fd1da1863a90584b78939cce1ce94c49ed4388", "size": 10601, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.4.tgz", "integrity": "sha512-B+l3smq3Lyob9qiuywC/IymCCyV2Gm/l1ZtxRzQI98RDTKei1PrRriIi3Hrg/AkK59HirwR7P7wiNhF2Pqg3VA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.4_1591966873111_0.7140049538727842"}, "_hasShrinkwrap": false, "publish_time": 1591966873300, "_cnpm_publish_time": 1591966873300, "_cnpmcore_publish_time": "2021-12-16T15:26:29.663Z"}, "4.4.3": {"name": "@vue/cli-plugin-eslint", "version": "4.4.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.3", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "d9453e82271547a6f4c359968595cb303250b26c", "_id": "@vue/cli-plugin-eslint@4.4.3", "_nodeVersion": "12.17.0", "_npmVersion": "lerna/3.22.0/node@v12.17.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "c629bad585086068fd3b6c914e378989cc075329", "size": 10605, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.3.tgz", "integrity": "sha512-8WB1dx2Ih2zOIjrFMe8NkD/geyYHkyi2die2EcW3MdAfCeK6RUgDF6yIO6lCM3lpAzrk1O0y0OSHc2lrMxr9iA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.3_1591948471238_0.0946149219391752"}, "_hasShrinkwrap": false, "publish_time": 1591948471423, "_cnpm_publish_time": 1591948471423, "_cnpmcore_publish_time": "2021-12-16T15:26:29.958Z"}, "4.4.2": {"name": "@vue/cli-plugin-eslint", "version": "4.4.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.2", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "a4839e921bff841bf8893628282348f7f450a46e", "_id": "@vue/cli-plugin-eslint@4.4.2", "_nodeVersion": "12.17.0", "_npmVersion": "lerna/3.22.0/node@v12.17.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "4b1488108b033628edde311c6099b5b24f1b103b", "size": 10587, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.2.tgz", "integrity": "sha512-/7LuP8w7cyoyEbbRQoTYP6wzgc7DghL57dZh75nRe9fxvFLlgXm70jSv/4WbYpqP09o2xcTmzDK2mk6gUQobQg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.2_1591948057180_0.8742312805801364"}, "_hasShrinkwrap": false, "publish_time": 1591948057374, "_cnpm_publish_time": 1591948057374, "_cnpmcore_publish_time": "2021-12-16T15:26:30.211Z"}, "4.4.1": {"name": "@vue/cli-plugin-eslint", "version": "4.4.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.1", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "34f303b0b6a03f96e8c21d308931219154a7e627", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.4.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "e39d6517da6de231195d227f995f495e2958a74a", "size": 10593, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.1.tgz", "integrity": "sha512-T+9+q44iajQEbe59z6Io3otFOsWnPOEVU+/hrDyC6aOToJbQo6P4VacByDDcuGYENAjAd8ENLSt18TaPNSIyRw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.1_1590394268004_0.6113235986963796"}, "_hasShrinkwrap": false, "publish_time": 1590394268156, "_cnpm_publish_time": 1590394268156, "_cnpmcore_publish_time": "2021-12-16T15:26:30.394Z"}, "4.4.0": {"name": "@vue/cli-plugin-eslint", "version": "4.4.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.4.0", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "6323b73260e461c4aeb866fde4231f15d19f9c4e", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.4.0", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.21.0/node@v12.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "fb328328b7df8036e6413c780f9007dacecc6ad6", "size": 10590, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.4.0.tgz", "integrity": "sha512-edTP5wHpqWpnh43TVKEfVYd3u2t3PwG+v9iokGaCpv6upfMRoJk7XP32O9EH3XmskaI4CVsK7bMxDH+QDHyITQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.4.0_1589901670883_0.03257143143322638"}, "_hasShrinkwrap": false, "publish_time": 1589901671038, "_cnpm_publish_time": 1589901671038, "_cnpmcore_publish_time": "2021-12-16T15:26:30.631Z"}, "4.3.1": {"name": "@vue/cli-plugin-eslint", "version": "4.3.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.3.1", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "2ec479a331291d736d58592563ae81ac64805348", "_id": "@vue/cli-plugin-eslint@4.3.1", "_nodeVersion": "12.16.1", "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "2f5e09bd7d1d8c494134b6c71af2b779938d289a", "size": 10586, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.3.1.tgz", "integrity": "sha512-5UEP93b8C/JQs9Rnuldsu8jMz0XO4wNXG0lL/GdChYBEheKCyXJXzan7qzEbIuvUwG3I+qlUkGsiyNokIgXejg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.3.1_1586274071051_0.4123906759178346"}, "_hasShrinkwrap": false, "publish_time": 1586274071785, "_cnpm_publish_time": 1586274071785, "_cnpmcore_publish_time": "2021-12-16T15:26:30.837Z"}, "4.3.0": {"name": "@vue/cli-plugin-eslint", "version": "4.3.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.3.0", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "465986923a689ae3bb6344e87660e60cd939f3bc", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.3.0", "_nodeVersion": "12.16.1", "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "dd174b3668aa71321f2a25020912b00a06137bf5", "size": 10493, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.3.0.tgz", "integrity": "sha512-0M8U45JETVam/zS/9AQChcgf+thvvjNg2Dkeba6mCklEjT5Gjpql1hEFv+7lo1d3Co3pnCjfHI2PCJztc2Kvsg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.3.0_1585728072400_0.26346479562486724"}, "_hasShrinkwrap": false, "publish_time": 1585728072614, "_cnpm_publish_time": 1585728072614, "_cnpmcore_publish_time": "2021-12-16T15:26:31.029Z"}, "4.2.3": {"name": "@vue/cli-plugin-eslint", "version": "4.2.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.2.3", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "inquirer": "^6.3.1", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "0380f226b9d5df188622c6bdb3d1b12f8b4e044c", "_id": "@vue/cli-plugin-eslint@4.2.3", "_nodeVersion": "12.16.1", "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "2a70df99aaa60697a13893e94034a1386f31c3ee", "size": 10505, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.2.3.tgz", "integrity": "sha512-r3J0OAa8x0tZZYFxxFZ/C5WWxYGGH8hLfUUhs3FePo0VsBci28lrgvnBqr69uj/T40v/ndBh0geAz28mjjQ+mg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.2.3_1582814006043_0.3901664106819609"}, "_hasShrinkwrap": false, "publish_time": 1582814006183, "_cnpm_publish_time": 1582814006183, "_cnpmcore_publish_time": "2021-12-16T15:26:31.347Z"}, "4.2.2": {"name": "@vue/cli-plugin-eslint", "version": "4.2.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.2.2", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "inquirer": "^6.3.1", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "94da5243bebae0359951288568c9992c6933a1a6", "_id": "@vue/cli-plugin-eslint@4.2.2", "_nodeVersion": "10.18.1", "_npmVersion": "lerna/3.20.2/node@v10.18.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "ea0ecfc3f816102f61df3050d82f23909aa3df1b", "size": 10505, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.2.2.tgz", "integrity": "sha512-C6Foeq+XjsAMy+F9XTXGUtCKBgTJXGRjqUdmehU5J4i84erSIRulOahqjHr6J6IslJcDAmBekF8xXhMzX839NA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.2.2_1581073936302_0.9631452467954551"}, "_hasShrinkwrap": false, "publish_time": 1581073936450, "_cnpm_publish_time": 1581073936450, "_cnpmcore_publish_time": "2021-12-16T15:26:31.560Z"}, "4.2.1": {"name": "@vue/cli-plugin-eslint", "version": "4.2.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.2.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "inquirer": "^6.3.1", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "8b951f81d586a9a7472ce43240fe2e525dc09ded", "_id": "@vue/cli-plugin-eslint@4.2.1", "_nodeVersion": "10.18.1", "_npmVersion": "lerna/3.20.2/node@v10.18.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "aeb8bf8db0e65eec2136a3518ade757388e2ecd3", "size": 10501, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.2.1.tgz", "integrity": "sha512-toES0xMAtdwe/iG1dG7BXo/8OWhBp4pQ/7uBGoXa4I1LpDM60PN8Bv5IuC6kope+xlOF/6h4iUWfmvElNujUeg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.2.1_1581070286140_0.14387481215671705"}, "_hasShrinkwrap": false, "publish_time": 1581070286260, "_cnpm_publish_time": 1581070286260, "_cnpmcore_publish_time": "2021-12-16T15:26:31.779Z"}, "4.2.0": {"name": "@vue/cli-plugin-eslint", "version": "4.2.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.2.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "inquirer": "^6.3.1", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "079a451f18003eabf3eca88107df75b5583024d7", "_id": "@vue/cli-plugin-eslint@4.2.0", "_nodeVersion": "10.18.1", "_npmVersion": "lerna/3.20.2/node@v10.18.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "2be90c384a5ce05a14c75a8e509778cace53ed5a", "size": 10501, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.2.0.tgz", "integrity": "sha512-tpz15MYDQJTl5/J6RAsE+3dAK6SKB4swkdsc1STjgZGbZPPn1uszG0WTA6wolyJS3p6gL89qglEv0hvhrXBQRw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.2.0_1581061573031_0.25020635210823694"}, "_hasShrinkwrap": false, "publish_time": 1581061573135, "_cnpm_publish_time": 1581061573135, "_cnpmcore_publish_time": "2021-12-16T15:26:32.263Z"}, "4.1.2": {"name": "@vue/cli-plugin-eslint", "version": "4.1.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.1.2", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "3b7227d54402bc1c21394bd57597e5fbe94e9107", "_id": "@vue/cli-plugin-eslint@4.1.2", "_nodeVersion": "10.17.0", "_npmVersion": "lerna/3.19.0/node@v10.17.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "173d2a40beb7debc03a217db3bb4c67cbf255bd6", "size": 9773, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.1.2.tgz", "integrity": "sha512-j6Z6tyhas7AFBwSvQ8JdKPLfaakZbwmK0+Xk8H6BK1/GrEpSCsb8pzBV8faStbKCPUO9vlKEuO319kHypUTJ1g=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.1.2_1577537762279_0.12530456552654767"}, "_hasShrinkwrap": false, "publish_time": 1577537762422, "_cnpm_publish_time": 1577537762422, "_cnpmcore_publish_time": "2021-12-16T15:26:32.617Z"}, "4.1.1": {"name": "@vue/cli-plugin-eslint", "version": "4.1.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.1.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "2ddcc65dfe2a1f75df9df0b391c9b3e181407faf", "_id": "@vue/cli-plugin-eslint@4.1.1", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "ad09b71f94dc7518a6c83debacd39e34f6d5a71e", "size": 9775, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.1.1.tgz", "integrity": "sha512-7bb5idaWcXREaxVYmQ9NK31gy26Qms6cQ9ENovXQurFpsSd29+Fmqc/EkAhHhWn82gModvypIoJyOhKt21jxKg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.1.1_1574867767429_0.5556424553884243"}, "_hasShrinkwrap": false, "publish_time": 1574867767580, "_cnpm_publish_time": 1574867767580, "_cnpmcore_publish_time": "2021-12-16T15:26:32.885Z"}, "4.1.0": {"name": "@vue/cli-plugin-eslint", "version": "4.1.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.1.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "d316a187894a588722018c46113182c9dcb6c178", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.1.0", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "33c9615e520c49ce5b3b94f6972c974f5e6779d6", "size": 9777, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.1.0.tgz", "integrity": "sha512-wuXfneeimR4UaEC2vRMfqgfr2IuiI6b3o26AH5ufaJbFo8lBZZhrbh+OmioekMq8c0dFPOn3JJNhtWbJV7VZGw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.1.0_1574838006603_0.5112870050090805"}, "_hasShrinkwrap": false, "publish_time": 1574838006736, "_cnpm_publish_time": 1574838006736, "_cnpmcore_publish_time": "2021-12-16T15:26:33.134Z"}, "4.1.0-beta.0": {"name": "@vue/cli-plugin-eslint", "version": "4.1.0-beta.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.1.0-beta.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "1c41371a42d1e5c2d7c1213a4297dc899b9535e6", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.1.0-beta.0", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.18.1/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7da3626fbf1c24dc43b5eb55a74af404013575b7", "size": 9776, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.1.0-beta.0.tgz", "integrity": "sha512-Hks+7PhnNYFc6B4TNWS53w0mB7re6UMq74assfXnBNtkPyQ+EU6pNDyA+1v1uxi9EkT7OU3BqpLW+vPTaY4www=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.1.0-beta.0_1573307452074_0.2248287428061464"}, "_hasShrinkwrap": false, "publish_time": 1573307452184, "_cnpm_publish_time": 1573307452184, "_cnpmcore_publish_time": "2021-12-16T15:26:33.371Z"}, "4.0.5": {"name": "@vue/cli-plugin-eslint", "version": "4.0.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.5", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "17925c6ae643608853d43bb413ceccc4b8a1bd6b", "_id": "@vue/cli-plugin-eslint@4.0.5", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.18.1/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "f23f46478b5e7a40ba55f6ba1bb11dbec49c45ba", "size": 9777, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.5.tgz", "integrity": "sha512-hiPU2+knz3GgSUDniekbp81Iciax9yIFzz1swy1QTJGABXT/3gqakz7Gc0IGgpo+wRkMHk9DyCK8+TpI6wdtWg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.5_1571745311235_0.4818468772683746"}, "_hasShrinkwrap": false, "publish_time": 1571745311413, "_cnpm_publish_time": 1571745311413, "_cnpmcore_publish_time": "2021-12-16T15:26:33.603Z"}, "3.12.1": {"name": "@vue/cli-plugin-eslint", "version": "3.12.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.12.1", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "_id": "@vue/cli-plugin-eslint@3.12.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.12.0", "dist": {"shasum": "302c463867f38e790bb996eafdf7159c782dc8cf", "size": 8587, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.12.1.tgz", "integrity": "sha512-tVTZlEZsy3sQbO4LLWFK11yzlWwqVAqaM+IY+BeWHITBzEJKh2KmouG+x6x/reXiU3qROsMJ4Ej3Hs8buSMWyQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.12.1_1571387877609_0.5295843690422712"}, "_hasShrinkwrap": false, "publish_time": 1571387877729, "_cnpm_publish_time": 1571387877729, "_cnpmcore_publish_time": "2021-12-16T15:26:33.852Z"}, "4.0.4": {"name": "@vue/cli-plugin-eslint", "version": "4.0.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.4", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "d82a2bbe268dc53b5a0a20ce3c5cba28a3cfee0d", "_id": "@vue/cli-plugin-eslint@4.0.4", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7afddfdaf3bfdafdc826ed0c87edf41e67de3208", "size": 9708, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.4.tgz", "integrity": "sha512-YpQQLxHxzEVvTqs5zs4KSwqjGF6ILJ0OwD+4mPjCQL0Ms742RzzHb2MTLR1GF3bwcComwgNDNmmzzQERFiWajA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.4_1571384971607_0.7720100512277341"}, "_hasShrinkwrap": false, "publish_time": 1571384971725, "_cnpm_publish_time": 1571384971725, "_cnpmcore_publish_time": "2021-12-16T15:26:34.070Z"}, "4.0.3": {"name": "@vue/cli-plugin-eslint", "version": "4.0.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.3", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "aa29fd152647a428fd497ebd142a354374e68195", "_id": "@vue/cli-plugin-eslint@4.0.3", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "f8ae3b80f716825a0d814c9256978086860968ff", "size": 9712, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.3.tgz", "integrity": "sha512-UyXTunfS7IaaGHPT8g3HrugPrldpzgeqS4LxXuJhCTt2E70MjUQtgRaag3dLsXX0gvHC0emLzRbcBJn/wbwkwA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.3_1571299226900_0.7967763743255818"}, "_hasShrinkwrap": false, "publish_time": 1571299227041, "_cnpm_publish_time": 1571299227041, "_cnpmcore_publish_time": "2021-12-16T15:26:34.271Z"}, "4.0.2": {"name": "@vue/cli-plugin-eslint", "version": "4.0.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.2", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "8b08c73e8e688cf2901464f095aab306f98f7dc9", "_id": "@vue/cli-plugin-eslint@4.0.2", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "f0503098dc7ce6389f187ca1b04b0646ce358768", "size": 9490, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.2.tgz", "integrity": "sha512-dxrexOPCkR6fmRSejNAYy5N3jmNBzzZCJ9omSSXUCmUDvwsOErkavXQXj/xZa0Dpy0UMRv1wcTfAiczvi6my1Q=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.2_1571279060896_0.7291110255714646"}, "_hasShrinkwrap": false, "publish_time": 1571279061061, "_cnpm_publish_time": 1571279061061, "_cnpmcore_publish_time": "2021-12-16T15:26:34.476Z"}, "4.0.1": {"name": "@vue/cli-plugin-eslint", "version": "4.0.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "977080841143c951ec7a8334a95123c6f9fb5fa0", "_id": "@vue/cli-plugin-eslint@4.0.1", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "b39049b84edd520f43ee8198286d5238553f2256", "size": 9484, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.1.tgz", "integrity": "sha512-oaPz+4tS6ptBlNBfIihw8GSLMnjLNq3yYRtg83hV+A09H4ZytSZ4DM1WRyAzzw0TSbIR5806EPL/rn7XZUTcFQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.1_1571229289970_0.8766878507997495"}, "_hasShrinkwrap": false, "publish_time": 1571229290070, "_cnpm_publish_time": 1571229290070, "_cnpmcore_publish_time": "2021-12-16T15:26:34.665Z"}, "4.0.0": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "81f84577e48cd7966409e4e0736774b607d1ab15", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "36500be0e819c5a90ede6ed11888d6cc231ec069", "size": 9516, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0.tgz", "integrity": "sha512-SRPWGLTq7UQQck6gEUgPJObfM0wC9qaMRVe84YxWqvrEqbB2NjWff9x5GiG2eVgzN930U5NtrU4FOC6K/n2MWQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0_1571224807164_0.7564145060444938"}, "_hasShrinkwrap": false, "publish_time": 1571224807424, "_cnpm_publish_time": 1571224807424, "_cnpmcore_publish_time": "2021-12-16T15:26:34.889Z"}, "4.0.0-rc.8": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.8", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "5cce80c2f7da5d5fa033cde48591fd1bfc2a68f2", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.8", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "76450a54d29d9578d934b7d3cc601aedc9d5cc94", "size": 9518, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.8.tgz", "integrity": "sha512-o+ZLkVLKz7UBJpcJvKh4sG3N5kv4YuXi6mmqBPLZ1D7B7l884s4qs61NN026X0ESJgEN57xSLDmIJ7cR54I1uw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.8_1570781547558_0.7036070876325469"}, "_hasShrinkwrap": false, "publish_time": 1570781547702, "_cnpm_publish_time": 1570781547702, "_cnpmcore_publish_time": "2021-12-16T15:26:35.178Z"}, "3.12.0": {"name": "@vue/cli-plugin-eslint", "version": "3.12.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.12.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "57c6f031b303436212e4882927f028194545001a", "_id": "@vue/cli-plugin-eslint@3.12.0", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.14.1/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "f02990b693bd1f52127f13ce14a1e06d8ad84164", "size": 9371, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.12.0.tgz", "integrity": "sha512-0LKwKi+x0yxQjKOq3bXBXjn3XfhybLgSIJz4TNuvhcxh7NZt9NrvaU3iZUVb1gMUNBlFy7arK3Kame4kGQoFeQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.12.0_1570698102183_0.7244918676149568"}, "_hasShrinkwrap": false, "publish_time": 1570698102315, "_cnpm_publish_time": 1570698102315, "_cnpmcore_publish_time": "2021-12-16T15:26:35.386Z"}, "4.0.0-rc.7": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.7", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "58592fed4e33f550a3c9d9a2fbc97c5a626dec29", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.7", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "39c271dc7daa1c984258a3a9c17ab2df3d5f9e0f", "size": 9519, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.7.tgz", "integrity": "sha512-pt/eG6W3+AIVLo62ibngK4rqcFVjAQx6YL9a9fbz3zDz4UEzFdfY4Hls+XNxeKSIYr74E5WkP3ug5Ho2ESeJtQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.7_1569944230120_0.14206791976517952"}, "_hasShrinkwrap": false, "publish_time": 1569944230288, "_cnpm_publish_time": 1569944230288, "_cnpmcore_publish_time": "2021-12-16T15:26:35.592Z"}, "4.0.0-rc.6": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.6", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "b823ca84d35b5de6c92a3b582fc4c333217260df", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.6", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "9bce8b17470762bc2e9c83ab5a08dfa6b69c0a1e", "size": 9519, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.6.tgz", "integrity": "sha512-z2pVAqRjnHV0HU0bshxZZscoOpfnwks6rcEd1bI57xiOWrc5IgSwtzcT5Sn3rM+vnkKkoCox2wBtI6CEVYLsUQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.6_1569846578199_0.7889763990809986"}, "_hasShrinkwrap": false, "publish_time": 1569846578332, "_cnpm_publish_time": 1569846578332, "_cnpmcore_publish_time": "2021-12-16T15:26:35.810Z"}, "4.0.0-rc.5": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.5", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "d49f784eb0d69691ddd964f2d6e59db4d8df6669", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.5", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "3a2ea14ee050ee4e64df089c9536f2ae70fb23ed", "size": 9520, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.5.tgz", "integrity": "sha512-YHjScrOF339738JLkEI7YD9eXJTqkLC0crzUawVstj32CkpdzN9EE0o92W9V4FNiVJ64FX6eCvQz0WmbZFrqyQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.5_1569840081474_0.7575931863186107"}, "_hasShrinkwrap": false, "publish_time": 1569840081624, "_cnpm_publish_time": 1569840081624, "_cnpmcore_publish_time": "2021-12-16T15:26:36.020Z"}, "4.0.0-rc.4": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.4", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "21257378d7cb2817d680ba983c171f0b9649964e", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.4", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.16.4/node@v10.16.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "79dd6f4a981177b541515ad1b561f28f9a1b0434", "size": 9520, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.4.tgz", "integrity": "sha512-BKXl5aESsZUlpPxmnesim1VWNaEtUdsVkv9yjDrwceXGzN8wgH9uhbnxLlaNvhm2WN7kAxXFQgnMUjVaxGGRoQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.4_1569385962082_0.6684585643353083"}, "_hasShrinkwrap": false, "publish_time": 1569385962218, "_cnpm_publish_time": 1569385962218, "_cnpmcore_publish_time": "2021-12-16T15:26:36.213Z"}, "4.0.0-rc.3": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.3", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "863d6bbfbf1a2564d9593c2ce576a1f5db5b8a29", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.3", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.16.4/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "79488f98fd43390dae26f35b395530c73611c9ca", "size": 9519, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.3.tgz", "integrity": "sha512-zVYnkp9sE52thWoOzAfFY3b0rzvmO5VBRwxT9fCvcqwpXUevYGYmHnFkopEnB/5dPdb0LSW0nHf5NbXEL2lgPg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.3_1568045226853_0.9215767503252081"}, "_hasShrinkwrap": false, "publish_time": 1568045227036, "_cnpm_publish_time": 1568045227036, "_cnpmcore_publish_time": "2021-12-16T15:26:36.436Z"}, "4.0.0-rc.2": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.2", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "d37d68afbab1bfee899ad3468149efd731ab052e", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.2", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.16.4/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "f677c329eb1a90918b8584b728d318f7e8a72db7", "size": 9517, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.2.tgz", "integrity": "sha512-CdQoSR8RKjkVQc7bL0iamOl6vdLGxshPulgCet4Sp0i6elPsdZfGqmRg9UTT5d3oxO03ITZsCVa3Wj9qk4s2Mw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.2_1567914864272_0.3619338258294824"}, "_hasShrinkwrap": false, "publish_time": 1567914864432, "_cnpm_publish_time": 1567914864432, "_cnpmcore_publish_time": "2021-12-16T15:26:36.691Z"}, "4.0.0-rc.1": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "df0f813703e5c8fc00d4977feddbeb75723af39d", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.1", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.16.4/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "02a14a9716f25fd42961e92f118f6fd32f32767c", "size": 9232, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.1.tgz", "integrity": "sha512-Wggw/kfKUsFqOCx9wBL9SZ1af+Tf+cwRwk8fxQKhhQAZnLexp7Uq+N02wdIRMs2dQYIiSrUMomhAHxT+kKEYCw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.1_1567585269543_0.0840038375722536"}, "_hasShrinkwrap": false, "publish_time": 1567585269738, "_cnpm_publish_time": 1567585269738, "_cnpmcore_publish_time": "2021-12-16T15:26:36.928Z"}, "3.11.0": {"name": "@vue/cli-plugin-eslint", "version": "3.11.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.11.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "9272ead6e8d36257d530f6bec00a290220e9c62a", "_id": "@vue/cli-plugin-eslint@3.11.0", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.14.1/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "99b14a9d4ab05d96fc707d958db9de4856133329", "size": 9368, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.11.0.tgz", "integrity": "sha512-cZWTXFbMu7/Ha6uAyvFsOxjC82afmHk0M9ye2MyeXmkvdL+tB2rcIITbd9HZypPaky1qv46Mp5l/lSd9W4PVCA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.11.0_1566403589509_0.0987564033077406"}, "_hasShrinkwrap": false, "publish_time": 1566403589656, "_cnpm_publish_time": 1566403589656, "_cnpmcore_publish_time": "2021-12-16T15:26:37.179Z"}, "4.0.0-rc.0": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-rc.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-rc.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "91024a7843c28a82074152c57672d533c6a8ed5b", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-rc.0", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.16.4/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7da356e7819f1e64262faa3fdca495838d6bf4d2", "size": 9229, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-rc.0.tgz", "integrity": "sha512-UXnm2704S+JxbEYsUSKA7YRoPR0DUmaIp+HvnttcvuuKL6tqjNEf7rGf4Qvn9zElJB+9mYEAZVbtQ+YUUj67cQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-rc.0_1566380991318_0.08518319007457076"}, "_hasShrinkwrap": false, "publish_time": 1566380991486, "_cnpm_publish_time": 1566380991486, "_cnpmcore_publish_time": "2021-12-16T15:26:37.395Z"}, "4.0.0-beta.3": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-beta.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-beta.3", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "f9a9880d93eefb9ac0d089c8566c04c9b1b08fa3", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-beta.3", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.16.4/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "477a116d1ebbc8d389bdc6c9eeba03392161ddba", "size": 9234, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-beta.3.tgz", "integrity": "sha512-TA1pIaV67GeZNM7rFfAW4H1CUEGRv2T3obiDTtJaWGETs4IWDxwFi9dAXnI8l7Lyl3Va+ZeRfgIfMLOb+QqQ0A=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-beta.3_1565233886418_0.15529079893682263"}, "_hasShrinkwrap": false, "publish_time": 1565233886558, "_cnpm_publish_time": 1565233886558, "_cnpmcore_publish_time": "2021-12-16T15:26:37.953Z"}, "3.10.0": {"name": "@vue/cli-plugin-eslint", "version": "3.10.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.10.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "075921cf32783023fa1f3eaa4ed2ac1b37275e6d", "_id": "@vue/cli-plugin-eslint@3.10.0", "_nodeVersion": "10.16.1", "_npmVersion": "lerna/3.14.1/node@v10.16.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "44a2abcecadc5b4854ee8fc4a7c5930f660293b0", "size": 9362, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.10.0.tgz", "integrity": "sha512-grM3Z4je8XlPomhYqTC4ILB26rTrJKZhEkCwbXdduMrWtpn2Ggotl2nYayplOzDgoZ4Cx3ykJMkRulla2Zi47g=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.10.0_1564834117654_0.9668736389542703"}, "_hasShrinkwrap": false, "publish_time": 1564834117757, "_cnpm_publish_time": 1564834117757, "_cnpmcore_publish_time": "2021-12-16T15:26:38.187Z"}, "4.0.0-beta.2": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-beta.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-beta.2", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0"}, "gitHead": "7a399ef7e29329c8975fa22b1f8b292375a2490f", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-beta.2", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "66461093a570f032b8b47ccb9b62bc39642f55eb", "size": 9280, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-beta.2.tgz", "integrity": "sha512-ofmp4dkJD4t/yxPCfqmKKKLRWHf1zhAjuhARUmJeLweE7n+uYhMBRhE+u5DMjjIN0GwJJIDA6GtJAZ1bD0W4Hg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-beta.2_1564417447046_0.7653490214975784"}, "_hasShrinkwrap": false, "publish_time": 1564417447203, "_cnpm_publish_time": 1564417447203, "_cnpmcore_publish_time": "2021-12-16T15:26:38.390Z"}, "4.0.0-beta.1": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-beta.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-beta.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "575349982b33e38d1b7fe792be8fe7373c45b272", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-beta.1", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "280e17a5c643692a87f94c9f37776d1b775f89fb", "size": 9270, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-beta.1.tgz", "integrity": "sha512-bEW8CzV9Lp9Qydkt/CRp+fNKUCMnLbO02l1jpfuJ8QRLY0g2EfI0bTDh+iySIwVwYg5B9uSrg4xr99aewg3Vmg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-beta.1_1564022495437_0.6169705982476188"}, "_hasShrinkwrap": false, "publish_time": 1564022495586, "_cnpm_publish_time": 1564022495586, "_cnpmcore_publish_time": "2021-12-16T15:26:38.670Z"}, "4.0.0-beta.0": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-beta.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-beta.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "00996ba7cf63d7d54aa8631586104aed60bf3cee", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-beta.0", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "fa07e811cbda83ecbad0ad5fec3ed8b7024eb694", "size": 9259, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-beta.0.tgz", "integrity": "sha512-TGJABHF9uKV8fmxqEr/4BhqwgzMGkpKiytKq7EH0Z6JPdZQ4o6cPukAwkO88Vakjj55MKLZEQ5q4r5dqItsxBA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-beta.0_1563805473897_0.8519016635250678"}, "_hasShrinkwrap": false, "publish_time": 1563805474090, "_cnpm_publish_time": 1563805474090, "_cnpmcore_publish_time": "2021-12-16T15:26:38.856Z"}, "4.0.0-alpha.5": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-alpha.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-alpha.5", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "615ae0f5b7adb79746fb71827b63fc745dd15ad4", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-alpha.5", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "612e789e7d810e5c77968b06431a89a9250c9b9f", "size": 9201, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-alpha.5.tgz", "integrity": "sha512-vB12rM4CUkYwh8/cA4tah+75y23YQBuUoA2pZIdvheeucFc2CXGNslOkHxhlThjDgjPqdkya4j3XAK9xgEuBeg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-alpha.5_1563117249529_0.5317715346239509"}, "_hasShrinkwrap": false, "publish_time": 1563117249717, "_cnpm_publish_time": 1563117249717, "_cnpmcore_publish_time": "2021-12-16T15:26:39.084Z"}, "4.0.0-alpha.4": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-alpha.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-alpha.4", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "bd82649a64c706493cf4a74c8c661bc20e6dff2b", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-alpha.4", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "d823a9ed4a715922a8478f8c2d3470e976b6c39d", "size": 9201, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-alpha.4.tgz", "integrity": "sha512-gsiDNnqVd4nJywdkdVMsRBlLiq0DsCnoUfxos7Vc/I8b8TCUetDKL8z/6B1AWwEZXfss3UYxrroLuXl3G3nyFA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-alpha.4_1562413943543_0.6095764370323518"}, "_hasShrinkwrap": false, "publish_time": 1562413943926, "_cnpm_publish_time": 1562413943926, "_cnpmcore_publish_time": "2021-12-16T15:26:39.279Z"}, "3.9.2": {"name": "@vue/cli-plugin-eslint", "version": "3.9.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.9.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "eda08fb2a999354017f99c77856673fe9c83a4b2", "_id": "@vue/cli-plugin-eslint@3.9.2", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "747c616b13a11f34ac80554eee899cbfcd1977b8", "size": 9333, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.9.2.tgz", "integrity": "sha512-AdvWJN+4Px2r3hbTDM2/rCtTcS6VyI7XuRljbfr2V9nF9cJiH4qsXFrTCRj3OgupbXJ14fUGKrLxmznLZIm1jA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.9.2_1562413079756_0.724142586570518"}, "_hasShrinkwrap": false, "publish_time": 1562413079846, "_cnpm_publish_time": 1562413079846, "_cnpmcore_publish_time": "2021-12-16T15:26:39.471Z"}, "4.0.0-alpha.3": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-alpha.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-alpha.3", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "300d2aa2d4732f508b040d9d1c56c465a3c07a5d", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-alpha.3", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "545cf033f125d47394a521a3443bf8066f53a0f7", "size": 9194, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-alpha.3.tgz", "integrity": "sha512-VA7YyzukbeyLzBQv4I7F0jhFS/AAoSIxoSfFglJx5kv5ztDsNHPhEM4yYFsSuX/Sj+RACiqf3f5uX1myOgYDvQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-alpha.3_1562202612932_0.14878907203567304"}, "_hasShrinkwrap": false, "publish_time": 1562202613026, "_cnpm_publish_time": 1562202613026, "_cnpmcore_publish_time": "2021-12-16T15:26:39.721Z"}, "3.9.1": {"name": "@vue/cli-plugin-eslint", "version": "3.9.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.9.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "ea91df2d704375f0b9655a053f5707cf422484df", "_id": "@vue/cli-plugin-eslint@3.9.1", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "11f3bdc425619ea2f9e2ab74a8f89f75aa6c2657", "size": 9317, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.9.1.tgz", "integrity": "sha512-j/ZF3G+OhtYO229UIkH6ef6UekW6MER9+o5jGOkwWed4ZheeT2ssFD2rCSJMXCuV7ygJvKOFGaBJBULL8Ibmzg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.9.1_1562202211523_0.24935307050197064"}, "_hasShrinkwrap": false, "publish_time": 1562202211798, "_cnpm_publish_time": 1562202211798, "_cnpmcore_publish_time": "2021-12-16T15:26:39.915Z"}, "3.9.0": {"name": "@vue/cli-plugin-eslint", "version": "3.9.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.9.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "f276598ece07204e5b8b54e4bbcccfa81cd1b478", "_id": "@vue/cli-plugin-eslint@3.9.0", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "e4c94976755c960c9770b16dd2cdc60959fb5fc0", "size": 9317, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.9.0.tgz", "integrity": "sha512-H26tsx6ZeXlbe1yZXIjzQ2TdqJEQ2mss4bS2LZK2PVQiXHSv2WOUf89nD71OD+1Li/9TORjoWmRVdy2Nptu0Zg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.9.0_1562142117435_0.4147254405024843"}, "_hasShrinkwrap": false, "publish_time": 1562142117584, "_cnpm_publish_time": 1562142117584, "_cnpmcore_publish_time": "2021-12-16T15:26:40.116Z"}, "4.0.0-alpha.2": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-alpha.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-alpha.2", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "yorkie": "^2.0.0"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "0c2ebc4ae2d63191ff04ca2af9c90a2727c67090", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-alpha.2", "_nodeVersion": "10.16.0", "_npmVersion": "lerna/3.14.1/node@v10.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "3fc08464b62a9e5bf4cbc7c80534d2a7844d2e00", "size": 9189, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-alpha.2.tgz", "integrity": "sha512-X9GqDTyajDnyo1I1FY6VFWPSkDKi1s8mqqFf2krm1AyQ2zzFTQpU+y0gladAAY+eLCzGcHapX+fcOC+WwhJzWA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-alpha.2_1562139821074_0.5214923394320143"}, "_hasShrinkwrap": false, "publish_time": 1562139821206, "_cnpm_publish_time": 1562139821206, "_cnpmcore_publish_time": "2021-12-16T15:26:40.308Z"}, "3.8.0": {"name": "@vue/cli-plugin-eslint", "version": "3.8.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.8.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "d6ce9c2fe97a0bc8ad09df418765c095f386a5c1", "_id": "@vue/cli-plugin-eslint@3.8.0", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.14.1/node@v10.15.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "0402dfd3864b0224bceb264df89a4209000bb98c", "size": 9305, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.8.0.tgz", "integrity": "sha512-18LDJmjH0DDw9T4+RbrSVk4xkF8t8RDRsEPJLPurno1YVJodIkQ6lqVu82faVgtvPyCoqiaicoTq/iwi1avoLQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.8.0_1558797658367_0.5186988633984317"}, "_hasShrinkwrap": false, "publish_time": 1558797658492, "_cnpm_publish_time": 1558797658492, "_cnpmcore_publish_time": "2021-12-16T15:26:40.590Z"}, "4.0.0-alpha.1": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-alpha.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-alpha.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "8fa6fa694a7bcca4bfebfe461bd71bfa8feb9ee3", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-alpha.1", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.14.1/node@v10.15.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "3876100939485fd1390b715d50c6fcd9eb1c977f", "size": 9177, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-alpha.1.tgz", "integrity": "sha512-q7aT5I1Y9/Qutcbcbt+8s7v+jdR1Vww3KWTu4PVcOjzlWy1xVvf0kGNh/vqNMZ8FfsvWhII5l0rR/lAhneBoIw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-alpha.1_1558777810511_0.47274712364555094"}, "_hasShrinkwrap": false, "publish_time": 1558777810668, "_cnpm_publish_time": 1558777810668, "_cnpmcore_publish_time": "2021-12-16T15:26:41.403Z"}, "4.0.0-alpha.0": {"name": "@vue/cli-plugin-eslint", "version": "4.0.0-alpha.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.0.0-alpha.0", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29"}, "peerDependencies": {"eslint": ">= 1.6.0"}, "gitHead": "63f501a2ca54232b6a9d48c56c1e06a1787ddc83", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@4.0.0-alpha.0", "_nodeVersion": "8.16.0", "_npmVersion": "lerna/3.13.4/node@v8.16.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "a42ac5fdca242d04868768ac896dca9b3dab9661", "size": 9174, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.0.0-alpha.0.tgz", "integrity": "sha512-f7aCn65tbFOs5dQdxXRd9I/fS9m42+zkpMbXpOXXpAWtRS4NLlkktMIremtphIGaN6WubwyN9poaz+ui6SAKAg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.0.0-alpha.0_1556641280717_0.5830004577010273"}, "_hasShrinkwrap": false, "publish_time": 1556641280879, "_cnpm_publish_time": 1556641280879, "_cnpmcore_publish_time": "2021-12-16T15:26:41.613Z"}, "3.7.0": {"name": "@vue/cli-plugin-eslint", "version": "3.7.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.7.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "0dbfa5a0bfc0da80bfaa43c1318aad7b3ff88353", "_id": "@vue/cli-plugin-eslint@3.7.0", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "6b495fe3c82ec94347c424a9de3cca467a53f90e", "size": 9306, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.7.0.tgz", "integrity": "sha512-oFdOLQu6PQKbxinF55XH1lH8hgiDRyb3gIvSKu5YV5r6dnsRdKDxOKLE1PTbaZzQot3Ny/Y7gk025x1qpni3IA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.7.0_1556432595153_0.8152026167910813"}, "_hasShrinkwrap": false, "publish_time": 1556432595350, "_cnpm_publish_time": 1556432595350, "_cnpmcore_publish_time": "2021-12-16T15:26:41.847Z"}, "3.6.0": {"name": "@vue/cli-plugin-eslint", "version": "3.6.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.6.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.2.0", "webpack": ">=4 < 4.29", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "99a06d1758183e671928af7dc6ae3eec47d58180", "_id": "@vue/cli-plugin-eslint@3.6.0", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "83bd27a3cc8166c49acd4adc15d91c1dc13c9c1e", "size": 9304, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.6.0.tgz", "integrity": "sha512-jY/Lvkzv+tBdBFj6DmzZaUw4cFGom5kyxpTUPzM2swsWQITApg+0GmFq/VnH7lVY81fuOBmiFgL1YI+4WX6wKQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.6.0_1555171157543_0.14741688191824132"}, "_hasShrinkwrap": false, "publish_time": 1555171157670, "_cnpm_publish_time": 1555171157670, "_cnpmcore_publish_time": "2021-12-16T15:26:42.445Z"}, "3.5.1": {"name": "@vue/cli-plugin-eslint", "version": "3.5.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.5.1", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.0.0", "webpack": ">=4 < 4.29", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "0b363bec49164c2476dac59f6aa9bf42d4eba552", "_id": "@vue/cli-plugin-eslint@3.5.1", "_nodeVersion": "10.15.1", "_npmVersion": "lerna/3.13.0/node@v10.15.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "20b4689be35d6b2146cbde2eb6f9d2f3033a0e81", "size": 9250, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.5.1.tgz", "integrity": "sha512-0RSF3LPOXUIgArrg06HK1Yg6wcR348wssVX3pG41zxDAGTmfe8TpL2XPiP/KinrYwQLSUBPbr8zdf9Hb1+Tv2w=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.5.1_1552377944883_0.36355008172300574"}, "_hasShrinkwrap": false, "publish_time": 1552377945009, "_cnpm_publish_time": 1552377945009, "_cnpmcore_publish_time": "2021-12-16T15:26:42.697Z"}, "3.5.0": {"name": "@vue/cli-plugin-eslint", "version": "3.5.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.5.0", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.0.0", "webpack": ">=4 < 4.29", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "65eec48581d812474969f54644a7f18c1e0d159b", "_id": "@vue/cli-plugin-eslint@3.5.0", "_nodeVersion": "10.15.1", "_npmVersion": "lerna/3.13.0/node@v10.15.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "c5529ace3925766ffb45d99f0fddcc019b0181b5", "size": 9244, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.5.0.tgz", "integrity": "sha512-tt5vjx3gT4KjIcwwsNK1FrvJHw6DkK8PA81oGBH7ARgf6HHXB80ruufwmddEhpn5MNKCsbvLkkUnVPs6t9WRUw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.5.0_1552027352172_0.4677286460331669"}, "_hasShrinkwrap": false, "publish_time": 1552027352310, "_cnpm_publish_time": 1552027352310, "_cnpmcore_publish_time": "2021-12-16T15:26:42.918Z"}, "3.4.1": {"name": "@vue/cli-plugin-eslint", "version": "3.4.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.4.1", "babel-eslint": "^10.0.1", "eslint-loader": "^2.1.2", "globby": "^9.0.0", "webpack": ">=4 < 4.29", "eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "optionalDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue": "^4.7.1"}, "gitHead": "0c3f6183a80c9d3e5330554bc52db17afdb6fec5", "_id": "@vue/cli-plugin-eslint@3.4.1", "_nodeVersion": "10.15.1", "_npmVersion": "lerna/3.10.7/node@v10.15.1+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "59896379e12d68aefcdccac28c691cb6e23e5720", "size": 9253, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.4.1.tgz", "integrity": "sha512-1Ka7PyEatxkpoHEz2u5J/4E2joaUFs0UDrvsKLVbtz05LYpv/HHwFWCmhbZPZ1GSLDeEewwQ11fBgeQZAE5TYw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.4.1_1550645408145_0.10865163047564352"}, "_hasShrinkwrap": false, "publish_time": 1550645408336, "_cnpm_publish_time": 1550645408336, "_cnpmcore_publish_time": "2021-12-16T15:26:43.104Z"}, "3.4.0": {"name": "@vue/cli-plugin-eslint", "version": "3.4.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.4.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^9.0.0", "webpack": ">=4 < 4.29"}, "gitHead": "22232df78154ef2e5b312f235a71535ef471480f", "_id": "@vue/cli-plugin-eslint@3.4.0", "_nodeVersion": "10.15.0", "_npmVersion": "lerna/3.10.7/node@v10.15.0+x64 (darwin)", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "e9fceff873661848be7e1341b31dad23302cd0ac", "size": 9212, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.4.0.tgz", "integrity": "sha512-KbUpN3Zd/V5zCah9nT9cukTHmd9g4IRskyuIeBw5KZqRDoUgCS7I2+OWlcAMneRuqZwgFbTFYmr9N3s6gz4SVg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.4.0_1548960353086_0.35567078064991686"}, "_hasShrinkwrap": false, "publish_time": 1548960353256, "_cnpm_publish_time": 1548960353256, "_cnpmcore_publish_time": "2021-12-16T15:26:43.761Z"}, "3.3.0": {"name": "@vue/cli-plugin-eslint", "version": "3.3.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.3.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "gitHead": "5937a67a48af1ff4c0053be787e8cd64440466df", "_id": "@vue/cli-plugin-eslint@3.3.0", "dist": {"shasum": "74689305e8623057106ed7654dfb0ea7d11395b1", "size": 8910, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.3.0.tgz", "integrity": "sha512-kL68xVHv8xFqB06DmzACSviuNPaJcTvlKrCeZarxrXecry2MiOhbyiBToCQXiu2YhfxH8pUy3GFsSRyn3eYEuw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.3.0_1546920294991_0.7412185833742742"}, "_hasShrinkwrap": false, "publish_time": 1546920295314, "_cnpm_publish_time": 1546920295314, "_cnpmcore_publish_time": "2021-12-16T15:26:43.982Z"}, "3.2.2": {"name": "@vue/cli-plugin-eslint", "version": "3.2.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.2.2", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "_id": "@vue/cli-plugin-eslint@3.2.2", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "e235c84f337ba6a83a9bdc997d73494191295a67", "size": 8157, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.2.2.tgz", "integrity": "sha512-usaTfKjMq7mGkUyTkgoGT36XLnfWjbxTAK00Hl46u3Iz24/ACapS9/p5O+uMv2Lj7epYWV+C36g72agzRrMP9A=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.2.2_1546175972297_0.5109500925980555"}, "_hasShrinkwrap": false, "publish_time": 1546175972526, "_cnpm_publish_time": 1546175972526, "_cnpmcore_publish_time": "2021-12-16T15:26:44.208Z"}, "3.2.1": {"name": "@vue/cli-plugin-eslint", "version": "3.2.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.2.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "gitHead": "79eb41fa9fb069185f88e409ac633a3e9167cb3f", "_id": "@vue/cli-plugin-eslint@3.2.1", "dist": {"shasum": "4dc353add93023363bf4c6b347e64472d1f2f432", "size": 8772, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.2.1.tgz", "integrity": "sha512-Z/eQw18FjTypMMryNg8WCYJxEBmSAtnzukRWWNFwqNnh2zM/2J6yR4dYhsyjNtMEMUOnQsAsJnqgw45rLu8sJg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.2.1_1543345432290_0.8801744457027869"}, "_hasShrinkwrap": false, "publish_time": 1543345432415, "_cnpm_publish_time": 1543345432415, "_cnpmcore_publish_time": "2021-12-16T15:26:44.400Z"}, "3.2.0": {"name": "@vue/cli-plugin-eslint", "version": "3.2.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.2.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "gitHead": "d60657af6d2da66a3b682b0caf00773c85a379d5", "_id": "@vue/cli-plugin-eslint@3.2.0", "dist": {"shasum": "111807a3456ecf5a77f5ce24ff8a71b3f17ebee2", "size": 8765, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.2.0.tgz", "integrity": "sha512-uqrXg86D3SpWU5K2fOZU6OmX+egGbu+n3gUyCkiBASbxKNmww6UcFE9KsG2rj1FXM2mW3FwDTXspM++tptUSVg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.2.0_1543344712702_0.5323898055327903"}, "_hasShrinkwrap": false, "publish_time": 1543344712826, "_cnpm_publish_time": 1543344712826, "_cnpmcore_publish_time": "2021-12-16T15:26:44.640Z"}, "3.1.5": {"name": "@vue/cli-plugin-eslint", "version": "3.1.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.1.1", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "gitHead": "cd7a5d0b5231fcb6970d8aac42708c3ab29c0df3", "_id": "@vue/cli-plugin-eslint@3.1.5", "dist": {"shasum": "9143d3e8670ad6e73a408cafda9daba76559ddef", "size": 8767, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.1.5.tgz", "integrity": "sha512-ZcRJqqDeMxf5c6YkCT7nr29SGePBfyeuYfG8p3QVSND90nhS/952/G6ZAb7Ph1dNNsEONU4plQYnQAte6Caf4w=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.1.5_1542010910135_0.30383268327612734"}, "_hasShrinkwrap": false, "publish_time": 1542010910260, "_cnpm_publish_time": 1542010910260, "_cnpmcore_publish_time": "2021-12-16T15:26:44.873Z"}, "3.1.4": {"name": "@vue/cli-plugin-eslint", "version": "3.1.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.1.1", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "_id": "@vue/cli-plugin-eslint@3.1.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7cf1a4a4540db9210a5e418eae3c19e1eb302275", "size": 7985, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.1.4.tgz", "integrity": "sha512-1ET67AfYEMb5Lesd8YC6X8WLdrt/Mqr9x/oQjrc6hIYInIThOVo6hkWZXgznru5A1lD1kw4GQWq/DCUKfw6v2g=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.1.4_1541170606853_0.7325415711597161"}, "_hasShrinkwrap": false, "publish_time": 1541170607016, "_cnpm_publish_time": 1541170607016, "_cnpmcore_publish_time": "2021-12-16T15:26:45.102Z"}, "3.1.3": {"name": "@vue/cli-plugin-eslint", "version": "3.1.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.1.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "_id": "@vue/cli-plugin-eslint@3.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "aaf0fd3c15b38981599c61ad7a103a0590898257", "size": 7970, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.1.3.tgz", "integrity": "sha512-T1pGx8j2lCJe3/ExN3I+C6yHWoxkuELKp4Vd+UlMXQGrjxfdfBNrRLQ+RRvyH+i6dy/Vr+PFSYsUG7AL56NwoQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.1.3_1540986757390_0.7692717437440737"}, "_hasShrinkwrap": false, "publish_time": 1540986757560, "_cnpm_publish_time": 1540986757560, "_cnpmcore_publish_time": "2021-12-16T15:26:45.304Z"}, "3.1.2": {"name": "@vue/cli-plugin-eslint", "version": "3.1.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.1.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1", "globby": "^8.0.1"}, "_id": "@vue/cli-plugin-eslint@3.1.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "7b6664ee51f8d694552cc5e0ba6718c3d5a5f76d", "size": 7960, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.1.2.tgz", "integrity": "sha512-9CttpXl4W6bY3RGOfp/AlM8/pcjYJtYnY8BDaS00EVa1rliXAxEb/UhGmWsy52GhvXmbS5GoqC4wOSqanDoWRQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.1.2_1540985148983_0.5761843103264093"}, "_hasShrinkwrap": false, "publish_time": 1540985149144, "_cnpm_publish_time": 1540985149144, "_cnpmcore_publish_time": "2021-12-16T15:26:45.541Z"}, "3.1.1": {"name": "@vue/cli-plugin-eslint", "version": "3.1.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.1.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1"}, "_id": "@vue/cli-plugin-eslint@3.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "e0acc90912f83ca9e16384c5c0a75d7efea44c33", "size": 7923, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.1.1.tgz", "integrity": "sha512-4ajW19l55su2ENeKgBf2zbgFzApcKlkauBVnLcmfVJggBpX46A0IlbRoZr8TnKnWZ9vP4SYkm74jgWo7i452vQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.1.1_1540958306371_0.14621156266874236"}, "_hasShrinkwrap": false, "publish_time": 1540958306511, "_cnpm_publish_time": 1540958306511, "_cnpmcore_publish_time": "2021-12-16T15:26:45.773Z"}, "3.1.0": {"name": "@vue/cli-plugin-eslint", "version": "3.1.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.1.0", "babel-eslint": "^10.0.1", "eslint": "^4.19.1", "eslint-loader": "^2.1.1", "eslint-plugin-vue": "^4.7.1"}, "_id": "@vue/cli-plugin-eslint@3.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "dist": {"shasum": "76324ef234aa754b8457c376d6ba2347a3363b53", "size": 7917, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.1.0.tgz", "integrity": "sha512-E2mTLvejVOpZ7D5GJnU5dPZ2PgPW8VyXwdWYMrV6IoOBMEmUDCylPede0hnFKPxJ2D8/Pl31+B9HTcoUGbgT8w=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.1.0_1540923614552_0.7485176554693096"}, "_hasShrinkwrap": false, "publish_time": 1540923614693, "_cnpm_publish_time": 1540923614693, "_cnpmcore_publish_time": "2021-12-16T15:26:45.993Z"}, "3.0.5": {"name": "@vue/cli-plugin-eslint", "version": "3.0.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.5", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.5", "dist": {"shasum": "d673775eaf612658f8c2fbde35610dd050c9c11a", "size": 7397, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.5.tgz", "integrity": "sha512-FIOHXr+/blWenEhOieINKOrs6y+mfSKn6JoIBkX0TvNFVw0qprKyJFnYt/Eu/1wUis9B45uhHme+3eKfzqxSVQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.5_1539076824871_0.4491858454537365"}, "_hasShrinkwrap": false, "publish_time": 1539076824998, "_cnpm_publish_time": 1539076824998, "_cnpmcore_publish_time": "2021-12-16T15:26:46.228Z"}, "3.0.4": {"name": "@vue/cli-plugin-eslint", "version": "3.0.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.4", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.4", "dist": {"shasum": "0c62607c44a3b63fc730fdc0dd0ad5165bc559f6", "size": 7346, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.4.tgz", "integrity": "sha512-6B1IbCPXTu2DvEugDiEFkg5WGjIJ/82MXdHS1J+lYlI2u5ooVNJ6DjHerdpHpCxmM4dBoDNNLI57ZcLFGyv/KA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.4_1537862474141_0.825474785308238"}, "_hasShrinkwrap": false, "publish_time": 1537862474286, "_cnpm_publish_time": 1537862474286, "_cnpmcore_publish_time": "2021-12-16T15:26:46.518Z"}, "3.0.3": {"name": "@vue/cli-plugin-eslint", "version": "3.0.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.2", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.3", "dist": {"shasum": "728daadd5d3b8af69679c7dcbc63b7f8d00e6c0c", "size": 7344, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.3.tgz", "integrity": "sha512-FnaK0y7SlTPbFzPlJxnF+hQqVChxE+GAg2qdX1QDuasMV3LzUwi2FZYW2Gb0xoD/it8ZDnv0RFgYcQxoRCrPMQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.3_1536729523314_0.29783806716404104"}, "_hasShrinkwrap": false, "publish_time": 1536729523474, "_cnpm_publish_time": 1536729523474, "_cnpmcore_publish_time": "2021-12-16T15:26:46.717Z"}, "3.0.2": {"name": "@vue/cli-plugin-eslint", "version": "3.0.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.2", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.2", "dist": {"shasum": "714a3d612364a215309f28c630f930bac24f66d1", "size": 7341, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.2.tgz", "integrity": "sha512-2830hwhsY193JUF886zo9W2xOMYM/1Utg5105h8eBO0Ga4XqnzUQDjmSwk5xIvZTDC/Jnlatxt6aekfraX+GxA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.2_1536677178586_0.673360823553341"}, "_hasShrinkwrap": false, "publish_time": 1536677178748, "_cnpm_publish_time": 1536677178748, "_cnpmcore_publish_time": "2021-12-16T15:26:46.945Z"}, "3.0.1": {"name": "@vue/cli-plugin-eslint", "version": "3.0.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.1", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.1", "dist": {"shasum": "9330cfe4843058f28b0ab2871a8862fa31f3a5c0", "size": 7280, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.1.tgz", "integrity": "sha512-I1N8iHKI1wlU1769mS8X7CbWJi8G3aecmTrIgSn0Mco00ANxyU63aWFY08+NTMwwt/C4dZqpfoSu9H/piqHLBQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.1_1534435853582_0.2779307157023625"}, "_hasShrinkwrap": false, "publish_time": 1534435853666, "_cnpm_publish_time": 1534435853666, "_cnpmcore_publish_time": "2021-12-16T15:26:47.153Z"}, "3.0.0": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0", "dist": {"shasum": "97b163df1272b39e61e18c8add5dfe28a92375c4", "size": 6955, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0.tgz", "integrity": "sha512-J4UuVgMYColq8v3YXpmXjfjqTEvb6aVkoAqtsu0LgWcJWAps0LHP/lbjv5Cw0wKoBF8EHrdMAE5mW3L12E+V8g=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0_1533913304367_0.5951617868226959"}, "_hasShrinkwrap": false, "publish_time": 1533913304437, "_cnpm_publish_time": 1533913304437, "_cnpmcore_publish_time": "2021-12-16T15:26:47.376Z"}, "3.0.0-rc.12": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.12", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.12", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.12", "dist": {"shasum": "5bd6b85a6097d143f8684c4a4ab42d3625a3ab3f", "size": 6959, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.12.tgz", "integrity": "sha512-X5Of/31CCDD9AQ68Vb5XVsKZFg4w0an7764mVBvgGRhALmCcHL1Kasr2sc0oXMxJDEp868tFA2bvxrYBmRsDEw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.12_1533795829796_0.6499060015903488"}, "_hasShrinkwrap": false, "publish_time": 1533795829853, "_cnpm_publish_time": 1533795829853, "_cnpmcore_publish_time": "2021-12-16T15:26:47.597Z"}, "3.0.0-rc.11": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.11", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.11", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.11", "_npmVersion": "6.2.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "dist": {"shasum": "87d23a6cafc1e7b9759228d347117f0d558c4d05", "size": 6880, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.11.tgz", "integrity": "sha512-OoCauHcbjMVfGC+htFqkrwQMhZZLCaDHRu4C9EYIheOg8DPTzkJzejprTVpMqkvJFtr3OtWRpK70fpeFZQrQwg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.11_1533657490477_0.7807963807510638"}, "_hasShrinkwrap": false, "publish_time": 1533657491143, "_cnpm_publish_time": 1533657491143, "_cnpmcore_publish_time": "2021-12-16T15:26:47.789Z"}, "3.0.0-rc.10": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.10", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.10", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.10", "dist": {"shasum": "ca3034fd9ce828819256178cec249d361a944cc8", "size": 6700, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.10.tgz", "integrity": "sha512-f1GS50DfXrhgRowskP7jWqChcupEWEPCS4wHmaRwzFQCSZb7RfT5zSF1xulnbjwGl0CtaVvcKp8V31CTJvfM9Q=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.10_1532989173060_0.6253798364166705"}, "_hasShrinkwrap": false, "publish_time": 1532989173133, "_cnpm_publish_time": 1532989173133, "_cnpmcore_publish_time": "2021-12-16T15:26:48.019Z"}, "3.0.0-rc.9": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.9", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.9", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.9", "dist": {"shasum": "543b2934108abda8e1b930b21a479d15128b3daa", "size": 6701, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.9.tgz", "integrity": "sha512-sGRezpCsxtd1RCTBBls8q5WgHuB0AeHg2riyK6OxZ61XNbAdSU29Ilrqoz25XRsaDWpdD94+HRXujz6ZdlxeHg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.9_1532834636126_0.9112608163770697"}, "_hasShrinkwrap": false, "publish_time": 1532834636215, "_cnpm_publish_time": 1532834636215, "_cnpmcore_publish_time": "2021-12-16T15:26:48.219Z"}, "3.0.0-rc.8": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.8", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.8", "dist": {"shasum": "7e1f79c0d94ff90d70c5b9edefb2601a9c2d09b9", "size": 6643, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.8.tgz", "integrity": "sha512-i+4ySsL0a/p6a2Kb2Y1kB7GdCvu3/O2SA8t61o8JoM/dRxNsSPvij4Ws21Jigalao7h4djVW0dNggUWZJu7Nrg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.8_1532714624722_0.10932318288412435"}, "_hasShrinkwrap": false, "publish_time": 1532714624805, "_cnpm_publish_time": 1532714624805, "_cnpmcore_publish_time": "2021-12-16T15:26:48.468Z"}, "3.0.0-rc.7": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.7", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.7", "dist": {"shasum": "883cc1b0e8db65d5a6a4dc36fdc138c5ba95b20f", "size": 6646, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.7.tgz", "integrity": "sha512-gcpnDrdTCO5P+OCUnR8rXibg4VMcsRfRIYlHPQ6wv6K/e5ZdkqKSQ2L3UW0Anoaftwbt+kggwI/rsJh+g4HL4A=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.7_1532659119140_0.9188614411687663"}, "_hasShrinkwrap": false, "publish_time": 1532659119196, "_cnpm_publish_time": 1532659119196, "_cnpmcore_publish_time": "2021-12-16T15:26:48.679Z"}, "3.0.0-rc.6": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.6", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.6", "dist": {"shasum": "343f9d88b7d186007ff2462ed86ebfe1112ec009", "size": 6645, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.6.tgz", "integrity": "sha512-Tn5pOM3jABvaoyD8PYi7bG3dMCcFZF223+89PTx+7iuaUJ7DcYZBW953I8ddHmGI7HJFDgg4dZ3URZ81wVtWxA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.6_1532643136568_0.978109504007534"}, "_hasShrinkwrap": false, "publish_time": 1532643137046, "_cnpm_publish_time": 1532643137046, "_cnpmcore_publish_time": "2021-12-16T15:26:48.886Z"}, "3.0.0-rc.5": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.5", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.5", "dist": {"shasum": "48870b2d6e511b6abe5bd8b5f15168d3cff466bf", "size": 6672, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.5.tgz", "integrity": "sha512-EpKgzceh6Do6LUBfg4A6p09cYtHdseBKx/AsgYzX++jSEJ0YHMTpYh9WeQJlCkdfgZqUwQ9AcaSiq6mWia/ggw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "akryum", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.5_1531749113166_0.8839590885964372"}, "_hasShrinkwrap": false, "publish_time": 1531749113218, "_cnpm_publish_time": 1531749113218, "_cnpmcore_publish_time": "2021-12-16T15:26:49.110Z"}, "3.0.0-rc.4": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.4", "babel-eslint": "^8.2.5", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.4", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "dist": {"shasum": "ac9c8be51c56e9cb6e08883683f771df841d9b0a", "size": 6601, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.4.tgz", "integrity": "sha512-6GxpuN76QLnnNsuQdo1+YzT2oEFZdSpUZl0GtMCi4PJAZxMjE47r/xQCcfr/O3Sc6NGrsPmpbUZhy37jBRa7+g=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.4_1531501892989_0.21544289032770503"}, "_hasShrinkwrap": false, "publish_time": 1531501893056, "_cnpm_publish_time": 1531501893056, "_cnpmcore_publish_time": "2021-12-16T15:26:49.296Z"}, "3.0.0-rc.3": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.3", "babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.3", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "dist": {"shasum": "9248d44f7d44ab8523346ff75486e26a20d51e94", "size": 6578, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.3.tgz", "integrity": "sha512-U6o1AEKDZfAGVkvytQg4aJDbnp3xiiL8pmupfL8lYgPBRB5Hj525D669EeJhsGlXQk7dhs0nf47JtUCYANQPeg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.3_1529332809321_0.2945760217849247"}, "_hasShrinkwrap": false, "publish_time": 1529332809404, "_cnpm_publish_time": 1529332809404, "_cnpmcore_publish_time": "2021-12-16T15:26:49.566Z"}, "3.0.0-rc.2": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.2", "babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.2", "dist": {"shasum": "e44ae313f2d5cbd414bb6c595e172eef3316449b", "size": 6651, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.2.tgz", "integrity": "sha512-DK+Vh6Lvz8CRmm4KaqGavI98BHasgopEzrE/BYYI2Jce3V59xSuyghsnlyeR1FuPoQT72sE49TtnTag6YF2pYA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.2_1528983904216_0.7938825660822866"}, "_hasShrinkwrap": false, "publish_time": 1528983904276, "_cnpm_publish_time": 1528983904276, "_cnpmcore_publish_time": "2021-12-16T15:26:49.793Z"}, "3.0.0-rc.1": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-rc.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-rc.1", "babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0", "launch-editor": "^2.2.1"}, "_id": "@vue/cli-plugin-eslint@3.0.0-rc.1", "dist": {"shasum": "6acf899ac117e07faf1717b28ab6b4b6a16ec1b3", "size": 6649, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-rc.1.tgz", "integrity": "sha512-5RQgNVYXX7slEXOIw3ntoPOXmGC0cQboq+Gaq211NM13f5IOuCgDZV0Q3S2uiyAnGgVh6kDG+tV6j6+sVfOb8w=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-rc.1_1528920689494_0.39720810782756666"}, "_hasShrinkwrap": false, "publish_time": 1528920689570, "_cnpm_publish_time": 1528920689570, "_cnpmcore_publish_time": "2021-12-16T15:26:50.013Z"}, "3.0.0-beta.16": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.16", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-beta.16", "babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.16", "dist": {"shasum": "9dfb37cc9506996b319860562551838bc55a635e", "size": 6278, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.16.tgz", "integrity": "sha512-P133IpAPTfFPmpF97gZtM8plBmQJYByF/na05UhzdKm3raeWsWk9sjVcPj8HjX9GIRLJ0+LuFGSvxf1qoZmbjg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.16_1528433505462_0.39360179326764966"}, "_hasShrinkwrap": false, "publish_time": 1528433505564, "_cnpm_publish_time": 1528433505564, "_cnpmcore_publish_time": "2021-12-16T15:26:50.199Z"}, "3.0.0-beta.15": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.15", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^3.0.0-beta.15", "babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.15", "dist": {"shasum": "857d2f0f539f9853484d78a5104ad06077ab4dc9", "size": 6279, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.15.tgz", "integrity": "sha512-BrxB8e7ySU3Z1MZBVN3o2LFKnYbjDe5R410wQj10NSxUU9FZGjywovUux8iaIFUa2+ch+dM+MjoETiRI77FEsQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.15_1527702188267_0.6619140697208563"}, "_hasShrinkwrap": false, "publish_time": 1527702188326, "_cnpm_publish_time": 1527702188326, "_cnpmcore_publish_time": "2021-12-16T15:26:50.445Z"}, "3.0.0-beta.14": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.14", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.14", "dist": {"shasum": "30e4a45fabd8640cadcdb4981de60bd9a9eed99e", "size": 6266, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.14.tgz", "integrity": "sha512-gXCogz7wevnF/8Pu7n57KJBTstGUgUQlrPkbhIUw9s15GuFK+b/WD77H1EOpuQEvICVOmufc+2GrmKJmBG6PoQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.14_1527614850486_0.5909767170603306"}, "_hasShrinkwrap": false, "publish_time": 1527614850554, "_cnpm_publish_time": 1527614850554, "_cnpmcore_publish_time": "2021-12-16T15:26:50.657Z"}, "3.0.0-beta.13": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.13", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.13", "dist": {"shasum": "686ff254506aa20a9d018db23ec7654d6eca10bf", "size": 6265, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.13.tgz", "integrity": "sha512-7uR0p46l7x9qIR31QDnqDEVYs/Rsq3SSEb3j2rsiB3Y+H7LvI7U4YwImKY+GjN2W3mVJomrULWZKxoqMd2SI3w=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.13_1527614504129_0.4640386900860336"}, "_hasShrinkwrap": false, "publish_time": 1527614504201, "_cnpm_publish_time": 1527614504201, "_cnpmcore_publish_time": "2021-12-16T15:26:50.868Z"}, "3.0.0-beta.12": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.12", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.12", "dist": {"shasum": "d7067d34468045e8704c8070dde2e14290eda9e7", "size": 6267, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.12.tgz", "integrity": "sha512-a8aN8aUvDT8AR4halLmjJzQ9dZ3yg3uyrBBUk8UoX3t9QlGlo/Hb6j1H7REJltK5un9kHf/4TpzP2z7mU1q5aA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.12_1527571765567_0.5828842137912085"}, "_hasShrinkwrap": false, "publish_time": 1527571765657, "_cnpm_publish_time": 1527571765657, "_cnpmcore_publish_time": "2021-12-16T15:26:51.124Z"}, "3.0.0-beta.11": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.11", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.11", "dist": {"shasum": "09c6715b29ed81256ac65e09511b4439acecc646", "size": 4007, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.11.tgz", "integrity": "sha512-NSAS/IXqybQSXi/VviJIkZIpddQHChlLXkk+dljrgqYuljVIZboeYpep+4wqx+A2OOocuElJBQ51/iVCGEy+LA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.11_1526931810400_0.1729949969423965"}, "_hasShrinkwrap": false, "publish_time": 1526931810515, "_cnpm_publish_time": 1526931810515, "_cnpmcore_publish_time": "2021-12-16T15:26:51.328Z"}, "3.0.0-beta.10": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.10", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.10", "dist": {"shasum": "aa6bcc9bcbcfe72d835176a9e18ee5467d1110ce", "size": 3214, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.10.tgz", "integrity": "sha512-zt8r1y+lKaNQjRJ3HfWs/SLt+Og66IFIMlfOQOlzhr7rjFFjxUIY69lPKwU4ZJtiNLT4Cm69x+6AaOcNF/pFsQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.10_1526011390439_0.1198814850690344"}, "_hasShrinkwrap": false, "publish_time": 1526011390517, "_cnpm_publish_time": 1526011390517, "_cnpmcore_publish_time": "2021-12-16T15:26:51.540Z"}, "3.0.0-beta.9": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.9", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.9", "dist": {"shasum": "d72c9eec7c3eeb70e7e20f54bec00942501e9138", "size": 3184, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.9.tgz", "integrity": "sha512-vYBRCaM8QPRXCeinU89ieJ98wQMJCwFwiVQTDRzO1sKJZFnV23ZlaCKS8vZdSX2ZUszdNi/HPOH0hrs16RW00w=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.9_1524882968149_0.7810317711927997"}, "_hasShrinkwrap": false, "publish_time": 1524882968235, "_cnpm_publish_time": 1524882968235, "_cnpmcore_publish_time": "2021-12-16T15:26:51.777Z"}, "3.0.0-beta.8": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.8", "dist": {"shasum": "46edda312605a627f4dcb02844b3b4e8dcaa1253", "size": 3185, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.8.tgz", "integrity": "sha512-d+ViC0ZZZRvm1CKoC76IgkTFLPMlxL1fCD84NWHDrGgjRltBTshwZVi9LAl7amY1PtEid5HMaHc1tQXT24GsSg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.8_1524872467473_0.5833242914722179"}, "_hasShrinkwrap": false, "publish_time": 1524872467569, "_cnpm_publish_time": 1524872467569, "_cnpmcore_publish_time": "2021-12-16T15:26:51.996Z"}, "3.0.0-beta.7": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.3", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.5.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.7", "dist": {"shasum": "55076477ab4118da808455cfb086c7212c885e41", "size": 3205, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.7.tgz", "integrity": "sha512-3RR9OwKZaA8pPNf0cW8qggEpd2tD15CG2W2J2culWCoku3MpzgLVaCmrNtqQwak6+C9SH+ej8mNY4gqTkNM//g=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.7_1524668340893_0.5907649727619877"}, "_hasShrinkwrap": false, "publish_time": 1524668340978, "_cnpm_publish_time": 1524668340978, "_cnpmcore_publish_time": "2021-12-16T15:26:52.183Z"}, "3.0.0-beta.6": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.2", "eslint": "^4.18.2", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.3.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.6", "dist": {"shasum": "92e2e3801a70199374edefdd1051aed6b4464824", "size": 3204, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.6.tgz", "integrity": "sha512-8kQDwWNFGeimJkbLkawoWFE5KNX1X359LtNHufrSZZf7+nUs/i5dk5YyUgxusjw5cyEpHNQNV7BornWTYPKvug=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.6_1520368247032_0.9870931176779862"}, "_hasShrinkwrap": false, "publish_time": 1520368247235, "_cnpm_publish_time": 1520368247235, "_cnpmcore_publish_time": "2021-12-16T15:26:52.418Z"}, "3.0.0-beta.5": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.2", "eslint": "^4.18.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.3.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.5", "dist": {"shasum": "298d210897120c00f58f261e616992246fcc417c", "size": 3082, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.5.tgz", "integrity": "sha512-ZkPWECTOAe1tQun5Pvj0HApOQwO2u0bYKCRBHWgQB6VQVN0NcfLdZuZd02xZTESHGw385u5yBQ4hgZQMb+VmIw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.5_1520290877307_0.390020656940562"}, "_hasShrinkwrap": false, "publish_time": 1520290877372, "_cnpm_publish_time": 1520290877372, "_cnpmcore_publish_time": "2021-12-16T15:26:52.632Z"}, "3.0.0-beta.4": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.2", "eslint": "^4.18.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.3.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.4", "dist": {"shasum": "2579644b2c33d32cc5fca6d24b419bc46c44afaa", "size": 3078, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.4.tgz", "integrity": "sha512-ojUmCKiHA5SxwK7n5K+NWICsqCeGQsPj0BuJee2Bl0YSGrP5kA9rNkVebxzfA4NQJ08C3oNr1n51TjsYsaf2gA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.4_1520278963475_0.7091436953974077"}, "_hasShrinkwrap": false, "publish_time": 1520278963617, "_cnpm_publish_time": 1520278963617, "_cnpmcore_publish_time": "2021-12-16T15:26:52.836Z"}, "3.0.0-beta.3": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.2", "eslint": "^4.18.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.3.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.3", "dist": {"shasum": "467e4cec1c08561f792ed05fc1424836fcab0636", "size": 3080, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.3.tgz", "integrity": "sha512-tv/RHuSk6sDkbYf8YxjJdU0gv8kgphlBjWYXqeyDbrr78k5ncssD9MwnAghIxsYajz1lR71ZbW/tALu+fgCEPw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.3_1520047715307_0.9333839520663223"}, "_hasShrinkwrap": false, "publish_time": 1520047715367, "_cnpm_publish_time": 1520047715367, "_cnpmcore_publish_time": "2021-12-16T15:26:53.059Z"}, "3.0.0-beta.2": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.2", "eslint": "^4.18.1", "eslint-loader": "^2.0.0", "eslint-plugin-vue": "^4.3.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.2", "dist": {"shasum": "fbc9ba2976aa3b70032eaeab83a3101753a337b5", "size": 3068, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.2.tgz", "integrity": "sha512-TvrIYBJXXHqN5pouy3LL4keGpI7hEWW0g/wBbdmJCmoiMesQY21EybhV+mrX2Yd3BnB56GMJpvVB0LIdT5tScA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.2_1519861496592_0.24644811657892718"}, "_hasShrinkwrap": false, "publish_time": 1519861496669, "_cnpm_publish_time": 1519861496669, "_cnpmcore_publish_time": "2021-12-16T15:26:53.314Z"}, "3.0.0-beta.1": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-beta.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-beta.1", "dist": {"shasum": "a6026901336d91aea83d4af658a6da509fb2ab52", "size": 3090, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-beta.1.tgz", "integrity": "sha512-JEDGTiHu41t6AV6pfGRYTmJ8uZu6ipqBpG4UY8d9h71MNZFEBLfmHxWL7MnvrMBFKW/o2zl/5BWC0GgIobxxaA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-beta.1_1518778215191_0.021894988471652166"}, "_hasShrinkwrap": false, "publish_time": 1518778215244, "_cnpm_publish_time": 1518778215244, "_cnpmcore_publish_time": "2021-12-16T15:26:53.531Z"}, "3.0.0-alpha.13": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.13", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.13", "dist": {"shasum": "7b50b35bf05738615f6582916d9fd50ffcf19a56", "size": 3049, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.13.tgz", "integrity": "sha512-BfmopWGyf1203W880o4uZXQctFkNWTCYIvyWaV8OGYD8uZUIjI+UhVmlYo3MdqMZT/51g4WmnV2hdmLL5rqNdg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-alpha.13_1518546462686_0.6549030112654308"}, "_hasShrinkwrap": false, "publish_time": 1518546463400, "_cnpm_publish_time": 1518546463400, "_cnpmcore_publish_time": "2021-12-16T15:26:53.803Z"}, "3.0.0-alpha.12": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.12", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.12", "dist": {"shasum": "8bf4bd4cc5244783e9f3f311beb152d2dd478c42", "size": 3046, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.12.tgz", "integrity": "sha512-R3aBMuSxi0Vpt3LiH8qwGRvFle7wWfhO1xXvfUXjxSXpRj+hWE0dh1f0bjRqcp60giwVhECT1+AgyqesXx70CQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-alpha.12_1518473495282_0.5908040443463636"}, "_hasShrinkwrap": false, "publish_time": 1518473496064, "_cnpm_publish_time": 1518473496064, "_cnpmcore_publish_time": "2021-12-16T15:26:54.067Z"}, "3.0.0-alpha.11": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.11", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.11", "dist": {"shasum": "1d7229a107454405d3186eac0270b5ad155ebc0f", "size": 3026, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.11.tgz", "integrity": "sha512-pkPxSRm0PDXshAuCltzh0zWINQoZD+wHKjbcrViHqe4CHicC97/dJ06XBP3V1/TZUnR4Zz6BbXiGgbPtaPuvDQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-alpha.11_1518200689113_0.7457244184670833"}, "_hasShrinkwrap": false, "publish_time": 1518200689835, "_cnpm_publish_time": 1518200689835, "_cnpmcore_publish_time": "2021-12-16T15:26:54.300Z"}, "3.0.0-alpha.10": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.10", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.10", "dist": {"shasum": "1dbd02f03025b4a9d96ba65361b56dd015a9799a", "size": 2935, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.10.tgz", "integrity": "sha512-ngHLwyUUOmSRm4rsaHRAbJWtvsCb1SMk0aEkNszG7LJ7Dx7TubL+lgiHJlDoDwWJF0i2y8C2igDXJs1Wg3sBvA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-alpha.10_1518129494938_0.15100478404579554"}, "_hasShrinkwrap": false, "publish_time": 1518129495595, "_cnpm_publish_time": 1518129495595, "_cnpmcore_publish_time": "2021-12-16T15:26:54.526Z"}, "3.0.0-alpha.9": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.9", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.9", "dist": {"shasum": "89afb972d0c742376a158a4f6e2167ae455d2066", "size": 2614, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.9.tgz", "integrity": "sha512-tneYzH0IlXziOM3gS0yY92ezDyMn4nCZWgjLeSKWFzV7t3YFC4P4n1nSP6I+mvFom5o3BT2P+WPKChX5RXjDzA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_3.0.0-alpha.9_1517957420060_0.7456199441976694"}, "_hasShrinkwrap": false, "publish_time": 1517957420773, "_cnpm_publish_time": 1517957420773, "_cnpmcore_publish_time": "2021-12-16T15:26:54.738Z"}, "3.0.0-alpha.8": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.8", "dist": {"shasum": "dd63f55c8a61512dccb2019e1b371be38b2b841a", "size": 2612, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.8.tgz", "integrity": "sha512-M2mTveTskMksuBO1N/UI4R8xlKWRRNJia3m3y4WyS+Kn2ZCAmsf76USmUDg6QkfjhUY/M58XACPljh4+uZVuLw=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.8.tgz_1517759783500_0.9710858319886029"}, "directories": {}, "publish_time": 1517759784344, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517759784344, "_cnpmcore_publish_time": "2021-12-16T15:26:54.952Z"}, "3.0.0-alpha.7": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.7", "dist": {"shasum": "159338bf255589e911f41106d835accb35ed2c93", "size": 2609, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.7.tgz", "integrity": "sha512-QOVJFe07kd08WqZ5nwxeJMzIWZGZTPNwK1J+VLNAewbz/Myasni00RuOuXw+nfhn7t8ze4eUWwUGaAsZAJa0JQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.7.tgz_1517611284976_0.5168217751197517"}, "directories": {}, "publish_time": 1517611285127, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517611285127, "_cnpmcore_publish_time": "2021-12-16T15:26:55.144Z"}, "3.0.0-alpha.6": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.2"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.6", "dist": {"shasum": "f444c20a47d3f0ead1b1245be810b17d670655bf", "size": 2228, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.6.tgz", "integrity": "sha512-+vUo7vnhJpIMcpBp6xhYaSeTB7EmHNTKLi37sfu7X4PLdZyfFUeVUjhVoKVCXj6gbcL8JPtxVXCRIqAZVOmilQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.6.tgz_1517561508801_0.6966057096142322"}, "directories": {}, "publish_time": 1517561509924, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517561509924, "_cnpmcore_publish_time": "2021-12-16T15:26:55.365Z"}, "3.0.0-alpha.5": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.5", "dist": {"shasum": "18d39da2b2b5146bb284b4a0d2d1f6bc336d58ff", "size": 2200, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.5.tgz", "integrity": "sha512-C0BBkOymZCkztaGY3cRvnntY+SylpsOUowae/dBkHQFBUFM6gqBrn/EdmuSw2cPaWx0Nl+vwDMYU7WkzJ5fp/Q=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.5.tgz_1517246971639_0.09941492741927505"}, "directories": {}, "publish_time": 1517246975046, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517246975046, "_cnpmcore_publish_time": "2021-12-16T15:26:55.583Z"}, "3.0.0-alpha.4": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0", "eslint-plugin-vue": "^4.2.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.4", "dist": {"shasum": "4fe4d4d7163c1f55e87231973e0c2b93499b3999", "size": 2203, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.4.tgz", "integrity": "sha512-8EJGZ5jfSGzSWhYPWwxB7C/DFvxCPM9NQhYHq3lQ2WhwdWh3Oge1XaUwUeBMw4Fm0Ogjcmvg/1PYwBuZ0gw2pA=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.4.tgz_1517009645725_0.9081394074019045"}, "directories": {}, "publish_time": 1517009646655, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517009646655, "_cnpmcore_publish_time": "2021-12-16T15:26:55.818Z"}, "3.0.0-alpha.3": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0"}, "devDependencies": {"eslint-plugin-vue": "^4.0.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.3", "dist": {"shasum": "bb4bf99e42a07b9e4900bc6a5c9fae803c2d1e53", "size": 2244, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.3.tgz", "integrity": "sha512-/nT/gzhOztfd9qqubSUcE7lZ1kr0ftvZdDmXLnWqO3lHZFuWST8gnS379Ae8sXusv/KCtXDgXR+5Vboku39BsQ=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.3.tgz_1516938322990_0.4963601597119123"}, "directories": {}, "publish_time": 1516938324260, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516938324260, "_cnpmcore_publish_time": "2021-12-16T15:26:56.027Z"}, "3.0.0-alpha.2": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0"}, "devDependencies": {"eslint-plugin-vue": "^4.0.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.2", "dist": {"shasum": "4b06052cb6b6cf52194df88465756b9fe9cb59e3", "size": 2251, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.2.tgz", "integrity": "sha512-SX09Nv2o4luFDRf4tv9yWxQrprJI9zU1NTRWY8zLom/gwdEJaTM3W5Hx35+4aDEQCYUXgMQ12FqTvY+MxcGJTg=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.2.tgz_1516901046137_0.6562224018853158"}, "directories": {}, "publish_time": 1516901047206, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516901047206, "_cnpmcore_publish_time": "2021-12-16T15:26:56.254Z"}, "3.0.0-alpha.1": {"name": "@vue/cli-plugin-eslint", "version": "3.0.0-alpha.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"babel-eslint": "^8.2.1", "eslint": "^4.16.0", "eslint-loader": "^1.9.0"}, "devDependencies": {"eslint-plugin-vue": "^4.0.0"}, "_id": "@vue/cli-plugin-eslint@3.0.0-alpha.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "dist": {"shasum": "3ac44b14fab1586219b817cd0030a49a6af91c62", "size": 2215, "noattachment": false, "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.0.0-alpha.1.tgz", "integrity": "sha512-kTM6ihLy7cZdUB/wYnFPLub1mVeIoPvT14WRJQZSNFywT0YxxlFdFy4Kcxq51Zwa10QB/6+cM+oLrUa2eugv5A=="}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint-3.0.0-alpha.1.tgz_1516898184259_0.6293282895348966"}, "directories": {}, "publish_time": 1516898185168, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516898185168, "_cnpmcore_publish_time": "2021-12-16T15:26:56.435Z"}, "5.0.0-rc.2": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-rc.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-rc.2", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "95577c0a68b3b90201699330559e028f4cb182ca", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-rc.2", "_nodeVersion": "16.13.0", "_npmVersion": "lerna/4.0.0/node@v16.13.0+arm64 (darwin)", "dist": {"integrity": "sha512-kRP6/618AkV6Y12ht7YIwvWtW9ygku6s/5uySoBCYEegWce82NPxk2A2TPFmqYRsOG50A3XojK+v0QMdMc+VSQ==", "shasum": "22a751276041ce6c3b248d893eb12496ee2e48fa", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-rc.2.tgz", "fileCount": 16, "unpackedSize": 32786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4uGlCRA9TVsSAnZWagAAXJ8P/ihV2s7GdYlrvQkB92JY\noFj1D6oapYpWU8aqd7baIh4OJW3z1o9BB/yJxlk3clYl/T61tk3KWQRJHhJa\nJgUb2BnC4anv276s66yzElQ+vxTYllR5doPBfgXbmB8SsNT896NLl4P/yrc4\niqiAdHjLyNsXGyF6WaFo5lAVubZzcOx8qXyRYvHB1tcdBN7+tw8CqlV6Hn93\nA2R3kSs51eiUIWxScHqPUB3D/bmSIdfdKvkzd+H+svMw/bY8/Sbrbtv46+G3\n2GSV6URIJt8fjBsutcBOrOwz1qfH/yca6uWNTtN566VtGn/XweIW6aArEXWG\nwaxK5y6iBYuwIj7GmAdy5ZpfBdBcgujHPCoEv8o1QL2QkCLAwwkkY4U7ki6Q\nQBtNefH5Vs9oPisIld7Fv6QaV9EPNAguwRmN/eTZg9LiD1pMK+s4sIdPEdmZ\nW920S941mzlIO/3UBYGS4SZmX3vT7qRDfAaiYOGcwU6eGNCSYItsi6v6Y9Z6\nm9/IoQS4cNLH/+lXkt81EjK7xHGOrTto2eg/4/CBXHgj1XiqMQgfHv9c/gaC\nBQT+SXrDDW2JIEi2VdQLNZBaI1h4dC+1AzI/M1cQ1uHcTf9PyYKqw4KIYZk9\nTaUuSPytmR9aS3EzCqCq/7q2n4w/9vzlC41P5b/jFBW5Oi8KcD+BJLmtK0zd\nZXjm\r\n=Oz5W\r\n-----END PGP SIGNATURE-----\r\n", "size": 11713}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-rc.2_1642258853563_0.7495210866841369"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-15T15:01:01.352Z"}, "5.0.0-rc.3": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0-rc.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0-rc.3", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "75a6d690eee4c7b3e46528d6b1d7b0b7a6d2cf3d", "readmeFilename": "README.md", "_id": "@vue/cli-plugin-eslint@5.0.0-rc.3", "_nodeVersion": "16.13.0", "_npmVersion": "lerna/4.0.0/node@v16.13.0+arm64 (darwin)", "dist": {"integrity": "sha512-DNvHWMt9ruHPhzVLO4jP2Z1X6utQjNBmxak9Ko4QI0CNA9cAly4DezXMYgBt5/x/SH7LvIBxk0wYu/KqmOB9VA==", "shasum": "04ac5338f3e13e3bc69d2cde93d131aa83261607", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0-rc.3.tgz", "fileCount": 16, "unpackedSize": 32786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBMYeCRA9TVsSAnZWagAAGfsP/0bNS1E7x8hQwAFezoL9\n+JMWg+PPtzW5ht1D0NSTobN8XVTJSr9AvxFoifXdzNttSo+nH5PTSymL6tXQ\nYz9UJpbhq6VWKlIFIgpyt3W+XC77BwJm1xMMBaQQY9fYxbUYv65E9FRD/tFD\ngxLslGgRyolXC5DD8MJpzW+OmLzKPLPPHUDrTK0ziasM2+MMnUVgx822FLpd\nLkr93CykcuB5+Nbz+Dwzh1QHaAgj/gRCMauwghF/q7otE65K1ZITftBLoQJh\nD0rEYd5jZ4dYG4ckNOm0ZLbvlZOo/Tn+b2D167I+aLr06sDEsIaTSSV0Wk+g\nfNOMhIr1Jjremqh1qzPHH47UO7xjLyun+DrRi35s0ok5fq2nTWgnQ/z3PGF1\nR21QaQVcoqJi5qASA1MNBTU+U3e1Ww311Vm/8cQI3zot2LYPWgWIQ2loHOb1\nLzy8/mgL2MWXuZPT1EwpoTS+yK8MM75RX8aeEzySALr9AK9O5Hp5gaacyWcD\neGkTkK2S3rXbdcvN09gf8NgttnfbNq2MBoQYHyjDSGQLT6pXgviRsl+yS6LS\nOQ/vBtszh5RNuFujqDADjrPY8hJV/RL5amhx3u3psGZgU6GUOE+7ZsNNg5V9\nSkj1EoLQl0/gvcNg3ui1T3XrtP+seRegnOp0WfDdD+TL0EgvB+aJsQOHZanQ\ntFrG\r\n=T1eg\r\n-----END PGP SIGNATURE-----\r\n", "size": 11712}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0-rc.3_1644480029978_0.10558946817014947"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-10T08:05:28.861Z"}, "5.0.0": {"name": "@vue/cli-plugin-eslint", "version": "5.0.0", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.0", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "c913cdcb67f4a8e7c1f8affd1a6ba9a93c0f3ebd", "_id": "@vue/cli-plugin-eslint@5.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "lerna/4.0.0/node@v16.13.0+arm64 (darwin)", "dist": {"integrity": "sha512-TqOjfWWUNI/pHhFRBln7QnbqVx4dI64ERQBI+Ap99v+GXZTJMjkdfSeX3UJDT6bvDIWp4n8+Qdy7lKDKiM3HcA==", "shasum": "b26e30d13cb82889c8e07e5fe3e8bcd8fe492a4a", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.0.tgz", "fileCount": 16, "unpackedSize": 32776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDi33ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpARA//dyR2Ou2sRevqZJPz+MbALOZOzSLzG2smRsI04/ZROdrJ+bzZ\r\n2njieQpzeLPWFbJbZ5G3LYPLl9mG85AUrKH6ErQ4JgtE/QgVy0JTg9PAkxzJ\r\nIuk4DIKJyf40kxR/3657n5oC0DbYd4h8GDSidehY2buIvQEKsbJS+lcRVJxF\r\nXdI9yabd5MzJBQQApMIS/0SqjpgQJ8cun75Q065FXxCHuL2ZpFFPa9VdDnW/\r\nKYm8+b1BxiP0JQy56QY5RtyIWiilDJ0clFmXu9qNaXVvpiBcr2mv748PvAx3\r\nbsZmH/4rVUTweICFtZJZMDfz/aFfxKAQEKRbyzWCJE/KyZR7kzlVE20mHjDC\r\ndnx03ZLshHiZdBTn+WZh57ltxnw6QPhY63WrNX0H3BA3DyBlDCm2a3estn7C\r\n2grMGAaEoqCJ/2ADlqTSAT663mTvSTB+d2ZHHO6zha250sSngx7JuVQieqLF\r\nLDJ6xiMavmKg9oSmOFui8Ptez72lL1a1DcMq0LNLGkPcZmzsfLYq4nOldIyr\r\n1eoucp7afTbbqgaEmLE1ioLs76O/aGCwZABKrSpXgoX2ftkzs9X8NzVwx+mQ\r\nEKv3w4JM8RgwkcPaErfA3e6zEXzDgY+C3I5/TzCOLQh4FA6FYAfE+tBw6QdM\r\nFr/iGxtDlpvHd/5Tx6cS+vTg7q0MP7BcfY4=\r\n=T9G4\r\n-----END PGP SIGNATURE-----\r\n", "size": 11708}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.0_1645096439781_0.7212207946558311"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-17T11:14:09.498Z"}, "5.0.1": {"name": "@vue/cli-plugin-eslint", "version": "5.0.1", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.1", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "92d80a89122cea830a4e7e32946af64b4c3b62f7", "_id": "@vue/cli-plugin-eslint@5.0.1", "_nodeVersion": "16.13.0", "_npmVersion": "lerna/4.0.0/node@v16.13.0+arm64 (darwin)", "dist": {"integrity": "sha512-ZGxIAT4b9ppMbePJrsZA6e5882n7JIQgji8LLyYSMuf0+GTsEjUDz01nRmAD7PW+Gpf8+B5oA2tU+S3E5l6/WA==", "shasum": "5944eafb84bb5076d6280432edb7d674d0f86962", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.1.tgz", "fileCount": 16, "unpackedSize": 32776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDi65ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouYw//TP6If6RznnzaSjmLe1k5kjY8hO+LHqbclGnBbj2JeElaf9zt\r\nA+ftXI7qw3IwKcgKq94ZM/QpmjWf+k8VNu7F7B2rUslXGMJjTGDb5t6d9ssk\r\nvG9OvbgbvgZZ/3Q+bFJv40MvCGurDSB77JqI2tMefiWs+SWTPfs3TXsR+VrI\r\nwjm3Y4HYZM+i/i308miEYPZvENWmlOyR4fnysAWKKHNr+DBSKZPQUuHI6F+x\r\n+fUvuD4t9YD/ryul4DMxlYR/HtSHhtKUBnCakxTGOa97gOm3qanfbo7RjusX\r\nCwR93JwfFRCe8fHmUGK/BRZWnqJksIJVG04nj71QrkkRaOdLr4IJL1z6mW2j\r\n4BCWDLj1ulh3RTCJXeopw0VFb3JQM+e4iDQ9r9iJAFaglEnSraB8CKo5VaAp\r\nP1XRlCX5t9ItGVTaNQvR2aWEDvNky8uF+k2Ds0kEeDGgShACI/21n0HarQhZ\r\nMKMS5ZEUL1QMbNp931Bl/erah8KmKTzEG/tvoB7ypZ6JfDbRJ5JTpV1f0spg\r\nPHkeulqc5Xg8LT4zzbhBT/tm179UQlmYjwZOB3uM6oDchYbc6+wkLW2maJiM\r\niBgBqEeNsroB1/R/azMjTlrWrPHdAvI+FH69FgrjIJXLmPgkMlB6ugVBbJhw\r\n29ThZ8AjrmmcxOWYbGJG1fFwihS/hI624kI=\r\n=boJU\r\n-----END PGP SIGNATURE-----\r\n", "size": 11713}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.1_1645096633635_0.1345420340638468"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-17T11:18:01.601Z"}, "5.0.2": {"name": "@vue/cli-plugin-eslint", "version": "5.0.2", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.2", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "a859b1fdf26bca3fcd44b535965926da333d11f8", "_id": "@vue/cli-plugin-eslint@5.0.2", "_nodeVersion": "16.14.0", "_npmVersion": "lerna/4.0.0/node@v16.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-65Ddx3dBhlSjct5c2f/Es85P6bDVvTkjtKZlTE4w47fOMopr1hVF8A85/NwuYTpSZNGAGS11ufuf6XfkUcnO9Q==", "shasum": "278d46885d7bec1ea5695968b2b8ffe47581da63", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.2.tgz", "fileCount": 16, "unpackedSize": 32776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMI97ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwIA//ZW44TV+u56OvXnXOORCP0/76/I9uAVNxV0eRl5NQzDhgKG39\r\nv4eqd34Usbkv1891R6HduYnheitw5rCuD4JE1MqBi6OPvdo5FgwcaTLKv6BQ\r\nJ8zagmlQQq0Cm9vxKCKeloMsmHiCNCF/xstj2r5eD04d5hr3uZmO4Q1WRMDi\r\n9RnR9Jc1ixhMpPsP/XK9vapyUEyXYYiQroCC7ut/W8bMuXIXajKo2Keo1/H4\r\nm3E2wIZBRNCB7IqsFJcvjFxB/2p5z8ztYT8wqwfMG2ulJwJ2nB195K/yR+ky\r\nodymmyjR3/obLzAo2q6YVdn8OUZlC093aQpaytOkROX00oNiOwqKNLDC0VrN\r\neFGxDQy5HttxfKxWTHzcND46fnvZOCUlSMHt1tA9JyJIiG4it2APw2Bgeriw\r\neBfK7lcAIsBNdxcije5fzkNvZtEbUBmjqIBFwwZ2dMS/cRJxEIlPv5MSgw3E\r\nwP5Qnk/WSZjOjo8s67SG6t6u7gZhc1ebEOFDr7Ks0xYF789P4eJRFJb6jsPm\r\nHOs6Ug/ZqOF7PD+vQHEi8vhbfzEPvIjurOfBtOtlBcAlc7j4EHjy0cfM8oWB\r\nGoSByk1U/aAGeaQ9twkQ66andf1GUjgYsZqwhEdMDbomvXGg+7sQ+XKSotJs\r\nAxK2Mt54bqq7egpx8m/R/WzzqlqFUrVxMDg=\r\n=M1+F\r\n-----END PGP SIGNATURE-----\r\n", "size": 11711}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.2_1647349627784_0.2870850550074402"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-15T13:07:20.452Z"}, "5.0.3": {"name": "@vue/cli-plugin-eslint", "version": "5.0.3", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.3", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "dd53f26bc0c51fec7c5fb2c18f4769de984ad79c", "_id": "@vue/cli-plugin-eslint@5.0.3", "_nodeVersion": "16.14.0", "_npmVersion": "lerna/4.0.0/node@v16.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-gp2UdXYp3ofXndD2vKWQ56e6GXwCkrkS5yBzfM6T9aKFS9+3TV5FipQGQf4O2V3rhlCjWulUV4i3uaQMRems6g==", "shasum": "47f00fea50205513f1908fbb1f93420760f6091c", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.3.tgz", "fileCount": 16, "unpackedSize": 32776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMJECACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpg7w/9E3uHIsaXbwe9mCJ247DO5QZ+WIWin8VqWwCtSMqSUKn5nXJt\r\ne4vNWCxBr/0VWT2tEif6PXmpmTseKz/u06ytEHdoLS2+LTTY6uYJQdmny+K9\r\nfFT8hiM+xdxStd4kHMxSZ2CGwLLf3+Gltxut158z0+b74T7EiAHpH3Cge0ir\r\nVyUCpd2hnDu5vG4xgcn+NiYfus+JLig01RBx0ftPdrFzTaTGoZ9BY1Ss63dX\r\nQY2ti1Bgvgf7SSi4hs7W79ZA2U4y/yD6RZsIihLHv3R6jEcL3FFXyyaQpMy2\r\nuVkNM8LFoeSMNhwMXoVwRSCGLJduBSg6F/0ww7L4m6iSs2XjVBRrKn1VDpoL\r\nuR1c562QxxqhL/tem2lc1Boq/IO42mQfa0BDWd23baDZ7KsLVpeYITHkERAz\r\nbPiYj3QWZKfGoIYzDIudMtKaa7+PAiMoPBKy7Lil2yXSeeeK23DSIrSWupRK\r\nOgrzAcsfK5Cob8exfIxvb+bC3ux4LVOeXuwFxR3frhWsh50gFHSpXBglj5UG\r\n484Z3hB/MA0NL07+1pXCs7UEiXmxVi7DJwanNFlgu6107fmqEuOqNQCH5Y4A\r\n9X9EXLych7ynjJYmX0kuIR5llmCFw/sIR+b6/8DHRmdeiBRcbegx9ca48Em2\r\nfxNVh7cW2XbF/H0JivCd7qeNXT1vgX8D4Jo=\r\n=SQj6\r\n-----END PGP SIGNATURE-----\r\n", "size": 11712}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.3_1647350018159_0.5009914500846788"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-15T13:15:38.722Z"}, "4.5.16": {"name": "@vue/cli-plugin-eslint", "version": "4.5.16", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.16", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "c38e7551a182174d8154b4e4c2ef54404052812f", "_id": "@vue/cli-plugin-eslint@4.5.16", "_nodeVersion": "16.14.0", "_npmVersion": "lerna/3.22.1/node@v16.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-vaf6IKeeBYKgLMfM5zaNonC0ArBZnlhv4rw+uVdCM+INRX/BDvdTN9DoczGcCn4sAWG2dBOtstegr2OJAXT1+w==", "shasum": "aa7cdef1ebc1726fd505055dada50c8dba0c0497", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.16.tgz", "fileCount": 16, "unpackedSize": 29688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMJH7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBrA/+Ke<PERSON>+/VPKqyAMN2Lip1Wz1j6HTMUKND820g1ASbhvv0EnlVu7\r\nFLUac4yDevHLk2TmMVKJDcza1vmjhegecs+f61W5nXJpNHMpJFI29fxYlNTh\r\n1vygsI/S6yXEKgBJEAKGOPKDzuDyyoQmSnVnERBevq9I/zGi0R+qA7X7xgPe\r\nlDK1+zkR1RHyNX40GEZZPQl/cKCAWNia6UGeVqIS8QMtbfLOijODjAo9z0HG\r\nZrXPfH60iQwJxoHjL7durwKdFFa2/rUvYeDjPTrtcC3OEwYmCUcDFeekXLRL\r\nvWOj05SYR9TnDqGi9hbnurjeCla/+Ed/crFjdLncH0sUw2JiSf7/hLxGdpx/\r\nSgOoyhF2cbw3dFvz9D9K/iF3lkdlmtwHKdsq1GFv1tccaH5V8LALxuOjLLC/\r\nPxbFkZWlPdGHMZpBavk1mjNPtF4NR/Wkw8k4WNb6eobPikPBr+Qz8JZE13G9\r\nbvumZIxgxY3ckS6t0HeUvv1hnAGMTfcgs3lP5FEagKSYrY0n6N27KisKRPmv\r\nUqdIyalWaWTbggUrV2ymgYMSIqH/AlPagf1SxbVh1vHlDdEGtDHc5JZbA1nR\r\nkcK18fA4zA8I0wjbwtjO9MKCxUhm6PILFf2c8Aneads5N0iK90+DoA5mtnSS\r\njp//60L1yi26veUxoaUtwx2NM0toHIDywcY=\r\n=EX5P\r\n-----END PGP SIGNATURE-----\r\n", "size": 10808}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.16_1647350266939_0.38564340097396155"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-15T13:18:28.420Z"}, "5.0.4": {"name": "@vue/cli-plugin-eslint", "version": "5.0.4", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.4", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "ca97fc2920a3fc9b0288d5fabef1a97356b8da23", "_id": "@vue/cli-plugin-eslint@5.0.4", "_nodeVersion": "16.14.0", "_npmVersion": "lerna/4.0.0/node@v16.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-NLBEKFTFJhYHMzQ3z+sb6xlkcNaN/RcbZ3hiCY72aJe4YOO8jEAp0XkPzlHd4xYkMW7jrmOwwLOPw+3BJ4b77Q==", "shasum": "33ee3e971d8ee1664d55ffd15b2af548af66fef8", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.4.tgz", "fileCount": 16, "unpackedSize": 32776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOdwMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos0hAAk1vm20Vdm7d/FJxBgMcuzec4cKJffNBrLoxc1nKA6X6HMinA\r\nMEYJGPU5iPhr/TPTXFrSIN/FcDGBWLdzlr46QHshZIqvb0tYL1L8QOS8DH0c\r\nfrdAD8aWfC1NovwkurNf1YoKOHYb6OPaBoXhT29+s4XlFy3KB9jWDsclbQ2j\r\nZAyMWBwMho0QysyHzEqjQFBK3YXm2zyhch3qQOQN+VllnLnWG5nUW6BvG6Sr\r\n5G0K3iksbJgJmDYvBli7ZL7+jJ8XxNNJCVW2mn/3/99jj1jBbVgIO4anzja8\r\n3aDmOwn/eyR+MANWJA4BBtDJf6ktt9LSUa0WBZqHKT8GIBCK9EzuPYPUoAzJ\r\nNDPCWVa0raL0Xxk8YKpdnceD9JfEnKjFF/pPlqopXPwBc6gm+mOCmKt36s1E\r\n54rxGJkSQixb99V1EFTRnLp7tX56vPFgYajlFwcBI+zVrcCFc5iY7ZuuIU6W\r\nePPNFsFF+W98l0V4qNdGjO798rbRYjAHqr8Vu8zoEHgN3D3C/+IqTu3bw1BM\r\nPsMiyQ+HCltsSCMy0VVJEoPFc2AtF5KoApBOWhcexiiijgEzZ4+xmUfwoyRu\r\niPjPa1t49XvBW8ZPoXjbFrLKjXHH+KCYWJcDWywb25UW40Y9iR49xKwlWXHd\r\nfnUEiEGv1qd+xPbvU4mXtFBVBtK+Hu4b8fg=\r\n=b8+5\r\n-----END PGP SIGNATURE-----\r\n", "size": 11710}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.4_1647959052020_0.5674976613523086"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-22T15:31:37.213Z"}, "4.5.17": {"name": "@vue/cli-plugin-eslint", "version": "4.5.17", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.17", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "58ff39cef3ec3574018089b3ea5bba5bb0abc10f", "_id": "@vue/cli-plugin-eslint@4.5.17", "_nodeVersion": "16.14.0", "_npmVersion": "lerna/3.22.1/node@v16.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-bVNDP+SuWcuJrBMc+JLaKvlxx25XKIlZBa+zzFnxhHZlwPZ7CeBD3e2wnsygJyPoKgDZcZwDgmEz1BZzMEjsNw==", "shasum": "7667bf87bdfdb39faeb3baed58657622354a17bc", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.17.tgz", "fileCount": 16, "unpackedSize": 29688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOrZFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolpBAAjbuahlmcufekw7fIGHGyEZhOCIfyVa95eexn+gD5kxmakdCw\r\nmgmbzXdzkhINBfb1uV7dX6Gn2M2JRil2XK3yAZjUii0qjkm8c4iKKeVXCrbr\r\nnm74sBIb+YtF0p971WxAEgTEIjR5LYjHbXQTNbYsSu8aYLcyNsESy4+njCsZ\r\nxtn7TIhX1yF6e4CHyX/LJx8fcck7lr0iejf6YzsP0HPEi/6iol51Y8RZE4XA\r\nTfEEckNeGU37ai/WjxkUTuMlw2fgQDr/8U/r1HHFGw0l9apCGF3Iz6HbZfoP\r\no0+No7onVfSCOiCR4oGGay6dHd+hOUSsWX2yufHo/GiCfoBJ/yAR2JJTYqLK\r\nnqrDjW8+uaf0wWlczSZcozN1SxiDJ5VKocJiSOfvK+Ge+ynUIxIO60i6gF6U\r\nSy4E0fqsOhY1GRznQoTsf+s+r9r+6AKvdPi+QKf192Cwdmf+qC8y7XInuXuv\r\nNjPftzeIaEylorTAoWbo+YMJGcnyTpq2WBVJiXFsxuQObuMwXJdyrzKYDYiJ\r\n5f59rXjifhqtEbaaiTeb6+rhcmPPtU3cVjuoHN08K2F1G0Rl12CYnD5YDN/0\r\nKzp9W7GGwl1BJorRHtEV5yChuecsL+gaVZx26rV7VcOBp3Bd1t2FtugOTr4M\r\nj5E5nTr5O4mYcohXiQJNFz6RHg/d1c3T478=\r\n=vB6h\r\n-----END PGP SIGNATURE-----\r\n", "size": 10804}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.17_1648014917131_0.6272218873987079"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-23T05:55:49.431Z"}, "5.0.5": {"name": "@vue/cli-plugin-eslint", "version": "5.0.5", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.5", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "98c66c93ae45d3347f62c56838caab86561ad4f7", "_id": "@vue/cli-plugin-eslint@5.0.5", "_nodeVersion": "16.14.0", "_npmVersion": "lerna/4.0.0/node@v16.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-eEe4HY6VQkeaUDsgKitqdxanlSfzLkxEroMjLQrQB8pPtOpw9w4V9d9DqWJJhQnbw4LxQHnYvcu4eluDS3X0dA==", "shasum": "2a2fe7514171c9527a381a2bd21d850f6d266360", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.5.tgz", "fileCount": 16, "unpackedSize": 32776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDZFA0gj4o5IMRGssgvWb9LOICSWcu1TIiP7+gM2O2gQIhAI7nPsqVgG+MdHtxiFpRAhTMhDZwDMy6b4KUbbmPagDR"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqsh7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplOA//cgPmI0ATGxBcTy+kahFTNakoMfn/7DJp+v+bINiPVfSn1dTu\r\ntv+FATqIKTdpLk+Isj9QOuMp/+HwYnjkXC9wDxF8YnWfn4Y9x/RC+Jv1I5Yt\r\nAQ2dwG5fSXosNyIjS8jgp62BDUqpM2t3yAs3BcT9f8kKCVTG5Zu5BBYXl2aE\r\n31x9TzUgqhrC+1gblkxHfsTVD9xZOZG5bgBckmugaBD2804npSikZ3a2WdqD\r\nvnF03Y2NBamI+ANMtKjCsil9DqrzbBLVPCsH2SGVyObJbgaI4VeZs8NbBllE\r\nfJGW+Z1o0EDg8hYXGm555HAhZnogNrRBGD9VKS+Z+GR5T2UXdCFEBEKkvBTj\r\nA704h1ztrJe0/HJPaSTKsOKplaygNxiyRkPuf3M/qTs5jHtVpKAs28PAUD+X\r\n6I3Df9FJsoeus7FeHBnHQtW8uBQaj75IZmAe/bi6+G0/mT7WCaszOIWFksw1\r\nbS2VCkY9I6lMpRzbp1f2tuYEOPYwfhxFlx5nlwmodepq07GxL/7V+0C/4zFu\r\nGVPHJAmF11qg9bszPSlIyYtK8s9UfoL9n1ST5JwP7RfIIuNm2fcS3sVYuvdy\r\nyBP8iRTcKbux1fE2P0DN48lW1lCgs6R5NrpZiSc3Bj0XJ5ne8fMnCH2JUUNl\r\n1PTCnABQ5hnwTG5bY/S6s90z40hd+FGCr7I=\r\n=0Iob\r\n-----END PGP SIGNATURE-----\r\n", "size": 11714}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.5_1655359611695_0.8338199913937232"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-16T06:08:04.483Z"}, "4.5.18": {"name": "@vue/cli-plugin-eslint", "version": "4.5.18", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.18", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "9245eb2f5f93418b41c2368a9f7bd18072f9e9ba", "_id": "@vue/cli-plugin-eslint@4.5.18", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/3.22.1/node@v16.15.0+arm64 (darwin)", "dist": {"integrity": "sha512-UWacV26Ul+W61PwvMBv4Msh2h3Umr5FG0ApXFzLQTgyc/rCmXvhR53pW252zVZ2tf7s2n7xDFnp/mrCUrkb2tw==", "shasum": "5cb4a25eba6a51cff8d8fd46345d424a7a05bc57", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.18.tgz", "fileCount": 16, "unpackedSize": 29688, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGYBcQhVxtHUXRIfIYrmrWLnVDcZOTH46Xuw9/D6XcuwIhAJYa5s5duNUWyGTHcN+J2ZOKb5YKrNQ61EVv7CCY24ia"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0C3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8UA/9GHkOVDkxFvBXDm8xm+1+H7s74KZer22EY2elhvISkUezy73f\r\nvsFgAv4jHwUz2rcfUPCl3a97RamKD9lvgFfFVLp6M4dKqFavvE+d582cFXgf\r\nLjaRVmLmag3kF/0OOVvAFxEclVybDuFvuDIHu1iZ5MJJkqRyiGG+csNuG0xs\r\nPWAH2kR1HXAymI3GFwV5uAzJdDI5XzPBSPjgMowqHeTzq/apxpwX9YurSsVE\r\ns5K/kam9yGO7fDMb5STvXNy5WJ4sebywOgSipYBfUa1gRfDdHSVYpg5A2P11\r\nHwuK/aphg3frHwXMoROPArnClxHuz+zTKXnjwyHKbQbHmDhRacmCDgmZSGig\r\nQaC0cpWrlm6KH5d6Fe3avEp/gjCTtk1R4Fnneu2dQYduW95ajJAzhN0YbMyl\r\nyIu2vUYL5TeP3+pZtClvZqydWN+0O1sV++AdK7I5xnjz87ykMHTuAbfOOqMe\r\nLAW7E2VLWNqA4UzDPwIJdgPSufZ307eSs5VXnZ92+3uF0LJEd3NBP/MWxVCh\r\n8VuNC+MiplU0nOSdiYTxmDyNuff6oqvPW6gTXf8mNt6wMxxAAbAZIGTMg6m9\r\nAeZRJVSQUXY433NT9i7tzUxSZm2UcTOGkl6mNIr4FVSOx3+15vn1Q3uoJorS\r\noqBW9ihKlwltt4Hjj9BriOLDmhrid9/bp9E=\r\n=y0hm\r\n-----END PGP SIGNATURE-----\r\n", "size": 10808}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.18_1655390391688_0.6366461306302282"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-16T15:25:59.990Z"}, "5.0.6": {"name": "@vue/cli-plugin-eslint", "version": "5.0.6", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.6", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "ef08a08c41b028a2484f262414a8c91d151febc7", "_id": "@vue/cli-plugin-eslint@5.0.6", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/4.0.0/node@v16.15.0+arm64 (darwin)", "dist": {"integrity": "sha512-fuc8w2IujzoenA/cdr/QYYMq2yaI1fYr0Fqom/M55B+DmW19GIuIRxYQvh0CEeCbFB2hAl3Flrazfx96/nAaQA==", "shasum": "63688b6366feb0e4949a23453dbd4984996c6092", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.6.tgz", "fileCount": 16, "unpackedSize": 32776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIECuB8AON48AlqmjVPdfJRpOul0lbBO6AWjT2ppwtAMWAiEA3JDixk6fCO96HwnTEh6l4a/ebflURMbk33C0xI3u/9k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0EmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprEw//bEl1BejECW7zD4s07V1EXBkoRsvO3+1mqtc2KsWWp9wNTPZo\r\naZ2g8bIZbMmEzbKHzcsneK8vN8hI6SmiFbx+JW1gFnVtwC+tYjp0v8NpOtiX\r\nT2ghq0Ab6h9oPCijH9kPVtmek//aYraZMKHRvYKaaNWF0U/mtaWaQnIS+YBA\r\nVogNcSTbuCJpwPU5lsHxegpS3CE1l34VA0AWJvZItVcDcf12E4kIIIyfSM84\r\nQYZ9BX5v5wmgh44ZrKiGHhRfhYDfYOgc7Tnsh5PSCgsC1vCPBmUxHA8ZE5yT\r\nI8mTLwXypmNOXHSa/MHWSZJ1T6KTpLO6FFE9t3LRB9wm8xDCOMq9Cf7M+EWm\r\nemWkTPgeNR6S4L3JJuPMh3LoyujGeKlbJbrD5RQQ7y2zqcusdZ3qqTmxpUSq\r\n4ap/9KFJKoPeSIgsPOqqCV0Kmi6p6PWIPmYPjairXYsUATctp/agmB4YMNlO\r\n/N4/5356yZlwjZVyXIC/hWZnrPVDjT+f4AJPlecpo/rUHQ06lj5scymhnesJ\r\nKuj0RR4JCWcsE/5KyIeAYd7d2Fr2tdps3C6LQndHoMIUIBok5RW+CEXd5fHi\r\nKBDLijtaGJ/F/i386HeuReKLpgWA1tnGKYNfDNemHakeFHENpEm4Q7nSE5je\r\nkGgpHh44w6gpcoyUg214h6VIriQVWT7gka0=\r\n=6+cH\r\n-----END PGP SIGNATURE-----\r\n", "size": 11713}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.6_1655390502250_0.7251970951559314"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-16T15:26:00.918Z"}, "4.5.19": {"name": "@vue/cli-plugin-eslint", "version": "4.5.19", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.19", "eslint-loader": "^2.2.1", "globby": "^9.2.0", "inquirer": "^7.1.0", "webpack": "^4.0.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0", "eslint": ">= 1.6.0 < 7.0.0"}, "gitHead": "bef7a67566585876d56fa0e41b364675467bba8f", "_id": "@vue/cli-plugin-eslint@4.5.19", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/3.22.1/node@v16.15.0+arm64 (darwin)", "dist": {"integrity": "sha512-53sa4Pu9j5KajesFlj494CcO8vVo3e3nnZ1CCKjGGnrF90id1rUeepcFfz5XjwfEtbJZp2x/NoX/EZE6zCzSFQ==", "shasum": "d1f908b5d079f2902dc23301290e4dd8176f204c", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.19.tgz", "fileCount": 16, "unpackedSize": 29688, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC8A1U1Uygx9TJxkLwjkA1xFYbxgNwdv44h/lzRcarntAiEA4h974u1SaWiYezA35MKrYap3hRST/CSzQLtLWuntphQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuoEXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSIA/7BSJ/tOEuxvPYFKFo8Vcwahz3W0rQoOwMpCCojciScAVS6Taw\r\nFVMR8B5k2O9WKmeaRQq7gdej/O5CyDzg2XJs39IvfBldL57COgpDDUF06WfP\r\nIo9oY8agKS69nUIV2tCHnn1hCKzkVosA9BR2SMQK+bjeTRLio5fsCwkl43o5\r\nC7rkzpASh4QdnakAg7B/vByntexP1nPj2Ne7jumhwDU/kneahIT6wK5qGaZQ\r\n+TXxmldVSZ8ic1bWkUa50Tcup17Mz4WLanQMT2hoAzRPRhYMJdV8cBDj1yTI\r\nqZN5quCIAgRw8m2TmxLB1RdExlAkbGFu2DrXwbs/NaGYq/Bm6EmWoRGTI9cV\r\n72kClmwSsYbOMHPtX6Dgb4BW63lmzc4pqTYAf6Ajmpju3z0IxbcUtulwmxkc\r\nxLuQU3v8A4510gi3/Voo7F7z9lnAijyboqL/BMoC1IeNP7jS5U2Twf5BNP+m\r\nIS2gxC724UtU24VpmMnL4ceBJfcF4G8ioCiZfyWYVY/kFHoY2nx02PehOJLn\r\ntv7nl0W7IYWNXHL3Xp+vaNk/EU3Ikmnt92tV0vMAjRUDYOmm2GaWuxnsXHCy\r\ntBiHU3U+PX0r+unQ74tzCdBhFpYL8yTc6iEbABOZTV+MDIyY2xe8Vhgfkz6n\r\nDv7ugWcbqPbn3oAvkv0RsiJHS/Lit6Bm/8Q=\r\n=lQ54\r\n-----END PGP SIGNATURE-----\r\n", "size": 10802}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_4.5.19_1656389911230_0.8624332375672417"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-28T04:40:11.027Z"}, "5.0.7": {"name": "@vue/cli-plugin-eslint", "version": "5.0.7", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.7", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "4a0655f7ac09b64d2b47506e7f21e7923d43262b", "_id": "@vue/cli-plugin-eslint@5.0.7", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/4.0.0/node@v16.15.0+arm64 (darwin)", "dist": {"integrity": "sha512-KOiMq+zcMkKC1oUpPWHGYd4g+7ehCx2/NJqqA3Ds2wsLSNPZCJWGTfzK1p1j87kDVgM3zTPXxRCBFHa2lZW8eg==", "shasum": "078d3c7fb8c31023fd0dcc38558fb385a0235912", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.7.tgz", "fileCount": 16, "unpackedSize": 32776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFpvCSD8Vy9IMVgfXv03qVpLe/IEAhTp7qHpdtPS83bhAiB3IKti8G/D9hRN4hHpHZFKBnnGrMMo9O5HdmzHwlN3Zg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiw/wbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZjA/9FdxUORM+CnKXcyIRFYcswxAdgFXtgVQGGtnnX3VxMcOD7+Yf\r\nzJDRVzilC84NVCXIosQK/BiEFcTIWmcB5myJgDnpGl1MkSMFU4w35Vk+RJc+\r\nskrBr4s6tkpGjoIEOapbnT/gM4nVQKSx/e+vKenLrP0jiPMmdsmFoGtngOMn\r\np0+CnY0VJIrn8CQb/1UJc5kqCxo9RoVuAY6MTHAVJe2HK6cm4l4Dd3jUjX4Q\r\n2hWz5IFaCXd+vnXUp7Yp4gN5xqH3TrHrjWEiRSinsebazsfk4M+4vz8QEL6n\r\nDk+8GDrxEUVhjPQND3ryiVF/aeXxdGP3ST3CsSPr6CmcvIzCHI6FlGIvu//M\r\n5P5Qb7QXnXjqxVbu2q3dCL1D1Dn3EnZSdUz+Wzziu5K8T6xvPWabkgfHXfGu\r\nLliFDuTCLZ7B9H2wRW/Z1ouR9yPAqepmofQYC9N1ydfmiNvmCCk7qMUoa8ng\r\niRws0yIYeeKQBgWiWVF3bBtGFQPCrMrsbLak9cqKxAcm7VkbYeC5awWcValc\r\nr0xJ/+RK65nQPLtVfygqjeZSzqziKZzgQA6FOr19EAufJxF2WR1Q/P7Z+2Mn\r\nL7xhc5lNqL5zkfjRwAaWyDqClwDkO0ySKWDjmj4o6+PezmUMfNHjUj/DjvgM\r\nTUPk/Rm2u0dJJ7hRIVvXASAo4jMjGrKmf0c=\r\n=XLt5\r\n-----END PGP SIGNATURE-----\r\n", "size": 11714}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.7_1657011227637_0.34814960977486775"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-05T09:33:37.472Z"}, "5.0.8": {"name": "@vue/cli-plugin-eslint", "version": "5.0.8", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.8", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "b154dbd7aca4b4538e6c483b1d4b817499d7b8eb", "_id": "@vue/cli-plugin-eslint@5.0.8", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/4.0.0/node@v16.15.0+arm64 (darwin)", "dist": {"integrity": "sha512-d11+I5ONYaAPW1KyZj9GlrV/E6HZePq5L5eAF5GgoVdu6sxr6bDgEoxzhcS1Pk2eh8rn1MxG/FyyR+eCBj/CNg==", "shasum": "754939265c2c5b746fa36c7d7705a89138e193bf", "tarball": "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.8.tgz", "fileCount": 16, "unpackedSize": 32776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6tGQB7pyefOYf4cFSZjO09Wff4M3mcm38hxopAhtX0AiAwxWsrAClnlmmi3fG9LURnDr64LdhqS1bkt6hQR5rgjg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixrPPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJIA//RNuTZd0MGSrtHksvHTwoGvaXeMDy2IoenuBHpWR9AsThmLw0\r\ntHJ5EbBYBn697qI3EPscQnMLOGlz9Zx3xOos+ktHNty0V514diBw+GuaUVbl\r\nx4Vc8gaWgHDGqfr2bgxUQVu1MSRihGP1tuBVCz4D6SUJ+5Pq4kuL8lGPmftd\r\n6W3HFcPEvBp2TqvQY6Zmg8oFp5jWDpTWheu6WYMomYznqbBObrUeZl6AJfbT\r\nN/yvpzyVVoN61ctz0InRaelMtouxHsIemtLWCw+KFJpoI0DQL/xcvx/Hr0mX\r\nysvC079wj58BgrYQigjQPoOHbRhBuwWpgR52uHMXBkxbFogWxNcPMmEXNNLH\r\n8uHAlT8L5x4CrL2Wev7b9AqPYRMY3IWLB1ZTX+JH6Ytd4LFE0N3Tvo1AgvTX\r\nPzm/hU/3uZu3bjdhKZHdPPdpjAeGvw1wdoCzvw5D6Isvdw5ManV4PFfD6AKs\r\ntnSNstH8mtuMNW40a9q6zhpRXo4JG5QykTVvL/hoq9av/6LsKsrExoXumuPN\r\nGGhPGRtFv3xN2JfhPtrTPzgPao+o+qqknqdjyVruH7Yq9qJolU7io/eIQLQA\r\nKkTapUtguXoPiE0R9WmQBOBf98Qt18waDP1B11g3NGaEdPNIYb70AdmuPJ+5\r\nzrxNXGk7JwbvFdlZ+iPe4yJkUF6sKd7olu8=\r\n=JnlF\r\n-----END PGP SIGNATURE-----\r\n", "size": 11713}, "_npmUser": {"name": "soda", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "akryum", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "soda", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cli-plugin-eslint_5.0.8_1657189327025_0.8524522272611352"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-07T11:03:51.466Z"}}, "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "keywords": ["vue", "cli", "eslint"], "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "_source_registry_name": "default"}