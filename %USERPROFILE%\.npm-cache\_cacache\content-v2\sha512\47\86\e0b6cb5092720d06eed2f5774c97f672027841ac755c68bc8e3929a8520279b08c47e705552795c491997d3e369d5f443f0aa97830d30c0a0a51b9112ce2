{"_id": "@rollup/rollup-linux-loongarch64-gnu", "_rev": "4979953-6752f24ee5e1f75634f7aa4b", "dist-tags": {"beta": "4.33.0-0", "latest": "4.45.0"}, "name": "@rollup/rollup-linux-loongarch64-gnu", "time": {"created": "2024-12-06T12:47:10.684Z", "modified": "2025-07-12T05:54:39.711Z", "4.28.1": "2024-12-06T11:45:17.080Z", "4.29.0-0": "2024-12-16T06:40:15.429Z", "4.29.0-1": "2024-12-19T06:37:50.507Z", "4.29.0-2": "2024-12-20T06:56:24.307Z", "4.29.0": "2024-12-20T18:37:46.172Z", "4.29.1": "2024-12-21T07:16:23.633Z", "4.30.0-0": "2024-12-21T07:17:35.663Z", "4.30.0-1": "2024-12-30T06:52:38.479Z", "4.29.2": "2025-01-05T12:08:05.334Z", "4.30.0": "2025-01-06T06:36:59.761Z", "4.30.1": "2025-01-07T10:36:13.430Z", "4.31.0-0": "2025-01-14T05:58:03.018Z", "4.31.0": "2025-01-19T12:57:08.131Z", "4.32.0": "2025-01-24T08:27:55.322Z", "4.33.0-0": "2025-01-28T08:30:29.294Z", "4.32.1": "2025-01-28T08:33:37.259Z", "4.33.0": "2025-02-01T07:12:21.435Z", "4.34.0": "2025-02-01T08:40:43.190Z", "4.34.1": "2025-02-03T06:58:33.803Z", "4.34.2": "2025-02-04T08:10:23.438Z", "4.34.3": "2025-02-05T09:22:26.508Z", "4.34.4": "2025-02-05T21:31:33.455Z", "4.34.5": "2025-02-07T08:53:24.047Z", "4.34.6": "2025-02-07T16:32:28.506Z", "4.34.7": "2025-02-14T09:54:19.867Z", "4.34.8": "2025-02-17T06:26:45.408Z", "4.34.9": "2025-03-01T07:32:58.168Z", "4.35.0": "2025-03-08T06:25:08.520Z", "4.36.0": "2025-03-17T08:36:06.370Z", "4.37.0": "2025-03-23T14:57:26.560Z", "4.38.0": "2025-03-29T06:29:29.369Z", "4.39.0": "2025-04-02T04:49:53.854Z", "4.40.0": "2025-04-12T08:39:56.494Z", "4.40.1": "2025-04-28T04:35:44.692Z", "4.40.2": "2025-05-06T07:27:15.739Z", "4.41.0": "2025-05-18T05:33:51.047Z", "4.41.1": "2025-05-24T06:14:57.399Z", "4.41.2": "2025-06-06T11:40:54.657Z", "4.42.0": "2025-06-06T14:48:34.867Z", "4.43.0": "2025-06-11T05:23:03.501Z", "4.44.0": "2025-06-19T06:23:21.142Z", "4.44.1": "2025-06-26T04:34:38.960Z", "4.44.2": "2025-07-04T12:56:30.544Z", "4.45.0": "2025-07-12T05:54:27.193Z"}, "versions": {"4.28.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.28.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.28.1", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vPul4uodvWvLhRco2w0GcyZcdyBfpfDRgNKU+p35AWEbJ/HPs1tOUrkSueVbBS0RQHAf/A+nNtDpvw95PeVKOA==", "shasum": "23c6609ba0f7fa7a7f2038b6b6a08555a5055a87", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.28.1.tgz", "fileCount": 3, "unpackedSize": 2559081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+jYiWh5rj/XQFbeZMM8dVyOVrtTqpqwruLXQ6MpVJIAiEApRcnP8uCzakPaE/iBc3ydpDMAuVQgExH//JNUWK8ixQ="}], "size": 1025226}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.28.1_1733485516862_0.3239848693556191"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T11:45:17.080Z", "publish_time": 1733485517080, "_source_registry_name": "default"}, "4.29.0-0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.29.0-0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.29.0-0", "readmeFilename": "README.md", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dsL4z38e2EB8W1xy6AhPOd2ixBx2BPjan/unRFbMKPcTil37/+D9huLkztJSkI+iak37oeGUNq0C5PeWnffrcA==", "shasum": "8a3c35b3009b7a8b65a530b56444423ad237d58a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.29.0-0.tgz", "fileCount": 3, "unpackedSize": 2559083, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5F61IStVToGHlZKMhFcBO7DYvjD6qCVw5JwxzaKxRuwIhAK4c1VM1qVrUl77H9nD0rNMrGqYXXiG7LoG5FCZdTnFh"}], "size": 1023531}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.29.0-0_1734331215223_0.9575056206123753"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-16T06:40:15.429Z", "publish_time": 1734331215429, "_source_registry_name": "default"}, "4.29.0-1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.29.0-1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.29.0-1", "readmeFilename": "README.md", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3hDiC7viwSog5fB4gwBekYTbzoaj2xX4zV48m75/v6SYZVKxgJNFYx2iIzbLItyItNOJD/kvgF7OYSKw8LsVJQ==", "shasum": "296d2770c073a8d4c04643b7e671b3d8bdcd2991", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.29.0-1.tgz", "fileCount": 3, "unpackedSize": 2559083, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRYrXbtu46HFb0pOQOcLXxOOqeu/6UjVR49FWDwg88PQIgVauiNi5WVVhst7mqSFcz0nDFPXhA6WAHZkNyEBFH48w="}], "size": 1023531}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.29.0-1_1734590270269_0.8578194369083212"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-19T06:37:50.507Z", "publish_time": 1734590270507, "_source_registry_name": "default"}, "4.29.0-2": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.29.0-2", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.29.0-2", "readmeFilename": "README.md", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-U1LpPu44v53hK84HPft9o8rTuSH26bNbVoX2xZMVeFxGIjQPI7SjaTXMLy+7E/TGhIgvVMe4KfIPiMzSPQ7euw==", "shasum": "988156006ea1ffb14f1af3efb4992ddab4b9f253", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.29.0-2.tgz", "fileCount": 3, "unpackedSize": 2559083, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKdyF3J6gSBloiLWY7CUavurbMoYZ4KISDF60C1rk7AgIhAMrUob/wqvs4OjUH3FGxlvh3ysVyud85uzaKVjQHhzau"}], "size": 1023531}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.29.0-2_1734677784074_0.9113783765448062"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T06:56:24.307Z", "publish_time": 1734677784307, "_source_registry_name": "default"}, "4.29.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.29.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.29.0", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1+jPFClHmDATqbk0Cwi74KEOymVcs09Vbqe/CTKqLwCP0TeP2CACfnMnjYBs5CJgO20e/4bxFtmbR/9fKE1gug==", "shasum": "cfc1d1ce10f5d709364ad2f33ba57fbfb06e9a0a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.29.0.tgz", "fileCount": 3, "unpackedSize": 2559081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDq2SuX/aQYLPmRuieku1N9SJfFvOwwK5DnXSgw8SyRUgIhAPY86pxfMnnJU370500Jm+gpkb9pqZ6V5zVrLg0sAvar"}], "size": 1023256}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.29.0_1734719865930_0.9846091562928709"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T18:37:46.172Z", "publish_time": 1734719866172, "_source_registry_name": "default"}, "4.29.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.29.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.29.1", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-5a7q3tnlbcg0OodyxcAdrrCxFi0DgXJSoOuidFUzHZ2GixZXQs6Tc3CHmlvqKAmOs5eRde+JJxeIf9DonkmYkw==", "shasum": "07027feb883408e74a3002c8e50caaedd288ae38", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.29.1.tgz", "fileCount": 3, "unpackedSize": 2559081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh4HOrr1OEKvEblHzj+I7hLhUxtQFEgETFjIdUiRNFoAIgPHwmBFQdd4c1NuDdljMe+LSM+6iB/maoX+knEUi2vuc="}], "size": 1023256}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.29.1_1734765383301_0.11173249669220908"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:16:23.633Z", "publish_time": 1734765383633, "_source_registry_name": "default"}, "4.30.0-0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.30.0-0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.30.0-0", "readmeFilename": "README.md", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-EGELnP25JY9d18IXGRHNWPsdr9v0K5F+lyd+ft2PbZ+8cr81w4adm9bBWJutPRdYn6n9Q2n7C9RBQzBY39OoWQ==", "shasum": "0ddf9933a7587042bbb44f811983024c6bf33491", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.30.0-0.tgz", "fileCount": 3, "unpackedSize": 2559083, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcEu0Fo3BCalkpxZ3yTCMTDkKLMQl+QUobqqNdfNg8WAIgAyzlrp3PPkdjRLCKdXgFAf9EDZw05m/KDZ69RMXYakc="}], "size": 1023259}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.30.0-0_1734765455465_0.21968355840749476"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:17:35.663Z", "publish_time": 1734765455663, "_source_registry_name": "default"}, "4.30.0-1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.30.0-1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.30.0-1", "readmeFilename": "README.md", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-<PERSON><PERSON>7ohZkE1I8b36YQP8Up5/8E8GOApddWQ1wopUWUqXRBoujlv5MVKjlgb5PMHPbtqMYSuwZ9gQDF0bjb3yih3A==", "shasum": "6b62251c0fb57d15778cc1ea3036e3b303b357d3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.30.0-1.tgz", "fileCount": 3, "unpackedSize": 2559083, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBf3UmYI6AFGxBWOXUheU0+ZzkoXIrxwBl4A8x/kiDrQIgcEBKKigYpZ/1C9e3JjDOYtLxbMMP/7EreCTr127NOPk="}], "size": 1020654}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.30.0-1_1735541558294_0.6410686927094524"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-30T06:52:38.479Z", "publish_time": 1735541558479, "_source_registry_name": "default"}, "4.29.2": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.29.2", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.29.2", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-kO9Fv5zZuyj2zB2af4KA29QF6t7YSxKrY7sxZXfw8koDQj9bx5Tk5RjH+kWKFKok0wLGTi4bG117h31N+TIBEg==", "shasum": "5c65dd6557fda1f45c285cfeb4c5eda4c868341d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.29.2.tgz", "fileCount": 3, "unpackedSize": 2559081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBnKmPvcxICUXXPPCi0S6QkFiOSKVJDgtrfxyBeC1FUeAiBNML5KOI0DZ9U9CxjBYYW9lzSezMCrhFaadnghBS0ssQ=="}], "size": 1020651}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.29.2_1736078885079_0.9773230172462546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-05T12:08:05.334Z", "publish_time": 1736078885334, "_source_registry_name": "default"}, "4.30.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.30.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.30.0", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-fBpoYwLEPivL3q368+gwn4qnYnr7GVwM6NnMo8rJ4wb0p/Y5lg88vQRRP077gf+tc25akuqd+1Sxbn9meODhwA==", "shasum": "1c2c2bb30f61cbbc0fcf4e6c359777fcdb7108cc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.30.0.tgz", "fileCount": 3, "unpackedSize": 2559081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSKwWQzXiAxNRSwdELsQBDp17YBiLPK+Vy2+npJDBp/AIgI+AiuN+iHM7L+AEbUqrxgTqYgBeDqESA/EYWm9iSYP4="}], "size": 1020650}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.30.0_1736145419554_0.8230813315175292"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-06T06:36:59.761Z", "publish_time": 1736145419761, "_source_registry_name": "default"}, "4.30.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.30.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.30.1", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-fARcF5g296snX0oLGkVxPmysetwUk2zmHcca+e9ObOovBR++9ZPOhqFUM61UUZ2EYpXVPN1redgqVoBB34nTpQ==", "shasum": "e957bb8fee0c8021329a34ca8dfa825826ee0e2e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.30.1.tgz", "fileCount": 3, "unpackedSize": 2559081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsbN4bs1ZniO3FBIpGRJ3TUK3h7rkHxF6u/la63GGXGAiEA36Oy0HRtn10TqZNfJONxB0en3qLQdtJvfDgZ08QefqU="}], "size": 1020650}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.30.1_1736246173176_0.4103505148755333"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-07T10:36:13.430Z", "publish_time": 1736246173430, "_source_registry_name": "default"}, "4.31.0-0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.31.0-0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.31.0-0", "readmeFilename": "README.md", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WheyyX0x32yGw/ONFOgJ+EUhxRTPty39BPVxSuHrOh6mBRBIil40OfXT5jX16Zkn84OcbzSlPRrbidXcJOEsMg==", "shasum": "e6d1f64baf853134758e3c36fcc24ab0c0ccd6ae", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.31.0-0.tgz", "fileCount": 3, "unpackedSize": 2559083, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBNX1AvXavOE51GjprWGdDHZdNBcB7H8/TbXDRGWlbe8AiAPUwpHJgqqlBcO9Png5Zw0U+EUW9tRVXKxgCYAz38zxQ=="}], "size": 1020654}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.31.0-0_1736834282657_0.5084954055496658"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-14T05:58:03.018Z", "publish_time": 1736834283018, "_source_registry_name": "default"}, "4.31.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.31.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.31.0", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-pMlxLjt60iQTzt9iBb3jZphFIl55a70wexvo8p+vVFK+7ifTRookdoXX3bOsRdmfD+OKnMozKO6XM4zR0sHRrQ==", "shasum": "0f0324044e71c4f02e9f49e7ec4e347b655b34ee", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.31.0.tgz", "fileCount": 3, "unpackedSize": 2591849, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9rEPRbgmD6iPiR9KbCMuQIrk5H02Do8hpRsGth43V1AIhAIsuLTk/PgUG9OeRR9YQbOmMlRW70OPYTuZvLKmtp3sC"}], "size": 1030133}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.31.0_1737291427898_0.738473955980713"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-19T12:57:08.131Z", "publish_time": 1737291428131, "_source_registry_name": "default"}, "4.32.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.32.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.32.0", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VAEzZTD63YglFlWwRj3taofmkV1V3xhebDXffon7msNz4b14xKsz7utO6F8F4cqt8K/ktTl9rm88yryvDpsfOw==", "shasum": "7bf0ebd8c5ad08719c3b4786be561d67f95654a7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.32.0.tgz", "fileCount": 3, "unpackedSize": 2575465, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEwX1sqRweMvzO7wWHvdc1ycDF6lUKx9FZ4PUvtP/WOqAiAbbrFYsX+ckoGj24J/1+4gL0v89MHJE30sBadFoPVYFQ=="}], "size": 1023863}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.32.0_1737707275094_0.41381466719431637"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-24T08:27:55.322Z", "publish_time": 1737707275322, "_source_registry_name": "default"}, "4.33.0-0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["loong64"], "dist": {"shasum": "48e2c8eb30bdb974c020999796cdd595c4a1af8f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-5DxndrY39d/ZJVi9lPnBSwKT+fdDZU/rZtuHM5G+0R1+DV3bLnW5P60xocFtOCySzNyZuedfaFWtwlHC8Ajo6w==", "signatures": [{"sig": "MEYCIQD5rEaWmcmDbgrmInS4ctevPKo1JEGT8vUwVlboL8yoTwIhAMG99ygvm8jGsqCUd1yQvVC/6HO8qEB6dBgM/sCB+0HB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2575467, "size": 1023866}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-loongarch64-gnu_4.33.0-0_1738053029102_0.7194582353114349", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-01-28T08:30:29.294Z", "publish_time": 1738053029294, "_source_registry_name": "default"}, "4.32.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.32.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.32.1", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-F51qLdOtpS6P1zJVRzYM0v6MrBNypyPEN1GfMiz0gPu9jN8ScGaEFIZQwteSsGKg799oR5EaP7+B2jHgL+d+Kw==", "shasum": "ad7b35f193f1d2e0dc37eba733069b4af5f6498d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.32.1.tgz", "fileCount": 3, "unpackedSize": 2575465, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHeZu6Y4Mr4gXgCGXgGfwn1gy9Dlxizu/iaXDFmUDi93AiEA5tGgxWkyWSZgOtV+fTtg1vdmkIRz7EJI/NGjXJLQi3Q="}], "size": 1023863}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.32.1_1738053216972_0.5431071672065044"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-28T08:33:37.259Z", "publish_time": 1738053217259, "_source_registry_name": "default"}, "4.33.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["loong64"], "dist": {"shasum": "9e1ae32747a9f0f14639b9baee22a68013077c61", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-j991T8HolnEaoJZ4sgzCNhAv6F62+SPzMwUUgZp5R1i8+FknghmMmdMny42erAh4jYbCUpYqFS6PFxjD7B947A==", "signatures": [{"sig": "MEYCIQDm18j0fnVib0ZCE0ibdwXTefOiNAmrZ2KYxsnAQkihvgIhAOAByK5eMojb4mLp24T0YvFFjU1ZQyAC+LvykLQQ8sfE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2577497, "size": 1024042}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-loongarch64-gnu_4.33.0_1738393941192_0.4320507918903256", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-02-01T07:12:21.435Z", "publish_time": 1738393941435, "_source_registry_name": "default"}, "4.34.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.0", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-e5XiCinINCI4RdyU3sFyBH4zzz7LiQRvHqDtRe9Dt8o/8hTBaYpdPimayF00eY2qy5j4PaaWK0azRgUench6WQ==", "shasum": "160903324cfcf09dc61be9d97e623e326c1a863a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.0.tgz", "fileCount": 3, "unpackedSize": 2577497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDdEhcnvuKEft4x6eeXmT/imS664AzLfK5s1B+te88hpQIgJvOSoU4UpF99fhawzvFvuzuCz6QzK7cEbyWDaSBSV5A="}], "size": 1024042}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.0_1738399242959_0.9519630330066509"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-01T08:40:43.190Z", "publish_time": 1738399243190, "_source_registry_name": "default"}, "4.34.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.1", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-rSzb1TsY4lSwH811cYC3OC2O2mzNMhM13vcnA7/0T6Mtreqr3/qs6WMDriMRs8yvHDI54qxHgOk8EV5YRAHFbw==", "shasum": "64f7871f5718a086786485b9c4d22fd263a37b44", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.1.tgz", "fileCount": 3, "unpackedSize": 2577497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDOndOYZ6J6eI4tIjhWqFEVDaHHcXO2Hi7JxfjSqYeMLAiBJ+hocRvSojEyZbogm6dxN6ekNAvJ2w8D3YQpIe/vrpQ=="}], "size": 1024043}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.1_1738565913593_0.03941720562385442"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-03T06:58:33.803Z", "publish_time": 1738565913803, "_source_registry_name": "default"}, "4.34.2": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.2", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.2", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hJhfsD9ykx59jZuuoQgYT1GEcNNi3RCoEmbo5OGfG8RlHOiVS7iVNev9rhLKh7UBYq409f4uEw0cclTXx8nh8Q==", "shasum": "ae81c19fd33f2377ee0407b3179fd1d301598453", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.2.tgz", "fileCount": 3, "unpackedSize": 2577497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH6XEigtv6Xpkwv3+Sr5atD6uZa9LH5xeNwlhE/uzOiJAiEA7426MCpja6y8ELsJzAXC5TvDVguBt0cg/w7Lqd8Tmqo="}], "size": 1024043}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.2_1738656623209_0.3421862914947196"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-04T08:10:23.438Z", "publish_time": 1738656623438, "_source_registry_name": "default"}, "4.34.3": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.3", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.3", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-QlW1jCUZ1LHUIYCAK2FciVw1ptHsxzApYVi05q7bz2A8oNE8QxQ85NhM4arLxkAlcnS42t4avJbSfzSQwbIaKg==", "shasum": "a1149b186e16d009d8fd715285e84ed63ba3cbbc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.3.tgz", "fileCount": 3, "unpackedSize": 2577497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCZPkVBCY+Hi0JrhINUJrA5oZ0dyXPV+s+QiZvL4HbMWgIgDGCfT2FnzIGVOO61oR2LmglhT9HEsTEjKeCGGiokcU0="}], "size": 1024043}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.3_1738747346251_0.7276360748311299"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T09:22:26.508Z", "publish_time": 1738747346508, "_source_registry_name": "default"}, "4.34.4": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.4", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.4", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-uuphLuw1X6ur11675c2twC6YxbzyLSpWggvdawTUamlsoUv81aAXRMPBC1uvQllnBGls0Qt5Siw8reSIBnbdqQ==", "shasum": "eb2af43af6456235e1dc097e0a06d058e87603cb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.4.tgz", "fileCount": 3, "unpackedSize": 2577497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDFoqFI5b4hDZ0xUoXqZpGsuAWEPEhsE6XyrxsRmgHGrgIhANxGylo+zp2giUICCeVVfzTlW57PBifaCZLeJFTorPci"}], "size": 1024042}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.4_1738791093275_0.12045990585610755"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T21:31:33.455Z", "publish_time": 1738791093455, "_source_registry_name": "default"}, "4.34.5": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.5", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.5", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-pARu8ZKANZH4wINLdHLKG69EPwJswM6A+Ox1a9LpiclRQoyjacFFTtXN3akKQ2ufJXDasO/pWvxKN9ZfCgEoFA==", "shasum": "7b59ce9aa4d181e8394555a34ed7bbb5818d69af", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.5.tgz", "fileCount": 3, "unpackedSize": 2561113, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD0pErO/m9T45GPC329UOXVw+H/yxUxjZRERwRX3KsxjAIhAPE9Oh7uQuafEHlMNDuO9Vswp23/mbDHm+YuaHypgbL+"}], "size": 1022303}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.5_1738918403748_0.575299692661315"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T08:53:24.047Z", "publish_time": 1738918404047, "_source_registry_name": "default"}, "4.34.6": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.6", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.6", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4Qmkaps9yqmpjY5pvpkfOerYgKNUGzQpFxV6rnS7c/JfYbDSU0y6WpbbredB5cCpLFGJEqYX40WUmxMkwhWCjw==", "shasum": "19418cc57579a5655af2d850a89d74b3f7e9aa92", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.6.tgz", "fileCount": 3, "unpackedSize": 2561113, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD1ADsQ+4Z3C6024a2dWNe+PCekdZ0z/V5fTfK48GUqPAIgNV2eDmDU7lqJ62PBm/EQWqJeHETVS/1gCKUuSWvvaOs="}], "size": 1019555}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.6_1738945948249_0.6859400979194892"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T16:32:28.506Z", "publish_time": 1738945948506, "_source_registry_name": "default"}, "4.34.7": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.7", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.7", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ws8pc68UcJJqCpneDFepnwlsMUFoWvPbWXT/XUrJ7rWUL9vLoIN3GAasgG+nCvq8xrE3pIrd+qLX/jotcLy0Qw==", "shasum": "a7488ab078233111e8aeb370d1ecf107ec7e1716", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.7.tgz", "fileCount": 3, "unpackedSize": 2561113, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHyIi5/SUeSHV9me0//rdIyQd0vKWmE1YDzLmYNV8earAiEAy6SBwq6UKOYrR9sa2bLyK/qCd8W+uwgZ4cnXpmsWS/A="}], "size": 1019653}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.7_1739526859640_0.7042516603369378"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T09:54:19.867Z", "publish_time": 1739526859867, "_source_registry_name": "default"}, "4.34.8": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.8", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.8", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NyF4gcxwkMFRjgXBM6g2lkT58OWztZvw5KkV2K0qqSnUEqCVcqdh2jN4gQrTn/YUpAcNKyFHfoOZEer9nwo6uQ==", "shasum": "5869dc0b28242da6553e2b52af41374f4038cd6e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.8.tgz", "fileCount": 3, "unpackedSize": 2561113, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBo5nKcliIm0vhRtLMT3dOxEo00cfMf+9duJBK+V0/VtAiEAstSxNC+MnYt6XAWb4D5kCPBwIrXd957Wh9Ax5PDaXCs="}], "size": 1019652}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.8_1739773605154_0.37364613990872186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-17T06:26:45.408Z", "publish_time": 1739773605408, "_source_registry_name": "default"}, "4.34.9": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.34.9", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.34.9", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dRAgTfDsn0TE0HI6cmo13hemKpVHOEyeciGtvlBTkpx/F65kTvShtY/EVyZEIfxFkV5JJTuQ9tP5HGBS0hfxIg==", "shasum": "c9cd5dbbdc6b3ca4dbeeb0337498cf31949004a0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.9.tgz", "fileCount": 3, "unpackedSize": 2643033, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCHikAJTmZhKtlxjPmDyo814nXUodPze+5uWvyuMLIr5wIgUnwjgPIciQVMhAnTAqVe+zX3M4pJ9Hp5B9+oIaBUntA="}], "size": 1054730}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.34.9_1740814377983_0.34151131703901827"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-01T07:32:58.168Z", "publish_time": 1740814378168, "_source_registry_name": "default"}, "4.35.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.35.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.35.0", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-5pMT5PzfgwcXEwOaSrqVsz/LvjDZt+vQ8RT/70yhPU06PTuq8WaHhfT1LW+cdD7mW6i/J5/XIkX/1tCAkh1W6g==", "shasum": "561bd045cd9ce9e08c95f42e7a8688af8c93d764", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.35.0.tgz", "fileCount": 3, "unpackedSize": 2659417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCP9471kAv4XV5q/R/C0m/gnnFQj5PDb6dTH4TvUwj2dAIgArIInfSg1p4kEMc3DXq8e5ds9zoPPHcGnQhJ1hdXxoY="}], "size": 1055295}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.35.0_1741415108227_0.9219380767291743"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-08T06:25:08.520Z", "publish_time": 1741415108520, "_source_registry_name": "default"}, "4.36.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.36.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.36.0", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1ZPyEDWF8phd4FQtTzMh8FQwqzvIjLsl6/84gzUxnMNFBtExBtpL51H67mV9xipuxl1AEAerRBgBwFNpkw8+Lg==", "shasum": "9ad12d1a5d3abf4ecb90fbe1a49249608cee8cbb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.36.0.tgz", "fileCount": 3, "unpackedSize": 2643033, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCAmoXjjdM/RLSlAS6i/iK25DBppsKit1vZQKV6TBkAmQIhAPBXHl+F5gXTKkLO9xPitnENJasUa+lklHdT8VNh8Km+"}], "size": 1056249}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.36.0_1742200566120_0.6058764863638588"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-17T08:36:06.370Z", "publish_time": 1742200566370, "_source_registry_name": "default"}, "4.37.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.37.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.37.0", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-yCE0NnutTC/7IGUq/PUHmoeZbIwq3KRh02e9SfFh7Vmc1Z7atuJRYWhRME5fKgT8aS20mwi1RyChA23qSyRGpA==", "shasum": "5660181c1c1efb7b19c7a531d496e685236c5ce7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.37.0.tgz", "fileCount": 3, "unpackedSize": 2659417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCgFcdiv1gWOWwQadTKoa3Z4WJ2V/m/q/1UWa7bYhLxnQIhAM7FQl0Jb8xiPwUhKN+HkxlxEWlxgP0r93FHSSnOIKnP"}], "size": 1052755}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.37.0_1742741846321_0.7681704579254218"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-23T14:57:26.560Z", "publish_time": 1742741846560, "_source_registry_name": "default"}, "4.38.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.38.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.38.0", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hz5oqQLXTB3SbXpfkKHKXLdIp02/w3M+ajp8p4yWOWwQRtHWiEOCKtc9U+YXahrwdk+3qHdFMDWR5k+4dIlddg==", "shasum": "0d6dcaa3671cf987faace4b34ab7320ee3c18b65", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.38.0.tgz", "fileCount": 3, "unpackedSize": 2659417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGs/f8pn5zW995c7CCcSrEMbd9rxjHggD3TsR/bO/qQjAiATKYXCt5YPLHYkqM5NGE5AO/SWv16qWkVs5kUamIkHKg=="}], "size": 1054269}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.38.0_1743229769142_0.592968013956277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-29T06:29:29.369Z", "publish_time": 1743229769369, "_source_registry_name": "default"}, "4.39.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.39.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.39.0", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NMRUT40+h0FBa5fb+cpxtZoGAggRem16ocVKIv5gDB5uLDgBIwrIsXlGqYbLwW8YyO3WVTk1FkFDjMETYlDqiw==", "shasum": "53403889755d0c37c92650aad016d5b06c1b061a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.39.0.tgz", "fileCount": 3, "unpackedSize": 2659417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBKTH6vaTQbB67XQwsqBEz+7Fj/yLZp/PTt4yS/Jyl7jAiBSA8eSpv+y3AkCNa34J3I2Uy1HKs3aGD2aBdoAKc9tYA=="}], "size": 1054270}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.39.0_1743569393607_0.9285564052171498"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T04:49:53.854Z", "publish_time": 1743569393854, "_source_registry_name": "default"}, "4.40.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.40.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.40.0", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-wG9e2XtIhd++QugU5MD9i7OnpaVb08ji3P1y/hNbxrQ3sYEelKJOq1UJ5dXczeo6Hj2rfDEL5GdtkMSVLa/AOg==", "shasum": "a0a310e51da0b5fea0e944b0abd4be899819aef6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.40.0.tgz", "fileCount": 3, "unpackedSize": 2660737, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCHFiMoHO0N0f/vUgsBETMH8Uc0mfpwW941iNT3m3y9aQIhAIAZs0foEUHQqZpL30OwULKVQGizQ9SF+Kng2oALb4W4"}], "size": 1060457}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.40.0_1744447196266_0.03690097547867088"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-12T08:39:56.494Z", "publish_time": 1744447196494, "_source_registry_name": "default"}, "4.40.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.40.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.40.1", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ySyWikVhNzv+BV/IDCsrraOAZ3UaC8SZB67FZlqVwXwnFhPihOso9rPOxzZbjp81suB1O2Topw+6Ug3JNegejQ==", "shasum": "20b77dc78e622f5814ff8e90c14c938ceb8043bc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.40.1.tgz", "fileCount": 3, "unpackedSize": 2726297, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBendloOgOQl1cWn+a+YjreKHfrI84AQJrHGQytqnJR/AiEAvqjaXcCVFkx9arp3slJCM09wUK88GuK8bmvH+5qDSxU="}], "size": 1072044}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.40.1_1745814944473_0.08383839506953095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-28T04:35:44.692Z", "publish_time": 1745814944692, "_source_registry_name": "default"}, "4.40.2": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.40.2", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.40.2", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Ybc/1qUampKuRF4tQXc7G7QY9YRyeVSykfK36Y5Qc5dmrIxwFhrOzqaVTNoZygqZ1ZieSWTibfFhQ5qK8jpWxw==", "shasum": "818de242291841afbfc483a84f11e9c7a11959bc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.40.2.tgz", "fileCount": 3, "unpackedSize": 2726297, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHGvzirMT982hzY1qQ4xIM54CSusUv0oDUSReQTOozszAiEAu+SW5yt5zQQcE9L1lEo+hxuIOsO9cpWlntlLZQKTt20="}], "size": 1074578}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.40.2_1746516435480_0.16744726478129857"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T07:27:15.739Z", "publish_time": 1746516435739, "_source_registry_name": "default"}, "4.41.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.41.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.41.0", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WbnJaxPv1gPIm6S8O/Wg+wfE/OzGSXlBMbOe4ie+zMyykMOeqmgD1BhPxZQuDqwUN+0T/xOFtL2RUWBspnZj3w==", "shasum": "b06374601ce865a1110324b2f06db574d3a1b0e1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.0.tgz", "fileCount": 3, "unpackedSize": 2709913, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDqCQMOzmsU3hqxogJCqRY2A6o0esKH07w9+47x6MKRAQIgAkMTAkqh5Vry6MDAfIHUAAAvkfNVujWNrKT4dsykVGM="}], "size": 1071077}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.41.0_1747546430798_0.004056753298549154"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-18T05:33:51.047Z", "publish_time": 1747546431047, "_source_registry_name": "default"}, "4.41.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.41.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.41.1", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bkCfDJ4qzWfFRCNt5RVV4DOw6KEgFTUZi2r2RuYhGWC8WhCA8lCAJhDeAmrM/fdiAH54m0mA0Vk2FGRPyzI+tw==", "shasum": "c659b01cc6c0730b547571fc3973e1e955369f98", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.1.tgz", "fileCount": 3, "unpackedSize": 2841201, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDz6QCZX+JVuS+WzbYCSy/tLZGIwi3tQSfnPpr3vKfqmAIgShlcfF3+Q9sTrnjTRnFneiAuXywRVMjmY+0Ki4oGrio="}], "size": 1120950}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.41.1_1748067297120_0.6273605589138089"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-24T06:14:57.399Z", "publish_time": 1748067297399, "_source_registry_name": "default"}, "4.41.2": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.41.2", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.41.2", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Bp2BYb+QWgefn2e8DblGUzejLEyhtG0DnIcSMzAzFtBK33ITvzLM9B8maTHPoy7Cx7XdxEb7l0iNKQAXBZ1NOw==", "shasum": "2662a182009b6fea6215db165a4eab30aa185663", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.2.tgz", "fileCount": 3, "unpackedSize": 2824817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCID6FDyeicQYgbggnnxmvTEVHKVN+LYg9E9xdhxuwWSj5AiBnJ4axkm5HwoH573nblDS6VAyGOqnDZ4b5a6ehfsgnqg=="}], "size": 1117899}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.41.2_1749210054413_0.3731251637301556"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T11:40:54.657Z", "publish_time": 1749210054657, "_source_registry_name": "default"}, "4.42.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.42.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.42.0", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-O8AplvIeavK5ABmZlKBq9/STdZlnQo7Sle0LLhVA7QT+CiGpNVe197/t8Aph9bhJqbDVGCHpY2i7QyfEDDStDg==", "shasum": "90d35336ad4cbf318648e41b0e7ce3920c28ebc9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.42.0.tgz", "fileCount": 3, "unpackedSize": 2824817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIB7xy2V0C8uZEngwjNPWni3EgMSpRmDeJXt/CTyHapeoAiEA9IF+ZgUDLCjcmRJ9Z9K7cZCuwy2k7fvrMJC3zk8G/NY="}], "size": 1117899}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.42.0_1749221314647_0.2734262781650172"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T14:48:34.867Z", "publish_time": 1749221314867, "_source_registry_name": "default"}, "4.43.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.43.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.43.0", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-HPGDIH0/ZzAZjvtlXj6g+KDQ9ZMHfSP553za7o2Odegb/BEfwJcR0Sw0RLNpQ9nC6Gy8s+3mSS9xjZ0n3rhcYg==", "shasum": "224ff524349e365baa56f1f512822548c2d76910", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.43.0.tgz", "fileCount": 3, "unpackedSize": 2824817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDT5WMjFnnmKrYM3QxStfBG5fBrraDkYf6XX0Zbq97TQAiEAv8RoBEGm5t8cvXDgE8xb24GSVE0sqrS4RD3Dbv+uSCE="}], "size": 1117899}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.43.0_1749619383256_0.6072415064261574"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T05:23:03.501Z", "publish_time": 1749619383501, "_source_registry_name": "default"}, "4.44.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.44.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-xw+FTGcov/ejdusVOqKgMGW3c4+AgqrfvzWEVXcNP6zq2ue+lsYUgJ+5Rtn/OTJf7e2CbgTFvzLW2j0YAtj0Gg==", "shasum": "9b6a49afde86c8f57ca11efdf8fd8d7c52048817", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.0.tgz", "fileCount": 3, "unpackedSize": 2841201, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEpBzQkPxH3Q0+mzzjZzJDFj9jI1TXA20TM5gGPyx0qtAiEA0AeJ6j5hOsOCksQQ9naBpNTuE+o+m3WgWikRH4sZoPg="}], "size": 1121768}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.44.0_1750314200953_0.11657611701689907"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-19T06:23:21.142Z", "publish_time": 1750314201142, "_source_registry_name": "default"}, "4.44.1": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.44.1", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.44.1", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1zqnUEMWp9WrGVuVak6jWTl4fEtrVKfZY7CvcBmUUpxAJ7WcSowPSAWIKa/0o5mBL/Ij50SIf9tuirGx63Ovew==", "shasum": "855a80e7e86490da15a85dcce247dbc25265bc08", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.1.tgz", "fileCount": 3, "unpackedSize": 2841201, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCmbFT5rM1naG8v2B973xzCPOD239hNk/nyMq/Q4iAvAgIgNX+f0wytP6rdOclWEQ9ZpZ7/11PkrV7tE020okeCGHY="}], "size": 1121768}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.44.1_1750912478721_0.12387230730383036"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-26T04:34:38.960Z", "publish_time": 1750912478960, "_source_registry_name": "default"}, "4.44.2": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.44.2", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Yl5Rdpf9pIc4GW1PmkUGHdMtbx0fBLE1//SxDmuf3X0dUC57+zMepow2LK0V21661cjXdTn8hO2tXDdAWAqE5g==", "shasum": "8303c4ea2ae7bcbb96b2c77cfb53527d964bfceb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2824817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFTgLsBPN1g2yxCD+oMYcxV7S1OUgMzuavob4CvX1eLOAiEA5KMexSrGLCAqLWOWrd3AVsb9wv60+DryfX4s63d1Liw="}], "size": 1122532}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.44.2_1751633790304_0.8988852708684609"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-04T12:56:30.544Z", "publish_time": 1751633790544, "_source_registry_name": "default"}, "4.45.0": {"name": "@rollup/rollup-linux-loongarch64-gnu", "version": "4.45.0", "os": ["linux"], "cpu": ["loong64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-loongarch64-gnu.node", "_id": "@rollup/rollup-linux-loongarch64-gnu@4.45.0", "gitHead": "b7c7c1159f70ebe8ad6f94c942ebab2fa59c7982", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Li0emNnwtUZdLwHjQPBxn4VWztcrw/h7mgLyHiEI5Z0MhpeFGlzaiBHpSNVOMB/xucjXTTcO+dhv469Djr16KA==", "shasum": "239feea00fa2a1e734bdff09b8d1c90def2abbf5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.45.0.tgz", "fileCount": 3, "unpackedSize": 2824817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDbvKLIgo6sSNnIYTVWHm0r6uSdeWpTBEJjkQq8xcaCxwIhAN5hbS1mS5zbYfO0bH+GetVTrfuWvRShmpuCnnmFhriU"}], "size": 1122532}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-loongarch64-gnu_4.45.0_1752299666929_0.027963241469636246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-12T05:54:27.193Z", "publish_time": 1752299667193, "_source_registry_name": "default"}}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "description": "Native bindings for Rollup", "homepage": "https://rollupjs.org/", "license": "MIT", "maintainers": [{"name": "lukastaegert", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "shellscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "readme": "# `@rollup/rollup-linux-loongarch64-gnu`\n\nThis is the **loongarch64-unknown-linux-gnu** binary for `rollup`\n", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "_source_registry_name": "default"}