{"_id": "@esbuild/darwin-x64", "_rev": "2328711-638e81d78a129d27a6ca9d06", "dist-tags": {"latest": "0.25.6"}, "name": "@esbuild/darwin-x64", "time": {"created": "2022-12-05T23:42:15.145Z", "modified": "2025-07-07T17:29:16.445Z", "0.15.18": "2022-12-05T23:42:06.231Z", "0.16.0": "2022-12-07T03:55:10.476Z", "0.16.1": "2022-12-07T04:48:19.075Z", "0.16.2": "2022-12-08T06:59:57.425Z", "0.16.3": "2022-12-08T20:13:14.928Z", "0.16.4": "2022-12-10T03:50:42.823Z", "0.16.5": "2022-12-13T17:47:47.207Z", "0.16.6": "2022-12-14T05:23:20.347Z", "0.16.7": "2022-12-14T22:47:00.840Z", "0.16.8": "2022-12-16T23:38:55.070Z", "0.16.9": "2022-12-18T04:31:33.009Z", "0.16.10": "2022-12-19T23:26:41.518Z", "0.16.11": "2022-12-27T01:39:15.417Z", "0.16.12": "2022-12-28T02:04:49.038Z", "0.16.13": "2023-01-02T22:57:27.191Z", "0.16.14": "2023-01-04T20:13:09.647Z", "0.16.15": "2023-01-07T04:19:09.947Z", "0.16.16": "2023-01-08T22:43:57.512Z", "0.16.17": "2023-01-11T21:58:09.947Z", "0.17.0": "2023-01-14T04:33:49.717Z", "0.17.1": "2023-01-16T18:05:47.336Z", "0.17.2": "2023-01-17T06:39:49.178Z", "0.17.3": "2023-01-18T19:14:38.915Z", "0.17.4": "2023-01-22T06:13:45.788Z", "0.17.5": "2023-01-27T16:37:56.243Z", "0.17.6": "2023-02-06T17:00:52.879Z", "0.17.7": "2023-02-09T22:26:49.524Z", "0.17.8": "2023-02-13T06:35:47.267Z", "0.17.9": "2023-02-19T17:45:27.800Z", "0.17.10": "2023-02-20T17:55:03.961Z", "0.17.11": "2023-03-03T22:40:21.241Z", "0.17.12": "2023-03-17T06:17:15.526Z", "0.17.13": "2023-03-24T18:57:16.098Z", "0.17.14": "2023-03-26T02:47:50.761Z", "0.17.15": "2023-04-01T22:27:00.790Z", "0.17.16": "2023-04-10T04:35:11.190Z", "0.17.17": "2023-04-16T21:23:47.290Z", "0.17.18": "2023-04-22T20:41:33.770Z", "0.17.19": "2023-05-13T00:06:40.966Z", "0.18.0": "2023-06-09T21:24:28.627Z", "0.18.1": "2023-06-12T04:51:51.929Z", "0.18.2": "2023-06-13T02:40:37.452Z", "0.18.3": "2023-06-15T12:21:12.478Z", "0.18.4": "2023-06-16T15:38:40.092Z", "0.18.5": "2023-06-20T00:52:48.687Z", "0.18.6": "2023-06-20T23:24:59.588Z", "0.18.7": "2023-06-24T02:46:30.805Z", "0.18.8": "2023-06-25T03:19:21.284Z", "0.18.9": "2023-06-26T05:28:10.944Z", "0.18.10": "2023-06-26T21:20:34.983Z", "0.18.11": "2023-07-01T06:04:02.495Z", "0.18.12": "2023-07-13T01:34:17.955Z", "0.18.13": "2023-07-15T02:37:21.790Z", "0.18.14": "2023-07-18T05:00:28.622Z", "0.18.15": "2023-07-20T12:53:23.329Z", "0.18.16": "2023-07-23T04:48:10.629Z", "0.18.17": "2023-07-26T01:40:59.117Z", "0.18.18": "2023-08-05T17:06:34.576Z", "0.18.19": "2023-08-07T02:51:26.105Z", "0.18.20": "2023-08-08T04:15:10.097Z", "0.19.0": "2023-08-08T15:52:38.349Z", "0.19.1": "2023-08-11T15:57:38.628Z", "0.19.2": "2023-08-14T01:58:26.234Z", "0.19.3": "2023-09-14T01:12:29.423Z", "0.19.4": "2023-09-28T01:47:20.210Z", "0.19.5": "2023-10-17T05:10:39.398Z", "0.19.6": "2023-11-19T07:11:43.115Z", "0.19.7": "2023-11-21T01:01:09.078Z", "0.19.8": "2023-11-26T23:08:11.357Z", "0.19.9": "2023-12-10T05:09:31.020Z", "0.19.10": "2023-12-19T00:21:42.021Z", "0.19.11": "2023-12-29T20:31:58.547Z", "0.19.12": "2024-01-23T17:40:32.023Z", "0.20.0": "2024-01-27T16:49:38.776Z", "0.20.1": "2024-02-19T06:38:08.813Z", "0.20.2": "2024-03-14T19:49:48.278Z", "0.21.0": "2024-05-07T02:52:32.478Z", "0.21.1": "2024-05-07T16:55:09.847Z", "0.21.2": "2024-05-12T20:32:59.666Z", "0.21.3": "2024-05-15T20:52:38.650Z", "0.21.4": "2024-05-25T02:10:55.140Z", "0.21.5": "2024-06-09T21:17:00.976Z", "0.22.0": "2024-06-30T20:37:51.713Z", "0.23.0": "2024-07-02T03:33:49.792Z", "0.23.1": "2024-08-16T22:13:19.973Z", "0.24.0": "2024-09-22T02:06:27.988Z", "0.24.1": "2024-12-20T05:40:51.629Z", "0.24.2": "2024-12-20T17:56:14.010Z", "0.25.0": "2025-02-08T03:02:13.146Z", "0.25.1": "2025-03-10T03:45:21.706Z", "0.25.2": "2025-03-30T17:32:51.655Z", "0.25.3": "2025-04-23T03:55:56.260Z", "0.25.4": "2025-05-06T00:30:38.520Z", "0.25.5": "2025-05-27T03:12:42.782Z", "0.25.6": "2025-07-07T16:58:55.105Z"}, "versions": {"0.15.18": {"name": "@esbuild/darwin-x64", "version": "0.15.18", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.15.18", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-pKNpGvjvDwzLLguZ00N36Z4oYi4Th70811GA6cd9/TumpnHdE96vCtqqjY9YXh81q4NqEssEhJosOD4UbYvAPw==", "shasum": "d2484bc3356ce47307210cc67c0bf47dba1bc5cd", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.15.18.tgz", "fileCount": 3, "unpackedSize": 9095928, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQSrmK9f0gFgDnQfwwA7ibmReRNWGH6k0kBqtki2TBfAIhAKygKhuSRKty1oMBkMupzNU8xMhRfTx7tBQb8TVt2y3h"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoHOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogiQ//cITUZ1CwilbcvcvFqkzBvjbIYarPiKXuiZ171alecx7k5RFN\r\nJBqjmrndSO6fI0lWvptj5l2qevDyhazs4NJ5dK+If11r2Cli661CVeXGX/sJ\r\nMNVZfy3vX5SuSfBeVKrOoSOajkhZriZ2rgv3SX94ehMSfUQS5FSmoBUVzeAv\r\nbrSSqoJrasHHjeqZgCUDDm5UEoacng+kG/hucSvaWCZZOi78mnW2KHfedwcp\r\nCkVuT804f8LMCobrtUgIthp+nNih7Jg/4OopIptkW2i5YxURIcC0Mz5f//iI\r\nxUaMUpP4ZUIB64PH9ytZG7lM3hsQoM30DljaCQgqfyqU9zUWBgVz1kNyraYB\r\nuGRngPr2ctdc5CopBD0jvr1/wOtUA6MFtnOB6hDXPXhg7kVLheu0zE60cv7k\r\nYTnwk4/lu6PEjMMXxT7CIGk5Zjuene1z+bekm98j48klNx7Kag+HWonuuXtU\r\nSAa7OWQcBe7O+KeVNRMp+sL3VcMplvIoLTEIctGHyb8eoT74YxeTpYqngtrk\r\nfwDDp2/l7wlCXAXt1NvXiSGYnFQ8Uz/Yb7Pk5OG1fxx5lvsVS1nVcXuWRqrO\r\nqlzxx5wPYKpWTu/IyyZCMgYrNyB+fcW3Ov1hRHqhd0aE8jLu5/TgjCd6PRR8\r\nKaVrJ2THzcEcfU1y5lT7aWP6JQYted8sXsE=\r\n=gZkh\r\n-----END PGP SIGNATURE-----\r\n", "size": 3708184}, "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.15.18_1670283725963_0.9569404288444563"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-05T23:42:13.323Z"}, "0.16.0": {"name": "@esbuild/darwin-x64", "version": "0.16.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-bp9Nbh1LTzxPvdCxLTuGiUlWJ4qITaLaV4Ku8FikrLO0cLuoppju4p7UXwBQGHXrCY3TYS0zON2Mi/ujGp3oew==", "shasum": "635265146bdb70fdbf15726473225bb97564fe62", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.0.tgz", "fileCount": 3, "unpackedSize": 9095863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkn/7eQ+oS030rQiPmMAfaunNHycyrgdhYiiCqZX6lmgIgA3wiNiVUZHaZYV23b/JYJXL6x/tG6rQSrCjeftHup2g="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpPA/9HIM34rhGOIOJHgDUoOkKD6wZhZ0lhisnrH+vUFL2xqZXyvdr\r\nBaGzfnidHiv1Q9R75qYrali1/Lti7Edo1nwk11KxZ4BPmAhrDYItfrSFJb0j\r\nWO8PC3yeUMwzpYx7wXLH/oT5Wu5U6pUVv9kmgkzpQoUT+x27Et1KPf2yHLDF\r\n9uhV4JUPGiwM3/ZgH6xOiffnjqFS04+1zQIN6KWVpTdSH+bGyfsNVQx9ezXE\r\n4NGIra/ebsZNJ7nAr5vPDT9ACCs8aXvbDa8uLwnoX3xeEcFFhte5yOG49I3p\r\nwFrtEi5tkmxlkd9AziOYVNvRq5MMweTFrZCRxPHiAG1gLOYok267H3URvmFs\r\nZunytUJcu5d6BKH7Jzwg+kHKbn8+cM3EeD59XDOizWN6WC9iPpXYAlfcgC7s\r\nCFkXjYPFuFYwQbn+mhHo5oo7CgRyvMtKXai39eH+oGAc8MPClOlWvmMgw9gh\r\nntz/xz/6FElECjtB++GnzvnC9nBRDmbvLPKx0rzWFLgDW88XIEcrA+0KEISf\r\nFo/EuAdCdeS6w81CMofEEMMzo6v+Xxyi/ShdeeMz5UZegq+9u4u0ow35Pcma\r\n0D513yIx7wsenDVeeq4WWy8TmV2mIS3IAmW7OTMp9WPWD30IOQCY4j1IbqWM\r\nHdL5v7hEvKB2miFkTkp2MKgUNz/sOoJMEEA=\r\n=zKKU\r\n-----END PGP SIGNATURE-----\r\n", "size": 3709757}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.0_1670385310228_0.03588577994559894"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T04:04:36.344Z"}, "0.16.1": {"name": "@esbuild/darwin-x64", "version": "0.16.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-UFJ8swS3ZiQgT51ll9P3K+WOiYSc3Dw68kbZqXlmF5zwB7p/nx31jilW6ie+UlKIFRw4X0Z1SejwVC6ZpH7PSQ==", "shasum": "9036cf2c6d796cf6040693c77e7343d2cc37fbed", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.1.tgz", "fileCount": 3, "unpackedSize": 9095863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFPZ2OZHtU1YpbqG+1Umw31YWul3TsGaiW4sTvQtk81AIgTdep00XOOtwOPCo8ZG6li0f3b3esJvK/4nLXPBezMbg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBsTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv5Q//TPXNsBIF8s0ib6UGbvsHjaAfFPc3roujVsMW9DPUu5u378zg\r\nwTBz928moewX3QAkz8LIfaILx8b05PPPcEai0+qFX0+XxMVOtrH4UPP7OMGp\r\n5b6WhusO1cNGw+8OrNagxaet+45nySKj4iXCCHGAy4MxL8loSLXEs6XgIemd\r\n95KpC+iVRZI1u79jC4nH1iOkupIOH/QcnIw8og+x7t8Gx825r0nqpouHOojQ\r\nrLaGvxZmG1PB5dh+IUVdU7GzaqqGQwBdPPHapbiCOwgX+7K5Q35ddpeNirMm\r\nHeFbj7WdLElNw6qicVUfCRMjTyejORfilhY5T/7qCjJTEzjko3hs+8w69mty\r\nLjG/t4PjDkhdQhuxw7E/DfxoQFM4B8QP7VkJYJ9qD7RYOsxEPpnLecBGp5+y\r\nbsCSG6fEz78hNiBRWyQJ58QYxHmnBEfCrVhxnaKMqT6Ius/VCY98PHjk/wXk\r\nEkK6Ug6BUUxsWXq7waZfJju+1j+J22c+QwWc6xbuFeFs5GHV4vtsYQw+SJ9H\r\nIS/lXM0EKABoKe/zH/7gQgiGhrH5JjsEfqIUZUqK5s5L4g536kCNGfmo7sFX\r\nnvAzu7hGikuHtwMZRKvRyxlWiytWM8RBwb42UYlrYyNrhZVuDxyDCeVKq3K9\r\n+q1lpxakpW/poz/jW8qOi0YJyJbvWNP+luc=\r\n=oxnT\r\n-----END PGP SIGNATURE-----\r\n", "size": 3709818}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.1_1670388498727_0.8040944126870202"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T04:49:38.785Z"}, "0.16.2": {"name": "@esbuild/darwin-x64", "version": "0.16.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-nq5cXgzbXHhBqZEPpuXrf2+BV6QWUM8vAyT/ElJrdIkoGOHwNQJEqZHl3KOWK+1V3KXEXgJhh7DsLixIc677ZQ==", "shasum": "d2695c66fd8a4e17a2f7d2c9f2bd6babf1c9c161", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.2.tgz", "fileCount": 3, "unpackedSize": 9104151, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBjjiR0WrYz9RkDG7RcUBpWU4FK4uBWxpDMEGsApBzHQAiEAoV7gzyFSGQ9OVJZIXYqU+4y+rkVtykPFrpbE0BffAwY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYttACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4kw//XiDdLECS3jxdpuIZ2OwPzdYfTyPPNihIwEac5rYA/XnxoO8q\r\nwl8Q7bxBa+qsRJCCQvW6mVrHDXy0BQfPp7L7uP+FQCrP+3jrEUNiXJs77WhW\r\nt36pZ/r+/qEmWlx5qGQgt64zloxInVT8LwhjEv5JEeKTVAzGGs7oArU0zSAR\r\noxmPnXiFgaGJ/6KQ050p6I95lBE20SaHKzTw/+fOKUsr5u52TKwdLwChuUBv\r\nx6Qh89rBbEzXMaexlDQEf3ZCuBNmYP2Z3D8sTLoWu92+dMrXEOMFMo4R3eHD\r\nfXqu+YqDJdV//ppZQz3zNT7RntxOA3xAVghCThwGRnQvSrTNh4/odwA+MX67\r\nuYTcEFtg4VknaAvMxHDXTfWgMR3JbCErSlHpypsumggAHRhq5dcCVjVkPBYN\r\n6UU9x1bREd08q54sRGuXeSJEup6/purp1Ny7ppXB3fjcQjbIHVcVm8StU97G\r\nyVbuoaqlVeCZxVdn0M8prSX/EMlrr95thVt28lMMCDgS0aNp5wpR0N9YR3E6\r\nr/f0KdppvzvZ5WPLvMjuuOjuhSd4HV/SSMKkmpexpfZqNW3XQOCpvnx4Ni49\r\nGofOT+HkWqiu91yx96btVp4+0AfLzTyA1yp8oTqzL+ufxi6RblhsIpi/9/pU\r\niqWayVxRWc4OPhMKAuQy4vhTK4p8a4lgHzA=\r\n=sGlA\r\n-----END PGP SIGNATURE-----\r\n", "size": 3713236}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.2_1670482797122_0.17338867163333638"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T07:23:54.631Z"}, "0.16.3": {"name": "@esbuild/darwin-x64", "version": "0.16.3", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-uEqZQ2omc6BvWqdCiyZ5+XmxuHEi1SPzpVxXCSSV2+Sh7sbXbpeNhHIeFrIpRjAs0lI1FmA1iIOxFozKBhKgRQ==", "shasum": "7fc3570c2b16e9ff4fc178593a0a4adb1ae8ea57", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.3.tgz", "fileCount": 3, "unpackedSize": 9108343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhhMpcPAqNG0hKymCovjKjHwB2Yyw1gvCj2PtkbfkdbAIgFWXK2iUiRgLKjj4VrwSkDoKxUyCHsYcikRhJeRTlDx4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6Ew/9EydhPVeZUqKok5IVAUZINlYLXtgRyi9/n0awnn7xA1Tlvgrl\r\nMpQV3pRmKAeM+X+PucZiApovDITVN5zhoigq3vHJXEqaHmzUfGUobB+I/dXA\r\n5rerFTyIalPmSakWAkRDvs2xi5D4NH4x6AR7R9uYe5X9jDRMUQs2GdkXIeTS\r\nRqmkAKhJej95VOiU5qwVM5RP0aZt7nXiTfVSsPBIWNXA2JY9ZoFuRJcaQofk\r\niIIxY4IsILZAWh1Q2y1nkx6qtUoNtBMPXFcjUbzncWPXdYxXwcP9V/7L3sa2\r\nDicGphlffXD/0AqFhQXt4df4p0E0QIuam0HjRAHG0NUhp9+3HDYJkonnnW2f\r\nlq6BTQNPtPqHFnJwmHSL3QzqY4Y4DLNRPQMbX+xe6o5WKzaWsyMn3bTC0PqI\r\no+7NH6dieSA8Iu0S6QAHd1aAGhZhokUeS2SRhQRe84d4c2V56/TYwBJR/I7m\r\nnCVPEFFOf8PVgVa2CmjEGVDGatnXFIsHMt+cXLkHKR/S06joLQoVftzaozAV\r\n8v4h2UeUKsSU+SvzdaMA43/OnfJb0D2/N44/kTdKd+Wg4FRrdk4pLXrJLiwq\r\nBxX3XLRuK+TZsxGdN9q6fglyfZGC39cTK1upLL4c3ev3scqRT8lGbKKHy0WJ\r\nPRYR1aH2RNPswxPaQyBIydQmrYTdUBWbiUc=\r\n=FVrv\r\n-----END PGP SIGNATURE-----\r\n", "size": 3713440}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.3_1670530394648_0.511840492390349"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T20:25:05.297Z"}, "0.16.4": {"name": "@esbuild/darwin-x64", "version": "0.16.4", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-e3doCr6Ecfwd7VzlaQqEPrnbvvPjE9uoTpxG5pyLzr2rI2NMjDHmvY1E5EO81O/e9TUOLLkXA5m6T8lfjK9yAA==", "shasum": "b726bbc84a1e277f6ec2509d10b8ee03f242b776", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.4.tgz", "fileCount": 3, "unpackedSize": 9108439, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICRYjHEAc8q/A4LBi+mx7cVwc10rrkR7n5lvrLeI6T4nAiEAiZ9NWKSDplu0dPrqTgqtAtLKJxmRRNMhO3J0QWip4rw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAISACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq82Q//Qp5s7cYkULykCjWt2M3VzmVariETzoJHwFjwQyjuBwHui4i4\r\ndPaOpekV6KgcOv4uZVHV7B8U7weNsgK4mUoVol4l3LiERd7q+K59v8DIirwg\r\nou8Z7sJnoGu1+eP1xJkHw9ekH7W2dc/j9Wpw9SBGuTiXrWM82hL/6NWfawMx\r\nhVOtpBBVVDYRsB8fKQDYuI4HrjM+lVoMuhQAjIAOGO2k0iYaY0xlUhXYArIq\r\nqUiIEo4xMmLOdCPqhDsugOpPjjBC3/4Nz4N1U7a+mNGMl6M0fgy4iFwqky2Q\r\nkRB5TeHwzZutd3xXkGufsMwYlSICmmK5BzlR/gC4H5gSV1cZA6dZfnCqfJC2\r\nTnbcu3k7qGBeX7tSQHt99NkD1UTX64s6UVhQZQRyIlHLX4dHiXApUBfZ9/Es\r\npXjz07ODbYfguG5k4zUZ1P+odc3VXzXb+TPBXANAxqdBhN5IxkTlyXY4xV3O\r\nFpgsjq0GmPPjdPhDb4NtWrvNsCGhqCBgAf61iva96oQnNrJIm6yvpcE+44BK\r\n2PmnkO7dROrU4sfHLSmgGZA7YCqjbmcvnTwhCcQYSpxoTrYtwhqAll53I/jj\r\nOavXbZZbJU3p2e8JvHjTnuUyCp4kTIpHMgCMv3mnP7UuBc1sPoTxjI2BfmU2\r\n9bK9Lin5AAIbUDpGDTlyT156PyT3/46kWFM=\r\n=MPVU\r\n-----END PGP SIGNATURE-----\r\n", "size": 3713316}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.4_1670644242514_0.7076720880083245"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-10T03:50:50.405Z"}, "0.16.5": {"name": "@esbuild/darwin-x64", "version": "0.16.5", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ZDCAxAPwbtKJ5YxRZusQKDFuywH+7YNKbilss0DCRPtXMxrKRZETcuSfcgIWGYBBc+ypdOazousx3yZss2Az0A==", "shasum": "a9c466dbeca614edb5fe1a9c1ccd97bce30301af", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.5.tgz", "fileCount": 3, "unpackedSize": 9120727, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEIegB04IZ87FuwbGUw9dj0favCiWYXlaVxLQj7kP8G3AiEAgkFumgSfOiIwRF47v0ZHsJNPDBlRMarGJSEQdNfScTw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLrDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNXQ/8Cr+tHf0Fr0g+1ezS7lEFi58kaPY/XGJ85TeA73hH5nC2jlOi\r\naNF6cqyl7hBKTJDO+GsILrZNCd/93ahTt51nfzpM2PZqkLXo+0ND4l9ewL3Z\r\npi7Q/XdukWXBuI78PD3xDQSu68SIv65KJfP1kn99LA87U5KdrEfPXIqwg/Kl\r\ncvUDqp+RroOXHn0OTEiiIjUmEtiFsj7/f5Soa34NKBXjjU7bUcW/RM06Y9z0\r\nM0v1eYXmYBkr7jk5T/6VpTWHLHaWUkEAwN2b6c0/RLDmlPBCJfVSF0+wi1ob\r\nbcpCbqH5TeihAzkx08d3tsAs94giwsYa1Lbl1tXZ649Pckq5IQA7UW/ITrTr\r\nqbRMbuOSswG3R5338ljicPvJhQTHRXQMhi3wTMYwxoD+oZXUiYvB/Xr9T8no\r\nmBxaGxDY6/t1H0z62WxX0RkgVdoWAPndQClOeohV7lLXNl15YfWBnN0qbgF2\r\nGw71O/IkAqn35WtiXE5kBB+LTURuETWvHkdhkcqep/qRJj5gUrVK7gypS10F\r\nWve9uXD0gWxRSQ4MBYy0zp8upIzquGJnpkPfzDx5Laj2aXL9AwQqdSZU+0Ug\r\nsDukq/q3hK+xVGFyHyrL5HRrLLswTHciaUKVCsFzNtEEMnHVlQ/ERi5R6/ZC\r\nO3tntoxG0KO/AG7HozS7nFFF585p3hdS2AU=\r\n=HNpJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3719421}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.5_1670953666911_0.6177214590012825"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-13T17:49:14.078Z"}, "0.16.6": {"name": "@esbuild/darwin-x64", "version": "0.16.6", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ND/o8hoEpXxIOqhRbt73tyvnu3WWA8MeuMAVww0crdubpzzEevH0S8r6uRjrHn1H4etRSmWwTbM3rHul68BJOA==", "shasum": "873b80540e5e8f0d1b87e2a48e7bfaecc097bfc0", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.6.tgz", "fileCount": 3, "unpackedSize": 9129015, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBEiK02ZVgGqFOh7HW7l6NYH6KLX0838tnLct0XU6XIpAiBr357HVIaFkM327ensBrjDAyeJ8+1QZ6C1gYhYrKJb2w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoyaw/9GXVWMmMMBL6v1XOVs0We3Q5qk4O7CiaLQykNQckY4rP89hxD\r\nZPX4Hp4IMpFgP5Mx+YeU3IgxyZCtyO2KqTjF0J5xTIkP0+7ABzIowt7+pMgq\r\nY1bwCsTXco7KaLuX+bmbtRO1jgfp0nHUv1JF15qc6ol/CR1cfQhf0HyZrset\r\nAqJno8c62POpvJR8vSaEzKFQ/prmKVxNQfTDLX0afEW82Hhltt/G4NMv5G9A\r\n4KnWRiKPuMOqW28MQ/75wA5dU1Ks5/I5n6cH/Dfn1OVfAvJDZg1QypBPDs0R\r\nWZJLvFu4Ri0xYz22uUJeb+8wvteskGBf00mq5bXIW6ASukMScrMcX3vnmYKx\r\n50cBtsd5iAQ+BOIMjybbHy2+WeaoK9XkjIIgJc6ac/iJ1iq68Dawr2+mxiLK\r\nq7AssDy1ieqzvRbnjrQBZsGHOPn+kS8d1XUTySVRaFfEb8wGcWbaqbITY3ga\r\nFJnQMfokyeSL7uetVaj+eZvJvBTMs34j9gVuJuVemH39HaKF6wBXRnIW2PdP\r\niGf0aSUJ1laWl5T01gkkBHFjzPcuVmxctxgek+iTlxvddWbA+B2yazU7nnrZ\r\nwMn4buWa8GbgaOa7XPZF3RYZ5e2lH+UOCN511Hu3zz1l88bP8U5gp3DmmqqA\r\nk1eLnFPDSGHVYpQMnq0f45qF0JvGV7U0wfU=\r\n=+dZf\r\n-----END PGP SIGNATURE-----\r\n", "size": 3722930}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.6_1670995400050_0.4002714900980364"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-14T05:23:28.630Z"}, "0.16.7": {"name": "@esbuild/darwin-x64", "version": "0.16.7", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-duterlv3tit3HI9vhzMWnSVaB1B6YsXpFq1Ntd6Fou82BB1l4tucYy3FI9dHv3tvtDuS0NiGf/k6XsdBqPZ01w==", "shasum": "2206042ac4396bb18dd53b379df83bec47eeb5fb", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.7.tgz", "fileCount": 3, "unpackedSize": 9133319, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjBKbStyhX+GzF9TH+b7S1FOHlzbqZYVgfos3isjAGMwIhAKF9z/Y5xPfKEy8Nbq8p3La/VQ1j8BxesvHz+TSjt/A2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopBA//Xrg8enqvpAplHvZ7zuzhPaSibGpG1uLJmy9lf7J59+8COtMi\r\nhNFvqTlAMk0O9+x9texIx9+Dj4bLY510FYWe63841/yrwN4jMyiu84Ih2bBv\r\nMsNF2/HIIx6DR/cltY9cnRx02sZCLeTM/aQfYjZZ5wJTCT6ZxDUkya9Aqwbo\r\n6hEyUUXeidu7yZQaYCkn+e/LQfXRqJqEjY1zSXsKtu7Jb3aimDQ+PHpsoOb8\r\nPkvGGowz3x4DyEht0tjoQN5YV33xB2d047G4xCIKKEosrNb4JpGz3mH3O67u\r\nIZY6mj0UcH4PrNzOgxyma+TiG5dnUbC/KUGay1BfKiPgW71S+lHrQE2S1cw8\r\nYW1QnIfLt6xHVbfE5zNMsW4I8utzf1OMTvATlclXPLv1dqO6sJ/fR7ZjK5+8\r\njjXw9jygAFuehIGpRokfckvgREYUETBsZIIdxTQM7sJudB9YRUED96wR22c6\r\npf6YWW9Jt6JIHpX9RoMGpb6d5aaL+M0tS2BiXNDKEPQEf108loRXgSzdS09M\r\nfdsz3HwxXnm0dNtDJztXG2WF5/IrA/TNCeH2HKhKzz9KCjnj3zwTDQHru6Gn\r\nRcPi9XbAtIczzBh8DfMBn8fgsT1ecL3Ir6PW9ToQjT0nHE+Xi9gZxUYCGEt7\r\nJHPTWws3JPvRGb/t7SQRGJ3/3WP60G5g4cE=\r\n=QcIw\r\n-----END PGP SIGNATURE-----\r\n", "size": 3725323}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.7_1671058020592_0.3332365416873184"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-14T22:47:07.534Z"}, "0.16.8": {"name": "@esbuild/darwin-x64", "version": "0.16.8", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-XXh2070hatspZdG/uPqyHLFlHlGbytvT4JlqZuTU3AizcyOvmatPBSnuARvwCtJMw30wjjehcYY8DWPZ5UF2og==", "shasum": "a1de180765d63a247a892c3abdf32cc9497f55ab", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.8.tgz", "fileCount": 3, "unpackedSize": 9141511, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKO+oY+cvMYoAixl3GGTXvfxqAWHRSyvyh66Mqr3PzeQIhAPO1bh5pGFqZNEKbzG5qLdgsGskqxAE6ZheUuWtxA5xP"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQGPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqspg//WPcudZ6KFMKQibty2+K/XZtNZGOrU5EGdRCMKxRpsZK0+eF/\r\n/MmIy+dFfLmHFdmZbGTXzPtyDKISCFOYfKg3QViUEEgEa9jhbjlJDMbBiTsC\r\noUVL/ymlcZgb8AK7Ep11q7KwkUsRJsBsLmT2JKkatJrtoA58RzYg8gOAKKou\r\nkOngnaO8hAE8cKOJv/d8FvIJXT7kfMjvbMrtugippzHdySmoaYB7ikroLd5s\r\nZYrF/5AgCmC/tbN6GNnhGoksbKhPG+kBWraEoQhp2VL2QqMehAiSIeri/1PL\r\nic2jUPrv63QfSO1UpdgyGkPc0YzUmAwxmV2ZbNnsEoWvX9BsqUbPNn5L8Wcw\r\n/6NLflhzVA5VqP5/BnIrLpUHYdQhPq0E1/jA0lApxq3YCoc/5WfgY0BiUa9U\r\njFcs5TiwKOX+TN+tnCU57gwCa9y51Yva0fL0gf8kyMPqTTzd+NThSGEwveMs\r\nvIkGYG2b1AyaIbultuKDGQwp+ZG/2CZXyUFY8RdqQYAjcuZP8K8aYEO/dcbg\r\nrC3+LETLW+BrSIBytK0EiHVNdJhQLMf8eu8DpkWVBYyWn9+Ltg19OKmeR//k\r\nUWYRqtI/DcRqeHnSYHzuXIU7FxJ4H3XZnlSXTIGE34fV4UmY4DsnkLVcxo5n\r\naiJXCIvbt0XB65hTKu7d7mGnSNG5t7J/X2Y=\r\n=bg9c\r\n-----END PGP SIGNATURE-----\r\n", "size": 3728299}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.8_1671233934784_0.7552415519297402"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-16T23:49:00.488Z"}, "0.16.9": {"name": "@esbuild/darwin-x64", "version": "0.16.9", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-LZc+Wlz06AkJYtwWsBM3x2rSqTG8lntDuftsUNQ3fCx9ZttYtvlDcVtgb+NQ6t9s6K5No5zutN3pcjZEC2a4iQ==", "shasum": "5a19c00781595e0dfeef1826b3512d04c37b98ff", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.9.tgz", "fileCount": 3, "unpackedSize": 9145895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrTqPJ+5pfHEa0jc27RmN0DK6D513aOrM2uJwXMES2+AIhANsPfCuUrZSLu1e2jN9GuMrjT2blbXsKqQcLTGxjXlKv"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpelACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdPw//a/o1K4SH1nk4hCGl3EMnIdQBskYrBS53VxjKjR6tkixLTWjM\r\nhk9m2Jr3tSFnKXNz9wn3wdt5tRpDK4EfU1dZIDklFxMYK3T7cDcFgL12+FTP\r\ndpXdOojZDuZlQQ0Air/U2dvKTmOfv/wo13TQ7L2NN2CldkY3TqHFm1sGtdxS\r\npyPstbDbZKUdSEFgQvOqhUrgs6TfMDDKJXVivTOlr5oylDP4hGKc2hiX8pL7\r\ndox+tLCiMZRyfYBhvTaJA+o4H05HhuI9l1kpjKVR6ZGVdF4IRwAXK6yoEyIS\r\n2YAw/4O2KDTJ2RIrZWDymcEhh5os6j6Y7uCG8qpLpOxdg6mk3OowkX+lMqai\r\npuQFd6127cfzmXdCIrS5MGVNiVlELc+vBvbKW3i7fHLzPWaCGQ3ow8TP04yt\r\not3lIlxshlIPZUXL8Pi9eZQ8MoXGghTeID7nSI6ZDsBcSQkp+Q+XgQmIB9gM\r\n1QSDN1U5BMfsJB4bVPp2W32932Bj8uQzFvMPdMDi/2Tf284TOoAvquigyeNf\r\nBG+TcR+2RlblZytwofa/RFv07yVikocrXlBsUVAk3e9f71yisq+1kG5Q7Mby\r\nf0Dv4Sr1vGRbgn4B7hsTkCiPrX1J+OTj8rGSn48y2/A2zC/mfNLUeJBnBjEU\r\nd01E6cGymBMer60Gb7FFdUFhQESCZrmPRG8=\r\n=Mb3l\r\n-----END PGP SIGNATURE-----\r\n", "size": 3729841}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.9_1671337892719_0.8663988649759995"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-18T04:31:40.379Z"}, "0.16.10": {"name": "@esbuild/darwin-x64", "version": "0.16.10", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-OXt7ijoLuy+AjDSKQWu+KdDFMBbdeaL6wtgMKtDUXKWHiAMKHan5+R1QAG6HD4+K0nnOvEJXKHeA9QhXNAjOTQ==", "shasum": "7eb71b8da4106627f01553def517d3c5e5942592", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.10.tgz", "fileCount": 3, "unpackedSize": 9158200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxtg8ojLEp+Pr1YFat19acFVftBWkjhvv2tzYEAz7t3AIhAPFe+rtIY4OZflK0aai+q+GK0dah7au0QkDI+8lLK6iH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPMxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEuxAAi18C2y1Y+dOfHzuLHCJkqtzIsgRV/bGg/HJhmZ9T0153Y8h8\r\nuFrYTWNTOsQCzqINVQIzWnOPAvpePuOi9bDil447eutN9myutZKD6fZwZ/um\r\njVwZ4V8cibaK8etS48Jtcs2y8WRDZHYytvIoTssHPZtcC5TKjRuxTRq62aYX\r\ntmA3Jm7qO1ku1dd1xRD0CNVSzl9G8zNKKUHk+LeDqfgmS/+OlIlDdmXj+N3n\r\nkfGkWC36FVmTj1xXnd/gWRto0A/Bny8TczYNVSMPDy4eN+uY2QBXOZVvdjFx\r\nkQk+TsgzAuQYo5n28UO1z4uIY6ssrYgASe1s1FvN5ALa39JEikv4Wb18DPuu\r\nwex9aHqA7S8FVSVgrdbJ69tidtFaOSyhbULaPrj4CuslfLgBPnzGe3qs6sN/\r\nb3dtNHyOeDsLJnbhwRyqMYKBL9JLU7kBVIgBKtpuEGc5EgIGKtjb8isQQ7FD\r\n/Z72L5BLeQJRnUaX8pI4eAJ704n/RUfxcGGJCTq85ilqVdLUO3mMMncQ03x7\r\nnfiuKzzYXHpAP/WZpM6GWns7P/vNthDvxCANMsWEkYe3Ocoj9TzWuda91ADy\r\nYhMxBRtS/40Ioh0PwlEiL8TdVjtGkVlcVWm2MVNHBGvjzLJPqz/aZAU5d3FR\r\ntPKhWO6zcPDjaoOIX8ArpslG2X6gKibl8Ac=\r\n=/JXb\r\n-----END PGP SIGNATURE-----\r\n", "size": 3733368}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.10_1671492401268_0.7135992337346595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-19T23:26:47.721Z"}, "0.16.11": {"name": "@esbuild/darwin-x64", "version": "0.16.11", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Gqx2/nYqnK46dwEDPGv3SwLqgLIZQJ7m2xNoNRzO50VZPvoCWSUqDaoirrZZf7uVfl+fxHoZBcdQJx2gOdxffQ==", "shasum": "f3249b1170515d123aae1f517ac38f93f9b90d5e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.11.tgz", "fileCount": 3, "unpackedSize": 9158264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDurF9ojFuUs5ukWPzVusf7AeOkcPzUd8e8Ogbkq8i5GgIhALBbf3tPM21x8hDucPHV9nwEwQWnQ/9imFX/XsSW6Ydn"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTOxAAoGUIKs76wc4FBJ3o+IFA7c1pzOEPrrAiv+tB5sf2pANqOg9K\r\nhh3nHPxw3Equ5Xg0WfTPybZ10dD0zmjuzjFR/iQIH52SPxuzxBv3HFY2VLWB\r\nTzpgSxb6/1M6fKhsWhqamymrZqClXc7PVLXLUcwqDJdqQKK+Ko5FqdtDfkDn\r\nT9gOTCOEStHGUBzw9nQP33lpuZ8IiqaHeKO2AjhwHhgF/BzAyPKVZ9o7d21W\r\n0SO+dfVvCg8o2zqieyw1OWDqpCI9iRErhTK/ac0ku1UoIBVY5Gcq+YOb5yhD\r\nY+oCJsmFX+ObxkqnWc0fygUwEDxOy4MsogjWNaTFiyHTmCJ9k6Txi+jYMVgK\r\ndu8+/lbC0k0owoblpJE/FDx7zbUX0reQhUfAe6SkeahIN/Z0s+E+6lVLtoJF\r\nZ36Go0UwBKI3bNE9cebz/7RgCdzbl49a5ibyfry+eionGDpPvMx222VzmwcL\r\n2wW0KlkVtL7iLGQwoT5tcZwDdkEduPpMXh4mSXkH2lR1mLdnxEtuWZtAOEeL\r\n4iqkr0tDcYpvrJ8iwCudq1jtibXuaNpghXAnsYUHJ5RpqS/58gLj0vawjswn\r\n67XosokvXYObavQSqgXUofKL3/H+TO/fXtWPNuILvZANH2fvX8+lzcjmGE07\r\ne8krqaaGvH2JvIonvhTYHp6MK7cOR1fjENs=\r\n=/udu\r\n-----END PGP SIGNATURE-----\r\n", "size": 3733821}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.11_1672105155162_0.7912531720520961"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-27T01:39:25.905Z"}, "0.16.12": {"name": "@esbuild/darwin-x64", "version": "0.16.12", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.12", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ApGRA6X5txIcxV0095X4e4KKv87HAEXfuDRcGTniDWUUN+qPia8sl/BqG/0IomytQWajnUn4C7TOwHduk/FXBQ==", "shasum": "3433e6432dd474994302bcfe35c5420fae46a206", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.12.tgz", "fileCount": 3, "unpackedSize": 9158264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF+k7KbLES3kKAvkpjySmdnRFDpQT9t7nMG3x0bltpRmAiEA4MJ7gv/+FYq9CaRTHd+ZNbCSxbC5YDrF9cLEScZJKcI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6RBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHxw//UKS88tLSGf4nD8jE2dricYEbC7Pe4giI06Odjj9k7aWq6LnC\r\nS7b+5IOKeaykqiJt8TxlzVFDdHELqjdDj/dblL3OKIYiZvSPABgnGVQYi1br\r\nHlTDFPFsPrBXdTnvNoSXqttwADvGZwCNOb0rSSvoh1YdHZ3MJU6MHcENwzm9\r\nAJ7mQwe1FjuVgb6l580fYERH4pH11oHJPz4fMiibyJNhvid/Z0qplbre/K7A\r\nuxuEqep4W7bRYQpwHrB6Y90ldtMMReln0YW34i7NTGiQTFMTUTFqpPbpz00C\r\nP0weiiQH/MrIOAcsGBhJ8HdSPqCHDsWRyyzQ/VVq56lkmer1za5Q1krKlf5y\r\nH+4rlL/JSkv6EEXyu+a/D7T+g5hfRnzUvr9K0h7RzyMPNWPfRQhXe4XNwgX2\r\nv3Bo+YMu+KSmLWKBQXz/jl3zw9HXvtzQXzK74LNBFSbzi92ZSnfG8OzOaHWA\r\ngoMlBifHXv3UlroSPbzZmQAGjshjfgnsli+c0+yWzS4fBqFBPCLP+5HqYPzB\r\n4aO205EVmYp2yXwSOxsuMtKiTGX+bsGTnyBBysRRkraGrsCMkgCc4A/NQxXq\r\nZaBLlAYKb8xstOb+KWrKLaO6wDLcWfVtsOciM2MCaUlF2s7SGpcSso8MiP4Z\r\n5brzFcaI+C7nfw2stt6TlaR5aaEDV0zBzak=\r\n=m+D3\r\n-----END PGP SIGNATURE-----\r\n", "size": 3733908}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.12_1672193088819_0.163995731731998"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-28T02:05:26.971Z"}, "0.16.13": {"name": "@esbuild/darwin-x64", "version": "0.16.13", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.13", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Agajik9SBGiKD7FPXE+ExW6x3MgA/dUdpZnXa9y1tyfE4lKQx+eQiknSdrBnWPeqa9wL0AOvkhghmYhpVkyqkA==", "shasum": "f1a6c9ea67d4eaaf4944e1cbceb800eabc6e7e74", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.13.tgz", "fileCount": 3, "unpackedSize": 9166600, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFjHtHYsTCLJPmK5yDcVsDtzoOIiiFjUFLhiLgNQENKAAiAaqT24ZjAThMEr7z2g4xnhm9SQ4cpftt7V1UtdMT/4rA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmopaw//RShrMJaW4dyAyUCpwu6Z1ShoPucj6mCFsvFfvezIsYv7gEGx\r\nbZdyMWNEOx3if8hn3Qe4EQXBww00z0A0RTElzWyJjJ+SlW8qALz6N2+QiTk5\r\nXDCTr9LLoBSTqBvKpnUiz3hIw88HzIZsDc6kU9CuwbphX0SvaXcG7QtcT8Um\r\nS50onOKHUWntzF2bkUxyCqqio0JQiMhJurkpWujLhe7EYaLjtLQ1eIxQZGrS\r\nPPyr/LXQNUgNHIpv7aj76ww6y3mfP3VrFYpkbuzhu+TVTciVi4UPir04XEqq\r\nFovZbgpoFu8qU5oGbkOzDoZg/oltrXHBQdheNJ9dePQbFPvwJ0VPBMLpqgro\r\nRYIMcmicy7FTi1GxWWbZmfaySWieHcVjwAvW+oRuRZzFapecFlzARywA76hK\r\n+XKGXAckYpmGxO16JBYKOcSpjeVp+l1Zp08voQspNfQWXUyqxZg1gnAPi1r8\r\nEqf7fqxXbt1KMuUS2nE8CRbnGv2QEZljGyMnZMmDpisJAg2lgoGt7E8uwzsp\r\nA5pmgNd8Ww6MgiE76r5fh453wHQDFILa0i76/YB6l0AdW86KNaEKK9v2Oitm\r\nNuyuTtRGGJei7hdF2U+zgK3L7QuYRvjBi8NWlWfRhLM28PyQs32j80WodvxQ\r\nRaAl75fPyp8olsORPUkNge8TcKYmq3YQC3o=\r\n=kIQ3\r\n-----END PGP SIGNATURE-----\r\n", "size": 3734683}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.13_1672700246927_0.20617311380671333"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-02T22:57:33.782Z"}, "0.16.14": {"name": "@esbuild/darwin-x64", "version": "0.16.14", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.14", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-KV1E01eC2hGYA2qzFDRCK4wdZCRUvMwCNcobgpiiOzp5QXpJBqFPdxI69j8vvzuU7oxFXDgANwEkXvpeQqyOyg==", "shasum": "519c9d127c5363d4a1e73b9d954460f798b41d2a", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.14.tgz", "fileCount": 3, "unpackedSize": 9204552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVFjR5HCMXf5oiZrY3GLnI/7QsfjWZ1v3tDx+3HkdCRQIhANFvFPER0qXmb3mZzWFzbzeqCZV8ffbMk4VUl0kDQ4MM"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBqhAAk8QOhEDAPn3voWIvylc/SXbk11W4dLQdPZp50CAGG1yIxiIy\r\no0Sixg9HdKLkHdPTWFycBbu7dfN4gM4fMgfq5v+MSHfSjWa5ngeIC62AHfvD\r\n9Cu5AvX4DYfBcyUXpL4UrXOUnq+4lE65NzYytgPvtLorlkHO8SbohPP56NVZ\r\nuxqHwHYzwp0Kbt+GwRtzMINmG5zuVToDNOQu0+oEPC4yTAYQS7CjA0WIr/OA\r\nMaWujUfv5DRFYX0FdEClX9h9VAVALwql1GKzkSIvATcAPWISi6f9BzF05Zav\r\nGLpTfJ7CG7qs6edHwfZsy6U1Qv3Ke/YkLJ/oB4k7Iu/dDnTZ3nicMurHCjmG\r\nvDbVIJUE30PACFeeekhx2qnBZxhbQvTs5wcNkkwKNlPxn4EPDJAyMKjlPmfU\r\nMTNhr/mXUF5zmrsaGHWdBfwDFh+DroKZxfLdSAoJLxZCh2qEds58R4UPdjyB\r\nkgnQRSGdQC2Uf7QxiF+7vSb/EN1L/Zt9JgU9LLbRqpFCi9k4TekOAeNHoEWO\r\n7KDfSquoBZdTIqPubBhSzhN+uHQ+eYzjx4mSA3VmTT/0CO8RBz3sKnNxTwAR\r\n8X6AIA/Il2VBXHcpuIwMAfmM+3WmKqCdFSpf+PI4HsI87gHSWhUfEayds5ED\r\nxJEHPGlwD+razzeSW/YY+sy+g2gUHdmS/TI=\r\n=9+Ex\r\n-----END PGP SIGNATURE-----\r\n", "size": 3747203}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.14_1672863189429_0.566501433043344"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-04T20:35:42.902Z"}, "0.16.15": {"name": "@esbuild/darwin-x64", "version": "0.16.15", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.15", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-qqrKJxoohceZGGP+sZ5yXkzW9ZiyFZJ1gWSEfuYdOWzBSL18Uy3w7s/IvnDYHo++/cxwqM0ch3HQVReSZy7/4Q==", "shasum": "38ec324a3653ade5acc5c190a7a27185caa6223e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.15.tgz", "fileCount": 3, "unpackedSize": 9204552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIELuegxM6hO86k/TYF4zgxQ8UajOQDaKfY3O+oRyZDkOAiBv2og0I6/Wwx5HLXb7N2ujdjO7BAQ2JD4ws5no+epNxA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPK9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq30g/+Or6l7cDIJql7r5Qe/lwyiwprPQ7ZfEbLETsq8sKIRLDMUpTB\r\noaZdFfO5Kx8vR7hnYE370A9k3SvhFzYBaJM/xqmHdWKP3dlDUr1MJfLmL9t+\r\noZ+MjlBV/0NnE3jFWbzhb2vZsEPHGV97QqNaSad5v6w2Y0GnVkHlq+BXlx8X\r\n7GeAPzRfrbAThutkOGati0lQic1Ic0XwJ/G5ggkX/yBDd3arD8smA/LatMVG\r\n4aKwpZW3PSXsX4VZCHKWoVO/wkpZ9VwOAGHyKkj41E8bEN9znjk67btmNvt8\r\nXgbz20A0TmBFySE3uTvjP7Apdb2O8iF0jCgr3repJjoz5C9glUXl7SmwB/T/\r\n/c+TfoMSlQwUaLndNRWOf3JnI6r+doFb4zGJMTcIVRSPFC5ghgv0rgicFz7P\r\nLlxanEg87+xtr1CjAFE7Ktm3YIh48CsO1S2HTjiE7/bXDC6gKB3k+HLOUjYx\r\nr76SJtXUeJdOHkJGYrXP6ZzyfGR7RpNn2/qJWtHI4F0O26DPgCGjA5B76TnL\r\n924AEj0gsBnxMLhG+DSYmLXpjT18AC4fLYmgfqyTS4U6lxX0mHao6oxyWqY4\r\nKKuavCQSsDQmLXO5CLN2noDvJjGtrKt3FdB3Kgy9XAKnT5U+HX3K7knqB9db\r\nZFPMdBRtgpmM+SRR5TiopbyoducwgOba+7A=\r\n=/36u\r\n-----END PGP SIGNATURE-----\r\n", "size": 3747631}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.15_1673065149633_0.41048340856751864"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-07T04:19:17.143Z"}, "0.16.16": {"name": "@esbuild/darwin-x64", "version": "0.16.16", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.16", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-CYkxVvkZzGCqFrt7EgjFxQKhlUPyDkuR9P0Y5wEcmJqVI8ncerOIY5Kej52MhZyzOBXkYrJgZeVZC9xXXoEg9A==", "shasum": "e9756d34cd9b3737a5354e89ca0fdca32d8df64c", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.16.tgz", "fileCount": 3, "unpackedSize": 9204552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmaBYKIGeRoR/W73LDAXoLOVJwKJ1mMBNvJTcwUBLbggIgBl0GR0Q6pKJn8XlT35zb+BF/XcXTSgpivt1NX91ImFg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0ctACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoawRAAk2vwunv0O01eM7uxSUIUdskShpt0wpDdHOCqlL7knaIKsVTF\r\nkHD42iQax31slUfmz18o0zcxMh9GEjjE5H0ZeNxbk6x+On6B0IOMBp0vHcHl\r\nI9v++XAZsbJTIz2A27qBs93+jSodiwc9Dx2pKfniSgrGOO9aHe8LCqhT36a8\r\nwQKNbs0UGIBOcMuVxH99AXDcFU5mD6GztUDe8ElR11TcvPNnhSDoGMV60fJO\r\nd2tOtX9DxyrxIPVVTjmUGWIqg5MlsbLofHjVkX8oNzGEshTCMWMPIakYswmw\r\nH41qYZq0Q9OlSesyDMpyaKwtPDIqHP1eF2lzrxEfdk/NhT857P6umuFeokDz\r\naagesI6Hat3hXU6iZ+lW/nHhfXQBCDuW0GNUleqQdprGgD4Y+PCkEanlTOvl\r\nANb3T5tABhtoY2vI3Dk4qfOdaL1bCLeyeKHHffWikAJblBXTjxRmsIgIQoVD\r\njtazMQcwN3iYxh/h+/cORZfWdI8JbAPrEOFEyoIG3HiHV9mTqMNezJJTsDxC\r\nHKfnETuwW33Jl5TkoxyYi5zO9WGua1+HHsRfM8pO3DRmtI6UfbR05efMlGx5\r\nbCVg/FMR15kWyTVcY6ArypdWxK2+z2KotFeNDZxbYyPYgOXdDMyQ8YZI5E7L\r\nCGSXzWtBEbM2DaGxLDrwg5Rkta0ZviLRmf4=\r\n=4Gjj\r\n-----END PGP SIGNATURE-----\r\n", "size": 3748124}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.16_1673217837234_0.15939409410593175"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-08T22:43:57.512Z", "publish_time": 1673217837512}, "0.16.17": {"name": "@esbuild/darwin-x64", "version": "0.16.17", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.16.17", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-2By45OBHulkd9Svy5IOCZt376Aa2oOkiE9QWUK9fe6Tb+WDr8hXL3dpqi+DeLiMed8tVXspzsTAvd0jUl96wmg==", "shasum": "42829168730071c41ef0d028d8319eea0e2904b4", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.16.17.tgz", "fileCount": 3, "unpackedSize": 9225224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6hV5piOGerJtvLnZiESAIl2tCLHSsbcxEK/03aGeaFQIhAJn1Oabk4BwnM+tQsqKqgm/71+Yfpwfi8WY7t4Ijgr3X"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqBQ/5AaDf496+nPKAEXlRMx0VG5Ekv3mVKBcCWnbXuz4mEG3niDsW\r\ny8LLAUBHqXftT1fNt/MFuJXZFtHgWPc6Rx8MzZfHVSdqTgFfE2UQdaxJCJ9c\r\nR0b4XBzpdsFE6S8AFPwzKfErmwUs9sN13VZGOmzRgwuqPy872waZfXXpjCIk\r\n9CSHUcpWLwtCJLz9VaO5dYGn7vByUJLpB7rb40KeVDyKrog80TCTRdCASE72\r\nB0ZNPW/xz9bWRAU8fNz9DkihVfLKt4ckvvJcypQWAYxeKEdjQvnHqbLGxHp0\r\nTU1YQEuLjFOIXlSf9vzN0UDz3irP4eixKvfbLdo/H0cxVCHH3vcLIp9pju3Y\r\nicpmYq87fdDfj+MWAHdir6a265Upv7HxumQ31OobelUSc2xWJT2rT4BAmqvS\r\n+ZaYEkqReQC08BXRj4m7rfkIkRQBnswjpE1jF2uKJNBw4o7E4bAqKXgv7FA1\r\n6l+SNkpt+T5xeRr7FErw3RAjXAu+rtZy0W8oAK+zaPqV3NVq+d8i5wXnukjo\r\nQ+zrPfXDsVyB1xlPh3WZQyTi5oG3AWH4NvEBEYkKc4W7LhlmYG59Efn2gtNe\r\ngG9C832ki59xAUVVpQOifoWVZN2I4GHQHhRg7Q4im+llU6S+sYirQBOfRRjr\r\nXXWoiIe548qhhpJXrtDIqylhdXJ62WN0wxY=\r\n=tCSx\r\n-----END PGP SIGNATURE-----\r\n", "size": 3756174}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.16.17_1673474289681_0.5750515753013783"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-11T21:58:09.947Z", "publish_time": 1673474289947}, "0.17.0": {"name": "@esbuild/darwin-x64", "version": "0.17.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-R4WB6D6V9KGO/3LVTT8UlwRJO26IBFatOdo/bRXksfJR0vyOi2/lgmAAMBSpgcnnwvts9QsWiyM++mTTlwRseA==", "shasum": "9a59890391f17cd3998d2c7959ea70a1aad28c93", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.0.tgz", "fileCount": 3, "unpackedSize": 9497191, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQKPNlYaDy3JUtNp8mx/B2xScvuOUG5yM1ZBJWD+8duwIhALPSJZ4prqjYXRUjMMNXd9vXXV9N17wiWp/8nK5QQFy8"}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjCtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgNQ/+IulzL+Y7T1KQvSYW9cs8QRWF8RcXOmHCIqmCe+xUz73CIx0j\r\n64CpfqE3LXy/fnibzLHVoec/dpN5XKFnO5DwoziXKt0uF9jw6/QrwkWLUQ4D\r\nbanpfwEzPBRYH21PsMRYNkWITbcgPM+q8DiQ1tk41U6QEUvpmBGC+VCG5x8r\r\n1MigqaNfTZhVr/WLUGYVf0SpXbgFSF4rHvvqFyLiPbiPw5YREsCMa5hksZ59\r\nrEWIL6YiJiF6YzWMyuzo7RMSxCCZ9i5j36t9jDRjTGmfzgCSJRZz+4lb1tkH\r\nbpcZcvzl98FHWWtFtyMXeLeczZsDzVCk7pYPSM7VJKECXYBEPrJGO9qoAKiM\r\nydz1BK74ww4oQ4pGDCQW3eAOtetmUvv0SZ5J+V+occ7us7M1zFNoNTlzULIU\r\nyDQ5ge5uBk9stUZiAJ2aBy+6RcdYEByT6NPq7FlVjCpu/AzL06J9O60NhpNG\r\nz+hZzVApQndT2bN2i7t5rtBPi6SfvPldAwrnpMnHBhRWjKiKUOF93pZBiXwt\r\nJA97AjkroVrgTEZcTSyn4t4d7/hDLe7urf6h9le49DHhdoxpN/mNcaAI9fmy\r\nGKtOkZ2dNZwxqWbp5sovwkFL3RRyEYXDPQeGr1xv3ZY/qWe2zhe6Nt4epuNI\r\nMaPI30UrDCKownSZj3+cFqZUl91/NnDMEyc=\r\n=d7NQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3868359}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.0_1673670829464_0.9794867688493203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-14T04:33:49.717Z", "publish_time": 1673670829717}, "0.17.1": {"name": "@esbuild/darwin-x64", "version": "0.17.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-fOXElELW/5siabypVqKTH++WtxVR7IhGvRkS6VKzffW+Kx9Yi5WwRaKG8iwb9QGwFfAKGLYxv73k6NAekkzIvw==", "shasum": "8c396dfb3ac35856778a99867614d559f2d5ce66", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.1.tgz", "fileCount": 3, "unpackedSize": 9505831, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0j/bYXnIT4RwGQ+IiGRQHDug+6CTcma2pK+iesVDoeQIgLYkBQEl/wR9vyWQ1PvZpTmzGWSVyOlW0FxbSg1G72uc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZH7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovig/9GcWfW0nJXiIU8GoiJIqaXFX/CMKeXNlHZMTFCoTu5HBCnCxZ\r\n+WR6M7mr6Y2oP4E8WatLzuhiiXcwpNs6gT7NBuIrWFhAPlg9uDsUXO3tKF/G\r\nHoTGt5JGK/oyZoMuKc0qn7nyL6wQRDQ9f3685fR6AD1SiaZYQvAXw/nRd8FK\r\nknEbk72Et78hKcup7v9dcgDyvJho5OqVjnIrQHK8+9mOOACIgBjNxtViygt+\r\niQdTaH14vGgLAndY71HW0jvjOlYOsU5tHWrPdmH4bcycLlChoWxPhUJxquJQ\r\n7qtw2Z9v6uqGdJEYmLjUsHIEy5oK58Wqn0xY0FTxMmgXXzClUnrM9nn9Mafh\r\nGVsFSoPu9DZiT8XqoFg0+cWgpgJzvTDeK85DVWoABucNgA/rsvfgeV370HdR\r\nlWErgYGozAoWV6SaOi3PUryPVml6zBq1bGYGlpWKT/BYDjJ2E4bBRMDtkFuQ\r\nYwDJI43h8T96Rz0dcKPkGuWIPBOdWTijcb64T4Ymv5jCYugLV2erTP5+gzwQ\r\nKcjy5yLD+saCAekkn4t8qi/uIhgeqMj6YknkxAkjNw6NToRXaFS0G7lWluiP\r\nswTAqBs0Shc/z6tut1cXDzasUZyL7uOP00e9OUmLJL2wYM2OgDr7w4sfUH2X\r\nzxHdcOO69MQGhUCeo4UyISkHRCJSoOcAT54=\r\n=a0O6\r\n-----END PGP SIGNATURE-----\r\n", "size": 3871045}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.1_1673892347072_0.8124012342601432"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-16T18:05:47.336Z", "publish_time": 1673892347336}, "0.17.2": {"name": "@esbuild/darwin-x64", "version": "0.17.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-j750nyrwoRZd3VnPo5sd12/5U27TxFGmvmoDv93G2jiaGJPYKJ/+5IfRAvHahGePTUIRPyOlE5YLFw9MlzuBnw==", "shasum": "4ae5735e1cd09b584cff4b8066a246cc62b06c97", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.2.tgz", "fileCount": 3, "unpackedSize": 9510247, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+QukYPSQiffhMiF9BPfll3eZ3wlUahncCnS+BOBkKAwIgRZU/mHda5j3luVtIamfglGZyGWGkYQmGZ5YPVTuckuI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkK1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrntg/9FrPhp+w+mINbO6IK+peGvbUq0itZlXHBvoOwRh8gz0JeC0Z+\r\n/7pqzgEzS7GfnO9FvJEXrlfDQgsYqF4BSZ/xJh2dJ4vKN1rGH60w70VHEPlF\r\nvlL12SK4CrqJZms4HlFfAJ0gnVz9p4IQPUlHxg5KGFmu0f+k7WKt1AGNavVG\r\njpMKV3uoVsDGfiTZk43ZLcD+iaC+AfDeE3+17F165iXfKkrhmGjb4IeZKIHg\r\nIcRz4qA6X7Uq+pbjZPmtlFY6HVlw5VCUKnPumXMv1q0ATvMTYC06pOtFiyeq\r\nYFYOnK7JRq2a5JCtbqQmWr/7tBP4P5ZP5SmmMkODymIiXXtNOLULsMZIPxjU\r\nMLN4d4jie24+Y28AMM1g5hc7jni7Vzs6MrJGftN1I6nVXasles8ZDInRAsuW\r\nN2bDKPpSv8G2979dj+AcZ6vBjoHZCjOEO+tHltwq0GYK9HniUlCew2S/ZyCu\r\npxupqqepaOU7pD5DA1gPxNU3EYQkWXmtg+hXChtI9sOyY1mLVCgavRBJVwuU\r\njaiprdo3EUfgALJRA7u2XDvhBu8QicS5KUkKT81xArii3s9ir02f0orByCje\r\nnc164Kn3Ls6rR06zKctpSrrcjigDa0P9o6gWnmCyVfGvSGt9UZtaspFJyNDZ\r\nVYnCkZA3gmZAipXBHoJ8qwghapitI0SKjuE=\r\n=QBUF\r\n-----END PGP SIGNATURE-----\r\n", "size": 3872295}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.2_1673937588906_0.5820061037001156"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T06:39:49.178Z", "publish_time": 1673937589178}, "0.17.3": {"name": "@esbuild/darwin-x64", "version": "0.17.3", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Eo2gq0Q/er2muf8Z83X21UFoB7EU6/m3GNKvrhACJkjVThd0uA+8RfKpfNhuMCl1bKRfBzKOk6xaYKQZ4lZqvA==", "shasum": "cbcbfb32c8d5c86953f215b48384287530c5a38e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.3.tgz", "fileCount": 3, "unpackedSize": 9510247, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6N6GLpm5KVIY5Uar4HpVsAyzbjSSk/V9U3hN4KCuX3QIgMez5xOLob38gzsgD62qVgeNKbkLTvGPTI+4WxXhGL5k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDg/8CaWa1FAEqcEHPoAzB84dp0hlNF2joLIxBwyjSs7y0DIrSIAb\r\nvcUCeBhTDZ44f9Mxe9Lhq4/yFbrkVp/pR9kgKeT0F1LDCw2bB3hj60sPyPDI\r\nP907nIAs9/6/CV7T9t6SS6wr1iX95PGI9sHh7NfbzsAgpl2dyTcMQKXxQBF/\r\nHA7kQvbD235sBJUl3AxDhZOQnD2aR8kRT4T/6fyFBAnF5lfV7/CSKYgtL8Wn\r\nUat3gWVT6Yqzj66Ratc4Zg4sWqQCDXLPjakb5oiYb5zU+DANn25NMl0kfw5C\r\n7Ik7xk2MEvs1o7uSe1lonIilK6vAPf8kTNgWr92D4f/v41yBukBF09GV+DAT\r\nc+4NZIRrL+MZD0mjXqJNZEELsookkx4W+s5qiMF9wwOVMx2J0AzzodqTShBz\r\nbxrt4ZIoLJI4RJB72fPM6L8Mss98hYoRODdexVKfpxKfIYVhz5E9ZCv20SzP\r\npkcx0cZ7QRfvj7cbjVeFu5JX8RCzw93D3yTNqJShNzQYqbUm6iIgztc7z7aA\r\nmOggLdR8ueOvNR2WItMicwHU9ekp8me1z6fEKen1E0NQ3pzjfcV/0kCvOqC0\r\nyYloDrhPgsjdYLHLmHbdCEs+Nm8IsA7+Vxv3zXtqowKpbpnuzsdRFmolAZcD\r\nahyMzNN9Wlsszt7JrVHx5GbUMD0clmQclCo=\r\n=xZpu\r\n-----END PGP SIGNATURE-----\r\n", "size": 3872395}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.3_1674069278649_0.018425760452470685"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-18T19:14:38.915Z", "publish_time": 1674069278915}, "0.17.4": {"name": "@esbuild/darwin-x64", "version": "0.17.4", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-phQuC2Imrb3TjOJwLN8EO50nb2FHe8Ew0OwgZDH1SV6asIPGudnwTQtighDF2EAYlXChLoMJwqjAp4vAaACq6w==", "shasum": "67f0213b3333248b32a97a7fc3fee880c2157674", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.4.tgz", "fileCount": 3, "unpackedSize": 9518695, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDpp6FJFsyAFM56RrdKPw3xcrAsAxSx5tmNMmSRzZBWOAiAk9bY/Y3gWm3MO97ZPpf33zuM3zfynepLFEXdaQEMy4Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGWRAAme8/aabAftDV6IF7DJhzQ67UMM4+EkrgKkOtSB5pL71sfCTz\r\npIbfKgbBD3a/Ux/FXtWnwtKnaOS+BRs+yMDU+zopiLF/Bts6xEaT6wqp/VyH\r\nizqgUsIeUwnzfSPwqerAjBZiQQmndqfa2hJRJHft3vImqu7nTkJEweWYkSxq\r\ntkeCdrZH9NbTaELuCk8JimzvnVW2LV316YaN0Ar1HaFm6UczLMjqJ4/bifNU\r\nq9t7kzk+05FEYu8VZBqtFDwyZGeZooaXiF+GgMuhq4+efefmAWOpi9V5a1nI\r\nyM22Ux9P4SGOA4LZ6fu01+wyI6wVAs1sUwQ5IJbBaJuWpXjM7fl44VHyrz9U\r\n7ChRFSOZNWMWPny1ve/R3xZ3cd0FMghuVWp2XB/n2NMfBn8iVDL5/1eMn2Xk\r\nLE4a830kVLwH1hP2NjUr87xZOxI3zfVGQkYXp0jcRhj8W4AplrqkLi9AWAND\r\ncOR2Ho5rDh1FtRENuAnYJEp44fDemqDXarM9sqU+TK7a5f2zCaWHZdlXq4QX\r\n4/zuRhg0mYXvvJ6A+ELoyP2ZMCW+WQxZCouoA2Lf27V54vaibGe6lL568+RC\r\npAwasO4IerDz5N9BkgYUBYTJeEr7V9EyHv1OXR3bWy3kT/qwUpJsuW8NcpqH\r\nFrWhmBYvPnugx644EhmV3Vtzo26yeKPKnMg=\r\n=xl2b\r\n-----END PGP SIGNATURE-----\r\n", "size": 3875424}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.4_1674368025497_0.7597079344285138"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-22T06:13:45.788Z", "publish_time": 1674368025788}, "0.17.5": {"name": "@esbuild/darwin-x64", "version": "0.17.5", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ha7QCJh1fuSwwCgoegfdaljowwWozwTDjBgjD3++WAy/qwee5uUi1gvOg2WENJC6EUyHBOkcd3YmLDYSZ2TPPA==", "shasum": "54b770f0c49f524ae9ba24c85d6dea8b521f610d", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.5.tgz", "fileCount": 3, "unpackedSize": 9522887, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHzTIyoBglL4Oq/TzxsVu8RcuriLKFJSkV3Jp8pJrkQVAiEAw5eRKaXIRLFQLqc9x2ZqXlnFpNCLh/H5/afGgvx4E/w="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvhA//cxqzUaFB4xH9ERLp7rP0Wz49A3fBpFNSa9QZJj+3Dm3iU3Xi\r\nw0NFDqS/nVWxAZj9g9zBeqJ4H0DiS5s766IIk0WLGfJJhTdEon+X0FQyYkUe\r\nT2/Vc2jqeNofiphuYgkH4wehI+X3rVyoi3EXzsflLBiPDHTVJvrWrXUw1NmY\r\nXxx+E3hOMmfQKmqqmifPcPsgiNWwypiPcSlAEPenrUFC4oEh0a0qZx+QBkxE\r\nDlLuswxInLmRVF6SWY55FcHlvtl75xM52F8PoCdyeP90QKQAa+KIjAS/YHxn\r\nWLETO8kJ/2JvpTx0ejpcHV/Lma2KMRsAmMoBFFunNF9axU8DdULk8FWDyi4g\r\nG5RMzguCazXpvnN5fQ6O3Gzkta4r2Q50yFi0U14/iQgVk5unDqBBunP1toCw\r\n1cJcRagK/JO0X0nGSgyx3NejSPEbRlf+5ONZBOgOq2iadF41EkZImQF07iGL\r\nRAI+BErvxiE6lg6yZSb/1Cfjke34hbU7aji5JCS7yF3AjYWKJTQc3TZb+16T\r\nWLQbfKFiqWyz/sXjDxr4gb9xVU7jrP6FkaR0kXphp6b8RDwfooPSB2NI7TxK\r\nSLSzj+xA6ub7PK6tlc754FzabFAmTZhoJjk1mv0yNO5QZKNWV4sB7SHYp+mu\r\nI7xtIR9pbAgJyYdP7Dtqf8Wwvz7JiSObpwI=\r\n=aNrz\r\n-----END PGP SIGNATURE-----\r\n", "size": 3877555}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.5_1674837475957_0.29472603396751285"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-27T16:37:56.243Z", "publish_time": 1674837476243}, "0.17.6": {"name": "@esbuild/darwin-x64", "version": "0.17.6", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-xh2A5oPrYRfMFz74QXIQTQo8uA+hYzGWJFoeTE8EvoZGHb+idyV4ATaukaUvnnxJiauhs/fPx3vYhU4wiGfosg==", "shasum": "f4dacd1ab21e17b355635c2bba6a31eba26ba569", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.6.tgz", "fileCount": 3, "unpackedSize": 9535191, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjIKwKuUTo4CW8wWKhyeIY9gIJgRh+cHbqeDd9juv97QIhALwDBojcfkB8G/IVYXx6wq1a70N9QBKkWx8r/HPsqoK9"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpySg//aoSMSjFw4MDgLVS3eAnwgfurlXl/DrsP25HsYrOEny7EFSST\r\nOHIPmQD3Ash2G/mDNgvSlTNopBZ9K0Fffu5/H+1KD1JVkgKJRQtx9HZwwRDg\r\nlCtlVO2Xcj9RXcRjcavtrqLLvG3jZlKmRvY9s+CYssfiB2QqiFUKWirXWfCq\r\nH9k+bLC4ZkJPnEyS3QBUZdlPNHCMJNQpZZvPIPC69sDGGT/8ZOTHhFHJN8Qo\r\neq30DEBt4pRvW7E3DmfrEs2B3coCvmm2rnWI0hc7/ai469tCrGYw5lSMhLGw\r\nxVNslKPVDgJlQ0r8MOQ+Gpgg5bR6xx3B1b5uzFfSEkac9BVKXbCbveUH88tA\r\na81sFMDfRQlvZLc/dcEXrGAzcsChsAcuw1Fl/kGkn3PGAqNk/eXYuppH79JZ\r\nHxrUdlyoT7RkdIFb1ZJlqXQZy6yaeNQrmDxrl32z98HNXjELb/nU7inya9i3\r\n9hseeEjTKj2xxiQyDszMW7Yx8DSGvYUvXINkjFqYsCY+PhNoa8Jsk7L68mn6\r\nW10CK+iIcPwd8VRTlavFurPE2ztEvEjEzYafiEGuwJBtLWX6mRE7vdI/uXOg\r\nfxIVkrtkqyTCmpjw2rN5wkJE09LrI0JAGw5bFQTZReFQaxy5+J3GEV5hRq+D\r\nzY4fK1tNlBlXoQ69k9R+KvvehdEBHv/yErc=\r\n=lr14\r\n-----END PGP SIGNATURE-----\r\n", "size": 3885801}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.6_1675702852466_0.7802257063574043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-06T17:00:52.879Z", "publish_time": 1675702852879}, "0.17.7": {"name": "@esbuild/darwin-x64", "version": "0.17.7", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-hRvIu3vuVIcv4SJXEKOHVsNssM5tLE2xWdb9ZyJqsgYp+onRa5El3VJ4+WjTbkf/A2FD5wuMIbO2FCTV39LE0w==", "shasum": "58cd69d00d5b9847ad2015858a7ec3f10bf309ad", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.7.tgz", "fileCount": 3, "unpackedSize": 9535287, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpQ3Kq+svh8Ru1HuFCqQ8KNVoBYsgitS7a1WH0MMHi2QIgQUc7xOdRrKfKFhN8Mvzs7iQ0r0lA/eEd7irNJAMzDko="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprvg//Xiczx1a3oD3yZxIGbJTDFRSw3H/1wrjKrXGbx1sQV7kfVAts\r\n2XeB8713GlGMYkLFXYCiMTIE2jE2IaxLXH1mhZhz89uCL3FGbd4I5ZMmHrEs\r\n4K8EwG083jqvKyA/mTfs/h8YGZMYb8WOQ+di/umvWUL09v4qBQCnMiXgYhfq\r\nqhjwTV2Mdf3T8R6hytsUHX9IidxMFY8C6ZlaIteYIjGSYesN6bbk1nF7wAwm\r\nrUgxBK4mETsOWKQTJxnKqcXmkqRdbdN3Yb8ESAmekiUYKwBnYm2IQCE1qW5d\r\n6meNv/Kl/SwbXeRBtPNQVQY1NytP6EKXKzveTD8Qb7W/tfKZZhxlswlB771s\r\nlhyPftEaISVLGSjh1/2uqwCpS8q25UikdrbgGcDCGc8PBTvhKUiHmLX2SVlW\r\naD0ntb+Hed7UrLy1wm1Ojm6xwVripb9OcQxWYAfWMUBsV/hyDGNsG713zdiI\r\nrqPwgMDDXohI0eNrUketeoMPg/0saA35Vv/0gGx3zQThWkWaLKfPoBtmt2gh\r\n37H2tU4Dea8oXWjWtxuFm+GF6u9pkyiRgIjrOriIhv9n39Ow12I+IaSUuusU\r\nTavfFggnPt0yo5JmwCoXP+JYV/Lwj30wl3IXZ1AoOv/YycoXSHHW51zT7Ef4\r\nMjZ432s6AtA3xQP3tHQqlJGqJwXxxn5vLzg=\r\n=TjUq\r\n-----END PGP SIGNATURE-----\r\n", "size": 3886954}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.7_1675981609218_0.9543554598997788"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-09T22:26:49.524Z", "publish_time": 1675981609524}, "0.17.8": {"name": "@esbuild/darwin-x64", "version": "0.17.8", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-n5WOpyvZ9TIdv2V1K3/iIkkJeKmUpKaCTdun9buhGRWfH//osmUjlv4Z5mmWdPWind/VGcVxTHtLfLCOohsOXw==", "shasum": "8a1aadb358d537d8efad817bb1a5bff91b84734b", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.8.tgz", "fileCount": 3, "unpackedSize": 9544071, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFchOf0xg4bP4r4soj9eOkVx/5N91Bz+/kml25xT1GgHAiEAnokiN8TkGjierI3H1tdAx7dchGLMQssxYDZ3WxT5/Ow="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dpDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmre8Q/7B8OBUKjG01QfDqDic/xsq9TsH8bMzyOLYngUWbs70K74Xf3I\r\nLQ31E1iIwuPZupkNn7QJRE5AV9QyFy0VYF7kdED47p+ir0EIt9EYci+bXN7/\r\n5sQ8zaxRAybtW5tylGoz38wXISaBwbFmiCIkW8ijXn+1tUaWrQ0/L25nNUEE\r\nrIUHlExyh605mr6yaFfl3iO7iw7lEYBJ7W5IbrJFt/OlaHvsHU9d9scuKn54\r\n6mIxfxkx4M3cK/9x2BZC15ZGVuCphorhTE6mFKdgCDl0mvQ/CWOAzoJfnFas\r\nEnlN65ZT64jn4FVuLKa8xGhAAEp0odqfN16U07dboNzxa2w5LaOdHSxDHQTt\r\nD4AR1oknYHihcxRfvJnonr0o12bG2f62Qm9LE41wKORRbjbMuShG+4QJqRxe\r\nYnKwmnu/P9ploICQXmylR7PpNJNpK5Ew7ezWYVRVb7EyqqivkvArtjc31vH7\r\nu76dTcYyvrzhCIGQBKqY1GFUcaIgETLngPoDbmGihpoG5YnT3MhPzi00AYgf\r\nJFMRD5/uhMC99kGeI92U99CwTveNfYotrVUXK1YIoiAh0BGILOoVs8L4Wc0H\r\nNSFdJ9EggIfkPeFj0uSeogA5qCIhOidmAvu4KUoimzJdx9d7p4JsJ+ZRuFVz\r\n7xHelkFrIu1j/JlC8kwzT4LOB0EZBWWR2Cw=\r\n=Ruvi\r\n-----END PGP SIGNATURE-----\r\n", "size": 3890115}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.8_1676270146935_0.44073871076527626"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-13T06:35:47.267Z", "publish_time": 1676270147267}, "0.17.9": {"name": "@esbuild/darwin-x64", "version": "0.17.9", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-GEZsUsDjJnCTVWuaq1cJ1Y3oV9GmNj/h4j6jA29VYSip7S7nSSiAo4dQFBJg734QKZZFos8fHc4abJpoN2ebGw==", "shasum": "c0d2c3b951b186fcdd851de08be2649c9e545331", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.9.tgz", "fileCount": 3, "unpackedSize": 9548167, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAK8CSmm9lQnjSiPdLaeXNY2B+MVCFD46XhNF5/rDf/IAiBqGj46b8426O9VD9hYI8eeX5C06DbxGAQ4lLXp7ZPcVg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mA3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnHxAAhTJPi9dbwMlkLu/S5JVGnxIgZCV2PIr3+Oi7sfd+eCBTWNZT\r\n38cp17ytWNf3iougYY3f103P1ZWPFWACAGgSTBgDEcZMQc9gQx5u5CyVOInf\r\n16dKJvGhyMWrQYG+JTrES6ry6eaRjn8ycU7fYF32FnL8rOXByApnk9wALl8M\r\nXYNdLvH2qRMgoiBD6zUCD1Io7ohJm1wGJ+Prfvr0tiU9o6sTqCkPeW0vefWc\r\nIqkz2CsXXG3SoNFSC/nLCyiQu3To2b4L5cMruIGwTwMsRglMh1id6/2wifME\r\nYOyPVA9e5qrdAuLG4P+sAi1zHLjHRL8EZkmyDa0QVxCVOv55TkR0J6Z5x0XV\r\nc/aYsvOS7rFlQDlbnktT+LsJKoDO1690n97Hw8ZDxCjeVjNU5fp1QJbkWqxP\r\nIxMI9CDiQzFhRQe67ptBl3T1DlYU1FhhdXz/pqIySXN6mTH8s8pEgHlEkNC/\r\nuv5Cr9SIFK8nowD8rU+SFUGOU91Sou3kuQiPccGbJFRYr6SEIKWJuOgrT68N\r\n8K7UV6U5CCoJ2bkxklwK3TrMzEjt/KIZGHtM/hFxVtdhD8G2pa+60flPASzI\r\n7MILOP236fWDk4vLo6MM5UwIHRzfJFh+JGXaJB1m4eMYuDHYuu4Vc73igLBG\r\njMd9dwEfnpiVFcw0abi3yzHBFpKogzohyDE=\r\n=A3my\r\n-----END PGP SIGNATURE-----\r\n", "size": 3890848}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.9_1676828727514_0.0026800363022749796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-19T17:45:27.800Z", "publish_time": 1676828727800}, "0.17.10": {"name": "@esbuild/darwin-x64", "version": "0.17.10", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-J4MJzGchuCRG5n+B4EHpAMoJmBeAE1L3wGYDIN5oWNqX0tEr7VKOzw0ymSwpoeSpdCa030lagGUfnfhS7OvzrQ==", "shasum": "362c7e988c61fe72d5edef4f717e4b4fc728da98", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.10.tgz", "fileCount": 3, "unpackedSize": 9544280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCs24FdRY1NBdMs2vmB8RaW18aTfEB/Wburx0dmeojoJwIgCmBObD/IdL+S3ELvCI9S473EXhcmiVrU87kaI1bcBtA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87P3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/rw/5ASZIEpMoFuXk7mfUEcJIMzAp8Xc9oBtttrm+6aThpSvuWZb7\r\nSW7pvwI7I4KR6Vwp9jjMkGfSmx8ABs++hrubaXpN2AIUJuBGMq8pCquCsJbo\r\nQVfswcPTa+jSO/FGeFTKJpTV41mZ6cNg9sWatfBAX5HyL8E0CoJAU3OAuwQI\r\nk6Esf1dSKyhEsFvDHNGBi8Sk9kFETwKqFzyJMsMHS7ee9P5gK/euH5pSf+DI\r\nvDdp+B1bzRcNm1UxbQfeFRCBxrzkttc1jEVSDbtnYtEiPG56i4+AOmBRqOGe\r\nmSMSsVYBX5NmjIAIfzRBWkcn3c2a458xNTpszPBYUdAW0aakppv8F3fsPOmt\r\nlh6Jvm6/kcpKmWxp/fGETSuLzf0EXBT3K6uZakM0TOIIbpyKxsZB2p+E1Jkf\r\nHOHvhjtEYAH/TtEZxArOSL9Z9LtAYbtxm0I1nmCO2FPcEj4Me6/2IfJT/6rn\r\nrOhXjJlajGE1E/+n+P7SDddDzwvHl9qcKUwY73ihwVrOAUrbk206e3kGz8tG\r\nPtMO1JiM5JnXDj1WMGtj7Ef6dRAVXDW+4t+2CYOGsHV/ugMID+YA+MG6/Nnk\r\n+tVH3qIGS+YAV7Wi2FzA+NwkGA3eBJkw5WtJ6BFWBaXOvm8Gn/4k5+hiXrAu\r\nvP2gg+AvrQ1FD+Di7haDcYE2Pcu7JINCYv4=\r\n=q7OP\r\n-----END PGP SIGNATURE-----\r\n", "size": 3890673}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.10_1676915703720_0.7038840183027271"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-20T17:55:03.961Z", "publish_time": 1676915703961}, "0.17.11": {"name": "@esbuild/darwin-x64", "version": "0.17.11", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-iB0dQkIHXyczK3BZtzw1tqegf0F0Ab5texX2TvMQjiJIWXAfM4FQl7D909YfXWnB92OQz4ivBYQ2RlxBJrMJOw==", "shasum": "2405cfdf70eb961c7cf973463ca7263dc2004c88", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.11.tgz", "fileCount": 3, "unpackedSize": 9558344, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHLp7SP6I1iOnxBkL3Sz1EKWVPa0AJlAPwFNW+5kFhzkAiBmAdJhAecg92uRLr2nSmGea8KyGyloa6XyMHL6VcwHXA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWdg//f4pq1SJip4KxugBYntru+SZ54i8yyV4RzvBBYIR5sQAt71HR\r\nI+U1xsADlEVMQDvOWfXlAfeM0k2qSvTgcQBIWSk0vD19XK9eyPl9mj9C/ieR\r\n1iHgbfWP9D7VLN7fejqS/PzfEE4B5XoXHvnmYNwYSoGroIgen585HUs6QEmY\r\nxVUAdpLRNxhQO5r+7Wf2/UBwwiubovmtYKZt/9YQf2g6PC9UEOZZmeY49qv2\r\n+jujukhwmw5PBIcPrK04uQKvQ+UlOns9RSunLrbUls2+MEw3qVC6VlNqjHoX\r\n0ji9T5ZfFFw3CylVdL7UAU54IFWFK5Yb3pI8rluEsNa4pR2lkuxHxZTYYq/+\r\nS68zJa13JE0pr24iHcYMX0c+M/uyHfXKADB67+SnNnrIAtDBkQu47YIqSEGT\r\neSqL9qtk/g5dRn6rJ2OVHoW8/txP2ax9luUqlz6wShbHu3n6lv0nm9GTUdv6\r\nDYqUIPoiwHdXBzXMXQIV46S/764ygaPLtNNYHQRbgo6x078UWtXaqY1M8qv1\r\nXgCgqqA97viaIyGYLLyNSk884vR796+jWbbRN2lR7Zr2QdUd9ZTSHoWDhxOt\r\nFEg2x+3J52uymn5zkazXahx6XhyBVgorDhgyIH186AraxFPgkYWHtPqauS2l\r\n+udzFLUlTfnny9GVHFG7lRXYLf0+8KhAclc=\r\n=NrVx\r\n-----END PGP SIGNATURE-----\r\n", "size": 3895158}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.11_1677883220912_0.5499740379451077"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-03T22:40:21.241Z", "publish_time": 1677883221241}, "0.17.12": {"name": "@esbuild/darwin-x64", "version": "0.17.12", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.12", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-5D48jM3tW27h1qjaD9UNRuN+4v0zvksqZSPZqeSWggfMlsVdAhH3pwSfQIFJwcs9QJ9BRibPS4ViZgs3d2wsCA==", "shasum": "e7b54bb3f6dc81aadfd0485cd1623c648157e64d", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.12.tgz", "fileCount": 3, "unpackedSize": 9595720, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/31GMSl7j6uj5WDip5lK9G+TkW1VjgU9jVWcMKnx/WAiEAh1a2dGwfr3dfz/RwZrLpQ3cvVrFHA7UcbyTXeDcNzZI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAXrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAmhAAnxYdimKwGbZvvOo//cu6w5DaEG/RRGRRpMSiPpT6of5JCpVz\r\nyg+EkGjEi+gECHNn0kNQ5x9SNlTbXS/ZLZleyVdOto+n3Pxdc8IV4vxsinWf\r\nRE6xIqdx0GFiM9NYZ5tURfXK+uOM0fVYSqTiBZnRKWa24ggGRjgGhFBDrVKf\r\nMmGuTYGqQpEPaKTDjXuvhaotKY158DYNtCxKMBGFao3kYjbOO358/1lloKpH\r\nqG6lVoYTcbZIOEPB6kzPo+iBGmym55gQw2mbAQyqupDYH7hrLU1C9HFzjVdm\r\nowuBgFbGywOtFbs4W/TMxU/iBbqoSlPCPxcL1v0MAHiuhp15DoH96SYCnMin\r\n3I71WH49iYmsu8ovjKNNWG8xw1fBMGyL3pAsti8v553UzQCJGQVHwxU6O+l9\r\nAQP56YLof7PRTyI05JWMOYzZQRQBLsSD0Jtq+LZ0uvRq8wnO3rNUGIT9cueA\r\niGFPA8YG4WsHSPDDkNGrNhqunSCFcXW9+2gnPwnNtKFnRJgRypJ56fjNf25S\r\nldanBd9feASjC8sPYWxjLdv+8UO0U5zrsH2ravhHti6Won6WmjKdnzS0mcq1\r\nYLzdv2LNiqknWwsfJ8I3f9ir+Is0CUljlJckmLdA/7Owfnfb7Zcw4Wt0BK8c\r\no1dxc59eNFxraQXb2Z9ua6p1s6OGVU3VUZI=\r\n=FJe/\r\n-----END PGP SIGNATURE-----\r\n", "size": 3914522}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.12_1679033835251_0.054668056326609316"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-17T06:17:15.526Z", "publish_time": 1679033835526}, "0.17.13": {"name": "@esbuild/darwin-x64", "version": "0.17.13", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.13", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Ku9Db2sblCxFvQdEO7X9nBaLR/S81uch81e2Q2+Os5z1NcnsFjuqhIYH0Gm6KNNpIKaEbC7gCLbiIPbLLMX4Pg==", "shasum": "330ad462247e5109079efbc1c782c981e584913f", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.13.tgz", "fileCount": 3, "unpackedSize": 9599912, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGx49Nilvxl4hAfLGPFUi2osVxs2ZOIrLBt9kqlLoZVhAiBeGxX83AHd60Z1ibXmP0xcCce5PrS3p/QMW3kQOmIN5A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDdw/9GIcSj8wFhZT9XlSx2h7pOgNv69B+CDzeOmMp7KE5Z05evCYC\r\n7ggYzaDikPOrLZ1h86+f9qV05jWiJBoGnrLm0tknrzXWuI4dsF+XII9g7gz7\r\nF/t32J1joaxcvQLnT4btWPQOoDwmjQueO64ZsgBfnRmtorC64qPAHM8d7Hkz\r\neCTqB83o27Kwn0UjzHVK8C6lJ3FQRrZN6u4L+YucseqEimQCbVQVfE41q8S7\r\niPeargikJ4FNj7aGbXCN4/ni+5hvvr4D6CXWwxGoioGFdYXnCriL6ET21qYu\r\nzZYrAArC5RFuOumtDLzG6Yn634Rqp0iO6YtjNDOJbbMoayrJsfcTu+ds2+Es\r\nVQOi59PfnFbJAG4ZtcRsbHLVr0jNJcLIZbYycDbBAUGir93xPWrzB9qg8n35\r\ndbEAo97RiP9VoKe3c65HsVYZanKGvKyfFmrGfT/Hly41/tcqJTKNSQozrkSb\r\nWWMgK6/99nt0zahIk8rD6D+9pxl/zDDqKU2BwpUaRaQ9LlLlSCGimv7XvxmF\r\nW9aXzC/wJdx83TpD3Fw5EIyd7ARy5IMkw7fiOQKomZWP2g09pvxpzxo0/tD7\r\nFPuugxMg0eW5tGoY7EZKfEbzrANT57QoQHzDJ9mYvIDD7YTrWAOlv8ThPQJJ\r\nEm1ZkGVKK4AB4fJHdgbEYREreOCjwVMcBUE=\r\n=B7n7\r\n-----END PGP SIGNATURE-----\r\n", "size": 3915263}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.13_1679684235813_0.2931732307207995"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-24T18:57:16.098Z", "publish_time": 1679684236098}, "0.17.14": {"name": "@esbuild/darwin-x64", "version": "0.17.14", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.14", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-zN0U8RWfrDttdFNkHqFYZtOH8hdi22z0pFm0aIJPsNC4QQZv7je8DWCX5iA4Zx6tRhS0CCc0XC2m7wKsbWEo5g==", "shasum": "4b49f195d9473625efc3c773fc757018f2c0d979", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.14.tgz", "fileCount": 3, "unpackedSize": 9638504, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEoarbkMRV37BEKBS7YJ2mknSFVELtgdaRDQjpf5uUeKAiEA98Pb5aFgU3Gn33VSIMHPEW0WHjK+lm6dafZNPWIewjI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcgBAAn5KI2ub2wVrYzDWjNews0TCQuZgQed/Hh9s2Jq6U6nnn8I9R\r\nLcNMYwPkxA7G9OUN8rawY+Iczw5IN2k7K3G6iKhpNIsHKPYt1+MLA+h/TPQJ\r\nc30gBOHAIHG7JdQOvZSQjlG3p2q4+D3f0+alox52Ip1NdniXxAzyWgadt7Fs\r\n5ODMAj8SyJ2RA7QCp/2iAGYL3XH9PiJkrVIZqDjSIVar61ME6WZ2yuU7dQC0\r\nkCXxF7KQ7Yys+f1toO+5fRK8zL0tv6MiLvgyT3uF/PtdPuM4C3q7wUZIEaLU\r\nJJgP0/rdgckKJ7taoLihC3VN6ToZ4VyZTvQUEReatLwPGBV9NlJn4WSlOpYk\r\ngMLjJkUspa3LhWQZeGPaVV//g6VX5njPus4pBW/55/33+N8JlmJxnnXtssGs\r\nJ2IJ3TbyGRPcgvyrDEyAXBRdzCfhu+pGqXC+kBeCDAKxP1uvzsCBKwgDkKJa\r\netsmGoitMURQ6yL+rzpV/+V/eCwzbYDDd1RkS6rZmwZdinanx4A3RPsATYiU\r\n977NlFPXeQ5rCgSfhyJgUn9G1X9lhTBCxsEKYJmxj8efUUDxjo5JpBjO1xEN\r\nUTqGGP+GBSD+l/dwS7bNnbP3m2ZnEENvUaXfKhqvkuVxjrrKKT0pYtEFdp3I\r\nh/oiGycZdiCWdhlW88YDok7PBcjo62PcNPo=\r\n=a9JO\r\n-----END PGP SIGNATURE-----\r\n", "size": 3931945}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.14_1679798870476_0.8557094837866241"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-26T02:47:50.761Z", "publish_time": 1679798870761}, "0.17.15": {"name": "@esbuild/darwin-x64", "version": "0.17.15", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.15", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-NbImBas2rXwYI52BOKTW342Tm3LTeVlaOQ4QPZ7XuWNKiO226DisFk/RyPk3T0CKZkKMuU69yOvlapJEmax7cg==", "shasum": "9384e64c0be91388c57be6d3a5eaf1c32a99c91d", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.15.tgz", "fileCount": 3, "unpackedSize": 9642600, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtghbg95s5BSXZinYt6bKjqgXxKo2PhLB0J8s+OU6eSAIhALQBo/DwYAj6lM674LwMu6ZHHMplJsp+rrhJRBmMV/J9"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNTA//Vo+h/nC4JpLvtQayflXyDByTLiXaAMc/Fa60KSwb8DyZxFQy\r\n00F89DSS/ss+y7EYHpAorrvKordAF/YNtJJmTzoFQxgIusWA5CeCUHasmBUi\r\nOrrtJj+TF+hiVfY9yiIlkJM8gb7qwBVYI+XJhy/bco32/5ZSENDRi0Op5aWi\r\npLzkJLULn1795OYzSO3unP5PF6Q4BgA57WdRSgenc4LsdYsiqiA92VTFuN2Z\r\nTMjLlW4V9NFtehMHV0kjDWNP2RrQ7QDtVetJ6+y1iRADn9cJLefXCSs1ugSI\r\nV29H1QDefKq549tB6RaX2AqCG0ogDWXSQtlfyPXKc7aoHuABUL1NIRMox+aQ\r\n7ypIyDLv/uWqfgSrRk8IkROSgh6G4zmoaLKtxNgyKaOYf8ivzPj3sHZ+tvU6\r\n3QsKXtne+/nkEB/75CCom8bx9EUs9HuE/GyhRFRfafI4uZ30iJugYNDYzL6V\r\n7+x1VzFpTyHlyvitg8fRGpiUz4TQLfP2zKyx/XlmwuBoZIh+8iRd+g1pQXZx\r\nEFChPhXMtlJ6lvxBjNqVG5vIEtnm2/mtInVwlvzV4gFXjMojV/JC07/eDXHO\r\nuCpzH6W7/vQNd4TcaSB5Nmn6Onca12DVW9lVfCz2HO0IgjiwuRaH6hhmFIbj\r\n3dHm+fPD7h0MCBeXh2RGJXyoUSlkT8W6BLQ=\r\n=Cd/U\r\n-----END PGP SIGNATURE-----\r\n", "size": 3932506}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.15_1680388020501_0.5962073633135858"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-01T22:27:00.790Z", "publish_time": 1680388020790}, "0.17.16": {"name": "@esbuild/darwin-x64", "version": "0.17.16", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.16", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-SzBQtCV3Pdc9kyizh36Ol+dNVhkDyIrGb/JXZqFq8WL37LIyrXU0gUpADcNV311sCOhvY+f2ivMhb5Tuv8nMOQ==", "shasum": "d5d68ee510507104da7e7503224c647c957e163e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.16.tgz", "fileCount": 3, "unpackedSize": 9642600, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw9YLtrtXiP8X4kTuNATQvTKAqtdgX5opytggFhW4iBQIgcwNZov3KNRS069UqYB4xOATD+Qxgy0gO2NatQ1ad2/I="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5H/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2sBAAhX7vViiMMxRX1Cqp6FPSv5vBEgRk8q7C8JABrnF/qIR1TDJU\r\nQP8kTthTf8LVaRNZ1pGjxsL/yWoMtmoctjpTYaAL44v2Xl7U9tUSF4Z9hFBI\r\nWOR8yuqbE4WoqVK0Rp7TljpLNW+DLEzIvhz6092ln5DsEfjEsA04gPiB5OGa\r\n/PcT8ZbKoUge07bI86pyrjBmTp/P2JULsjp/OMDaDuJfT/9Ypym5RN0r/b6F\r\n6YgCRSdKfq3H7b9itkhLWhRb0APp2GYscB9iRWP4c4DD8bydtjZpHOLuI9Ol\r\nZaK7pGxdGR0Va/63BymNVrr/79uoA7rFNPaACalimCgXzar52iOdKzpcyEtI\r\nt8aox0Ar/s7L+h6m3L9gxwCBxlXRCR2yZMP9k81axmTn6Ay31MiZ3ZksVMXQ\r\nkpXYqeI22jYXdYtLrutkwllOpH6S6bYHEuMHcwlwz/CjcX7f4z6SMmaHcHR3\r\nW1XZZlKoeLJU5+k5/Kprunzfq7mb1ABu5N0Z54N3zktbDRu+2CovcKX24k2V\r\nvNxzx5+PEdXttso3fR/ZWJc4ZNA63xqzTRbrRG3ZTy2IQ3LpMo1P7HLEytyw\r\ndj+WSn7NWLYjdJwgYckjWEZxBnDM22t/bhP+p+/VUIIkXN3tPEYpqVwrBZYZ\r\nOtJ2IVPqq+LdpBraK+T1hRIeVGwLvlQe254=\r\n=56es\r\n-----END PGP SIGNATURE-----\r\n", "size": 3934079}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.16_1681101310806_0.08929215160324788"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-10T04:35:11.190Z", "publish_time": 1681101311190}, "0.17.17": {"name": "@esbuild/darwin-x64", "version": "0.17.17", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.17", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-4utIrsX9IykrqYaXR8ob9Ha2hAY2qLc6ohJ8c0CN1DR8yWeMrTgYFjgdeQ9LIoTOfLetXjuCu5TRPHT9yKYJVg==", "shasum": "b81f3259cc349691f67ae30f7b333a53899b3c20", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.17.tgz", "fileCount": 3, "unpackedSize": 9646888, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEs7N3EKl+lGsIs+/74umQDPL2ASkUcgSdt6VBoFsKtDAiEAzuAZzd3Qjid5b41zXwVrHdjSC4WfQxTPOyuD448fJ0s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJOA//W9MnlPwyUF0j7wGD1ZOtLZymbvrSESIF09KGnh9NlVf8Ekmr\r\n0T9GioAng4/GXOUeBXXZGfPRzGDf7GD5QTfZwF7J40VucDqXsNzlMoXLGuUu\r\n4D82WeplQ2PBoxCUtJZl2bqBJ6Bp0kqPiUVIkZMXqrec9NVj+P7Mov0QKzVw\r\nAChOyxg4cCNQqMoW8czd0jGdHuVPNDySMLULWd4S+3yxk2DEewiTs5pKyn9N\r\nLo9HeIwcm89ZdAuIfFuPi/jR7xaDx7ZTwFv3SIK8fNM+Ni5s5V6oVB7toAVG\r\nf/c/y/nUD/DcZA+qzUNEMovujna5+OM4SlZ/Jjd7hOTQQlyIkydBNxeDEPBS\r\njec14/mEDGBzf/UJfwgMS/fWSP2ycJ3iNGN2C2QbIVn7Mm9lNGm2DpYOYALW\r\n/J/jMR29+dAa85YyrCKYfRo8j/CXqSdoxtNNKQE/oV5WqFprHHM+3lfjp/OY\r\nfDfhrK+2c4TYWoqtfCStB5xjNSXcc/F555YL/kpICVo0juimxGCAECneFqO4\r\nFHQ+gT35UvNsB8W+J5aZHKbPMqM4op5k4oywnW1Ev4w1LX4s88bsxsgdHTfN\r\nc0t6VCpsIGGTAPwmL+wvmwRmxVkXCaju9ednb+YR3tQVq2l47zZizOyjHJPc\r\njAYuuPBGEWBbSK+K2Deg4SololY/zBEiBlc=\r\n=w81Q\r\n-----END PGP SIGNATURE-----\r\n", "size": 3937118}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.17_1681680227008_0.690516628429716"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T21:23:47.290Z", "publish_time": 1681680227290}, "0.17.18": {"name": "@esbuild/darwin-x64", "version": "0.17.18", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.18", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Qq84ykvLvya3dO49wVC9FFCNUfSrQJLbxhoQk/TE1r6MjHo3sFF2tlJCwMjhkBVq3/ahUisj7+EpRSz0/+8+9A==", "shasum": "a6da308d0ac8a498c54d62e0b2bfb7119b22d315", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.18.tgz", "fileCount": 3, "unpackedSize": 9650984, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBs7Dmyht7+K5+Q0IYsL8VRTBOXt4VJlzIl9dSbu6yNmAiBHD7UcLImOwo10hzowDR/QGmEPG8TRN01ALnb6Y0NzHw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZ9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/OA/+J8smjPy6gzPz6EV2hMWCoR1KtfzeAdepDd2QKDgHKcfblHhJ\r\n2eF7Vr6XJr/HHfcNWVzpj4yytEyd41SmIyX+LbcZC+Uf7ZVFTs3I9xX2uFMb\r\nlQhYQ8qebYte0/Yc4grZOeLSJGyBFddPoZ+BBqd5OKS0ML6ElSj6ET39GRt1\r\nKN1KlQVMwHSwvlgI9Mis+HNECc5NMINjT7lDaMDUm9z8uRxGk4JpN6Fo+PdT\r\nrVMOYyCoDjMdrF04vBwh4iwD3gX6YzuqTa6jwO7na7QPn+mJsiupvko7HC6Q\r\nCbZ8Wst70C3/beRbDnTHbtB7Y1E+t9vQqoxSSXL3UvLKSbdwlYLepoM4tN33\r\nhD1cEA94KjV5RhOWDMcxcURXbue+ycaIXHZu2YwzPO4TqZo6Y9mV8uE7oUfi\r\n5jKuxzb5bnP01pvki0fuJn0yp+y33FAPfgGpVSZNo/lAppqYFq8Ez5RwAVR8\r\n4zk8VLQoJDTjBGoJnPBGdDvJfhVKsTC4M/Jno8b2mOlu5FfqYPp4BSHtYhHe\r\n1iZ/BGLpKdxa/JsYAmbSMHKwljDPMC4rvUpjW+vM6i1ifCJ44OKt4n80acww\r\nRzlDso7D6dfpIaL+490BwMTErYLLnqzitGgoWOjj6OAv9WIbVkNtQzU1B8Jc\r\ndy8IyMscF/XFdSyHUvkKz1jEZ+PZ1Bg+lTg=\r\n=NkcF\r\n-----END PGP SIGNATURE-----\r\n", "size": 3938728}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.18_1682196093463_0.007324752977380333"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-22T20:41:33.770Z", "publish_time": 1682196093770}, "0.17.19": {"name": "@esbuild/darwin-x64", "version": "0.17.19", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.17.19", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==", "shasum": "7751d236dfe6ce136cce343dce69f52d76b7f6cb", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.17.19.tgz", "fileCount": 3, "unpackedSize": 9659512, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQQ64eQBK8t1A3NS83t4EtdTQdOIUdEpRIhtfhSZoSLQIgc4VqDqyZgMg8d05O/L7kPyf4/ExLnp4DugDx0O8u3zU="}], "size": 3940951}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.17.19_1683936400650_0.8891337170481233"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T00:06:40.966Z", "publish_time": 1683936400966, "_source_registry_name": "default"}, "0.18.0": {"name": "@esbuild/darwin-x64", "version": "0.18.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-4K/QCksQ8F58rvC1D62Xi4q4E7YWpiyc3zy2H/n1W7y0hjQpOBBxciLn0qycMskP/m/I5h9HNbRlu1aK821sHg==", "shasum": "e9392250ed9701c659914321a9b180efe233af99", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.0.tgz", "fileCount": 3, "unpackedSize": 9663879, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdbSzzIzGsGPz8gDetqkW0rHbwyYFq+Hqd6OwsTdm0dAiEAsCEFF5wx4ZPAjMBRDekni6ZU0QTiRIisxYf/z2DFNnw="}], "size": 3943447}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.0_1686345868338_0.16628620041836717"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-09T21:24:28.627Z", "publish_time": 1686345868627, "_source_registry_name": "default"}, "0.18.1": {"name": "@esbuild/darwin-x64", "version": "0.18.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-p/ZIUt+NlW8qRNVTXoKJgRuc49teazvmXBquoGOm5D6IAimTfWJVJrEivqpoMKyDS/0/PxDMRM2lrkxlSa7XeQ==", "shasum": "733934692e038a50a209365b25836e2407e29328", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.1.tgz", "fileCount": 3, "unpackedSize": 9672215, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCcC9/eenYnsqrjP2Y0ykImNmLE9LbAB2/5afFzO5/HAiA5ZoSs5P0SFuYbpLX49ziQx2S0LisgssmLZ+IUnvNO4w=="}], "size": 3945059}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.1_1686545511689_0.34293840890716076"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-12T04:51:51.929Z", "publish_time": 1686545511929, "_source_registry_name": "default"}, "0.18.2": {"name": "@esbuild/darwin-x64", "version": "0.18.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-cvH58adz9L10JNsIcgtkWNS/1eutjRTi3rtWz1s3ZhR64BpdmkxJBAXE/UjqybyNAWLhaN8mPJdlYI2f+tQA7g==", "shasum": "194e9e907d9487325ba9e629644f9448c18764d0", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.2.tgz", "fileCount": 3, "unpackedSize": 9676343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbD1Rn4sfnPXI4CWjUCLomEijbQwWn7adzLWo0v7cQywIhAO/jEws0tGIiLlmmejYY2gjIHF7j53Jhb9cOZRhY4JBG"}], "size": 3946661}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.2_1686624037185_0.5864260145544293"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-13T02:40:37.452Z", "publish_time": 1686624037452, "_source_registry_name": "default"}, "0.18.3": {"name": "@esbuild/darwin-x64", "version": "0.18.3", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-NVBqMnxT9qvgu7Z322LUDlwjh4GDk6wEePyAQnHF9noxik/WvLFmr5v3Vgz5LSvqFducLCxsdmLztKhdpFW0Gg==", "shasum": "bdd2f75e6d53b3e47d542e2557a7e636cc4eea3a", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.3.tgz", "fileCount": 3, "unpackedSize": 9676343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHgOG+xZ1Zc/Jrgi+lHqFHQsYV65u4iuXmvsUOMXiRgAiAmqb99547a99cgMa94pz+dguqE8SxVngIDdJw741GO7w=="}], "size": 3946379}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.3_1686831672127_0.15028110161208086"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T12:21:12.478Z", "publish_time": 1686831672478, "_source_registry_name": "default"}, "0.18.4": {"name": "@esbuild/darwin-x64", "version": "0.18.4", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-uEsRtYRUDsz7i2tXg/t/SyF+5gU1cvi9B6B8i5ebJgtUUHJYWyIPIesmIOL4/+bywjxsDMA/XrNFMgMffLnh5A==", "shasum": "a0a3b2270ba201331e8056d7c896598d8a96a12f", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.4.tgz", "fileCount": 3, "unpackedSize": 9684615, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDcHjh+OJrkg61FdjbJ4L5Gr30pTjpUVNCxRAUdUDmjpAiBJ9umFa88aq8l4kIgLJmD8z0+I15uV1i+K9suS0+yMpA=="}], "size": 3948523}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.4_1686929919792_0.468112364327985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-16T15:38:40.092Z", "publish_time": 1686929920092, "_source_registry_name": "default"}, "0.18.5": {"name": "@esbuild/darwin-x64", "version": "0.18.5", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-eA39B8SxbxRdSSILD4AsePzvJiVao6ZaYrcTOJqg89jnnMEGR/EAh+ehV7E4GOx4WXQoWeJRP1P9JQSzIrROeg==", "shasum": "db1c70374da2e3833158b1e783cc7f9a9c48617a", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.5.tgz", "fileCount": 3, "unpackedSize": 9705527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtmJh8HAhEdf8YgahhFcBQN/hS1fGGFtN5FPpusjUebAIgRRXiuS3m5fgHjTWY8ZDG6JKIgXE+5th1K9y+pJ1vuw4="}], "size": 3960266}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.5_1687222368437_0.24853291874896533"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T00:52:48.687Z", "publish_time": 1687222368687, "_source_registry_name": "default"}, "0.18.6": {"name": "@esbuild/darwin-x64", "version": "0.18.6", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-L7IQga2pDT+14Ti8HZwsVfbCjuKP4U213T3tuPggOzyK/p4KaUJxQFXJgfUFHKzU0zOXx8QcYRYZf0hSQtppkw==", "shasum": "16a4c802000d00b139281198017fc00134b8df12", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.6.tgz", "fileCount": 3, "unpackedSize": 9713815, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEoLjZp4pZaocs5PCzdobQlyLZS+oAGev8GFQFp15rY3AiEAh4jQ7lGId0ui+WxfqVhwL28ApPDcVay2plb9pV7T49E="}], "size": 3962911}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.6_1687303499210_0.3142375959726509"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T23:24:59.588Z", "publish_time": 1687303499588, "_source_registry_name": "default"}, "0.18.7": {"name": "@esbuild/darwin-x64", "version": "0.18.7", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-w7aeD1UjDFXqyrZQLBIPYGmLR+gJsl+7QSwmSz+nVrCZOB7cyWEkIjCF8s4inUD3ja3WtKUIqzX5S4qDnU5q7Q==", "shasum": "9ad104e7cddbca36da7474a039bb557d33616fbd", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.7.tgz", "fileCount": 3, "unpackedSize": 9755863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcBYi8To1jCn/cMuRyi1lzbc0jv9tgoIAXE4fbtKcUzAiEAnKbhfpIz8DkxvrmNYhHLCusapYk9bDGwR8ovCio3Yxo="}], "size": 3982097}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.7_1687574790505_0.4982145439171457"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-24T02:46:30.805Z", "publish_time": 1687574790805, "_source_registry_name": "default"}, "0.18.8": {"name": "@esbuild/darwin-x64", "version": "0.18.8", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-OyEf+21R32glxR+IJpPhBgcbxSbc7adPe4hYggu2mbjqAAjJJAaYoYjNeojyp+ZKY2ZRX3FimBbeExVoPdEDfg==", "shasum": "d45d478085453635688466e62f8d64cf38badd94", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.8.tgz", "fileCount": 3, "unpackedSize": 9760055, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBPS4R01I1QuatWR1xTkHbC/y7r4eeQe8I1uYrNYmwWxAiBOZG5VOKZAiDIThIw4+dGMX1AZ7Nvg/CAty2eNmyZmyg=="}], "size": 3983086}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.8_1687663160969_0.7310771802642257"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-25T03:19:21.284Z", "publish_time": 1687663161284, "_source_registry_name": "default"}, "0.18.9": {"name": "@esbuild/darwin-x64", "version": "0.18.9", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-tDbKKMUeS0PckRtIxdF3+NgkE19kTyLFmUQ0umgXDnBvcWC3/DqhZyu4P4Af3zBzOfWH5DAAmGW1hgy53Z706w==", "shasum": "e42d4b0a5315a6eab3591baa0d3be4fccfca0fb9", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.9.tgz", "fileCount": 3, "unpackedSize": 9777335, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJTTfH3i7KUDH1AWpyLinMJhEG35NkiIVuM9pu+IdAiwIgLWQ+radDZVQpBLfOm8NvKnRl9iNfxztrYvCYgeZPfA8="}], "size": 3987802}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.9_1687757290652_0.03450826798543738"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T05:28:10.944Z", "publish_time": 1687757290944, "_source_registry_name": "default"}, "0.18.10": {"name": "@esbuild/darwin-x64", "version": "0.18.10", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-tnz/mdZk1L1Z3WpGjin/L2bKTe8/AKZpI8fcCLtH+gq8WXWsCNJSxlesAObV4qbtTl6pG5vmqFXfWUQ5hV8PAQ==", "shasum": "a8a8466559af303db881c7a760553bfc00593a62", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.10.tgz", "fileCount": 3, "unpackedSize": 9777336, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGzsxiKLSF4FsV4As+cofM6noDDSRZOXjDVdh7QxzSdrAiBCIEE2POkL+iD1rD/o9RJ0rPADGt5y4L5GUy8Hby9ldw=="}], "size": 3988053}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.10_1687814434784_0.7003603679935027"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T21:20:34.983Z", "publish_time": 1687814434983, "_source_registry_name": "default"}, "0.18.11": {"name": "@esbuild/darwin-x64", "version": "0.18.11", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-N15Vzy0YNHu6cfyDOjiyfJlRJCB/ngKOAvoBf1qybG3eOq0SL2Lutzz9N7DYUbb7Q23XtHPn6lMDF6uWbGv9Fw==", "shasum": "c5ac602ec0504a8ff81e876bc8a9811e94d69d37", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.11.tgz", "fileCount": 3, "unpackedSize": 9777336, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgkBIISvVPD3XFKL3tvQRbEWVGZn6m1bKVJ6WkQhLtOgIhAOhnTvlsYJ7OfgForpqYVrUoMIhZc5eewHGlFy+h9D6d"}], "size": 3987961}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.11_1688191442203_0.060650478720550405"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-01T06:04:02.495Z", "publish_time": 1688191442495, "_source_registry_name": "default"}, "0.18.12": {"name": "@esbuild/darwin-x64", "version": "0.18.12", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.12", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-ohqLPc7i67yunArPj1+/FeeJ7AgwAjHqKZ512ADk3WsE3FHU9l+m5aa7NdxXr0HmN1bjDlUslBjWNbFlD9y12Q==", "shasum": "1029cfbd5fe22e5426470dee63511b33eeeb8127", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.12.tgz", "fileCount": 3, "unpackedSize": 9777336, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDv1byMLrMhmPpXaZPU8Oqs+JWddE4Ni8R63PwCgnbCbwIgZbiUtyhpsV1f6WH6qnR5niy1/eTkCDGnEk2yNog+q0w="}], "size": 3988736}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.12_1689212057714_0.36882401366380657"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-13T01:34:17.955Z", "publish_time": 1689212057955, "_source_registry_name": "default"}, "0.18.13": {"name": "@esbuild/darwin-x64", "version": "0.18.13", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.13", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-RIrxoKH5Eo+yE5BtaAIMZaiKutPhZjw+j0OCh8WdvKEKJQteacq0myZvBDLU+hOzQOZWJeDnuQ2xgSScKf1Ovw==", "shasum": "81965b690bae86bf1289b2ce0732506fd41fb545", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.13.tgz", "fileCount": 3, "unpackedSize": 9778168, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCq4+5Gg9YBY07g409NkUCff0kseId3AEIXr0BmbPCR2QIgWeUAEsDU1eU0VbCEgh01mn2ldQ9WteCJe/XX8y636z0="}], "size": 3985759}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.13_1689388641530_0.8491358236912319"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-15T02:37:21.790Z", "publish_time": 1689388641790, "_source_registry_name": "default"}, "0.18.14": {"name": "@esbuild/darwin-x64", "version": "0.18.14", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.14", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-ZnI3Dg4ElQ6tlv82qLc/UNHtFsgZSKZ7KjsUNAo1BF1SoYDjkGKHJyCrYyWjFecmXpvvG/KJ9A/oe0H12odPLQ==", "shasum": "bc1884d9f812647e2078fa4c46e4bffec53c7c09", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.14.tgz", "fileCount": 3, "unpackedSize": 9817208, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk/L23BvZzkRGmV6P7iHk4fL3OaMdyHSq77o5MijHnUAIgZE+RnAsvM5PZrlfY36biUJMHJWYe7gy+bGl1b+X/Bus="}], "size": 4002974}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.14_1689656428310_0.4451857508961541"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-18T05:00:28.622Z", "publish_time": 1689656428622, "_source_registry_name": "default"}, "0.18.15": {"name": "@esbuild/darwin-x64", "version": "0.18.15", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.15", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-bMqBmpw1e//7Fh5GLetSZaeo9zSC4/CMtrVFdj+bqKPGJuKyfNJ5Nf2m3LknKZTS+Q4oyPiON+v3eaJ59sLB5A==", "shasum": "91cd2601c1604d123454d325e6b24fb6438350cf", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.15.tgz", "fileCount": 3, "unpackedSize": 9830024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFECJQr+MyRg3cfv8P7xgh27OqU5wR65i+0lCUvywzIAAiA94mGt6xci60yUlZbWMrKCAgQ/3gMIJulvF+NItCrYFQ=="}], "size": 4007585}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.15_1689857603103_0.6932033625021135"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T12:53:23.329Z", "publish_time": 1689857603329, "_source_registry_name": "default"}, "0.18.16": {"name": "@esbuild/darwin-x64", "version": "0.18.16", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.16", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-6w4Dbue280+rp3LnkgmriS1icOUZDyPuZo/9VsuMUTns7SYEiOaJ7Ca1cbhu9KVObAWfmdjUl4gwy9TIgiO5eA==", "shasum": "a8f3b61bee2807131cbe28eb164ad2b0333b59f5", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.16.tgz", "fileCount": 3, "unpackedSize": 9830200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjnwQv1H1m8NOWrQyuKBxAB0Xmh8TiGEuxqG7pJXiDgAiBM0jOBMurukwGbBiEpWrNjWblE6MYgVvO/QWPw3wGF7A=="}], "size": 4007984}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.16_1690087690378_0.014802360260552927"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-23T04:48:10.629Z", "publish_time": 1690087690629, "_source_registry_name": "default"}, "0.18.17": {"name": "@esbuild/darwin-x64", "version": "0.18.17", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.17", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-XDre+J5YeIJDMfp3n0279DFNrGCXlxOuGsWIkRb1NThMZ0BsrWXoTg23Jer7fEXQ9Ye5QjrvXpxnhzl3bHtk0g==", "shasum": "92f8763ff6f97dff1c28a584da7b51b585e87a7b", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.17.tgz", "fileCount": 3, "unpackedSize": 9851672, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeH/+f4dnsi+IfFa3MZiGnvNyiHWCXZYTfOrernj4rrgIgV445i8Vgx+F32941Yl2sihCi5rUwUYrriDfkvEJza6E="}], "size": 4014876}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.17_1690335658734_0.5149490070939513"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T01:40:59.117Z", "publish_time": 1690335659117, "_source_registry_name": "default"}, "0.18.18": {"name": "@esbuild/darwin-x64", "version": "0.18.18", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.18", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-ARFYISOWkaifjcr48YtO70gcDNeOf1H2RnmOj6ip3xHIj66f3dAbhcd5Nph5np6oHI7DhHIcr9MWO18RvUL1bw==", "shasum": "8aa691d0cbd3fb67f9f9083375c0c72e0463b8b2", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.18.tgz", "fileCount": 3, "unpackedSize": 9868824, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCav74W/DkjJ80dBY7wpTV1Ra12I1M/0w76FW/OC0NB1AIgQd0es5cR32vYBu8KsWKJLlB3yG1EbeTfbbICqhQO7Wc="}], "size": 4025272}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.18_1691255194335_0.48135287625822043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-05T17:06:34.576Z", "publish_time": 1691255194576, "_source_registry_name": "default"}, "0.18.19": {"name": "@esbuild/darwin-x64", "version": "0.18.19", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.19", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-m6JdvXJQt0thNLIcWOeG079h2ivhYH4B5sVCgqb/B29zTcFd7EE8/J1nIUHhdtwGeItdUeqKaqqb4towwxvglQ==", "shasum": "81024ab64232dd323f03796d449f018b59f04ca9", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.19.tgz", "fileCount": 3, "unpackedSize": 9907192, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmveT4I5TRBW3eF2ucBugskhpue892Ji3BjTLxP1qLEgIhAI0o4TY9ZITqD+xdMbtMmgZLw64p1/+MlhvDWz0FTTRK"}], "size": 4037995}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.19_1691376685813_0.44686103370484087"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-07T02:51:26.105Z", "publish_time": 1691376686105, "_source_registry_name": "default"}, "0.18.20": {"name": "@esbuild/darwin-x64", "version": "0.18.20", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.18.20", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==", "shasum": "d70d5790d8bf475556b67d0f8b7c5bdff053d85d", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz", "fileCount": 3, "unpackedSize": 9915560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID10oxBCgi/Da2N0MBeOyNUcSvHALW87hvXvWfGN20lkAiEAmmBxiKaNghHo9vELF18rK4/frSrTW2mOcnXr6VrbWh4="}], "size": 4041474}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.18.20_1691468109846_0.13805144670754332"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-08T04:15:10.097Z", "publish_time": 1691468110097, "_source_registry_name": "default"}, "0.19.0": {"name": "@esbuild/darwin-x64", "version": "0.19.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.0", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-9IRWJjqpWFHM9a5Qs3r3bK834NCFuDY5ZaLrmTjqE+10B6w65UMQzeZjh794JcxpHolsAHqwsN/33crUXNCM2Q==", "shasum": "b5bbde35468db093fdf994880b0eb4b62613b67c", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.0.tgz", "fileCount": 3, "unpackedSize": 9970215, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSb/d01iA6E/N4zwfEm+Ap68h0uNDs0Xok5ZlBPSvcgwIhANb3SJaXx8tICjjnu8rs8ZacEMBLC5g/Q1lDQbtOUlVi"}], "size": 4062213}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.0_1691509958033_0.6024955590123546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-08T15:52:38.349Z", "publish_time": 1691509958349, "_source_registry_name": "default"}, "0.19.1": {"name": "@esbuild/darwin-x64", "version": "0.19.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.1", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-SewtenJi6zCEfZRSUchb+LgJ/IQw8QvnKECPu/qHII1fLQKnVPUVR+VH2IuS03DD9WWnAi3yfOvBNwtrp3WXtg==", "shasum": "445017aec28152dd340464565a6796276adbd234", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.1.tgz", "fileCount": 3, "unpackedSize": 9982759, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGu5x/KJnQ09YaBojEwJE0GnTY4XcuQuQLR+FT+36qZbAiBi388+9NBx5gDStPgh+PD9QwYCgBsePVjQqv2CYXdW7Q=="}], "size": 4071110}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.1_1691769458383_0.9228265032206038"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-11T15:57:38.628Z", "publish_time": 1691769458628, "_source_registry_name": "default"}, "0.19.2": {"name": "@esbuild/darwin-x64", "version": "0.19.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.2", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tP+B5UuIbbFMj2hQaUr6EALlHOIOmlLM2FK7jeFBobPy2ERdohI4Ka6ZFjZ1ZYsrHE/hZimGuU90jusRE0pwDw==", "shasum": "982524af33a6424a3b5cb44bbd52559623ad719c", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.2.tgz", "fileCount": 3, "unpackedSize": 9982919, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCokC7FJ+9FsT+5VRktfcMiSHvbJek4dUchXuGDLXCMugIgSNFFvNLHvt1QApyj2MMuLwOyyAGvgEnZRG81eRCTI0Y="}], "size": 4072635}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.2_1691978305974_0.1981659486320657"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-14T01:58:26.234Z", "publish_time": 1691978306234, "_source_registry_name": "default"}, "0.19.3": {"name": "@esbuild/darwin-x64", "version": "0.19.3", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.3", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tPfZiwF9rO0jW6Jh9ipi58N5ZLoSjdxXeSrAYypy4psA2Yl1dAMhM71KxVfmjZhJmxRjSnb29YlRXXhh3GqzYw==", "shasum": "ebe99f35049180023bb37999bddbe306b076a484", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.3.tgz", "fileCount": 3, "unpackedSize": 9991623, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNC86Vx03wyP1a9QowE0Q5ZYMFJi4KqBUkqedzwoS7kQIgVp3sYs5mpZdLCOa+U9kHhIybCS20JeqkV4r+exgQmBM="}], "size": 4075630}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.3_1694653949123_0.19213371823488012"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-14T01:12:29.423Z", "publish_time": 1694653949423, "_source_registry_name": "default"}, "0.19.4": {"name": "@esbuild/darwin-x64", "version": "0.19.4", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.4", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-YHbSFlLgDwglFn0lAO3Zsdrife9jcQXQhgRp77YiTDja23FrC2uwnhXMNkAucthsf+Psr7sTwYEryxz6FPAVqw==", "shasum": "d8e26e1b965df284692e4d1263ba69a49b39ac7a", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.4.tgz", "fileCount": 3, "unpackedSize": 9991479, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFir0wmxLWkmBCdGYevkaiaRGK6edPXdpABrarQvSjTsAiEA9ZxeRqYtgaj86KnY9Q+lWML2ACyXP8u+CW8f1qJ6lvE="}], "size": 4076424}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.4_1695865639921_0.6785372289438005"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-28T01:47:20.210Z", "publish_time": 1695865640210, "_source_registry_name": "default"}, "0.19.5": {"name": "@esbuild/darwin-x64", "version": "0.19.5", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.5", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Ly8cn6fGLNet19s0X4unjcniX24I0RqjPv+kurpXabZYSXGM4Pwpmf85WHJN3lAgB8GSth7s5A0r856S+4DyiA==", "shasum": "2c553e97a6d2b4ae76a884e35e6cbab85a990bbf", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.5.tgz", "fileCount": 3, "unpackedSize": 10000039, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnCH6qt/DHvhqU8Gsjc+NyVFwgsQK/2K3qMqt4oiokNgIhAIjokfB/zOmOovENPm3bsMt9n5Emz9/KsvAQ4H1CelFl"}], "size": 4078370}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.5_1697519439049_0.9590789002283062"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-17T05:10:39.398Z", "publish_time": 1697519439398, "_source_registry_name": "default"}, "0.19.6": {"name": "@esbuild/darwin-x64", "version": "0.19.6", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.6", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-QCGHw770ubjBU1J3ZkFJh671MFajGTYMZumPs9E/rqU52md6lIil97BR0CbPq6U+vTh3xnTNDHKRdR8ggHnmxQ==", "shasum": "400bf20f9a35a7d68a17f5898c0f9ecb099f062b", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.6.tgz", "fileCount": 3, "unpackedSize": 10020887, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzwEdFSn9U52Dyore+eTgUuKrHdC/BPBEzx/KoARBKdQIgG8NYQzRqPEicI2dhLJbhXlHkxLCUu++iHLlu7xB+ho4="}], "size": 4086440}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.6_1700377902781_0.3993247158550357"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-19T07:11:43.115Z", "publish_time": 1700377903115, "_source_registry_name": "default"}, "0.19.7": {"name": "@esbuild/darwin-x64", "version": "0.19.7", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.7", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Lc0q5HouGlzQEwLkgEKnWcSazqr9l9OdV2HhVasWJzLKeOt0PLhHaUHuzb8s/UIya38DJDoUm74GToZ6Wc7NGQ==", "shasum": "02d1f8a572874c90d8f55dde8a859e5145bd06f6", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.7.tgz", "fileCount": 3, "unpackedSize": 10058375, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChat4CJq0GZn2r4VXzgG18sNwsFMSpboi2WCmQvjUrxQIhAPe+qEIaXA1m0PE27GL8604QSTE0poaHIE2V/E01u0PA"}], "size": 4098660}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.7_1700528468768_0.44510830877334095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-21T01:01:09.078Z", "publish_time": 1700528469078, "_source_registry_name": "default"}, "0.19.8": {"name": "@esbuild/darwin-x64", "version": "0.19.8", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.8", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-3sur80OT9YdeZwIVgERAysAbwncom7b4bCI2XKLjMfPymTud7e/oY4y+ci1XVp5TfQp/bppn7xLw1n/oSQY3/Q==", "shasum": "75c5c88371eea4bfc1f9ecfd0e75104c74a481ac", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.8.tgz", "fileCount": 3, "unpackedSize": 10058359, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBPfiKx7DXLRM2Ks9SfHY9TuzoMYSDm30B0XggLTC6/DAiEA+/Mpx92QVeJV+InL8lpb2r2JZsZEEhsjNfWs6QZFpjM="}], "size": 4098388}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.8_1701040091057_0.5651626049851046"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-26T23:08:11.357Z", "publish_time": 1701040091357, "_source_registry_name": "default"}, "0.19.9": {"name": "@esbuild/darwin-x64", "version": "0.19.9", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.9", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-vE0VotmNTQaTdX0Q9dOHmMTao6ObjyPm58CHZr1UK7qpNleQyxlFlNCaHsHx6Uqv86VgPmR4o2wdNq3dP1qyDQ==", "shasum": "8a216c66dcf51addeeb843d8cfaeff712821d12b", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.9.tgz", "fileCount": 3, "unpackedSize": 10177911, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGLdnvtLUZUSW67ae8xMtNMqNb/6JKOndydqINcYaDr3AiBCGUA+ExDIICdoHj8rhyyMv1aR06PPZfmd+y07j6BSoA=="}], "size": 4145005}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.9_1702184970766_0.30809473818034894"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-10T05:09:31.020Z", "publish_time": 1702184971020, "_source_registry_name": "default"}, "0.19.10": {"name": "@esbuild/darwin-x64", "version": "0.19.10", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.10", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-alfGtT+IEICKtNE54hbvPg13xGBe4GkVxyGWtzr+yHO7HIiRJppPDhOKq3zstTcVf8msXb/t4eavW3jCDpMSmA==", "shasum": "d1de20bfd41bb75b955ba86a6b1004539e8218c1", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.10.tgz", "fileCount": 3, "unpackedSize": 10182104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwHIDPg6/glRrwofDNR1I1+y/37wDeidVOTmjgbxEVoAiA+WyG3eKNImJQqKDIOMrQvZ7rTm5Hp0YLOBsdC58jr2w=="}], "size": 4147043}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.10_1702945301748_0.655678259971435"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-19T00:21:42.021Z", "publish_time": 1702945302021, "_source_registry_name": "default"}, "0.19.11": {"name": "@esbuild/darwin-x64", "version": "0.19.11", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/darwin-x64@0.19.11", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-fkFUiS6IUK9WYUO/+22omwetaSNl5/A8giXvQlcinLIjVkxwTLSktbF5f/kJMftM2MJp9+fXqZ5ezS7+SALp4g==", "shasum": "62f3819eff7e4ddc656b7c6815a31cf9a1e7d98e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.11.tgz", "fileCount": 3, "unpackedSize": 10182088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGEmdm4g/bZspAu4XoyDUlCoCHYIZJTpGDJdgRyY3XsFAiEA/YvYPkmccQNFzm5iynyF/f33kwcIDjfB5fTViKihhrs="}], "size": 4147564}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.11_1703881918292_0.6431343698447789"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-29T20:31:58.547Z", "publish_time": 1703881918547, "_source_registry_name": "default"}, "0.19.12": {"name": "@esbuild/darwin-x64", "version": "0.19.12", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.19.12", "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-hKoVkKzFiToTgn+41qGhsUJXFlIjxI/jSYeZf3ugemDYZldIXIxhvwN6erJGlX4t5h417iFuheZ7l+YVn05N3A==", "shasum": "e37d9633246d52aecf491ee916ece709f9d5f4cd", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.19.12.tgz", "fileCount": 3, "unpackedSize": 10182008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGOtVTARNX7CmrxT28451R7XhaKMUw6cYE3/FgItkl+KAiEAqpVcg0R1HoFL9vemCwUDs0/xVN2XUD6clmUSLKlfP0Q="}], "size": 4208172}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.19.12_1706031631811_0.7656747794448684"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-23T17:40:32.023Z", "publish_time": 1706031632023, "_source_registry_name": "default"}, "0.20.0": {"name": "@esbuild/darwin-x64", "version": "0.20.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.20.0", "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-bsgTPoyYDnPv8ER0HqnJggXK6RyFy4PH4rtsId0V7Efa90u2+EifxytE9pZnsDgExgkARy24WUQGv9irVbTvIw==", "shasum": "9561d277002ba8caf1524f209de2b22e93d170c1", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.20.0.tgz", "fileCount": 3, "unpackedSize": 10182051, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPh1bsXJQEFc2ZG6vO1mwbO2+WMSaX156i92VkSNs8KgIgAbyHsvCFOHS/ahKuCxUAmO7MpH+gAFI9crK97BzfRog="}], "size": 4208565}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.20.0_1706374178508_0.19259841026652613"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-27T16:49:38.776Z", "publish_time": 1706374178776, "_source_registry_name": "default"}, "0.20.1": {"name": "@esbuild/darwin-x64", "version": "0.20.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.20.1", "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-pFIfj7U2w5sMp52wTY1XVOdoxw+GDwy9FsK3OFz4BpMAjvZVs0dT1VXs8aQm22nhwoIWUmIRaE+4xow8xfIDZA==", "shasum": "92504077424584684862f483a2242cfde4055ba2", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.20.1.tgz", "fileCount": 3, "unpackedSize": 10199283, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAW1TFFJVWcPBf1np7AVmqdOBYmGOOSsSFJkDhfs61UoAiEAt9UU59xfZfZcYznMJxu8CmRwYJVEZ6DIib6PoiRzzEg="}], "size": 4213759}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.20.1_1708324688616_0.08791653026960056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-19T06:38:08.813Z", "publish_time": 1708324688813, "_source_registry_name": "default"}, "0.20.2": {"name": "@esbuild/darwin-x64", "version": "0.20.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.20.2", "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-tBcXp9KNphnNH0dfhv8KYkZhjc+H3XBkF5DKtswJblV7KlT9EI2+jeA8DgBjp908WEuYll6pF+UStUCfEpdysA==", "shasum": "90ed098e1f9dd8a9381695b207e1cff45540a0d0", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.20.2.tgz", "fileCount": 3, "unpackedSize": 10203379, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTNuiPk6eDHR61U7a01dKSv+Cx8GHKOkV0o4o/Jaw5bAiALWev4JekQ4pGG4rlADmm7DG1qVGGQDY2hwgV+k2fPzQ=="}], "size": 4215541}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.20.2_1710445788044_0.7596318284779415"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-14T19:49:48.278Z", "publish_time": 1710445788278, "_source_registry_name": "default"}, "0.21.0": {"name": "@esbuild/darwin-x64", "version": "0.21.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.21.0", "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-+dmvTVqVkAArjJyIbo4Rl2S4I4A/yRuivTPR9Igw0QMBVSJegJqixKxZvKLCh8xi6n8tePdq3EpfbFYH2KNNiw==", "shasum": "98c82eac94f0a61558da3da727be9bbbfbfbc1ab", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.0.tgz", "fileCount": 3, "unpackedSize": 10282371, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgiPC+gqfTABYlBwfZWQA5de1l5nuniem+X/UDU+x77gIgEDJBWSLDObmhpIDAU4vxXPHNJBc/63ESZouKd8voTkw="}], "size": 4249836}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.21.0_1715050352293_0.06261333128273971"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T02:52:32.478Z", "publish_time": 1715050352478, "_source_registry_name": "default"}, "0.21.1": {"name": "@esbuild/darwin-x64", "version": "0.21.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.21.1", "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-D3h3wBQmeS/vp93O4B+SWsXB8HvRDwMyhTNhBd8yMbh5wN/2pPWRW5o/hM3EKgk9bdKd9594lMGoTCTiglQGRQ==", "shasum": "db971502c9fa204906b89e489810c902bf6d9afb", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.1.tgz", "fileCount": 3, "unpackedSize": 10278275, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/to+VyRNvYZYwItwqYIEq89H64S5DGHQyPZbr/7DWawIgGoTpi532wPcJnLFV8c3kcudW7vF7Jts+LJkBxUTg0B8="}], "size": 4248797}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.21.1_1715100909617_0.360035057187857"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T16:55:09.847Z", "publish_time": 1715100909847, "_source_registry_name": "default"}, "0.21.2": {"name": "@esbuild/darwin-x64", "version": "0.21.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.21.2", "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-K4ZdVq1zP9v51h/cKVna7im7G0zGTKKB6bP2yJiSmHjjOykbd8DdhrSi8V978sF69rkwrn8zCyL2t6I3ei6j9A==", "shasum": "f295d838c60e0e068c7a91e7784674c6b06c358e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.2.tgz", "fileCount": 3, "unpackedSize": 10278275, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc88TPT2Yj0E7n0vLVN/tY1KwNbxMiWB2xRCO3KMA6gAIhAOFp9YljF9NArD/WWoEccWTh4G3TauJ8C/NTN9lj/n64"}], "size": 4249104}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.21.2_1715545979412_0.19382047309051775"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-12T20:32:59.666Z", "publish_time": 1715545979666, "_source_registry_name": "default"}, "0.21.3": {"name": "@esbuild/darwin-x64", "version": "0.21.3", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.21.3", "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-3m1CEB7F07s19wmaMNI2KANLcnaqryJxO1fXHUV5j1rWn+wMxdUYoPyO2TnAbfRZdi7ADRwJClmOwgT13qlP3Q==", "shasum": "2e450b8214f179a56b4559b2f107060e2b792c7e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.3.tgz", "fileCount": 3, "unpackedSize": 10274403, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB4KjY72Kndje7CshAmXOk4uggr0FftNgqud1p1IlZ8/AiBNkugAgpr9NLbpfRYRtCSvdlVjIAA0MTx0725grhVHNA=="}], "size": 4246530}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.21.3_1715806358380_0.3995898982857369"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T20:52:38.650Z", "publish_time": 1715806358650, "_source_registry_name": "default"}, "0.21.4": {"name": "@esbuild/darwin-x64", "version": "0.21.4", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.21.4", "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-uBsuwRMehGmw1JC7Vecu/upOjTsMhgahmDkWhGLWxIgUn2x/Y4tIwUZngsmVb6XyPSTXJYS4YiASKPcm9Zitag==", "shasum": "db623553547a5fe3502a63aa88306e9023178482", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.4.tgz", "fileCount": 3, "unpackedSize": 10286867, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnYmxuKa/bf9J9//aiFZVAVTfhCVKq5KEJHFoacfTTrwIhAJ2Cs/Fdem9/29cgNQlqH36D/i7MH+Z4vYHe6jlQoqu8"}], "size": 4251140}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.21.4_1716603054532_0.4452147825003405"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-25T02:10:55.140Z", "publish_time": 1716603055140, "_source_registry_name": "default"}, "0.21.5": {"name": "@esbuild/darwin-x64", "version": "0.21.5", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.21.5", "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==", "shasum": "c13838fa57372839abdddc91d71542ceea2e1e22", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz", "fileCount": 3, "unpackedSize": 10291059, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGkiAHbtGYfFPTxryzui/RLFsF2zUfNip/Y/kLjKd6rNAiEA7V9brdgg1walA8z4HbqEvORxHwsjewTYdz1in9KghUc="}], "size": 4251628}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.21.5_1717967820723_0.80096333545815"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-09T21:17:00.976Z", "publish_time": 1717967820976, "_source_registry_name": "default"}, "0.22.0": {"name": "@esbuild/darwin-x64", "version": "0.22.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.22.0", "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-vTaTQ9OgYc3VTaWtOE5pSuDT6H3d/qSRFRfSBbnxFfzAvYoB3pqKXA0LEbi/oT8GUOEAutspfRMqPj2ezdFaMw==", "shasum": "d07b4fe501fe9985590285b2790039ed4743f86e", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.22.0.tgz", "fileCount": 3, "unpackedSize": 10214675, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+4mJpT7QAtOMwAJHs54Hc9DHsLCXkIQLlV5CavuGbOQIgPQglb4COExCmMH+tDyyPUyZliVtvCaYAUdusgSQDhHE="}], "size": 4299428}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.22.0_1719779871450_0.724312998588635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T20:37:51.713Z", "publish_time": 1719779871713, "_source_registry_name": "default"}, "0.23.0": {"name": "@esbuild/darwin-x64", "version": "0.23.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.23.0", "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-IMQ6eme4AfznElesHUPDZ+teuGwoRmVuuixu7sv92ZkdQcPbsNHzutd+rAfaBKo8YK3IrBEi9SLLKWJdEvJniQ==", "shasum": "30c8f28a7ef4e32fe46501434ebe6b0912e9e86c", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.23.0.tgz", "fileCount": 3, "unpackedSize": 10214675, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTanux2J7o2OOy/jXYN5+RKzdnDsxyQoLKK4n3NBohAAIgQyB3Em5VAUUm5zSoxtwL89DJ+8hsPbfoPLwPJi/X5w4="}], "size": 4299347}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.23.0_1719891229486_0.18297626986997773"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T03:33:49.792Z", "publish_time": 1719891229792, "_source_registry_name": "default"}, "0.23.1": {"name": "@esbuild/darwin-x64", "version": "0.23.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.23.1", "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-aClqdgTDVPSEGgoCS8QDG37Gu8yc9lTHNAQlsztQ6ENetKEO//b8y31MMu2ZaPbn4kVsIABzVLXYLhCGekGDqw==", "shasum": "c58353b982f4e04f0d022284b8ba2733f5ff0931", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.23.1.tgz", "fileCount": 3, "unpackedSize": 10218787, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmGXuRIPtlDYtq5ftnhUmDtn9DXUo3SQFyTsAlwAu3OgIgHekalx6xBT2B9vKm8uy7BvweLrOsfziIIw2rC6q2PmA="}], "size": 4300407}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.23.1_1723846399744_0.21313339110902674"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-16T22:13:19.973Z", "publish_time": 1723846399973, "_source_registry_name": "default"}, "0.24.0": {"name": "@esbuild/darwin-x64", "version": "0.24.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.24.0", "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-rgtz6flkVkh58od4PwTRqxbKH9cOjaXCMZgWD905JOzjFKW+7EiUObfd/Kav+A6Gyud6WZk9w+xu6QLytdi2OA==", "shasum": "33087aab31a1eb64c89daf3d2cf8ce1775656107", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.24.0.tgz", "fileCount": 3, "unpackedSize": 10423907, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPruU+hxsLbcQggStoX0erSQCXdul2S+TEQkVSTqkqkAIhAI2fFWaa/IM//nDnlhkPYlNlbd37YVzbZXFgmgv6uONr"}], "size": 4396112}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/darwin-x64_0.24.0_1726970787754_0.8990558700910856"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-22T02:06:27.988Z", "publish_time": 1726970787988, "_source_registry_name": "default"}, "0.24.1": {"name": "@esbuild/darwin-x64", "version": "0.24.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.24.1", "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-siNAX65WSBKU7c+XJEKByMruE/JsHY9HU+n5BIMiNlo5axVbWwGXN4HhJQzOlY4TtOwSt3LRbS0zuI5SOjgoyg==", "shasum": "c8350c72d53fb118fbc08984b44b93ed207a63f0", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.24.1.tgz", "fileCount": 3, "unpackedSize": 10432691, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3fJzfJrJ4BoPam7fBCfmKkJOn3RUmknT2jY6i5yXb0AiEA7L24KZBtXvqYHY4P+F6RB5HaVaT3Y5+uPilOpqtKPSY="}], "size": 4400008}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.24.1_1734673251161_0.644477979566425"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T05:40:51.629Z", "publish_time": 1734673251629, "_source_registry_name": "default"}, "0.24.2": {"name": "@esbuild/darwin-x64", "version": "0.24.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.24.2", "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-WeSrmwwHaPkNR5H3yYfowhZcbriGqooyu3zI/3GGpF8AyUdsrrP0X6KumITGA9WOyiJavnGZUwPGvxvwfWPHIA==", "shasum": "9ec312bc29c60e1b6cecadc82bd504d8adaa19e9", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.24.2.tgz", "fileCount": 3, "unpackedSize": 10432691, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAMfhRNu/4rDrP7RhMhhkBY1CzfHiAQoYU6KGsD/tthAIhAIM7ZbeCbxCcs7wEKadBKz+E4M6BkI2YqujUzt4OCC8/"}], "size": 4400272}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.24.2_1734717373788_0.5649679786760287"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T17:56:14.010Z", "publish_time": 1734717374010, "_source_registry_name": "default"}, "0.25.0": {"name": "@esbuild/darwin-x64", "version": "0.25.0", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.0", "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg==", "shasum": "91979d98d30ba6e7d69b22c617cc82bdad60e47a", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.0.tgz", "fileCount": 3, "unpackedSize": 10486739, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFtIR5Dyet3If9nzSVG8pg2/2KQUdPa9GPqFL/g5JNxTAiBqjkk5uNwmMS/CFjg/pMduGlxo1ICui7Hj/kNsQgnaKA=="}], "size": 4419986}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.0_1738983732892_0.4946405944844028"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-08T03:02:13.146Z", "publish_time": 1738983733146, "_source_registry_name": "default"}, "0.25.1": {"name": "@esbuild/darwin-x64", "version": "0.25.1", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.1", "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-hxVnwL2Dqs3fM1IWq8Iezh0cX7ZGdVhbTfnOy5uURtao5OIVCEyj9xIzemDi7sRvKsuSdtCAhMKarxqtlyVyfA==", "shasum": "0643e003bb238c63fc93ddbee7d26a003be3cd98", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.1.tgz", "fileCount": 3, "unpackedSize": 10495027, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDnmc6U/6/YTpmel+i23OoSkzK8nFPwo7IY2Q++KGyNMwIhAKjgst/m3zVqFZjW1eUnw7PdpgXLR0UzK40TyAsu4JTr"}], "size": 4421417}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.1_1741578321484_0.46203624183679404"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-10T03:45:21.706Z", "publish_time": 1741578321706, "_source_registry_name": "default"}, "0.25.2": {"name": "@esbuild/darwin-x64", "version": "0.25.2", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.2", "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-5eRPrTX7wFyuWe8FqEFPG2cU0+butQQVNcT4sVipqjLYQjjh8a8+vUTfgBKM88ObB85ahsnTwF7PSIt6PG+QkA==", "shasum": "95ee222aacf668c7a4f3d7ee87b3240a51baf374", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.2.tgz", "fileCount": 3, "unpackedSize": 10499171, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB+tmSW8AYQftpxEwf+rAXnhGAJHJzCGn/GysnE0JunNAiBEHjY7p7Sx0ib6gA2MEPWSoqI5wqxHuIgRrAtfFyRUSQ=="}], "size": 4423108}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.2_1743355971446_0.9116206624093472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-30T17:32:51.655Z", "publish_time": 1743355971655, "_source_registry_name": "default"}, "0.25.3": {"name": "@esbuild/darwin-x64", "version": "0.25.3", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.3", "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Kd8glo7sIZtwOLcPbW0yLpKmBNWMANZhrC1r6K++uDR2zyzb6AeOYtI6udbtabmQpFaxJ8uduXMAo1gs5ozz8A==", "shasum": "d8c5342ec1a4bf4b1915643dfe031ba4b173a87a", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.3.tgz", "fileCount": 3, "unpackedSize": 10503299, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCZJE+zeZlvufrGOX0vm4E3QKKN+hEzT4aQnPaOr4IG5gIhANFSxS+VRtCHE5590GT6bmPndkf+e2gCOsAPQdgrtsml"}], "size": 4427533}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.3_1745380556034_0.055908037489611484"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-23T03:55:56.260Z", "publish_time": 1745380556260, "_source_registry_name": "default"}, "0.25.4": {"name": "@esbuild/darwin-x64", "version": "0.25.4", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.4", "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-C<PERSON><PERSON>ry8ZGM5VFVeyUYB3cdKpd/H69PYez4eJh1W/t38vzutdjEjtP7hB6eLKBoOdxcAlCtEYHzQ/PJ/oU9I4u0A==", "shasum": "e6813fdeba0bba356cb350a4b80543fbe66bf26f", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.4.tgz", "fileCount": 3, "unpackedSize": 10511555, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDPgIoH0Hq19pG+y17mddIoTtEuCN4JSMLL+QCDwnLjNwIhAK4Gi4gkqm2lepH6awaquzuuLpM0DM7aW/zo5Yr6Om6Q"}], "size": 4433131}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.4_1746491438281_0.04924190102118553"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T00:30:38.520Z", "publish_time": 1746491438520, "_source_registry_name": "default"}, "0.25.5": {"name": "@esbuild/darwin-x64", "version": "0.25.5", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==", "shasum": "e27a5d92a14886ef1d492fd50fc61a2d4d87e418", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 10515651, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICBgJIApfB9I9lcPfSqChiA+VYVpxJ9/ao62DAQ86rKyAiAoMDpkPM7BBN2vADOohlp/e79hIBfjLTnkIWBknWsLPQ=="}], "size": 4434256}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.5_1748315562556_0.15485791049823594"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-27T03:12:42.782Z", "publish_time": 1748315562782, "_source_registry_name": "default"}, "0.25.6": {"name": "@esbuild/darwin-x64", "version": "0.25.6", "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["darwin"], "cpu": ["x64"], "_id": "@esbuild/darwin-x64@0.25.6", "gitHead": "d38c1f0bc580b4a8a93f23559d0cd9085d7ba31f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-GfXs5kry/TkGM2vKqK2oyiLFygJRqKVhawu3+DOCk7OxLy/6jYkWXhlHwOoTb0WqGnWGAS7sooxbZowy+pK9Yg==", "shasum": "2b4a6cedb799f635758d7832d75b23772c8ef68f", "tarball": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.6.tgz", "fileCount": 3, "unpackedSize": 10515731, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIC3DHFSvulmgNA1GiO35QB/6dn5mdlAFlxso1y5HTH2XAiAqf/iJzDAtItE1z43G53If0jVWoH/iZVa2p437W2iNFA=="}], "size": 4436251}, "_npmUser": {"name": "evanw", "email": "<EMAIL>", "actor": {"name": "evanw", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/darwin-x64_0.25.6_1751907534867_0.6731181799438712"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T16:58:55.105Z", "publish_time": 1751907535105, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "description": "The macOS 64-bit binary for esbuild, a JavaScript bundler.", "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the macOS 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "_source_registry_name": "default"}