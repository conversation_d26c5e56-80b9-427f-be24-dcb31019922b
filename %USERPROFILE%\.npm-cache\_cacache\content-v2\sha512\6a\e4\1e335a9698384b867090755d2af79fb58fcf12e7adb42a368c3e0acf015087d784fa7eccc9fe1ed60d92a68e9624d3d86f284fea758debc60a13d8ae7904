{"_attachments": {}, "_id": "cache-loader", "_rev": "271140-61f1b4e2e5d4ac060adc6c29", "author": {"name": "<PERSON> @sokra"}, "description": "Caches the result of following loaders on disk.", "dist-tags": {"latest": "4.1.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "name": "cache-loader", "readme": "<div align=\"center\">\n  <a href=\"https://webpack.js.org/\">\n    <img width=\"200\" height=\"200\" src=\"https://cdn.rawgit.com/webpack/media/e7485eb2/logo/icon-square-big.svg\">\n  </a>\n</div>\n\n[![npm][npm]][npm-url]\n[![node][node]][node-url]\n[![deps][deps]][deps-url]\n[![tests][tests]][tests-url]\n[![coverage][cover]][cover-url]\n[![chat][chat]][chat-url]\n[![size][size]][size-url]\n\n# cache-loader\n\nThe `cache-loader` allow to Caches the result of following loaders on disk (default) or in the database.\n\n## Getting Started\n\nTo begin, you'll need to install `cache-loader`:\n\n```console\nnpm install --save-dev cache-loader\n```\n\nAdd this loader in front of other (expensive) loaders to cache the result on disk.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.ext$/,\n        use: ['cache-loader', ...loaders],\n        include: path.resolve('src'),\n      },\n    ],\n  },\n};\n```\n\n> ⚠️ Note that there is an overhead for saving the reading and saving the cache file, so only use this loader to cache expensive loaders.\n\n## Options\n\n|         Name          |                       Type                       |                        n Default                        | Description                                                                                                                                                            |\n| :-------------------: | :----------------------------------------------: | :-----------------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n|  **`cacheContext`**   |                    `{String}`                    |                       `undefined`                       | Allows you to override the default cache context in order to generate the cache relatively to a path. By default it will use absolute paths                            |\n|    **`cacheKey`**     |    `{Function(options, request) -> {String}}`    |                       `undefined`                       | Allows you to override default cache key generator                                                                                                                     |\n| **`cacheDirectory`**  |                    `{String}`                    | `findCacheDir({ name: 'cache-loader' }) or os.tmpdir()` | Provide a cache directory where cache items should be stored (used for default read/write implementation)                                                              |\n| **`cacheIdentifier`** |                    `{String}`                    |     `cache-loader:{version} {process.env.NODE_ENV}`     | Provide an invalidation identifier which is used to generate the hashes. You can use it for extra dependencies of loaders (used for default read/write implementation) |\n|     **`compare`**     |      `{Function(stats, dep) -> {Boolean}}`       |                       `undefined`                       | Allows you to override default comparison function between the cached dependency and the one is being read. Return `true` to use the cached resource                   |\n|    **`precision`**    |                    `{Number}`                    |                           `0`                           | Round `mtime` by this number of milliseconds both for `stats` and `dep` before passing those params to the comparing function                                          |\n|      **`read`**       |    `{Function(cacheKey, callback) -> {void}}`    |                       `undefined`                       | Allows you to override default read cache data from file                                                                                                               |\n|    **`readOnly`**     |                   `{Boolean}`                    |                         `false`                         | Allows you to override default value and make the cache read only (useful for some environments where you don't want the cache to be updated, only read from it)       |\n|      **`write`**      | `{Function(cacheKey, data, callback) -> {void}}` |                       `undefined`                       | Allows you to override default write cache data to file (e.g. Redis, memcached)                                                                                        |\n\n## Examples\n\n### Basic\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.js$/,\n        use: ['cache-loader', 'babel-loader'],\n        include: path.resolve('src'),\n      },\n    ],\n  },\n};\n```\n\n### Database Integration\n\n**webpack.config.js**\n\n```js\n// Or different database client - memcached, mongodb, ...\nconst redis = require('redis');\nconst crypto = require('crypto');\n\n// ...\n// connect to client\n// ...\n\nconst BUILD_CACHE_TIMEOUT = 24 * 3600; // 1 day\n\nfunction digest(str) {\n  return crypto\n    .createHash('md5')\n    .update(str)\n    .digest('hex');\n}\n\n// Generate own cache key\nfunction cacheKey(options, request) {\n  return `build:cache:${digest(request)}`;\n}\n\n// Read data from database and parse them\nfunction read(key, callback) {\n  client.get(key, (err, result) => {\n    if (err) {\n      return callback(err);\n    }\n\n    if (!result) {\n      return callback(new Error(`Key ${key} not found`));\n    }\n\n    try {\n      let data = JSON.parse(result);\n      callback(null, data);\n    } catch (e) {\n      callback(e);\n    }\n  });\n}\n\n// Write data to database under cacheKey\nfunction write(key, data, callback) {\n  client.set(key, JSON.stringify(data), 'EX', BUILD_CACHE_TIMEOUT, callback);\n}\n\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.js$/,\n        use: [\n          {\n            loader: 'cache-loader',\n            options: {\n              cacheKey,\n              read,\n              write,\n            },\n          },\n          'babel-loader',\n        ],\n        include: path.resolve('src'),\n      },\n    ],\n  },\n};\n```\n\n## Contributing\n\nPlease take a moment to read our contributing guidelines if you haven't yet done so.\n\n[CONTRIBUTING](./.github/CONTRIBUTING.md)\n\n## License\n\n[MIT](./LICENSE)\n\n[npm]: https://img.shields.io/npm/v/cache-loader.svg\n[npm-url]: https://npmjs.com/package/cache-loader\n[node]: https://img.shields.io/node/v/cache-loader.svg\n[node-url]: https://nodejs.org\n[deps]: https://david-dm.org/webpack-contrib/cache-loader.svg\n[deps-url]: https://david-dm.org/webpack-contrib/cache-loader\n[tests]: https://dev.azure.com/webpack-contrib/cache-loader/_apis/build/status/webpack-contrib.cache-loader?branchName=master\n[tests-url]: https://dev.azure.com/webpack-contrib/cache-loader/_build/latest?definitionId=4&branchName=master\n[cover]: https://codecov.io/gh/webpack-contrib/cache-loader/branch/master/graph/badge.svg\n[cover-url]: https://codecov.io/gh/webpack-contrib/cache-loader\n[chat]: https://badges.gitter.im/webpack/webpack.svg\n[chat-url]: https://gitter.im/webpack/webpack\n[size]: https://packagephobia.now.sh/badge?p=cache-loader\n[size-url]: https://packagephobia.now.sh/result?p=cache-loader\n", "time": {"created": "2022-01-26T20:53:54.285Z", "modified": "2025-06-20T23:11:54.542Z", "4.1.0": "2019-07-18T13:19:23.544Z", "4.0.1": "2019-06-26T12:18:52.557Z", "4.0.0": "2019-06-05T19:49:48.655Z", "3.0.1": "2019-05-13T11:32:36.242Z", "3.0.0": "2019-04-19T15:47:29.514Z", "2.0.1": "2019-01-04T19:25:25.475Z", "2.0.0": "2018-12-21T13:13:23.682Z", "1.2.5": "2018-10-31T09:49:14.324Z", "1.2.4": "2018-10-31T04:31:53.104Z", "1.2.3": "2018-10-30T14:13:08.825Z", "1.2.2": "2018-02-27T21:18:37.555Z", "1.2.1": "2018-02-26T22:30:48.711Z", "1.2.0": "2017-11-17T14:41:42.482Z", "1.1.0": "2017-10-09T01:53:42.471Z", "1.0.3": "2017-04-26T09:20:54.739Z", "1.0.2": "2017-04-26T09:16:04.302Z", "1.0.1": "2017-04-26T09:07:30.273Z"}, "versions": {"4.1.0": {"name": "cache-loader", "version": "4.1.0", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"buffer-json": "^2.0.0", "find-cache-dir": "^3.0.0", "loader-utils": "^1.2.3", "mkdirp": "^0.5.1", "neo-async": "^2.6.1", "schema-utils": "^2.0.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.6", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^5.0.0", "del-cli": "^2.0.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.18.0", "file-loader": "^4.1.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^9.2.0", "memory-fs": "^0.4.1", "normalize-path": "^3.0.0", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^6.0.1", "uuid": "^3.3.2", "webpack": "^4.36.1", "webpack-cli": "^3.3.6"}, "keywords": ["webpack"], "gitHead": "3371562bc05c645dcd20e851ec6c0cc82387fd7f", "_id": "cache-loader@4.1.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.10.1", "dist": {"shasum": "9948cae353aec0a1fcb1eafda2300816ec85387e", "size": 7891, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-4.1.0.tgz", "integrity": "sha512-ftOayxve0PwKzBF/GLsZNC9fJBXl8lkZE3TOsjkboHfVHVkL39iUEs1FO07A33mizmci5Dudt38UZrrYXDtbhw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_4.1.0_1563455963384_0.1536289880950128"}, "_hasShrinkwrap": false, "publish_time": 1563455963544, "_cnpm_publish_time": 1563455963544, "_cnpmcore_publish_time": "2021-12-16T12:40:38.378Z"}, "4.0.1": {"name": "cache-loader", "version": "4.0.1", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"buffer-json": "^2.0.0", "find-cache-dir": "^3.0.0", "loader-utils": "^1.2.3", "mkdirp": "^0.5.1", "neo-async": "^2.6.1", "schema-utils": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/defaults": "^5.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.6", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^2.0.0", "eslint": "^5.16.0", "eslint-config-prettier": "^4.3.0", "eslint-plugin-import": "^2.17.3", "eslint-plugin-prettier": "^3.1.0", "file-loader": "^4.0.0", "husky": "^2.4.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^8.1.7", "memory-fs": "^0.4.1", "normalize-path": "^3.0.0", "npm-run-all": "^4.1.5", "prettier": "^1.17.1", "standard-version": "^6.0.1", "uuid": "^3.3.2", "webpack": "^4.33.0", "webpack-cli": "^3.3.2"}, "keywords": ["webpack"], "gitHead": "fe5f341fd4cb6bfb2653b6b52ae207336805784b", "_id": "cache-loader@4.0.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.9.0", "dist": {"shasum": "198049cf04e9e599ad90bc6a9c695d5e866cc875", "size": 7768, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-4.0.1.tgz", "integrity": "sha512-r5uLI/Igk1qomgoaCvX3LiiDJfKJ03oeAOV7RTrLdkzwxmwjw/JZUr9LYPRsM6CV99SGnR55Tve534aSZsH0mw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_4.0.1_1561551532438_0.39133398018505483"}, "_hasShrinkwrap": false, "publish_time": 1561551532557, "_cnpm_publish_time": 1561551532557, "_cnpmcore_publish_time": "2021-12-16T12:40:38.982Z"}, "4.0.0": {"name": "cache-loader", "version": "4.0.0", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"buffer-json": "^2.0.0", "find-cache-dir": "^3.0.0", "loader-utils": "^1.2.3", "mkdirp": "^0.5.1", "neo-async": "^2.6.1", "schema-utils": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/defaults": "^5.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.6", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^2.0.0", "eslint": "^5.16.0", "eslint-config-prettier": "^4.3.0", "eslint-plugin-import": "^2.17.3", "eslint-plugin-prettier": "^3.1.0", "file-loader": "^4.0.0", "husky": "^2.4.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^8.1.7", "memory-fs": "^0.4.1", "normalize-path": "^3.0.0", "npm-run-all": "^4.1.5", "prettier": "^1.17.1", "standard-version": "^6.0.1", "webpack": "^4.33.0", "webpack-cli": "^3.3.2"}, "keywords": ["webpack"], "gitHead": "b357e885c8c86842cef65c562db0bcb51d42306a", "_id": "cache-loader@4.0.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.9.0", "dist": {"shasum": "f85ffae27780f6ab5c6732b4f12b84cb6bf193d2", "size": 7449, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-4.0.0.tgz", "integrity": "sha512-QTLCBoDju/InytQyQNHsn0q0xDlInCtId09CW8BLhFZY72GcqDHGbJdSiulsv1YBwB4zvy2QhioPADoSAl7qjg=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_4.0.0_1559764188528_0.988661080547967"}, "_hasShrinkwrap": false, "publish_time": 1559764188655, "_cnpm_publish_time": 1559764188655, "_cnpmcore_publish_time": "2021-12-16T12:40:39.250Z"}, "3.0.1": {"name": "cache-loader", "version": "3.0.1", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint": "eslint --cache src test", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"buffer-json": "^2.0.0", "find-cache-dir": "^2.1.0", "loader-utils": "^1.2.3", "mkdirp": "^0.5.1", "neo-async": "^2.6.1", "schema-utils": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@commitlint/cli": "^7.6.1", "@commitlint/config-conventional": "^7.6.0", "@webpack-contrib/defaults": "^4.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "babel-loader": "^8.0.6", "commitlint-azure-pipelines-cli": "^1.0.1", "cross-env": "^5.0.0", "del": "^4.1.1", "del-cli": "^1.0.0", "eslint": "^5.16.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-prettier": "^3.1.0", "file-loader": "^3.0.1", "husky": "^2.2.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^8.1.6", "memory-fs": "^0.4.1", "normalize-path": "^3.0.0", "pre-commit": "^1.0.0", "prettier": "^1.17.0", "standard-version": "^6.0.1", "webpack": "^4.31.0", "webpack-cli": "^3.3.2"}, "keywords": ["webpack"], "gitHead": "1bdf3e612dbe4a0c4a039ffa3d37cdf1b956a116", "_id": "cache-loader@3.0.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.9.0", "dist": {"shasum": "cee6cf4b3cdc7c610905b26bad6c2fc439c821af", "size": 6880, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-3.0.1.tgz", "integrity": "sha512-HzJIvGiGqYsFUrMjAJNDbVZoG7qQA+vy9AIoKs7s9DscNfki0I589mf2w6/tW+kkFH3zyiknoWV5Jdynu6b/zw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_3.0.1_1557747156122_0.5997592652024715"}, "_hasShrinkwrap": false, "publish_time": 1557747156242, "_cnpm_publish_time": 1557747156242, "_cnpmcore_publish_time": "2021-12-16T12:40:39.451Z"}, "3.0.0": {"name": "cache-loader", "version": "3.0.0", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint --cache src test", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "ci:coverage": "npm run test:coverage -- --runInBand", "ci:lint:commits": "commitlint --from=origin/master --to=${CIRCLE_SHA1}", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"buffer-json": "^2.0.0", "find-cache-dir": "^2.1.0", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.6.0", "normalize-path": "^3.0.0", "schema-utils": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/polyfill": "^7.2.3", "@babel/preset-env": "^7.2.3", "@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^7.1.2", "@webpack-contrib/defaults": "^3.0.5", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.5.0", "babel-loader": "^8.0.4", "cross-env": "^5.0.0", "del": "^3.0.0", "del-cli": "^1.0.0", "eslint": "^5.10.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^3.0.0", "file-loader": "^3.0.1", "husky": "^1.2.1", "jest": "^24.5.0", "lint-staged": "^8.1.0", "memory-fs": "^0.4.1", "pre-commit": "^1.0.0", "prettier": "^1.15.2", "standard-version": "^4.0.0", "webpack": "^4.27.1", "webpack-cli": "^3.1.2"}, "keywords": ["webpack"], "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "6.9.0"}, "useBuiltIns": "usage"}]]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "gitHead": "1369c05a64c428ce5eebfb23dc487fb053c71844", "_id": "cache-loader@3.0.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "041cd9dd1cab1f40c12eda24017fa9988b01f064", "size": 6994, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-3.0.0.tgz", "integrity": "sha512-VaSDv1VxKUc04aandtAJ85+CvdGrGYCPExAxNOVedt3tElJRj/xhS4tHw3Ifw7m+mbltzbBzU/6aLQA8gYIYJQ=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_3.0.0_1555688849320_0.43017724824817605"}, "_hasShrinkwrap": false, "publish_time": 1555688849514, "_cnpm_publish_time": 1555688849514, "_cnpmcore_publish_time": "2021-12-16T12:40:39.647Z"}, "2.0.1": {"name": "cache-loader", "version": "2.0.1", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint --cache src test", "prebuild": "npm run clean", "prepublish": "npm run build", "release": "standard-version", "security": "npm audit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "ci:coverage": "npm run test:coverage -- --runInBand", "ci:lint:commits": "commitlint --from=origin/master --to=${CIRCLE_SHA1}", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.6.0", "normalize-path": "^3.0.0", "schema-utils": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/polyfill": "^7.2.3", "@babel/preset-env": "^7.2.3", "@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^7.1.2", "@webpack-contrib/defaults": "^3.0.5", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-loader": "^8.0.4", "cross-env": "^5.0.0", "del": "^3.0.0", "del-cli": "^1.0.0", "eslint": "^5.10.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^3.0.0", "husky": "^1.2.1", "jest": "^23.6.0", "lint-staged": "^8.1.0", "memory-fs": "^0.4.1", "pre-commit": "^1.0.0", "prettier": "^1.15.2", "standard-version": "^4.0.0", "webpack": "^4.27.1", "webpack-cli": "^3.1.2"}, "keywords": ["webpack"], "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "6.9.0"}, "useBuiltIns": "usage"}]]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "gitHead": "4cb02e92446263a5e1c61ca283f8961aa42a9833", "_id": "cache-loader@2.0.1", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5758f41a62d7c23941e3c3c7016e6faeb03acb07", "size": 6547, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-2.0.1.tgz", "integrity": "sha512-V99T3FOynmGx26Zom+JrVBytLBsmUCzVG2/4NnUKgvXN4bEV42R1ERl1IyiH/cvFIDA1Ytq2lPZ9tXDSahcQpQ=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_2.0.1_1546629925340_0.08328225419674706"}, "_hasShrinkwrap": false, "publish_time": 1546629925475, "_cnpm_publish_time": 1546629925475, "_cnpmcore_publish_time": "2021-12-16T12:40:39.814Z"}, "2.0.0": {"name": "cache-loader", "version": "2.0.0", "description": "Caches the result of following loaders on disk.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "author": {"name": "<PERSON> @sokra"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint --cache src test", "prebuild": "npm run clean", "prepublish": "npm run build", "release": "standard-version", "security": "npm audit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "ci:coverage": "npm run test:coverage -- --runInBand", "ci:lint:commits": "commitlint --from=origin/master --to=${CIRCLE_SHA1}", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.5.0", "schema-utils": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/polyfill": "^7.2.3", "@babel/preset-env": "^7.2.3", "@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^7.1.2", "@webpack-contrib/defaults": "^3.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-loader": "^8.0.4", "cross-env": "^5.0.0", "del": "^3.0.0", "del-cli": "^1.0.0", "eslint": "^5.10.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^3.0.0", "husky": "^1.2.1", "jest": "^23.6.0", "lint-staged": "^8.1.0", "memory-fs": "^0.4.1", "normalize-path": "^3.0.0", "pre-commit": "^1.0.0", "prettier": "^1.15.2", "standard-version": "^4.0.0", "webpack": "^4.27.1", "webpack-cli": "^3.1.2"}, "keywords": ["webpack"], "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "6.9.0"}, "useBuiltIns": "usage"}]]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "gitHead": "28dfdbda2f89f393085a7fbb2a10209fa624083d", "_id": "cache-loader@2.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.14.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "75cad9eb1e1c882c2b8c31ea008b41ebb0787de0", "size": 6466, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-2.0.0.tgz", "integrity": "sha512-zi4iyjWDs80PWRwFfIwcVSgRVIFX2PjyHH8YbsUGFH0U03oA5o80OEryZBCjfN+5tWYHFct1FXha8IhiIhaN6w=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_2.0.0_1545398003512_0.04554292803439308"}, "_hasShrinkwrap": false, "publish_time": 1545398003682, "_cnpm_publish_time": 1545398003682, "_cnpmcore_publish_time": "2021-12-16T12:40:40.394Z"}, "1.2.5": {"name": "cache-loader", "version": "1.2.5", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "defaults": "webpack-defaults", "webpack-defaults": "webpack-defaults"}, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.5.0", "schema-utils": "^0.4.2"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "e288092b71a5c2bb444566fcc209aaec4f3b2a36", "_id": "cache-loader@1.2.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "9ab15b0ae5f546f376083a695fc1a75f546cb266", "size": 6410, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.2.5.tgz", "integrity": "sha512-enWKEQ4kO3YreDFd7AtVRjtJBmNiqh/X9hVDReu0C4qm8gsGmySkwuWtdc+N5O+vq5FzxL1mIZc30NyXCB7o/Q=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_1.2.5_1540979354180_0.017146632811751106"}, "_hasShrinkwrap": false, "publish_time": 1540979354324, "_cnpm_publish_time": 1540979354324, "_cnpmcore_publish_time": "2021-12-16T12:40:40.589Z"}, "1.2.4": {"name": "cache-loader", "version": "1.2.4", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "defaults": "webpack-defaults", "webpack-defaults": "webpack-defaults"}, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.5.0", "schema-utils": "^0.4.2"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "97be0c129a4c2e8713d5a6d2eecd87f06aefdfb4", "_id": "cache-loader@1.2.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "c36a71a21be0a90d5116296c53ee9a94ff273c9d", "size": 6369, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.2.4.tgz", "integrity": "sha512-mXsyY+7KHKR8HcH8dI6zrGejxKGQWT/ohsl0XUJ9ycCWTwPKIfA9ChRo+/hiJ8U67LKbwBTSIEDjoIlzWb5TFg=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_1.2.4_1540960313001_0.9271699786803982"}, "_hasShrinkwrap": false, "publish_time": 1540960313104, "_cnpm_publish_time": 1540960313104, "_cnpmcore_publish_time": "2021-12-16T12:40:40.812Z"}, "1.2.3": {"name": "cache-loader", "version": "1.2.3", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "defaults": "webpack-defaults"}, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.5.0", "schema-utils": "^0.4.2"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "ebd53aadf56dfae269fa44e3cb25ec37c1aea9f1", "_id": "cache-loader@1.2.2", "_npmVersion": "5.7.1", "_nodeVersion": "9.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "6d5c38ded959a09cc5d58190ab5af6f73bd353f5", "size": 6169, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.2.2.tgz", "integrity": "sha512-rsGh4SIYyB9glU+d0OcHwiXHXBoUgDhHZaQ1KAbiXqfz1CDPxtTboh1gPbJ0q2qdO8a9lfcjgC5CJ2Ms32y5bw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_1.2.2_1519766317509_0.3657868698476898"}, "_hasShrinkwrap": false, "publish_time": 1519766317555, "_cnpm_publish_time": 1519766317555, "_cnpmcore_publish_time": "2021-12-16T12:40:41.174Z", "deprecated": "[WARNING] Use 1.2.2 instead of 1.2.3, reason: https://github.com/webpack-contrib/cache-loader/issues/43"}, "1.2.2": {"name": "cache-loader", "version": "1.2.2", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "defaults": "webpack-defaults"}, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.5.0", "schema-utils": "^0.4.2"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "ebd53aadf56dfae269fa44e3cb25ec37c1aea9f1", "_id": "cache-loader@1.2.2", "_npmVersion": "5.7.1", "_nodeVersion": "9.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "6d5c38ded959a09cc5d58190ab5af6f73bd353f5", "size": 6169, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.2.2.tgz", "integrity": "sha512-rsGh4SIYyB9glU+d0OcHwiXHXBoUgDhHZaQ1KAbiXqfz1CDPxtTboh1gPbJ0q2qdO8a9lfcjgC5CJ2Ms32y5bw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_1.2.2_1519766317509_0.3657868698476898"}, "_hasShrinkwrap": false, "publish_time": 1519766317555, "_cnpm_publish_time": 1519766317555, "_cnpmcore_publish_time": "2021-12-16T12:40:41.174Z"}, "1.2.1": {"name": "cache-loader", "version": "1.2.1", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "defaults": "webpack-defaults"}, "dependencies": {"async": "^2.4.1", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "schema-utils": "^0.4.2"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "5ac27d0d9c1da24eb2ba15409eb51ed2a6200f42", "_id": "cache-loader@1.2.1", "_npmVersion": "5.7.1", "_nodeVersion": "9.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "e171d5670188788c08af51b07cb52f40e8b157a2", "size": 6102, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.2.1.tgz", "integrity": "sha512-jbAdEJ1FvVVCO5cf0j/MOLGRElu4uoOtAXrXC3OUDr2IOzYKr/sRHnn+LVJWaaMi80Hw+DtbRsd++qB1BBt1Vg=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader_1.2.1_1519684248583_0.735802726091846"}, "_hasShrinkwrap": false, "publish_time": 1519684248711, "_cnpm_publish_time": 1519684248711, "_cnpmcore_publish_time": "2021-12-16T12:40:41.566Z"}, "1.2.0": {"name": "cache-loader", "version": "1.2.0", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepublish": "npm run build", "release": "standard-version", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "webpack-defaults": "webpack-defaults"}, "dependencies": {"async": "^2.4.1", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "schema-utils": "^0.4.2"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "repository": {"type": "git", "url": "git+ssh://**************/webpack-contrib/cache-loader.git"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "f2ad69686b484bbfa0d28ebeffced6dc5a5d60db", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader#readme", "_id": "cache-loader@1.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "cdc313ae53b3c13ce8ee0c9296cf16c736b15252", "size": 6025, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.2.0.tgz", "integrity": "sha512-E95knP7jxy2bF/HKuw5gCEXm06tp7/sEjewNF39ezyVBnVmNzB9bnXflEFBvrqZrswsCmgiCbiIc7xIeVXW7Gw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader-1.2.0.tgz_1510929701545_0.7705065563786775"}, "directories": {}, "publish_time": 1510929702482, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510929702482, "_cnpmcore_publish_time": "2021-12-16T12:40:41.767Z"}, "1.1.0": {"name": "cache-loader", "version": "1.1.0", "description": "Caches the result of following loaders on disk.", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepublish": "npm run build", "release": "standard-version", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "webpack-defaults": "webpack-defaults", "appveyor:test": "npm run test"}, "dependencies": {"async": "^2.4.1", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-jest": "^21.2.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0", "cross-env": "^5.0.5", "del-cli": "^1.1.0", "eslint": "^4.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.7.0", "jest": "^21.2.1", "lint-staged": "^4.2.3", "nsp": "^2.8.1", "pre-commit": "^1.2.2", "react": "^15.5.4", "standard-version": "^4.1.0", "webpack": "^3.6.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "repository": {"type": "git", "url": "git+ssh://**************/webpack-contrib/cache-loader.git"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "88dfc00da6d16a8bedaccfb0cc8a5df5d9a95d89", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader#readme", "_id": "cache-loader@1.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "79e35201eb432bedad89058c1a39e46255fbb723", "size": 5084, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.1.0.tgz", "integrity": "sha512-3shBzq11ESBqD8sTBrn0+yDZsVwpnBfy1EdDZdn2faWKRxOcwa0sDo5fmtv7H9NUUZsmzyl5z/BfWv9JJuYXcw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cache-loader-1.1.0.tgz_1507514021242_0.2903130960185081"}, "directories": {}, "publish_time": 1507514022471, "_hasShrinkwrap": false, "_cnpm_publish_time": 1507514022471, "_cnpmcore_publish_time": "2021-12-16T12:40:42.009Z"}, "1.0.3": {"name": "cache-loader", "version": "1.0.3", "description": "Caches the result of following loaders on disk.", "main": "dist/cjs.js", "scripts": {"test": "jest", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/webpack-contrib/cache-loader.git"}, "dependencies": {"async": "^2.3.0", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.24.1", "babel-jest": "^19.0.0", "babel-loader": "^6.4.1", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.4.0", "babel-preset-latest": "^6.24.1", "cross-env": "^4.0.0", "del-cli": "^0.2.1", "eslint": "^3.19.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "jest": "^19.0.2", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "react": "^15.5.4", "standard-version": "^4.0.0", "webpack": "^2.4.1", "webpack-defaults": "^0.4.5"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "files": ["dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "89bab1ff20b7a5141ef31a384aa991f41fbc1937", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader#readme", "_id": "cache-loader@1.0.3", "_shasum": "7717963ec082db068b17a1412deaaa72d21c4e30", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "dist": {"shasum": "7717963ec082db068b17a1412deaaa72d21c4e30", "size": 4420, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.0.3.tgz", "integrity": "sha512-oGBi0BOcxCGoOUT28Hbcoxf2LyX8Rqc0TFLw/bK1wZpmvAJ+y8VUsAajq5LPC5U6ciJoMT4941s1HQLyr+qijw=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cache-loader-1.0.3.tgz_1493198453763_0.7343336790800095"}, "directories": {}, "publish_time": 1493198454739, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493198454739, "_cnpmcore_publish_time": "2021-12-16T12:40:42.318Z"}, "1.0.2": {"name": "cache-loader", "version": "1.0.2", "description": "Caches the result of following loaders on disk.", "main": "dist/cjs.js", "scripts": {"test": "jest", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/webpack-contrib/cache-loader.git"}, "dependencies": {"async": "^2.3.0", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.24.1", "babel-jest": "^19.0.0", "babel-loader": "^6.4.1", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.4.0", "babel-preset-latest": "^6.24.1", "cross-env": "^4.0.0", "del-cli": "^0.2.1", "eslint": "^3.19.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "jest": "^19.0.2", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "react": "^15.5.4", "standard-version": "^4.0.0", "webpack": "^2.4.1", "webpack-defaults": "^0.4.5"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "files": ["dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "58adb9cce3713c9472d7fea8fd7c684c43285d59", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader#readme", "_id": "cache-loader@1.0.2", "_shasum": "35c0000593d21a617b8d46a591d8a6957c5d10fa", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "dist": {"shasum": "35c0000593d21a617b8d46a591d8a6957c5d10fa", "size": 4311, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.0.2.tgz", "integrity": "sha512-IsVnjeXsivjo8t7Jb+3GnjLO9AV/USEJD7LRNijwEPopXh2mf+Qmhw6MouISrQWEGYah6efx9CAFcW2z8i+H3A=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cache-loader-1.0.2.tgz_1493198163256_0.3500180337578058"}, "directories": {}, "publish_time": 1493198164302, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493198164302, "_cnpmcore_publish_time": "2021-12-16T12:40:42.549Z"}, "1.0.1": {"name": "cache-loader", "version": "1.0.1", "description": "Caches the result of following loaders on disk.", "main": "dist/cjs.js", "scripts": {"test": "jest", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/webpack-contrib/cache-loader.git"}, "dependencies": {"async": "^2.3.0", "loader-utils": "^1.1.0", "mkdirp": "^0.5.1"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.24.1", "babel-jest": "^19.0.0", "babel-loader": "^6.4.1", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.4.0", "babel-preset-latest": "^6.24.1", "cross-env": "^4.0.0", "del-cli": "^0.2.1", "eslint": "^3.19.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "jest": "^19.0.2", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "react": "^15.5.4", "standard-version": "^4.0.0", "webpack": "^2.4.1", "webpack-defaults": "^0.4.5"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "files": ["dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "5c5dd2c59ec42ffa98e309054149bd8453e9f14d", "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader#readme", "_id": "cache-loader@1.0.1", "_shasum": "7cf44066d5bbd6bba678d135da60e230cdd73f68", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "dist": {"shasum": "7cf44066d5bbd6bba678d135da60e230cdd73f68", "size": 4517, "noattachment": false, "tarball": "https://registry.npmmirror.com/cache-loader/-/cache-loader-1.0.1.tgz", "integrity": "sha512-f4H4PreY+bURKLhmXjq4BprqvqbbUHkBOOIounKxS2joO5hyTT+aG+HsAP40KOMxBhLxK9zNtU0z62eE0+t9KQ=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "jtangelder", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cache-loader-1.0.1.tgz_1493197649334_0.37690210877917707"}, "directories": {}, "publish_time": 1493197650273, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493197650273, "_cnpmcore_publish_time": "2021-12-16T12:40:42.763Z"}}, "bugs": {"url": "https://github.com/webpack-contrib/cache-loader/issues"}, "homepage": "https://github.com/webpack-contrib/cache-loader", "keywords": ["webpack"], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/cache-loader.git"}, "_source_registry_name": "default"}