{"_attachments": {}, "_id": "ignore", "_rev": "1602-61f146d6b77ea98a748ff928", "author": {"name": "kael"}, "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "dist-tags": {"latest": "7.0.5"}, "license": "MIT", "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "name": "ignore", "readme": "| Linux / MacOS / Windows | Coverage | Downloads |\n| ----------------------- | -------- | --------- |\n| [![build][bb]][bl]      | [![coverage][cb]][cl] | [![downloads][db]][dl] |\n\n[bb]: https://github.com/kaelzhang/node-ignore/actions/workflows/nodejs.yml/badge.svg\n[bl]: https://github.com/kaelzhang/node-ignore/actions/workflows/nodejs.yml\n\n[cb]: https://codecov.io/gh/kaelzhang/node-ignore/branch/master/graph/badge.svg\n[cl]: https://codecov.io/gh/kaelzhang/node-ignore\n\n[db]: http://img.shields.io/npm/dm/ignore.svg\n[dl]: https://www.npmjs.org/package/ignore\n\n# ignore\n\n`ignore` is a manager, filter and parser which implemented in pure JavaScript according to the [.gitignore spec 2.22.1](http://git-scm.com/docs/gitignore).\n\n`ignore` is used by eslint, gitbook and [many others](https://www.npmjs.com/browse/depended/ignore).\n\nPay **ATTENTION** that [`minimatch`](https://www.npmjs.org/package/minimatch) (which used by `fstream-ignore`) does not follow the gitignore spec.\n\nTo filter filenames according to a .gitignore file, I recommend this npm package, `ignore`.\n\nTo parse an `.npmignore` file, you should use `minimatch`, because an `.npmignore` file is parsed by npm using `minimatch` and it does not work in the .gitignore way.\n\n### Tested on\n\n`ignore` is fully tested, and has more than **five hundreds** of unit tests.\n\n- Linux + Node: `0.8` - `7.x`\n- Windows + Node: `0.10` - `7.x`, node < `0.10` is not tested due to the lack of support of appveyor.\n\nActually, `ignore` does not rely on any versions of node specially.\n\nSince `4.0.0`, ignore will no longer support `node < 6` by default, to use in node < 6, `require('ignore/legacy')`. For details, see [CHANGELOG](https://github.com/kaelzhang/node-ignore/blob/master/CHANGELOG.md).\n\n## Table Of Main Contents\n\n- [Usage](#usage)\n- [`Pathname` Conventions](#pathname-conventions)\n- See Also:\n  - [`glob-gitignore`](https://www.npmjs.com/package/glob-gitignore) matches files using patterns and filters them according to gitignore rules.\n- [Upgrade Guide](#upgrade-guide)\n\n## Install\n\n```sh\nnpm i ignore\n```\n\n## Usage\n\n```js\nimport ignore from 'ignore'\nconst ig = ignore().add(['.abc/*', '!.abc/d/'])\n```\n\n### Filter the given paths\n\n```js\nconst paths = [\n  '.abc/a.js',    // filtered out\n  '.abc/d/e.js'   // included\n]\n\nig.filter(paths)        // ['.abc/d/e.js']\nig.ignores('.abc/a.js') // true\n```\n\n### As the filter function\n\n```js\npaths.filter(ig.createFilter()); // ['.abc/d/e.js']\n```\n\n### Win32 paths will be handled\n\n```js\nig.filter(['.abc\\\\a.js', '.abc\\\\d\\\\e.js'])\n// if the code above runs on windows, the result will be\n// ['.abc\\\\d\\\\e.js']\n```\n\n## Why another ignore?\n\n- `ignore` is a standalone module, and is much simpler so that it could easy work with other programs, unlike [isaacs](https://npmjs.org/~isaacs)'s [fstream-ignore](https://npmjs.org/package/fstream-ignore) which must work with the modules of the fstream family.\n\n- `ignore` only contains utility methods to filter paths according to the specified ignore rules, so\n  - `ignore` never try to find out ignore rules by traversing directories or fetching from git configurations.\n  - `ignore` don't cares about sub-modules of git projects.\n\n- Exactly according to [gitignore man page](http://git-scm.com/docs/gitignore), fixes some known matching issues of fstream-ignore, such as:\n  - '`/*.js`' should only match '`a.js`', but not '`abc/a.js`'.\n  - '`**/foo`' should match '`foo`' anywhere.\n  - Prevent re-including a file if a parent directory of that file is excluded.\n  - Handle trailing whitespaces:\n    - `'a '`(one space) should not match `'a  '`(two spaces).\n    - `'a \\ '` matches `'a  '`\n  - All test cases are verified with the result of `git check-ignore`.\n\n# Methods\n\n## .add(pattern: string | Ignore): this\n## .add(patterns: Array<string | Ignore>): this\n## .add({pattern: string, mark?: string}): this  since 7.0.0\n\n- **pattern** `string | Ignore` An ignore pattern string, or the `Ignore` instance\n- **patterns** `Array<string | Ignore>` Array of ignore patterns.\n- **mark?** `string` Pattern mark, which is used to associate the pattern with a certain marker, such as the line no of the `.gitignore` file. Actually it could be an arbitrary string and is optional.\n\nAdds a rule or several rules to the current manager.\n\nReturns `this`\n\nNotice that a line starting with `'#'`(hash) is treated as a comment. Put a backslash (`'\\'`) in front of the first hash for patterns that begin with a hash, if you want to ignore a file with a hash at the beginning of the filename.\n\n```js\nignore().add('#abc').ignores('#abc')    // false\nignore().add('\\\\#abc').ignores('#abc')   // true\n```\n\n`pattern` could either be a line of ignore pattern or a string of multiple ignore patterns, which means we could just `ignore().add()` the content of a ignore file:\n\n```js\nignore()\n.add(fs.readFileSync(filenameOfGitignore).toString())\n.filter(filenames)\n```\n\n`pattern` could also be an `ignore` instance, so that we could easily inherit the rules of another `Ignore` instance.\n\n## .ignores(pathname: [Pathname](#pathname-conventions)): boolean\n\n> new in 3.2.0\n\nReturns `Boolean` whether `pathname` should be ignored.\n\n```js\nig.ignores('.abc/a.js')    // true\n```\n\nPlease **PAY ATTENTION** that `.ignores()` is **NOT** equivalent to `git check-ignore` although in most cases they return equivalent results.\n\nHowever, for the purposes of imitating the behavior of `git check-ignore`, please use `.checkIgnore()` instead.\n\n### `Pathname` Conventions:\n\n#### 1. `Pathname` should be a `path.relative()`d pathname\n\n`Pathname` should be a string that have been `path.join()`ed, or the return value of `path.relative()` to the current directory,\n\n```js\n// WRONG, an error will be thrown\nig.ignores('./abc')\n\n// WRONG, for it will never happen, and an error will be thrown\n// If the gitignore rule locates at the root directory,\n// `'/abc'` should be changed to `'abc'`.\n// ```\n// path.relative('/', '/abc')  -> 'abc'\n// ```\nig.ignores('/abc')\n\n// WRONG, that it is an absolute path on Windows, an error will be thrown\nig.ignores('C:\\\\abc')\n\n// Right\nig.ignores('abc')\n\n// Right\nig.ignores(path.join('./abc'))  // path.join('./abc') -> 'abc'\n```\n\nIn other words, each `Pathname` here should be a relative path to the directory of the gitignore rules.\n\nSuppose the dir structure is:\n\n```\n/path/to/your/repo\n    |-- a\n    |   |-- a.js\n    |\n    |-- .b\n    |\n    |-- .c\n         |-- .DS_store\n```\n\nThen the `paths` might be like this:\n\n```js\n[\n  'a/a.js'\n  '.b',\n  '.c/.DS_store'\n]\n```\n\n#### 2. filenames and dirnames\n\n`node-ignore` does NO `fs.stat` during path matching, so `node-ignore` treats\n- `foo` as a file\n- **`foo/` as a directory**\n\nFor the example below:\n\n```js\n// First, we add a ignore pattern to ignore a directory\nig.add('config/')\n\n// `ig` does NOT know if 'config', in the real world,\n//   is a normal file, directory or something.\n\nig.ignores('config')\n// `ig` treats `config` as a file, so it returns `false`\n\nig.ignores('config/')\n// returns `true`\n```\n\nSpecially for people who develop some library based on `node-ignore`, it is important to understand that.\n\nUsually, you could use [`glob`](http://npmjs.org/package/glob) with `option.mark = true` to fetch the structure of the current directory:\n\n```js\nimport glob from 'glob'\n\nglob('**', {\n  // Adds a / character to directory matches.\n  mark: true\n}, (err, files) => {\n  if (err) {\n    return console.error(err)\n  }\n\n  let filtered = ignore().add(patterns).filter(files)\n  console.log(filtered)\n})\n```\n\n\n## .filter(paths: Array&lt;Pathname&gt;): Array&lt;Pathname&gt;\n\n```ts\ntype Pathname = string\n```\n\nFilters the given array of pathnames, and returns the filtered array.\n\n- **paths** `Array.<Pathname>` The array of `pathname`s to be filtered.\n\n## .createFilter()\n\nCreates a filter function which could filter an array of paths with `Array.prototype.filter`.\n\nReturns `function(path)` the filter function.\n\n## .test(pathname: Pathname): TestResult\n\n> New in 5.0.0\n\nReturns `TestResult`\n\n```ts\n// Since 5.0.0\ninterface TestResult {\n  ignored: boolean\n  // true if the `pathname` is finally unignored by some negative pattern\n  unignored: boolean\n  // The `IgnoreRule` which ignores the pathname\n  rule?: IgnoreRule\n}\n\n// Since 7.0.0\ninterface IgnoreRule {\n  // The original pattern\n  pattern: string\n  // Whether the pattern is a negative pattern\n  negative: boolean\n  // Which is used for other packages to build things upon `node-ignore`\n  mark?: string\n}\n```\n\n- `{ignored: true, unignored: false}`: the `pathname` is ignored\n- `{ignored: false, unignored: true}`: the `pathname` is unignored\n- `{ignored: false, unignored: false}`: the `pathname` is never matched by any ignore rules.\n\n## .checkIgnore(target: string): TestResult\n\n> new in 7.0.0\n\nDebugs gitignore / exclude files, which is equivalent to `git check-ignore -v`. Usually this method is used for other packages to implement the function of `git check-ignore -v` upon `node-ignore`\n\n- **target** `string` the target to test.\n\nReturns `TestResult`\n\n```js\nig.add({\n  pattern: 'foo/*',\n  mark: '60'\n})\n\nconst {\n  ignored,\n  rule\n} = checkIgnore('foo/')\n\nif (ignored) {\n  console.log(`.gitignore:${result}:${rule.mark}:${rule.pattern} foo/`)\n}\n\n// .gitignore:60:foo/* foo/\n```\n\nPlease pay attention that this method does not have a strong built-in cache mechanism.\n\nThe purpose of introducing this method is to make it possible to implement the `git check-ignore` command in JavaScript based on `node-ignore`.\n\nSo do not use this method in those situations where performance is extremely important.\n\n## static `isPathValid(pathname): boolean` since 5.0.0\n\nCheck whether the `pathname` is an valid `path.relative()`d path according to the [convention](#1-pathname-should-be-a-pathrelatived-pathname).\n\nThis method is **NOT** used to check if an ignore pattern is valid.\n\n```js\nimport {isPathValid} from 'ignore'\n\nisPathValid('./foo')  // false\n```\n\n## <strike>.addIgnoreFile(path)</strike>\n\nREMOVED in `3.x` for now.\n\nTo upgrade `ignore@2.x` up to `3.x`, use\n\n```js\nimport fs from 'fs'\n\nif (fs.existsSync(filename)) {\n  ignore().add(fs.readFileSync(filename).toString())\n}\n```\n\ninstead.\n\n## ignore(options)\n\n### `options.ignorecase` since 4.0.0\n\nSimilar to the `core.ignorecase` option of [git-config](https://git-scm.com/docs/git-config), `node-ignore` will be case insensitive if `options.ignorecase` is set to `true` (the default value), otherwise case sensitive.\n\n```js\nconst ig = ignore({\n  ignorecase: false\n})\n\nig.add('*.png')\n\nig.ignores('*.PNG')  // false\n```\n\n### `options.ignoreCase?: boolean` since 5.2.0\n\nWhich is an alternative to `options.ignoreCase`\n\n### `options.allowRelativePaths?: boolean` since 5.2.0\n\nThis option brings backward compatibility with projects which based on `ignore@4.x`. If `options.allowRelativePaths` is `true`, `ignore` will not check whether the given path to be tested is [`path.relative()`d](#pathname-conventions).\n\nHowever, passing a relative path, such as `'./foo'` or `'../foo'`, to test if it is ignored or not is not a good practise, which might lead to unexpected behavior\n\n```js\nignore({\n  allowRelativePaths: true\n}).ignores('../foo/bar.js') // And it will not throw\n```\n\n****\n\n# Upgrade Guide\n\n## Upgrade 4.x -> 5.x\n\nSince `5.0.0`, if an invalid `Pathname` passed into `ig.ignores()`, an error will be thrown, unless `options.allowRelative = true` is passed to the `Ignore` factory.\n\nWhile `ignore < 5.0.0` did not make sure what the return value was, as well as\n\n```ts\n.ignores(pathname: Pathname): boolean\n\n.filter(pathnames: Array<Pathname>): Array<Pathname>\n\n.createFilter(): (pathname: Pathname) => boolean\n\n.test(pathname: Pathname): {ignored: boolean, unignored: boolean}\n```\n\nSee the convention [here](#1-pathname-should-be-a-pathrelatived-pathname) for details.\n\nIf there are invalid pathnames, the conversion and filtration should be done by users.\n\n```js\nimport {isPathValid} from 'ignore' // introduced in 5.0.0\n\nconst paths = [\n  // invalid\n  //////////////////\n  '',\n  false,\n  '../foo',\n  '.',\n  //////////////////\n\n  // valid\n  'foo'\n]\n.filter(isPathValid)\n\nig.filter(paths)\n```\n\n## Upgrade 3.x -> 4.x\n\nSince `4.0.0`, `ignore` will no longer support node < 6, to use `ignore` in node < 6:\n\n```js\nvar ignore = require('ignore/legacy')\n```\n\n## Upgrade 2.x -> 3.x\n\n- All `options` of 2.x are unnecessary and removed, so just remove them.\n- `ignore()` instance is no longer an [`EventEmitter`](nodejs.org/api/events.html), and all events are unnecessary and removed.\n- `.addIgnoreFile()` is removed, see the [.addIgnoreFile](#addignorefilepath) section for details.\n\n****\n\n# Collaborators\n\n- [@whitecolor](https://github.com/whitecolor) *Alex*\n- [@SamyPesse](https://github.com/SamyPesse) *Samy Pessé*\n- [@azproduction](https://github.com/azproduction) *Mikhail Davydov*\n- [@TrySound](https://github.com/TrySound) *Bogdan Chadkin*\n- [@JanMattner](https://github.com/JanMattner) *Jan Mattner*\n- [@ntwb](https://github.com/ntwb) *Stephen Edgar*\n- [@kasperisager](https://github.com/kasperisager) *Kasper Isager*\n- [@sandersn](https://github.com/sandersn) *Nathan Shively-Sanders*\n", "time": {"created": "2022-01-26T13:04:22.605Z", "modified": "2025-05-31T08:28:49.172Z", "5.1.9": "2021-11-03T08:03:01.241Z", "5.1.8": "2020-05-30T03:27:50.434Z", "5.1.7": "2020-05-30T03:25:03.994Z", "5.1.6": "2020-05-22T15:15:00.038Z", "5.1.5": "2020-05-22T14:10:04.972Z", "5.1.4": "2019-08-14T09:30:32.172Z", "5.1.3": "2019-08-14T09:14:34.843Z", "5.1.2": "2019-05-28T13:37:06.245Z", "5.1.1": "2019-04-17T07:55:03.978Z", "5.1.0": "2019-04-14T08:49:24.748Z", "5.0.6": "2019-03-17T12:23:14.933Z", "5.0.5": "2019-01-26T00:50:54.505Z", "5.0.4": "2018-11-03T01:52:39.632Z", "5.0.3": "2018-10-28T23:31:03.431Z", "5.0.2": "2018-08-20T12:38:19.559Z", "5.0.1": "2018-08-14T00:22:04.418Z", "5.0.0": "2018-08-13T15:02:04.069Z", "4.0.6": "2018-08-12T04:39:22.496Z", "4.0.5": "2018-08-09T05:24:50.422Z", "4.0.4": "2018-08-09T05:19:26.876Z", "4.0.3": "2018-07-31T13:05:51.873Z", "4.0.2": "2018-06-22T02:01:00.317Z", "4.0.1": "2018-06-22T01:29:10.234Z", "4.0.0": "2018-06-21T23:41:49.682Z", "3.3.10": "2018-06-21T02:06:58.015Z", "3.3.9": "2018-06-21T01:37:31.839Z", "3.3.8": "2018-04-26T02:05:39.275Z", "3.3.7": "2017-10-26T14:00:54.429Z", "3.3.6": "2017-10-22T02:51:25.061Z", "3.3.5": "2017-08-29T07:35:40.390Z", "3.3.4": "2017-08-26T03:55:43.245Z", "3.3.3": "2017-05-19T11:14:38.357Z", "3.3.2": "2017-05-19T11:11:16.285Z", "3.3.1": "2017-05-19T11:10:13.607Z", "3.3.0": "2017-05-02T09:00:25.712Z", "3.2.7": "2017-04-07T01:48:11.067Z", "3.2.6": "2017-03-16T06:59:13.061Z", "3.2.5": "2017-03-15T09:40:02.686Z", "3.2.4": "2017-02-20T04:20:53.075Z", "3.2.3": "2017-02-20T04:01:09.840Z", "3.2.2": "2017-01-28T03:24:34.847Z", "3.2.1": "2017-01-28T02:55:29.585Z", "3.2.0": "2016-10-11T08:15:56.913Z", "3.1.5": "2016-08-15T04:19:24.469Z", "3.1.4": "2016-08-15T03:04:32.777Z", "3.1.3": "2016-06-22T02:42:50.798Z", "3.1.2": "2016-04-28T03:04:12.693Z", "3.1.1": "2016-04-09T05:43:05.520Z", "3.1.0": "2016-04-09T05:21:19.926Z", "3.0.14": "2016-03-30T04:59:15.503Z", "3.0.13": "2016-03-29T07:54:17.415Z", "3.0.12": "2016-03-28T05:12:24.769Z", "3.0.11": "2016-03-22T09:27:40.790Z", "3.0.10": "2016-03-10T02:41:29.726Z", "3.0.9": "2016-03-08T16:22:36.326Z", "3.0.8": "2016-03-08T16:03:23.175Z", "3.0.7": "2016-03-08T15:25:03.944Z", "3.0.6": "2016-03-08T14:46:09.273Z", "3.0.5": "2016-03-08T12:39:25.689Z", "3.0.4": "2016-03-08T12:37:56.460Z", "3.0.3": "2016-03-01T14:03:24.949Z", "3.0.2": "2016-03-01T13:54:16.489Z", "3.0.1": "2016-03-01T11:13:44.335Z", "3.0.0": "2016-03-01T10:00:42.944Z", "2.2.19": "2015-10-28T09:05:00.587Z", "2.2.18": "2015-10-08T04:34:36.370Z", "2.2.17": "2015-09-28T04:08:21.942Z", "2.2.16": "2015-07-23T05:29:57.230Z", "2.2.15": "2014-08-18T17:21:23.724Z", "2.2.14": "2014-06-16T10:19:56.014Z", "2.2.13": "2014-06-07T17:10:39.478Z", "2.2.12": "2014-02-08T21:26:38.688Z", "2.2.11": "2014-02-08T16:32:15.639Z", "2.2.10": "2014-02-08T16:19:30.511Z", "2.2.9": "2014-02-08T10:41:40.286Z", "2.2.8": "2013-12-03T02:02:18.743Z", "2.2.7": "2013-10-14T13:45:56.946Z", "2.2.1": "2013-10-07T09:36:18.002Z", "1.1.3": "2013-10-07T07:19:53.342Z", "0.1.2": "2013-09-26T02:32:50.111Z", "0.1.0": "2013-09-02T07:32:13.699Z", "5.2.0": "2021-12-19T03:48:57.874Z", "5.2.1": "2022-11-27T21:50:30.892Z", "5.2.2": "2022-12-19T07:36:07.025Z", "5.2.3": "2022-12-19T14:56:41.028Z", "5.2.4": "2022-12-19T16:01:43.614Z", "5.3.0": "2023-11-16T11:42:47.471Z", "5.3.1": "2024-02-01T02:26:41.175Z", "5.3.2": "2024-08-12T08:51:00.065Z", "6.0.0": "2024-09-17T11:55:38.818Z", "6.0.1": "2024-09-17T11:59:04.980Z", "6.0.2": "2024-09-17T13:35:33.937Z", "7.0.0": "2024-12-23T11:50:50.714Z", "7.0.1": "2025-01-13T13:14:07.290Z", "7.0.2": "2025-01-14T02:13:41.127Z", "7.0.3": "2025-01-14T03:52:41.363Z", "7.0.4": "2025-04-25T02:29:12.203Z", "7.0.5": "2025-05-31T02:18:53.410Z"}, "versions": {"5.1.9": {"name": "ignore", "version": "5.1.9", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "gitHead": "aeb459d08e36913455ac5912656250e745b550fd", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.9", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"shasum": "9ec1a5cbe8e1446ec60d4420060d43aa6e7382fb", "size": 13476, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.9.tgz", "integrity": "sha512-2zeMQpbKz5dhZ9IwL0gbxSW5w0NK/MSAMtNuhgIHEPmaU3vPdKPL0UdvUCXs5SS4JAwsBxysK5sFMW8ocFiVjQ=="}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.9_1635926581092_0.6637094031111468"}, "_hasShrinkwrap": false, "publish_time": 1635926581241, "_cnpm_publish_time": 1635926581241, "_cnpmcore_publish_time": "2021-12-13T15:07:41.103Z"}, "5.1.8": {"name": "ignore", "version": "5.1.8", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "gitHead": "a1f29fbadf258f630cdf45bd59e6fda5540e3169", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.8", "_nodeVersion": "14.1.0", "_npmVersion": "6.14.4", "dist": {"shasum": "f150a8b50a34289b33e22f5889abd4d8016f0e57", "size": 14109, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.8.tgz", "integrity": "sha512-BMpfD7PpiETpBl/A6S498BaIJ6Y/ABT93ETbby2fP00v4EbvPBXWEoaR1UBPKs3iR53pJY7EtZk5KACI57i1Uw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.8_1590809270251_0.29117314601321564"}, "_hasShrinkwrap": false, "publish_time": 1590809270434, "_cnpm_publish_time": 1590809270434, "_cnpmcore_publish_time": "2021-12-13T15:07:41.481Z"}, "5.1.7": {"name": "ignore", "version": "5.1.7", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "gitHead": "8374a3139913ee9f186be29b8bf23e9db97557f5", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.7", "_nodeVersion": "14.1.0", "_npmVersion": "6.14.4", "dist": {"shasum": "9288bb665bbcc7e155ad10ec4aefa6d27fcc603e", "size": 14093, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.7.tgz", "integrity": "sha512-INBK+PWLI4T9G9OgQukZu6kbDi9wVTebIEr9pC4EoXwpUPVyHaW+F+bZqB7F3kSTsTG4uJHNgPwXzJ9URoSsyw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.7_1590809103835_0.8310417697857106"}, "_hasShrinkwrap": false, "publish_time": 1590809103994, "_cnpm_publish_time": 1590809103994, "_cnpmcore_publish_time": "2021-12-13T15:07:41.817Z"}, "5.1.6": {"name": "ignore", "version": "5.1.6", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "gitHead": "55ecc53f23e4ba787fce051a2bcd9d863aacc1f6", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.6", "_nodeVersion": "14.1.0", "_npmVersion": "6.14.4", "dist": {"shasum": "643194ad4bf2712f37852e386b6998eff0db2106", "size": 14042, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.6.tgz", "integrity": "sha512-cgXgkypZBcCnOgSihyeqbo6gjIaIyDqPQB7Ra4vhE9m6kigdGoQDMHjviFhRZo3IMlRy6yElosoviMs5YxZXUA=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.6_1590160499931_0.33992953007614424"}, "_hasShrinkwrap": false, "publish_time": 1590160500038, "_cnpm_publish_time": 1590160500038, "_cnpmcore_publish_time": "2021-12-13T15:07:42.173Z"}, "5.1.5": {"name": "ignore", "version": "5.1.5", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "gitHead": "5753f0aaed8e71be536a1ee18b3edafa0c68661d", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.5", "_nodeVersion": "14.1.0", "_npmVersion": "6.14.4", "dist": {"shasum": "4e2f418f16d019e7fb97d0ea6ed1dcd67864d96b", "size": 14017, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.5.tgz", "integrity": "sha512-QD4ngLBjCwACR+Wq7ZPzjgCpOWOOBm+5+qwXN0rQfiht0kjTGmBcLXlJRxiyL9rpyq19Z8tuLyG5LLWEEfjXWw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.5_1590156604788_0.7416225713404856"}, "_hasShrinkwrap": false, "publish_time": 1590156604972, "_cnpm_publish_time": 1590156604972, "_cnpmcore_publish_time": "2021-12-13T15:07:42.656Z"}, "5.1.4": {"name": "ignore", "version": "5.1.4", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "codecov": "^3.5.0", "debug": "^4.1.1", "eslint": "^6.1.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.18.2", "mkdirp": "^0.5.1", "pre-suf": "^1.1.1", "rimraf": "^2.7.0", "spawn-sync": "^2.0.0", "tap": "^14.6.1", "tmp": "0.1.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 4"}, "gitHead": "151048b3f09166ffff42c23b78f3aedc58be5f0b", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.4", "_nodeVersion": "10.16.2", "_npmVersion": "6.9.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "84b7b3dbe64552b6ef0eca99f6743dbec6d97adf", "size": 13781, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.4.tgz", "integrity": "sha512-MzbUSahkTW1u7JpKKjY7LCARd1fU5W2rLdxlM4kdkayuCwZImjkpluF9CM1aLewYJguPDqewLam18Y6AU69A8A=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.4_1565775032042_0.****************"}, "_hasShrinkwrap": false, "publish_time": 1565775032172, "_cnpm_publish_time": 1565775032172, "_cnpmcore_publish_time": "2021-12-13T15:07:43.016Z"}, "5.1.3": {"name": "ignore", "version": "5.1.3", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "codecov": "^3.5.0", "debug": "^4.1.1", "eslint": "^5.16.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.17.3", "mkdirp": "^0.5.1", "pre-suf": "^1.1.1", "rimraf": "^2.6.3", "spawn-sync": "^2.0.0", "tap": "^14.2.0", "tmp": "0.1.0", "typescript": "^3.4.5"}, "engines": {"node": ">= 4"}, "gitHead": "0a95a9131d4423e2a11b36832fdcb7edd71478a9", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.3", "_nodeVersion": "10.16.2", "_npmVersion": "6.9.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "8c76acc9610103c2f5e9693412c70fab57861930", "size": 13700, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.3.tgz", "integrity": "sha512-HPatWVAd5BSzrBA+BiMv6vpZAzKVLl5oc35nyw8gDkJpi6aYBPIVMjKJFIJqOyvaoeCZMTv3Do/bPwNy2ta7Zw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.3_1565774074653_0.6040543986007063"}, "_hasShrinkwrap": false, "publish_time": 1565774074843, "_cnpm_publish_time": 1565774074843, "_cnpmcore_publish_time": "2021-12-13T15:07:43.443Z"}, "5.1.2": {"name": "ignore", "version": "5.1.2", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "codecov": "^3.5.0", "debug": "^4.1.1", "eslint": "^5.16.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.17.3", "mkdirp": "^0.5.1", "pre-suf": "^1.1.1", "rimraf": "^2.6.3", "spawn-sync": "^2.0.0", "tap": "^14.2.0", "tmp": "0.1.0", "typescript": "^3.4.5"}, "engines": {"node": ">= 4"}, "gitHead": "04b6270f7ddee75b719cb9bfb368bd335f3938c7", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.2", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "e28e584d43ad7e92f96995019cc43b9e1ac49558", "size": 13664, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.2.tgz", "integrity": "sha512-vdqWBp7MyzdmHkkRWV5nY+PfGRbYbahfuvsBCh277tq+w9zyNi7h5CYJCK0kmzti9kU+O/cB7sE8HvKv6aXAKQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.2_1559050626081_0.09081311360120137"}, "_hasShrinkwrap": false, "publish_time": 1559050626245, "_cnpm_publish_time": 1559050626245, "_cnpmcore_publish_time": "2021-12-13T15:07:43.819Z"}, "5.1.1": {"name": "ignore", "version": "5.1.1", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "codecov": "^3.2.0", "debug": "^4.1.1", "eslint": "^5.15.2", "eslint-config-ostai": "^2.0.0", "eslint-plugin-import": "^2.16.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.1", "rimraf": "^2.6.3", "spawn-sync": "^2.0.0", "tap": "^12.6.0", "tmp": "0.0.33", "typescript": "^3.3.3333"}, "engines": {"node": ">= 4"}, "gitHead": "bff5d32005050e1b29debc4febb7ce7387d49039", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.1", "_nodeVersion": "11.12.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "2fc6b8f518aff48fef65a7f348ed85632448e4a5", "size": 13625, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.1.tgz", "integrity": "sha512-DWjnQIFLenVrwyRCKZT+7a7/U4Cqgar4WG8V++K3hw+lrW1hc/SIwdiGmtxKCVACmHULTuGeBbHJmbwW7/sAvA=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.1_1555487703898_0.08803948648746251"}, "_hasShrinkwrap": false, "publish_time": 1555487703978, "_cnpm_publish_time": 1555487703978, "_cnpmcore_publish_time": "2021-12-13T15:07:44.260Z"}, "5.1.0": {"name": "ignore", "version": "5.1.0", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "codecov": "^3.2.0", "debug": "^4.1.1", "eslint": "^5.15.2", "eslint-config-ostai": "^2.0.0", "eslint-plugin-import": "^2.16.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.1", "rimraf": "^2.6.3", "spawn-sync": "^2.0.0", "tap": "^12.6.0", "tmp": "0.0.33", "typescript": "^3.3.3333"}, "engines": {"node": ">= 4"}, "gitHead": "8fa5282a41c64aeab280adcafe3d22c7ad4d8478", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.1.0", "_nodeVersion": "11.12.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "a949efb645e5d67fd78e46f470bee6b8c5d862f9", "size": 13504, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.1.0.tgz", "integrity": "sha512-d<PERSON><PERSON><PERSON><PERSON>wloo0gq40chdtDmE4tMp67ZGwN7MFTgjNqWi2VHEi5Ya6JkuvPWasjcAIm7lg+2if8xxn5R199wspcplg=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.1.0_1555231764592_0.28056876382732376"}, "_hasShrinkwrap": false, "publish_time": 1555231764748, "_cnpm_publish_time": 1555231764748, "_cnpmcore_publish_time": "2021-12-13T15:07:44.671Z"}, "5.0.6": {"name": "ignore", "version": "5.0.6", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "codecov": "^3.2.0", "debug": "^4.1.1", "eslint": "^5.15.2", "eslint-config-ostai": "^2.0.0", "eslint-plugin-import": "^2.16.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.1", "rimraf": "^2.6.3", "spawn-sync": "^2.0.0", "tap": "^12.6.0", "tmp": "0.0.33", "typescript": "^3.3.3333"}, "engines": {"node": ">= 4"}, "gitHead": "83a3038c2302f1627bc7b6051d447d5c570b9e43", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.6", "_nodeVersion": "11.12.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "562dacc7ec27d672dde433aa683c543b24c17694", "size": 13501, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.6.tgz", "integrity": "sha512-/+hp3kUf/Csa32ktIaj0OlRqQxrgs30n62M90UBpNd9k+ENEch5S+hmbW3DtcJGz3sYFTh4F3A6fQ0q7KWsp4w=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.6_1552825394753_0.8020898999058246"}, "_hasShrinkwrap": false, "publish_time": 1552825394933, "_cnpm_publish_time": 1552825394933, "_cnpmcore_publish_time": "2021-12-13T15:07:45.094Z"}, "5.0.5": {"name": "ignore", "version": "5.0.5", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.1.0", "debug": "^4.1.1", "eslint": "^5.12.0", "eslint-config-ostai": "^1.4.0", "eslint-plugin-import": "^2.14.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.3", "spawn-sync": "^2.0.0", "tap": "^12.1.1", "tmp": "0.0.33", "typescript": "^3.2.2"}, "engines": {"node": ">= 4"}, "gitHead": "d02d84aca6734b047649f43b4e6a347db7d98979", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "c663c548d6ce186fb33616a8ccb5d46e56bdbbf9", "size": 13365, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.5.tgz", "integrity": "sha512-kOC8IUb8HSDMVcYrDVezCxpJkzSQWTAzf3olpKM6o9rM5zpojx23O0Fl8Wr4+qJ6ZbPEHqf1fdwev/DS7v7pmA=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.5_1548463854355_0.9817452100429664"}, "_hasShrinkwrap": false, "publish_time": 1548463854505, "_cnpm_publish_time": 1548463854505, "_cnpmcore_publish_time": "2021-12-13T15:07:45.577Z"}, "5.0.4": {"name": "ignore", "version": "5.0.4", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "gitHead": "15de356a883bbfaec5252c009875fda3781d460b", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.4", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "33168af4a21e99b00c5d41cbadb6a6cb49903a45", "size": 13368, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.4.tgz", "integrity": "sha512-WLsTMEhsQuXpCiG173+f3aymI43SXa+fB1rSfbzyP4GkPP+ZFVuO0/3sFUGNBtifisPeDcl/uD/Y2NxZ7xFq4g=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.4_1541209959481_0.41712550837109075"}, "_hasShrinkwrap": false, "publish_time": 1541209959632, "_cnpm_publish_time": 1541209959632, "_cnpmcore_publish_time": "2021-12-13T15:07:45.993Z"}, "5.0.3": {"name": "ignore", "version": "5.0.3", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "gitHead": "4d97791228b6d4b0de8ad4ee84df322adf1ab8ae", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "b1ec93c7d9c3207937248ba06579dda6bf4657bf", "size": 13316, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.3.tgz", "integrity": "sha512-jJ7mKezpwiCj29DWDPORNJ6P90RpT2i4kfKLxioSb0VcGnoWuib5eg9dOXR45bghMYxVNUeKoJR1UGJ/sS3Oqw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.3_1540769463286_0.37629573667049443"}, "_hasShrinkwrap": false, "publish_time": 1540769463431, "_cnpm_publish_time": 1540769463431, "_cnpmcore_publish_time": "2021-12-13T15:07:46.401Z"}, "5.0.2": {"name": "ignore", "version": "5.0.2", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:ts": "node ./test/ts/simple.js", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "gitHead": "73cf5b8046ad306576a797edefdcfbdebf768cf9", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "42ae2651beb40c8c53b1e16b2af1c8387d492f88", "size": 13297, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.2.tgz", "integrity": "sha512-ilxkgh36cTqJxlipxQdCOxkbQae5dIeCwo5fSw6pBDW8m8GiMTnadClKST2+aATqjs9BTHsi0IqOsTp0jiihAw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.2_1534768699397_0.7244398930356268"}, "_hasShrinkwrap": false, "publish_time": 1534768699559, "_cnpm_publish_time": 1534768699559, "_cnpmcore_publish_time": "2021-12-13T15:07:46.813Z"}, "5.0.1": {"name": "ignore", "version": "5.0.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "gitHead": "7b962a70f980baa567d6b989123cda898d2644ab", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "1c3a8fdab8d98b2a1408d5b136d64f13fd5512b1", "size": 13176, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.1.tgz", "integrity": "sha512-Hk5rrTD3446f7JwRRe5oFDb8uMKmA9XlaxSrixKzEryN8qxS/rVS1chEsKNQ8ploITn/2JUsKXq0saVIbXNPZQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.1_1534206124251_0.41089891952975477"}, "_hasShrinkwrap": false, "publish_time": 1534206124418, "deprecated": "due to failure of importing in typescript", "_cnpm_publish_time": 1534206124418, "_cnpmcore_publish_time": "2021-12-13T15:07:47.256Z"}, "5.0.0": {"name": "ignore", "version": "5.0.0", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js", "test:others": "tap test/others.js", "test:cases": "tap test/*.js --coverage", "test-no-report": "npm run test:lint && npm run test:tsc && npm run test:cases", "test": "npm run test-no-report", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "gitHead": "c38c9a3a3ff8abfa92004a0d47a57e0220e5a477", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.0.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "472ae5bde65b41995a7ca43ad5634d3fdcb60eeb", "size": 13096, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.0.0.tgz", "integrity": "sha512-pztwzOo3khA5uk+1N6erjer98k1hBPsCA3EYoCBYEjeRKo1lYUEwLfOhnHuHE9ahHepH2o+Fc3KtUzQbmKTHAA=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.0.0_1534172523895_0.6636239709151193"}, "_hasShrinkwrap": false, "publish_time": 1534172524069, "deprecated": "due to failure of importing in typescript", "_cnpm_publish_time": 1534172524069, "_cnpmcore_publish_time": "2021-12-13T15:07:47.741Z"}, "4.0.6": {"name": "ignore", "version": "4.0.6", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "gitHead": "56976cef6d5ec0e5651910801669f9aa3daa8120", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.6", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "750e3db5862087b4737ebac8207ffd1ef27b25fc", "size": 11533, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.6.tgz", "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.6_1534048762397_0.9093604384889431"}, "_hasShrinkwrap": false, "publish_time": 1534048762496, "_cnpm_publish_time": 1534048762496, "_cnpmcore_publish_time": "2021-12-13T15:07:48.184Z"}, "4.0.5": {"name": "ignore", "version": "4.0.5", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.2", "eslint": "^5.0.0-rc.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^2.9.2"}, "engines": {"node": ">= 4"}, "gitHead": "9ff8eebc0ace107072bee9ab86dd7ead04f903d1", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "333535a20325ba4852c4ddb135d47392aa035e6d", "size": 11482, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.5.tgz", "integrity": "sha512-Q2daVnMtQJPacGrcCRyOEiI+syPCt+mR4YotoC0KEYeinV/6HztT5mUuVEj7UYyoNZ1jGYiu2XEem7I8oM44bg=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.5_1533792290302_0.7543634350902766"}, "_hasShrinkwrap": false, "publish_time": 1533792290422, "_cnpm_publish_time": 1533792290422, "_cnpmcore_publish_time": "2021-12-13T15:07:48.652Z"}, "4.0.4": {"name": "ignore", "version": "4.0.4", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.2", "eslint": "^5.0.0-rc.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^2.9.2"}, "engines": {"node": ">= 4"}, "gitHead": "b4b1e6e89b35031273b13a80e8c24fe5f47b4abf", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.4", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "5b3ef31afe749eea3cd47ca8090f3087f3e956f9", "size": 11483, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.4.tgz", "integrity": "sha512-q3/Ov3oZyltXb8WJGZr/aJ/k3/3b64VqOJqA+2RYZPGqcZJAU8LftTnhrxYIfhe09UPzPEWiNdeJ/7wCgYVppQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.4_1533791966804_0.2948283770838811"}, "_hasShrinkwrap": false, "publish_time": 1533791966876, "_cnpm_publish_time": 1533791966876, "_cnpmcore_publish_time": "2021-12-13T15:07:49.110Z"}, "4.0.3": {"name": "ignore", "version": "4.0.3", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.2", "eslint": "^5.0.0-rc.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^2.9.2"}, "engines": {"node": ">= 4"}, "gitHead": "63400ebced1d57515d08532ee9e960f71bdb82c4", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.3", "_npmVersion": "6.2.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "e2d58c9654d75b542529fa28d80ac95b29e4f467", "size": 11168, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.3.tgz", "integrity": "sha512-Z/vAH2GGIEATQnBVXMclE2IGV6i0GyVngKThcGZ5kHgHMxLo9Ow2+XHRq1aEKEej5vOF1TPJNbvX6J/anT0M7A=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.3_1533042351776_0.999141270131769"}, "_hasShrinkwrap": false, "publish_time": 1533042351873, "_cnpm_publish_time": 1533042351873, "_cnpmcore_publish_time": "2021-12-13T15:07:49.627Z"}, "4.0.2": {"name": "ignore", "version": "4.0.2", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=text-lcov && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.2", "eslint": "^5.0.0-rc.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^2.9.2"}, "engines": {"node": ">= 4"}, "gitHead": "fe682e8f6f78946663855025d74c49518d564f16", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "0a8dd228947ec78c2d7f736b1642a9f7317c1905", "size": 10388, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.2.tgz", "integrity": "sha512-uoxnT7PYpyEnsja+yX+7v49B7LXxmzDJ2JALqHH3oEGzpM2U1IGcbfnOr8Dt57z3B/UWs7/iAgPFbmye8m4I0g=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.2_1529632860185_0.47995525113340043"}, "_hasShrinkwrap": false, "publish_time": 1529632860317, "_cnpm_publish_time": 1529632860317, "_cnpmcore_publish_time": "2021-12-13T15:07:50.099Z"}, "4.0.1": {"name": "ignore", "version": "4.0.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=text-lcov && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.2", "eslint": "^5.0.0-rc.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^2.9.2"}, "engines": {"node": ">= 4"}, "gitHead": "1fff90759598aa974dd05a90beb740b62d0947be", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "030066be226d0376532d9022d50f8dcd5905fedd", "size": 10392, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.1.tgz", "integrity": "sha512-LX0UA2/v3je72Ppc/dsqEC752wQriqFQS5ZATVYLljhisN92fS5eSvwzCQc4zjFmhMFMeTNtRu77RdeFgReDkg=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.1_1529630950117_0.3570148484180562"}, "_hasShrinkwrap": false, "publish_time": 1529630950234, "_cnpm_publish_time": 1529630950234, "_cnpmcore_publish_time": "2021-12-13T15:07:50.595Z"}, "4.0.0": {"name": "ignore", "version": "4.0.0", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "files": ["legacy.js", "index.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test": "npm run test-no-cov", "posttest": "tap --coverage-report=text-lcov && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.2", "eslint": "^5.0.0-rc.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^2.9.2"}, "engines": {"node": ">= 4"}, "gitHead": "ca5df7b47f8c7b394df87bd99e83434b5bf70a42", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@4.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "43b1839bed2e6011d91a6f3a80d7253467782ad1", "size": 10037, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-4.0.0.tgz", "integrity": "sha512-mpvs0moBKEwjP/i7V7txWinseHOqvIb8Ej0LahMzpP8T7TavluadZ6HsRSpO0JmZcapSumU1j3NlCcxvyLCefg=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_4.0.0_1529624509595_0.19889418885068344"}, "_hasShrinkwrap": false, "publish_time": 1529624509682, "_cnpm_publish_time": 1529624509682, "_cnpmcore_publish_time": "2021-12-13T15:07:51.086Z"}, "3.3.10": {"name": "ignore", "version": "3.3.10", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o ignore.js index.js", "tsc": "tsc ./test/ts/simple.ts", "test": "npm run tsc && npm run build && istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "npm run tsc && npm run build && mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "~1.7.2", "codecov": "^3.0.2", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33", "typescript": "^2.9.2"}, "gitHead": "d01ef8d91497a771b5e3adf52b2ed19248a7c625", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.10", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "0a97fb876986e8081c631160f8f9f389157f0043", "size": 7922, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.10.tgz", "integrity": "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_3.3.10_1529546817916_0.8578310196983732"}, "_hasShrinkwrap": false, "publish_time": 1529546818015, "_cnpm_publish_time": 1529546818015, "_cnpmcore_publish_time": "2021-12-13T15:07:51.534Z"}, "3.3.9": {"name": "ignore", "version": "3.3.9", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o ignore.js index.js", "test": "npm run build && istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "npm run build && mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33"}, "gitHead": "8f74c9311114c7e77e5b872fe59a059de48b0df9", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.9", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "64c3cb5b4f100f064e44b05e64e1b3a1d43b0d7b", "size": 7882, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.9.tgz", "integrity": "sha512-9LVn4F46ZRXwh/qYI1qvo2N6ixSCll/3AqTprkNt9lUQBUxRIZYTx3AZWwPXQaEYkjX75Dc4nq0ZZwIcPyruiQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_3.3.9_1529545051782_0.09968359545363636"}, "_hasShrinkwrap": false, "publish_time": 1529545051839, "_cnpm_publish_time": 1529545051839, "_cnpmcore_publish_time": "2021-12-13T15:07:52.208Z"}, "3.3.8": {"name": "ignore", "version": "3.3.8", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o ignore.js index.js", "test": "npm run build && istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "npm run build && mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33"}, "gitHead": "c594bc08eee6f067acb12fc61e5afc78b3559783", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.8", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "3f8e9c35d38708a3a7e0e9abb6c73e7ee7707b2b", "size": 7899, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.8.tgz", "integrity": "sha512-pUh+xUQQhQzevjRHHFqqcTy0/dP/kS9I8HSrUydhihjuD09W6ldVWFtIrwhXdUJHis3i2rZNqEHpZH/cbinFbg=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_3.3.8_1524708339142_0.9572980251499248"}, "_hasShrinkwrap": false, "publish_time": 1524708339275, "_cnpm_publish_time": 1524708339275, "_cnpmcore_publish_time": "2021-12-13T15:07:52.809Z"}, "3.3.7": {"name": "ignore", "version": "3.3.7", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o ignore.js index.js", "test": "npm run build && istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "npm run build && mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33"}, "gitHead": "cc0395d925d477b022a153a656c5832eda4c9816", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.7", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "612289bfb3c220e186a58118618d5be8c1bab021", "size": 7886, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.7.tgz", "integrity": "sha512-YGG3ejvBNHRqu0559EOxxNFihD0AjpvHlC/pdGKd3X3ofe+CoJkYazwNJYTNebqpPKN+VVQbh4ZFn1DivMNuHA=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore-3.3.7.tgz_1509026454121_0.4620765303261578"}, "directories": {}, "publish_time": 1509026454429, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509026454429, "_cnpmcore_publish_time": "2021-12-13T15:07:53.448Z"}, "3.3.6": {"name": "ignore", "version": "3.3.6", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33"}, "gitHead": "3363984a18d11e1a1b194623bf13ec5feb95d5ad", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.6", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "b6f3196b38ed92f0c86e52f6f79b7fc4c8266c8d", "size": 8485, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.6.tgz", "integrity": "sha512-HrxmNxKTGZ9a3uAl/FNG66Sdt0G9L4TtMbbUQjP1WhGmSj0FOyHvSgx7623aGJvXfPOur8MwmarlHT+37jmzlw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore-3.3.6.tgz_1508640684588_0.5321662772912532"}, "directories": {}, "publish_time": 1508640685061, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508640685061, "_cnpmcore_publish_time": "2021-12-13T15:07:54.124Z"}, "3.3.5": {"name": "ignore", "version": "3.3.5", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.1", "spawn-sync": "^1.0.15", "tmp": "0.0.33"}, "gitHead": "4b19c27815d65389f3a80ba4c5f49b6d77a0089c", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.5", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "c4e715455f6073a8d7e5dae72d2fc9d71663dba6", "size": 8392, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.5.tgz", "integrity": "sha512-JLH93mL8amZQhh/p6mfQgVBH3M6epNq3DfsXsTSuSrInVjwyYlFE1nv2AgfRCC8PoOhM0jwQ5v8s9LgbK7yGDw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore-3.3.5.tgz_1503992140274_0.9996604435145855"}, "directories": {}, "publish_time": 1503992140390, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503992140390, "_cnpmcore_publish_time": "2021-12-13T15:07:54.827Z"}, "3.3.4": {"name": "ignore", "version": "3.3.4", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "7987d1f5b864eecdf7afcfbf09f078c4dd8563b2", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.4", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "85ab6d0a9ca8b27b31604c09efe1c14dc21ab872", "size": 8316, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.4.tgz", "integrity": "sha512-KjHyHxUgicfgFiTJaIA9DoeY3TIQz5thaKqm35re7RTVVB7zjF1fTMIDMXM4GUUBipR4FW8BvGnA115pZ/AxQQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore-3.3.4.tgz_1503719743133_0.9470621331129223"}, "directories": {}, "publish_time": 1503719743245, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503719743245, "_cnpmcore_publish_time": "2021-12-13T15:07:55.462Z"}, "3.3.3": {"name": "ignore", "version": "3.3.3", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "fc1aacc4d3a72c1f5f3fa5e9aadc12a14a382db4", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.3", "_shasum": "432352e57accd87ab3110e82d3fea0e47812156d", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "432352e57accd87ab3110e82d3fea0e47812156d", "size": 8199, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.3.tgz", "integrity": "sha512-EreSWopcoOuUkFfoYLwnaiDVfyyI4vmaYJN2k9XtwUH0GBRjXcJ6WC9yLrx7+5V1IL9VW+AltFnFG+N9Dp467Q=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.3.3.tgz_1495192478088_0.7693700036033988"}, "directories": {}, "publish_time": 1495192478357, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495192478357, "_cnpmcore_publish_time": "2021-12-13T15:07:56.162Z"}, "3.3.2": {"name": "ignore", "version": "3.3.2", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "c527b863b36865c629afcda4753c05a0c8681077", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.2", "_shasum": "93b43a377aa22de9878defc60be9371cb894f784", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "93b43a377aa22de9878defc60be9371cb894f784", "size": 8185, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.2.tgz", "integrity": "sha512-+nshm8sGAskHzwcQM7Lp5WjKND6OF2tLfqusuq7E/QjAPBPB36hk5hGlJUFji/C2LVjcTzonr1bFBjhal+uJxw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.3.2.tgz_1495192276035_0.18066685367375612"}, "directories": {}, "publish_time": 1495192276285, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495192276285, "_cnpmcore_publish_time": "2021-12-13T15:07:56.958Z"}, "3.3.1": {"name": "ignore", "version": "3.3.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "c7274d5af9503226cc6859eed1c5da4e8eabc4d0", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.1", "_shasum": "2778399a2c12c6a1f1a34df590a5a07d5ef15fb5", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "2778399a2c12c6a1f1a34df590a5a07d5ef15fb5", "size": 8181, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.1.tgz", "integrity": "sha512-5pezEUwgnVATQnhnncdh58VNAvFtSCQ7389I9HqBDjrCgURnFAPcp2WQJ9xS42e+/fqF23mDsnfa5U0FZVEngQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.3.1.tgz_1495192213318_0.4818163891322911"}, "directories": {}, "publish_time": 1495192213607, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495192213607, "_cnpmcore_publish_time": "2021-12-13T15:07:57.673Z"}, "3.3.0": {"name": "ignore", "version": "3.3.0", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "e7b16589c27efc321c6d31c1ee822e3050b2236d", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.3.0", "_shasum": "3812d22cbe9125f2c2b4915755a1b8abd745a001", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "3812d22cbe9125f2c2b4915755a1b8abd745a001", "size": 8142, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.3.0.tgz", "integrity": "sha512-JkX5lAQk9mBbAa5NMZ2o8IgyRgsxV+Fq77T0q5gW1FNuAh7QfE8S9mwj8pv6XeBHihS6f11dqLPYKqwD7t9Ggw=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ignore-3.3.0.tgz_1493715623974_0.9297560532577336"}, "directories": {}, "publish_time": 1493715625712, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493715625712, "_cnpmcore_publish_time": "2021-12-13T15:07:58.335Z"}, "3.2.7": {"name": "ignore", "version": "3.2.7", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "cb06a8101172a17536ac318a979b30c2c5951ed8", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.7", "_shasum": "4810ca5f1d8eca5595213a34b94f2eb4ed926bbd", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "dist": {"shasum": "4810ca5f1d8eca5595213a34b94f2eb4ed926bbd", "size": 7861, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.7.tgz", "integrity": "sha512-1TOmnRz9dPdENb7r5U4ChOc4Nf9KajGJArEPDs//IBHGisx7OY0KbQZN0ipmGFImuTzFDv7u0hsGY8Hbhtb5aQ=="}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ignore-3.2.7.tgz_1491529689131_0.6010189142543823"}, "directories": {}, "publish_time": 1491529691067, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491529691067, "_cnpmcore_publish_time": "2021-12-13T15:07:59.153Z"}, "3.2.6": {"name": "ignore", "version": "3.2.6", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "c62a7b1568a5674e6c0a68d234dc24f26f5324c5", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.6", "_shasum": "26e8da0644be0bb4cb39516f6c79f0e0f4ffe48c", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "26e8da0644be0bb4cb39516f6c79f0e0f4ffe48c", "size": 7869, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.6.tgz", "integrity": "sha512-6X+/4O51yfrZ5F/eTL2XO8EU2USFgt9UWaWkVJou/V9JMHm+MdyQf0ZQqjc7Y2CtxJwepmrwbAdTDGuaWRo0JA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.2.6.tgz_1489647552814_0.3715519669931382"}, "directories": {}, "publish_time": 1489647553061, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489647553061, "_cnpmcore_publish_time": "2021-12-13T15:07:59.975Z"}, "3.2.5": {"name": "ignore", "version": "3.2.5", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "6e113dbfac457e69bd4774315e77363bef892eb2", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.5", "_shasum": "6437903354653e32ebbf562c45e68e4922a95df6", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "6437903354653e32ebbf562c45e68e4922a95df6", "size": 7851, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.5.tgz", "integrity": "sha512-6P+5blgXq9zk2/u5ho46hq8WavXkVqY1nwbGfEaP4J07W09PVOsM85A2FJTHiRBqPL0drI62iDPEaZaQ5oBl+A=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ignore-3.2.5.tgz_1489570800726_0.6880804856773466"}, "directories": {}, "publish_time": 1489570802686, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489570802686, "_cnpmcore_publish_time": "2021-12-13T15:08:00.868Z"}, "3.2.4": {"name": "ignore", "version": "3.2.4", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "d2dd09ba2a8c35f4a283536c59f2f1b19ca151bd", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.4", "_shasum": "4055e03596729a8fabe45a43c100ad5ed815c4e8", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "4055e03596729a8fabe45a43c100ad5ed815c4e8", "size": 7803, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.4.tgz", "integrity": "sha512-pAgA00hxl+F8OBKYLi7DGis7KveroArYbZowZibOn+tLhU48SkEPhcf32IsACPxAIQoQH6dPUd1het511782DA=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ignore-3.2.4.tgz_1487564451134_0.239100860664621"}, "directories": {}, "publish_time": 1487564453075, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487564453075, "_cnpmcore_publish_time": "2021-12-13T15:08:01.804Z"}, "3.2.3": {"name": "ignore", "version": "3.2.3", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"chai": "~1.7.2", "codecov": "^1.0.1", "istanbul": "^0.4.5", "mocha": "~1.13.0"}, "gitHead": "07f3977f817815b8b02ebc95bc6ae897cf3651a5", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.3", "_shasum": "bb266840ab8c38f7556df998682d45b2102e433c", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "bb266840ab8c38f7556df998682d45b2102e433c", "size": 7712, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.3.tgz", "integrity": "sha512-mlS5frVrRQnfBJwS0kwYNmLNAwwWaB9nynb+DsEKw6o7nu9IpNPqvUJmjSauh88+RNbCj1TdLqoQUqfED+lRtw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.2.3.tgz_1487563269602_0.4952603033743799"}, "directories": {}, "publish_time": 1487563269840, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487563269840, "_cnpmcore_publish_time": "2021-12-13T15:08:02.582Z"}, "3.2.2": {"name": "ignore", "version": "3.2.2", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "5e530290ac4fe36e813e8bd8f58b888ca93f498c", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.2", "_shasum": "1c51e1ef53bab6ddc15db4d9ac4ec139eceb3410", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "1c51e1ef53bab6ddc15db4d9ac4ec139eceb3410", "size": 7572, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.2.tgz", "integrity": "sha512-/kEEGWvuI3XdEtm3W2BU93omTfehSgf5R22nR3TkZ4I0slYbiVnjqlc73uHfndf12Pn0Wo+uXUva0n1p3/VyDg=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ignore-3.2.2.tgz_1485573872985_0.3954287748783827"}, "directories": {}, "publish_time": 1485573874847, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485573874847, "_cnpmcore_publish_time": "2021-12-13T15:08:03.326Z"}, "3.2.1": {"name": "ignore", "version": "3.2.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "a42d468b4caa63b6bf196c08262874d455abf4e0", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.1", "_shasum": "c614608a4a5406e8297cf517a7b333234068a7bc", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "c614608a4a5406e8297cf517a7b333234068a7bc", "size": 7580, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.1.tgz", "integrity": "sha512-jtXc7+t90aXYTa+44StdX0R032cfCeW6Ylv5JOOHFFQYjCy+5vUYHEcKD3wI/k13BtDxxOrlclMaAPhJ8wlkOg=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ignore-3.2.1.tgz_1485572127851_0.6016609752550721"}, "directories": {}, "publish_time": 1485572129585, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1485572129585, "_cnpmcore_publish_time": "2021-12-13T15:08:04.098Z"}, "3.2.0": {"name": "ignore", "version": "3.2.0", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "703d5b198812a6c9b2d6250c1a04aeb81d5e3949", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.2.0", "_shasum": "8d88f03c3002a0ac52114db25d2c673b0bf1e435", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "8d88f03c3002a0ac52114db25d2c673b0bf1e435", "size": 7566, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.2.0.tgz", "integrity": "sha512-cH6yGq1zCxPOcN8tUmbBABAn2wOKiIHBIYnoy1vMtUJ/h9YogNGEWEKDD9FtJ4+6LqR5H+CrA+XhrjVbwAE/sw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.2.0.tgz_1476173756695_0.2819231322500855"}, "directories": {}, "publish_time": 1476173756913, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1476173756913, "_cnpmcore_publish_time": "2021-12-13T15:08:04.890Z"}, "3.1.5": {"name": "ignore", "version": "3.1.5", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "316d0704f4ad631df14e1be5b993c64f4466b2c1", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.1.5", "_shasum": "54ba1eb92ef9fff8d49e5a1fb23961cdba77eb7a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "54ba1eb92ef9fff8d49e5a1fb23961cdba77eb7a", "size": 7493, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.1.5.tgz", "integrity": "sha512-FZ+dWDQIXh+XNGyC92lpLajvhZsRhjjNQ+RV7fS9fQ4kEvx9WRXGTUQin4wfDePbWIDWNJavfgkVPK+fGqSuiQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.1.5.tgz_1471234764185_0.0016845394857227802"}, "directories": {}, "publish_time": 1471234764469, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1471234764469, "_cnpmcore_publish_time": "2021-12-13T15:08:05.623Z"}, "3.1.4": {"name": "ignore", "version": "3.1.4", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "293bd927bed14a61a308250044ece984cd48e53a", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.1.4", "_shasum": "aac13c8a69f58088474e5140bb0723f1345e7aaa", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "aac13c8a69f58088474e5140bb0723f1345e7aaa", "size": 7485, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.1.4.tgz", "integrity": "sha512-XRTlkU+oIVOcWNCIKc5V8IJDfO4lVUzoG3VYp4B4sD0aHXRQjW+NURx6oIbJfOdUBlYW/WYd27MaWtojY/5Tyw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.1.4.tgz_1471230272528_0.023431980283930898"}, "directories": {}, "publish_time": 1471230272777, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1471230272777, "_cnpmcore_publish_time": "2021-12-13T15:08:07.019Z"}, "3.1.3": {"name": "ignore", "version": "3.1.3", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "05adc3e323d72b76cf1e3be7e9e8e38f43d3a544", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.1.3", "_shasum": "9e890c0652519115ae9427da47516bd54d1d6999", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "9e890c0652519115ae9427da47516bd54d1d6999", "size": 5050, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.1.3.tgz", "integrity": "sha512-8jbofPRzl/bV3ydXBHqvr9OnuC4wzp5sMUB6f/gopdnb8jEUmUYMitUAGi0IDcGNd69N2N2cJYQfGqWxnHO5xg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ignore-3.1.3.tgz_1466563368081_0.9863177102524787"}, "directories": {}, "publish_time": 1466563370798, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1466563370798, "_cnpmcore_publish_time": "2021-12-13T15:08:07.893Z"}, "3.1.2": {"name": "ignore", "version": "3.1.2", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "3bfb96869396873cc08ab6239f0f08fbd43863f5", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.1.2", "_shasum": "dd17765e9233b4019762ba82b892202b0980161b", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "dd17765e9233b4019762ba82b892202b0980161b", "size": 7260, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.1.2.tgz", "integrity": "sha512-PKb/XF7/6PPJq918K57m8c42cjo8MGr8d1INLVR1kxSoRdkjIXpOiaxFo5LuYTUWByc+ukNYLfHfU4kji9zahQ=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ignore-3.1.2.tgz_1461812650504_0.7420230738352984"}, "directories": {}, "publish_time": 1461812652693, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1461812652693, "_cnpmcore_publish_time": "2021-12-13T15:08:08.921Z"}, "3.1.1": {"name": "ignore", "version": "3.1.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "043fa425fc967c9b5745d83ccb45253967d7a14e", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.1.1", "_shasum": "09e941c520c61452a8ec127fe70b4b6e4f281cde", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "09e941c520c61452a8ec127fe70b4b6e4f281cde", "size": 7199, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.1.1.tgz", "integrity": "sha512-NlUQyvew/se0/GekcwcLv0dJMwS2Ecfr1spahien0Uf32NgM65JORQG1a0yySXW9iUP1Md1KRBJ4qmPOb0kGcQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.1.1.tgz_1460180585110_0.08555315271951258"}, "directories": {}, "publish_time": 1460180585520, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1460180585520, "_cnpmcore_publish_time": "2021-12-13T15:08:09.893Z"}, "3.1.0": {"name": "ignore", "version": "3.1.0", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "9b3b2dd03de778addc3da7ad30335be2af170fff", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.1.0", "_shasum": "9c712aad19cb19ec1a4ca1d88038ff7b4b3e7a75", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "9c712aad19cb19ec1a4ca1d88038ff7b4b3e7a75", "size": 7193, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.1.0.tgz", "integrity": "sha512-5AvQgW2Rg/WLIJ0hTvVY4wRu3j+9I0O6Gx9HDF8ZHdQTBkJF2OMlYxRosez5mkpeQj7V6MiO+MBECzfjJCo7kA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.1.0.tgz_1460179279549_0.2266258834861219"}, "directories": {}, "publish_time": 1460179279926, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1460179279926, "_cnpmcore_publish_time": "2021-12-13T15:08:10.788Z"}, "3.0.14": {"name": "ignore", "version": "3.0.14", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "1c136a4cb2fee6a8bf13c38b8c29fbaaad7c9cac", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.14", "_shasum": "1b07d5d0810ac2571aab1a8b33485f2c48fb130e", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "1b07d5d0810ac2571aab1a8b33485f2c48fb130e", "size": 6793, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.14.tgz", "integrity": "sha512-t6yCBw237TWLa4WuOfrglbD4/tXHa4IxwVbhp2lc519o4oE2TK6n1hh1rin9BHx04UNMMrbuiXdz38JpEq4HDg=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.14.tgz_1459313955066_0.6848438421729952"}, "directories": {}, "publish_time": 1459313955503, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1459313955503, "_cnpmcore_publish_time": "2021-12-13T15:08:11.722Z"}, "3.0.13": {"name": "ignore", "version": "3.0.13", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "9311beba8a2a386a51576e47698b513fc0d11d72", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.13", "_shasum": "b6ffbdd44496e9fd588bf5ebe6d01c5380938647", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "b6ffbdd44496e9fd588bf5ebe6d01c5380938647", "size": 6618, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.13.tgz", "integrity": "sha512-OHSI2tT8U05AsloieR3ZnaCxhPlXzqw8htlH22EfXQfGIO3h0fruq/TzOJlyTj0Ak7qwpjv0VBtm7byB9szPOA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.13.tgz_1459238056978_0.3897583440411836"}, "directories": {}, "publish_time": 1459238057415, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1459238057415, "_cnpmcore_publish_time": "2021-12-13T15:08:12.733Z"}, "3.0.12": {"name": "ignore", "version": "3.0.12", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "308664db001eadb113ee14de04b27b35808aab05", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.12", "_shasum": "6f76d84ac38279c49d1c66201a5dd9371120db8c", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "6f76d84ac38279c49d1c66201a5dd9371120db8c", "size": 6600, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.12.tgz", "integrity": "sha512-V94De+D85RINmaiyOKdzvwVqRkCwzhH2XbvBb6kVOC1SLbPSwZ86SnU1OVjjEqcDU9GvkyJZLx6IwyW2cwtKyA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.12.tgz_1459141944386_0.9100932809524238"}, "directories": {}, "publish_time": 1459141944769, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1459141944769, "_cnpmcore_publish_time": "2021-12-13T15:08:13.730Z"}, "3.0.11": {"name": "ignore", "version": "3.0.11", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "c8c058cb7390ec03871684089b04a132a114a07c", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.11", "_shasum": "bb3439e55bdcdc18aee4d85f265b315791c06fcf", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "bb3439e55bdcdc18aee4d85f265b315791c06fcf", "size": 6530, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.11.tgz", "integrity": "sha512-nocgeSgLcsj4ayF4UOh93GuCuSM76jmD/PIqV0Ab07vcKnhYRC6A9izPe0EiYUq8a46HWZRRrvZitzqjJirRew=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.11.tgz_1458638860355_0.9130491907708347"}, "directories": {}, "publish_time": 1458638860790, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1458638860790, "_cnpmcore_publish_time": "2021-12-13T15:08:14.557Z"}, "3.0.10": {"name": "ignore", "version": "3.0.10", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "86ce136142fea132b20d3a4590d0d303939af429", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.10", "_shasum": "f729abd3397a960ed805c853ec8f1d9567f5f102", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "f729abd3397a960ed805c853ec8f1d9567f5f102", "size": 6507, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.10.tgz", "integrity": "sha512-0GwPOEDbsk+Lb+Cghe+RJfMVoqR02/lewBWGbSdrE9RZh0zk6iJbNPH5oAhrh2ilMWG5MS7FYxBq/dDMBdz09A=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.10.tgz_1457577689344_0.12228059931658208"}, "directories": {}, "publish_time": 1457577689726, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457577689726, "_cnpmcore_publish_time": "2021-12-13T15:08:15.432Z"}, "3.0.9": {"name": "ignore", "version": "3.0.9", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "261955ca5fcc088f084dbb0e15132c37cc547d25", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.9", "_shasum": "6c0d8609818c5689f71621511ec3454a262d5c83", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "6c0d8609818c5689f71621511ec3454a262d5c83", "size": 6507, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.9.tgz", "integrity": "sha512-V6ffA/gKBg8hBA8kO0HlmhzAcWjFYeEwGNCxfQkwZbQpoqkQqvuTulTIoU6DPNh11bKqyPlQysKGDYTzg7NOtw=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.9.tgz_1457454152195_0.4477692584041506"}, "directories": {}, "publish_time": 1457454156326, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457454156326, "_cnpmcore_publish_time": "2021-12-13T15:08:16.404Z"}, "3.0.8": {"name": "ignore", "version": "3.0.8", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "83b7f45e074ab96c79409427fbb70dfae8b75608", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.8", "_shasum": "ea5f65a087935d261410333221f7d55061229ef1", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "ea5f65a087935d261410333221f7d55061229ef1", "size": 6438, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.8.tgz", "integrity": "sha512-/PyJWOOPB/JOiDSMjhtCcMzYp4sdYGZkuHuiLgYeyNPaL0dMQQhLPZiReQ1zJS3wQ5sn/2D9kry1Hu/8ZwcKbw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.8.tgz_1457452999047_0.7156061171554029"}, "directories": {}, "publish_time": 1457453003175, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457453003175, "_cnpmcore_publish_time": "2021-12-13T15:08:17.329Z"}, "3.0.7": {"name": "ignore", "version": "3.0.7", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "056db8495e740e7a427dc3d91447c282d6802fb8", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.7", "_shasum": "c3c7e62d660f17fb086e9e8008ab9ecbeaf24bcd", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "c3c7e62d660f17fb086e9e8008ab9ecbeaf24bcd", "size": 6388, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.7.tgz", "integrity": "sha512-+Z3TT5k/j1EYo/0XHvmlBTAgjHE8Sq7M4nJJQwuZdEAHhc5iEzMZ+mSUqhoPbTtnHYC16/6YX4JUHY+uAUoddQ=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.7.tgz_1457450699554_0.8954281678888947"}, "directories": {}, "publish_time": 1457450703944, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457450703944, "_cnpmcore_publish_time": "2021-12-13T15:08:18.384Z"}, "3.0.6": {"name": "ignore", "version": "3.0.6", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "775d3d807648586b404ae015cd809ee294df2b20", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.6", "_shasum": "d21f5d2ac2c91c2ebdd7c419e13450cd6a87746b", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "d21f5d2ac2c91c2ebdd7c419e13450cd6a87746b", "size": 6385, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.6.tgz", "integrity": "sha512-cIRt7nTyu0Ub+wh+8SD3hQ1cNxFgpJQ8udu5fz6ss4PALcBYHedmdw4O8nPEWhfYWgTrKVW5lv6Or3vUdZX8BQ=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.6.tgz_1457448366782_0.047212473349645734"}, "directories": {}, "publish_time": 1457448369273, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457448369273, "_cnpmcore_publish_time": "2021-12-13T15:08:19.308Z"}, "3.0.5": {"name": "ignore", "version": "3.0.5", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "a18ae713eafef284d48dc523fc946b76ed7aea78", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.5", "_shasum": "b2613cfc4666183185544b48c55169071ed5d2f4", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "b2613cfc4666183185544b48c55169071ed5d2f4", "size": 6057, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.5.tgz", "integrity": "sha512-oxdUnfeB41HJY///6trfbGKn/ahljlbX3+VBgxXEXFeOwk727TkMGij9KRC2KX6csXjZn3JlLIqkdD+UIURlwQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.5.tgz_1457440761999_0.0653461494948715"}, "directories": {}, "publish_time": 1457440765689, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457440765689, "_cnpmcore_publish_time": "2021-12-13T15:08:20.253Z"}, "3.0.4": {"name": "ignore", "version": "3.0.4", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "b6a3f6019b0902062c230642a174505b84571012", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.4", "_shasum": "426b30ed0a589682390e9ac174b20099c589151a", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "426b30ed0a589682390e9ac174b20099c589151a", "size": 6054, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.4.tgz", "integrity": "sha512-qhorCnPDr7fYt10BVHpY7njBl5wWvD23MPqIoFE8IibDsS4RgW7MjzmNrx9n9YJwosYGNII+y/VdeQV0wOnM4w=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.4.tgz_1457440672852_0.9447373012080789"}, "directories": {}, "publish_time": 1457440676460, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1457440676460, "_cnpmcore_publish_time": "2021-12-13T15:08:21.253Z"}, "3.0.3": {"name": "ignore", "version": "3.0.3", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "b0dd0858f90c39b4b923f3a0fbeae222d375faa7", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.3", "_shasum": "8caf5840f838b994e0ebb5bcf774f0f3fd0c713a", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "8caf5840f838b994e0ebb5bcf774f0f3fd0c713a", "size": 6891, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.3.tgz", "integrity": "sha512-8CVD72gfqrhH4ZqzHa0OG7A6BAB3VmuhRivZ4bSRVvFxRG+jApHl+usiKAZgiZCqGlOFbGOfr/RhTy37SpYtJA=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/ignore-3.0.3.tgz_1456841001590_0.8033128555398434"}, "directories": {}, "publish_time": 1456841004949, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1456841004949, "_cnpmcore_publish_time": "2021-12-13T15:08:22.198Z"}, "3.0.2": {"name": "ignore", "version": "3.0.2", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "dd3d9da8411b1fb669b60102c2f2a2e603d6cb2e", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.2", "_shasum": "b3fa8a9e057d2e9ad79f0411494a3c2c6fab4b8b", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "b3fa8a9e057d2e9ad79f0411494a3c2c6fab4b8b", "size": 6910, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.2.tgz", "integrity": "sha512-Icp936PhDoVMAMchOr3vM15R+LiAC0/7tj5kFwxkEVczrUVNpVxexjbGi0TbxWN/oNjjiv1e8Kz4vabGLgJVsw=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/ignore-3.0.2.tgz_1456840453399_0.8623598462436348"}, "directories": {}, "publish_time": 1456840456489, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1456840456489, "_cnpmcore_publish_time": "2021-12-13T15:08:23.216Z"}, "3.0.1": {"name": "ignore", "version": "3.0.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "de73bc3e81b247ac801efc8ce02d207489542330", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.1", "_shasum": "6253f0321ee2b4e665129bd503caafc16da6e840", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "6253f0321ee2b4e665129bd503caafc16da6e840", "size": 6870, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.1.tgz", "integrity": "sha512-SJiHdna9NsVq9HpTrpBZaQvjlRvhz2o0JCYPSwiCtf+1tnm6MZYIohLffmMISC0O8KLuBQ5wspB4kGnyYOQIUg=="}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.1.tgz_1456830822617_0.3830663589760661"}, "directories": {}, "publish_time": 1456830824335, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1456830824335, "_cnpmcore_publish_time": "2021-12-13T15:08:24.288Z"}, "3.0.0": {"name": "ignore", "version": "3.0.0", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "7bd35dd0c1be992f15311290a01c576b4757a822", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@3.0.0", "_shasum": "e4c906213706b1c8648a05d56c0f2916e85ed492", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "e4c906213706b1c8648a05d56c0f2916e85ed492", "size": 6849, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-3.0.0.tgz", "integrity": "sha512-qxOPGwo4FVXzEkwvE7GoeWqXDDIeyyrr2alAbdFBuGP40DZ5GzFB9panjseZmENLzLZn0qH0IxIV4yqeXPt+wA=="}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/ignore-3.0.0.tgz_1456826438566_0.9639915754087269"}, "directories": {}, "publish_time": 1456826442944, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1456826442944, "_cnpmcore_publish_time": "2021-12-13T15:08:25.372Z"}, "2.2.19": {"name": "ignore", "version": "2.2.19", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "97df28de4e8401e08c620986156924c1c0f3f37e", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@2.2.19", "_shasum": "4c845a61f7e50b4a410f6156aaa38b6ad95e0c8f", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "0.12.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "4c845a61f7e50b4a410f6156aaa38b6ad95e0c8f", "size": 5979, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.19.tgz", "integrity": "sha512-Mq1FKJDxSky/AR+Goatl1RmCzWbAHE4fNvgkmJu9Ln8T8A8JbKAd/BHHZQaqknHSFCBe0zQJg7akDpI57pKTlQ=="}, "directories": {}, "publish_time": 1446023100587, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1446023100587, "_cnpmcore_publish_time": "2021-12-13T15:08:26.282Z"}, "2.2.18": {"name": "ignore", "version": "2.2.18", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "e891f01b6fa909a11c57cb6d7f72696c07c875f9", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@2.2.18", "_shasum": "fb1d11a785613a05cfdc56c670dde24fb569ea2f", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "fb1d11a785613a05cfdc56c670dde24fb569ea2f", "size": 5980, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.18.tgz", "integrity": "sha512-ZQjgEPaqKT9knDaYRdgv9p92Np+61PZkbwmlZ3o+AcD8BgTcejBuEQ6ic5mYRbuKhOU81fjiHGEelH89uHCdiQ=="}, "directories": {}, "publish_time": 1444278876370, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1444278876370, "_cnpmcore_publish_time": "2021-12-13T15:08:27.146Z"}, "2.2.17": {"name": "ignore", "version": "2.2.17", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "files": ["index.js", "LICENSE-MIT"], "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "bd58885f541a482a6456a4b4e9819ede7a228347", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@2.2.17", "_shasum": "e597d8f221cff1c377df3f7c4bc1d0042af3dd72", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "e597d8f221cff1c377df3f7c4bc1d0042af3dd72", "size": 5922, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.17.tgz", "integrity": "sha512-QPzSd17UGQxsEidUm/p3mqI9v/2u2ZMylpoYEPsYmuNeAsw3nnTsJGaE56r8acDH3e8VWHa2rpL/EVfac7IAkw=="}, "directories": {}, "publish_time": 1443413301942, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1443413301942, "_cnpmcore_publish_time": "2021-12-13T15:08:28.062Z"}, "2.2.16": {"name": "ignore", "version": "2.2.16", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha --reporter spec ./test/ignore.js"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "gitHead": "81f94749af3d50de751a317e36b85408c34e3d7c", "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.16", "_shasum": "7da08c5db2200e26955af017eec23bc8c915cfe5", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "7da08c5db2200e26955af017eec23bc8c915cfe5", "size": 8100, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.16.tgz", "integrity": "sha512-7yC4e+7cJPSmIFBTaQrPxcU1lX7Du2z+k5LsYvOVxzPD8hBn+RNmIkLWqAfZ+zyQnlVNKMmyAWBRapaiEHoWGw=="}, "directories": {}, "publish_time": 1437629397230, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1437629397230, "_cnpmcore_publish_time": "2021-12-13T15:08:28.920Z"}, "2.2.15": {"name": "ignore", "version": "2.2.15", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.15", "_shasum": "6bd552185e0d1cd393b416603ee686879ec3bc3b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "dist": {"shasum": "6bd552185e0d1cd393b416603ee686879ec3bc3b", "size": 8173, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.15.tgz", "integrity": "sha512-BKmNJZ4GRibszZqYaUBhsGTYSHh1x9B4Z5yak6UKd2RdebehxcR6Hq8pk5V6YRZuRIyDh/RL5CH6HFJqgW5d8g=="}, "directories": {}, "publish_time": 1408382483724, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1408382483724, "_cnpmcore_publish_time": "2021-12-13T15:08:29.892Z"}, "2.2.14": {"name": "ignore", "version": "2.2.14", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.14", "dist": {"shasum": "1c9034ef6a2a71f9216abeefe6256acb8547a01e", "size": 8171, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.14.tgz", "integrity": "sha512-3Pfmq5shzQHMQb/vLeyDK3NQPgsxQ9veb5WuH8F6g3X6F5gXsmk7uo8iKho9kG+3QPEX/hsUISaPRpd480BF4Q=="}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1402913996014, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1402913996014, "_cnpmcore_publish_time": "2021-12-13T15:08:30.879Z"}, "2.2.13": {"name": "ignore", "version": "2.2.13", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.13", "dist": {"shasum": "13b34543d62b017cf9eb184dd4218b79ac9b2185", "size": 8153, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.13.tgz", "integrity": "sha512-F/sDvorVzoZ3JW5dW1pdy1eVMKhGFtZxIoPPTTiSL56gLaEIpwPpmIzMfslU3OYcbAAhQEcejQG9MbnfDe9W2A=="}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1402161039478, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1402161039478, "_cnpmcore_publish_time": "2021-12-13T15:08:31.916Z"}, "2.2.12": {"name": "ignore", "version": "2.2.12", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.12", "dist": {"shasum": "4a5353392cd47b258615f5d2cb9cca9997ad55cd", "size": 8271, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.12.tgz", "integrity": "sha512-naUJc5nqqUUmV9QK65PogyAaX2qS6sGVrtT5YvZ0pjDCWHEZbHHlhTli6dsLvmPDBwkaxyJxakMF2/eHRfXqdg=="}, "_from": ".", "_npmVersion": "1.3.22", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391894798688, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1391894798688, "_cnpmcore_publish_time": "2021-12-13T15:08:33.013Z"}, "2.2.11": {"name": "ignore", "version": "2.2.11", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.11", "dist": {"shasum": "f7e8b9fa7905a68d5492c3102e31c3bd674b232f", "size": 8279, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.11.tgz", "integrity": "sha512-kk6P52m1hXS/FphC7KtxMo1YZrD6Jg0IhrRf+OsEYVRD8Aozc3EVXuBKN69hbCMZPEbFreHhI4dFIg2qgbgIPA=="}, "_from": ".", "_npmVersion": "1.3.22", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391877135639, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1391877135639, "_cnpmcore_publish_time": "2021-12-13T15:08:34.059Z"}, "2.2.10": {"name": "ignore", "version": "2.2.10", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.10", "dist": {"shasum": "9972e7a3f79dd1c95888f9c83765d85d03ee361a", "size": 8279, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.10.tgz", "integrity": "sha512-xYPoDtdIfCQrfXnEkUSs43zbWBbVumFTWE47a8eP2KvoKcL6R5ml4w6F2ZraQ/Za1TKRfEOITn4hcsoTTlvBDA=="}, "_from": ".", "_npmVersion": "1.3.22", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391876370511, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1391876370511, "_cnpmcore_publish_time": "2021-12-13T15:08:35.390Z"}, "2.2.9": {"name": "ignore", "version": "2.2.9", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "homepage": "https://github.com/kaelzhang/node-ignore", "_id": "ignore@2.2.9", "dist": {"shasum": "e6c2d894a947be9b12672c8e769032100bfcdc52", "size": 7914, "noattachment": false, "tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.9.tgz", "integrity": "sha512-rxdQuCf6Yimf0tvrowTvBTvndWYbIkxwYpIlTbqnF1snz+MTU554zRUUpzM0vfLGvD6vdGmt4DJ1mocA9O9Bdg=="}, "_from": ".", "_npmVersion": "1.3.22", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391856100286, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1391856100286, "_cnpmcore_publish_time": "2021-12-13T15:08:36.641Z"}, "2.2.8": {"name": "ignore", "version": "2.2.8", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "readmeFilename": "README.md", "_id": "ignore@2.2.8", "dist": {"tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.8.tgz", "shasum": "87a5b5832ca9d4b539261d0cc1bcce1835f59ad1", "size": 6608, "noattachment": false, "integrity": "sha512-0jtBO/rTyIw/R21Qa/VjUby6xb/2AMTF+8lt8p415vRIo3Y50spxeQr5YOE8g31IGJoVz4kyPTjlwLiWpoi8/w=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386036138743, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1386036138743, "_cnpmcore_publish_time": "2021-12-13T15:08:37.664Z"}, "2.2.7": {"name": "ignore", "version": "2.2.7", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "readmeFilename": "README.md", "_id": "ignore@2.2.7", "dist": {"tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.7.tgz", "shasum": "81f4245db439f1757d85a79cd06c675ed65aeeb7", "size": 6605, "noattachment": false, "integrity": "sha512-jkWK7gj7Owu2/qAY+KavfoLhGdGhwN/2BQGoY4yvlKeoUjagh0FLFhegG0pV9l4h9JQGIUSCyPoK7cKpPpxlUQ=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381758356946, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1381758356946, "_cnpmcore_publish_time": "2021-12-13T15:08:38.855Z"}, "2.2.1": {"name": "ignore", "version": "2.2.1", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "readmeFilename": "README.md", "_id": "ignore@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/ignore/-/ignore-2.2.1.tgz", "shasum": "b04d8a6d8258a4f59e8efb53a1c8099fcb1eace0", "size": 6493, "noattachment": false, "integrity": "sha512-k9n4EqeqsnvlOqtYfw2ULhn1Km6Nsz5Hr+Gv/BchrIIyQUxQ9AjENt05UqnCeEGyK6IUkSM0auX/TmFNGpzAMA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381138578002, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1381138578002, "_cnpmcore_publish_time": "2021-12-13T15:08:40.037Z"}, "1.1.3": {"name": "ignore", "version": "1.1.3", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "rules", "manager", "filter", "regexp", "regex", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dependencies": {}, "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "readmeFilename": "README.md", "_id": "ignore@1.1.3", "dist": {"tarball": "https://registry.npmmirror.com/ignore/-/ignore-1.1.3.tgz", "shasum": "5108ccf1af942980adb696fad73024f9bd18929d", "size": 5657, "noattachment": false, "integrity": "sha512-oSbKlhDKx7W7+vHoOFmld+aUrejHDjIqDzjZTsLAtIyD2qB49Bn+bZmqmtNnnCvc7OCMR4FthwpJ0u0VEw0tCA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381130393342, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1381130393342, "_cnpmcore_publish_time": "2021-12-13T15:08:41.236Z"}, "0.1.2": {"name": "ignore", "version": "0.1.2", "description": "Ignore is a lightweight readable stream(not implemented until 0.2.x) to apply minimatch ignore rules (like .gitignore). And it is also has util methods to get filtered files and directories.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/kaelzhang/ignore.git"}, "keywords": ["ignore", "ignore-rules", "ignore-files", "readable", "stream", "minimatch"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/ignore/issues"}, "dependencies": {"glob": "~3.2.6", "ignore-rules": "~0.1.0"}, "devDependencies": {"chai": "~1.7.2"}, "readmeFilename": "README.md", "_id": "ignore@0.1.2", "dist": {"tarball": "https://registry.npmmirror.com/ignore/-/ignore-0.1.2.tgz", "shasum": "b6cf8b175ef476ce74140e47fe66e179e8d57c2f", "size": 3893, "noattachment": false, "integrity": "sha512-tH/+LmIRGLbM6JYJD+05N5wMQjhRIXGg72qF457Dep1MURYscX/NvQ/aGh5Y3jlbiwr98l9PvrKND277kB/pqQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380162770111, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1380162770111, "_cnpmcore_publish_time": "2021-12-13T15:08:42.545Z"}, "0.1.0": {"name": "ignore", "version": "0.1.0", "description": "Ignore is a lightweight readable stream(not implemented until 0.2.x) to apply minimatch ignore rules (like .gitignore). And it is also has util methods to get filtered files and directories.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/kaelzhang/ignore.git"}, "keywords": ["ignore", "ignore-rules", "ignore-files", "readable", "stream", "minimatch"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/ignore/issues"}, "dependencies": {"glob": "~3.2.6", "ignore-rules": "~0.1.0"}, "devDependencies": {"chai": "~1.7.2"}, "readmeFilename": "README.md", "_id": "ignore@0.1.0", "dist": {"tarball": "https://registry.npmmirror.com/ignore/-/ignore-0.1.0.tgz", "shasum": "1d4f6970e026bb91570f7dac2ad1d4cfa5166ee4", "size": 3808, "noattachment": false, "integrity": "sha512-cTt2NyCjswWIZAlFpmPKpk/sqb+GuCxiXFzbdfJp47v/AiqY77KY2ZAZD1hVT90B4EX61/kAWDKBBEqfuXs3mg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378107133699, "deprecated": "several bugs fixed in v3.2.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1378107133699, "_cnpmcore_publish_time": "2021-12-13T15:08:43.782Z"}, "5.2.0": {"name": "ignore", "version": "5.2.0", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "types": "./index.d.ts", "gitHead": "5768b70cf856289c9a6488e3ba2053df78b7c2c7", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.2.0", "_nodeVersion": "16.5.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==", "shasum": "6d3bac8fa7fe0d45d9f9be7bac2fc279577e345a", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.2.0.tgz", "fileCount": 6, "unpackedSize": 48870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvqupCRA9TVsSAnZWagAAJ9AP/j859IffZXFncyHhHI3R\nhKZcgSzAagGrMAWucRLalshB1iWTGkWlSUQGcuBaVBoJrvlCSTSEuABi4FN9\nZKsIPxbVrMnKVg6F8n9o4fYsqrJnk8ms4pqUbIwkNJhsz6PSi9HDHOdcnsJQ\nZz5DCDDjdY5dcuXSP9r0tLzrRazWzgKYydXRCggkYkHeQWCkVq6FP+Rvo5te\nsKecptFqJnfj67AGdvOs/vXtEsrw90atyZ4GO/m0GrkPNUcoMibPDHZgva/4\n0e1PGW5uvhmfrxfjOtu21yy+mPmGtIlLcWNXCkYBV4BVlDDSQJohGXjOPD/s\n8CKXnBYZ0jvK3cbYTiX5ilYRHWzaAE0trxMzUNRMiEo4AI1F+cEIYJnLWKap\nEEfs8LhDiOclteIKrlFM9ZrMaJu/GrDytzk+bHWD7WPmZ92wXY9zjSPR5hZt\nW7imyqiXUevtLG4FHiKwrulb1CpIpYACCv/ITUNualIoMVMDK2Sr348TtJ97\nheZjdd4eQ4FsMBIl9nlT3rWFg6Z8EiVF09a5AB5dhZNvalV/yIzgSueK8p/8\n/2ZEb96wOczAwMYeO4fY099Sp/yu2CozYRs4fp1mSeWgCt5QeAKdcdMhD0BU\nTCdpIA0E9yvFx6034FmDpJazqAwz1aht3L3zXrCAwRQX1A+w0rsygRJp81Kd\nUsIu\r\n=O9r1\r\n-----END PGP SIGNATURE-----\r\n", "size": 13830}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.2.0_1639885737730_0.652568393392218"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-19T03:59:02.437Z"}, "5.2.1": {"name": "ignore", "version": "5.2.1", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "types": "./index.d.ts", "gitHead": "124f5a6caef3c0b2589aad61a3344fec60db53f1", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.2.1", "_nodeVersion": "18.1.0", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-d2qQLzTJ9WxQftPAuEQpSPmKqzxePjzVbpAVv62AQ64NTL+wR4JkrVqR/LqFsFEUsHDAiId52mJteHDFuDkElA==", "shasum": "c2b1f76cb999ede1502f3a226a9310fdfe88d46c", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.2.1.tgz", "fileCount": 6, "unpackedSize": 48871, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCu88Y+4lNKL+sn41adsZV5cDCazJn+0ApYTLdPYN9CnQIhALj5OEGds4/6CYPtzaw6dpwQkCemkuadI1ZJym6uWyri"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjg9umACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGTA/9EpSIQunIOEWUybnYhKpBkSxgXlG10/77IykmcqUlJrnHYiL7\r\nRZ6K8EWY7fIaj3REpfGH5E7au3Q+wFsL2C2fZ1Zv0pmgys14W4puc9v3jNnS\r\nFSAjUQxYzvBSUC8mH2szDwWKqLysGvsXd+WGqybdsgTIMu/7uBD3A31Qf8Wj\r\nWDgEkqAsWOvDGpgbvxaaHb9pwzID//d5RTrLvxZjCpVN42UNXg25BcIhOHfi\r\nZBRwXW0kp5kqVllN/H0RagGGGjj+BOYt+uItngDNNAzunQW1HWYUHKJByksr\r\n4JZuMHlmVAT7Mn7XRqsAe0IUBm8HbzNuYx8vn2yp9OKOwJ8ssiwqBeFG060A\r\nFkTsoAVEshC4PsviDBmAn0DIiDIQAo1KA//VhBjzQZBFC40Py/KBv3p+hFMx\r\nDkUBK3hmjfQ4C9h7ZiIkIjKkgjKTZ+g7VF3BD2aXAhc4Uob+7M2G2KPs5ENO\r\nwIAtlM9+O4y2BAFd9M8bNJ/TvJmDewAZ00rMrCqsKc8Oy0eeD4mAV30pyohM\r\nAi8j2ys/k+VG1i+lN+DEo59hQFjF7Px1rukN4GWMWdTWdhtdaRhOOdEO8a2H\r\nhXjRv3xOuevnDBdUDG/FoRWh1iPfjPNj+wpRAdPk02iWG35UX7B5dYhp3s54\r\nSxZ8d3/GLilQl3MHQIFb1WP22bgwV74HWto=\r\n=P9tY\r\n-----END PGP SIGNATURE-----\r\n", "size": 13836}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.2.1_1669585830684_0.6431905066940493"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-27T21:50:41.532Z"}, "5.2.2": {"name": "ignore", "version": "5.2.2", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "types": "./index.d.ts", "gitHead": "1e0235c281ec3b802e70bf50a516403838cc2988", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.2.2", "_nodeVersion": "18.1.0", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-m1MJSy4Z2NAcyhoYpxQeBsc1ZdNQwYjN0wGbLBlnVArdJ90Gtr8IhNSfZZcCoR0fM/0E0BJ0mf1KnLNDOCJP4w==", "shasum": "7e5f30224584b67aeeefe383a24a61dce4cb370d", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.2.2.tgz", "fileCount": 6, "unpackedSize": 49090, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG+M7RGQqAOODGJpwy6uBAv4434xGzEf9sfe7LJlLIo8AiBlenh24A+5iJGKDOsnNcGEcb8oRrIzms8O7we5mkoyxg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoBRnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJLg/+MLpHjg26vRnsNYaL/n+lILt4A4znvvD/J406356ZcWYc2MPK\r\nPmBj8u9MVGKZ8W05F7cqHft4RQt5EAsjV2r34s1ZWj/67/ZxZk0ysPjsKY5p\r\nnLEqDAFHvp7YT0iFBvvks8L15rPeWYUGjvSbylsuQjlIBJGJaQC4Kza72fYn\r\n57MYifjxP/v9412djgrwMY6Ohw9MiIJAGeyqt9iXX2C6TvTPX0th//9QE8lL\r\nu2nFxMiM077t5IqaqT6RK1ilD9xyWO4GV/RTnCGXKsd/yfDSd3iBLdn2NDtY\r\niQurIQrMduNCU5Vbnzw/3yhDbakw/jcJQ54eKj0HAF4tllwWxojsGdODdcO+\r\ndBDoPHEWMfceEYyVu74MvuHdj5iT3HLBoQCm6uUoqdzJKMfVqeFcdVSSviIL\r\nRkQTtyHxrz6LE+b2Jsg6L017v2DLjo7DC9qHfdpxMOZ42oYNcTHf7aALrV5G\r\n9Bw85zERC4tUzRhBwNKzEAT5eAMnxbNLMw6Xmdkiqu3d7V/i5sar4Fepvada\r\nWYQsgldj9ApR2CNn/RBiIcEQ3bFvDF2kNPk9+/CLpmS7SrhHlANMXgcVcE24\r\n1eqbgKBPf2K7CQZmCqDL5Lp9rA9C/jSy/SzWlVy8ZIWSeCcu92yzYpGOzw8/\r\nDWzc4NOLQIuQYdu3Ea7igeimiiVCYzbneqI=\r\n=/qhH\r\n-----END PGP SIGNATURE-----\r\n", "size": 13875}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.2.2_1671435366796_0.11794588759527103"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-19T07:36:14.512Z"}, "5.2.3": {"name": "ignore", "version": "5.2.3", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "posttest": "tap --coverage-report=html && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "codecov": "^3.7.0", "debug": "^4.1.1", "eslint": "^7.0.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^14.10.7", "tmp": "0.2.1", "typescript": "^3.9.3"}, "engines": {"node": ">= 4"}, "types": "./index.d.ts", "gitHead": "9e4e370278041022e77eee6404d17788d06066e5", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.2.3", "_nodeVersion": "18.1.0", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-6BCmRZymdnasx6eA82MnzchMPSxA7ZwIYDLnUER0T9Xhf9XrsceU05+7nt9KPC1yjG3fDA1yk37yPlld3YX7oA==", "shasum": "b0910aec0816d7c9f8c83ba0912a7ac16e125154", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.2.3.tgz", "fileCount": 6, "unpackedSize": 49796, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5jahaLdOrmLjZH8t7Ia1uZYixChhvTi/5Gfk8UlvJSAiAhyBX50H2JIiWCG9WWT75ChaVE0gXajLQCRB6Gw8A3Zg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoHupACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmretQ//YXnn5HEKHFqGaitIijF2aAlCsL6koVuEjgyZMVJhNBw1V48K\r\nGc9LKpkat8rf3aQVqryOKgkz37BGxcfkNAIf3T2+UNNIsEKD6CCYzMyPsh0p\r\nvR0Ll6lQW256Pv4mU35+Vp88Ir2aZuEnlMQbDXw/PCrewSd2O01Z8ZA7AdX/\r\neMCcDOfWKcXMVs9RtWsj3eoA1Ri00xw5pY27TRtGK3wJlgDix/jTFNsG3i1i\r\npYQfm0Umigywyg6/9xIbsfVDXtWN05zd4ScwABABi+jiBK7r7CPMXpEuiqZ2\r\nrE0/8eiDwcy/WUNgP/lE33o+435Tw0HH5GBDA0CX0fbEHUzQ5GTeeVYuKprT\r\nFNSW43oD2hvqDOVpq2NUJ5/GIl7crcDzWYMlr/lEYdzksSccqLYNDId3UZC5\r\nJr25blbe1RLhsKC6q8c8WBNW2ffFbkwEAVJ+jTa+XYGonWo7VOf3PbySWkTP\r\nwW1DdxEX1ROkIHL4/4dXTxFou9EAeb1HrUrpjt3KPom9Gdl7aUIUqwJ9xdtI\r\nzMt5374MMnCnOk4GmbfjBfU0y4/LmrE6d2vRa10QxVbPtE54WShvEBKB6Npo\r\nJDW40jUqgZj9oxBftdXBSnFP0GQy21VKkvENRk97FmDv414okpngt98XYxVa\r\neEGgmUWb7H6k4LO2l8g4DIhPDFjfxufFgKQ=\r\n=8hl6\r\n-----END PGP SIGNATURE-----\r\n", "size": 14023}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.2.3_1671461800846_0.24216994898211164"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-19T14:58:18.946Z"}, "5.2.4": {"name": "ignore", "version": "5.2.4", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.30.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.26.0", "mkdirp": "^1.0.4", "pre-suf": "^1.1.1", "rimraf": "^3.0.2", "spawn-sync": "^2.0.0", "tap": "^16.3.2", "tmp": "0.2.1", "typescript": "^4.9.4"}, "engines": {"node": ">= 4"}, "types": "./index.d.ts", "gitHead": "45a5a9f4e033a8b21b9caa9e27e0e821379ecc60", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_id": "ignore@5.2.4", "_nodeVersion": "18.1.0", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "shasum": "a291c0c6178ff1b960befe47fcdec301674a6324", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz", "fileCount": 6, "unpackedSize": 51223, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9eXqD/21MNnzkMZE+PNmblT30zcRDlVuwXcn94/4tcwIgSLztdyKnkD/36z2Ya5zTjvHSPn+eT249uueyL8PjQ7M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoIrnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTIhAAhstLEnzvu6SBdWIiA/ft4Onxe/A2SzLtqyrfaAqbiZVQjR8N\r\nIt3dO9elHJvri4nmYxq+rSEF4uWQphBvJu9N3Ey4S9ZgGXYmZHUAY6rEKeNI\r\nTqhVbLxuvPK7jl6MjXWJ7KE42yT7fXM4hGrxHBOhNJUu1HpFMqHphBGUPzcm\r\nq1vzFpLgIZImLIA+lrPDb3GrMzoIMvYp7iK8cqlVdRMU4Bx90rkYVJAg1BKb\r\ndX7tgq7G+6iykuchmkHPP6wkVX4SbW5Oy/aufBzN/5NhVuzDKCuS/bqhcucW\r\noK+1tN+AgfePuiFIzXiv1hnfzXI2mHxhdPADov+GTZrJ8bRJrvVeQM+iD9+d\r\nFjI2E5bnrVSVDhPqo9as/X6U8GwYUSZWj9fCeitys7WPAZVvwPfkWqGq0HlL\r\nQxVZd/ocQOOYDxOMBjFXZ6/wFpXJcXQso4P6QZ76z0szHf6Ql2y3iKpbsFbO\r\n4XrESGQsbpONznLkq6B+HZ8DVCMekaQe6mdmwEdJt8ZT4dNVoiArZN/k1CAg\r\nLwjK//zsMygwd6NNxB4NjY3l7kPaSy1+trkaQsojSK5lekwYL+qSZzWb0941\r\nkx/hahHP0IISLj1MYvgPn58x+rT7fWbTFuyBYpYVUKilu3EYr2Yr9hTZgr46\r\nb4IGrYmte5OPL41qWnD6GEwnAawsAbNB+yk=\r\n=qaEQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 14379}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.2.4_1671465703353_0.8778575539863844"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-19T16:26:49.060Z"}, "5.3.0": {"name": "ignore", "version": "5.3.0", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^5.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.1", "typescript": "^5.1.6"}, "engines": {"node": ">= 4"}, "_id": "ignore@5.3.0", "gitHead": "e7f02c836fecdbbf53737f2637d91aba6c808faa", "types": "./index.d.ts", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-g7dmpshy+gD7mh88OC9NwSGTKoc3kyLAZQRU1mt53Aw/vnvfXnbC+F/7F7QoYVKbV+KNvJx8wArewKy1vXMtlg==", "shasum": "67418ae40d34d6999c95ff56016759c718c82f78", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.3.0.tgz", "fileCount": 6, "unpackedSize": 51230, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPg/6Vpns+12WBOmOJZBOnr8rrsxZEf1Cg2DYxjzoY5gIgR99gB2KW1v/AVTaEtidfBWPqli5O/QZ7wpkFxi0MzJE="}], "size": 14373}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.3.0_1700134967261_0.11090994119540865"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-16T11:42:47.471Z", "publish_time": 1700134967471, "_source_registry_name": "default"}, "5.3.1": {"name": "ignore", "version": "5.3.1", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^5.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.1", "typescript": "^5.1.6"}, "engines": {"node": ">= 4"}, "_id": "ignore@5.3.1", "gitHead": "49bd7fa2f1be34ed3e78995d4f6a92fa0b5033d2", "types": "./index.d.ts", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==", "shasum": "5073e554cd42c5b33b394375f538b8593e34d4ef", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.3.1.tgz", "fileCount": 6, "unpackedSize": 51455, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNWFIDxMP736k7iT38Rs9oysecolj9dKu6uyrWovYnrAiEA96gTJoFchGJL/XYauufH0rB5lKGS+e1ivt2Uwya5/rs="}], "size": 14437}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.3.1_1706754400894_0.44844114385019274"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-01T02:26:41.175Z", "publish_time": 1706754401175, "_source_registry_name": "default"}, "5.3.2": {"name": "ignore", "version": "5.3.2", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "typescript": "^5.1.6"}, "engines": {"node": ">= 4"}, "_id": "ignore@5.3.2", "gitHead": "cfd0fce1258fe87950d8ce3d18b345bf7d4ed780", "types": "./index.d.ts", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "shasum": "3cd40e729f3643fd87cb04e50bf0eb722bc596f5", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-5.3.2.tgz", "fileCount": 6, "unpackedSize": 53630, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPELocnAYOzbeD3jL5ncfyVJlmuYK8abQDwvVcwzPplAiAwInO5nUYUQ9IEYbQ0OGHigYYpYXL2ECaTsl/mzGMpuA=="}], "size": 15266}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_5.3.2_1723452659889_0.7906479894948728"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-12T08:51:00.065Z", "publish_time": 1723452660065, "_source_registry_name": "default"}, "6.0.0": {"name": "ignore", "version": "6.0.0", "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "_id": "ignore@6.0.0", "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "homepage": "https://github.com/kaelzhang/node-ignore#readme", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "dist": {"shasum": "2ebe982d9acc114fd5ca60598911cfb5d25e2d6f", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-mLmIlWsE32X6sO4Cd48j/uLEhg9Cm8lH4i6xJEsqaDKaBWyZdY4YVEGOEQONuPR3At+42Bn+30OieW6eM+86iA==", "signatures": [{"sig": "MEUCIQCiq8c6U9jIpmoHsx0A8zwPSj1m4J2n5YOK7J1vBGP7swIgRzJP7K55kFk0wL+tDpoyASr3Wd9nHh1lGhFpb5Bnsuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70411, "size": 20405}, "main": "index.js", "types": "index.d.ts", "module": "index.mjs", "engines": {"node": ">= 4"}, "gitHead": "7047400f146f68c9072cc150d7497b55d1916879", "scripts": {"tap": "tap --reporter classic", "test": "npm run test:only", "build": "babel -o legacy.js index.js && node ./scripts/build.js", "report": "tap --coverage-report=html", "test:ts": "node ./test/ts/simple.js", "posttest": "npm run report && codecov", "test:git": "npm run tap test/git-check-ignore.js", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:lint": "eslint .", "test:only": "npm run test:lint && npm run build && npm run test:tsc && npm run test:tsc:16 && npm run test:ts && npm run test:cases", "test:cases": "npm run tap test/*.js -- --coverage", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "test:ignore": "npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:tsc:16": "tsc ./test/ts/simple.ts --lib ES6 --moduleResolution Node16 --module Node16", "prepublishOnly": "npm run build", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.js", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage"}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/kaelzhang/node-ignore.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "directories": {}, "_nodeVersion": "21.6.2", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "_npmOperationalInternal": {"tmp": "tmp/ignore_6.0.0_1726574138651_0.9979153500544462", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-09-17T11:55:38.818Z", "publish_time": 1726574138818, "_source_registry_name": "default", "deprecated": "this package has been deprecated due to issues"}, "6.0.1": {"name": "ignore", "version": "6.0.1", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js && node ./scripts/build.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:tsc:16": "tsc ./test/ts/simple.ts --lib ES6 --moduleResolution Node16 --module Node16", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run build && npm run test:tsc && npm run test:tsc:16 && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@6.0.1", "gitHead": "249df63b13483747bf1381b5ed9f899e3c30bb06", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-9hCx6FGveEYzwsldacntlq0RdPsTjOAALVL4nqi1O8JU6OIzzchHELMNE9f+6ZMtuHG1vd+owvczaMhu6EM2Xw==", "shasum": "18cac00bcbaa3d892bc9ba146b2ee5e488f87f61", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-6.0.1.tgz", "fileCount": 7, "unpackedSize": 70442, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDtVBFKrpeY4Y8kYGyo3o3ld2SseyQz9u0riyyQ5IA7IAiAyMgo0IEwjvfzkV8/9XdSPEmLIX20cEuKhunZ1uQpY5w=="}], "size": 20415}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_6.0.1_1726574344710_0.9773323743686901"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-17T11:59:04.980Z", "publish_time": 1726574344980, "_source_registry_name": "default", "deprecated": "this package has been deprecated due to issues"}, "6.0.2": {"name": "ignore", "version": "6.0.2", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.js", "test:others": "npm run tap test/others.js", "test:cases": "npm run tap test/*.js -- --coverage", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "typescript": "^5.1.6"}, "engines": {"node": ">= 4"}, "_id": "ignore@6.0.2", "gitHead": "ee70c66d32e900f6e793c733a4ca27a1dafb3819", "types": "./index.d.ts", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-InwqeHHN2XpumIkMvpl/DCJVrAHgCsG5+cn1XlnLWGwtZBm8QJfSusItfrwx81CTp5agNZqpKU2J/ccC5nGT4A==", "shasum": "77cccb72a55796af1b6d2f9eb14fa326d24f4283", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-6.0.2.tgz", "fileCount": 6, "unpackedSize": 53630, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUhd4zGOf3Qm/Ba7pIjQqRkTTRjT2CQfKTlnOGg9+kpQIgeQ0ee+wt2V69AhYRc2zZfrOv4azPyhU+zkrN04wlQS0="}], "size": 15265}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ignore_6.0.2_1726580133759_0.6457717894906843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-17T13:35:33.937Z", "publish_time": 1726580133937, "_source_registry_name": "default"}, "7.0.0": {"name": "ignore", "version": "7.0.0", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js && node ./scripts/build.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:tsc:16": "tsc ./test/ts/simple.ts --lib ES6 --moduleResolution Node16 --module Node16", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.test.js", "test:ignore": "npm run tap test/ignore.test.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.test.js", "test:others": "npm run tap test/others.test.js", "test:cases": "npm run tap test/*.test.js -- --coverage", "test:no-coverage": "npm run tap test/*.test.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run build && npm run test:tsc && npm run test:tsc:16 && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "// posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.3", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@7.0.0", "gitHead": "872d495954bf97e5eb426984d46a22062e6f2e2f", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lcX8PNQygAa22u/0BysEY8VhaFRzlOkvdlKczDPnJvrkJD1EuqzEky5VYYKM2iySIuaVIDv9N190DfSreSLw2A==", "shasum": "52da780b009bd0845d1f9dd4d8ae6a7569ae73c4", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-7.0.0.tgz", "fileCount": 7, "unpackedSize": 81505, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB3rLrwTeI75RfssTIbnula1AlYOBIwh19kMCZ3IcG/lAiEAvYhofscdcMMo63cpcUJt8ql4AyXUEG+fkEFqKVuiK6g="}], "size": 22845}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ignore_7.0.0_1734954650506_0.155141357103046"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-23T11:50:50.714Z", "publish_time": 1734954650714, "_source_registry_name": "default"}, "7.0.1": {"name": "ignore", "version": "7.0.1", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "==================== linting ======================": "", "lint": "eslint .", "=================== typescript ====================": "", "ts": "npm run test:ts && npm run test:16", "test:ts": "ts-node ./test/ts/simple.ts", "test:16": "npm run test:ts:16 && npm run test:cjs:16 && npm run test:mjs:16", "test:ts:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/ts/simple.ts", "test:cjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/ts/simple.cjs", "test:mjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/ts/simple.mjs", "===================== cases =======================": "", "test:cases": "npm run tap test/*.test.js -- --coverage", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.test.js", "test:ignore": "npm run tap test/ignore.test.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.test.js", "test:others": "npm run tap test/others.test.js", "test:no-coverage": "npm run tap test/*.test.js -- --no-check-coverage", "test": "npm run lint && npm run ts && npm run build && npm run test:cases", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@typescript-eslint/eslint-plugin": "^8.19.1", "codecov": "^3.8.3", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@7.0.1", "gitHead": "cd9f6405c56dae9382a5c31e209411a66df30583", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-D1gVletsbVOoiXF963rgZnfobGAbq7Lb+dz3fcBmlOmZg6hHkpbycLqL8PLNB8f4GVv6dOVYwhPL/r7hwiH0Fw==", "shasum": "e0aca8bd28ed81f45bfbc042c57f24eb682aa2b9", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-7.0.1.tgz", "fileCount": 6, "unpackedSize": 62461, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlBitKLhfuqkazrrSCsbVu3gmZ2LjOehOlqIOIRSFHbAIgaHCz+Fiv5uBDSzZoWxhiHQsB5Q2hA1ne9qxMc/x7T34="}], "size": 17064}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ignore_7.0.1_1736774047097_0.9385385056951592"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-13T13:14:07.290Z", "publish_time": 1736774047290, "_source_registry_name": "default"}, "7.0.2": {"name": "ignore", "version": "7.0.2", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js && node ./scripts/build.js", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:tsc:16": "tsc ./test/ts/simple.ts --lib ES6 --moduleResolution Node16 --module Node16", "test:ts": "node ./test/ts/simple.js", "tap": "tap --reporter classic", "test:git": "npm run tap test/git-check-ignore.test.js", "test:ignore": "npm run tap test/ignore.test.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.test.js", "test:others": "npm run tap test/others.test.js", "test:cases": "npm run tap test/*.test.js -- --coverage", "test:no-coverage": "npm run tap test/*.test.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run build && npm run test:tsc && npm run test:tsc:16 && npm run test:ts && npm run test:cases", "test": "npm run test:only", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html", "// posttest": "npm run report && codecov"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.3", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@7.0.2", "gitHead": "52f8e855b6aca711579c008f38829f48e5a4c78b", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Wx5VKTZatJNNa26J1dMfJF1bZu4Lw31EHwhFRcSjTvro8Mqsrd3rJanyW48W43Eyd+gpaiDNkveYd62DvXaZeQ==", "shasum": "f3159fb41f147eab6bbc1242a5469ba5d9af6f65", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-7.0.2.tgz", "fileCount": 7, "unpackedSize": 81505, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWeENFurlTZB7N/JLsWfAXhVGtJgw4QG2rcLb7XLdVVgIgT641E2xYIZZ0lAYIX8rAjemxVRn/W2gidKjhloR15n4="}], "size": 22846}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ignore_7.0.2_1736820820947_0.7632781711956833"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-14T02:13:41.127Z", "publish_time": 1736820821127, "_source_registry_name": "default"}, "7.0.3": {"name": "ignore", "version": "7.0.3", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "==================== linting ======================": "", "lint": "eslint .", "===================== import ======================": "", "ts": "npm run test:ts && npm run test:16", "test:ts": "ts-node ./test/import/simple.ts", "test:16": "npm run test:ts:16 && npm run test:cjs:16 && npm run test:mjs:16", "test:ts:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.ts && tsc ./test/import/simple.ts --lib ES6 --moduleResolution Node16 --module Node16 && node ./test/import/simple.js", "test:cjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.cjs", "test:mjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.mjs && babel -o ./test/import/simple-mjs.js ./test/import/simple.mjs && node ./test/import/simple-mjs.js", "===================== cases =======================": "", "test:cases": "npm run tap test/*.test.js -- --coverage", "tap": "tap --reporter classic", "===================== debug =======================": "", "test:git": "npm run tap test/git-check-ignore.test.js", "test:ignore": "npm run tap test/ignore.test.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.test.js", "test:others": "npm run tap test/others.test.js", "test:no-coverage": "npm run tap test/*.test.js -- --no-check-coverage", "test": "npm run lint && npm run ts && npm run build && npm run test:cases", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@typescript-eslint/eslint-plugin": "^8.19.1", "codecov": "^3.8.3", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@7.0.3", "gitHead": "f0bd5340a18a45433f231523c1604a36e18a5931", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bAH5jbK/F3T3Jls4I0SO1hmPR0dKU0a7+SY6n1yzRtG54FLO8d6w/nxLFX2Nb7dBu6cCWXPaAME6cYqFUMmuCA==", "shasum": "397ef9315dfe0595671eefe8b633fec6943ab733", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-7.0.3.tgz", "fileCount": 6, "unpackedSize": 63148, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCf+xD/td3ut8+nqNebo96773yMc3NKNz0lJMNOvbu28wIgSeFamXXPPFbewYRHDOvMsfAniVHvCFLQmBAUBdHfqRQ="}], "size": 17205}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ignore_7.0.3_1736826761173_0.4683158000951846"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-14T03:52:41.363Z", "publish_time": 1736826761363, "_source_registry_name": "default"}, "7.0.4": {"name": "ignore", "version": "7.0.4", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "==================== linting ======================": "", "lint": "eslint .", "===================== import ======================": "", "ts": "npm run test:ts && npm run test:16", "test:ts": "ts-node ./test/import/simple.ts", "test:16": "npm run test:ts:16 && npm run test:cjs:16 && npm run test:mjs:16", "test:ts:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.ts && tsc ./test/import/simple.ts --lib ES6 --moduleResolution Node16 --module Node16 && node ./test/import/simple.js", "test:cjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.cjs", "test:mjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.mjs && babel -o ./test/import/simple-mjs.js ./test/import/simple.mjs && node ./test/import/simple-mjs.js", "===================== cases =======================": "", "test:cases": "npm run tap test/*.test.js -- --coverage", "tap": "tap --reporter classic", "===================== debug =======================": "", "test:git": "npm run tap test/git-check-ignore.test.js", "test:ignore": "npm run tap test/ignore.test.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.test.js", "test:others": "npm run tap test/others.test.js", "test:no-coverage": "npm run tap test/*.test.js -- --no-check-coverage", "test": "npm run lint && npm run ts && npm run build && npm run test:cases", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@typescript-eslint/eslint-plugin": "^8.19.1", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@7.0.4", "gitHead": "93669c063b2d9fa21bd293390ef6543dc5ee3009", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-gJzzk+PQNznz8ysRrC0aOkBNVRBDtE1n53IqyqEf3PXrYwomFs5q4pGMizBMJF+ykh03insJ27hB8gSrD2Hn8A==", "shasum": "a12c70d0f2607c5bf508fb65a40c75f037d7a078", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-7.0.4.tgz", "fileCount": 6, "unpackedSize": 63139, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCFSvmkDSxak9lDs6IuOnsp/8onheQTqehW19T3aY4GogIhAIyRmNyqUYhvDAjRdQ+BOROEXWkuDkV/K3hn3U2Y81Xd"}], "size": 17211}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ignore_7.0.4_1745548152012_0.251472363208199"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-25T02:29:12.203Z", "publish_time": 1745548152203, "_source_registry_name": "default"}, "7.0.5": {"name": "ignore", "version": "7.0.5", "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "types": "index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "babel -o legacy.js index.js", "==================== linting ======================": "", "lint": "eslint .", "===================== import ======================": "", "ts": "npm run test:ts && npm run test:16", "test:ts": "ts-node ./test/import/simple.ts", "test:16": "npm run test:ts:16 && npm run test:cjs:16 && npm run test:mjs:16", "test:ts:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.ts && tsc ./test/import/simple.ts --lib ES6 --moduleResolution Node16 --module Node16 && node ./test/import/simple.js", "test:cjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.cjs", "test:mjs:16": "ts-node --compilerOptions '{\"moduleResolution\": \"Node16\", \"module\": \"Node16\"}' ./test/import/simple.mjs && babel -o ./test/import/simple-mjs.js ./test/import/simple.mjs && node ./test/import/simple-mjs.js", "===================== cases =======================": "", "test:cases": "npm run tap test/*.test.js -- --coverage", "tap": "tap --reporter classic", "===================== debug =======================": "", "test:git": "npm run tap test/git-check-ignore.test.js", "test:ignore": "npm run tap test/ignore.test.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.test.js", "test:others": "npm run tap test/others.test.js", "test:no-coverage": "npm run tap test/*.test.js -- --no-check-coverage", "test": "npm run lint && npm run ts && npm run build && npm run test:cases", "test:win32": "IGNORE_TEST_WIN32=1 npm run test", "report": "tap --coverage-report=html"}, "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": {"name": "kael"}, "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@typescript-eslint/eslint-plugin": "^8.19.1", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "engines": {"node": ">= 4"}, "_id": "ignore@7.0.5", "gitHead": "84d052ddfe7c326b01b306154e06709d6e7e2ed8", "homepage": "https://github.com/kaelzhang/node-ignore#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==", "shasum": "4cb5f6cd7d4c7ab0365738c7aea888baa6d7efd9", "tarball": "https://registry.npmmirror.com/ignore/-/ignore-7.0.5.tgz", "fileCount": 6, "unpackedSize": 63384, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBoHL3bWyea3HRir1gzyncpKjyex9VAexS9Rf+3rnTMcAiBcF+woLEcCw2eZPSHfz+R1PJVC0EJr+5c+iqXNmmJImg=="}], "size": 17273}, "_npmUser": {"name": "kael", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kael", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ignore_7.0.5_1748657933211_0.05106178386500648"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-31T02:18:53.410Z", "publish_time": 1748657933410, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "homepage": "https://github.com/kaelzhang/node-ignore#readme", "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "_source_registry_name": "default"}