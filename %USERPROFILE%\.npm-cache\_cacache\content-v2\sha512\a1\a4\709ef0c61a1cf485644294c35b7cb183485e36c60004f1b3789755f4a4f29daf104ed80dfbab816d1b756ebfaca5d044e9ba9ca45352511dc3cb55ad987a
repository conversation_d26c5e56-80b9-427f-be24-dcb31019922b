{"_attachments": {}, "_id": "form-data", "_rev": "1730-61f1476dfbcaa28a7594e3b3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "dist-tags": {"latest": "4.0.3", "v2-backport": "2.5.3", "v3-backport": "3.0.3"}, "license": "MIT", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "name": "form-data", "readme": "# Form-Data [![NPM Module](https://img.shields.io/npm/v/form-data.svg)](https://www.npmjs.com/package/form-data) [![Join the chat at https://gitter.im/form-data/form-data](http://form-data.github.io/images/gitterbadge.svg)](https://gitter.im/form-data/form-data)\n\nA library to create readable ```\"multipart/form-data\"``` streams. Can be used to submit forms and file uploads to other web applications.\n\nThe API of this library is inspired by the [XMLHttpRequest-2 FormData Interface][xhr2-fd].\n\n[xhr2-fd]: http://dev.w3.org/2006/webapi/XMLHttpRequest-2/Overview.html#the-formdata-interface\n\n[![Linux Build](https://img.shields.io/travis/form-data/form-data/v4.0.3.svg?label=linux:6.x-12.x)](https://travis-ci.org/form-data/form-data)\n[![MacOS Build](https://img.shields.io/travis/form-data/form-data/v4.0.3.svg?label=macos:6.x-12.x)](https://travis-ci.org/form-data/form-data)\n[![Windows Build](https://img.shields.io/travis/form-data/form-data/v4.0.3.svg?label=windows:6.x-12.x)](https://travis-ci.org/form-data/form-data)\n\n[![Coverage Status](https://img.shields.io/coveralls/form-data/form-data/v4.0.3.svg?label=code+coverage)](https://coveralls.io/github/form-data/form-data?branch=master)\n[![Dependency Status](https://img.shields.io/david/form-data/form-data.svg)](https://david-dm.org/form-data/form-data)\n\n## Install\n\n```\nnpm install --save form-data\n```\n\n## Usage\n\nIn this example we are constructing a form with 3 fields that contain a string,\na buffer and a file stream.\n\n``` javascript\nvar FormData = require('form-data');\nvar fs = require('fs');\n\nvar form = new FormData();\nform.append('my_field', 'my value');\nform.append('my_buffer', new Buffer(10));\nform.append('my_file', fs.createReadStream('/foo/bar.jpg'));\n```\n\nAlso you can use http-response stream:\n\n``` javascript\nvar FormData = require('form-data');\nvar http = require('http');\n\nvar form = new FormData();\n\nhttp.request('http://nodejs.org/images/logo.png', function (response) {\n  form.append('my_field', 'my value');\n  form.append('my_buffer', new Buffer(10));\n  form.append('my_logo', response);\n});\n```\n\nOr @mikeal's [request](https://github.com/request/request) stream:\n\n``` javascript\nvar FormData = require('form-data');\nvar request = require('request');\n\nvar form = new FormData();\n\nform.append('my_field', 'my value');\nform.append('my_buffer', new Buffer(10));\nform.append('my_logo', request('http://nodejs.org/images/logo.png'));\n```\n\nIn order to submit this form to a web application, call ```submit(url, [callback])``` method:\n\n``` javascript\nform.submit('http://example.org/', function (err, res) {\n  // res – response object (http.IncomingMessage)  //\n  res.resume();\n});\n\n```\n\nFor more advanced request manipulations ```submit()``` method returns ```http.ClientRequest``` object, or you can choose from one of the alternative submission methods.\n\n### Custom options\n\nYou can provide custom options, such as `maxDataSize`:\n\n``` javascript\nvar FormData = require('form-data');\n\nvar form = new FormData({ maxDataSize: 20971520 });\nform.append('my_field', 'my value');\nform.append('my_buffer', /* something big */);\n```\n\nList of available options could be found in [combined-stream](https://github.com/felixge/node-combined-stream/blob/master/lib/combined_stream.js#L7-L15)\n\n### Alternative submission methods\n\nYou can use node's http client interface:\n\n``` javascript\nvar http = require('http');\n\nvar request = http.request({\n  method: 'post',\n  host: 'example.org',\n  path: '/upload',\n  headers: form.getHeaders()\n});\n\nform.pipe(request);\n\nrequest.on('response', function (res) {\n  console.log(res.statusCode);\n});\n```\n\nOr if you would prefer the `'Content-Length'` header to be set for you:\n\n``` javascript\nform.submit('example.org/upload', function (err, res) {\n  console.log(res.statusCode);\n});\n```\n\nTo use custom headers and pre-known length in parts:\n\n``` javascript\nvar CRLF = '\\r\\n';\nvar form = new FormData();\n\nvar options = {\n  header: CRLF + '--' + form.getBoundary() + CRLF + 'X-Custom-Header: 123' + CRLF + CRLF,\n  knownLength: 1\n};\n\nform.append('my_buffer', buffer, options);\n\nform.submit('http://example.com/', function (err, res) {\n  if (err) throw err;\n  console.log('Done');\n});\n```\n\nForm-Data can recognize and fetch all the required information from common types of streams (```fs.readStream```, ```http.response``` and ```mikeal's request```), for some other types of streams you'd need to provide \"file\"-related information manually:\n\n``` javascript\nsomeModule.stream(function (err, stdout, stderr) {\n  if (err) throw err;\n\n  var form = new FormData();\n\n  form.append('file', stdout, {\n    filename: 'unicycle.jpg', // ... or:\n    filepath: 'photos/toys/unicycle.jpg',\n    contentType: 'image/jpeg',\n    knownLength: 19806\n  });\n\n  form.submit('http://example.com/', function (err, res) {\n    if (err) throw err;\n    console.log('Done');\n  });\n});\n```\n\nThe `filepath` property overrides `filename` and may contain a relative path. This is typically used when uploading [multiple files from a directory](https://wicg.github.io/entries-api/#dom-htmlinputelement-webkitdirectory).\n\nFor edge cases, like POST request to URL with query string or to pass HTTP auth credentials, object can be passed to `form.submit()` as first parameter:\n\n``` javascript\nform.submit({\n  host: 'example.com',\n  path: '/probably.php?extra=params',\n  auth: 'username:password'\n}, function (err, res) {\n  console.log(res.statusCode);\n});\n```\n\nIn case you need to also send custom HTTP headers with the POST request, you can use the `headers` key in first parameter of `form.submit()`:\n\n``` javascript\nform.submit({\n  host: 'example.com',\n  path: '/surelynot.php',\n  headers: { 'x-test-header': 'test-header-value' }\n}, function (err, res) {\n  console.log(res.statusCode);\n});\n```\n\n### Methods\n\n- [_Void_ append( **String** _field_, **Mixed** _value_ [, **Mixed** _options_] )](https://github.com/form-data/form-data#void-append-string-field-mixed-value--mixed-options-).\n- [_Headers_ getHeaders( [**Headers** _userHeaders_] )](https://github.com/form-data/form-data#array-getheaders-array-userheaders-)\n- [_String_ getBoundary()](https://github.com/form-data/form-data#string-getboundary)\n- [_Void_ setBoundary()](https://github.com/form-data/form-data#void-setboundary)\n- [_Buffer_ getBuffer()](https://github.com/form-data/form-data#buffer-getbuffer)\n- [_Integer_ getLengthSync()](https://github.com/form-data/form-data#integer-getlengthsync)\n- [_Integer_ getLength( **function** _callback_ )](https://github.com/form-data/form-data#integer-getlength-function-callback-)\n- [_Boolean_ hasKnownLength()](https://github.com/form-data/form-data#boolean-hasknownlength)\n- [_Request_ submit( _params_, **function** _callback_ )](https://github.com/form-data/form-data#request-submit-params-function-callback-)\n- [_String_ toString()](https://github.com/form-data/form-data#string-tostring)\n\n#### _Void_ append( **String** _field_, **Mixed** _value_ [, **Mixed** _options_] )\nAppend data to the form. You can submit about any format (string, integer, boolean, buffer, etc.). However, Arrays are not supported and need to be turned into strings by the user.\n```javascript\nvar form = new FormData();\nform.append('my_string', 'my value');\nform.append('my_integer', 1);\nform.append('my_boolean', true);\nform.append('my_buffer', new Buffer(10));\nform.append('my_array_as_json', JSON.stringify(['bird', 'cute']));\n```\n\nYou may provide a string for options, or an object.\n```javascript\n// Set filename by providing a string for options\nform.append('my_file', fs.createReadStream('/foo/bar.jpg'), 'bar.jpg');\n\n// provide an object.\nform.append('my_file', fs.createReadStream('/foo/bar.jpg'), { filename: 'bar.jpg', contentType: 'image/jpeg', knownLength: 19806 });\n```\n\n#### _Headers_ getHeaders( [**Headers** _userHeaders_] )\nThis method adds the correct `content-type` header to the provided array of `userHeaders`.\n\n#### _String_ getBoundary()\nReturn the boundary of the formData. By default, the boundary consists of 26 `-` followed by 24 numbers\nfor example:\n```javascript\n--------------------------515890814546601021194782\n```\n\n#### _Void_ setBoundary(String _boundary_)\nSet the boundary string, overriding the default behavior described above.\n\n_Note: The boundary must be unique and may not appear in the data._\n\n#### _Buffer_ getBuffer()\nReturn the full formdata request package, as a Buffer. You can insert this Buffer in e.g. Axios to send multipart data.\n```javascript\nvar form = new FormData();\nform.append('my_buffer', Buffer.from([0x4a,0x42,0x20,0x52,0x6f,0x63,0x6b,0x73]));\nform.append('my_file', fs.readFileSync('/foo/bar.jpg'));\n\naxios.post('https://example.com/path/to/api', form.getBuffer(), form.getHeaders());\n```\n**Note:** Because the output is of type Buffer, you can only append types that are accepted by Buffer: *string, Buffer, ArrayBuffer, Array, or Array-like Object*. A ReadStream for example will result in an error.\n\n#### _Integer_ getLengthSync()\nSame as `getLength` but synchronous.\n\n_Note: getLengthSync __doesn't__ calculate streams length._\n\n#### _Integer_ getLength(**function** _callback_ )\nReturns the `Content-Length` async. The callback is used to handle errors and continue once the length has been calculated\n```javascript\nthis.getLength(function (err, length) {\n  if (err) {\n    this._error(err);\n    return;\n  }\n\n  // add content length\n  request.setHeader('Content-Length', length);\n\n  ...\n}.bind(this));\n```\n\n#### _Boolean_ hasKnownLength()\nChecks if the length of added values is known.\n\n#### _Request_ submit(_params_, **function** _callback_ )\nSubmit the form to a web application.\n```javascript\nvar form = new FormData();\nform.append('my_string', 'Hello World');\n\nform.submit('http://example.com/', function (err, res) {\n  // res – response object (http.IncomingMessage)  //\n  res.resume();\n} );\n```\n\n#### _String_ toString()\nReturns the form data as a string. Don't use this if you are sending files or buffers, use `getBuffer()` instead.\n\n### Integration with other libraries\n\n#### Request\n\nForm submission using  [request](https://github.com/request/request):\n\n```javascript\nvar formData = {\n  my_field: 'my_value',\n  my_file: fs.createReadStream(__dirname + '/unicycle.jpg'),\n};\n\nrequest.post({url:'http://service.com/upload', formData: formData}, function (err, httpResponse, body) {\n  if (err) {\n    return console.error('upload failed:', err);\n  }\n  console.log('Upload successful!  Server responded with:', body);\n});\n```\n\nFor more details see [request readme](https://github.com/request/request#multipartform-data-multipart-form-uploads).\n\n#### node-fetch\n\nYou can also submit a form using [node-fetch](https://github.com/bitinn/node-fetch):\n\n```javascript\nvar form = new FormData();\n\nform.append('a', 1);\n\nfetch('http://example.com', { method: 'POST', body: form })\n    .then(function (res) {\n        return res.json();\n    }).then(function (json) {\n        console.log(json);\n    });\n```\n\n#### axios\n\nIn Node.js you can post a file using [axios](https://github.com/axios/axios):\n```javascript\nconst form = new FormData();\nconst stream = fs.createReadStream(PATH_TO_FILE);\n\nform.append('image', stream);\n\n// In Node.js environment you need to set boundary in the header field 'Content-Type' by calling method `getHeaders`\nconst formHeaders = form.getHeaders();\n\naxios.post('http://example.com', form, {\n  headers: {\n    ...formHeaders,\n  },\n})\n  .then(response => response)\n  .catch(error => error)\n```\n\n## Notes\n\n- ```getLengthSync()``` method DOESN'T calculate length for streams, use ```knownLength``` options as workaround.\n- ```getLength(cb)``` will send an error as first parameter of callback if stream length cannot be calculated (e.g. send in custom streams w/o using ```knownLength```).\n- ```submit``` will not add `content-length` if form length is unknown or not calculable.\n- Starting version `2.x` FormData has dropped support for `node@0.10.x`.\n- Starting version `3.x` FormData has dropped support for `node@4.x`.\n\n## License\n\nForm-Data is released under the [MIT](License) license.\n", "time": {"created": "2022-01-26T13:06:53.231Z", "modified": "2025-07-08T00:19:19.562Z", "4.0.0": "2021-02-15T17:38:54.740Z", "3.0.1": "2021-02-15T17:33:58.367Z", "3.0.0": "2019-11-06T07:56:58.467Z", "2.5.1": "2019-08-29T06:48:54.586Z", "2.5.0": "2019-07-04T04:44:27.090Z", "2.4.0": "2019-06-19T07:27:06.596Z", "2.3.3": "2018-10-17T07:26:22.767Z", "2.3.2": "2018-02-14T00:31:49.698Z", "2.3.2-rc1": "2018-02-12T17:58:22.688Z", "2.3.1": "2017-08-24T23:00:54.577Z", "2.2.0": "2017-06-11T08:46:00.717Z", "2.1.4": "2017-04-09T15:09:59.126Z", "2.1.2": "2016-11-08T04:02:19.622Z", "2.1.1": "2016-10-04T06:33:17.917Z", "2.1.0": "2016-09-25T22:14:22.111Z", "2.0.0": "2016-09-17T06:10:17.649Z", "1.0.1": "2016-08-26T09:44:37.316Z", "1.0.0": "2016-08-26T09:01:06.786Z", "1.0.0-rc4": "2016-03-15T16:35:47.585Z", "1.0.0-rc3": "2015-07-30T04:10:06.749Z", "1.0.0-rc2": "2015-07-22T02:48:28.688Z", "1.0.0-rc1": "2015-06-13T15:50:04.021Z", "0.2.0": "2014-12-06T21:14:10.402Z", "0.1.4": "2014-06-24T06:35:44.934Z", "0.1.3": "2014-06-02T07:48:24.579Z", "0.1.2": "2013-10-03T04:06:55.129Z", "0.1.1": "2013-08-22T04:22:58.445Z", "0.1.0": "2013-07-08T18:46:13.834Z", "0.0.10": "2013-05-09T05:35:21.282Z", "0.0.9": "2013-04-29T17:09:12.258Z", "0.0.8": "2013-04-21T23:32:42.861Z", "0.0.7": "2013-02-10T02:53:05.643Z", "0.0.6": "2013-01-08T08:04:51.296Z", "0.0.5": "2012-12-05T07:06:20.011Z", "0.0.4": "2012-09-06T16:47:43.385Z", "0.0.3": "2012-08-10T03:05:05.412Z", "0.0.2": "2012-07-02T21:59:37.447Z", "0.0.0": "2011-05-16T14:58:22.532Z", "2.5.2": "2024-10-10T03:46:33.800Z", "3.0.2": "2024-10-10T03:48:50.494Z", "4.0.1": "2024-10-10T03:49:46.006Z", "2.5.3": "2025-02-14T21:56:16.969Z", "3.0.3": "2025-02-14T23:00:12.690Z", "4.0.2": "2025-02-14T23:23:57.699Z", "4.0.3": "2025-06-05T14:51:11.479Z"}, "versions": {"4.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "4.0.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "puppeteer": "^1.19.0", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "license": "MIT", "gitHead": "53adbd81e9bde27007b28083068f2fc8272614dc", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@4.0.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "dist": {"shasum": "93919daeaf361ee529584b9b31664dc12c9fa452", "size": 10490, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="}, "directories": {}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_4.0.0_1613410734571_0.08216993276960127"}, "_hasShrinkwrap": false, "publish_time": 1613410734740, "_cnpm_publish_time": 1613410734740, "_cnpmcore_publish_time": "2021-12-13T13:15:05.039Z"}, "3.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "3.0.1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "puppeteer": "^1.19.0", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "license": "MIT", "gitHead": "c72ad6728ae394e55c0f7a025864429edd656879", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@3.0.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "dist": {"shasum": "ebd53791b78356a99af9a300d4282c4d5eb9755f", "size": 10369, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-3.0.1.tgz", "integrity": "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg=="}, "directories": {}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_3.0.1_1613410438253_0.6573676413560436"}, "_hasShrinkwrap": false, "publish_time": 1613410438367, "_cnpm_publish_time": 1613410438367, "_cnpmcore_publish_time": "2021-12-13T13:15:05.323Z"}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "puppeteer": "^1.19.0", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "license": "MIT", "gitHead": "5c9b3a94aad8482ad8126cd7405a3060cd8e022e", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@3.0.0", "_nodeVersion": "12.8.0", "_npmVersion": "6.10.2", "dist": {"shasum": "31b7e39c85f1355b7139ee0c647cf0de7f83c682", "size": 10298, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-3.0.0.tgz", "integrity": "sha512-CKMFDglpbMi6PyN+brwB9Q/GOw0eAnsrEZDgcsH5Krhz5Od/haKHAX0NmQfha2zPPz0JpWzA7GJHGSnvCRLWsg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_3.0.0_1573027018258_0.8075773865628744"}, "_hasShrinkwrap": false, "publish_time": 1573027018467, "_cnpm_publish_time": 1573027018467, "_cnpmcore_publish_time": "2021-12-13T13:15:05.704Z"}, "2.5.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.5.1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "license": "MIT", "gitHead": "8ce81f56cccf5466363a5eff135ad394a929f59b", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.5.1", "_nodeVersion": "12.8.0", "_npmVersion": "6.10.2", "dist": {"shasum": "f2cbec57b5e59e23716e128fe44d4e5dd23895f4", "size": 10102, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.5.1.tgz", "integrity": "sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_2.5.1_1567061334343_0.40267661488012263"}, "_hasShrinkwrap": false, "publish_time": 1567061334586, "_cnpm_publish_time": 1567061334586, "_cnpmcore_publish_time": "2021-12-13T13:15:06.027Z"}, "2.5.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.5.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.5.4", "tape": "^4.6.2", "typescript": "^3.5.2"}, "license": "MIT", "gitHead": "905f173a3f785e8d312998e765634ee451ca5f42", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.5.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"shasum": "094ec359dc4b55e7d62e0db4acd76e89fe874d37", "size": 9935, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.5.0.tgz", "integrity": "sha512-WXieX3G/8side6VIqx44ablyULoGruSde5PNTxoUyo5CeyAMX6nVWUd0rgist/EuX655cjhUhTo1Fo3tRYqbcA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_2.5.0_1562215466927_0.3140293290591394"}, "_hasShrinkwrap": false, "publish_time": 1562215467090, "_cnpm_publish_time": 1562215467090, "_cnpmcore_publish_time": "2021-12-13T13:15:06.376Z"}, "2.4.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.4.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "gitHead": "8512eef436e28372f5bc88de3ca76a9cb46e6847", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.4.0", "_nodeVersion": "11.10.1", "_npmVersion": "6.7.0", "dist": {"shasum": "4902b831b051e0db5612a35e1a098376f7b13ad8", "size": 34620, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.4.0.tgz", "integrity": "sha512-4FinE8RfqYnNim20xDwZZE0V2kOs/AuElIjFUbPuegQSaoZM+vUT5FnwSl10KPugH4voTg1bEQlcbCG9ka75TA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_2.4.0_1560929226437_0.46886561193583787"}, "_hasShrinkwrap": false, "publish_time": 1560929226596, "_cnpm_publish_time": 1560929226596, "_cnpmcore_publish_time": "2021-12-13T13:15:06.828Z"}, "2.3.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.3.3", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "gitHead": "b16916a568a0d06f3f8a16c31f9a8b89b7844094", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.3.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "dcce52c05f644f298c6a7ab936bd724ceffbf3a6", "size": 34269, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_2.3.3_1539761182623_0.0008897599197468242"}, "_hasShrinkwrap": false, "publish_time": 1539761182767, "_cnpm_publish_time": 1539761182767, "_cnpmcore_publish_time": "2021-12-13T13:15:07.202Z"}, "2.3.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.3.2", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "1.0.6", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "licenseText": "Copyright (c) 2012 <PERSON> (<EMAIL>) and contributors\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n", "_id": "form-data@2.3.2", "dist": {"shasum": "4970498be604c20c005d4f5c23aecd21d6b49099", "size": 8176, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.3.2.tgz", "integrity": "sha512-6DD2fGWwyxCca2EASUT50GsxWEuwNQDpjMhD9TTaBvI1NE3nLkCr5v7nRdtlmG5g+mNqosdOVHVro+UGmp0Kcw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_2.3.2_1518568309628_0.869333162629065"}, "_hasShrinkwrap": false, "publish_time": 1518568309698, "_cnpm_publish_time": 1518568309698, "_cnpmcore_publish_time": "2021-12-13T13:15:07.633Z"}, "2.3.2-rc1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.3.2-rc1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "1.0.6-rc1", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "readmeFilename": "README.md", "licenseText": "Copyright (c) 2012 <PERSON> (<EMAIL>) and contributors\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n", "_id": "form-data@2.3.2-rc1", "dist": {"shasum": "4d2cac539eec1ac2a8cfb27a61137a3b546db44f", "size": 8189, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.3.2-rc1.tgz", "integrity": "sha512-6xnzoVpu2mLUf4EymxYh2bdM7XAwNqesF9BMa1HQ40BGdCyQ3RpsiESlblj6JxWttlMw/qDXaXrAs54BrjaecQ=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_2.3.2-rc1_1518458302643_0.061536252107149414"}, "_hasShrinkwrap": false, "publish_time": 1518458302688, "_cnpm_publish_time": 1518458302688, "_cnpmcore_publish_time": "2021-12-13T13:15:08.083Z"}, "2.3.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.3.1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "gitHead": "7629e30d4175fa07965a59f70ba5022172f9494a", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.3.1", "_shasum": "6fb94fbd71885306d73d15cc497fe4cc4ecd44bf", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.1", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "6fb94fbd71885306d73d15cc497fe4cc4ecd44bf", "size": 8101, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.3.1.tgz", "integrity": "sha512-ZznzvgkNMfVvSHP0rlg09OeW/g7ib4+NpwNGxLFJOrwUcjN0O8OUASn5cvnpnWve9ZlzW6GUa6NhhlCdb6DqCw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data-2.3.1.tgz_1503615654461_0.18250337382778525"}, "directories": {}, "publish_time": 1503615654577, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503615654577, "_cnpmcore_publish_time": "2021-12-13T13:15:08.485Z"}, "2.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.2.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "gitHead": "6edf2cd4fdf4e61aba23bd538025fd8746a94fa7", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.2.0", "_shasum": "9a5e3b9295f980b2623cf64fa238b14cebca707b", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "9a5e3b9295f980b2623cf64fa238b14cebca707b", "size": 7772, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.2.0.tgz", "integrity": "sha512-eszcMNxZ9pMS8vU1F6tXl82DNfIq+XBc3D0MebyJd9Pe1WoCol0Bwv/v2uQhV4F1xBmjAEY9DQp5LvsevzE2oA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data-2.2.0.tgz_1497170760607_0.7442727568559349"}, "directories": {}, "publish_time": 1497170760717, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497170760717, "_cnpmcore_publish_time": "2021-12-13T13:15:08.922Z"}, "2.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.1.4", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "gitHead": "d7398c3e7cd81ed12ecc0b84363721bae467db02", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.1.4", "_shasum": "33c183acf193276ecaa98143a69e94bfee1750d1", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.1", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "33c183acf193276ecaa98143a69e94bfee1750d1", "size": 7574, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.1.4.tgz", "integrity": "sha512-8HWGSLAPr+AG0hBpsqi5Ob8HrLStN/LWeqhpFl14d7FJgHK48TmgLoALPz69XSUR65YJzDfLUX/BM8+MLJLghQ=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/form-data-2.1.4.tgz_1491750597266_0.5097400255035609"}, "directories": {}, "publish_time": 1491750599126, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491750599126, "_cnpmcore_publish_time": "2021-12-13T13:15:09.318Z"}, "2.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.1.2", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "license": "MIT", "gitHead": "03444d21961a7a44cdc2eae11ee3630f6969023d", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.1.2", "_shasum": "89c3534008b97eada4cbb157d58f6f5df025eae4", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "89c3534008b97eada4cbb157d58f6f5df025eae4", "size": 7552, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.1.2.tgz", "integrity": "sha512-qJCdAGsMWKS90wemiAqqkz+sz4pKoffKqOeVkmV83xX619JRoYJzqsZjoQ1qzGW8ZzmuhvHv4qBHxpSfKSwvLg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/form-data-2.1.2.tgz_1478577739404_0.6574864208232611"}, "directories": {}, "publish_time": 1478577739622, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478577739622, "_cnpmcore_publish_time": "2021-12-13T13:15:09.796Z"}, "2.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.1.1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "ci-lint": "is-node-modern && npm run lint || is-node-not-modern", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "devDependencies": {"coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.7.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.75.0", "rimraf": "^2.5.4"}, "license": "MIT", "gitHead": "ebee8412f79798b87fd3ebed44748c1ca06fc1ac", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.1.1", "_shasum": "4adf0342e1a79afa1e84c8c320a9ffc82392a1f3", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "4adf0342e1a79afa1e84c8c320a9ffc82392a1f3", "size": 7444, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.1.1.tgz", "integrity": "sha512-UtB210Ec3l/KmwXZ5xdOdYjVcVtKbN1FZTivzfPKmn2SbniUyTxYYeMXwvVL3b+DdF4SvWu5Qqv31bXePkm7nw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/form-data-2.1.1.tgz_1475562797683_0.23411617754027247"}, "directories": {}, "publish_time": 1475562797917, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475562797917, "_cnpmcore_publish_time": "2021-12-13T13:15:10.305Z"}, "2.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.1.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "ci-lint": "is-node-modern && npm run lint || is-node-not-modern", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}, "devDependencies": {"coveralls": "^2.11.13", "cross-spawn": "^4.0.0", "eslint": "^3.5.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.74.0", "rimraf": "^2.5.4"}, "license": "MIT", "gitHead": "4718daefac2d16bfb3b32b9761c356cdd2461d71", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.1.0", "_shasum": "58870d83386cf0592165241a5380942276bb9134", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "58870d83386cf0592165241a5380942276bb9134", "size": 7438, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.1.0.tgz", "integrity": "sha512-xEWs0bJE3c5fHML4Na+f15lKgQ+s50bEIYB39gTjRE4kHAd5xKe1mu7sBABUG37jXOqXnJFhU+VdkGQnDsZz+Q=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/form-data-2.1.0.tgz_1474841659621_0.4027709998190403"}, "directories": {}, "publish_time": 1474841662111, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474841662111, "_cnpmcore_publish_time": "2021-12-13T13:15:10.888Z"}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "ci-lint": "is-node-modern && npm run lint || is-node-not-modern", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}, "devDependencies": {"coveralls": "^2.11.13", "cross-spawn": "^4.0.0", "eslint": "^3.5.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.74.0", "rimraf": "^2.5.4"}, "license": "MIT", "gitHead": "652b16ff5b9077bdf65eb66b67286c823c2a1040", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@2.0.0", "_shasum": "6f0aebadcc5da16c13e1ecc11137d85f9b883b25", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "6f0aebadcc5da16c13e1ecc11137d85f9b883b25", "size": 7367, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.0.0.tgz", "integrity": "sha512-BWUNep0UvjzlIJgDsi0SFD3MvnLlwiRaVpfr82Hj2xgc9MJJcl1tSQj01CJDMG+w/kzm+vkZMmXwRM2XrkBuaA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/form-data-2.0.0.tgz_1474092617403_0.5404838663525879"}, "directories": {}, "publish_time": 1474092617649, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474092617649, "_cnpmcore_publish_time": "2021-12-13T13:15:11.471Z"}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/**/*.js", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^2.0.1", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}, "devDependencies": {"coveralls": "^2.11.12", "cross-spawn": "^4.0.0", "eslint": "^2.13.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.74.0", "rimraf": "^2.5.4"}, "license": "MIT", "gitHead": "158443da3b2ce221f0a06ccb3b8ab8c56b68b034", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@1.0.1", "_shasum": "ae315db9a4907fa065502304a66d7733475ee37c", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "ae315db9a4907fa065502304a66d7733475ee37c", "size": 7471, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-1.0.1.tgz", "integrity": "sha512-M4Yhq2mLogpCtpUmfopFlTTuIe6mSCTgKvnlMhDj3NcgVhA1uS20jT0n+xunKPzpmL5w2erSVtp+SKiJf1TlWg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/form-data-1.0.1.tgz_1472204677067_0.1879937476478517"}, "directories": {}, "publish_time": 1472204677316, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472204677316, "_cnpmcore_publish_time": "2021-12-13T13:15:12.029Z"}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover test/run.js", "posttest": "istanbul report lcov text", "lint": "eslint lib/*.js test/*.js test/**/*.js", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^2.0.1", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}, "devDependencies": {"coveralls": "^2.11.12", "cross-spawn": "^4.0.0", "eslint": "^2.13.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.74.0", "rimraf": "^2.5.4"}, "license": "MIT", "gitHead": "6fd2c6555b5c685dbd791f364486df45472f70a4", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@1.0.0", "_shasum": "2285b4456dae81efdbc391949347f4d3e2dce03f", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "2285b4456dae81efdbc391949347f4d3e2dce03f", "size": 7588, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-1.0.0.tgz", "integrity": "sha512-QDT0s+89sJ37Z0PxJl0gB0FoBrogbSI+GgxBRMvPPXE8/1GgjHgEOFSdaMn1MXJhGXA0KCZzCV/BFpHxeEd9FA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/form-data-1.0.0.tgz_1472202064764_0.1383217212278396"}, "directories": {}, "publish_time": 1472202066786, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472202066786, "_cnpmcore_publish_time": "2021-12-13T13:15:12.542Z"}, "1.0.0-rc4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "1.0.0-rc4", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover --report none test/run.js", "posttest": "istanbul report", "lint": "eslint lib/*.js test/*.js test/**/*.js", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "coverage": "codacy-coverage < ./coverage/lcov.info; true"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^1.5.2", "combined-stream": "^1.0.5", "mime-types": "^2.1.10"}, "license": "MIT", "devDependencies": {"codacy-coverage": "^1.1.3", "coveralls": "^2.11.8", "cross-spawn": "^2.1.5", "eslint": "^2.4.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "istanbul": "^0.4.2", "pre-commit": "^1.1.2", "request": "^2.69.0", "rimraf": "^2.5.2"}, "gitHead": "f73996e0508ee2d4b2b376276adfac1de4188ac2", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@1.0.0-rc4", "_shasum": "05ac6bc22227b43e4461f488161554699d4f8b5e", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.11", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "05ac6bc22227b43e4461f488161554699d4f8b5e", "size": 8707, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-1.0.0-rc4.tgz", "integrity": "sha512-HSbGQ2uxT2+Mi1E0KP5lM5lv/r88xkGCpiLdq546q/3vXz94egfIIBpY45NTWMjMzb4nLopsfkG1A3suNa5qUg=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/form-data-1.0.0-rc4.tgz_1458059747097_0.14101114077493548"}, "directories": {}, "publish_time": 1458059747585, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458059747585, "_cnpmcore_publish_time": "2021-12-13T13:15:13.075Z"}, "1.0.0-rc3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "1.0.0-rc3", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"test": "./test/run.js"}, "pre-commit": ["test"], "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^1.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.3"}, "license": "MIT", "devDependencies": {"fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "pre-commit": "^1.0.10", "request": "^2.60.0"}, "gitHead": "c174f1b7f3a78a00ec5af0360469280445e37804", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@1.0.0-rc3", "_shasum": "d35bc62e7fbc2937ae78f948aaa0d38d90607577", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "d35bc62e7fbc2937ae78f948aaa0d38d90607577", "size": 6400, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-1.0.0-rc3.tgz", "integrity": "sha512-Z5JWXWsFDI8x73Rt/Dc7SK/EvKBzudhqIVBtEhcAhtoevCTqO3YJmctGBLzT0Ggg39xFcefkXt00t1TYLz6D0w=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1438229406749, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438229406749, "_cnpmcore_publish_time": "2021-12-13T13:15:13.668Z"}, "1.0.0-rc2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "1.0.0-rc2", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^1.2.1", "combined-stream": "^1.0.3", "mime-types": "^2.1.1"}, "license": "MIT", "devDependencies": {"fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "request": "^2.57.0"}, "gitHead": "9f29fefe9633f3adae72d6416fd6822c060ff6b6", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "homepage": "https://github.com/felixge/node-form-data#readme", "_id": "form-data@1.0.0-rc2", "_shasum": "5bc9c9b3dd3dec1977b0abf58790192081d95235", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "5bc9c9b3dd3dec1977b0abf58790192081d95235", "size": 5964, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-1.0.0-rc2.tgz", "integrity": "sha512-VMd2h5jDswSjdINqjjvjKCQO6EYbjpjBRuX5qEMuojpiN5jAP3NxQqY3AHUuumWE3OBw3frw7Y8v0c2e0Xvniw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1437533308688, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437533308688, "_cnpmcore_publish_time": "2021-12-13T13:15:14.230Z"}, "1.0.0-rc1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "1.0.0-rc1", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^1.2.1", "combined-stream": "^1.0.3", "mime-types": "^2.1.1"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "request": "^2.57.0"}, "gitHead": "e6650a4c078fd09c130ed712848d71d8609c6518", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "homepage": "https://github.com/felixge/node-form-data#readme", "_id": "form-data@1.0.0-rc1", "_shasum": "de5d87ff28439596f4f5500bff58d1244d54793a", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "de5d87ff28439596f4f5500bff58d1244d54793a", "size": 5956, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-1.0.0-rc1.tgz", "integrity": "sha512-KVOHPgpHRLfzsIjcGyHN22MgwWSK4MaXPyURwv4rdUbY32vcngVUo7KqJvgDeYJ0eFnT3zM3x2H2JTuX9QKloA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1434210604021, "_hasShrinkwrap": false, "_cnpm_publish_time": 1434210604021, "_cnpmcore_publish_time": "2021-12-13T13:15:14.860Z"}, "0.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.2.0", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"async": "~0.9.0", "combined-stream": "~0.0.4", "mime-types": "~2.0.3"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.36.0"}, "gitHead": "dfc1a2aef40b97807e2ffe477da06cb2c37e259f", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "homepage": "https://github.com/felixge/node-form-data", "_id": "form-data@0.2.0", "_shasum": "26f8bc26da6440e299cbdcfb69035c4f77a6e466", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "26f8bc26da6440e299cbdcfb69035c4f77a6e466", "size": 5939, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.2.0.tgz", "integrity": "sha512-LkinaG6JazVhYj2AKi67NOIAhqXcBOQACraT0WdhWW4ZO3kTiS0X7C1nJ1jFZf6wak4bVHIA/oOzWkh2ThAipg=="}, "directories": {}, "publish_time": 1417900450402, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417900450402, "_cnpmcore_publish_time": "2021-12-13T13:15:15.495Z"}, "0.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.1.4", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.11", "async": "~0.9.0"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.36.0"}, "gitHead": "5f5f4809ea685f32658809fa0f13d7eface0e45a", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "homepage": "https://github.com/felixge/node-form-data", "_id": "form-data@0.1.4", "_shasum": "91abd788aba9702b1aabfa8bc01031a2ac9e3b12", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "91abd788aba9702b1aabfa8bc01031a2ac9e3b12", "size": 5927, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.1.4.tgz", "integrity": "sha512-x8eE+nzFtAMA0YYlSxf/Qhq6vP1f8wSoZ7Aw1GuctBcmudCNuTUmmx45TfEplyb6cjsZO/jvh6+1VpZn24ez+w=="}, "directories": {}, "publish_time": 1403591744934, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403591744934, "_cnpmcore_publish_time": "2021-12-13T13:15:16.153Z"}, "0.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.1.3", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.11", "async": "~0.9.0"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.36.0"}, "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "homepage": "https://github.com/felixge/node-form-data", "_id": "form-data@0.1.3", "dist": {"shasum": "4ee4346e6eb5362e8344a02075bd8dbd8c7373ea", "size": 5628, "noattachment": false, "tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.1.3.tgz", "integrity": "sha512-khpfkwI/RQybQdwruvz89OCmcXiFZstZ88llcc552BrzvOhqIOHC6YCRJ44GLK7BRFBEMGH9zJ2zMy0nz27Y9w=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1401695304579, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401695304579, "_cnpmcore_publish_time": "2021-12-13T13:15:16.892Z"}, "0.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.1.2", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.6"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.11", "async": "~0.2.9"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.27.0"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "_id": "form-data@0.1.2", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.1.2.tgz", "shasum": "1143c21357911a78dd7913b189b4bab5d5d57445", "size": 5504, "noattachment": false, "integrity": "sha512-ynryYjLcBdZ6Iujum9BMhIRq84ViJBe85+gr5nL+gvRGU4Skti1fUwvggb0pdanQB+ji9IsLGimXUbJq22fuiA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1380773215129, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380773215129, "_cnpmcore_publish_time": "2021-12-13T13:15:17.478Z"}, "0.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.1.1", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.6"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.11", "async": "~0.2.9"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.27.0"}, "readmeFilename": "Readme.md", "_id": "form-data@0.1.1", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.1.1.tgz", "shasum": "0d5f2805647b45533ba10bc8a59cf17d1efa5f12", "size": 5393, "noattachment": false, "integrity": "sha512-+jCaXVx4daeev23hAVaKzaU8Jm020oCDjjkO1yzkZgbIRUwS/904YH+GHmHgeZ5wrYifIpA7Gt/T/EvN/Zl+Fg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1377145378445, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377145378445, "_cnpmcore_publish_time": "2021-12-13T13:15:18.113Z"}, "0.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.6"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.9", "async": "~0.2.9"}, "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.22.0"}, "readmeFilename": "Readme.md", "_id": "form-data@0.1.0", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.1.0.tgz", "shasum": "d36b59baf9b292bb2e5034d7a6079b2bd1e9df83", "size": 5136, "noattachment": false, "integrity": "sha512-YG8xdtf5UK8lcFeIuNF2Kief3BLvv16qShJkynAEwW70TJjxuLm8GC9IjrdmDrrqdr8rOZQQ56vx70sOWnT6gA=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1373309173834, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373309173834, "_cnpmcore_publish_time": "2021-12-13T13:15:18.837Z"}, "0.0.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.10", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.6"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.2", "async": "~0.2.7"}, "devDependencies": {"fake": "~0.2.1", "far": "~0.0.7", "formidable": "~1.0.13", "request": "~2.16.6"}, "readmeFilename": "Readme.md", "_id": "form-data@0.0.10", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.10.tgz", "shasum": "db345a5378d86aeeb1ed5d553b869ac192d2f5ed", "size": 4787, "noattachment": false, "integrity": "sha512-Z9/PpT/agxXi80nMpOH6GFD7XOr6mwk5aWMxDt/KMY+Nm7e4FnRMjddM4/mLPJhpmp6alY1F/1JQpRE6z07xng=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1368077721282, "_hasShrinkwrap": false, "_cnpm_publish_time": 1368077721282, "_cnpmcore_publish_time": "2021-12-13T13:15:19.475Z"}, "0.0.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.9", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.6"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.2", "async": "~0.2.7"}, "devDependencies": {"fake": "~0.2.1", "far": "~0.0.7", "formidable": "~1.0.13", "request": "~2.16.6"}, "readmeFilename": "Readme.md", "_id": "form-data@0.0.9", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.9.tgz", "shasum": "f229b4e39bed528c75836045a23bf8f8ff4db16d", "size": 4754, "noattachment": false, "integrity": "sha512-6wrR4vIlNGFXMu+wC+JGEIHso4Wo8QwxaoN5DO0rEbN8y7N/nRBlA/KVTyu2FGJm6oUswpdCXSq7OxoBbERmbA=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1367255352258, "_hasShrinkwrap": false, "_cnpm_publish_time": 1367255352258, "_cnpmcore_publish_time": "2021-12-13T13:15:20.195Z"}, "0.0.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.8", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.6"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.2", "async": "~0.2.7"}, "devDependencies": {"fake": "~0.2.1", "far": "~0.0.7", "formidable": "~1.0.13", "request": "~2.16.6"}, "readmeFilename": "Readme.md", "_id": "form-data@0.0.8", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.8.tgz", "shasum": "0890cd1005c5ccecc0b9d24a88052c92442d0db5", "size": 111950, "noattachment": false, "integrity": "sha512-yzpBIhe8Ll+dYTXjd+4ORxbQktke+abD0dJjedvqsVVayMkb+PgLGatJNLwo95Va75l3YDZ01SrouzyW9bC2Fg=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1366587162861, "_hasShrinkwrap": false, "_cnpm_publish_time": 1366587162861, "_cnpmcore_publish_time": "2021-12-13T13:15:20.974Z"}, "0.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.7", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.2", "async": "~0.1.9"}, "devDependencies": {"fake": "0.2.1", "far": "0.0.1", "formidable": "1.0.2", "request": "~2.9.203"}, "readmeFilename": "Readme.md", "_id": "form-data@0.0.7", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.7.tgz", "shasum": "7211182a26a266ce39710dc8bc4a81b7040859be", "size": 27366, "noattachment": false, "integrity": "sha512-Y9kJwAs/7CCx1nGC5nBTHiLACbNxlKIx/YUHprIqiDCo/i2eD3qtumFy4x/DbKINCLsBbwcGLEsFGrfGU9tvCg=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "celer", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1360464785643, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360464785643, "_cnpmcore_publish_time": "2021-12-13T13:15:21.795Z"}, "0.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.6", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {"combined-stream": "0.0.3", "mime": "~1.2.2", "async": "~0.1.9"}, "devDependencies": {"fake": "0.2.1", "far": "0.0.1", "formidable": "1.0.2", "request": "~2.9.203"}, "readmeFilename": "Readme.md", "_id": "form-data@0.0.6", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.6.tgz", "shasum": "77ba50dea04bc6eb9bc7bd2d119e64b1f8070a41", "size": 29949, "noattachment": false, "integrity": "sha512-0BZ4TJPFMMIBeCjiRpQUkZimbt2hgMXpovD9hq5sWdiOp+dIHAcsD5fyv9q+fIRVa0kJ8y02qKhK3rInyIXNNQ=="}, "_npmVersion": "1.1.65", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1357632291296, "_hasShrinkwrap": false, "_cnpm_publish_time": 1357632291296, "_cnpmcore_publish_time": "2021-12-13T13:15:22.767Z"}, "0.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.5", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {"combined-stream": "0.0.3", "mime": "~1.2.2", "async": "~0.1.9"}, "devDependencies": {"fake": "0.2.1", "far": "0.0.1", "formidable": "1.0.2", "request": "~2.9.203"}, "readmeFilename": "Readme.md", "_id": "form-data@0.0.5", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.5.tgz", "shasum": "2fe21ccdb8c09cf52d60f78d67f2dd242f2a6102", "size": 29077, "noattachment": false, "integrity": "sha512-8pb178BuxUkO8Etx1MyqYFTqe804p6jZj2dC9wwmoOMkWD9QLKOKuFHOgqYlukawYQjY5eqxv9vzsDmk4rcgDA=="}, "_npmVersion": "1.1.65", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1354691180011, "_hasShrinkwrap": false, "_cnpm_publish_time": 1354691180011, "_cnpmcore_publish_time": "2021-12-13T13:15:23.539Z"}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {"combined-stream": "0.0.3", "mime": "~1.2.2", "async": "~0.1.9"}, "devDependencies": {"fake": "0.2.1", "far": "0.0.1", "formidable": "1.0.2", "request": "~2.9.203"}, "_id": "form-data@0.0.4", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.4.tgz", "shasum": "564115f4a26826903510ec6a94488b4cb11ea317", "size": 27362, "noattachment": false, "integrity": "sha512-ZsOJYryx6jikIr9mTBTgMwew/Ebkobi5ssCeIZ8g2ZsEU4oy8ASbNn27XPPJAam4Rhiy0wXZn009WeXwzYvYNA=="}, "_npmVersion": "1.1.51", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1346950063385, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346950063385, "_cnpmcore_publish_time": "2021-12-13T13:15:24.331Z"}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {"combined-stream": "0.0.3", "mime": "1.2.2", "async": "0.1.9"}, "devDependencies": {"fake": "0.2.1", "far": "0.0.1", "formidable": "1.0.2", "request": "~2.9.203"}, "_id": "form-data@0.0.3", "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.3.tgz", "shasum": "6eea17b45790b42d779a1d581d1b3600fe0c7c0d", "size": 27353, "noattachment": false, "integrity": "sha512-+UDRxY5KMkUvW23fx4+oICuoAy4s4YRXsva2vQ2bFoU/RtrNeTB727dIPZt6GRQhl1W1mXI9BX+bYAJ2BopLiA=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1344567905412, "_hasShrinkwrap": false, "_cnpm_publish_time": 1344567905412, "_cnpmcore_publish_time": "2021-12-13T13:15:25.158Z"}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {"combined-stream": "0.0.3", "mime": "1.2.2", "async": "0.1.9"}, "devDependencies": {"fake": "0.2.1", "far": "0.0.1", "formidable": "1.0.2"}, "_npmUser": {"name": "idralyuk", "email": "<EMAIL>"}, "_id": "form-data@0.0.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.6.15", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.2.tgz", "shasum": "6d1470b4355088034bb28c0a5417845facf10068", "size": 23542, "noattachment": false, "integrity": "sha512-eqyg6Clj1yljJt6wb9qf66B++Nrq6EYwhbflWHoVk5/Zy3AnfpFDlldL9GpKLCJSXEeLuGZkhRjWMk0A4fLkmw=="}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1341266377447, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341266377447, "_cnpmcore_publish_time": "2021-12-13T13:15:26.082Z"}, "0.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A module to create readable `\"application/x-www-form-urlencoded\"` streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/felixge/form-data.git"}, "main": "./lib/form_data", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "_id": "form-data@0.0.0", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/form-data/-/form-data-0.0.0.tgz", "shasum": "c18c31c227bbb33b053217e8fec0c2255e06a1e8", "size": 6019, "noattachment": false, "integrity": "sha512-tWZJTTXUMBG3OGHTOnqioxmBWoRePNJOAlGiIermah/lgFzAXMnEzSAJCY2PI0bBOfiTh1lgBPSvViTZYzUeYw=="}, "scripts": {}, "directories": {}, "publish_time": 1305557902532, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305557902532, "_cnpmcore_publish_time": "2021-12-13T13:15:26.935Z"}, "2.5.2": {"name": "form-data", "version": "2.5.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.5.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "dc653743d1de2fcc340ceea38079daf6e9069fd2", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.5.2.tgz", "fileCount": 7, "integrity": "sha512-GgwY0PS7DbXqajuGf4OYlsrIu3zgxD6Vvql43IBhm6MahqA5SK/7mwhtNj2AdH2z35YR34ujJ7BN+3fFC3jP5Q==", "signatures": [{"sig": "MEUCIQCdPFoscjxRNsLU7GO8n0LHQXccKfNPa86hAAbrhg6cYgIgMDdc+FZNbkq5Rwa4l9+ykfhxdY8kuKlHPchwKIgh8go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30046, "size": 9987}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "7020dd4c1260370abc40e86e3dfe49c5d576fbda", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "safe-buffer": "^5.2.1", "combined-stream": "^1.0.6"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.5.2_1728531993604_0.17849460895581437", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-10-10T03:46:33.800Z", "publish_time": 1728531993800, "_source_registry_name": "default"}, "3.0.2": {"name": "form-data", "version": "3.0.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@3.0.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "83ad9ced7c03feaad97e293d6f6091011e1659c8", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-sJe+TQb2vIaIyO783qN6BlMYWMw3WBOHA1Ay2qxsnjuafEOQFJ2JakedOQirT6D5XPRxDvS7AHYyem9fTpb4LQ==", "signatures": [{"sig": "MEQCIDiM3L2FXLHwbkn8tTH5KO/v+qJ/lNSjm8ehbvRm0uFPAiBY9q8dBK1QkSOnD3RjGHWv8dDTyDyw0FYhwE1piKuwPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31035, "size": 10203}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "f06b0d85d10bc942b3bf586b01ace6e874ac61b3", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "puppeteer": "^1.19.0", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_3.0.2_1728532130262_0.5520780399754868", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-10-10T03:48:50.494Z", "publish_time": 1728532130494, "_source_registry_name": "default"}, "4.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "tests-only": "istanbul cover test/run.js", "posttests-only": "istanbul report lcov text", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "puppeteer": "^1.19.0", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "license": "MIT", "_id": "form-data@4.0.1", "gitHead": "d04f7381b0111c707baa47190de2d48a02988b5b", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==", "shasum": "ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-4.0.1.tgz", "fileCount": 7, "unpackedSize": 31430, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGtHBWWsiRDX0+3zwj7vExb0JnWd4wVT85pGe9spI5RVAiEAi9CB2MmO6rWyrbGifa0MMebcCtIgyZA18pvJqcW73Bg="}], "size": 10320}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/form-data_4.0.1_1728532185836_0.4433628713393647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-10T03:49:46.006Z", "publish_time": 1728532186006, "_source_registry_name": "default"}, "2.5.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "2.5.3", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "tests-only": "istanbul cover test/run.js", "posttests-only": "istanbul report lcov text", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 0.12"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.35", "safe-buffer": "^5.2.1"}, "devDependencies": {"@types/combined-stream": "^1.0.6", "@types/mime-types": "^2.1.4", "@types/node": "^12.20.55", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^4.0.2", "eslint": "^6.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.16", "pkgfiles": "^2.3.2", "pre-commit": "^1.2.2", "request": "~2.87.0", "rimraf": "^2.7.1", "tape": "^5.9.0", "typescript": "^3.9.10"}, "license": "MIT", "_id": "form-data@2.5.3", "readmeFilename": "Readme.md", "gitHead": "9457283e1dce6122adc908fdd7442cfc54cabe7a", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_nodeVersion": "23.8.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-XHIrMD0NpDrNM/Ckf7XJiBbLl57KEhT3+i3yY+eWm+cqYZJQTZrKo8Y8AWKnuV5GT4scfuUGt9LzNoIx3dU1nQ==", "shasum": "f9bcf87418ce748513c0c3494bb48ec270c97acc", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-2.5.3.tgz", "fileCount": 7, "unpackedSize": 30454, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDXVzrquqGQwUqb/HpsZAqAWoH/LcbR5CjTrp1NoodQBwIhANExbX2SC+7RslSWF4rrZ53MIFen/tAHaSBfHhcG8Pog"}], "size": 10072}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/form-data_2.5.3_1739570176821_0.47804769752077725"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T21:56:16.969Z", "publish_time": 1739570176969, "_source_registry_name": "default"}, "3.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "3.0.3", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "tests-only": "istanbul cover test/run.js", "posttests-only": "istanbul report lcov text", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.35"}, "devDependencies": {"@types/node": "^12.20.55", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^6.0.6", "eslint": "^6.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.2", "pre-commit": "^1.2.2", "puppeteer": "^1.20.0", "request": "~2.87.0", "rimraf": "^2.7.1", "tape": "^5.9.0", "typescript": "^3.9.10"}, "license": "MIT", "_id": "form-data@3.0.3", "readmeFilename": "Readme.md", "gitHead": "e46f09bb5338bff090f5cceea5119a8b37c9b147", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_nodeVersion": "23.8.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-q5Y<PERSON>eWy6E2Un0nMGWMgI65MAKtaylxfNJGJxpGh45YDciZB4epbWpaAfImil6CPAPTYB4sh0URQNDRIZG5F2w==", "shasum": "349c8f2c9d8f8f0c879ee0eb7cc0d300018d6b09", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-3.0.3.tgz", "fileCount": 7, "unpackedSize": 31368, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDQXlKzYmVUnZHg6BAjq9x1/osM0oAVS0WwZmRt/t7SKAIgQPvHk9Hk4tDcaZyH5wJwgwPmoaVp3cDeS3+6BIUVO44="}], "size": 10269}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/form-data_3.0.3_1739574012492_0.7782722656980241"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T23:00:12.690Z", "publish_time": 1739574012690, "_source_registry_name": "default"}, "4.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "4.0.2", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "tests-only": "istanbul cover test/run.js", "posttests-only": "istanbul report lcov text", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "in-publish && npm run update-readme || not-in-publish", "postpublish": "npm run restore-readme"}, "pre-commit": ["lint", "ci-test", "check"], "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "devDependencies": {"@types/combined-stream": "^1.0.6", "@types/mime-types": "^2.1.4", "@types/node": "^12.20.55", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^6.0.6", "eslint": "^6.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.2", "pre-commit": "^1.2.2", "puppeteer": "^1.20.0", "request": "~2.87.0", "rimraf": "^2.7.1", "tape": "^5.9.0", "typescript": "^3.9.10"}, "license": "MIT", "_id": "form-data@4.0.2", "gitHead": "7465e1337244831e91f9a2413a2bf49bdc2a2e04", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_nodeVersion": "23.8.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "shasum": "35cabbdd30c3ce73deb2c42d3c8d3ed9ca51794c", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-4.0.2.tgz", "fileCount": 7, "unpackedSize": 31836, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDsc8+kG0IfOthXSQCLdi2rgzXN0YYGzQ3D4tXCl7U1OAiEA4FxyHLX1TtCQUPk1pkXej4soPFGPkYRytEm5gBcrRoQ="}], "size": 10390}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/form-data_4.0.2_1739575437467_0.440653080386882"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T23:23:57.699Z", "publish_time": 1739575437699, "_source_registry_name": "default"}, "4.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "4.0.3", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "tests-only": "istanbul cover test/run.js", "posttests-only": "istanbul report lcov text", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "npm run update-readme", "postpublish": "npm run restore-readme"}, "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^6.0.6", "eslint": "=8.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.2", "request": "~2.87.0", "rimraf": "^2.7.1", "tape": "^5.9.0"}, "license": "MIT", "_id": "form-data@4.0.3", "gitHead": "d8d67dc8ac79285154edf7d3f57dbab593b9a146", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_nodeVersion": "24.1.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==", "shasum": "608b1b3f3e28be0fccf5901fc85fb3641e5cf0ae", "tarball": "https://registry.npmmirror.com/form-data/-/form-data-4.0.3.tgz", "fileCount": 8, "unpackedSize": 44398, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC98ZP7eRRzRiynqPOLaLqNLd2gz95DwW0wCH0MuUEciwIhANjlKLBtWIxHtFlG6b9esJjnxwrBPv4XRL0OfgiagUfr"}], "size": 13699}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/form-data_4.0.3_1749135071283_0.22305244563165738"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-05T14:51:11.479Z", "publish_time": 1749135071479, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "_source_registry_name": "default"}