{"_attachments": {}, "_id": "less-loader", "_rev": "56280-61f181e00053da9891019867", "author": {"name": "<PERSON> @jhnns"}, "description": "A Less loader for webpack. Compiles Less to CSS.", "dist-tags": {"latest": "12.3.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "name": "less-loader", "readme": "<div align=\"center\">\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\" src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n</div>\n\n[![npm][npm]][npm-url]\n[![node][node]][node-url]\n[![tests][tests]][tests-url]\n[![cover][cover]][cover-url]\n[![discussion][discussion]][discussion-url]\n[![size][size]][size-url]\n\n# less-loader\n\nA Less loader for webpack. Compiles Less to CSS.\n\n## Getting Started\n\nTo begin, you'll need to install `less` and `less-loader`:\n\n```console\nnpm install less less-loader --save-dev\n```\n\nor\n\n```console\nyarn add -D less less-loader\n```\n\nor\n\n```console\npnpm add -D less less-loader\n```\n\nThen add the loader to your `webpack` config. For example:\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          // compiles Less to CSS\n          \"style-loader\",\n          \"css-loader\",\n          \"less-loader\",\n        ],\n      },\n    ],\n  },\n};\n```\n\nAnd run `webpack` via your preferred method.\n\n## Options\n\n- **[`lessOptions`](#lessoptions)**\n- **[`additionalData`](#additionalData)**\n- **[`sourceMap`](#sourcemap)**\n- **[`webpackImporter`](#webpackimporter)**\n- **[`implementation`](#implementation)**\n- **[`lessLogAsWarnOrErr`](#lesslogaswarnorerr)**\n\n### `lessOptions`\n\nType:\n\n```ts\ntype lessOptions = import('less').options | ((loaderContext: LoaderContext) => import('less').options})\n```\n\nDefault: `{ relativeUrls: true }`\n\nYou can pass any Less specific options to the `less-loader` through the `lessOptions` property in the [loader options](https://webpack.js.org/configuration/module/#ruleoptions--rulequery). See the [Less documentation](http://lesscss.org/usage/#command-line-usage-options) for all available options in dash-case. Since we're passing these options to Less programmatically, you need to pass them in camelCase here:\n\n#### `object`\n\nUse an object to pass options through to Less.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          {\n            loader: \"style-loader\",\n          },\n          {\n            loader: \"css-loader\",\n          },\n          {\n            loader: \"less-loader\",\n            options: {\n              lessOptions: {\n                strictMath: true,\n              },\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n#### `function`\n\nAllows setting the options passed through to Less based off of the loader context.\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              lessOptions: (loaderContext) => {\n                // More information about available properties https://webpack.js.org/api/loaders/\n                const { resourcePath, rootContext } = loaderContext;\n                const relativePath = path.relative(rootContext, resourcePath);\n\n                if (relativePath === \"styles/foo.less\") {\n                  return {\n                    paths: [\"absolute/path/c\", \"absolute/path/d\"],\n                  };\n                }\n\n                return {\n                  paths: [\"absolute/path/a\", \"absolute/path/b\"],\n                };\n              },\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n### `additionalData`\n\nType:\n\n```ts\ntype additionalData =\n  | string\n  | ((content: string, loaderContext: LoaderContext) => string);\n```\n\nDefault: `undefined`\n\nPrepends/Appends `Less` code to the actual entry file.\nIn this case, the `less-loader` will not override the source but just **prepend** the entry's content.\n\nThis is especially useful when some of your Less variables depend on the environment:\n\n> Since you're injecting code, this will break the source mappings in your entry file. Often there's a simpler solution than this, like multiple Less entry files.\n\n#### `string`\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              additionalData: `@env: ${process.env.NODE_ENV};`,\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n#### `function`\n\n##### `Sync`\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              additionalData: (content, loaderContext) => {\n                // More information about available properties https://webpack.js.org/api/loaders/\n                const { resourcePath, rootContext } = loaderContext;\n                const relativePath = path.relative(rootContext, resourcePath);\n\n                if (relativePath === \"styles/foo.less\") {\n                  return \"@value: 100px;\" + content;\n                }\n\n                return \"@value: 200px;\" + content;\n              },\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n##### `Async`\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              additionalData: async (content, loaderContext) => {\n                // More information about available properties https://webpack.js.org/api/loaders/\n                const { resourcePath, rootContext } = loaderContext;\n                const relativePath = path.relative(rootContext, resourcePath);\n\n                if (relativePath === \"styles/foo.less\") {\n                  return \"@value: 100px;\" + content;\n                }\n\n                return \"@value: 200px;\" + content;\n              },\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n### `sourceMap`\n\nType:\n\n```ts\ntype sourceMap = boolean;\n```\n\nDefault: depends on the `compiler.devtool` value\n\nBy default generation of source maps depends on the [`devtool`](https://webpack.js.org/configuration/devtool/) option. All values enable source map generation except `eval` and `false` value.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          {\n            loader: \"css-loader\",\n            options: {\n              sourceMap: true,\n            },\n          },\n          {\n            loader: \"less-loader\",\n            options: {\n              sourceMap: true,\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n### `webpackImporter`\n\nType:\n\n```ts\ntype webpackImporter = boolean | \"only\";\n```\n\nDefault: `true`\n\nEnables/Disables the default `webpack` importer.\n\nThis can improve performance in some cases. Use it with caution because aliases and `@import` from [`node_modules`](https://webpack.js.org/configuration/resolve/#resolvemodules) will not work.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              webpackImporter: false,\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n### `implementation`\n\nType:\n\n```ts\ntype implementation = object | string;\n```\n\n> less-loader compatible with Less 3 and 4 versions\n\nThe special `implementation` option determines which implementation of Less to use. Overrides the locally installed `peerDependency` version of `less`.\n\n**This option is only really useful for downstream tooling authors to ease the Less 3-to-4 transition.**\n\n#### `object`\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              implementation: require(\"less\"),\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n#### `string`\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              implementation: require.resolve(\"less\"),\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n### `lessLogAsWarnOrErr`\n\nType:\n\n```ts\ntype lessLogAsWarnOrErr = boolean;\n```\n\nDefault: `false`\n\n`Less` warnings and errors will be webpack warnings and errors, not just logs.\n\n**warning.less**\n\n```less\ndiv {\n  &:extend(.body1);\n}\n```\n\nIf `lessLogAsWarnOrErr` is set to `false` it will be just a log and webpack will compile successfully, but if you set this option to `true` webpack will compile fail with a warning.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          \"css-loader\",\n          {\n            loader: \"less-loader\",\n            options: {\n              lessLogAsWarnOrErr: true,\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n## Examples\n\n### Normal usage\n\nChain the `less-loader` with the [`css-loader`](https://github.com/webpack-contrib/css-loader) and the [`style-loader`](https://github.com/webpack-contrib/style-loader) to immediately apply all styles to the DOM.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          {\n            loader: \"style-loader\", // creates style nodes from JS strings\n          },\n          {\n            loader: \"css-loader\", // translates CSS into CommonJS\n          },\n          {\n            loader: \"less-loader\", // compiles Less to CSS\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\nUnfortunately, Less doesn't map all options 1-by-1 to camelCase. When in doubt, [check their executable](https://github.com/less/less.js/blob/3.x/bin/lessc) and search for the dash-case option.\n\n### Source maps\n\nTo enable sourcemaps for CSS, you'll need to pass the `sourceMap` property in the loader's options. If this is not passed, the loader will respect the setting for webpack source maps, set in `devtool`.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  devtool: \"source-map\", // any \"source-map\"-like devtool is possible\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          \"style-loader\",\n          {\n            loader: \"css-loader\",\n            options: {\n              sourceMap: true,\n            },\n          },\n          {\n            loader: \"less-loader\",\n            options: {\n              sourceMap: true,\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\nIf you want to edit the original Less files inside Chrome, [there's a good blog post](https://medium.com/@toolmantim/getting-started-with-css-sourcemaps-and-in-browser-sass-editing-b4daab987fb0). The blog post is about Sass but it also works for Less.\n\n### In production\n\nUsually, it's recommended to extract the style sheets into a dedicated file in production using the [MiniCssExtractPlugin](https://github.com/webpack-contrib/mini-css-extract-plugin). This way your styles are not dependent on JavaScript.\n\n### Imports\n\nFirst we try to use built-in `less` resolve logic, then `webpack` resolve logic.\n\n#### Webpack Resolver\n\n`webpack` provides an [advanced mechanism to resolve files](https://webpack.js.org/configuration/resolve/).\n`less-loader` applies a Less plugin that passes all queries to the webpack resolver if `less` could not resolve `@import`.\nThus you can import your Less modules from `node_modules`.\n\n```css\n@import \"bootstrap/less/bootstrap\";\n```\n\nUsing `~` is deprecated and can be removed from your code (**we recommend it**), but we still support it for historical reasons.\nWhy you can removed it? The loader will first try to resolve `@import` as relative, if it cannot be resolved, the loader will try to resolve `@import` inside [`node_modules`](https://webpack.js.org/configuration/resolve/#resolvemodules).\n\nDefault resolver options can be modified by [`resolve.byDependency`](https://webpack.js.org/configuration/resolve/#resolvebydependency):\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  devtool: \"source-map\", // any \"source-map\"-like devtool is possible\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\"style-loader\", \"css-loader\", \"less-loader\"],\n      },\n    ],\n  },\n  resolve: {\n    byDependency: {\n      // More options can be found here https://webpack.js.org/configuration/resolve/\n      less: {\n        mainFiles: [\"custom\"],\n      },\n    },\n  },\n};\n```\n\n#### Less Resolver\n\nIf you specify the `paths` option, modules will be searched in the given `paths`. This is `less` default behavior. `paths` should be an array with absolute paths:\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  module: {\n    rules: [\n      {\n        test: /\\.less$/i,\n        use: [\n          {\n            loader: \"style-loader\",\n          },\n          {\n            loader: \"css-loader\",\n          },\n          {\n            loader: \"less-loader\",\n            options: {\n              lessOptions: {\n                paths: [path.resolve(__dirname, \"node_modules\")],\n              },\n            },\n          },\n        ],\n      },\n    ],\n  },\n};\n```\n\n### Plugins\n\nIn order to use [plugins](http://lesscss.org/usage/#plugins), simply set the `plugins` option like this:\n\n**webpack.config.js**\n\n```js\nconst CleanCSSPlugin = require('less-plugin-clean-css');\n\nmodule.exports = {\n  ...\n    {\n      loader: 'less-loader',\n      options: {\n        lessOptions: {\n          plugins: [\n            new CleanCSSPlugin({ advanced: true }),\n          ],\n        },\n      },\n    },\n  ...\n};\n```\n\n> [!NOTE]\n>\n> Access to the [loader context](https://webpack.js.org/api/loaders/#the-loader-context) inside the custom plugin can be done using the `pluginManager.webpackLoaderContext` property.\n\n```js\nmodule.exports = {\n  install: function (less, pluginManager, functions) {\n    functions.add(\"pi\", function () {\n      // Loader context is available in `pluginManager.webpackLoaderContext`\n\n      return Math.PI;\n    });\n  },\n};\n```\n\n### Extracting style sheets\n\nBundling CSS with webpack has some nice advantages like referencing images and fonts with hashed urls or [hot module replacement](https://webpack.js.org/concepts/hot-module-replacement/) in development. In production, on the other hand, it's not a good idea to apply your style sheets depending on JS execution. Rendering may be delayed or even a [FOUC](https://en.wikipedia.org/wiki/Flash_of_unstyled_content) might be visible. Thus it's often still better to have them as separate files in your final production build.\n\nThere are two possibilities to extract a style sheet from the bundle:\n\n- [`extract-loader`](https://github.com/peerigon/extract-loader) (simpler, but specialized on the css-loader's output)\n- [`MiniCssExtractPlugin`](https://github.com/webpack-contrib/mini-css-extract-plugin) (more complex, but works in all use-cases)\n\n### CSS modules gotcha\n\nThere is a known problem with Less and [CSS modules](https://github.com/css-modules/css-modules) regarding relative file paths in `url(...)` statements. [See this issue for an explanation](https://github.com/webpack-contrib/less-loader/issues/109#issuecomment-253797335).\n\n## Contributing\n\nPlease take a moment to read our contributing guidelines if you haven't yet done so.\n\n[CONTRIBUTING](./.github/CONTRIBUTING.md)\n\n## License\n\n[MIT](./LICENSE)\n\n[npm]: https://img.shields.io/npm/v/less-loader.svg\n[npm-url]: https://npmjs.com/package/less-loader\n[node]: https://img.shields.io/node/v/less-loader.svg\n[node-url]: https://nodejs.org\n[tests]: https://github.com/webpack-contrib/less-loader/workflows/less-loader/badge.svg\n[tests-url]: https://github.com/webpack-contrib/less-loader/actions\n[cover]: https://codecov.io/gh/webpack-contrib/less-loader/branch/master/graph/badge.svg\n[cover-url]: https://codecov.io/gh/webpack-contrib/less-loader\n[discussion]: https://img.shields.io/github/discussions/webpack/webpack\n[discussion-url]: https://github.com/webpack/webpack/discussions\n[size]: https://packagephobia.now.sh/badge?p=less-loader\n[size-url]: https://packagephobia.now.sh/result?p=less-loader\n", "time": {"created": "2022-01-26T17:16:16.797Z", "modified": "2025-07-07T09:18:18.192Z", "10.2.0": "2021-10-18T13:18:55.001Z", "10.1.0": "2021-10-11T21:30:49.731Z", "10.0.1": "2021-07-02T12:53:35.686Z", "10.0.0": "2021-06-17T12:58:53.746Z", "9.1.0": "2021-06-10T16:55:05.773Z", "9.0.0": "2021-05-13T19:33:23.004Z", "8.1.1": "2021-04-15T11:43:19.763Z", "8.1.0": "2021-04-09T15:25:21.006Z", "8.0.0": "2021-02-01T16:26:13.608Z", "7.3.0": "2021-01-21T12:31:17.142Z", "7.2.1": "2020-12-28T13:33:29.934Z", "7.2.0": "2020-12-23T13:30:29.067Z", "7.1.0": "2020-11-11T11:57:31.174Z", "7.0.2": "2020-10-09T13:27:05.582Z", "7.0.1": "2020-09-03T14:37:18.862Z", "7.0.0": "2020-08-25T11:47:37.427Z", "6.2.0": "2020-07-03T15:14:10.734Z", "6.1.3": "2020-06-29T12:22:53.666Z", "6.1.2": "2020-06-22T13:05:17.443Z", "6.1.1": "2020-06-11T16:05:15.878Z", "6.1.0": "2020-05-07T12:56:29.995Z", "6.0.0": "2020-04-24T16:35:26.838Z", "5.0.0": "2019-04-29T11:32:15.215Z", "4.1.0": "2018-03-09T11:29:02.989Z", "4.0.6": "2018-02-27T01:51:53.579Z", "4.0.5": "2017-07-10T01:46:05.343Z", "4.0.4": "2017-05-30T13:29:54.859Z", "4.0.3": "2017-03-30T18:17:20.424Z", "4.0.2": "2017-03-21T22:32:45.149Z", "4.0.1": "2017-03-21T11:54:00.617Z", "4.0.0": "2017-03-20T19:43:37.488Z", "3.0.0": "2017-03-08T03:05:53.237Z", "2.2.3": "2016-03-25T19:48:59.738Z", "2.2.2": "2015-11-25T18:51:24.123Z", "2.2.1": "2015-09-16T20:21:35.993Z", "2.2.0": "2015-03-22T22:45:35.100Z", "2.1.0": "2015-03-06T16:34:27.415Z", "2.0.0": "2014-12-22T08:21:44.499Z", "0.7.8": "2014-12-06T14:19:52.339Z", "0.7.7": "2014-08-09T11:01:47.305Z", "0.7.6": "2014-08-04T06:18:14.045Z", "0.7.5": "2014-05-28T05:41:28.347Z", "0.7.4": "2014-05-21T15:16:21.644Z", "0.7.3": "2014-05-08T08:56:12.080Z", "0.7.2": "2014-03-13T14:14:36.688Z", "0.7.1": "2014-01-28T15:03:53.080Z", "0.7.0": "2014-01-21T15:21:40.342Z", "0.6.2": "2013-11-29T15:35:31.526Z", "0.6.1": "2013-10-29T13:42:42.565Z", "0.6.0": "2013-10-22T13:11:21.208Z", "0.5.1": "2013-05-10T11:49:09.326Z", "0.5.0": "2013-02-01T07:47:29.083Z", "0.2.2": "2012-09-26T12:20:39.777Z", "0.2.1": "2012-09-26T11:21:54.489Z", "0.2.0": "2012-08-23T16:47:57.572Z", "0.1.3": "2012-07-01T13:56:05.852Z", "0.1.2": "2012-06-30T14:12:31.393Z", "0.1.1": "2012-05-20T22:03:05.784Z", "0.1.0": "2012-05-01T13:44:48.442Z", "11.0.0": "2022-05-17T17:50:05.368Z", "11.1.0": "2022-10-06T21:34:45.422Z", "11.1.1": "2023-05-28T00:11:32.062Z", "11.1.2": "2023-05-31T15:29:16.088Z", "11.1.3": "2023-06-08T22:07:12.327Z", "11.1.4": "2023-12-27T18:05:01.139Z", "12.0.0": "2024-01-15T14:13:14.626Z", "12.1.0": "2024-01-19T16:46:48.926Z", "12.2.0": "2024-01-30T15:49:20.962Z", "12.3.0": "2025-05-01T10:44:46.409Z"}, "versions": {"10.2.0": {"name": "less-loader", "version": "10.2.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.1", "husky": "^7.0.1", "jest": "^27.0.6", "less": "^4.1.1", "less-plugin-glob": "^3.0.0", "lint-staged": "^11.0.1", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.51.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "341020b76d107e7a19f8376e39b4d7cc9765372d", "_id": "less-loader@10.2.0", "_nodeVersion": "12.22.7", "_npmVersion": "7.24.0", "dist": {"shasum": "97286d8797dc3dc05b1d16b0ecec5f968bdd4e32", "size": 9748, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-10.2.0.tgz", "integrity": "sha512-AV5KHWvCezW27GT90WATaDnfXBv99llDbtaj4bshq6DvAihMdNjaPDcUMa6EXKLRF+P2opFenJp89BXg91XLYg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_10.2.0_1634563134877_0.6920930405478269"}, "_hasShrinkwrap": false, "publish_time": 1634563135001, "_cnpm_publish_time": 1634563135001, "_cnpmcore_publish_time": "2021-12-16T10:09:01.459Z"}, "10.1.0": {"name": "less-loader", "version": "10.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.1", "husky": "^7.0.1", "jest": "^27.0.6", "less": "^4.1.1", "less-plugin-glob": "^3.0.0", "lint-staged": "^11.0.1", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.51.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "f8de1ea7d32ae388e36cdd85baf49c532d55534b", "_id": "less-loader@10.1.0", "_nodeVersion": "12.22.6", "_npmVersion": "7.24.0", "dist": {"shasum": "4379bdff409d1eef36c4cb50611b8944a27659b2", "size": 9632, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-10.1.0.tgz", "integrity": "sha512-Xz5e/YLoLmdWFu7XzzA6LVX8a2+6onDz5LJb8oLWAkOnIZ5Hjcpe+nf+Sta6UgEoIMfzWVrR3XlIVfPeZDb10A=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_10.1.0_1633987849564_0.2284210186503457"}, "_hasShrinkwrap": false, "publish_time": 1633987849731, "_cnpm_publish_time": 1633987849731, "_cnpmcore_publish_time": "2021-12-16T10:09:01.668Z"}, "10.0.1": {"name": "less-loader", "version": "10.0.1", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.5", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.2", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.0", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.4", "less": "^4.1.1", "lint-staged": "^11.0.0", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.1", "standard-version": "^9.3.0", "strip-ansi": "^7.0.0", "webpack": "^5.39.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "56d2c736186d3b28b681fbd0033e21ef262fcf04", "_id": "less-loader@10.0.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.16.0", "dist": {"shasum": "c05aaba68d00400820275f21c2ad87cb9fa9923f", "size": 9563, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-10.0.1.tgz", "integrity": "sha512-Crln//HpW9M5CbtdfWm3IO66Cvx1WhZQvNybXgfB2dD/6Sav9ppw+IWqs/FQKPBFO4B6X0X28Z0WNznshgwUzA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_10.0.1_1625230415569_0.14823922026448066"}, "_hasShrinkwrap": false, "publish_time": 1625230415686, "_cnpm_publish_time": 1625230415686, "_cnpmcore_publish_time": "2021-12-16T10:09:01.921Z"}, "10.0.0": {"name": "less-loader", "version": "10.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.5", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.2", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.0", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.4", "less": "^4.1.1", "lint-staged": "^11.0.0", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.1", "standard-version": "^9.3.0", "strip-ansi": "^7.0.0", "webpack": "^5.39.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "3332a1d5f8630e64b9f1ae58b31e1a21b1975c46", "_id": "less-loader@10.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.16.0", "dist": {"shasum": "2c21a204a29a46cba7de4e7d3659efa1e303c7d1", "size": 9531, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-10.0.0.tgz", "integrity": "sha512-JjioAkw9qyavL0BzMPUOHJa0a20fh+ipq/MNZH4OkU8qERsCMeZIWRE0FDBIx2O+cFguvY01vHh/lmBA9LyWDg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_10.0.0_1623934733640_0.42579472880714264"}, "_hasShrinkwrap": false, "publish_time": 1623934733746, "_cnpm_publish_time": 1623934733746, "_cnpmcore_publish_time": "2021-12-16T10:09:02.159Z"}, "9.1.0": {"name": "less-loader", "version": "9.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.5", "@babel/preset-env": "^7.14.5", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.2", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.4", "less": "^4.1.1", "lint-staged": "^11.0.0", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.1", "standard-version": "^9.3.0", "strip-ansi": "^7.0.0", "webpack": "^5.38.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "8ddb7ff058536a56eca9f4732b69bb4ac89551e0", "_id": "less-loader@9.1.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.16.0", "dist": {"shasum": "5568a30637edadfebcb576c648ae8336b04b11e7", "size": 9722, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-9.1.0.tgz", "integrity": "sha512-/BPur/W4NaDBvw4A7pTGMtKpAsAnexeJdw2VWRVzWEQCNX2FboFa1GUIFwkzBFwX5x/q7M/Srtng96QnhV5wIA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_9.1.0_1623344105521_0.5966962438853327"}, "_hasShrinkwrap": false, "publish_time": 1623344105773, "_cnpm_publish_time": 1623344105773, "_cnpmcore_publish_time": "2021-12-16T10:09:02.373Z"}, "9.0.0": {"name": "less-loader", "version": "9.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.13.16", "@babel/core": "^7.14.2", "@babel/preset-env": "^7.14.2", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "husky": "^6.0.0", "jest": "^26.6.3", "less": "^4.1.1", "lint-staged": "^11.0.0", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.0", "standard-version": "^9.3.0", "strip-ansi": "^7.0.0", "webpack": "^5.37.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "774dafa3b0b689d902540abf88eca4655893a2d6", "_id": "less-loader@9.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.12.1", "dist": {"shasum": "71a0b530174bddf89bb11a5019dd725f54df4791", "size": 9491, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-9.0.0.tgz", "integrity": "sha512-bPen1xeGTZuYFFobcdz9kMUVgSSSDZQJtyhawtCtcz1QboQOwhkI7uCwp5UO+IZpO+LJS1W73YwxsufbBT6SBQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_9.0.0_1620934402764_0.07424410548318816"}, "_hasShrinkwrap": false, "publish_time": 1620934403004, "_cnpm_publish_time": 1620934403004, "_cnpmcore_publish_time": "2021-12-16T10:09:02.615Z"}, "8.1.1": {"name": "less-loader", "version": "8.1.1", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.13.14", "@babel/core": "^7.13.15", "@babel/preset-env": "^7.13.15", "@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.24.0", "eslint-config-prettier": "^8.2.0", "eslint-plugin-import": "^2.22.1", "husky": "^4.3.8", "jest": "^26.6.3", "less": "^4.1.1", "lint-staged": "^10.5.4", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.2.0", "strip-ansi": "^6.0.0", "webpack": "^5.33.2"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "74795fd0b49b01b532b6fb337bb2ddbf74f45c31", "_id": "less-loader@8.1.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.9.0", "dist": {"shasum": "ababe912580457ad00a4318146aac5b53e023f42", "size": 13292, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-8.1.1.tgz", "integrity": "sha512-K93jJU7fi3n6rxVvzp8Cb88Uy9tcQKfHlkoezHwKILXhlNYiRQl4yowLIkQqmBXOH/5I8yoKiYeIf781HGkW9g=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_8.1.1_1618486999640_0.6458957914177554"}, "_hasShrinkwrap": false, "publish_time": 1618486999763, "_cnpm_publish_time": 1618486999763, "_cnpmcore_publish_time": "2021-12-16T10:09:02.836Z"}, "8.1.0": {"name": "less-loader", "version": "8.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.0", "husky": "^4.3.8", "jest": "^26.6.3", "less": "^4.1.1", "lint-staged": "^10.5.3", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.1.0", "strip-ansi": "^6.0.0", "webpack": "^5.19.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "a48a7705b34f9db6d1d49c6a61998422b17fb11f", "_id": "less-loader@8.1.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.7.6", "dist": {"shasum": "8276adc16bf6576dd80b71563685a2cfe93b0a50", "size": 13243, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-8.1.0.tgz", "integrity": "sha512-IE73O5LY5WHA71EDwszM2PIEGDF30xz45GplpRhYuxMXhAvXoMudu/ItjllNR/ht7XLh5N7JegzRg11HYu+xxg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_8.1.0_1617981920883_0.10323426552398463"}, "_hasShrinkwrap": false, "publish_time": 1617981921006, "_cnpm_publish_time": 1617981921006, "_cnpmcore_publish_time": "2021-12-16T10:09:03.067Z"}, "8.0.0": {"name": "less-loader", "version": "8.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.0", "husky": "^4.3.8", "jest": "^26.6.3", "less": "^4.1.1", "lint-staged": "^10.5.3", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.1.0", "strip-ansi": "^6.0.0", "webpack": "^5.19.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "6a682b8c5f2731c57b9fa4645656024e812a96c6", "_id": "less-loader@8.0.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.11", "dist": {"shasum": "cd7f10e3c13ca8f71244a11ee396405ae3ce9c82", "size": 13031, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-8.0.0.tgz", "integrity": "sha512-tnDs0ZdwPZgNOg0NGJ0sAD2KViG9TvOMDVibT33fH1bpLkT4xMo5Ue2FsbjFsVsUKtuRTlU0tYp2/lRizrycLg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_8.0.0_1612196773486_0.22461124534187826"}, "_hasShrinkwrap": false, "publish_time": 1612196773608, "_cnpm_publish_time": 1612196773608, "_cnpmcore_publish_time": "2021-12-16T10:09:03.302Z"}, "7.3.0": {"name": "less-loader", "version": "7.3.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.18.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.0", "husky": "^4.3.8", "jest": "^26.6.3", "less": "^4.1.0", "lint-staged": "^10.5.3", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.1.0", "strip-ansi": "^6.0.0", "webpack": "^5.16.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "a6c4e899d7a2debafe024a3cb1a97e5912e3f6e6", "_id": "less-loader@7.3.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.11", "dist": {"shasum": "f9d6d36d18739d642067a05fb5bd70c8c61317e5", "size": 12642, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.3.0.tgz", "integrity": "sha512-Mi8915g7NMaLlgi77mgTTQvK022xKRQBIVDSyfl3ErTuBhmZBQab0mjeJjNNqGbdR+qrfTleKXqbGI4uEFavxg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.3.0_1611232277033_0.17753254319815848"}, "_hasShrinkwrap": false, "publish_time": 1611232277142, "_cnpm_publish_time": 1611232277142, "_cnpmcore_publish_time": "2021-12-16T10:09:03.592Z"}, "7.2.1": {"name": "less-loader", "version": "7.2.1", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.16.0", "eslint-config-prettier": "^7.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.0", "husky": "^4.3.6", "jest": "^26.6.3", "less": "^4.0.0", "lint-staged": "^10.5.3", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.0.0", "strip-ansi": "^6.0.0", "webpack": "^5.11.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "7bd4618b5ca1ecfe9420e4dbb8b1fbd189cf4126", "_id": "less-loader@7.2.1", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.10", "dist": {"shasum": "a923df8567256751b0ab4e0c3eecff10fd0a5876", "size": 12353, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.2.1.tgz", "integrity": "sha512-4v83WZ7KGbluOWPgk3iNjreAaJDNStfmmdfJbQIib3Jlc8mejV3w6A9xU+EkaivjBVqwQEK0y8cFthyNeGnrTQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.2.1_1609162409801_0.02001357679644644"}, "_hasShrinkwrap": false, "publish_time": 1609162409934, "_cnpm_publish_time": 1609162409934, "_cnpmcore_publish_time": "2021-12-16T10:09:03.865Z"}, "7.2.0": {"name": "less-loader", "version": "7.2.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.16.0", "eslint-config-prettier": "^7.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.0", "husky": "^4.3.6", "jest": "^26.6.3", "less": "^4.0.0", "lint-staged": "^10.5.3", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.0.0", "strip-ansi": "^6.0.0", "webpack": "^5.11.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "78befcdf71c34231efefd977cfb107844eeffcbc", "_id": "less-loader@7.2.0", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.10", "dist": {"shasum": "37ba9c778d999930cd24fa1b59aa4e0ba5ba0441", "size": 12275, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.2.0.tgz", "integrity": "sha512-C/08kYL2+wFLFQ/c8tey4qX6xpPVtBBwGa9gtpI/bktWpSUaUkZxeOUMM2zGDrQ4s/ZVpA+Rv2RyIvwEXNVuIg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.2.0_1608730228902_0.04851594099960321"}, "_hasShrinkwrap": false, "publish_time": 1608730229067, "_cnpm_publish_time": 1608730229067, "_cnpmcore_publish_time": "2021-12-16T10:09:04.086Z"}, "7.1.0": {"name": "less-loader", "version": "7.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/preset-env": "^7.12.1", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.5.2", "cross-env": "^7.0.2", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.3.0", "jest": "^26.6.3", "less": "^3.12.2", "lint-staged": "^10.5.1", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.2", "standard-version": "^9.0.0", "strip-ansi": "^6.0.0", "webpack": "^5.4.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "01806f69225d46fc3c9bfe90c374585cc5f98451", "_id": "less-loader@7.1.0", "_nodeVersion": "12.19.0", "_npmVersion": "6.14.8", "dist": {"shasum": "958d41e86d7de0bcb490711ee0f235aa9dc596aa", "size": 12196, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.1.0.tgz", "integrity": "sha512-EHbnRaTzHgsxnd3RK6OXSiygcCJs72+2ezXVLg+Hgl/ijUTtthKZXZh4MvQkWJr3h/SSKvxGZr7IIHzuS2KbVQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.1.0_1605095851022_0.3069791101689068"}, "_hasShrinkwrap": false, "publish_time": 1605095851174, "_cnpm_publish_time": 1605095851174, "_cnpmcore_publish_time": "2021-12-16T10:09:04.315Z"}, "7.0.2": {"name": "less-loader", "version": "7.0.2", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.5.2", "cross-env": "^7.0.2", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.3.0", "inspect-loader": "^1.0.0", "jest": "^26.5.2", "less": "^3.12.2", "lint-staged": "^10.4.0", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.2", "standard-version": "^9.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.44.2"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "46a7e278fd66e09e202cd596df879302d4b93f82", "_id": "less-loader@7.0.2", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.8", "dist": {"shasum": "0d73a49ec32a9d3ff12614598e6e2b47fb2a35c4", "size": 12203, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.0.2.tgz", "integrity": "sha512-7MKlgjnkCf63E3Lv6w2FvAEgLMx3d/tNBExITcanAq7ys5U8VPWT3F6xcRjYmdNfkoQ9udoVFb1r2azSiTnD6w=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.0.2_1602250025376_0.5695392254577671"}, "_hasShrinkwrap": false, "publish_time": 1602250025582, "_cnpm_publish_time": 1602250025582, "_cnpmcore_publish_time": "2021-12-16T10:09:04.534Z"}, "7.0.1": {"name": "less-loader", "version": "7.0.1", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.3", "loader-utils": "^2.0.0", "schema-utils": "^2.7.1"}, "devDependencies": {"@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "@babel/preset-env": "^7.11.5", "@commitlint/cli": "^10.0.0", "@commitlint/config-conventional": "^10.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.1.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.8.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.1.0", "less": "^3.12.2", "lint-staged": "^10.3.0", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.1", "standard-version": "^9.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.44.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "637450180227a381b725575b911ffc9079c72dbf", "_id": "less-loader@7.0.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.8", "dist": {"shasum": "115ef3b2817f6f9e14ade6de3509b878c1bc0c13", "size": 12200, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.0.1.tgz", "integrity": "sha512-TO0+5HuDJhHArpOWnjzPMQ0xfdvyo0nxF75NNnSZ/n8b08aDuhk91/zV9sqYHzPfWvBh1PjpX/pTEEi2QnmaYA=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.0.1_1599143838726_0.37463392230405357"}, "_hasShrinkwrap": false, "publish_time": 1599143838862, "_cnpm_publish_time": 1599143838862, "_cnpmcore_publish_time": "2021-12-16T10:09:04.742Z"}, "7.0.0": {"name": "less-loader", "version": "7.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^3.5.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.3", "loader-utils": "^2.0.0", "schema-utils": "^2.7.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@commitlint/cli": "^10.0.0", "@commitlint/config-conventional": "^10.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.1.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.3.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.1.0", "less": "^3.12.2", "lint-staged": "^10.2.11", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.0", "standard-version": "^9.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.44.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "75d4a093b0364fa46e47ea5b85e243f7f076cb1f", "_id": "less-loader@7.0.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.8", "dist": {"shasum": "d2a66f4fbbb4911e587f691579ee0ae79e21cfe5", "size": 11900, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-7.0.0.tgz", "integrity": "sha512-fAgAaZHQJdX/woSMyNhvJt6bQUhpOtKODfuhk5AqgVPo6FVD3PezwHIZEtNPr4aumYoNQ1KBLnhynGX2XE/Lrg=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_7.0.0_1598356057296_0.5417018233127429"}, "_hasShrinkwrap": false, "publish_time": 1598356057427, "_cnpm_publish_time": 1598356057427, "_cnpmcore_publish_time": "2021-12-16T10:09:04.953Z"}, "6.2.0": {"name": "less-loader", "version": "6.2.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"clone": "^2.1.2", "less": "^3.11.3", "loader-utils": "^2.0.0", "schema-utils": "^2.7.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@commitlint/cli": "^9.0.1", "@commitlint/config-conventional": "^9.0.1", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.1.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.3.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.1.0", "lint-staged": "^10.2.11", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "3625cb8cc6d9782c017f3870089ba57898657eb0", "_id": "less-loader@6.2.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.5", "dist": {"shasum": "8b26f621c155b342eefc24f5bd6e9dc40c42a719", "size": 11619, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-6.2.0.tgz", "integrity": "sha512-Cl5h95/Pz/PWub/tCBgT1oNMFeH1WTD33piG80jn5jr12T4XbxZcjThwNXDQ7AG649WEynuIzO4b0+2Tn9Qolg=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_6.2.0_1593789250587_0.4031217235503095"}, "_hasShrinkwrap": false, "publish_time": 1593789250734, "_cnpm_publish_time": 1593789250734, "_cnpmcore_publish_time": "2021-12-16T10:09:05.182Z"}, "6.1.3": {"name": "less-loader", "version": "6.1.3", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"clone": "^2.1.2", "less": "^3.11.3", "loader-utils": "^2.0.0", "schema-utils": "^2.7.0"}, "devDependencies": {"@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@babel/preset-env": "^7.10.3", "@commitlint/cli": "^9.0.1", "@commitlint/config-conventional": "^9.0.1", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.0.1", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.3.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.0.1", "lint-staged": "^10.2.11", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "d3c5f5866d9da610d2edd6c3e2e5dd05ee1a7ba7", "_id": "less-loader@6.1.3", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.5", "dist": {"shasum": "f1d46e69158da1282e26f738117dd891cb13fc50", "size": 11535, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-6.1.3.tgz", "integrity": "sha512-gzG3gQd3da+E06yasOB1zR6klEEyfXp/kYZ+5Su89AVE656klCilD58QFYALqky6yL1d/ZSEKgVjkwH3meIpIQ=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_6.1.3_1593433373503_0.9266000258173273"}, "_hasShrinkwrap": false, "publish_time": 1593433373666, "_cnpm_publish_time": 1593433373666, "_cnpmcore_publish_time": "2021-12-16T10:09:05.408Z"}, "6.1.2": {"name": "less-loader", "version": "6.1.2", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"clone": "^2.1.2", "less": "^3.11.3", "loader-utils": "^2.0.0", "schema-utils": "^2.7.0"}, "devDependencies": {"@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@babel/preset-env": "^7.10.3", "@commitlint/cli": "^9.0.1", "@commitlint/config-conventional": "^9.0.1", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.0.1", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.3.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.0.1", "lint-staged": "^10.2.11", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "d5b578851a2670cbf803fd3c3f8159278b55eaa9", "_id": "less-loader@6.1.2", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.5", "dist": {"shasum": "793f2ab76dea3c4fca6a27f3effceab16fc3cd9a", "size": 11499, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-6.1.2.tgz", "integrity": "sha512-80g+EURm8H98wirYTNnIJMxVnJU9NYIXRs7rxsghL8C+UyuGzsqDXPgQcLUrNlItMwUviYeBfSOEyULI6iza+g=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_6.1.2_1592831117323_0.3636798989730896"}, "_hasShrinkwrap": false, "publish_time": 1592831117443, "_cnpm_publish_time": 1592831117443, "_cnpmcore_publish_time": "2021-12-16T10:09:05.619Z"}, "6.1.1": {"name": "less-loader", "version": "6.1.1", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"clone": "^2.1.2", "less": "^3.11.3", "loader-utils": "^2.0.0", "schema-utils": "^2.7.0"}, "devDependencies": {"@babel/cli": "^7.10.1", "@babel/core": "^7.10.2", "@babel/preset-env": "^7.10.2", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.0.1", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-prettier": "^3.1.3", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.0.1", "lint-staged": "^10.2.9", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "930b75e9f38aff7362facc018958fd036846817f", "_id": "less-loader@6.1.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.5", "dist": {"shasum": "cb17cc2b7acba46e5d6932416e0ba8fa5964fcfe", "size": 11095, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-6.1.1.tgz", "integrity": "sha512-K8WPJLbQZPszy4RD9Tsruyre4zrvEo8kE4yFfg7Gnr2H4DuYBxmNACul4UOy3wlEMBHeBElLmVLZaxaSGuL5Bw=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_6.1.1_1591891515656_0.037665262649004605"}, "_hasShrinkwrap": false, "publish_time": 1591891515878, "_cnpm_publish_time": 1591891515878, "_cnpmcore_publish_time": "2021-12-16T10:09:05.811Z"}, "6.1.0": {"name": "less-loader", "version": "6.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"clone": "^2.1.2", "less": "^3.11.1", "loader-utils": "^2.0.0", "schema-utils": "^2.6.6"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.0.1", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.3", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^26.0.1", "lint-staged": "^10.2.2", "memfs": "^3.1.2", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "cc2300744ce679de4f8799d0719aaf7998c0499b", "_id": "less-loader@6.1.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.5", "dist": {"shasum": "59fd591df408ced89a40fce11a2aea449b005631", "size": 10895, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-6.1.0.tgz", "integrity": "sha512-/jLzOwLyqJ7Kt3xg5sHHkXtOyShWwFj410K9Si9WO+/h8rmYxxkSR0A3/hFEntWudE20zZnWMtpMYnLzqTVdUA=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_6.1.0_1588856189866_0.6014981562011652"}, "_hasShrinkwrap": false, "publish_time": 1588856189995, "_cnpm_publish_time": 1588856189995, "_cnpmcore_publish_time": "2021-12-16T10:09:06.025Z"}, "6.0.0": {"name": "less-loader", "version": "6.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"clone": "^2.1.2", "less": "^3.11.1", "loader-utils": "^2.0.0", "schema-utils": "^2.6.6"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.4.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.3", "husky": "^4.2.5", "inspect-loader": "^1.0.0", "jest": "^25.4.0", "lint-staged": "^10.1.7", "memfs": "^3.1.2", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "standard-version": "^7.1.0", "strip-ansi": "^6.0.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "73740a89aef0748e27657a06b80b327c90b6f2b4", "_id": "less-loader@6.0.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.4", "dist": {"shasum": "f7833177c2fc3de5014de072d5c26eca0954c1cb", "size": 10357, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-6.0.0.tgz", "integrity": "sha512-k9KrSkjkdGCQwbKPHfbJT9AfRCmOCHCCjiQCc0v2fdVCRTlJvr1Si68Zk6Z4d4UyVkp0U/nEEdQeH4wV/jW8/g=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_6.0.0_1587746126714_0.9434238447317791"}, "_hasShrinkwrap": false, "publish_time": 1587746126838, "_cnpm_publish_time": 1587746126838, "_cnpmcore_publish_time": "2021-12-16T10:09:06.284Z"}, "5.0.0": {"name": "less-loader", "version": "5.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 4.8.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "build:fixtures": "babel-node test/helpers/createSpec.js", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint": "eslint --cache src test", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test npm run build:fixtures && jest", "test:watch": "cross-env NODE_ENV=test npm run build:fixtures && jest --watch", "test:coverage": "cross-env NODE_ENV=test npm run build:fixtures && jest --collectCoverageFrom='src/**/*.js' --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "peerDependencies": {"less": "^2.3.1 || ^3.0.0", "webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^4.0.1"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/node": "^7.2.2", "@babel/preset-env": "^7.4.4", "@commitlint/cli": "^7.5.2", "@commitlint/config-conventional": "^7.5.0", "@webpack-contrib/defaults": "^4.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.7.1", "commitlint-azure-pipelines-cli": "^1.0.1", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^5.16.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-prettier": "^3.0.1", "husky": "^2.1.0", "inspect-loader": "^1.0.0", "jest": "^24.7.1", "jest-junit": "^6.3.0", "less": "^3.9.0", "lint-staged": "^8.1.5", "memory-fs": "^0.4.1", "prettier": "^1.17.0", "standard-version": "^5.0.2", "webpack": "^4.30.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "821c12d407b36f49aacc506737a02d1dc46aee56", "_id": "less-loader@5.0.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "dist": {"shasum": "498dde3a6c6c4f887458ee9ed3f086a12ad1b466", "size": 10168, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-5.0.0.tgz", "integrity": "sha512-bquCU89mO/yWLaUq0Clk7qCsKhsF/TZpJUzETRvJa9KSVEL9SO3ovCvdEHISBhrC81OwC8QSVX7E0bzElZj9cg=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_5.0.0_1556537534919_0.03988475305048089"}, "_hasShrinkwrap": false, "publish_time": 1556537535215, "_cnpm_publish_time": 1556537535215, "_cnpmcore_publish_time": "2021-12-16T10:09:06.460Z"}, "4.1.0": {"name": "less-loader", "version": "4.1.0", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "create-spec": "babel-node test/helpers/createSpec.js", "lint": "eslint --cache src test", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "nsp check", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "pretravis:coverage": "npm run pretest", "travis:coverage": "npm run test:coverage -- --runInBand", "appveyor:test": "npm run test", "defaults": "webpack-defaults"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^3.0.0"}, "peerDependencies": {"less": "^2.3.1 || ^3.0.0", "webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^21.2.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.6.0", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.18.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^21.2.1", "less": "^3.0.1", "lint-staged": "^3.4.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "9a73fe20ce3b943a7f2600430b0e2af520ebf86a", "_id": "less-loader@4.1.0", "_npmVersion": "5.7.1", "_nodeVersion": "9.7.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "2c1352c5b09a4f84101490274fd51674de41363e", "size": 10534, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.1.0.tgz", "integrity": "sha512-KNTsgCE9tMOM70+ddxp9yyt9iHqgmSs0yTZc5XH5Wo+g80RWRIYNqE58QJKm/yMud5wZEvz50ugRDuzVIkyahg=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_4.1.0_1520594942896_0.39752937565014257"}, "_hasShrinkwrap": false, "publish_time": 1520594942989, "_cnpm_publish_time": 1520594942989, "_cnpmcore_publish_time": "2021-12-16T10:09:06.929Z"}, "4.0.6": {"name": "less-loader", "version": "4.0.6", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "main": "dist/cjs.js", "files": ["dist"], "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "nsp check", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "pretravis:coverage": "npm run pretest", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "clean": "del-cli dist"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^3.0.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^21.2.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.6.0", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.18.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^21.2.1", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "latest", "webpack-defaults": "^1.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "3ce5c2d17f1cb6d354cf50486289220eb60f156f", "_id": "less-loader@4.0.6", "_npmVersion": "5.7.1", "_nodeVersion": "9.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "7bcfbb9053181c18d57e213e87346958e02b2769", "size": 10181, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.6.tgz", "integrity": "sha512-WPFY3NMJGJna8kIxtgSu6AVG7K6uRPdfE2J7vpQqFWMN/RkOosV09rOVUt3wghNClWH2Pg7YumD1dHiv1Thfug=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_4.0.6_1519696313471_0.8325892297898443"}, "_hasShrinkwrap": false, "publish_time": 1519696313579, "_cnpm_publish_time": 1519696313579, "_cnpmcore_publish_time": "2021-12-16T10:09:07.304Z"}, "4.0.5": {"name": "less-loader", "version": "4.0.5", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "dist/cjs.js", "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "npm run test -- --runInBand", "appveyor:test": "npm run test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepublish": "npm run build", "release": "standard-version", "security": "nsp check", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "pretravis:coverage": "npm run pretest", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "clean": "del-cli dist"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^2.3.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.0.0 || ^3.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^20.0.3", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.2.2", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.18.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^20.0.3", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "latest", "webpack-defaults": "^1.0.1"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "license": "MIT", "files": ["README.md", "LICENSE", "dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "670ab182bc4984c63e1316d91c6e400157e07b58", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@4.0.5", "_shasum": "ae155a7406cac6acd293d785587fcff0f478c4dd", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "ae155a7406cac6acd293d785587fcff0f478c4dd", "size": 10098, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.5.tgz", "integrity": "sha512-d60APVM2w4KG6K/l6LyO/TrguyGNFs7eqrAMHApyMXlUEYRixUgLQk8gcp5rTPJV2dAcfwH9Zqt4PynMlR5gng=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader-4.0.5.tgz_1499651164169_0.012289232341572642"}, "directories": {}, "publish_time": 1499651165343, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499651165343, "_cnpmcore_publish_time": "2021-12-16T10:09:07.498Z"}, "4.0.4": {"name": "less-loader", "version": "4.0.4", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "dist/index.js", "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "yarn run test", "appveyor:test": "npm test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "yarn run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint-staged": "lint-staged", "prebuild": "yarn run clean", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "clean": "del-cli dist"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^2.3.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.2.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^20.0.3", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.2.2", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.18.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^20.0.3", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "^2.3.2", "webpack-defaults": "^1.0.1"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "license": "MIT", "files": ["README.md", "LICENSE", "dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "83837f7c12d1d4ef23cfad176ea5c3ddbe89974a", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@4.0.4", "_shasum": "b4a8c43843e65c67d2ea2eb1465b5c4233d5006a", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "b4a8c43843e65c67d2ea2eb1465b5c4233d5006a", "size": 9917, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.4.tgz", "integrity": "sha512-P8qMVMDfYEdkf8dVPhfIiiZXl5jt81NcNa03lC7pzsQ7maGY6mk2gW+Db6cZ0Y2hkrhKnqhBn1yXo/r8QlSE/w=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader-4.0.4.tgz_1496150993848_0.3434112824033946"}, "directories": {}, "publish_time": 1496150994859, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496150994859, "_cnpmcore_publish_time": "2021-12-16T10:09:07.755Z"}, "4.0.3": {"name": "less-loader", "version": "4.0.3", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "dist/index.js", "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "yarn run test", "appveyor:test": "npm test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security"}, "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^2.3.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.2.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^19.0.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.2.2", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.18.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^19.0.2", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "^2.3.2", "webpack-defaults": "^0.4.5"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "license": "MIT", "files": ["README.md", "LICENSE", "dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "e8b7d48a43ebbe82a818efa33efdb6fcf36c4a61", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@4.0.3", "_shasum": "d1e6462ca2f090c11248455e14b8dda4616d0521", "_from": ".", "_npmVersion": "4.4.4", "_nodeVersion": "7.7.3", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "d1e6462ca2f090c11248455e14b8dda4616d0521", "size": 13857, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.3.tgz", "integrity": "sha512-HILCpB/PgqDFogNKIzc9u4BsaaWo0tDUBjvVoBytrpxvJBQRMHkaCPMlCOIVtJrgWyM5KN8HJs1HdujqELnF5A=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-loader-4.0.3.tgz_1490897838453_0.23240861087106168"}, "directories": {}, "publish_time": 1490897840424, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490897840424, "_cnpmcore_publish_time": "2021-12-16T10:09:07.971Z"}, "4.0.2": {"name": "less-loader", "version": "4.0.2", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "dist/index.js", "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "yarn run test", "appveyor:test": "npm test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security"}, "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.0.2", "pify": "^2.3.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.2.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^19.0.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.2.1", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.17.1", "eslint-config-webpack": "^1.1.0", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^19.0.2", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "^2.2.0", "webpack-defaults": "^0.4.1"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "license": "MIT", "files": ["README.md", "LICENSE", "dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "a48295810fface3c98bcf49b01dbc85d7a38cef1", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@4.0.2", "_shasum": "3ec6909c31e07f2658266fed7b04d4d5d7e71d25", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "3ec6909c31e07f2658266fed7b04d4d5d7e71d25", "size": 13570, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.2.tgz", "integrity": "sha512-q131sgHfAvhEfjuvhrZ3OPSLOeOPVnbDUz7+OmYeLhcOd2f6HVx+ZW+aeIGFFpQrFTKrTezO2sXG4dlUqyODCw=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/less-loader-4.0.2.tgz_1490135564387_0.6537213786505163"}, "directories": {}, "publish_time": 1490135565149, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490135565149, "_cnpmcore_publish_time": "2021-12-16T10:09:08.158Z"}, "4.0.1": {"name": "less-loader", "version": "4.0.1", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "dist/index.js", "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "yarn run test", "appveyor:test": "npm test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security"}, "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "dependencies": {"clone-deep": "^0.2.4", "loader-utils": "^1.0.2", "pify": "^2.3.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.2.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^19.0.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.2.1", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.17.1", "eslint-config-webpack": "^1.1.0", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^19.0.2", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "^2.2.0", "webpack-defaults": "^0.4.1"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "license": "MIT", "files": ["README.md", "LICENSE", "dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "13dec0075a647f23ea1a92e61903ae9ac14db611", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@4.0.1", "_shasum": "bf0a5d6c5f61f272de592421d0ea87a168d1edf7", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "bf0a5d6c5f61f272de592421d0ea87a168d1edf7", "size": 13546, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.1.tgz", "integrity": "sha512-kb3kIIx1S1ZLqTGVnKvxaDNCC4K0PvX1fLKdDAFsAKW6PLJ6I/rSxq/wSjEy3qHnIa828FdfpbFJcZq0kM2FbQ=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/less-loader-4.0.1.tgz_1490097239864_0.8458297774195671"}, "directories": {}, "publish_time": 1490097240617, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490097240617, "_cnpmcore_publish_time": "2021-12-16T10:09:08.407Z"}, "4.0.0": {"name": "less-loader", "version": "4.0.0", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "dist/cjs.js", "scripts": {"create-spec": "babel-node test/helpers/createSpec.js", "pretest": "npm run create-spec", "test": "jest", "posttest": "npm run lint", "travis:test": "yarn run test", "appveyor:test": "npm test", "lint": "eslint --cache src test", "webpack-defaults": "webpack-defaults", "start": "yarn run serve:dev src", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "lint-staged": "lint-staged", "prebuild": "yarn run clean:dist", "prepublish": "yarn run build", "release": "yarn run standard-version", "security": "nsp check", "serve:dev": "nodemon $2 --exec babel-node", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security"}, "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "dependencies": {"clone-deep": "^0.2.4", "loader-utils": "^1.0.2", "pify": "^2.3.0"}, "peerDependencies": {"less": "^2.3.1", "webpack": "^2.2.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-jest": "^19.0.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.2.1", "cross-env": "^3.2.4", "del-cli": "^0.2.1", "eslint": "^3.17.1", "eslint-config-webpack": "^1.1.0", "eslint-plugin-import": "^2.2.0", "inspect-loader": "^1.0.0", "jest": "^19.0.2", "less": "^2.3.1", "lint-staged": "^3.4.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack": "^2.2.0", "webpack-defaults": "^0.4.1"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "license": "MIT", "files": ["README.md", "LICENSE", "dist"], "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "0f7d70e9d7b2f3ebf70c3505136ac2f5d7c553f1", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@4.0.0", "_shasum": "1bc89e6ed1f817972d8fbf79dc6845c35dd8a7f8", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "1bc89e6ed1f817972d8fbf79dc6845c35dd8a7f8", "size": 13447, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-4.0.0.tgz", "integrity": "sha512-SWHH1vhXWe7Tap/ARQEMEKpv5THtXRS+lVXWUeMbUPyOBPt6/s/HKO1ONVyw5rv5hsi9dREOe4yybs7HRmoy6A=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-loader-4.0.0.tgz_1490039015437_0.5040909647941589"}, "directories": {}, "publish_time": 1490039017488, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490039017488, "_cnpmcore_publish_time": "2021-12-16T10:09:08.802Z"}, "3.0.0": {"name": "less-loader", "version": "3.0.0", "author": {"name": "<PERSON> @jhnns"}, "description": "Less loader for webpack. Compiles Less to CSS.", "main": "lib/loader.js", "scripts": {"create-spec": "node test/tools/runCreateSpec.js", "pretest": "npm run create-spec", "test": "nyc --all mocha -R spec -t 10000", "posttest": "npm run lint", "travis:test": "npm test", "appveyor:test": "npm test", "lint": "eslint lib test"}, "engines": {"node": ">=4.0.0"}, "dependencies": {"clone-deep": "^0.2.4", "loader-utils": "^1.0.2"}, "peerDependencies": {"less": "^2.3.1"}, "devDependencies": {"css-loader": "^0.26.2", "eslint": "^3.17.1", "eslint-config-peerigon": "^9.0.0", "eslint-plugin-jsdoc": "^2.4.0", "extract-text-webpack-plugin": "^2.1.0", "less": "^2.6.1", "mocha": "^3.2.0", "nyc": "^10.1.2", "raw-loader": "^0.5.1", "should": "^11.2.0", "webpack": "^2.2.1"}, "repository": {"type": "git", "url": "git://github.com/webpack-contrib/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "files": ["lib", "README", "LICENSE"], "gitHead": "6df69570cc4f6da74ae3d029d3a9dc4fef4735cd", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader#readme", "_id": "less-loader@3.0.0", "_shasum": "199f7f4a79ff8a195ab47649d71c6d7109da32b9", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.1", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "199f7f4a79ff8a195ab47649d71c6d7109da32b9", "size": 6114, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-3.0.0.tgz", "integrity": "sha512-lGa3jE5tfBjgNkl/3z04GRJSb9RSEmhWJe23t7dfH/5vqley0UlIqR5lVjpO9Yxp1dUuV0SCtVWzSwHzLhh3cg=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/less-loader-3.0.0.tgz_1488942352456_0.6360611179843545"}, "directories": {}, "publish_time": 1488942353237, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488942353237, "_cnpmcore_publish_time": "2021-12-16T10:09:09.044Z"}, "2.2.3": {"name": "less-loader", "version": "2.2.3", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node --no-deprecation node_modules/.bin/_mocha -R spec", "test-source-map": "webpack --config test/sourceMap/webpack.config.js && open ./test/sourceMap/index.html"}, "peerDependencies": {"less": "^2.3.1"}, "devDependencies": {"css-loader": "^0.23.1", "enhanced-require": "^0.5.0-beta6", "extract-text-webpack-plugin": "^1.0.1", "less": "^2.6.1", "mocha": "^2.4.5", "raw-loader": "^0.5.1", "should": "^8.3.0", "webpack": "^1.12.14"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "7f0fd0fb7baabebbeaadb9507896bdc0438030b5", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader#readme", "_id": "less-loader@2.2.3", "_shasum": "b6d8f8139c8493df09d992a93a00734b08f84528", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.1", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "b6d8f8139c8493df09d992a93a00734b08f84528", "size": 22536, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-2.2.3.tgz", "integrity": "sha512-U7lgRusyqTj1TUB6OBlmow6GigLk0n5ADuTSGblCp0nkXOk+lFq/lHTDXCHkm3WydZha2FVNZivEjCEZNFJCiw=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/less-loader-2.2.3.tgz_1458935338853_0.0017933058552443981"}, "directories": {}, "publish_time": 1458935339738, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458935339738, "_cnpmcore_publish_time": "2021-12-16T10:09:09.241Z"}, "2.2.2": {"name": "less-loader", "version": "2.2.2", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "mocha -R spec", "test-source-map": "webpack --config test/sourceMap/webpack.config.js && open ./test/sourceMap/index.html"}, "peerDependencies": {"less": "^2.3.1"}, "devDependencies": {"css-loader": "^0.9.1", "enhanced-require": "^0.5.0-beta6", "extract-text-webpack-plugin": "^0.5.0", "less": "^2.3.1", "mocha": "^2.0.1", "raw-loader": "^0.5.1", "should": "^5.2.0", "webpack": "^1.1.8"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "26b8c3c5e5b60140f6d7913c6332b423f0d01f3b", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader#readme", "_id": "less-loader@2.2.2", "_shasum": "084f0e549b4bab8fa12c2580deacd27439ea8529", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "dist": {"shasum": "084f0e549b4bab8fa12c2580deacd27439ea8529", "size": 22284, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-2.2.2.tgz", "integrity": "sha512-Fa1jMkUhX3sfkd+euZs0t0QW1mUMzmYvfn5e3DUeXep2ZkDLM6JEHo0Tg9vtTOiDwCTEYrwZSHU+9MmHSqTT2g=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1448477484123, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448477484123, "_cnpmcore_publish_time": "2021-12-16T10:09:09.523Z"}, "2.2.1": {"name": "less-loader", "version": "2.2.1", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "mocha -R spec", "test-source-map": "webpack --config test/sourceMap/webpack.config.js && open ./test/sourceMap/index.html"}, "peerDependencies": {"less": "^2.3.1"}, "devDependencies": {"css-loader": "^0.9.1", "enhanced-require": "^0.5.0-beta6", "extract-text-webpack-plugin": "^0.5.0", "less": "^2.3.1", "mocha": "^2.0.1", "raw-loader": "^0.5.1", "should": "^5.2.0", "webpack": "^1.1.8"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "41c6caf2d20afea537a7202858bdff5a17728ef1", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader#readme", "_id": "less-loader@2.2.1", "_shasum": "b8571c9b62bae889c93cdd2c21ae6945d6571e46", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "dist": {"shasum": "b8571c9b62bae889c93cdd2c21ae6945d6571e46", "size": 6509, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-2.2.1.tgz", "integrity": "sha512-hLyHqs/FCIvORBgctZPOtv4ZS7rF3mu5nS03OYJyDKW3qBtvh1xf3v9G9DaFJJ6q68/kv5GbmEI8dPZVaXqM/w=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1442434895993, "_hasShrinkwrap": false, "_cnpm_publish_time": 1442434895993, "_cnpmcore_publish_time": "2021-12-16T10:09:09.720Z"}, "2.2.0": {"name": "less-loader", "version": "2.2.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "mocha -R spec", "test-source-map": "webpack --config test/sourceMap/webpack.config.js && open ./test/sourceMap/index.html"}, "peerDependencies": {"less": "^2.3.1"}, "devDependencies": {"css-loader": "^0.9.1", "enhanced-require": "^0.5.0-beta6", "extract-text-webpack-plugin": "^0.5.0", "less": "^2.3.1", "mocha": "^2.0.1", "raw-loader": "^0.5.1", "should": "^5.2.0", "webpack": "^1.1.8"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "41f9b687ed52b7f2a5b220d5da53b89a5b429f0e", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@2.2.0", "_shasum": "652ced15fa34747e6f0832ea2f3aa88b7037353b", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "dist": {"shasum": "652ced15fa34747e6f0832ea2f3aa88b7037353b", "size": 20234, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-2.2.0.tgz", "integrity": "sha512-118WZtLrfl9K+ivj7f+wtZwwjgpXpMNCC1o4P5fwwk4gxdW3GOev3YjMNRfShtnnhjn3LFGUamM48P8LXHXA6A=="}, "directories": {}, "publish_time": 1427064335100, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427064335100, "_cnpmcore_publish_time": "2021-12-16T10:09:09.965Z"}, "2.1.0": {"name": "less-loader", "version": "2.1.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "mocha -R spec", "test-source-map": "webpack --config test/sourceMap/webpack.config.js && open ./test/sourceMap/index.html"}, "peerDependencies": {"less": "^2.3.1"}, "devDependencies": {"css-loader": "^0.9.1", "enhanced-require": "^0.5.0-beta6", "extract-text-webpack-plugin": "^0.3.8", "less": "^2.3.1", "mocha": "^2.0.1", "raw-loader": "^0.5.1", "should": "^4.4.1", "webpack": "^1.1.8"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "29866afe509a70b1b8e97d9bf292c5679906ae7e", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@2.1.0", "_shasum": "fc8345b7be32b23efd30f197b898f65107c4d90b", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "jhnns", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "dist": {"shasum": "fc8345b7be32b23efd30f197b898f65107c4d90b", "size": 17477, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-2.1.0.tgz", "integrity": "sha512-jwnyFI/J1Oe9N8nPZ4k/N/49CJi4Y47IsewkZt5RU/iUjBIX762CwhI/fIX5goS3nDHPmWU9zF/g5FiLMDVThg=="}, "directories": {}, "publish_time": 1425659667415, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425659667415, "_cnpmcore_publish_time": "2021-12-16T10:09:10.180Z"}, "2.0.0": {"name": "less-loader", "version": "2.0.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "mocha -R spec"}, "peerDependencies": {"less": "^2.1.1"}, "devDependencies": {"enhanced-require": "^0.5.0-beta6", "less": "^2.1.1", "mocha": "^2.0.1", "raw-loader": "^0.5.1", "should": "^4.4.1", "webpack": "^1.1.8"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "3affb7b98f72a400c3978a74c3219358fd534e44", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@2.0.0", "_shasum": "0ff2d1275a03cda987c2c243e7f13327542240ab", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "dist": {"shasum": "0ff2d1275a03cda987c2c243e7f13327542240ab", "size": 3391, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-2.0.0.tgz", "integrity": "sha512-x0VDxZWQc8jWooQAlPD1a/rcNsHDc39WoAS5ebbizbvZYX76y+jqf6JQ30T+Yo6VGI7QgGOuz2LBAvE2b/C3gQ=="}, "directories": {}, "publish_time": 1419236504499, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419236504499, "_cnpmcore_publish_time": "2021-12-16T10:09:10.403Z"}, "0.7.8": {"name": "less-loader", "version": "0.7.8", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node node_modules/mocha/bin/mocha -R spec"}, "peerDependencies": {"less": "^1.5"}, "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2", "webpack": "^1.1.8", "raw-loader": "^0.5.1", "enhanced-require": "^0.5.0-beta6"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.5"}, "gitHead": "eb241cecbd9cf53edf402f029316cbc758c43f44", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@0.7.8", "_shasum": "8c5351a3ee22c30b5bc331fa0cc3fab1dae2e443", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "dist": {"shasum": "8c5351a3ee22c30b5bc331fa0cc3fab1dae2e443", "size": 3199, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.8.tgz", "integrity": "sha512-jlsY7rI+IvE0FUBy89B2So3QefeIpteDR53DK/0GOjTsC0YkHIjbGIlQ2pul9Ye1zHYgsvgpNOVqTCCzjX/sdg=="}, "directories": {}, "publish_time": 1417875592339, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417875592339, "_cnpmcore_publish_time": "2021-12-16T10:09:10.615Z"}, "0.7.7": {"name": "less-loader", "version": "0.7.7", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node node_modules/mocha/bin/mocha -R spec"}, "peerDependencies": {"less": "^1.5"}, "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2", "webpack": "^1.1.8", "raw-loader": "^0.5.1", "enhanced-require": "^0.5.0-beta6"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.2"}, "gitHead": "0df7586e664668f10636f2db1027387f86b6714a", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@0.7.7", "_shasum": "abe448d5c8dcf4c505b75be9973ed3749684d5d8", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "dist": {"shasum": "abe448d5c8dcf4c505b75be9973ed3749684d5d8", "size": 5481, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.7.tgz", "integrity": "sha512-ZZpVDtgdrDOIA4VIfoZHXsjqqW8e8xbGVaR/QMOASUXjrnM4pDMhqz0kPOPORwdiXDmp3yAj+7hxDgxqR2KyMQ=="}, "directories": {}, "publish_time": 1407582107305, "_hasShrinkwrap": false, "_cnpm_publish_time": 1407582107305, "_cnpmcore_publish_time": "2021-12-16T10:09:10.911Z"}, "0.7.6": {"name": "less-loader", "version": "0.7.6", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node node_modules/mocha/bin/mocha -R spec"}, "peerDependencies": {"less": "^1.5"}, "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2", "webpack": "^1.1.8", "raw-loader": "^0.5.1", "enhanced-require": "^0.5.0-beta6"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.2"}, "gitHead": "d956802da2cea84dc6af6b0b7b57b2a0d5997f7e", "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@0.7.6", "_shasum": "adfd56c79ecbbdeee9dab71a4b74bc030e7dd36b", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "dist": {"shasum": "adfd56c79ecbbdeee9dab71a4b74bc030e7dd36b", "size": 5302, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.6.tgz", "integrity": "sha512-X17rP7VrrnWiFQsIfgXZiDi6tDSQmq25MQ7kp6ygLJ+zsuKW7mFhjNWwn1XFd3zXAtDcOtYRDjgv6NFwiSz4kg=="}, "directories": {}, "publish_time": 1407133094045, "_hasShrinkwrap": false, "_cnpm_publish_time": 1407133094045, "_cnpmcore_publish_time": "2021-12-16T10:09:11.107Z"}, "0.7.5": {"name": "less-loader", "version": "0.7.5", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node node_modules/mocha/bin/mocha -R spec"}, "peerDependencies": {"less": "^1.5"}, "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2", "webpack": "^1.1.8", "raw-loader": "^0.5.1", "enhanced-require": "^0.5.0-beta6"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.2"}, "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@0.7.5", "dist": {"shasum": "5eb7bd01c3fc734b798aba5a40f7954351a00cd3", "size": 4967, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.5.tgz", "integrity": "sha512-up7hUDxgj3tykJ6XX8kt3JLonLFtzFAk+EqxPmyuWfLwEtOtp0P6uVOy1XpkCmv2OwPmt6j9n78gXBP0Dam0hQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1401255688347, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401255688347, "_cnpmcore_publish_time": "2021-12-16T10:09:11.339Z"}, "0.7.4": {"name": "less-loader", "version": "0.7.4", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node node_modules/mocha/bin/mocha -R spec"}, "peerDependencies": {"less": "^1.5"}, "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2", "less": "^1.5", "webpack": "^1.1.8", "raw-loader": "^0.5.1", "enhanced-require": "^0.5.0-beta6"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.2"}, "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@0.7.4", "dist": {"shasum": "7cb79a9bc620b8329d48936090df3d3b1cf231ab", "size": 3182, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.4.tgz", "integrity": "sha512-npfh2Tt8eXfwXAQ9GiqvkF3A4FEA/je973JHjxHXjm5JgzoP9agwskkzHQxEv9CU+w3+Phbwtc/OBKW3PRKABg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1400685381644, "_hasShrinkwrap": false, "_cnpm_publish_time": 1400685381644, "_cnpmcore_publish_time": "2021-12-16T10:09:11.582Z"}, "0.7.3": {"name": "less-loader", "version": "0.7.3", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "scripts": {"test": "node node_modules/mocha/bin/mocha -R spec"}, "peerDependencies": {"less": "^1.5"}, "devDependencies": {"should": "^3.3.1", "mocha": "^1.18.2", "less": "^1.5", "webpack": "^1.1.8", "raw-loader": "^0.5.1", "enhanced-require": "^0.5.0-beta6"}, "repository": {"type": "git", "url": "git://github.com/webpack/less-loader.git"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"loader-utils": "^0.2.2"}, "bugs": {"url": "https://github.com/webpack/less-loader/issues"}, "homepage": "https://github.com/webpack/less-loader", "_id": "less-loader@0.7.3", "dist": {"shasum": "ce77bd6ef11812f597229ada9803041d87b9cf62", "size": 3167, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.3.tgz", "integrity": "sha512-x1lB1qeXb54Ub57ZHieJhgpupulrLbaekasdfWq1CajPyJIURaqaPpNCtcKaLKIn0PIVNpROdY1Yqx99QaBFug=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399539372080, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399539372080, "_cnpmcore_publish_time": "2021-12-16T10:09:11.773Z"}, "0.7.2": {"name": "less-loader", "version": "0.7.2", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "peerDependencies": {"less": "^1.5"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "_id": "less-loader@0.7.2", "dist": {"shasum": "99df7fb62a2a8ef611db61e309371ad1bf3c8e94", "size": 1712, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.2.tgz", "integrity": "sha512-2J2DZ+E4Wv0BMenUpmP4iObRUMtJ/7ohcRMcHc3+Q9iow7wz2VQ3F3UqD0pcpns3zF2+6+VuROW3gvuEJ1uRGg=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1394720076688, "_hasShrinkwrap": false, "_cnpm_publish_time": 1394720076688, "_cnpmcore_publish_time": "2021-12-16T10:09:11.998Z"}, "0.7.1": {"name": "less-loader", "version": "0.7.1", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.5.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "_id": "less-loader@0.7.1", "dist": {"shasum": "977751009a52863fb0f5ce15a707922d330ef0ca", "size": 1668, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.1.tgz", "integrity": "sha512-6m5kT0rHlb/wXI3Wg8xe0CfzDNuEhbEGbIWlNgMzy9yhHEgDnlHVIxeMBrhFf6RKo+/TV2sKxLa5CCwhEhoWdQ=="}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390921433080, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390921433080, "_cnpmcore_publish_time": "2021-12-16T10:09:12.223Z"}, "0.7.0": {"name": "less-loader", "version": "0.7.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.5.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "_id": "less-loader@0.7.0", "dist": {"shasum": "6cef06a8f1593315b320699ef555a4dde7f39bc2", "size": 1636, "noattachment": false, "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.7.0.tgz", "integrity": "sha512-uMwanYkDqzxnF5ES6CHDaw88EoD/QtGTp1h7E0D1QeFo+o1EhX7h8TB+neI9uFD4fyPVMqJwXHpP1L1jotbCZA=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390317700342, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390317700342, "_cnpmcore_publish_time": "2021-12-16T10:09:12.480Z"}, "0.6.2": {"name": "less-loader", "version": "0.6.2", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.5.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "readmeFilename": "README.md", "_id": "less-loader@0.6.2", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.6.2.tgz", "shasum": "9de2df8060b9bd6d84bb2af036075102c5c9da23", "size": 1593, "noattachment": false, "integrity": "sha512-LGsmGztexbRawMiZKuHlP3k8IsK0tCGpL9RPmTmnc2JWkrpwRDSvSjW3mAHqZ1jbjR9NYLhaSVESC06TQxDaWg=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385739331526, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385739331526, "_cnpmcore_publish_time": "2021-12-16T10:09:12.714Z"}, "0.6.1": {"name": "less-loader", "version": "0.6.1", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.5.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "readmeFilename": "README.md", "_id": "less-loader@0.6.1", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.6.1.tgz", "shasum": "a98e73a39d31ed3755ccececb44b0fa13bd44e4c", "size": 1434, "noattachment": false, "integrity": "sha512-W24CwPh8CzIPdVa7ERP6ZZzyMIlx0mo7VA7kRDl6ddzctUlk3jw5blKPT7GTt0ksadLYiYhyEmxRmzzlbrMv2A=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383054162565, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383054162565, "_cnpmcore_publish_time": "2021-12-16T10:09:12.931Z"}, "0.6.0": {"name": "less-loader", "version": "0.6.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.5.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "readmeFilename": "README.md", "_id": "less-loader@0.6.0", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.6.0.tgz", "shasum": "452ac0387f39cf42fb4c02972731707ddbad53d2", "size": 1335, "noattachment": false, "integrity": "sha512-Nbqp4eZJPo176Lgjs21vBvyo5FFBk8mGsZdOSFiVhnS96fKNZWQVx4bchJeIXfiVt4/0Iwakw/KXHNYl0C9n/A=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1382447481208, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382447481208, "_cnpmcore_publish_time": "2021-12-16T10:09:13.153Z"}, "0.5.1": {"name": "less-loader", "version": "0.5.1", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "readmeFilename": "README.md", "_id": "less-loader@0.5.1", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.5.1.tgz", "shasum": "d75912281bcb7e71b91cd2b22cf02b1250a025ef", "size": 1335, "noattachment": false, "integrity": "sha512-K1y/FetX3mK9crFzhSOLIPFLGo4/9crFrHxMqZiuLTclEJdFKjuntpYSjuRFiKZq0zu3aV8NId9s5zCfjMpqVA=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1368186549326, "_hasShrinkwrap": false, "_cnpm_publish_time": 1368186549326, "_cnpmcore_publish_time": "2021-12-16T10:09:13.398Z"}, "0.5.0": {"name": "less-loader", "version": "0.5.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "_id": "less-loader@0.5.0", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.5.0.tgz", "shasum": "b2dea53705c1e697ea97e9482793aa3e212ad086", "size": 1351, "noattachment": false, "integrity": "sha512-JD3rdW7xuwkm4nAd8j03zqEQmEZepLKZulVSwmvAC8czubJZBRCHKjESiSV0L4M0oQsSCfsypPO5rY2gwmC8Lg=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1359704849083, "_hasShrinkwrap": false, "_cnpm_publish_time": 1359704849083, "_cnpmcore_publish_time": "2021-12-16T10:09:13.610Z"}, "0.2.2": {"name": "less-loader", "version": "0.2.2", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_id": "less-loader@0.2.2", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.2.2.tgz", "shasum": "66336491603374fd576eb5f1ce460788059958e8", "size": 1384, "noattachment": false, "integrity": "sha512-3DKZFCxrNTaN6fz2qD/r9GtEJzKSfI1Wj9yqJCuXmKYgxdzdDK1YE/+H1wqm7xsaKcK3BhNtjKZhSZZNe9eu8g=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1348662039777, "_hasShrinkwrap": false, "_cnpm_publish_time": 1348662039777, "_cnpmcore_publish_time": "2021-12-16T10:09:13.848Z"}, "0.2.1": {"name": "less-loader", "version": "0.2.1", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_id": "less-loader@0.2.1", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.2.1.tgz", "shasum": "7a29cf863b8923ec7c27fa2a550480b90e65ac93", "size": 1362, "noattachment": false, "integrity": "sha512-E6FPG0kwtIAXjubviB/0cLUubEZWC+T9s3rMTNMdPdT/JuQ8sh/2UrujlXzPJwkDfIM5sdg2bC2xrlYWj2hFKw=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1348658514489, "_hasShrinkwrap": false, "_cnpm_publish_time": 1348658514489, "_cnpmcore_publish_time": "2021-12-16T10:09:14.073Z"}, "0.2.0": {"name": "less-loader", "version": "0.2.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_id": "less-loader@0.2.0", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.2.0.tgz", "shasum": "16ec413009dca1e1c5f9f64584766ee9d2040d12", "size": 1350, "noattachment": false, "integrity": "sha512-DeOe5evXiwdb3nD4AwuGIis9vlmY5mXyMH0HpGnfnPM1WLHvw1+clSJLmW9grZn4v2KnUFYfSLNoB7uo45OAaQ=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345740477572, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345740477572, "_cnpmcore_publish_time": "2021-12-16T10:09:14.353Z"}, "0.1.3": {"name": "less-loader", "version": "0.1.3", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_id": "less-loader@0.1.3", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.1.3.tgz", "shasum": "40cc5102506ce20984952b32c4d28b39d3adc325", "size": 1275, "noattachment": false, "integrity": "sha512-DJa6yHvib/VvOwYOEUznYzZBzmyOXcg34wN00sOjf0M52cFPLKTa1cMOgLKaBZuYlB/0EAKnKWYniV7Egchk5w=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341150965852, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341150965852, "_cnpmcore_publish_time": "2021-12-16T10:09:14.570Z"}, "0.1.2": {"name": "less-loader", "version": "0.1.2", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_id": "less-loader@0.1.2", "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.1.2.tgz", "shasum": "e7da838b281d37aeaca72de627f6db753cb4433e", "size": 1262, "noattachment": false, "integrity": "sha512-nixUbhCMPvGQUZiCPSlvMK/S69thqP+5tsdUPitvw+ElqqXn7l5xTQhdCWn+xZ92ohrFDtWf84WP1yqdqKQDZQ=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341065551393, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341065551393, "_cnpmcore_publish_time": "2021-12-16T10:09:14.774Z"}, "0.1.1": {"name": "less-loader", "version": "0.1.1", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "_id": "less-loader@0.1.1", "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.1.1.tgz", "shasum": "1f167849adeef48e0b1571fcc709b8807d7bcf87", "size": 1147, "noattachment": false, "integrity": "sha512-gruRF/MgsOhEWKyaZbWYXUCuEn75omCt0NX6FPwWYCB/EnVyQbLt1VYe0A5dCjaNhHpprgpLL/2AHPspYvdtRw=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1337551385784, "_hasShrinkwrap": false, "_cnpm_publish_time": 1337551385784, "_cnpmcore_publish_time": "2021-12-16T10:09:14.992Z"}, "0.1.0": {"name": "less-loader", "version": "0.1.0", "author": {"name": "<PERSON> @sokra"}, "description": "less loader module for webpack", "dependencies": {"less": "1.3.x"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "license": "MIT", "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "_id": "less-loader@0.1.0", "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-0.1.0.tgz", "shasum": "320b90393e6bee6f296448a504af8c14572104c0", "size": 1107, "noattachment": false, "integrity": "sha512-0e1KEiEWUKiwP8H/8Bbld6Rk3qIMwwGdrdyrOzKTInfvEUoxwETlsuv7FDR+AO/KwJNBsB44UEGDwCSuiuZkHA=="}, "maintainers": [{"name": "jhnns", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1335879888442, "_hasShrinkwrap": false, "_cnpm_publish_time": 1335879888442, "_cnpmcore_publish_time": "2021-12-16T10:09:15.199Z"}, "11.0.0": {"name": "less-loader", "version": "11.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^28.1.0", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.15.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.1", "husky": "^8.0.1", "jest": "^28.1.0", "less": "^4.1.1", "less-plugin-glob": "^3.0.0", "lint-staged": "^12.4.1", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.51.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "bcf73dba191e8788573c9772669711246c9eb23a", "_id": "less-loader@11.0.0", "_nodeVersion": "18.1.0", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-9+LOWWjuoectIEx3zrfN83NAGxSUB5pWEabbbidVQVgZhN+wN68pOvuyirVlH1IK4VT1f3TmlyvAnCXh8O5KEw==", "shasum": "a31b2bc5cdfb62f1c7de9b2d01cd944c22b1a024", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-11.0.0.tgz", "fileCount": 8, "unpackedSize": 32764, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5ZXuvlX+i1QjHcZxfO0pAL3c8jDR/EQL8+W1vz16usAiEAhMy2mu0pkSLRyCGSRB32+xX32m/d+ZNHYHSGOtT7hlw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig+BNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Sg/+IQk8zz2QXEPPWTyhpm3WgXCQ4ANYIG/yV7gyrauokJ5aXjie\r\nwBT3URLCC6UtH939AbmU1yLDl2l+tN2r54WEivS2nWkHLwmhW+Sg3hy8HYFh\r\n+bJ/QvBL3ZIu/Hxzy2X7oB13uovExD5TBsGsMpg8v6cnpUYvx01hY/sKjGoC\r\ng3BGYi15h2QU1xXdXAGlBThtTaGF79fO0C3XHkPa4TOBfj/bLL1AJ9Jtx6MP\r\ncpWbhqtnjaHLyej77DvBcL9E6q2qf60rMsc4Pvy5EVmMlSuN+E6ZBtsRdUud\r\nwXbKpQcFaqzN2UKGDh0K6Wszb4UoYLLPB3IC30qG3Un091mFO7EjXbuoUT2B\r\nPD3RlKkP4GMbe3/EsgZ2AfkEj4qgRFZcvrme9RVSyiNxoLM6MLLzerA/Xz0v\r\n24M1w8Jmx2u6dQsEY/7feEAOLKjTHAcICgFnbQXbizgZxtfjhRH+yhOiizYp\r\nMbunLugXhkLsGYhCZJ1DOtYXAEEnk4SXCGsDjRItMUcESKwAYOm5A8TZwoQd\r\n9ZQ/rUxlOcbZxSVrEwppsW3oZbZqKg51ithHllSpaR1Lz4vIolTU7o3IU2qi\r\nEIilaCZlcB/X9UPmMn+ivHF93lUK/sQf7E8n6x9HU3CyvGsu6ShezE1h/3Te\r\n2I37DKg78ILVPIIB6ed/0SMcdHxZTj1AL1U=\r\n=vvea\r\n-----END PGP SIGNATURE-----\r\n", "size": 9661}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_11.0.0_1652809805176_0.3900505504625431"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-17T18:47:47.085Z"}, "11.1.0": {"name": "less-loader", "version": "11.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.3", "@babel/preset-env": "^7.19.3", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.1.2", "cross-env": "^7.0.3", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.1", "husky": "^8.0.1", "jest": "^29.1.2", "less": "^4.1.3", "less-plugin-glob": "^2.0.2", "lint-staged": "^12.5.0", "memfs": "^3.4.7", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.74.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "21caa3e55b3295490abd5624b1838fccfe309dda", "_id": "less-loader@11.1.0", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-C+uDBV7kS7W5fJlUjq5mPBeBVhYpTIm5gB09APT9o3n/ILeaXVsiSFTbZpTJCJwQ/Crczfn3DmfQFwxYusWFug==", "shasum": "a452384259bdf8e4f6d5fdcc39543609e6313f82", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-11.1.0.tgz", "fileCount": 8, "unpackedSize": 32181, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDktvlI71wpPAGym9geQXakmftV5o4B/LEJeFapQCRs6AiEAkz9NnSBg9vyJbvjw9BRilHVxn0dWZHzPg7bAPtjPbDk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP0n1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjVA/+Jx5Qu5xZ5f1+7PVkGuVmH6k/LESdZsqZjPzsfPVgRV8tUEDS\r\n6ZWuZgKYhfCmKUQKhF/bkPsTd806d6MornIOzt/R/EN2FTY6FPFuXOHcVl3b\r\nruZ1RUBaFQ9ZtuVFgU3/OkVcgeueo6YkQWtaXgC81Xugf811lkKgqHvblvCF\r\noCx6YDSUx4enDhnQVlP+Rnq7V+0jhMeVicIHYyjiq1co5kXxlmnmdRks3cKR\r\n8vZe+kDkdgF0niePLTMp5ac+F1Hd0tSGhZ6jJVnPvvsK2M/J+LHKQJGfQnRE\r\n9VH+rz2gdOoUHvZUV8QLR/aXzHT+mD3Z3B0uzBBPs68svpfntQ8hCBmnukOg\r\n7k0gWylx2qkm+FtZliD9gHSVTckKXB6XjL8FI+/3MCX1OMiZZ+KlHUpaSrR2\r\nh4wZuONyVd5ehckeu5hRx+0BNv7rDprByhCaHsOsiX906rkSKz0jLZCC2XGC\r\npnglp17seFYWTlOWTRuGlG0RM6DyAlEQEx5tJV1xT2RpkR0hY91eQY7ZRij3\r\nCKL2WH/7Qsjn/s6juGy11jjpGywFUb4noOXOc1d9FEg31lKNYspTAkWMczjs\r\nWrtoCG+ziyg4a9Wa7PO3ZPicAn3sBsajGUhmZUn+OLqRy4JhcmZs3LAF+Tsc\r\nYs/ouzUuU0d3JIP46mtw5kWHJXqyk/VA1Fo=\r\n=dMzM\r\n-----END PGP SIGNATURE-----\r\n", "size": 9436}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_11.1.0_1665092085192_0.08178823206230912"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-06T21:34:58.483Z"}, "11.1.1": {"name": "less-loader", "version": "11.1.1", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.6", "v": "^0.3.0"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.21.5", "@babel/preset-env": "^7.21.5", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.5.0", "cross-env": "^7.0.3", "cspell": "^6.31.1", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "husky": "^8.0.3", "jest": "^29.5.0", "less": "^4.1.3", "less-plugin-glob": "^2.0.2", "lint-staged": "^13.2.2", "memfs": "^3.5.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.81.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "8b9c873435d5b52440cb0d37625997b808be1265", "_id": "less-loader@11.1.1", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-sxfYqC6nYpZMF57gMmtlgbNwtH++zOCqqRnOlrvmgtNTQEyX7jFb1GlMXOpHb1+B8KZugOK72onl1SEkEgWH2g==", "shasum": "f1ba2bac94afe2a9c37baa3aa3f17efcfd56a5d5", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-11.1.1.tgz", "fileCount": 7, "unpackedSize": 32021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHx1ZW1ApQ2b3fHcn9LwY+SoYJEwYK5y7T9/kdpcwYyiAiEA/pgLXWXUWbmVGxIJAZxf45ixIGc3qp9AikIYomwr/q8="}], "size": 9432}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_11.1.1_1685232691886_0.6707514451450476"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-28T00:11:32.062Z", "publish_time": 1685232692062, "_source_registry_name": "default"}, "11.1.2": {"name": "less-loader", "version": "11.1.2", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.6"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.21.5", "@babel/preset-env": "^7.21.5", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.5.0", "cross-env": "^7.0.3", "cspell": "^6.31.1", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "husky": "^8.0.3", "jest": "^29.5.0", "less": "^4.1.3", "less-plugin-glob": "^2.0.2", "lint-staged": "^13.2.2", "memfs": "^3.5.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.81.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "401978c9c4e45741812f19f3372b0a81650d8771", "_id": "less-loader@11.1.2", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-2bSaN2j13bUh/5BuwJKuY2DDWVmfBsS6oWRe8v1mGj7F0EpcL+WyWkDQVoEfsVRE4ac5/OuP44ZCaVsXWrQQ9A==", "shasum": "ff1ea68ce60347a0a1ec67f998ac1e56b204f6da", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-11.1.2.tgz", "fileCount": 7, "unpackedSize": 31969, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDndVYpi+AC92qr04dsId3AP2eq8tR6Lb9ZbT5L5YnVrwIgf12fD7rGcPaPuJrV/JPc4/srjtGMu6PivGmxcNCxYWA="}], "size": 9407}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_11.1.2_1685546955917_0.166874492335374"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-31T15:29:16.088Z", "publish_time": 1685546956088, "_source_registry_name": "default"}, "11.1.3": {"name": "less-loader", "version": "11.1.3", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.22.1", "@babel/preset-env": "^7.22.4", "@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.5.0", "cross-env": "^7.0.3", "cspell": "^6.31.1", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "husky": "^8.0.3", "jest": "^29.5.0", "less": "^4.1.3", "less-plugin-glob": "^2.0.2", "lint-staged": "^13.2.2", "memfs": "^3.5.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "standard-version": "^9.3.1", "strip-ansi": "^7.1.0", "webpack": "^5.85.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "gitHead": "646d641711fc78ab650b672eb97a4a2cd78dc48e", "_id": "less-loader@11.1.3", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-A5b7O8dH9xpxvkosNrP0dFp2i/dISOJa9WwGF3WJflfqIERE2ybxh1BFDj5CovC2+jCE4M354mk90hN6ziXlVw==", "shasum": "1bb62d6ca9bf00a177c02793b54baac40f9be694", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-11.1.3.tgz", "fileCount": 7, "unpackedSize": 31927, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH4/yBAvLi5T3yG5cUXEaMWEeEp0g79r8qFM81hwYjlTAiBdrzj9DF5Z1M6yxOtUqebJYjQ50TgGWZbt9R7uvfXMww=="}], "size": 9396}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_11.1.3_1686262032125_0.033336956180454624"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T22:07:12.327Z", "publish_time": 1686262032327, "_source_registry_name": "default"}, "11.1.4": {"name": "less-loader", "version": "11.1.4", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.22.20", "@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^6.31.2", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.50.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.28.1", "husky": "^8.0.3", "jest": "^29.7.0", "less": "^4.2.0", "less-plugin-glob": "^2.0.2", "lint-staged": "^13.2.3", "memfs": "^3.5.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "standard-version": "^9.3.1", "strip-ansi": "^7.1.0", "webpack": "^5.88.2"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "_id": "less-loader@11.1.4", "gitHead": "5913f98fcb95d4b09a0f9ce4a8b8604d1eebd6c8", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-6/GrYaB6QcW6Vj+/9ZPgKKs6G10YZai/l/eJ4SLwbzqNTBsAqt5hSLVF47TgsiBxV1P6eAU0GYRH3YRuQU9V3A==", "shasum": "e8a070844efaefbe59b978acaf57b9d3e868cf08", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-11.1.4.tgz", "fileCount": 7, "unpackedSize": 32067, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfXwUdt73cP0So3tk4/2EoTuIfjzttzaJFnWTwuJkH1gIgKjtoyflpwYmgRaRxjH/TikM03GQJlyKaZy2EC6xSGgc="}], "size": 9455}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_11.1.4_1703700300885_0.5533970067739371"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-27T18:05:01.139Z", "publish_time": 1703700301139, "_source_registry_name": "default"}, "12.0.0": {"name": "less-loader", "version": "12.0.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^6.31.2", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.56.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "jest": "^29.7.0", "less": "^4.2.0", "less-plugin-glob": "^2.0.2", "lint-staged": "^13.2.3", "memfs": "^3.5.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "standard-version": "^9.3.1", "strip-ansi": "^7.1.0", "webpack": "^5.89.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "_id": "less-loader@12.0.0", "gitHead": "3559287d827ad76678e96cde7ea44998222a7fa2", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-fcRoWK28+eD+1PxuwNG+44V2v32IBdzsYAi0keUncHVblbpxMPWwrGlnw0wZKCdOg7O0HNwfhWNw/DrRZ45xCA==", "shasum": "d45b322c01b3e6c3784298d7e4257ce25f714516", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-12.0.0.tgz", "fileCount": 7, "unpackedSize": 32066, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8sSSUNHvf2i5m9S25PxOLGmQU/E929uJpCH64Oaak4QIgRLdUUDHeuTPRbAkAaSWcQJWgArldnX40mwkXwe9GhAk="}], "size": 9453}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_12.0.0_1705327994475_0.673003040691265"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-15T14:13:14.626Z", "publish_time": 1705327994626, "_source_registry_name": "default"}, "12.1.0": {"name": "less-loader", "version": "12.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^8.3.2", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "jest": "^29.7.0", "less": "^4.2.0", "less-plugin-glob": "^3.0.0", "lint-staged": "^15.2.0", "memfs": "^4.6.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.2", "standard-version": "^9.3.1", "strip-ansi": "^7.1.0", "webpack": "^5.89.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "_id": "less-loader@12.1.0", "gitHead": "c4928a3ffbb523e97822e6ed955a04b658164d0c", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-N/MRZA9iILOW+TQ9xoDptsSPbtBJDWshOj3LNqL+UJAYDhtoraLECiBa93DeLJUfR4m/VE6bWuxaVs40+wBXYw==", "shasum": "d13878ed094c0fef87f12bb616c760eb5b366fca", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-12.1.0.tgz", "fileCount": 7, "unpackedSize": 33530, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChXh61TXfdENhEWUJyhXP20MZCPOwG//u0pDx+WIuk2AIga35B2QYDKDgdgtSk3q3JPbKQAQ5cdIxtfPsZ63HjM20="}], "size": 9736}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_12.1.0_1705682808747_0.37110272926945176"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-19T16:46:48.926Z", "publish_time": 1705682808926, "_source_registry_name": "default"}, "12.2.0": {"name": "less-loader", "version": "12.2.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^8.3.2", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "jest": "^29.7.0", "less": "^4.2.0", "less-plugin-glob": "^3.0.0", "lint-staged": "^15.2.0", "memfs": "^4.6.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.2", "standard-version": "^9.3.1", "strip-ansi": "^7.1.0", "webpack": "^5.89.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "_id": "less-loader@12.2.0", "gitHead": "aba2f5b6149bab0900d7eed6ef9fbc52650078e0", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-MYUxjSQSBUQmowc0l5nPieOYwMzGPUaTzB6inNW/bdPEG9zOL3eAAD1Qw5ZxSPk7we5dMojHwNODYMV1hq4EVg==", "shasum": "e1e94522f6abe9e064ef396c29a3151bc6c1b6cc", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-12.2.0.tgz", "fileCount": 7, "unpackedSize": 33695, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCqntKEemU7qZUBovveEdphYzjY7jz024QxDlE0QFhigIhALPQKuySIuIXQ91gQ1pWHnfepYapA6ozkKItv8WqT+hp"}], "size": 9781}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-loader_12.2.0_1706629760819_0.13318965771112934"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-30T15:49:20.962Z", "publish_time": 1706629760962, "_source_registry_name": "default"}, "12.3.0": {"name": "less-loader", "version": "12.3.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "author": {"name": "<PERSON> @jhnns"}, "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky && npm run build", "release": "standard-version"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}, "devDependencies": {"@babel/cli": "^7.24.7", "@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^8.10.0", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^9.1.3", "jest": "^29.7.0", "less": "^4.2.0", "less-plugin-glob": "^3.0.0", "lint-staged": "^15.2.7", "memfs": "^4.9.3", "npm-run-all": "^4.1.5", "prettier": "^3.3.2", "standard-version": "^9.3.1", "strip-ansi": "^7.1.0", "webpack": "^5.92.1"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "_id": "less-loader@12.3.0", "gitHead": "ca57a9f84198ec5b64d12342ed8454d38b40bf68", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-0M6+uYulvYIWs52y0LqN4+QM9TqWAohYSNTo4htE8Z7Cn3G/qQMEmktfHmyJT23k+20kU9zHH2wrfFXkxNLtVw==", "shasum": "d4a00361568be86a97da3df4f16954b0d4c15340", "tarball": "https://registry.npmmirror.com/less-loader/-/less-loader-12.3.0.tgz", "fileCount": 7, "unpackedSize": 33936, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCpH3Z2t+D0uhXlHF4pBJ/DRhR07tfTnIT/WFi99+9nTwIhAJPTsdOf+ufubCJYXwky2q++o27mn2R8iNZuBvYdl1xv"}], "size": 9842}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/less-loader_12.3.0_1746096286213_0.3253378874885995"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-01T10:44:46.409Z", "publish_time": 1746096286409, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "homepage": "https://github.com/webpack-contrib/less-loader", "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "_source_registry_name": "default"}