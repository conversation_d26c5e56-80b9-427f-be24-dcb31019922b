{"_attachments": {}, "_id": "source-map", "_rev": "1515-61f14694963ca28f5ee3e714", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Generates and consumes source maps", "dist-tags": {"latest": "0.7.4", "next": "0.8.0-beta.0"}, "license": "BSD-3-<PERSON><PERSON>", "maintainers": [{"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "factorui.npm", "email": "<EMAIL>"}, {"name": "project-nimbus-publishing", "email": "<EMAIL>"}, {"name": "brizental", "email": "<EMAIL>"}, {"name": "moz<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "knowtheory", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tigleym", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "llisi-moz", "email": "<EMAIL>"}, {"name": "jdarcangelo-mozilla", "email": "<EMAIL>"}], "name": "source-map", "readme": "# Source Map\n\n[![Build Status](https://travis-ci.org/mozilla/source-map.png?branch=master)](https://travis-ci.org/mozilla/source-map)\n\n[![Coverage Status](https://coveralls.io/repos/github/mozilla/source-map/badge.svg)](https://coveralls.io/github/mozilla/source-map)\n\n[![NPM](https://nodei.co/npm/source-map.png?downloads=true&downloadRank=true)](https://www.npmjs.com/package/source-map)\n\nThis is a library to generate and consume the source map format\n[described here][format].\n\n[format]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit\n\n## Use with Node\n\n    $ npm install source-map\n\n## Use on the Web\n\n    <script src=\"https://unpkg.com/source-map@0.7.3/dist/source-map.js\"></script>\n    <script>\n        sourceMap.SourceMapConsumer.initialize({\n            \"lib/mappings.wasm\": \"https://unpkg.com/source-map@0.7.3/lib/mappings.wasm\"\n        });\n    </script>\n\n--------------------------------------------------------------------------------\n\n<!-- `npm run toc` to regenerate the Table of Contents -->\n\n<!-- START doctoc generated TOC please keep comment here to allow auto update -->\n<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->\n## Table of Contents\n\n- [Examples](#examples)\n  - [Consuming a source map](#consuming-a-source-map)\n  - [Generating a source map](#generating-a-source-map)\n    - [With SourceNode (high level API)](#with-sourcenode-high-level-api)\n    - [With SourceMapGenerator (low level API)](#with-sourcemapgenerator-low-level-api)\n- [API](#api)\n  - [SourceMapConsumer](#sourcemapconsumer)\n    - [SourceMapConsumer.initialize(options)](#sourcemapconsumerinitializeoptions)\n    - [new SourceMapConsumer(rawSourceMap)](#new-sourcemapconsumerrawsourcemap)\n    - [SourceMapConsumer.with](#sourcemapconsumerwith)\n    - [SourceMapConsumer.prototype.destroy()](#sourcemapconsumerprototypedestroy)\n    - [SourceMapConsumer.prototype.computeColumnSpans()](#sourcemapconsumerprototypecomputecolumnspans)\n    - [SourceMapConsumer.prototype.originalPositionFor(generatedPosition)](#sourcemapconsumerprototypeoriginalpositionforgeneratedposition)\n    - [SourceMapConsumer.prototype.generatedPositionFor(originalPosition)](#sourcemapconsumerprototypegeneratedpositionfororiginalposition)\n    - [SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)](#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition)\n    - [SourceMapConsumer.prototype.hasContentsOfAllSources()](#sourcemapconsumerprototypehascontentsofallsources)\n    - [SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])](#sourcemapconsumerprototypesourcecontentforsource-returnnullonmissing)\n    - [SourceMapConsumer.prototype.eachMapping(callback, context, order)](#sourcemapconsumerprototypeeachmappingcallback-context-order)\n  - [SourceMapGenerator](#sourcemapgenerator)\n    - [new SourceMapGenerator([startOfSourceMap])](#new-sourcemapgeneratorstartofsourcemap)\n    - [SourceMapGenerator.fromSourceMap(sourceMapConsumer)](#sourcemapgeneratorfromsourcemapsourcemapconsumer)\n    - [SourceMapGenerator.prototype.addMapping(mapping)](#sourcemapgeneratorprototypeaddmappingmapping)\n    - [SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)](#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])](#sourcemapgeneratorprototypeapplysourcemapsourcemapconsumer-sourcefile-sourcemappath)\n    - [SourceMapGenerator.prototype.toString()](#sourcemapgeneratorprototypetostring)\n  - [SourceNode](#sourcenode)\n    - [new SourceNode([line, column, source[, chunk[, name]]])](#new-sourcenodeline-column-source-chunk-name)\n    - [SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])](#sourcenodefromstringwithsourcemapcode-sourcemapconsumer-relativepath)\n    - [SourceNode.prototype.add(chunk)](#sourcenodeprototypeaddchunk)\n    - [SourceNode.prototype.prepend(chunk)](#sourcenodeprototypeprependchunk)\n    - [SourceNode.prototype.setSourceContent(sourceFile, sourceContent)](#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceNode.prototype.walk(fn)](#sourcenodeprototypewalkfn)\n    - [SourceNode.prototype.walkSourceContents(fn)](#sourcenodeprototypewalksourcecontentsfn)\n    - [SourceNode.prototype.join(sep)](#sourcenodeprototypejoinsep)\n    - [SourceNode.prototype.replaceRight(pattern, replacement)](#sourcenodeprototypereplacerightpattern-replacement)\n    - [SourceNode.prototype.toString()](#sourcenodeprototypetostring)\n    - [SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])](#sourcenodeprototypetostringwithsourcemapstartofsourcemap)\n\n<!-- END doctoc generated TOC please keep comment here to allow auto update -->\n\n## Examples\n\n### Consuming a source map\n\n```js\nconst rawSourceMap = {\n  version: 3,\n  file: 'min.js',\n  names: ['bar', 'baz', 'n'],\n  sources: ['one.js', 'two.js'],\n  sourceRoot: 'http://example.com/www/js/',\n  mappings: 'CAAC,IAAI,IAAM,SAAUA,GAClB,OAAOC,IAAID;CCDb,IAAI,IAAM,SAAUE,GAClB,OAAOA'\n};\n\nconst whatever = await SourceMapConsumer.with(rawSourceMap, null, consumer => {\n\n  console.log(consumer.sources);\n  // [ 'http://example.com/www/js/one.js',\n  //   'http://example.com/www/js/two.js' ]\n\n  console.log(consumer.originalPositionFor({\n    line: 2,\n    column: 28\n  }));\n  // { source: 'http://example.com/www/js/two.js',\n  //   line: 2,\n  //   column: 10,\n  //   name: 'n' }\n\n  console.log(consumer.generatedPositionFor({\n    source: 'http://example.com/www/js/two.js',\n    line: 2,\n    column: 10\n  }));\n  // { line: 2, column: 28 }\n\n  consumer.eachMapping(function (m) {\n    // ...\n  });\n\n  return computeWhatever();\n});\n```\n\n### Generating a source map\n\nIn depth guide:\n[**Compiling to JavaScript, and Debugging with Source Maps**](https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/)\n\n#### With SourceNode (high level API)\n\n```js\nfunction compile(ast) {\n  switch (ast.type) {\n  case 'BinaryExpression':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      [compile(ast.left), \" + \", compile(ast.right)]\n    );\n  case 'Literal':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      String(ast.value)\n    );\n  // ...\n  default:\n    throw new Error(\"Bad AST\");\n  }\n}\n\nvar ast = parse(\"40 + 2\", \"add.js\");\nconsole.log(compile(ast).toStringWithSourceMap({\n  file: 'add.js'\n}));\n// { code: '40 + 2',\n//   map: [object SourceMapGenerator] }\n```\n\n#### With SourceMapGenerator (low level API)\n\n```js\nvar map = new SourceMapGenerator({\n  file: \"source-mapped.js\"\n});\n\nmap.addMapping({\n  generated: {\n    line: 10,\n    column: 35\n  },\n  source: \"foo.js\",\n  original: {\n    line: 33,\n    column: 2\n  },\n  name: \"christopher\"\n});\n\nconsole.log(map.toString());\n// '{\"version\":3,\"file\":\"source-mapped.js\",\"sources\":[\"foo.js\"],\"names\":[\"christopher\"],\"mappings\":\";;;;;;;;;mCAgCEA\"}'\n```\n\n## API\n\nGet a reference to the module:\n\n```js\n// Node.js\nvar sourceMap = require('source-map');\n\n// Browser builds\nvar sourceMap = window.sourceMap;\n\n// Inside Firefox\nconst sourceMap = require(\"devtools/toolkit/sourcemap/source-map.js\");\n```\n\n### SourceMapConsumer\n\nA `SourceMapConsumer` instance represents a parsed source map which we can query\nfor information about the original file positions by giving it a file position\nin the generated source.\n\n#### SourceMapConsumer.initialize(options)\n\nWhen using `SourceMapConsumer` outside of node.js, for example on the Web, it\nneeds to know from what URL to load `lib/mappings.wasm`. You must inform it by\ncalling `initialize` before constructing any `SourceMapConsumer`s.\n\nThe options object has the following properties:\n\n* `\"lib/mappings.wasm\"`: A `String` containing the URL of the\n  `lib/mappings.wasm` file, or an `ArrayBuffer` with the contents of `lib/mappings.wasm`.\n\n```js\nsourceMap.SourceMapConsumer.initialize({\n  \"lib/mappings.wasm\": \"https://example.com/source-map/lib/mappings.wasm\"\n});\n```\n\n#### new SourceMapConsumer(rawSourceMap)\n\nThe only parameter is the raw source map (either as a string which can be\n`JSON.parse`'d, or an object). According to the spec, source maps have the\nfollowing attributes:\n\n* `version`: Which version of the source map spec this map is following.\n\n* `sources`: An array of URLs to the original source files.\n\n* `names`: An array of identifiers which can be referenced by individual\n  mappings.\n\n* `sourceRoot`: Optional. The URL root from which all sources are relative.\n\n* `sourcesContent`: Optional. An array of contents of the original source files.\n\n* `mappings`: A string of base64 VLQs which contain the actual mappings.\n\n* `file`: Optional. The generated filename this source map is associated with.\n\nThe promise of the constructed souce map consumer is returned.\n\nWhen the `SourceMapConsumer` will no longer be used anymore, you must call its\n`destroy` method.\n\n```js\nconst consumer = await new sourceMap.SourceMapConsumer(rawSourceMapJsonData);\ndoStuffWith(consumer);\nconsumer.destroy();\n```\n\nAlternatively, you can use `SourceMapConsumer.with` to avoid needing to remember\nto call `destroy`.\n\n#### SourceMapConsumer.with\n\nConstruct a new `SourceMapConsumer` from `rawSourceMap` and `sourceMapUrl`\n(see the `SourceMapConsumer` constructor for details. Then, invoke the `async\nfunction f(SourceMapConsumer) -> T` with the newly constructed consumer, wait\nfor `f` to complete, call `destroy` on the consumer, and return `f`'s return\nvalue.\n\nYou must not use the consumer after `f` completes!\n\nBy using `with`, you do not have to remember to manually call `destroy` on\nthe consumer, since it will be called automatically once `f` completes.\n\n```js\nconst xSquared = await SourceMapConsumer.with(\n  myRawSourceMap,\n  null,\n  async function (consumer) {\n    // Use `consumer` inside here and don't worry about remembering\n    // to call `destroy`.\n\n    const x = await whatever(consumer);\n    return x * x;\n  }\n);\n\n// You may not use that `consumer` anymore out here; it has\n// been destroyed. But you can use `xSquared`.\nconsole.log(xSquared);\n```\n\n#### SourceMapConsumer.prototype.destroy()\n\nFree this source map consumer's associated wasm data that is manually-managed.\n\n```js\nconsumer.destroy();\n```\n\nAlternatively, you can use `SourceMapConsumer.with` to avoid needing to remember\nto call `destroy`.\n\n#### SourceMapConsumer.prototype.computeColumnSpans()\n\nCompute the last column for each generated mapping. The last column is\ninclusive.\n\n```js\n// Before:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n\nconsumer.computeColumnSpans();\n\n// After:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1,\n//     lastColumn: 9 },\n//   { line: 2,\n//     column: 10,\n//     lastColumn: 19 },\n//   { line: 2,\n//     column: 20,\n//     lastColumn: Infinity } ]\n```\n\n#### SourceMapConsumer.prototype.originalPositionFor(generatedPosition)\n\nReturns the original source, line, and column information for the generated\nsource's line and column positions provided. The only argument is an object with\nthe following properties:\n\n* `line`: The line number in the generated source.  Line numbers in\n  this library are 1-based (note that the underlying source map\n  specification uses 0-based line numbers -- this library handles the\n  translation).\n\n* `column`: The column number in the generated source.  Column numbers\n  in this library are 0-based.\n\n* `bias`: Either `SourceMapConsumer.GREATEST_LOWER_BOUND` or\n  `SourceMapConsumer.LEAST_UPPER_BOUND`. Specifies whether to return the closest\n  element that is smaller than or greater than the one we are searching for,\n  respectively, if the exact element cannot be found.  Defaults to\n  `SourceMapConsumer.GREATEST_LOWER_BOUND`.\n\nand an object is returned with the following properties:\n\n* `source`: The original source file, or null if this information is not\n  available.\n\n* `line`: The line number in the original source, or null if this information is\n  not available.  The line number is 1-based.\n\n* `column`: The column number in the original source, or null if this\n  information is not available.  The column number is 0-based.\n\n* `name`: The original identifier, or null if this information is not available.\n\n```js\nconsumer.originalPositionFor({ line: 2, column: 10 })\n// { source: 'foo.coffee',\n//   line: 2,\n//   column: 2,\n//   name: null }\n\nconsumer.originalPositionFor({ line: 99999999999999999, column: 999999999999999 })\n// { source: null,\n//   line: null,\n//   column: null,\n//   name: null }\n```\n\n#### SourceMapConsumer.prototype.generatedPositionFor(originalPosition)\n\nReturns the generated line and column information for the original source,\nline, and column positions provided. The only argument is an object with\nthe following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: The column number in the original source.  The column\n  number is 0-based.\n\nand an object is returned with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.generatedPositionFor({ source: \"example.js\", line: 2, column: 10 })\n// { line: 1,\n//   column: 56 }\n```\n\n#### SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)\n\nReturns all generated line and column information for the original source, line,\nand column provided. If no column is provided, returns all mappings\ncorresponding to a either the line we are searching for or the next closest line\nthat has any mappings. Otherwise, returns all mappings corresponding to the\ngiven line and either the column we are searching for or the next closest column\nthat has any offsets.\n\nThe only argument is an object with the following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: Optional. The column number in the original source.  The\n  column number is 0-based.\n\nand an array of objects is returned, each with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.allGeneratedpositionsfor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n```\n\n#### SourceMapConsumer.prototype.hasContentsOfAllSources()\n\nReturn true if we have the embedded source content for every source listed in\nthe source map, false otherwise.\n\nIn other words, if this method returns `true`, then\n`consumer.sourceContentFor(s)` will succeed for every source `s` in\n`consumer.sources`.\n\n```js\n// ...\nif (consumer.hasContentsOfAllSources()) {\n  consumerReadyCallback(consumer);\n} else {\n  fetchSources(consumer, consumerReadyCallback);\n}\n// ...\n```\n\n#### SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])\n\nReturns the original source content for the source provided. The only\nargument is the URL of the original source file.\n\nIf the source content for the given source is not found, then an error is\nthrown. Optionally, pass `true` as the second param to have `null` returned\ninstead.\n\n```js\nconsumer.sources\n// [ \"my-cool-lib.clj\" ]\n\nconsumer.sourceContentFor(\"my-cool-lib.clj\")\n// \"...\"\n\nconsumer.sourceContentFor(\"this is not in the source map\");\n// Error: \"this is not in the source map\" is not in the source map\n\nconsumer.sourceContentFor(\"this is not in the source map\", true);\n// null\n```\n\n#### SourceMapConsumer.prototype.eachMapping(callback, context, order)\n\nIterate over each mapping between an original source/line/column and a\ngenerated line/column in this source map.\n\n* `callback`: The function that is called with each mapping. Mappings have the\n  form `{ source, generatedLine, generatedColumn, originalLine, originalColumn,\n  name }`\n\n* `context`: Optional. If specified, this object will be the value of `this`\n  every time that `callback` is called.\n\n* `order`: Either `SourceMapConsumer.GENERATED_ORDER` or\n  `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to iterate over\n  the mappings sorted by the generated file's line/column order or the\n  original's source/line/column order, respectively. Defaults to\n  `SourceMapConsumer.GENERATED_ORDER`.\n\n```js\nconsumer.eachMapping(function (m) { console.log(m); })\n// ...\n// { source: 'illmatic.js',\n//   generatedLine: 1,\n//   generatedColumn: 0,\n//   originalLine: 1,\n//   originalColumn: 0,\n//   name: null }\n// { source: 'illmatic.js',\n//   generatedLine: 2,\n//   generatedColumn: 0,\n//   originalLine: 2,\n//   originalColumn: 0,\n//   name: null }\n// ...\n```\n### SourceMapGenerator\n\nAn instance of the SourceMapGenerator represents a source map which is being\nbuilt incrementally.\n\n#### new SourceMapGenerator([startOfSourceMap])\n\nYou may pass an object with the following properties:\n\n* `file`: The filename of the generated source that this source map is\n  associated with.\n\n* `sourceRoot`: A root for all relative URLs in this source map.\n\n* `skipValidation`: Optional. When `true`, disables validation of mappings as\n  they are added. This can improve performance but should be used with\n  discretion, as a last resort. Even then, one should avoid using this flag when\n  running tests, if possible.\n\n```js\nvar generator = new sourceMap.SourceMapGenerator({\n  file: \"my-generated-javascript-file.js\",\n  sourceRoot: \"http://example.com/app/js/\"\n});\n```\n\n#### SourceMapGenerator.fromSourceMap(sourceMapConsumer)\n\nCreates a new `SourceMapGenerator` from an existing `SourceMapConsumer` instance.\n\n* `sourceMapConsumer` The SourceMap.\n\n```js\nvar generator = sourceMap.SourceMapGenerator.fromSourceMap(consumer);\n```\n\n#### SourceMapGenerator.prototype.addMapping(mapping)\n\nAdd a single mapping from original source line and column to the generated\nsource's line and column for this source map being created. The mapping object\nshould have the following properties:\n\n* `generated`: An object with the generated line and column positions.\n\n* `original`: An object with the original line and column positions.\n\n* `source`: The original source file (relative to the sourceRoot).\n\n* `name`: An optional original token name for this mapping.\n\n```js\ngenerator.addMapping({\n  source: \"module-one.scm\",\n  original: { line: 128, column: 0 },\n  generated: { line: 3, column: 456 }\n})\n```\n\n#### SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for an original source file.\n\n* `sourceFile` the URL of the original source file.\n\n* `sourceContent` the content of the source file.\n\n```js\ngenerator.setSourceContent(\"module-one.scm\",\n                           fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])\n\nApplies a SourceMap for a source file to the SourceMap.\nEach mapping to the supplied source file is rewritten using the\nsupplied SourceMap. Note: The resolution for the resulting mappings\nis the minimum of this map and the supplied map.\n\n* `sourceMapConsumer`: The SourceMap to be applied.\n\n* `sourceFile`: Optional. The filename of the source file.\n  If omitted, sourceMapConsumer.file will be used, if it exists.\n  Otherwise an error will be thrown.\n\n* `sourceMapPath`: Optional. The dirname of the path to the SourceMap\n  to be applied. If relative, it is relative to the SourceMap.\n\n  This parameter is needed when the two SourceMaps aren't in the same\n  directory, and the SourceMap to be applied contains relative source\n  paths. If so, those relative source paths need to be rewritten\n  relative to the SourceMap.\n\n  If omitted, it is assumed that both SourceMaps are in the same directory,\n  thus not needing any rewriting. (Supplying `'.'` has the same effect.)\n\n#### SourceMapGenerator.prototype.toString()\n\nRenders the source map being generated to a string.\n\n```js\ngenerator.toString()\n// '{\"version\":3,\"sources\":[\"module-one.scm\"],\"names\":[],\"mappings\":\"...snip...\",\"file\":\"my-generated-javascript-file.js\",\"sourceRoot\":\"http://example.com/app/js/\"}'\n```\n\n### SourceNode\n\nSourceNodes provide a way to abstract over interpolating and/or concatenating\nsnippets of generated JavaScript source code, while maintaining the line and\ncolumn information associated between those snippets and the original source\ncode. This is useful as the final intermediate representation a compiler might\nuse before outputting the generated JS and source map.\n\n#### new SourceNode([line, column, source[, chunk[, name]]])\n\n* `line`: The original line number associated with this source node, or null if\n  it isn't associated with an original line.  The line number is 1-based.\n\n* `column`: The original column number associated with this source node, or null\n  if it isn't associated with an original column.  The column number\n  is 0-based.\n\n* `source`: The original source's filename; null if no filename is provided.\n\n* `chunk`: Optional. Is immediately passed to `SourceNode.prototype.add`, see\n  below.\n\n* `name`: Optional. The original identifier.\n\n```js\nvar node = new SourceNode(1, 2, \"a.cpp\", [\n  new SourceNode(3, 4, \"b.cpp\", \"extern int status;\\n\"),\n  new SourceNode(5, 6, \"c.cpp\", \"std::string* make_string(size_t n);\\n\"),\n  new SourceNode(7, 8, \"d.cpp\", \"int main(int argc, char** argv) {}\\n\"),\n]);\n```\n\n#### SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])\n\nCreates a SourceNode from generated code and a SourceMapConsumer.\n\n* `code`: The generated code\n\n* `sourceMapConsumer` The SourceMap for the generated code\n\n* `relativePath` The optional path that relative sources in `sourceMapConsumer`\n  should be relative to.\n\n```js\nconst consumer = await new SourceMapConsumer(fs.readFileSync(\"path/to/my-file.js.map\", \"utf8\"));\nconst node = SourceNode.fromStringWithSourceMap(fs.readFileSync(\"path/to/my-file.js\"), consumer);\n```\n\n#### SourceNode.prototype.add(chunk)\n\nAdd a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.add(\" + \");\nnode.add(otherNode);\nnode.add([leftHandOperandNode, \" + \", rightHandOperandNode]);\n```\n\n#### SourceNode.prototype.prepend(chunk)\n\nPrepend a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.prepend(\"/** Build Id: f783haef86324gf **/\\n\\n\");\n```\n\n#### SourceNode.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for a source file. This will be added to the\n`SourceMap` in the `sourcesContent` field.\n\n* `sourceFile`: The filename of the source file\n\n* `sourceContent`: The content of the source file\n\n```js\nnode.setSourceContent(\"module-one.scm\",\n                      fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceNode.prototype.walk(fn)\n\nWalk over the tree of JS snippets in this node and its children. The walking\nfunction is called once for each snippet of JS and is passed that snippet and\nthe its original associated source's line/column location.\n\n* `fn`: The traversal function.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.walk(function (code, loc) { console.log(\"WALK:\", code, loc); })\n// WALK: uno { source: 'b.js', line: 3, column: 4, name: null }\n// WALK: dos { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: tres { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: quatro { source: 'c.js', line: 5, column: 6, name: null }\n```\n\n#### SourceNode.prototype.walkSourceContents(fn)\n\nWalk over the tree of SourceNodes. The walking function is called for each\nsource file content and is passed the filename and source content.\n\n* `fn`: The traversal function.\n\n```js\nvar a = new SourceNode(1, 2, \"a.js\", \"generated from a\");\na.setSourceContent(\"a.js\", \"original a\");\nvar b = new SourceNode(1, 2, \"b.js\", \"generated from b\");\nb.setSourceContent(\"b.js\", \"original b\");\nvar c = new SourceNode(1, 2, \"c.js\", \"generated from c\");\nc.setSourceContent(\"c.js\", \"original c\");\n\nvar node = new SourceNode(null, null, null, [a, b, c]);\nnode.walkSourceContents(function (source, contents) { console.log(\"WALK:\", source, \":\", contents); })\n// WALK: a.js : original a\n// WALK: b.js : original b\n// WALK: c.js : original c\n```\n\n#### SourceNode.prototype.join(sep)\n\nLike `Array.prototype.join` except for SourceNodes. Inserts the separator\nbetween each of this source node's children.\n\n* `sep`: The separator.\n\n```js\nvar lhs = new SourceNode(1, 2, \"a.rs\", \"my_copy\");\nvar operand = new SourceNode(3, 4, \"a.rs\", \"=\");\nvar rhs = new SourceNode(5, 6, \"a.rs\", \"orig.clone()\");\n\nvar node = new SourceNode(null, null, null, [ lhs, operand, rhs ]);\nvar joinedNode = node.join(\" \");\n```\n\n#### SourceNode.prototype.replaceRight(pattern, replacement)\n\nCall `String.prototype.replace` on the very right-most source snippet. Useful\nfor trimming white space from the end of a source node, etc.\n\n* `pattern`: The pattern to replace.\n\n* `replacement`: The thing to replace the pattern with.\n\n```js\n// Trim trailing white space.\nnode.replaceRight(/\\s*$/, \"\");\n```\n\n#### SourceNode.prototype.toString()\n\nReturn the string representation of this source node. Walks over the tree and\nconcatenates all the various snippets together to one string.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toString()\n// 'unodostresquatro'\n```\n\n#### SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])\n\nReturns the string representation of this tree of source nodes, plus a\nSourceMapGenerator which contains all the mappings between the generated and\noriginal sources.\n\nThe arguments are the same as those to `new SourceMapGenerator`.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toStringWithSourceMap({ file: \"my-output-file.js\" })\n// { code: 'unodostresquatro',\n//   map: [object SourceMapGenerator] }\n```\n", "time": {"created": "2022-01-26T13:03:16.215Z", "modified": "2025-07-13T17:08:54.293Z", "0.8.0-beta.0": "2018-11-16T00:03:32.324Z", "0.7.3": "2018-05-16T17:29:49.200Z", "0.7.2": "2018-02-26T23:30:02.206Z", "0.7.1": "2018-02-14T18:58:41.826Z", "0.7.0": "2018-01-19T21:40:28.391Z", "0.6.1": "2017-09-29T14:42:30.948Z", "0.6.0": "2017-09-27T14:32:14.978Z", "0.5.7": "2017-08-21T16:30:15.907Z", "0.5.6": "2016-05-02T17:26:02.930Z", "0.5.5": "2016-04-25T16:48:26.946Z", "0.5.4": "2016-04-22T20:08:32.822Z", "0.5.3": "2015-10-23T15:39:27.746Z", "0.5.2": "2015-10-21T16:01:45.524Z", "0.5.1": "2015-09-23T23:06:50.830Z", "0.5.0": "2015-09-10T15:13:56.564Z", "0.4.4": "2015-07-13T19:07:44.340Z", "0.4.3": "2015-07-07T20:29:01.648Z", "0.4.2": "2015-03-12T17:19:35.745Z", "0.4.1": "2015-03-02T19:05:04.884Z", "0.4.0": "2015-02-25T19:04:22.094Z", "0.3.0": "2015-02-10T18:28:31.894Z", "0.2.0": "2015-01-26T23:09:58.598Z", "0.1.43": "2015-01-08T18:25:06.813Z", "0.1.42": "2014-12-31T20:44:32.510Z", "0.1.41": "2014-12-17T19:24:20.497Z", "0.1.40": "2014-10-02T15:33:22.147Z", "0.1.39": "2014-09-09T21:00:18.550Z", "0.1.38": "2014-08-03T16:54:48.055Z", "0.1.37": "2014-07-11T18:05:50.696Z", "0.1.36": "2014-07-09T18:17:31.168Z", "0.1.35": "2014-07-08T17:01:07.625Z", "0.1.34": "2014-06-09T23:24:45.292Z", "0.1.33": "2014-02-27T02:27:53.235Z", "0.1.32": "2014-02-11T23:10:33.088Z", "0.1.31": "2013-11-01T18:40:25.890Z", "0.1.30": "2013-09-30T23:09:43.379Z", "0.1.29": "2013-08-22T00:29:20.519Z", "0.1.28": "2013-08-16T21:12:52.163Z", "0.1.27": "2013-07-22T20:37:22.337Z", "0.1.26": "2013-07-15T18:51:40.129Z", "0.1.25": "2013-06-27T19:05:03.794Z", "0.1.24": "2013-06-24T21:29:09.782Z", "0.1.23": "2013-06-14T00:24:06.756Z", "0.1.22": "2013-04-04T18:45:04.574Z", "0.1.21": "2013-04-02T04:34:47.865Z", "0.1.20": "2013-04-02T01:23:25.121Z", "0.1.19": "2013-03-25T21:43:18.536Z", "0.1.18": "2013-03-25T20:43:15.224Z", "0.1.17": "2013-03-25T20:22:39.938Z", "0.1.16": "2013-03-22T22:35:12.720Z", "0.1.15": "2013-03-22T21:39:07.305Z", "0.1.14": "2013-03-21T02:44:33.993Z", "0.1.13": "2013-03-20T20:58:36.999Z", "0.1.12": "2013-03-20T18:37:52.714Z", "0.1.11": "2013-03-20T16:56:07.393Z", "0.1.10": "2013-03-19T18:27:06.685Z", "0.1.9": "2013-03-01T01:02:37.514Z", "0.1.8": "2012-11-19T22:48:36.067Z", "0.1.7": "2012-11-02T19:09:11.172Z", "0.1.6": "2012-11-02T18:52:59.069Z", "0.1.5": "2012-10-31T01:28:00.060Z", "0.1.4": "2012-10-29T20:16:21.630Z", "0.1.3": "2012-10-12T18:43:39.522Z", "0.1.2": "2012-09-05T16:30:51.322Z", "0.1.1": "2012-06-19T22:27:12.055Z", "0.1.0": "2011-09-08T23:37:50.688Z", "0.0.0": "2011-08-30T19:45:40.104Z", "0.7.4": "2022-06-04T22:57:10.068Z"}, "versions": {"0.8.0-beta.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.8.0-beta.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "browser": {"./lib/url.js": "./lib/url-browser.js", "./lib/read-wasm.js": "./lib/read-wasm-browser.js"}, "publishConfig": {"tag": "next"}, "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint *.js lib/ test/", "prebuild": "npm run lint", "test": "node test/run-tests.js", "coverage": "nyc node test/run-tests.js", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "dev:watch": "watch 'npm run coverage' lib/ test/", "predev": "npm run setup", "dev": "npm-run-all -p --silent dev:*", "clean": "rm -rf coverage .nyc_output", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^1.3.1", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "nyc": "^11.7.1", "watch": "^1.0.2"}, "nyc": {"reporter": "html"}, "typings": "source-map", "dependencies": {"whatwg-url": "^7.0.0"}, "gitHead": "11cecea2b64ead3f93cde37a5789dc7683685a7c", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.8.0-beta.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d4c1bb42c3f7ee925f005927ba10709e0d1d1f11", "size": 60052, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.8.0-beta.0.tgz", "integrity": "sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map_0.8.0-beta.0_1542326612158_0.2007835954375803"}, "_hasShrinkwrap": false, "publish_time": 1542326612324, "_cnpm_publish_time": 1542326612324, "_cnpmcore_publish_time": "2021-12-13T10:46:43.609Z"}, "0.7.3": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.3", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint *.js lib/ test/", "prebuild": "npm run lint", "build": "webpack --color", "pretest": "npm run build", "test": "node test/run-tests.js", "precoverage": "npm run build", "coverage": "nyc node test/run-tests.js", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "dev:watch": "watch 'npm run coverage' lib/ test/", "predev": "npm run setup", "dev": "npm-run-all -p --silent dev:*", "clean": "rm -rf coverage .nyc_output", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "nyc": "^11.7.1", "watch": "^1.0.2", "webpack": "^3.10"}, "nyc": {"reporter": "html"}, "typings": "source-map", "gitHead": "b2171d58e90e64472b0e858013c0cc5f6772a83d", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.7.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5302f8169031735226544092e64981f751750383", "size": 85929, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.7.3.tgz", "integrity": "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map_0.7.3_1526491788984_0.1999301401216913"}, "_hasShrinkwrap": false, "publish_time": 1526491789200, "_cnpm_publish_time": 1526491789200, "_cnpmcore_publish_time": "2021-12-13T10:46:43.934Z"}, "0.7.2": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.2", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^3.10"}, "typings": "source-map", "gitHead": "43b2687e6e2f3cf6c22926aa473d5031fb7df89d", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.7.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "115c3e891aaa9a484869fd2b89391a225feba344", "size": 86733, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.7.2.tgz", "integrity": "sha512-NDJB/R2BS7YJG0tP9SbE4DKwKj1idLT5RJqfVYZ7dreFX7wulZT3xxVhbYKrQo9n0JkRptl51TrX/5VK3HodMA=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map_0.7.2_1519687802151_0.28165524412028553"}, "_hasShrinkwrap": false, "publish_time": 1519687802206, "_cnpm_publish_time": 1519687802206, "_cnpmcore_publish_time": "2021-12-13T10:46:44.248Z"}, "0.7.1": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.1", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^3.10"}, "typings": "source-map", "gitHead": "3181bacfb8d449ecdb7d3da8ffcd59e29bfe98d7", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.7.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "493620ba1692945d680b93862435bf0ed95a2aa4", "size": 77974, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.7.1.tgz", "integrity": "sha512-c+mPk+5wTnOd1y9oyVp0EQIo3N8k8gdxzxOo7jq1ApEN2+ew5c/UV+F1wtNHeS6Aa4JPsxiq5OU+95aMpRTGJA=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map_0.7.1_1518634721569_0.5285039948740915"}, "_hasShrinkwrap": false, "publish_time": 1518634721826, "_cnpm_publish_time": 1518634721826, "_cnpmcore_publish_time": "2021-12-13T10:46:44.744Z"}, "0.7.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^3.10"}, "typings": "source-map", "gitHead": "86d105bfda30fc71aba95c21f2e665a384b5b69f", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.7.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ebf0d13a48f3619f91891816fdda932f83a6021f", "size": 77663, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.7.0.tgz", "integrity": "sha512-JsXsCYrKzxA5kU8LanQJHIPoEY3fEH5WYSMJ8Z77ESByI18VFEoxB46H2eNHqK2nVqTRjUe5DYvNHmyT3JOd1w=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map-0.7.0.tgz_1516398028220_0.05363522795960307"}, "directories": {}, "publish_time": 1516398028391, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516398028391, "_cnpmcore_publish_time": "2021-12-13T10:46:45.182Z"}, "0.6.1": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.6.1", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "typings": "source-map", "gitHead": "ac518d2f21818146f3310557bd51c13d8cff2ba8", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.6.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "tromey", "email": "<EMAIL>"}, "dist": {"shasum": "74722af32e9614e9c287a8d0bbde48b5e2f1a263", "size": 199644, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map-0.6.1.tgz_1506696150821_0.6614652345888317"}, "directories": {}, "publish_time": 1506696150948, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506696150948, "_cnpmcore_publish_time": "2021-12-13T10:46:45.516Z"}, "0.6.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.6.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "typings": "source-map", "gitHead": "83d389f62d80344aec4f60aaba649c76cdc002bb", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.6.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "tromey", "email": "<EMAIL>"}, "dist": {"shasum": "36446016b0e5b626cf0315d6ff14b15bafb9dc10", "size": 199283, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.6.0.tgz", "integrity": "sha512-mTozplhTX4tLKIHYji92OTZzVyZvi+Z1qRZDeBvQFI2XUB89wrRoj/xXad3c9NZ1GPJXXRvB+k41PQCPTMC+aA=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map-0.6.0.tgz_1506522734674_0.8769479114562273"}, "directories": {}, "publish_time": 1506522734978, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506522734978, "_cnpmcore_publish_time": "2021-12-13T10:46:45.914Z"}, "0.5.7": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.7", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "typings": "source-map", "gitHead": "326dd955a366569759d9537ef5f0f167c89d92d2", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.7", "_shasum": "8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "tromey", "email": "<EMAIL>"}, "dist": {"shasum": "8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc", "size": 189755, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz", "integrity": "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map-0.5.7.tgz_1503333015516_0.19087489508092403"}, "directories": {}, "publish_time": 1503333015907, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503333015907, "_cnpmcore_publish_time": "2021-12-13T10:46:46.272Z"}, "0.5.6": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.6", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "gitHead": "aa0398ced67beebea34f0d36f766505656c344f6", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.6", "_shasum": "75ce38f52bf0733c5a7f0c118d81334a2bb5f412", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "75ce38f52bf0733c5a7f0c118d81334a2bb5f412", "size": 186942, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.6.tgz", "integrity": "sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/source-map-0.5.6.tgz_1462209962516_0.9263619624543935"}, "directories": {}, "publish_time": 1462209962930, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462209962930, "_cnpmcore_publish_time": "2021-12-13T10:46:46.670Z"}, "0.5.5": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.5", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "gitHead": "d90ac8b806d4130b4bfdf2fc9482dfd5f7930692", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.5", "_shasum": "d9230c5a63dd59f1ebaecabf78e900302b892c49", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "d9230c5a63dd59f1ebaecabf78e900302b892c49", "size": 186777, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.5.tgz", "integrity": "sha512-ArRdlQmLJcNU2myOsSBipgolcBLAeYFpI0VA9jCa9Zx1Uta1+ocOgwH44b2JozO+As5TszFy85IRBCGefVCD/w=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/source-map-0.5.5.tgz_1461602906442_0.309012166922912"}, "directories": {}, "publish_time": 1461602906946, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461602906946, "_cnpmcore_publish_time": "2021-12-13T10:46:47.125Z"}, "0.5.4": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.4", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "gitHead": "7c4905497b5f0a62fd6a3d3f786ba8eac0db4615", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.4", "_shasum": "2beb18a2ab8abd88320a72178c671f0e4c5def28", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "2beb18a2ab8abd88320a72178c671f0e4c5def28", "size": 188180, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.4.tgz", "integrity": "sha512-GrSnGzRVA7tmdZ8LixSZWOjZJ6iSXV8UBl+p3pnFIOu4rQdh/zMWQVESPOD017+p4c9eRHR/mFTA5dO3D9zNKw=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/source-map-0.5.4.tgz_1461355709333_0.3527497702743858"}, "directories": {}, "publish_time": 1461355712822, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461355712822, "_cnpmcore_publish_time": "2021-12-13T10:46:47.585Z"}, "0.5.3": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.3", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.3", "_shasum": "82674b85a71b0be76c3e7416d15e9f5252eb3be0", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "82674b85a71b0be76c3e7416d15e9f5252eb3be0", "size": 182981, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.3.tgz", "integrity": "sha512-d/a1s6Jnb1bJISdw0+Q5YgNlczWabJWSL1jeKGH2p1Je4A0lIoiKI5YC/12RUgJ8ugm+XPoM9PCG7Kl0K9ABLA=="}, "directories": {}, "publish_time": 1445614767746, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445614767746, "_cnpmcore_publish_time": "2021-12-13T10:46:48.051Z"}, "0.5.2": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.2", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/", "dist/"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.2", "_shasum": "ebb6e5e87424f497ad6f972c6389eacf3c0cbe00", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "ebb6e5e87424f497ad6f972c6389eacf3c0cbe00", "size": 577114, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.2.tgz", "integrity": "sha512-pmmcvfMW3TBExWSMSi933VuLDKMpboWoD6ztiW62Ke2bFUrJnthmOAWwMyi4uu93skkhGFpGTQOQb+DtFmXd9g=="}, "directories": {}, "publish_time": 1445443305524, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445443305524, "_cnpmcore_publish_time": "2021-12-13T10:46:48.668Z"}, "0.5.1": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.1", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.1", "_shasum": "5fbd40a1cde485feab4d36460b8f496edb362ecf", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "5fbd40a1cde485feab4d36460b8f496edb362ecf", "size": 30596, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.1.tgz", "integrity": "sha512-tqh0zAvqU01jNbVwh9+BBxbX3BZWF9fhOUQcRSCqSbted8+MkQzwRnP/YGF82i80d9F31WEfzvaneWSJl7pY/g=="}, "directories": {}, "publish_time": 1443049610830, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443049610830, "_cnpmcore_publish_time": "2021-12-13T10:46:49.115Z"}, "0.5.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.5.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "lib/"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.5.0", "_shasum": "0fe96503ac86a5adb5de63f4e412ae4872cdbe86", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "0fe96503ac86a5adb5de63f4e412ae4872cdbe86", "size": 30504, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.5.0.tgz", "integrity": "sha512-gjGnxNN0K+/Pr4Mi4fs/pOtda10dKB6Wn9QvjOrH6v5TWsI7ghHuJUHoIgyM6DkUL5kr2GtPFGererzKpMBWfA=="}, "directories": {}, "publish_time": 1441898036564, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441898036564, "_cnpmcore_publish_time": "2021-12-13T10:46:49.555Z"}, "0.4.4": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.4.4", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "files": ["lib/", "build/"], "engines": {"node": ">=0.8.0"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.4.4", "_shasum": "eba4f5da9c0dc999de68032d8b4f76173652036b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "eba4f5da9c0dc999de68032d8b4f76173652036b", "size": 31659, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.4.4.tgz", "integrity": "sha512-Y8nIfcb1s/7DcobUz1yOO1GSp7gyL+D9zLHDehT7iRESqGSxjJ448Sg7rvfgsRJCnKLdSl11uGf0s9X80cH0/A=="}, "publish_time": 1436814464340, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436814464340, "_cnpmcore_publish_time": "2021-12-13T10:46:50.010Z"}, "0.4.3": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.4.3", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.4.3", "_shasum": "8e8408163c922cf92166f3609156703b1c5d254e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "8e8408163c922cf92166f3609156703b1c5d254e", "size": 104336, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.4.3.tgz", "integrity": "sha512-x4Y6Wtdxhw1UdLq9nDNiGdi7+ZWXmdQxHo64tpFnFYADhpU3jb5nePxPKOvgOvy62dGRe7PXDv+1w69bxZUKtw=="}, "publish_time": 1436300941648, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436300941648, "_cnpmcore_publish_time": "2021-12-13T10:46:50.503Z"}, "0.4.2": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.4.2", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.4.2", "_shasum": "dc9f3114394ab7c1f9782972f3d11820fff06f1f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "dc9f3114394ab7c1f9782972f3d11820fff06f1f", "size": 97456, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.4.2.tgz", "integrity": "sha512-gPYyTI9C5vzUyavo23otNfekXxaSM7vUlpkPaymnD3mMtrvb9hHK7fukct2GuMboxmHjeoeJ0JelmZcV6DcAPw=="}, "publish_time": 1426180775745, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426180775745, "_cnpmcore_publish_time": "2021-12-13T10:46:51.049Z"}, "0.4.1": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.4.1", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.4.1", "_shasum": "db90f97e821c6cf5ba5efa76f33c1bb1c88dd32e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "db90f97e821c6cf5ba5efa76f33c1bb1c88dd32e", "size": 2079734, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.4.1.tgz", "integrity": "sha512-9YpkrzyaARBEr90PuJ/beaN3X7kmQ0OMzjaHtZ6sydGfBuGXLpFCtlRdKZkLT9B7QTc5GxK1K9Cp9250HFYdoA=="}, "publish_time": 1425323104884, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425323104884, "_cnpmcore_publish_time": "2021-12-13T10:46:51.828Z"}, "0.4.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.4.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.4.0", "_shasum": "c6ddd824fca4ccad4329f8f37903c1bc902c2bb2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "c6ddd824fca4ccad4329f8f37903c1bc902c2bb2", "size": 2079536, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.4.0.tgz", "integrity": "sha512-Dgg/U/7dtOK/VXKCu9UZlSPchwEoyJwhbic9qXXAe1fpjJ6HpmXmySrgsOX1oWgJupTkC/JYaZAe6/zTlUgMEg=="}, "publish_time": 1424891062094, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424891062094, "_cnpmcore_publish_time": "2021-12-13T10:46:52.599Z"}, "0.3.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.3.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.3.0", "_shasum": "8586fb9a5a005e5b501e21cd18b6f21b457ad1f9", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "8586fb9a5a005e5b501e21cd18b6f21b457ad1f9", "size": 45179, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.3.0.tgz", "integrity": "sha512-jz8leTIGS8+qJywWiO9mKza0hJxexdeIYXhDHw9avTQcXSNAGk3hiiRMpmI2Qf9dOrZDrDpgH9VNefzuacWC9A=="}, "publish_time": 1423592911894, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423592911894, "_cnpmcore_publish_time": "2021-12-13T10:46:53.248Z"}, "0.2.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.2.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.2.0", "_shasum": "dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d", "size": 44789, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.2.0.tgz", "integrity": "sha512-CBdZ2oa/BHhS4xj5DlhjWNHcan57/5YuvfdLf17iVmIpd9KRm+DFLmC6nBNj+6Ua7Kt3TmOjDpQT1aTYOQtoUA=="}, "publish_time": 1422313798598, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422313798598, "_cnpmcore_publish_time": "2021-12-13T10:46:53.813Z"}, "0.1.43": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.43", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.43", "_shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "size": 40878, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.43.tgz", "integrity": "sha512-VtCvB9SIQhk3aF6h+N85EaqIaBFIAfZ9Cu+NJHHVvc8BbEcnvDcFw6sqQ2dQrT6SlOrZq3tIvyD9+EGq/lJryQ=="}, "publish_time": 1420741506813, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420741506813, "_cnpmcore_publish_time": "2021-12-13T10:46:54.330Z"}, "0.1.42": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.42", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.42", "_shasum": "37c2958c79fee8d919173e5a7ccbd933b2ff835e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "37c2958c79fee8d919173e5a7ccbd933b2ff835e", "size": 39967, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.42.tgz", "integrity": "sha512-xgd1jQodLuEIH270zvt+ZsR2X+rpPDxhnzzpyup0eNZWMKAO0q4ksUgT6WBjIkiN6Zpqb+AVf9B73VDJYexEog=="}, "publish_time": 1420058672510, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420058672510, "_cnpmcore_publish_time": "2021-12-13T10:46:54.989Z"}, "0.1.41": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.41", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.41", "_shasum": "d6cc9c8a9bb5d995dc69b38457bd4b1747327f5a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "d6cc9c8a9bb5d995dc69b38457bd4b1747327f5a", "size": 39783, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.41.tgz", "integrity": "sha512-5+m6cR4TZwyCNqossuf4ngNJFawVzRQL8Tvm3S9qGBGd0kDku51MqFdZu0wDU3WsX/NJSA2wRpJ3YxiqsfzS6w=="}, "publish_time": 1418844260497, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418844260497, "_cnpmcore_publish_time": "2021-12-13T10:46:55.537Z"}, "0.1.40": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.40", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.40", "_shasum": "7e0ee49ec0452aa9ac2b93ad5ae54ef33e82b37f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "7e0ee49ec0452aa9ac2b93ad5ae54ef33e82b37f", "size": 38577, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.40.tgz", "integrity": "sha512-HqDIJdduqqp2qy0BKV+19pUcMBIuJOKTUfEnFWAtHvLxptZeqBl6jRxCxFYhQGrvq9fJE2HTICp2BWjKLdWPWQ=="}, "publish_time": 1412264002147, "_hasShrinkwrap": false, "_cnpm_publish_time": 1412264002147, "_cnpmcore_publish_time": "2021-12-13T10:46:56.120Z"}, "0.1.39": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.39", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.39", "_shasum": "64ad329c4761ab956ff7d011c6b205aeb66a2d4a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "64ad329c4761ab956ff7d011c6b205aeb66a2d4a", "size": 37937, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.39.tgz", "integrity": "sha512-MiCty8yXfwPQ42cqHbPUbS3M4DK6Wt+oq6MXitiShP0FI39BZdvIyjWY6+2DzUGfzbyZ8MqPtBqNfypsJWlSYQ=="}, "publish_time": 1410296418550, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410296418550, "_cnpmcore_publish_time": "2021-12-13T10:46:56.711Z"}, "0.1.38": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.38", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.38", "_shasum": "f93a6f9d96a5b9cf5494c043497d9542f9fa6b33", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "f93a6f9d96a5b9cf5494c043497d9542f9fa6b33", "size": 37786, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.38.tgz", "integrity": "sha512-gATsj0h69ctEQbieEXcmysXmDR/eZ95fGPTiJMGCviBtSC9vf/Ljmvepv2RTpF8AiA4oV6PCujqQ40d2yvPkbg=="}, "publish_time": 1407084888055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1407084888055, "_cnpmcore_publish_time": "2021-12-13T10:46:57.495Z"}, "0.1.37": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.37", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.37", "_shasum": "511fa6ed1685cb37e70ae1de2966405096054832", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "511fa6ed1685cb37e70ae1de2966405096054832", "size": 37827, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.37.tgz", "integrity": "sha512-+ocd2JnHadJ62zqa5xp6IRu2lJvFKpP2SeuHII1o4QBf0OCLBZ5UnVEuIy8K3XWAp9KhHnFXj98/Bcfvo1lpxw=="}, "publish_time": 1405101950696, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405101950696, "_cnpmcore_publish_time": "2021-12-13T10:46:58.164Z"}, "0.1.36": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.36", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.36", "_shasum": "7af95322decf58cc78a18d5a0d4ade258ae31c35", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "7af95322decf58cc78a18d5a0d4ade258ae31c35", "size": 36920, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.36.tgz", "integrity": "sha512-4XWYWF3SDexPHeV9BgFQSDat7ukmnud5pmgx6GtA7RiNOJQBmirFfY+P34mlaG3K/Fjc5HXogMi/wWEKBRVcuQ=="}, "publish_time": 1404929851168, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404929851168, "_cnpmcore_publish_time": "2021-12-13T10:46:58.830Z"}, "0.1.35": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.35", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.35", "_shasum": "06a1b58b57ac8176476b74f3c1ece4925cea2e8c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "06a1b58b57ac8176476b74f3c1ece4925cea2e8c", "size": 36817, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.35.tgz", "integrity": "sha512-dmnvgJQlZ1uj7hU1TNauhy3iFsXGQwgr5viox9EZKk2LWuwE5uzQT1H6fGarddmI3pcSquRDWONb56fR5iAkMA=="}, "publish_time": 1404838867625, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404838867625, "_cnpmcore_publish_time": "2021-12-13T10:46:59.465Z"}, "0.1.34": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.34", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.1.34", "_shasum": "a7cfe89aec7b1682c3b198d0acfb47d7d090566b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "dist": {"shasum": "a7cfe89aec7b1682c3b198d0acfb47d7d090566b", "size": 35646, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.34.tgz", "integrity": "sha512-yfCwDj0vR9RTwt3pEzglgb3ZgmcXHt6DjG3bjJvzPwTL+5zDQ2MhmSzAcTy0GTiQuCiriSWXvWM1/NhKdXuoQA=="}, "publish_time": 1402356285292, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402356285292, "_cnpmcore_publish_time": "2021-12-13T10:47:00.151Z"}, "0.1.33": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.33", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_id": "source-map@0.1.33", "dist": {"shasum": "c659297a73af18c073b0aa2e7cc91e316b5c570c", "size": 34906, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.33.tgz", "integrity": "sha512-bled/6w7dhwC1MDeEDF+y3ojAzDZsz0N2ki61/YaGVQu+dGsm+VOvtGw2ZLomckp66Fuas9D9zsQkQqqgXnvhw=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1393468073235, "_hasShrinkwrap": false, "_cnpm_publish_time": 1393468073235, "_cnpmcore_publish_time": "2021-12-13T10:47:00.777Z"}, "0.1.32": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.32", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_id": "source-map@0.1.32", "dist": {"shasum": "c8b6c167797ba4740a8ea33252162ff08591b266", "size": 31843, "noattachment": false, "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.32.tgz", "integrity": "sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1392160233088, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392160233088, "_cnpmcore_publish_time": "2021-12-13T10:47:01.474Z"}, "0.1.31": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.31", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.31", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.31.tgz", "shasum": "9f704d0d69d9e138a81badf6ebb4fde33d151c61", "size": 31718, "noattachment": false, "integrity": "sha512-qFALUiKHo35Duky0Ubmb5YKj9b3c6CcgGNGeI60sd6Nn3KaY7h9fclEOcCVk0hwszwYYP6+X2/jpS5hHqqVuig=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1383331225890, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383331225890, "_cnpmcore_publish_time": "2021-12-13T10:47:02.320Z"}, "0.1.30": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.30", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.30", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.30.tgz", "shasum": "182726b50671d8fccaefc5ec35bf2a65c1956afb", "size": 31252, "noattachment": false, "integrity": "sha512-azjGocEF+vixFej9v+zXJpP1YrTdMWp+lkJbRDiMzEqxjdTaQIFeKFBWVUucB5bwIzovwuRiVjie/l1du4vKkQ=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1380582583379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380582583379, "_cnpmcore_publish_time": "2021-12-13T10:47:03.115Z"}, "0.1.29": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.29", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.29", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.29.tgz", "shasum": "39d571a0988fb7a548a676c4de72db78914d173c", "size": 29769, "noattachment": false, "integrity": "sha512-/XGYfd2ykeYNmXopQsGZK+oMxR7A48VBHK7u8610061aivyuopi86C87gG2yfQeTsQxQDw0N9cyP5JQIbnn9UA=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1377131360519, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377131360519, "_cnpmcore_publish_time": "2021-12-13T10:47:03.919Z"}, "0.1.28": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.28", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.28", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.28.tgz", "shasum": "9cae9d9b8352fb030f77c4e12226cc28cb251f39", "size": 29226, "noattachment": false, "integrity": "sha512-5eK1mz3ehRAIydz3tlkhPEZEZUZSCpnWevQzSq4d8yheVzFrsvDDJbhc4SUW7jpKWaZk52lbvO05tYWtZbqu8g=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1376687572163, "_hasShrinkwrap": false, "_cnpm_publish_time": 1376687572163, "_cnpmcore_publish_time": "2021-12-13T10:47:04.697Z"}, "0.1.27": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.27", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.27", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.27.tgz", "shasum": "f114e06a8b5c05cbc51aa1fa600e728162455eda", "size": 28874, "noattachment": false, "integrity": "sha512-Q39uRzHeN1Wtw63pQXMErr4s8U7IEEZ0HPOlTTS1B+cd8zzU6PRMBUVOy4jHxaqJKVDa1/TFhoE+y31iVbwLgA=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1374525442337, "_hasShrinkwrap": false, "_cnpm_publish_time": 1374525442337, "_cnpmcore_publish_time": "2021-12-13T10:47:05.488Z"}, "0.1.26": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.26", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.26", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.26.tgz", "shasum": "4cce2dcb5fa02cfe1b4cf32cc83962b1b3997560", "size": 28811, "noattachment": false, "integrity": "sha512-s+AbKwK602CKyafNAufmKM2/nw89coKgesppy/uhN8OrZwOD7kQsHprCJxllmYjCfvJW/2AMxL5/wGUWyXgs/g=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1373914300129, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373914300129, "_cnpmcore_publish_time": "2021-12-13T10:47:06.189Z"}, "0.1.25": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.25", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.25", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.25.tgz", "shasum": "5851545c1f4a40243829065c20e6f40b023fba1a", "size": 28665, "noattachment": false, "integrity": "sha512-3WIHR+Lnqu8KVh+ShT1Y9wSrF8tx/Df2zgDcnNhvE9M/2STspZE0cGIg0wQSG3cVwjSjbkgL5cF4g6FCCFB5Jg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1372359903794, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372359903794, "_cnpmcore_publish_time": "2021-12-13T10:47:07.085Z"}, "0.1.24": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.24", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "github.com/usrbincc"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.24", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.24.tgz", "shasum": "5b533dd08948de2d8c3b002a9c31842cb1b35103", "size": 28653, "noattachment": false, "integrity": "sha512-/sRNqTalV7TVLik0wM5G9PR2LjAbsGJh5ulQ5DtIoxeKvTi3TTphEgawVgmiQUvDct6Ja6WCQcxkX8R3wVvlfg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1372109349782, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372109349782, "_cnpmcore_publish_time": "2021-12-13T10:47:07.862Z"}, "0.1.23": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.23", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "github.com/usrbincc"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.23", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.23.tgz", "shasum": "079f8fa7867f318b48cf5d4c716ab6d4dc859be9", "size": 28240, "noattachment": false, "integrity": "sha512-50T9Uzx6Mw1Ikd9pxStyb5DIGhvvMQdVdwy93YlgVx3eS+Jcq+3hO5ynAv6TJGnCnIEwSM4itCjIvxoHk0GGRA=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1371169446756, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371169446756, "_cnpmcore_publish_time": "2021-12-13T10:47:08.774Z"}, "0.1.22": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.22", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "github.com/usrbincc"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.22", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.22.tgz", "shasum": "425906162f81bf110552ccc9931dba079e9f1341", "size": 28014, "noattachment": false, "integrity": "sha512-1Sgz8HqDxL9PI+43U01qMUI9Z1jZePsEynvzgfGcghVsNWDFh/Z5hQ4f12gQ6O6Ly7lHuj68KMX8csSCFyoXKQ=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1365101104574, "_hasShrinkwrap": false, "_cnpm_publish_time": 1365101104574, "_cnpmcore_publish_time": "2021-12-13T10:47:09.623Z"}, "0.1.21": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.21", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.21", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.21.tgz", "shasum": "ecd4efc4c3579de66b95913dda08c72ea0256469", "size": 27462, "noattachment": false, "integrity": "sha512-1xiMBfkTOUQR/bLyb0e8rAmQSiHkyqhPuV8gSKZ0ikd5RXt7JumOm/BXq6YC0NWoYewyzbLNiy2j0vCfbMZjgw=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1364877287865, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364877287865, "_cnpmcore_publish_time": "2021-12-13T10:47:10.527Z"}, "0.1.20": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.20", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.20", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.20.tgz", "shasum": "e41ce3288cac4c4f6eca181b53ae12558e7ad02a", "size": 27188, "noattachment": false, "integrity": "sha512-QaCwjGTrTg+ubYG/d1pNkBo0a0EtNB0tkyNklaPgJr1Ly+8o3lwQAaJCSAzjx2MpdyKm95+K11ICURv+17LUeQ=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1364865805121, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364865805121, "_cnpmcore_publish_time": "2021-12-13T10:47:11.442Z"}, "0.1.19": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.19", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js"}, "readmeFilename": "README.md", "_id": "source-map@0.1.19", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.19.tgz", "shasum": "61ce72499b6caaaa15484bd4b5dc718b0aebf76a", "size": 26139, "noattachment": false, "integrity": "sha512-9+SEkrrPKHBdX9FCW7v59Q4vlfV1X4Yi4hgMt+e8Eo5wKPpQQC6Z+evh0/1wTtjbTZ3fg/HPHioCkuiGR6VfxA=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1364247798536, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364247798536, "_cnpmcore_publish_time": "2021-12-13T10:47:12.396Z"}, "0.1.18": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.18", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.18", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.18.tgz", "shasum": "6645e412fe48f748ba3a4ae56ed53f38a550f17e", "size": 26036, "noattachment": false, "integrity": "sha512-NQ+E49peTslQTpOwkL8tZUVefUTiI1QKjynD2sy4Qa6GvvAJWP2y0pr441KgYM6pRFgonoJVsWIEqMvp+C4zmQ=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1364244195224, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364244195224, "_cnpmcore_publish_time": "2021-12-13T10:47:13.382Z"}, "0.1.17": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.17", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.17", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.17.tgz", "shasum": "1b34e5b30616d48137d604b996cf5585dc78e204", "size": 25959, "noattachment": false, "integrity": "sha512-GZTADpE9eY07Hw7Hr4zDeOZy4vpVuBIBOpGXNOH6XPU/c/FaDuRk/VnBwP5LwBBMWRoPOZpTWu6QlYbhaU5ZFA=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1364242959938, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364242959938, "_cnpmcore_publish_time": "2021-12-13T10:47:14.386Z"}, "0.1.16": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.16", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.16", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.16.tgz", "shasum": "4a1b371e9abd69a45fa698a1d281e2b213333dca", "size": 24803, "noattachment": false, "integrity": "sha512-C9K3IisBzhg0YWJeErUpSjsRX8H2mAWGDPLM5DdXMIgnBfc6AGP8XU7wRiHCbjmsiMNnQAdLX0Y5hsaqzl/Jdg=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363991712720, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363991712720, "_cnpmcore_publish_time": "2021-12-13T10:47:15.385Z"}, "0.1.15": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.15", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.15", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.15.tgz", "shasum": "4eed4690db25839af3505ea4d018b6f5523f0807", "size": 24790, "noattachment": false, "integrity": "sha512-QrEvXkIggRYh0/fUiPtm6M5voOYUwDeRfL0UAgp307Z+/PnepfZHmuYLn3D8yer1Mn0+crCtYWNepAh6uW0JMw=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363988347305, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363988347305, "_cnpmcore_publish_time": "2021-12-13T10:47:17.426Z"}, "0.1.14": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.14", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.14", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.14.tgz", "shasum": "81fb3b85f54abb1ad6a8e3f92e55b8fcd3eb836d", "size": 23459, "noattachment": false, "integrity": "sha512-b1WHWKyPmI67BEjEUbqEEBFh7Q3uFJaCW+g8BYur7kAv4Smr98qS9qhIhs8mW+gNdm5Qrhkxl+e529Cju8PuXw=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363833873993, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363833873993, "_cnpmcore_publish_time": "2021-12-13T10:47:18.431Z"}, "0.1.13": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.13", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.13", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.13.tgz", "shasum": "cc3d10be14dcafbd8f8259546438f19b72995a1c", "size": 23123, "noattachment": false, "integrity": "sha512-f/JBMURtDPyVWfllJchypSnV3thR+ChdVUwphyycImeLfRB5pwuiNLg0ArenxeZClQ3rQ6CKyf3//71gMX31vQ=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363813116999, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363813116999, "_cnpmcore_publish_time": "2021-12-13T10:47:19.322Z"}, "0.1.12": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.12", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.12", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.12.tgz", "shasum": "55d5a5c58d98ffadacc4a8a7fbd7c9d4e459abd2", "size": 22717, "noattachment": false, "integrity": "sha512-xQVA+H4jiqGgAmkJIUFtAhLqLwFxzYn2QTBPW3bCXPlegOmEQ5QEicePbrZj0iprB6nlEEPiY6TN9ymWWoLaeg=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363804672714, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363804672714, "_cnpmcore_publish_time": "2021-12-13T10:47:20.245Z"}, "0.1.11": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.11", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.11", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.11.tgz", "shasum": "2eef2fd65a74c179880ae5ee6975d99ce21eb7b4", "size": 22640, "noattachment": false, "integrity": "sha512-/32+GwjyioXGrcJ+8FK3/WAVIfC1EFo8pgeiuMouTFrCTHZW2pc5qBToOuZgBR4RLTSRnUhcPNK4Fx5KwHV9bA=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363798567393, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363798567393, "_cnpmcore_publish_time": "2021-12-13T10:47:21.328Z"}, "0.1.10": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.10", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.10", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.10.tgz", "shasum": "0bdafd2e1e97d147a862b7c7d1b26387ecc93aa9", "size": 22533, "noattachment": false, "integrity": "sha512-YW/WjLPxnMkvpA7uGMqg0H6kLFQ//zK+C4h5gWWC9jujAQdTUGiUdgkMDLBxMynuyxcparVOAx4MN7v76aOdhA=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1363717626685, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363717626685, "_cnpmcore_publish_time": "2021-12-13T10:47:22.368Z"}, "0.1.9": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.9", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "readmeFilename": "README.md", "_id": "source-map@0.1.9", "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.9.tgz", "shasum": "250224e4e9ef7e91f4cad76cae714b90f6218599", "size": 21438, "noattachment": false, "integrity": "sha512-Mng4gipySnzGC2nAOJZ8FclGkAjRAVl2xXWW1LdgCTmPMNkZZ4tsvH2ncXbCF+8Zjwzw8E8FVnbOxDLX1NJ2Kw=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1362099757514, "_hasShrinkwrap": false, "_cnpm_publish_time": 1362099757514, "_cnpmcore_publish_time": "2021-12-13T10:47:23.440Z"}, "0.1.8": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.8", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.8", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.8.tgz", "shasum": "0bcc088a50ed8c586f50c8da4833a27dc0cc0c30", "size": 21499, "noattachment": false, "integrity": "sha512-cRNnzj32eB4RnnoUThDbSMvyvwx8vf7crdyjehMo4SsmIZBXLRswQf2atJozDN+Dcs4bom6Fezl1ihgZumuLrA=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1353365316067, "_hasShrinkwrap": false, "_cnpm_publish_time": 1353365316067, "_cnpmcore_publish_time": "2021-12-13T10:47:24.355Z"}, "0.1.7": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.7", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.7", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.7.tgz", "shasum": "92da34014a5576d60676150bcf0f55cbd1f395c0", "size": 20556, "noattachment": false, "integrity": "sha512-w+KE48PaQuM3nrnj1IXLnLdmGY/XqJKv9LEAD7j3uAnpLhQmV7OTIxtxz6XsH5p5c7xKq35i6yaL7w2JrGFShA=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1351883351172, "_hasShrinkwrap": false, "_cnpm_publish_time": 1351883351172, "_cnpmcore_publish_time": "2021-12-13T10:47:25.571Z"}, "0.1.6": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.6", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.6", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.6.tgz", "shasum": "0035ddb2d9a191df8c8b13a0362eb618d881ea34", "size": 20501, "noattachment": false, "integrity": "sha512-28LTwX+tpqQxVz661uuGwNSXFv+lRTv0G5eYpzDfrrUgkg2RDSx2wlbCfK2H+kGpF0/wmzemc4IlEMKa2IvyCQ=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1351882379069, "_hasShrinkwrap": false, "_cnpm_publish_time": 1351882379069, "_cnpmcore_publish_time": "2021-12-13T10:47:26.791Z"}, "0.1.5": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.5", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.5", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.5.tgz", "shasum": "63aaaee4c9dc9bea95ba9469a80775576927bf08", "size": 20266, "noattachment": false, "integrity": "sha512-vvaEptThqdLPoeA6yD4uzq2PmV+0HRrPkiBZJxtODKEqvK/qOqeR8Vu8jQ0Wab4atLPxHLqIDPB75Kb4rDMfiw=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1351646880060, "_hasShrinkwrap": false, "_cnpm_publish_time": 1351646880060, "_cnpmcore_publish_time": "2021-12-13T10:47:27.955Z"}, "0.1.4": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.4", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.4", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.4.tgz", "shasum": "cd67f54e9c4fdc713c00a869241c419e9fedace0", "size": 20127, "noattachment": false, "integrity": "sha512-IfyH6tzAt8gpC8MTfk5f1GG7Trd1CXMorkTEsOXgKbZ1dIhY5D7RYphFs7EQw4zNOmV4rLWqqxZO1cHyRQWM7Q=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1351541781630, "_hasShrinkwrap": false, "_cnpm_publish_time": 1351541781630, "_cnpmcore_publish_time": "2021-12-13T10:47:29.028Z"}, "0.1.3": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.3", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"requirejs": "==0.26.0"}, "devDependencies": {"dryice": "~0.4.8"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.3", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.3.tgz", "shasum": "ed3165c4d0f0a6291e53e485cb95b5f1a0a25183", "size": 20140, "noattachment": false, "integrity": "sha512-KNUc1sS93Y/OV2WyI9UaAHV3OvBK7/74LxueBPjgkRAGSF+f3FPmUf4lbQI/e4b54L/0ZAIYSlFp7mnnU7vv7A=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1350067419522, "_hasShrinkwrap": false, "_cnpm_publish_time": 1350067419522, "_cnpmcore_publish_time": "2021-12-13T10:47:30.275Z"}, "0.1.2": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.2", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"requirejs": "==0.26.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.2", "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.2.tgz", "shasum": "7132efc1fa95273bc71bff8f8202d7b4eed57993", "size": 19512, "noattachment": false, "integrity": "sha512-2zlER8v057dkDIXq/hNRbmnVyL7Fpw8VpnJ4W8hr6Pnh3/2z1dS5lyNiBJkS3lVgnv4B+iWgHdh0yTKCx1i/HQ=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1346862651322, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346862651322, "_cnpmcore_publish_time": "2021-12-13T10:47:31.403Z"}, "0.1.1": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.1", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"requirejs": ">=0.26.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "_id": "source-map@0.1.1", "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.6.19", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.1.tgz", "shasum": "524b0c511d53c3b1a76a211c084361bde15da726", "size": 19852, "noattachment": false, "integrity": "sha512-oCRWrHvj0njD0AY9FXc5YugZbX7BQ/3CUdHp5CD+Om/YBZX9X89KJYg6fI+g2tq4UPDq7CpM9Z17RjtRjCMV3Q=="}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1340144832055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1340144832055, "_cnpmcore_publish_time": "2021-12-13T10:47:32.580Z"}, "0.1.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "licenses": [{"type": "MPL", "url": "https://www.mozilla.org/MPL/MPL-1.1.html"}, {"type": "GPL", "url": "http://www.gnu.org/licenses/gpl.html"}, {"type": "LGPL", "url": "http://www.gnu.org/licenses/lgpl.html"}], "dependencies": {"requirejs": ">=0.26.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/source-map/0.1.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "source-map@0.1.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.1.0.tgz", "shasum": "229a5427719f1971be234b37cf968538b0600136", "size": 16383, "noattachment": false, "integrity": "sha512-XnamEXxnJC2B0HVOfVYsEtRGdPt8Fu6v0YXSoXlLoH5unX+nXLB6NMvc4/pv9C4viRc7/HaBSv9SwiYTV7UMTg=="}, "scripts": {}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1315525070688, "_hasShrinkwrap": false, "_cnpm_publish_time": 1315525070688, "_cnpmcore_publish_time": "2021-12-13T10:47:33.789Z"}, "0.0.0": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.0.0", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "licenses": [{"type": "MPL", "url": "https://www.mozilla.org/MPL/MPL-1.1.html"}, {"type": "GPL", "url": "http://www.gnu.org/licenses/gpl.html"}, {"type": "LGPL", "url": "http://www.gnu.org/licenses/lgpl.html"}], "dependencies": {"requirejs": ">=0.26.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/source-map/0.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "source-map@0.0.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.0.0.tgz", "shasum": "44220b0adf1572e603614d853727d3b05078d56c", "size": 13066, "noattachment": false, "integrity": "sha512-NFAVRu1LLObrZFGIsM8SNgSqJtGcYbquqT06sI5ocWievxyiCBueoqT+yGpJ+e4vrULtaxhGEbWtjq/icGbP5g=="}, "scripts": {}, "maintainers": [{"name": "andrew-sunada", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "hami<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "publish_time": 1314733540104, "_hasShrinkwrap": false, "_cnpm_publish_time": 1314733540104, "_cnpmcore_publish_time": "2021-12-13T10:47:34.866Z"}, "0.7.4": {"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.4", "homepage": "https://github.com/mozilla/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint *.js lib/ test/", "prebuild": "npm run lint", "build": "webpack --color", "pretest": "npm run build", "test": "node test/run-tests.js", "precoverage": "npm run build", "coverage": "nyc node test/run-tests.js", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "dev:watch": "watch 'npm run coverage' lib/ test/", "predev": "npm run setup", "dev": "npm-run-all -p --silent dev:*", "clean": "rm -rf coverage .nyc_output", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^1.3.1", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "nyc": "^11.7.1", "watch": "^1.0.2", "webpack": "^4.9.1", "webpack-cli": "^3.1"}, "nyc": {"reporter": "html"}, "typings": "source-map", "gitHead": "a999ec31686810d8a6aa2e8ff76c5df9bc3bfdcd", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "_id": "source-map@0.7.4", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==", "shasum": "a9bbe705c9d8846f4e08ff6765acf0f1b0898656", "tarball": "https://registry.npmmirror.com/source-map/-/source-map-0.7.4.tgz", "fileCount": 18, "unpackedSize": 226000, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMoXsZG1A0Q3vS0+ehewy31/0pCeca20yU9vBAfjCFYgIhAIzFiFPKPhYt2HkFy0UQSbdESaOhsqyfRssw+k8WKJ8b"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJim+NGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdmg//YO8dM0q3bO/Fc+Uo1pvi+i7m3wa/p6b8CJXcmn2k9HU9+7s5\r\n4dcGZbK1WV7ErRwHCi1lR4Xckci/eK9V5x22auujGZLOT3QCEfRvxy4GvQUt\r\nz/I1YhHYKyBNnT757eS8LBc9eNiw4w3iIoNOeOyCIX6C4OVd85Ard6asK6ud\r\nSPEv47p+z5Cy+kAWz0Kltyjh10pVrKdERVaLnLKOcNzFvBGmI0H/Nn51tBFn\r\nN1rOepn5TI2+27y/sTkQzPLWLVfBZOr/28W7lq1bWbLvQjtUD29Y2lvAupmO\r\nGk4wiG5hWP1tLYtpa4UrJ9a9qvBY9zMhorcNeo8vW8XF74411QVgDRjxWWwk\r\nIlxtfZSkNdnRDNWgnYvOSkoyK4GX0PFjLFCKtFuD+2Iw0C1dPemE17OdNw8X\r\nYB/kv5mYacxfiL71e1ev3o05ThVUCQR9FVjwsKA37GM0OIhRPCG/Q5jNxMnN\r\n0NSfrYj3yeAA/tcOUATUK6r/UzCzRekuKD4rhSVKKEmTxi3gXII/qPkP3E98\r\nm9UTMZXLoP+SYeXrtTmRAhbYhjDxLfyfORrmdbQ8+3IZlKmb0xR0IqCJ4DJ/\r\nIZQYrmD0x9IbtDkio8/M0fMA/B3/ZTPMtGX1lnJUexl0SBNlG0x7z+JTzTFB\r\nl8iOxaPsGLwGTBRtxrNMyz4WZMy5gy5XMgQ=\r\n=QeLs\r\n-----END PGP SIGNATURE-----\r\n", "size": 65061}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tigleym", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "factorui.npm", "email": "<EMAIL>"}, {"name": "project-nimbus-publishing", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "brizental", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "moz<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "knowtheory", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map_0.7.4_1654383429872_0.7539603373460522"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-05T13:42:48.944Z"}}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "_source_registry_name": "default"}