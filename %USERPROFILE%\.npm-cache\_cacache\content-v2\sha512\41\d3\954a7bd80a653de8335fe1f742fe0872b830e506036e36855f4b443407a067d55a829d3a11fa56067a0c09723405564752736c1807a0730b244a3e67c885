{"_attachments": {}, "_id": "echarts", "_rev": "138-61f143fbb677e08f51135e82", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "dist-tags": {"alpha": "5.0.0-alpha.2", "beta": "6.0.0-beta.1", "latest": "5.6.0", "rc": "5.6.0-rc.1"}, "license": "Apache-2.0", "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "echarts", "readme": "# Apache ECharts\n\n<a href=\"https://echarts.apache.org/\">\n    <img style=\"vertical-align: top;\" src=\"./asset/logo.png?raw=true\" alt=\"logo\" height=\"50px\">\n</a>\n\nApache ECharts is a free, powerful charting and visualization library offering easy ways to add intuitive, interactive, and highly customizable charts to your commercial products. It is written in pure JavaScript and based on <a href=\"https://github.com/ecomfe/zrender\">zrender</a>, which is a whole new lightweight canvas library.\n\n**[中文官网](https://echarts.apache.org/zh/index.html)** | **[ENGLISH HOMEPAGE](https://echarts.apache.org/en/index.html)**\n\n[![License](https://img.shields.io/npm/l/echarts?color=5470c6)](https://github.com/apache/echarts/blob/master/LICENSE) [![Latest npm release](https://img.shields.io/npm/v/echarts?color=91cc75)](https://www.npmjs.com/package/echarts) [![NPM downloads](https://img.shields.io/npm/dm/echarts.svg?label=npm%20downloads&style=flat&color=fac858)](https://www.npmjs.com/package/echarts) [![Contributors](https://img.shields.io/github/contributors/apache/echarts?color=3ba272)](https://github.com/apache/echarts/graphs/contributors)\n\n[![Build Status](https://github.com/apache/echarts/actions/workflows/ci.yml/badge.svg)](https://github.com/apache/echarts/actions/workflows/ci.yml)\n\n## Get Apache ECharts\n\nYou may choose one of the following methods:\n\n+ Download from the [official website](https://echarts.apache.org/download.html)\n+ `npm install echarts --save`\n+ CDN: [jsDelivr CDN](https://www.jsdelivr.com/package/npm/echarts?path=dist)\n\n## Docs\n\n+ [Get Started](https://echarts.apache.org/handbook)\n+ [API](https://echarts.apache.org/api.html)\n+ [Option Manual](https://echarts.apache.org/option.html)\n+ [Examples](https://echarts.apache.org/examples)\n\n## Get Help\n\n+ [GitHub Issues](https://github.com/apache/echarts/issues) for bug report and feature requests\n+ Email [<EMAIL>](mailto:<EMAIL>) for general questions\n+ Subscribe to the [mailing list](https://echarts.apache.org/maillist.html) to get updated with the project\n\n## Build\n\nBuild echarts source code:\n\nExecute the instructions in the root directory of the echarts:\n([Node.js](https://nodejs.org) is required)\n\n```shell\n# Install the dependencies from NPM:\nnpm install\n\n# Rebuild source code immediately in watch mode when changing the source code.\n# It opens the `./test` directory, and you may open `-cases.html` to get the list\n# of all test cases.\n# If you wish to create a test case, run `npm run mktest:help` to learn more.\nnpm run dev\n\n# Check the correctness of TypeScript code.\nnpm run checktype\n\n# If intending to build and get all types of the \"production\" files:\nnpm run release\n```\n\nThen the \"production\" files are generated in the `dist` directory.\n\n## Contribution\n\nPlease refer to the [contributing](https://github.com/apache/echarts/blob/master/CONTRIBUTING.md) document if you wish to debug locally or make pull requests.\n\n## Resources\n\n### Awesome ECharts\n\n[https://github.com/ecomfe/awesome-echarts](https://github.com/ecomfe/awesome-echarts)\n\n### Extensions\n\n+ [ECharts GL](https://github.com/ecomfe/echarts-gl) An extension pack of ECharts, which provides 3D plots, globe visualization, and WebGL acceleration.\n\n+ [Liquidfill 水球图](https://github.com/ecomfe/echarts-liquidfill)\n\n+ [Wordcloud 字符云](https://github.com/ecomfe/echarts-wordcloud)\n\n+ [Extension for Baidu Map 百度地图扩展](https://github.com/apache/echarts/tree/master/extension-src/bmap) An extension provides a wrapper of Baidu Map Service SDK.\n\n+ [vue-echarts](https://github.com/ecomfe/vue-echarts) ECharts component for Vue.js\n\n+ [echarts-stat](https://github.com/ecomfe/echarts-stat) Statistics tool for ECharts\n\n## License\n\nECharts is available under the Apache License V2.\n\n## Code of Conduct\n\nPlease refer to [Apache Code of Conduct](https://www.apache.org/foundation/policies/conduct.html).\n\n## Paper\n\nDeqing Li, Honghui Mei, Yi Shen, Shuang Su, Wenli Zhang, Junting Wang, Ming Zu, Wei Chen.\n[ECharts: A Declarative Framework for Rapid Construction of Web-based Visualization](https://www.sciencedirect.com/science/article/pii/S2468502X18300068).\nVisual Informatics, 2018.\n", "time": {"created": "2022-01-26T12:52:11.216Z", "modified": "2025-07-09T01:34:22.488Z", "5.3.0": "2022-01-26T17:49:58.742Z", "5.2.2": "2021-11-01T04:08:58.587Z", "5.2.1": "2021-09-21T02:26:31.319Z", "5.2.0": "2021-09-01T04:55:12.997Z", "5.1.2": "2021-06-08T15:17:35.252Z", "5.1.1": "2021-04-27T03:48:06.720Z", "5.1.0": "2021-04-16T11:41:10.178Z", "5.0.2": "2021-02-06T06:20:33.974Z", "5.0.1": "2021-01-16T06:15:16.160Z", "5.0.0": "2020-12-02T06:51:00.460Z", "5.0.0-rc.1": "2020-11-10T16:08:31.291Z", "5.0.0-beta.2": "2020-10-26T12:05:03.351Z", "5.0.0-beta.1": "2020-10-14T12:26:10.380Z", "5.0.0-alpha.2": "2020-08-17T18:18:05.909Z", "5.0.0-alpha.1": "2020-07-31T17:01:15.150Z", "4.8.0": "2020-05-25T09:42:29.718Z", "4.7.0": "2020-03-18T09:12:04.141Z", "4.6.0": "2019-12-27T09:10:30.836Z", "4.5.0": "2019-11-18T08:08:27.607Z", "4.5.0-rc.1": "2019-11-07T15:19:30.587Z", "4.4.0": "2019-10-14T18:19:54.876Z", "4.4.0-rc.1": "2019-09-29T09:28:40.142Z", "4.3.0": "2019-09-16T16:00:11.272Z", "4.3.0-rc.2": "2019-09-08T08:45:35.437Z", "4.3.0-rc.1": "2019-08-30T14:56:29.062Z", "4.2.1": "2019-03-21T10:35:01.223Z", "4.2.1-rc.3": "2019-02-28T07:05:59.876Z", "4.2.1-rc.2": "2019-02-25T08:35:24.648Z", "4.2.1-rc.1": "2019-01-25T06:43:30.631Z", "4.2.0-rc.2": "2018-10-15T16:22:45.728Z", "4.2.0-rc.1": "2018-09-15T08:02:33.195Z", "4.1.0": "2018-04-29T15:19:21.637Z", "4.0.3": "2018-02-28T06:44:46.883Z", "4.0.2": "2018-01-18T07:52:53.463Z", "4.0.1": "2018-01-17T06:40:22.532Z", "4.0.0": "2018-01-16T14:18:49.828Z", "3.8.5": "2017-11-14T06:16:27.355Z", "3.8.4": "2017-11-13T12:58:55.396Z", "3.8.3": "2017-11-10T08:36:12.149Z", "3.8.2": "2017-11-10T05:20:57.955Z", "3.8.1": "2017-11-10T05:05:14.903Z", "3.8.0": "2017-11-08T12:44:40.327Z", "3.7.2": "2017-09-28T03:37:22.986Z", "3.7.1": "2017-09-01T11:00:26.578Z", "3.7.0": "2017-08-22T04:34:23.022Z", "3.6.2": "2017-06-15T15:40:17.596Z", "3.6.1": "2017-05-27T02:25:53.553Z", "3.6.0": "2017-05-26T06:36:20.779Z", "3.5.4": "2017-04-27T03:52:46.589Z", "3.5.3": "2017-04-14T07:44:10.456Z", "3.5.2": "2017-04-05T11:13:18.631Z", "3.5.1": "2017-03-27T13:52:43.858Z", "3.5.0": "2017-03-26T13:21:40.583Z", "3.4.0": "2017-01-13T05:16:10.745Z", "3.3.2": "2016-11-24T08:31:25.535Z", "3.3.1": "2016-11-03T08:16:08.379Z", "3.3.0": "2016-11-01T08:33:40.002Z", "3.2.3": "2016-08-17T05:09:45.491Z", "3.2.2": "2016-07-12T12:24:39.759Z", "3.2.0": "2016-06-30T15:29:00.715Z", "3.1.10": "2016-05-19T07:43:42.425Z", "3.1.9": "2016-05-11T14:57:09.666Z", "3.1.8": "2016-05-11T08:07:50.709Z", "3.1.7": "2016-04-22T06:48:54.220Z", "3.1.6": "2016-04-11T05:56:06.704Z", "3.1.5": "2016-03-29T06:27:35.944Z", "3.1.4": "2016-03-21T07:02:33.944Z", "3.1.3": "2016-03-10T08:59:04.827Z", "3.1.2": "2016-03-01T12:31:03.384Z", "3.1.1": "2016-02-22T13:17:33.440Z", "3.0.2-beta3": "2016-01-27T08:58:44.455Z", "3.0.2-beta2": "2016-01-27T08:55:03.280Z", "3.0.2-beta1": "2016-01-27T08:11:45.194Z", "3.0.1": "2016-01-16T17:57:57.339Z", "3.0.0": "2016-01-12T10:18:15.371Z", "3.0.0-beta4": "2016-01-01T13:58:44.423Z", "3.0.0-beta3": "2016-01-01T13:39:21.429Z", "3.0.0-beta2": "2015-12-11T13:05:33.234Z", "2.2.8": "2015-11-07T07:19:43.252Z", "2.2.7-amd-beta1": "2015-09-03T06:34:55.627Z", "2.2.7-beta7": "2015-08-21T03:18:48.228Z", "4.9.0": "2020-08-28T04:04:28.328Z", "4.5.0-rc.2": "2019-11-08T10:07:52.900Z", "4.0.4": "2018-02-28T12:42:04.344Z", "3.2.1": "2016-07-04T11:28:53.031Z", "3.0.0-beta1": "2015-12-05T14:03:48.534Z", "2.2.7-amd-beta2": "2015-09-03T06:45:31.072Z", "2.2.1-amd-beta1": "2015-09-03T06:17:30.667Z", "5.3.0-rc.1": "2022-01-23T07:32:09.294Z", "5.3.1-rc.1": "2022-03-04T08:24:01.745Z", "5.3.1": "2022-03-07T06:38:47.825Z", "5.3.2-rc.1": "2022-03-28T09:59:18.122Z", "5.3.2": "2022-04-01T04:46:42.248Z", "5.3.3-rc.1": "2022-06-10T10:36:11.091Z", "5.3.3": "2022-06-14T03:50:22.988Z", "5.4.0-rc.1": "2022-09-13T17:40:55.599Z", "5.4.0": "2022-09-25T13:56:19.900Z", "5.4.1-rc.1": "2022-12-02T13:29:58.060Z", "5.4.1": "2022-12-09T16:09:30.533Z", "5.4.2-rc.1": "2023-03-17T04:21:21.258Z", "5.4.2": "2023-03-23T07:01:57.740Z", "5.4.3-rc.1": "2023-07-14T07:59:30.805Z", "5.4.3": "2023-07-18T02:46:54.645Z", "5.5.0-rc.1": "2024-01-31T08:56:37.948Z", "5.5.0-rc.2": "2024-02-04T07:45:38.874Z", "5.5.0": "2024-02-18T09:39:59.444Z", "5.5.1-rc.1": "2024-06-20T09:06:14.622Z", "5.5.1": "2024-06-27T07:27:29.634Z", "5.6.0-rc.1": "2024-12-21T09:25:20.337Z", "5.6.0": "2024-12-28T07:21:42.839Z", "6.0.0-beta.1": "2025-06-25T15:11:43.062Z"}, "versions": {"5.3.0": {"name": "echarts", "version": "5.3.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "0878d303ee5d798b808d3bab1444c5ec3e7e60d6", "_id": "echarts@5.3.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-zENufmwFE6WjM+24tW3xQq4ICqQtI0CGj4bDVDNd3BK3LtaA/5wBp+64ykIyKy3QElz0cieKqSYP4FX9Lv9MwQ==", "shasum": "39342fcf0f763413fecd9d2afd1c415163de694d", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.0.tgz", "fileCount": 1234, "unpackedSize": 41323772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8YnGCRA9TVsSAnZWagAAtHgP/3Qla63+chwTdRgUvkNM\nJ6VfAtvZAu/leWwS8+Qlzn0aMacGkT0BiyFeMtccwxWJbvwhXOt44j6iSx/W\nVMy5a0ViGuB8hyXdN8NYC/kVNmYuG/4lW+EtVHGvMxuJTh5w4xzN/Dd86Cdv\nZYwU122X3vCSJPTvA+MIQnMLBe98Yc4OdTLwa7JEsEmHYgbc1swRbwhet9uT\n7Z0Y8xqWBRwadRt4HnwgeSM+kQ6AnoHPZ9vMPeDhwxEii2R/c+7OcjBEjjFU\n0DeLOv4bj9q1BE3SI3YYayrPrMWrE+39bK9JfJHEzRyhAjNl6AU+SoGIBmTa\nXZtriOOsL3HjUjwssKLp9b9F7XjGZxStOEIogdRBntkUw3DUIX13YFb0IRlz\nS4z4np2o1CaYO5nklltk8JRwQSp3lwAnjI82rruc8BfmAh1O/FGJjbU3zXOq\nSETWpFwXCdHeAbN0hgcEGgNWmh4oiOgMe7uU3503DBtaNjXtkIDAjf7+MC2z\nYU1OGjen3jfp/DauwkWoR0HnipDAzY9fzUsK/eBuf0LlUKQIsA/WVLFhVciW\nJKku4Ju7kzksCYf3MmmjL4ozaR+LXlERZAOBEs8ghcBKWE97ynQCVVWMHBIr\nJLZZrz8ZObQ1/FLBdbaCfOcwILjL0mduU68THI/20C3JdJ4h09weCiFDHE3k\nUk9R\r\n=bbm3\r\n-----END PGP SIGNATURE-----\r\n", "size": 8456869}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.0_1643219398334_0.35739612114223984"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-26T17:50:20.937Z"}, "5.2.2": {"name": "echarts", "version": "5.2.2", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.2.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.3.5"}, "gitHead": "000ee7d2d1272cee96b0bd19bff9207301096c8d", "_id": "echarts@5.2.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"shasum": "ec3c8b2a151cbba71ba3c2c7cf9b2f2047ce4370", "size": 8360179, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.2.2.tgz", "integrity": "sha512-yxuBfeIH5c+0FsoRP60w4De6omXhA06c7eUYBsC1ykB6Ys2yK5fSteIYWvkJ4xJVLQgCvAdO8C4mN6MLeJpBaw=="}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.2.2_1635739738162_0.2248167442404776"}, "_hasShrinkwrap": false, "publish_time": 1635739738587, "_cnpm_publish_time": 1635739738587, "_cnpmcore_publish_time": "2021-12-16T10:11:56.785Z"}, "5.2.1": {"name": "echarts", "version": "5.2.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.2.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.3.5"}, "gitHead": "1c70026ec4d0bc18ac91163da7d11ed59c401ed4", "_id": "echarts@5.2.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"shasum": "bd58ec011cd82def4a714e4038ef4b73b8417bc3", "size": 8350369, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.2.1.tgz", "integrity": "sha512-OJ79b22eqRfbSV8vYmDKmA+XWfNbr0Uk/OafWcFNIGDWti2Uw9A6eVCiJLmqPa9Sk+EWL+t5v26aak0z3gxiZw=="}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.2.1_1632191190982_0.21303269532489955"}, "_hasShrinkwrap": false, "publish_time": 1632191191319, "_cnpm_publish_time": 1632191191319, "_cnpmcore_publish_time": "2021-12-16T10:12:10.587Z"}, "5.2.0": {"name": "echarts", "version": "5.2.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.2.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.3.5"}, "gitHead": "41b7769648333d0a1da1101ebebfc9174d618863", "_id": "echarts@5.2.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "9f1fbfbf048c15ab630bf0a74525c4c534d6cebc", "size": 8343334, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.2.0.tgz", "integrity": "sha512-7CrCKGRjFdpLIJ/Yt1gpHeqs5PiCem2GHPdWZPwKl7WSYeZu0Qzm1bcCFe9/b4dfVaL1zlY4JmdzaVwKksVeqg=="}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.2.0_1630472112468_0.6037032802923108"}, "_hasShrinkwrap": false, "publish_time": 1630472112997, "_cnpm_publish_time": 1630472112997, "_cnpmcore_publish_time": "2021-12-16T10:12:21.055Z"}, "5.1.2": {"name": "echarts", "version": "5.1.2", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.0.3", "zrender": "5.1.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.1.2"}, "gitHead": "00fde3c9d3053de7f65a05df1866cf3e5cb8b601", "_id": "echarts@5.1.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "aa1ab0cef5b74fa2f7c620261a5f286893d30fd1", "size": 8188189, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.1.2.tgz", "integrity": "sha512-okUhO4sw22vwZp+rTPNjd/bvTdpug4K4sHNHyrV8NdAncIX9/AarlolFqtJCAYKGFYhUBNjIWu1EznFrSWTFxg=="}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.1.2_1623165454637_0.8385704411083614"}, "_hasShrinkwrap": false, "publish_time": 1623165455252, "_cnpm_publish_time": 1623165455252, "_cnpmcore_publish_time": "2021-12-16T10:12:31.941Z"}, "5.1.1": {"name": "echarts", "version": "5.1.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.0.3", "zrender": "5.1.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.1.2"}, "gitHead": "786af8b3a55c855425f3825ff663e1856fcb1cf2", "_id": "echarts@5.1.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"shasum": "b186f162f017c555cfd67b12ede6762bdc3ddfda", "size": 8159230, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.1.1.tgz", "integrity": "sha512-b3nP8M9XwZM2jISuA+fP0EuJv8lcfgWrinel185Npy8bE/UhXTDIPJcqgQOCWdvk0c5CeT6Dsm1xBjmJXAGlxQ=="}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.1.1_1619495286283_0.7617045139623919"}, "_hasShrinkwrap": false, "publish_time": 1619495286720, "_cnpm_publish_time": 1619495286720, "_cnpmcore_publish_time": "2021-12-16T10:12:45.400Z"}, "5.1.0": {"name": "echarts", "version": "5.1.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.0.3", "zrender": "5.1.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "canvas": "^2.6.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "^4.1.2"}, "gitHead": "4628dd73cce2c0f1d085a8545d56f34c6126bd9a", "_id": "echarts@5.1.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "768cc585aa092560f48f01b2d52de493aeecd2b0", "size": 8173252, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.1.0.tgz", "integrity": "sha512-/X2nnN5BXW2tuA/Hv9YY279rDfwcXaBAjK9Azi//llshbKyUXXxBknsug21GJRpwTmLZbE8rjjbhchdm01bZtw=="}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.1.0_1618573269798_0.842074795273803"}, "_hasShrinkwrap": false, "publish_time": 1618573270178, "_cnpm_publish_time": 1618573270178, "_cnpmcore_publish_time": "2021-12-16T10:12:57.056Z"}, "5.0.2": {"name": "echarts", "version": "5.0.2", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.0.3", "zrender": "5.0.4"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.23", "eslint": "^7.15.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "^4.1.2"}, "gitHead": "03bee9947eca4b99590b791399c1cbeb0ae2cf49", "_id": "echarts@5.0.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "1726d17a57cf05d62cd0567b4325e1201a56baf6", "size": 7989893, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.2.tgz", "integrity": "sha512-En0VYpc96nw2/2AZoBWPHsGi471zMublttj50kfFpYAeR4geup0Tj9iVgEXh7QYZFPnRiruDJEjcB5PXZ+BYzQ=="}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.2_1612592433568_0.6839877463288861"}, "_hasShrinkwrap": false, "publish_time": 1612592433974, "_cnpm_publish_time": 1612592433974, "_cnpmcore_publish_time": "2021-12-16T10:13:07.421Z"}, "5.0.1": {"name": "echarts", "version": "5.0.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.0.3", "zrender": "5.0.3"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.23", "eslint": "^7.15.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "^4.1.2"}, "gitHead": "f9896f006781dae6b10ad44680e386feec6b4513", "_id": "echarts@5.0.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "b999ff6f515b1aa851ed09576eaf89686b57ce97", "size": 6691509, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.1.tgz", "integrity": "sha512-<PERSON><PERSON>n22Dolt2esY2jEzUsw1OxbobuW67oGjIoTjZO3rW89SWkfJ4kbrmC2OW9JjsBrD1rdkmaWBuZZ2HgmThyxJw=="}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.1_1610777715751_0.33769274382862124"}, "_hasShrinkwrap": false, "publish_time": 1610777716160, "_cnpm_publish_time": 1610777716160, "_cnpmcore_publish_time": "2021-12-16T10:13:18.542Z"}, "5.0.0": {"name": "echarts", "version": "5.0.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "main": "index.js", "module": "echarts.all.js", "jsdelivr": "dist/echarts.min.js", "types": "types/dist/echarts.d.ts", "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js", "build:i18n": "node build/build-i18n.js", "build:full": "node build/build.js --clean", "watch": "node build/build.js --watch", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "release": "node build/release.js", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "jest --config test/ut/jest.config.js", "test:single": "jest --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "1.10.0", "zrender": "5.0.1"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/types": "^7.10.5", "@microsoft/api-extractor": "7.7.2", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^2.15.0", "@typescript-eslint/parser": "^2.18.0", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.4.1", "eslint": "6.3.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "1.28.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-dts": "1.4.2", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-typescript2": "0.25.3", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "gitHead": "de46c55144e695240305d38e8ea874e03e323506", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@5.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "66ec0ea47e3c93b2aadfbb2d719869932ad13ce0", "size": 7960154, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.0.tgz", "integrity": "sha512-6SDcJbLVOcfQyjPg+spNU1+JVrkU1B9fzUa5tpbP/mMNUPyigCOJwcEIQAJSbp9jt5UP3EXvQR0vtYXIo9AjyA=="}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.0_1606891860022_0.04092327090934389"}, "_hasShrinkwrap": false, "publish_time": 1606891860460, "_cnpm_publish_time": 1606891860460, "_cnpmcore_publish_time": "2021-12-16T10:13:32.349Z"}, "5.0.0-rc.1": {"name": "echarts", "version": "5.0.0-rc.1", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "main": "index.js", "module": "echarts.all.js", "jsdelivr": "dist/echarts.min.js", "types": "types/dist/echarts.d.ts", "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js", "build:i18n": "node build/build-i18n.js", "build:full": "node build/build.js --clean", "watch": "node build/build.js --watch", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "release": "node build/release.js", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "jest --config test/ut/jest.config.js", "test:single": "jest --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "5.0.0-rc.1", "tslib": "1.10.0"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/types": "^7.10.5", "@microsoft/api-extractor": "7.7.2", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^2.15.0", "@typescript-eslint/parser": "^2.18.0", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.4.1", "eslint": "6.3.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "1.28.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-dts": "1.4.2", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-typescript2": "0.25.3", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "3a211ce93fab66d241268845069c08aaa3717a42", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@5.0.0-rc.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"shasum": "cd16279e4e7e2eaab32c8a07c093a608d00c4ee3", "size": 8349266, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.0-rc.1.tgz", "integrity": "sha512-QzXizwRBAhLBGwQW/37C9MqD54oo3dKY5s/9ia2fJ/FjfAtbwFmmkGOtgrMmpq+BDM8X6thpwIW9YYd1r4OGvw=="}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.0-rc.1_1605024510848_0.3328580661838816"}, "_hasShrinkwrap": false, "publish_time": 1605024511291, "_cnpm_publish_time": 1605024511291, "_cnpmcore_publish_time": "2021-12-16T10:13:45.013Z"}, "5.0.0-beta.2": {"name": "echarts", "version": "5.0.0-beta.2", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "main": "index.js", "module": "echarts.all.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js", "build:i18n": "node build/build-i18n.js", "build:full": "node build/build.js --clean", "watch": "node build/build.js --watch", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "release": "node build/release.js", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "jest --config test/ut/jest.config.js", "test:single": "jest --config test/ut/jest.config.js --coverage=false -t", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "5.0.0-beta.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/types": "^7.10.5", "@microsoft/api-extractor": "7.7.2", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^2.15.0", "@typescript-eslint/parser": "^2.18.0", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "esbuild": "^0.4.1", "eslint": "6.3.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "1.28.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-typescript2": "0.25.3", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "ts-jest": "^26.4.1", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "3e7bdc37ffadcc10fbeda1bfb7d711c7906c83d1", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@5.0.0-beta.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "6e25bee063f645caeb6fcd3f73aaf089ca0d8c46", "size": 9368327, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.0-beta.2.tgz", "integrity": "sha512-i3cxjm6/t0JgoxDonUltC0sRX1y4y5kiD6VPuBvJejZm2houIah4em1C+4F3MDqO9wKoNiQtQ/30J0/KOuqhdA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.0-beta.2_1603713902964_0.6006256251955426"}, "_hasShrinkwrap": false, "publish_time": 1603713903351, "_cnpm_publish_time": 1603713903351, "_cnpmcore_publish_time": "2021-12-16T10:14:00.073Z"}, "5.0.0-beta.1": {"name": "echarts", "version": "5.0.0-beta.1", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "main": "index.js", "module": "echarts.all.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js", "build:i18n": "node build/build-i18n.js", "build:full": "node build/build.js --clean", "watch": "node build/build.js --watch", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "release": "node build/release.js", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "test:single": "jest --config test/ut/jest.config.js --coverage=false -t", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "5.0.0-beta.1"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/types": "^7.10.5", "@microsoft/api-extractor": "7.7.2", "@typescript-eslint/eslint-plugin": "^2.15.0", "@typescript-eslint/parser": "^2.18.0", "husky": "^4.2.5", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "esbuild": "^0.4.1", "eslint": "6.3.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "1.28.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-typescript2": "0.25.3", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "2f566e92c18d91eefe9c92f76d40e5f842864312", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@5.0.0-beta.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "bb9d32e4f2a938f7f0bbbc8dd8d23f285000ec5c", "size": 9169078, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.0-beta.1.tgz", "integrity": "sha512-JaZIRZuavJF81Bn1GP5WNBT3sDrqfVDTrENQZIoBRgM9Kyv6+zNqcaIiWnel6tCIoaOjb0aDKol86AfYJvNkOw=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.0-beta.1_1602678369934_0.28146640220022"}, "_hasShrinkwrap": false, "publish_time": 1602678370380, "_cnpm_publish_time": 1602678370380, "_cnpmcore_publish_time": "2021-12-16T10:14:16.860Z"}, "5.0.0-alpha.2": {"name": "echarts", "version": "5.0.0-alpha.2", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "main": "index.js", "module": "echarts.all.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js", "build:i18n": "node build/build-i18n.js", "build:full": "node build/build.js --clean", "watch": "node build/build.js --watch", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "release": "node build/release.js", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "5.0.0-alpha.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/types": "^7.10.5", "@microsoft/api-extractor": "7.7.2", "@typescript-eslint/eslint-plugin": "^2.15.0", "@typescript-eslint/parser": "^2.18.0", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "esbuild": "^0.4.1", "eslint": "6.3.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "1.28.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-typescript2": "0.25.3", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "7cde54664e4408a073f4a5fbf65957b435d7632b", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@5.0.0-alpha.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "d08f12c12c5052e0e34c57936847246c9d594cee", "size": 8984542, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.0-alpha.2.tgz", "integrity": "sha512-SPjOb2sc+vno64HVesychrnWM83o4QrflgIG/SITjk7oWYrtL/QWpp1QxzhzsSJe1V/wsgNv/D30p/9Thn2bGg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.0-alpha.2_1597688285034_0.2808838007145378"}, "_hasShrinkwrap": false, "publish_time": 1597688285909, "_cnpm_publish_time": 1597688285909, "_cnpmcore_publish_time": "2021-12-16T10:14:50.195Z"}, "5.0.0-alpha.1": {"name": "echarts", "version": "5.0.0-alpha.1", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "main": "index.js", "module": "echarts.all.js", "jsdelivr": "dist/echarts.min.js", "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js", "build:i18n": "node build/build-i18n.js", "build:full": "node build/build.js --clean", "watch": "node build/build.js --watch", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "release": "node build/release.js", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "5.0.0-alpha.1"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/types": "^7.10.5", "@microsoft/api-extractor": "7.7.2", "@typescript-eslint/eslint-plugin": "^2.15.0", "@typescript-eslint/parser": "^2.18.0", "canvas": "^2.6.0", "chalk": "^3.0.0", "chokidar": "^3.4.0", "commander": "2.11.0", "esbuild": "^0.4.1", "eslint": "6.3.0", "fs-extra": "0.26.7", "glob": "7.0.0", "globby": "11.0.0", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "lodash.debounce": "^4.0.8", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "1.28.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-typescript2": "0.25.3", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "3d83d38554d6abd933344b36e87918414eb7baf5", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@5.0.0-alpha.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "0dcb3bc1d7852b458a8ab8dde40c5ead2e4c110b", "size": 8875950, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.0.0-alpha.1.tgz", "integrity": "sha512-6B4lQKT5NwxgnbQXA5p6FtrvpQKrgnffIcArcP8L0nx+FN2mjdVqcCrirU2X4rShXUVLtX5nAmJCmvNT3ybAEA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.0.0-alpha.1_1596214874867_0.15094101718972608"}, "_hasShrinkwrap": false, "publish_time": 1596214875150, "_cnpm_publish_time": 1596214875150, "_cnpmcore_publish_time": "2021-12-16T10:15:03.511Z"}, "4.8.0": {"name": "echarts", "version": "4.8.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.3.1"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "canvas": "^2.6.0", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "eslint": "6.3.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0"}, "gitHead": "3b61add89d9901b1fa88aaf3b09e1314e123c599", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.8.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "b2c1cfb9229b13d368ee104fc8eea600b574d4c4", "size": 9991809, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.8.0.tgz", "integrity": "sha512-YwShpug8fWngj/RlgxDaYrLBoD+LsZUArrusjNPHpAF+is+gGe38xx4W848AwWMGoi745t3OXM52JedNrv+F6g=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.8.0_1590399749237_0.8224636021917155"}, "_hasShrinkwrap": false, "publish_time": 1590399749718, "_cnpm_publish_time": 1590399749718, "_cnpmcore_publish_time": "2021-12-16T10:15:21.273Z"}, "4.7.0": {"name": "echarts", "version": "4.7.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.3.0"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "canvas": "^2.6.0", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "eslint": "6.3.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0"}, "gitHead": "4f3748d2e0cba28a892fc152e9fcd977c0b96226", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.7.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "5b3875a4c2f91e3929425fabab9eace7e4098b3f", "size": 9954473, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.7.0.tgz", "integrity": "sha512-NlOTdUcAsIyCCG+N4uh0ZEvXtrPW2jvcuqf03RyqYeCKzyPbiOQ4I3MdKXMhxG3lBdqQNdNXVT71SB4KTQjN0A=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.7.0_1584522723676_0.23409047363347013"}, "_hasShrinkwrap": false, "publish_time": 1584522724141, "_cnpm_publish_time": 1584522724141, "_cnpmcore_publish_time": "2021-12-16T10:15:38.360Z"}, "4.6.0": {"name": "echarts", "version": "4.6.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.2.0"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "jshint": "2.10.2", "eslint": "6.3.0"}, "gitHead": "f4fc1b0625f3c88b9f4dbed03eeb091ed6bd1079", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.6.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "b5a47a1046cec93ceeef954f9ee54751340558ec", "size": 9901174, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.6.0.tgz", "integrity": "sha512-xKkcr6v9UVOSF+PMuj7Ngt3bnzLwN1sSXWCvpvX+jYb3mePYsZnABq7wGkPac/m0nV653uGHXoHK8DCKCprdNg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.6.0_1577437830357_0.14331177539766582"}, "_hasShrinkwrap": false, "publish_time": 1577437830836, "_cnpm_publish_time": 1577437830836, "_cnpmcore_publish_time": "2021-12-16T10:15:57.054Z"}, "4.5.0": {"name": "echarts", "version": "4.5.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test": "node build/build.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.1.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "jshint": "2.10.2", "eslint": "6.3.0"}, "gitHead": "3185bb1e43564ba3c96f1e1341e3e0630c47fe82", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "2111960645a345eb819ddac4792a2c065bdff162", "size": 9994222, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.5.0.tgz", "integrity": "sha512-q9M0errodeX/786uPifro76x0elbrUQkbSHh235QzbkaASuvP9AQoMErhGBno4iC/yq6kFDLqgmm3XCPWQGLzA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.5.0_1574064507080_0.6503339703818067"}, "_hasShrinkwrap": false, "publish_time": 1574064507607, "_cnpm_publish_time": 1574064507607, "_cnpmcore_publish_time": "2021-12-16T10:16:12.743Z"}, "4.5.0-rc.1": {"name": "echarts", "version": "4.5.0-rc.1", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test": "node build/build.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.1.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "jshint": "2.10.2", "eslint": "6.3.0"}, "readmeFilename": "README.md", "gitHead": "a2e3de1365b967a4b4ee2ad7bc1db7ea6e9dfb92", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.5.0-rc.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "c7715fbe6132990b798fcba135bf507ef69a948f", "size": 9994180, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.5.0-rc.1.tgz", "integrity": "sha512-+IddHpMWbQzKI91tQQmApFotC8z3i7O1Qiy+rPPTx2rJ2c4BNFXYBfpW0yPGH9GxQLQiz/eyiYyK057DOt62sQ=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.5.0-rc.1_1573139970143_0.5572608969736128"}, "_hasShrinkwrap": false, "publish_time": 1573139970587, "_cnpm_publish_time": 1573139970587, "_cnpmcore_publish_time": "2021-12-16T10:16:48.826Z"}, "4.4.0": {"name": "echarts", "version": "4.4.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test": "node build/build.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.1.1"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "jshint": "2.10.2", "eslint": "6.3.0"}, "gitHead": "e75bfa370c29551f84c4b3d20ffb216487099473", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "d5260566cef0b98f5a00baf1b5010e92c1c23661", "size": 9949869, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.4.0.tgz", "integrity": "sha512-zJD1YaONA2Ib6rKCpkU9rT/K+OFJ/F0/3Mj5FamGLXytBV5g36gsQGv8sYL7fVs/BFj/sn3wemyctlgIHMCA5w=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.4.0_1571077194497_0.045466822164190956"}, "_hasShrinkwrap": false, "publish_time": 1571077194876, "_cnpm_publish_time": 1571077194876, "_cnpmcore_publish_time": "2021-12-16T10:17:03.479Z"}, "4.4.0-rc.1": {"name": "echarts", "version": "4.4.0-rc.1", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test": "node build/build.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.1.1"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "jshint": "2.10.2", "eslint": "6.3.0"}, "readmeFilename": "README.md", "gitHead": "b2b38d67fe5d7e5b4244dd1499d43cc73e1aad40", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.4.0-rc.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "05bca4a86e09969cee46d27638939277fb081387", "size": 9949788, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.4.0-rc.1.tgz", "integrity": "sha512-r98JMkTzJa9bhSXShIkhRbEB60+M+UFhgvgMsrsqTMdS1TR2bBFbHkL0PStOujkG9UKgvnMRKv+pdDfItQdSQQ=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.4.0-rc.1_1569749319566_0.8531842035242765"}, "_hasShrinkwrap": false, "publish_time": 1569749320142, "_cnpm_publish_time": 1569749320142, "_cnpmcore_publish_time": "2021-12-16T10:17:21.068Z"}, "4.3.0": {"name": "echarts", "version": "4.3.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "test": "node build/build.js"}, "dependencies": {"zrender": "4.1.0"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "bf3606621937303e221296317a39ebecfd3ccbbb", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.3.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "3728c2b5e8f277898f7299b4fe1cfc537170abd5", "size": 9873102, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.3.0.tgz", "integrity": "sha512-tX2dAyhI9D78eVrlKhqJGdrM7Ku599HHvPxT2Pu6tgb/dBP8tYmkWjn8r1Ea9oABQxAKXr0p2/tG1OzKdzULbw=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.3.0_1568649610281_0.07936416477699337"}, "_hasShrinkwrap": false, "publish_time": 1568649611272, "_cnpm_publish_time": 1568649611272, "_cnpmcore_publish_time": "2021-12-16T10:17:37.367Z"}, "4.3.0-rc.2": {"name": "echarts", "version": "4.3.0-rc.2", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "test": "node build/build.js"}, "dependencies": {"zrender": "4.1.0"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1"}, "readmeFilename": "README.md", "gitHead": "739033d601a9e06000097a85093e1e5f27c5c94d", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.3.0-rc.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "a9998994639c5bd2c49ddbb59f902b25f88a32ee", "size": 9873104, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.3.0-rc.2.tgz", "integrity": "sha512-VYVwHUa2CQgHDfCXHTOEO6CgBh0tNeshw1ivu+r9/V5+qVNALFQMRp8s70lEDupzBd711NqlEpCMvDPMyvXWJA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.3.0-rc.2_1567932334992_0.19263155480424965"}, "_hasShrinkwrap": false, "publish_time": 1567932335437, "_cnpm_publish_time": 1567932335437, "_cnpmcore_publish_time": "2021-12-16T10:17:53.169Z"}, "4.3.0-rc.1": {"name": "echarts", "version": "4.3.0-rc.1", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "test": "node build/build.js"}, "dependencies": {"zrender": "4.1.0"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1"}, "readmeFilename": "README.md", "gitHead": "b62ab99217f436a3723ee22c5989e23ec3098753", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.3.0-rc.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "2946d60c4b16fd25de6a5e99953a834e4d8cacac", "size": 9872446, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.3.0-rc.1.tgz", "integrity": "sha512-A6c5fxr7cwbcM77Ife1yJmSVIHk2g3Zd7dbDXlJdy+1aAq2oNK8o9ZiTTqXNOTbTAD2/Tuj4zrcWUr4XCpqSyg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.3.0-rc.1_1567176988637_0.25860315866442063"}, "_hasShrinkwrap": false, "publish_time": 1567176989062, "_cnpm_publish_time": 1567176989062, "_cnpmcore_publish_time": "2021-12-16T10:18:12.717Z"}, "4.2.1": {"name": "echarts", "version": "4.2.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.7"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "010315e947235415b7dfc4be9aa6865e7e5d76dc", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.2.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "9a8ea3b03354f86f824d97625c334cf16965ef03", "size": 9540681, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.2.1.tgz", "integrity": "sha512-pw4xScRPsLegD/cqEcoXRKeA2SD4+s+Kyo0Na166NamOWhzNl2yI5RZ2rE97tBlAopNmhyMeBVpAeD5qb+ee1A=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.2.1_1553164500692_0.20774366699711"}, "_hasShrinkwrap": false, "publish_time": 1553164501223, "_cnpm_publish_time": 1553164501223, "_cnpmcore_publish_time": "2021-12-16T10:18:35.464Z"}, "4.2.1-rc.3": {"name": "echarts", "version": "4.2.1-rc.3", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.7"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1"}, "readmeFilename": "README.md", "gitHead": "1a2e99059fc66ff30b1f0072fc5fd71921199908", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.2.1-rc.3", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "efb7fdfe127026c134c9dffb8d49d17615f54c16", "size": 9540701, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.2.1-rc.3.tgz", "integrity": "sha512-8GEArv78bw8jYB/OkSJMXyCs3s1Nfyccek7X91lKoftJuiwcy3upsdoKbbAGQSF96ZGhSwhevyS2r7mNuusIew=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.2.1-rc.3_1551337559383_0.9538904625879885"}, "_hasShrinkwrap": false, "publish_time": 1551337559876, "_cnpm_publish_time": 1551337559876, "_cnpmcore_publish_time": "2021-12-16T10:18:53.795Z"}, "4.2.1-rc.2": {"name": "echarts", "version": "4.2.1-rc.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.7"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1"}, "readmeFilename": "README.md", "gitHead": "f2d76c9b72bac2e2d62867392f7af7c921d239a7", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.2.1-rc.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "6a81508e0befabf277df1a8177a644b9f9ce3965", "size": 9538312, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.2.1-rc.2.tgz", "integrity": "sha512-a2oLXlrmVjUJSWVc0JLJHmkFqijMonN0Z1wF2jdl790VyXpLS+7G1geSzIeYESwt7UNYxGQmkmHjxvQYh2iD6Q=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.2.1-rc.2_1551083724112_0.47602695309389365"}, "_hasShrinkwrap": false, "publish_time": 1551083724648, "_cnpm_publish_time": 1551083724648, "_cnpmcore_publish_time": "2021-12-16T10:19:15.303Z"}, "4.2.1-rc.1": {"name": "echarts", "version": "4.2.1-rc.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.6"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.6"}, "readmeFilename": "README.md", "gitHead": "9234f0309137250a2561212e8ecd1639551119b1", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.2.1-rc.1", "_nodeVersion": "10.10.0", "_npmVersion": "6.7.0", "dist": {"shasum": "0fdf7e3c5a3f81825c2b3f635ab87966d48c0655", "size": 9545228, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.2.1-rc.1.tgz", "integrity": "sha512-BpGPaBOiJLdg594t+V6J+oyROEsDIVUAtBpbm+byqKtPCvDmNlGP4q+16hzo9LtcyUzvHqZdbL0AuDbvE7D+4Q=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.2.1-rc.1_1548398609722_0.9346006746867919"}, "_hasShrinkwrap": false, "publish_time": 1548398610631, "_cnpm_publish_time": 1548398610631, "_cnpmcore_publish_time": "2021-12-16T10:19:38.207Z"}, "4.2.0-rc.2": {"name": "echarts", "version": "4.2.0-rc.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.5"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.5"}, "gitHead": "1a566dea8d585c255b094e925fe5e86243720323", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.2.0-rc.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "6a98397aafa81b65cbf0bc15d9afdbfb244df91e", "size": 9480784, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.2.0-rc.2.tgz", "integrity": "sha512-5Y4Kyi4eNsRM9Cnl7Q8C6PFVjznBJv1VIiMm/VSQ9zyqeo+ce1695GqUd9v4zfVx+Ow1gnwMJX67h0FNvarScw=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.2.0-rc.2_1539620565369_0.8834549600665353"}, "_hasShrinkwrap": false, "publish_time": 1539620565728, "_cnpm_publish_time": 1539620565728, "_cnpmcore_publish_time": "2021-12-16T10:20:00.640Z"}, "4.2.0-rc.1": {"name": "echarts", "version": "4.2.0-rc.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.5"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.5"}, "gitHead": "dbd5a323b63ce0b7d6f68358486fc7a50ebf8ff9", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.2.0-rc.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "c76bc392e9d34f914480f8299305f66fa0b79cb7", "size": 9480431, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.2.0-rc.1.tgz", "integrity": "sha512-zhjpI+iTewF6U9Q9+eGEBTKXpOQe9h1pRoHJMKDw2ppCScQ0UulksqWlVNSE50lriBDJtCx8PNF7Tqa59lv2ag=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.2.0-rc.1_1536998552820_0.23140291090855536"}, "_hasShrinkwrap": false, "publish_time": 1536998553195, "_cnpm_publish_time": 1536998553195, "_cnpmcore_publish_time": "2021-12-16T10:20:20.777Z"}, "4.1.0": {"name": "echarts", "version": "4.1.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.4"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.4"}, "gitHead": "40949ae5d19a6de64d4b5cd0fa109a4c04051e66", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@4.1.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "d588c95f73c1a9928b9c73d5b769751c3185bcdc", "size": 9212668, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.1.0.tgz", "integrity": "sha512-gP1e1fNnAj9KJpTDLXV21brklbfJlqeINmpQDJCDta9TX3cPoqyQOiDVcEPzbOVHqgBRgTOwNxC5iGwJ89014A=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.1.0_1525015161280_0.9952575161283432"}, "_hasShrinkwrap": false, "publish_time": 1525015161637, "_cnpm_publish_time": 1525015161637, "_cnpmcore_publish_time": "2021-12-16T10:20:41.655Z"}, "4.0.3": {"name": "echarts", "version": "4.0.3", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.2"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.2"}, "gitHead": "e00f3f1c974d05d690e61c940fa722b606a342df", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@4.0.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "14b0904407acf563e599fcaa9a33658b0b57cbc6", "size": 8966130, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.0.3.tgz", "integrity": "sha512-ZN6XLCcGfeSPa5qrLGpBrxA2190QGnpU529+N+V/PWJlGx3lvi5Ggw8Vh6Dx7fVpZz3+sQxWx4PNmQ37UKVuvA=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.0.3_1519800286646_0.579966844095547"}, "_hasShrinkwrap": false, "publish_time": 1519800286883, "_cnpm_publish_time": 1519800286883, "_cnpmcore_publish_time": "2021-12-16T10:21:17.584Z"}, "4.0.2": {"name": "echarts", "version": "4.0.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.1"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.1"}, "gitHead": "debcd7f324b77ad444f7ba31195535c749d08e53", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@4.0.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "c405e9e479af982383c8ae2b5946e52d058c6220", "size": 8864821, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.0.2.tgz", "integrity": "sha512-2kLhHvSAVbJ6QYVDxuySFp/+LfJxlYTYyPaOfwqydiFMAL9Wx7jzarfR1P4P6DFzLCZi4U4mv6EcZhH9BantKQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-4.0.2.tgz_1516261973079_0.11676668864674866"}, "directories": {}, "publish_time": 1516261973463, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516261973463, "_cnpmcore_publish_time": "2021-12-16T10:21:33.109Z"}, "4.0.1": {"name": "echarts", "version": "4.0.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.0"}, "gitHead": "b449ed4f11514bc00b7ec23c7df7dafad5960be2", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@4.0.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "59dbf1099acd299c1d221208eb9d41ae25f755f8", "size": 8860497, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.0.1.tgz", "integrity": "sha512-02bNHAwyoeDrxPESAUn8cFRg0g1z69ss7AcXcKFcV4ITKseO0rz1tJ64T94MUElU6FMmxja1tRxbqnIyHTo+Ng=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-4.0.1.tgz_1516171222038_0.19301213906146586"}, "directories": {}, "publish_time": 1516171222532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516171222532, "_cnpmcore_publish_time": "2021-12-16T10:21:50.707Z"}, "4.0.0": {"name": "echarts", "version": "4.0.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.0"}, "gitHead": "fdb8d4e9e04cb9831be0947ca9efbdb09f40fe30", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@4.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "74340ff3fc88b67aee9839a80b1cb72faacd911a", "size": 8860461, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.0.0.tgz", "integrity": "sha512-7MsZs2Bxx21kgUV0o8ELBK7QRdwdn4rHPCMdjR1yBZ+tllU7P7k3lY1Jx7PaW75RFL83nUuHK5P0xLfQnPohag=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-4.0.0.tgz_1516112329444_0.7084791497327387"}, "directories": {}, "publish_time": 1516112329828, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516112329828, "_cnpmcore_publish_time": "2021-12-16T10:22:09.310Z"}, "3.8.5": {"name": "echarts", "version": "3.8.5", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "3.7.4"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "3.7.3"}, "gitHead": "eb95d20f527c5e91f98579647c6343b07b4f8c6e", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.8.5", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "58e4a51d2743c6fb75257b0dc0a9cf9f5378ac0e", "size": 8418278, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.8.5.tgz", "integrity": "sha512-E+nnROMfCeiLeoT/fZyX8SE8mKzwkTjyemyoBF543oqjRtjTSKQAVDEihMXy4oC6pJS0tYGdMqCA2ATk8onyRg=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.8.5.tgz_1510640186971_0.14323125244118273"}, "directories": {}, "publish_time": 1510640187355, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510640187355, "_cnpmcore_publish_time": "2021-12-16T10:22:24.644Z"}, "3.8.4": {"name": "echarts", "version": "3.8.4", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "3.7.3"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "3.7.3"}, "gitHead": "12820435d83f7adbb39239d10f94ae96453fcdc3", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.8.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "57d48738cf64ac3fcbcdf53dee7a7b31301b8ca2", "size": 8408931, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.8.4.tgz", "integrity": "sha512-4ZbiPnvehck63iqxTKdUNglcQPPCoYIvesBPAr/ncQoV9OO+aWds+d+bOcCiCdWqZfiOzQXo+FjjudMBcwmtgQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.8.4.tgz_1510577934927_0.7769940663129091"}, "directories": {}, "publish_time": 1510577935396, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510577935396, "_cnpmcore_publish_time": "2021-12-16T10:22:38.856Z"}, "3.8.3": {"name": "echarts", "version": "3.8.3", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "3.7.2"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "3.7.2"}, "gitHead": "71b5d2b2e0e5be8bdb50f54b36378d20358ab1b9", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.8.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "0ecf0ceb870ee8cca2426f3a18a10e287532603c", "size": 8396707, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.8.3.tgz", "integrity": "sha512-YfIer70EQ5TxiaRmu7dZqHzr8qqhi1GbMns4AJTgscXdNOm2SdB+KCxr3+pTnztg/t3myy1peQd3cqG8YxxwkA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.8.3.tgz_1510302971290_0.29898750805296004"}, "directories": {}, "publish_time": 1510302972149, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510302972149, "_cnpmcore_publish_time": "2021-12-16T10:22:54.386Z"}, "3.8.2": {"name": "echarts", "version": "3.8.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "3.7.2"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "3.7.2"}, "gitHead": "d235731e8de3097970fc165e20a53c393ede1d60", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.8.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "277531877e2168c5bbf6a46661507d8fa947ca56", "size": 8396695, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.8.2.tgz", "integrity": "sha512-x8P/BWBgKMTFrENQ+T1EWKFGE59ZhCITm/V7HvNKuSISNzHuC9PGkAa7XCgB2D/uBtNb3CsTXbRohoQGBL8QQw=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.8.2.tgz_1510291256551_0.2555538828019053"}, "directories": {}, "publish_time": 1510291257955, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510291257955, "_cnpmcore_publish_time": "2021-12-16T10:23:12.923Z"}, "3.8.1": {"name": "echarts", "version": "3.8.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "rm -r lib; cp -r src lib"}, "dependencies": {"zrender": "3.7.1"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "3.7.1"}, "gitHead": "93436cafb830f26535ae5270edeb98d43f98fb36", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.8.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "e7f6fe7f518d461612bae30c30e4b133d498db65", "size": 8418194, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.8.1.tgz", "integrity": "sha512-NP+1SzxqosoJoNAEmpaviqKuiiV3ePpimfVb+7aSFDQKqBTXJcRWP6sGg/3+YbCZ9RCMwA4w4SEpKL5OO/rcKQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.8.1.tgz_1510290313972_0.19680778519250453"}, "directories": {}, "publish_time": 1510290314903, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510290314903, "_cnpmcore_publish_time": "2021-12-16T10:23:38.198Z"}, "3.8.0": {"name": "echarts", "version": "3.8.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "rm -r lib; cp -r src lib"}, "dependencies": {"zrender": "^3.7.0"}, "devDependencies": {"commander": "^2.11.0", "coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.7", "glob": "^7.0.0", "rollup": "^0.50.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "zrender": "^3.7.0"}, "gitHead": "7f1392ca5e6c20c528818a297ed7a2b6d45b5efc", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.8.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "7e3ec5bb81f5afe3b59710d3ce121935d9a6ec52", "size": 8428134, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.8.0.tgz", "integrity": "sha512-azDEpI2I6BbseEehRf2Ti11kdluNkXBI+1NzJcmI7b0FMOc2TP8C2qqywZ+6IGSvMh6aLF+g3ISFWgyXkLLxqA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.8.0.tgz_1510145079870_0.7824400479439646"}, "directories": {}, "publish_time": 1510145080327, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510145080327, "_cnpmcore_publish_time": "2021-12-16T10:24:00.899Z"}, "3.7.2": {"name": "echarts", "version": "3.7.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.6.2"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.6.2"}, "gitHead": "d31b247a0e6f9b532cb948fd35d247058868159a", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.7.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "3b37764d8bc0c8ab49cbea16bc0b55726451caaa", "size": 6888438, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.7.2.tgz", "integrity": "sha512-aBuGNlnBDMbIMyU5qkh3Zp1F55gfSlsVrkiZYWA7J3qUFzoZBtLXJVKZVxIEZ7TsWmppQ6q+Rckscp5fM73ZaQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.7.2.tgz_1506569842677_0.6189761734567583"}, "directories": {}, "publish_time": 1506569842986, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506569842986, "_cnpmcore_publish_time": "2021-12-16T10:24:21.014Z"}, "3.7.1": {"name": "echarts", "version": "3.7.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.6.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.6.1"}, "gitHead": "cdb4a5cf544537fbd99e469b25957341af4cb53f", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.7.1", "_shasum": "fad25d4b1cbb7ec28f1b0fa890e9a0d86056b2d5", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "fad25d4b1cbb7ec28f1b0fa890e9a0d86056b2d5", "size": 5349932, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.7.1.tgz", "integrity": "sha512-qJD7tq+BxvVd+7QGPqUHowLEBq2GL9NrhDYst2mDijK6UNw5J1Mi+bMpwO0wq+W9YPGii186TSegnnSQB2Aviw=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.7.1.tgz_1504263625905_0.20825243089348078"}, "directories": {}, "publish_time": 1504263626578, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504263626578, "_cnpmcore_publish_time": "2021-12-16T10:24:34.181Z"}, "3.7.0": {"name": "echarts", "version": "3.7.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.6.0"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.6.0"}, "gitHead": "55d7b0513d159eae8e3c0b3fd8d69ec0bc18e1ba", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.7.0", "_shasum": "79d21b2a7be18a43f6a3f19f8c0ec07c2cf4a3dc", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "79d21b2a7be18a43f6a3f19f8c0ec07c2cf4a3dc", "size": 5341060, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.7.0.tgz", "integrity": "sha512-TviAF1cKSkGMiW/2NAiZfLhDsUtRvxVyFsu9XIh1/74l84B5J/HpZXOjmJMB1SXuAFWTCrkTTbSjxWgtoiYy9Q=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.7.0.tgz_1503376462666_0.16084303613752127"}, "directories": {}, "publish_time": 1503376463022, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503376463022, "_cnpmcore_publish_time": "2021-12-16T10:24:46.448Z"}, "3.6.2": {"name": "echarts", "version": "3.6.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.5.2"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.5.2"}, "gitHead": "31c35216b64ea5b9397829aefe57b8a1bc185e20", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.6.2", "_shasum": "862954c8b5810bff87a48b0de0416ed8c4bb1c36", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "862954c8b5810bff87a48b0de0416ed8c4bb1c36", "size": 5257530, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.6.2.tgz", "integrity": "sha512-UPaJ4TR+nKhmbvTT/aCNT6EQHgJ0zZzgnaZq05JWluoLHE3mBINM/QpNUe7akzGabX4+E9GrEx8zGgrnNQPIGQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.6.2.tgz_1497541217393_0.18100260011851788"}, "directories": {}, "publish_time": 1497541217596, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497541217596, "_cnpmcore_publish_time": "2021-12-16T10:25:01.194Z"}, "3.6.1": {"name": "echarts", "version": "3.6.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.5.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.5.1"}, "gitHead": "205070d9782ae2752e24447fc90090292ce957ba", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.6.1", "_shasum": "6cb31173c6ac05ba713a613cdf4dd72145e0ab6f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "6cb31173c6ac05ba713a613cdf4dd72145e0ab6f", "size": 5245114, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.6.1.tgz", "integrity": "sha512-hUpda7oZm8SiCP+sy4UJNnL+76Le8hO2KcTEa6pBjJ6chBtCy5ABD9G20WbAbp0R3lL4JHxv/M8uPtcIJSKNMQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.6.1.tgz_1495851953323_0.2761863712221384"}, "directories": {}, "publish_time": 1495851953553, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495851953553, "_cnpmcore_publish_time": "2021-12-16T10:25:16.815Z"}, "3.6.0": {"name": "echarts", "version": "3.6.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.5.0"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.5.0"}, "gitHead": "3f79399174cc34ff50aa29fe82b7f5592c763f3c", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.6.0", "_shasum": "cbcb11cb1cb024a9498ff7fa47c3dc95c4186cca", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "cbcb11cb1cb024a9498ff7fa47c3dc95c4186cca", "size": 5245055, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.6.0.tgz", "integrity": "sha512-WDjGr9R7etmeu9DrJ0pC8oTMePKMq1hOjsT2ePO2NqPDZohj87PRDcwGqe7EdaNxdP9YG1TZDbuGQOp+ISJlUA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts-3.6.0.tgz_1495780580101_0.8350496590137482"}, "directories": {}, "publish_time": 1495780580779, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495780580779, "_cnpmcore_publish_time": "2021-12-16T10:25:27.995Z"}, "3.5.4": {"name": "echarts", "version": "3.5.4", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.4.4"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.4.4"}, "gitHead": "99350dd4033ea7d3f39a4205a53ccd701f6e7433", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.5.4", "_shasum": "f4fb32559711edc56375b06bf1d7628a707ff9e2", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "f4fb32559711edc56375b06bf1d7628a707ff9e2", "size": 5189241, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.5.4.tgz", "integrity": "sha512-ZRJYNB7lDOg+bWmpup/TEQ+zWoHojmKH8q1Un05qm5bgH4HRhpIjDPn9IVJ5ExjVjvsc64l1kUPVbAG0ZqZyFQ=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/echarts-3.5.4.tgz_1493265161980_0.7242565157357603"}, "directories": {}, "publish_time": 1493265166589, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493265166589, "_cnpmcore_publish_time": "2021-12-16T10:25:38.652Z"}, "3.5.3": {"name": "echarts", "version": "3.5.3", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.4.3"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.4.3"}, "gitHead": "058d74371944947e9ef6a137fc234c41df1682f5", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.5.3", "_shasum": "771e6752cc4c163e77ddbfeaeecf5985a9320847", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "771e6752cc4c163e77ddbfeaeecf5985a9320847", "size": 5917411, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.5.3.tgz", "integrity": "sha512-tOMBxRqXZ+WeOHXTOLXkPWM7TtN7aECoPJD5oX31e/axBeGT3V8rhzIuKqmra6sTWx3LIwJgCpynTU3ztSntkw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.5.3.tgz_1492155850178_0.9612469885032624"}, "directories": {}, "publish_time": 1492155850456, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492155850456, "_cnpmcore_publish_time": "2021-12-16T10:25:50.638Z"}, "3.5.2": {"name": "echarts", "version": "3.5.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.4.2"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.4.2"}, "gitHead": "32f5c8ea9a4e77e70bc2f6626ab82b5d3790269b", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.5.2", "_shasum": "c5baf0157c257394b22d3e89937eb368a27bf6ac", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "c5baf0157c257394b22d3e89937eb368a27bf6ac", "size": 5901227, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.5.2.tgz", "integrity": "sha512-DXFWCXCAdrgG6Q8V2PYL27G6XxzgDKbAXfxw7jAfVn+hGij97EqLrdKuApaz8eHNhyMX1jUNSsAlXIfcu00USg=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/echarts-3.5.2.tgz_1491390795957_0.552238728152588"}, "directories": {}, "publish_time": 1491390798631, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491390798631, "_cnpmcore_publish_time": "2021-12-16T10:26:04.691Z"}, "3.5.1": {"name": "echarts", "version": "3.5.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.4.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.4.1"}, "gitHead": "e104a4f08b37fb674333d12a412c52b16c977b0a", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.5.1", "_shasum": "97263b87ed6dd8662acd37caed9f211f69b309af", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "97263b87ed6dd8662acd37caed9f211f69b309af", "size": 5888933, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.5.1.tgz", "integrity": "sha512-53lMGRrQTkEcDcNbmHZEiui/TKQAYtv7CYqy+9uMwhbpZObkGkFSkBdohZFHhrGPCuS2k4H+DBgbY7NlCVetUw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.5.1.tgz_1490622763493_0.820956353796646"}, "directories": {}, "publish_time": 1490622763858, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490622763858, "_cnpmcore_publish_time": "2021-12-16T10:26:19.447Z"}, "3.5.0": {"name": "echarts", "version": "3.5.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.4.0"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.4.0"}, "gitHead": "79004a2e6946cd042e0a9cb0a86f604be1feba53", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.5.0", "_shasum": "2a63a7c6d58fe58022dca82137a92c13bfa0d7e9", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "2a63a7c6d58fe58022dca82137a92c13bfa0d7e9", "size": 5888986, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.5.0.tgz", "integrity": "sha512-+1m525NHFKvgcvo/O9ow9TblOdz/shglmeBFnlzNDbeKF/NsLUP29sTuoaXgQ+92kycnppGP2p4mQB3Nlh5bzw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.5.0.tgz_1490534500295_0.8608935712836683"}, "directories": {}, "publish_time": 1490534500583, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490534500583, "_cnpmcore_publish_time": "2021-12-16T10:26:34.710Z"}, "3.4.0": {"name": "echarts", "version": "3.4.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.3.0"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.2.2"}, "gitHead": "10692d9c36ee8fee505b6ecf77442019df5ad4a0", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.4.0", "_shasum": "425e83be18a70170d2e1a9daf22c75bcd2da58d4", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "425e83be18a70170d2e1a9daf22c75bcd2da58d4", "size": 5742603, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.4.0.tgz", "integrity": "sha512-dMcmRd8740RAm1KPJGxMVaijFgL19hmpctqZw9/0p5XxNkSXEawgwUPJeeHNWRs0YVnC1yEsIFEI4hPO0TK4LA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.4.0.tgz_1484284570445_0.2176836256403476"}, "directories": {}, "publish_time": 1484284570745, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484284570745, "_cnpmcore_publish_time": "2021-12-16T10:26:47.311Z"}, "3.3.2": {"name": "echarts", "version": "3.3.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.2.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.2.2"}, "gitHead": "aa81ed5fe7ee5e91030d9b57191bddc4f48090ff", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.3.2", "_shasum": "06f42a47d213248d45fe585719ec8940fdc610aa", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "06f42a47d213248d45fe585719ec8940fdc610aa", "size": 27884285, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.3.2.tgz", "integrity": "sha512-TxCpJiJ4UCFRZr5kD0SQlz0tIUwoLH9yaikvoOKNrfD63UFOXphR0vn2MSl5vk7LMl6caMSu1VcGN4vsoYqTnA=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/echarts-3.3.2.tgz_1479976281174_0.7253076781053096"}, "directories": {}, "publish_time": 1479976285535, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479976285535, "_cnpmcore_publish_time": "2021-12-16T10:27:46.237Z"}, "3.3.1": {"name": "echarts", "version": "3.3.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.2.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.2.1"}, "gitHead": "9117d472be86a89f2cf28b963dc78382e08a42e3", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.3.1", "_shasum": "35ad7b1e9e941450ad3e0134635a04bf1e61bb42", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "35ad7b1e9e941450ad3e0134635a04bf1e61bb42", "size": 27833352, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.3.1.tgz", "integrity": "sha512-A7A7JP7sdG0eblrdLAQXNnl1do3CWmw6BuxOBITPEHSFQl/8wN3ntcZRBrzKQsNvWhrsJdG1iezDF9f1evUZLA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.3.1.tgz_1478160967931_0.5401361424010247"}, "directories": {}, "publish_time": 1478160968379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478160968379, "_cnpmcore_publish_time": "2021-12-16T10:28:44.484Z"}, "3.3.0": {"name": "echarts", "version": "3.3.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.2.0"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.2.0"}, "gitHead": "8f885ec3a42506c05f907385244a482d078d9b0a", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.3.0", "_shasum": "c310ab67e4b81a58e16b95f00babaa803f051c39", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "c310ab67e4b81a58e16b95f00babaa803f051c39", "size": 5407510, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.3.0.tgz", "integrity": "sha512-cjLPYAkAOeDyigNBA6+3m/HA0LUkTj48+fj4botbzdtHXShptWP/UVSLd6Jq/f/xKIxfcary4B2b1i1bt+cKTg=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/echarts-3.3.0.tgz_1477989217620_0.3657646293286234"}, "directories": {}, "publish_time": 1477989220002, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477989220002, "_cnpmcore_publish_time": "2021-12-16T10:28:58.612Z"}, "3.2.3": {"name": "echarts", "version": "3.2.3", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.1.3"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.1.3"}, "gitHead": "0efee6d756e534554f2f52b620a8d5fa46b6b2ce", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.2.3", "_shasum": "4f01e15bfda3352fcd8ef9049f13d8448971a258", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "4f01e15bfda3352fcd8ef9049f13d8448971a258", "size": 4632896, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.2.3.tgz", "integrity": "sha512-Kk7J2KbaaClgYN/GWAjTsFBJtBuppeRRMOqkC0gMAhZdnekachChnv4W0MkIFPYkr3PAOT3VzSWXNAjzHm6fYg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/echarts-3.2.3.tgz_1471410580203_0.6158337357919663"}, "directories": {}, "publish_time": 1471410585491, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471410585491, "_cnpmcore_publish_time": "2021-12-16T10:29:09.313Z"}, "3.2.2": {"name": "echarts", "version": "3.2.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.1.2"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.1.2"}, "gitHead": "352ca171b3cee4a40043c2e752c59aa4bc8e5c0a", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.2.2", "_shasum": "32852af95664ff79c6588c814d6cc07813559098", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "32852af95664ff79c6588c814d6cc07813559098", "size": 3910271, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.2.2.tgz", "integrity": "sha512-zOP/Gy5Vh0dz1uA3XeaBV0dtKMFhjntczfRFj7diCh4nCalDqg5AhmVgi+T0kQDf1ORYDBzuy7EWq3E5YWxHeA=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/echarts-3.2.2.tgz_1468326275502_0.8952550571411848"}, "directories": {}, "publish_time": 1468326279759, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468326279759, "_cnpmcore_publish_time": "2021-12-16T10:29:15.881Z"}, "3.2.0": {"name": "echarts", "version": "3.2.0", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.1.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.1.1"}, "gitHead": "d00542a130e2be3df52a38bb1816c9368ad3367a", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.2.0", "_shasum": "d90fe2d688c39aeeb7a1ed47b1da047781bd6596", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "d90fe2d688c39aeeb7a1ed47b1da047781bd6596", "size": 3893759, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.2.0.tgz", "integrity": "sha512-xano2NGwyvyxPX9a5PWoPz6NGYCnMQktN3Osw+bEkYfBqzXQk5PGRpoenCiJ4j4ZP1sSOuGOLi0rT8ATl0ZxWg=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.2.0.tgz_1467300540242_0.9933648761361837"}, "directories": {}, "publish_time": 1467300540715, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467300540715, "_cnpmcore_publish_time": "2021-12-16T10:29:36.083Z"}, "3.1.10": {"name": "echarts", "version": "3.1.10", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.1.0"}, "devDependencies": {"zrender": "^3.1.0", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13"}, "gitHead": "5b08696ef103314c827907907432b15146940c45", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.10", "_shasum": "d936e71e5a7bb81a2139ad7325166528cadcfb3c", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "d936e71e5a7bb81a2139ad7325166528cadcfb3c", "size": 3697690, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.10.tgz", "integrity": "sha512-7o8euQf1/ep4IITantKrF76RAKXl58r9limMMK1cInYmxOjkSsGjUu3rW4Aplgp4VNEUPvR/TLhn2yvLwyc46Q=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.1.10.tgz_1463643821840_0.937132294755429"}, "directories": {}, "publish_time": 1463643822425, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463643822425, "_cnpmcore_publish_time": "2021-12-16T10:29:44.618Z"}, "3.1.9": {"name": "echarts", "version": "3.1.9", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.9"}, "devDependencies": {"zrender": "^3.0.9", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13"}, "gitHead": "ead50f50c9c09f971ca760d917c785f9910e53fb", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.9", "_shasum": "ffc6114db24ca252e250db77a1f6ae064a7291ae", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "ffc6114db24ca252e250db77a1f6ae064a7291ae", "size": 3683652, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.9.tgz", "integrity": "sha512-785DWNJCJJKOeALgUAe9E/D8GgwvUSmTCghNdCYF74Uwog4lkZdmp3LbJSR5FMSmnoJtOdDy+b+jmmeL0nuDFQ=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/echarts-3.1.9.tgz_1462978625477_0.8570252268109471"}, "directories": {}, "publish_time": 1462978629666, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462978629666, "_cnpmcore_publish_time": "2021-12-16T10:29:52.676Z"}, "3.1.8": {"name": "echarts", "version": "3.1.8", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.9"}, "devDependencies": {"zrender": "^3.0.9", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13"}, "gitHead": "3f0a6af008d17a226da1d4a7a5b5984798c1197a", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.8", "_shasum": "751373e593048c3f86b7a7f4a977d21a6ba5259c", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "751373e593048c3f86b7a7f4a977d21a6ba5259c", "size": 3683563, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.8.tgz", "integrity": "sha512-bAEDfS699+FhAHCU1wLNUu02VVnLnNQS+HJOl4UYhZ8SbhqiADiK16k5KShBlb3pbKsSweGTT2L0+zK5bc3BiQ=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/echarts-3.1.8.tgz_1462954067155_0.9738276405259967"}, "directories": {}, "publish_time": 1462954070709, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462954070709, "_cnpmcore_publish_time": "2021-12-16T10:29:59.752Z"}, "3.1.7": {"name": "echarts", "version": "3.1.7", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.8"}, "devDependencies": {"zrender": "^3.0.8", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13"}, "gitHead": "81472fa39f3130bbf2319559c590a691cde1c642", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.7", "_shasum": "a3558473327e132d0a42fe23ef536a0eeb09d361", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "a3558473327e132d0a42fe23ef536a0eeb09d361", "size": 3653553, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.7.tgz", "integrity": "sha512-bHnu2nD1cYDjp64SZlL7vsASb5U+gMFN7FiA5XE0xNoLsGAtqPMLvyblSDjThpVJrPLC0mtdujuv8GU4qvQDPw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.1.7.tgz_1461307733671_0.9472135005053133"}, "directories": {}, "publish_time": 1461307734220, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461307734220, "_cnpmcore_publish_time": "2021-12-16T10:30:05.880Z"}, "3.1.6": {"name": "echarts", "version": "3.1.6", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.7"}, "devDependencies": {"zrender": "^3.0.7", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13"}, "gitHead": "3724a163ccb4c6dd4b585dbeab6ffd25c2fcead1", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.6", "_shasum": "b619c12c2e2917cafae4f592d8250a6368657b77", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "b619c12c2e2917cafae4f592d8250a6368657b77", "size": 3645732, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.6.tgz", "integrity": "sha512-FbBC1265PWaCk0E8xSBn6mo8NQr55cBtY3Pi0qiFUm6iahJ/pd12u56c9BDo3bwsuEbrDn0X60jtKhMDNQ4GTQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.1.6.tgz_1460354166239_0.12788267573341727"}, "directories": {}, "publish_time": 1460354166704, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460354166704, "_cnpmcore_publish_time": "2021-12-16T10:30:13.691Z"}, "3.1.5": {"name": "echarts", "version": "3.1.5", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.6"}, "devDependencies": {"zrender": "^3.0.6", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13"}, "gitHead": "b73c87ed2586db4d1d00baa939e551b58a1ed700", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.5", "_shasum": "297a247ad320b7997b58319531ab6173745ddcc2", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "297a247ad320b7997b58319531ab6173745ddcc2", "size": 3647566, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.5.tgz", "integrity": "sha512-Bsalkw41D69We8hLZ7oHs2jcwXzGo2O3TOKRAcUkTRsWNo9ZLNz62CU7w0PnmOgBTab5oNyBr4mj3dmemMbc6g=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.1.5.tgz_1459232855429_0.9049914658535272"}, "directories": {}, "publish_time": 1459232855944, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459232855944, "_cnpmcore_publish_time": "2021-12-16T10:30:19.266Z"}, "3.1.4": {"name": "echarts", "version": "3.1.4", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.5"}, "devDependencies": {"escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.0.4"}, "gitHead": "fe8d7ea288ed18ac6b45b2f8191a0cd6d473bc6e", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.4", "_shasum": "311157251408ae14f4933be6ab8367724d87262d", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "311157251408ae14f4933be6ab8367724d87262d", "size": 3639375, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.4.tgz", "integrity": "sha512-UNMHmtDaj+LeQ8mzrKx9YWM+d11ZZW9Gj2e795PyJpBXBD0EThuAua2PH34h0JCwP+kajHzujLqhxteN+LD2Vg=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/echarts-3.1.4.tgz_1458543753459_0.002820537658408284"}, "directories": {}, "publish_time": 1458543753944, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458543753944, "_cnpmcore_publish_time": "2021-12-16T10:30:26.852Z"}, "3.1.3": {"name": "echarts", "version": "3.1.3", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.0.4"}, "devDependencies": {"escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.0.4"}, "gitHead": "4bcf738068c2325e913b0507e237bb2a6e227634", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.3", "_shasum": "4ea218fc9ab06d94ac985e29cf980c9b0990d329", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "4ea218fc9ab06d94ac985e29cf980c9b0990d329", "size": 3631320, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.3.tgz", "integrity": "sha512-SKJKE1vJ1yN1na7BGqbcXPIjIE/bbwcpDXc5sEPu80Qn6ipXg997BwL1EPYe3HPrpwEsUccZSUc6UWHT2njIwg=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/echarts-3.1.3.tgz_1457600344266_0.457144808024168"}, "directories": {}, "publish_time": 1457600344827, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457600344827, "_cnpmcore_publish_time": "2021-12-16T10:30:33.269Z"}, "3.1.2": {"name": "echarts", "version": "3.1.2", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "dependencies": {"zrender": "^3.0.3"}, "devDependencies": {"zrender": "^3.0.3", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "etpl": "^3.0.1", "optimist": "^0.6.1", "uglify-js": "^2.6.1"}, "gitHead": "33ffda2f4da456f3db6555d838e96c89fd563336", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.2", "_shasum": "445a307006ba920b81e0a4c2ff229fb7353accf9", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "445a307006ba920b81e0a4c2ff229fb7353accf9", "size": 3612898, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.2.tgz", "integrity": "sha512-IRt9OdM70+ADTGpPJMzPV+tuXGLpH0p5LHG4vUwveGr99XVWSKW+Us7tHbj3cQjZNjvNrA014yO6diB84p97Tg=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/echarts-3.1.2.tgz_1456835458585_0.20834622206166387"}, "directories": {}, "publish_time": 1456835463384, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456835463384, "_cnpmcore_publish_time": "2021-12-16T10:30:40.059Z"}, "3.1.1": {"name": "echarts", "version": "3.1.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "dependencies": {"zrender": "^3.0.2"}, "devDependencies": {"escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "etpl": "^3.0.1", "optimist": "^0.6.1", "uglify-js": "^2.6.1"}, "gitHead": "3cbfba5624071d01c6e5691eede1706f3e96408e", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.1.1", "_shasum": "503e8cc1f192c4e2d54b9d27da6374d2628f4cc6", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "503e8cc1f192c4e2d54b9d27da6374d2628f4cc6", "size": 3594364, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.1.1.tgz", "integrity": "sha512-4Kz59qHc9J/amRcuHy6HLOxo/FwGo8bO8MY/vppYcZbhOmhEaByT4cc1wNM+8rMKLc+7iRrqy35buMo8ijkQcw=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/echarts-3.1.1.tgz_1456147047651_0.1726064884569496"}, "directories": {}, "publish_time": 1456147053440, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456147053440, "_cnpmcore_publish_time": "2021-12-16T10:30:46.225Z"}, "3.0.2-beta3": {"name": "echarts", "version": "3.0.2-beta3", "description": "A powerful charting and visualization library for browser", "main": "echarts-all", "scripts": {"build": "npm run dist && npm run lib", "dist": "cd build && npm run build && cd ..", "lib": "gulp lib", "prerelease": "npm run dist && gulp prerelease", "release": "npm run prerelease && cd .publish && npm publish", "test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "dependencies": {"zrenderjs": "~3.0.3"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0", "run-sequence": "^1.1.5"}, "browserify": "dist/echarts.min.js", "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "visualization", "vml"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "gitHead": "d8b60b8d7e789f02f877064f4196cf9e4c20060c", "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.2-beta3", "_shasum": "368c98dd40efeaaddcf3280c70a077e5c2602911", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "368c98dd40efeaaddcf3280c70a077e5c2602911", "size": 3550682, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.2-beta3.tgz", "integrity": "sha512-SSywM7dYDCPKWtpTMFNl48TfJtbQQEVzOECgmvfm+2/YrO/vSxCp/tt2fGyTXN8NMWcdr4yYq8cWTyPVYQhi5Q=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453885124455, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453885124455, "_cnpmcore_publish_time": "2021-12-16T10:30:52.728Z"}, "3.0.2-beta2": {"name": "echarts", "version": "3.0.2-beta2", "description": "A powerful charting and visualization library for browser", "main": "echarts-all", "scripts": {"build": "npm run dist && npm run lib", "dist": "cd build && npm run build && cd ..", "lib": "gulp lib", "prerelease": "npm run dist && gulp prerelease", "release": "npm run prerelease && cd .publish && npm publish", "test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "dependencies": {"zrenderjs": "~3.0.3"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0", "run-sequence": "^1.1.5"}, "browserify": "dist/echarts.min.js", "files": ["dist", "src"], "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "visualization", "vml"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "gitHead": "d8b60b8d7e789f02f877064f4196cf9e4c20060c", "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.2-beta2", "_shasum": "d7433f669db53cb1efa81710f66b7bcf79635edf", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "d7433f669db53cb1efa81710f66b7bcf79635edf", "size": 1281007, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.2-beta2.tgz", "integrity": "sha512-nPiRTadahoHy2rbo97syhtZ7LSwIi3O8UZUkQQEB+Fk9LvjMP+APkPqzFOQFW10EFdtRUIQti0KHc7ba+SHsFg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453884903280, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453884903280, "_cnpmcore_publish_time": "2021-12-16T10:30:54.922Z"}, "3.0.2-beta1": {"name": "echarts", "version": "3.0.2-beta1", "description": "A powerful charting and visualization library for browser", "main": "echarts", "scripts": {"build": "npm run dist && npm run lib", "dist": "cd build && npm run build && cd ..", "lib": "gulp lib", "prerelease": "npm run dist && gulp prerelease", "release": "npm run prerelease && cd .publish && npm publish", "test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "dependencies": {"zrenderjs": "~3.0.3"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0", "run-sequence": "^1.1.5"}, "browserify": "dist/echarts.min.js", "files": ["dist", "src"], "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "visualization", "vml"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.2-beta1", "_shasum": "097cf4bc6c54052f547258e4931a53bf5bbee3f1", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "097cf4bc6c54052f547258e4931a53bf5bbee3f1", "size": 1280997, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.2-beta1.tgz", "integrity": "sha512-0RRxZCKGqSR1IfFBqGGzUl4aR76hcejFszkOyM/pgtSzfStyHZUmBOhfAJ8BfYNl1G7V7CiIiMVINo/1a/vbyg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453882305194, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453882305194, "_cnpmcore_publish_time": "2021-12-16T10:30:57.649Z"}, "3.0.1": {"name": "echarts", "version": "3.0.1", "description": "A powerful charting and visualization library for browser", "main": "lib/echarts", "scripts": {"build": "npm run dist && npm run lib", "dist": "cd build && npm run build && cd ..", "lib": "gulp lib", "prerelease": "npm run dist && gulp prerelease", "release": "npm run prerelease && cd .publish && npm publish", "test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "dependencies": {"zrenderjs": "~3.0.2"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0", "run-sequence": "^1.1.5"}, "browserify": "dist/echarts.min.js", "files": ["dist", "src"], "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "visualization", "vml"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.1", "_shasum": "4fb7b6b586a9a8ad9927ccc1935c121a273f585b", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "5.1.1", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "4fb7b6b586a9a8ad9927ccc1935c121a273f585b", "size": 1275072, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.1.tgz", "integrity": "sha512-8Hvaa+hOUAYaFAgQjRISWN/2LKZ4g66nE33dHNjG8wT3S/SU7m2ENJ2+96mEkLQN5m9VvQKhmjCMqiky3J/png=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452967077339, "deprecated": "deprecated", "_hasShrinkwrap": false, "_cnpm_publish_time": 1452967077339, "_cnpmcore_publish_time": "2021-12-16T10:30:59.538Z"}, "3.0.0": {"name": "echarts", "version": "3.0.0", "description": "A powerful charting and visualization library for browser", "main": "echarts", "dependencies": {"zrenderjs": "~3.0.0"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0"}, "scripts": {"test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "browserify": "dist/echarts.min.js", "files": ["dist", "src"], "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "visualization", "vml"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.0", "_shasum": "c449c5ef8c6dedd96c61614c62266df310ddd6b2", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "c449c5ef8c6dedd96c61614c62266df310ddd6b2", "size": 1015349, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.0.tgz", "integrity": "sha512-XMFhSkMw8+fuLmQG9GMTBA+kMOd88NS8YpPYQiYUmfFx0pDM3LBVpEsk7Uxz1W+CPeQg8fD2ZxrWNoyuIUbrCA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452593895371, "deprecated": "deprecated", "_hasShrinkwrap": false, "_cnpm_publish_time": 1452593895371, "_cnpmcore_publish_time": "2021-12-16T10:31:01.644Z"}, "3.0.0-beta4": {"name": "echarts", "version": "3.0.0-beta4", "description": "Enterprise Charts, build for webpack. 基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "main": "echarts", "dependencies": {"zrenderjs": "3.0.0-beta5"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0"}, "scripts": {"test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "vml"], "license": "ISC", "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.0-beta4", "_shasum": "5fed76eb11fd0dcad93bc4ba8087e3c51003daf5", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "5.1.1", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "5fed76eb11fd0dcad93bc4ba8087e3c51003daf5", "size": 2359098, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.0-beta4.tgz", "integrity": "sha512-99Bt/ESo0YuXZwnkkhjn13Nz1f/wBZPiSAk/+wJaT+SprVF5Fy83z8B9kEypLHrHUsAaCpAwx3L9PeQCMxL7iQ=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1451656724423, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451656724423, "_cnpmcore_publish_time": "2021-12-16T10:31:06.034Z"}, "3.0.0-beta3": {"name": "echarts", "version": "3.0.0-beta3", "description": "Enterprise Charts, build for webpack. 基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "main": "echarts", "dependencies": {"zrenderjs": "3.0.0-beta5"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0"}, "scripts": {"test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "keywords": ["echarts", "chart", "charts", "graph", "graphs", "webpack", "canvas", "svg", "vml"], "license": "ISC", "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.0-beta3", "_shasum": "1f0af72cb2e1ae4aaad3d272c5e5357cf2bc6187", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "5.1.1", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "1f0af72cb2e1ae4aaad3d272c5e5357cf2bc6187", "size": 2355358, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.0-beta3.tgz", "integrity": "sha512-xLSX//a+9knv5s3QB4d9HnG1WeDOm+cFHerrq1T3PHoAyaJbcwHhDSvJGpIZDGxw28wlb4Uhm7pD1N7okOjsVA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1451655561429, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451655561429, "_cnpmcore_publish_time": "2021-12-16T10:31:10.301Z"}, "3.0.0-beta2": {"name": "echarts", "version": "3.0.0-beta2", "description": "Enterprise Charts, build for webpack. 基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "main": "echarts", "dependencies": {"zrenderjs": "3.0.0-beta4"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0"}, "scripts": {"test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "keywords": ["echarts", "chart", "graph", "webpack", "canvas", "svg", "vml"], "license": "ISC", "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.0-beta2", "_shasum": "b372dec9de64691a443d32abc291fd40f9737981", "_from": ".", "_npmVersion": "3.5.1", "_nodeVersion": "5.1.1", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "b372dec9de64691a443d32abc291fd40f9737981", "size": 2286566, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.0-beta2.tgz", "integrity": "sha512-gHwqoJ9fo+O28KnZfOJdRNVpS2CWKH2mdT4cbW7mpvUOIuDKJrtp0y2rltIFiRxF76Qybx7Gnw16D37hqqkR/A=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1449839133234, "_hasShrinkwrap": false, "_cnpm_publish_time": 1449839133234, "_cnpmcore_publish_time": "2021-12-16T10:31:15.022Z"}, "2.2.8": {"name": "echarts", "description": "Enterprise Charts, build for webpack. 基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "version": "2.2.8", "scripts": {}, "main": "echarts", "keywords": ["echarts", "chart", "graph", "webpack", "canvas"], "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/luqin/echarts.git"}, "homepage": "http://echarts.baidu.com", "license": "SEE LICENSE IN <./LICENSE.txt>", "dependencies": {"zrenderjs": "~2.1.1-amd-beta1"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4"}, "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "_id": "echarts@2.2.8", "_shasum": "81a86694c4e5e917e6fdd39d52989002a685f5e7", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "81a86694c4e5e917e6fdd39d52989002a685f5e7", "size": 1793852, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-2.2.8.tgz", "integrity": "sha512-qiM9+nLWBwNpvBziMT2JDIyu0yCetTXzkjhNm0be95pG6AiwqZfnhRT5sW3lmE5xSl1sd2MXYcRkWiqxMUlvNA=="}, "directories": {}, "publish_time": 1446880783252, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446880783252, "_cnpmcore_publish_time": "2021-12-16T10:31:20.702Z"}, "2.2.7-amd-beta1": {"name": "echarts", "description": "Enterprise Charts，基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "version": "2.2.7-amd-beta1", "scripts": {}, "main": "echarts", "keywords": ["echarts", "chart", "graph", "webpack", "canvas"], "repository": {"type": "git", "url": "git://github.com/uooo/echarts"}, "homepage": "http://echarts.baidu.com", "license": "BSD", "dependencies": {"zrenderjs": "~2.1.1-amd-beta1"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4"}, "bugs": {"url": "https://github.com/uooo/echarts/issues"}, "_id": "echarts@2.2.7-amd-beta1", "_shasum": "a47f575d52be4a1efdeb21a597e216742fda0a0d", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "a47f575d52be4a1efdeb21a597e216742fda0a0d", "size": 2744091, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-2.2.7-amd-beta1.tgz", "integrity": "sha512-I4HZq29NH9Ll9ch3rZY4/hAn+L8yTcp2xfkeV03CXgBJPYzwk+NiNr5958Afe0b2Tlisv556pphQJ7oI/5vW1w=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441262095627, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441262095627, "_cnpmcore_publish_time": "2021-12-16T10:31:29.153Z"}, "2.2.7-beta7": {"name": "echarts", "description": "Enterprise Charts，Build for Webpack。基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "version": "2.2.7-beta7", "scripts": {}, "main": "echarts", "keywords": ["echarts", "chart", "graph", "webpack", "canvas"], "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dependencies": {"zrender": "https://github.com/uooo/zrender/archive/2.1.1-webpack.tar.gz"}, "repository": {"type": "git", "url": "git://github.com/uooo/echarts.git"}, "homepage": "http://echarts.baidu.com", "bugs": {"url": "https://github.com/uooo/echarts/issues"}, "_id": "echarts@2.2.7-beta7", "_shasum": "8585dd8aeaeb8deba31231987f78668d28bf027b", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "8585dd8aeaeb8deba31231987f78668d28bf027b", "size": 1068983, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-2.2.7-beta7.tgz", "integrity": "sha512-MNW/0IhQbzvEp1PLCjGjG7oa50KL7iLboOcUhAm5bP5DFuLl/09KdMCHLJkfYXg0SaWCRtEIfYSRz73JkYsaWw=="}, "directories": {}, "publish_time": 1440127128228, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440127128228, "_cnpmcore_publish_time": "2021-12-16T10:31:37.161Z"}, "4.9.0": {"name": "echarts", "version": "4.9.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.3.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "canvas": "^2.6.0", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "eslint": "6.3.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "husky": "^4.2.5", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0"}, "gitHead": "90243fca100866ea802249a98df8b0899e68927e", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.9.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"shasum": "a9b9baa03f03a2a731e6340c55befb57a9e1347d", "size": 10241622, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.9.0.tgz", "integrity": "sha512-+ugizgtJ+KmsJyyDPxaw2Br5FqzuBnyOWwcxPKO6y0gc5caYcfnEUIlNStx02necw8jmKmTafmpHhGo4XDtEIA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.9.0_1598587467985_0.6465890981116671"}, "_hasShrinkwrap": false, "publish_time": 1598587468328, "_cnpm_publish_time": 1598587468328, "_cnpmcore_publish_time": "2021-12-16T10:14:36.229Z"}, "4.5.0-rc.2": {"name": "echarts", "version": "4.5.0-rc.2", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "git+https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test": "node build/build.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.1.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "jshint": "2.10.2", "eslint": "6.3.0"}, "readmeFilename": "README.md", "gitHead": "0ebdeaf42d8a8e3830c160fa9cd27bcc434ce502", "bugs": {"url": "https://github.com/apache/incubator-echarts/issues"}, "_id": "echarts@4.5.0-rc.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "8019fd8deff79514a53780c1137adde8ff9f6330", "size": 9994228, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.5.0-rc.2.tgz", "integrity": "sha512-3hj+2DoYJgk5Mdd131zvDX84r9HTjWUyDU6TVpp+KReT0ILlYilvbVwWf3pV2ytDRJRIZIGEk2xg0aQel5PL1w=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.5.0-rc.2_1573207672378_0.9752255132050776"}, "_hasShrinkwrap": false, "publish_time": 1573207672900, "_cnpm_publish_time": 1573207672900, "_cnpmcore_publish_time": "2021-12-16T10:16:33.370Z"}, "4.0.4": {"name": "echarts", "version": "4.0.4", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "dependencies": {"zrender": "4.0.3"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "rollup": "0.50.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "zrender": "4.0.3"}, "gitHead": "871af5ec38649984ca326ce689603ce8f51ec85c", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@4.0.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "36aa8a47d33543d3dfe6a7bc1228ddc3a1e41157", "size": 8966335, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-4.0.4.tgz", "integrity": "sha512-PDWGchRwBvMcNJbg94/thIIDgD8Jw2APtbK6K9rq1X8h6rQIdQ3IFTEvRwGS9U0zsUgJQQwXFLXIw+RJ/EH3fw=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_4.0.4_1519821723926_0.609958195181455"}, "_hasShrinkwrap": false, "publish_time": 1519821724344, "_cnpm_publish_time": 1519821724344, "_cnpmcore_publish_time": "2021-12-16T10:20:59.286Z"}, "3.2.1": {"name": "echarts", "version": "3.2.1", "description": "A powerful charting and visualization library for browser", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.baidu.com/", "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/echarts.git"}, "scripts": {"prepublish": "node build/amd2common.js"}, "dependencies": {"zrender": "^3.1.1"}, "devDependencies": {"coordtransform": "^2.0.2", "escodegen": "^1.8.0", "esprima": "^2.7.2", "estraverse": "^4.1.1", "fs-extra": "^0.26.5", "glob": "^7.0.0", "webpack": "^1.12.13", "zrender": "^3.1.1"}, "gitHead": "17da8048717be39112f7088f5234b63fcfdb2bba", "bugs": {"url": "https://github.com/ecomfe/echarts/issues"}, "_id": "echarts@3.2.1", "_shasum": "d354a0ff4e710cba8aa1384df8a092eab58e2b36", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dist": {"shasum": "d354a0ff4e710cba8aa1384df8a092eab58e2b36", "size": 3896920, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.2.1.tgz", "integrity": "sha512-zk2lRT0zNyn4aasvRNErsE/XcMxs8pcTt3cwDAjmIkuUPXjMl2igMLN5sGJ9K0XY4k8XfzLXUgH3k3JClVJVDA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/echarts-3.2.1.tgz_1467631732535_0.5836196839809418"}, "directories": {}, "publish_time": 1467631733031, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467631733031, "_cnpmcore_publish_time": "2021-12-16T10:29:26.727Z"}, "3.0.0-beta1": {"name": "echarts", "version": "3.0.0-beta1", "description": "Enterprise Charts, build for webpack. 基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "main": "echarts", "dependencies": {"zrender.js": "3.0.0-beta3"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4", "jshint": "^2.8.0"}, "scripts": {"test": "jshint src"}, "repository": {"type": "git", "url": "git+https://github.com/luqin/echarts.git"}, "keywords": ["echarts", "chart", "graph", "webpack", "canvas"], "license": "ISC", "bugs": {"url": "https://github.com/luqin/echarts/issues"}, "homepage": "https://github.com/luqin/echarts#readme", "_id": "echarts@3.0.0-beta1", "_shasum": "15b74ae471536c015bbcec74ed01a8ce3f65ef48", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "15b74ae471536c015bbcec74ed01a8ce3f65ef48", "size": 2258315, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-3.0.0-beta1.tgz", "integrity": "sha512-gBwKgzx49u9SxrAi1FCTU5QgMADzThvtj76q6SgHoICS69kzksLHuuhjkE4Pqw3EDA+tTmAMDUpFopV2NyHdgA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1449324228534, "_hasShrinkwrap": false, "_cnpm_publish_time": 1449324228534, "_cnpmcore_publish_time": "2021-12-16T10:31:18.621Z"}, "2.2.7-amd-beta2": {"name": "echarts", "description": "Enterprise Charts，基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "version": "2.2.7-amd-beta2", "scripts": {}, "main": "echarts", "keywords": ["echarts", "chart", "graph", "webpack", "canvas"], "repository": {"type": "git", "url": "git://github.com/uooo/echarts"}, "homepage": "http://echarts.baidu.com", "license": "BSD", "dependencies": {"zrenderjs": "~2.1.1-amd-beta1"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4"}, "bugs": {"url": "https://github.com/uooo/echarts/issues"}, "_id": "echarts@2.2.7-amd-beta2", "_shasum": "068e2fc239f90646589e4aedb0215b8164d0cd1f", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "068e2fc239f90646589e4aedb0215b8164d0cd1f", "size": 2744071, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-2.2.7-amd-beta2.tgz", "integrity": "sha512-cAEK3d82CYB+EkVaA7vDwzt3NPVB7kXQlHA+1qnIm+ZcqBD7Z3dAD3Ghfx2F/7bK9K24c2r0yFL2XE9JY7kWHw=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441262731072, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441262731072, "_cnpmcore_publish_time": "2021-12-16T10:31:24.836Z"}, "2.2.1-amd-beta1": {"name": "echarts", "description": "Enterprise Charts，基于Canvas，纯Javascript图表库，提供直观，生动，可交互，可个性化定制的数据可视化表。", "version": "2.2.1-amd-beta1", "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}], "dependencies": {"zrenderjs": "~2.0.8-beta1"}, "devDependencies": {"del": "^2.0.0", "gulp": "^3.9.0", "gulp-plumber": "^1.0.1", "gulp-replace": "^0.5.4"}, "repository": {"type": "git", "url": "git://github.com/uooo/echarts"}, "main": "echarts", "homepage": "http://echarts.baidu.com", "bugs": {"url": "https://github.com/uooo/echarts/issues"}, "_id": "echarts@2.2.1-amd-beta1", "scripts": {}, "_shasum": "ac236665cb4795f6ae99c52c02fa98ef7ede19ea", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "luqin", "email": "<EMAIL>"}, "dist": {"shasum": "ac236665cb4795f6ae99c52c02fa98ef7ede19ea", "size": 2626421, "noattachment": false, "tarball": "https://registry.npmmirror.com/echarts/-/echarts-2.2.1-amd-beta1.tgz", "integrity": "sha512-h/h53TLKK4NXgLsFL9FzN9Gi/NHzOCVhiRSBRQT901Q5mqra1mlOoJJvM3ZvjM6DZX/ZsgQumD+kqEe5zDWW0w=="}, "directories": {}, "publish_time": 1441261050667, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441261050667, "_cnpmcore_publish_time": "2021-12-16T10:31:33.767Z"}, "5.3.0-rc.1": {"name": "echarts", "version": "5.3.0-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "readmeFilename": "README.md", "gitHead": "0878d303ee5d798b808d3bab1444c5ec3e7e60d6", "_id": "echarts@5.3.0-rc.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-peaOPUjw8eigntRLWQr63vPUJvZSzt2VMV2iRPG24R+XOGLCYaajzeqa4kmn/9MJmgTdcgtU7gJMqesmrsFFtA==", "shasum": "ad098ef3e57c994cc6fd4e5eb10ccf2d788e46f6", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.0-rc.1.tgz", "fileCount": 1234, "unpackedSize": 41323777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7QR5CRA9TVsSAnZWagAArpYQAIk88uwLtYO034PfMyQi\nPgY9IHK3T8FtD2u8mQ0btaM7CIMbyufi1cCTt+NlP4lmSzyeeASgwf9otwVO\nUu0l4ApOkUT3jFRjTafDNYvorlRpu4yFdEcwPDxowHSGK1XAgoI8N/0BLxYG\nn29fzKgYlwED7FIPL+kABtLTatXVnd5CO8SN1vgtiPgBh9sUugHilDVEaXpF\nXYvNz5lqDwVN3GpvCnJ3dUY2evNCA0mBJuJ73s3jTUKaUOEI6AU1+10TDgoB\n7xWOrJScF0V1BkBRLQ+E9nIrSr2TDd2cmlMGWKwpZu+L3/ohBt7/8rBJIGBc\n04EcnGpooLfC0mvCExPNBEpS1ILacUXvAPstyPyfWK2QjyQaistDPYWnK5/z\nPFVgfDz+UWwUJJPmEkynECl8ww+XrklTqGqftTz4dQ+5QES17I4HeGw5f42E\n0lmkJ/VzuL7Tieq7/q8gQBQkgaQMaxXfcdf2ICs1uovCnKlNNVh/oZckI96x\nqZgFFU7mVQKjn6ZN0yR4N8xGk9NqKrQPLWc0s9ZzrRnfwHWmXjnlm96VPnI/\nN8AyfHyjvpKS7wKGTynIvTsoLTALKMQl5u25ReSPUsIMlUHnkzRMRRO6N8CM\n/E3iZBKXQpNAg4XtbbasqVzQkw7isxrDStndeIXjsKjFzlUm5cWweDsU5Ya/\n/i96\r\n=ooW5\r\n-----END PGP SIGNATURE-----\r\n", "size": 8456873}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.0-rc.1_1642923128887_0.2231488221201401"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-23T07:42:21.991Z"}, "5.3.1-rc.1": {"name": "echarts", "version": "5.3.1-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "readmeFilename": "README.md", "gitHead": "06ee7c1378c758dd50957df8dbc2237e075cfac7", "_id": "echarts@5.3.1-rc.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.5.2", "dist": {"integrity": "sha512-/umldAwtvR5hCq3H2et0yIARbDraDZibLq+69t8wzfgbWlPcXRT3j1P3ptwrmiCHCALf0A7nXgMsFG8IkigBKA==", "shasum": "34196e7987700e5907324282d71315a4f452e516", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.1-rc.1.tgz", "fileCount": 1238, "unpackedSize": 41364538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIcyhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUjA/+IbymuAM5JZsf7O8kKcOEe652gxajkCb9cNMbKzVvZHZAhuIf\r\nfQjudozxufvZjb0Ht/FpCy9c4Y3RBQnoKppSIO+GH14z573zc24Uu0YHpxkM\r\nH62+9bwpyvidyEKEP6cKwXZSDfxU7MX9de8n0FclS3uQBbedlTC9KVZbHEwT\r\nOO/aWph9o+DnoRHX8lEqbFkJAxXyf1k+CHm+I4LVR/vLtP6eKtYA/rBMuWYw\r\nUJc7T7oQ9DXZjFth/puMfBbm3DadnxZTw4IsroR62rx5WsVLU50n0I88jQjd\r\nhjJyGxxTdcQoNm8UUxPuDjvZ4ADJ59szqDc+mXIuu6tpVO+MfkQvwtjxQ3PQ\r\n+oq61C+ADaonI7sjs78ndOM4Zobup4zPyRkEMf+VvmiVmxL7GkR7+VLU4xCE\r\nQSzGrMsDhKFIHRV9dSShvKHVCqwZ8Te6O2nqYR8R5bLTMEkacjpDCIyks+r7\r\nng15CezsNGTbEhMeg7G9VPYp3r48O1qNo3FzSGiNRvFKQC2wBwt2VSkNg3pL\r\nCzuRrv5oCe9+On3XHKU+9MKrgZQZZeO9oi+4FU4RmPWLYdcY+TGXtQEqQLJz\r\n/AEvX1nKoH0Jq4bm3Ph/7UsjzuuIs/1a5gh0hU/6QYIOhyvvwfKwAAbpUMqR\r\nViFq/DjhfGQcI2FJvo9IBmFX7c8DtntH+B0=\r\n=TAFj\r\n-----END PGP SIGNATURE-----\r\n", "size": 8308135}, "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.1-rc.1_1646382241375_0.20516852865686874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-04T08:24:10.366Z"}, "5.3.1": {"name": "echarts", "version": "5.3.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "06ee7c1378c758dd50957df8dbc2237e075cfac7", "_id": "echarts@5.3.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-nWdlbgX3OVY0hpqncSvp0gDt1FRSKWn7lsWEH+PHmfCuvE0QmSw17pczQvm8AvawnLEkmf1Cts7YwQJZNC0AEQ==", "shasum": "709e03aaf8c7a4439788272ee5c500cf0dce7e5b", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.1.tgz", "fileCount": 1238, "unpackedSize": 41364533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJah3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqrjg/+Ishakslhu1ZLL31BZ0mw+A5Wh2A7eItrelVGbH28B1DyqGMq\r\nj0E015nNWrNSWsLeMD51G82TtxmVKN585rRF71ysTFQr1LCnkI1nc0vOxtU+\r\nKwDnBIXakk/uBKcu1dFiHUiVYtK8WKgpqFrTDDgu9PFghEaY5WH+zEcpTwp8\r\nCfTbqjAJby6mMb8Put0iWkCbiseHOowmdAp+h3GUO0BwM/xneGs03x83aL4c\r\nD344l4b9R3gtX/XEuWEKkPM5g7FEoTmU5RYQTTc0oEeGd/8eTrJ/rHP5GNLP\r\nZ+oTrLJ3vUoAbgEBMKbNCt2UbfQ/7hBd1LC6OAN5bTDixggdkKQGjm6He60O\r\n/rTtMH3VvB5wV/EvNLemLb60cZSYgpzPuv+/1/XTX+8gcWnyWMEtFgWaHaQV\r\nzHCsnwXgfM9l13awMAPy1qj+IuB1b5LessZEBu/rWOmzaQ3s6DdXUCerusLt\r\nScpXxz1y+ZxIkbE+j0RqSPY/6YyHAdVIjkVmVPxUI6eNUsK5Qymb5ss58xu6\r\nc96YlkPADDyPUyqwBKBqvWP36VaOJUdQ7XNSjs2kZ+OHQZiPqWaSEtwGmM1j\r\naAYxjmo8DjvdDQu8ohHmDefOJ8uh6XO/cre7B6Ueu2mrBXwW2Efwti+Jxawq\r\n9LknQM1UV5JRx3Swe2jynV6wEc70j9W7l0g=\r\n=K7Kc\r\n-----END PGP SIGNATURE-----\r\n", "size": 8308133}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.1_1646635127539_0.27021562296218415"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-07T06:38:55.859Z"}, "5.3.2-rc.1": {"name": "echarts", "version": "5.3.2-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "readmeFilename": "README.md", "gitHead": "8b2a4c4fa878b8b1147b7d14356124ccc947cf2a", "_id": "echarts@5.3.2-rc.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.5.4", "dist": {"integrity": "sha512-v/HjefNi+P9NsJYLxc3cIBUgc9qgbs9MR/8opRKcAHwHBrdd4MVAfLIW42y7mymVtMs5IIFk6xyBM65n405SZA==", "shasum": "40ec27c0385ee4733ab5adde4c3365cafa8fb9c3", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.2-rc.1.tgz", "fileCount": 1239, "unpackedSize": 41529080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQYb2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdBhAAl7xDQOHrwdj3bn9iTusjc3NN91UWLB0TEAMbi8jlAMy/a8MF\r\nzx7QvWt5u7pREGidTHmOVIxNgjFVK8rg5RdS1AYUWgnt9mmUEvHuhOOvWFt8\r\nWNFmrtju6QaSLIrD/GiiY94xsAanOd9dF8AV3dxQQha8gRuq4OJjT7RHTXjl\r\nWG+aUGcCw6Cv2XDyBz0ze9QmzFtDO2/oaHFXhPvzxAZ/PG3w4e+8q3zWu4im\r\n7iiqfoj3lkIhALpS563VSBv5Gpw9zvBJEiKpFbS7G1251oZ9fjz8Mz15b0TA\r\nQY3+TyDr8I/FzS0qtUGeXeHnvKPm7fdNwz0tVDC6WJqXuzubolpRyb+TJRU0\r\nYq7pj6/KE2fJY9vqOTEwoEH5b/C1t7USR+Iv9pOMZMiNu5pKvo1YAfUaYuE7\r\nBXfgYxYvMLBsi4zFGdkWbgNA9bBux/A+OagTIOQn61Kn9ZL3tY5u8OfKHyM6\r\n9O0WpRXnRm/EitVoyADlpIpxGnfS9IAZbvTxoZ/953ENsdjV82M0Rb0KQill\r\ny/St/KT0hgwmfp9XnE+mhYHNc0ZOqBgTtfugifZ8QVDXJasUiQ0FqDmZ+dXr\r\njEBiwTU8MxgsOfTXEq0eGi7/QiQYjjTJZhb3TrIWQ2jWL9vvfAh97RHzzdqo\r\nCwClxKJszO51AdyhO6QNPVaYnvQEowE8GUA=\r\n=B15b\r\n-----END PGP SIGNATURE-----\r\n", "size": 8329142}, "_npmUser": {"name": "plainheart", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.2-rc.1_1648461557850_0.8932680004797653"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-28T09:59:25.208Z"}, "5.3.2": {"name": "echarts", "version": "5.3.2", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "http://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "8b2a4c4fa878b8b1147b7d14356124ccc947cf2a", "_id": "echarts@5.3.2", "_nodeVersion": "16.13.2", "_npmVersion": "8.5.4", "dist": {"integrity": "sha512-LWCt7ohOKdJqyiBJ0OGBmE9szLdfA9sGcsMEi+GGoc6+Xo75C+BkcT/6NNGRHAWtnQl2fNow05AQjznpap28TQ==", "shasum": "0a7b3be8c48a48b2e7cb1b82121df0c208d42d2c", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.2.tgz", "fileCount": 1239, "unpackedSize": 41531315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFdvJDcpfgMkPq6VDQYezRhSbpbnFQ8tED4Akvkpy3bQAiAxPI8s5kB/xhzEKMn3lfstTLoBzt6GCYTjCKeBngBCjA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRoOyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptdA//Q5p5cI2IA/x2UulGtiaXtSNQIXjlbFBam9o/oD+O5vD4TLVx\r\nr7g3mLjMz5i+Qo93C2pGFWMGKRM/yIu5xBXNXzIbqOvFkD00/IEpJtZmlrG3\r\n3WOlHPOI2Siup/P2SV0ewYpfVFP497JSkW8KqaqfvkLdWfBqGeuu5TU65URq\r\nOydlyKtPil0ZLCbCGMM8uG88meWqMGnVCBl+MnEJ2LGYcqZGNW7eR7dzD7dA\r\nAxXfSl+OYe3bSdJlRJrgUdjUgUP8d33J6TjC0mfMDbcDLwFvnwZDgLTbyAWv\r\noW4v3GmT88RLIrlXiB1DNvG4fBBcVjN4bX0ZqR0/Z8LM13Ptp3dn56GeW438\r\nGdomK0z1nxhkkGXhdfGqxQ021J6IXxGEOmuoyfZY8hkheAnR1+rMYXi7AFmA\r\nVt5X7XHtS68Qe85F91RgW/OWy79N5Q9fCPtubvBpUENmJPzbde6a1iv1QO/y\r\nzhOOmkLDBUA+i7uoaGFYqqdb76dZ/jhwXFgw7yRnZ8uynt4rJnDEGO36JxzV\r\nXKQvrNXp5YyAQO7xWjDPSa6I0yADVVZt4WLPW55+jFz8FAQF/IpBwCBI2ZW2\r\nFPnYZGDv7XagYDNPl7q3OsUik/yRB7JTKjmmTC1w9mrURl9KUMhDHJKkJesS\r\ncU/i6p6xjyTKlkSGhU1QiFyddDCIbGfZuKc=\r\n=WKZe\r\n-----END PGP SIGNATURE-----\r\n", "size": 8329432}, "_npmUser": {"name": "plainheart", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.2_1648788401710_0.1696504135553336"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-01T04:46:49.108Z"}, "5.3.3-rc.1": {"name": "echarts", "version": "5.3.3-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.2"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "86eba716ffa3d76c017e696599494dc3e83e9260", "readmeFilename": "README.md", "_id": "echarts@5.3.3-rc.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-WELthQidgwJtMSfFKyzak26MXnROYHafV8qR+mIU3aiWPzLaL4DNU840jkEGYMDwhOlprdXeKv+O5tlGMSRNZw==", "shasum": "4942ce8e7f7d40c58b709cef89fa669615e5e880", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.3-rc.1.tgz", "fileCount": 1239, "unpackedSize": 41423557, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3G0teC24bWv5fWJNFkQ3OQOvwhlxy7lvZTN5tBIfMlAiB0d3aW9OOcuIWGekWaVpfmfKT2lDp+vEatFBLJ82YtCA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiox6bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMMRAAihlZmwd/RhOV6QUOmXoLukL+XJ864AjBVBch/mz+XfoPmCT3\r\ndkMW8VXJjDa3DFArI6//05owYL2DtjE1mR6N+HbjxA/lfuscISSeqWYB9ltm\r\n+iSmUMqdVc6DKpnXBCUPMBotxEyb7FvNuEvUu9JLVRf2o3QDhf2RLPdyfJmU\r\nEL0OIfUq/mDIQVJDdx1J29ZT3Zp2TjzR8M7EzQ03UBPvLiUiPwsxQOF3dSTV\r\nZYcvLJ+pGm4fyr5jC1TFtu/m87iiKXZMgNxQp07B5dXX1D7o8VmIe1H8Dszs\r\nUwm27ClQhFThbc8Pa8ucTIgx+5/76bKW5milBp/mqEOjnceKQkI16oxu55KW\r\nI8nM005oK3n2zJvrZEAwDGTzObFgcn8LVRQoJILDVMm4gPBw/rUr7odmRNcn\r\neeGv8WlTnTODR8XsQ5eAELF1TKJEgMjzlw0irUnKltqSlQuAK6UG90fflK1a\r\n5ZNZXGh4SVALY1UNYSjqAfjV+kniMr3zNCRGhb0wAF0Wm/sMISvMoXzJNjpX\r\ngzYMRRNyqm1/wUZ5qxuAQF5UfpoIae7ZMIxGpiWABS2wSFxx2S5KAFB0UmZK\r\nhDVpozIM0y9yL9jxDrGsniX2ibTrq9ehOXDOgoq7ocjb8hUIshzF6Jh4umY0\r\nD0YpJgwyWy4C73tHdKGUwkYuTD58SnkqNhM=\r\n=pUFc\r\n-----END PGP SIGNATURE-----\r\n", "size": 8327530}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.3-rc.1_1654857370787_0.9770339727744011"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-10T13:19:52.275Z"}, "5.3.3": {"name": "echarts", "version": "5.3.3", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.3.2"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.29.2", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "86eba716ffa3d76c017e696599494dc3e83e9260", "_id": "echarts@5.3.3", "_nodeVersion": "16.13.1", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-BRw2serInRwO5SIwRviZ6Xgm5Lb7irgz+sLiFMmy/HOaf4SQ+7oYqxKzRHAKp4xHQ05AuHw1xvoQWJjDQq/FGw==", "shasum": "df97b09c4c0e2ffcdfb44acf518d50c50e0b838e", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.3.3.tgz", "fileCount": 1239, "unpackedSize": 41423552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvhlJtgeuUEJdhUxKz4gOh+L3L2cpvMctI+tgMJyJDyQIhAM2d4PW+2eNVCNqTx02G482y/fWHwEhRe3j92t9e9JS1"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqAV/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1xA/+M69cUM4rTbcxePgtrjXl+P2k2Kj9BMEdBtsr1KENAgBEZ5Fp\r\nT/GhtC9g935D39x93Fqlx3yBy/ykUKabVa7RE3SzCYoM1Q00XkB+Oa4XG0v0\r\nhFmaWDrZu7DytzxO90nyrjnrvM4M2pKAbzJzL2+PhqP5tHl7Ai7HkrojCH4r\r\ngWOiCeYVxN87Tz28/0W/fAwCg7xDsuT+5Kj69QSzFNkYvLA0FQq+XFGG/yQM\r\n2ULXdgmTVDH5G7cnFzKN77DR/tdVUX9kPdWgdJ6HXhtkI1ykmBJ2d+in+6Z2\r\nyGo7GPD3jWNZodJNWaDeb82Q267dkyBbvEM7se7B2fLKGglmuHtBsUE0gDEj\r\no4sIOPPSGDToGaYLJYfpsQlPRvFGUM/2Ujl+MpsgvHr2RZT3de77u/qKbN4w\r\nTGIByqpj6CZI/Q3UUKA1iWdSJnGHosZQuBYaicpEXKHQ1/ZSgoI74BcmzcyG\r\nU5qr3JsJoLiy+KCfkErdo2iiKrrEiW/ALmJMNFLMC+YDgLHqiJ3Ks+kq700D\r\nt+ogsU20e94pKwzUP1QVWZekxGWrBDuugnFUw8ZLufT9kKA+RGLHyl5muJ9x\r\nXP54jVEGdOdjxk+xzKNfkX2DxBmoQdflaoPrGDSLvuYOIDTQdGTl+xhlmf/N\r\nx5lW3urpyyORVrR61wLQeeVbCh3SzoT6pv0=\r\n=+Hwo\r\n-----END PGP SIGNATURE-----\r\n", "size": 8327528}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.3.3_1655178622572_0.2717746045683078"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-14T04:19:22.713Z"}, "5.4.0-rc.1": {"name": "echarts", "version": "5.4.0-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.5.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "readmeFilename": "README.md", "gitHead": "6347c2c3e9707ae34d004d72ede087e9eb23359d", "_id": "echarts@5.4.0-rc.1", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-0ayhh5kbBvGWwjuv6sS4IvHpvcfrhkffVI+YXxPGdEwP4gDnqMs2E8P7irhTWgofYJ03vbMtIrije0Gs68l/+A==", "shasum": "da3f8efc6088edc01e5df1a9d57b3b5b1a8df382", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.0-rc.1.tgz", "fileCount": 1244, "unpackedSize": 41583343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHGtrAKDNyfqMrU6Ta9ROsnrwGIl5jDyjyUzJSiumH5CAiBtOTxLTA1OJK6LQpC4JIomDQFYiDlfOWZpijSqyir2Sw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIMCnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBDg/9FMtMeTsnh/9u/ucWjVRAQ3eltTt4tgMkav+DCwc1g3cfvtPk\r\nEj1b52smE0w1rzrBIlP6XTideuC/ezikZwPJt/zPbu1544rSnlDLEgatztWt\r\nO4UUGw+zzqauzRITv53FMCgTqCD2lkIIAVocPqeZfpDLQJ3EWC1aqVcQlg2F\r\nbRBjqthPTiMaSsOFJSidoZGmFsBXuWdvObj962JPWJmSwYHSl4wzjgC2EYmH\r\nNZFcIHZxkVQAYQ7SEw3a9c34Nsd+1ublnDt8DYNQYxqXvUZN9W6WkkyREf+r\r\nvV89nPXBorxN6luJSrMP+VYx4H4o8rvE+BVuSQvO9Hj8m0YN5LgJIKpjDvlv\r\n12udsOm3j7/MZydNv540SNRPSQitSFGBpqo+HRV8XDiE+lp+sUdv+pHiFO7G\r\nqlc15m9qjWx27gFAH+fGXnG2WoGkjgtksCYCdSBahbjRso28o0Ig+UrysdGT\r\n0RE9eCzsiqxcO8n+ylgPgXBzvGIXRpzr1wIYBZeXxU3lnJF1b9WRL8qQkMOA\r\nJsPGLBKMGaIXp3svClYKMRMaBGuGlaCFJ6vgbTFocw4BAJhERDNpUphZtnVf\r\nBW37hJLw2in2YmeHcKVBMn1qzlDSxN5ZYbXLQZb1UV8GriykgsxGwvIL2zdF\r\nyqNQ01hoc6jKo8Jv5lmBkBUAI/vvd3KnkYs=\r\n=rGrN\r\n-----END PGP SIGNATURE-----\r\n", "size": 8363535}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.0-rc.1_1663090855167_0.6989001343171035"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-13T17:48:12.820Z"}, "5.4.0": {"name": "echarts", "version": "5.4.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npm run dev:fast", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.7.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.10.2", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.5.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "6347c2c3e9707ae34d004d72ede087e9eb23359d", "_id": "echarts@5.4.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-uPsO9VRUIKAdFOoH3B0aNg7NRVdN7aM39/OjovjO9MwmWsAkfGyeXJhK+dbRi51iDrQWliXV60/XwLA7kg3z0w==", "shasum": "a9a8e5367293a397408d3bf3e2638b869249ce04", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.0.tgz", "fileCount": 1244, "unpackedSize": 41583338, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxmy16MkPHOL/7L+rpyZ/DBQPqoOrpkChqhmr/XJ96oAiEAvl2MNru8AwPaFEEC8v/HFkr6x95nJ3JFlvglyDRNtSA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMF4DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI1A//YcmCCXOIv+bIg74kRJf1ubBzgu2jGWuiYX/UFcqbOp/LdBDl\r\nGIV/FehZCWtFvMxfmcRzM/C/O+yVXWxbLlGHFtEpBUVUmGNlY4LHP0cseVuQ\r\nZBB4rhs6DVdoo7iSSo/iAdzPH5jDvh443UCSoMV02zE/Nd5PH2KZpdSe6qP/\r\n9gLcliOYJLnKyZo4PGFwM2Fd8mB867lcw387Mxm9XcGFMXdvOuqb8zklQhEj\r\n6i+s52GjqY13IZm+Rl0fi4wyRxDy2FkWx+k6vUkYkn9YiRyK+yyN+sHOL/60\r\nYGaVmhg7AmARYSpY98UR7C+g2rXZMc/Bw0yjI2wIc8PzwMNyHoAC1FfCfU4x\r\n+y6ZyVU0b3xgyB5DNQ7xHvhnjMSnqWAVPJH6zq+GtGS2eyd5tn12Bg8G9lrO\r\nm31ewxU3cb8aNJ9DCxtDzYvYOMjhVunYuHlv4s+///SqhRn9UNxBHhUsa+v1\r\nVzHONjHF6Z9WbZhLaIAC9YKUzQRjpzIXQJkpsksLK/QEYse31NMqdZvxFpwe\r\nnlwft/D/FRTqAoYWmkJ2EMyTTZVaJ+WI+KM3RpMu20cURgS6OIJ2JMbwpe8N\r\nPob169cX2CTr4TVppEGKTpSGiWumetQgaXWFKIZZGNlplmnx+3GO39R2E5l5\r\nWprJIrMu6Gnmxgcg/UVAOOTB6fO+h1xyoEk=\r\n=c4Ua\r\n-----END PGP SIGNATURE-----\r\n", "size": 8363536}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.0_1664114179541_0.06902996194365185"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-25T13:57:04.787Z"}, "5.4.1-rc.1": {"name": "echarts", "version": "5.4.1-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.5.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "readmeFilename": "README.md", "gitHead": "c308ec76f20f5dadcd04d2a10eeee9e06fbdad81", "_id": "echarts@5.4.1-rc.1", "_nodeVersion": "16.3.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-naWsXfZiCidaKNmhqEJcQwdcg7heFEHIJtNj5OJpgAKmfVv3qgnni2SC5CYEJAlp2HWp49teF8x6RYIvqsAVeQ==", "shasum": "a1b4c1b7dc724838a32f742cbd47933b76dd92b3", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.1-rc.1.tgz", "fileCount": 1246, "unpackedSize": 41660938, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA3BauwsExfcdCxnIJtLeSjKcuhOfYnAflLOiaVdgDUpAiEAllLY2cmo0SQhUA+LlBMMWbpCPii9G6FD4n/1JEP/0iI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjif3WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpb8xAAlnMBvkHsmQdqZ+y/qmMx2UjHkWCk89YcfQv/ervz8HdaoOCZ\r\nUVUaeMEuyr7qHdJN5rndQQcbeQ+o6QENsayFgMHQMy2xHzPjox3brVmckQ69\r\nu+Z8qv3GqF5BbeA1lf3Goq3Mst40/UYNFkg02EPR01/GIdDdDHxh7AA6AyeM\r\n1jzsAcF5HEebQ/3XaEyrBWoH7lJI49F32On6G6KcGoGAFmVwHZF3cO7FkqlU\r\nnY08MWTiha4UgvnDTZn+I82SPeFmjuzNdLnBd8dOJWDloi1Gf8rTbp0ygmau\r\nCsjRa8/oaLizUFDUAX6irNFXP2+ptwcgVgaJm0X+kW/atFBVFufjzy+Rl4sc\r\nsPhSvYEb004I+DcchvdqQNUueHxnnmxnVtppPBMqWh99wF0q5zV2DCgg//eB\r\nxAE00IcS2QnjIFjqADQBaWZbuSbTBFGO/gzNgKjTjDgyxGY9TSxX5lSkS3gu\r\nWqwk/osOB+/hOQsDgFXBf/VoK2v76ukkpa7iPIrz4lDVTNT1TSZ5zfb6iLoX\r\noTzoXq/3lf95ZenWUihegsnmbsU84KUZZvrYjtkWkvLUpu82HZuAIfF+mI8p\r\nttP+GpGXiLeWTJIO5HqgSmMFfq4DeMqoGvssdUXIqMB/+5Nrxjxx1YbCGOiU\r\nKpEWR7IdBbuq6ZBD8/WAYuLiaC5tlbmk1/c=\r\n=ri+W\r\n-----END PGP SIGNATURE-----\r\n", "size": 8369830}, "_npmUser": {"name": "susiwen8", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.1-rc.1_1669987797779_0.38194723904164185"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-02T13:44:12.897Z"}, "5.4.1": {"name": "echarts", "version": "5.4.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.5.0", "terser": "^5.3.8", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "c308ec76f20f5dadcd04d2a10eeee9e06fbdad81", "_id": "echarts@5.4.1", "_nodeVersion": "16.3.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-9ltS3M2JB0w2EhcYjCdmtrJ+6haZcW6acBolMGIuf01Hql1yrIV01L1aRj7jsaaIULJslEP9Z3vKlEmnJaWJVQ==", "shasum": "d7f65a584d78beff62568d878b16151b3381811c", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.1.tgz", "fileCount": 1246, "unpackedSize": 41660933, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPpooJEhkFEp026sKWKqxZUAo5067H60WMgl3O2GoSIAIgcNQP424EqlWbz/wsEKuKnv1HXsR+BpVsxLHfiVHzcc8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjk126ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmob8Q/9HseLQ7aRcQMfRubcfuu8jBmKuQXTorYfJfXLUK3jYO71FeE7\r\nstuzJlZVcbsTpejTu5wCdaaV8UH0+h5yG1OLS+ReX5iads+9Bbkcup8MFLrX\r\n9mGzfetXtUbxTvoZMShS8lfh8zM0bzJT7kOT3r8YFZV3tNKUlYuL/UO2K/WU\r\nFybcm9B0SGqMYX9pIqGH3m2WaNu3Hvb7s7ZgRHVrMfdV33dEfy+wsiWWmo+Q\r\nWyGOr+dbO5R5XSSnZPgZa0qp7N8l6ykbCbqaoNAiPIHOpEPc3WfIiqFnM7j9\r\ndfQevga1De3frr3Kili7W2znw3xLuBVLvMD64Rw/2tItOEDeoGO+5L98XwOZ\r\nO1KEqg791ke/NgmMv5kaUFjtnByTRgd9fLOUYCZ2+sNR9WsfwK59/rjwjOzn\r\nsYZbfgQo+IarhaCXPpCAHLoNyCRdYsDct/V4b31UCF+saO21ql6pheezuQjt\r\noRqL7aePXH8IBfzHj6CnVnK0Q5yz6917FBI6ZjtVODELNFCgL9VQT9vP4p4t\r\n9ZLH0deJGI0aBk/GiLcAJHWyCHAueVko9foaJ36Jiks3MOOh56wQKwE9kyMo\r\nh05tUXGxXsmIB3bFgPG6XH2E9GWonhbrx9JOOTS8fhAqrW7Umt5q7wfJD7Mh\r\neWVziRaBAPTlOhzkL1eT7CedjRAL4NwhpmY=\r\n=fvf3\r\n-----END PGP SIGNATURE-----\r\n", "size": 8369830}, "_npmUser": {"name": "susiwen8", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.1_1670602170195_0.5607100762331165"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-09T16:19:26.757Z"}, "5.4.2-rc.1": {"name": "echarts", "version": "5.4.2-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.3"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.5.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "cfb3a8a544440588d0d51a2cf622f340f0d6ebd8", "readmeFilename": "README.md", "_id": "echarts@5.4.2-rc.1", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-y+YYURpxYG8EVXe36S9MAbrdO47C/cQy78TACLngRlvC7653vqcRGzZjTYCwHZJ5SMhWJkNnPNiAwXX2UPeeqw==", "shasum": "e91eb5319fe569f91a7b6e2fcd7849cf28cfdf71", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.2-rc.1.tgz", "fileCount": 1252, "unpackedSize": 41484319, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuu3PuEDP1qx3JWSiSlsig36E37FdMJpIhsffZOWaZ0QIgNW0tnwjEn8jfxN2QYUrDXSV9BcBASb8sv801e7hBzwM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkE+rBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpzig//VaR9I1q/0LKueyrKaBkutYAGkB2tC4ZomJqj0C41rLF53AjF\r\nA2WFm1PXuj3LwBgHWx9uCC7zCLlR4Hvn5Sh7wijCOH8KdaKjHT0Uo5uflj1H\r\n62QX0/wuDqK8YC5rGDn/Z2DGRTE+HC2YfrQLaujrOB9T7JMqCGTZURViGDgw\r\nEv/cvtmvGQL2LCxKrcxSWf79Z5WN6kSLl0ilZVeyacqYZWX1LIoE1PcSXEv/\r\nim2t3BQAcZlcexZ2RtwcOHSDW+37b2tS7KCW4fO0K2YD+y9LF2R71sm+VKdO\r\n0MblOl4ZPyoSnm5FqXgYYSt5gxfKtn/zhc4OVka9sp1T+rk7613fuOUAbmbF\r\nUknr10ugPFFJNxxYoPdAPTxFtbQNzlHW+idcRyZ/s8P7vqVuBE24Y/wwgvWG\r\nqgrfzjtt/7KOfllMRY1pQnIWJLi0pY9tjEuoJxF3Vf8d/aJ/b8CtuNqrvt3s\r\nxU6xZTes9dVp0XWZRhEt3LOHqVQW0wxQeJ1NjDiI5/v/kX49F25zaqdKL80c\r\nEt5+9kd4j9fmuI637/058R8/O3jIaUlY2/01X76xVmP/8Y0CaIF66q0RM12v\r\n1u5S+Nkq17jP3HmwwuIwzZJyYhdSXIlVcldTfYn2UktVO0nQ2QZ1H0nTQRxI\r\nwA5slOsfYExDWmD/AKFIXqhTDgqp9Jopa/I=\r\n=rRq+\r\n-----END PGP SIGNATURE-----\r\n", "size": 8149803}, "_npmUser": {"name": "wfsheep", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.2-rc.1_1679026880832_0.3986204898018788"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-17T04:21:21.258Z", "publish_time": 1679026881258}, "5.4.2": {"name": "echarts", "version": "5.4.2", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepublish": "npm run build:lib", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.3"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "cwebp-bin": "^6.1.1", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "glob": "7.0.0", "globby": "11.0.0", "husky": "^4.2.5", "jest": "^26.6.1", "jest-canvas-mock": "^2.2.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.5.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "cfb3a8a544440588d0d51a2cf622f340f0d6ebd8", "_id": "echarts@5.4.2", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-2W3vw3oI2tWJdyAz+b8DuWS0nfXtSDqlDmqgin/lfzbkB01cuMEN66KWBlmur3YMp5nEDEEt5s23pllnAzB4EA==", "shasum": "9f38781c9c6ae323e896956178f6956952c77a48", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.2.tgz", "fileCount": 1252, "unpackedSize": 41484314, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCORNZG1DdjwIcRnzLVAHxDqxzMRkIz2uWYWANLNg4sXwIhAJGwgst1pq4qZBGQ9FnIJQkxi2pKwE3ltUc8lQXIejef"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG/llACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3TBAAl3FpgpSFkc4JiFYSPzq5DlZFgwBNmtR7KNKFNEZ46tLU/Vmq\r\nAA0/V1BNBhYYl9zSiR/vL60mDaEBbh8/Y9Ucx7VwKEgOv9q3bO2mq8+clxHc\r\nY8ou4OsCJ6FIuHoUjbMeJgoVDXvMQKrbP/Dv/wY1t4Cs9FmVoxQ2wpSTaQpv\r\nNTfE/rr5NyD4Okj63tJlDEGkKbtUSLFUoC/YHcJ1bpK9h4aKvQTYQcT14MJR\r\nvSAjWvKEjvfRAbdVtIkzyW731WPv1r/uKdO1R4ueI7p81JKxMyfBXzi2ulhv\r\nDv6yQP9P2yanCIJXXqqdu9mkDIXxPNKyE0xW9mPhlJOcd8pPZjAKlBXLJpt7\r\nrttZ/MEx/9ycD21i7jF6t3uhg9+3z7Uc37ddcG8tmCzmZY/cn2vml96l+DI8\r\nHFu892vlGu1HC82yfE1eawMRkd9AFdEDmm1EOUlsnaBzLBGY+qmhjb8LqZZr\r\numDZPv03wj+TAgGQWipEpk9hutw6caDoV+Oyeu4AJYfzHyKcvaeuDerhdNTi\r\npAQZ/DynehOOZwhIsftRlcW7fJwwodJbkRTx0p5hV4rSR5B5LFeVk8CTjjJP\r\nR6XSC9xxo5hM5iLiDE6TAiLHhySvo9USNObhmHNj40SnUGj3uWz7vILYk+FV\r\nzOEMRlVFjXwvZa7QBSdeSYEeX69vYpDE3v8=\r\n=pBNW\r\n-----END PGP SIGNATURE-----\r\n", "size": 8149807}, "_npmUser": {"name": "wfsheep", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.2_1679554917267_0.15483552936493306"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-23T07:01:57.740Z", "publish_time": 1679554917740}, "5.4.3-rc.1": {"name": "echarts", "version": "5.4.3-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.4"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "18b361adfcafda1bd182e9a5168f5e917e1bd6b7", "readmeFilename": "README.md", "_id": "echarts@5.4.3-rc.1", "_nodeVersion": "14.17.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-m7iRnqOwuK1aQINiYPhDxo4ZOc46ZW+bzHTdFZqcIV67sUauFes7o6e34OpxPiFucs2D2B80PCpK8sMbEdHb4Q==", "shasum": "61106f6f5961f4f0e6ee5ab2323a41e0cffdce30", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.3-rc.1.tgz", "fileCount": 1252, "unpackedSize": 41691878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEKPS7ktyQXzg+Riz3mK+8FYxxfZw38fSAKahf/YD1soAiALK4p9+xCzVFqEX3unKvxx5tFah0jRWKKwyhTQyNP61w=="}], "size": 8178139}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.3-rc.1_1689321570340_0.6396482942942208"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-14T07:59:30.805Z", "publish_time": 1689321570805, "_source_registry_name": "default"}, "5.4.3": {"name": "echarts", "version": "5.4.3", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.js", "test:single": "npx jest --config test/ut/jest.config.js --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.js --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.4.4"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "gitHead": "18b361adfcafda1bd182e9a5168f5e917e1bd6b7", "_id": "echarts@5.4.3", "_nodeVersion": "14.17.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-mYKxLxhzy6zyTi/FaEbJMOZU1ULGEQHaeIeuMR5L+JnJTpz+YR03mnnpBhbR4+UYJAgiXgpyTVLffPAjOTLkZA==", "shasum": "f5522ef24419164903eedcfd2b506c6fc91fb20c", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.4.3.tgz", "fileCount": 1252, "unpackedSize": 41691873, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApwZj+yB/jPmqWRvPd/X4N/kcLfmC6t8nZ4ENlQP8d+AiEA2kyQwDxPP5g39YDO/5WQhgXtIllx3TTaYKXsZGJ4dgQ="}], "size": 8178136}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.4.3_1689648414288_0.6103533430662056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-18T02:46:54.645Z", "publish_time": 1689648414645, "_source_registry_name": "default"}, "5.5.0-rc.1": {"name": "echarts", "version": "5.5.0-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.5.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./index.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./*": "./*"}, "gitHead": "4d3bbaefa53dd85ad730dbaa09805d7152019ea4", "readmeFilename": "README.md", "_id": "echarts@5.5.0-rc.1", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-Fnfls+zDAg9HLuWK27JakdjLjpkEZsKTk+eeSA6eOqphBNA9iXCI1CvQJZ5xD8WJY40bqdh4zCaE4AxSDjJslw==", "shasum": "31ee2c4e09da24fe8149bb011c1fbd271a2bf10f", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.5.0-rc.1.tgz", "fileCount": 1274, "unpackedSize": 41606342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHyAslTyP3k6YRKAuvQZ4/cUZuzd7TkVIFEBwKHn9MySAiEAoqLZGvPetEa+X3p9yKIwaEvU/VRaT7GB/eqj0uihFrc="}], "size": 8157838}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.5.0-rc.1_1706691397639_0.30520041574543977"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-31T08:56:37.948Z", "publish_time": 1706691397948, "_source_registry_name": "default"}, "5.5.0-rc.2": {"name": "echarts", "version": "5.5.0-rc.2", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.5.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "readmeFilename": "README.md", "gitHead": "0f4967b1bdef6b35e795b9fb1403e765489649fa", "_id": "echarts@5.5.0-rc.2", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-8NVDNvlzuiE1t/yJZEvQbaPohCKOUfsA7XtBLbk/MhSrecmKnLF9zpNyj6VecjpIur6uadHJY5mgb3mOjS9QlA==", "shasum": "769e01fa4662edd61ed167fe3e24793adee60852", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.5.0-rc.2.tgz", "fileCount": 1277, "unpackedSize": 52846045, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFfuv7NSEjIz7AMDd1vfZi2VduCuslwSzpDotCL0TcJLAiEA6t0jVJinl1Udca2M9/dc2VX1bGXDCa4qIuzG3i5AbjY="}], "size": 10372891}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.5.0-rc.2_1707032738585_0.93128881739915"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-04T07:45:38.874Z", "publish_time": 1707032738874, "_source_registry_name": "default"}, "5.5.0": {"name": "echarts", "version": "5.5.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.5.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "0.0.64", "@definitelytyped/utils": "0.0.64", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "gitHead": "0f4967b1bdef6b35e795b9fb1403e765489649fa", "_id": "echarts@5.5.0", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-rNYnNCzqDAPCr4m/fqyUFv7fD9qIsd50S6GDFgO1DxZhncCsNsG7IfUlAlvZe5oSEQxtsjnHiUuppzccry93Xw==", "shasum": "c13945a7f3acdd67c134d8a9ac67e917830113ac", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.5.0.tgz", "fileCount": 1277, "unpackedSize": 52846040, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpRGC6Q3GzvkYiPamtr8tbPmbaZkh7omSzJlS6M2z0bQIhAOBc2+PEP1WH17lv0+EuAxWbbDMxu5+Lfkp23xjOJtxK"}], "size": 10372872}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.5.0_1708249199114_0.4672141271620063"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-18T09:39:59.444Z", "publish_time": 1708249199444, "_source_registry_name": "default"}, "5.5.1-rc.1": {"name": "echarts", "version": "5.5.1-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.6.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./types/dist/charts": "./types/dist/charts.d.ts", "./types/dist/components": "./types/dist/components.d.ts", "./types/dist/core": "./types/dist/core.d.ts", "./types/dist/echarts": "./types/dist/echarts.d.ts", "./types/dist/features": "./types/dist/features.d.ts", "./types/dist/option": "./types/dist/option.d.ts", "./types/dist/renderers": "./types/dist/renderers.d.ts", "./types/dist/shared": "./types/dist/shared.d.ts", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "readmeFilename": "README.md", "gitHead": "2caf68489c567417b8e357595997d4466c73f6db", "_id": "echarts@5.5.1-rc.1", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-x+IwxSpVOhlSovphKE8j7KiqkPOGxy1fEGNFHKE9rhgVyHu3dpPv2i5yf7dYXrqaSDcq/wA9VRGJLboxWtEPGA==", "shasum": "2fb9ab08b3021a60ff9e3c1d5ec08d7406e68b10", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.5.1-rc.1.tgz", "fileCount": 1239, "unpackedSize": 52808895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICubHT1ryhhidQKHlkHel7F0Vc1siFUl4Ebd5I53MPyBAiAuhCvsG/gzG6nQIINizaebM55Ty4yNfOW0eKMakrsmYw=="}], "size": 10364264}, "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.5.1-rc.1_1718874374108_0.5952420805742433"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-20T09:06:14.622Z", "publish_time": 1718874374622, "_source_registry_name": "default"}, "5.5.1": {"name": "echarts", "version": "5.5.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.6.0"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./types/dist/charts": "./types/dist/charts.d.ts", "./types/dist/components": "./types/dist/components.d.ts", "./types/dist/core": "./types/dist/core.d.ts", "./types/dist/echarts": "./types/dist/echarts.d.ts", "./types/dist/features": "./types/dist/features.d.ts", "./types/dist/option": "./types/dist/option.d.ts", "./types/dist/renderers": "./types/dist/renderers.d.ts", "./types/dist/shared": "./types/dist/shared.d.ts", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "gitHead": "2caf68489c567417b8e357595997d4466c73f6db", "_id": "echarts@5.5.1", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-Fce8upazaAXUVUVsjgV6mBnGuqgO+JNDlcgF79Dksy4+wgGpQB2lmYoO4TSweFg/mZITdpGHomw/cNBJZj1icA==", "shasum": "8dc9c68d0c548934bedcb5f633db07ed1dd2101c", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.5.1.tgz", "fileCount": 1239, "unpackedSize": 52808890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA++rqwetzyO5NpRAxW6guIeRnhH8LLyv8w1kgQ00fXDAiA7OJ1Rfh5CYU+9GYV5R5rC/RQIAlS5TER/YSaludoYjA=="}], "size": 10364258}, "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/echarts_5.5.1_1719473249173_0.7674007403538534"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-27T07:27:29.634Z", "publish_time": 1719473249634, "_source_registry_name": "default"}, "5.6.0-rc.1": {"name": "echarts", "version": "5.6.0-rc.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./types/dist/charts": "./types/dist/charts.d.ts", "./types/dist/components": "./types/dist/components.d.ts", "./types/dist/core": "./types/dist/core.d.ts", "./types/dist/echarts": "./types/dist/echarts.d.ts", "./types/dist/features": "./types/dist/features.d.ts", "./types/dist/option": "./types/dist/option.d.ts", "./types/dist/renderers": "./types/dist/renderers.d.ts", "./types/dist/shared": "./types/dist/shared.d.ts", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "_id": "echarts@5.6.0-rc.1", "readmeFilename": "README.md", "gitHead": "fe42bc1ea3a8d2ef7864cfe303de34f480149d09", "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-xS4zGGYncGu1+8jBq55d1ptcgFClV77Nzk9qW66inK1Tg3DZhTtPtaHd54amqI2Je5NCYxsLWpqifxDVMwrhRA==", "shasum": "ee09a19cc1ddf3c2b81ca346f1403744eebd8a8a", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.6.0-rc.1.tgz", "fileCount": 1247, "unpackedSize": 53164587, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1oWcbjceLUIA4NtTzuXm01wGiKHOSQdqY3h/e7cVLXAIhAPW4MZQkPAF9kiI+XV9LbdcEL8AJhsqRou5YySAr262H"}], "size": 10432432}, "_npmUser": {"name": "plainheart", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/echarts_5.6.0-rc.1_1734773119980_0.8076613148933176"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T09:25:20.337Z", "publish_time": 1734773120337, "_source_registry_name": "default"}, "5.6.0": {"name": "echarts", "version": "5.6.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./types/dist/charts": "./types/dist/charts.d.ts", "./types/dist/components": "./types/dist/components.d.ts", "./types/dist/core": "./types/dist/core.d.ts", "./types/dist/echarts": "./types/dist/echarts.d.ts", "./types/dist/features": "./types/dist/features.d.ts", "./types/dist/option": "./types/dist/option.d.ts", "./types/dist/renderers": "./types/dist/renderers.d.ts", "./types/dist/shared": "./types/dist/shared.d.ts", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "_id": "echarts@5.6.0", "gitHead": "fe42bc1ea3a8d2ef7864cfe303de34f480149d09", "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==", "shasum": "2377874dca9fb50f104051c3553544752da3c9d6", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-5.6.0.tgz", "fileCount": 1247, "unpackedSize": 53164582, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCibrLnyi04fcOM0QVEzikMwoHX6frp/5UrwHiXqcwspwIgP6BSRlOeiQGbqIxp345EXVhkgFTYt0/Rqtr6lsA1Rvo="}], "size": 10432423}, "_npmUser": {"name": "plainheart", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/echarts_5.6.0_1735370501784_0.6848979686949546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-28T07:21:42.839Z", "publish_time": 1735370502839, "_source_registry_name": "default"}, "6.0.0-beta.1": {"name": "echarts", "version": "6.0.0-beta.1", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "6.0.0-rc.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./types/dist/charts": "./types/dist/charts.d.ts", "./types/dist/components": "./types/dist/components.d.ts", "./types/dist/core": "./types/dist/core.d.ts", "./types/dist/echarts": "./types/dist/echarts.d.ts", "./types/dist/features": "./types/dist/features.d.ts", "./types/dist/option": "./types/dist/option.d.ts", "./types/dist/renderers": "./types/dist/renderers.d.ts", "./types/dist/shared": "./types/dist/shared.d.ts", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/matrix": "./lib/component/matrix.js", "./lib/component/thumbnail": "./lib/component/thumbnail.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}, "_id": "echarts@6.0.0-beta.1", "readmeFilename": "README.md", "gitHead": "fdf3315c233b00604cb555f5cc9f90880fb37113", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-hEtCVOohAWr8fCMNXwg0cRZjkWO+LwbhO30cX/fzwb2LF4sHt06YHVWlAQclayhwHlxCyYtMG9FkFnNUAHK72Q==", "shasum": "a0b1f33b0e8b061d70609b28adc8c3572604513d", "tarball": "https://registry.npmmirror.com/echarts/-/echarts-6.0.0-beta.1.tgz", "fileCount": 1315, "unpackedSize": 57423897, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFf/vgM/job5kxYpzPP9OlboTsXTu4QAHjj8bKRiqscnAiEAku2hpbgMv9hLRWk7/LVuX9n4bcgW4ARuR0gw0APBe/w="}], "size": 11407623}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "wfsheep", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "plainheart", "email": "<EMAIL>"}, {"name": "susiwen8", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "apache-echarts", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/echarts_6.0.0-beta.1_1750864302753_0.6499787534652222"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-25T15:11:43.062Z", "publish_time": 1750864303062, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "homepage": "https://echarts.apache.org", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "_source_registry_name": "default"}