{"_attachments": {}, "_id": "xlsx", "_rev": "91368-61f189ec61011c8ed85c6b70", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "dist-tags": {"latest": "0.18.5"}, "license": "Apache-2.0", "maintainers": [{"name": "s5s", "email": "<EMAIL>"}], "name": "xlsx", "readme": "# [SheetJS](https://sheetjs.com)\n\nThe SheetJS Community Edition offers battle-tested open-source solutions for\nextracting useful data from almost any complex spreadsheet and generating new\nspreadsheets that will work with legacy and modern software alike.\n\n[SheetJS Pro](https://sheetjs.com/pro) offers solutions beyond data processing:\nEdit complex templates with ease; let out your inner Picasso with styling; make\ncustom sheets with images/graphs/PivotTables; evaluate formula expressions and\nport calculations to web apps; automate common spreadsheet tasks, and much more!\n\n![License](https://img.shields.io/github/license/SheetJS/sheetjs)\n[![Build Status](https://img.shields.io/github/workflow/status/sheetjs/sheetjs/Tests:%20node.js)](https://github.com/SheetJS/sheetjs/actions)\n[![Snyk Vulnerabilities](https://img.shields.io/snyk/vulnerabilities/github/SheetJS/sheetjs)](https://snyk.io/test/github/SheetJS/sheetjs)\n[![npm Downloads](https://img.shields.io/npm/dm/xlsx.svg)](https://npmjs.org/package/xlsx)\n[![Analytics](https://ga-beacon.appspot.com/***********-1/SheetJS/sheetjs?pixel)](https://github.com/SheetJS/sheetjs)\n\n[**Browser Test and Support Matrix**](https://oss.sheetjs.com/sheetjs/tests/)\n\n[![Build Status](https://saucelabs.com/browser-matrix/sheetjs.svg)](https://saucelabs.com/u/sheetjs)\n\n**Supported File Formats**\n\n![circo graph of format support](formats.png)\n\n![graph legend](legend.png)\n\n## Table of Contents\n\n<details>\n  <summary><b>Expand to show Table of Contents</b></summary>\n\n<!-- toc -->\n\n- [Getting Started](#getting-started)\n  * [Installation](#installation)\n  * [Usage](#usage)\n  * [The Zen of SheetJS](#the-zen-of-sheetjs)\n  * [JS Ecosystem Demos](#js-ecosystem-demos)\n- [Acquiring and Extracting Data](#acquiring-and-extracting-data)\n  * [Parsing Workbooks](#parsing-workbooks)\n  * [Processing JSON and JS Data](#processing-json-and-js-data)\n  * [Processing HTML Tables](#processing-html-tables)\n- [Processing Data](#processing-data)\n  * [Modifying Workbook Structure](#modifying-workbook-structure)\n  * [Modifying Cell Values](#modifying-cell-values)\n  * [Modifying Other Worksheet / Workbook / Cell Properties](#modifying-other-worksheet--workbook--cell-properties)\n- [Packaging and Releasing Data](#packaging-and-releasing-data)\n  * [Writing Workbooks](#writing-workbooks)\n  * [Writing Examples](#writing-examples)\n  * [Streaming Write](#streaming-write)\n  * [Generating JSON and JS Data](#generating-json-and-js-data)\n  * [Generating HTML Tables](#generating-html-tables)\n  * [Generating Single-Worksheet Snapshots](#generating-single-worksheet-snapshots)\n- [Interface](#interface)\n  * [Parsing functions](#parsing-functions)\n  * [Writing functions](#writing-functions)\n  * [Utilities](#utilities)\n- [Common Spreadsheet Format](#common-spreadsheet-format)\n  * [General Structures](#general-structures)\n  * [Cell Object](#cell-object)\n    + [Data Types](#data-types)\n    + [Dates](#dates)\n  * [Sheet Objects](#sheet-objects)\n    + [Worksheet Object](#worksheet-object)\n    + [Chartsheet Object](#chartsheet-object)\n    + [Macrosheet Object](#macrosheet-object)\n    + [Dialogsheet Object](#dialogsheet-object)\n  * [Workbook Object](#workbook-object)\n    + [Workbook File Properties](#workbook-file-properties)\n  * [Workbook-Level Attributes](#workbook-level-attributes)\n    + [Defined Names](#defined-names)\n    + [Workbook Views](#workbook-views)\n    + [Miscellaneous Workbook Properties](#miscellaneous-workbook-properties)\n  * [Document Features](#document-features)\n    + [Formulae](#formulae)\n    + [Row and Column Properties](#row-and-column-properties)\n    + [Number Formats](#number-formats)\n    + [Hyperlinks](#hyperlinks)\n    + [Cell Comments](#cell-comments)\n    + [Sheet Visibility](#sheet-visibility)\n    + [VBA and Macros](#vba-and-macros)\n- [Parsing Options](#parsing-options)\n  * [Input Type](#input-type)\n  * [Guessing File Type](#guessing-file-type)\n- [Writing Options](#writing-options)\n  * [Supported Output Formats](#supported-output-formats)\n  * [Output Type](#output-type)\n- [Utility Functions](#utility-functions)\n  * [Array of Arrays Input](#array-of-arrays-input)\n  * [Array of Objects Input](#array-of-objects-input)\n  * [HTML Table Input](#html-table-input)\n  * [Formulae Output](#formulae-output)\n  * [Delimiter-Separated Output](#delimiter-separated-output)\n    + [UTF-16 Unicode Text](#utf-16-unicode-text)\n  * [HTML Output](#html-output)\n  * [JSON](#json)\n- [File Formats](#file-formats)\n- [Testing](#testing)\n  * [Node](#node)\n  * [Browser](#browser)\n  * [Tested Environments](#tested-environments)\n  * [Test Files](#test-files)\n- [Contributing](#contributing)\n  * [OSX/Linux](#osxlinux)\n  * [Windows](#windows)\n  * [Tests](#tests)\n- [License](#license)\n- [References](#references)\n\n<!-- tocstop -->\n\n</details>\n\n## Getting Started\n\n### Installation\n\n**Standalone Browser Scripts**\n\nThe complete browser standalone build is saved to `dist/xlsx.full.min.js` and\ncan be directly added to a page with a `script` tag:\n\n```html\n<script lang=\"javascript\" src=\"dist/xlsx.full.min.js\"></script>\n```\n\n<details>\n  <summary><b>CDN Availability</b> (click to show)</summary>\n\n|    CDN     | URL                                        |\n|-----------:|:-------------------------------------------|\n|    `unpkg` | <https://unpkg.com/xlsx/>                  |\n| `jsDelivr` | <https://jsdelivr.com/package/npm/xlsx>    |\n|    `CDNjs` | <https://cdnjs.com/libraries/xlsx>         |\n\nFor example, `unpkg` makes the latest version available at:\n\n```html\n<script src=\"https://unpkg.com/xlsx/dist/xlsx.full.min.js\"></script>\n```\n\n</details>\n\n<details>\n  <summary><b>Browser builds</b> (click to show)</summary>\n\nThe complete single-file version is generated at `dist/xlsx.full.min.js`\n\n`dist/xlsx.core.min.js` omits codepage library (no support for XLS encodings)\n\nA slimmer build is generated at `dist/xlsx.mini.min.js`. Compared to full build:\n- codepage library skipped (no support for XLS encodings)\n- no support for XLSB / XLS / Lotus 1-2-3 / SpreadsheetML 2003 / Numbers\n- node stream utils removed\n\n</details>\n\n\nWith [bower](https://bower.io/search/?q=js-xlsx):\n\n```bash\n$ bower install js-xlsx\n```\n\n**ECMAScript Modules**\n\nThe ECMAScript Module build is saved to `xlsx.mjs` and can be directly added to\na page with a `script` tag using `type=module`:\n\n```html\n<script type=\"module\">\nimport { read, writeFileXLSX } from \"./xlsx.mjs\";\n\n/* load the codepage support library for extended support with older formats  */\nimport { set_cptable } from \"./xlsx.mjs\";\nimport * as cptable from './dist/cpexcel.full.mjs';\nset_cptable(cptable);\n</script>\n```\n\nThe [npm package](https://www.npmjs.org/package/xlsx) also exposes the module\nwith the `module` parameter, supported in Angular and other projects:\n\n```ts\nimport { read, writeFileXLSX } from \"xlsx\";\n\n/* load the codepage support library for extended support with older formats  */\nimport { set_cptable } from \"xlsx\";\nimport * as cptable from 'xlsx/dist/cpexcel.full.mjs';\nset_cptable(cptable);\n```\n\n**Deno**\n\n`xlsx.mjs` can be imported in Deno.  It is available from `unpkg`:\n\n```ts\n// @deno-types=\"https://unpkg.com/xlsx/types/index.d.ts\"\nimport * as XLSX from 'https://unpkg.com/xlsx/xlsx.mjs';\n\n/* load the codepage support library for extended support with older formats  */\nimport * as cptable from 'https://unpkg.com/xlsx/dist/cpexcel.full.mjs';\nXLSX.set_cptable(cptable);\n```\n\n**NodeJS**\n\nWith [npm](https://www.npmjs.org/package/xlsx):\n\n```bash\n$ npm install xlsx\n```\n\nBy default, the module supports `require`:\n\n```js\nvar XLSX = require(\"xlsx\");\n```\n\nThe module also ships with `xlsx.mjs` for use with `import`:\n\n```js\nimport * as XLSX from 'xlsx/xlsx.mjs';\n\n/* load 'fs' for readFile and writeFile support */\nimport * as fs from 'fs';\nXLSX.set_fs(fs);\n\n/* load 'stream' for stream support */\nimport { Readable } from 'stream';\nXLSX.stream.set_readable(Readable);\n\n/* load the codepage support library for extended support with older formats  */\nimport * as cpexcel from 'xlsx/dist/cpexcel.full.mjs';\nXLSX.set_cptable(cpexcel);\n```\n\n**Photoshop and InDesign**\n\n`dist/xlsx.extendscript.js` is an ExtendScript build for Photoshop and InDesign\nthat is included in the `npm` package.  It can be directly referenced with a\n`#include` directive:\n\n```extendscript\n#include \"xlsx.extendscript.js\"\n```\n\n\n<details>\n  <summary><b>Internet Explorer and ECMAScript 3 Compatibility</b> (click to show)</summary>\n\nFor broad compatibility with JavaScript engines, the library is written using\nECMAScript 3 language dialect as well as some ES5 features like `Array#forEach`.\nOlder browsers require shims to provide missing functions.\n\nTo use the shim, add the shim before the script tag that loads `xlsx.js`:\n\n```html\n<!-- add the shim first -->\n<script type=\"text/javascript\" src=\"shim.min.js\"></script>\n<!-- after the shim is referenced, add the library -->\n<script type=\"text/javascript\" src=\"xlsx.full.min.js\"></script>\n```\n\nThe script also includes `IE_LoadFile` and `IE_SaveFile` for loading and saving\nfiles in Internet Explorer versions 6-9.  The `xlsx.extendscript.js` script\nbundles the shim in a format suitable for Photoshop and other Adobe products.\n\n</details>\n\n### Usage\n\nMost scenarios involving spreadsheets and data can be broken into 5 parts:\n\n1) **Acquire Data**:  Data may be stored anywhere: local or remote files,\n   databases, HTML TABLE, or even generated programmatically in the web browser.\n\n2) **Extract Data**:  For spreadsheet files, this involves parsing raw bytes to\n   read the cell data. For general JS data, this involves reshaping the data.\n\n3) **Process Data**:  From generating summary statistics to cleaning data\n   records, this step is the heart of the problem.\n\n4) **Package Data**:  This can involve making a new spreadsheet or serializing\n   with `JSON.stringify` or writing XML or simply flattening data for UI tools.\n\n5) **Release Data**:  Spreadsheet files can be uploaded to a server or written\n   locally.  Data can be presented to users in an HTML TABLE or data grid.\n\nA common problem involves generating a valid spreadsheet export from data stored\nin an HTML table.  In this example, an HTML TABLE on the page will be scraped,\na row will be added to the bottom with the date of the report, and a new file\nwill be generated and downloaded locally. `XLSX.writeFile` takes care of\npackaging the data and attempting a local download:\n\n```js\n// Acquire Data (reference to the HTML table)\nvar table_elt = document.getElementById(\"my-table-id\");\n\n// Extract Data (create a workbook object from the table)\nvar workbook = XLSX.utils.table_to_book(table_elt);\n\n// Process Data (add a new row)\nvar ws = workbook.Sheets[\"Sheet1\"];\nXLSX.utils.sheet_add_aoa(ws, [[\"Created \"+new Date().toISOString()]], {origin:-1});\n\n// Package and Release Data (`writeFile` tries to write and save an XLSB file)\nXLSX.writeFile(workbook, \"Report.xlsb\");\n```\n\nThis library tries to simplify steps 2 and 4 with functions to extract useful\ndata from spreadsheet files (`read` / `readFile`) and generate new spreadsheet\nfiles from data (`write` / `writeFile`).  Additional utility functions like\n`table_to_book` work with other common data sources like HTML tables.\n\nThis documentation and various demo projects cover a number of common scenarios\nand approaches for steps 1 and 5.\n\nUtility functions help with step 3.\n\n[\"Acquiring and Extracting Data\"](#acquiring-and-extracting-data) describes\nsolutions for common data import scenarios.\n\n[\"Packaging and Releasing Data\"](#packaging-and-releasing-data) describes\nsolutions for common data export scenarios.\n\n[\"Processing Data\"](#packaging-and-releasing-data) describes solutions for\ncommon workbook processing and manipulation scenarios.\n\n[\"Utility Functions\"](#utility-functions) details utility functions for\ntranslating JSON Arrays and other common JS structures into worksheet objects.\n\n### The Zen of SheetJS\n\n_Data processing should fit in any workflow_\n\nThe library does not impose a separate lifecycle.  It fits nicely in websites\nand apps built using any framework.  The plain JS data objects play nice with\nWeb Workers and future APIs.\n\n_JavaScript is a powerful language for data processing_\n\nThe [\"Common Spreadsheet Format\"](#common-spreadsheet-format) is a simple object\nrepresentation of the core concepts of a workbook.  The various functions in the\nlibrary provide low-level tools for working with the object.\n\nFor friendly JS processing, there are utility functions for converting parts of\na worksheet to/from an Array of Arrays.  The following example combines powerful\nJS Array methods with a network request library to download data, select the\ninformation we want and create a workbook file:\n\n<details>\n  <summary><b>Get Data from a JSON Endpoint and Generate a Workbook</b> (click to show)</summary>\n\nThe goal is to generate a XLSB workbook of US President names and birthdays.\n\n**Acquire Data**\n\n_Raw Data_\n\n<https://theunitedstates.io/congress-legislators/executive.json> has the desired\ndata.  For example, John Adams:\n\n```js\n{\n  \"id\": { /* (data omitted) */ },\n  \"name\": {\n    \"first\": \"John\",          // <-- first name\n    \"last\": \"Adams\"           // <-- last name\n  },\n  \"bio\": {\n    \"birthday\": \"1735-10-19\", // <-- birthday\n    \"gender\": \"M\"\n  },\n  \"terms\": [\n    { \"type\": \"viceprez\", /* (other fields omitted) */ },\n    { \"type\": \"viceprez\", /* (other fields omitted) */ },\n    { \"type\": \"prez\", /* (other fields omitted) */ } // <-- look for \"prez\"\n  ]\n}\n```\n\n_Filtering for Presidents_\n\nThe dataset includes Aaron Burr, a Vice President who was never President!\n\n`Array#filter` creates a new array with the desired rows.  A President served\nat least one term with `type` set to `\"prez\"`.  To test if a particular row has\nat least one `\"prez\"` term, `Array#some` is another native JS function.  The\ncomplete filter would be:\n\n```js\nconst prez = raw_data.filter(row => row.terms.some(term => term.type === \"prez\"));\n```\n\n_Lining up the data_\n\nFor this example, the name will be the first name combined with the last name\n(`row.name.first + \" \" + row.name.last`) and the birthday will be the subfield\n`row.bio.birthday`.  Using `Array#map`, the dataset can be massaged in one call:\n\n```js\nconst rows = prez.map(row => ({\n  name: row.name.first + \" \" + row.name.last,\n  birthday: row.bio.birthday\n}));\n```\n\nThe result is an array of \"simple\" objects with no nesting:\n\n```js\n[\n  { name: \"George Washington\", birthday: \"1732-02-22\" },\n  { name: \"John Adams\", birthday: \"1735-10-19\" },\n  // ... one row per President\n]\n```\n\n**Extract Data**\n\nWith the cleaned dataset, `XLSX.utils.json_to_sheet` generates a worksheet:\n\n```js\nconst worksheet = XLSX.utils.json_to_sheet(rows);\n```\n\n`XLSX.utils.book_new` creates a new workbook and `XLSX.utils.book_append_sheet`\nappends a worksheet to the workbook. The new worksheet will be called \"Dates\":\n\n```js\nconst workbook = XLSX.utils.book_new();\nXLSX.utils.book_append_sheet(workbook, worksheet, \"Dates\");\n```\n\n**Process Data**\n\n_Fixing headers_\n\nBy default, `json_to_sheet` creates a worksheet with a header row. In this case,\nthe headers come from the JS object keys: \"name\" and \"birthday\".\n\nThe headers are in cells A1 and B1.  `XLSX.utils.sheet_add_aoa` can write text\nvalues to the existing worksheet starting at cell A1:\n\n```js\nXLSX.utils.sheet_add_aoa(worksheet, [[\"Name\", \"Birthday\"]], { origin: \"A1\" });\n```\n\n_Fixing Column Widths_\n\nSome of the names are longer than the default column width.  Column widths are\nset by [setting the `\"!cols\"` worksheet property](#row-and-column-properties).\n\nThe following line sets the width of column A to approximately 10 characters:\n\n```js\nworksheet[\"!cols\"] = [ { wch: 10 } ]; // set column A width to 10 characters\n```\n\nOne `Array#reduce` call over `rows` can calculate the maximum width:\n\n```js\nconst max_width = rows.reduce((w, r) => Math.max(w, r.name.length), 10);\nworksheet[\"!cols\"] = [ { wch: max_width } ];\n```\n\nNote: If the starting point was a file or HTML table, `XLSX.utils.sheet_to_json`\nwill generate an array of JS objects.\n\n**Package and Release Data**\n\n`XLSX.writeFile` creates a spreadsheet file and tries to write it to the system.\nIn the browser, it will try to prompt the user to download the file.  In NodeJS,\nit will write to the local directory.\n\n```js\nXLSX.writeFile(workbook, \"Presidents.xlsx\");\n```\n\n**Complete Example**\n\n```js\n// Uncomment the next line for use in NodeJS:\n// const XLSX = require(\"xlsx\"), axios = require(\"axios\");\n\n(async() => {\n  /* fetch JSON data and parse */\n  const url = \"https://theunitedstates.io/congress-legislators/executive.json\";\n  const raw_data = (await axios(url, {responseType: \"json\"})).data;\n\n  /* filter for the Presidents */\n  const prez = raw_data.filter(row => row.terms.some(term => term.type === \"prez\"));\n\n  /* flatten objects */\n  const rows = prez.map(row => ({\n    name: row.name.first + \" \" + row.name.last,\n    birthday: row.bio.birthday\n  }));\n\n  /* generate worksheet and workbook */\n  const worksheet = XLSX.utils.json_to_sheet(rows);\n  const workbook = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(workbook, worksheet, \"Dates\");\n\n  /* fix headers */\n  XLSX.utils.sheet_add_aoa(worksheet, [[\"Name\", \"Birthday\"]], { origin: \"A1\" });\n\n  /* calculate column width */\n  const max_width = rows.reduce((w, r) => Math.max(w, r.name.length), 10);\n  worksheet[\"!cols\"] = [ { wch: max_width } ];\n\n  /* create an XLSX file and try to save to Presidents.xlsx */\n  XLSX.writeFile(workbook, \"Presidents.xlsx\");\n})();\n```\n\nFor use in the web browser, assuming the snippet is saved to `snippet.js`,\nscript tags should be used to include the `axios` and `xlsx` standalone builds:\n\n```html\n<script src=\"https://unpkg.com/xlsx/dist/xlsx.full.min.js\"></script>\n<script src=\"https://unpkg.com/axios/dist/axios.min.js\"></script>\n<script src=\"snippet.js\"></script>\n```\n\n\n</details>\n\n_File formats are implementation details_\n\nThe parser covers a wide gamut of common spreadsheet file formats to ensure that\n\"HTML-saved-as-XLS\" files work as well as actual XLS or XLSX files.\n\nThe writer supports a number of common output formats for broad compatibility\nwith the data ecosystem.\n\nTo the greatest extent possible, data processing code should not have to worry\nabout the specific file formats involved.\n\n\n### JS Ecosystem Demos\n\nThe [`demos` directory](demos/) includes sample projects for:\n\n**Frameworks and APIs**\n- [`angularjs`](demos/angular/)\n- [`angular and ionic`](demos/angular2/)\n- [`knockout`](demos/knockout/)\n- [`meteor`](demos/meteor/)\n- [`react and react-native`](demos/react/)\n- [`vue 2.x and weex`](demos/vue/)\n- [`XMLHttpRequest and fetch`](demos/xhr/)\n- [`nodejs server`](demos/server/)\n- [`databases and key/value stores`](demos/database/)\n- [`typed arrays and math`](demos/array/)\n\n**Bundlers and Tooling**\n- [`browserify`](demos/browserify/)\n- [`fusebox`](demos/fusebox/)\n- [`parcel`](demos/parcel/)\n- [`requirejs`](demos/requirejs/)\n- [`rollup`](demos/rollup/)\n- [`systemjs`](demos/systemjs/)\n- [`typescript`](demos/typescript/)\n- [`webpack 2.x`](demos/webpack/)\n\n**Platforms and Integrations**\n- [`deno`](demos/deno/)\n- [`electron application`](demos/electron/)\n- [`nw.js application`](demos/nwjs/)\n- [`Chrome / Chromium extensions`](demos/chrome/)\n- [`Download a Google Sheet locally`](demos/google-sheet/)\n- [`Adobe ExtendScript`](demos/extendscript/)\n- [`Headless Browsers`](demos/headless/)\n- [`canvas-datagrid`](demos/datagrid/)\n- [`x-spreadsheet`](demos/xspreadsheet/)\n- [`react-data-grid`](demos/react/modify/)\n- [`vue3-table-light`](demos/vue/modify/)\n- [`Swift JSC and other engines`](demos/altjs/)\n- [`\"serverless\" functions`](demos/function/)\n- [`internet explorer`](demos/oldie/)\n\nOther examples are included in the [showcase](demos/showcase/).\n\n<https://sheetjs.com/demos/modify.html> shows a complete example of reading,\nmodifying, and writing files.\n\n<https://github.com/SheetJS/sheetjs/blob/HEAD/bin/xlsx.njs> is the command-line\ntool included with node installations, reading spreadsheet files and exporting\nthe contents in various formats.\n## Acquiring and Extracting Data\n\n### Parsing Workbooks\n\n**API**\n\n_Extract data from spreadsheet bytes_\n\n```js\nvar workbook = XLSX.read(data, opts);\n```\n\nThe `read` method can extract data from spreadsheet bytes stored in a JS string,\n\"binary string\", NodeJS buffer or typed array (`Uint8Array` or `ArrayBuffer`).\n\n\n_Read spreadsheet bytes from a local file and extract data_\n\n```js\nvar workbook = XLSX.readFile(filename, opts);\n```\n\nThe `readFile` method attempts to read a spreadsheet file at the supplied path.\nBrowsers generally do not allow reading files in this way (it is deemed a\nsecurity risk), and attempts to read files in this way will throw an error.\n\nThe second `opts` argument is optional. [\"Parsing Options\"](#parsing-options)\ncovers the supported properties and behaviors.\n\n**Examples**\n\nHere are a few common scenarios (click on each subtitle to see the code):\n\n<details>\n  <summary><b>Local file in a NodeJS server</b> (click to show)</summary>\n\n`readFile` uses `fs.readFileSync` under the hood:\n\n```js\nvar XLSX = require(\"xlsx\");\n\nvar workbook = XLSX.readFile(\"test.xlsx\");\n```\n\nFor Node ESM, the `readFile` helper is not enabled. Instead, `fs.readFileSync`\nshould be used to read the file data as a `Buffer` for use with `XLSX.read`:\n\n```js\nimport { readFileSync } from \"fs\";\nimport { read } from \"xlsx/xlsx.mjs\";\n\nconst buf = readFileSync(\"test.xlsx\");\n/* buf is a Buffer */\nconst workbook = read(buf);\n```\n\n</details>\n\n<details>\n  <summary><b>Local file in a Deno application</b> (click to show)</summary>\n\n`readFile` uses `Deno.readFileSync` under the hood:\n\n```js\n// @deno-types=\"https://deno.land/x/sheetjs/types/index.d.ts\"\nimport * as XLSX from 'https://deno.land/x/sheetjs/xlsx.mjs'\n\nconst workbook = XLSX.readFile(\"test.xlsx\");\n```\n\nApplications reading files must be invoked with the `--allow-read` flag.  The\n[`deno` demo](demos/deno/) has more examples\n\n</details>\n\n<details>\n  <summary><b>User-submitted file in a web page (\"Drag-and-Drop\")</b> (click to show)</summary>\n\nFor modern websites targeting Chrome 76+, `File#arrayBuffer` is recommended:\n\n```js\n// XLSX is a global from the standalone script\n\nasync function handleDropAsync(e) {\n  e.stopPropagation(); e.preventDefault();\n  const f = e.dataTransfer.files[0];\n  /* f is a File */\n  const data = await f.arrayBuffer();\n  /* data is an ArrayBuffer */\n  const workbook = XLSX.read(data);\n\n  /* DO SOMETHING WITH workbook HERE */\n}\ndrop_dom_element.addEventListener(\"drop\", handleDropAsync, false);\n```\n\nFor maximal compatibility, the `FileReader` API should be used:\n\n```js\nfunction handleDrop(e) {\n  e.stopPropagation(); e.preventDefault();\n  var f = e.dataTransfer.files[0];\n  /* f is a File */\n  var reader = new FileReader();\n  reader.onload = function(e) {\n    var data = e.target.result;\n    /* reader.readAsArrayBuffer(file) -> data will be an ArrayBuffer */\n    var workbook = XLSX.read(data);\n\n    /* DO SOMETHING WITH workbook HERE */\n  };\n  reader.readAsArrayBuffer(f);\n}\ndrop_dom_element.addEventListener(\"drop\", handleDrop, false);\n```\n\n<https://oss.sheetjs.com/sheetjs/> demonstrates the FileReader technique.\n\n</details>\n\n<details>\n  <summary><b>User-submitted file with an HTML INPUT element</b> (click to show)</summary>\n\nStarting with an HTML INPUT element with `type=\"file\"`:\n\n```html\n<input type=\"file\" id=\"input_dom_element\">\n```\n\nFor modern websites targeting Chrome 76+, `Blob#arrayBuffer` is recommended:\n\n```js\n// XLSX is a global from the standalone script\n\nasync function handleFileAsync(e) {\n  const file = e.target.files[0];\n  const data = await file.arrayBuffer();\n  /* data is an ArrayBuffer */\n  const workbook = XLSX.read(data);\n\n  /* DO SOMETHING WITH workbook HERE */\n}\ninput_dom_element.addEventListener(\"change\", handleFileAsync, false);\n```\n\nFor broader support (including IE10+), the `FileReader` approach is recommended:\n\n```js\nfunction handleFile(e) {\n  var file = e.target.files[0];\n  var reader = new FileReader();\n  reader.onload = function(e) {\n    var data = e.target.result;\n    /* reader.readAsArrayBuffer(file) -> data will be an ArrayBuffer */\n    var workbook = XLSX.read(e.target.result);\n\n    /* DO SOMETHING WITH workbook HERE */\n  };\n  reader.readAsArrayBuffer(file);\n}\ninput_dom_element.addEventListener(\"change\", handleFile, false);\n```\n\nThe [`oldie` demo](demos/oldie/) shows an IE-compatible fallback scenario.\n\n</details>\n\n<details>\n  <summary><b>Fetching a file in the web browser (\"Ajax\")</b> (click to show)</summary>\n\nFor modern websites targeting Chrome 42+, `fetch` is recommended:\n\n```js\n// XLSX is a global from the standalone script\n\n(async() => {\n  const url = \"http://oss.sheetjs.com/test_files/formula_stress_test.xlsx\";\n  const data = await (await fetch(url)).arrayBuffer();\n  /* data is an ArrayBuffer */\n  const workbook = XLSX.read(data);\n\n  /* DO SOMETHING WITH workbook HERE */\n})();\n```\n\nFor broader support, the `XMLHttpRequest` approach is recommended:\n\n```js\nvar url = \"http://oss.sheetjs.com/test_files/formula_stress_test.xlsx\";\n\n/* set up async GET request */\nvar req = new XMLHttpRequest();\nreq.open(\"GET\", url, true);\nreq.responseType = \"arraybuffer\";\n\nreq.onload = function(e) {\n  var workbook = XLSX.read(req.response);\n\n  /* DO SOMETHING WITH workbook HERE */\n};\n\nreq.send();\n```\n\nThe [`xhr` demo](demos/xhr/) includes a longer discussion and more examples.\n\n<http://oss.sheetjs.com/sheetjs/ajax.html> shows fallback approaches for IE6+.\n\n</details>\n\n<details>\n  <summary><b>Local file in a PhotoShop or InDesign plugin</b> (click to show)</summary>\n\n`readFile` wraps the `File` logic in Photoshop and other ExtendScript targets.\nThe specified path should be an absolute path:\n\n```js\n#include \"xlsx.extendscript.js\"\n\n/* Read test.xlsx from the Documents folder */\nvar workbook = XLSX.readFile(Folder.myDocuments + \"/test.xlsx\");\n```\n\nThe [`extendscript` demo](demos/extendscript/) includes a more complex example.\n\n</details>\n\n<details>\n  <summary><b>Local file in an Electron app</b> (click to show)</summary>\n\n`readFile` can be used in the renderer process:\n\n```js\n/* From the renderer process */\nvar XLSX = require(\"xlsx\");\n\nvar workbook = XLSX.readFile(path);\n```\n\nElectron APIs have changed over time.  The [`electron` demo](demos/electron/)\nshows a complete example and details the required version-specific settings.\n\n</details>\n\n<details>\n  <summary><b>Local file in a mobile app with React Native</b> (click to show)</summary>\n\nThe [`react` demo](demos/react) includes a sample React Native app.\n\nSince React Native does not provide a way to read files from the filesystem, a\nthird-party library must be used.  The following libraries have been tested:\n\n- [`react-native-file-access`](https://npm.im/react-native-file-access)\n\nThe `base64` encoding returns strings compatible with the `base64` type:\n\n```js\nimport XLSX from \"xlsx\";\nimport { FileSystem } from \"react-native-file-access\";\n\nconst b64 = await FileSystem.readFile(path, \"base64\");\n/* b64 is a base64 string */\nconst workbook = XLSX.read(b64, {type: \"base64\"});\n```\n\n- [`react-native-fs`](https://npm.im/react-native-fs)\n\nThe `ascii` encoding returns binary strings compatible with the `binary` type:\n\n```js\nimport XLSX from \"xlsx\";\nimport { readFile } from \"react-native-fs\";\n\nconst bstr = await readFile(path, \"ascii\");\n/* bstr is a binary string */\nconst workbook = XLSX.read(bstr, {type: \"binary\"});\n```\n\n</details>\n\n<details>\n  <summary><b>NodeJS Server File Uploads</b> (click to show)</summary>\n\n`read` can accept a NodeJS buffer.  `readFile` can read files generated by a\nHTTP POST request body parser like [`formidable`](https://npm.im/formidable):\n\n```js\nconst XLSX = require(\"xlsx\");\nconst http = require(\"http\");\nconst formidable = require(\"formidable\");\n\nconst server = http.createServer((req, res) => {\n  const form = new formidable.IncomingForm();\n  form.parse(req, (err, fields, files) => {\n    /* grab the first file */\n    const f = Object.entries(files)[0][1];\n    const path = f.filepath;\n    const workbook = XLSX.readFile(path);\n\n    /* DO SOMETHING WITH workbook HERE */\n  });\n}).listen(process.env.PORT || 7262);\n```\n\nThe [`server` demo](demos/server) has more advanced examples.\n\n</details>\n\n<details>\n  <summary><b>Download files in a NodeJS process</b> (click to show)</summary>\n\nNode 17.5 and 18.0 have native support for fetch:\n\n```js\nconst XLSX = require(\"xlsx\");\n\nconst data = await (await fetch(url)).arrayBuffer();\n/* data is an ArrayBuffer */\nconst workbook = XLSX.read(data);\n```\n\nFor broader compatibility, third-party modules are recommended.\n\n[`request`](https://npm.im/request) requires a `null` encoding to yield Buffers:\n\n```js\nvar XLSX = require(\"xlsx\");\nvar request = require(\"request\");\n\nrequest({url: url, encoding: null}, function(err, resp, body) {\n  var workbook = XLSX.read(body);\n\n  /* DO SOMETHING WITH workbook HERE */\n});\n```\n\n[`axios`](https://npm.im/axios) works the same way in browser and in NodeJS:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst axios = require(\"axios\");\n\n(async() => {\n  const res = await axios.get(url, {responseType: \"arraybuffer\"});\n  /* res.data is a Buffer */\n  const workbook = XLSX.read(res.data);\n\n  /* DO SOMETHING WITH workbook HERE */\n})();\n```\n\n</details>\n\n<details>\n  <summary><b>Download files in an Electron app</b> (click to show)</summary>\n\nThe `net` module in the main process can make HTTP/HTTPS requests to external\nresources.  Responses should be manually concatenated using `Buffer.concat`:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst { net } = require(\"electron\");\n\nconst req = net.request(url);\nreq.on(\"response\", (res) => {\n  const bufs = []; // this array will collect all of the buffers\n  res.on(\"data\", (chunk) => { bufs.push(chunk); });\n  res.on(\"end\", () => {\n    const workbook = XLSX.read(Buffer.concat(bufs));\n\n    /* DO SOMETHING WITH workbook HERE */\n  });\n});\nreq.end();\n```\n\n</details>\n\n<details>\n  <summary><b>Readable Streams in NodeJS</b> (click to show)</summary>\n\nWhen dealing with Readable Streams, the easiest approach is to buffer the stream\nand process the whole thing at the end:\n\n```js\nvar fs = require(\"fs\");\nvar XLSX = require(\"xlsx\");\n\nfunction process_RS(stream, cb) {\n  var buffers = [];\n  stream.on(\"data\", function(data) { buffers.push(data); });\n  stream.on(\"end\", function() {\n    var buffer = Buffer.concat(buffers);\n    var workbook = XLSX.read(buffer, {type:\"buffer\"});\n\n    /* DO SOMETHING WITH workbook IN THE CALLBACK */\n    cb(workbook);\n  });\n}\n```\n\n</details>\n\n<details>\n  <summary><b>ReadableStream in the browser</b> (click to show)</summary>\n\nWhen dealing with `ReadableStream`, the easiest approach is to buffer the stream\nand process the whole thing at the end:\n\n```js\n// XLSX is a global from the standalone script\n\nasync function process_RS(stream) {\n  /* collect data */\n  const buffers = [];\n  const reader = stream.getReader();\n  for(;;) {\n    const res = await reader.read();\n    if(res.value) buffers.push(res.value);\n    if(res.done) break;\n  }\n\n  /* concat */\n  const out = new Uint8Array(buffers.reduce((acc, v) => acc + v.length, 0));\n\n  let off = 0;\n  for(const u8 of arr) {\n    out.set(u8, off);\n    off += u8.length;\n  }\n\n  return out;\n}\n\nconst data = await process_RS(stream);\n/* data is Uint8Array */\nconst workbook = XLSX.read(data);\n```\n\n</details>\n\nMore detailed examples are covered in the [included demos](demos/)\n\n### Processing JSON and JS Data\n\nJSON and JS data tend to represent single worksheets.  This section will use a\nfew utility functions to generate workbooks.\n\n_Create a new Workbook_\n\n```js\nvar workbook = XLSX.utils.book_new();\n```\n\nThe `book_new` utility function creates an empty workbook with no worksheets.\n\nSpreadsheet software generally require at least one worksheet and enforce the\nrequirement in the user interface.  This library enforces the requirement at\nwrite time, throwing errors if an empty workbook is passed to write functions.\n\n\n**API**\n\n_Create a worksheet from an array of arrays of JS values_\n\n```js\nvar worksheet = XLSX.utils.aoa_to_sheet(aoa, opts);\n```\n\nThe `aoa_to_sheet` utility function walks an \"array of arrays\" in row-major\norder, generating a worksheet object.  The following snippet generates a sheet\nwith cell `A1` set to the string `A1`, cell `B1` set to `B1`, etc:\n\n```js\nvar worksheet = XLSX.utils.aoa_to_sheet([\n  [\"A1\", \"B1\", \"C1\"],\n  [\"A2\", \"B2\", \"C2\"],\n  [\"A3\", \"B3\", \"C3\"]\n]);\n```\n\n[\"Array of Arrays Input\"](#array-of-arrays-input) describes the function and the\noptional `opts` argument in more detail.\n\n\n_Create a worksheet from an array of JS objects_\n\n```js\nvar worksheet = XLSX.utils.json_to_sheet(jsa, opts);\n```\n\nThe `json_to_sheet` utility function walks an array of JS objects in order,\ngenerating a worksheet object.  By default, it will generate a header row and\none row per object in the array.  The optional `opts` argument has settings to\ncontrol the column order and header output.\n\n[\"Array of Objects Input\"](#array-of-arrays-input) describes the function and\nthe optional `opts` argument in more detail.\n\n**Examples**\n\n[\"Zen of SheetJS\"](#the-zen-of-sheetjs) contains a detailed example \"Get Data\nfrom a JSON Endpoint and Generate a Workbook\"\n\n\n[`x-spreadsheet`](https://github.com/myliang/x-spreadsheet) is an interactive\ndata grid for previewing and modifying structured data in the web browser.  The\n[`xspreadsheet` demo](/demos/xspreadsheet) includes a sample script with the\n`xtos` function for converting from x-spreadsheet data object to a workbook.\n<https://oss.sheetjs.com/sheetjs/x-spreadsheet> is a live demo.\n\n<details>\n  <summary><b>Records from a database query (SQL or no-SQL)</b> (click to show)</summary>\n\nThe [`database` demo](/demos/database/) includes examples of working with\ndatabases and query results.\n\n</details>\n\n\n<details>\n  <summary><b>Numerical Computations with TensorFlow.js</b> (click to show)</summary>\n\n[`@tensorflow/tfjs`](@tensorflow/tfjs) and other libraries expect data in simple\narrays, well-suited for worksheets where each column is a data vector.  That is\nthe transpose of how most people use spreadsheets, where each row is a vector.\n\nWhen recovering data from `tfjs`, the returned data points are stored in a typed\narray.  An array of arrays can be constructed with loops. `Array#unshift` can\nprepend a title row before the conversion:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst tf = require('@tensorflow/tfjs');\n\n/* suppose xs and ys are vectors (1D tensors) -> tfarr will be a typed array */\nconst tfdata = tf.stack([xs, ys]).transpose();\nconst shape = tfdata.shape;\nconst tfarr = tfdata.dataSync();\n\n/* construct the array of arrays */\nconst aoa = [];\nfor(let j = 0; j < shape[0]; ++j) {\n  aoa[j] = [];\n  for(let i = 0; i < shape[1]; ++i) aoa[j][i] = tfarr[j * shape[1] + i];\n}\n/* add headers to the top */\naoa.unshift([\"x\", \"y\"]);\n\n/* generate worksheet */\nconst worksheet = XLSX.utils.aoa_to_sheet(aoa);\n```\n\nThe [`array` demo](demos/array/) shows a complete example.\n\n</details>\n\n\n### Processing HTML Tables\n\n**API**\n\n_Create a worksheet by scraping an HTML TABLE in the page_\n\n```js\nvar worksheet = XLSX.utils.table_to_sheet(dom_element, opts);\n```\n\nThe `table_to_sheet` utility function takes a DOM TABLE element and iterates\nthrough the rows to generate a worksheet.  The `opts` argument is optional.\n[\"HTML Table Input\"](#html-table-input) describes the function in more detail.\n\n\n\n_Create a workbook by scraping an HTML TABLE in the page_\n\n```js\nvar workbook = XLSX.utils.table_to_book(dom_element, opts);\n```\n\nThe `table_to_book` utility function follows the same logic as `table_to_sheet`.\nAfter generating a worksheet, it creates a blank workbook and appends the\nspreadsheet.\n\nThe options argument supports the same options as `table_to_sheet`, with the\naddition of a `sheet` property to control the worksheet name.  If the property\nis missing or no options are specified, the default name `Sheet1` is used.\n\n**Examples**\n\nHere are a few common scenarios (click on each subtitle to see the code):\n\n<details>\n  <summary><b>HTML TABLE element in a webpage</b> (click to show)</summary>\n\n```html\n<!-- include the standalone script and shim.  this uses the UNPKG CDN -->\n<script src=\"https://unpkg.com/xlsx/dist/shim.min.js\"></script>\n<script src=\"https://unpkg.com/xlsx/dist/xlsx.full.min.js\"></script>\n\n<!-- example table with id attribute -->\n<table id=\"tableau\">\n  <tr><td>Sheet</td><td>JS</td></tr>\n  <tr><td>12345</td><td>67</td></tr>\n</table>\n\n<!-- this block should appear after the table HTML and the standalone script -->\n<script type=\"text/javascript\">\n  var workbook = XLSX.utils.table_to_book(document.getElementById(\"tableau\"));\n\n  /* DO SOMETHING WITH workbook HERE */\n</script>\n```\n\nMultiple tables on a web page can be converted to individual worksheets:\n\n```js\n/* create new workbook */\nvar workbook = XLSX.utils.book_new();\n\n/* convert table \"table1\" to worksheet named \"Sheet1\" */\nvar sheet1 = XLSX.utils.table_to_sheet(document.getElementById(\"table1\"));\nXLSX.utils.book_append_sheet(workbook, sheet1, \"Sheet1\");\n\n/* convert table \"table2\" to worksheet named \"Sheet2\" */\nvar sheet2 = XLSX.utils.table_to_sheet(document.getElementById(\"table2\"));\nXLSX.utils.book_append_sheet(workbook, sheet2, \"Sheet2\");\n\n/* workbook now has 2 worksheets */\n```\n\nAlternatively, the HTML code can be extracted and parsed:\n\n```js\nvar htmlstr = document.getElementById(\"tableau\").outerHTML;\nvar workbook = XLSX.read(htmlstr, {type:\"string\"});\n```\n\n</details>\n\n<details>\n  <summary><b>Chrome/Chromium Extension</b> (click to show)</summary>\n\nThe [`chrome` demo](demos/chrome/) shows a complete example and details the\nrequired permissions and other settings.\n\nIn an extension, it is recommended to generate the workbook in a content script\nand pass the object back to the extension:\n\n```js\n/* in the worker script */\nchrome.runtime.onMessage.addListener(function(msg, sender, cb) {\n  /* pass a message like { sheetjs: true } from the extension to scrape */\n  if(!msg || !msg.sheetjs) return;\n  /* create a new workbook */\n  var workbook = XLSX.utils.book_new();\n  /* loop through each table element */\n  var tables = document.getElementsByTagName(\"table\")\n  for(var i = 0; i < tables.length; ++i) {\n    var worksheet = XLSX.utils.table_to_sheet(tables[i]);\n    XLSX.utils.book_append_sheet(workbook, worksheet, \"Table\" + i);\n  }\n  /* pass back to the extension */\n  return cb(workbook);\n});\n```\n\n</details>\n\n<details>\n  <summary><b>Server-Side HTML Tables with Headless Chrome</b> (click to show)</summary>\n\nThe [`headless` demo](demos/headless/) includes a complete demo to convert HTML\nfiles to XLSB workbooks.  The core idea is to add the script to the page, parse\nthe table in the page context, generate a `base64` workbook and send it back\nfor further processing:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst { readFileSync } = require(\"fs\"), puppeteer = require(\"puppeteer\");\n\nconst url = `https://sheetjs.com/demos/table`;\n\n/* get the standalone build source (node_modules/xlsx/dist/xlsx.full.min.js) */\nconst lib = readFileSync(require.resolve(\"xlsx/dist/xlsx.full.min.js\"), \"utf8\");\n\n(async() => {\n  /* start browser and go to web page */\n  const browser = await puppeteer.launch();\n  const page = await browser.newPage();\n  await page.goto(url, {waitUntil: \"networkidle2\"});\n\n  /* inject library */\n  await page.addScriptTag({content: lib});\n\n  /* this function `s5s` will be called by the script below, receiving the Base64-encoded file */\n  await page.exposeFunction(\"s5s\", async(b64) => {\n    const workbook = XLSX.read(b64, {type: \"base64\" });\n\n    /* DO SOMETHING WITH workbook HERE */\n  });\n\n  /* generate XLSB file in webpage context and send back result */\n  await page.addScriptTag({content: `\n    /* call table_to_book on first table */\n    var workbook = XLSX.utils.table_to_book(document.querySelector(\"TABLE\"));\n\n    /* generate XLSX file */\n    var b64 = XLSX.write(workbook, {type: \"base64\", bookType: \"xlsb\"});\n\n    /* call \"s5s\" hook exposed from the node process */\n    window.s5s(b64);\n  `});\n\n  /* cleanup */\n  await browser.close();\n})();\n```\n\n</details>\n\n<details>\n  <summary><b>Server-Side HTML Tables with Headless WebKit</b> (click to show)</summary>\n\nThe [`headless` demo](demos/headless/) includes a complete demo to convert HTML\nfiles to XLSB workbooks using [PhantomJS](https://phantomjs.org/). The core idea\nis to add the script to the page, parse the table in the page context, generate\na `binary` workbook and send it back for further processing:\n\n```js\nvar XLSX = require('xlsx');\nvar page = require('webpage').create();\n\n/* this code will be run in the page */\nvar code = [ \"function(){\",\n  /* call table_to_book on first table */\n  \"var wb = XLSX.utils.table_to_book(document.body.getElementsByTagName('table')[0]);\",\n\n  /* generate XLSB file and return binary string */\n  \"return XLSX.write(wb, {type: 'binary', bookType: 'xlsb'});\",\n\"}\" ].join(\"\");\n\npage.open('https://sheetjs.com/demos/table', function() {\n  /* Load the browser script from the UNPKG CDN */\n  page.includeJs(\"https://unpkg.com/xlsx/dist/xlsx.full.min.js\", function() {\n    /* The code will return an XLSB file encoded as binary string */\n    var bin = page.evaluateJavaScript(code);\n\n    var workbook = XLSX.read(bin, {type: \"binary\"});\n    /* DO SOMETHING WITH workbook HERE */\n\n    phantom.exit();\n  });\n});\n```\n\n</details>\n\n<details>\n  <summary><b>NodeJS HTML Tables without a browser</b> (click to show)</summary>\n\nNodeJS does not include a DOM implementation and Puppeteer requires a hefty\nChromium build.  [`jsdom`](https://npm.im/jsdom) is a lightweight alternative:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst { readFileSync } = require(\"fs\");\nconst { JSDOM } = require(\"jsdom\");\n\n/* obtain HTML string.  This example reads from test.html */\nconst html_str = fs.readFileSync(\"test.html\", \"utf8\");\n/* get first TABLE element */\nconst doc = new JSDOM(html_str).window.document.querySelector(\"table\");\n/* generate workbook */\nconst workbook = XLSX.utils.table_to_book(doc);\n```\n\n</details>\n\n## Processing Data\n\nThe [\"Common Spreadsheet Format\"](#common-spreadsheet-format) is a simple object\nrepresentation of the core concepts of a workbook.  The utility functions work\nwith the object representation and are intended to handle common use cases.\n\n### Modifying Workbook Structure\n\n**API**\n\n_Append a Worksheet to a Workbook_\n\n```js\nXLSX.utils.book_append_sheet(workbook, worksheet, sheet_name);\n```\n\nThe `book_append_sheet` utility function appends a worksheet to the workbook.\nThe third argument specifies the desired worksheet name. Multiple worksheets can\nbe added to a workbook by calling the function multiple times.  If the worksheet\nname is already used in the workbook, it will throw an error.\n\n_Append a Worksheet to a Workbook and find a unique name_\n\n```js\nvar new_name = XLSX.utils.book_append_sheet(workbook, worksheet, name, true);\n```\n\nIf the fourth argument is `true`, the function will start with the specified\nworksheet name.  If the sheet name exists in the workbook, a new worksheet name\nwill be chosen by finding the name stem and incrementing the counter:\n\n```js\nXLSX.utils.book_append_sheet(workbook, sheetA, \"Sheet2\", true); // Sheet2\nXLSX.utils.book_append_sheet(workbook, sheetB, \"Sheet2\", true); // Sheet3\nXLSX.utils.book_append_sheet(workbook, sheetC, \"Sheet2\", true); // Sheet4\nXLSX.utils.book_append_sheet(workbook, sheetD, \"Sheet2\", true); // Sheet5\n```\n\n_List the Worksheet names in tab order_\n\n```js\nvar wsnames = workbook.SheetNames;\n```\n\nThe `SheetNames` property of the workbook object is a list of the worksheet\nnames in \"tab order\".  API functions will look at this array.\n\n_Replace a Worksheet in place_\n\n```js\nworkbook.Sheets[sheet_name] = new_worksheet;\n```\n\nThe `Sheets` property of the workbook object is an object whose keys are names\nand whose values are worksheet objects.  By reassigning to a property of the\n`Sheets` object, the worksheet object can be changed without disrupting the\nrest of the worksheet structure.\n\n**Examples**\n\n<details>\n  <summary><b>Add a new worksheet to a workbook</b> (click to show)</summary>\n\nThis example uses [`XLSX.utils.aoa_to_sheet`](#array-of-arrays-input).\n\n```js\nvar ws_name = \"SheetJS\";\n\n/* Create worksheet */\nvar ws_data = [\n  [ \"S\", \"h\", \"e\", \"e\", \"t\", \"J\", \"S\" ],\n  [  1 ,  2 ,  3 ,  4 ,  5 ]\n];\nvar ws = XLSX.utils.aoa_to_sheet(ws_data);\n\n/* Add the worksheet to the workbook */\nXLSX.utils.book_append_sheet(wb, ws, ws_name);\n```\n\n</details>\n\n### Modifying Cell Values\n\n**API**\n\n_Modify a single cell value in a worksheet_\n\n```js\nXLSX.utils.sheet_add_aoa(worksheet, [[new_value]], { origin: address });\n```\n\n_Modify multiple cell values in a worksheet_\n\n```js\nXLSX.utils.sheet_add_aoa(worksheet, aoa, opts);\n```\n\nThe `sheet_add_aoa` utility function modifies cell values in a worksheet.  The\nfirst argument is the worksheet object.  The second argument is an array of\narrays of values.  The `origin` key of the third argument controls where cells\nwill be written.  The following snippet sets `B3=1` and `E5=\"abc\"`:\n\n```js\nXLSX.utils.sheet_add_aoa(worksheet, [\n  [1],                             // <-- Write 1 to cell B3\n  ,                                // <-- Do nothing in row 4\n  [/*B5*/, /*C5*/, /*D5*/, \"abc\"]  // <-- Write \"abc\" to cell E5\n], { origin: \"B3\" });\n```\n\n[\"Array of Arrays Input\"](#array-of-arrays-input) describes the function and the\noptional `opts` argument in more detail.\n\n**Examples**\n\n<details>\n  <summary><b>Appending rows to a worksheet</b> (click to show)</summary>\n\nThe special origin value `-1` instructs `sheet_add_aoa` to start in column A of\nthe row after the last row in the range, appending the data:\n\n```js\nXLSX.utils.sheet_add_aoa(worksheet, [\n  [\"first row after data\", 1],\n  [\"second row after data\", 2]\n], { origin: -1 });\n```\n\n</details>\n\n\n### Modifying Other Worksheet / Workbook / Cell Properties\n\nThe [\"Common Spreadsheet Format\"](#common-spreadsheet-format) section describes\nthe object structures in greater detail.\n\n## Packaging and Releasing Data\n\n### Writing Workbooks\n\n**API**\n\n_Generate spreadsheet bytes (file) from data_\n\n```js\nvar data = XLSX.write(workbook, opts);\n```\n\nThe `write` method attempts to package data from the workbook into a file in\nmemory.  By default, XLSX files are generated, but that can be controlled with\nthe `bookType` property of the `opts` argument.  Based on the `type` option,\nthe data can be stored as a \"binary string\", JS string, `Uint8Array` or Buffer.\n\nThe second `opts` argument is required.  [\"Writing Options\"](#writing-options)\ncovers the supported properties and behaviors.\n\n_Generate and attempt to save file_\n\n```js\nXLSX.writeFile(workbook, filename, opts);\n```\n\nThe `writeFile` method packages the data and attempts to save the new file.  The\nexport file format is determined by the extension of `filename` (`SheetJS.xlsx`\nsignals XLSX export, `SheetJS.xlsb` signals XLSB export, etc).\n\nThe `writeFile` method uses platform-specific APIs to initiate the file save. In\nNodeJS, `fs.readFileSync` can create a file.  In the web browser, a download is\nattempted using the HTML5 `download` attribute, with fallbacks for IE.\n\n_Generate and attempt to save an XLSX file_\n\n```js\nXLSX.writeFileXLSX(workbook, filename, opts);\n```\n\nThe `writeFile` method embeds a number of different export functions.  This is\ngreat for developer experience but not amenable to tree shaking using the\ncurrent developer tools.  When only XLSX exports are needed, this method avoids\nreferencing the other export functions.\n\nThe second `opts` argument is optional.  [\"Writing Options\"](#writing-options)\ncovers the supported properties and behaviors.\n\n**Examples**\n\n<details>\n  <summary><b>Local file in a NodeJS server</b> (click to show)</summary>\n\n`writeFile` uses `fs.writeFileSync` in server environments:\n\n```js\nvar XLSX = require(\"xlsx\");\n\n/* output format determined by filename */\nXLSX.writeFile(workbook, \"out.xlsb\");\n```\n\nFor Node ESM, the `writeFile` helper is not enabled. Instead, `fs.writeFileSync`\nshould be used to write the file data to a `Buffer` for use with `XLSX.write`:\n\n```js\nimport { writeFileSync } from \"fs\";\nimport { write } from \"xlsx/xlsx.mjs\";\n\nconst buf = write(workbook, {type: \"buffer\", bookType: \"xlsb\"});\n/* buf is a Buffer */\nconst workbook = writeFileSync(\"out.xlsb\", buf);\n```\n\n</details>\n\n<details>\n  <summary><b>Local file in a Deno application</b> (click to show)</summary>\n\n`writeFile` uses `Deno.writeFileSync` under the hood:\n\n```js\n// @deno-types=\"https://deno.land/x/sheetjs/types/index.d.ts\"\nimport * as XLSX from 'https://deno.land/x/sheetjs/xlsx.mjs'\n\nXLSX.writeFile(workbook, \"test.xlsx\");\n```\n\nApplications writing files must be invoked with the `--allow-write` flag.  The\n[`deno` demo](demos/deno/) has more examples\n\n</details>\n\n<details>\n  <summary><b>Local file in a PhotoShop or InDesign plugin</b> (click to show)</summary>\n\n`writeFile` wraps the `File` logic in Photoshop and other ExtendScript targets.\nThe specified path should be an absolute path:\n\n```js\n#include \"xlsx.extendscript.js\"\n\n/* output format determined by filename */\nXLSX.writeFile(workbook, \"out.xlsx\");\n/* at this point, out.xlsx is a file that you can distribute */\n```\n\nThe [`extendscript` demo](demos/extendscript/) includes a more complex example.\n\n</details>\n\n<details>\n  <summary><b>Download a file in the browser to the user machine</b> (click to show)</summary>\n\n`XLSX.writeFile` wraps a few techniques for triggering a file save:\n\n- `URL` browser API creates an object URL for the file, which the library uses\n  by creating a link and forcing a click. It is supported in modern browsers.\n- `msSaveBlob` is an IE10+ API for triggering a file save.\n- `IE_FileSave` uses VBScript and ActiveX to write a file in IE6+ for Windows\n  XP and Windows 7.  The shim must be included in the containing HTML page.\n\nThere is no standard way to determine if the actual file has been downloaded.\n\n```js\n/* output format determined by filename */\nXLSX.writeFile(workbook, \"out.xlsb\");\n/* at this point, out.xlsb will have been downloaded */\n```\n\n</details>\n\n<details>\n  <summary><b>Download a file in legacy browsers</b> (click to show)</summary>\n\n`XLSX.writeFile` techniques work for most modern browsers as well as older IE.\nFor much older browsers, there are workarounds implemented by wrapper libraries.\n\n[`FileSaver.js`](https://github.com/eligrey/FileSaver.js/) implements `saveAs`.\nNote: `XLSX.writeFile` will automatically call `saveAs` if available.\n\n```js\n/* bookType can be any supported output type */\nvar wopts = { bookType:\"xlsx\", bookSST:false, type:\"array\" };\n\nvar wbout = XLSX.write(workbook,wopts);\n\n/* the saveAs call downloads a file on the local machine */\nsaveAs(new Blob([wbout],{type:\"application/octet-stream\"}), \"test.xlsx\");\n```\n\n[`Downloadify`](https://github.com/dcneiner/downloadify) uses a Flash SWF button\nto generate local files, suitable for environments where ActiveX is unavailable:\n\n```js\nDownloadify.create(id,{\n  /* other options are required! read the downloadify docs for more info */\n  filename: \"test.xlsx\",\n  data: function() { return XLSX.write(wb, {bookType:\"xlsx\", type:\"base64\"}); },\n  append: false,\n  dataType: \"base64\"\n});\n```\n\nThe [`oldie` demo](demos/oldie/) shows an IE-compatible fallback scenario.\n\n</details>\n\n<details>\n  <summary><b>Browser upload file (ajax)</b> (click to show)</summary>\n\nA complete example using XHR is [included in the XHR demo](demos/xhr/), along\nwith examples for fetch and wrapper libraries.  This example assumes the server\ncan handle Base64-encoded files (see the demo for a basic nodejs server):\n\n```js\n/* in this example, send a base64 string to the server */\nvar wopts = { bookType:\"xlsx\", bookSST:false, type:\"base64\" };\n\nvar wbout = XLSX.write(workbook,wopts);\n\nvar req = new XMLHttpRequest();\nreq.open(\"POST\", \"/upload\", true);\nvar formdata = new FormData();\nformdata.append(\"file\", \"test.xlsx\"); // <-- server expects `file` to hold name\nformdata.append(\"data\", wbout); // <-- `data` holds the base64-encoded data\nreq.send(formdata);\n```\n\n</details>\n\n<details>\n  <summary><b>PhantomJS (Headless Webkit) File Generation</b> (click to show)</summary>\n\nThe [`headless` demo](demos/headless/) includes a complete demo to convert HTML\nfiles to XLSB workbooks using [PhantomJS](https://phantomjs.org/). PhantomJS\n`fs.write` supports writing files from the main process but has a different\ninterface from the NodeJS `fs` module:\n\n```js\nvar XLSX = require('xlsx');\nvar fs = require('fs');\n\n/* generate a binary string */\nvar bin = XLSX.write(workbook, { type:\"binary\", bookType: \"xlsx\" });\n/* write to file */\nfs.write(\"test.xlsx\", bin, \"wb\");\n```\n\nNote: The section [\"Processing HTML Tables\"](#processing-html-tables) shows how\nto generate a workbook from HTML tables in a page in \"Headless WebKit\".\n\n</details>\n\n\n\nThe [included demos](demos/) cover mobile apps and other special deployments.\n\n### Writing Examples\n\n- <http://sheetjs.com/demos/table.html> exporting an HTML table\n- <http://sheetjs.com/demos/writexlsx.html> generates a simple file\n\n### Streaming Write\n\nThe streaming write functions are available in the `XLSX.stream` object.  They\ntake the same arguments as the normal write functions but return a NodeJS\nReadable Stream.\n\n- `XLSX.stream.to_csv` is the streaming version of `XLSX.utils.sheet_to_csv`.\n- `XLSX.stream.to_html` is the streaming version of `XLSX.utils.sheet_to_html`.\n- `XLSX.stream.to_json` is the streaming version of `XLSX.utils.sheet_to_json`.\n\n<details>\n  <summary><b>nodejs convert to CSV and write file</b> (click to show)</summary>\n\n```js\nvar output_file_name = \"out.csv\";\nvar stream = XLSX.stream.to_csv(worksheet);\nstream.pipe(fs.createWriteStream(output_file_name));\n```\n\n</details>\n\n<details>\n  <summary><b>nodejs write JSON stream to screen</b> (click to show)</summary>\n\n```js\n/* to_json returns an object-mode stream */\nvar stream = XLSX.stream.to_json(worksheet, {raw:true});\n\n/* the following stream converts JS objects to text via JSON.stringify */\nvar conv = new Transform({writableObjectMode:true});\nconv._transform = function(obj, e, cb){ cb(null, JSON.stringify(obj) + \"\\n\"); };\n\nstream.pipe(conv); conv.pipe(process.stdout);\n```\n\n</details>\n\n<details>\n  <summary><b>Exporting NUMBERS files</b> (click to show)</summary>\n\nThe NUMBERS writer requires a fairly large base.  The supplementary `xlsx.zahl`\nscripts provide support.  `xlsx.zahl.js` is designed for standalone and NodeJS\nuse, while `xlsx.zahl.mjs` is suitable for ESM.\n\n_Browser_\n\n```html\n<meta charset=\"utf8\">\n<script src=\"xlsx.full.min.js\"></script>\n<script src=\"xlsx.zahl.js\"></script>\n<script>\nvar wb = XLSX.utils.book_new(); var ws = XLSX.utils.aoa_to_sheet([\n  [\"SheetJS\", \"<3\",\"விரிதாள்\"],\n  [72,,\"Arbeitsblätter\"],\n  [,62,\"数据\"],\n  [true,false,],\n]); XLSX.utils.book_append_sheet(wb, ws, \"Sheet1\");\nXLSX.writeFile(wb, \"textport.numbers\", {numbers: XLSX_ZAHL, compression: true});\n</script>\n```\n\n_Node_\n\n```js\nvar XLSX = require(\"./xlsx.flow\");\nvar XLSX_ZAHL = require(\"./dist/xlsx.zahl\");\nvar wb = XLSX.utils.book_new(); var ws = XLSX.utils.aoa_to_sheet([\n  [\"SheetJS\", \"<3\",\"விரிதாள்\"],\n  [72,,\"Arbeitsblätter\"],\n  [,62,\"数据\"],\n  [true,false,],\n]); XLSX.utils.book_append_sheet(wb, ws, \"Sheet1\");\nXLSX.writeFile(wb, \"textport.numbers\", {numbers: XLSX_ZAHL, compression: true});\n```\n\n_Deno_\n\n```ts\nimport * as XLSX from './xlsx.mjs';\nimport XLSX_ZAHL from './dist/xlsx.zahl.mjs';\n\nvar wb = XLSX.utils.book_new(); var ws = XLSX.utils.aoa_to_sheet([\n  [\"SheetJS\", \"<3\",\"விரிதாள்\"],\n  [72,,\"Arbeitsblätter\"],\n  [,62,\"数据\"],\n  [true,false,],\n]); XLSX.utils.book_append_sheet(wb, ws, \"Sheet1\");\nXLSX.writeFile(wb, \"textports.numbers\", {numbers: XLSX_ZAHL, compression: true});\n```\n\n</details>\n\n<https://github.com/sheetjs/sheetaki> pipes write streams to nodejs response.\n\n### Generating JSON and JS Data\n\nJSON and JS data tend to represent single worksheets. The utility functions in\nthis section work with single worksheets.\n\nThe [\"Common Spreadsheet Format\"](#common-spreadsheet-format) section describes\nthe object structure in more detail.  `workbook.SheetNames` is an ordered list\nof the worksheet names.  `workbook.Sheets` is an object whose keys are sheet\nnames and whose values are worksheet objects.\n\nThe \"first worksheet\" is stored at `workbook.Sheets[workbook.SheetNames[0]]`.\n\n**API**\n\n_Create an array of JS objects from a worksheet_\n\n```js\nvar jsa = XLSX.utils.sheet_to_json(worksheet, opts);\n```\n\n_Create an array of arrays of JS values from a worksheet_\n\n```js\nvar aoa = XLSX.utils.sheet_to_json(worksheet, {...opts, header: 1});\n```\n\nThe `sheet_to_json` utility function walks a workbook in row-major order,\ngenerating an array of objects.  The second `opts` argument controls a number of\nexport decisions including the type of values (JS values or formatted text). The\n[\"JSON\"](#json) section describes the argument in more detail.\n\nBy default, `sheet_to_json` scans the first row and uses the values as headers.\nWith the `header: 1` option, the function exports an array of arrays of values.\n\n**Examples**\n\n[`x-spreadsheet`](https://github.com/myliang/x-spreadsheet) is an interactive\ndata grid for previewing and modifying structured data in the web browser.  The\n[`xspreadsheet` demo](/demos/xspreadsheet) includes a sample script with the\n`stox` function for converting from a workbook to x-spreadsheet data object.\n<https://oss.sheetjs.com/sheetjs/x-spreadsheet> is a live demo.\n\n<details>\n  <summary><b>Previewing data in a React data grid</b> (click to show)</summary>\n\n[`react-data-grid`](https://npm.im/react-data-grid) is a data grid tailored for\nreact.  It expects two properties: `rows` of data objects and `columns` which\ndescribe the columns.  For the purposes of massaging the data to fit the react\ndata grid API it is easiest to start from an array of arrays.\n\nThis demo starts by fetching a remote file and using `XLSX.read` to extract:\n\n```js\nimport { useEffect, useState } from \"react\";\nimport DataGrid from \"react-data-grid\";\nimport { read, utils } from \"xlsx\";\n\nconst url = \"https://oss.sheetjs.com/test_files/RkNumber.xls\";\n\nexport default function App() {\n  const [columns, setColumns] = useState([]);\n  const [rows, setRows] = useState([]);\n  useEffect(() => {(async () => {\n    const wb = read(await (await fetch(url)).arrayBuffer(), { WTF: 1 });\n\n    /* use sheet_to_json with header: 1 to generate an array of arrays */\n    const data = utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], { header: 1 });\n\n    /* see react-data-grid docs to understand the shape of the expected data */\n    setColumns(data[0].map((r) => ({ key: r, name: r })));\n    setRows(data.slice(1).map((r) => r.reduce((acc, x, i) => {\n      acc[data[0][i]] = x;\n      return acc;\n    }, {})));\n  })(); });\n\n  return <DataGrid columns={columns} rows={rows} />;\n}\n```\n\n</details>\n\n<details>\n  <summary><b>Previewing data in a VueJS data grid</b> (click to show)</summary>\n\n[`vue3-table-lite`](https://github.com/linmasahiro/vue3-table-lite) is a simple\nVueJS 3 data table.  It is featured [in the VueJS demo](/demos/vue/modify/).\n\n</details>\n\n<details>\n  <summary><b>Populating a database (SQL or no-SQL)</b> (click to show)</summary>\n\nThe [`database` demo](/demos/database/) includes examples of working with\ndatabases and query results.\n\n</details>\n\n<details>\n  <summary><b>Numerical Computations with TensorFlow.js</b> (click to show)</summary>\n\n[`@tensorflow/tfjs`](@tensorflow/tfjs) and other libraries expect data in simple\narrays, well-suited for worksheets where each column is a data vector.  That is\nthe transpose of how most people use spreadsheets, where each row is a vector.\n\nA single `Array#map` can pull individual named rows from `sheet_to_json` export:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst tf = require('@tensorflow/tfjs');\n\nconst key = \"age\"; // this is the field we want to pull\nconst ages = XLSX.utils.sheet_to_json(worksheet).map(r => r[key]);\nconst tf_data = tf.tensor1d(ages);\n```\n\nAll fields can be processed at once using a transpose of the 2D tensor generated\nwith the `sheet_to_json` export with `header: 1`. The first row, if it contains\nheader labels, should be removed with a slice:\n\n```js\nconst XLSX = require(\"xlsx\");\nconst tf = require('@tensorflow/tfjs');\n\n/* array of arrays of the data starting on the second row */\nconst aoa = XLSX.utils.sheet_to_json(worksheet, {header: 1}).slice(1);\n/* dataset in the \"correct orientation\" */\nconst tf_dataset = tf.tensor2d(aoa).transpose();\n/* pull out each dataset with a slice */\nconst tf_field0 = tf_dataset.slice([0,0], [1,tensor.shape[1]]).flatten();\nconst tf_field1 = tf_dataset.slice([1,0], [1,tensor.shape[1]]).flatten();\n```\n\nThe [`array` demo](demos/array/) shows a complete example.\n\n</details>\n\n\n### Generating HTML Tables\n\n**API**\n\n_Generate HTML Table from Worksheet_\n\n```js\nvar html = XLSX.utils.sheet_to_html(worksheet);\n```\n\nThe `sheet_to_html` utility function generates HTML code based on the worksheet\ndata.  Each cell in the worksheet is mapped to a `<TD>` element.  Merged cells\nin the worksheet are serialized by setting `colspan` and `rowspan` attributes.\n\n**Examples**\n\nThe `sheet_to_html` utility function generates HTML code that can be added to\nany DOM element by setting the `innerHTML`:\n\n```js\nvar container = document.getElementById(\"tavolo\");\ncontainer.innerHTML = XLSX.utils.sheet_to_html(worksheet);\n```\n\nCombining with `fetch`, constructing a site from a workbook is straightforward:\n\n<details>\n  <summary><b>Vanilla JS + HTML fetch workbook and generate table previews</b> (click to show)</summary>\n\n```html\n<body>\n  <style>TABLE { border-collapse: collapse; } TD { border: 1px solid; }</style>\n  <div id=\"tavolo\"></div>\n  <script src=\"https://unpkg.com/xlsx/dist/xlsx.full.min.js\"></script>\n  <script type=\"text/javascript\">\n(async() => {\n  /* fetch and parse workbook -- see the fetch example for details */\n  const workbook = XLSX.read(await (await fetch(\"sheetjs.xlsx\")).arrayBuffer());\n\n  let output = [];\n  /* loop through the worksheet names in order */\n  workbook.SheetNames.forEach(name => {\n\n    /* generate HTML from the corresponding worksheets */\n    const worksheet = workbook.Sheets[name];\n    const html = XLSX.utils.sheet_to_html(worksheet);\n\n    /* add a header with the title name followed by the table */\n    output.push(`<H3>${name}</H3>${html}`);\n  });\n  /* write to the DOM at the end */\n  tavolo.innerHTML = output.join(\"\\n\");\n})();\n  </script>\n</body>\n```\n\n</details>\n\n<details>\n  <summary><b>React fetch workbook and generate HTML table previews</b> (click to show)</summary>\n\nIt is generally recommended to use a React-friendly workflow, but it is possible\nto generate HTML and use it in React with `dangerouslySetInnerHTML`:\n\n```jsx\nfunction Tabeller(props) {\n  /* the workbook object is the state */\n  const [workbook, setWorkbook] = React.useState(XLSX.utils.book_new());\n\n  /* fetch and update the workbook with an effect */\n  React.useEffect(() => { (async() => {\n    /* fetch and parse workbook -- see the fetch example for details */\n    const wb = XLSX.read(await (await fetch(\"sheetjs.xlsx\")).arrayBuffer());\n    setWorkbook(wb);\n  })(); });\n\n  return workbook.SheetNames.map(name => (<>\n    <h3>name</h3>\n    <div dangerouslySetInnerHTML={{\n      /* this __html mantra is needed to set the inner HTML */\n      __html: XLSX.utils.sheet_to_html(workbook.Sheets[name])\n    }} />\n  </>));\n}\n```\n\nThe [`react` demo](demos/react) includes more React examples.\n\n</details>\n\n<details>\n  <summary><b>VueJS fetch workbook and generate HTML table previews</b> (click to show)</summary>\n\nIt is generally recommended to use a VueJS-friendly workflow, but it is possible\nto generate HTML and use it in VueJS with the `v-html` directive:\n\n```jsx\nimport { read, utils } from 'xlsx';\nimport { reactive } from 'vue';\n\nconst S5SComponent = {\n  mounted() { (async() => {\n    /* fetch and parse workbook -- see the fetch example for details */\n    const workbook = read(await (await fetch(\"sheetjs.xlsx\")).arrayBuffer());\n    /* loop through the worksheet names in order */\n    workbook.SheetNames.forEach(name => {\n      /* generate HTML from the corresponding worksheets */\n      const html = utils.sheet_to_html(workbook.Sheets[name]);\n      /* add to state */\n      this.wb.wb.push({ name, html });\n    });\n  })(); },\n  /* this state mantra is required for array updates to work */\n  setup() { return { wb: reactive({ wb: [] }) }; },\n  template: `\n  <div v-for=\"ws in wb.wb\" :key=\"ws.name\">\n    <h3>{{ ws.name }}</h3>\n    <div v-html=\"ws.html\"></div>\n  </div>`\n};\n```\n\nThe [`vuejs` demo](demos/vue) includes more React examples.\n\n</details>\n\n### Generating Single-Worksheet Snapshots\n\nThe `sheet_to_*` functions accept a worksheet object.\n\n**API**\n\n_Generate a CSV from a single worksheet_\n\n```js\nvar csv = XLSX.utils.sheet_to_csv(worksheet, opts);\n```\n\nThis snapshot is designed to replicate the \"CSV UTF8 (`.csv`)\" output type.\n[\"Delimiter-Separated Output\"](#delimiter-separated-output) describes the\nfunction and the optional `opts` argument in more detail.\n\n_Generate \"Text\" from a single worksheet_\n\n```js\nvar txt = XLSX.utils.sheet_to_txt(worksheet, opts);\n```\n\nThis snapshot is designed to replicate the \"UTF16 Text (`.txt`)\" output type.\n[\"Delimiter-Separated Output\"](#delimiter-separated-output) describes the\nfunction and the optional `opts` argument in more detail.\n\n_Generate a list of formulae from a single worksheet_\n\n```js\nvar fmla = XLSX.utils.sheet_to_formulae(worksheet);\n```\n\nThis snapshot generates an array of entries representing the embedded formulae.\nArray formulae are rendered in the form ", "time": {"created": "2022-01-26T17:50:36.202Z", "modified": "2023-07-27T15:50:56.227Z", "0.18.0": "2022-02-01T06:12:03.935Z", "0.17.4": "2021-11-14T05:16:28.581Z", "0.17.3": "2021-10-13T07:24:13.011Z", "0.17.2": "2021-09-16T02:12:59.258Z", "0.17.1": "2021-08-18T18:38:00.480Z", "0.17.0": "2021-05-13T19:13:41.651Z", "0.16.9": "2020-11-20T07:22:22.859Z", "0.16.8": "2020-10-06T22:16:55.612Z", "0.16.7": "2020-09-11T09:33:04.351Z", "0.16.6": "2020-08-12T21:59:08.541Z", "0.16.5": "2020-07-31T03:43:38.836Z", "0.16.4": "2020-07-16T23:46:50.336Z", "0.16.3": "2020-06-29T09:19:14.062Z", "0.16.2": "2020-06-05T06:31:11.398Z", "0.16.1": "2020-05-17T05:53:40.350Z", "0.16.0": "2020-04-30T16:10:32.962Z", "0.15.6": "2020-03-15T08:00:38.984Z", "0.15.5": "2020-01-28T01:42:40.203Z", "0.15.4": "2019-12-23T03:23:08.517Z", "0.15.3": "2019-11-27T10:14:20.408Z", "0.15.2": "2019-11-15T02:03:21.113Z", "0.15.1": "2019-08-14T21:34:05.645Z", "0.15.0": "2019-08-04T21:39:05.755Z", "0.14.5": "2019-08-03T23:54:33.610Z", "0.14.4": "2019-07-21T04:14:52.966Z", "0.14.3": "2019-04-30T07:41:51.819Z", "0.14.2": "2019-04-01T15:03:36.053Z", "0.14.1": "2018-11-13T21:07:24.443Z", "0.14.0": "2018-09-06T08:31:59.801Z", "0.13.5": "2018-08-26T00:33:30.976Z", "0.13.4": "2018-08-15T20:09:31.324Z", "0.13.3": "2018-07-25T07:16:35.976Z", "0.13.2": "2018-07-09T04:29:31.663Z", "0.13.1": "2018-06-22T23:45:40.502Z", "0.13.0": "2018-06-01T17:15:58.269Z", "0.12.13": "2018-05-20T04:10:35.820Z", "0.12.12": "2018-05-05T07:09:11.898Z", "0.12.11": "2018-04-27T21:36:48.460Z", "0.12.10": "2018-04-20T02:56:49.129Z", "0.12.9": "2018-04-13T05:31:18.575Z", "0.12.8": "2018-04-06T07:07:23.434Z", "0.12.7": "2018-03-29T05:08:54.166Z", "0.12.6": "2018-03-19T22:41:29.951Z", "0.12.5": "2018-03-13T03:28:08.022Z", "0.12.4": "2018-03-06T03:01:37.527Z", "0.12.3": "2018-02-28T10:51:47.219Z", "0.12.2": "2018-02-21T07:43:29.438Z", "0.12.1": "2018-02-14T20:49:42.474Z", "0.12.0": "2018-02-08T19:43:24.317Z", "0.11.19": "2018-02-03T21:20:06.109Z", "0.11.18": "2018-01-23T09:52:06.203Z", "0.11.17": "2018-01-09T08:14:57.348Z", "0.11.16": "2017-12-30T08:45:12.746Z", "0.11.15": "2017-12-25T03:39:01.966Z", "0.11.14": "2017-12-15T08:25:26.950Z", "0.11.13": "2017-12-09T07:33:46.874Z", "0.11.12": "2017-12-04T05:44:21.276Z", "0.11.11": "2017-12-01T06:29:01.817Z", "0.11.10": "2017-11-20T02:36:26.251Z", "0.11.9": "2017-11-15T19:30:02.412Z", "0.11.8": "2017-11-05T19:55:35.703Z", "0.11.7": "2017-10-27T20:52:17.390Z", "0.11.6": "2017-10-17T01:00:48.585Z", "0.11.5": "2017-09-30T07:21:25.543Z", "0.11.4": "2017-09-22T23:01:44.489Z", "0.11.3": "2017-08-19T23:57:43.647Z", "0.11.2": "2017-08-11T00:45:50.715Z", "0.11.1": "2017-08-05T07:42:40.669Z", "0.11.0": "2017-08-01T08:29:17.532Z", "0.10.9": "2017-07-29T00:22:47.864Z", "0.10.8": "2017-07-10T23:08:36.938Z", "0.10.7": "2017-07-05T23:00:58.586Z", "0.10.6": "2017-06-24T07:30:33.119Z", "0.10.5": "2017-06-10T02:37:34.236Z", "0.10.3": "2017-05-17T18:20:04.462Z", "0.10.1": "2017-05-11T19:02:04.575Z", "0.10.0": "2017-05-09T18:31:39.904Z", "0.9.13": "2017-04-30T16:55:11.663Z", "0.9.12": "2017-04-21T22:15:17.846Z", "0.9.11": "2017-04-16T08:04:18.083Z", "0.9.10": "2017-04-09T04:22:06.871Z", "0.9.9": "2017-04-03T06:24:14.427Z", "0.9.8": "2017-03-31T22:02:17.266Z", "0.9.6": "2017-03-25T22:45:36.532Z", "0.9.4": "2017-03-20T22:09:16.482Z", "0.9.3": "2017-03-16T04:46:20.515Z", "0.9.2": "2017-03-13T07:29:16.407Z", "0.9.0": "2017-03-10T01:44:32.002Z", "0.8.8": "2017-03-09T06:42:24.296Z", "0.8.0": "2015-04-03T01:28:54.505Z", "0.7.12": "2014-10-26T06:02:01.865Z", "0.7.0": "2014-05-16T01:10:05.082Z", "0.6.1": "2014-04-23T01:44:27.364Z", "0.6.0": "2014-04-15T09:13:17.745Z", "0.5.17": "2014-04-04T23:05:52.937Z", "0.5.8": "2014-02-17T08:53:08.538Z", "0.5.7": "2014-02-15T05:14:38.249Z", "0.5.6": "2014-02-14T06:28:17.761Z", "0.5.0": "2014-02-04T00:08:12.335Z", "0.4.3": "2014-01-31T11:56:38.048Z", "0.3.10": "2014-01-25T22:29:44.680Z", "0.3.3": "2013-12-06T19:57:25.989Z", "0.17.5": "2022-01-10T19:05:18.238Z", "0.18.1": "2022-02-14T01:51:42.133Z", "0.18.2": "2022-02-15T07:39:54.319Z", "0.18.3": "2022-03-03T09:09:46.088Z", "0.18.4": "2022-03-16T04:05:47.113Z", "0.18.5": "2022-03-24T14:23:09.623Z"}, "versions": {"0.18.0": {"name": "xlsx", "version": "0.18.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "cfb": "^1.1.4", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5f0f2c91c55e4e8fea8c3733846e90f56dff21ff", "_id": "xlsx@0.18.0", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-zQluErfRAr7ga2me77sIlDoljSrPCXnrNaiKo2+YFLtGkd0aW0Z9zfARVgNn9nytYBhsEjf6A+H5TogTeddscg==", "shasum": "b16e5d9bc0ef9a83609ccc9a73bc72ae2174a120", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.0.tgz", "fileCount": 29, "unpackedSize": 10878749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+M8zCRA9TVsSAnZWagAAWwgP/2NMYPefb2ex18Szklzc\naJjs0LXZUq9yOmfgxXHxbQtGAGZjGYZinN0T33cSH/LwZ08pGqZyfKmMaQWx\nWu9y+0X3Shn+AsQ3mSE5Kpd+BCjNaROyx+hcrCvp3EuaHWrn+WO9RZodHMov\n0lPdXE3IHsNVfQv7047YztHOMnHq5alnAs2/oYmDeQvDtCLnbF/NthMWQe1M\ngNI2rwjwqG54RAybmWY5cgKswVejPsXx/kvO/f8FXe8QgYV+WuxYbGGcKiGM\ntuoNDeSMQGRAgoqFsRvbSNHz1ubv5o9btQSMQuoKS/4GRih5LAn5Mpi7AaVG\nCvm45Fa7Vpzp8kEanFTU7M4qU7xgmDj+WqBM6BOc50dmBr2MneJEVSg396W8\n2YmbZIznCUjByXiDu6srv/YKncHVKNmHYWGSPLK9Jcgd6s2aNUwMOJmAA5JV\nIMPKSG3zvoBPbYwyiPF5lOPCKLAVudx4fExQ+OM8xfrMkz7ppogRjjqzfkUF\nv166ujHaAvQOWkGxyXqnIYoabjhCtXc2nudpnjoxRvFekQ5NZ0zWpiNNBAGA\nZZu8iI0XxMB0zBuPL86cIVolaRqQstPG8hVdS8RgeVAwWUxZEdToDFkVy/Pz\npnQqSQa0Nt8Dr7rZ92lgsKV1EsyPsz9/PM9sYjZKl9SCVXEu1ObxkHpMlv65\nXozQ\r\n=mSp7\r\n-----END PGP SIGNATURE-----\r\n", "size": 3340125}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.18.0_1643695923677_0.6780304124047067"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-01T06:12:11.146Z"}, "0.17.4": {"name": "xlsx", "version": "0.17.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.15.0", "crc-32": "~1.2.0", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "fcf9182fa628b8c5f6fbf1997a7ad6fcbb695fd1", "_id": "xlsx@0.17.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-9aKt8g9ZLP0CUdBX8L5xnoMDFwSiLI997eQnDThCaqQMYB9AEBIRzblSSNN/ICMGLYIHUO3VKaItcedZJ3ijIg==", "shasum": "dc3e3a0954c835f4d0fdd643645db6f4ac3f28f2", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.4.tgz", "fileCount": 30, "unpackedSize": 11171092, "size": 3590472, "noattachment": false}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.17.4_1636866988336_0.5358435244717645"}, "_hasShrinkwrap": false, "publish_time": 1636866988581, "_cnpm_publish_time": 1636866988581, "_cnpmcore_publish_time": "2021-12-16T10:04:40.086Z"}, "0.17.3": {"name": "xlsx", "version": "0.17.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.15.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "alex": "^9.1.0", "blanket": "~1.2.3", "dtslint": "^0.1.2", "eslint": "^7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "874ea42987c069d795b82a994617d0256bc78f61", "_id": "xlsx@0.17.3", "_nodeVersion": "16.11.1", "_npmVersion": "8.0.0", "dist": {"shasum": "1c2dd36ff1cecb0ebdf79ba4f268e945d0070849", "size": 3549450, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.3.tgz", "integrity": "sha512-dGZKfyPSXfnoITruwisuDVZkvnxhjgqzWJXBJm2Khmh01wcw8//baRUvhroVRhW2SLbnlpGcCZZbeZO1qJgMIw=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.17.3_1634109852751_0.07806649870763982"}, "_hasShrinkwrap": false, "publish_time": 1634109853011, "_cnpm_publish_time": 1634109853011, "_cnpmcore_publish_time": "2021-12-16T10:04:46.802Z"}, "0.17.2": {"name": "xlsx", "version": "0.17.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.15.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "fflate": "^0.3.8", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.4.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "alex": "^9.1.0", "blanket": "~1.2.3", "dtslint": "^0.1.2", "eslint": "^7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "6670c8448ec320313bbdd5cc14ba49ad1992dfd9", "_id": "xlsx@0.17.2", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"shasum": "032bcca02ca01b5ebf7da93dcb035891f2c092af", "size": 3202400, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.2.tgz", "integrity": "sha512-RIhN6/oc/ZqYZqY4jz4AX92yNfULhtNrcZP1lknIcsyR+Ra8Zu/9F1lAZWncYbDex95iYQX/XNNNzNFXZjlNOQ=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.17.2_1631758379005_0.7177362154773006"}, "_hasShrinkwrap": false, "publish_time": 1631758379258, "_cnpm_publish_time": 1631758379258, "_cnpmcore_publish_time": "2021-12-16T10:04:51.154Z"}, "0.17.1": {"name": "xlsx", "version": "0.17.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.15.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "fflate": "^0.3.8", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "alex": "^9.1.0", "blanket": "~1.2.3", "dtslint": "^0.1.2", "eslint": "^7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7468f3b6359fb9c232680c3a5e63af4bddb98e12", "_id": "xlsx@0.17.1", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "2557400d594998301831ef60b08ddde80f2842ee", "size": 2812655, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.1.tgz", "integrity": "sha512-SrvK+kMEjiVIKYyJSjSIJwzm2cZn8nQWVh708g7O+pTsmgjoa+uYNLEUn7jmwQdMI/ffCHcY5yEvwBXssBwpRA=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.17.1_1629311880258_0.9340101323733223"}, "_hasShrinkwrap": false, "publish_time": 1629311880480, "_cnpm_publish_time": 1629311880480, "_cnpmcore_publish_time": "2021-12-16T10:04:56.073Z"}, "0.17.0": {"name": "xlsx", "version": "0.17.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "fflate": "^0.3.8", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "alex": "^9.1.0", "blanket": "~1.2.3", "dtslint": "^0.1.2", "eslint": "^7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "3542d62fffc155dd505a23230ba182c4402a0e2c", "_id": "xlsx@0.17.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "028176a0140967dcee1817d221678461e47481c8", "size": 2803438, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.0.tgz", "integrity": "sha512-bZ36FSACiAyjoldey1+7it50PMlDp1pcAJrZKcVZHzKd8BC/z6TQ/QAN8onuqcepifqSznR6uKnjPhaGt6ig9A=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.17.0_1620933221415_0.6944439731287029"}, "_hasShrinkwrap": false, "publish_time": 1620933221651, "_cnpm_publish_time": 1620933221651, "_cnpmcore_publish_time": "2021-12-16T10:05:01.993Z"}, "0.16.9": {"name": "xlsx", "version": "0.16.9", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "fflate": "^0.3.8", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "20212e1cc222b047d7414d21428c611508c33a67", "_id": "xlsx@0.16.9", "_nodeVersion": "15.2.1", "_npmVersion": "7.0.8", "dist": {"shasum": "dacd5bb46bda6dd3743940c9c3dc1e2171826256", "size": 2782503, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.9.tgz", "integrity": "sha512-gxi1I3EasYvgCX1vN9pGyq920Ron4NO8PNfhuoA3Hpq6Y8f0ECXiy4OLrK4QZBnj1jx3QD+8Fq5YZ/3mPZ5iXw=="}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.9_1605856942598_0.7382299376612493"}, "_hasShrinkwrap": false, "publish_time": 1605856942859, "_cnpm_publish_time": 1605856942859, "_cnpmcore_publish_time": "2021-12-16T10:05:07.008Z"}, "0.16.8": {"name": "xlsx", "version": "0.16.8", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9f1ba60c8dedd4bfb53c9faf40d9b94b80a5886d", "_id": "xlsx@0.16.8", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.8", "dist": {"shasum": "5546de9b0ba15169b36770d4e43b24790d3ff1b8", "size": 2781092, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.8.tgz", "integrity": "sha512-qWub4YCn0xLEGHI7WWhk6IJ73MDu7sPSJQImxN6/LiI8wsHi0hUhICEDbyqBT+jgFgORZxrii0HvhNSwBNAPoQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.8_1602022615280_0.5554466096275135"}, "_hasShrinkwrap": false, "publish_time": 1602022615612, "_cnpm_publish_time": 1602022615612, "_cnpmcore_publish_time": "2021-12-16T10:05:12.559Z"}, "0.16.7": {"name": "xlsx", "version": "0.16.7", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "82b7ada6c7819cdf53f55bf956468c456574d6cd", "_id": "xlsx@0.16.7", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.8", "dist": {"shasum": "62fd6590addac7c4419daaaa2b0c5388015d5f69", "size": 2780781, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.7.tgz", "integrity": "sha512-Xc4NRjci2Grbh9NDk/XoaWycJurxEug1wwn0aJCmB0NvIMyQuHYq2muWLWGidYNZPf94aUbqm6K8Fbjd7gKTZg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.7_1599816784136_0.27709977097362937"}, "_hasShrinkwrap": false, "publish_time": 1599816784351, "_cnpm_publish_time": 1599816784351, "_cnpmcore_publish_time": "2021-12-16T10:05:18.083Z"}, "0.16.6": {"name": "xlsx", "version": "0.16.6", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "2e32611dbb0d759fdda587ac483a5a7814dc03fb", "_id": "xlsx@0.16.6", "_nodeVersion": "14.6.0", "_npmVersion": "6.14.6", "dist": {"shasum": "ec759df166cbfe5fdf01d08636a6796db26af9a3", "size": 2779495, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.6.tgz", "integrity": "sha512-iAPNt15C2umCx7Q6h9owZvNHRYVOImLp7Mc00uA4UhJvzjfM1OV3V7TgBXIHuawGF1G6GLXZr/r75e5jsiBefg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.6_1597269548343_0.0812455065487947"}, "_hasShrinkwrap": false, "publish_time": 1597269548541, "_cnpm_publish_time": 1597269548541, "_cnpmcore_publish_time": "2021-12-16T10:05:23.224Z"}, "0.16.5": {"name": "xlsx", "version": "0.16.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "227f970ca1231bc9e4191e4737b24414fff4c985", "_id": "xlsx@0.16.5", "_nodeVersion": "14.6.0", "_npmVersion": "6.14.6", "dist": {"shasum": "6c320d19d8dd633b369296f9856ea7a51fbd44f8", "size": 2778862, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.5.tgz", "integrity": "sha512-t2OQ7hTs/2Vtv0Xx9UVoWmAgUlYYRc3Ic79PlLofS7og8Hn0TvBbHEUvU/KpgS0txzT/J25avSf0ANecNrMyZA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.5_1596167018629_0.4476414261774948"}, "_hasShrinkwrap": false, "publish_time": 1596167018836, "_cnpm_publish_time": 1596167018836, "_cnpmcore_publish_time": "2021-12-16T10:05:27.693Z"}, "0.16.4": {"name": "xlsx", "version": "0.16.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "6b1f5a013766121af79eb718d23ca3b2413508a8", "_id": "xlsx@0.16.4", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "6cc8913fb12846a7c76e090650d8bc4c4d3f02d1", "size": 2773865, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.4.tgz", "integrity": "sha512-l1xqTdXRK3DCxkxHGj3OxZM1ertzeqjWodi0jevBNSivoyYMPEJAHhVW7BAfM3gFXK35dCM0CacGUXbATdFvqQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.4_1594943210170_0.19459279709388277"}, "_hasShrinkwrap": false, "publish_time": 1594943210336, "_cnpm_publish_time": 1594943210336, "_cnpmcore_publish_time": "2021-12-16T10:05:32.058Z"}, "0.16.3": {"name": "xlsx", "version": "0.16.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "word": "~0.3.0", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "768e1bdcf8d1820bdb69b62252c4b53ab22b10f8", "_id": "xlsx@0.16.3", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"shasum": "7a91a75eb939db4961122da6f949b8a8f0c8af1a", "size": 2772034, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.3.tgz", "integrity": "sha512-LInZ1OK6vpe+Em8XDZ5gDH3WixARwxI7UWc+3chLeafI6gUwECEgL43k4Tjbs1uRfkxpM7wQFy5DLE0hFBRqRw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.3_1593422353870_0.5542132193048368"}, "_hasShrinkwrap": false, "publish_time": 1593422354062, "_cnpm_publish_time": 1593422354062, "_cnpmcore_publish_time": "2021-12-16T10:05:35.496Z"}, "0.16.2": {"name": "xlsx", "version": "0.16.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.3", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5c1296a46d584a47059e6fbc758201f32ef55ed7", "_id": "xlsx@0.16.2", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "84043c36b76ddd776b3b49973530303b21894266", "size": 2733958, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.2.tgz", "integrity": "sha512-XTqOy7YpCUtGbvCYaCh1t1RsZ/y8cSCbZCOYtqqZ4/EmHkyv+/ghxmCvvR8yc4Tn5fhny+3j7voKwJaRlffNKA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.2_1591338670846_0.58599148245098"}, "_hasShrinkwrap": false, "publish_time": 1591338671398, "_cnpm_publish_time": 1591338671398, "_cnpmcore_publish_time": "2021-12-16T10:05:39.086Z"}, "0.16.1": {"name": "xlsx", "version": "0.16.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.3", "wmf": "~1.0.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "baea1798cf825a2cc8b1365cb7818605a8d4f464", "_id": "xlsx@0.16.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "fb1a59d2c4a540a08556492ec786f98f973f1260", "size": 2734374, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.1.tgz", "integrity": "sha512-0feXqUm6W6/h+2bGSX2nkjVHCJ7cjq6pjcFixMTLlmNYrvm+nHg1BUIqyu+3Hlax7K5EbmUWqVxS3X0kuZQGvg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.1_1589694820077_0.8045200302868827"}, "_hasShrinkwrap": false, "publish_time": 1589694820350, "_cnpm_publish_time": 1589694820350, "_cnpmcore_publish_time": "2021-12-16T10:05:43.198Z"}, "0.16.0": {"name": "xlsx", "version": "0.16.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "wmf": "~1.0.1", "ssf": "~0.10.3"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "257d4e6db2444ce1a0be814c1c352423f4aba7b5", "_id": "xlsx@0.16.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"shasum": "2a86ba433ca198f7e87343da86a831d48d713943", "size": 2731877, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.16.0.tgz", "integrity": "sha512-W/LQZjh6o7WDGmCIUXp2FUPSej2XRdaiTgZN31Oh68JlL7jpm796p3eI5zOpphYNT12qkgnXYaCysAsoiyZvOQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.16.0_1588263032659_0.2161102997019302"}, "_hasShrinkwrap": false, "publish_time": 1588263032962, "_cnpm_publish_time": 1588263032962, "_cnpmcore_publish_time": "2021-12-16T10:05:47.746Z"}, "0.15.6": {"name": "xlsx", "version": "0.15.6", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "wmf": "~1.0.1", "ssf": "~0.10.3"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "0a57229d3b59255e3af472d3c0f1bdf948a36efc", "_id": "xlsx@0.15.6", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "461f841d6d9ea1a8375e2cd246bf23aece08a1d5", "size": 2724430, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.6.tgz", "integrity": "sha512-7vD9eutyLs65iDjNFimVN+gk/oDkfkCgpQUjdE82QgzJCrBHC4bGPH7fzKVyy0UPp3gyFVQTQEFJaWaAvZCShQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.6_1584259238764_0.42203538621821224"}, "_hasShrinkwrap": false, "publish_time": 1584259238984, "_cnpm_publish_time": 1584259238984, "_cnpmcore_publish_time": "2021-12-16T10:05:52.484Z"}, "0.15.5": {"name": "xlsx", "version": "0.15.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "a81bb78f18960053a03bb3350f31ea54d28245be", "_id": "xlsx@0.15.5", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "37d5bb8076de7337e5bb0769b3ad55d18d68eaec", "size": 2719657, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.5.tgz", "integrity": "sha512-iWyTqe6UGTkp3XQOeeKPEBcZvmBfzIo3hDIVDfhGIEoTGVIq2JWEk6tIx0F+oKUje3pfZUx4V1W+P6892AB8kQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.5_1580175759995_0.5194518221820967"}, "_hasShrinkwrap": false, "publish_time": 1580175760203, "_cnpm_publish_time": 1580175760203, "_cnpmcore_publish_time": "2021-12-16T10:05:57.674Z"}, "0.15.4": {"name": "xlsx", "version": "0.15.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "6551dd0e051acac5031ffb728a16932bbf34c80a", "_id": "xlsx@0.15.4", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "42ef1807829c4f9e98e8fce2641d53df5b5bde3e", "size": 2716944, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.4.tgz", "integrity": "sha512-5NKT5f4uRlQO6R9dEzWH5rxXNeT5iDB3I/80EFsAaYejxgP7a09l2KVtPap0pZRUrt1F8MVy8CRsEOJ5tbjheg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.4_1577071387973_0.16611815512219819"}, "_hasShrinkwrap": false, "publish_time": 1577071388517, "_cnpm_publish_time": 1577071388517, "_cnpmcore_publish_time": "2021-12-16T10:06:02.383Z"}, "0.15.3": {"name": "xlsx", "version": "0.15.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7ef3f3e531c63fd54939587d52980695c4ec8481", "_id": "xlsx@0.15.3", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "d8b4daabde7529b53aad88273f0776c5e9253692", "size": 2716367, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.3.tgz", "integrity": "sha512-ho5yLr2YLmUZXH/pTPxJ9XImnPQtha3CZeqyhSZ7gmNl8ERRt6JbH2gnYmC2nLc+zegQ2GjfD/K003+G3k1qKQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.3_1574849660160_0.13156556910733297"}, "_hasShrinkwrap": false, "publish_time": 1574849660408, "_cnpm_publish_time": 1574849660408, "_cnpmcore_publish_time": "2021-12-16T10:06:05.600Z"}, "0.15.2": {"name": "xlsx", "version": "0.15.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "unpkg": "dist/xlsx.min.js", "jsdelivr": "dist/xlsx.min.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7e932aec0080256b15a824a71e65a6755a9d386c", "_id": "xlsx@0.15.2", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "1938e712e62813035b0ec9803a12ffaa400ce34f", "size": 2715850, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.2.tgz", "integrity": "sha512-0YcFaYgoqSHh9JcWeQU5X/kR6hyXrs+boc6BomPnJAUVYGgo+Jna9blIwMztjQYfJ6WHs5tKtg5GBBGjkLFVhA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.2_1573783400846_0.20549180451920046"}, "_hasShrinkwrap": false, "publish_time": 1573783401113, "_cnpm_publish_time": 1573783401113, "_cnpmcore_publish_time": "2021-12-16T10:06:10.290Z"}, "0.15.1": {"name": "xlsx", "version": "0.15.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "36fcb00045ad6837383debab4b31caaed4e3b5f7", "_id": "xlsx@0.15.1", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"shasum": "a767c4d412cbd0c95b30dbcd48bed7de428eb107", "size": 2695534, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.1.tgz", "integrity": "sha512-z+o4+QPMc32EPboLCzJAz94o0Zyy+8jrmWTsVpfzwknFln9qDO6/HN1KrGGVC4//sGA7dh4R3HA4fhbGIKCDOA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.1_1565818445425_0.45578470097106916"}, "_hasShrinkwrap": false, "publish_time": 1565818445645, "_cnpm_publish_time": 1565818445645, "_cnpmcore_publish_time": "2021-12-16T10:06:14.325Z"}, "0.15.0": {"name": "xlsx", "version": "0.15.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.3", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "4aeb0a786a6177cd5176dc08c7827a2cae4da3f6", "_id": "xlsx@0.15.0", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"shasum": "7e5633bbeb3995373b8b9960deb6fbda912ab602", "size": 2694882, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.15.0.tgz", "integrity": "sha512-H2m6K+YdjQWhpquA0eU3Ft0Nahu9CDRKRIvHqFILNeweEvC7M5KR167pYZ46ohuaZT7gcnWW4rz8nstjzrJ3+w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.15.0_1564954745480_0.12033979834997854"}, "_hasShrinkwrap": false, "publish_time": 1564954745755, "_cnpm_publish_time": 1564954745755, "_cnpmcore_publish_time": "2021-12-16T10:06:17.436Z"}, "0.14.5": {"name": "xlsx", "version": "0.14.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.2", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "442c4342df6627a592100612c7152dbe02d03a42", "_id": "xlsx@0.14.5", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"shasum": "3637e914d791bdca7382816e173f7d725ed0e0d2", "size": 2473004, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.14.5.tgz", "integrity": "sha512-s/5f4/mjeWREmIWZ+HtDfh/rnz51ar+dZ4LWKZU3u9VBx2zLdSIWTdXgoa52/pnZ9Oe/Vu1W1qzcKzLVe+lq4w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.14.5_1564876473362_0.6403474612024922"}, "_hasShrinkwrap": false, "publish_time": 1564876473610, "_cnpm_publish_time": 1564876473610, "_cnpmcore_publish_time": "2021-12-16T10:06:21.274Z"}, "0.14.4": {"name": "xlsx", "version": "0.14.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.2", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9aabf361422d3af0ae461d14708d8ccc74977016", "_id": "xlsx@0.14.4", "_shasum": "7c2ccfce81d56282df55d0cea76722ef83986c02", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.16.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "7c2ccfce81d56282df55d0cea76722ef83986c02", "size": 2473730, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.14.4.tgz", "integrity": "sha512-AYVNzZKmU9pxDf4d6dSOy52EivzRnJ5lo0Jyxqk7Pz5G+8dMCUsO1PdFlGj6C6MQZ8xBRPinmIpTHYzI2DyfXg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.14.4_1563682492724_0.15195608251425385"}, "_hasShrinkwrap": false, "publish_time": 1563682492966, "_cnpm_publish_time": 1563682492966, "_cnpmcore_publish_time": "2021-12-16T10:06:24.344Z"}, "0.14.3": {"name": "xlsx", "version": "0.14.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.0", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9a6d8a1d3d80c78dad5201fb389316f935279cdc", "_id": "xlsx@0.14.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "09b7534d95ba8f9aca2c462d0f6603e080202224", "size": 2470759, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.14.3.tgz", "integrity": "sha512-wgt9zGKeFp4WIQdPx+j2sNNbDYRbJ+M+uuFcS16pf2yLo/aKcG3RaD4xmS/LHT5rznc6V27NRwIXNWmxLj6eZw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.14.3_1556610111460_0.4783724983345101"}, "_hasShrinkwrap": false, "publish_time": 1556610111819, "_cnpm_publish_time": 1556610111819, "_cnpmcore_publish_time": "2021-12-16T10:06:28.096Z"}, "0.14.2": {"name": "xlsx", "version": "0.14.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.0", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "0c36667c0c1c50cabec680387d06df4245ec6876", "_id": "xlsx@0.14.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "7f3994a77515490482471243fe9f790d4868c872", "size": 2470530, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.14.2.tgz", "integrity": "sha512-6+4TkmU34s1p/qsl8omWSEOa7pOtWlw4SuRJH/FGRk3iF/gcvSWGgCI1L28NaSDx2tI82aeq2SPY+xeFQJD27A=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.14.2_1554131015265_0.6736757060071967"}, "_hasShrinkwrap": false, "publish_time": 1554131016053, "_cnpm_publish_time": 1554131016053, "_cnpmcore_publish_time": "2021-12-16T10:06:31.667Z"}, "0.14.1": {"name": "xlsx", "version": "0.14.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.0", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "1eb1ec985a640b71c5b5bbe006e240f45cf239ab", "_id": "xlsx@0.14.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "ecedf536bd1e94055486ff484f5888dcaa89bd3d", "size": 2468622, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.14.1.tgz", "integrity": "sha512-7hjB5YuyJo1fuuzXQjwuxD8LSUzE4Rxu5ToC3fB5JSunZxGjLcgKg69bEFG9GYoxeVDx5GL0k1dUodlvaQNRQw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.14.1_1542143244238_0.3020178050904536"}, "_hasShrinkwrap": false, "publish_time": 1542143244443, "_cnpm_publish_time": 1542143244443, "_cnpmcore_publish_time": "2021-12-16T10:06:34.393Z"}, "0.14.0": {"name": "xlsx", "version": "0.14.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.0", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "078e5b7ebac1d8138b118292668a4dddaf0a9455", "_id": "xlsx@0.14.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "4da79b08ad9e32106de66613f7a0d3ce559b1fad", "size": 2466073, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.14.0.tgz", "integrity": "sha512-1MDQ7XYRj6JqbXmgCN+ivL6Nr77NzyynM2Inekr5xfmVdsr628FK1nvy6d3T7Y40fbzCj4EMaica4i6YT5UfBA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.14.0_1536222719313_0.6284640599198088"}, "_hasShrinkwrap": false, "publish_time": 1536222719801, "_cnpm_publish_time": 1536222719801, "_cnpmcore_publish_time": "2021-12-16T10:06:38.243Z"}, "0.13.5": {"name": "xlsx", "version": "0.13.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.8", "codepage": "~1.14.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "eca6b457be1193815174b2a716366ac2d8402d4b", "_id": "xlsx@0.13.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "65826ba1944c6f4d6c023b7eb6c89e6b6e0d71b7", "size": 2417942, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.13.5.tgz", "integrity": "sha512-AQo8Anyuv8ZxegAH2EUJ9ZauLf3lIDPfmV7OpJi79LNW6jO4gsviJyQCjNCJY7Deu1SLCrr7LY6rM9N91ixaDQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.13.5_1535243610732_0.201496687629364"}, "_hasShrinkwrap": false, "publish_time": 1535243610976, "_cnpm_publish_time": 1535243610976, "_cnpmcore_publish_time": "2021-12-16T10:06:41.321Z"}, "0.13.4": {"name": "xlsx", "version": "0.13.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.8", "codepage": "~1.14.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d3d5bfc9883a544122e83bc645162110ca6524da", "_id": "xlsx@0.13.4", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "e7b482f72fd7c05373d0cba98d07dcc06a4a19ba", "size": 2418110, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.13.4.tgz", "integrity": "sha512-pdPp2EtZHHqwHGT8CdezinmKzHNrjpJgqGkCY+t5J2+2Yxon3vXTtKpqcyurmlXa4u5ouPGpXuMHMy/bMR3wcA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.13.4_1534363771056_0.8458637915875411"}, "_hasShrinkwrap": false, "publish_time": 1534363771324, "_cnpm_publish_time": 1534363771324, "_cnpmcore_publish_time": "2021-12-16T10:06:44.386Z"}, "0.13.3": {"name": "xlsx", "version": "0.13.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.8", "codepage": "~1.14.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "10439f787958942fd27c7483ad83cc045e6aad50", "_id": "xlsx@0.13.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "99da3641119b7e4683e302b39b93c4a84cd244be", "size": 2416909, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.13.3.tgz", "integrity": "sha512-Uk9tc66kNJFJ6CycdRgRJDvFct0GrZMLVF/QCOZiLVBOw3ic1Ud2pUHUe039huM/a+uApNtSXlceJtj0KrcDcg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.13.3_1532502995772_0.775245267318166"}, "_hasShrinkwrap": false, "publish_time": 1532502995976, "_cnpm_publish_time": 1532502995976, "_cnpmcore_publish_time": "2021-12-16T10:06:47.183Z"}, "0.13.2": {"name": "xlsx", "version": "0.13.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.8", "codepage": "~1.14.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "a9b8588e6c57fd4615bdae082143e9478ed86c80", "_id": "xlsx@0.13.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "89173c11ae9d72fcd9ec48836b6cf1e877de2423", "size": 2416693, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.13.2.tgz", "integrity": "sha512-COExny0yVgLZq7ObWQkEXGzsGGtzHGT3j07nhH+jdh1mfOg5WvEqw19r350j9nOSN4Vsy2co89WlDRk5eZ9CZg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.13.2_1531110571512_0.9709647752223693"}, "_hasShrinkwrap": false, "publish_time": 1531110571663, "_cnpm_publish_time": 1531110571663, "_cnpmcore_publish_time": "2021-12-16T10:06:50.766Z"}, "0.13.1": {"name": "xlsx", "version": "0.13.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.7", "codepage": "~1.13.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9866dfc010338394e4cfcd33a9fbc15dae017ee5", "_id": "xlsx@0.13.1", "_shasum": "65cfa245e1af858ece5ba22baa11d19a46e9b400", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "65cfa245e1af858ece5ba22baa11d19a46e9b400", "size": 2408968, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.13.1.tgz", "integrity": "sha512-p8evQU2wNB/KRLhSdnmAr8YCPo9sB+AYfYd0eANBFJA/wC56gEFT5j/tZAJ8k+MSgZEfv1Gat/gCKXfJeLSkJw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.13.1_1529711140232_0.037148182435912114"}, "_hasShrinkwrap": false, "publish_time": 1529711140502, "_cnpm_publish_time": 1529711140502, "_cnpmcore_publish_time": "2021-12-16T10:06:54.869Z"}, "0.13.0": {"name": "xlsx", "version": "0.13.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.7", "codepage": "~1.13.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "64798fd1f9cbdf1483a70c6e232422b71ffdbea4", "_id": "xlsx@0.13.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "2b933690abf137e8ad9253764c73018b100cde43", "size": 2409352, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.13.0.tgz", "integrity": "sha512-TD2NM86NaxshpggGaN5RwU+lyVbg70a/P67L9P7xScvZPVO/MNPwdqOY5hMWAPZUnWZ9NZwFGi1yn35nLCedPw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.13.0_1527873358037_0.4193615383727365"}, "_hasShrinkwrap": false, "publish_time": 1527873358269, "_cnpm_publish_time": 1527873358269, "_cnpmcore_publish_time": "2021-12-16T10:06:58.034Z"}, "0.12.13": {"name": "xlsx", "version": "0.12.13", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.7", "codepage": "~1.13.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "233102d172a8a9b75535e1989d2e7727d05ea591", "_id": "xlsx@0.12.13", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "f6bec7cf8d916cf625685116170cc3b224228658", "size": 2405033, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.13.tgz", "integrity": "sha512-9/2H4PLphmG8sDvI3mfWb6JIFqbvK8IRGhgS55Pw5F+fmKPuzdv4OW9RFjrH5PiTKeqB/883Z8o+jW3JrDahmw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.13_1526789435635_0.9155339235181286"}, "_hasShrinkwrap": false, "publish_time": 1526789435820, "_cnpm_publish_time": 1526789435820, "_cnpmcore_publish_time": "2021-12-16T10:07:00.864Z"}, "0.12.12": {"name": "xlsx", "version": "0.12.12", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.7", "codepage": "~1.13.0", "commander": "~2.15.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "c0b48958813950d9653ea2784abd440a0ad93420", "_id": "xlsx@0.12.12", "_shasum": "2d17459f2cb1e4f1b564c170c565a18baa9239dc", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "2d17459f2cb1e4f1b564c170c565a18baa9239dc", "size": 2400837, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.12.tgz", "integrity": "sha512-/G7MNpoa7+TMKJOJaBhaEZ3I2hP+wE2tIR0+CG8hh11Kux9iM45whwLMRt60W29nLk+YF3r6FsXAndGBNw9Jew=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.12_1525504151073_0.9537645014288203"}, "_hasShrinkwrap": false, "publish_time": 1525504151898, "_cnpm_publish_time": 1525504151898, "_cnpmcore_publish_time": "2021-12-16T10:07:05.265Z"}, "0.12.11": {"name": "xlsx", "version": "0.12.11", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.6", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "eb5fc87be4c78491c2cad0c298a11637f133fb07", "_id": "xlsx@0.12.11", "_shasum": "54806d78a7bb02998aba3d59c49995a6f0be001e", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "54806d78a7bb02998aba3d59c49995a6f0be001e", "size": 2383016, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.11.tgz", "integrity": "sha512-yVL1rmHwSy6BuVyjdd7VsPa//u0EFkw1VHMPGTgrMYj4qc/xs4COUWVjc1t8bpHP6OW5KumI643zdUiaL/j/mA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.11_1524865008339_0.5387713128719342"}, "_hasShrinkwrap": false, "publish_time": 1524865008460, "_cnpm_publish_time": 1524865008460, "_cnpmcore_publish_time": "2021-12-16T10:07:08.915Z"}, "0.12.10": {"name": "xlsx", "version": "0.12.10", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.6", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f032f34c6e236001a091a0850d457214bdc23a74", "_id": "xlsx@0.12.10", "_shasum": "c2e71be364d90ece2e08f0bae91563483c4897f8", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "9.11.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "c2e71be364d90ece2e08f0bae91563483c4897f8", "size": 2380356, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.10.tgz", "integrity": "sha512-uCPtNo7v62fVBl3bSOA+0ztnFAQb2872EArVtWAEROYttPO39bBQpU21LDPnTRfxmZxOLI79ivm6hy6ysiP7Gg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.10_1524193008845_0.7777625795989838"}, "_hasShrinkwrap": false, "publish_time": 1524193009129, "_cnpm_publish_time": 1524193009129, "_cnpmcore_publish_time": "2021-12-16T10:07:12.643Z"}, "0.12.9": {"name": "xlsx", "version": "0.12.9", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.6", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "04d5e49e951d2417d77b2a32b07cea51ed6bb04f", "_id": "xlsx@0.12.9", "_shasum": "839bad5107531f63d1a7229d97121e5ef00abe38", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "839bad5107531f63d1a7229d97121e5ef00abe38", "size": 2380366, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.9.tgz", "integrity": "sha512-SDg/AVy9UfMFzRf9k+V5L0myQneLdUBm5xBfxIddwsfj0f3C2UI6tGLeH3+9bzFWWgvtSFNa7JlJiUyyGwL7Gw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.9_1523597478270_0.6386217326371979"}, "_hasShrinkwrap": false, "publish_time": 1523597478575, "_cnpm_publish_time": 1523597478575, "_cnpmcore_publish_time": "2021-12-16T10:07:15.536Z"}, "0.12.8": {"name": "xlsx", "version": "0.12.8", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.5", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "93f7749bec413aaeda683cc09569dcb38a09899f", "_id": "xlsx@0.12.8", "_shasum": "02d16a712900be9a75bb5d723e0ca885647a76df", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "02d16a712900be9a75bb5d723e0ca885647a76df", "size": 2379205, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.8.tgz", "integrity": "sha512-op3Uhbx5Y7EZBxlw7b3OO4MbvB4JH4Dnkg6IjUIIrOnrTIg3AVJV1fdLbvDGLPeRrzwzBdwsbJs5vEWE/ny6zA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.8_1522998443209_0.8879955211920036"}, "_hasShrinkwrap": false, "publish_time": 1522998443434, "_cnpm_publish_time": 1522998443434, "_cnpmcore_publish_time": "2021-12-16T10:07:18.502Z"}, "0.12.7": {"name": "xlsx", "version": "0.12.7", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.5", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "08bb7e6e60117eb993befbd8501fa07b5a75508f", "_id": "xlsx@0.12.7", "_shasum": "4b767b7d71d6b5f56928e9d213231805dfaf864e", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.11.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "4b767b7d71d6b5f56928e9d213231805dfaf864e", "size": 2375961, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.7.tgz", "integrity": "sha512-o6i8qiTlrCUceccSECWTszqOs4+EivwaMgHqCfFAab+EGrt8Hs2y/4fwd/Jjm6hX0dAZSk2R8HNaqjy2llcuDw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.7_1522300134034_0.9860051411929931"}, "_hasShrinkwrap": false, "publish_time": 1522300134166, "_cnpm_publish_time": 1522300134166, "_cnpmcore_publish_time": "2021-12-16T10:07:22.051Z"}, "0.12.6": {"name": "xlsx", "version": "0.12.6", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.5", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "dc2128cacada6f929b52294d1ac3770d9a22b78b", "_id": "xlsx@0.12.6", "_npmVersion": "5.6.0", "_nodeVersion": "9.8.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "be8483e6caac0417dc82df0991281c5554cf6b43", "size": 2378195, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.6.tgz", "integrity": "sha512-rgxqgIcpqy1cfvjg6teLCyyWivVDu49iozm0PP25i0hyIz4oVpHr/ji1ncHQn5LpxmvSSC+bf1TVT0y4tGcKhg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.6_1521499289790_0.2846155330298854"}, "publish_time": 1521499289951, "_hasShrinkwrap": false, "_cnpm_publish_time": 1521499289951, "_cnpmcore_publish_time": "2021-12-16T10:07:25.137Z"}, "0.12.5": {"name": "xlsx", "version": "0.12.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.5", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.12.5", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "8830e75596fe222c08c3dd68a4080cebd9a678b3", "size": 2377835, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.5.tgz", "integrity": "sha512-QcUlV6I8QoZWpNlvDMh3DTmT/W5HcDfADX299m/momTfMSEPwveV6Et9T3GG1CzmHbTBrX7R9664CmwSLjegAQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.5_1520911687866_0.31939591653834265"}, "_hasShrinkwrap": false, "publish_time": 1520911688022, "_cnpm_publish_time": 1520911688022, "_cnpmcore_publish_time": "2021-12-16T10:07:28.387Z"}, "0.12.4": {"name": "xlsx", "version": "0.12.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.5", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7149728c7ce83185f0d2bb0e60dba3b66bd84d1d", "_id": "xlsx@0.12.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "047ba115c0500b42dc1ea984cd09879c4141a1a0", "size": 2377429, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.4.tgz", "integrity": "sha512-51EcZb0CxpZZxs4BBTmhoo6Eg8pv4YVf9FTnEAu4O8OxR+i82Jk38346EREaD7T6bXW4sQTBkiH0+tHl7YhP2A=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.4_1520305297234_0.7237527351102546"}, "_hasShrinkwrap": false, "publish_time": 1520305297527, "_cnpm_publish_time": 1520305297527, "_cnpmcore_publish_time": "2021-12-16T10:07:30.928Z"}, "0.12.3": {"name": "xlsx", "version": "0.12.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.4", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5dd16ae6405e84156789e9288d3aa4e40a6158b8", "_id": "xlsx@0.12.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.6.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "964758093e7e17879f15fd53b8fb5e0c5e6ac1bb", "size": 2376786, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.3.tgz", "integrity": "sha512-VT2dpmgkzNkaUtalJHphp5a84+4U7bf6MHCTPG+87CS73aosd0gOvKsWLmspvoTXWeK7pJjuvaO82IJFhc7SIw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.3_1519815107034_0.27561787600189036"}, "_hasShrinkwrap": false, "publish_time": 1519815107219, "_cnpm_publish_time": 1519815107219, "_cnpmcore_publish_time": "2021-12-16T10:07:33.647Z"}, "0.12.2": {"name": "xlsx", "version": "0.12.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.4", "codepage": "~1.12.1", "commander": "~2.14.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.12.2", "_shasum": "04e3814499ec74fe3b1439b733adab84e6a0ecc9", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "04e3814499ec74fe3b1439b733adab84e6a0ecc9", "size": 2372593, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.2.tgz", "integrity": "sha512-trCZ21EU0q+oY7M7icnnNwmTKABuCZy8ggaf1wOUsQOCLi0PbriDr7joWvZ8r39pC37q50X7I1Ys9PdpdQ0hdw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.2_1519199008895_0.041053937415173714"}, "_hasShrinkwrap": false, "publish_time": 1519199009438, "_cnpm_publish_time": 1519199009438, "_cnpmcore_publish_time": "2021-12-16T10:07:36.832Z"}, "0.12.1": {"name": "xlsx", "version": "0.12.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.3", "codepage": "~1.12.0", "commander": "~2.13.0", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "19620da30be2a7d7b9801938a0b9b1fd3c4c4b00", "_id": "xlsx@0.12.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "24d68ea9a179030e793838bf46ca4761c044d364", "size": 2373447, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.1.tgz", "integrity": "sha512-biJl4dTypLcbYKxMJNiTYeInrcspz48DhfW1m8wuwlVZ15Mqz/lCE+f+OEynrtllaijJeJgkqyAYY9o9ep4MUw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.1_1518641380853_0.6424320756445727"}, "_hasShrinkwrap": false, "publish_time": 1518641382474, "_cnpm_publish_time": 1518641382474, "_cnpmcore_publish_time": "2021-12-16T10:07:39.443Z"}, "0.12.0": {"name": "xlsx", "version": "0.12.0", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.2", "codepage": "~1.12.0", "commander": "~2.13.0", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f002afae4bea332739e1b19d755792909fc1cfc6", "_id": "xlsx@0.12.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "c6a99b4100a4bf0ec427e14e5293862f1e8add4f", "size": 2361733, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.12.0.tgz", "integrity": "sha512-tnmq2bfpEmisIXuKBM58Ic2zVvCq7IMuLEo0oHxp5lNyP5373OoDLoe8kLxAKvpP+QC6mDbsoCQP6zb0NvhhtA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.12.0_1518119002579_0.17892110103026537"}, "_hasShrinkwrap": false, "publish_time": 1518119004317, "_cnpm_publish_time": 1518119004317, "_cnpmcore_publish_time": "2021-12-16T10:07:44.018Z"}, "0.11.19": {"name": "xlsx", "version": "0.11.19", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.2", "codepage": "~1.12.0", "commander": "~2.13.0", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "75845a0ca722b634dd39993958ccf813a9649405", "_id": "xlsx@0.11.19", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "2f019d9df756f6345aac5bc1af2442cf22a025e3", "size": 2110530, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.19.tgz", "integrity": "sha512-UTfD64o5Ka/E6QHL12fzcq5wnt9MCtuwgoUdYSTDxjjDkhNmZwSfPlJH/+Yh8vE6nU/0ax3MXNrc9AP4haAmIg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.19.tgz_1517692803677_0.7727568962145597"}, "directories": {}, "publish_time": 1517692806109, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517692806109, "_cnpmcore_publish_time": "2021-12-16T10:07:47.009Z"}, "0.11.18": {"name": "xlsx", "version": "0.11.18", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "~1.0.2", "codepage": "~1.12.0", "commander": "~2.13.0", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "c2ec7555fb6e2bc6588fadbadfb668b98ba0245e", "_id": "xlsx@0.11.18", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "36320107ccee1bc96b702c1aac2a004edb22c3d8", "size": 2104834, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.18.tgz", "integrity": "sha512-PE59VITaVU4W81k6bintXaaHS06uuyZYviX2pTOOf4XOs6efmwjDKVO8+cNKIfYOgW/p50O/fEATVg9UrIsPhQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.18.tgz_1516701123702_0.8740331495646387"}, "directories": {}, "publish_time": 1516701126203, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516701126203, "_cnpmcore_publish_time": "2021-12-16T10:07:49.361Z"}, "0.11.17": {"name": "xlsx", "version": "0.11.17", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "1d749777188b8bd51a526f16c05612f4d6aae608", "_id": "xlsx@0.11.17", "_shasum": "507461fb9783ad0afe1cf751aeb59956e93232da", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "507461fb9783ad0afe1cf751aeb59956e93232da", "size": 2107649, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.17.tgz", "integrity": "sha512-cqMP5nITj9Yr6fOBe7iMdDxB6OQ8cArSaQyFPNxNmXkCFAlHz3plkj8Z35AyHJqe2aCmL3rKKAEqNn6LZn472g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.17.tgz_1515485697102_0.5797179029323161"}, "directories": {}, "publish_time": 1515485697348, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515485697348, "_cnpmcore_publish_time": "2021-12-16T10:07:51.797Z"}, "0.11.16": {"name": "xlsx", "version": "0.11.16", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f277ebe1409bdbda49db5c33cc167d753f0851fe", "_id": "xlsx@0.11.16", "_shasum": "e85e44e9e85d5e43e104256d8db480068aa927a3", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "e85e44e9e85d5e43e104256d8db480068aa927a3", "size": 2101040, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.16.tgz", "integrity": "sha512-1/zGvw2JK3aAzGwTFE2n2Lntqtn4Wob7DKDB5bUjJB/KirN/oCEJEQUHrP4QP+VUeIyhlprDu7EoQjybzMjHuA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.16.tgz_1514623510578_0.928651635767892"}, "directories": {}, "publish_time": 1514623512746, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514623512746, "_cnpmcore_publish_time": "2021-12-16T10:07:54.514Z"}, "0.11.15": {"name": "xlsx", "version": "0.11.15", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "33f96fc6aee5e5a9378b8700b5554301ecd805f9", "_id": "xlsx@0.11.15", "_shasum": "5f0fa6d3bf9836029d06c0d3c743b46baf1e1d8e", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5f0fa6d3bf9836029d06c0d3c743b46baf1e1d8e", "size": 2087529, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.15.tgz", "integrity": "sha512-5eSApktU1M2lE5aCi2nlLAo5Z5xJXHY/gh+uaNRXLeRJzoh0X/+/RugOQNV2FSYqqL1Xw9hQDWiiyMxHIWkE0A=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.15.tgz_1514173138898_0.5327647356316447"}, "directories": {}, "publish_time": 1514173141966, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514173141966, "_cnpmcore_publish_time": "2021-12-16T10:07:58.170Z"}, "0.11.14": {"name": "xlsx", "version": "0.11.14", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b3873ea61583549a0ce1790b724e2a129ee38eb5", "_id": "xlsx@0.11.14", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "bf9d84479c2f1486c331a80f2cb9c198da0dc85e", "size": 2089264, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.14.tgz", "integrity": "sha512-Vq1sDnO0v2QucixKMZ69fZ9wN6GhSApj1NfweoiFBhxTybpMtFTAKhY3d+oH97ZW1kp5fCbKY1dmZ39RnWFJpA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.14.tgz_1513326324982_0.6532475182320923"}, "directories": {}, "publish_time": 1513326326950, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513326326950, "_cnpmcore_publish_time": "2021-12-16T10:08:01.630Z"}, "0.11.13": {"name": "xlsx", "version": "0.11.13", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "c9cab8078c9eb34e1a70b99def6b34d0e44a180f", "_id": "xlsx@0.11.13", "_shasum": "20f87f632b282935dbd52240ab7b82d759745de0", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "20f87f632b282935dbd52240ab7b82d759745de0", "size": 2077737, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.13.tgz", "integrity": "sha512-9mjdU1/EdVec9qpL7DZBPwHxI/mekpJ57yLF0z5oMxSpmQwCu/TR+oL/oqngExAWb9MvZ2zs1zQ1hMZT5n3w/w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.13.tgz_1512804824743_0.9817875155713409"}, "directories": {}, "publish_time": 1512804826874, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512804826874, "_cnpmcore_publish_time": "2021-12-16T10:08:04.604Z"}, "0.11.12": {"name": "xlsx", "version": "0.11.12", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "eff7d153e83ce146684aaabf4b1289ca4ada0538", "_id": "xlsx@0.11.12", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "827d6ad738970a69e138619ab2c1440d2127f839", "size": 2066727, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.12.tgz", "integrity": "sha512-Sy+weyvpi7kmA78QWzK4NTCjqiZLGpbsbEK1iUS1/N9NkCHreLQnUmVySbPdh5j4+OQxB3tRvtDILtpgZ0VS/w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.12.tgz_1512366259147_0.8123012681026012"}, "directories": {}, "publish_time": 1512366261276, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512366261276, "_cnpmcore_publish_time": "2021-12-16T10:08:06.983Z"}, "0.11.11": {"name": "xlsx", "version": "0.11.11", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.1", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "cd2e639fc258a8d8b61ba6eeac39005f211e7e28", "_id": "xlsx@0.11.11", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "af8c7b448d156e2709ef9d59cf6f2f0811f297a9", "size": 2057979, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.11.tgz", "integrity": "sha512-yPL7+GKFUM157VI1P/4CArCAqYYHwYEwzVFeZlkNf/dTLDP5XEJ9Rp0MZmksvWXfgeXh6DAE7TwqPQy0ZXLCEQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.11.tgz_1512109739815_0.41873626923188567"}, "directories": {}, "publish_time": 1512109741817, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512109741817, "_cnpmcore_publish_time": "2021-12-16T10:08:11.164Z"}, "0.11.10": {"name": "xlsx", "version": "0.11.10", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.0", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "547fba56a2384b84a598ccf9ad26bab617bf92f3", "_id": "xlsx@0.11.10", "_shasum": "fc1fe4729c0fc011ab10427c8967b91863344643", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "fc1fe4729c0fc011ab10427c8967b91863344643", "size": 2051561, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.10.tgz", "integrity": "sha512-YPOTr3zqq+OnYqVMkFTbeWmC0RrK1ENIMP1NyvPyDFkhE7/9eKkB7rd7UtHXfLYFRMpTlSGBI2GhLVJLy6E8Kw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.10.tgz_1511145383904_0.4111387194134295"}, "directories": {}, "publish_time": 1511145386251, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511145386251, "_cnpmcore_publish_time": "2021-12-16T10:08:14.604Z"}, "0.11.9": {"name": "xlsx", "version": "0.11.9", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.1.0", "cfb": "~1.0.0", "codepage": "~1.11.0", "commander": "~2.11.0", "crc-32": "~1.1.1", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7c7f4a69d47ea21e9ca17999e09382212ff42e30", "_id": "xlsx@0.11.9", "_shasum": "4c0ec7fe3f5e9ed4b141db2f1bcd9157348d7765", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "4c0ec7fe3f5e9ed4b141db2f1bcd9157348d7765", "size": 2048217, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.9.tgz", "integrity": "sha512-neCWw2VGxczY1OZUBW5yW+Ktwl/a1Lfs1NY3mRg16F/BeyO+nEM50cnMNaxs9NkjrJLkJ92lWGSMj4Jv3BetKg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.9.tgz_1510774200303_0.7250824794173241"}, "directories": {}, "publish_time": 1510774202412, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510774202412, "_cnpmcore_publish_time": "2021-12-16T10:08:18.225Z"}, "0.11.8": {"name": "xlsx", "version": "0.11.8", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~1.0.0", "crc-32": "~1.1.1", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "3f6f8e93ed87d43e09fda2936e4f33773726ff87", "_id": "xlsx@0.11.8", "_shasum": "2ec1969df1eb6d23a016a8ee1cc7c6f970fed579", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "2ec1969df1eb6d23a016a8ee1cc7c6f970fed579", "size": 2045591, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.8.tgz", "integrity": "sha512-wUpBH/PEexJ3fx0K/OdFXy46WpONPz8HuxGbHBJsda3LyqeMNA4izEs+hEtHdhUqqOGvgjPSFwxV7vWdw+Mgqw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.8.tgz_1509911732599_0.2988559815566987"}, "directories": {}, "publish_time": 1509911735703, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509911735703, "_cnpmcore_publish_time": "2021-12-16T10:08:20.829Z"}, "0.11.7": {"name": "xlsx", "version": "0.11.7", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.13.2", "crc-32": "~1.1.1", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.11.7", "_shasum": "fb1ceb77154093c8719d2dc56c3a2de47634bbaf", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.8.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "fb1ceb77154093c8719d2dc56c3a2de47634bbaf", "size": 2045102, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.7.tgz", "integrity": "sha512-syCx2WGq6aPmGyYlWJ7PwK09MZt+O9avbVrYeGx1ClaPW1DXABFheidG9HpDu6lGU26sv5JuvCe2cyRNvTOwiQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.7.tgz_1509137535282_0.011704017408192158"}, "directories": {}, "publish_time": 1509137537390, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509137537390, "_cnpmcore_publish_time": "2021-12-16T10:08:22.812Z"}, "0.11.6": {"name": "xlsx", "version": "0.11.6", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.13.1", "crc-32": "~1.1.1", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f968dfe4ed5c267a16aa004066ccba0c446ab23b", "_id": "xlsx@0.11.6", "_shasum": "94cd53fc81397b5d866c8ebf1ae791d297d5f9d6", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.7.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "94cd53fc81397b5d866c8ebf1ae791d297d5f9d6", "size": 2031521, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.6.tgz", "integrity": "sha512-0qnbDeNfK0bE1uySPJede8gMxfij26XBibkRu7VoeSEbdtIpJoFrblREN7vOJ/mFzoeQ98WLUppOx9eQa8Q6/g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.6.tgz_1508202046381_0.8911866580601782"}, "directories": {}, "publish_time": 1508202048585, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508202048585, "_cnpmcore_publish_time": "2021-12-16T10:08:25.714Z"}, "0.11.5": {"name": "xlsx", "version": "0.11.5", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.13.1", "crc-32": "~1.1.1", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["wtf", "holes"]}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "af3df44633ec803990a976eb417a3f366597b952", "_id": "xlsx@0.11.5", "_shasum": "8c66cd97140be072e3dd320d12baa099689ceb74", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "8c66cd97140be072e3dd320d12baa099689ceb74", "size": 2003766, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.5.tgz", "integrity": "sha512-AsuHJMUoD1G7tvVnCpQFx/U4FgizqUXxONR9KR1oinesHQbAl48UBPKbANemf7tqn9ZAaICDkPrgXtdDb6vZVQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.5.tgz_1506756083348_0.6236308158840984"}, "directories": {}, "publish_time": 1506756085543, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506756085543, "_cnpmcore_publish_time": "2021-12-16T10:08:28.800Z"}, "0.11.4": {"name": "xlsx", "version": "0.11.4", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.13.1", "crc-32": "~1.1.1", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d02650055d05b1f267848806747546980d34c301", "_id": "xlsx@0.11.4", "_shasum": "75c8e0df4a8134342691429409313e62a8a9b1e1", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "75c8e0df4a8134342691429409313e62a8a9b1e1", "size": 1999713, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.4.tgz", "integrity": "sha512-PC8kDAcj+86ZqrAvY7lxuNuggGr9HUmJXvmSfd/WASVV3IwMFKOSHABpB06M30vy306v98/OQhB52h7ESGQ3BQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.4.tgz_1506121302123_0.19127414538525045"}, "directories": {}, "publish_time": 1506121304489, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506121304489, "_cnpmcore_publish_time": "2021-12-16T10:08:31.295Z"}, "0.11.3": {"name": "xlsx", "version": "0.11.3", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.12.1", "crc-32": "~1.1.0", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "jsdom": "~11.1.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "635310b63975ae3b139e823d1a34ad5ec28e591a", "_id": "xlsx@0.11.3", "_shasum": "d5394137d5d1a65b046bbe41c2a4d7468af8670b", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "d5394137d5d1a65b046bbe41c2a4d7468af8670b", "size": 1987942, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.3.tgz", "integrity": "sha512-6Rq5H+eJEeMcgpTBYYIBSfGz0es+XAiop6ccwfPo9BmMgTED3y5UzfCYq6VEcqrFSFKEHiK69xdll6lBbhc18Q=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.3.tgz_1503187061506_0.9873519684188068"}, "directories": {}, "publish_time": 1503187063647, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503187063647, "_cnpmcore_publish_time": "2021-12-16T10:08:33.466Z"}, "0.11.2": {"name": "xlsx", "version": "0.11.2", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.12.1", "crc-32": "~1.1.0", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5b67ac0806337135096c9e28330a65270d76fd4a", "_id": "xlsx@0.11.2", "_shasum": "d70622f786c61c9cfd70625041ba754e620e0e7c", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "d70622f786c61c9cfd70625041ba754e620e0e7c", "size": 1982427, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.2.tgz", "integrity": "sha512-8atyj4LH/2BsLQ3N9twaUzKFrCIKANFEEK5BZ7wqbYnYldYvXOivJIm2stHuEGzx8CHBrUTEqqO1BDNBE44Ltg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.2.tgz_1502412348528_0.5977027344051749"}, "directories": {}, "publish_time": 1502412350715, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502412350715, "_cnpmcore_publish_time": "2021-12-16T10:08:36.345Z"}, "0.11.1": {"name": "xlsx", "version": "0.11.1", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.11.0", "cfb": "~0.12.0", "crc-32": "~1.1.0", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.11.1", "_shasum": "02ded68001ac941b7ef9dd1cc03455e01bb0ad1e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "02ded68001ac941b7ef9dd1cc03455e01bb0ad1e", "size": 1977407, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.1.tgz", "integrity": "sha512-OMjtXyv2kK1DTrJoRUnCn77TeMXIym2sMMYUw/Yq7E+lZd9veuKAz0HXUr+iP6Uu27V5tuvJSJHBZ63PYs3CkA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.1.tgz_1501918958439_0.15880794473923743"}, "directories": {}, "publish_time": 1501918960669, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501918960669, "_cnpmcore_publish_time": "2021-12-16T10:08:38.820Z"}, "0.11.0": {"name": "xlsx", "version": "0.11.0", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.1", "codepage": "~1.10.1", "cfb": "~0.12.0", "crc-32": "~1.1.0", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7d15f35e3e819392addb61ab986c65e9d5bf1f6f", "_id": "xlsx@0.11.0", "_shasum": "****************************************", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "****************************************", "size": 1973568, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.11.0.tgz", "integrity": "sha512-r2wFIdnz84h5ezbj8S6TVNSB/iJlpnX9xyFPH+1TFrhf/tOqBYU8FEaQV9Nk07tFYXdfnD/O3CPNXwuq8tuJEw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.11.0.tgz_1501576155225_0.7438833762425929"}, "directories": {}, "publish_time": 1501576157532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501576157532, "_cnpmcore_publish_time": "2021-12-16T10:08:41.060Z"}, "0.10.9": {"name": "xlsx", "version": "0.10.9", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.1", "ssf": "~0.10.0", "codepage": "~1.10.1", "cfb": "~0.12.0", "crc-32": "~1.1.0", "adler-32": "~1.1.0", "commander": "~2.11.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.9.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "ce37f99ec32bc311c4a33fff5f99c48c0543a065", "_id": "xlsx@0.10.9", "_shasum": "2c43d8c04ec2521d61955287246ea8defe19275f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "2c43d8c04ec2521d61955287246ea8defe19275f", "size": 2024805, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.9.tgz", "integrity": "sha512-XZrdkPmoZt8qPpVmuKNe7EYXyckvBArwFI7SMKiqdXz9ZAXRWx6EFMKpGTyiKklPzRsm2n4cmzADinPIlPi1Jg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.10.9.tgz_1501287765652_0.9997775147203356"}, "directories": {}, "publish_time": 1501287767864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501287767864, "_cnpmcore_publish_time": "2021-12-16T10:08:43.389Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.8": {"name": "xlsx", "version": "0.10.8", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.4", "codepage": "~1.9.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "@sheetjs/uglify-js": "", "@types/node": "", "@types/commander": "", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "6a913d3a7b43ca5a924328faacc7094dbb937dd8", "_id": "xlsx@0.10.8", "_shasum": "0a6b66bc0c5d1738665ea28f0246f3bbf6411502", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "0a6b66bc0c5d1738665ea28f0246f3bbf6411502", "size": 2017893, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.8.tgz", "integrity": "sha512-WO0as1UdhOjWjBZo9QqomC3IQxGebreimkF5ePb6fhRj+0qZOcusxTQ7wWRsW6Wl+W0GqwxFTQhgkXMgjuaIrg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.10.8.tgz_1499728116571_0.7388213840313256"}, "directories": {}, "publish_time": 1499728116938, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499728116938, "_cnpmcore_publish_time": "2021-12-16T10:08:45.568Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.7": {"name": "xlsx", "version": "0.10.7", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.4", "codepage": "~1.9.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "@sheetjs/uglify-js": "", "@types/node": "", "@types/commander": "", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.10.7", "_shasum": "d4c530eed693f7594438d19c2fa43817ef1f753f", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "d4c530eed693f7594438d19c2fa43817ef1f753f", "size": 2014786, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.7.tgz", "integrity": "sha512-VpCfLFkcbXnyDISvYEUTjnId+D+XcdaZJ44B95yOACeeVBfebNFlBOf6LJQY2zIMXSVcaLbTRbYWTnyPGr11qw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.10.7.tgz_1499295658296_0.3898854723665863"}, "directories": {}, "publish_time": 1499295658586, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499295658586, "_cnpmcore_publish_time": "2021-12-16T10:08:47.788Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.6": {"name": "xlsx", "version": "0.10.6", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.4", "codepage": "~1.9.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "@sheetjs/uglify-js": "", "@types/node": "", "@types/commander": "", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.10.6", "_shasum": "e040d2fbb4f2a3d70907acccd077c2ed3bd93245", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "e040d2fbb4f2a3d70907acccd077c2ed3bd93245", "size": 2014163, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.6.tgz", "integrity": "sha512-Wg0RLxjuCdFNFw+46GcMOPKgYYRWy+sOk+DjAl0sZQenpk/sY2pghpqcOJ8iWtqDwKlt7SsPBmyWheyCrVtaQw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.10.6.tgz_1498289430917_0.9340819444041699"}, "directories": {}, "publish_time": 1498289433119, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498289433119, "_cnpmcore_publish_time": "2021-12-16T10:08:50.273Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.5": {"name": "xlsx", "version": "0.10.5", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.4", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "@sheetjs/uglify-js": "", "@types/node": "", "@types/commander": "", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.10.5", "_shasum": "7a93adc112179872e35531f56e7720593ef510b0", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "7a93adc112179872e35531f56e7720593ef510b0", "size": 1970425, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.5.tgz", "integrity": "sha512-<PERSON>kAScHyWcMUHAGmtSA1Af2DAOFybcOGaLFqdXFeWNYKFOdpzPMN0cMw54xo/xPBd1Q3XcI09cwIK8z1ULqww3g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx-0.10.5.tgz_1497062251991_0.5013867444358766"}, "directories": {}, "publish_time": 1497062254236, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497062254236, "_cnpmcore_publish_time": "2021-12-16T10:08:52.769Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.3": {"name": "xlsx", "version": "0.10.3", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLS/XML) ODS and other spreadsheet format (CSV/DIF/DBF/SYLK) parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "types": "types", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.3", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "@sheetjs/uglify-js": "", "@types/node": "", "@types/commander": "", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.10.3", "_shasum": "6983d9c7dd63bf0f296ac91045a3d0b57bea4d06", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "6983d9c7dd63bf0f296ac91045a3d0b57bea4d06", "size": 1959857, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.3.tgz", "integrity": "sha512-V3SDVX0mpuE2vdcsJwKHu+3AQZDSpTANKCi6LTCqz8qgwfw00cnTox4/dSt9mLX5SSUPfL+8OKMw4imgKOlbNg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.10.3.tgz_1495045202251_0.9024353458080441"}, "directories": {}, "publish_time": 1495045204462, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495045204462, "_cnpmcore_publish_time": "2021-12-16T10:08:55.410Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.1": {"name": "xlsx", "version": "0.10.1", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.2", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.10.1", "_shasum": "ec09068106c7077bdc59ec8562db7084e7864123", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "ec09068106c7077bdc59ec8562db7084e7864123", "size": 1949749, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.1.tgz", "integrity": "sha512-eXiCwadI/afQG+HUqii31U6Ip7kkAr4PAd99z7kyavzDoPQIGDeNqVNQRRFhQ1l2lfYFP2joBba2c7O6EDZe0Q=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.10.1.tgz_1494529322228_0.3902239194139838"}, "directories": {}, "publish_time": 1494529324575, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494529324575, "_cnpmcore_publish_time": "2021-12-16T10:08:57.547Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.10.0": {"name": "xlsx", "version": "0.10.0", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.2", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.10.0", "_shasum": "151a7114a5241803304c2506b23f920196ddb4e4", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "151a7114a5241803304c2506b23f920196ddb4e4", "size": 1932098, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.10.0.tgz", "integrity": "sha512-LjT5DRymUvQvXMEDLEn8qh0q2XE4I8ApIvVkeA1pM7ou6n/QFXf5uduruWSDhPYN+E9ZNM7lyUvailo/smWaow=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.10.0.tgz_1494354697789_0.13913680659607053"}, "directories": {}, "publish_time": 1494354699904, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494354699904, "_cnpmcore_publish_time": "2021-12-16T10:09:00.169Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.13": {"name": "xlsx", "version": "0.9.13", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.1", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.2", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.9.13", "_shasum": "5861d11e10a1f99b6f2b491e2d119a7777d066e7", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5861d11e10a1f99b6f2b491e2d119a7777d066e7", "size": 1905103, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.13.tgz", "integrity": "sha512-C3SsHOLbx05UCN4iRW6iL/JdO1XyPzv7XzNmLInfo2jVfZAL2XoJx3fj5q8fIZZaHI7/7uYqToQzHmHIyWTOJw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.13.tgz_1493571308522_0.12690789229236543"}, "directories": {}, "publish_time": 1493571311663, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493571311663, "_cnpmcore_publish_time": "2021-12-16T10:09:03.357Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.12": {"name": "xlsx", "version": "0.9.12", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "stream": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.0", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8c4f1f4e84c7ca8e9bd2ca9e0204034b29d0bb02", "_id": "xlsx@0.9.12", "_shasum": "97c558ba71dab088ce8949a4100b8774079601f2", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "97c558ba71dab088ce8949a4100b8774079601f2", "size": 1880172, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.12.tgz", "integrity": "sha512-2j2Q07fniB0GDY0umDIuV4RHgWG3ax7NZ9ySV1eZU0xc7XwtyK4q1vyfMx5wXg45UHhNTOcy6eCy4yWpnY2OiQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.12.tgz_1492812914748_0.7934930906631052"}, "directories": {}, "publish_time": 1492812917846, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492812917846, "_cnpmcore_publish_time": "2021-12-16T10:09:05.288Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.11": {"name": "xlsx", "version": "0.9.11", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "stream": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.0", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f51feb375a0072ae15461955f37d0047fdf521c8", "_id": "xlsx@0.9.11", "_shasum": "cafd6b052acdedd85cfc4798fb1b8aa86adf1e32", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "cafd6b052acdedd85cfc4798fb1b8aa86adf1e32", "size": 1878135, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.11.tgz", "integrity": "sha512-4hCNoAIxn3av0cKY9mgiesqx/+kgzoUqbhBdBqxYeNzminoVc3nR/sa86J50KqQgokzG4WfcvClXUkX2beuiVA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.11.tgz_1492329857175_0.9092787986155599"}, "directories": {}, "publish_time": 1492329858083, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492329858083, "_cnpmcore_publish_time": "2021-12-16T10:09:08.004Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.10": {"name": "xlsx", "version": "0.9.10", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "stream": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.0", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "51182e57efe0b695bc7a616927799a8e01465830", "_id": "xlsx@0.9.10", "_shasum": "86434dd92fc743d8fced728c3e1e373b22ca2820", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "86434dd92fc743d8fced728c3e1e373b22ca2820", "size": 1843600, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.10.tgz", "integrity": "sha512-i4DFneNFedB2WuiFNv8xOMTadibLQ7RSbLXPMeUKO6O2dwEesMW+jT3WyvswaHLXJab5W2N8VamOrGx6389D+g=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.10.tgz_1491711723807_0.049218264408409595"}, "directories": {}, "publish_time": 1491711726871, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491711726871, "_cnpmcore_publish_time": "2021-12-16T10:09:09.502Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.9": {"name": "xlsx", "version": "0.9.9", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.0", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "01d1c32fa13a857c2fa23d89baa069c3aad51788", "_id": "xlsx@0.9.9", "_shasum": "85a628139f0dd9c9bc36c5cc42cb27cdd831dfa4", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "85a628139f0dd9c9bc36c5cc42cb27cdd831dfa4", "size": 1767891, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.9.tgz", "integrity": "sha512-99JDUZN/izuB6rll8VddDmc9Srp5G4POTKiwE62IXI391svLQi4ea+V1paaR5SugZX5iXsbdcwnuO7EdnwlBCA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.9.tgz_1491200651711_0.7646330988500267"}, "directories": {}, "publish_time": 1491200654427, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491200654427, "_cnpmcore_publish_time": "2021-12-16T10:09:11.743Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.8": {"name": "xlsx", "version": "0.9.8", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.0", "codepage": "~1.8.0", "cfb": "~0.11.1", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "e42cf43c02767c92175a78812d69c1ac76eeb845", "_id": "xlsx@0.9.8", "_shasum": "ab301a81f810849a82973473ba08230a9d385386", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "ab301a81f810849a82973473ba08230a9d385386", "size": 1642015, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.8.tgz", "integrity": "sha512-PCw0JcvjJlUs5jN4xjP3x1oj7KljigQimbwP2M5XaB/6NxKknC0d5+Zvyvag34kcYBV/zUr9UGFBiYRWRXNMmg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.8.tgz_1490997736427_0.1292000573594123"}, "directories": {}, "publish_time": 1490997737266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490997737266, "_cnpmcore_publish_time": "2021-12-16T10:09:13.413Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.6": {"name": "xlsx", "version": "0.9.6", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.9.0", "codepage": "~1.7.0", "cfb": "~0.11.0", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "471275b7619267b8697794bcba859a3297caa228", "_id": "xlsx@0.9.6", "_shasum": "6a1062c4ced8407866da3183c6d96b48fdc8b1ee", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "6a1062c4ced8407866da3183c6d96b48fdc8b1ee", "size": 1585327, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.6.tgz", "integrity": "sha512-n9NHpVmrIWHheWL4O4egFOG0GCegT97NlU0nVO5hn7EzPz06e3i2KayVlo8+zIK+B+OPVCrTPlvF3BAnzKJqsA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.6.tgz_1490481935732_0.9853562549687922"}, "directories": {}, "publish_time": 1490481936532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490481936532, "_cnpmcore_publish_time": "2021-12-16T10:09:15.045Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.4": {"name": "xlsx", "version": "0.9.4", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.8.1", "codepage": "~1.7.0", "cfb": "~0.11.0", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "3cacfc406cd0bd5ce5b428517c1f7953b766d4f9", "_id": "xlsx@0.9.4", "_shasum": "e4ef9397552ed6d0e040176a7cd9342ce8eef6ed", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "e4ef9397552ed6d0e040176a7cd9342ce8eef6ed", "size": 1563859, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.4.tgz", "integrity": "sha512-//k2YjYsPDZ5xa5EpegKSLGtYwNw4JICvouNBAKgT3H1stBa+MwjHGnPHr9Z75VzbgthSii7Jg1Fgok1ZtRG2w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.4.tgz_1490047755625_0.5089759728871286"}, "directories": {}, "publish_time": 1490047756482, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490047756482, "_cnpmcore_publish_time": "2021-12-16T10:09:16.712Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.3": {"name": "xlsx", "version": "0.9.3", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.8.1", "codepage": "~1.7.0", "cfb": "~0.11.0", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make travis"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "_id": "xlsx@0.9.3", "_shasum": "46512ac09b342f6e883f737d1d4b25d667f0c94a", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "46512ac09b342f6e883f737d1d4b25d667f0c94a", "size": 1541793, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.3.tgz", "integrity": "sha512-Lv3fgUC7DmZEYE6drfRqGFvH8MukgMsA0rkHGTq2Rq0ImgJ3CXn3VhAontoth1zTf3dXwBwTqSLfa51PKd7xiw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.3.tgz_1489639577787_0.589506150688976"}, "directories": {}, "publish_time": 1489639580515, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489639580515, "_cnpmcore_publish_time": "2021-12-16T10:09:17.873Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.2": {"name": "xlsx", "version": "0.9.2", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "~1.0.0", "ssf": "~0.8.1", "codepage": "~1.7.0", "cfb": "~0.11.0", "crc-32": "~1.0.0", "adler-32": "~1.0.0", "commander": "~2.9.0"}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "456ab63dc420cc011b1dffd2d61df8e8f12329e5", "_id": "xlsx@0.9.2", "_shasum": "ab1a336e0eb960bd26adf86a47a2394b050ca0bb", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "ab1a336e0eb960bd26adf86a47a2394b050ca0bb", "size": 1521790, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.2.tgz", "integrity": "sha512-V0LhLBxRUgCO/9CumbcieSiQCWiv7B4p6DJM7c94RwL740mntp4uldokZ/iJd06dxOCbirYkAkUCax4Yh6Kb0w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.2.tgz_1489390155506_0.9617145634256303"}, "directories": {}, "publish_time": 1489390156407, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489390156407, "_cnpmcore_publish_time": "2021-12-16T10:09:19.089Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.9.0": {"name": "xlsx", "version": "0.9.0", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false}, "dependencies": {"exit-on-epipe": "", "ssf": "~0.8.1", "codepage": "", "cfb": "~0.11.0", "crc-32": "", "adler-32": "", "commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "54b528eaede80e86aec32fbbd268027b239b471a", "_id": "xlsx@0.9.0", "_shasum": "e1992a613e7686c4a226d8c1b06e05d474c7de1c", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "e1992a613e7686c4a226d8c1b06e05d474c7de1c", "size": 12051995, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.9.0.tgz", "integrity": "sha512-cb<PERSON><PERSON>cYJe60VRofQc0gbbmcSGQEBk8IGVNjdX5mIsLCeI7mYBZ6QZF8u4RGTtom0cKgqa4UdgRtZk4F8nUMY5dA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.9.0.tgz_1489110271113_0.5391368796117604"}, "directories": {}, "publish_time": 1489110272002, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489110272002, "_cnpmcore_publish_time": "2021-12-16T10:09:24.959Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.8.8": {"name": "xlsx", "version": "0.8.8", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS (ODS/FODS/UOS) spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "browser": {"node": false, "crypto": false, "fs": false, "../xlsx.js": false}, "dependencies": {"exit-on-epipe": "", "ssf": "~0.8.1", "codepage": "", "cfb": "~0.11.0", "crc-32": "", "adler-32": "", "commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "homepage": "https://oss.sheetjs.com/js-xlsx/", "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "7b6fb7b327eed15703c980a5675140d628fc09d3", "_id": "xlsx@0.8.8", "_shasum": "b92d76e0b5e4f060c9208d4b744756510443853a", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "b92d76e0b5e4f060c9208d4b744756510443853a", "size": 1432020, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.8.8.tgz", "integrity": "sha512-aNxJqlXJgzZtjxd4Qx+gCXeg/o3jNVPjHq7aQuhe2s8fmN8Z6Rqk4egQVQS7jX9L3u97X9LVWDmp9hq7zwoUeQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/xlsx-0.8.8.tgz_1489041743559_0.6318800146691501"}, "directories": {}, "publish_time": 1489041744296, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489041744296, "_cnpmcore_publish_time": "2021-12-16T10:09:26.476Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.8.0": {"name": "xlsx", "version": "0.8.0", "author": {"name": "sheetjs"}, "description": "Excel (XLSB/XLSX/XLSM/XLS/XML) and ODS spreadsheet parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.8.1", "codepage": "~1.3.6", "cfb": ">=0.10.0", "jszip": "2.4.0", "crc-32": "", "adler-32": "", "commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "704442c3a6ac8803df448fea6ef428cd88c4ff48", "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.8.0", "_shasum": "253ca61c9e1e14aa4b905dece4ce4757ace99d26", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "253ca61c9e1e14aa4b905dece4ce4757ace99d26", "size": 1336306, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.8.0.tgz", "integrity": "sha512-dLZ6OMH7KtTWi+/yO6kFZ9cpRFyYjvL+xztWSeYg8kXBc+6G9kcb2KWNZvjHMfmh/+icNM8eqOVCKyCw3qjY2g=="}, "directories": {}, "publish_time": 1428024534505, "_hasShrinkwrap": false, "_cnpm_publish_time": 1428024534505, "_cnpmcore_publish_time": "2021-12-16T10:09:27.419Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.7.12": {"name": "xlsx", "version": "0.7.12", "author": {"name": "sheetjs"}, "description": "Excel 2007+ spreadsheet (XLSB/XLSX/XLSM) and ODS parser and writer", "keywords": ["excel", "xlsx", "xlsb", "xlsm", "ods", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.8.1", "codepage": "~1.3.6", "cfb": ">=0.10.0", "jszip": "2.4.0", "crc-32": "", "adler-32": "", "commander": ""}, "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "f9097d403b44a0165ecb45dd9a798492b462b339", "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.7.12", "_shasum": "7144831d8ecd49c062141f7c48975d1a400988e4", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "7144831d8ecd49c062141f7c48975d1a400988e4", "size": 969727, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.7.12.tgz", "integrity": "sha512-+dljNu2OdnbvAsjWZPlB4YMsJjC0JcNeR0cYH5lowp2YrxF55HjgD0jcff9alUDHxEKGw0nLIlbd9K+QbteEgg=="}, "directories": {}, "publish_time": 1414303321865, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414303321865, "_cnpmcore_publish_time": "2021-12-16T10:09:28.424Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.7.0": {"name": "xlsx", "version": "0.7.0", "author": {"name": "sheetjs"}, "description": "XLSB/XLSX/XLSM (Excel 2007+ Spreadsheet) parser and writer", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.6.5", "codepage": "1.x", "cfb": "", "jszip": "2.2.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.7.0", "dist": {"shasum": "8169d2b747df502c08c00e25ae4d0d4174d31ea7", "size": 804112, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.7.0.tgz", "integrity": "sha512-gVeqQCnb/Kslgaoy4A0wBtYrJDUD+bnHLHMZh6KCGjJhpI3BnVolcpEDoP8JJFlLfvNqy3HpbmhBEs30q2FQOQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1400202605082, "_cnpm_publish_time": 1400202605082, "_cnpmcore_publish_time": "2021-12-16T10:09:29.211Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.6.1": {"name": "xlsx", "version": "0.6.1", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM (Excel 2007+ Spreadsheet) parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.6.4", "codepage": "1.x", "cfb": "", "jszip": "~2.2.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.6.1", "dist": {"shasum": "15e9dd2a4093695d3fe806cc26ffccbad2339608", "size": 712083, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.6.1.tgz", "integrity": "sha512-R<PERSON>raoaAFSDEkT2m+L0YskkM9avtVaQvQEHUwx4TJ31AxRFlaN0iaKQRkFVPRCZ+iYI+JcR7NNNhX7rrslDVsdw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398217467364, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398217467364, "_cnpmcore_publish_time": "2021-12-16T10:09:30.153Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.6.0": {"name": "xlsx", "version": "0.6.0", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM (Excel 2007+ Spreadsheet) parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.6.4", "codepage": "", "cfb": "", "jszip": "~2.1.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.6.0", "dist": {"shasum": "59914a8142dbfa2cc40f5f75957a7db30a6fd89e", "size": 192608, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.6.0.tgz", "integrity": "sha512-9VL7ICpWPCQxxIuN6VlcacIldppRw73myxdgU8Ljta3gb/URMmuuHKpHMPihrj7FXqR8ATDQV/P+wBq6MUabUA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1397553197745, "_hasShrinkwrap": false, "_cnpm_publish_time": 1397553197745, "_cnpmcore_publish_time": "2021-12-16T10:09:30.475Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.5.17": {"name": "xlsx", "version": "0.5.17", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM (Excel 2007+ Spreadsheet) parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.6.3", "codepage": "", "cfb": "", "jszip": "~2.1.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.5.17", "dist": {"shasum": "bc68f94a2b02e9547ed7b413ec22c1a0ccf08ffd", "size": 190437, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.5.17.tgz", "integrity": "sha512-QYb9fIM4eiXN63p40GSOn/Ku0puEGmCiBhdTviY075rv/jMBKPyDjPrmkEiZ2mxlwxwz3a/P5WPtigoTtMifaw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396652752937, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396652752937, "_cnpmcore_publish_time": "2021-12-16T10:09:30.944Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.5.8": {"name": "xlsx", "version": "0.5.8", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.5.6", "codepage": "", "cfb": "", "jszip": "~2.1.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.5.8", "dist": {"shasum": "d99a1345253b979b0ad7edc126ba58ca9501537c", "size": 170253, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.5.8.tgz", "integrity": "sha512-AzD6psfswYRO48JUsEdSsRZ9AOC+cC7H4VnDloN2E+7ZZaeYouIXi4jPACbHqGxETbwLKhNCQr4rhYZoQsVnSw=="}, "_from": ".", "_npmVersion": "1.3.26", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392627188538, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392627188538, "_cnpmcore_publish_time": "2021-12-16T10:09:31.317Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.5.7": {"name": "xlsx", "version": "0.5.7", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.5.6", "codepage": "", "cfb": "", "jszip": "~2.1.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.5.7", "dist": {"shasum": "8deedcd76b648d6b2dc4b6b806d178bfd0dc04b3", "size": 142007, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.5.7.tgz", "integrity": "sha512-8aTn4cajTOZR2N3EOwxohr2/thUnjgTo68Ze844EZjArVOUwPv4UKOS2hAGveYKbpkQ5ujcEKiJBwyjzA8ptBA=="}, "_from": ".", "_npmVersion": "1.3.26", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392441278249, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392441278249, "_cnpmcore_publish_time": "2021-12-16T10:09:31.705Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.5.6": {"name": "xlsx", "version": "0.5.6", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": "~0.5.6", "codepage": "", "jszip": "~2.1.0", "commander": ""}, "devDependencies": {"mocha": "", "uglify-js": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.5.6", "dist": {"shasum": "fb9970c570546517e6e07fdca9edc829cd415b7e", "size": 141367, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.5.6.tgz", "integrity": "sha512-ZAul/AT1pHDtA3hQrIk4UumrrVdql+15jVZSSv3Gv+psCeC54ecLrg/MboeYhNzxAUPqM3JXSTk3Ft75X7eOiw=="}, "_from": ".", "_npmVersion": "1.3.26", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392359297761, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392359297761, "_cnpmcore_publish_time": "2021-12-16T10:09:32.062Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.5.0": {"name": "xlsx", "version": "0.5.0", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": ">=0.5.5", "codepage": "", "jszip": "~0.2.1", "commander": ""}, "devDependencies": {"mocha": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.5.0", "dist": {"shasum": "f89e11a5e8801d2cc68df52611b576bb3b41e4d5", "size": 89470, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.5.0.tgz", "integrity": "sha512-Cz/cusxrOgCye8aud/vV9TwWGIdmWpX/ilM/M4p3Pn7Vb32Z1ezj0Wk+rHLKzklw5p5PQVp78yGkXieOQ2mkYA=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391472492335, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391472492335, "_cnpmcore_publish_time": "2021-12-16T10:09:32.344Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.4.3": {"name": "xlsx", "version": "0.4.3", "author": {"name": "sheetjs"}, "description": "XLSB / XLSX / XLSM parser", "keywords": ["xlsx", "xlsb", "xlsm", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": ">=0.5.5", "codepage": "", "jszip": "~0.2.1", "commander": ""}, "devDependencies": {"mocha": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.4.3", "dist": {"shasum": "9fcddaed5a8a26b8b24518b35bb8068e296f034a", "size": 89087, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.4.3.tgz", "integrity": "sha512-LVLVS6kFz/3u12FWmQBKPSbu5ioZBLyyk4cT0c6tldxXEFNoZdxUbIymcWoDs9vJUZKXnnIX1VunMlxoZ1qnIg=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391169398048, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391169398048, "_cnpmcore_publish_time": "2021-12-16T10:09:32.663Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.3.10": {"name": "xlsx", "version": "0.3.10", "author": {"name": "sheetjs"}, "description": "(one day) a full-featured XLSX parser and writer.  For now, primitive parser", "keywords": ["xlsx", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": ">=0.1.1", "codepage": "", "commander": ""}, "devDependencies": {"mocha": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.3.10", "dist": {"shasum": "80889db94012b14bd99ef1e6261bc2934478e38a", "size": 64913, "noattachment": false, "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.3.10.tgz", "integrity": "sha512-gpEMv7KmP8/oEi9ExStgzVnR1ulRvY1lp92xfFyt21w+sU3TT7Z+GbSRM4JCEVYdpkyb+gALpfkw3Bl4loLprA=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390688984680, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390688984680, "_cnpmcore_publish_time": "2021-12-16T10:09:32.924Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.3.3": {"name": "xlsx", "version": "0.3.3", "author": {"name": "sheetjs"}, "description": "(one day) a full-featured XLSX parser and writer.  For now, primitive parser", "keywords": ["xlsx", "office", "excel", "spreadsheet"], "bin": {"xlsx2csv": "./bin/xlsx2csv.njs"}, "main": "./xlsx", "dependencies": {"ssf": ">=0.1.1", "codepage": "", "commander": ""}, "devDependencies": {"mocha": "", "jasmine-node": "x"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make mocha", "test-jasmine": "jasmine-node --verbose tests/"}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/js-xlsx", "_id": "xlsx@0.3.3", "dist": {"tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.3.3.tgz", "shasum": "d0f2307bca5ba16051e0db39902c97ffa819cb82", "size": 55326, "noattachment": false, "integrity": "sha512-EIstFfwjxbxJ0XejMiUrhUw23PCk9cmlBJ3jgidVvbiJU7TQqteMTEOLIZRKiPtYLfBDwAhAQUOMo8OCGl3npQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386359845989, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386359845989, "_cnpmcore_publish_time": "2021-12-16T10:09:33.183Z", "deprecated": "this version is no longer supported.  More info at https://cdn.sheetjs.com/xlsx/"}, "0.17.5": {"name": "xlsx", "version": "0.17.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.15.0", "crc-32": "~1.2.0", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "0ddeba4fc358394438e3651910bce3b3ef29578a", "_id": "xlsx@0.17.5", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-lXNU0TuYsvElzvtI6O7WIVb9Zar1XYw7Xb3VAx2wn8N/n0whBYrCnHMxtFyIiUU1Wjf09WzmLALDfBO5PqTb1g==", "shasum": "78b788fcfc0773d126cdcd7ea069cb7527c1ce81", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.5.tgz", "fileCount": 30, "unpackedSize": 10946393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3INuCRA9TVsSAnZWagAA0KUP/0vueJkyISIoO+QwJ2N9\noNm9H2gj86bDUr8P2ej+cL68yRI88dogn43l8F3LFgNPrPx2loBIag7Eg/wL\nZ0mMpvIG5kD6JTxxbioWS6WGzCGEmViDK4QOEmUkYYamdIU565XN6w885PJk\nqgPVJpiInizRbYiLby4wbLtI88GQrZvWoxXGKNnprbGvATy3Qtbxux88a9BA\nOHniFQaBob7XdOEGgGlhimFEcXsZWDZBKrBeMGbet0qE2YDWVlusesa4TnJq\nSLTvBK/MURV/A7CHJbJZXlf1KOVOeELl4NJC9PEoaz05dFZ/AGu/tJsJ+zwd\nI8Nf8ucFX1ud9LstF6iQlRDMbexALENNIuVGJXQWWhXKb6RUPy8EL1v4mv01\nG3Z2Gz/ynEZXHSRelib88wbz3yF3UwAWK2GmuP3vovbLV7ZiNCui3jQ/KoUT\nBxjXmhujaLKU4JSWtX/TYjyC0kTc7r4vIQJMn9YzMLGm8tp7RY0aPTKKpgbV\neaZxBqgXdM7y2duyHS9Bn84LRjQDRBeGdW6THfbZrYS+DUnkprVHsigd/56n\n92lCCLZJapYHQHEtB/BA2zzvYoYdTsW0ifR3ulI278uEdfPu0++biKvTpCLS\niJe4sf+K59uNTjpP9cqUlXLLgDXqosN20moIYzBS7EJ5jPHZttiR3k1O393A\nfvg3\r\n=a42d\r\n-----END PGP SIGNATURE-----\r\n", "size": 3340192}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.17.5_1641841517939_0.06077030184784005"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-10T19:19:35.590Z"}, "0.18.1": {"name": "xlsx", "version": "0.18.1", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "module": "xlsx.mjs", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "8e6c0411d91edbb0ad0205a5e1231c75e728ccb5", "_id": "xlsx@0.18.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-ycGi2qQv98oPDugY2RUidLyYLEQkwLN06/Y/6pQrGNpfWrRqz/H2qQ8DhfEEtsHKIJ55Af6PLxlipLX3902l/Q==", "shasum": "38987f04cfa234e11d389be828fd04b6ad1e7b81", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.1.tgz", "fileCount": 28, "unpackedSize": 9687376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCbWuCRA9TVsSAnZWagAATEgQAIu0yrW51jwEXMCFLDef\np2+BCTHOegb2iF7FJXLqefZFFif1YyJOcQx7UpYgTK8gOsMXBnaODaAA39Rv\nRevpImelSrL2iN77wNbllZwOwmbtko3iQmZbdys9qdBBU0EOmGHoOeQzIB10\nbkPNkpTYmGdNamTX5DQIULqMWI42mXCRbNWTxCuIvf6StO3JFBCW+eXKCFQz\nJvjbeg18aiQ7wNP4U1/yAUNWQ+bELJm5Uk8TW+x1KEcXgT+YFdaEdM38uwel\nptAkLqf32vwXGYHp9QLfwaIy6KjP9fE/wL6/PV7Dqclmxsf7GA0RnlyyP0bd\nZPmzdlwuTrdIeBt1HvE3a8J0wISIFUxNZzuRekAFaTTVdhwMzE9/OH0EQydq\nMEBbJ5gKJB2O29nH0Bi10Th+X0XBl3YXKe668fpG+798mXsRhJAgY5uFHpk6\nnyFWPBUxDRXd4ashbR5LHX+NfgCeO6ntCDiFZne30uIlSf071Vayn+RsJTLv\nUAnM44GMECDO22m2Pyx5u8sB/vO9mRj2jtxQgZH+lt9xX9ArTBuIvedi0G9g\nJ506NWmUnHN6jTb7cW/GQUl/8POnR0Fo1udMdtc8tksSMZ8jw2uoIDjL9nqW\nE/Fnh0Tm2jdH8G/fNIfpcUnTrXeCGkOgoAPn8rYk3O87O47VX95hDh29Mewy\nR7m8\r\n=xqm9\r\n-----END PGP SIGNATURE-----\r\n", "size": 3091821}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.18.1_1644803501894_0.04314737776458655"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-14T01:51:55.231Z"}, "0.18.2": {"name": "xlsx", "version": "0.18.2", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "module": "xlsx.mjs", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "fbf43d4b73fe6de2fa5a078cbeb120d8ae362893", "_id": "xlsx@0.18.2", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-BW<PERSON>+GO5yg5Hnro8mpbNkZq/a+dZ8689otFuHmb9wgCtiMpL+I9dpc+Sans6K9yYxTLEZ235Kr/JkmoTEMunzQ==", "shasum": "b30b80659623d0260173b3c96c4ad308e9a5151e", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.2.tgz", "fileCount": 29, "unpackedSize": 10007995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC1jKCRA9TVsSAnZWagAA+usP/3iMpcPekSqcoJs61vLs\nSAiHwZOjNKVfzAa0HdF4SNfVy3G7YbmUh28bqJ8NnkcviW0WyNYHzVV2lCqR\ni1WWr01ZoPNQACN491P4j22+0bFeSDixfZL9Gkpa3bhk3LhFaEEWC2Z8ykxr\nIJPTeGQc6cam2BFXXOY08tS7TtxWs/OZ7tOfP+V6/8CSdCX8sfMP6z0AUg9V\nSo7ZTpTN1W0exxSZ3eUpy9PVWXbt8H0oIGlVC16iSP1PeNjaucEvs2w8BMZ0\nYf8V5qcY3+KAkli8lnviDbrZ9ggP25BHQTF/eggqWQLWhLbgk8/WJwtBHKDP\nMKwKIajbWl+eA9KsrbHqv9mbLbUnNmyfAh9FfDrT9uabTF06SgklSzxsev1O\nnxVUDfNXPBw4MhQVrhng5Xxe0ObscuXHjBuGndh06soTooJGY6QKI//omzoQ\nP8T/bYEvElkDnnDOcz+6HemD5zJgAmTjy6cmXjW8a+bwur+bnN5mR7E3GsO7\n62VDCHF9nPKG6BzAMFj33Y1Z9VL565lQPPQJ4LEmhWNgXKPqLh3hHaMliTp0\n3obY4EmtVFQ+yn4r5VwsI8XDV1K3bU8J91zaY/wGqDnFORFvgvTYWxkLc22L\nxr+I0lu8ulWLryyvCxZ4V3b+eAj6Sn+WuROdUTGaXfHA/t18ZkbOv0vZbRqz\nnkOk\r\n=eaWB\r\n-----END PGP SIGNATURE-----\r\n", "size": 3408175}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.18.2_1644910794103_0.19496961202009944"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-15T07:40:03.446Z"}, "0.18.3": {"name": "xlsx", "version": "0.18.3", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "module": "xlsx.mjs", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "e14aee3e512a838a9d2a125568c8ae8070695957", "_id": "xlsx@0.18.3", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-iNBdKSJO11bI59C/dJjYWfR2gglRUrbWtDK9YoKKvu+RA1dinyWU3XXc0BaZUoSYFkxvPrO/9dwBGu6mhVNVGQ==", "shasum": "03a95a1082b0ac436afa0fcbc50adb98ee4b288e", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.3.tgz", "fileCount": 24, "unpackedSize": 7370052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIIXaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppRA/+NfidOlojolslm8T9sem9JIrCBL4rBW1eY8HprnGfO1Pb5kt6\r\nBnj3Nw8Frt5bSUTt0/gqhLkCiCC0sksHkJ+MT/JtSO0yaC452FqDWRkyjpQB\r\nAGD42lSy+rjUCKiaYfXs+7i/iNEgJa5rmYU/Sz3Rnmj58Imtess8g5IfPRVT\r\nDcv92/suKmh9nCsTVaAzlMNxPwI9/zrp361fa3XgOOB4ClLCfxR4RFNw9U26\r\nSy4PFD/b0kmUagH68MZxhP9eH4lnfGoksQ5klhTl4+nz4VNJu3kEFFhQ1sKG\r\nNyuRjv0M7tY9U+Z0TyoZN7ZC8qjkrfpXq031tPlrehqeTQnsreaTpm/MHsHF\r\nZRvDf5iLl0dujgOMDZfmA2hfnIREWKzDmdsGE5EeEfz99M5Wj68EFbkYhPGC\r\nHxe36LHL88nSJIVxSig/E4Qf9mqDtbqfrso4p//aaLRMmw0xjFX2yWYCvlAN\r\n6mkep/HybBJCCClJPi+NNCZSVTAcEGENFhteNQhdqcRvMmstjPN9fZ77jv+i\r\njagZZiUf7bfpBCDIDm9X616OgKcB0nnd2iCekTOnvwLaut7kQpjbNOxGAyQf\r\nQXhGVs9gAjgx37I2nXYKWBm7H+5VelFLiv9P9EaCZioy244GOFjBx8/L0Mdx\r\nRWTn34I7tXxUu0I4nDRygwKhVukqV8tExMU=\r\n=2OpX\r\n-----END PGP SIGNATURE-----\r\n", "size": 2430423}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.18.3_1646298585900_0.3630564141999386"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-03T09:11:01.937Z"}, "0.18.4": {"name": "xlsx", "version": "0.18.4", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "module": "xlsx.mjs", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "^9.1.0", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "61b17a8bc857dc7d20dd3ec1248024a5c45c6e38", "_id": "xlsx@0.18.4", "_nodeVersion": "16.14.1", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-u2plLVR/Ju2NxByPcWuxW3eOHEE/EH8w0vDyvHx8pFa8qO3fpq+l/57msr/Eq4cY5LErFOFD4sp4jIyPd6nFzQ==", "shasum": "eae7df428d6eb6a57cf595ee1fff6df95cc0a43d", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.4.tgz", "fileCount": 24, "unpackedSize": 7310583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMWIbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXbQ//U6u8u/tNYgy2tZsIhmGqjawaT+XmyqgHQH/dRQ6GGuzeZ83f\r\ndeeXYcKhkAMx4FZ2uoTeDTUejR9s8F4pwlDr95Dedwc3CHToXHSiHlMJLUTV\r\nCtncTZ0ylORLF4zj1uUBGDFT1gAQaccWdXOZ/Aw5IbGgE+db5EcIFyDknJCh\r\nptIUsIa1RCeKchC05ZubWLTCbhi2ltGHRzugVjUHxGUWcGoE0KJkIFYIVTIx\r\n4yMyEoauD50vkcANdxng9DnwQEdfghVBwtdEnfi+03qHMjC/8hpErmFkglpI\r\nY/8Q3fbii95XcYnel2MiV0lBvXHRVnYGTzn/OALXYO126PhfBwtIENivumO8\r\n3RHrDldX4brU1eakZ3iIKRsXSUtdUdGre6nKpFrZ69DOQWvzQ1hP2glTOdxc\r\nhLyH50vh3nMKsE3IZEmP8CFJWtexy88z3U5R82d46yNZeFETB9cPJ16oYDde\r\nFvmtZdXuMPdN19q+oasDKQ0I/IQH8jg+NsmzHdjuYdAKMMyRoSzaBYWzRzsQ\r\nZCZ/8dA1498Kh7wkyK7StTWg1acUn8goVty5sZ5M57lUn0XDXvfr2BY2UV/Z\r\na0COqKVKjWRuOD+DCqCw5ABKHBWO87SZ/vsWaoe3Tx15UhYOExKY8fm+wLS6\r\nrwEPTdhnBVjTyko5d46Me2iA3ty3AvR2PCU=\r\n=8LuZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 2347734}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.18.4_1647403546886_0.7115914327072403"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-16T04:05:55.048Z"}, "0.18.5": {"name": "xlsx", "version": "0.18.5", "author": {"name": "sheetjs"}, "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "bin/xlsx.njs"}, "main": "xlsx.js", "module": "xlsx.mjs", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "sideEffects": false, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "8.1.1", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "0400a87e622887ec83f6bcde9d0ad3ce99ee13f8", "_id": "xlsx@0.18.5", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==", "shasum": "16711b9113c848076b8a177022799ad356eba7d0", "tarball": "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.5.tgz", "fileCount": 26, "unpackedSize": 7499035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPH7NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvZg//aqdZbCIstWq2ojzYB8PEASWtSvhGG7tXLxg01kUdWhYMl7zk\r\nPtQIo4OwE4Q66R2J2HOZ3owLwMYlU09gvWx+f91gnY1lMzAGWrAkEFikegSS\r\nup7nQ40yF8Z2d90zkYJVBuux5ORO2yusyOrciWh7L93DtfFhW2+Pi03ZdRHk\r\nhTRdGcwjGwSNnkVAWaUyhsn15C1ePZCSN+0alZMSXKbxDYBGRIEOooUeO7x4\r\nyFHG0Njyx/GmBJcP/oS4pTNKvOud9jqmF2Xl7xDKEv/Y11F6hhtqzvLTH8eH\r\nSZ7wgnfexm1aZ6e/QQKUjhxHxhPparH9z/ugz5c7wAz1K9OAaYy/Z1PMsKOl\r\n8vmrGJ+IfGikJMW2iPAQwilzM7SMsbNF8X8lq+udVsMrfDsPdbsuNClNzZxh\r\nEAwHuLCbO6vj6k26su0W8ODXLgYEraQ0A+UhZR0wC9/lKWmrm70cHAx1EjDi\r\nkD3KV0yQ3VjJxHPNjbW6vQEEsAF0D7GLS32Qo1znDbQX4i2sc0KHrAwUz7IZ\r\nRthuNmXZeoiqunvcge2WH7rGtmNoO6SqTaFfS0kOEVg2VgPY063Ny4l5IT53\r\nZNf1FABjHhj7PJjyeyDSTdhPt0/JiLzBbThNImk63KPVw1xy0oiKh+fiSNO5\r\n1t4DexEvYJjVbHJoLj9H/xzSW3sQzSJG7uY=\r\n=ZSEx\r\n-----END PGP SIGNATURE-----\r\n", "size": 2441660}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xlsx_0.18.5_1648131789359_0.5099421847379153"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-24T15:00:09.799Z"}}, "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "homepage": "https://sheetjs.com/", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "_source_registry_name": "default"}