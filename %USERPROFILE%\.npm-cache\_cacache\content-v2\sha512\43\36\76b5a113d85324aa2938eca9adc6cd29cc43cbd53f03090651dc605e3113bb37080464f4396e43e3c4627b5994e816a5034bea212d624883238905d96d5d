{"_id": "@esbuild/openbsd-arm64", "_rev": "4614593-66819a4fc61208940274e059", "dist-tags": {"latest": "0.25.6"}, "name": "@esbuild/openbsd-arm64", "time": {"created": "2024-06-30T17:47:59.853Z", "modified": "2025-07-07T17:28:57.581Z", "0.0.1": "2024-06-30T17:45:43.452Z", "0.22.0": "2024-06-30T20:37:40.512Z", "0.23.0": "2024-07-02T03:33:36.244Z", "0.23.1": "2024-08-16T22:13:07.339Z", "0.24.0": "2024-09-22T02:06:16.033Z", "0.24.1": "2024-12-20T05:40:30.055Z", "0.24.2": "2024-12-20T17:55:52.372Z", "0.25.0": "2025-02-08T03:01:59.985Z", "0.25.1": "2025-03-10T03:45:10.042Z", "0.25.2": "2025-03-30T17:32:43.603Z", "0.25.3": "2025-04-23T03:55:32.356Z", "0.25.4": "2025-05-06T00:30:28.840Z", "0.25.5": "2025-05-27T03:12:28.338Z", "0.25.6": "2025-07-07T16:58:42.288Z"}, "versions": {"0.0.1": {"name": "@esbuild/openbsd-arm64", "version": "0.0.1", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.0.1", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "3ec74f7714d53b7069710d0665fd629e2d43d840", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.0.1.tgz", "fileCount": 2, "integrity": "sha512-87kxwOiAQ6jF2Varulbu3BvILT203nSsdOvA24MEv+itmeC0kBphunvJANgcTlg8x5lf2wtiz8+mnc0Ai/qoOg==", "signatures": [{"sig": "MEYCIQDIrcvxL2rMkhfYFfUe/YsPlbbjbGFWC6xAPLUQRBuMiAIhAOsAMDCtS4tezjNvR0wFPY7HnnLpGb/sLuZ4rE3eTs1Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 532, "size": 411}, "engines": {"node": ">=12"}, "gitHead": "b7220009d0fde2e89917ed3ea97c6b8ca04adbf1", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.0.1_1719769543264_0.3644942174725192", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-30T17:45:43.452Z", "publish_time": 1719769543452, "_source_registry_name": "default"}, "0.22.0": {"name": "@esbuild/openbsd-arm64", "version": "0.22.0", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.22.0", "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-<PERSON>zlhu/YyITmXwKSB+Zu/QqD7cxrjrpiw85cc0Rbd3AWr2wsgp+dWbWOE8MqHaLW9NKMZvuL0DhbJbvzR7F6Zvg==", "shasum": "7cb42e3a0d3da039d1a4b7ccbd0c19b0f71ae453", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.22.0.tgz", "fileCount": 3, "unpackedSize": 9372461, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFicHvaLsr6qcRkD9JauO/Dakz1NaOnyvXr+zwMp1chfAiBOXwXJlaf/Ke5PJIZtPMcQQLJJpGXnTCFPaEH9jmE/LA=="}], "size": 3889642}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/openbsd-arm64_0.22.0_1719779860284_0.2356870049158184"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T20:37:40.512Z", "publish_time": 1719779860512, "_source_registry_name": "default"}, "0.23.0": {"name": "@esbuild/openbsd-arm64", "version": "0.23.0", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.23.0", "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-suXjq53gERueVWu0OKxzWqk7NxiUWSUlrxoZK7usiF50C6ipColGR5qie2496iKGYNLhDZkPxBI3erbnYkU0rQ==", "shasum": "72fc55f0b189f7a882e3cf23f332370d69dfd5db", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.23.0.tgz", "fileCount": 3, "unpackedSize": 9372461, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+6h7RMC61xshWJXxB2NX3iozt8AHPyBJ9BOWyAOzwNgIhAKwrYcLuJMl0r2TC2hEqwhn2jpyhGtg4NEZHlCzL7Erf"}], "size": 3889583}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/openbsd-arm64_0.23.0_1719891215960_0.8947388456607726"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T03:33:36.244Z", "publish_time": 1719891216244, "_source_registry_name": "default"}, "0.23.1": {"name": "@esbuild/openbsd-arm64", "version": "0.23.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.23.1", "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-3x37szhLexNA4bXhLrCC/LImN/YtWis6WXr1VESlfVtVeoFJBRINPJ3f0a/6LV8zpikqoUg4hyXw0sFBt5Cr+Q==", "shasum": "05c5a1faf67b9881834758c69f3e51b7dee015d7", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.23.1.tgz", "fileCount": 3, "unpackedSize": 9372461, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGxU4U3yyp6R7KJVmS3+3V54B99qCVD42zvX6BslnbjTAiBTpw3o1jyJnwhniy3PWAp4XcfoAuJ4A+sQcDHBrwxvbQ=="}], "size": 3892115}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/openbsd-arm64_0.23.1_1723846387061_0.14930368564966812"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-16T22:13:07.339Z", "publish_time": 1723846387339, "_source_registry_name": "default"}, "0.24.0": {"name": "@esbuild/openbsd-arm64", "version": "0.24.0", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.24.0", "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-MD9uzzkPQbYehwcN583yx3Tu5M8EIoTD+tUgKF982WYL9Pf5rKy9ltgD0eUgs8pvKnmizxjXZyLt0z6DC3rRXg==", "shasum": "5d904a4f5158c89859fd902c427f96d6a9e632e2", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.0.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvFETxWdWiDBzcZaqQ0eOmdNwyEsPQDVKG6i28+YLCgwIhAPQpOhliE1mSTm/jiMy3LkKb5oB62BZ7vBDs4TUObo8V"}], "size": 3968235}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/openbsd-arm64_0.24.0_1726970775778_0.6290145734894095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-22T02:06:16.033Z", "publish_time": 1726970776033, "_source_registry_name": "default"}, "0.24.1": {"name": "@esbuild/openbsd-arm64", "version": "0.24.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.24.1", "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-wy2psEw0wc+xbSB4Et3XZaONClCagOlQTsqRJaLtCcPggnuZMfb17c5T5w6RO6pFF5J2SWoM7+MJuWUEzvQN+Q==", "shasum": "117fe1658cab3f2b42c10ed1fae908f571acf4af", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.1.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUstpO70HSFMJr2Jx1keZPkFhxguUz0jPsFu3jluatJgIhAP0KiOmBmh0U0qh65ukbmeOlO6A/22nAJ07qtHeH0tWj"}], "size": 3971215}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.24.1_1734673229821_0.07682383815466376"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T05:40:30.055Z", "publish_time": 1734673230055, "_source_registry_name": "default"}, "0.24.2": {"name": "@esbuild/openbsd-arm64", "version": "0.24.2", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.24.2", "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-YQbi46SBct6iKnszhSvdluqDmxCJA+Pu280Av9WICNwQmMxV7nLRHZfjQzwbPs3jeWnuAhE9Jy0NrnJ12Oz+0A==", "shasum": "58b00238dd8f123bfff68d3acc53a6ee369af89f", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.2.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEYvplvY8ZnHHttwjq8I92dSefhzhyXEWNYXVC4voY9vAiEAwkgwO1GBXU5D5In3mbyvRan6K++oP20HsRKTlFUDTuw="}], "size": 3971649}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.24.2_1734717352096_0.08728590777037071"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T17:55:52.372Z", "publish_time": 1734717352372, "_source_registry_name": "default"}, "0.25.0": {"name": "@esbuild/openbsd-arm64", "version": "0.25.0", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.0", "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==", "shasum": "8fd55a4d08d25cdc572844f13c88d678c84d13f7", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCxroMjNz9Avv1LraHJ1VTTRqaVryFnmP+XqNdTSMNPYwIgBRxegpCLuHbH6sxxYc2gbHo96o9uDXr1Tm+/sAmTSJc="}], "size": 3992859}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.0_1738983719684_0.4493517058259433"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-08T03:01:59.985Z", "publish_time": 1738983719985, "_source_registry_name": "default"}, "0.25.1": {"name": "@esbuild/openbsd-arm64", "version": "0.25.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.1", "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Na9T3szbXezdzM/Kfs3GcRQNjHzM6GzFBeU1/6IV/npKP5ORtp9zbQjvkDJ47s6BCgaAZnnnu/cY1x342+MvZg==", "shasum": "d9021b884233673a05dc1cc26de0bf325d824217", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.1.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCiW7QOLn85ldo5QkEpJFmrBNLgGvaWOl7auObv6Hjg7QIhAIziebHKavaET0Em1my5aAMKUJ6hGrbmm2+I/T0tM709"}], "size": 3994291}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.1_1741578309761_0.8475919071009486"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-10T03:45:10.042Z", "publish_time": 1741578310042, "_source_registry_name": "default"}, "0.25.2": {"name": "@esbuild/openbsd-arm64", "version": "0.25.2", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.2", "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-dcXYOC6NXOqcykeDlwId9kB6OkPUxOEqU+rkrYVqJbK2hagWOMrsTGsMr8+rW02M+d5Op5NNlgMmjzecaRf7Tg==", "shasum": "f9caf987e3e0570500832b487ce3039ca648ce9f", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.2.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDdTgRNoGy+NU4AadkePzfaTd6JnHAxvrSy9hkeAgJkVwIgO2mdTjBB9yFbeev+lq78hnIntrUHjC1tf7vuxAxA7hc="}], "size": 3996436}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.2_1743355963347_0.9675726175568138"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-30T17:32:43.603Z", "publish_time": 1743355963603, "_source_registry_name": "default"}, "0.25.3": {"name": "@esbuild/openbsd-arm64", "version": "0.25.3", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.3", "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-zGAVApJEYTbOC6H/3QBr2mq3upG/LBEXr85/pTtKiv2IXcgKV0RT0QA/hSXZqSvLEpXeIxah7LczB4lkiYhTAQ==", "shasum": "ca078dad4a34df192c60233b058db2ca3d94bc5c", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.3.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHYCW1YM0UpYjyC/imMmMHP61AfbekyEs57AJXY1IexiAiEA2iw166bXlM9FNw2KUzmHL258UePoNHGR3/cMtiHhSGk="}], "size": 3999505}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.3_1745380532085_0.10621904759230372"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-23T03:55:32.356Z", "publish_time": 1745380532356, "_source_registry_name": "default"}, "0.25.4": {"name": "@esbuild/openbsd-arm64", "version": "0.25.4", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.4", "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Ct2WcFEANlFDtp1nVAXSNBPDxyU+j7+tId//iHXU2f/lN5AmO4zLyhDcpR5Cz1r08mVxzt3Jpyt4PmXQ1O6+7A==", "shasum": "f272c2f41cfea1d91b93d487a51b5c5ca7a8c8c4", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.4.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAoN5GgbGW7w2hyAjCVwAQrfqqicVMRjhCm662Fsw6o7AiEAjKvI396RUibAdqTbMgpp7W1ylElrReAJXrCr71E/3/w="}], "size": 4001852}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.4_1746491428551_0.26728045581262605"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T00:30:28.840Z", "publish_time": 1746491428840, "_source_registry_name": "default"}, "0.25.5": {"name": "@esbuild/openbsd-arm64", "version": "0.25.5", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==", "shasum": "2a796c87c44e8de78001d808c77d948a21ec22fd", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDpUXT9sUF8JUNiUM0QS939lHn9FYJa8K2NJmVqsvZREQIgcd30/VaDsPUMOYRdTPKZJw6UnsHXkTZirX7k5XA62hI="}], "size": 4002392}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.5_1748315548074_0.1502666641047985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-27T03:12:28.338Z", "publish_time": 1748315548338, "_source_registry_name": "default"}, "0.25.6": {"name": "@esbuild/openbsd-arm64", "version": "0.25.6", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.6", "gitHead": "d38c1f0bc580b4a8a93f23559d0cd9085d7ba31f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-l8ZCvXP0tbTJ3iaqdNf3pjaOSd5ex/e6/omLIQCVBLmHTlfXW3zAxQ4fnDmPLOB1x9xrcSi/xtCWFwCZRIaEwg==", "shasum": "a51be60c425b85c216479b8c344ad0511635f2d2", "tarball": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.6.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGgi8L2YBduDIreJ31LDMHfZ5w0bWqDC4uWSv7KInEWPAiEAny2YhQEr+lFYdnpj5lUjxDoNRcOn9bO+jFqxIIUv40E="}], "size": 4003325}, "_npmUser": {"name": "evanw", "email": "<EMAIL>", "actor": {"name": "evanw", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.6_1751907522029_0.5602833565985634"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T16:58:42.288Z", "publish_time": 1751907522288, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "_source_registry_name": "default"}