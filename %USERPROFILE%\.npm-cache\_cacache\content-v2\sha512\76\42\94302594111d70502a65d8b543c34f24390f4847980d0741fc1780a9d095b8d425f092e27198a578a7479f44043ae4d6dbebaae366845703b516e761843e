{"_attachments": {}, "_id": "vue-eslint-parser", "_rev": "8400-61f164174ce7cf8f582900c8", "author": {"name": "<PERSON><PERSON>"}, "description": "The ESLint custom parser for `.vue` files.", "dist-tags": {"experimental": "2.0.1-beta.3", "latest": "10.2.0", "next": "9.0.0"}, "license": "MIT", "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "name": "vue-eslint-parser", "readme": "# vue-eslint-parser\n\n[![npm version](https://img.shields.io/npm/v/vue-eslint-parser.svg)](https://www.npmjs.com/package/vue-eslint-parser)\n[![Downloads/month](https://img.shields.io/npm/dm/vue-eslint-parser.svg)](http://www.npmtrends.com/vue-eslint-parser)\n[![Build Status](https://github.com/vuejs/vue-eslint-parser/workflows/CI/badge.svg)](https://github.com/vuejs/vue-eslint-parser/actions)\n\nThe ESLint custom parser for `.vue` files.\n\n## ⤴️ Motivation\n\nThis parser allows us to lint the `<template>` of `.vue` files. We can make mistakes easily on `<template>` if we use complex directives and expressions in the template. This parser and the rules of [eslint-plugin-vue](https://github.com/vuejs/eslint-plugin-vue) would catch some of the mistakes.\n\n## 💿 Installation\n\n```bash\nnpm install --save-dev eslint vue-eslint-parser\n```\n\n## 📖 Usage\n\nWrite `parser` option into your `eslint.config.*` file.\n\n```js\nimport vueParser from \"vue-eslint-parser\"\nexport default [\n    js.configs.recommended,\n    {\n        files: [\"*.vue\", \"**/*.vue\"],\n        languageOptions: {\n            parser: vueParser,\n        },\n    }\n]\n```\n\n## 🔧 Options\n\n`parserOptions` has the same properties as what [espree](https://github.com/eslint/espree#usage), the default parser of ESLint, is supporting.\nFor example:\n\n```js\nimport vueParser from \"vue-eslint-parser\"\nexport default [\n    {\n        files: [\"*.vue\", \"**/*.vue\"],\n        languageOptions: {\n            parser: vueParser,\n            sourceType: \"module\",\n            ecmaVersion: \"latest\",\n            parserOptions: {\n                ecmaFeatures: {\n                    globalReturn: false,\n                    impliedStrict: false,\n                    jsx: false\n                }\n            }\n        },\n    }\n]\n```\n\n### parserOptions.parser\n\nYou can use `parserOptions.parser` property to specify a custom parser to parse `<script>` tags.\nOther properties than parser would be given to the specified parser.\nFor example:\n\n```js\nimport vueParser from \"vue-eslint-parser\"\nimport babelParser from \"@babel/eslint-parser\"\nexport default [\n    {\n        files: [\"*.vue\", \"**/*.vue\"],\n        languageOptions: {\n            parser: vueParser,\n            parserOptions: {\n                parser: babelParser,\n            }\n        },\n    }\n]\n```\n\n```js\nimport vueParser from \"vue-eslint-parser\"\nimport tsParser from \"@typescript-eslint/parser\"\nexport default [\n    {\n        files: [\"*.vue\", \"**/*.vue\"],\n        languageOptions: {\n            parser: vueParser,\n            parserOptions: {\n                parser: tsParser,\n            }\n        },\n    }\n]\n```\n\nYou can also specify an object and change the parser separately for `<script lang=\"...\">`.\n\n```js\nimport vueParser from \"vue-eslint-parser\"\nimport tsParser from \"@typescript-eslint/parser\"\nexport default [\n    {\n        files: [\"*.vue\", \"**/*.vue\"],\n        languageOptions: {\n            parser: vueParser,\n            parserOptions: {\n                \"parser\": {\n                    // Script parser for `<script>`\n                    \"js\": \"espree\",\n\n                    // Script parser for `<script lang=\"ts\">`\n                    \"ts\": tsParser,\n\n                    // Script parser for vue directives (e.g. `v-if=` or `:attribute=`)\n                    // and vue interpolations (e.g. `{{variable}}`).\n                    // If not specified, the parser determined by `<script lang =\"...\">` is used.\n                    \"<template>\": \"espree\",\n                }\n            }\n        },\n    }\n]\n```\n\nIf the `parserOptions.parser` is `false`, the `vue-eslint-parser` skips parsing `<script>` tags completely.\nThis is useful for people who use the language ESLint community doesn't provide custom parser implementation.\n\n### parserOptions.vueFeatures\n\nYou can use `parserOptions.vueFeatures` property to specify how to parse related to Vue features.\nFor example:\n\n```js\nimport vueParser from \"vue-eslint-parser\"\nexport default [\n    {\n        files: [\"*.vue\", \"**/*.vue\"],\n        languageOptions: {\n            parser: vueParser,\n            parserOptions: {\n                vueFeatures: {\n                    filter: true,\n                    interpolationAsNonHTML: true,\n                    styleCSSVariableInjection: true,\n                    customMacros: []\n                }\n            }\n        },\n    }\n]\n```\n\n### parserOptions.vueFeatures.filter\n\nYou can use `parserOptions.vueFeatures.filter` property to specify whether to parse the Vue2 filter. If you specify `false`, the parser does not parse `|` as a filter.\nFor example:\n\n```json\n{\n    \"parserOptions\": {\n        \"vueFeatures\": {\n            \"filter\": false\n        }\n    }\n}\n```\n\nIf you specify `false`, it can be parsed in the same way as Vue 3.\nThe following template parses as a bitwise operation.\n\n```vue\n<template>\n  <div>{{ a | b }}</div>\n</template>\n```\n\nHowever, the following template that are valid in Vue 2 cannot be parsed.\n\n```vue\n<template>\n  <div>{{ a | valid:filter }}</div>\n</template>\n```\n\n### parserOptions.vueFeatures.interpolationAsNonHTML\n\nYou can use `parserOptions.vueFeatures.interpolationAsNonHTML` property to specify whether to parse the interpolation as HTML. If you specify `true`, the parser handles the interpolation as non-HTML (However, you can use HTML escaping in the interpolation). Default is `true`.\nFor example:\n\n```json\n{\n    \"parserOptions\": {\n        \"vueFeatures\": {\n            \"interpolationAsNonHTML\": true\n        }\n    }\n}\n```\n\nIf you specify `true`, it can be parsed in the same way as Vue 3.\nThe following template can be parsed well.\n\n```vue\n<template>\n  <div>{{a<b}}</div>\n</template>\n```\n\nBut, it cannot be parsed with Vue 2.\n\n### parserOptions.vueFeatures.styleCSSVariableInjection\n\nIf set to `true`, to parse expressions in `v-bind` CSS functions inside `<style>` tags. `v-bind()` is parsed into the `VExpressionContainer` AST node and held in the `VElement` of `<style>`. Default is `true`.\n\nSee also to [here](https://github.com/vuejs/rfcs/blob/master/active-rfcs/0043-sfc-style-variables.md).\n\n### parserOptions.vueFeatures.customMacros\n\nSpecifies an array of names of custom macros other than Vue standard macros.  \nFor example, if you have a custom macro `defineFoo()` and you want it processed by the parser, specify `[\"defineFoo\"]`.\n\nNote that this option only works in `<script setup>`.\n\n### parserOptions.templateTokenizer\n\n**This is an experimental feature. It may be changed or deleted without notice in the minor version.**\n\nYou can use `parserOptions.templateTokenizer` property to specify custom tokenizers to parse `<template lang=\"...\">` tags.\n\nFor example to enable parsing of pug templates:\n\n```jsonc\n{\n    \"parserOptions\": {\n        \"templateTokenizer\": {\n             // template tokenizer for `<template lang=\"pug\">`\n            \"pug\": \"vue-eslint-parser-template-tokenizer-pug\",\n        }\n    }\n}\n```\n\nThis option is only intended for plugin developers. **Be careful** when using this option directly, as it may change behaviour of rules you might have enabled.  \nIf you just want **pug** support, use [eslint-plugin-vue-pug](https://github.com/rashfael/eslint-plugin-vue-pug) instead, which uses this option internally.\n\nSee [implementing-custom-template-tokenizers.md](./docs/implementing-custom-template-tokenizers.md) for information on creating your own template tokenizer.\n\n## 🎇 Usage for custom rules / plugins\n\n- This parser provides `parserServices` to traverse `<template>`.\n    - `defineTemplateBodyVisitor(templateVisitor, scriptVisitor, options)` ... returns ESLint visitor to traverse `<template>`.\n    - `getTemplateBodyTokenStore()` ... returns ESLint `TokenStore` to get the tokens of `<template>`.\n    - `getDocumentFragment()` ... returns the root `VDocumentFragment`.\n    - `defineCustomBlocksVisitor(context, customParser, rule, scriptVisitor)` ... returns ESLint visitor that parses and traverses the contents of the custom block.\n    - `defineDocumentVisitor(documentVisitor, options)` ... returns ESLint visitor to traverses the document.\n- [ast.md](./docs/ast.md) is `<template>` AST specification.\n- [mustache-interpolation-spacing.js](https://github.com/vuejs/eslint-plugin-vue/blob/b434ff99d37f35570fa351681e43ba2cf5746db3/lib/rules/mustache-interpolation-spacing.js) is an example.\n- Check your version of ESLint as the location of `defineTemplateBodyVisitor` was moved from `context` to `context.sourceCode` after major version `9.x`\n\n### `defineTemplateBodyVisitor(templateBodyVisitor, scriptVisitor, options)`\n\n*Arguments*\n\n- `templateBodyVisitor` ... Event handlers for `<template>`.\n- `scriptVisitor` ... Event handlers for `<script>` or scripts. (optional)\n- `options` ... Options. (optional)\n  - `templateBodyTriggerSelector` ... Script AST node selector that triggers the templateBodyVisitor. Default is `\"Program:exit\"`. (optional)\n\n```ts\nimport { AST } from \"vue-eslint-parser\"\n\nexport function create(context) {\n    return context.sourceCode.parserServices.defineTemplateBodyVisitor(\n        // Event handlers for <template>.\n        {\n            VElement(node: AST.VElement): void {\n                //...\n            }\n        },\n        // Event handlers for <script> or scripts. (optional)\n        {\n            Program(node: AST.ESLintProgram): void {\n                //...\n            }\n        },\n        // Options. (optional)\n        {\n            templateBodyTriggerSelector: \"Program:exit\"\n        }\n    )\n}\n```\n\n## ⚠️ Known Limitations\n\nSome rules make warnings due to the outside of `<script>` tags.\nPlease disable those rules for `.vue` files as necessary.\n\n- [eol-last](http://eslint.org/docs/rules/eol-last)\n- [linebreak-style](http://eslint.org/docs/rules/linebreak-style)\n- [max-len](http://eslint.org/docs/rules/max-len)\n- [max-lines](http://eslint.org/docs/rules/max-lines)\n- [no-trailing-spaces](http://eslint.org/docs/rules/no-trailing-spaces)\n- [unicode-bom](http://eslint.org/docs/rules/unicode-bom)\n- Other rules which are using the source code text instead of AST might be confused as well.\n\n## 📰 Changelog\n\n- [GitHub Releases](https://github.com/vuejs/vue-eslint-parser/releases)\n\n## 🍻 Contributing\n\nWelcome contributing!\n\nPlease use GitHub's Issues/PRs.\n\nIf you want to write code, please execute `npm install` after you cloned this repository.\nThe `npm install` command installs dependencies.\n\n### Development Tools\n\n- `npm test` runs tests and measures coverage.\n- `npm run build` compiles TypeScript source code to `index.js`, `index.js.map`, and `index.d.ts`.\n- `npm run coverage` shows the coverage result of `npm test` command with the default browser.\n- `npm run clean` removes the temporary files which are created by `npm test` and `npm run build`.\n- `npm run lint` runs ESLint.\n- `npm run update-fixtures` updates files in `test/fixtures/ast` directory based on `test/fixtures/ast/*/source.vue` files.\n- `npm run watch` runs `build`, `update-fixtures`, and tests with `--watch` option.\n", "time": {"created": "2022-01-26T15:09:11.591Z", "modified": "2025-07-07T03:36:11.950Z", "8.0.1": "2021-10-30T04:01:40.387Z", "8.0.0": "2021-10-19T00:21:19.395Z", "7.11.0": "2021-09-05T23:01:27.459Z", "7.10.0": "2021-07-28T06:09:41.370Z", "7.9.0": "2021-07-18T00:41:59.145Z", "7.8.0": "2021-07-06T10:25:43.089Z", "7.7.2": "2021-07-02T10:33:08.648Z", "7.7.1": "2021-07-02T02:42:24.689Z", "7.7.0": "2021-07-02T00:12:45.477Z", "7.6.0": "2021-03-02T10:05:23.567Z", "7.5.0": "2021-02-14T00:37:25.968Z", "7.4.1": "2021-01-21T12:18:56.716Z", "7.4.0": "2021-01-21T11:07:43.666Z", "7.3.0": "2020-12-14T13:09:25.824Z", "7.2.0": "2020-12-03T03:04:07.228Z", "7.1.1": "2020-10-12T10:35:31.208Z", "7.1.0": "2020-05-15T10:40:00.538Z", "7.0.0": "2019-11-09T13:31:37.547Z", "6.0.5": "2019-11-09T11:09:57.677Z", "6.0.4": "2019-04-21T08:33:14.456Z", "6.0.3": "2019-02-28T08:55:58.438Z", "6.0.2": "2019-02-10T08:05:59.574Z", "6.0.1": "2019-02-09T17:11:38.068Z", "6.0.0": "2019-02-07T22:01:56.534Z", "6.0.0-beta.0": "2019-02-07T20:47:39.261Z", "5.0.0": "2019-01-04T17:17:39.365Z", "4.0.3": "2018-12-05T19:00:10.369Z", "4.0.2": "2018-12-01T10:41:50.658Z", "4.0.1": "2018-12-01T09:36:14.361Z", "4.0.0": "2018-12-01T08:54:41.895Z", "3.3.0": "2018-11-24T06:47:44.095Z", "3.2.2": "2018-07-25T07:09:13.003Z", "3.2.1": "2018-07-24T08:12:12.778Z", "3.2.0": "2018-07-24T06:23:20.558Z", "3.1.1": "2018-07-20T07:38:49.531Z", "3.1.0": "2018-07-09T10:09:55.846Z", "3.0.0": "2018-07-03T05:28:12.145Z", "2.0.3": "2018-02-17T09:51:15.903Z", "2.0.2": "2018-01-07T12:05:28.949Z", "2.0.1": "2017-12-31T04:06:24.376Z", "2.0.1-beta.3": "2017-12-25T10:10:40.909Z", "2.0.1-beta.2": "2017-11-07T06:54:15.409Z", "2.0.1-beta.1": "2017-09-07T10:22:37.833Z", "2.0.1-beta.0": "2017-09-01T13:31:58.570Z", "2.0.0-beta.10": "2017-08-19T06:48:55.929Z", "2.0.0-beta.9": "2017-08-19T06:00:31.116Z", "2.0.0-beta.8": "2017-08-17T23:16:16.336Z", "2.0.0-beta.7": "2017-08-08T08:37:55.309Z", "2.0.0-beta.6": "2017-08-07T03:57:59.917Z", "2.0.0-beta.5": "2017-08-04T10:52:28.757Z", "2.0.0-beta.4": "2017-08-03T21:47:38.540Z", "2.0.0-beta.3": "2017-08-02T22:25:18.188Z", "2.0.0-beta.2": "2017-07-31T12:44:45.040Z", "2.0.0-beta.0": "2017-07-30T07:57:07.917Z", "1.1.0-9": "2017-07-30T06:08:56.564Z", "1.1.0-8": "2017-07-30T02:18:37.036Z", "1.1.0-7": "2017-06-27T10:08:08.319Z", "1.1.0-6": "2017-06-18T11:16:56.605Z", "1.0.0": "2016-12-29T14:32:38.569Z", "0.2.0": "2016-12-22T04:13:30.894Z", "0.1.4": "2016-12-19T01:51:22.490Z", "0.1.3": "2016-12-17T12:31:26.936Z", "0.1.2": "2016-12-17T06:10:51.114Z", "0.1.1": "2016-12-16T04:21:25.017Z", "0.1.0": "2016-12-15T04:09:42.084Z", "8.1.0": "2022-01-21T00:51:43.655Z", "8.2.0": "2022-01-24T05:30:49.555Z", "8.3.0": "2022-02-22T08:51:28.420Z", "9.0.0-alpha.0": "2022-04-14T08:53:13.515Z", "9.0.0": "2022-05-11T23:31:29.063Z", "9.0.1": "2022-05-11T23:39:48.529Z", "9.0.2": "2022-05-17T10:11:47.214Z", "9.0.3": "2022-06-23T07:43:39.367Z", "9.1.0": "2022-09-09T03:18:07.353Z", "9.1.1": "2023-03-30T06:39:34.986Z", "9.2.0": "2023-05-03T00:36:36.287Z", "9.2.1": "2023-05-07T00:01:06.400Z", "9.3.0": "2023-05-14T01:07:56.324Z", "9.3.1": "2023-06-11T08:44:57.926Z", "9.3.2": "2023-10-08T12:36:55.038Z", "9.4.0": "2024-01-08T14:33:33.964Z", "9.4.1": "2024-01-22T07:37:02.795Z", "9.4.2": "2024-01-22T23:29:20.055Z", "9.4.3": "2024-06-01T01:12:13.486Z", "10.0.0": "2025-03-05T03:57:05.621Z", "10.1.0": "2025-03-05T07:31:28.216Z", "10.1.1": "2025-03-05T13:59:26.369Z", "10.1.2": "2025-04-04T01:03:59.958Z", "10.1.3": "2025-04-05T03:15:45.498Z", "10.1.4": "2025-06-24T01:29:57.192Z", "10.2.0": "2025-07-01T11:56:53.814Z"}, "versions": {"8.0.1": {"name": "vue-eslint-parser", "version": "8.0.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.2", "eslint-scope": "^6.0.0", "eslint-visitor-keys": "^3.0.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-syntax-decorators": "^7.14.5", "@babel/plugin-syntax-pipeline-operator": "^7.15.0", "@babel/plugin-syntax-typescript": "^7.14.5", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^5.0.0-0", "@typescript-eslint/parser": "^5.0.0-0", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^8.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.5.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "a16cdbdbbd57984c8b3386b8d7dc46e2e3b205e9", "_id": "vue-eslint-parser@8.0.1", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "25e08b20a414551531f3e19f999902e1ecf45f13", "size": 172782, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.0.1.tgz", "integrity": "sha512-lhWjDXJhe3UZw2uu3ztX51SJAPGPey1Tff2RK3TyZURwbuI4vximQLzz4nQfCv8CZq4xx7uIiogHMMoSJPr33A=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_8.0.1_1635566500162_0.9273348287568015"}, "_hasShrinkwrap": false, "publish_time": 1635566500387, "_cnpm_publish_time": 1635566500387, "_cnpmcore_publish_time": "2021-12-16T10:37:17.232Z"}, "8.0.0": {"name": "vue-eslint-parser", "version": "8.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.2", "eslint-scope": "^6.0.0", "eslint-visitor-keys": "^3.0.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-syntax-decorators": "^7.14.5", "@babel/plugin-syntax-pipeline-operator": "^7.15.0", "@babel/plugin-syntax-typescript": "^7.14.5", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^5.0.0-0", "@typescript-eslint/parser": "^5.0.0-0", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^8.0.0-0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.5.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "db2d94511091826c63676440c9ae5e7059381eae", "_id": "vue-eslint-parser@8.0.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "d77fe0f47a378a7022d3d10c44d5c3df158bd27a", "size": 172539, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.0.0.tgz", "integrity": "sha512-YxN5bkPDji+XLQ4sx+ULLxckL+y/oS3xzgFRkcjJL2asfVcRhzbrNRwMtWgj/70fXsrr+hkFjkxze8PBZ5O3ug=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_8.0.0_1634602879219_0.9166192005373093"}, "_hasShrinkwrap": false, "publish_time": 1634602879395, "_cnpm_publish_time": 1634602879395, "_cnpmcore_publish_time": "2021-12-16T10:37:18.250Z"}, "7.11.0": {"name": "vue-eslint-parser", "version": "7.11.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^6.3.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-syntax-decorators": "^7.14.5", "@babel/plugin-syntax-pipeline-operator": "^7.15.0", "@babel/plugin-syntax-typescript": "^7.14.5", "@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "eslint-plugin-jsonc": "^1.4.0", "eslint-plugin-node-dependencies": "^0.5.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "b5a5af9dd33f828231a62bb033cfdd0c256bea76", "_id": "vue-eslint-parser@7.11.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "214b5dea961007fcffb2ee65b8912307628d0daf", "size": 172477, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.11.0.tgz", "integrity": "sha512-qh3VhDLeh773wjgNTl7ss0VejY9bMMa0GoDG2fQVyDzRFdiU3L7fw74tWZDHNQXdZqxO3EveQroa9ct39D2nqg=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.11.0_1630882887238_0.30795652714321964"}, "_hasShrinkwrap": false, "publish_time": 1630882887459, "_cnpm_publish_time": 1630882887459, "_cnpmcore_publish_time": "2021-12-16T10:37:18.807Z"}, "7.10.0": {"name": "vue-eslint-parser", "version": "7.10.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^6.3.0"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --inspect --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "42e4f923f7bc2527f73fb4a566cf7f3ee5ed2e0e", "_id": "vue-eslint-parser@7.10.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "ea4e4b10fd10aa35c8a79ac783488d8abcd29be8", "size": 171899, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.10.0.tgz", "integrity": "sha512-7tc/ewS9Vq9Bn741pvpg8op2fWJPH3k32aL+jcIcWGCTzh/zXSdh7pZ5FV3W2aJancP9+ftPAv292zY5T5IPCg=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.10.0_1627452581199_0.23213585722878172"}, "_hasShrinkwrap": false, "publish_time": 1627452581370, "_cnpm_publish_time": 1627452581370, "_cnpmcore_publish_time": "2021-12-16T10:37:19.345Z"}, "7.9.0": {"name": "vue-eslint-parser", "version": "7.9.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^6.3.0"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --inspect --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "02b6d084a9d08e1fd61a4aed039b80d50ee05111", "_id": "vue-eslint-parser@7.9.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "5eeedc71f22ebc7b18b957d1ab171acf29a41e64", "size": 170693, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.9.0.tgz", "integrity": "sha512-QBlhZ5LteDRVy2dISfQhNEmmcqph+GTaD4SH41bYzXcVHFPJ9p34zCG6QAqOZVa8PKaVgbomFnoZpGJRZi14vg=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.9.0_1626568918958_0.8992153106786782"}, "_hasShrinkwrap": false, "publish_time": 1626568919145, "_cnpm_publish_time": 1626568919145, "_cnpmcore_publish_time": "2021-12-16T10:37:19.830Z"}, "7.8.0": {"name": "vue-eslint-parser", "version": "7.8.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^6.3.0"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --inspect --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "c36717c65fc90f015c0bc310f6a7b91dcf7577f7", "_id": "vue-eslint-parser@7.8.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "43850bf856c9a69d62c0e12769609c338423684b", "size": 163567, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.8.0.tgz", "integrity": "sha512-eh<PERSON>rLZNYLUoKayvVW8l8HyPQIfuYZHiJoQLRP3dapDlTU7bGs4tqIKVGdAEpMuXS/b4R/PImCt7Tkj4UhX1SQ=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.8.0_1625567142904_0.9261762146299792"}, "_hasShrinkwrap": false, "publish_time": 1625567143089, "_cnpm_publish_time": 1625567143089, "_cnpmcore_publish_time": "2021-12-16T10:37:20.865Z"}, "7.7.2": {"name": "vue-eslint-parser", "version": "7.7.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^6.3.0"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "4d6e4c1a14a28f463878913fb75674b6d70c1d6c", "_id": "vue-eslint-parser@7.7.2", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "a723080b29c27fa0b3737bedceaeebe30fd0f359", "size": 156215, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.7.2.tgz", "integrity": "sha512-zkfxSttpwBW9SQEa+rLR+j6sFHGGhanVH3VuzHQwybCQWJsg/Yi1W619gXOW01U/zekN4D+J4/S4Zufd1sClZg=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.7.2_1625221988432_0.2489410422333016"}, "_hasShrinkwrap": false, "publish_time": 1625221988648, "_cnpm_publish_time": 1625221988648, "_cnpmcore_publish_time": "2021-12-16T10:37:21.776Z"}, "7.7.1": {"name": "vue-eslint-parser", "version": "7.7.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.1.1", "eslint-visitor-keys": "^1.1.0", "espree": "^8.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.3.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "3908b3495e941acdd64e180baa1219d73f730e84", "_id": "vue-eslint-parser@7.7.1", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "db018d6cb5c28f5c62329bc2e98433c8d897afdc", "size": 155812, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.7.1.tgz", "integrity": "sha512-rUGUCKH5rSGTvF9QlxYXi6MyoSATcrTbLwiCqFuFRhaSJLlDYBmsXJ08xIZoLdsUl/OfCC1ibnkj7SWHRTrStQ=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.7.1_1625193744535_0.8419128268008049"}, "_hasShrinkwrap": false, "publish_time": 1625193744689, "_cnpm_publish_time": 1625193744689, "_cnpmcore_publish_time": "2021-12-16T10:37:22.314Z"}, "7.7.0": {"name": "vue-eslint-parser", "version": "7.7.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.2.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "semver": "^7.3.4", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "aa55d1c5d732e6ec8f46933306ae52aa2e1c49ab", "_id": "vue-eslint-parser@7.6.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "01ea1a2932f581ff244336565d712801f8f72561", "size": 145025, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.6.0.tgz", "integrity": "sha512-QXxqH8ZevBrtiZMZK0LpwaMfevQi9UL7lY6Kcp+ogWHC88AuwUPwwCIzkOUc1LR4XsYAt/F9yHXAB/QoD17QXA=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.6.0_1614679523342_0.5784162704498463"}, "_hasShrinkwrap": false, "publish_time": 1614679523567, "_cnpm_publish_time": 1614679523567, "_cnpmcore_publish_time": "2021-12-16T10:37:23.545Z", "deprecated": "[WARNING] Use 7.6.0 instead of 7.7.0, reason: https://github.com/vuejs/vue-eslint-parser/issues/112"}, "7.6.0": {"name": "vue-eslint-parser", "version": "7.6.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.2.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "semver": "^7.3.4", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "aa55d1c5d732e6ec8f46933306ae52aa2e1c49ab", "_id": "vue-eslint-parser@7.6.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "01ea1a2932f581ff244336565d712801f8f72561", "size": 145025, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.6.0.tgz", "integrity": "sha512-QXxqH8ZevBrtiZMZK0LpwaMfevQi9UL7lY6Kcp+ogWHC88AuwUPwwCIzkOUc1LR4XsYAt/F9yHXAB/QoD17QXA=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.6.0_1614679523342_0.5784162704498463"}, "_hasShrinkwrap": false, "publish_time": 1614679523567, "_cnpm_publish_time": 1614679523567, "_cnpmcore_publish_time": "2021-12-16T10:37:23.545Z"}, "7.5.0": {"name": "vue-eslint-parser", "version": "7.5.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.4.0", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.2.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "semver": "^7.3.4", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "c6e6aa325ada47a29dc7577b9693cfb48ee1c1cc", "_id": "vue-eslint-parser@7.5.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "b68221c55fee061899afcfb4441ec74c1495285e", "size": 144492, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.5.0.tgz", "integrity": "sha512-6EHzl00hIpy4yWZo3qSbtvtVw1A1cTKOv1w95QSuAqGgk4113XtRjvNIiEGo49r0YWOPYsrmI4Dl64axL5Agrw=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.5.0_1613263045767_0.7687962616251751"}, "_hasShrinkwrap": false, "publish_time": 1613263045968, "_cnpm_publish_time": 1613263045968, "_cnpmcore_publish_time": "2021-12-16T10:37:24.126Z"}, "7.4.1": {"name": "vue-eslint-parser", "version": "7.4.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.14.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.2.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "semver": "^7.3.4", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "57248514ec1e91eb978ef8a057032c74e2e4701e", "_id": "vue-eslint-parser@7.4.1", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "e4adcf7876a7379758d9056a72235af18a587f92", "size": 144283, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.4.1.tgz", "integrity": "sha512-AFvhdxpFvliYq1xt/biNBslTHE/zbEvSnr1qfHA/KxRIpErmEDrQZlQnvEexednRHmLfDNOMuDYwZL5xkLzIXQ=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.4.1_1611231536507_0.0007925246393034602"}, "_hasShrinkwrap": false, "publish_time": 1611231536716, "_cnpm_publish_time": 1611231536716, "_cnpmcore_publish_time": "2021-12-16T10:37:24.755Z"}, "7.4.0": {"name": "vue-eslint-parser", "version": "7.4.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.7.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.2.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "3196668a150c39a88f8ecbf6feb907cf0429b488", "_id": "vue-eslint-parser@7.4.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "174d07c0feb1591bc503ba912e78080d5b5afcf5", "size": 144273, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.4.0.tgz", "integrity": "sha512-eGVI9eMckiYrpYUEZuC3rXNNd8Hlo7i5l3WQ7oANbtSBDTXGuL/v2uCtM6k0+r/bAiZIrcPqgaj2I8wldGqO+Q=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.4.0_1611227263498_0.8846516914704408"}, "_hasShrinkwrap": false, "publish_time": 1611227263666, "_cnpm_publish_time": 1611227263666, "_cnpmcore_publish_time": "2021-12-16T10:37:25.382Z"}, "7.3.0": {"name": "vue-eslint-parser", "version": "7.3.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/eslint": "^7.2.6", "@types/estree": "0.0.45", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.7.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "jsonc-eslint-parser": "^0.6.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "prettier": "^2.2.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "17507a405709b4dc15e9a53de7baa522d382923e", "_id": "vue-eslint-parser@7.3.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "894085839d99d81296fa081d19643733f23d7559", "size": 141616, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.3.0.tgz", "integrity": "sha512-n5PJKZbyspD0+8LnaZgpEvNCrjQx1DyDHw8JdWwoxhhC+yRip4TAvSDpXGf9SWX6b0umeB5aR61gwUo6NVvFxw=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.3.0_1607951365643_0.3109478079173933"}, "_hasShrinkwrap": false, "publish_time": 1607951365824, "_cnpm_publish_time": 1607951365824, "_cnpmcore_publish_time": "2021-12-16T10:37:26.028Z"}, "7.2.0": {"name": "vue-eslint-parser", "version": "7.2.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^4.7.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~4.0.5", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "test:debug": "mocha --inspect --require ts-node/register \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "0041efa24834df0faa11cd6829aafe7430e8fb98", "_id": "vue-eslint-parser@7.2.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "1e17ae94ca71e617025e05143c8ac5593aacb6ef", "size": 132556, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.2.0.tgz", "integrity": "sha512-uVcQqe8sUNzdHGcRHMd2Z/hl6qEaWrAmglTKP92Fnq9TYU9un8xsyFgEdFJaXh/1rd7h8Aic1GaiQow5nVneow=="}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.2.0_1606964647065_0.569323354091583"}, "_hasShrinkwrap": false, "publish_time": 1606964647228, "_cnpm_publish_time": 1606964647228, "_cnpmcore_publish_time": "2021-12-16T10:37:26.616Z"}, "7.1.1": {"name": "vue-eslint-parser", "version": "7.1.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^11.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^2.31.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~3.4.4", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "node -e \"if(process.env.ESLINT=='5')process.exit(1)\" && eslint src test --ext .js,.ts || node -e \"if(process.env.ESLINT!='5')process.exit(1)\"", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "f7d69af9ba51f831008a863877458d6297a6dc68", "_id": "vue-eslint-parser@7.1.1", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c43c1c715ff50778b9a7e9a4e16921185f3425d3", "size": 130675, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.1.1.tgz", "integrity": "sha512-8FdXi0gieEwh1IprIBafpiJWcApwrU+l2FEj8c1HtHFdNXMd0+2jUSjBVmcQYohf/E72irwAXEXLga6TQcB3FA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.1.1_1602498930959_0.7635784557040723"}, "_hasShrinkwrap": false, "publish_time": 1602498931208, "_cnpm_publish_time": 1602498931208, "_cnpmcore_publish_time": "2021-12-16T10:37:27.163Z"}, "7.1.0": {"name": "vue-eslint-parser", "version": "7.1.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^11.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^2.31.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^7.0.0", "fs-extra": "^7.0.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~3.4.4", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "node -e \"if(process.env.ESLINT=='5')process.exit(1)\" && eslint src test --ext .js,.ts || node -e \"if(process.env.ESLINT!='5')process.exit(1)\"", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "c627e36c57ab6607c96f995e81b8ce693ac846ef", "_id": "vue-eslint-parser@7.1.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.14.4", "dist": {"shasum": "9cdbcc823e656b087507a1911732b867ac101e83", "size": 130319, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.1.0.tgz", "integrity": "sha512-Kr21uPfthDc63nDl27AGQEhtt9VrZ9nkYk/NTftJ2ws9XiJwzJJCnCr3AITQ2jpRMA0XPGDECxYH8E027qMK9Q=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.1.0_1589539200404_0.04232663017449778"}, "_hasShrinkwrap": false, "publish_time": 1589539200538, "_cnpm_publish_time": 1589539200538, "_cnpmcore_publish_time": "2021-12-16T10:37:27.757Z"}, "7.0.0": {"name": "vue-eslint-parser", "version": "7.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=8.10"}, "main": "index.js", "peerDependencies": {"eslint": ">=5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.1.2", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "devDependencies": {"@mysticatea/eslint-plugin": "^11.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^6.1.0", "fs-extra": "^7.0.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~3.4.4", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "node -e \"if(process.env.ESLINT=='5')process.exit(1)\" && eslint src test --ext .js,.ts || node -e \"if(process.env.ESLINT!='5')process.exit(1)\"", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "84dac95f64fbe1ae18c5848d8ab287fa00260dfe", "_id": "vue-eslint-parser@7.0.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.1", "dist": {"shasum": "a4ed2669f87179dedd06afdd8736acbb3a3864d6", "size": 129219, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-7.0.0.tgz", "integrity": "sha512-yR0dLxsTT7JfD2YQo9BhnQ6bUTLsZouuzt9SKRP7XNaZJV459gvlsJo4vT2nhZ/2dH9j3c53bIx9dnqU2prM9g=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_7.0.0_1573306297337_0.09901807932312412"}, "_hasShrinkwrap": false, "publish_time": 1573306297547, "_cnpm_publish_time": 1573306297547, "_cnpmcore_publish_time": "2021-12-16T10:37:28.298Z"}, "6.0.5": {"name": "vue-eslint-parser", "version": "6.0.5", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0 || ^6.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~3.4.4", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "cce993af9cec648cfe7550b65d0cc2d44b9e59cc", "_id": "vue-eslint-parser@6.0.5", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.1", "dist": {"shasum": "c1c067c2755748e28f3872cd42e8c1c4c1a8059f", "size": 128859, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.5.tgz", "integrity": "sha512-Bvjlx7rH1Ulvus56KHeLXOjEi3JMOYTa1GAqZr9lBQhd8weK8mV7U7V2l85yokBZEWHJQjLn6X3nosY8TzkOKg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.5_1573297797527_0.4720154977751181"}, "_hasShrinkwrap": false, "publish_time": 1573297797677, "_cnpm_publish_time": 1573297797677, "_cnpmcore_publish_time": "2021-12-16T10:37:28.902Z"}, "6.0.4": {"name": "vue-eslint-parser", "version": "6.0.4", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.1.0", "typescript": "~3.4.4", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "9868ae4017345d59d0f560e6a42507f25d04543a", "_id": "vue-eslint-parser@6.0.4", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "56ff47e2c2644bff39951d5a284982c7ecd6f7fa", "size": 128630, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.4.tgz", "integrity": "sha512-GYsDsDWwKaGtnkW4nGUxr01wqIO2FB9/QHQTW1Gl5SUr5OyQvpnR90/D+Gq2cIxURX7aJ7+VyD+37Yx9eFwTgw=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.4_1555835594339_0.35427961243657036"}, "_hasShrinkwrap": false, "publish_time": 1555835594456, "_cnpm_publish_time": 1555835594456, "_cnpmcore_publish_time": "2021-12-16T10:37:29.453Z"}, "6.0.3": {"name": "vue-eslint-parser", "version": "6.0.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.2.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.3.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "a05d416440974fc7ab5622c58a561242d1325038", "_id": "vue-eslint-parser@6.0.3", "_nodeVersion": "11.9.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "e8b4748a8c63c72889341a8a2e48c33e58d1c9d3", "size": 128597, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.3.tgz", "integrity": "sha512-f2jZQojTLyZmuDLhCybJ8qIicfZfC6PmO7dEubZ0U/0tQ4dweRFuB0RpbbaHeMUfVFSYiKexbiemkze/h3/6SA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.3_1551344158305_0.8660003197277968"}, "_hasShrinkwrap": false, "publish_time": 1551344158438, "_cnpm_publish_time": 1551344158438, "_cnpmcore_publish_time": "2021-12-16T10:37:29.982Z"}, "6.0.2": {"name": "vue-eslint-parser", "version": "6.0.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.2.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.3.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "47c0c1af92f64e96ac33795a54e93a8b4ed6f0b7", "_id": "vue-eslint-parser@6.0.2", "_nodeVersion": "11.9.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "0fa8d960bfbfb76582ad029b4a9908816ae5d76f", "size": 128247, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.2.tgz", "integrity": "sha512-BxDDlPIYFYOrxs4WMvGxGj0Ve83iWYPydaqEzoVz0tVgzDXdS98FAZEX/Kd7OiHgXy1hR4nDnHHZ5sGIHMfSHw=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.2_1549785959365_0.953867620593625"}, "_hasShrinkwrap": false, "publish_time": 1549785959574, "_cnpm_publish_time": 1549785959574, "_cnpmcore_publish_time": "2021-12-16T10:37:30.422Z"}, "6.0.1": {"name": "vue-eslint-parser", "version": "6.0.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.2.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.3.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "fb08ee11b732a4264b6b626cceef111750ebc704", "_id": "vue-eslint-parser@6.0.1", "_nodeVersion": "11.9.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "86057ca8c3ef4e6c581432b10242b027423330cb", "size": 128143, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.1.tgz", "integrity": "sha512-F+IFOzsiPXwq6z3y3TykHISIM9aATWzlGHSWGsmL92AmeOKg3aB92EbrTpwB1iYfRmm1DlG3aNSWn2oR5M+kSg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.1_1549732297928_0.6096824565987444"}, "_hasShrinkwrap": false, "publish_time": 1549732298068, "_cnpm_publish_time": 1549732298068, "_cnpmcore_publish_time": "2021-12-16T10:37:31.029Z"}, "6.0.0": {"name": "vue-eslint-parser", "version": "6.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.2.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.3.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "28729dde1c1e088b3d0344a95b535210a7fd33a4", "_id": "vue-eslint-parser@6.0.0", "_nodeVersion": "11.9.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "05928d34868d27969242cc614e250c99ce5c0a0d", "size": 128133, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.0.tgz", "integrity": "sha512-QVGE3/bTulg8ybrkdRgqcLbqNeB26iFucn2hYfDqcresBJHVnvUdAM8klydHoWztz48oGSax5bseR6lzWMeRQA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.0_1549576916450_0.962349028425445"}, "_hasShrinkwrap": false, "publish_time": 1549576916534, "_cnpm_publish_time": 1549576916534, "_cnpmcore_publish_time": "2021-12-16T10:37:31.545Z"}, "6.0.0-beta.0": {"name": "vue-eslint-parser", "version": "6.0.0-beta.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.1", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^9.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.120", "@types/mocha": "^5.2.4", "@types/node": "^10.12.21", "@typescript-eslint/parser": "^1.2.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.13.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.2.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.3.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "readmeFilename": "README.md", "gitHead": "3c356d974b4fb49f1f8371282707b40841abe4a7", "_id": "vue-eslint-parser@6.0.0-beta.0", "_nodeVersion": "11.9.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "ba4a94b864b653decd744d73bfaf76bac1f0c29d", "size": 128142, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-6.0.0-beta.0.tgz", "integrity": "sha512-GDCtgm55qRwSK3CdBi1x1EfJMjzVRo92w0Hhj4bKeoFsj7PtqrbZ1azoWKGfsbRQbSXpcwfwkf/nneNoL3EGFw=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_6.0.0-beta.0_1549572459129_0.8174627482877412"}, "_hasShrinkwrap": false, "publish_time": 1549572459261, "_cnpm_publish_time": 1549572459261, "_cnpmcore_publish_time": "2021-12-16T10:37:32.034Z"}, "5.0.0": {"name": "vue-eslint-parser", "version": "5.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.1.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^7.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.118", "@types/mocha": "^5.2.4", "@types/node": "^10.12.9", "acorn": "^6.0.4", "acorn-jsx": "^5.0.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.9.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.67.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.1.6", "typescript-eslint-parser": "^21.0.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "09b4d1e287bb0ed3959afb835a0488873e0363a7", "_id": "vue-eslint-parser@5.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "00f4e4da94ec974b821a26ff0ed0f7a78402b8a1", "size": 124999, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-5.0.0.tgz", "integrity": "sha512-JlHVZwBBTNVvzmifwjpZYn0oPWH2SgWv5dojlZBsrhablDu95VFD+hriB1rQGwbD+bms6g+rAFhQHk6+NyiS6g=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_5.0.0_1546622259188_0.26600979736455144"}, "_hasShrinkwrap": false, "publish_time": 1546622259365, "_cnpm_publish_time": 1546622259365, "_cnpmcore_publish_time": "2021-12-16T10:37:32.624Z"}, "4.0.3": {"name": "vue-eslint-parser", "version": "4.0.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.1.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^7.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.118", "@types/mocha": "^5.2.4", "@types/node": "^10.12.9", "acorn": "^6.0.4", "acorn-jsx": "^5.0.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.9.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.67.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.1.6", "typescript-eslint-parser": "^21.0.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "b59976461d4f3c6f9e63a87c6c600da105dd4c7f", "_id": "vue-eslint-parser@4.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "80cf162e484387b2640371ad21ba1f86e0c10a61", "size": 122515, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-4.0.3.tgz", "integrity": "sha512-AUeQsYdO6+7QXCems+WvGlrXd37PHv/zcRQSQdY1xdOMwdFAPEnMBsv7zPvk0TPGulXkK/5p/ITgrjiYB7k3ag=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_4.0.3_1544036410256_0.6698835967816084"}, "_hasShrinkwrap": false, "publish_time": 1544036410369, "_cnpm_publish_time": 1544036410369, "_cnpmcore_publish_time": "2021-12-16T10:37:32.992Z"}, "4.0.2": {"name": "vue-eslint-parser", "version": "4.0.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.1.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^7.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.118", "@types/mocha": "^5.2.4", "@types/node": "^10.12.9", "acorn": "^6.0.4", "acorn-jsx": "^5.0.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.9.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.67.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.1.6", "typescript-eslint-parser": "^21.0.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "0248739f6b137298742e071a096583ef0df7670c", "_id": "vue-eslint-parser@4.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "7d10ec5b67d9b2ef240cac0f0e8b2f03773d810e", "size": 122361, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-4.0.2.tgz", "integrity": "sha512-A+teFdsAVtVawd4rtLsv8q6uuj2MYt3Uw+GCodqEkjozB3g20G91hbukz60yWa9IUx/yFz+JzKBu/3Djkod36g=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_4.0.2_1543660910549_0.8330933810492462"}, "_hasShrinkwrap": false, "publish_time": 1543660910658, "_cnpm_publish_time": 1543660910658, "_cnpmcore_publish_time": "2021-12-16T10:37:33.571Z"}, "4.0.1": {"name": "vue-eslint-parser", "version": "4.0.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.1.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^7.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.118", "@types/mocha": "^5.2.4", "@types/node": "^10.12.9", "acorn": "^6.0.4", "acorn-jsx": "^5.0.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.9.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.67.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.1.6", "typescript-eslint-parser": "^21.0.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "a6135f89632d48601d453856c343275a64d525be", "_id": "vue-eslint-parser@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "293a01855560888bb6c8ddbd9e7149139358d708", "size": 122388, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-4.0.1.tgz", "integrity": "sha512-bc4LgG9dYGTOuPJul9NgxNOfxGZEd9q1GOct9MaeV9bCbo2OKihnV5j4zaJyReLbQLi7glmGvZHPHuvoM6DOBA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_4.0.1_1543656974216_0.18001052086638047"}, "_hasShrinkwrap": false, "publish_time": 1543656974361, "_cnpm_publish_time": 1543656974361, "_cnpmcore_publish_time": "2021-12-16T10:37:33.977Z"}, "4.0.0": {"name": "vue-eslint-parser", "version": "4.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.1.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^7.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.118", "@types/mocha": "^5.2.4", "@types/node": "^10.12.9", "acorn": "^6.0.4", "acorn-jsx": "^5.0.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.9.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.67.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.1.6", "typescript-eslint-parser": "^21.0.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "4cea14175e0d7545107a15664b19f5cdc5474065", "_id": "vue-eslint-parser@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "d94e693f20efae43086d859ddcd65d06dd5afb98", "size": 122385, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-4.0.0.tgz", "integrity": "sha512-EppTxlbh4PefuYjyu+ayk/iW3Sy5sPf/Rj8IBnRhpdlYTDnyL4NDfvk+ivumzYxvQaESFqg6/o+NTYy395IwUg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_4.0.0_1543654481754_0.18487865985968122"}, "_hasShrinkwrap": false, "publish_time": 1543654481895, "_cnpm_publish_time": 1543654481895, "_cnpmcore_publish_time": "2021-12-16T10:37:34.571Z"}, "3.3.0": {"name": "vue-eslint-parser", "version": "3.3.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^4.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.1.0", "esquery": "^1.0.1", "lodash": "^4.17.11"}, "devDependencies": {"@mysticatea/eslint-plugin": "^7.0.0", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.118", "@types/mocha": "^5.2.4", "@types/node": "^10.12.9", "acorn": "^6.0.4", "acorn-jsx": "^5.0.0", "babel-eslint": "^10.0.1", "chokidar": "^2.0.4", "codecov": "^3.1.0", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.9.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.67.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~3.1.6", "typescript-eslint-parser": "^21.0.1", "wait-on": "^3.2.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "59d52edb9d01989713f06c82618ce5c2b74a5646", "_id": "vue-eslint-parser@3.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "06b195d18bb66ac72c6b7f2469b549109a61d72c", "size": 117804, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.3.0.tgz", "integrity": "sha512-gUsSihfwXmSIbxtqq8YT9CBdkqTHj+6ahj+glY6vJSYu0ylMHQ1A9ClC1YkF5YLRs+WShAwJklXfiL8CEZhgog=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.3.0_1543042063934_0.0758401160271478"}, "_hasShrinkwrap": false, "publish_time": 1543042064095, "_cnpm_publish_time": 1543042064095, "_cnpmcore_publish_time": "2021-12-16T10:37:35.214Z"}, "3.2.2": {"name": "vue-eslint-parser", "version": "3.2.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "lodash": "^4.17.10"}, "devDependencies": {"@mysticatea/eslint-plugin": "^5.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.110", "@types/mocha": "^5.2.4", "@types/node": "^6.0.113", "babel-eslint": "^8.2.5", "chokidar": "^2.0.4", "codecov": "^3.0.2", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.1", "wait-on": "^2.1.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "da877ec63b2314e6ee0e9f7df9b4e4aa3b359bc5", "_id": "vue-eslint-parser@3.2.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "47c971ee4c39b0ee7d7f5e154cb621beb22f7a34", "size": 118586, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.2.2.tgz", "integrity": "sha512-dprI6ggKCTwV22r+i8dtUGquiOCn063xyDmb7BV/BjG5Oc/m5EoMNrWevpvTcrlGuFZmYVPs5fgsu8UIxmMKzg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.2.2_1532502552929_0.5897785699490634"}, "_hasShrinkwrap": false, "publish_time": 1532502553003, "_cnpm_publish_time": 1532502553003, "_cnpmcore_publish_time": "2021-12-16T10:37:35.826Z"}, "3.2.1": {"name": "vue-eslint-parser", "version": "3.2.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "lodash": "^4.17.10"}, "devDependencies": {"@mysticatea/eslint-plugin": "^5.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.110", "@types/mocha": "^5.2.4", "@types/node": "^6.0.113", "babel-eslint": "^8.2.5", "chokidar": "^2.0.4", "codecov": "^3.0.2", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.1", "wait-on": "^2.1.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "a6156b8c87395ecda8100a670e1135833e417ddb", "_id": "vue-eslint-parser@3.2.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "b21a138a2347b61a04bad9926a2dd4255b48718a", "size": 118585, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.2.1.tgz", "integrity": "sha512-itB0sUobbpHG0bP5vSX7buBxDpars3IbAjHLHH+bQRLAqVcfyIb9JBOVhHQkqXUVeGAg+WLlyCmsBKCKAYgMlA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.2.1_1532419932624_0.3589801430148616"}, "_hasShrinkwrap": false, "publish_time": 1532419932778, "_cnpm_publish_time": 1532419932778, "_cnpmcore_publish_time": "2021-12-16T10:37:36.506Z"}, "3.2.0": {"name": "vue-eslint-parser", "version": "3.2.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "lodash": "^4.17.10"}, "devDependencies": {"@mysticatea/eslint-plugin": "^5.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.110", "@types/mocha": "^5.2.4", "@types/node": "^6.0.113", "babel-eslint": "^8.2.5", "chokidar": "^2.0.4", "codecov": "^3.0.2", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.1", "wait-on": "^2.1.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "51a4bab0d688a7fe2bf774ec89d9a758f212dcad", "_id": "vue-eslint-parser@3.2.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "25260b71e166f637134d078e5414b79e4de1d0f5", "size": 118315, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.2.0.tgz", "integrity": "sha512-5YePd3tc3nfqf3dRqDf4H8YKjT868LmP5RN8PfhGSIBZL6lsZZcqZ4/87Vh82ihcH3qk5hVDtDRHFs883lGmyA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.2.0_**********372_0.28089341556399217"}, "_hasShrinkwrap": false, "publish_time": **********558, "_cnpm_publish_time": **********558, "_cnpmcore_publish_time": "2021-12-16T10:37:37.245Z"}, "3.1.1": {"name": "vue-eslint-parser", "version": "3.1.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "lodash": "^4.17.10"}, "devDependencies": {"@mysticatea/eslint-plugin": "^5.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.110", "@types/mocha": "^5.2.4", "@types/node": "^6.0.113", "babel-eslint": "^8.2.5", "chokidar": "^2.0.4", "codecov": "^3.0.2", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.1", "wait-on": "^2.1.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "e564707373bf4af0543736346606092ff46fe6b9", "_id": "vue-eslint-parser@3.1.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "162050c101a4f46661141992f6ceb3010ab7d68a", "size": 117685, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.1.1.tgz", "integrity": "sha512-niAjtuJrEGXFHmDBkpLTHQxp1fGN1I8T075Jz8VQEVxBMvoaGS8sKCZKIAwf0NKaLu6FERmrR4VEUCEp0m/z9Q=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.1.1_1532072328823_0.24579593618810391"}, "_hasShrinkwrap": false, "publish_time": 1532072329531, "_cnpm_publish_time": 1532072329531, "_cnpmcore_publish_time": "2021-12-16T10:37:37.962Z"}, "3.1.0": {"name": "vue-eslint-parser", "version": "3.1.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "lodash": "^4.17.10"}, "devDependencies": {"@mysticatea/eslint-plugin": "^5.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.110", "@types/mocha": "^5.2.4", "@types/node": "^6.0.113", "babel-eslint": "^8.2.5", "chokidar": "^2.0.4", "codecov": "^3.0.2", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.1", "wait-on": "^2.1.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "12079c4bd962f38d1d8abc816a59e93ebbf57733", "_id": "vue-eslint-parser@3.1.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "9c8d7239cac950f62885afdf3f1e1d5c85f3b5a1", "size": 117661, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.1.0.tgz", "integrity": "sha512-vkFlFH3QSeKqeLOBFuMYlfuqAPqNEoDJn8Rwbim8z5JJ758oJxmi06AtPx59nvpUgpkEqCkqYbZ50bbByBtf0w=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.1.0_1531130995757_0.6178214606346111"}, "_hasShrinkwrap": false, "publish_time": 1531130995846, "_cnpm_publish_time": 1531130995846, "_cnpmcore_publish_time": "2021-12-16T10:37:38.478Z"}, "3.0.0": {"name": "vue-eslint-parser", "version": "3.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=6.5"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": "^5.0.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^4.0.0", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "lodash": "^4.17.10"}, "devDependencies": {"@mysticatea/eslint-plugin": "^5.0.1", "@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.110", "@types/mocha": "^5.2.4", "@types/node": "^6.0.113", "babel-eslint": "^8.2.5", "chokidar": "^2.0.4", "codecov": "^3.0.2", "cross-spawn": "^6.0.5", "dts-bundle": "^0.7.3", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.1", "wait-on": "^2.1.0", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "7a9bde94f0403a52c31ff9eeb728ae918b37a12d", "_id": "vue-eslint-parser@3.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "e9bd9339c1595651fb045ef56ae8bd73556c93dc", "size": 117493, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-3.0.0.tgz", "integrity": "sha512-xQhJwS1Fmfa3asMhn4Pg58YteG0ebVFWMIycrBPgQuDRg+JcIh2h7KagoNDDuwoDlaMpZ1pn85v6jFGhV0QfWQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_3.0.0_1530595692087_0.5046136339627325"}, "_hasShrinkwrap": false, "publish_time": 1530595692145, "_cnpm_publish_time": 1530595692145, "_cnpmcore_publish_time": "2021-12-16T10:37:38.878Z"}, "2.0.3": {"name": "vue-eslint-parser", "version": "2.0.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^3.7.1", "eslint-visitor-keys": "^1.0.0", "espree": "^3.5.2", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.91", "@types/mocha": "^2.2.44", "@types/node": "^6.0.85", "babel-eslint": "^8.1.1", "chokidar": "^1.7.0", "codecov": "^3.0.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.14.0", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^5.0.0", "mocha": "^4.0.1", "npm-run-all": "^4.1.2", "nyc": "^11.4.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.53.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.6.2", "typescript-eslint-parser": "^11.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "de3fe7f18feaebdf61dc1c9661d8446b074c41c2", "_id": "vue-eslint-parser@2.0.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "c268c96c6d94cfe3d938a5f7593959b0ca3360d1", "size": 115674, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.3.tgz", "integrity": "sha512-ZezcU71Owm84xVF6gfurBQUGg8WQ+WZGxgDEQu1IHFBZNx7BFZg3L1yHxrCBNNwbwFtE1GuvfJKMtb6Xuwc/Bw=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_2.0.3_1518861075518_0.08373813688075593"}, "_hasShrinkwrap": false, "publish_time": 1518861075903, "_cnpm_publish_time": 1518861075903, "_cnpmcore_publish_time": "2021-12-16T10:37:39.238Z"}, "2.0.2": {"name": "vue-eslint-parser", "version": "2.0.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^3.7.1", "eslint-visitor-keys": "^1.0.0", "espree": "^3.5.2", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.91", "@types/mocha": "^2.2.44", "@types/node": "^6.0.85", "babel-eslint": "^8.1.1", "chokidar": "^1.7.0", "codecov": "^3.0.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.14.0", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^5.0.0", "mocha": "^4.0.1", "npm-run-all": "^4.1.2", "nyc": "^11.4.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.53.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.6.2", "typescript-eslint-parser": "^11.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "6111437a4b470fb63227099a69749a7ef2f022a4", "_id": "vue-eslint-parser@2.0.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "8d603545e9d7c134699075bd1772af1ffd86b744", "size": 115567, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.2.tgz", "integrity": "sha512-MQE1Tl4kYhp51opFMtRcZuyrFru/erpRI82w96tPiSnhcwK3QjJejAEJ5RlLcLU07Ua7A1WvhXG3i2KFveeGsA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.2.tgz_1515326728799_0.07105589751154184"}, "directories": {}, "publish_time": 1515326728949, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515326728949, "_cnpmcore_publish_time": "2021-12-16T10:37:39.864Z"}, "2.0.1": {"name": "vue-eslint-parser", "version": "2.0.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^3.7.1", "eslint-visitor-keys": "^1.0.0", "espree": "^3.5.2", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.91", "@types/mocha": "^2.2.44", "@types/node": "^6.0.85", "babel-eslint": "^8.1.1", "chokidar": "^1.7.0", "codecov": "^3.0.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.14.0", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^5.0.0", "mocha": "^4.0.1", "npm-run-all": "^4.1.2", "nyc": "^11.4.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.53.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.6.2", "typescript-eslint-parser": "^11.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "340b66a2b6c134143ff93da5b35897579b67d2e7", "_id": "vue-eslint-parser@2.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "30135771c4fad00fdbac4542a2d59f3b1d776834", "size": 115626, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.1.tgz", "integrity": "sha512-X0krSmOiAml2+Ie8BwG7G/eGoQhAOVwa3tsqJ1jhzD2yAFKRPfL7tcFa8/04BRwbjzEJtsWIS64sNb4/+uFLbA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.1.tgz_1514693184234_0.8523736961651593"}, "directories": {}, "publish_time": 1514693184376, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514693184376, "_cnpmcore_publish_time": "2021-12-16T10:37:40.315Z"}, "2.0.1-beta.3": {"name": "vue-eslint-parser", "version": "2.0.1-beta.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^3.7.1", "eslint-visitor-keys": "^1.0.0", "espree": "^3.5.2", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.30", "@types/estree": "0.0.38", "@types/lodash": "^4.14.91", "@types/mocha": "^2.2.44", "@types/node": "^6.0.85", "babel-eslint": "^8.1.1", "chokidar": "^1.7.0", "codecov": "^3.0.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.14.0", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^5.0.0", "mocha": "^4.0.1", "npm-run-all": "^4.1.2", "nyc": "^11.4.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.53.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.6.2", "typescript-eslint-parser": "^11.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "22c12d5ba9374792cf25ccd456c96aab7b379b50", "_id": "vue-eslint-parser@2.0.1-beta.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "cf1391d9c277dd72ee7faa2a2171d71aa1b85f70", "size": 115385, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.1-beta.3.tgz", "integrity": "sha512-cI+Hz/x7rH+eEEohz8pKLi6sMacIsCoUimY6a5Bn+QSENRwdjzR9HJD7V6JQHc5GAFdm17jaM4dII0pGhclLbA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.1-beta.3.tgz_1514196640822_0.6812508595176041"}, "directories": {}, "publish_time": 1514196640909, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514196640909, "_cnpmcore_publish_time": "2021-12-16T10:37:40.837Z"}, "2.0.1-beta.2": {"name": "vue-eslint-parser", "version": "2.0.1-beta.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.1.0", "eslint-scope": "^3.7.1", "espree": "^3.5.1", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^8.0.2", "chokidar": "^1.7.0", "codecov": "^3.0.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.10.0", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^4.0.2", "mocha": "^4.0.1", "npm-run-all": "^4.1.1", "nyc": "^11.3.0", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.50.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.5.3", "typescript-eslint-parser": "^8.0.1", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 10000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint src test --ext .ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "492e1ced9c657e719d1c06dd85ea2d499236c9af", "_id": "vue-eslint-parser@2.0.1-beta.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "82e5130ac18ae4b0c894a7c831b55ec92478fa2f", "size": 117025, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.1-beta.2.tgz", "integrity": "sha512-lmm2WjMeK9o344Cevs+w112g3TomjG6OXvh2J0wBzS1iSVk8n6t6ORRcQwGUfv51azWeE24q0+a2HAcN1HVkhA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.1-beta.2.tgz_1510037655312_0.6599745007697493"}, "directories": {}, "publish_time": 1510037655409, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510037655409, "_cnpmcore_publish_time": "2021-12-16T10:37:41.411Z"}, "2.0.1-beta.1": {"name": "vue-eslint-parser", "version": "2.0.1-beta.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.0.0", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.4.2", "typescript-eslint-parser": "^5.0.1", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "f704031501946ca21bb15b5064df897aa9259d5c", "_id": "vue-eslint-parser@2.0.1-beta.1", "_npmVersion": "5.4.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "7e1b3c0865905264605169497fe9e42d27c1ae60", "size": 116877, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.1-beta.1.tgz", "integrity": "sha512-VjbDnSaHM/bFafktgIadCC7AyFXEv3ImBfLYhGZe90TPQVOWLs6xijCcvaADofiFqCnJom6fftAYufdBwr6KVw=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.1-beta.1.tgz_1504779757725_0.37825552583672106"}, "directories": {}, "publish_time": 1504779757833, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504779757833, "_cnpmcore_publish_time": "2021-12-16T10:37:42.150Z"}, "2.0.1-beta.0": {"name": "vue-eslint-parser", "version": "2.0.1-beta.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.0.0", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "esquery": "^1.0.0", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.4.2", "typescript-eslint-parser": "^5.0.1", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "45ce600551805f0326c3ebb0931089bc4e6e4bca", "_id": "vue-eslint-parser@2.0.1-beta.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "1ce5a7619bfb9ebaacd4f4ba9aeb591edd9132ce", "size": 115344, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.1-beta.0.tgz", "integrity": "sha512-p6ZOQao/SvTnskv4Xsi7yRzVOD7ok1pSp+XtyqCQsE4ItRoOZbzOcmDb8tc1dqnjtmZPAwqasc4MXrgrEIkEJQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.1-beta.0.tgz_1504272718006_0.7606270133983344"}, "directories": {}, "publish_time": 1504272718570, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504272718570, "_cnpmcore_publish_time": "2021-12-16T10:37:42.504Z"}, "2.0.0-beta.10": {"name": "vue-eslint-parser", "version": "2.0.0-beta.10", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.0.0", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.4.2", "typescript-eslint-parser": "^5.0.1", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "426a9e399e7cca7f8c6a73989af40b69095ff893", "_id": "vue-eslint-parser@2.0.0-beta.10", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "b645910e1f0dbffa611137f205bd2a5c571dbb16", "size": 99678, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.10.tgz", "integrity": "sha512-GE3cxTOQbqw1jgt++7k83agG03MVxapiw+CKUca1U0FDrQL0T2YPcIfmDIsbZWdVL84d//So0EMydEf3tpzu9g=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.10.tgz_1503125335812_0.3584956955164671"}, "directories": {}, "publish_time": 1503125335929, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503125335929, "_cnpmcore_publish_time": "2021-12-16T10:37:42.915Z"}, "2.0.0-beta.9": {"name": "vue-eslint-parser", "version": "2.0.0-beta.9", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.0.0", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.4.2", "typescript-eslint-parser": "^5.0.1", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "0036d670730d4b9ea4c3b9055a462bd48a35a45d", "_id": "vue-eslint-parser@2.0.0-beta.9", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "46d704755c3ebbf1439e0d86289d5f0949ed9a0a", "size": 99616, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.9.tgz", "integrity": "sha512-SHeXSlpTJZrv2qkMUqfEv8GFeriJ8gEhJrnrvv1L9+1h2aW1vuyh4niOBLEXHu/8IF8+MLbXpC91b7R+jUZwmg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.9.tgz_1503122430136_0.7996932168025523"}, "directories": {}, "publish_time": 1503122431116, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503122431116, "_cnpmcore_publish_time": "2021-12-16T10:37:43.249Z"}, "2.0.0-beta.8": {"name": "vue-eslint-parser", "version": "2.0.0-beta.8", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^3.0.0", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.4.2", "typescript-eslint-parser": "^5.0.1", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "c09858dc8e7afbef2344bcaa2e56f3c0612bf077", "_id": "vue-eslint-parser@2.0.0-beta.8", "_npmVersion": "5.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "05d778fb220407f2c2c2a2cba5214fae7d2d2e70", "size": 99489, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.8.tgz", "integrity": "sha512-W+JevKwTMyotEJhSjMzImYpVE/Bb6p0S8BmBh9Nq8/msqbvFVBIxFMX+e+3KSo4LwvKn5vm0u2A5PQIv4N8Mfw=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.8.tgz_1503011776188_0.4762219483964145"}, "directories": {}, "publish_time": 1503011776336, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503011776336, "_cnpmcore_publish_time": "2021-12-16T10:37:43.692Z"}, "2.0.0-beta.7": {"name": "vue-eslint-parser", "version": "2.0.0-beta.7", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "d0b2e9a0543a8b98c28a5d9d5fbe66016e371848", "_id": "vue-eslint-parser@2.0.0-beta.7", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "43737861129620d16326ab7486919365b3d6e379", "size": 98745, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.7.tgz", "integrity": "sha512-S62CeCnpOZ4WfFFW/DCvu5L8Pxy6bsWEDAt8sLHI03BH0x4rbcRZgfi45GiVe/FKUl79xDI1RuCPrZmqguHCtg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.7.tgz_1502181475221_0.3444461014587432"}, "directories": {}, "publish_time": 1502181475309, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502181475309, "_cnpmcore_publish_time": "2021-12-16T10:37:44.058Z"}, "2.0.0-beta.6": {"name": "vue-eslint-parser", "version": "2.0.0-beta.6", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "8a231511c3d60fcae734e3ce3c7af21e4328f288", "_id": "vue-eslint-parser@2.0.0-beta.6", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "19c3b73bf2ca997c70b97d02240b58f87887e6bd", "size": 98746, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.6.tgz", "integrity": "sha512-wOdA4iT6a5ygDOMH1A8zFtK3/I3c3C39wH9hxLZuilrY+cnWBLr5V5yjwb/G3rPPe9fJaGDC4z7PTGrqz+WwkA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.6.tgz_1502078279590_0.043557701632380486"}, "directories": {}, "publish_time": 1502078279917, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502078279917, "_cnpmcore_publish_time": "2021-12-16T10:37:44.600Z"}, "2.0.0-beta.5": {"name": "vue-eslint-parser", "version": "2.0.0-beta.5", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "0bc6d7a972c87b4bdb3e735d09bf7161361447d2", "_id": "vue-eslint-parser@2.0.0-beta.5", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "5c2b59370870bfc8ea0e148658b4b91895b09ccc", "size": 98670, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.5.tgz", "integrity": "sha512-/fw/KY6wW8d6YC+flx6qKdiPGsoSiLya09DCdrNqMW88dcWv4jhi/3xY7M5LGXJTqLLDhqx0NXql0SWOGLbCtg=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.5.tgz_1501843948414_0.5852143655065447"}, "directories": {}, "publish_time": 1501843948757, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501843948757, "_cnpmcore_publish_time": "2021-12-16T10:37:44.949Z"}, "2.0.0-beta.4": {"name": "vue-eslint-parser", "version": "2.0.0-beta.4", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "bddd716dae925de0354176293f3ee4b3d88023e7", "_id": "vue-eslint-parser@2.0.0-beta.4", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "ee2f176fc085f40940edcc5ea01367c30eddc26d", "size": 98646, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.4.tgz", "integrity": "sha512-lUJLB6LhDXb9gnCbcGKcVlb+jcehU1uEtw8fULeAwLvwmdcsh4EnDw//+bI+zPC9j80x7E2fr1BbkyV+kip8ow=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.4.tgz_1501796858252_0.20865482417866588"}, "directories": {}, "publish_time": 1501796858540, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501796858540, "_cnpmcore_publish_time": "2021-12-16T10:37:45.452Z"}, "2.0.0-beta.3": {"name": "vue-eslint-parser", "version": "2.0.0-beta.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "e5213a4a0979573ccb572e0e38e5282dbd2b5040", "_id": "vue-eslint-parser@2.0.0-beta.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "11677f29f7714cad350213108ebdb07503925761", "size": 98607, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.3.tgz", "integrity": "sha512-/h9CORTgmIPEsu6PKXY5IbNzcwCkb1aV5mpnSojwNcShdWi+VKXi95HMnpiWuLAa3M6Hh2tMENwR/twyrX3h1w=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.3.tgz_1501712718058_0.2568831390235573"}, "directories": {}, "publish_time": 1501712718188, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501712718188, "_cnpmcore_publish_time": "2021-12-16T10:37:45.798Z"}, "2.0.0-beta.2": {"name": "vue-eslint-parser", "version": "2.0.0-beta.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "446f7bfa737241f6b527fd89d7303b314344cb94", "_id": "vue-eslint-parser@2.0.0-beta.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "88a0832fe9e9c91a5dfdd4f551e0796d7e037eac", "size": 98173, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.2.tgz", "integrity": "sha512-bcoX+jDpF7xY3nbPK9W6yBiH7I0WOFA1SU0LnYkusZzY6ZRGbJWOiyNpl1TWAkNmLH0Az2uGVVnzsBVB1aSl6w=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.2.tgz_1501505084445_0.04132794891484082"}, "directories": {}, "publish_time": 1501505085040, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501505085040, "_cnpmcore_publish_time": "2021-12-16T10:37:46.340Z"}, "2.0.0-beta.0": {"name": "vue-eslint-parser", "version": "2.0.0-beta.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "833af75cf6643183f49ee81c9208826af553daff", "_id": "vue-eslint-parser@2.0.0-beta.0", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.4", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "7af40077e3a9d6e1036cc968295f1b2cb1a564a9", "size": 98002, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-2.0.0-beta.0.tgz", "integrity": "sha512-GfJgr4S0kEBsT2k24yqBaYIq1cKHXwiajrEzF+6gpr56pRFY/5FibDwCTEo4QmNqugtG5XSKwlwwygDMMH8g5g=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-2.0.0-beta.0.tgz_1501401427804_0.14214306371286511"}, "directories": {}, "publish_time": 1501401427917, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501401427917, "_cnpmcore_publish_time": "2021-12-16T10:37:46.858Z"}, "1.1.0-9": {"name": "vue-eslint-parser", "version": "1.1.0-9", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["lib"], "scripts": {"_mocha": "_mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js lib \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "nyc npm run _mocha", "update-fixtures": "node test/tools/update-template-ast.js", "watch": "npm run _mocha -- --growl --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": ">=3.3.2", "lodash.sortedindex": "^4.1.0", "lodash.sortedindexby": "^4.6.0", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^2.1.0", "eslint": "^3.19.0", "eslint-config-mysticatea": "^10.0.0", "fs-extra": "^2.1.2", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "~2.2.1", "typescript-eslint-parser": "^2.1.0"}, "peerDependencies": {"eslint": ">=3.9.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "dd5553b33b58d0cfbe4402aea789a2ffbf277dd4", "_id": "vue-eslint-parser@1.1.0-9", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.4", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "c01f9275f6a938d97959194605a3826cbcb02c4e", "size": 25900, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-1.1.0-9.tgz", "integrity": "sha512-EfvzuAqOho6Ml/dzjvgZKmbYVa7yC/gL+OzUbmRXZBOGIdfaQRvWsr/QLYVUAW8glnyW0XrBDiCf1tuNBkjqsQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-1.1.0-9.tgz_1501394936288_0.4534743642434478"}, "directories": {}, "publish_time": 1501394936564, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501394936564, "_cnpmcore_publish_time": "2021-12-16T10:37:47.136Z"}, "1.1.0-8": {"name": "vue-eslint-parser", "version": "1.1.0-8", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["index.d.ts", "index.js", "index.js.map"], "peerDependencies": {"eslint": ">=3.9.0"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": "^3.3.2", "lodash": "^4.17.4"}, "devDependencies": {"@types/debug": "0.0.29", "@types/estree": "0.0.35", "@types/lodash": "^4.14.71", "@types/mocha": "^2.2.41", "@types/node": "^6.0.85", "babel-eslint": "^7.1.1", "chokidar": "^1.7.0", "codecov": "^2.1.0", "cross-spawn": "^5.1.0", "dts-bundle": "^0.7.3", "eslint": "^4.3.0", "eslint-config-mysticatea": "^11.0.0", "fs-extra": "^4.0.0", "mocha": "^3.2.0", "npm-run-all": "^4.0.2", "nyc": "^11.1.0", "opener": "^1.4.2", "rimraf": "^2.5.4", "rollup": "^0.45.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "typescript": "~2.3.4", "typescript-eslint-parser": "^4.0.0", "wait-on": "^2.0.2", "warun": "^1.0.0"}, "scripts": {"_mocha": "_mocha \"test/*.js\" --reporter dot --timeout 5000", "prebuild": "npm run -s clean", "build": "tsc && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "nyc report --reporter lcovonly && codecov", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint lib test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "nyc npm run _mocha", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node test/tools/update-fixtures-ast.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --no-initial -- nyc -r lcov npm run -s _mocha", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node test/tools/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "6d10aa425251cf18ab0f8eb21fcda488aafe92ed", "_id": "vue-eslint-parser@1.1.0-8", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.4", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "de739e299454f332f38872405e6b4746f995e2c1", "size": 97094, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-1.1.0-8.tgz", "integrity": "sha512-K6dNK/vfzVAFjPV/2RCjIcGBPNkw3ffxf7Aa3a6rz6xNwD2pih8OkIuH9l2RN6CsyIQJtMRarTfBkvHOlEGxdA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-1.1.0-8.tgz_1501381116791_0.9799063564278185"}, "directories": {}, "publish_time": 1501381117036, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501381117036, "_cnpmcore_publish_time": "2021-12-16T10:37:47.482Z"}, "1.1.0-7": {"name": "vue-eslint-parser", "version": "1.1.0-7", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["lib"], "scripts": {"_mocha": "_mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js lib \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "nyc npm run _mocha", "update-fixtures": "node test/tools/update-template-ast.js", "watch": "npm run _mocha -- --growl --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": ">=3.3.2", "lodash.sortedindex": "^4.1.0", "lodash.sortedindexby": "^4.6.0", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^2.1.0", "eslint": "^3.19.0", "eslint-config-mysticatea": "^10.0.0", "fs-extra": "^2.1.2", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "~2.2.1", "typescript-eslint-parser": "^2.1.0"}, "peerDependencies": {"eslint": ">=3.9.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "acdcf249914ad61dcd00fe5c028b06ad1f0a5b3e", "_id": "vue-eslint-parser@1.1.0-7", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "fd29030c6a1ec221ed173257072bc2359235be8d", "size": 25939, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-1.1.0-7.tgz", "integrity": "sha512-dY0AR5BxSjN8TT4bStUuGb55yjSyAvN1ifUrzfuD7kMdmd5g7e/LYIXCqcvB3GuI5wWOs3fa3o31fqQHfQOHew=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-1.1.0-7.tgz_1498558088087_0.35017063305713236"}, "directories": {}, "publish_time": 1498558088319, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498558088319, "_cnpmcore_publish_time": "2021-12-16T10:37:47.688Z"}, "1.1.0-6": {"name": "vue-eslint-parser", "version": "1.1.0-6", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["lib"], "scripts": {"_mocha": "_mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js lib \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "nyc npm run _mocha", "update-fixtures": "node test/tools/update-template-ast.js", "watch": "npm run _mocha -- --growl --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"debug": "^2.6.3", "eslint-scope": "^3.7.1", "espree": ">=3.3.2", "lodash.sortedindex": "^4.1.0", "lodash.sortedindexby": "^4.6.0", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^2.1.0", "eslint": "^3.19.0", "eslint-config-mysticatea": "^10.0.0", "fs-extra": "^2.1.2", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "~2.2.1", "typescript-eslint-parser": "^2.1.0"}, "peerDependencies": {"eslint": ">=3.9.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "b9497a6b7d89f0691272c56152bf7e2f5f208b17", "_id": "vue-eslint-parser@1.1.0-6", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "d56f9d9abe498a2ecc5cc741e0c35bc1143ff20b", "size": 25819, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-1.1.0-6.tgz", "integrity": "sha512-HKaWwqfPOUSR3lMX1/2fQkgNoVnmhQtHyeGzE3kyf1j6BgAw4fzaHeoNLPLCLvq7c/SoxY2VXRxFCxq+1mB3JQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser-1.1.0-6.tgz_1497784616485_0.4566870303824544"}, "directories": {}, "publish_time": 1497784616605, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497784616605, "_cnpmcore_publish_time": "2021-12-16T10:37:47.910Z"}, "1.0.0": {"name": "vue-eslint-parser", "version": "1.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["lib"], "scripts": {"_mocha": "_mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js lib \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "nyc npm run _mocha", "watch": "npm run _mocha -- --growl --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "^2.1.4", "typescript-eslint-parser": "^1.0.0"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "d21b3ac1de41fea6dd4b14fd0b7389f9908349a2", "_id": "vue-eslint-parser@1.0.0", "_shasum": "0d058a3e49a6cc97c6da6482caf8c6d41850d640", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "6.0.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "0d058a3e49a6cc97c6da6482caf8c6d41850d640", "size": 3578, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-1.0.0.tgz", "integrity": "sha512-9WWDgMyLFfG5N1B7l9cb7ICJQSCdWyRA1FXNdryvVICveBADEkiZ/Qw/MPlKdZCiplVoS0oj7zZMyXDRUEmnXA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-1.0.0.tgz_1483021956678_0.3487029285170138"}, "directories": {}, "publish_time": 1483021958569, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483021958569, "_cnpmcore_publish_time": "2021-12-16T10:37:48.556Z"}, "0.2.0": {"name": "vue-eslint-parser", "version": "0.2.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": ["lib"], "scripts": {"_mocha": "_mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js lib \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "nyc npm run _mocha", "watch": "npm run _mocha -- --growl --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0", "vue-template-compiler": "^2.1.6"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "^2.1.4", "typescript-eslint-parser": "^1.0.0"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "5b102847aa7f699d052d667a3bcc2564bec7616c", "_id": "vue-eslint-parser@0.2.0", "_shasum": "e88aa190fdb93eef2fd98a91e2b2bb098599bb2c", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "7.3.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "e88aa190fdb93eef2fd98a91e2b2bb098599bb2c", "size": 4806, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-0.2.0.tgz", "integrity": "sha512-KYVxBGxLFhIQsqzVdR3JoE7wSS1uPhSxJ8AQnGRuq6XFXGYnYGFahbFfTEwPZsn8IsbhCMpW4hN/KVS3UOm5cA=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-0.2.0.tgz_1482380010654_0.8725562826730311"}, "directories": {}, "publish_time": 1482380010894, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482380010894, "_cnpmcore_publish_time": "2021-12-16T10:37:48.844Z"}, "0.1.4": {"name": "vue-eslint-parser", "version": "0.1.4", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": [], "scripts": {"clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "nyc mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "watch": "mocha \"test/*.js\" --compilers js:babel-register --growl --reporter progress --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "^2.1.4", "typescript-eslint-parser": "^1.0.0"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "94ee8fdd8583044cffb506fc47c1e93a5544e8eb", "_id": "vue-eslint-parser@0.1.4", "_shasum": "033bcb367081d69684ff16b0129fe70efa476279", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "033bcb367081d69684ff16b0129fe70efa476279", "size": 4116, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-0.1.4.tgz", "integrity": "sha512-EgGVDkKc/gUBdL/neuYKyXAFRl/6E5KtB7EC2cS1FsRcvyXZSRoMg3xHtDspdtFwcu3HW96VEveKs/nCwho2jQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-0.1.4.tgz_1482112280697_0.472800322342664"}, "directories": {}, "publish_time": 1482112282490, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482112282490, "_cnpmcore_publish_time": "2021-12-16T10:37:49.049Z"}, "0.1.3": {"name": "vue-eslint-parser", "version": "0.1.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": [], "scripts": {"clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js \"test/*.js\"", "postinstall": "git submodule update --init && cd test/fixtures/eslint && npm install", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "test": "nyc mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 30000", "watch": "mocha \"test/*.js\" --compilers js:babel-register --growl --reporter progress --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-eslint": "^7.1.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4", "typescript": "^2.1.4", "typescript-eslint-parser": "^1.0.0"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "b3a780aa0c2affeb4df50b216ebe6d063b21b5da", "_id": "vue-eslint-parser@0.1.3", "_shasum": "8a97bcfc4efc7400daf9edc83af617532c4bacbe", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "8a97bcfc4efc7400daf9edc83af617532c4bacbe", "size": 4117, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-0.1.3.tgz", "integrity": "sha512-YaRkisZS3Lj/gxM+zooL3lf6Z7TvC0PraE7fJomENuY3PrPvDhA4mmqBufQbmNasj5e5ecKwIxZ86BXn5RsqlQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-0.1.3.tgz_1481977886700_0.34942883835174143"}, "directories": {}, "publish_time": 1481977886936, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481977886936, "_cnpmcore_publish_time": "2021-12-16T10:37:49.294Z", "hasInstallScript": true}, "0.1.2": {"name": "vue-eslint-parser", "version": "0.1.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": [], "scripts": {"clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "test": "nyc mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 10000", "watch": "mocha \"test/*.js\" --compilers js:babel-register --growl --reporter progress --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "33eb3e5f8205c9493d674185b3517fa9c2165313", "_id": "vue-eslint-parser@0.1.2", "_shasum": "d603f372fea43fcdd88f6f242b4bfc40b5c4726f", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "d603f372fea43fcdd88f6f242b4bfc40b5c4726f", "size": 3528, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-0.1.2.tgz", "integrity": "sha512-DLpxs5kjQb2xj91WyJGNzNg+jl3I10EUx/e6mLlOEECLWXO3jO/epFi3GT8HSptVqlrgd+G9ju5JPF4L30NhAQ=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-0.1.2.tgz_1481955050876_0.8764828494749963"}, "directories": {}, "publish_time": 1481955051114, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481955051114, "_cnpmcore_publish_time": "2021-12-16T10:37:49.544Z"}, "0.1.1": {"name": "vue-eslint-parser", "version": "0.1.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": [], "scripts": {"clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "test": "nyc mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 10000", "watch": "mocha \"test/*.js\" --compilers js:babel-register --growl --reporter progress --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "483e006d61d3e1ebad64cff6c0d9b8e41f34c909", "_id": "vue-eslint-parser@0.1.1", "_shasum": "32a8bd5a19129946a737cdec0e7fefc2449ebe68", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "32a8bd5a19129946a737cdec0e7fefc2449ebe68", "size": 3516, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-0.1.1.tgz", "integrity": "sha512-R9N5wFFmeu6W8rcuBqWG9mk8sa4A6u+LkcuWW1h8NhUg+gY4mewSP8zRHaBT2rjXVK9VpRVAAtngwIKXrK3c/A=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-0.1.1.tgz_1481862083339_0.24762273440137506"}, "directories": {}, "publish_time": 1481862085017, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481862085017, "_cnpmcore_publish_time": "2021-12-16T10:37:49.755Z"}, "0.1.0": {"name": "vue-eslint-parser", "version": "0.1.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": ">=4"}, "main": "index.js", "files": [], "scripts": {"clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener ./coverage/lcov-report/index.html", "lint": "eslint index.js \"test/*.js\"", "postversion": "git push && git push --tags", "pretest": "npm run lint", "preversion": "npm test", "test": "nyc mocha \"test/*.js\" --compilers js:babel-register --reporter progress --timeout 10000", "watch": "mocha \"test/*.js\" --compilers js:babel-register --growl --reporter progress --watch", "codecov": "nyc report --reporter lcovonly && codecov"}, "dependencies": {"espree": ">=3.3.2", "parse5": "^3.0.0"}, "devDependencies": {"@types/node": "^6.0.52", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.18.0", "codecov": "^1.0.1", "eslint": "^3.12.2", "eslint-config-mysticatea": "^7.0.1", "fs-extra": "^1.0.0", "mocha": "^3.2.0", "nyc": "^10.0.0", "opener": "^1.4.2", "power-assert": "^1.4.2", "rimraf": "^2.5.4"}, "peerDependencies": {"eslint": ">=3.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/vue-eslint-parser/issues"}, "homepage": "https://github.com/mysticatea/vue-eslint-parser#readme", "gitHead": "30834a3f0987b1c26cbd01d19258b6dcdcd35189", "_id": "vue-eslint-parser@0.1.0", "_shasum": "e1f23279f35f696c26eaff43b917c812e45c8e20", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "e1f23279f35f696c26eaff43b917c812e45c8e20", "size": 2981, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-0.1.0.tgz", "integrity": "sha512-IeX4X20hxsTElQsXjHz1wQ2H9oFX0IuaYFyhIfcrky5j4OlrRTOiGsRXf5fePLq+VaMYriLBKw+nlTRjPZ533g=="}, "maintainers": [{"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-eslint-parser-0.1.0.tgz_1481774981852_0.3033078720327467"}, "directories": {}, "publish_time": 1481774982084, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481774982084, "_cnpmcore_publish_time": "2021-12-16T10:37:50.139Z"}, "8.1.0": {"name": "vue-eslint-parser", "version": "8.1.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.2", "eslint-scope": "^7.0.0", "eslint-visitor-keys": "^3.1.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.2.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.6.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "8a0a474916c27aec7a6d04fbfc8694722dbc0644", "_id": "vue-eslint-parser@8.1.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-R8Pl6MSvuU81JKyUguexcABQxfM51Q2ZJVBX0TrKiLltPB+haVLp4hfuR5msiu9oqpsQkaV+TF18r4CBw9c9qg==", "shasum": "0ffc2488ca032043ac36b13a333401c48608bd82", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.1.0.tgz", "fileCount": 6, "unpackedSize": 986671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6gOfCRA9TVsSAnZWagAAFMAP/0qQpdOt0WslKg9Mrx76\nxsUgnz9D/SrBRk3lDI2j900oTtj/pPDXUgVo3wwJQPVSvqRFxVESOgJoP/Pp\nLgfC7WMHXjiuvUjjPIPq+AIIYZvTsbLrxgalNtfygtR8nzEPt6BbgoWUKxPC\nQ2W3F0G/1mSgeo0gmdQuvOp/sDDCkoq3tWy6yVgj8r7wT+kDZToad5csRLQx\nLzhbOFMVYqOI4++/3nqxFIeFOc+n/Yi2xkeZIe/zT2Nrtl/4tEmG+uKG+j9v\n5QePxQemsJz5b1nW+PapeIDSrK4AZiDbVZaUQ5eTb3karsv6ppxcuDe5XN8Q\nMJbIYPuVI+aRZQ2TScA07Dqvk402BxHMRzpArGu0ARojtfKoDUdhj4IeJR7n\npJpVZg4pf0lz680hqwDx59jk5BUxVgHGwy4boUWHpPzJWfb0Vmt6M181/TAh\nJhe+Hta1kvmTtqX4slUcjk1P4KCkHcqf9bJ6bv/rZrz1hydZXERQ2VKe+vB4\n3/c0QQ5NXDbQgdglw3dyH8wJNVWyHZm8u+m9Bdfe9iphQ3HhgsX1mNEhv77p\n9M8AvhqveOiF9/pfDOb47EsmHJVArvGQFOnTkdxBgbfip93U5EPEfSiHb/Vx\n5GWFr8yzNotnYHzx4jOu06eG7MIOwl6q2xATa6m6r0I66Blrrrj77iUr2qVb\nNTW2\r\n=9GY2\r\n-----END PGP SIGNATURE-----\r\n", "size": 174044}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_8.1.0_1642726303154_0.3784222756236537"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-21T00:51:52.556Z"}, "8.2.0": {"name": "vue-eslint-parser", "version": "8.2.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.2", "eslint-scope": "^7.0.0", "eslint-visitor-keys": "^3.1.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.2.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.6.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "734a87cafca2dbd570c0963f17ac890c2205b646", "_id": "vue-eslint-parser@8.2.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-hvl8OVT8imlKk/lQyhkshqwQQChzHETcBd5abiO4ePw7ib7QUZLfW+2TUrJHKUvFOCFRJrDin5KJO9OHzB5bRQ==", "shasum": "8c3990deb901b0d528d99f4d052a831cd1d0284c", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.2.0.tgz", "fileCount": 6, "unpackedSize": 1002285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7jmJCRA9TVsSAnZWagAAC0AP/jWmCeTbooCGD/VBFvGh\nNopLDFF3RnOunGHzM348jG64Q8Dljg8XDrmX5vFI5BnBYTSpzy54jldM8QHu\nDMlDTdasKcV1RCBAPqQK5/xFC+kcNzI6Q9mD4Sg6biuSPFA+NaT5yMN2gVGl\n5yCi8fcHQf8pCnOtetNMbHJ3CCGtSp3DTzpPEraDDgSY8g9wq4ZeN+gPj/0r\n04yn51QWpLuwwmoZVW8d34L2HW8TwUc18Zq5nKE36vzUvN6LjEedh8E76wOe\nEgn2uLroFM5vBng0qRI3+4Oozj9WV1YXfnnAsmTAHYrPsDntWHlAvb0vbLy8\nH+98txuq+JU73MgmRFPSntsBohaGjDlJQ5EmFavWbTPXi7YHPEnkRyd0M+VF\nsuDsSp05tSWbGSYi46nkb+XHhMsoqlAzozdhoL4nOM5G/NwRbQtQp+TEDiAk\nZM7cjr2dhoYMCOdNpyGGYSgfidRBSqQm/2HQxM7fs2DyM6jrZHJUL7SvNpe2\nAVisBmE31w/O4ySND7stsWjuI42+ShMKxpX+4i0osEArUlMpuC6/almbil42\nAzrGlLgNDoe2wRzeOrSSRad6cAwnKiRNoOmqP+ktDIB7k23qyIqRIZL5I2lb\nH2PdVFFNIAhhqSEk4cdxeCWnedLeGsWz2jLOAVwI2PTVn00Yh+fbipCuy87q\n1wwv\r\n=KhOs\r\n-----END PGP SIGNATURE-----\r\n", "size": 177111}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_8.2.0_1643002249335_0.18417010864412942"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-24T05:40:54.399Z"}, "8.3.0": {"name": "vue-eslint-parser", "version": "8.3.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.2", "eslint-scope": "^7.0.0", "eslint-visitor-keys": "^3.1.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.2.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.6.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "d900ec22be9f76edfab33d378fcfbeb25544122c", "_id": "vue-eslint-parser@8.3.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g==", "shasum": "5d31129a1b3dd89c0069ca0a1c88f970c360bd0d", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz", "fileCount": 6, "unpackedSize": 1003522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFKQQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1eQ/9H04OCMsRBSPm0J6Lhik9xh37qMmAMXECHuaaiDaIXBOCLftA\r\n8uwrAkXkepWd3lzoidVp1PHwn2XOoQ+LXBIEJGO5xRrcIMIGUL4sZ9r25FHy\r\ndTUW9BiAnuf2hApss96wDSJZ9QQIVT3lBJ4AHihM2bZmRC3tTK2S+G4o/15a\r\nbdw/Zi3Bol2QEWLwjKAslvu+vkSbs+Tpm39OLKsaVFvRnaSa902IdEVjlHsf\r\nJNqZW3yFn9KdOTc31QfNRjQKck0d0xIB3V2nWSYiNA52zex9IT200EpXGn58\r\nu+z0Fv62FMLw99Mwb33nLijK3q2kjzf2PVzTFVTOEYALHcm/Zc9NRTsiWwwi\r\npQrqQlE0ksj4n2c9gDGhRAgaWsqFYzpidddrJ9+ksgnmqDxFCtEINHFPKwc4\r\nEtTvgECGFY5mIhiZEW0XxiZqlmE5B5eh+o3xLeDCgSpks/yfVpHD9lOvH6CU\r\nZMZBChverZun8xXuelfARhL2sisARS8y948He5DOe62OHFSTefYoWWHPLmLn\r\nT2eSVuzhdXeQrzlJk5M/I4mrXeeKaoi0POgeAjChXeZpkwTFgoag+gSqnt75\r\ntCDlJl+aJYkcN8QFtPRsTsoAVFoAjU1eVH1YMHF7sKGzgmrYRfRuagM3DSed\r\n5nXgVhMuHC0t2I3rsz+ytMGE9bMTaeGr3tU=\r\n=/2xb\r\n-----END PGP SIGNATURE-----\r\n", "size": 177282}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_8.3.0_1645519888232_0.24932847099279232"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-22T08:52:14.888Z"}, "9.0.0-alpha.0": {"publishConfig": {"tag": "next"}, "name": "vue-eslint-parser", "version": "9.0.0-alpha.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "readmeFilename": "README.md", "gitHead": "c476e7c25ab161b336339448765085e6219d0e04", "_id": "vue-eslint-parser@9.0.0-alpha.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-btRN9nvSE1xQYOSDSLTDARGYOpz+qoST0OJNtIrmdeLTqm2ZHGfJ6I7/uIEarVCK+yHOqfUoEFiG+Fdfbrfx9A==", "shasum": "cbd87430f50691f66b047a85cba28fb039c70f19", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.0.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 1060547, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVQPDWEeqXKvvWrB0XGzft6O6AkA0cGhQoO/e6k6JlsAIgUdtjqUSJIhqWDzc7wmxOXJr62C2KKdnAL9lX+lBn2f8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiV+D5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZUQ/9Ejw698H97kmVtTCtyEvFxAcxEoAhNKV48MUwQ7JI0ZYbdQj1\r\nuB50rE5GPysaNunZMnSxxlmvqPlhQja2FbStqCjlsmS8cTmilztHmHUhHe/n\r\nhdl3vb8eF4nwtbajGD8kvEriC1xsrzdqcgL2nbbaTkze479vfnkVbizxRFSe\r\ncfbK5BbvEi/7TWKTRn1LevOCahasWxRZ4TXkyOL8y38/vI2o0NmTpnKbcst8\r\nWLiQx324GKPuC95nBEnDAuoPQJwu8qmdNO7JDL5NylD917dsblEF3+ljtZeC\r\n+q2bDqAjMeL4rsUUvszhU+253+ChcHFhZyD2IC5Xh+z9CZQOX2rGocuZQac6\r\nRXKSROfPWhjok4q7TiW6FHkLghI6RbAmk1C9hsSnG3p10F7wEjjSelmDDCmq\r\nfY+dwmMNyNRWtWaxusLs3uP+2DrGwa9ApZQvSdxRBHmEq2pegrgvc8wNRcCL\r\nQ1uBRnpEXAIIMaqoMCI8KLM/9fYFYlvaYnzzqe2YZ+RLodvQmuO/fXOxg2k3\r\np10wyLU2obBsFl80FEgUpA+Pa34mc5S+jy9da7gf/PDhZlMmcVSDExVtvc1s\r\noqLFv5KmFYOILKiBTY11Ye4sDhzY9170rX8QThzYRQPNNcSffvi6DLKsxmfV\r\nypE6Fjr+W9C8BIE39hNa8U8XYUVjAPAZ5pM=\r\n=9Yp8\r\n-----END PGP SIGNATURE-----\r\n", "size": 187621}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.0.0-alpha.0_1649926393306_0.9678314500917735"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-14T09:08:54.912Z"}, "9.0.0": {"publishConfig": {"tag": "next"}, "name": "vue-eslint-parser", "version": "9.0.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "readmeFilename": "README.md", "gitHead": "50b0126b4373a0e951dc29d18b69ef828d39ba6b", "_id": "vue-eslint-parser@9.0.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-lwH6ZR79Kc68UotOxIBcEu8u6fLzF51g4t+mGAN4owUAjy1Bkg19bvxbYybZbQB0/ToAFrPhwrTgG1+tS3jmgA==", "shasum": "a5444d84f26b044cd445f18b1da597f76f9f4662", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.0.0.tgz", "fileCount": 6, "unpackedSize": 1060539, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrfwyyw0q+qo51snqE0XMQFGe7ypzsFKTletwdS3+EAgIhAKt53ExChoKgWSPE1BpgwCuJ+mK4NeXPdFC+u/3PxuJ0"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifEdRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoimw//Ybas2MBiHzqVcliUx4lE6pstNEIT1SkxGK0JOZe0ztWE02MV\r\n1onQ8/Tu2LQAFgeXU1OIAae6n9VrfY1sHiDM6MuA5b2Coth78OE+T1XFFsfN\r\n0KmI4oilbrdHktU33ZSsDCRLI1HfgKWZ+0wxNpQShA5EHVpmuBpg0By9/BLc\r\nIBPq/DtTS5iPrBj2kdxEevupeMVnke8OBUnGjPllQM47fn9uDd6CK6ELdEUM\r\nAEbtH0gVCp+ivxQvQUmsvYw/nZn02vnJDZAB13hi1cGn1IbzbWpCLpYj7Xiq\r\nboaxkNLV4L9Une8hvnpn1hhPKiY6yLSOI7mW1S8ek/TFs6ILYuMRN7PgMWL6\r\nKys3cSOQGpXfuMCRinVsf8v1de0CNz5qyc1/jiOSv4Zw776pcB8HStUCWbEM\r\nGGiakM8iEVaxAAxTOm1eGOTbEjtWnqeDlhALgSP7Sq+SxwsBZ18PMv8MKMxO\r\ndR6c1tglHIX32BIxgdaADZwq90lQpCYVCJLP6Ly0wlIlLvI/xvRTnV9qgECp\r\nZhtX9vXpJ1qDiq6mWcDiosAecP/W24za+/AjjQmEappkAQF/co6FEl3AKiho\r\np2aRekPaeiv5K1bB2f/jbI8SeIflfIW5zy6sT+466yXpr8ECpaAH30GrcRa3\r\nlnT0thrJ1dzuRe+xZWoE0lRQemUA2j09+TQ=\r\n=hRNR\r\n-----END PGP SIGNATURE-----\r\n", "size": 187618}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.0.0_1652311888828_0.15357453284240297"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-11T23:31:37.378Z"}, "9.0.1": {"name": "vue-eslint-parser", "version": "9.0.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "cfa2b47f96d42a36355660ae7019ceb2751eae13", "_id": "vue-eslint-parser@9.0.1", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-Duy/yrHiElax9AW+3uoH35jazQG7XB3OBDIJhvcnbsAI/5hQ8beBIYforOu6fznCRVGt+PmPTlnQMzdlgOAuWw==", "shasum": "a30e2a421cee3d3a3427ed27d2551939f379e5c3", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.0.1.tgz", "fileCount": 6, "unpackedSize": 1060495, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDi5kIAaEAKG1wEkTP3WaeoV+IVvZI5wtlq+dbbdMH2vAIhAJl9GcSjDU9SWYVkihcV0868fSet7tGS8GuO/4yCv5zS"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifElEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOXw/8D7JoTO5biiqYU7JLJ4N89rL8SJPYHZm2EL1+W79dy6cXNnff\r\nZ6budW4Fc21JpD1cWV4JnIFdfeOS5XoAX1J5J3UrpHRJfau3fgVc6RvtfVLc\r\n6Tcgxe3dOJ8fgYGaJ30YNdWbOtRoX1oKHKAT2aRNd2sL7jga68qWUMKNGMyA\r\nrg/KssNwQyq0Fjg5f0PlVv+OdupGWqYfvCRjmOeYUwGhVNYVjmEJtuEKd2Z2\r\nXyZqvMJ1jbb5O7C5nDwnhs/KTEhQ/iCn0GQlMTZKlENy/gxMoX5nei5ISEFa\r\n1dz9cx0Cv7TEXKEp4s/ihi/dED+poAq46L0UT3C0A4qg8MTYnxnBpcauu5pG\r\ntyETVNnwQI2FsuDmOWy1e/u9juoxGujW8V7BQSfqXPyhpH2WTwukID+Iogsy\r\nsSgBiVi7z3HfNDzbs6kRPaccD/dk8CFdxrmmECh00+yH3EXiIDjNGVHPZ2H2\r\nhJ8ZfryCEPcGz4g6ObLSb4t4i6Vt5Z8+0DhhOheMDzRwRjCUEJKQ7jd3EsmV\r\nsOOOojUhQMkONXX8fxKXelC7im1DWjN3BDqy69m+j74yy2S8zLhTt5m7piW/\r\nc+wR77WS2ofdGr/Ip1tRnapOFZTxmxdakPaPmtMzM+HcbWgiYcTFLyAMJ3Ir\r\nLk+wxNbfu00xlhqRFucHKNroVKqTFyV5cOw=\r\n=doDV\r\n-----END PGP SIGNATURE-----\r\n", "size": 187604}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.0.1_1652312388350_0.7249250928144435"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-11T23:39:57.434Z"}, "9.0.2": {"name": "vue-eslint-parser", "version": "9.0.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "806a38c291c802749e6322d80cce2678549058bb", "_id": "vue-eslint-parser@9.0.2", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-uCPQwTGjOtAYrwnU+76pYxalhjsh7iFBsHwBqDHiOPTxtICDaraO4Szw54WFTNZTAEsgHHzqFOu1mmnBOBRzDA==", "shasum": "d2535516f3f55adb387939427fe741065eb7948a", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.0.2.tgz", "fileCount": 6, "unpackedSize": 1060610, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDW1BOybnHfpVMSdQLIaAkletpIJy0XG2el/IAGv9zbJAiAXDOqzx0Y1YZa8z27IyBh9L9TXz6RYRuDTjqcxjMZRwg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig3TjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVwQ//WxYIs08YO512yJkMBmADYd7t3jAsfkws/v5IjQU5SMm03l8B\r\nTSY7AytxzsMezo8pnMGqVJH92FeXeqBbMHDdRSimw+4ivioUNmBHD34QppWe\r\nXwWUo5NCHdmK+Y6BqRefOTGqLwJsvWi6yfyIkA+vQQXXT4YFPvMdHw4jpbWJ\r\nauWE8yAAyc2FM9jGjDAbKFm63M+7juvsebWc3Ed4mtvsGQq7evmujC+wl5oy\r\nAznrWr0pxREUAC+EY7kqLt+EQPXeJeZobcsHL4yzhW3VLvQJXcrBpnDGQ+3M\r\n/+9MSujXh/rMM6w4ghxqJzouM9ftm1zCWCrrBovAQKJYrFf5FA9RJ5aw+mV8\r\nnfPxObWdJRPWCbm88w6P3n4/RQukRt387vQTBwZ4lKidR+REJMKxmHk8YFaX\r\n5tw5AqFzgWxcu8IDjGFSGniaA5ITF6X0SdIAN0F7Um9U8CEAuWu+XK1QFGD+\r\nWVumYZnPVylW+qdvBH5s23H6qWL3FKiIDwDWrNWFkgwbM/OaA+gxbulRgR4b\r\n+0mhyRAXDeSnMQcu3/z9HRSLMoCdA704aePBBj7pVRlfd+/odytV4898+76i\r\nUN3R6xWihQQC2IhCLa4zsoHRYLHEc1C7sflRLyjPUr+NZ25/MNqhcWYh3p9k\r\ny3RpDhFuQ42+UTsXxzB+dK1TbU7yNtJBzXY=\r\n=ak2N\r\n-----END PGP SIGNATURE-----\r\n", "size": 187629}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.0.2_1652782306987_0.8577904626256219"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-17T12:23:15.217Z"}, "9.0.3": {"name": "vue-eslint-parser", "version": "9.0.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "gitHead": "9dc89b5317c6d016d0307791e178919f5605c597", "_id": "vue-eslint-parser@9.0.3", "_nodeVersion": "14.19.3", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-yL+ZDb+9T0ELG4VIFo/2anAOz8SvBdlqEnQnvJ3M7Scq56DvtjY0VY88bByRZB0D4J0u8olBcfrXTVONXsh4og==", "shasum": "0c17a89e0932cc94fa6a79f0726697e13bfe3c96", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.0.3.tgz", "fileCount": 6, "unpackedSize": 1060655, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBfAdC7nyqQHw8IggYe7wM26cNoC7Fb07t61rNoi7RL2AiBBAszqG1PWLSq3eMBaa4c2vnUt//onilrJr/JnkMjG/A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitBmrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4Ew//WFVcgowLnyd30UlrxzTB98BTFyBf8S6n/nYUsY3bQ9bUJSUi\r\nsbc+AHclLWz2IwOfSNIwDIGN3JWW4QSdZMDGn1twb20BGYUcRplOGScoGgVl\r\n73m1WfUZ3Ph5CHyEBXpCmoLgDizP/GcC02+E+U0eBvfMGy4eIfG30SUV4FSt\r\nASH0XXc4EbJ8qM0OkVhrU33RHNDS80/J3DsIKx7FxH6TE0XQ2AFB3bTU2dX3\r\nYMV9pHDHOknVer9XjMuHiro7F1N1aqz+Cg8rmzzq3zo4Mum6zJJrotGc2N15\r\n2Q/oLFQPZB7XX8pWeHIb9UrGBHhRcY31PPGSPJCusZ8rZHNlYsbUIy22CfOD\r\nJUT8o3OhOfR05g44jBx9GEfglQLu6Uc3fxqQwpwU85tRG4IrDEvO1Wnz2T7L\r\npYMm1kcufkMMHW/hI7idBGkOU+QpusG3HX0Ot3MjTym9gwNr3VCnKpbrx1n1\r\nyBZXSycli3XPo2VDuOwvI908pxZSMX9NAZSz+EnbuOFGtaVJtOM4zhSAVtbY\r\n02WBLLXL0Pe0+ODutud/hxXP94H3bfju14j0tZYetJclsWsrv+rauQc4bm6o\r\nROsznjndPmTfZi69bspUxSNFXPi4+hqvFxRAPBTer6iTQ6tKO/6scZq7nTk5\r\nau48pTe0DIDCrrwpWPP2AihZRnUyk8ztm8M=\r\n=+FNJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 187627}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.0.3_1655970219101_0.46717344516901904"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-23T09:06:32.392Z"}, "9.1.0": {"name": "vue-eslint-parser", "version": "9.1.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "0631f8c7a2fb9d799cb90b07a95338560c933be8", "_id": "vue-eslint-parser@9.1.0", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-NGn/iQy8/Wb7RrRa4aRkokyCZfOUWk19OP5HP6JEozQFX5AoS/t+Z0ZN7FY4LlmWc4FNI922V7cvX28zctN8dQ==", "shasum": "0e121d1bb29bd10763c83e3cc583ee03434a9dd5", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.1.0.tgz", "fileCount": 6, "unpackedSize": 1064564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGgo6oRYHSiDfPXM9O1SXmEyJ//YfWBfI1w6+EpnkJiVAiEAsSbDwsNeNSkdZCa8hm8bMla17mBNpM9H7EJH72i6crQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGrBvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUQw//Xxi+LnwBU3NjoVeZp7D9vspvRJ5Ns/q5Kh6tpuVzWnGLGLLB\r\nuNBiLoe32a4Pt4Q5sB9tpbSSTHeVEtaWs915FNPlGJM42pS5IWWp7YTl2BFa\r\nv7lUKq/HzNWTyKsE7pcvFN5bPtkbVCnI8KnlnnlLMRCk7FU9L9cTcHHK3tsl\r\ng85F7eEWgv1/ay/n3oFSczr6yYjVrk7JiNTR/9VPW/pENSbeKXt1wqH9Cul9\r\n6ZgtwPpGePrWaDyCjrrOz+9wm2vfJkC5BOh290ayozs3H2fjgAeYcxaamykn\r\nQdCi9D2qhQVj/EvdvrBw1zhL1ZtkN+mCJsaigYDwr8Bn0jsBKPNINkQx/Yne\r\nP+29d+0OAXj1wY6H0O9rK0FoIu7ZYGIN0yOtPHQ32g8DKrPL0hCI2FuHkB8a\r\n2gVcf1p4QZNvAwVDZdOtE/ZCW5vo3g/xvwdoV0vacSCW4PfDk9KaZ6Wi6whe\r\nA35l/LEicNi0aGZHlC6jelo7mPv7OX4CoW5t68dWxM31MzdbzBF47Y4R8KDh\r\nW94JX4UXS9h09mnTM/iDcS/rN5cAwJjMqxD8oFLtwO7bVBFzSuCIcaspjCZP\r\no1n1t8gQQB9PVaSQ8Pcpnk+njLHs5p9QeqduBizayBN1bE2cvQJuo4r4g5WO\r\nzyNtHmx2IROia/IRO/dY00SkqpaGWFRBGnM=\r\n=Iu2g\r\n-----END PGP SIGNATURE-----\r\n", "size": 185107}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.1.0_1662693487162_0.5882035849541571"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-09T09:19:30.962Z"}, "9.1.1": {"name": "vue-eslint-parser", "version": "9.1.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "36a492972620b70e45ffdbd96aa865e73d3b48f0", "_id": "vue-eslint-parser@9.1.1", "_nodeVersion": "18.12.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-C2aI/r85Q6tYcz4dpgvrs4wH/MqVrRAVIdpYedrxnATDHHkb+TroeRcDpKWGZCx/OcECMWfz7tVwQ8e+Opy6rA==", "shasum": "3f4859be7e9bb7edaa1dc7edb05abffee72bf3dd", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.1.1.tgz", "fileCount": 6, "unpackedSize": 1067603, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5vgKLzyMQ5SAjHmw8vI+0K35rp4y0fZ9F/cUfWz9wiAiB/qvxvm/TxNwizaeh8QEmHNtxYELo3SQ4PKM4W5CJVhg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJS6nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+ag//XjTyAaLU8eETokRLgE9pfQBZ49539kDxHqO7J7NA/Gld3PQh\r\nlm+1oNYgqOGS9Ey+CL+VyUivVjHgY2PvTM449hT+XAuvi0PpOUfGN94ONTbS\r\nVbFOaSeIKkgIXhE++i49M6oNc5aeIUcjw1rL8DvcYVu9I6YwfkxiB9wJS228\r\nKOT8cnpeLBumeKQPCLnnxdqMByCL0r86Er/MynBPomXzJo/0NRQr57SB6mcC\r\nAEPZatcP3ME6UPK6jj9eCyEMg5cYTlZkP2PBFxenRnuz1Cd4ba1L5+MyUxVs\r\nzivKx7tUR5tIeSG0uFc7ndoWrKAOkJUvQ5IhD/LAkk6pn9FOmDECHOb+pVvs\r\nP7IvsNqHs9Vz6BGtWziDXoFtVfkfMzrKiYiqWyC3BXttdmbTLcNQHcc2Dw22\r\n8riGOhkc+HNc5nH7GyWFqlXDOftkDVPuL+pbr91KFy/j3wp0zd2HzfQsYugJ\r\ngw/zL8kYcGoAhOL/8RplESWhDiMUbttYvvwJWSiDFsFkUyuVjDBELyr1kGDV\r\nulevCkhafebDVZzxAhGjgJcuPRV2jXUwrF3K0T764WQfDhe8Lbouhh6cU2R2\r\n+14OmULWrAFxvvRSMkHdBgLWcNj6QNhoFvyaIV0fQFT12j3nqy0J11212yY4\r\n0nyrGboQWJIRVKMR29bbFi1Hm9engHb0gy0=\r\n=GKU6\r\n-----END PGP SIGNATURE-----\r\n", "size": 185570}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.1.1_1680158374810_0.031241039509690927"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-30T06:39:34.986Z", "publish_time": 1680158374986}, "9.2.0": {"name": "vue-eslint-parser", "version": "9.2.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "384b52118df0d4d1b8bb4de52787437d47adb05f", "_id": "vue-eslint-parser@9.2.0", "_nodeVersion": "18.12.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-aFXipsUbKU4TzgP9OU6cXIm2Nnp9ryKJc2mzY0s2xzwfjHg6WDT33LUAQRGR9K0NFncBgUEZ2njdrS3Lj/sOLw==", "shasum": "b397a7afae29961e5c59d80e895ab54a2e7f2f41", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.2.0.tgz", "fileCount": 6, "unpackedSize": 1068524, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEZQL8Q0TZ76bLveV/EnlLKSJnaUGR27Q6RC60nwj7z9AiEAxCrO+oR8wNu6p5U2Gpdfa2MHEU2/nap7/SvtJW9Ktb8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUayUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSEQ/7BY/25wws3/JMir81Rcbu74NKaWsHbEMMYJm433PuNAT3Q2YQ\r\nbPD8Uv35PbtmKsCE7pzkUhSTay5aa99OSGYMqjD6FhKRWdtZU2UU8LFuGrRx\r\nJwzbTPX/pS6D7T3x+B4KzDlrr183F1B5x5xrxDP3V4LTeaIm8FFESK0LaRvx\r\nlEHOAXO+/VuE9+KaglzGJbxoDUZVV7zkhyA5epMH3nunJD+az36AtMD7gSWA\r\n393l1MPlT7rVgxvE/mCc7sVH0Mmj+CKCjO8VthNjEUQy8C9JAe4a45Ln5dj8\r\nf4/Zroejig27sSXmRi224FblGkBf1jPADFak88YP2F3MnpMzIdUG7QdkT9wV\r\nmnlifxKX3fTgbYvDRXdqeCn5ZI8MIdD7hN/wUCEeV8huTidEmfDUuwx4fTyp\r\nH+u0dRAaMWbgyLCVmKn8dRkArYrWoWICbrxcU3oOKvuz26eJMGxwgT7Y03O9\r\nxqekMjxOgxiQJcN+VxDFPx5LQ5xcf5gFqHJmvY/1dK8FbSdjrN6N0zwtgQIO\r\niX3ob41p/oWvGx4Hb91NuBTXy/btjoyjTl5jQPw6HRJbEGPCdGMHUdSoP9C1\r\nVOJv7TUeGai7+Qeb5Wtm6Od0BKXWbckWuNBr6WHnKZzUIIwqWdL87ZbtPlXY\r\n3KEtNRrLZciZdSSUJwVG3lZspABKCXKU04k=\r\n=6zkU\r\n-----END PGP SIGNATURE-----\r\n", "size": 185840}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.2.0_1683074196030_0.18383771930899462"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-03T00:36:36.287Z", "publish_time": 1683074196287, "_source_registry_name": "default"}, "9.2.1": {"name": "vue-eslint-parser", "version": "9.2.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "preupdate-fixtures": "npm run -s build", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "fafbc7d38d38b31f5db529e2633af6736f30cce9", "_id": "vue-eslint-parser@9.2.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tPOex4n6jit4E7h68auOEbDMwE58XiP4dylfaVTCOVCouR45g+QFDBjgIdEU52EXJxKyjgh91dLfN2rxUcV0bQ==", "shasum": "b011a5520ea7c24cadc832c8552122faaccfd2e6", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.2.1.tgz", "fileCount": 6, "unpackedSize": 1068782, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8rmZlStcShew364qkpU8Ju4W06VNzBhKOyyYR/wCSrAiEAkKOge4I7kvinuAN5aIFqfwKXPCVIZ0BFmJBEq+nZ3/I="}], "size": 185881}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.2.1_1683417666206_0.7048660720654245"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-07T00:01:06.400Z", "publish_time": 1683417666400, "_source_registry_name": "default"}, "9.3.0": {"name": "vue-eslint-parser", "version": "9.3.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "a2d5182ae1d37216f3b978131910b9de6369f05d", "_id": "vue-eslint-parser@9.3.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-48IxT9d0+wArT1+3wNIy0tascRoywqSUe2E1YalIC1L8jsUGe5aJQItWfRok7DVFGz3UYvzEI7n5wiTXsCMAcQ==", "shasum": "775a974a0603c9a73d85fed8958ed9e814a4a816", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.0.tgz", "fileCount": 6, "unpackedSize": 1120923, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFeu6C2/npRCw69lTfF9gJQow8KW1WSUkXLEq07/j5mAIhAJhsgfW0nx3I9yIZltI1jXgAhiXwuw16dEUfr4VB98mt"}], "size": 194833}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.3.0_1684026476079_0.33993137911764415"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-14T01:07:56.324Z", "publish_time": 1684026476324, "_source_registry_name": "default"}, "9.3.1": {"name": "vue-eslint-parser", "version": "9.3.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "6aa8585df1722d6154890db9a820680f1b2ee61a", "_id": "vue-eslint-parser@9.3.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Clr85iD2XFZ3lJ52/ppmUDG/spxQu6+MAeHXjjyI4I1NUYZ9xmenQp4N0oaHJhrA8OOxltCVxMRfANGa70vU0g==", "shasum": "429955e041ae5371df5f9e37ebc29ba046496182", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.1.tgz", "fileCount": 6, "unpackedSize": 1121017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYit4UWRrWejHV5vFIYYpq1lU6QwYNP7ZVIfUTEixf+AiEA1nknTjB3FvGhK+vEWUTdbdIZkX2Oqy63oYH21/EthtY="}], "size": 194824}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.3.1_1686473097693_0.7631747205987551"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-11T08:44:57.926Z", "publish_time": 1686473097926, "_source_registry_name": "default"}, "9.3.2": {"name": "vue-eslint-parser", "version": "9.3.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "92dd3f306c30b3991a81eaca519286bc97b5f7ba", "_id": "vue-eslint-parser@9.3.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-q7tWyCVaV9f8iQyIA5Mkj/S6AoJ9KBN8IeUSf3XEmBrOtxOZnfTg5s4KClbZBCK3GtnT/+RyCLZyDHuZwTuBjg==", "shasum": "6f9638e55703f1c77875a19026347548d93fd499", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.2.tgz", "fileCount": 6, "unpackedSize": 1124097, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfa4nDQb/z4l8j06hgsIWnwykXI1vfIEw2+PMnZzClPwIgKzaLaahNGn0pC1kR8yZf7vwMTsLuO5ktB27d2ZXInlQ="}], "size": 195278}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.3.2_1696768614812_0.908934719688766"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-08T12:36:55.038Z", "publish_time": 1696768615038, "_source_registry_name": "default"}, "9.4.0": {"name": "vue-eslint-parser", "version": "9.4.0", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "4587eef81a037ae5e48ec96de34f3adffa1d4151", "_id": "vue-eslint-parser@9.4.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-7KsNBb6gHFA75BtneJsoK/dbZ281whUIwFYdQxA68QrCrGMXYzUMbPDHGcOQ0OocIVKrWSKWXZ4mL7tonCXoUw==", "shasum": "dfd22302e2992fe45748a76553cef7afa5bdde27", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.0.tgz", "fileCount": 6, "unpackedSize": 1129625, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEaLx8Ir/LOtjS6OOC6mHTvZbLV6ZmbqdOJRsXgwk+kyAiEAnYOBMnoYps2U+Y2uRCSM578ox7xuvol5GjwMgx+LxxA="}], "size": 196247}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.4.0_1704724413745_0.06982296039559288"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-08T14:33:33.964Z", "publish_time": 1704724413964, "_source_registry_name": "default"}, "9.4.1": {"name": "vue-eslint-parser", "version": "9.4.1", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "7c9c24bdf7c36a035d324608b2809c7f2ef0fc3f", "_id": "vue-eslint-parser@9.4.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-EmIbJ5cCI/E06SlI8K5sldVZ+Ef5vy26Ck0lNALxgY7FEAMOjNR32qcsVM3FUJUbvVWTBEiOy5lQvbhPK/ynBw==", "shasum": "3375106ba45f65c4d6f59ec6635a1f51d0726fa2", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.1.tgz", "fileCount": 6, "unpackedSize": 1134269, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEy5eDtXkv93bUvtNQU5pHwNOOsyQECLpLc23DEepGy4AiEA0WboPnxglzVWYShsUQnod2Z45oTGGMmNtHzrGF8iw3Y="}], "size": 197050}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.4.1_1705909022590_0.41369196182129864"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-22T07:37:02.795Z", "publish_time": 1705909022795, "_source_registry_name": "default"}, "9.4.2": {"name": "vue-eslint-parser", "version": "9.4.2", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "types": "./index.d.ts", "gitHead": "d79bcad8fba6f9e8cc4f7282a130a2a34f646267", "_id": "vue-eslint-parser@9.4.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Ry9oiGmCAK91HrKMtCrKFWmSFWvYkpGglCeFAIqDdr9zdXmMMpJOmUJS7WWsW7fX81h6mwHmUZCQQ1E0PkSwYQ==", "shasum": "02ffcce82042b082292f2d1672514615f0d95b6d", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.2.tgz", "fileCount": 6, "unpackedSize": 1135050, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCI+WdzNd0t+/c1tRj0D04loSjFD/9mwPtvx0uH+VtrcwIgNhIjX/sWVKG3Kzc51gdXaiqhQ1WerMPz4hlHezBGJaA="}], "size": 197191}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.4.2_1705966159832_0.1419322871457407"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-22T23:29:20.055Z", "publish_time": 1705966160055, "_source_registry_name": "default"}, "9.4.3": {"name": "vue-eslint-parser", "version": "9.4.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@9.4.3", "gitHead": "b0e0ccc6d302bb40c5cb496528536bd355ee5151", "types": "./index.d.ts", "_nodeVersion": "20.13.0", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-2rYRLWlIpaiN8xbPiDyXZXRgLGOtWxERV7ND5fFAv5qo1D2N9Fu9MNajBNc6o13lZ+24DAWCkQCvj4klgmcITg==", "shasum": "9b04b22c71401f1e8bca9be7c3e3416a4bde76a8", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.3.tgz", "fileCount": 6, "unpackedSize": 1136110, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID0ZsWQaA2ZHrPUI2m/UKxRzDD2qVmSwJtff1xgWkoIBAiBoqOW0J2hV3M6Q18rvU6USnP9pYq0o/BiWmS8LDIgrEw=="}], "size": 198221}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-eslint-parser_9.4.3_1717204333294_0.9929571997977744"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-01T01:12:13.486Z", "publish_time": 1717204333486, "_source_registry_name": "default"}, "10.0.0": {"name": "vue-eslint-parser", "version": "10.0.0", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.0.0", "gitHead": "7b006255972609abadf02b4b7358f3f6f80ead92", "types": "./index.d.ts", "_nodeVersion": "22.4.1", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-xGtmOQJzWUIi6opA7CfJTJsJyCRjibJKHNzq39yqJ/f4AJ+N7Ngr7lDZMl/9ePaobBnsZgifu+7G6bST+gaYJA==", "shasum": "a3a4e9c7fa76f01e0801e36dd41b67870a7c055d", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.0.0.tgz", "fileCount": 6, "unpackedSize": 1125679, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF4yBAmqRTB88oj0u5dHQ6lrCGb3QVM2ASFIk7/6ElJXAiAKlGWniqTJlTgnNiIkv95Tm2FD0ivU28EeZySXONs0KA=="}], "size": 196739}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.0.0_1741147025411_0.6110751624325994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-05T03:57:05.621Z", "publish_time": 1741147025621, "_source_registry_name": "default"}, "10.1.0": {"name": "vue-eslint-parser", "version": "10.1.0", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.1.0", "gitHead": "2af97668c8c783f4614928d1779cc07df9fbed67", "types": "./index.d.ts", "_nodeVersion": "22.4.1", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-ZlBmdu5DorbpnKQpVUBAuxNkyZmaK8vTc+849e+CtozTtnjsfigCmauQaBMP5ARYVXnaq7b87G+n4bMwrusF2w==", "shasum": "2c8977f894afea9c8a65c876459e403350b81899", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.1.0.tgz", "fileCount": 6, "unpackedSize": 1126622, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD5VIl6ZFVDgSWxGfTNlT32xDZCyrj7fMwEi6zDpRV3QAIga46B8hMsEXlT4E8p/bxWrnvNV0KBUkNKbw0iB2e6clU="}], "size": 196852}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.1.0_1741159888024_0.42805005792151896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-05T07:31:28.216Z", "publish_time": 1741159888216, "_source_registry_name": "default"}, "10.1.1": {"name": "vue-eslint-parser", "version": "10.1.1", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.1.1", "gitHead": "9574d91be036d050f058485a8a73fd2eea17b5fe", "types": "./index.d.ts", "_nodeVersion": "22.4.1", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-bh2Z/Au5slro9QJ3neFYLanZtb1jH+W2bKqGHXAoYD4vZgNG3KeotL7JpPv5xzY4UXUXJl7TrIsnzECH63kd3Q==", "shasum": "0d56d17a8e64cda088c8601dcbfc2ea51548e3d2", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.1.1.tgz", "fileCount": 6, "unpackedSize": 1126970, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCsvrR/43ly3NxYfrti9B+Ieia06hLlknhDG6z/jU5+iQIgBXb4Fg6IAxdHrz9FmNSCYlZj54nz/wwvy3andZcxe7o="}], "size": 196929}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.1.1_1741183166082_0.8019037341585031"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-05T13:59:26.369Z", "publish_time": 1741183166369, "_source_registry_name": "default"}, "10.1.2": {"name": "vue-eslint-parser", "version": "10.1.2", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.1.2", "gitHead": "4ddeeef46545eaac8eef35de14d5b26202cdd4bb", "types": "./index.d.ts", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-1guOfYgNlD7JH2popr/bt5vc7Mzt6quRCnEbqLgpMHvoHEGV1oImzdqrLd+oMD76cHt8ilBP4cda9WA72TLFDQ==", "shasum": "dd52eaa6873301c934256122b9578007f4df7898", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.1.2.tgz", "fileCount": 6, "unpackedSize": 1127458, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC+/ObD6Xj/yA9R5YW1QAXl1BCkhL4qmop0Yrbmaf5L6gIhAPhB0ADg4iO2QXwdCdoTlLKph3QGWTUryvECl15QOOyn"}], "size": 197014}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.1.2_1743728639372_0.5497623642141218"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-04T01:03:59.958Z", "publish_time": 1743728639958, "_source_registry_name": "default"}, "10.1.3": {"name": "vue-eslint-parser", "version": "10.1.3", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.1.3", "gitHead": "01ed265959a71d6ca819830529021e1f2e261e1b", "types": "./index.d.ts", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-dbCBnd2e02dYWsXoqX5yKUZlOt+ExIpq7hmHKPb5ZqKcjf++Eo0hMseFTZMLKThrUk61m+Uv6A2YSBve6ZvuDQ==", "shasum": "96457823a5915a62001798cfd9cc15a89067bf81", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.1.3.tgz", "fileCount": 6, "unpackedSize": 1127262, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC0O6+XMlEO5xR8i8CNbtUEbeHsm02j36ct2U55zGWHCgIhANc+44H1RN6+wJMFrWXczBilX0ulBxekTeNf4zkgIKYm"}], "size": 196966}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.1.3_1743822945191_0.2367899161823026"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-05T03:15:45.498Z", "publish_time": 1743822945498, "_source_registry_name": "default"}, "10.1.4": {"name": "vue-eslint-parser", "version": "10.1.4", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.1.4", "gitHead": "73dcb3e4b06b2a9e8aaf035751540da0d36c06e5", "types": "./index.d.ts", "_nodeVersion": "24.2.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-EIZvCukIEMHEb3mxOKemtvWR1fcUAdWWAgkfyjmRHzvyhrZvBvH9oz69+thDIWhGiIQjZnPkCn8yHqvjM+a9eg==", "shasum": "3992b13d30df4639f07c32ddb384a1232bf91145", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.1.4.tgz", "fileCount": 6, "unpackedSize": 1127636, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGknCYeOHjw2N3mIrs8weWaJs2dzIYjqWyVau9I+VgEAAiEAzq5/YJ6DLrYgdR8v0BED75xRql8vQkNRjUIsJ0ZqsF8="}], "size": 196965}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>", "actor": {"name": "ota-meshi", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.1.4_1750728596997_0.34813995258439534"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-24T01:29:57.192Z", "publish_time": 1750728597192, "_source_registry_name": "default"}, "10.2.0": {"name": "vue-eslint-parser", "version": "10.2.0", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- ts-node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea", "_id": "vue-eslint-parser@10.2.0", "gitHead": "62321dbd75a5b962400c0f1201c1a61a0917e765", "types": "./index.d.ts", "_nodeVersion": "24.2.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-CydUvFOQKD928UzZhTp4pr2vWz1L+H99t7Pkln2QSPdvmURT0MoC4wUccfCnuEaihNsu9aYYyk+bep8rlfkUXw==", "shasum": "cb53f89b14c7f5bf6a95c9532e3b2961ab619d61", "tarball": "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.2.0.tgz", "fileCount": 6, "unpackedSize": 1146032, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDGOFu7Ka4dLbJHV0VSdTDkb+vzADCRTDWS2xwbdfL8OQIgeSY2iYXduf4EC6rALBWVtL1FHD7UzA0d9iMzY79z2Pg="}], "size": 201653}, "_npmUser": {"name": "ota-meshi", "email": "<EMAIL>", "actor": {"name": "ota-meshi", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}, {"name": "ota-meshi", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-eslint-parser_10.2.0_1751371013571_0.9679888612441481"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T11:56:53.814Z", "publish_time": 1751371013814, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "_source_registry_name": "default"}