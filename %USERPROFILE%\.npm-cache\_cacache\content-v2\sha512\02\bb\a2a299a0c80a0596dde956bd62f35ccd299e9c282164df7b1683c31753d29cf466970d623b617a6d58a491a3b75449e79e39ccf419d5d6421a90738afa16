{"_id": "@esbuild/linux-ppc64", "_rev": "2328728-638e84fcbfdd3a39a4ab91bd", "dist-tags": {"latest": "0.25.6"}, "name": "@esbuild/linux-ppc64", "time": {"created": "2022-12-05T23:55:40.481Z", "modified": "2025-07-07T17:30:11.249Z", "0.15.18": "2022-12-05T23:51:57.493Z", "0.16.0": "2022-12-07T03:55:41.579Z", "0.16.1": "2022-12-07T04:49:10.059Z", "0.16.2": "2022-12-08T07:00:31.021Z", "0.16.3": "2022-12-08T20:13:53.052Z", "0.16.4": "2022-12-10T03:51:15.397Z", "0.16.5": "2022-12-13T17:48:18.380Z", "0.16.6": "2022-12-14T05:23:52.813Z", "0.16.7": "2022-12-14T22:47:32.974Z", "0.16.8": "2022-12-16T23:39:43.850Z", "0.16.9": "2022-12-18T04:32:08.000Z", "0.16.10": "2022-12-19T23:27:17.268Z", "0.16.11": "2022-12-27T01:39:40.005Z", "0.16.12": "2022-12-28T02:09:13.034Z", "0.16.13": "2023-01-02T22:58:01.037Z", "0.16.14": "2023-01-04T20:13:51.725Z", "0.16.15": "2023-01-07T04:19:51.065Z", "0.16.16": "2023-01-08T22:44:29.454Z", "0.16.17": "2023-01-11T21:58:39.220Z", "0.17.0": "2023-01-14T04:37:41.975Z", "0.17.1": "2023-01-16T18:06:28.395Z", "0.17.2": "2023-01-17T06:40:24.118Z", "0.17.3": "2023-01-18T19:15:11.892Z", "0.17.4": "2023-01-22T06:14:08.802Z", "0.17.5": "2023-01-27T16:38:26.398Z", "0.17.6": "2023-02-06T17:01:19.599Z", "0.17.7": "2023-02-09T22:27:23.884Z", "0.17.8": "2023-02-13T06:36:16.580Z", "0.17.9": "2023-02-19T17:45:55.441Z", "0.17.10": "2023-02-20T17:55:30.610Z", "0.17.11": "2023-03-03T22:40:57.290Z", "0.17.12": "2023-03-17T06:17:48.976Z", "0.17.13": "2023-03-24T18:57:44.935Z", "0.17.14": "2023-03-26T02:48:20.437Z", "0.17.15": "2023-04-01T22:27:41.811Z", "0.17.16": "2023-04-10T04:35:47.308Z", "0.17.17": "2023-04-16T21:24:13.965Z", "0.17.18": "2023-04-22T20:42:08.955Z", "0.17.19": "2023-05-13T00:07:10.180Z", "0.18.0": "2023-06-09T21:24:55.297Z", "0.18.1": "2023-06-12T04:52:18.402Z", "0.18.2": "2023-06-13T02:41:35.205Z", "0.18.3": "2023-06-15T12:22:13.619Z", "0.18.4": "2023-06-16T15:39:13.999Z", "0.18.5": "2023-06-20T00:53:27.358Z", "0.18.6": "2023-06-20T23:25:31.039Z", "0.18.7": "2023-06-24T02:47:02.904Z", "0.18.8": "2023-06-25T03:19:48.626Z", "0.18.9": "2023-06-26T05:29:01.930Z", "0.18.10": "2023-06-26T21:20:59.940Z", "0.18.11": "2023-07-01T06:04:32.691Z", "0.18.12": "2023-07-13T01:34:47.270Z", "0.18.13": "2023-07-15T02:37:52.453Z", "0.18.14": "2023-07-18T05:00:57.491Z", "0.18.15": "2023-07-20T12:54:09.845Z", "0.18.16": "2023-07-23T04:48:43.245Z", "0.18.17": "2023-07-26T01:41:32.073Z", "0.18.18": "2023-08-05T17:07:02.474Z", "0.18.19": "2023-08-07T02:51:57.623Z", "0.18.20": "2023-08-08T04:15:48.675Z", "0.19.0": "2023-08-08T15:53:09.525Z", "0.19.1": "2023-08-11T15:58:11.217Z", "0.19.2": "2023-08-14T01:58:59.606Z", "0.19.3": "2023-09-14T01:12:57.511Z", "0.19.4": "2023-09-28T01:48:48.465Z", "0.19.5": "2023-10-17T05:11:16.056Z", "0.19.6": "2023-11-19T07:12:10.767Z", "0.19.7": "2023-11-21T01:02:13.222Z", "0.19.8": "2023-11-26T23:08:46.826Z", "0.19.9": "2023-12-10T05:10:01.305Z", "0.19.10": "2023-12-19T00:22:18.532Z", "0.19.11": "2023-12-29T20:33:07.439Z", "0.19.12": "2024-01-23T17:41:26.472Z", "0.20.0": "2024-01-27T16:50:09.800Z", "0.20.1": "2024-02-19T06:39:39.662Z", "0.20.2": "2024-03-14T19:50:19.676Z", "0.21.0": "2024-05-07T02:52:59.345Z", "0.21.1": "2024-05-07T16:56:12.115Z", "0.21.2": "2024-05-12T20:33:24.800Z", "0.21.3": "2024-05-15T20:53:07.822Z", "0.21.4": "2024-05-25T02:11:20.668Z", "0.21.5": "2024-06-09T21:17:31.268Z", "0.22.0": "2024-06-30T20:38:26.008Z", "0.23.0": "2024-07-02T03:34:18.694Z", "0.23.1": "2024-08-16T22:13:54.241Z", "0.24.0": "2024-09-22T02:07:00.445Z", "0.24.1": "2024-12-20T05:42:13.112Z", "0.24.2": "2024-12-20T17:57:34.639Z", "0.25.0": "2025-02-08T03:02:59.412Z", "0.25.1": "2025-03-10T03:46:13.148Z", "0.25.2": "2025-03-30T17:33:43.246Z", "0.25.3": "2025-04-23T03:57:05.427Z", "0.25.4": "2025-05-06T00:31:30.101Z", "0.25.5": "2025-05-27T03:13:40.420Z", "0.25.6": "2025-07-07T16:59:51.435Z"}, "versions": {"0.15.18": {"name": "@esbuild/linux-ppc64", "version": "0.15.18", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.15.18", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-7cHBFsrP6QAv4tBoz6Y+IIXOEWZ0AHevHCavkO+v/FDmF8vlGNf/56EUaVR6YaPlc2JbN5JxhcO4o67+9uk+LA==", "shasum": "56794a79a1510930b50a14e10484e68f1b1397c6", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.15.18.tgz", "fileCount": 3, "unpackedSize": 8258054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5nPiKrIGZW6Hzuk8w+8e5hZFG3lWupFChs1/VoSM0/wIhAOzNPZ+usyXPrSMg3oR7SZVdujwu+EUaY8zdJoa57zes"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoQdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSxRAAo0USX010hkV+YRkXpHqueFMm3jGP0ix9r8QqPJFfGOkp1GnF\r\nHLJ4XD4oH/sIJwRB1NMAVTEJe2jwppNL/0EOQriz9z4p7YBHrpRintW87Tys\r\nz8F+reQxKh1bPyRY7ecuyoP5EfajKTM9P0Pxb2dSY3qbK9spXk50jd+6n4N9\r\nz9OtHm4FMlzo9XJjAHab3Umvdc2O3TmYI9IJ91XAf+NVpZvXAF1Q/OhZbJCH\r\n1r516Bux5JBrgeJLuwh8Bdps8FzpLnI3UcljrrpE7NFT1p7Twgm6A+pjuGg5\r\nFHR/FsFkih9ZDzSLw59jDW5ZhS4rZsG+mNDT7nGT6cyp2HXyfW4cq+QZsN8D\r\n7K7wyDVjYC4fiW5VHVIsgEz95fkTJejj9dicmR8WCexBBdKiRpWNJOOWogYy\r\niQnDJxYA9HtSQ5RWL43Z1Csj8FmdPDG7PjHy0cSMHR3/I1GSjnLblRm7poat\r\nVpXAcelbWd4rO41FmKguJ1NzBA14KjbE0dW5EZjCfZEB5zPlulqKuLdJ6Nnw\r\nHukvkwI0vlYX3Y6QxZHDdQbZd1aA0fisEtMiw/ouXzEjJufnxRRPpZvdIzFP\r\nRKhFTJMzo/tuFsTP7Rk4JMW7g9w3qbBX0dBXnT275plRJIhQR835Sdr3Tw83\r\ngygmXLamIITY1CKMWEJcXzofg5CwDRXnhEM=\r\n=NQco\r\n-----END PGP SIGNATURE-----\r\n", "size": 3228894}, "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.15.18_1670284317218_0.7784703535778479"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-05T23:55:36.196Z"}, "0.16.0": {"name": "@esbuild/linux-ppc64", "version": "0.16.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-rCk3uAfAFm+Bo4c9qBPVxeYo/gsl9HdexgAvu7HkZg6cPnXScstF0JrHAt1JPYmMba0F/9ewHFR7WYe0v/G1zQ==", "shasum": "54ad4d6d109fba582d4c7d832bccf766e997ee22", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.0.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCj2ju9u1EZcHgcU0NVsb7HnP2JzC2JnWjWL+whyyx0tgIhAIhRstkYZUyGz5S9kzpCyuY0mC98RAosUap/yP5RQ9vh"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA69ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt6g/8CIX9XqUfIs/8SBYO7nG9u6FK0GNVsWaFte2jLMf0QJuMOBso\r\n743xLYkcj0ghitRG9HaYpnWeH8UJPHoDunmjWje9q2tFChScz3bz98oIhFEL\r\nq2gBWOytNwWCJ6auO8ZLdafUjpU8MNOJioZ/5G7gdn0cEMPh3L4UC7rljWVU\r\nilfSDX52mT1IANoh+aMu2qa4Q6Kyn7p65nxWDWHGlJRZJODxi05BFmBH3hES\r\nUF29hipk7z1ZuwKfhRrtX27Iwk5zxmsguku/zoulHWGDEX0/Q1MGHn2bYcw5\r\nY6fOm7adPjt96YES3FrKW1kU8qhhTlxTJYCC5FN1q4JjVbTuU4979uBUh7pw\r\nBfWsEXwnKCprm/MDZjHj+3o2HsJ6DSqAT4yrzaC6YssRAcgkViVOVyfgaA8X\r\nyLKzdlbQMJGw1SRytce3fY7W6zy24t4BRqvBVMYNQ3VzS95dgSg4U3pt+EaS\r\nl8DoknNkOTFkVyVAcpZX/xxKxNFt297+S/Db41a2i5HasvRWgNBBea84kX2i\r\nA4CJBDKkljaqjcwh6v7UomK8EJbS+FKRwbaHctWOSuhdkfkiphRPYL9rZ6fQ\r\n6KCZ1EJjOHqOvx1x85rT3IQAZV/3xBFZRnsK0oYJ2ZNqDw6EDvuzRzlCTiP4\r\nk4iN8VISj8o/vo74PrUogAf+pXl28eif2J0=\r\n=EVCo\r\n-----END PGP SIGNATURE-----\r\n", "size": 3231582}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.0_1670385341365_0.01811997114369701"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T04:04:36.161Z"}, "0.16.1": {"name": "@esbuild/linux-ppc64", "version": "0.16.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-//BU2o/gfw6clxJCrU8xa0gxElP18HiAzS/pN1HKzL2ayqz8WinOYEzPOZrqJvkC4u2Qoh5NEiVd98wTr2C9eg==", "shasum": "8af2ab35166ff56bcabb2094e956d12b261db321", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.1.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFcZCZejR1GCcVnCDUwXGF0yK9zbb0iuneSW6iI9DpowAiEAvLTxQLeLY5DFjaXTz/5LVj+Wir570gDbAG4Nr5/2r7o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBtGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpcxg//f0WYPONsj4jaAdSHmfExEZ9SDfVvk+8KBncXI7JAvBCb+Jlr\r\nBJ7GMzBk3bCSiMVw+8eqKz5yvYO6yo8NqSpTVdhVSpQENVs3RscTDYZNzOJt\r\nXwn4E2+syr0IK0tPjL9IFzC1Vl6cIbuBu83r+HmjusW5YwK2fGHBjHHmbB9Y\r\ngIKFB/6wxX+/rPmIhkDn702bwlB+4HKa6oKy3PQZ8xltD2nrUPuvcS4KjjZe\r\n5dhro+ePGj/ITRNisHxPTtDPemaNFr093N53GJc1sBv4+bRJ/hwPqLVLSiL+\r\nTlr/OCwxkjjKK+AE6SWvDCEGBqecCAMvWI+5Akp4fwME6wt6IPZHYWcuMZTV\r\n/defQeFDYKMd59ImXMA604l6ZbPSF5FAIrw29pG367JQpRFx0sMP9EoX7gi2\r\n0Wk2m/jY6mkx9OnaWgJQrr0aGVcWcDUPZeKcWmRR8O7NK6/IAnviySW3Zb3d\r\nwNWHFM/VoKwWPMyFNRtqa6laRRdwgcRRDnHSYu6lfmsgGs9C6/3ggU/ctijY\r\nhM5WK4ZFvZanwH8HGvHnGAKgrpShzfcNFw+n+0cwn6yQqk2PPmXnjacoO6uC\r\nykx6laJZ089bCrFjjBPAe01w0yLbRl/vOdqvV5cxIhPG2nFiJll/aWtRKPRz\r\nDvlw5dHkOTLiuPyY3hsZAKXsTmVaPsx+XdQ=\r\n=fbQP\r\n-----END PGP SIGNATURE-----\r\n", "size": 3231526}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.1_1670388549754_0.20954333826645866"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T04:49:36.891Z"}, "0.16.2": {"name": "@esbuild/linux-ppc64", "version": "0.16.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-s4YuINcRxCA9TElEf2iBdG6oZWdNu2Eb6R9TbRBcZOTdcgdBKIinaVyEiQ8H6nmCafWCuuJT8u66zds2ET3t1Q==", "shasum": "db567dd114122bcb034910d79000ea9cca9db897", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.2.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcjgDn3norX2l42hf0MThf+tVIONtlc7ANOg7eqTwrqgIhAMC5zQ+7/zYdEklFW/hskCPkTVOj/gHL5Fllz99iN2Fb"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYuPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgEQ/7BogQ8HPEhwm/QDnFBP+/ZuuD8Ca6NF4XPFat2MuuRR8xFaOR\r\nL3YkyBwo5s+5DXoPxualfn/JTxjuNqTd+Q6Y59mnQRczQ4UNcmJq+wqlkfvN\r\nnEd9J3bQkvCTIrwJlgGb7QGoxdh55hD/4v5dvriYbZZuSsPsoN0FqKpWT1Ud\r\nxATCTYlKXGZiaVufmdZiC7FQ2lZwOpTDGitRheCOqIT2BMzVylUtDiAp3i3u\r\n5UZBRtexU/Iws/x2Z409SWweKL8m10KI3BKgRFu2pZXIuQNjncpF8omOiyz4\r\nja7Cr/b++GuWN4VwdWxZTVTlPqvQftkkuZJxJRldHxVJ/Qk6GSkhhasiIia9\r\nU5nVRaNpb+lwfhOD1vIY/vVIDai3fc+L4KEGnXBqrj14JmTlonAd493O79t1\r\nhn4q2/QsRGPJQtcFQe9nw/4yc8VFO73I7fcskm+lIuhWTGnLsbA/1a6LJm04\r\nofON8x9LB87GEBytFBtiAFii4uYEwgwQVJzHF01jbSgV3e/Nzch97wE8FvFc\r\nPkfUThaBEbIq5HwlS0zbH0GUQar5wEEnZc515G0fe90cBfH6q7BbWiZXCmAW\r\ntgHn0Bah9Nru+aLF5vRnEnQcJJzTAH8pRArSLnvvDvxep+Rahdx1BGsRP2Hx\r\nUYnSvWXSdFM7ChTaYbV6wRmpj1PK8vByyO0=\r\n=ack8\r\n-----END PGP SIGNATURE-----\r\n", "size": 3235051}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.2_1670482830680_0.17502672608772074"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T07:25:35.956Z"}, "0.16.3": {"name": "@esbuild/linux-ppc64", "version": "0.16.3", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-EV7LuEybxhXrVTDpbqWF2yehYRNz5e5p+u3oQUS2+ZFpknyi1NXxr8URk4ykR8Efm7iu04//4sBg249yNOwy5Q==", "shasum": "36c62e24eae7fa3f0d921506da8fc1e6098a1364", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.3.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBLqlJ9qLSIYPhuu6ASshVg6H6o3hEQVwXlKZGwpY+grAiEAmgcUnKGoVZpxtjd1WBdrnQGLYHRAcRzIBbry1Tc9Cw4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkWBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmyA//VaHO8B8D1soBlxvyzTbmYHlHe8Ylz0EefXtqabL+INyTTtS4\r\ni6xDl1+9Fd0h6U8/nHy4JbQlsW94BKwttqyZeZLwb+6NjGeVPiJZS77S2ht0\r\nDSTwaV3hdvGTKUS2rKA1DwGSw93OKLLAxm3unXESsQZ29vcNTi/5Kb7hEaMT\r\nDSmRF9ZWDq1Gnh3Lu2aJgj0Iq0IuSruV9F+X3IajjaZqKWXcGuheS16ppZTj\r\nTYIezEYvlyHl+GGRXsz/dLgh7f+7ANfsF5k38fp9nKdQX7gSAfmuaiGYo1bp\r\nO+rwqSulgrHUjYfIy/bxL5kX4uFUFOGcUz7BVNr3NGXsZ2ZbVAzIox5fwRxG\r\nlcZHnTYRRE2P9NSo39atpkS30oJV1UhmIP98wCcPBD+zSX4OsOCmM6HiyV+3\r\ntFNL2RSibksjFLbVeHM1NkoqTzQ8FJPHgivdLQ824FgsnMFS/T6aBR99RouG\r\nosb/d+d7MCVLnI3LG5iX53kQCJskwLNFDyI15NJFezO0ckuSxZz8DouDbddH\r\nfhR5BCm5fsutrJgmJmcwksQ3Mm7pqrPnltTDbf0Tv3g6N5anIPoA8BDFXRot\r\n0UBK5Bd9NSX7AeGjzU4ocd3GPJ+AEgju089ITApfFS7yRwq1/J4eI9V1XrCQ\r\n2aUOOB9dgYF3fMR0Jc2n2pAgotmjzgRFoaY=\r\n=vsyH\r\n-----END PGP SIGNATURE-----\r\n", "size": 3235523}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.3_1670530432784_0.8888419857676253"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T20:25:10.886Z"}, "0.16.4": {"name": "@esbuild/linux-ppc64", "version": "0.16.4", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-X1HSqHUX9D+d0l6/nIh4ZZJ94eQky8d8z6yxAptpZE3FxCWYWvTDd9X9ST84MGZEJx04VYUD/AGgciddwO0b8g==", "shasum": "69d56c2a960808bee1c7b9b84a115220ec9ce05c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.4.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCH2hHqUwW6aGyhTgvrieyPVhyfg+oA+0z849xopm9bbkCIQC8AbJe9dKp2dnd7JO0IocCuFbUUpeqKs8F4QxaCXAgTA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAIzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFMw//Tp+jcFpjoJ20BiakyccKTC6ND7X5Qn5UIVWapuMr8AueX4Yj\r\nZors5LWRsYfiCP7XJdUFwXbGERAJaQfC4fOBlebE0YG+6AnWAvEP1LwJo2eL\r\nA2qshpaGFegxWeXXNdQ1P6jd+VHiMBwLlk39KkpLeVdqeGFMaHBYp1KTYhLB\r\nqQFTqL9Vp/1YQxcucnxTDUMP6oB33pQOliEHtIr6ObAk2KLfQvM+XvCX8Yul\r\nU6aXBRiTvBSVC++wGiNE43TCH3wpDf8qBoxHXoMF/0ufFwomEVF+1jHFZmnN\r\nJ7vN3AAgiZwx8vSH4VJhw2RS6BwTzzsk2EuJg8VIhRIZtczw0xdQ1A9sUZyz\r\ntEkMkGOROKXkim/szheK6qP/yIUCdyO2olV7FRaflvsu7THpJtsrMmR6ez77\r\n/Kke6TYD9eA+yvZLf0wz0mApwoRwUFhBuk2WBgsCKxOAfwzOGuIUb5GgRokn\r\nlibNGt9zNGQKuDRXcbzDEr8iGkg6hOUEA8hEuM0SvOdeyALRvxnBp2xdkU1c\r\nt+l9Un7eRMS5mnZUCOd1Hj6jyNpp4aOHfG2LYBSmKz7ItZOwczpHOwcm/ld6\r\nQuIdy1ar4SAGfgVfN8cGDfEi19uZNrHBwFpOj8nVeROnQ/iPO3NLLv+K0OaY\r\n54+ZouC3dOSuDfOkbIFch2mFU1tMCO4vR6w=\r\n=+QZ/\r\n-----END PGP SIGNATURE-----\r\n", "size": 3235745}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.4_1670644275155_0.2746354265547608"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-10T03:51:22.547Z"}, "0.16.5": {"name": "@esbuild/linux-ppc64", "version": "0.16.5", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-29x+DtRGcYH0Sh3QSnoF+D2SYkHLxwx5AugoGLIlVtcVqDb4fEb654d67k9VcAR2RiTAYUZ764KXzWB+ItQfgw==", "shasum": "175d4a6f7b304c63686059861f276151d015cf26", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.5.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTXTYaE3UgSoy8UkaErkbB0gVA0hbGBEIFi3cFUlO5zAiEAwsHItnF3y9hCckNJvTCqFEJygtJQjzO4WiVHmyNvazU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLriACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqY/w/+NpCjigUWR0TAzNPrRTNpPybRKzcLl4DvOEYQErmIfvCLfTzH\r\n+OT24eMCdSxQyfGp7LZLCZBIO1DeZSu7R0iT5i//66EIoDJ4F1Ky3wQy8hib\r\n4IKrzXUT8SF2/ux/zXjrPjuqdljVWJ3HMODWQY4HU5jQc/rCw+lecjkdw1xW\r\nCmF0KXXeTFCrEfEGQG7zWCMLsSeygss6GTwgHNylqC+QEHtG6Kp3QKm6B5/M\r\nXSCPm7ZHv1aZIHGlobCJYPSu+cLlmEBRtxxhY05neYMYvPvGYYr+5YWYHJaw\r\nmvR2JAj95XchfLTimooIPz7rzUBL21wgAKGJsQlXDMm6WSgmFy6tCbqnflia\r\nCK8gqS09rbNvL63O8svVHDuArDPkGKxJEW0zszALbLEWfT+ooIiQruAoDJsO\r\ni2SfcouaRWGT5R6AoEw8Xoci3VmFVT+LZEeeuSNQ+/XPT+7IHz2uNSDr0lf7\r\n2MRXT4vpUpRj7uVSdWJVquaMvCMlZhSdMoKTXoqBiCkiXMegJoAKc01mh+yY\r\nf2YP2Jah0ZHmhvd8F5WCXIPIxOlILqe7D0HRqj2kqHWOJXUSjHkT/c3PkDt4\r\nPdQmtfV4suNuJwYYl/jlcD5PDpRy7wzx3U9AjdJqTEXL7fLSv4z9Y0yHA6k6\r\n4e3WRU/SJB+OD0xQrFq9OsdcOQVtf68wgSM=\r\n=wlvW\r\n-----END PGP SIGNATURE-----\r\n", "size": 3240069}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.5_1670953698046_0.7790394590143697"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-13T17:49:14.430Z"}, "0.16.6": {"name": "@esbuild/linux-ppc64", "version": "0.16.6", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-zwIKMrYQzh59ftwiuXREcXwyjvsRNLELOgdIE17CwTnc5Xxj2IR9Gi8NvQcMTquFoGaHOh8O7F2zJ3vU5LQEhA==", "shasum": "aa2b0fc6ab0dea618f23d13946032d327da7806e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.6.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE8QT5H6VduOHx1p46zbj/74vH9QBB7SY3r2X4zRG4R9AiEAzdCUmTBf4IDSqTCpRMXrhPlF4OMCWwUoypMDebjhb3Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonJw//Ql8pU/KBpUjMrsraMH20zDQ99r3RJPDFStLiov/A6F5n18ZN\r\nxf1xg97nlOD6g2iOK7l7Emk6wt7Mlk/bdhtJfpGqRRB0zczzpIs/sA9SIf1o\r\nVBZufF7i+XOr8zMPC6gMmjMwIKhEKZ8UeL+p/cNQVMNRByKoqiUvxKdC3msK\r\nUqndJUo43z6Pa/YjrMm9o/pdhTHsFAcrjX30gHEq0hqRZdL5aXUlyMl1t6RZ\r\nOUzJRNky9mWYA00mKuoAr+72tZsM+wPwvu7Bgf+L+AtZtaAfrxQzlBvTPUVX\r\nUEY0iQsbid0AQfP8lc7QIJ40KuUZs2egwB2t3q6QqumUSAR9IFjBkTClOfez\r\niPOuiee+Xoa5JdiMcWiktnoyOtQe3QFFrS7LereLRqoLak92NFJPtaox9ULV\r\nb2wIOaLounDRGkz1uyQ5ufSaqBtnO4a9/jHgq1/fXvkD1T6iN8T2w8GjDVtB\r\n++BkhUNWlBy/IkGkBkvciYKnztR+vFsTb+tmsNGYHb1ykxUU/rR1BCBrD2OO\r\nSVZSaPYzGQjwQlQrqoJ/VAupGFwLgR644z/PD2YmBCuVYmAJgte98/hs54zA\r\nihbnMJWP1DKbhVUWlcBbasTo5IBhT0RruqBVvBbrWIlYe5poc2vYHSKx2zMa\r\nfA7wn4SmXLM9soFrBMeGGsrEkIHGrSPS1+U=\r\n=cjUG\r\n-----END PGP SIGNATURE-----\r\n", "size": 3241924}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.6_1670995432535_0.5085516905245016"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-14T05:24:10.323Z"}, "0.16.7": {"name": "@esbuild/linux-ppc64", "version": "0.16.7", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-WM83Dac0LdXty5xPhlOuCD5Egfk1xLND/oRLYeB7Jb/tY4kzFSDgLlq91wYbHua/s03tQGA9iXvyjgymMw62Vw==", "shasum": "72a00c9788f3ca5df56ecec060d5b92f945c9a2d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.7.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4dqdZ2gmJWUjqBmRci0aUAk9qKRoxa4cwgvovE9HbyAIhAJrSuja4sUbsPuiQkkC5bN4aTccGm994EkUr4eJfrXNF"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlKFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+BRAAnkYlLXYxMZa7q9cnegua9PGwVEH0KNfnBHZjat6JKzkzbEJz\r\nD/bIHkZVAQE3v9Cpt5ruvJFvKf9ZAbm3yG3ve2pc0g90uQHWpYZJFMrCQH4q\r\nL1U80EP9Y8fAsQk2Sh93oHd0C7TNS0A9oMvusU5Za6PZqSet83ESDx02oVo2\r\nNppQ4lwBS+U4LvcDqjSS1dpfFPZrJNOphdoxxrFpyCNHnjVANIykzkf0A3EW\r\nAfYlejFdFZqmjh5R2ZQPmQycGpRhGoZOri+3C9604LsWrlsb58xoeNIwUIvG\r\nOc8VuSiOBUaTMnc+W5eoSX+HM8/NJpNxxyw1ZTa8VT/4CksVeL69qO19lkDJ\r\n+L4ESsVvuT5Nr56ixJG5LjjKYNppyK4sy0x0nPt4HfqxLEEhjwJL4GvBg4Uw\r\nEEWY/DKxe44yOFBgboP2pdSzy6+nG2F3jamuyOmak+ru9T2X2GdkuRSoeCso\r\ni2cz4Y7HBUatcL41usmLoZ2lvi4UCg7pcsaR2KLZ9xdFtCgvPx9FAIm1Oqqu\r\nL0NFJVzfBwXM8mWlg9h1yMJhU82ouvHphpI26TYt7DV8ZtedNAi6jHVDt58u\r\niJrVO0W3Rj+lcJ7kkE1DYev9ixnVhx7V38930Scu+1dd0EG8DnK7kTzc7XCT\r\n/uydbKR3Lw+uhF/x/GACxwZoeRFrxyBGySI=\r\n=DnXq\r\n-----END PGP SIGNATURE-----\r\n", "size": 3243011}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.7_1671058052707_0.09380647684996402"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-14T22:47:37.960Z"}, "0.16.8": {"name": "@esbuild/linux-ppc64", "version": "0.16.8", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-fNGvIKXyigXYhSflraBsqR/EBhXhuH0/0r7IpU+3reh+8yX3VjowjC/dwmqHDOSQXbcj+HJb1o9kWYi+fJQ/3g==", "shasum": "ed3f4aa0a835fc385554711cdc0ac785d2f3856d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.8.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeTH6D9znc0T+KaVfnPlMtp93BkP0i00AoNXw4c7+IrAIgeSJYwYN00Y0UwpDeReOgE26cWIPL9xvnQHtg6GbiQV8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQG/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr59A/6Ap4HBKwWmub08I+6v00/EzbVFqE2gn4Dtb+t0yL9QF1v6iTK\r\n9wtAFNpI7+3K2wUz2JKB9i9s8+21xmtlP+KZA3X1Fmv2RldEEBj+ZL3od4JF\r\nR6KloBrj6yq9ePYihnHeuFo3u2JyXcEV6Nllf7rWYjz/pn8w/+H4Zpv1sJSp\r\nwYlJAVbU+Wn6DDCTVyBCkQ9QcP1pF2zlq3MEuSHISdW/IKW8eZmG8n0nWt8B\r\nWcd9wZN1I1wg729UEJnAs32UT/VBHJ/HgDXIaLqRthOfYQzPJpJQhUlST/jE\r\nOq68bE94AKf3cWnShXZnkpryaBxnOobxrMUAyGjn1yfRUJPpPBTR7yDyhFzK\r\nkrqHDRmI4zD/efRy0CLRLD2DFrKdS/HwK6vJB9VfCV66UUia0poul2dfpDI5\r\nF9Ek1d8uNFYvnfBE74XRjrtgT2vB1Yj+4+WgUt9xMDpmgVk72k5SeRQ4idiJ\r\nCpeYgjURycPaIc9UvvCx92EY00qbQR+ADd7sDH4BBpKjBJJ17Zfe7N48aYVN\r\n9rKIHQQXIIM1qD6HgHfFfvkaMDr3PvJXoopOl0jObvTNeSh8PiG3HMLMI3gB\r\npb97wGEQqjqghqyzkg610YsdDnB+dKdFXr1GEliwbZ0LDBxaKIDpqs3hHAjP\r\nXh6wPbO/KKYwO38xGpuCK6R5nO7l9h9K/1o=\r\n=PzIk\r\n-----END PGP SIGNATURE-----\r\n", "size": 3244901}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.8_1671233983587_0.48959912601823685"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-16T23:49:20.562Z"}, "0.16.9": {"name": "@esbuild/linux-ppc64", "version": "0.16.9", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-k+ca5W5LDBEF3lfDwMV6YNXwm4wEpw9krMnNvvlNz3MrKSD2Eb2c861O0MaKrZkG/buTQAP4vkavbLwgIe6xjg==", "shasum": "ccaf759fc4f7a5fe72bdac05b4f5bf18ef1fe01b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.9.tgz", "fileCount": 3, "unpackedSize": 8258053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICmncDUx+uQUP3HWL09wxntKqe1UDLwN/7qHwplK0uEFAiEA5chs9DvLXgVymZvU4m2N17XeMk8vh9y87/IahN6fcz4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpfIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDMQ//VNfci+/WFJtmbwUzNSHrNhJQ9fbjgECGqfIdHfjq6DTS1Ghn\r\nVaXt9fP0C0uz5Qw9JmLPfNkJch2CO5dJtbV7NeYWYBnuMIqdv1ADMotWzWyP\r\n8KeIoGpKA0N+H3syRGdyvZLcR6zHpU1Q//qd9Ggkg5s4iRqU6524N61eQJvq\r\n4mmUTRVG6mRXH+Pt2khlVRZGOmFgcWA8+icZJR6shMSejnDZ+nQKmxAIopm9\r\nGTh7g1TdcxVo/Vc2BixmTSNmvI3ZIAo8mk96lFedpB+UEk4/3OiPOlaE+ZeL\r\nWpO79n9K3P3Gp1v/VhR4Whh4fvwnxfLX9DBPJFKXytAZlBArEXBDLfHYtqoC\r\nxMguickOhYSCdjeh9rY3BTWW8fqU3O9tUSJDJp/Hm+gC+3W3OzJlXgBJQpEw\r\n29O/acHtJDWQULjz6pBcyY6jlj24QILHdWDz66dXmDaNRvT2IhGlrNp8LEdZ\r\nDLP+IK/gzeqtVTxi70VVVMqYJCfnRQ+z2CGmupyHS0KycOcr770tY8FhH3ir\r\ngXBsbNHh+J8447ZtPaH0sbAuuiazgAXF/rkqte0bgu1Q4reM7OP2csLTyUKi\r\n2XxX9GkhVjbn3J6nYfrzHSTXtRh5Y6gu/eJfkMzeTGB7S5qYFkuBLuoWEfbY\r\nuN4gPsrIyemzU9WV4LrqoqHMHRLRUZ8WcPI=\r\n=NVVr\r\n-----END PGP SIGNATURE-----\r\n", "size": 3245941}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.9_1671337927714_0.3582769078993884"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-18T04:32:19.318Z"}, "0.16.10": {"name": "@esbuild/linux-ppc64", "version": "0.16.10", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Dg9RiqdvHOAWnOKIOTsIx8dFX9EDlY2IbPEY7YFzchrCiTZmMkD7jWA9UdZbNUygPjdmQBVPRCrLydReFlX9yg==", "shasum": "11da658c54514a693813af56bb28951d563a90c3", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.10.tgz", "fileCount": 3, "unpackedSize": 8258054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCURUprsVrpR2i7rn9KHg3ypSXu7aHKW2XCxSgFjglvUAIgRgiPi+R8nUF+IZvLDSEpbq/7VJAArUdrfmRQB3rbLpc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPNVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowIQ//ZA8o2roZLOZ/HJtHMhcVO9UVaRC+slLWbd3CaoimzMM1a5Au\r\nqfsl20fnvY+UwyY88PZrnci/eubwYkMfbjyXQ0bZ/2pJVlYiRDDFVK1CnoA6\r\nrU8IUyEA6DfEnRFWhJkMh8IQMgKoJBUpRs/dRoBid+rixYV2jWECRCt8eovq\r\nsLdNh6pPHPDorhSx5pNiiHTwsKPpQZwdYp7PyMVJ694kcvkshsSQiiygQnHZ\r\nYsypoc99N/5PF6kv4RieueTPkSXbRfO4Zh/VArwBuW4BAu+zfRQvA0AfMD3U\r\nin02C8JmUNttJ/0jvrQtiM0n7UFA5GDMqHJ2lgPWC7qlaqjQ5OY8hLKtL7uU\r\ncODVyRACb28PWkvD5m9sWaHfic9RL20d2vfrN37dbBbbMmSn4DQzuv+WUL40\r\nX5BKnkluh0zmCbOq5FBxV+cajGjyJBmY3ytLSIqiZgl7kR/Tqs99M+V0eWg8\r\nCcZhgzyROYoE31+Z4TnZkD4oLIsnsIls8J4M3I5pPMbGKipRVX/qYiL+DYIq\r\ndz9Pc7GIuroVcXe3LJEWXfhO5XRUOtAbHt6mu3ib3YbAkCgU/r25SQBbDPpk\r\n2QrECTLKR8GS4D5oDFfbiGLdKuSygVx8yjKp3av7GnMI902g3TPOUOmVwl4t\r\nYU1XwD8knDAP7j8OO7w1hiluE7EwGXwWHwo=\r\n=iwAx\r\n-----END PGP SIGNATURE-----\r\n", "size": 3250213}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.10_1671492436989_0.6544792970049262"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-19T23:27:23.506Z"}, "0.16.11": {"name": "@esbuild/linux-ppc64", "version": "0.16.11", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-NUMtxvb0j41UL8yf8VnTMNbCQxKqIPmF0Wf/N44UrxpKE8iCNmWT95Wt98Ivr2ebHdz+V3kptlgBuZNYcJLI6g==", "shasum": "9e49e13b7d36dbd318bdedc99b73a58fa914a580", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.11.tgz", "fileCount": 3, "unpackedSize": 8258054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmzG3Mi32LxJy1Lpzc5Ud0ZZqOYyQTo6lxdoCdi9+I5AiBl3UHBSCbgL0ISUsdmD2WKK7t67qEwVSDTVUDHDYKfLA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkng//fUFaH1amauRCKWCwpF2rLJ7FlJg6a2TJZ8zfBN6BhmFvGgaS\r\njT8uXRLODqs2GHAE3joCnTFSHnINLuGJnxiGkuxj5PderPve32a8NGugnH+2\r\nF6KectsDOM3egwMVEjcu6fI7vUZh4Ai5dHcFu59dukA1+axRJXBIObVFVq7/\r\ns3lZsloW7nzPW4bEQ7Jixp4UOrZOq2q1Fkbw3M83QfaysoeYqtnyY4JN6Oa9\r\nDoFavSOgknOo+7HqLViG4louh5q3fNFbC8dRTzAv+m9ywm68mlsGvDUy7cIl\r\nE/Ud65gE7czFP7uyuhMOl1YjerhgGVWiVYnArx2eE2okRjUnOjc0INTqGtWu\r\na1jJlBfwWl74gdN/wPn7Bc6wj1Agaks7j0q4FK77G0YnthWLL57a0X/dHQX9\r\nOOGV5pb2XS9wk7Y5WxwQaTQ8j/T3ku/TKnOyu+YowLx+MLOGLaCqPjN6Ptzp\r\nqnamIoxg3WJ72Nlq6m5rIjgCn0bJqvrtNDcZ1ZGkHVRrD31idN7VmAdQHk3/\r\nxMnKmCXNz2oGQPhktOrUj2PL7T2VrVl5Hy7UG0bmVlP80il7Flu1Rf8qdzFQ\r\ny9XphC05FfUQC/NPjKv8NfzJDhXwhnMoATLZgHQT5NG/VceaimtbDwYyoeFE\r\nhVobi5GDi4QVl6jhsiyV48Y9Z98BK+Se7GQ=\r\n=QFRa\r\n-----END PGP SIGNATURE-----\r\n", "size": 3250466}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.11_1672105179726_0.0755170604426103"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-27T01:40:01.299Z"}, "0.16.12": {"name": "@esbuild/linux-ppc64", "version": "0.16.12", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.12", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-/C8OFXExoMmvTDIOAM54AhtmmuDHKoedUd0Otpfw3+AuuVGemA1nQK99oN909uZbLEU6Bi+7JheFMG3xGfZluQ==", "shasum": "b033a248212249c05c162b64124744345a041f92", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.12.tgz", "fileCount": 3, "unpackedSize": 8258054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuac6sSESaa6GauWpV/ae5jJ7p9atbRJtbS9LwfqQJ4wIgWUw3dyOj3KVI1F/QoP6vm0XygzlDebo38239gabK5Bk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6VJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMyQ//eJxHbSFsHat9QVKl+vKtyU2NSiOnNgRPC8k/XlvaPuf3U79m\r\n2AG8xohgqaC1WMv7Zscqfz2NHxQSA4d9uZJASv17LeFRegujxdcSiHPXsRag\r\nzs3X497hI2nzkslvG+bR02+ADT4NPHbu4DlmWYAq7hoWX0gRkDMHg4aAb92R\r\nIIezHzyQIGbEnsiRw8vl/9CgoivGO6W+TV4APxe8/8xe9LjN9Jgpm0SdKqGm\r\nBYUOg/6qeBfxe1esSwneozD1RjNwCNy+2ftnGlt50aIkUJdrFMHT8S+le/OB\r\noJB0Mg4Tl+ZOY5+wbLYm1nQdaiyk6PT217P/O4ItMMwRqVUzfjn3U1FBzmN5\r\nWZ6NkHLuAM7Hjgi3pBikcqM0RsaYmcbBcI49IuxUkR4ApABCMjA7L8WGp9KS\r\n/PO7xt4yaOqBI9Q1X8x4hWZlnSfp6X8A2GwNGbTmBXzcWiWlQYz69BWbamdy\r\nbDDBdS5YrLTTHMIowXX3dQ11hQRjl9Cctai6m1Vy3x7D1EVNcagN0DbTpBIl\r\nY0Rh3AimQbVee5VWkOVkg9yUU3fBHizSLYZwEUtF8s+5g6UsyFveW9w0i0GB\r\nyRTbxEa5e7CAgRREZGnWXWHlYgd2UZ3ZDks4sepbvEt6BJHa2tG/umruoV+3\r\nGO8z3EMFt4uQBX4dssZ1H8UMcS3gOffIJPc=\r\n=mGzN\r\n-----END PGP SIGNATURE-----\r\n", "size": 3250532}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.12_1672193352754_0.4209657104312352"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-28T02:09:20.026Z"}, "0.16.13": {"name": "@esbuild/linux-ppc64", "version": "0.16.13", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.13", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-cVWAPKsrRVxI1jCeJHnYSbE3BrEU+pZTZK2gfao9HRxuc+3m4+RLfs3EVEpGLmMKEcWfVCB9wZ3yNxnknutGKQ==", "shasum": "32a3855d4b79ba1d2b63dab592cb9f0d4a9ba485", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.13.tgz", "fileCount": 3, "unpackedSize": 8258054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGRkNZqKvuSSstCuBw2S2+H9aHMezVhBQp+xBrELFJQWAiBnpwMnBrTFVYTC66J/ClW9Q+IBo4REFKPtnyLdJz+j0A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2F5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7bw/+JwnTGa0nZHUp6kRQnBdpY44RtuTAEnDmIxZrhkwmkAroKdyQ\r\nfX/VVoeRjdvMSLsvQWJgpk5oZNM1hE8cFU4HYgBxlCwIKSKjU7qlzA/HC9/j\r\nlm4jcYpDv8MmVKeDtZU95a2OZHAWmNJ6d/CJWsqAWWAY1zFChGGjicaYHu5u\r\nR8YPrdXfEwAn7VMUAGHNkb1EIaTWjAKapeQdXQnsY3BSQqG9+Q/Fi43h7Wv0\r\nLOy8Bxw937E14C1eAvQG9Vj+93+R3pR7C1ZCGMnRil39yMKNi5/AnowRKjUg\r\n+I4pBgDdOjbK7Z69nLQziy6mfAw1po11KL1JC3Lbef8NljMKh6fGd5/hDcLy\r\nYpEmv+CKn6iiCHeiz/ciHqwsTUk9g+2ZEm3bdokxIqFQR7xCbcGXJ1qVSdrO\r\nBhbKsWH3TmsunqVWq8R9/4RCmHkrLr/NbtmikyXt4TE3zSpngg6tLfpCWmpD\r\n9YaZ0RePNZeYS/AVmQq0Iany7/xuUn0muinUHz3nteiR+lR6+J7jIC7j+KNZ\r\n4CLgGWQie64Z3AyjbSrX83cbxOJ0YjSqsHz6Nf3ICXpVM0yTvHshNTQIndUo\r\nbqonmD+MBuKtvImZclryLwZZR7bWDER/iV/Cqy3kUfbsY3XHrw/Tvd+8svyn\r\nKTvrTPxZIiCSAHUIzNw4VZKBqAnuqDxNk+4=\r\n=s9/A\r\n-----END PGP SIGNATURE-----\r\n", "size": 3251334}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.13_1672700280752_0.49729518592577615"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-02T22:58:08.276Z"}, "0.16.14": {"name": "@esbuild/linux-ppc64", "version": "0.16.14", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.14", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-kclKxvZvX5YhykwlJ/K9ljiY4THe5vXubXpWmr7q3Zu3WxKnUe1VOZmhkEZlqtnJx31GHPEV4SIG95IqTdfgfg==", "shasum": "3fa3f8c6c9db3127f2ec5b2eba1cec67ff9a9b8e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.14.tgz", "fileCount": 3, "unpackedSize": 8323590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHu+x+bSRKoMyOwSrjaEr7bHF5V+sEiN5DyVhbnhWDaHAiAaUXHlNUsD6umMzMvCmbaI1u6UDE9oZNU+zJpW4SPB5A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDCA/+JKs1h3P5JyvvGQpgXEom7FbtIE4D7EVJwXBO/xYwW+gs7TyA\r\nTfqgJpe9AAggKu9cn0S61PKwx65RDLP+7NpVC6gx1e9TUkOLewW5UIElhFYe\r\nhLafRFtrGDIsIXtcbXgRyyRh8BBpIClY+wZc0edzooT39vVIpFzuqIu4/xV7\r\n6Wnm9pmmRX+6SD0z3YJcB1izySSB3P0stGsrgBr5gJa5B2TpnNrAgwlY7pTA\r\n49Vlvwn5MxP3lh2dYs38rnlCsA8/DKL0C8kd3fhC52saB+xm1FF8aNLhl+Dp\r\nd+sp1yM16gaafMzNmTtzrjo6i9x7yyR5PwwfzO0QDxHPeTN0L1G3ui3C1/I8\r\nvA12O1YjyafqdN5vyf0Aldm87qiLfZmYoEd5gMJwqF6BDBy/77/36Vz4127p\r\nsx7iP1Q5bDJcct7rJHGBkUYzhtVV+xKpTUfBdr44SIJR9nJtAwylU10xwVqF\r\nbA2RYuvcC/3uAyoHOagimg+eWdbK2DVgz+bCMoeIW6KzC9oBJQi/opiPIKkf\r\nVaPBk/C7Qb1HbJTFiaQN4mys+s2vXfPUVgdXk0RwQruTuZkMumI+YnsqQEWT\r\nnonf3nTtQ6UcjJCX0ioVIDKKD/1MV06WL2kKVjrIOF8w99LTtyt59oN0djjl\r\nDioSSGJnvAMWRJD2iH5ZqrjvEM5h+w6spTo=\r\n=yP20\r\n-----END PGP SIGNATURE-----\r\n", "size": 3261076}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.14_1672863231440_0.04364690586626074"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-04T20:37:00.683Z"}, "0.16.15": {"name": "@esbuild/linux-ppc64", "version": "0.16.15", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.15", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-8PgbeX+N6vmqeySzyxO0NyDOltCEW13OS5jUHTvCHmCgf4kNXZtAWJ+zEfJxjRGYhVezQ1FdIm7WfN1R27uOyg==", "shasum": "51c7ed8fec6f9860716cdb1bb86835bdb9206108", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.15.tgz", "fileCount": 3, "unpackedSize": 8323590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqu9lwgUClZ7z+cpes+YBExUiehapv+vQqyd1uN5pz8AiEAyC2yAOPVnhq+5z7zdQ+2Pl5rMhHiDKBjbtB+KqkFexQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPLnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsDQ/+Jkd9wuggqJrhonnD5Cq//0O0EHyfOVT7QPwOGyQXAWCscgBi\r\n+NZKHx30HmJ5vFhOxVY2NbQtYmCth68swFeJA1fTTJfCe0d2z9JRuzIq+Q8r\r\n0rxz2M5sw5KnM2aezixI9yKmWrKdHr1j3qbY6z5A5hRFM3efM93rTOsHQHeg\r\nlYzZQ78VIono4ULlpYNVvRe4BKUluIFW9XyRHd/nx78l38KzsIShUe2XDiTn\r\nMktuhlnFA75Os+UwrleP6JY32mDLjbV+GG9lktqFs3HUuhvpqSvvK67twwgw\r\nnuH5/X5Ziom1+XHrDNlTgLfHYfF6e8kPHsv/X+szBptglFIkmzxYRe4qGOx2\r\njQ32efa433pL6jenLLjg3VcSXkSOagxo5YASuc1T6bzQ1W2aEuSpMCTyOnLO\r\n5c0Q4J8VDLjkO0yet2GJIjVO6ZdSPF84fQtJBUgs6rKEU/6jrDunwg84F/RR\r\n/wj5yp/6UmqDP2cn4+0OsbJUvKZdVkY8C+JDGPb9hploIhZIDzByWv9WV2PA\r\njQaV8LfPlX1HEi0wopfH1rex6S/tMYiXccXokYCIXSTqIVNwyKhSm1cXn20a\r\nr3eBDCMOqkMcHi7nXdu5U/TfyNCdUNlG0aSnX5WKczZc46nj/aUMIObDQS0q\r\nLJOYB2cVBb6sBcdM3tdTrBlDxWNANIXM2dc=\r\n=GBTg\r\n-----END PGP SIGNATURE-----\r\n", "size": 3261268}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.15_1673065190805_0.8970535279525516"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-07T04:19:57.641Z"}, "0.16.16": {"name": "@esbuild/linux-ppc64", "version": "0.16.16", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.16", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-DSl5Czh5hCy/7azX0Wl9IdzPHX2H8clC6G87tBnZnzUpNgRxPFhfmArbaHoAysu4JfqCqbB/33u/GL9dUgCBAw==", "shasum": "24a0013bf727830df44fece571172ebe31b5c5e6", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.16.tgz", "fileCount": 3, "unpackedSize": 8323590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBzKePIQ1oVzsTfrexwEU9dVWShLQ8WuXMRFXeyw3kyRAiEAm9CxzFjo1FTwRghOBYHR6L2dk/aM6Sd29J56r2R6ywk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0dNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmon0xAAmxm1RLzz9yKWBLZMf1YijszyMuLEgyk6gYJ+MTY2r0vZqj1x\r\n6lMSBu7e30KuVqPtdrc8IRMrJ5gOmrUZruZ8k7CspylahJ1LJyDmMGNxdCgz\r\n+ieLPE2z8MgvSCBQ30/drQbwX+1Ai94+zvARvNFbTbkv4z36abx4SppghUL1\r\n+mzfCKiJCN/jgVhzWecyLfcH7J+DzXkU/jSE8DW8r5Fqj5GMyxz7yh8WcAUe\r\nBIzreWivrcbVu6DaEtEKmSbYhGLUiYRvOdg+bWq0aSNACZyxA0PDL67DEJ4H\r\ntZUAZBI860zg0ihd+gIzCExu+R/6HrbA1x5ZPk4Dy28cvMqEhq3X6nDiwJVu\r\n0aRBpx4JEJrNATl6xA1aPk0VkWh4FVe/sUJe5rlDG8YYaQdm8otAZ65NnoA5\r\n598W+ePbKy81B6+ukwRFmz01p8pQeNr1zfGxSeOfxjF+Bx3YwQXbaF/2YJhA\r\nehBGhNr8OJssVpoPodmFlFTRJHuhDUIg/U1omzvY7snz4EwUG/2CZq+kXdg6\r\nURaELAvGIXH8FscMmLUTUkH6y8yKu/1/QnjeGeH2bYDrudmLTlakvgfnOGqr\r\n36tZykAVSGP8DHPK4x1aclItqGuqk4tqqcDOVj+9gIwqeHT3724ANJPXf5WW\r\niUosfXXxtQL6E7w9W4biBowFfTFaEsVqUFM=\r\n=mxL6\r\n-----END PGP SIGNATURE-----\r\n", "size": 3262090}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.16_1673217869192_0.674854180170293"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-08T22:44:29.454Z", "publish_time": 1673217869454}, "0.16.17": {"name": "@esbuild/linux-ppc64", "version": "0.16.17", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.16.17", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-dzS678gYD1lJsW73zrFhDApLVdM3cUF2MvAa1D8K8KtcSKdLBPP4zZSLy6LFZ0jYqQdQ29bjAHJDgz0rVbLB3g==", "shasum": "206443a02eb568f9fdf0b438fbd47d26e735afc8", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.16.17.tgz", "fileCount": 3, "unpackedSize": 8323590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgq9F2vTfo1cliccbtMuSqCCkhiHEA2C7VUwbqa5LYEAiEAysQ579vuyVJzhd5GNzHyY52VebWC/LY3t2HDRmvbD4o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzEPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyXA/9GvX/J6/hdJsSe1pEbiPG6dxXQEO46b7gWAN4Y1m7LHOWRmo/\r\nqpMDyT2Kp/na0CVnESUhObvPrSUH4duBvxv9+wJ+ybPi//UaPG6i/YsBdive\r\nlqlbwAWTZRHx65HjI7QFrDx9vegaJaRmKKD5n8UpuPdvRereH9MB3zIdcvF7\r\nwiOA+BM0t/D4xR3SaatuRnXi4mMY1IAFjE6SXylu0+ymYokOiKIec3tQNex1\r\nbQet33XAOicCtvaA5hKYx3KPSnsyK8Qei3nRHDX9GymzGUHOR+64LiyWzlHA\r\n1cf29XWLxIzY1lM/Q/6IA1qwndNEr3Sw26jKXCgn1hl5bL1eqtBwvpCplTyk\r\n5UAbuNwlgMZbhlahvaBFnSxsBnaMkXU/zzP311tCtLj5mob7+qxZUdZNzmUM\r\nASnLCi3t7h4chrMxf+HBgfbaTiwJ2TnhlVWDnX594M37USzEZ59aIxjIQGHm\r\nwYbA8WMRaBnqbU7hRBcwntTdun9ruOwqYYtcFtSrnQ3MA0ueWdyiU0ucUZo6\r\nHUggLCeQq0ZEIImxAnSx+zeBfz0PHEbirRhdU1/yDUzeXLr4MszuX8dgDTD8\r\n2Lu87ugtIEk+Am9DOYuRZeNP1S9w/NrnMUT5auyVYJAZjVHEGJeZdIwEnIkZ\r\n4XZWCW6QxWtC/pCcZ89feioDU43fYIvqVHU=\r\n=CtTk\r\n-----END PGP SIGNATURE-----\r\n", "size": 3267713}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.16.17_1673474318988_0.5283811150571476"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-11T21:58:39.220Z", "publish_time": 1673474319220}, "0.17.0": {"name": "@esbuild/linux-ppc64", "version": "0.17.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-f40N8fKiTQslUcUuhof2/syOQ+DC9Mqdnm9d063pew+Ptv9r6dBNLQCz4300MOfCLAbb0SdnrcMSzHbMehXWLw==", "shasum": "3a580bc8b494d3b273cf08a3bb0d893b31b786ae", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.0.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCI+eIn8x+SH9nNmR+fJ5ec89WpRCvDf0t6gmT3qOXlwQIhALNcgy7h4RYjD9NxlQbUMqv17pxSK+WkDA4gES3SrAoz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjGWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP4w/+MDHJg126W85gwCu2Q7RPeSr+oUnF7aclJnrVNTpUYvdyWAeO\r\ncvj1JPBS2KZhJWfq4JT39gl5qy406LWYG4K8PRVP+S1gpwK3VmvrJIUeVonp\r\nQuHpIerle9xM38FDs4EmIOZXe84F59lOL9LKzubA1lWMDzaGistM4WLQ8XwY\r\n8/KdFIR8edQ9F3hLUjIx+f/AoeB484e8EHyoVSn8m0/YMs2SG2niEUneu1sm\r\nkKw3St3nGu4cq/NHHSuvYOh2gc37+e1A9BtoycVPdKwTznXhNginnHBIrDaG\r\nWXxSahGFSBvHdXNtY4fRqp2tiF4xSCuLPv8vlVzWY/ZudVFyxQTjOTAVQGqc\r\nmpM+0W6KsYzjkKaohjA/cRQvAhF/9ll7egZ/VX7vfX5YizZj2KeaM3ioYyxL\r\n9+yotDbaiyHI53w16rFKKMNJ5lwnYVqIiaUJgxqZb78MV5bvwxoQKUSh92A6\r\nLjHiyBem7sS6z/twI8bBO0re++iugDcOn9gA9J5PiWcslWI+038ZSnnskgFd\r\na6CR3cEyMIV6N+JEl44p3yVRjr0W/GRy2gWzvj/QUqQ0dSapgoiwSju5txQ/\r\nDa8fiLjyn+foXlXMqvVRulH2kg6g/RwuZpg5i3aGG3rVm0r8hdksFlzYgf14\r\n/SQqu5/PlFi8P+aZFM7/B8jAKJ5H5/GVFeY=\r\n=hzNQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3361035}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.0_1673671061731_0.40435751818223853"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-14T04:37:41.975Z", "publish_time": 1673671061975}, "0.17.1": {"name": "@esbuild/linux-ppc64", "version": "0.17.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-R8HchAp+w6Ar85mEMZHobmLO6fT6/6CW6m8cq+dz4APHaHv24qNxJKJiceJALM//mvGnP/wVoLsJCmE6Wv3N8g==", "shasum": "73ab32a62587ebb5143557da531bd8d4709bcbc0", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.1.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC1JSXTlvorLmwx3r5KfdyGCQ3ru/4lCooDcZ+rEIih4AiBaGHxkJ+e5PNhZvNnVn5jCREsonfSDwsVnxGAQm26EmQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZIkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUDBAAoI5BuPkOiWXmC9RSNMA3iIf9ZOJRi/yn9VJjDtaZKAU3okIO\r\nJYV1ZjWZaebMJwVxuBOdogrVUmHh3SVSaZY5rmposEIbGWBXP2ryhgy/KbHH\r\nYVWFezokEF3J3gi2J3UH9UyhMdlW1tHdaCRGVCqsFfew/GhQUYtScpFsu98+\r\n9LnVT+DicC1fx5IoTeZ1TuGBiSUMfprasOSQ3gpBcVARyfDpjrny9EqIY/3K\r\nIDxn1LDw5seFTVM6U2T7bM3lbqI/fb6Svz6Ifv/pxr0BRZwTnZ9mRc6zTsyb\r\nc2TbB1zNVOSLcCzst3kQJkd7HQ/8ds2WGrIMX6gxDLqTZhHn2vlKgHr0s2ia\r\nU2pnIwmpzYWzOtkb3LuFDm4WoqYnTSO7UVf5BZpBANBPAlAu+bV276xnaFPp\r\nQZygLnkBe88a5X4XluKtXvhlBBM4dIpwydLQfYa2IqGPSETirLu1pogJA4AO\r\n0BGKCkNylWMXczM3XciyHWFh4kirvDy9lXmLGfeDsM94W0LcRFZiRax3qChK\r\n7C9Ask+DsXJNR6bYURuU3zHK+M84v3SkJc+pvGidgn9IYCfEegbZGHDmGRRi\r\nnxqOo1h5CSfxO92VdCS5ur2vcnZcKqd/yrCoQilwUn0iZKhKg3QpSrvze3w5\r\nLPhDjutKd8xKI9dAZMYV+xHGQZ2o+VrGKvM=\r\n=6Leq\r\n-----END PGP SIGNATURE-----\r\n", "size": 3362727}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.1_1673892388119_0.8474580705415895"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-16T18:06:28.395Z", "publish_time": 1673892388395}, "0.17.2": {"name": "@esbuild/linux-ppc64", "version": "0.17.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-fzHTnIGIVqgUGZcFnnisguKD4UneF4uwWwkG+i8kBspMDdU1wJ0jha1VdtxWP7Ob1KGxuXcoUlrQkCVO+Z5iOw==", "shasum": "45252f5343c5178dae93f8f7fc97aa4304cc5cca", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.2.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIATj8t68cgkmm9/adYajkKPv/KR/dgaBvTPgJ+gMr1qtAiEAm/glz+Q0X/ifgZs7G9zdfbhFY/iFMF5Yd6pAVSltAio="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkLYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRWhAAmHiMWZnB4XX6Q0nnL1YkDGr16HSDpIRRtlj/2wSW4PIRR7J7\r\nEDHbCEXJr9XVgWBjonCsoZAGk/BOVotlVoKTTkl2IksHuPDj+GudqtD5P5lK\r\n4joZZJr9MqrboOFXpuIgYG0NzaPjl8LpLSFjRlidQxR1T963ehceWDVp7feA\r\nr/scWxqjgZBQx3tK8EjYJiDiUE2txLtEUzXVrjGDlotKP+sbHLMGmSPev2RY\r\nS1fk+azyMeal8MFH4Qzbtha5+Xgq091m+GyzAvjBnlvsTY++gU1OTQN/6KeF\r\npoRATwRXH0toK9TUQ92F29sJ7pQiblZAi7GmPcRQxKc91G+FVAV6Oe9+n4GV\r\nhKEXh6PCd45J9SgE9W863Z7FB6xV1gsoF02H4YqilSQUDLjKx8JXlW00e7rX\r\nfvmpC9ziBiMkt1oBhxbRlBPucwx85EU/rlmd4ZshBGzA/AgT2AIAOjI1lNti\r\nt6RkGSqgKiqcIBNjwGetU/hVjX68rAGSXeRltPf5fRNNCu0ll9VIv5g3UWeX\r\nULoE5wLGp3oNwCer5SQWUVmg8M/ZRffeCFBd0WnLr8FxVqsquiQ+J2iWNDiN\r\nzQ0ajT5IMYLsjzbaD81Q9Kg5tgUQd2hixghtgOnCSe13daWXfQSPYS9Acpbu\r\nxELa5B5qbM4+gX4NEZfxginZnP8+Z7Ue2NE=\r\n=E2Lz\r\n-----END PGP SIGNATURE-----\r\n", "size": 3364053}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.2_1673937623854_0.09048976224897931"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T06:40:24.118Z", "publish_time": 1673937624118}, "0.17.3": {"name": "@esbuild/linux-ppc64", "version": "0.17.3", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-BDYf/l1WVhWE+FHAW3FzZPtVlk9QsrwsxGzABmN4g8bTjmhazsId3h127pliDRRu5674k1Y2RWejbpN46N9ZhQ==", "shasum": "c157a602b627c90d174743e4b0dfb7630b101dbf", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.3.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQAOxthws45hN3cu1B53WNdrmIRNhcinMtk4+Z2DWEgQIhAK1A+PH2LkI1vH9OXgmUx0pfNC0sO8HWmGCwKgvIeGG1"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEU/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjlA/+NHBLmkJPDdVZfUGTyy1EJ9J9evD7ps9lyXMmUeIzAnzlao3H\r\nvU5tfwxgZVDyVVBMOgwUqWlC2vaFoBoc76q9d5M18uej1eZCXgH6xm5UmzIS\r\nX8VYm2rUFiMrwKYFJm2CG+nmwI6r/44IV7LmqeQh+MFoujWGT0mJVgQYl8ve\r\ny/lcdc+9hcPdKCeNVseoYlKi/Rkd5gcHmP6QtTSjWlSWFEG0OG9wOyADZIaV\r\nRFpZ+cz9kpwSXm4E0LCikLlkK4nAOVEz+oCVM1j+q09+7OKAEVuWAvjS7PYo\r\nJU2TOOe3HZ+0nD2ysceGG/AyqbXPUPI0lYqEmQogLoMplwUVrIs0hNPF5okR\r\nBzXlOBjXyZm+45DgaUkpFlwMo94hxz80JagqdpdNjVVPpLVAxNIsxMLm32iJ\r\nhpDDAwgQWruhzNY9W+QK49TjO4vAvjHOWOd8tdMPdpSSu3XpToQQAk6uxK/K\r\n6YqPIcBT6TahCd8SdWwtEWim+2VYu0Af1X/kz9pY+1jTSELxMeDcmr2Wry4v\r\nn5BYYa7sW6ws44BkGolW4pDSTo7Xy94yjM7mBjZAOpEPJy3SURWgfMywOFy4\r\n6SfKxxc7pHJtEsYLFn+A9A5J8E4s+tDBBhArfuRJRK9nXSGTQeVEaSHYXDYC\r\nRdWGZSw4Htp8bFerJfoJ64UFUd4+e0ERMXg=\r\n=QB19\r\n-----END PGP SIGNATURE-----\r\n", "size": 3364248}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.3_1674069311639_0.782512199673588"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-18T19:15:11.892Z", "publish_time": 1674069311892}, "0.17.4": {"name": "@esbuild/linux-ppc64", "version": "0.17.4", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-lOD9p2dmjZcNiTU+sGe9Nn6G3aYw3k0HBJies1PU0j5IGfp6tdKOQ6mzfACRFCqXjnBuTqK7eTYpwx09O5LLfg==", "shasum": "defaff6db9a60f08936fc0c59e0eabfb1055968a", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.4.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLDKb/owBnaK8N8Unodg8rZDm0ObI9Sz6pQAbcxl7r8wIhAJq+8BUmfDWl0Ka5iVFH6EhrmWiUc+D7GAtOOQgpAJfx"}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAEg//co2CtdvkgOY+N5CQk++/+rjrmBWuKheCHgEfd/OX0wwfocF9\r\noUuxaNTmnyn3lsgwqqZ8qmCvQ4lZQb93nZEe0Jk+XUypcngEH9YFsBdBw9D3\r\naOJq3u2/JAcpmo7ZfIBaQBoIUdpFnjZejjyYbvR+1Vxd+jNO/lr2kS1sXmOk\r\n1tHyWlbdFoLy6/DCRqHu1Eb9vm2rOoyW50kSttp4glCWZ2SCiAzAlc0boDiM\r\nAcx24m3fZ0PRZW5hEEtVzdV/87vlqPzof/hWIkx1dSKKbtpaQIXYziRVPzLd\r\nwzUJxGagBQwsOKzR7TqwrdS883FP+XHlw5tbrRGbKXqaCf9kK9yPbBocPRtK\r\n68OCCvPU025s6dPf/Gj1qfRaMapA9Xqp7ytJir7Z1afMmm5egjxKalYdE+RL\r\n6KbVNStG5KKOQFbtuLbcv8T7gPLp0BLgs2UEdn6dpj79Gl+ofcK61bpeh99S\r\nk1U+LZxQf2Lth0WuXhFtI3KZJHPDz8yAZWU84TOtAsFy341dqd/+46ZEuasu\r\nOUon+PnmB8TcF0jBQxM5WLs3AP8oJbQvKyROxyOXISbe4QOGT1BnUozL3OdX\r\nPm9D3mIH5ED5Vmh0RWaXkERx3xLW81Z6LKO3I0/OCrkQEKwdrmgecU3X9B2b\r\nbDR9gcaVegHSj7h8pN1XUOXOZxCLU//Ooeg=\r\n=4u+v\r\n-----END PGP SIGNATURE-----\r\n", "size": 3367733}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.4_1674368048548_0.05002981561796638"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-22T06:14:08.802Z", "publish_time": 1674368048802}, "0.17.5": {"name": "@esbuild/linux-ppc64", "version": "0.17.5", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-5dbQvBLbU/Y3Q4ABc9gi23hww1mQcM7KZ9KBqabB7qhJswYMf8WrDDOSw3gdf3p+ffmijMd28mfVMvFucuECyg==", "shasum": "ea12e8f6b290a613ac4903c9e00835c69ced065c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.5.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVKLfJULZM6jlEwFs8EUZcQcW5R9/RIGIMs+U6S/1A8wIhAMBlzX2d2BnmbTJMNZBzUTghPnXiVsiGM6LyH3Pl80ah"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/4CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqbRAAo3hnxiZ+0HYwi8TS+8/HPW2FQVR2u0asq94XH27/sRiylxJL\r\nGingdTfPd/TxfgYedcOFQv+g6C7a7GyQb2TofQq39YLannZrVmq6F8vRf5So\r\nBlpF7hskjoo8NxKAr5PACy3E2833cG4Z3le2HP5qf9q0BqNuU3O3wy5IWyLG\r\ng5VOSRPHCa1gCMIlmsz5CWwOszDjwzO2qGFVvLsC30NMz+AfElX/hklq0OQx\r\nggm5Sog0oStWTjqKQbqK3iLvdWwsrg5bxO6kVrYgJHeyHfqwPECadCm/9nJS\r\nbpZfjCpetloqJ+UQhWFXwreF5xGK+FPhhxDxL+1oew0R1IU8bQ6EZJfWrIf4\r\nWqhTT4dp/3DmYkw8v/dh9Kc0T9twXX9WbeBSdwMXqwsv8Pat6kW8UgWoZ9nS\r\ncDEVdvDDA1h4jFR8CSlarh1ZuECYGVrGu2DObiJd5IRnG0/jmaAFkhdpG97o\r\nDekzNAVxIjeBqBWSWRzU2Ai4bjjaWDMyZUDk4jtGrczVQuDGIzh9X2LrP88m\r\n8/O2g7Ux3oqTtlp3IFF9F5R4XqPiTig86oDcHZDlnLH85iOjA8dioo6ujAwd\r\nY4j1Iz6bAT8XL9OIbu2K8KGOlzKKJ0FAopf1fKqeZh/LqKfX/VheFHRhaWMd\r\nL/cCenGIo1cO04zXsBxMCw2XoJjbaElzxJY=\r\n=ZE5+\r\n-----END PGP SIGNATURE-----\r\n", "size": 3370189}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.5_1674837506116_0.3014237799768904"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-27T16:38:26.398Z", "publish_time": 1674837506398}, "0.17.6": {"name": "@esbuild/linux-ppc64", "version": "0.17.6", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-AmLhMzkM8JuqTIOhxnX4ubh0XWJIznEynRnZAVdA2mMKE6FAfwT2TWKTwdqMG+qEaeyDPtfNoZRpJbD4ZBv0Tg==", "shasum": "38210094e8e1a971f2d1fd8e48462cc65f15ef19", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.6.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICvTEldloV7EW5D3gBeDSAF8P4SSYrG0in/AfuJBJaCAAiB/X9U8mcZXLDxdsbPIX4EqI5dJt/h42ynVN+NxTeqd7A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrDQ/+MgS99Bf3FifLKOptG5+PTTQU151t4LAZaSp6ZVEuDAViBQ+U\r\ns9aN5o0L+YblADFAzAfiN2/A41zKCPJyhnnwATWMaNmM2pitV2L6C9WTp7bs\r\n1bmEsgodTR3QH4nMrczzado6R5WY7DlaWfASMG1kBwGTma4ilWOfrlK0z+PW\r\nnKfHGQhX61SyY0Pp78S1g+w91/piw7sHxG0JsR/LVzp+NPQzkl20GBPlJW5i\r\nin9gPOXHabrXMr7W/HavOcUN1edCtlcRsvuZ0rKejUO1yFOKHC+j6enYtCZK\r\nB1rQVkGtiPjiL+HyNEy20QTOLKCZ4fT+X4T1Gmm0HOn1jeXkWb4YmwJIjGdi\r\nsJvl72efcslLSAJkddZ4EXvJNkHIzvCm67SRIrJMONq38jthx3k6Kr4XwGnq\r\nkeQ4DQYVVvQkgVtNBP/v76cexOmPHyhMRjrH6JpghXSylwYmYqZfDieDnKkn\r\n3J4hAWeIICZTnc0oTVKnWLRfonEL+//NbNbMQElEBteT14mg9Kgw58SMv1Pg\r\n3lZR6pa/DZbIuFpp0MQjpHo7ADEwVMjkLMuNJ5Cxs5vizfiCdDZcR0TpUaOi\r\nka20fBPqv2sgAwTsegeDstqypvvBLzKSb15hSXCOWhaTstb3ygEnCA83Vwj7\r\nrBfLAejtN973jJITELbaktUAeDPWBghfArg=\r\n=8AH5\r\n-----END PGP SIGNATURE-----\r\n", "size": 3371743}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.6_1675702879305_0.5905092971089674"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-06T17:01:19.599Z", "publish_time": 1675702879599}, "0.17.7": {"name": "@esbuild/linux-ppc64", "version": "0.17.7", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-UWtLhRPKzI+v2bKk4j9rBpGyXbLAXLCOeqt1tLVAt1mfagHpFjUzzIHCpPiUfY3x1xY5e45/+BWzGpqqvSglNw==", "shasum": "ad1c9299c463f0409e57166e76e91afb6193ea9f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.7.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDh0KQh+6O9XkTUVuB14Jieeut+3qBkuLD7Yg/599Vb3gIhAMhjP/iJSDGNkAws0UvNnjdKqkmSf6OTPq8R4F9nknJm"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XNLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnjRAAkwts7lqcRNnz9dVM4xRUKNpfKeqe7tnf/BgNgYmQmtDxDIhD\r\n8wx6i6alEbY0Pal4vckUxEYeE2GORtL8FZE2h2u3Lv53Jdu0CjkfwghX3cAq\r\nCPnuzbuXxdyVfJ4M2RYsSZ2HvoOUn1hDwGMc88oT0eUChRCn+gFYlZOmTGVt\r\n4m51GHkWFnEKrLB1UW+V9ZQCUoBvbVKG7/UKTTdI4MOo66/HudtNIdvTEtls\r\nMw/32ullczqgQRH58s4CVteonYVPhNwlP2TiW0UWee4eL5h5DdOAphtkUCRm\r\nis30h74GDsw3E/oDUOXTKe/l5dHMxvRaz7tJneZSMgkO5FS+AHlbAt0KoWLw\r\njxtrMZ4nNN2PBNBv85L2fYv0OsdiZl1LJDz/hx7N+qfnd9Db4FlV9cf27vsw\r\nwWIxMbE37E9b7fGLKyKP27TMfCHA6DUxGvmsw+j0MxcQe80gff728czX/oJx\r\n1oAdHcQAzwLBBzaRRNYLNnAGS+WALSHWUtW/3Ek/kqGBDflTy7hcLV4PP6B2\r\nvq89bFmh0mpR/sJq3fXRGloLC1rphbyLRmsPK+O7NBkLX2gQqjCPRUN2dB1h\r\nGYyGvmR87EjuoYwgNhZMCLcepwPVlVqvyBubLRqfwVWrYf09bIaVGp+c1LLx\r\n3mNpgEEQWogaTJWxLPjNRQE5yAfxqTaQGbU=\r\n=Cd4r\r\n-----END PGP SIGNATURE-----\r\n", "size": 3372269}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.7_1675981643620_0.3275768811948776"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-09T22:27:23.884Z", "publish_time": 1675981643884}, "0.17.8": {"name": "@esbuild/linux-ppc64", "version": "0.17.8", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-bytLJOi55y55+mGSdgwZ5qBm0K9WOCh0rx+vavVPx+gqLLhxtSFU0XbeYy/dsAAD6xECGEv4IQeFILaSS2auXw==", "shasum": "1e628be003e036e90423716028cc884fe5ba25bd", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.8.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDQJ9tOp5I0n18drX9byDVPAIPLi13fhPZDtTVi+Hw0IAiBUSlPT5xGrhCrp2uuH2c3vVUEQnFEusnNqlx1dk4kOAw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dpgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsdQ/5ARw/3Cz5RsbYcL8xnXs0AaXYmgznLtyh1EpKN4hzhC4eDVNC\r\nUuWnPXPNzZpf6XeKNDMvDQB44O33QrBhktQBfZlraqE0esSHD8sde41N+wWv\r\nui67dn9kSB8J+QPwFo5CiAmMO6wrj/kVKSn8qekQIy/ystlbW7o/CLjoteFa\r\nle+T9bcLt1VWErKyrmegEUkZcshPSu1nnpYJhetDbMCOscqVJQgVBCjbCEen\r\nuQW4nzDo4usVasYqwC0Pkj0N19REPetjRhcZXOm2jCBmBY7pWH6NmgnTcl2m\r\nLCJ2ErHDbULs+6xMpLerd9fXV3Or+YumB00GcMbTds6WkgORDCGF22K5P1k7\r\nmHObP0PJF6E+fRTYnz4SC1k9l+s3f/DY4S0Dujhby0xFNt2lcDKRg3dOfR8r\r\neZly2eA9D6RH21/z3x1EWXsuM9DKAPvXtCxOYHPinEEcf9YFMOmj+upke0Tr\r\nhL+qYS3vdfAsDbEdFM/5PRw/oiSg+r1cKhb6ow//TwswjRrbLCM3PufYIAoZ\r\n5HFd8ntKSZxymQEt1U+r2ZkKh2ip2n/JBLLJUrdewluwEiLgchFewtuPxuLX\r\niAjxspYHPzKsG+6qb9VoffG8aIf0++qIyei05OBVNzqLPPKZIisp3rHXDomE\r\nHi3LJjJD/MQDXdrqMz66DhnazUVQmDkda6o=\r\n=N3VE\r\n-----END PGP SIGNATURE-----\r\n", "size": 3376193}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.8_1676270176265_0.9561509665141437"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-13T06:36:16.580Z", "publish_time": 1676270176580}, "0.17.9": {"name": "@esbuild/linux-ppc64", "version": "0.17.9", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-tOwSTDZ0X5rcYK3OyfJVf4fFlvYLv3HGCOJxdE9gZVeRkXXd6O9CJ/A4Li1Tt9JQs9kJcFWCXxCwhY70h+t9iw==", "shasum": "95ed6f6e86b55086225356adea68f1132f0de745", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.9.tgz", "fileCount": 3, "unpackedSize": 8585733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHhj+9bYSdjHuM6cOZNQ0Z7nHSaOSM6Z2lA5kn9YiZ1AiEAgHwu34jCDCKtIk4PDK5skqR9bDAgGXF8thF+qoMj+cs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mBTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphOBAAiukXgRuAcf5svdaCOOhVP84q60aNA0+WwqvnLmkX/n1EmfOf\r\nw34DBuaHQ15bStifwFgjaUfGGZBCP0wMHKAdxEUE6PEGHvwf5beyyrmg/eFv\r\neJ4+rZyPtowMMEPQDS9dn6+RXFnYJ1Z1IVR+Z2eS5Z4JmjTENOkREh829+AT\r\n8oWfjQdZ9F2lC5n1t+7+cfC64wcFmVWYKhsfU8eqSbGz8799MGat+w4E4Vme\r\nFixbtFgxgOH9km7fl/Fdo/V/RwXUPqe5KiOSLdV7CyiNw8gGLoKDvkELRGhY\r\nMXkRvHIjXOWWRBZDySXfzB0f2ZqoI0UwV9J+nOLSgli/Zpds8Pi7adpPRLHF\r\nkf4Ts3MBqgAQZblDDpV7wQqdjPKHJNxd4lhGlKxjMPkQabL+YI0+GudqfjdB\r\n8TtdsqXsENBuPNt012DYP+e81A29uB4SPULRgxM3uo0wLUglhbCKoZxeg/nm\r\nGBrLZZtavYKYMNFM79WKsAYP5b5nJI+MPDpGwZYvitnWtGjL4bi8Zh0/JUrE\r\ntzpg4AlN/+0FU/2j2QIgDR03xEtCFAkQ0v5+KBvXU9bPheS+rpQTKKwd0zPf\r\n2H7uI3mnzaA3wT6gz+cJenKjN/h+ZfR7C2Xd9yRM7MWZrIzRz0OVKY5DtFFs\r\nAAw2+FjruXZ7Er06EwQAk7gH/BtHTQzEqho=\r\n=ymE4\r\n-----END PGP SIGNATURE-----\r\n", "size": 3377150}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.9_1676828755229_0.3690886762734986"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-19T17:45:55.441Z", "publish_time": 1676828755441}, "0.17.10": {"name": "@esbuild/linux-ppc64", "version": "0.17.10", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-kM4Rmh9l670SwjlGkIe7pYWezk8uxKHX4Lnn5jBZYBNlWpKMBCVfpAgAJqp5doLobhzF3l64VZVrmGeZ8+uKmQ==", "shasum": "1ab5802e93ae511ce9783e1cb95f37df0f84c4af", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.10.tgz", "fileCount": 3, "unpackedSize": 8585734, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/VxOrz1Hj7KkH/eYYJiulFj0TPkHznHH/TxPREPSL9QIgddZsTyu4oPfukMtY6N40pAmLJCOWd/dn5jyFbLqyZp8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87QSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwCw/6A2PjengpJ8vC9e+NR3AHMPutW/pUm+VtPGSpyJatNMjBEgRb\r\n/y8MRXK/KxCoLh2MtbgcXF9QxtAd0461O6n6yYxn08jOlrUI3ZRIsBVA2LIt\r\nu7MNfO4B2LChWPhZvaRaiilHl6MyEwNb8J92rus0QHdrNSoF4KC6UkgLYYMS\r\nmdagGuEoIQzmu3epBy4JPZ8DuO9dUQG5xLWXh3o4cE1pczs6WxxdUWQHBjcT\r\ni4IwNNo8BuApupHev6lHJ5pjZQhCpHZK9wcMwzbvFOcBsR2txLjdJUzi9vlC\r\nOid8JWDVbg/AmWolqKo2tRaMNVoXdyx27SgKZVY1OtMIG8qYl+DL5CnY9amW\r\ne4kmk1VZwcUYjrnDbNSXPPo6Q2WwaEg8U9FQ/f6ncMknmH8VmCSMxVdtGK89\r\no9zNrcBUNuz3c4QeGUAbGljcWnEuzL2ATrylBMabXXUgCmsCZ6H71eWtRzJT\r\n4lToVUNBWXIN92k0kbN8peJvH3kIz6GuacEWySCuEqUJulXbGlsjaBn4hB4F\r\nzFHmhnE6leiYhEu/lE08KmtHKgHNT81VOjcLKq5dJnjmjKfIpxA1ijZHpJsi\r\n1UV/wunegv7DMfTsdeyqEzK9vkKSBot4n89MBOTdvvTgTBihEml1/BOIxsxI\r\n+mS+cq1T30c1aF4LMJm8nVUs/tompdSZ5/E=\r\n=rnG/\r\n-----END PGP SIGNATURE-----\r\n", "size": 3376876}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.10_1676915730323_0.7221416093690625"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-20T17:55:30.610Z", "publish_time": 1676915730610}, "0.17.11": {"name": "@esbuild/linux-ppc64", "version": "0.17.11", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-BdlziJQPW/bNe0E8eYsHB40mYOluS+jULPCjlWiHzDgr+ZBRXPtgMV1nkLEGdpjrwgmtkZHEGEPaKdS/8faLDA==", "shasum": "2d152cb3a253afb8c100a165ad132dc96f36cb11", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.11.tgz", "fileCount": 3, "unpackedSize": 8585734, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnQO5ihPfACP3M1hEUK+Ggghvc9cfN0KrX/vt1M7sSwwIgR3RK6a/tUDPUJKlO6lZyNaE7MVDIBR/8h8pverkYJXc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAnd5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/7Q/+ONNmODuK02q0GemPjibTquk+0q09fq3j9qxVcSTDSYIHQQO+\r\nJmbxAW+lGWT8tV136g0Ktzcjy7RSSZgAqMQBZaoERr2VpjZf8k1xOB/olqLY\r\nnc17p9x/eLftnCDV4Es4xnRz3nw2O+G+1rLxgXZxcUjWgPkwOAyrEsyM8UR1\r\ndr8nJRHph1Si5w2smTBHUuX4HzxcpUbRB6beFDxMC5ZjyzvVeGxBzbXLfmNH\r\nS2WlvbYEIUuPK3pUNr/fyVqTQvhs11FD3zWYl0AQ08yhomaq3cCm8LKN68Ge\r\nJYWVsSiauE35vtl3z7BnKdvWJccC9Bh7LLDJKvsE7dUG/Jjo2uf1g1VNAL1p\r\n7jAgjhsKEMmkCx22GqBoJo4Iu83XXATp0s29yCDxEjYZDmDYrREx5qydr0fX\r\nIKkH4167o53EQByL9RmIIdAu/TqpQk0eoGWZHLR7r0M+XaZVsuZeR1jBKCie\r\nwN6bp0IEatXK9XnTXZxU+XEHW5JdU/h2q9Kfg5j9uBlj77YIczK5uJCbXplp\r\nPH6+BZ8EboML3tjHKdEo9zbTB6LVr3aAb20huaARRrHaqdz3OEgANVHxYcot\r\nNpvdeicPyPgN3l1v0MK275T4JYfgwwaozW2dfjTzFM/uyGE36Q4KRavoeM1S\r\nMP1x7KVoZLGMy6Du1cozj8alPklDjXIf0c8=\r\n=KOPL\r\n-----END PGP SIGNATURE-----\r\n", "size": 3377942}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.11_1677883257022_0.6732745976612571"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-03T22:40:57.290Z", "publish_time": 1677883257290}, "0.17.12": {"name": "@esbuild/linux-ppc64", "version": "0.17.12", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.12", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-biMLH6NR/GR4z+ap0oJYb877LdBpGac8KfZoEnDiBKd7MD/xt8eaw1SFfYRUeMVx519kVkAOL2GExdFmYnZx3A==", "shasum": "1638d3d4acf1d34aaf37cf8908c2e1cefed16204", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.12.tgz", "fileCount": 3, "unpackedSize": 8585734, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIET7mo54bSEvkV2fPc8v8S7MyFbuaYT1g3zbtJOlUbnZAiBpNwneGtoRHMfdF51WD/Dii+tASClu+ppv0PDeLj60tA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAYNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq07g/8DIbqkOjRUX+19VN2ywwC8BNmjWr+K+u4KjegFfADhF67iRGn\r\n5ThJXAbmmSoY2sQm1Oya3pJjATlr0e2Neqd2hddCT7LVsVIiDuy2+Heg3UO5\r\nT5BReWpmfMXuDvPmn0uceGcBM+anHfDtQmFCXTmaI3a2m+kZKHla2QfUhlWD\r\n13lkgF6Nd94JVN12iqTwa22GwRdMTUNVL7cSewTGQUsR4uCGAafzQkARl9Gv\r\nGdwLdkPiZxsv/kY3aXBLLVPi9pSIZltcscQUQ631ufhMAU65Ehf4Xy473XRB\r\nOGrzlyqghfAC1jE1MKXjE0XYhsyLJI+SIyvP8BrlO52YrigMVv1njWg0WrXj\r\n4x/8C/ax7fKgqv+lma64u2kIORV47Jumiq99pYNpf4MOlmvm7A+edv5ctyCW\r\n2jguuiMt68DidGD+E1Y2AhEk96XvXMxTGHk5sFINGW1cUzaKnGeqYJhFCE1Y\r\nj3aW8TK0F5PFaw22I4wzLcRkeb4JNioi9u+EI56GL5db1Z+g3zMJ1KAS9lvC\r\nF2xY0iRmJ3lbCv5gwTtylHFWIfsWkN1jX6Lz1nn+2dH57UnNsqLg5DVWcz5/\r\nDKExT0yrzexuliyAZjURRi1zZ5eqdRd5+WO8yK2Tayjn5U+jvqzbvJOE8ak3\r\nQdO2dRh8BBvdEVfrPzbvFbWLrnYMtthtn2s=\r\n=h8G8\r\n-----END PGP SIGNATURE-----\r\n", "size": 3388485}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.12_1679033868691_0.11837663833631051"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-17T06:17:48.976Z", "publish_time": 1679033868976}, "0.17.13": {"name": "@esbuild/linux-ppc64", "version": "0.17.13", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.13", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-C9sMpa/VcGLjVtsT01sXtzZNS7bAZ+icUclkKkiUwBQ9hzT+J+/Xpj+EykI5hB3KgtxQVo4XUahanFoZNxbQ1g==", "shasum": "7b668c77ecd58b499622e360400670a8a57697b8", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.13.tgz", "fileCount": 3, "unpackedSize": 8585734, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBg8Kr1ETFCe63YV1Gpbj6cPtrh05x+UoVEN3J33CYzVAiEA6UMMMKkUpmyJDbwG7TQaNecKsmJXEIHYB4jze1oQGkg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDrQ/6AiT2ZbUcontWB24AaykOAgAJcP40ORRoQc/zKCO4uhX1pxAo\r\nrUksJx2c62J6B+SGkuqBaD5N8RyzBGWGCQgSUo20CJJN+nA4ryYCBzLsFlPE\r\nDSicZA14VfnnuLXro1cwB151jN1j2TS8vZgdaOv3q59W2iOeAd1rQkWdYWZj\r\nOUnUWQK2HiirMaL6uKPcGQfQSMAqwLdbyp/ds5OR6SGFkyOVPmNJ5Lybpbeg\r\nQ861O+Y3/zdlMSWv/G+gL5Kb3YRA+Jsg2DtXyJWURoQiFGJPlSWkd+/z2GO7\r\n6TpgL/lxyY+MlmLQkq9uyJj9q0iI7ftm9zTDmgjPXqhJZwT9VR8btVtmljWN\r\neadNHE2P7Cy+XPUELCWOEKjmBWy2Of+qUZ3ts5gEgpDru0ZxUUCokLCMjsfe\r\nYhPv6E0oj9tEe5JVOIfpVPKqVbDoOPrzobxI6ioyaHo0Bod1bK9g/yiWzACN\r\nV08ftD0It29sUKmidIUpOS9zAhjaPndtimS7HAl1lpqHd/bXNDMBB5H2bPCk\r\nCZxeq0lo+owLhVoFMqgmDmr8rTNLbuVnfFzt4BQ7GQ4mYsIDThWmRgegIpVN\r\nn7NFkZZ1+fN1wXXj9szo78DuVX9bYJpfLFmG5Cm+a8zlHVj2w6Gjj4TEdSsX\r\nDJRp3pPn/0R3Uryhr/IECSqUbLjtnxqHkUk=\r\n=DhZX\r\n-----END PGP SIGNATURE-----\r\n", "size": 3390024}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.13_1679684264626_0.33101863728464953"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-24T18:57:44.935Z", "publish_time": 1679684264935}, "0.17.14": {"name": "@esbuild/linux-ppc64", "version": "0.17.14", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.14", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-NnBGeoqKkTugpBOBZZoktQQ1Yqb7aHKmHxsw43NddPB2YWLAlpb7THZIzsRsTr0Xw3nqiPxbA1H31ZMOG+VVPQ==", "shasum": "32d918d782105cbd9345dbfba14ee018b9c7afdf", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.14.tgz", "fileCount": 3, "unpackedSize": 8716806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcqBQjNc3zgDXApNnHDmTdCPq/3kOA5Elw2aNdl3wDBAIhAI6PRVvWRXIeuGchOIC44Uc4/g0uDlfR4oyoOmZ3j5zu"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7J0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtZA/+OjguewOpMF2SYSOLDjFYo3BR8qGZnnMNjAwOg03+bbepG8t0\r\nHlffHZ2/a6YXyYYUBGGoCmAFplfaIVNpdn1P4vab1ZqsEULawFk6l7Q2Uh3I\r\nwcsxU4hvgopR9LvZNdfPLxzxc/RBR89jj4l2pBYuKEdHsnGyLNliMFQrz4cL\r\n+Euvw/mcaTZXzpvAWrow/UubdC0H3vHZoi5tCQIznsLrCQ5FCf2TpufPU2QY\r\n0/0Em+hpu4Ox5ZszLpxA0Jod58kzu3MrbNRKv8zkloCAPRb+DsQ/vuuJ4Jf/\r\ncSssYMS7roMDF0EHgo1lcezXCcqJ1MC9MMNxX0KoUFTpMQkfL6ZblHMvUH2W\r\n+nmcTrkO40TxKpJ1qBHg4ioaWI9SW9P4TM+ZB/icabxmTXjCvAvUFKpfLC+T\r\nyO3FS5fFCO6MZ8azN1kjf6bQAYXjCe0IrNzq82WC06RIrj/WRcLmQBPFEZJZ\r\ndYL7AVo9m0ZNAZS+2z6AC+y4+EikkI3Y+b2MpGXO9UaOvlnnEUjUpxXkPA0d\r\nUvVVR1c7I8XGDwuGORblC6r3Dp3kw3qpcWf9HaZAS29ldR0hgZJ7TeDsq23a\r\nsPok64FO8h/YeDqtEV2q+mQNZDR4YMDJAkkY3c9ju9BEKDM7WqcPcr/RfTmY\r\n3Y8fGuIRL+Brvx+6XwiEWN5mnYUgd66v5BI=\r\n=hb01\r\n-----END PGP SIGNATURE-----\r\n", "size": 3405370}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.14_1679798900118_0.46650501868360283"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-26T02:48:20.437Z", "publish_time": 1679798900437}, "0.17.15": {"name": "@esbuild/linux-ppc64", "version": "0.17.15", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.15", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-mY6dPkIRAiFHRsGfOYZC8Q9rmr8vOBZBme0/j15zFUKM99d4ILY4WpOC7i/LqoY+RE7KaMaSfvY8CqjJtuO4xg==", "shasum": "34c5ed145b2dfc493d3e652abac8bd3baa3865a5", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.15.tgz", "fileCount": 3, "unpackedSize": 8716806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZyjeMqVMK7g0kdJTr4edVUTcUQ4KQcJMF1m4wTloongIhALw6XVdHrYgWBwQYd9OSHIw3xAHRctLX/8Ljigxcd0W0"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK/dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqK9w/9GRnCU+xygnF1sFmg25/blC0Wn9y/xQ7g/cY9CtcPsOorzqPD\r\nO/GXQkraqIz+72IL0sWfEYZblDJclbCZ+kGdJVqUDb7xew17w8sPl4IEk4/V\r\nqxyyBPZukUuUiqQYVWrPo/r0hOYhWv81OpY+nIj5WdcLqh6FykKfVFGvsRcX\r\nCOE4FiNTaWVTNey9H0ByaiiKEiDBC/y1Wk9TrHlaYHGzlcQsqStYy7kI0Tj8\r\nwjsAjzYuqCbkry+osILaMXR9uICC28gsuwCCkFWI/K1o06lkfIkdvOqxcBXq\r\nIkuaE6mzBDI/rwgncPBVAZDEC6sIWG25IGct/5m+c4xTnxPCLj9V3yD6so0K\r\nI6Bka+Z0HueOnmG7UGgj6bvC7b5+nSzUhrONaA8fPJQB+LErQ44tiBT24j1n\r\nqt4H9TXMkI+xnTLqkonuEwOI1OMK71u3PgwGiKBd3VcLwHqapx4fUQrHwcIo\r\nvWoMWJKzDfr+kPydVMupk8HRD3kl7rDfEAv6AC6izrEMt9L/ICVUrfGX81uS\r\nsDDH77C/qvx914oOZaoBoKGACJgz6RTo5aZKAuebE4V6EfJdX+R2HBSZi550\r\nPyWosruHGLtQUKT3FDh5vx14AaMSKE0SlTddyPB1madQGghJbuFvvdxm51uD\r\n/L5wlzm7o4cog+kBB0/WxLiSMgJAdcQ9j7s=\r\n=FjSr\r\n-----END PGP SIGNATURE-----\r\n", "size": 3405622}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.15_1680388061519_0.8036220224468023"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-01T22:27:41.811Z", "publish_time": 1680388061811}, "0.17.16": {"name": "@esbuild/linux-ppc64", "version": "0.17.16", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.16", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-io6yShgIEgVUhExJejJ21xvO5QtrbiSeI7vYUnr7l+v/O9t6IowyhdiYnyivX2X5ysOVHAuyHW+Wyi7DNhdw6Q==", "shasum": "2f19075d63622987e86e83a4b7866cd57b796c60", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.16.tgz", "fileCount": 3, "unpackedSize": 8716806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDvOr7EOtAdpALnlEydj48ktAFGfbIVp2+g119h/I1E/AiAKcYkvWCqblF5JFr/Tfa/eAd0GJ4rlCaif18SxpLaVEg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5IjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqE9Q/+N2M7biOet5Oz56XP0106FWdUaV2grjsBaTdiBxlZ6cNf4fq0\r\n92IhNHKE71Gg1GU8R1dPxUSHP4XvAaSy1R1hYBmv0cpu4nq/xGmPk1wcOUML\r\nIoYQ/qkhVdGFILPoqC5iQ+wSVs+zk7/DmOErUFeJJo9rgOJLdqgUr4YpbGvO\r\n397om1bMtSv6eTZpZSp/4nwfqeHOTo2uV9uQJbQE+SZ6HnzD5wzqzftXq+UK\r\ni1Ytu8Zt5GNrr1vvxUFqamvcPEA9q7jd9Yls9+JuoCTBj9QMvILfnM3Rhs+D\r\nmsy9mIUG++Zts6HDqqodNQUrOUjU18U4jKicD7nGzNxZXVwtkIon8fxgXUUK\r\ns7hVoUY59Lyb+osC+5D1PxwAeXXihW+Af9CwDWZZ8G/rHrtmt6zqkoGo98jD\r\nUvWbX2j0136+ct95gU9KHjajflRt939aP2SSQM5toyket/qh/T2ES1coQwho\r\niNTG8C37oF7p9cvZtf0dCN5nck9afxfzteo3GMExsZzBJvUAChlLgDub8aRf\r\nWnxdf/9/Y7rCCYy2zYaD33x5olOp65xTAdECAiALl1uGqHkGqAfnZIqi2h7A\r\nq/v6BWgqauzY2wSAsPE7TlEGxzSuffAbZjlWjq7IHT88NnW0Z4mKqAz8NIEW\r\nx3Gtei+lkK8awzNeEZ6H+nDtXy2ek9Crq0Y=\r\n=eV1I\r\n-----END PGP SIGNATURE-----\r\n", "size": 3407005}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.16_1681101347045_0.2936011414733508"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-10T04:35:47.308Z", "publish_time": 1681101347308}, "0.17.17": {"name": "@esbuild/linux-ppc64", "version": "0.17.17", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.17", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-KIdG5jdAEeAKogfyMTcszRxy3OPbZhq0PPsW4iKKcdlbk3YE4miKznxV2YOSmiK/hfOZ+lqHri3v8eecT2ATwQ==", "shasum": "ef6558ec5e5dd9dc16886343e0ccdb0699d70d3c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.17.tgz", "fileCount": 3, "unpackedSize": 8716806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNbum9ytF1MRnpqUqE/nue+BCyfd9dNXzTu5iOXVzVHQIgHuzljriMMR3rePgzxzaWBQHr9ifSbmz4EPUqz059/PU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGd+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2VBAAgbtA3NMQtPhIoMoBZ8j5wcefFuU0S/pB98eyDwHwyqa4EQmE\r\ncNPd0D0PioKs9L+/G1QZybOvSp15UDflL4V6tTNCs5qTWBkHBUwYnfHG5j0O\r\nrE5QM3zFwxBH02gMq8nOioP47t0p5iH6v1y/H1Yz1RLSZXaWnV9dRPSuuWL9\r\ntFuA3GDZSeSPqLc8A69pC6LFLHZ5XWxOPyisCYoN8y00Ld5Lp6IvGuiMfTzj\r\nuZg6iLXcNtKsRxCta/AXVjMVAAXB614jqwHm2BiVOpTvI0FDg/ZEyTgcMSok\r\nuseEE7K/h3JlQ55jr0k5Eu/JwN0YbhTgnD6eNzKQ3+HtL65w8+BSr2g8btUV\r\n4HiuBTm6RyoSWMy2pEVBfNi7btF9eXtmwVwefoZrbj1w6KtAXeABt3+9akWp\r\n7VXO8Gz06L/wgjBsOf04wxd+lbLbKdyqFXMja5rDK+VOpd+Kpd3KYpU4Juh0\r\nEDrN97iCiwv9XQP1+SpI7H46dFZWRbAVIP6MJNiy4vMWboS6I0OYpkAoR6Ud\r\n9ktGW9F6QujI0Z4dYCm+3iPVtrnD3MsUCvY+7beyUfgoQ4Xv/8+nqeM6YOe6\r\nFym3oo0FnmohUV7xuDl3GLXRoQ/KNxK8dSXqqacEm5DG8jsU9tHAc0usN38a\r\nk6/4CiJGoy68dU9BSvCqPmygLzIuFLKXs2o=\r\n=fcSF\r\n-----END PGP SIGNATURE-----\r\n", "size": 3408252}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.17_1681680253588_0.8293737435281427"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T21:24:13.965Z", "publish_time": 1681680253965}, "0.17.18": {"name": "@esbuild/linux-ppc64", "version": "0.17.18", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.18", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-3dLlQO+b/LnQNxgH4l9rqa2/IwRJVN9u/bK63FhOPB4xqiRqlQAU0qDU3JJuf0BmaH0yytTBdoSBHrb2jqc5qQ==", "shasum": "3d9deb60b2d32c9985bdc3e3be090d30b7472783", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.18.tgz", "fileCount": 3, "unpackedSize": 8716806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDwxZ+n+TH6ajRH+NljDD/lMbETMr38GYGdP9UEljXQwIhAM7a11QduWssPj23/MIQQ/CXK7rXR3QPNj6YhDI2M/xz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREagACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8SxAAlfW/xNtFhjDysFGir4lPJdFHOpGCjiPkcd51cB7tnsFWo70k\r\nn4VxdlBM5Dl0wbzjNlY8RDOTg2JfIoWtUTC08dnb3NYEkoHm0rIcNJFrRqSU\r\nehOaomzGTsyP72xlT62vH0M04O+KRVoPAzw9zictgRDRaS7x++SOL6KDk1yH\r\n6hgWzSHJmxtoIBvYdoqC9TuHjr6pTN7S9matdXfGCO4rkeIf8dE2v8xy05+M\r\nrLYeUBDW1ja+e5IsiB1P5H9YfbJdZWL63jNZ4YfGmS27AF0oh3cppZL4Pfyp\r\nenslsc6OFQL9tCaE928qVH/fjieYnvsOxvO7WIYwFuOZVywWngJvIQzxBDXe\r\nmBah1EQ5Y6CE+Z9pjRkTfguqK/eGJnjKL0iNTbEvSwE1tUlMGg0ZQ6dXrNZ5\r\nRLEKSoek0APNk4SzlMqfcP5CNL0O67R8n0Au8uGJLq9dXI5v/Kk703hYi1uq\r\nadncz/cOSlPePunpc7+ibjXdTOqtt4sxdk13YkeGLRGufoaWVBPvrPa3ULYS\r\nt+fguXFMfgREGAP/9YuD63EvXf05/U0taTbxIlAI2kyF/pJs8JXFG4itBmOx\r\n6Ro75e1oMtAvCib6sqVirA7ltK8QQmvoXeCIMEqCef3KEPAL6QP8oQ1eyWVf\r\n1kVZsj3PFxjJfsdYKVcmFAFWjbBFAgrx1U4=\r\n=mQYr\r\n-----END PGP SIGNATURE-----\r\n", "size": 3409653}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.18_1682196128711_0.707398038481218"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-22T20:42:08.955Z", "publish_time": 1682196128955}, "0.17.19": {"name": "@esbuild/linux-ppc64", "version": "0.17.19", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.17.19", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==", "shasum": "876590e3acbd9fa7f57a2c7d86f83717dbbac8c7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.19.tgz", "fileCount": 3, "unpackedSize": 8716806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFlxyRPCR1rUMUoodMAAJOhaIm+dI3J7oGgQkqMxiRdLAiEAkm2IRWpHHr7tsxVV1A45TZDJ/Y1T2xraLLESlT3uEyQ="}], "size": 3411527}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.17.19_1683936429827_0.32071959135490347"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T00:07:10.180Z", "publish_time": 1683936430180, "_source_registry_name": "default"}, "0.18.0": {"name": "@esbuild/linux-ppc64", "version": "0.18.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-obz/firdtou244DIjHzdKmJChwGseqA3tWGa6xPMfuq54Ca4Pp1a4ANMrqy2IZ67rfpRHcJTlb2h3rSfW6tvAA==", "shasum": "5640cbe44466e443cab764b2509325955adf5ce7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.0.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDySh8OyF8VqP6iu27T3LjOvI1BreR+PLDkctgn1WB3VAiEAloJ8L6rgm7rP571Bpfck8z1csmgIIZL0ymmVAPqXwd8="}], "size": 3416054}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.0_1686345895031_0.8410536002605893"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-09T21:24:55.297Z", "publish_time": 1686345895297, "_source_registry_name": "default"}, "0.18.1": {"name": "@esbuild/linux-ppc64", "version": "0.18.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-NkDjIvleUc3lSV1VI3QE9Oh5mz3nY11H5TCbi4DJ8X09FGwHN5pDVXdAsQYPGjlt/frXZzq6x7vMmTOb5VyBog==", "shasum": "8dc674268e506daab0b30f3e95af57c314e0797e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.1.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDz4PektntU4rTmA64PEss3NK6+ruOom1RyIcD+QJ8LAiBOcdETAPjAbLnl0DGEL+eXnVnrOpe8GjSU9VehPUzdLA=="}], "size": 3417961}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.1_1686545538149_0.9650168071187948"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-12T04:52:18.402Z", "publish_time": 1686545538402, "_source_registry_name": "default"}, "0.18.2": {"name": "@esbuild/linux-ppc64", "version": "0.18.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Ov+VHayvCPb52axma6+xm8QDawRjwHscPXedHg4U92DxlhKQ0H+6onRiC3J9kKI50p8pKKypprpCWrRrXjZN7Q==", "shasum": "6c842f1cbb0062369bc0403fff79f6a50dcdc472", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.2.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoUrzjsVns2/Qdl2iAbNd0WyaC37jfKI0urvT+JiV9ggIgCtwnsTOZdOf9EI1HEiFEmiL1Trlw2CQ1jvci9KrBIyM="}], "size": 3418867}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.2_1686624094897_0.5321085533440124"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-13T02:41:35.205Z", "publish_time": 1686624095205, "_source_registry_name": "default"}, "0.18.3": {"name": "@esbuild/linux-ppc64", "version": "0.18.3", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-t7zK1Cheh0xvzfZbimztiE0wGnpV+YRsBg3tefcEBN3O4GzgLu6fFpA5HxEyVm3hHZW1jAC4OhoGEp7C5Ii6Eg==", "shasum": "3022bc526f53b37fe324d25f4ff8e228e7a48dd7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.3.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCR2SVB+cfzSQ3EbDpcI4NUTBWwuMv3sYLKT5e09aI6WQIhAMSMHrot3K1Co4kDwxJDUPNyq3baX/GvXfBRrXEDADUb"}], "size": 3418410}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.3_1686831733369_0.6318755129527269"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T12:22:13.619Z", "publish_time": 1686831733619, "_source_registry_name": "default"}, "0.18.4": {"name": "@esbuild/linux-ppc64", "version": "0.18.4", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-jtQgoZjM92gauVRxNaaG/TpL3Pr4WcL3Pwqi9QgdrBGrEXzB+twohQiWNSTycs6lUygakos4mm2h0B9/SHveng==", "shasum": "7654e479378e4e9ec6ac96cabb471a6c1c819b42", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.4.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGbvEUA61EivwIxRzO8svavqjadeYDBRQCNehtA0PskQIgDd2fgnPuktmfaoudayIHQhVJNRBHE0zUFFQ0bgANvRA="}], "size": 3420646}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.4_1686929953678_0.3623481486956168"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-16T15:39:13.999Z", "publish_time": 1686929953999, "_source_registry_name": "default"}, "0.18.5": {"name": "@esbuild/linux-ppc64", "version": "0.18.5", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-BksOs2uYTafS+u75QiN4RoLbEMNjE192adJCBalncI3E2PWyR2i1kEs9rEghHK7pw0SD0uWgV9otRmV7G5b2lQ==", "shasum": "f73322d6ad2ed93b4bd60b6920bc33c10eb957dc", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.5.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbKb9gOvtptcl7AZZDaIclVTI+PSrD9nDSj6c/OUt6WAiABjnPr0fOrUVV7Uh/Dn3/MWe1ezQRGSGq7SgCX7IkuWg=="}], "size": 3428183}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.5_1687222407094_0.20280224741456387"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T00:53:27.358Z", "publish_time": 1687222407358, "_source_registry_name": "default"}, "0.18.6": {"name": "@esbuild/linux-ppc64", "version": "0.18.6", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-B+wTKz+8pi7mcWXFQV0LA79dJ+qhiut5uK9q0omoKnq8yRIwQJwfg3/vclXoqqcX89Ri5Y5538V0Se2v5qlcLA==", "shasum": "7576dce4c2a7390ab21f6aeffbb14b1c5fd4d237", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.6.tgz", "fileCount": 3, "unpackedSize": 8716805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbk+w6AfvEQEcg5mF6zriU0srxlscDtaZ640F0FzLoRAIhAJIPM017Vc3iYNVTjCKirjLjJkUjiGyVdbhc0YFGnWWf"}], "size": 3430819}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.6_1687303530786_0.3294253855560132"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T23:25:31.039Z", "publish_time": 1687303531039, "_source_registry_name": "default"}, "0.18.7": {"name": "@esbuild/linux-ppc64", "version": "0.18.7", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-x/k1+daIqiGJt0Yhr5llFJ/zkRg1XAqcS2ntAYzS3pHogO8oIyc+LjsINgVyFCeFMFUZ9Ae9W5z2Ib05bMum3g==", "shasum": "faea79660896105276d95a3ca06156b8e41f5517", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.7.tgz", "fileCount": 3, "unpackedSize": 8782341, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGI9vgDvRuHS6Aw4VjpFy6MAGtVjuhRhcRLARbpzOz3AIhAKxw2GGrRZ1RumD+AA++FLv4hoZpnUbqNlnzrjhYKXhy"}], "size": 3446235}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.7_1687574822594_0.5284916660204324"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-24T02:47:02.904Z", "publish_time": 1687574822904, "_source_registry_name": "default"}, "0.18.8": {"name": "@esbuild/linux-ppc64", "version": "0.18.8", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-9AFk6CgYytoQ0/RMnmr1zlpTA88g9ksxk0gmo9apY+O8Yzmcjd+Dl9LUX9k89dLnyyLgkHl6uLg1tpEzpQS+yA==", "shasum": "42d242ef0444ea5443fb577a76e3de9cb7f11a95", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.8.tgz", "fileCount": 3, "unpackedSize": 8782341, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCx4kdPZt8WlCTccUW49UFxsZeuui5xsa0aEWDb3JygRgIgCS74eYIbOLBR2DW7k3HqzcwMaYCdVU/02vcK3lGVaBo="}], "size": 3447092}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.8_1687663188364_0.973949834007978"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-25T03:19:48.626Z", "publish_time": 1687663188626, "_source_registry_name": "default"}, "0.18.9": {"name": "@esbuild/linux-ppc64", "version": "0.18.9", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-y2viEHwLpNfWP1eLa+vV+DWIbw/pQyv1Vf6qxSGJeBQmmu9T2hOagMiCr6zhDo89l+MUAXiShdKmqlKI6HdCkw==", "shasum": "2f5ede177ba030070c17466c372d438690758297", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.9.tgz", "fileCount": 3, "unpackedSize": 8782341, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUo4Yve3X8x5edHq2TSSarGxmqAHk8s9Thl5ds8yaa0wIgOJTnWge2Hz1RZr7PbMnuNyIYIc8oXdlw+MAWvIHE4Sg="}], "size": 3451495}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.9_1687757341675_0.7260715987593733"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T05:29:01.930Z", "publish_time": 1687757341930, "_source_registry_name": "default"}, "0.18.10": {"name": "@esbuild/linux-ppc64", "version": "0.18.10", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-sO3PjjxEGy+PY2qkGe2gwJbXdZN9wAYpVBZWFD0AwAoKuXRkWK0/zaMQ5ekUFJDRDCRm8x5U0Axaub7ynH/wVg==", "shasum": "b5d179a6271d123b20694402364162bfa583b35b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.10.tgz", "fileCount": 3, "unpackedSize": 8782342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDY2/vTHPiMmXAGTyRUXyZ445NgU4Sae/xg2IPdp/ZH6gIhAM9E3VQovqoPwThVOWYWD4jp36rzG+X7a1SRj8HKCdOQ"}], "size": 3451697}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.10_1687814459663_0.5418973183514482"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T21:20:59.940Z", "publish_time": 1687814459940, "_source_registry_name": "default"}, "0.18.11": {"name": "@esbuild/linux-ppc64", "version": "0.18.11", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-T3yd8vJXfPirZaUOoA9D2ZjxZX4Gr3QuC3GztBJA6PklLotc/7sXTOuuRkhE9W/5JvJP/K9b99ayPNAD+R+4qQ==", "shasum": "842abadb7a0995bd539adee2be4d681b68279499", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.11.tgz", "fileCount": 3, "unpackedSize": 8782342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEIDpD4ucfFGjzSOGFpq2Zd3fWDo5JIY0IzdPSsHUGtAIgROPrfMS7Tongz8bPsYbKvvOmlCZammI8s1cKpK//WTM="}], "size": 3451976}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.11_1688191472340_0.10633085541453213"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-01T06:04:32.691Z", "publish_time": 1688191472691, "_source_registry_name": "default"}, "0.18.12": {"name": "@esbuild/linux-ppc64", "version": "0.18.12", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.12", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-TeN//1Ft20ZZW41+zDSdOI/Os1bEq5dbvBvYkberB7PHABbRcsteeoNVZFlI0YLpGdlBqohEpjrn06kv8heCJg==", "shasum": "38d0d25174e5307c443884e5723887e7dada49f1", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.12.tgz", "fileCount": 3, "unpackedSize": 8782342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuK90y5noVU2FvZdQC7J3jQuWBH4JP9qIFNcejC53A1wIhANDR8Cip6eiRfz/C4mRXMtOOOGU6QqDmDsbA69M3XfNq"}], "size": 3452546}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.12_1689212086996_0.39250119748930223"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-13T01:34:47.270Z", "publish_time": 1689212087270, "_source_registry_name": "default"}, "0.18.13": {"name": "@esbuild/linux-ppc64", "version": "0.18.13", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.13", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-pfn/OGZ8tyR8YCV7MlLl5hAit2cmS+j/ZZg9DdH0uxdCoJpV7+5DbuXrR+es4ayRVKIcfS9TTMCs60vqQDmh+w==", "shasum": "9a9befd275a6a3f5baeed89aaafb746df7ba735d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.13.tgz", "fileCount": 3, "unpackedSize": 8782342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEk3UJfMOGZVN8qdLzkwAko6eeRq5aRgMR6im5RMfRHQIhAM8MnYKTHVqlbWJfjbCehlXi5syb/H9M8NSm61Fs+AAa"}], "size": 3449832}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.13_1689388672166_0.6095371451160534"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-15T02:37:52.453Z", "publish_time": 1689388672453, "_source_registry_name": "default"}, "0.18.14": {"name": "@esbuild/linux-ppc64", "version": "0.18.14", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.14", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-aGzXzd+djqeEC5IRkDKt3kWzvXoXC6K6GyYKxd+wsFJ2VQYnOWE954qV2tvy5/aaNrmgPTb52cSCHFE+Z7Z0yg==", "shasum": "9b2bb80b7e30667a81ffbcddb74ad8e79330cc94", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.14.tgz", "fileCount": 3, "unpackedSize": 8847878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5WiHPiVhtGTXB0AgphXu2WPoMmfTVd8UWwx6ZlAyA3QIhAOuFarjANt9EcbGFylzPWvDTtaulPWReP8aokFVkYw5Y"}], "size": 3462408}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.14_1689656457133_0.747022189053107"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-18T05:00:57.491Z", "publish_time": 1689656457491, "_source_registry_name": "default"}, "0.18.15": {"name": "@esbuild/linux-ppc64", "version": "0.18.15", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.15", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-KXPY69MWw79QJkyvUYb2ex/OgnN/8N/Aw5UDPlgoRtoEfcBqfeLodPr42UojV3NdkoO4u10NXQdamWm1YEzSKw==", "shasum": "7dcb394e69cb47e4dc8a5960dd58b1a273d07f5d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.15.tgz", "fileCount": 3, "unpackedSize": 8847878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH/YDnDdFJH4irYiIgYGUbamXj4l30WBnEr5CpwW3dxPAiEApVCLrB4dPYEVC9n7z141Ug4mt6JCTOilCBxp30TC3Mk="}], "size": 3466635}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.15_1689857649605_0.95802736675408"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T12:54:09.845Z", "publish_time": 1689857649845, "_source_registry_name": "default"}, "0.18.16": {"name": "@esbuild/linux-ppc64", "version": "0.18.16", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.16", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Wkz++LZ29lDwUyTSEnzDaaP5OveOgTU69q9IyIw9WqLRxM4BjTBjz9un4G6TOvehWpf/J3gYVFN96TjGHrbcNQ==", "shasum": "d6913e7e9be9e242a6a20402800141bdbe7009f7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.16.tgz", "fileCount": 3, "unpackedSize": 8847878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMUr1jc1GIrxsWKCAtwKDU9Zs6iH5BLipVDsZYhVZ8sAIgc3EoyhhizSCQ9Lfv5ugZSXVlhv5Qz1al/8SaaDg4mTo="}], "size": 3467385}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.16_1690087722972_0.07536572612622572"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-23T04:48:43.245Z", "publish_time": 1690087723245, "_source_registry_name": "default"}, "0.18.17": {"name": "@esbuild/linux-ppc64", "version": "0.18.17", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.17", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-BAAilJ0M5O2uMxHYGjFKn4nJKF6fNCdP1E0o5t5fvMYYzeIqy2JdAP88Az5LHt9qBoUa4tDaRpfWt21ep5/WqQ==", "shasum": "7fdc0083d42d64a4651711ee0a7964f489242f45", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.17.tgz", "fileCount": 3, "unpackedSize": 8847878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNXzaJg12ZAcKWgzNI7fGz7vKpnkXadn+1zNFbfahckAiA7W07Erzn57/bkOuIO6HBtcVfzw3KCmDss4Q7aS064LQ=="}], "size": 3475085}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.17_1690335691782_0.9919443846903413"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T01:41:32.073Z", "publish_time": 1690335692073, "_source_registry_name": "default"}, "0.18.18": {"name": "@esbuild/linux-ppc64", "version": "0.18.18", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.18", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-fRChqIJZ7hLkXSKfBLYgsX9Ssb5OGCjk3dzCETF5QSS1qjTgayLv0ALUdJDB9QOh/nbWwp+qfLZU6md4XcjL7w==", "shasum": "2ea6a4e0c6b0db21770d2c3c1525623dceadfe46", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.18.tgz", "fileCount": 3, "unpackedSize": 8847878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJO/pLVYssRutzZ3CyJTYsETe5Yn371SpkscUqVbuHwQIgdC56nIucNfkqKStsaqbD+mznlPtgmc+URQbrW27tOvM="}], "size": 3480640}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.18_1691255222174_0.6995978146365269"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-05T17:07:02.474Z", "publish_time": 1691255222474, "_source_registry_name": "default"}, "0.18.19": {"name": "@esbuild/linux-ppc64", "version": "0.18.19", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.19", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tgG41lRVwlzqO9tv9l7aXYVw35BxKXLtPam1qALScwSqPivI8hjkZLNH0deaaSCYCFT9cBIdB+hUjWFlFFLL9A==", "shasum": "568b5a051f47af732c4314e697bb559a14b3d811", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.19.tgz", "fileCount": 3, "unpackedSize": 8913414, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFSxR1nIz0LqH5HH/RuP/Y/BlJKUGagDDy3InFKed2y0AiEA3gaE+OoixwTbwYHUwZLafNi8yU7j8tpfbkdaiwfdSAo="}], "size": 3495121}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.19_1691376717354_0.07718154018679502"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-07T02:51:57.623Z", "publish_time": 1691376717623, "_source_registry_name": "default"}, "0.18.20": {"name": "@esbuild/linux-ppc64", "version": "0.18.20", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.18.20", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==", "shasum": "2f7156bde20b01527993e6881435ad79ba9599fb", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.20.tgz", "fileCount": 3, "unpackedSize": 8913414, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFqadBbjzRvn0IMG/pjjxYkZdlfoQwuxRcAqjgJ0W1BnAiAt49LlLhPB0z5ybXRXVQ254q8Ggxyh7/bxK/N8ILExKA=="}], "size": 3497509}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.18.20_1691468148365_0.7171078309046024"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-08T04:15:48.675Z", "publish_time": 1691468148675, "_source_registry_name": "default"}, "0.19.0": {"name": "@esbuild/linux-ppc64", "version": "0.19.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.0", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-3LC6H5/gCDorxoRBUdpLV/m7UthYSdar0XcCu+ypycQxMS08MabZ06y1D1yZlDzL/BvOYliRNRWVG/YJJvQdbg==", "shasum": "104771ef6ce2719ac17031f6b9ed8aa98f8e5faf", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.0.tgz", "fileCount": 3, "unpackedSize": 8978949, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDf0TgVGMXinaFdoroDO4+AwQ8oi8Ov6CSIx0eoBD9CyAIgO10phyT7DxSO1xfghKPs62Gj+BVGPVNl33Y8rtCxCz4="}], "size": 3517849}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.0_1691509989245_0.3171047187221281"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-08T15:53:09.525Z", "publish_time": 1691509989525, "_source_registry_name": "default"}, "0.19.1": {"name": "@esbuild/linux-ppc64", "version": "0.19.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.1", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-mPOxA7bd3nmx8TkuO/9M/tE0fnvmuX0wlpwnTL6DPLgkb/Z/KkupexSIw4cLfznn/fPzD89y17VWBjlVNyrpCQ==", "shasum": "43d07d27dde2819ab6e0dd200174b9a7dd25cb1f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.1.tgz", "fileCount": 3, "unpackedSize": 8978949, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMMaoHqAV9klxK7Ux3ujBwjcQ9FH4+kfa6Nr6TLn+jjwIgW/vnzpqOCklLLcSsoqgw4ul5bmQDB496QAwoP/7n3WU="}], "size": 3522557}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.1_1691769490855_0.51449999804759"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-11T15:58:11.217Z", "publish_time": 1691769491217, "_source_registry_name": "default"}, "0.19.2": {"name": "@esbuild/linux-ppc64", "version": "0.19.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.2", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-dJ0kE8KTqbiHtA3Fc/zn7lCd7pqVr4JcT0JqOnbj4LLzYnp+7h8Qi4yjfq42ZlHfhOCM42rBh0EwHYLL6LEzcw==", "shasum": "d75798da391f54a9674f8c143b9a52d1dbfbfdde", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.2.tgz", "fileCount": 3, "unpackedSize": 8978949, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAKnag1+dLk6XmCP4S8RlyFoaFgWovJ300/bU9mmmF8mAiEA8w3dOZNXZ++7mTjnYUKuregBaTHNRPEF+2J9PfOuses="}], "size": 3523216}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.2_1691978339335_0.5671671759932131"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-14T01:58:59.606Z", "publish_time": 1691978339606, "_source_registry_name": "default"}, "0.19.3": {"name": "@esbuild/linux-ppc64", "version": "0.19.3", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.3", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-UrYLFu62x1MmmIe85rpR3qou92wB9lEXluwMB/STDzPF9k8mi/9UvNsG07Tt9AqwPQXluMQ6bZbTzYt01+Ue5g==", "shasum": "facf910b0d397e391b37b01a1b4f6e363b04e56b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.3.tgz", "fileCount": 3, "unpackedSize": 8978949, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGfOeK8XgCLB2jxLFXgg9mJ+0t8pOv/pZ/G8SF+umcrvAiB9m3Zktya5rnYnoA0WrkhgKfzxHcVNzdATvdYnqzzjig=="}], "size": 3525257}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.3_1694653977233_0.8568466390253535"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-14T01:12:57.511Z", "publish_time": 1694653977511, "_source_registry_name": "default"}, "0.19.4": {"name": "@esbuild/linux-ppc64", "version": "0.19.4", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.4", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tQ92n0WMXyEsCH4m32S21fND8VxNiVazUbU4IUGVXQpWiaAxOBvtOtbEt3cXIV3GEBydYsY8pyeRMJx9kn3rvw==", "shasum": "c49032f4abbcfa3f747b543a106931fe3dce41ff", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.4.tgz", "fileCount": 3, "unpackedSize": 8978949, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpAU4TeAwLYkG+MgOb9Yvg2IrNwTgvX5dfJEJDWWreLAIgNNaBHjxqX2nsxkSEAsXvcITWigvTBJL6se505XBIzUE="}], "size": 3525481}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.4_1695865728157_0.8989823524132332"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-28T01:48:48.465Z", "publish_time": 1695865728465, "_source_registry_name": "default"}, "0.19.5": {"name": "@esbuild/linux-ppc64", "version": "0.19.5", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.5", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-yJAxJfHVm0ZbsiljbtFFP1BQKLc8kUF6+17tjQ78QjqjAQDnhULWiTA6u0FCDmYT1oOKS9PzZ2z0aBI+Mcyj7Q==", "shasum": "7960cb1666f0340ddd9eef7b26dcea3835d472d0", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.5.tgz", "fileCount": 3, "unpackedSize": 8978949, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCY1T47E+absf9Wcc5IpuWtV7fHmZfbfzXkyQsasqQg1QIhANVqVIJ6K120MzSsSRmTNfITuUuegiN8eexZkt2Epk3c"}], "size": 3526322}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.5_1697519475697_0.29182227885487877"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-17T05:11:16.056Z", "publish_time": 1697519476056, "_source_registry_name": "default"}, "0.19.6": {"name": "@esbuild/linux-ppc64", "version": "0.19.6", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.6", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Qt+D7xiPajxVNk5tQiEJwhmarNnLPdjXAoA5uWMpbfStZB0+YU6a3CtbWYSy+sgAsnyx4IGZjWsTzBzrvg/fMA==", "shasum": "a7fccf924824999b301546843adb4f51051965e8", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.6.tgz", "fileCount": 3, "unpackedSize": 9044485, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDr+aa/pK760s4P2DSBOjbS6BULYp3EnFwO0iZR72gqigIgPTnecTfIMXHpkQ5G5XgjstF4n9wVrI/EjXcX2Bf+95Q="}], "size": 3535488}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.6_1700377930461_0.16508520869588783"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-19T07:12:10.767Z", "publish_time": 1700377930767, "_source_registry_name": "default"}, "0.19.7": {"name": "@esbuild/linux-ppc64", "version": "0.19.7", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.7", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-JQ1p0SmUteNdUaaiRtyS59GkkfTW0Edo+e0O2sihnY4FoZLz5glpWUQEKMSzMhA430ctkylkS7+vn8ziuhUugQ==", "shasum": "614eafd08b0c50212f287b948b3c08d6e60f221f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.7.tgz", "fileCount": 3, "unpackedSize": 9044485, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJxaR0qVioDezEDi6ZEzS5kwF6Im28xN1VNCYS2XV9QQIgLFzShmvDDF5yjvi/xPF2qhrtsFcOv88uHMFcpX/LxLc="}], "size": 3546439}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.7_1700528532957_0.7746530194626557"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-21T01:02:13.222Z", "publish_time": 1700528533222, "_source_registry_name": "default"}, "0.19.8": {"name": "@esbuild/linux-ppc64", "version": "0.19.8", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.8", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-ETaW6245wK23YIEufhMQ3HSeHO7NgsLx8gygBVldRHKhOlD1oNeNy/P67mIh1zPn2Hr2HLieQrt6tWrVwuqrxg==", "shasum": "9ba436addc1646dc89dae48c62d3e951ffe70951", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.8.tgz", "fileCount": 3, "unpackedSize": 9044485, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgIajgaHnpAVGET6+bVCbJ2C2uE9LbLOUSRfYvaBm2bAiEAxgI/6VnHOyPBixo1qBHwe4teFkjkV2UtfsKA57MVU0c="}], "size": 3545926}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.8_1701040126585_0.19420157750753875"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-26T23:08:46.826Z", "publish_time": 1701040126826, "_source_registry_name": "default"}, "0.19.9": {"name": "@esbuild/linux-ppc64", "version": "0.19.9", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.9", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tkV0xUX0pUUgY4ha7z5BbDS85uI7ABw3V1d0RNTii7E9lbmV8Z37Pup2tsLV46SQWzjOeyDi1Q7Wx2+QM8WaCQ==", "shasum": "8385332713b4e7812869622163784a5633f76fc4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.9.tgz", "fileCount": 3, "unpackedSize": 9175557, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+rbdhbFnh3Wotttr3wqdihQKq2ISyFX5rT/sxPUP5hAiBvs4z4v0vjsFt3OQnWv9qiU7IXbQPUBgOGmn3FqxrjAA=="}], "size": 3581411}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.9_1702185001068_0.3243726079329494"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-10T05:10:01.305Z", "publish_time": 1702185001305, "_source_registry_name": "default"}, "0.19.10": {"name": "@esbuild/linux-ppc64", "version": "0.19.10", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.10", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-tgT/7u+QhV6ge8wFMzaklOY7KqiyitgT1AUHMApau32ZlvTB/+efeCtMk4eXS+uEymYK249JsoiklZN64xt6oQ==", "shasum": "5d8b59929c029811e473f2544790ea11d588d4dd", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.10.tgz", "fileCount": 3, "unpackedSize": 9175558, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICfKFq4qRBK8D4AwBgEtFDlJupOouv4wdvNuIdZrrgqAAiEAvBJXgu1t0Vub4h/k8N7Kf+bJV+kxx+jPUujuq44ISd0="}], "size": 3584330}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.10_1702945338273_0.4533232131323459"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-19T00:22:18.532Z", "publish_time": 1702945338532, "_source_registry_name": "default"}, "0.19.11": {"name": "@esbuild/linux-ppc64", "version": "0.19.11", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ppc64@0.19.11", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-MHrZYLeCG8vXblMetWyttkdVRjQlQUb/oMgBNurVEnhj4YWOr4G5lmBfZjHYQHHN0g6yDmCAQRR8MUHldvvRDA==", "shasum": "9c5725a94e6ec15b93195e5a6afb821628afd912", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.11.tgz", "fileCount": 3, "unpackedSize": 9175558, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6dda21OG2yNa6lz/yGYXXITbTemKbkoMvZ5TNWB6sFAiBMce2m8g93yKejwOWzoO98ea/KdZ4zWBlXEnJkjC44ow=="}], "size": 3584585}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.11_1703881987145_0.6440635377050608"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-29T20:33:07.439Z", "publish_time": 1703881987439, "_source_registry_name": "default"}, "0.19.12": {"name": "@esbuild/linux-ppc64", "version": "0.19.12", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.19.12", "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-nYJA2/QPimDQOh1rKWedNOe3Gfc8PabU7HT3iXWtNUbRzXS9+vgB0Fjaqr//XNbd82mCxHzik2qotuI89cfixg==", "shasum": "adb67dadb73656849f63cd522f5ecb351dd8dee8", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.19.12.tgz", "fileCount": 3, "unpackedSize": 9175558, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmKxuQPchGuhgQ0k6Xj7N19Kmd3j+NT8cgiMVMtkFfKwIhAI0K8kMIX2SNQx29cpdCWrzswzHWGgS5QELNmZNa2u2H"}], "size": 3650520}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.19.12_1706031686236_0.3721895285508321"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-23T17:41:26.472Z", "publish_time": 1706031686472, "_source_registry_name": "default"}, "0.20.0": {"name": "@esbuild/linux-ppc64", "version": "0.20.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.20.0", "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-CoWSaaAXOZd+CjbUTdXIJE/t7Oz+4g90A3VBCHLbfuc5yUQU/nFDLOzQsN0cdxgXd97lYW/psIIBdjzQIwTBGw==", "shasum": "8f95baf05f9486343bceeb683703875d698708a4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.20.0.tgz", "fileCount": 3, "unpackedSize": 9175601, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDXS/NPSaoufDYiznLeFSbgOjTWEUV8zeOBhX9r7H928AiAR0Qz6/Grxx4QYssNMYh2Ztzw54qC2+lvEtkEOCA7Ucg=="}], "size": 3650927}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.20.0_1706374209571_0.6457016922316923"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-27T16:50:09.800Z", "publish_time": 1706374209800, "_source_registry_name": "default"}, "0.20.1": {"name": "@esbuild/linux-ppc64", "version": "0.20.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.20.1", "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-b+yuD1IUeL+Y93PmFZDZFIElwbmFfIKLKlYI8M6tRyzE6u7oEP7onGk0vZRh8wfVGC2dZoy0EqX1V8qok4qHaw==", "shasum": "0765a55389a99237b3c84227948c6e47eba96f0d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.20.1.tgz", "fileCount": 3, "unpackedSize": 9175601, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDihhddJoYW/I2Eo/aK3A3q/ZeM480pUs1vQmxbZ9jSTAiEA2kPRe/muhdC08gA41QOZZ1S4jXy8wAttOZ6I7W+Mi+I="}], "size": 3655708}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.20.1_1708324779470_0.6221617177530789"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-19T06:39:39.662Z", "publish_time": 1708324779662, "_source_registry_name": "default"}, "0.20.2": {"name": "@esbuild/linux-ppc64", "version": "0.20.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.20.2", "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-rD3KsaDprDcfajSKdn25ooz5J5/fWBylaaXkuotBDGnMnDP1Uv5DLAN/45qfnf3JDYyJv/ytGHQaziHUdyzaAg==", "shasum": "8d252f0b7756ffd6d1cbde5ea67ff8fd20437f20", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.20.2.tgz", "fileCount": 3, "unpackedSize": 9175601, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBwa8t83kP94B8PwsMEvui58c71A7xbCqa3RMJWMXYd+AiEA6mzvOnCbG2+WE+jbq55it5/G45XFFruc5pafliVaaBI="}], "size": 3657275}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.20.2_1710445819388_0.9737867471909818"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-14T19:50:19.676Z", "publish_time": 1710445819676, "_source_registry_name": "default"}, "0.21.0": {"name": "@esbuild/linux-ppc64", "version": "0.21.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.21.0", "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-wLi9VRnLDRg1Gudic24gcT5aa5LZGBwLi4aYghQ9bVb8z0qYHrZnRTNxulErFvOsSgijUWS5uNLCUaLwj+tvIQ==", "shasum": "4effa514e942d183fdbd6781285722a9fd0ce931", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.0.tgz", "fileCount": 3, "unpackedSize": 9241137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCV/h9A+hveCXwo7df03shGYKNvo2xQ0lbDJ7QRDr8sOgIhAPF7oDhUPtTF08BAdjEhjEzimk/GcdbzoGkyvy8xQDUV"}], "size": 3686549}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.21.0_1715050379091_0.8057164974399638"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T02:52:59.345Z", "publish_time": 1715050379345, "_source_registry_name": "default"}, "0.21.1": {"name": "@esbuild/linux-ppc64", "version": "0.21.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.21.1", "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-bw7bcQ+270IOzDV4mcsKAnDtAFqKO0jVv3IgRSd8iM0ac3L8amvCrujRVt1ajBTJcpDaFhIX+lCNRKteoDSLig==", "shasum": "2aafcfe2826c7d5d2e3c41eb8934e6368a7cada5", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.1.tgz", "fileCount": 3, "unpackedSize": 9241137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqEIfbkJLAs4Z6un42QHSyviqLerozdIPSXJm5ZG74vQIhANIolSG66IcQchOtoBVtpBp6rjBdWF4vNGr3hf9MfFcM"}], "size": 3687429}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.21.1_1715100971874_0.514816130477395"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T16:56:12.115Z", "publish_time": 1715100972115, "_source_registry_name": "default"}, "0.21.2": {"name": "@esbuild/linux-ppc64", "version": "0.21.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.21.2", "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-y0T4aV2CA+ic04ULya1A/8M2RDpDSK2ckgTj6jzHKFJvCq0jQg8afQQIn4EM0G8u2neyOiNHgSF9YKPfuqKOVw==", "shasum": "d9e79563999288d367eeba2b8194874bef0e8a35", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.2.tgz", "fileCount": 3, "unpackedSize": 9241137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBcxqTvxDK2V8/U1f4J4iyh6MbeoV61NWIZRgC5m1FT5AiB6VkQ+3YkC9CI0Xlx44qEwzgkhfz7KTvsLrk9o7gNwKw=="}], "size": 3687652}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.21.2_1715546004525_0.7742681356953307"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-12T20:33:24.800Z", "publish_time": 1715546004800, "_source_registry_name": "default"}, "0.21.3": {"name": "@esbuild/linux-ppc64", "version": "0.21.3", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.21.3", "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-h0zj1ldel89V5sjPLo5H1SyMzp4VrgN1tPkN29TmjvO1/r0MuMRwJxL8QY05SmfsZRs6TF0c/IDH3u7XYYmbAg==", "shasum": "23b9064d5bc0bf28a115a2f9cf69f3b01cdfe01c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.3.tgz", "fileCount": 3, "unpackedSize": 9175601, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJ8DsNB9kksiXoeOEfilVBL++sW0S0dW3eqdpbVhNjUgIgVUKIOytdlzMn1IUnsK9VibAEmK3NyEUMFvc93/4FPmM="}], "size": 3684136}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.21.3_1715806387545_0.00575995683497843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T20:53:07.822Z", "publish_time": 1715806387822, "_source_registry_name": "default"}, "0.21.4": {"name": "@esbuild/linux-ppc64", "version": "0.21.4", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.21.4", "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-yB8AYzOTaL0D5+2a4xEy7OVvbcypvDR05MsB/VVPVA7nL4hc5w5Dyd/ddnayStDgJE59fAgNEOdLhBxjfx5+dg==", "shasum": "ea3b5e13b0fc8666bd4c6f7ea58bd1830f3e6e78", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.4.tgz", "fileCount": 3, "unpackedSize": 9241137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCayc3hAK2T2Jogcr61/HfTG8agQTGIuaDp2YEB8+bWKQIgCPoBL8ouuy0NLiHE9fimPMxp/iNYmWZqsBF4jVYfX08="}], "size": 3689484}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.21.4_1716603080387_0.4001268684016601"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-25T02:11:20.668Z", "publish_time": 1716603080668, "_source_registry_name": "default"}, "0.21.5": {"name": "@esbuild/linux-ppc64", "version": "0.21.5", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.21.5", "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==", "shasum": "5f2203860a143b9919d383ef7573521fb154c3e4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz", "fileCount": 3, "unpackedSize": 9241137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHt5YyWsJS2wF+Q7S1UmN2fkgBh9G3oPYKoVIG7ob4hpAiAvMUhk2XoZKMb2o9LA9vtCCC/f9SMjpNInk7ijeF1K3w=="}], "size": 3690416}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.21.5_1717967851054_0.021849120878972617"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-09T21:17:31.268Z", "publish_time": 1717967851268, "_source_registry_name": "default"}, "0.22.0": {"name": "@esbuild/linux-ppc64", "version": "0.22.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.22.0", "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-ewxg6FLLUio883XgSjfULEmDl3VPv/TYNnRprVAS3QeGFLdCYdx1tIudBcd7n9jIdk82v1Ajov4jx87qW7h9+g==", "shasum": "6609478066083e05cc1854a8b272daf62a7e944b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.22.0.tgz", "fileCount": 3, "unpackedSize": 9700041, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmyyf1JjVJ25qzYyZqBriTukTqZmMFLLL/bxm3PsKU3AiEA3IBalzfA6LGiPILL+JK35gP6zc9S8bj2U3mYiDvgw3A="}], "size": 3863381}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.22.0_1719779905763_0.11745563067726472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T20:38:26.008Z", "publish_time": 1719779906008, "_source_registry_name": "default"}, "0.23.0": {"name": "@esbuild/linux-ppc64", "version": "0.23.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.23.0", "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-cShCXtEOVc5GxU0fM+dsFD10qZ5UpcQ8AM22bYj0u/yaAykWnqXJDpd77ublcX6vdDsWLuweeuSNZk4yUxZwtw==", "shasum": "f3a79fd636ba0c82285d227eb20ed8e31b4444f6", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.23.0.tgz", "fileCount": 3, "unpackedSize": 9700041, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF9AsP/88xuGbx8hhkkwj9EvNFFfUBwwHAT4o3FDX7/9AiB7UZzjx4BdJkCKIIXT5keJxe5JT7f5T923lxhRW/F7Wg=="}], "size": 3863447}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.23.0_1719891258421_0.5327332050136335"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T03:34:18.694Z", "publish_time": 1719891258694, "_source_registry_name": "default"}, "0.23.1": {"name": "@esbuild/linux-ppc64", "version": "0.23.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.23.1", "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-dKN8fgVqd0vUIjxuJI6P/9SSSe/mB9rvA98CSH2sJnlZ/OCZWO1DJvxj8jvKTfYUdGfcq2dDxoKaC6bHuTlgcw==", "shasum": "06a2744c5eaf562b1a90937855b4d6cf7c75ec96", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.23.1.tgz", "fileCount": 3, "unpackedSize": 9700041, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHdB9JRwB+u5vPxaeAuOFxtia6fD18ibFX5SVZ7mVi7gIhALdKq0rEbBwcLE2W3WHOF8mskzyfW+bCdFtBOCkJWyWI"}], "size": 3866101}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.23.1_1723846433916_0.2683047542267709"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-16T22:13:54.241Z", "publish_time": 1723846434241, "_source_registry_name": "default"}, "0.24.0": {"name": "@esbuild/linux-ppc64", "version": "0.24.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.24.0", "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-HcZh5BNq0aC52UoocJxaKORfFODWXZxtBaaZNuN3PUX3MoDsChsZqopzi5UupRhPHSEHotoiptqikjN/B77mYQ==", "shasum": "9d6b188b15c25afd2e213474bf5f31e42e3aa09e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.24.0.tgz", "fileCount": 3, "unpackedSize": 9831113, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB9qMCU9SaqZrtw+DTURf5/Crk63d1jRHR9sr1Eln7+eAiEAtfTZ48BOUybxjaLsJPtQ9+yYriyMcAT0W8NLWrFBgAw="}], "size": 3945023}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ppc64_0.24.0_1726970820170_0.2118986384053303"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-22T02:07:00.445Z", "publish_time": 1726970820445, "_source_registry_name": "default"}, "0.24.1": {"name": "@esbuild/linux-ppc64", "version": "0.24.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.24.1", "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-I+XQCBhTIXKqyLFDcyMP9Dp0u0fx2TiH3BTh4iIg58/a5hmS3l3Yr2AHG8gEsmjUA7WGfKy2ZqxsaVud15iI1w==", "shasum": "261c3ac86b75940e4b0e81e9676cbdb59b60a176", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.24.1.tgz", "fileCount": 3, "unpackedSize": 9831113, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFDD05eliu0caGAAQTLmOvILQWt/DWljdZBVic1GMgQoAiBaGE83EQ40PBnyTkcwsOVsUOIWHSB7DPVjHoq7TDfFBw=="}], "size": 3949259}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.24.1_1734673332865_0.8623482411887837"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T05:42:13.112Z", "publish_time": 1734673333112, "_source_registry_name": "default"}, "0.24.2": {"name": "@esbuild/linux-ppc64", "version": "0.24.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.24.2", "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-shsVrgCZ57Vr2L8mm39kO5PPIb+843FStGt7sGGoqiiWYconSxwTiuswC1VJZLCjNiMLAMh34jg4VSEQb+iEbw==", "shasum": "8e3fc54505671d193337a36dfd4c1a23b8a41412", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.24.2.tgz", "fileCount": 3, "unpackedSize": 9831113, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOvNpbvnLuek8r/6Ujk62CugWGSPhQ2n2z27xsnSN+hAIgM4bvOG2yDK81zmNXIjbMMbpQyVp4e69CZmG1409LetM="}], "size": 3950103}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.24.2_1734717454304_0.8270336700189529"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T17:57:34.639Z", "publish_time": 1734717454639, "_source_registry_name": "default"}, "0.25.0": {"name": "@esbuild/linux-ppc64", "version": "0.25.0", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.0", "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw==", "shasum": "8860a4609914c065373a77242e985179658e1951", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.0.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHndr88ips5mUUli4/wWaIw0EXjlbW6Xl+MT/yGRTImBAiEAwgc8fnp8+YJjYQb6TaOD46zppYfs1AKFdq22O2tnqA0="}], "size": 3974511}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.0_1738983779210_0.8332746992855509"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-08T03:02:59.412Z", "publish_time": 1738983779412, "_source_registry_name": "default"}, "0.25.1": {"name": "@esbuild/linux-ppc64", "version": "0.25.1", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.1", "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-/6VBJOwUf3TdTvJZ82qF3tbLuWsscd7/1w+D9LH0W/SqUgM5/JJD0lrJ1fVIfZsqB6RFmLCe0Xz3fmZc3WtyVg==", "shasum": "5d7e6b283a0b321ea42c6bc0abeb9eb99c1f5589", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.1.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH3sAi361hdwt7cilzGwoum5B8gnfMnlRg3aQhTTifCtAiEAr3qpdyd/OXFYXRk8BV5XSJRPGEudFEO5g+YA/NrufqE="}], "size": 3976402}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.1_1741578372926_0.11957265038146003"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-10T03:46:13.148Z", "publish_time": 1741578373148, "_source_registry_name": "default"}, "0.25.2": {"name": "@esbuild/linux-ppc64", "version": "0.25.2", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.2", "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-tsHu2RRSWzipmUi9UBDEzc0nLc4HtpZEI5Ba+Omms5456x5WaNuiG3u7xh5AO6sipnJ9r4cRWQB2tUjPyIkc6g==", "shasum": "6bf8695cab8a2b135cca1aa555226dc932d52067", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.2.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCIbcuvY779gIMJYOGGJn83kZ7CxGTFxQeHjRDWtqptOQIhAJI3mvbZKZNlKpEwtRE3w71jbzuOV4YU9COWkuK2MH/2"}], "size": 3976469}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.2_1743356022999_0.8089578872769014"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-30T17:33:43.246Z", "publish_time": 1743356023246, "_source_registry_name": "default"}, "0.25.3": {"name": "@esbuild/linux-ppc64", "version": "0.25.3", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.3", "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-ZC4jV2p7VbzTlnl8nZKLcBkfzIf4Yad1SJM4ZMKYnJqZFD4rTI+pBG65u8ev4jk3/MPwY9DvGn50wi3uhdaghg==", "shasum": "2e85d9764c04a1ebb346dc0813ea05952c9a5c56", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.3.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGZOntZLWbOU1cRJylXoAmjFl/IkL+PuqMSj8GCoq8tAAiEA+Jlo/krFKulYqaxwU/BbiBFElwlpKMvMwhn+rZ+ylQM="}], "size": 3977855}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.3_1745380625127_0.34851544793788647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-23T03:57:05.427Z", "publish_time": 1745380625427, "_source_registry_name": "default"}, "0.25.4": {"name": "@esbuild/linux-ppc64", "version": "0.25.4", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.4", "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-HOy0aLTJTVtoTeGZh4HSXaO6M95qu4k5lJcH4gxv56iaycfz1S8GO/5Jh6X4Y1YiI0h7cRyLi+HixMR+88swag==", "shasum": "64f4ae0b923d7dd72fb860b9b22edb42007cf8f5", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.4.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIADJ5hvy8ttkxv33P5S7JhcU9QKlFo9YUxScr61Mr1anAiEA9ftA3e1O86MYZ0XvCGH8CPtMjHURfjUMk54eaWBChNg="}], "size": 3979685}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.4_1746491489760_0.06536621469675175"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T00:31:30.101Z", "publish_time": 1746491490101, "_source_registry_name": "default"}, "0.25.5": {"name": "@esbuild/linux-ppc64", "version": "0.25.5", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==", "shasum": "0d954ab39ce4f5e50f00c4f8c4fd38f976c13ad9", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCHAy3P6aivZI1C2xt+6rAERcVpMtx7Wc0BgeMPATpOfgIgHI2ZyRzJbgNKOKoWjPek3vl8WnlCG1N0DjoMr9YRMIw="}], "size": 3980235}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.5_1748315620180_0.4839890188473457"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-27T03:13:40.420Z", "publish_time": 1748315620420, "_source_registry_name": "default"}, "0.25.6": {"name": "@esbuild/linux-ppc64", "version": "0.25.6", "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ppc64"], "_id": "@esbuild/linux-ppc64@0.25.6", "gitHead": "d38c1f0bc580b4a8a93f23559d0cd9085d7ba31f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-wyYKZ9NTdmAMb5730I38lBqVu6cKl4ZfYXIs31Baf8aoOtB4xSGi3THmDYt4BTFHk7/EcVixkOV2uZfwU3Q2Jw==", "shasum": "477cbf8bb04aa034b94f362c32c86b5c31db8d3e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.6.tgz", "fileCount": 3, "unpackedSize": 9962185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDmoWzZ99EKgyae4fz292bi3QOSaneMcO14bvrcJePSMgIhAJsSqVWSNlw8VEuo8wF42G0QpVIP2+//zOs9B7iviN9i"}], "size": 3982015}, "_npmUser": {"name": "evanw", "email": "<EMAIL>", "actor": {"name": "evanw", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ppc64_0.25.6_1751907591177_0.32617087117480303"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T16:59:51.435Z", "publish_time": 1751907591435, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "description": "The Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the Linux PowerPC 64-bit Little Endian binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "_source_registry_name": "default"}