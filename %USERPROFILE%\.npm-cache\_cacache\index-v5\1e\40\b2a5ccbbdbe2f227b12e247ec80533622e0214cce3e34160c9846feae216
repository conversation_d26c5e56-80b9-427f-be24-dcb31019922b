
84340675350ae84b43ab2f4892422ea8d10ffff0	{"key":"make-fetch-happen:request-cache:https://cdn.npmmirror.com/packages/%40babel/plugin-transform-named-capturing-groups-regex/7.27.1/plugin-transform-named-capturing-groups-regex-7.27.1.tgz","integrity":"sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==","time":1752472016751,"size":2291,"metadata":{"time":1752472011823,"url":"https://cdn.npmmirror.com/packages/%40babel/plugin-transform-named-capturing-groups-regex/7.27.1/plugin-transform-named-capturing-groups-regex-7.27.1.tgz","reqHeaders":{},"resHeaders":{"cache-control":"max-age=86400","content-type":"application/octet-stream","date":"Mon, 14 Jul 2025 02:12:34 GMT","etag":"\"F3247A944A8BDFD8B36569FA9920D5D0\"","last-modified":"Wed, 30 Apr 2025 15:09:19 GMT"},"options":{"compress":true}}}