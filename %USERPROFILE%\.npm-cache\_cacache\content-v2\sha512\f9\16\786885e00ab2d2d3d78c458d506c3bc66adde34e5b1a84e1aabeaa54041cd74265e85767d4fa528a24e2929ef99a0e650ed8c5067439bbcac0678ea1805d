{"_attachments": {}, "_id": "lodash", "_rev": "249-61f1443323990e8a812e2850", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Lodash modular utilities.", "dist-tags": {"latest": "4.17.21"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bnjmnt4n", "email": "<EMAIL>"}], "name": "lodash", "readme": "# lodash v4.17.21\n\nThe [Lodash](https://lodash.com/) library exported as [Node.js](https://nodejs.org/) modules.\n\n## Installation\n\nUsing npm:\n```shell\n$ npm i -g npm\n$ npm i --save lodash\n```\n\nIn Node.js:\n```js\n// Load the full build.\nvar _ = require('lodash');\n// Load the core build.\nvar _ = require('lodash/core');\n// Load the FP build for immutable auto-curried iteratee-first data-last methods.\nvar fp = require('lodash/fp');\n\n// Load method categories.\nvar array = require('lodash/array');\nvar object = require('lodash/fp/object');\n\n// Cherry-pick methods for smaller browserify/rollup/webpack bundles.\nvar at = require('lodash/at');\nvar curryN = require('lodash/fp/curryN');\n```\n\nSee the [package source](https://github.com/lodash/lodash/tree/4.17.21-npm) for more details.\n\n**Note:**<br>\nInstall [n_](https://www.npmjs.com/package/n_) for Lodash use in the Node.js < 6 REPL.\n\n## Support\n\nTested in Chrome 74-75, Firefox 66-67, IE 11, Edge 18, Safari 11-12, & Node.js 8-12.<br>\nAutomated [browser](https://saucelabs.com/u/lodash) & [CI](https://travis-ci.org/lodash/lodash/) test runs are available.\n", "time": {"created": "2022-01-26T12:53:07.923Z", "modified": "2025-07-12T04:19:51.167Z", "4.17.21": "2021-02-20T15:42:16.891Z", "4.17.20": "2020-08-13T16:53:54.152Z", "4.17.19": "2020-07-08T17:14:40.866Z", "4.17.18": "2020-07-08T16:07:27.110Z", "4.17.17": "2020-07-08T12:08:37.292Z", "4.17.16": "2020-07-08T10:50:37.915Z", "4.17.15": "2019-07-19T02:28:46.584Z", "4.17.14": "2019-07-10T15:44:39.173Z", "4.17.13": "2019-07-09T22:24:38.453Z", "4.17.12": "2019-07-09T21:07:51.647Z", "4.17.11": "2018-09-12T18:32:16.141Z", "4.17.10": "2018-04-24T18:07:37.696Z", "4.17.9": "2018-04-24T17:44:40.268Z", "4.17.5": "2018-02-04T00:34:41.111Z", "4.17.4": "2016-12-31T22:33:56.623Z", "4.17.3": "2016-12-24T14:25:39.754Z", "4.17.2": "2016-11-16T07:21:41.106Z", "4.17.1": "2016-11-15T07:03:25.950Z", "4.17.0": "2016-11-14T07:00:08.291Z", "4.16.6": "2016-11-01T06:38:07.989Z", "4.16.5": "2016-10-31T06:49:14.797Z", "4.16.4": "2016-10-06T15:13:30.196Z", "4.16.3": "2016-10-03T16:43:31.571Z", "4.16.2": "2016-09-26T03:11:05.302Z", "4.16.1": "2016-09-20T16:59:53.967Z", "4.16.0": "2016-09-19T14:59:14.886Z", "4.15.0": "2016-08-12T14:39:28.783Z", "4.14.2": "2016-08-08T15:35:49.019Z", "4.14.1": "2016-07-29T14:49:10.278Z", "4.14.0": "2016-07-24T18:40:58.495Z", "4.13.1": "2016-05-23T15:59:05.944Z", "4.13.0": "2016-05-23T05:07:23.403Z", "4.12.0": "2016-05-08T19:25:43.826Z", "4.11.2": "2016-05-02T15:01:02.189Z", "4.11.1": "2016-04-14T07:21:23.548Z", "4.11.0": "2016-04-13T15:32:30.507Z", "4.10.0": "2016-04-11T14:43:56.586Z", "4.9.0": "2016-04-08T15:22:34.228Z", "4.8.2": "2016-04-05T02:15:16.661Z", "4.8.1": "2016-04-04T15:43:49.109Z", "4.8.0": "2016-04-04T14:54:33.612Z", "4.7.0": "2016-03-31T15:46:33.373Z", "4.6.1": "2016-03-02T18:09:40.696Z", "4.6.0": "2016-03-02T03:24:37.179Z", "4.5.1": "2016-02-22T06:42:24.244Z", "4.5.0": "2016-02-17T08:39:42.533Z", "4.4.0": "2016-02-16T07:10:16.856Z", "4.3.0": "2016-02-08T08:57:19.880Z", "4.2.1": "2016-02-03T16:00:16.046Z", "4.2.0": "2016-02-02T08:50:17.287Z", "4.1.0": "2016-01-29T16:33:24.543Z", "4.0.1": "2016-01-25T16:06:17.924Z", "4.0.0": "2016-01-12T23:13:20.539Z", "3.10.1": "2015-08-04T06:05:06.887Z", "3.10.0": "2015-06-30T15:13:28.602Z", "3.9.3": "2015-05-26T01:47:44.058Z", "3.9.2": "2015-05-24T20:57:57.973Z", "3.9.1": "2015-05-19T21:00:20.625Z", "3.9.0": "2015-05-19T18:26:55.450Z", "3.8.0": "2015-05-01T15:45:44.760Z", "2.4.2": "2015-04-26T21:04:49.443Z", "3.7.0": "2015-04-16T15:47:35.770Z", "1.0.2": "2015-03-30T15:58:01.337Z", "3.6.0": "2015-03-25T15:36:29.983Z", "3.5.0": "2015-03-09T05:01:51.264Z", "3.4.0": "2015-03-06T16:44:06.018Z", "3.3.1": "2015-02-24T16:02:47.458Z", "3.3.0": "2015-02-20T17:08:28.864Z", "3.2.0": "2015-02-12T17:01:18.403Z", "3.1.0": "2015-02-03T16:53:35.795Z", "3.0.1": "2015-01-30T09:33:51.621Z", "3.0.0": "2015-01-26T15:09:31.198Z", "2.4.1": "2013-12-03T16:51:12.879Z", "2.4.0": "2013-11-26T19:40:30.164Z", "2.3.0": "2013-11-11T17:30:27.058Z", "2.2.1": "2013-10-03T18:29:30.163Z", "2.2.0": "2013-09-29T21:52:47.266Z", "2.1.0": "2013-09-23T05:57:42.595Z", "2.0.0": "2013-09-14T04:22:28.159Z", "1.3.1": "2013-09-04T14:25:40.429Z", "1.3.0": "2013-09-04T14:25:19.793Z", "1.2.1": "2013-09-04T14:24:58.381Z", "1.2.0": "2013-09-04T14:24:34.140Z", "1.1.1": "2013-09-04T14:24:07.907Z", "1.1.0": "2013-09-04T14:23:45.728Z", "1.0.1": "2013-08-31T05:16:47.715Z", "1.0.0": "2013-08-31T05:11:42.645Z", "1.0.0-rc.3": "2013-08-31T05:08:51.705Z", "1.0.0-rc.2": "2013-08-31T05:05:31.147Z", "1.0.0-rc.1": "2013-08-31T05:00:28.060Z", "0.10.0": "2013-08-31T04:56:09.871Z", "0.9.2": "2013-08-31T04:52:21.307Z", "0.9.1": "2013-08-31T04:49:15.754Z", "0.9.0": "2013-08-31T04:46:20.474Z", "0.8.2": "2012-10-10T07:51:31.600Z", "0.8.1": "2012-10-04T08:53:29.540Z", "0.8.0": "2012-10-02T06:49:38.116Z", "0.7.0": "2012-09-11T16:24:07.425Z", "0.6.1": "2012-08-30T08:01:38.808Z", "0.6.0": "2012-08-28T16:01:09.459Z", "0.5.2": "2012-08-22T16:22:03.757Z", "0.5.1": "2012-08-18T20:15:42.131Z", "0.5.0": "2012-08-17T20:13:07.054Z", "0.5.0-rc.1": "2012-08-07T15:08:27.331Z", "0.4.2": "2012-07-16T18:49:41.162Z", "0.4.1": "2012-07-12T04:56:31.883Z", "0.4.0": "2012-07-11T17:14:20.142Z", "0.3.2": "2012-06-14T19:19:49.846Z", "0.3.1": "2012-06-11T04:12:51.792Z", "0.3.0": "2012-06-06T20:01:49.669Z", "0.2.2": "2012-05-30T07:56:26.644Z", "0.2.1": "2012-05-24T21:53:08.449Z", "0.2.0": "2012-05-22T04:06:24.044Z", "0.1.0": "2012-04-23T16:37:12.603Z"}, "versions": {"4.17.21": {"name": "lodash", "version": "4.17.21", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "gitHead": "c6e281b878b315c7a10d90f9c2af4cdb112d9625", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.21", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"shasum": "679591c564c3bffaae8454cf0b3df370c3d6911c", "size": 318961, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bnjmnt4n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.21_1613835736675_0.01913912595366596"}, "_hasShrinkwrap": false, "publish_time": 1613835736891, "_cnpm_publish_time": 1613835736891, "_cnpmcore_publish_time": "2021-12-13T06:37:54.524Z"}, "4.17.20": {"name": "lodash", "version": "4.17.20", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "gitHead": "f2e7063ee409ff40a60b14370c58dceee1a2efd4", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.20", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.7", "dist": {"shasum": "b44a9b6297bcb698f1c51a3545a2b3b368d59c52", "size": 316680, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.20.tgz", "integrity": "sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA=="}, "maintainers": [{"email": "<EMAIL>", "name": "bnjmnt4n"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON>ias"}], "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.20_1597337634014_0.4491479741207298"}, "_hasShrinkwrap": false, "publish_time": 1597337634152, "_cnpm_publish_time": 1597337634152, "_cnpmcore_publish_time": "2021-12-13T06:37:55.179Z"}, "4.17.19": {"name": "lodash", "version": "4.17.19", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.19", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.4", "dist": {"shasum": "e48ddedbe30b3321783c5b4301fbd353bc1e4a4b", "size": 315311, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.19.tgz", "integrity": "sha512-JNvd8XER9GQX0v2qJgsaN/mzFCNA5BRe/j8JN9d+tWyGLSodKQHKFicdwNYzWwI3wjRnaKPsGj1XkBjx/F96DQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.19_1594228480690_0.8185161063468651"}, "_hasShrinkwrap": false, "publish_time": 1594228480866, "_cnpm_publish_time": 1594228480866, "_cnpmcore_publish_time": "2021-12-13T06:37:55.773Z"}, "4.17.18": {"name": "lodash", "version": "4.17.18", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.15", "_nodeVersion": "12.2.0", "_npmVersion": "6.10.1", "dist": {"shasum": "b447f6670a0455bbfeedd11392eff330ea097548", "size": 314170, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.15_1563503326395_0.45150483878351166"}, "_hasShrinkwrap": false, "publish_time": 156**********, "_cnpm_publish_time": 156**********, "_cnpmcore_publish_time": "2021-12-13T06:37:57.866Z", "deprecated": "[WARNING] Use 4.17.15 instead of 4.17.18, reason: https://github.com/lodash/lodash/issues/4850"}, "4.17.17": {"name": "lodash", "version": "4.17.17", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.15", "_nodeVersion": "12.2.0", "_npmVersion": "6.10.1", "dist": {"shasum": "b447f6670a0455bbfeedd11392eff330ea097548", "size": 314170, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.15_1563503326395_0.45150483878351166"}, "_hasShrinkwrap": false, "publish_time": 156**********, "_cnpm_publish_time": 156**********, "_cnpmcore_publish_time": "2021-12-13T06:37:57.866Z", "deprecated": "[WARNING] Use 4.17.15 instead of 4.17.17, reason: https://github.com/lodash/lodash/issues/4848"}, "4.17.16": {"name": "lodash", "version": "4.17.16", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.15", "_nodeVersion": "12.2.0", "_npmVersion": "6.10.1", "dist": {"shasum": "b447f6670a0455bbfeedd11392eff330ea097548", "size": 314170, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.15_1563503326395_0.45150483878351166"}, "_hasShrinkwrap": false, "publish_time": 156**********, "_cnpm_publish_time": 156**********, "_cnpmcore_publish_time": "2021-12-13T06:37:57.866Z", "deprecated": "[WARNING] Use 4.17.15 instead of 4.17.16, reason: https://github.com/lodash/lodash/issues/4846"}, "4.17.15": {"name": "lodash", "version": "4.17.15", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.15", "_nodeVersion": "12.2.0", "_npmVersion": "6.10.1", "dist": {"shasum": "b447f6670a0455bbfeedd11392eff330ea097548", "size": 314170, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.15_1563503326395_0.45150483878351166"}, "_hasShrinkwrap": false, "publish_time": 156**********, "_cnpm_publish_time": 156**********, "_cnpmcore_publish_time": "2021-12-13T06:37:57.866Z"}, "4.17.14": {"name": "lodash", "version": "4.17.14", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.14", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9ce487ae66c96254fe20b599f21b6816028078ba", "size": 307301, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.14.tgz", "integrity": "sha512-mmKYbW3GLuJeX+iGP+Y7Gp1AiGHGbXHCOh/jZmrawMmsE7MS4znI3RL2FsjbqOyMayHInjOeykW7PEajUk1/xw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.14_1562773479015_0.866734273135471"}, "_hasShrinkwrap": false, "publish_time": 1562773479173, "_cnpm_publish_time": 1562773479173, "_cnpmcore_publish_time": "2021-12-13T06:37:58.439Z"}, "4.17.13": {"name": "lodash", "version": "4.17.13", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.13", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.2", "dist": {"shasum": "0bdc3a6adc873d2f4e0c4bac285df91b64fc7b93", "size": 306707, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.13.tgz", "integrity": "sha512-vm3/XWXfWtRua0FkUyEHBZy8kCPjErNBT9fJx8Zvs+U6zjqPbTUOpkaoum3O5uiA8sm+yNMHXfYkTUHFoMxFNA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.13_1562711078256_0.5295983360095642"}, "_hasShrinkwrap": false, "publish_time": 1562711078453, "_cnpm_publish_time": 1562711078453, "_cnpmcore_publish_time": "2021-12-13T06:37:59.007Z"}, "4.17.12": {"name": "lodash", "version": "4.17.12", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.12", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.2", "dist": {"shasum": "a712c74fdc31f7ecb20fe44f157d802d208097ef", "size": 306705, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.12.tgz", "integrity": "sha512-+CiwtLnsJhX03p20mwXuvhoebatoh5B3tt+VvYlrPgZC1g36y+RRbkufX95Xa+X4I59aWEacDFYwnJZiyBh9gA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.12_1562706471399_0.7178658287459723"}, "_hasShrinkwrap": false, "publish_time": 1562706471647, "_cnpm_publish_time": 1562706471647, "_cnpmcore_publish_time": "2021-12-13T06:37:59.502Z"}, "4.17.11": {"name": "lodash", "version": "4.17.11", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.11", "_npmVersion": "6.3.0", "_nodeVersion": "6.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b39ea6229ef607ecd89e2c8df12536891cac9b8d", "size": 305584, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.11.tgz", "integrity": "sha512-cQKh8igo5QUhZ7lg38DYWAxMvjSAKG0A8wGSVimP07SIUEK2UO+arSRKbRZWtelMtN5V0Hkwh5ryOto/SshYIg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.11_1536777135932_0.2638891224123521"}, "_hasShrinkwrap": false, "publish_time": 1536777136141, "_cnpm_publish_time": 1536777136141, "_cnpmcore_publish_time": "2021-12-13T06:37:59.992Z"}, "4.17.10": {"name": "lodash", "version": "4.17.10", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.10", "_npmVersion": "6.0.0-next.2", "_nodeVersion": "9.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1b7793cf7259ea38fb3661d4d38b3260af8ae4e7", "size": 305668, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.10.tgz", "integrity": "sha512-UejweD1pDoXu+AD825lWwp4ZGtSwgnpZxb3JDViD7StjQz+Nb/6l093lx4OQ0foGWNRoc19mWy7BzL+UAK2iVg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.10_1524593257507_0.03186110402277009"}, "_hasShrinkwrap": false, "publish_time": 1524593257696, "_cnpm_publish_time": 1524593257696, "_cnpmcore_publish_time": "2021-12-13T06:38:00.481Z"}, "4.17.9": {"name": "lodash", "version": "4.17.9", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.9", "_npmVersion": "6.0.0-next.2", "_nodeVersion": "9.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9c056579af0bdbb4322e23c836df13ef2b271cb7", "size": 305642, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.9.tgz", "integrity": "sha512-vuRLquvot5sKUldMBumG0YqLvX6m/RGBBOmqb3CWR/MC/QvvD1cTH1fOqxz2FJAQeoExeUdX5Gu9vP2EP6ik+Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash_4.17.9_1524591880132_0.5972035908632127"}, "_hasShrinkwrap": false, "publish_time": 1524591880268, "_cnpm_publish_time": 1524591880268, "_cnpmcore_publish_time": "2021-12-13T06:38:01.019Z"}, "4.17.5": {"name": "lodash", "version": "4.17.5", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.5", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "99a92d65c0272debe8c96b6057bc8fbfa3bed511", "size": 305530, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.5.tgz", "integrity": "sha512-svL3uiZf1RwhH+cWrfZn3A4+U58wbP0tGVTLQPbjplZxZ8ROD9VLuNgsRniTlLe7OlSqR79RUehXgpBW/s0IQw=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON>ias"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash-4.17.5.tgz_1517704479112_0.7115055937319994"}, "directories": {}, "publish_time": 1517704481111, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517704481111, "_cnpmcore_publish_time": "2021-12-13T06:38:01.545Z"}, "4.17.4": {"name": "lodash", "version": "4.17.4", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.4", "_shasum": "78203a4d1c328ae1d86dca6460e369b57f4055ae", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "7.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "78203a4d1c328ae1d86dca6460e369b57f4055ae", "size": 310669, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.4.tgz", "integrity": "sha512-6X37Sq9KCpLSXEh8uM12AKYlviHPNNk4RxiGBn4cmKGJinbXBneWIV7iE/nXkM928O7ytHcHb6+X6Svl0f4hXg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.17.4.tgz_1483223634314_0.5332164366263896"}, "directories": {}, "publish_time": 1483223636623, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483223636623, "_cnpmcore_publish_time": "2021-12-13T06:38:02.027Z"}, "4.17.3": {"name": "lodash", "version": "4.17.3", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.3", "_shasum": "557ed7d2a9438cac5fd5a43043ca60cb455e01f7", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "7.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "557ed7d2a9438cac5fd5a43043ca60cb455e01f7", "size": 310673, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.3.tgz", "integrity": "sha512-H+sg4+uBLOBrw9833P6gCURJjV+puWPbxM8S3H4ORlhVCmQpF5yCE50bc4Exaqm9U5Nhjw83Okq1azyb1U7mxw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.17.3.tgz_1482589537076_0.02431614836677909"}, "directories": {}, "publish_time": 1482589539754, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482589539754, "_cnpmcore_publish_time": "2021-12-13T06:38:02.633Z"}, "4.17.2": {"name": "lodash", "version": "4.17.2", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.2", "_shasum": "34a3055babe04ce42467b607d700072c7ff6bf42", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "7.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "34a3055babe04ce42467b607d700072c7ff6bf42", "size": 310476, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.2.tgz", "integrity": "sha512-8mozooKYfrbhO6eMFCjiieXZalOKHpwENKTqMn/g0TrH1j4MLw8qwivmVwfcX5/+3LQJmXAWRXWhWUL+COdYNg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/lodash-4.17.2.tgz_1479280899250_0.8391377755906433"}, "directories": {}, "publish_time": 1479280901106, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479280901106, "_cnpmcore_publish_time": "2021-12-13T06:38:03.176Z"}, "4.17.1": {"name": "lodash", "version": "4.17.1", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.1", "_shasum": "e75eaf17a34730c6491d9956f4d81f3a044f01bf", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "7.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e75eaf17a34730c6491d9956f4d81f3a044f01bf", "size": 310655, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.1.tgz", "integrity": "sha512-TU8moLfqGOx1RJogO9uU2d90q0L5J/1g/i4fkdB5ZmQ5qsjwVnCvg5XWuiwqqPNnyBQlQp8ukd9ADTY27dGsiQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.17.1.tgz_1479193405702_0.2180279449094087"}, "directories": {}, "publish_time": 1479193405950, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479193405950, "_cnpmcore_publish_time": "2021-12-13T06:38:03.740Z"}, "4.17.0": {"name": "lodash", "version": "4.17.0", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.17.0", "_shasum": "93f4466e5ab73e5a1f1216c34eea11535f0a8df5", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "7.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "93f4466e5ab73e5a1f1216c34eea11535f0a8df5", "size": 310563, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.17.0.tgz", "integrity": "sha512-wXJoUnY8v5rCurNIL6tM0TOv/F6B2NzDzLzStNOQoX/i88s8hPGkYSRPMtcdjORIx1dHUB1lQVhsNyC9yjD/7w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/lodash-4.17.0.tgz_1479106806125_0.49467423441819847"}, "directories": {}, "publish_time": 1479106808291, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479106808291, "_cnpmcore_publish_time": "2021-12-13T06:38:04.243Z"}, "4.16.6": {"name": "lodash", "version": "4.16.6", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.6", "_shasum": "d22c9ac660288f3843e16ba7d2b5d06cca27d777", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d22c9ac660288f3843e16ba7d2b5d06cca27d777", "size": 309588, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.6.tgz", "integrity": "sha512-QXrLkYI2gXjL0QoQ9j932ca+Oh/wCUBeZULjqsJy78KjntrohXawEoOfgA2fXwy4vKh7OTD00p757/pUROtv+w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/lodash-4.16.6.tgz_1477982285913_0.34612850472331047"}, "directories": {}, "publish_time": 1477982287989, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477982287989, "_cnpmcore_publish_time": "2021-12-13T06:38:04.905Z"}, "4.16.5": {"name": "lodash", "version": "4.16.5", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.5", "_shasum": "77d88feac548009b1a5c4ca7b49ac431ce346ae8", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "6.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "77d88feac548009b1a5c4ca7b49ac431ce346ae8", "size": 309690, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.5.tgz", "integrity": "sha512-DZbgdZTnZw9hGKYyvELBSE5P++SNV95Jk1B04G5mesp9f2xzwKyyy7u+lsvBWOOg9vxcbQ+OgimaB0ZLecOx3Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.16.5.tgz_1477896554541_0.9171323315240443"}, "directories": {}, "publish_time": 1477896554797, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477896554797, "_cnpmcore_publish_time": "2021-12-13T06:38:05.591Z"}, "4.16.4": {"name": "lodash", "version": "4.16.4", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.4", "_shasum": "01ce306b9bad1319f2a5528674f88297aeb70127", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "01ce306b9bad1319f2a5528674f88297aeb70127", "size": 307899, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.4.tgz", "integrity": "sha512-nnaYhZqDvVKdmBJYqTAxIXxVUlRNwr8yfDKjKSpBEwoxi0Os+e5pU+/GtSO0mtTd06g49RCINYTmuixJR1Tz2w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.16.4.tgz_1475766806071_0.6463651182129979"}, "directories": {}, "publish_time": 1475766810196, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475766810196, "_cnpmcore_publish_time": "2021-12-13T06:38:06.276Z"}, "4.16.3": {"name": "lodash", "version": "4.16.3", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.3", "_shasum": "0ba761439529127c7a38c439114ca153efa999a2", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0ba761439529127c7a38c439114ca153efa999a2", "size": 306979, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.3.tgz", "integrity": "sha512-XHD7mGAORGJeO5f32yHhWmS/9Fb13bjKedNt9EjV0tMl5dXplIWbFgLXHRjpZ41pVFdGMj8H/sdcuetZyAJ32g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.16.3.tgz_1475513008878_0.7973488194402307"}, "directories": {}, "publish_time": 1475513011571, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475513011571, "_cnpmcore_publish_time": "2021-12-13T06:38:06.875Z"}, "4.16.2": {"name": "lodash", "version": "4.16.2", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.2", "_shasum": "3e626db827048a699281a8a125226326cfc0e652", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3e626db827048a699281a8a125226326cfc0e652", "size": 306595, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.2.tgz", "integrity": "sha512-t7POblJ+wdC7jto56BrMLpkyS0/ryvQxhmIgN3x+RBZIQCPszPpbxDy+ZcSTmYFDBrgDkwAc20P/C14y7xXWMQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.16.2.tgz_1474859465050_0.057903866516426206"}, "directories": {}, "publish_time": 1474859465302, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474859465302, "_cnpmcore_publish_time": "2021-12-13T06:38:07.459Z"}, "4.16.1": {"name": "lodash", "version": "4.16.1", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.1", "_shasum": "75e15fcfe2721adbd6a7c5985f855288a03fc36d", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "75e15fcfe2721adbd6a7c5985f855288a03fc36d", "size": 305889, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.1.tgz", "integrity": "sha512-qK+vzI6bQ91q3gkfBKpO0rgR0qV6ECjT1+ZBprnFejldHGi74AQ9MPWfSreqqzaF0aLRC7qnnYb5AjTwLXSNKw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.16.1.tgz_1474390790200_0.7545584393665195"}, "directories": {}, "publish_time": 1474390793967, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474390793967, "_cnpmcore_publish_time": "2021-12-13T06:38:08.084Z"}, "4.16.0": {"name": "lodash", "version": "4.16.0", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.16.0", "_shasum": "834cb785057157032242beaf101bb3d99e55d0d8", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "834cb785057157032242beaf101bb3d99e55d0d8", "size": 305880, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.16.0.tgz", "integrity": "sha512-Hcf3oefWvjGo2dixw7g+prUj/8A2YbRVe5IirVtTLQzW/dMojxXGvi7NXEa17q4vhz4KbRwLPi6NFLK6cYOh+w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.16.0.tgz_1474297151529_0.16563124535605311"}, "directories": {}, "publish_time": 1474297154886, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474297154886, "_cnpmcore_publish_time": "2021-12-13T06:38:08.711Z"}, "4.15.0": {"name": "lodash", "version": "4.15.0", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.15.0", "_shasum": "3162391d8f0140aa22cf8f6b3c34d6b7f63d3aa9", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3162391d8f0140aa22cf8f6b3c34d6b7f63d3aa9", "size": 303619, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.15.0.tgz", "integrity": "sha512-/XM2P+O3xDTOR/CrI4ZqqzgC3tFOLfn2sUIIKN9CYI3YmZnZ9QcElpYh0MHiHXMNJLWwbVxkCaO6zFGS97Xj0g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.15.0.tgz_1471012765624_0.7570270872674882"}, "directories": {}, "publish_time": 1471012768783, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471012768783, "_cnpmcore_publish_time": "2021-12-13T06:38:09.373Z"}, "4.14.2": {"name": "lodash", "version": "4.14.2", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.14.2", "_shasum": "bbccce6373a400fbfd0a8c67ca42f6d1ef416432", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bbccce6373a400fbfd0a8c67ca42f6d1ef416432", "size": 301364, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.14.2.tgz", "integrity": "sha512-L5PieqD7phyya3Uave78zpkVE5uc022V1h5iAWt7q1z71SS7Rtw5OX8Q30OZ4L8GVtRLKxI1mn76X288L7EdeA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.14.2.tgz_1470670544899_0.8695246241986752"}, "directories": {}, "publish_time": 1470670549019, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470670549019, "_cnpmcore_publish_time": "2021-12-13T06:38:10.145Z"}, "4.14.1": {"name": "lodash", "version": "4.14.1", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.14.1", "_shasum": "5b5d0a516ad3d46e12012d7f59ad66a5659bf408", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5b5d0a516ad3d46e12012d7f59ad66a5659bf408", "size": 301509, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.14.1.tgz", "integrity": "sha512-G7KWBIFduJHp0Nnbhsanp15Yr5Cp9oW9EiXG+SmyZ7U4aG+leOCLooJK/TT9IULKDZXmcIQwAvMFXmKvHzlmzw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.14.1.tgz_1469803745646_0.5584656686987728"}, "directories": {}, "publish_time": 1469803750278, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469803750278, "_cnpmcore_publish_time": "2021-12-13T06:38:10.898Z"}, "4.14.0": {"name": "lodash", "version": "4.14.0", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.14.0", "_shasum": "b742d6a80b5ee87e7c1a35c143c5b19bd966c10b", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b742d6a80b5ee87e7c1a35c143c5b19bd966c10b", "size": 301336, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.14.0.tgz", "integrity": "sha512-/dOeXHnVxZOSQ9di7dLSTCZqZH4fpzep1ATkJ4D3k8/gmeMLzhEQiartyb2s61Aaw3kiORDu6jzKCj6Ftgo01Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.14.0.tgz_1469385658233_0.2943053827621043"}, "directories": {}, "publish_time": 1469385658495, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469385658495, "_cnpmcore_publish_time": "2021-12-13T06:38:11.668Z"}, "4.13.1": {"name": "lodash", "version": "4.13.1", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.13.1", "_shasum": "83e4b10913f48496d4d16fec4a560af2ee744b68", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "83e4b10913f48496d4d16fec4a560af2ee744b68", "size": 296137, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.13.1.tgz", "integrity": "sha512-j/GRONYpkXt1aB1bQHzkq0Th7zhv/syoDVrzCDA3FDMntIin0b7TjXi62q9juDC+QfhRs9COr0LFW38vQSH9Tg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.13.1.tgz_1464019142054_0.5244540225248784"}, "directories": {}, "publish_time": 1464019145944, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464019145944, "_cnpmcore_publish_time": "2021-12-13T06:38:12.385Z"}, "4.13.0": {"name": "lodash", "version": "4.13.0", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.13.0", "_shasum": "6172ad9cfaa5db21268b23b6da6c3a42442aab65", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6172ad9cfaa5db21268b23b6da6c3a42442aab65", "size": 295498, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.13.0.tgz", "integrity": "sha512-Ab4JJMgGNOpKsBaR0UtUXIjym7eclpVKVaK6QGmxT9f8ODHmn+fhp12KhRKgIeposBlrzfFsN/ilBe/Et1BRog=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.13.0.tgz_1463980042961_0.5916273870971054"}, "directories": {}, "publish_time": 1463980043403, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463980043403, "_cnpmcore_publish_time": "2021-12-13T06:38:13.149Z"}, "4.12.0": {"name": "lodash", "version": "4.12.0", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.12.0", "_shasum": "2bd6dc46a040f59e686c972ed21d93dc59053258", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "6.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2bd6dc46a040f59e686c972ed21d93dc59053258", "size": 294010, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.12.0.tgz", "integrity": "sha512-H7Y9y0h/WibZBZyt9IOEEDaNJCzmpEoUQNB6d/+sHuS6fKFWCRuYSAf5s2mfK3hYrPqHbosJI4/bv0HUf2OvVw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.12.0.tgz_1462735540938_0.36508488352410495"}, "directories": {}, "publish_time": 1462735543826, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462735543826, "_cnpmcore_publish_time": "2021-12-13T06:38:13.905Z"}, "4.11.2": {"name": "lodash", "version": "4.11.2", "description": "Lodash modular utilities.", "keywords": ["modules", "stdlib", "util"], "homepage": "https://lodash.com/", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "gitHead": "ddde027fd9bd5d25416b6a788e273babc959f307", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.11.2", "_shasum": "d6b4338b110a58e21dae5cebcfdbbfd2bc4cdb3b", "_from": ".", "_npmVersion": "2.15.4", "_nodeVersion": "6.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d6b4338b110a58e21dae5cebcfdbbfd2bc4cdb3b", "size": 873145, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.11.2.tgz", "integrity": "sha512-kzYAjjUS0vKRLVcNZgK2k7NJOT5cQoFO3w8ddED6mDBdgu3AIL9xhAktXJ5Dm6GD1x+eqqyu1wKAzEt8Uq77NQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.11.2.tgz_1462201261706_0.9612307662609965"}, "directories": {}, "publish_time": 1462201262189, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462201262189, "_cnpmcore_publish_time": "2021-12-13T06:38:14.709Z"}, "4.11.1": {"name": "lodash", "version": "4.11.1", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.11.1", "_shasum": "a32106eb8e2ec8e82c241611414773c9df15f8bc", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a32106eb8e2ec8e82c241611414773c9df15f8bc", "size": 289572, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.11.1.tgz", "integrity": "sha512-zQXZjpw5m367A/MrkEIx1xEJISQmZykAJ5EzCPznYZiVLK211R1hDiye+TU4eyWKDfsaBsLW6/7gH5juuZ4J6Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.11.1.tgz_1460618480099_0.40750555554404855"}, "directories": {}, "publish_time": 1460618483548, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460618483548, "_cnpmcore_publish_time": "2021-12-13T06:38:15.669Z"}, "4.11.0": {"name": "lodash", "version": "4.11.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.11.0", "_shasum": "428f7172a5e9a82e9a459b543816489810ecb8af", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "428f7172a5e9a82e9a459b543816489810ecb8af", "size": 289505, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.11.0.tgz", "integrity": "sha512-18uE0HVAF7WGaXB37G3yJdx1M39bez8eCHPP+NCfOPzDbcP1XqqrVz6EuF2Ip2Ye/Hcmhvy1bGUGUDOGNzsHPw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.11.0.tgz_1460561550034_0.7776633501052856"}, "directories": {}, "publish_time": 1460561550507, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460561550507, "_cnpmcore_publish_time": "2021-12-13T06:38:16.523Z"}, "4.10.0": {"name": "lodash", "version": "4.10.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.10.0", "_shasum": "3d8f3ac7a5a904fdb01e2cfe1401879bf9652c6a", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3d8f3ac7a5a904fdb01e2cfe1401879bf9652c6a", "size": 288092, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.10.0.tgz", "integrity": "sha512-uDkT/I1Y2X0gvxCdeosY9/XImqohEKRrvj/NLXA7H1NKWdu61lHnrIHiQPNlvIXCzsbnBqwdheW0AbKMxMQF+Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash-4.10.0.tgz_1460385833529_0.7400152564514428"}, "directories": {}, "publish_time": 1460385836586, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460385836586, "_cnpmcore_publish_time": "2021-12-13T06:38:17.381Z"}, "4.9.0": {"name": "lodash", "version": "4.9.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.9.0", "_shasum": "4c20d742f03ce85dc700e0dd7ab9bcab85e6fc14", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4c20d742f03ce85dc700e0dd7ab9bcab85e6fc14", "size": 288602, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.9.0.tgz", "integrity": "sha512-UGUdLhDm+6tXViVGFOiDt+3HXNxVpLPaEcqzoTowq9XtmsHTq1nskdpr6UR1XV3aM1eJaiFtwg0DarxFW/ypsA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.9.0.tgz_1460128953630_0.9709006256889552"}, "directories": {}, "publish_time": 1460128954228, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460128954228, "_cnpmcore_publish_time": "2021-12-13T06:38:18.195Z"}, "4.8.2": {"name": "lodash", "version": "4.8.2", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.8.2", "_shasum": "478ad7ff648c3c71a2f6108e032c5c0cc40747df", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "478ad7ff648c3c71a2f6108e032c5c0cc40747df", "size": 287650, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.8.2.tgz", "integrity": "sha512-/uqqdn0gpGl3QldtTG79iyo2ao2zOsFce+GBxbE4rWmU6EtEWl4pPRp7Xw61DHge1vlfDfjq3zkytObW6RV0zQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.8.2.tgz_1459822516091_0.773343451321125"}, "directories": {}, "publish_time": 1459822516661, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459822516661, "_cnpmcore_publish_time": "2021-12-13T06:38:19.068Z"}, "4.8.1": {"name": "lodash", "version": "4.8.1", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.8.1", "_shasum": "97797b4684d238bad1a2d0a2e53108fe57e5f95f", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "97797b4684d238bad1a2d0a2e53108fe57e5f95f", "size": 287436, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.8.1.tgz", "integrity": "sha512-izVINTxb821BWw4WBV06xd6qfr7oWRFUyZHUsKNeyVRp+KURAvLLMUTRwzqwAsLaJ5duFMtnEeDpSgiQz71F8g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.8.1.tgz_1459784628521_0.6884815152734518"}, "directories": {}, "publish_time": 1459784629109, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459784629109, "_cnpmcore_publish_time": "2021-12-13T06:38:19.992Z"}, "4.8.0": {"name": "lodash", "version": "4.8.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.8.0", "_shasum": "297fb94a164c02e7eb8d37cde135be62456df446", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "297fb94a164c02e7eb8d37cde135be62456df446", "size": 287430, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.8.0.tgz", "integrity": "sha512-le8DgAtRoZUyhKrjm6O9zEXbBHR3XPtT2siu8cNTTQ8GdiBPlTsYOzkQuPf9xBQRSlkxRwNNOXVK9r8N0hGJCg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.8.0.tgz_1459781672956_0.8200249187648296"}, "directories": {}, "publish_time": 1459781673612, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459781673612, "_cnpmcore_publish_time": "2021-12-13T06:38:20.889Z"}, "4.7.0": {"name": "lodash", "version": "4.7.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.7.0", "_shasum": "b8088876bdc290ca1f34f90350f7b5cb71810c05", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b8088876bdc290ca1f34f90350f7b5cb71810c05", "size": 285406, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.7.0.tgz", "integrity": "sha512-d4bJYO5ctbajdv2vz64ECUWJG8rgo73FeF6j64bGT47I0krA0DMQSoQW1EOPGibkJB86MoNvZVUwJBHxiwmFDQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.7.0.tgz_1459439192794_0.5509570257272571"}, "directories": {}, "publish_time": 1459439193373, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459439193373, "_cnpmcore_publish_time": "2021-12-13T06:38:21.706Z"}, "4.6.1": {"name": "lodash", "version": "4.6.1", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.6.1", "_shasum": "df00c1164ad236b183cfc3887a5e8d38cc63cbbc", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "df00c1164ad236b183cfc3887a5e8d38cc63cbbc", "size": 275653, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.6.1.tgz", "integrity": "sha512-85DFrB4TYPwVZbZw/HCGnmRUdDQOxGJuDmSfZ9OQEESdFJ1NxlYLoQObfHJGr+UsWwmVQhwG2iPmUCi6+eKzUg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.6.1.tgz_1456942179866_0.5565699376165867"}, "directories": {}, "publish_time": 1456942180696, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456942180696, "_cnpmcore_publish_time": "2021-12-13T06:38:22.554Z"}, "4.6.0": {"name": "lodash", "version": "4.6.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.6.0", "_shasum": "c6926922ed75379993f0212a4764c18a760a2198", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c6926922ed75379993f0212a4764c18a760a2198", "size": 275699, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.6.0.tgz", "integrity": "sha512-Lh9Djp+57UEqdXgLUyQmqaAKZyTzcMLVsNAfWYeZ/EKyfS8/43b84BIAto6MPG32lc0ziKHZopbPiGlxl2Iudw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash-4.6.0.tgz_1456889076647_0.5880196213256568"}, "directories": {}, "publish_time": 1456889077179, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456889077179, "_cnpmcore_publish_time": "2021-12-13T06:38:23.328Z"}, "4.5.1": {"name": "lodash", "version": "4.5.1", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.5.1", "_shasum": "80e8a074ca5f3893a6b1c10b2a636492d710c316", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "80e8a074ca5f3893a6b1c10b2a636492d710c316", "size": 272619, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.5.1.tgz", "integrity": "sha512-N6UtfG+knq6dx13HVBHXsSagpSAQsTM8GHAv//TNcEMPc1ayGEb8wcAUvNWx0x3Xpd7PTP6hhe6AoNeIiq/IYA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash-4.5.1.tgz_1456123341085_0.7924484650138766"}, "directories": {}, "publish_time": 1456123344244, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456123344244, "_cnpmcore_publish_time": "2021-12-13T06:38:24.221Z"}, "4.5.0": {"name": "lodash", "version": "4.5.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.5.0", "_shasum": "2284aa06f5b136adbd954b903511b62fc39d1f59", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2284aa06f5b136adbd954b903511b62fc39d1f59", "size": 245412, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.5.0.tgz", "integrity": "sha512-QX5CQiKEsYsnEvcTGDRDcq75WePSnzrptmplm8BIARgWrDmdjg051fpi3FGhDdwGhlv6Q4HZKwNYrUb3cQ8Gtg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash-4.5.0.tgz_1455698378277_0.6987434786278754"}, "directories": {}, "publish_time": 1455698382533, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455698382533, "_cnpmcore_publish_time": "2021-12-13T06:38:25.192Z"}, "4.4.0": {"name": "lodash", "version": "4.4.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.4.0", "_shasum": "bc5c6b741d04d53111b26cf1a2feab86c268cd7a", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bc5c6b741d04d53111b26cf1a2feab86c268cd7a", "size": 245396, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.4.0.tgz", "integrity": "sha512-yE9fdlwq2UjPVJphtZBgIJaF7a6Y9gK5B5FCHPcZiOQ694DQ28pAN5jQTcR21g/P1O5RjpX8+nryVpRgnHrQHw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash-4.4.0.tgz_1455606613194_0.7996933336835355"}, "directories": {}, "publish_time": 1455606616856, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455606616856, "_cnpmcore_publish_time": "2021-12-13T06:38:26.155Z"}, "4.3.0": {"name": "lodash", "version": "4.3.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.3.0", "_shasum": "efd9c4a6ec53f3b05412429915c3e4824e4d25a4", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "efd9c4a6ec53f3b05412429915c3e4824e4d25a4", "size": 244280, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.3.0.tgz", "integrity": "sha512-JDXk2rxAUY3cHGG9OJfRbhd7zc2feQRdMthkMdqmK19l0+ojybg9ISylGUnmqHtteg/wXH8QudOLN+RKgKNKIQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash-4.3.0.tgz_1454921838929_0.49659619107842445"}, "directories": {}, "publish_time": 1454921839880, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454921839880, "_cnpmcore_publish_time": "2021-12-13T06:38:27.108Z"}, "4.2.1": {"name": "lodash", "version": "4.2.1", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.2.1", "_shasum": "171fdcfbbc30d689c544cd18c0529f56de6c1aa9", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "171fdcfbbc30d689c544cd18c0529f56de6c1aa9", "size": 242793, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.2.1.tgz", "integrity": "sha512-G<PERSON>ttIEgpAXIRPojhmAjK8qs5sDIYi7Usv7vp0l2O6ccLU9fxZB0TtvkemdjHyljjMFQt02w4lhKLyvB5YH2C7g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash-4.2.1.tgz_1454515215050_0.8410219918005168"}, "directories": {}, "publish_time": 1454515216046, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454515216046, "_cnpmcore_publish_time": "2021-12-13T06:38:28.081Z"}, "4.2.0": {"name": "lodash", "version": "4.2.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.2.0", "_shasum": "0506f4dad737c79cb2602ac11f8b74b7136464e8", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0506f4dad737c79cb2602ac11f8b74b7136464e8", "size": 242542, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.2.0.tgz", "integrity": "sha512-snufRc058TNWSy4Y0drZ8p6/ntrD0UnJHe+NNwLQaJW7dk1Ig9rnKwFmdWwP9ObKJzO2bvc0wWcbeL9pnKyhBg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash-4.2.0.tgz_1454403016517_0.5934681817889214"}, "directories": {}, "publish_time": 1454403017287, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454403017287, "_cnpmcore_publish_time": "2021-12-13T06:38:29.207Z"}, "4.1.0": {"name": "lodash", "version": "4.1.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.1.0", "_shasum": "299894283de01a9eefbedff4c4b9b00a6a2e6e96", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "299894283de01a9eefbedff4c4b9b00a6a2e6e96", "size": 241357, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.1.0.tgz", "integrity": "sha512-B9sgtKUlz0xe7lkYb80BcOpwwJJw5iOiz4HkBDzF0+i5nJLiwfBnL08m7bBkCOPBfi+0aqvrJDMdZDfAvs8vYg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1454085204543, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454085204543, "_cnpmcore_publish_time": "2021-12-13T06:38:30.319Z"}, "4.0.1": {"name": "lodash", "version": "4.0.1", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "lodash.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.0.1", "_shasum": "cd8c902c9e03f2e69ce3e0e456d505ab89eb98f4", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cd8c902c9e03f2e69ce3e0e456d505ab89eb98f4", "size": 227757, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.0.1.tgz", "integrity": "sha512-jA3EFncTLSpNOJLgZUlI7Xrsg3t/DYbgPuRVD1qbLoyUXgzUEvB0EpJimh+WBM6CUNtAyKJ1CVPD+7wE+LrraQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453737977924, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453737977924, "_cnpmcore_publish_time": "2021-12-13T06:38:31.316Z"}, "4.0.0": {"name": "lodash", "version": "4.0.0", "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@4.0.0", "_shasum": "9ac43844c595e28d30108b7ba583703395922dfc", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9ac43844c595e28d30108b7ba583703395922dfc", "size": 202897, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-4.0.0.tgz", "integrity": "sha512-bWpSlBobTcHYK9eUzcBYHhSBGzvSzEsxocnW5+v7p6wCRlY1icneTe2ACam3mGdAu82+RLL32cmyl7TRlJHqZw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452640400539, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452640400539, "_cnpmcore_publish_time": "2021-12-13T06:38:32.376Z"}, "3.10.1": {"name": "lodash", "version": "3.10.1", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.10.1", "_shasum": "5bf45e8e49ba4189e17d482789dfd15bd140b7b6", "_from": ".", "_npmVersion": "2.13.1", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "5bf45e8e49ba4189e17d482789dfd15bd140b7b6", "size": 173550, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.10.1.tgz", "integrity": "sha512-9mDDwqVIma6OZX79ZlDACZl8sBm0TEnkf99zV3iMA4GzkIT/9hiqP5mY0HoT1iNLCrKc/R1HByV+yJfRWVJryQ=="}, "directories": {}, "publish_time": 1438668306887, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438668306887, "_cnpmcore_publish_time": "2021-12-13T06:38:33.374Z"}, "3.10.0": {"name": "lodash", "version": "3.10.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.10.0", "_shasum": "93d51c672828a4416a12af57220ba8a8737e2fbb", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "93d51c672828a4416a12af57220ba8a8737e2fbb", "size": 173595, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.10.0.tgz", "integrity": "sha512-y4sq/rWWfUsEaOR6VYulMCC6QzL7mqb5wV8R09xGUJZ84UoBkn4/nNqZ04YSqXkEQOQabBRKxk3xgzgig2h75A=="}, "directories": {}, "publish_time": 1435677208602, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435677208602, "_cnpmcore_publish_time": "2021-12-13T06:38:34.444Z"}, "3.9.3": {"name": "lodash", "version": "3.9.3", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.9.3", "_shasum": "0159e86832feffc6d61d852b12a953b99496bd32", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "0159e86832feffc6d61d852b12a953b99496bd32", "size": 172504, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.9.3.tgz", "integrity": "sha512-v5SKZhnCUujcTpFpHEIJZDVcBM2OYjROx732HyJ6kzKZtwStTb4LG6noqmK9etHqDNhf6X7itXx5s0hTpAXPpQ=="}, "directories": {}, "publish_time": 1432604864058, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432604864058, "_cnpmcore_publish_time": "2021-12-13T06:38:35.407Z"}, "3.9.2": {"name": "lodash", "version": "3.9.2", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.9.2", "_shasum": "e86b404e374a7787fd272be086991b7bed2ce06f", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "e86b404e374a7787fd272be086991b7bed2ce06f", "size": 172473, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.9.2.tgz", "integrity": "sha512-dVjBoiZBcGClNGpTaW8CiL6Xn06hHp9qwT455o/QZhkUPjOSPWyW0F1fPJUU0u6prVRrqP80iq9bAatv7c+zXQ=="}, "directories": {}, "publish_time": 1432501077973, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432501077973, "_cnpmcore_publish_time": "2021-12-13T06:38:36.360Z"}, "3.9.1": {"name": "lodash", "version": "3.9.1", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.9.1", "_shasum": "5102372ecaa2ccf71fec63b55b44642eeb77b59a", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "5102372ecaa2ccf71fec63b55b44642eeb77b59a", "size": 172308, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.9.1.tgz", "integrity": "sha512-UQIAfzv9CTa+meu2L6aYg14hocAeeRLoNn4YfEWr842Qrp9wgWkpiW8V+/xwDHsMgSCWU/DNYI2FeYzhBwlCMg=="}, "directories": {}, "publish_time": 1432069220625, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432069220625, "_cnpmcore_publish_time": "2021-12-13T06:38:37.702Z"}, "3.9.0": {"name": "lodash", "version": "3.9.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.9.0", "_shasum": "5bf547f9fcce37f78678859ba11854dc75ffa21b", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "5bf547f9fcce37f78678859ba11854dc75ffa21b", "size": 171928, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.9.0.tgz", "integrity": "sha512-ad4kdm6L5dPTQAx9hcmz6fX8c3oKMlRWdMAquKfLx4WN4jDQcXCdDQ6qLEszjpWUfYzfpw+aASTLsQJEArBDIg=="}, "directories": {}, "publish_time": 1432060015450, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432060015450, "_cnpmcore_publish_time": "2021-12-13T06:38:38.893Z"}, "3.8.0": {"name": "lodash", "version": "3.8.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.8.0", "_shasum": "376eb98bdcd9382a9365c33c4cb8250de1325b91", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "376eb98bdcd9382a9365c33c4cb8250de1325b91", "size": 173317, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.8.0.tgz", "integrity": "sha512-u93G/0wg7ukZx5nAxzzxRvVsCnBATHNDbYLego2eokz4fvL/+nzJOIJS48ru7NlXnjthNw56mFpLlbp9SjUEgg=="}, "directories": {}, "publish_time": 1430495144760, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430495144760, "_cnpmcore_publish_time": "2021-12-13T06:38:40.037Z"}, "2.4.2": {"name": "lodash", "version": "2.4.2", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_id": "lodash@2.4.2", "scripts": {}, "_shasum": "fadd834b9683073da179b3eae6d9c0d15053f73e", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "fadd834b9683073da179b3eae6d9c0d15053f73e", "size": 196716, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw=="}, "directories": {}, "publish_time": 1430082289443, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430082289443, "_cnpmcore_publish_time": "2021-12-13T06:38:41.311Z"}, "3.7.0": {"name": "lodash", "version": "3.7.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.7.0", "_shasum": "3678bd8ab995057c07ade836ed2ef087da811d45", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "3678bd8ab995057c07ade836ed2ef087da811d45", "size": 172121, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.7.0.tgz", "integrity": "sha512-73GDDlioRJOCHV8N9gnBEpjdWI34+e9AvMnS4qdqdMfl8/yH/dJP1tfuqUFccZ/deZQlHuJiRSuKXjKIfDwBOg=="}, "directories": {}, "publish_time": 1429199255770, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429199255770, "_cnpmcore_publish_time": "2021-12-13T06:38:42.469Z"}, "1.0.2": {"name": "lodash", "version": "1.0.2", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "https://lodash.com/", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "_id": "lodash@1.0.2", "scripts": {}, "_shasum": "8f57560c83b59fc270bd3d561b690043430e2551", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "8f57560c83b59fc270bd3d561b690043430e2551", "size": 113923, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.0.2.tgz", "integrity": "sha512-0VSEDVec/Me2eATuoiQd8IjyBMMX0fahob8YJ96V1go2RjvCk1m1GxmtfXn8RNSaLaTtop7fsuhhu9oLk3hUgA=="}, "directories": {}, "publish_time": 1427731081337, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427731081337, "_cnpmcore_publish_time": "2021-12-13T06:38:43.592Z"}, "3.6.0": {"name": "lodash", "version": "3.6.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.6.0", "_shasum": "5266a8f49dd989be4f9f681b6f2a0c55285d0d9a", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "5266a8f49dd989be4f9f681b6f2a0c55285d0d9a", "size": 166105, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.6.0.tgz", "integrity": "sha512-fysFKsJtaOtRGZT/b3Xx03iyEmO0zjU+d1HBH5NcEaUjtg7XO0wDY5I7IJFfr2rguJt0Rve2V32426Za3zYyRw=="}, "directories": {}, "publish_time": 1427297789983, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427297789983, "_cnpmcore_publish_time": "2021-12-13T06:38:44.585Z"}, "3.5.0": {"name": "lodash", "version": "3.5.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.5.0", "_shasum": "19bb3f4d51278f0b8c818ed145c74ecf9fe40e6d", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "19bb3f4d51278f0b8c818ed145c74ecf9fe40e6d", "size": 161883, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.5.0.tgz", "integrity": "sha512-eC2gi6pEoXXAWKz1fKvWnA0wOdBPsvTVFT71szjM6dFoDE9llinK81xW4EH4QxF+wNZoVeLIxUp21BDPniMn4g=="}, "directories": {}, "publish_time": 1425877311264, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425877311264, "_cnpmcore_publish_time": "2021-12-13T06:38:45.547Z"}, "3.4.0": {"name": "lodash", "version": "3.4.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.4.0", "_shasum": "7c39c1336faf5f96e6409f8355f26ea192f41821", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "7c39c1336faf5f96e6409f8355f26ea192f41821", "size": 161587, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.4.0.tgz", "integrity": "sha512-nOn1D2yGLp11KdIHF/Ww1CYuB1LVaK4+n3/eQbFN5eyfqVHPiCFgJUrWoRoPdoDw4C+XJzycLHK8fLTCyM7MqA=="}, "directories": {}, "publish_time": 1425660246018, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425660246018, "_cnpmcore_publish_time": "2021-12-13T06:38:47.038Z"}, "3.3.1": {"name": "lodash", "version": "3.3.1", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.3.1", "_shasum": "3b914d4a1bb27efcee076e0dfa58152018e2042e", "_from": ".", "_npmVersion": "2.6.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "3b914d4a1bb27efcee076e0dfa58152018e2042e", "size": 159118, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.3.1.tgz", "integrity": "sha512-LwPGU4nXJqOmsJvDDwFl2vuaecFFqX1I8PMJJWbAAGIfhNK5+CFtd6fgldg9VFuOmy6WLi09sbBguMBAxaJbOA=="}, "directories": {}, "publish_time": 1424793767458, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424793767458, "_cnpmcore_publish_time": "2021-12-13T06:38:48.488Z"}, "3.3.0": {"name": "lodash", "version": "3.3.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.3.0", "_shasum": "011ce52d01b14e60eaeea76acb9b8ba42c70581b", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "011ce52d01b14e60eaeea76acb9b8ba42c70581b", "size": 159048, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.3.0.tgz", "integrity": "sha512-gpux6tVfBHsUdUIciz5HoV0ChAxUTvi0ChpQMIjAsKtg6FTYFtd1B1G0JlqHvAio3teaMVGPDPk2seVq1INwOQ=="}, "directories": {}, "publish_time": 1424452108864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424452108864, "_cnpmcore_publish_time": "2021-12-13T06:38:49.797Z"}, "3.2.0": {"name": "lodash", "version": "3.2.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.2.0", "_shasum": "4bf50a3243f9aeb0bac41a55d3d5990675a462fb", "_from": ".", "_npmVersion": "2.5.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "4bf50a3243f9aeb0bac41a55d3d5990675a462fb", "size": 158485, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.2.0.tgz", "integrity": "sha512-pg876Sz2B6iut18K5PlFGZUxagOP9LACajQ4nzQHPZ8JcU15tviPV6q/u/dEQMV64TzKu8yJM3NprFxqRb3oHA=="}, "directories": {}, "publish_time": 1423760478403, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423760478403, "_cnpmcore_publish_time": "2021-12-13T06:38:51.174Z"}, "3.1.0": {"name": "lodash", "version": "3.1.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.1.0", "_shasum": "d41b8b33530cb3be088853208ad30092d2c27961", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "d41b8b33530cb3be088853208ad30092d2c27961", "size": 153490, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.1.0.tgz", "integrity": "sha512-kH7H/6T8ut2DMWAHIKJWZoDzYdvy+echL6u2VZbhYYkEjr6Zp15Yjt9pPvIPQwAT/5QaQYdaVPwCcLMP9qeu0w=="}, "directories": {}, "publish_time": 1422982415795, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422982415795, "_cnpmcore_publish_time": "2021-12-13T06:38:52.521Z"}, "3.0.1": {"name": "lodash", "version": "3.0.1", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.0.1", "_shasum": "14d49028a38bc740241d11e2ecd57ec06d73c19a", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "14d49028a38bc740241d11e2ecd57ec06d73c19a", "size": 153207, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.0.1.tgz", "integrity": "sha512-Hw4nFcX+LaJVkNNhjLgAlK82Q1/8DgCdaXQm2ZDQsZlqC6DiZcGhNSfguEesBBrITt8ZOgoMBUzUyWi1TaqLkg=="}, "directories": {}, "publish_time": 1422610431621, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422610431621, "_cnpmcore_publish_time": "2021-12-13T06:38:53.884Z"}, "3.0.0": {"name": "lodash", "version": "3.0.0", "description": "The modern build of lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "main": "index.js", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash@3.0.0", "_shasum": "493364b7183a37104d65255f38b0fe5869cbe0fd", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "dist": {"shasum": "493364b7183a37104d65255f38b0fe5869cbe0fd", "size": 153066, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash/-/lodash-3.0.0.tgz", "integrity": "sha512-GFMDPshMSMkDX/6y0h60FED+G00qMjVGpi+obx0feBDpeWUC9jXDAA+OmnXa4sWDvgpY7ogaaHLDJz8jXyRLqg=="}, "directories": {}, "publish_time": 1422284971198, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422284971198, "_cnpmcore_publish_time": "2021-12-13T06:38:55.254Z"}, "2.4.1": {"name": "lodash", "version": "2.4.1", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.4.1.tgz", "shasum": "5b7723034dda4d262e5a46fb2c58d7cc22f71420", "size": 195871, "noattachment": false, "integrity": "sha512-qa6QqjA9jJB4AYw+NpD2GI4dzHL6Mv0hL+By6iIul4Ce0C1refrjZJmcGvWdnLUwl4LIPtvzje3UQfGH+nCEsQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386089472879, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386089472879, "_cnpmcore_publish_time": "2021-12-13T06:38:56.661Z"}, "2.4.0": {"name": "lodash", "version": "2.4.0", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.4.0.tgz", "shasum": "55074982883381b6b7134b742a5900bbbdab6b09", "size": 195227, "noattachment": false, "integrity": "sha512-nIjaweI+LHygGWjtPBi4E03eBE36hme8t0mgkl2tDJVOMBIH8P+m3FB/vdX1Tw5bmRqyRQzE0HBQ6uvlysC1xw=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385494830164, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385494830164, "_cnpmcore_publish_time": "2021-12-13T06:38:58.116Z"}, "2.3.0": {"name": "lodash", "version": "2.3.0", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.3.0.tgz", "shasum": "dfbdac99cf87a59a022c474730570d8716c267dd", "size": 191718, "noattachment": false, "integrity": "sha512-1cKXXnLDXhXBP8H1ZECyE1znMFLUjMiV9WrVx6/Mi8Ocm/wwjuhWNpLFefQdv05+uMtIAdOVPnG3SFxcOUZzBw=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384191027058, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384191027058, "_cnpmcore_publish_time": "2021-12-13T06:38:59.407Z"}, "2.2.1": {"name": "lodash", "version": "2.2.1", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.2.1.tgz", "shasum": "ca935fd14ab3c0c872abacf198b9cda501440867", "size": 186752, "noattachment": false, "integrity": "sha512-rGaKzxe4Biu8YdCPD/tUkBF4/fnAqgj63A6PeAyQnH/NEKNUHgppGZUgYBYOmAZsBKwwAb343Q1Zew0RDB2jIg=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380824970163, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380824970163, "_cnpmcore_publish_time": "2021-12-13T06:39:00.811Z"}, "2.2.0": {"name": "lodash", "version": "2.2.0", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.2.0.tgz", "shasum": "6bd68cc1c37a3885c15211bbe1dce6d69f01e01d", "size": 186283, "noattachment": false, "integrity": "sha512-2arP4V6OWGU82q5hCYAfjuN0mizPZV2c65j2aEgwjabPLYIuSfaJFoytcjR8I8bbDVYh1HMfm5OoGdv0BqzT6w=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380491567266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380491567266, "_cnpmcore_publish_time": "2021-12-13T06:39:02.199Z"}, "2.1.0": {"name": "lodash", "version": "2.1.0", "description": "A utility library delivering consistency, customization, performance, & extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.1.0.tgz", "shasum": "0637eaaa36a8a1cfc865c3adfb942189bfb0998d", "size": 186172, "noattachment": false, "integrity": "sha512-5BzuEUmAfrZX28Zv3p6KKyIl3SnswGZblKJA3O20ZUDfBkqlXv87oMvmTsiQnM9g0dVXVOtKTuIIg7cnAGZICA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379915862595, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379915862595, "_cnpmcore_publish_time": "2021-12-13T06:39:03.409Z"}, "2.0.0": {"name": "lodash", "version": "2.0.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "dist/lodash.js", "keywords": ["amd", "browser", "client", "customize", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "jam": {"main": "dist/lodash.compat.js"}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-2.0.0.tgz", "shasum": "9dcf9e3fac04ad0a38c9e2db69c9fb7ce26dabc2", "size": 184960, "noattachment": false, "integrity": "sha512-6Ebs0FtZaSrJX8Kr+yFYBlWpv5yoRWiaVQCeiSQiJxouECdp3JXvWiWymwsv3uK3qTmV3OB72nFc8w8Fn76imA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379132548159, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379132548159, "_cnpmcore_publish_time": "2021-12-13T06:39:04.955Z"}, "1.3.1": {"name": "lodash", "version": "1.3.1", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.d.ts", "*.map", "*.md", "*.txt", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@1.3.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.3.1.tgz", "shasum": "a4663b53686b895ff074e2ba504dfb76a8e2b770", "size": 256883, "noattachment": false, "integrity": "sha512-F7AB8u+6d00CCgnbjWzq9fFLpzOMCgq6mPjOW4+8+dYbrnc0obRrC+IHctzfZ1KKTQxX0xo/punrlpOWcf4gpw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378304740429, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378304740429, "_cnpmcore_publish_time": "2021-12-13T06:39:06.439Z"}, "1.3.0": {"name": "lodash", "version": "1.3.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.d.ts", "*.map", "*.md", "*.txt", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "readmeFilename": "README.md", "_id": "lodash@1.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.3.0.tgz", "shasum": "09f6f55bceac513ee5681665947630fa8ae7593f", "size": 217865, "noattachment": false, "integrity": "sha512-hvb4OS6A2bHKEjqLNb2B724q4Q+B5z0yEX2/RstbFJXTZyx27aculQfs8v27wUehdWBSydJ+kCVSzJaXJY6dlQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378304719793, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378304719793, "_cnpmcore_publish_time": "2021-12-13T06:39:07.933Z"}, "1.2.1": {"name": "lodash", "version": "1.2.1", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "readmeFilename": "README.md", "_id": "lodash@1.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.2.1.tgz", "shasum": "ed47b16e46f06b2b40309b68e9163c17e93ea304", "size": 201498, "noattachment": false, "integrity": "sha512-EKI82Edm8glH3FUu17sQIF+Ly1cW2ROPc0qgf1L4DBUysBlQVL+/b+WtufJw0O8FtMo7Vq9KTrzUboyWSgW/tg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378304698381, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378304698381, "_cnpmcore_publish_time": "2021-12-13T06:39:09.573Z"}, "1.2.0": {"name": "lodash", "version": "1.2.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "readmeFilename": "README.md", "_id": "lodash@1.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.2.0.tgz", "shasum": "5f16a3318ab3bf680c7b41b5bb6327e9e1086ec4", "size": 200284, "noattachment": false, "integrity": "sha512-ExQAVirH2oQGyLAJv8jODSh25/hP2Fz24nn/7Yu6XeYcJKs5l/AY/9N1CZ85nykxoRkJBB6CyWrv+AxO3eSXyg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378304674140, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378304674140, "_cnpmcore_publish_time": "2021-12-13T06:39:10.990Z"}, "1.1.1": {"name": "lodash", "version": "1.1.1", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "readmeFilename": "README.md", "_id": "lodash@1.1.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.1.1.tgz", "shasum": "41a2b2e9a00e64d6d1999f143ff6b0755f6bbb24", "size": 197137, "noattachment": false, "integrity": "sha512-SFeNKyKPh4kvYv0yd95fwLKw4JXM45PJLsPRdA8v7/q0lBzFeK6XS8xJTl6mlhb8PbAzioMkHli1W/1g0y4XQQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378304647907, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378304647907, "_cnpmcore_publish_time": "2021-12-13T06:39:12.870Z"}, "1.1.0": {"name": "lodash", "version": "1.1.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "readmeFilename": "README.md", "_id": "lodash@1.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.1.0.tgz", "shasum": "bd0bb7f858f6c09ae140f23869261e555bd6ec1d", "size": 197122, "noattachment": false, "integrity": "sha512-KXV9Vye0OKVjfJYpkXYIx3Q8xa2c9rYPufqhnva6ck4PNK5WR65n9JF9a3ikWBS/M4rZjXVfWRqiqUcVYVIBiw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378304625728, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378304625728, "_cnpmcore_publish_time": "2021-12-13T06:39:14.445Z"}, "1.0.1": {"name": "lodash", "version": "1.0.1", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com/", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "readmeFilename": "README.md", "_id": "lodash@1.0.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.0.1.tgz", "shasum": "57945732498d92310e5bd4b1ff4f273a79e6c9fc", "size": 113580, "noattachment": false, "integrity": "sha512-FgX9IMPSwJM589qAZJzLArkmvkqkZIAvgYVuFwXO/UFls4/CzTJEBECNAYkGXnfU14m8nptUlOl7bQpeWNmEbg=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377926207715, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377926207715, "_cnpmcore_publish_time": "2021-12-13T06:39:15.977Z"}, "1.0.0": {"name": "lodash", "version": "1.0.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./dist/lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./dist/lodash.compat.js"}, "readmeFilename": "README.md", "_id": "lodash@1.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.0.0.tgz", "shasum": "2535c2f7dcb6937cee8d2672c5f7138eee0df6a9", "size": 113330, "noattachment": false, "integrity": "sha512-WHDYO0qSDoEe4PJznK52IoMBj2szpaTUnG6iJarwjeW0LymFN46KNDoiOLOt4OS78e1e+A9jQrfbMNxue6l3GQ=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377925902645, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377925902645, "_cnpmcore_publish_time": "2021-12-13T06:39:17.531Z"}, "1.0.0-rc.3": {"name": "lodash", "version": "1.0.0-rc.3", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@1.0.0-rc.3", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.0.0-rc.3.tgz", "shasum": "807ec29d0b768c7bb1025a796b8dec137fb06957", "size": 72389, "noattachment": false, "integrity": "sha512-NJD0CmjjmQfqJgny4TwIqwJjuhD7QgTbMvhjG/tbEP5xzxUVyhBe7VR5J2AxX2ZDjIHBy1xFBcjcPMflaJiApA=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377925731705, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377925731705, "_cnpmcore_publish_time": "2021-12-13T06:39:19.141Z"}, "1.0.0-rc.2": {"name": "lodash", "version": "1.0.0-rc.2", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@1.0.0-rc.2", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.0.0-rc.2.tgz", "shasum": "58c819a059ca25d52c289f7a01b419a4f3b945e9", "size": 70543, "noattachment": false, "integrity": "sha512-cOgWHORUWnM0YhviZKqaoTNZOp8t6VCBBfuFhPXIvff2jVOD+bCCJuWpu3+u3rfdyG5ur828hnxvWjnsyZh+Dw=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377925531147, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377925531147, "_cnpmcore_publish_time": "2021-12-13T06:39:20.741Z"}, "1.0.0-rc.1": {"name": "lodash", "version": "1.0.0-rc.1", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@1.0.0-rc.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-1.0.0-rc.1.tgz", "shasum": "4d25e7a3471e8c79f21a53bc9548ff4c3c751246", "size": 70507, "noattachment": false, "integrity": "sha512-RU2HVROwIiC4i0GxJXd+p8ZEOVIA2G30fA0asPT4OQhagGB/IqebmZHTebFoWmp/H6bwFLD3aF7pHh6NdqGvTA=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377925228060, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377925228060, "_cnpmcore_publish_time": "2021-12-13T06:39:22.539Z"}, "0.10.0": {"name": "lodash", "version": "0.10.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@0.10.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.10.0.tgz", "shasum": "5254bbc2c46c827f535a27d631fd4f2bff374ce7", "size": 68407, "noattachment": false, "integrity": "sha512-v56iCcHwqxrkkFW08F6GFAEYjL/E7Exbk2HZKhaZJsogEMSqse9as4Zmx5fmci4ZCQ/UNVoFHGudYzONfGxANQ=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377924969871, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377924969871, "_cnpmcore_publish_time": "2021-12-13T06:39:24.326Z"}, "0.9.2": {"name": "lodash", "version": "0.9.2", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@0.9.2", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.9.2.tgz", "shasum": "8f3499c5245d346d682e5b0d3b40767e09f1a92c", "size": 67706, "noattachment": false, "integrity": "sha512-LVbt/rjK62gSbhehDVKL0vlaime4Y1IBixL+bKeNfoY4L2zab/jGrxU6Ka05tMA/zBxkTk5t3ivtphdyYupczw=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377924741307, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377924741307, "_cnpmcore_publish_time": "2021-12-13T06:39:25.955Z"}, "0.9.1": {"name": "lodash", "version": "0.9.1", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@0.9.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.9.1.tgz", "shasum": "9009df00e6e3166935312b3a3edfd9dff06e3562", "size": 66699, "noattachment": false, "integrity": "sha512-TwMDfvu57jxxYuFmj39eW5PKYNtC1+vQ8MDawNVC/OwyAiC910EszLl7tIq4L7kcsqJa48Wz85YIWv52DXfCyQ=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377924555754, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377924555754, "_cnpmcore_publish_time": "2021-12-13T06:39:27.692Z"}, "0.9.0": {"name": "lodash", "version": "0.9.0", "description": "A utility library delivering consistency, customization, performance, and extras.", "homepage": "http://lodash.com", "license": "MIT", "main": "./lodash.js", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash.git"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "readmeFilename": "README.md", "_id": "lodash@0.9.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.9.0.tgz", "shasum": "5647e17bc474a1903f4873616d5a2bc615d6620a", "size": 66823, "noattachment": false, "integrity": "sha512-9GKocE3Wxer2MpDZvyC502zdGct3HplIfhCAxstyFqXuDhnD8bzsDKrulssxCZ0XtkZOois3f8Xu9RLTJ6tzNw=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377924380474, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377924380474, "_cnpmcore_publish_time": "2021-12-13T06:39:29.333Z"}, "0.8.2": {"name": "lodash", "version": "0.8.2", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "./lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "scripts": {"build": "node build", "test": "node test/test && node test/test-build"}, "_id": "lodash@0.8.2", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.8.2.tgz", "shasum": "4c59d6b23ce894412a13dab22304772d0cb11ae4", "size": 5183600, "noattachment": false, "integrity": "sha512-M+DpqmTu6ynuy13IGw1062/jOE8lNuAWad5X912Y4/l2EmXXlzKZu7KH7LPAETMllWQypknIlQPcIwILVLS5+g=="}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1349855491600, "_hasShrinkwrap": false, "_cnpm_publish_time": 1349855491600, "_cnpmcore_publish_time": "2021-12-13T06:39:31.272Z"}, "0.8.1": {"name": "lodash", "version": "0.8.1", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "./lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "scripts": {"build": "node build", "test": "node test/test && node test/test-build"}, "_id": "lodash@0.8.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.8.1.tgz", "shasum": "e4948649217958a8413600fa3a63941cfb8f4bb6", "size": 5184213, "noattachment": false, "integrity": "sha512-bDYGM5Mgyx+00YUAJjcIoi4QOtTSP64KwtRpsoDSUkptiQfpjuIAldD5oR9W1tZmU8BNiPonD4CxtfQ3FVy4lw=="}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1349340809540, "_hasShrinkwrap": false, "_cnpm_publish_time": 1349340809540, "_cnpmcore_publish_time": "2021-12-13T06:39:33.483Z"}, "0.8.0": {"name": "lodash", "version": "0.8.0", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "./lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.js"}, "scripts": {"build": "node build", "test": "node test/test && node test/test-build"}, "_id": "lodash@0.8.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.8.0.tgz", "shasum": "9c66ca5f6a886087b1f980eef2a3991a98a22f7a", "size": 5183813, "noattachment": false, "integrity": "sha512-KvKW++QipwbsGkAJ1z0hGM4NgfCKyltT71GyF7tLyymD3BKTcrxSuQA/gBx5kwKji10SziRP8j3Oul3jcCroXQ=="}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1349160578116, "_hasShrinkwrap": false, "_cnpm_publish_time": 1349160578116, "_cnpmcore_publish_time": "2021-12-13T06:39:35.661Z"}, "0.7.0": {"name": "lodash", "version": "0.7.0", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "./lodash.min.js"}, "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.7.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.7.0.tgz", "shasum": "8d0649e1fcb58a546ae9cb9f86f61d442571ae83", "size": 5177822, "noattachment": false, "integrity": "sha512-agVE2mFWg/GfSdeIFIs8l5Mq8sh4rNBvDfTOqOPRkqqrT25z9bcTZemaSr+V9SS0IVHKNq28Gl3WYJrRQPFFYw=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1347380647425, "_hasShrinkwrap": false, "_cnpm_publish_time": 1347380647425, "_cnpmcore_publish_time": "2021-12-13T06:39:37.997Z"}, "0.6.1": {"name": "lodash", "version": "0.6.1", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "lodash.min.js"}, "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.6.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.6.1.tgz", "shasum": "9b093d490fd4fc50091609a92f4bbb5d10396357", "size": 5169689, "noattachment": false, "integrity": "sha512-OPb6TH8ycWKgocyPsP3KEP6q/2bqqGa0/Tm+BKpKE1M83hmFpI/2bmN/TYSAZLtXm4D8qFWQKjeOkeEn3+pw8g=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1346313698808, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346313698808, "_cnpmcore_publish_time": "2021-12-13T06:39:40.452Z"}, "0.6.0": {"name": "lodash", "version": "0.6.0", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "lodash.min.js"}, "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.6.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.6.0.tgz", "shasum": "78712a5c31fb040f4396e3c4d42884d4c75a3ecb", "size": 5169662, "noattachment": false, "integrity": "sha512-ifOsEB21qdwE/X4j0Icw5Ot6jdFrJVJQKqsgSI2nTTrsr1157ixUgTD0j7rxDTl8mraya/DqxqUKzxVSbpLFdQ=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1346169669459, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346169669459, "_cnpmcore_publish_time": "2021-12-13T06:39:42.596Z"}, "0.5.2": {"name": "lodash", "version": "0.5.2", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "lodash.min.js"}, "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.5.2", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.5.2.tgz", "shasum": "4c873f93bdddeaa07401a675f8e17ded5af9a827", "size": 5167297, "noattachment": false, "integrity": "sha512-fCK8wz3zQ8rmj9elvA8MsdA5IgmuaASVlL6kuYBF+2/GDYMNodIrzl7QKeHFiYxUhgMpDXtSohBpKEHw2l6ERg=="}, "_npmVersion": "1.1.49", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1345652523757, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345652523757, "_cnpmcore_publish_time": "2021-12-13T06:39:45.239Z"}, "0.5.1": {"name": "lodash", "version": "0.5.1", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "lodash.min.js"}, "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.5.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.5.1.tgz", "shasum": "70a60e7ae06c7a0838cf44b96dab9d8cc1454838", "size": 5166138, "noattachment": false, "integrity": "sha512-F0Br1/oIhUjch7aa7WHrkz3i3Y1bJ4Io3vQ8r5L0KkW+DGDzabORXMjTlwmfMtUCQQC8pACRNm4LPcnE3SzNww=="}, "_npmVersion": "1.1.49", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1345320942131, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345320942131, "_cnpmcore_publish_time": "2021-12-13T06:39:47.431Z"}, "0.5.0": {"name": "lodash", "version": "0.5.0", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "jam": {"main": "lodash.min.js"}, "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.5.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.5.0.tgz", "shasum": "37f7912579b92139e9b657f2e2b80ab5c8ca4643", "size": 5165966, "noattachment": false, "integrity": "sha512-DE5+P5UOOeTIhLolU1V6qylAv+WnqNXieXjdw0HdKFhhn4XGifI18UiZWd1da1ZtWrXqhnhDpLQKQNBj/Gukcg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1345234387054, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345234387054, "_cnpmcore_publish_time": "2021-12-13T06:39:49.578Z"}, "0.5.0-rc.1": {"name": "lodash", "version": "0.5.0-rc.1", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.5.0-rc.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.5.0-rc.1.tgz", "shasum": "b3084c61e5515d86cf680aca01a5840c51e15e6a", "size": 5140075, "noattachment": false, "integrity": "sha512-BLGWug79CJFou7y/WPR6cE8jPMkIgah/af9fTvs7MK5qiIUCcmeFH6WL1bkFCl8G8yMUuIID7aaujehdyp6wmg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1344352107331, "_hasShrinkwrap": false, "_cnpm_publish_time": 1344352107331, "_cnpmcore_publish_time": "2021-12-13T06:39:51.718Z"}, "0.4.2": {"name": "lodash", "version": "0.4.2", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.4.2", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.4.2.tgz", "shasum": "e152362a0e7c5ef8c35894e58d99a520c822aed6", "size": 5132140, "noattachment": false, "integrity": "sha512-T3HW30V48FSIMUbHQ1241toUKtuedXjdlMGwikYC3sypCr/hJ20INyEqu1NmLy1Ki+c1BWifVeA0VrnpCeGacQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1342464581162, "_hasShrinkwrap": false, "_cnpm_publish_time": 1342464581162, "_cnpmcore_publish_time": "2021-12-13T06:39:53.947Z"}, "0.4.1": {"name": "lodash", "version": "0.4.1", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.4.1.tgz", "shasum": "56dda6e88d947361d53c075da64084be4b2b9907", "size": 5104665, "noattachment": false, "integrity": "sha512-V6kjvvv1Axc36Q73Z7Bq6fvGFD6fm7RVm/yhS1AWpxQHyqq/plg7IEVYU/uoswNHlkG6vNoDJek5lFKXA6aS6w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1342068991883, "_hasShrinkwrap": false, "_cnpm_publish_time": 1342068991883, "_cnpmcore_publish_time": "2021-12-13T06:39:56.166Z"}, "0.4.0": {"name": "lodash", "version": "0.4.0", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/lodash.git"}, "bin": {"lodash": "./build.js"}, "directories": {"doc": "./doc", "test": "./test"}, "engines": ["node", "rhino"], "scripts": {"build": "node build", "test": "node test/test"}, "_id": "lodash@0.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.4.0.tgz", "shasum": "9305c807119214f9d9d7728d1f3c86c3881431f5", "size": 5104504, "noattachment": false, "integrity": "sha512-oQcT6m8qkgaNSP3s6+ysJD5CV7J7XPTD37AUor2osl+okrfZMj1mi7uBPkeSEWsBuxXHEm4GCftH94e+44lLCg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1342026860142, "_hasShrinkwrap": false, "_cnpm_publish_time": 1342026860142, "_cnpmcore_publish_time": "2021-12-13T06:39:58.368Z"}, "0.3.2": {"name": "lodash", "version": "0.3.2", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.3.2", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.3.2.tgz", "shasum": "b28474cacc670134c856b24ad4183ff20dec7b8e", "size": 154848, "noattachment": false, "integrity": "sha512-v7NeemI3apXsXmkaOvSw5bqHBCH2uvVH1WK1YY0Jgs4QYvRLp0fd+Gs1quMxb8rjI42NhhsaHh2I8fLMuRXH/A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1339701589846, "_hasShrinkwrap": false, "_cnpm_publish_time": 1339701589846, "_cnpmcore_publish_time": "2021-12-13T06:40:00.491Z"}, "0.3.1": {"name": "lodash", "version": "0.3.1", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.3.1", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.3.1.tgz", "shasum": "4449723baa6d2ff0ab710fd5e4ea70e06ad5e319", "size": 154616, "noattachment": false, "integrity": "sha512-x6Ulr3iXKfQrRw0hgPDQK/KhIbxZFN5QDoxCYpGdm5hVAfXKncylMDLUCQIOlM49JNgJD3mFhGQNx/QjdUNqfw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1339387971792, "_hasShrinkwrap": false, "_cnpm_publish_time": 1339387971792, "_cnpmcore_publish_time": "2021-12-13T06:40:02.414Z"}, "0.3.0": {"name": "lodash", "version": "0.3.0", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.3.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.3.0.tgz", "shasum": "e7db413e8b50d48834e86f82c52fd64f0a9e5d6e", "size": 151763, "noattachment": false, "integrity": "sha512-J/aUWOfASTdkrHQTrPFv15Z3jKM3YfRi9iw5iv3hro5A2yiPlDQONmhe/MFz5OR1l6gMgPz6iaTZP46NC6sacw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1339012909669, "_hasShrinkwrap": false, "_cnpm_publish_time": 1339012909669, "_cnpmcore_publish_time": "2021-12-13T06:40:04.340Z"}, "0.2.2": {"name": "lodash", "version": "0.2.2", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.2.2", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.2.2.tgz", "shasum": "d79277dafa86346a181dd924365215b126fdf2cb", "size": 147586, "noattachment": false, "integrity": "sha512-OPzZZb4xiAJMdQiiX0+lNENfJi2Q81d47msW92eAO53WsGvfSLPNuH3Y5rj16X95LyDJJFZI5oTyZH8hqw+YAA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1338364586644, "_hasShrinkwrap": false, "_cnpm_publish_time": 1338364586644, "_cnpmcore_publish_time": "2021-12-13T06:40:06.180Z"}, "0.2.1": {"name": "lodash", "version": "0.2.1", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.2.1", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.2.1.tgz", "shasum": "fcdd734c401af6215d1ac511dc30e8003dde4607", "size": 147281, "noattachment": false, "integrity": "sha512-rF+K2OphXlCLxiBFZHXnqXfP3xfbaF5VxS+sFZRLvqxHzHWPI7ZjN4HW2EkvoMu9cC3mVQ2HZPrC/4qYh3x3/A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1337896388449, "_hasShrinkwrap": false, "_cnpm_publish_time": 1337896388449, "_cnpmcore_publish_time": "2021-12-13T06:40:08.395Z"}, "0.2.0": {"name": "lodash", "version": "0.2.0", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "homepage": "http://lodash.com", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://lodash.com/license"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.2.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.2.0.tgz", "shasum": "efaa16fb4614bdce9a7a9fdda487125d3fab38a2", "size": 146726, "noattachment": false, "integrity": "sha512-WUvXFMYqweevRJzqnj5G8pJ2wOjNHTnP4+x+qBdoGstLRo3oWob98Oydu7fMD3bZJ2oXrzx5AJBZoc/jlBfLeA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1337659584044, "_hasShrinkwrap": false, "_cnpm_publish_time": 1337659584044, "_cnpmcore_publish_time": "2021-12-13T06:40:10.416Z"}, "0.1.0": {"name": "lodash", "version": "0.1.0", "description": "A drop-in replacement for Underscore.js that delivers up to 8x performance improvements, bug fixes, and additional features.", "homepage": "https://github.com/bestiejs/lodash", "main": "lodash", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/lodash.git"}, "engines": ["node", "rhino"], "directories": {"doc": "./doc", "test": "./test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.1.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/lodash/-/lodash-0.1.0.tgz", "shasum": "392617f69a947e40cec7848d85fcc3dd29d74bc5", "size": 263470, "noattachment": false, "integrity": "sha512-ufIfwX7g5obXKaJhzbnAJBNf5Idxqty+AHaNadWFxtNKjGmF/ZO8ptSEjQRQRymBPZtLa0NV9sbrsH87Ae2R1A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "publish_time": 1335199032603, "_hasShrinkwrap": false, "_cnpm_publish_time": 1335199032603, "_cnpmcore_publish_time": "2021-12-13T06:40:12.672Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "keywords": ["modules", "stdlib", "util"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}