{"_id": "@esbuild/linux-ia32", "_rev": "2328724-638e837f7ab81081f0e9db33", "dist-tags": {"latest": "0.25.6"}, "name": "@esbuild/linux-ia32", "time": {"created": "2022-12-05T23:49:19.901Z", "modified": "2025-07-07T17:29:42.267Z", "0.15.18": "2022-12-05T23:47:50.881Z", "0.16.0": "2022-12-07T03:55:20.550Z", "0.16.1": "2022-12-07T04:48:48.536Z", "0.16.2": "2022-12-08T07:00:09.787Z", "0.16.3": "2022-12-08T20:13:23.742Z", "0.16.4": "2022-12-10T03:50:53.050Z", "0.16.5": "2022-12-13T17:48:01.354Z", "0.16.6": "2022-12-14T05:23:31.181Z", "0.16.7": "2022-12-14T22:47:15.356Z", "0.16.8": "2022-12-16T23:39:07.898Z", "0.16.9": "2022-12-18T04:31:46.608Z", "0.16.10": "2022-12-19T23:26:54.747Z", "0.16.11": "2022-12-27T01:39:23.202Z", "0.16.12": "2022-12-28T02:08:19.564Z", "0.16.13": "2023-01-02T22:57:37.715Z", "0.16.14": "2023-01-04T20:13:21.896Z", "0.16.15": "2023-01-07T04:19:21.229Z", "0.16.16": "2023-01-08T22:44:09.190Z", "0.16.17": "2023-01-11T21:58:18.497Z", "0.17.0": "2023-01-14T04:37:09.163Z", "0.17.1": "2023-01-16T18:06:00.110Z", "0.17.2": "2023-01-17T06:40:00.193Z", "0.17.3": "2023-01-18T19:14:48.519Z", "0.17.4": "2023-01-22T06:13:52.943Z", "0.17.5": "2023-01-27T16:38:07.077Z", "0.17.6": "2023-02-06T17:01:03.555Z", "0.17.7": "2023-02-09T22:26:59.845Z", "0.17.8": "2023-02-13T06:35:56.536Z", "0.17.9": "2023-02-19T17:45:38.442Z", "0.17.10": "2023-02-20T17:55:13.662Z", "0.17.11": "2023-03-03T22:40:31.203Z", "0.17.12": "2023-03-17T06:17:25.006Z", "0.17.13": "2023-03-24T18:57:26.368Z", "0.17.14": "2023-03-26T02:48:00.019Z", "0.17.15": "2023-04-01T22:27:12.795Z", "0.17.16": "2023-04-10T04:35:19.945Z", "0.17.17": "2023-04-16T21:23:53.891Z", "0.17.18": "2023-04-22T20:41:44.018Z", "0.17.19": "2023-05-13T00:06:49.821Z", "0.18.0": "2023-06-09T21:24:37.902Z", "0.18.1": "2023-06-12T04:52:01.164Z", "0.18.2": "2023-06-13T02:40:50.145Z", "0.18.3": "2023-06-15T12:21:33.824Z", "0.18.4": "2023-06-16T15:38:53.458Z", "0.18.5": "2023-06-20T00:52:58.592Z", "0.18.6": "2023-06-20T23:25:13.364Z", "0.18.7": "2023-06-24T02:46:42.579Z", "0.18.8": "2023-06-25T03:19:29.719Z", "0.18.9": "2023-06-26T05:28:27.026Z", "0.18.10": "2023-06-26T21:20:44.703Z", "0.18.11": "2023-07-01T06:04:12.740Z", "0.18.12": "2023-07-13T01:34:28.833Z", "0.18.13": "2023-07-15T02:37:32.779Z", "0.18.14": "2023-07-18T05:00:38.780Z", "0.18.15": "2023-07-20T12:53:45.279Z", "0.18.16": "2023-07-23T04:48:21.554Z", "0.18.17": "2023-07-26T01:41:12.705Z", "0.18.18": "2023-08-05T17:06:44.896Z", "0.18.19": "2023-08-07T02:51:38.559Z", "0.18.20": "2023-08-08T04:15:20.615Z", "0.19.0": "2023-08-08T15:52:48.452Z", "0.19.1": "2023-08-11T15:57:49.321Z", "0.19.2": "2023-08-14T01:58:36.779Z", "0.19.3": "2023-09-14T01:12:39.791Z", "0.19.4": "2023-09-28T01:47:53.386Z", "0.19.5": "2023-10-17T05:10:52.061Z", "0.19.6": "2023-11-19T07:11:53.138Z", "0.19.7": "2023-11-21T01:01:26.765Z", "0.19.8": "2023-11-26T23:08:23.817Z", "0.19.9": "2023-12-10T05:09:42.120Z", "0.19.10": "2023-12-19T00:21:54.989Z", "0.19.11": "2023-12-29T20:32:26.493Z", "0.19.12": "2024-01-23T17:40:50.429Z", "0.20.0": "2024-01-27T16:49:47.894Z", "0.20.1": "2024-02-19T06:38:40.765Z", "0.20.2": "2024-03-14T19:49:57.703Z", "0.21.0": "2024-05-07T02:52:42.423Z", "0.21.1": "2024-05-07T16:55:23.024Z", "0.21.2": "2024-05-12T20:33:08.406Z", "0.21.3": "2024-05-15T20:52:46.643Z", "0.21.4": "2024-05-25T02:11:02.800Z", "0.21.5": "2024-06-09T21:17:14.861Z", "0.22.0": "2024-06-30T20:38:02.773Z", "0.23.0": "2024-07-02T03:34:00.162Z", "0.23.1": "2024-08-16T22:13:31.881Z", "0.24.0": "2024-09-22T02:06:40.665Z", "0.24.1": "2024-12-20T05:41:34.866Z", "0.24.2": "2024-12-20T17:56:50.883Z", "0.25.0": "2025-02-08T03:02:38.856Z", "0.25.1": "2025-03-10T03:45:50.594Z", "0.25.2": "2025-03-30T17:33:15.111Z", "0.25.3": "2025-04-23T03:56:27.886Z", "0.25.4": "2025-05-06T00:31:03.764Z", "0.25.5": "2025-05-27T03:13:10.790Z", "0.25.6": "2025-07-07T16:59:20.921Z"}, "versions": {"0.15.18": {"name": "@esbuild/linux-ia32", "version": "0.15.18", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.15.18", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-+I2pQyUBrh3I7FQXzzM4H8X8V6aG9JIwV0taSMzHoW/cg/vrVNtzGPXPXPf3Y3abgWGqTPWW+OConzvJrNpyqg==", "shasum": "6b0477521e24f94674a1c5c29a223e19c6721585", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.15.18.tgz", "fileCount": 3, "unpackedSize": 8208856, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEPIRaeDgQWrFi7GIsAjphx9+VVb1JGX7X6uCqj3ZdTuAiBbz+I4+AzO0srjVcVh3JI4AAg7S9CZGtVdm12kHV1Edw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoMmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqw4A//Uwq0QGLzIJG5Kb1UpYwDUTn257BuGNkVUIaDPXPRPhjamMJl\r\nnmHqhYEIKr/FdhiCy/WWQ3tzbh/cFpWNRxgQLau5dDdnD3lfWPE9GjwiGgGD\r\nwZrrePKXfkQfhZnU/W05MBLFaEs8wwDruTL1jhVzkoE+Jw9aqm/19U2MZC1j\r\nqtu594P20JQB9RAtOyYB8ZchYTiqxR227rcW4TbhJ7R1t2HkdhRQdfetNmvD\r\n8VejoP/XtIfnM78Vb+n3fEOUw++zsPVFFF32kW0OiRrleD4OaZrDiAHSMc7b\r\nM4tWKD1NhshmHSFM9G80bIG7KHRkrUMg1W3QR5NS1hSAVYzEuj1E8nUYgPvF\r\nF+SLLe3CsqgGLKEa78Ca81oJnGcDZRdkCpx/GCv0JTWxq/MCXSeL2U5f1IdU\r\n6t4AnY3b8B7hUOrjHHwlLspSyb4tr+r4PqRdYI9aHtloorqsib318J0d/Jyg\r\nDRHHjRrovzSV5G4kOSZtqhSqfGl9v8/udewoucRXxW+msTuhcjBBFSx8TixZ\r\nmROcEt/DA3MVEZw/okytN1CqbVmolwtS/T3S21+aZcvPj28tzGLNwyZAgDo1\r\nRQxXvBEUaMxdcW6yoaf9Rw3FV52goDKH5OvzJoX74DSQ8vPEAXqZko/+P7nE\r\nRHIGC/US97c9Mbh8ArNv4hJ9BO2UNynSG4o=\r\n=cp3c\r\n-----END PGP SIGNATURE-----\r\n", "size": 3402787}, "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.15.18_1670284070592_0.5219551841189689"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-05T23:49:17.843Z"}, "0.16.0": {"name": "@esbuild/linux-ia32", "version": "0.16.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-565grRH/xWOKNZifcDY7Oz75X4JeWe9n49zxpuMX//mKB3JKpjhbmePCR8RK9PDSMuRJXAR2TtpmzuAiR9pLjA==", "shasum": "5f1f5c4e3392d8c4ffcc33102a4b56908da5240d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.0.tgz", "fileCount": 3, "unpackedSize": 8208855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIANJXnGbK5ndLYx3WwzpaRTGerV839Pnj78O5hnOgYyfAiEAzXqyJBM4OVP9NXSLoCxKSSdDV+HaLcvUoiTY5ROOzLQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7YBAAnjooZ0Cz4lqKt5AazuEOl20KjF/324iMP7BYgaLmuzUOvy12\r\nY3YjgHD4ZNmb7YZl5RABH6xt22TjoKyZVULXUP2NTm1FXUAFXW+suhqjGJCL\r\n3JvIizYzOMHZPU8MG6J1QfRr7WdzI60lDjgNtwswDBfQ7LAyXEfoMbrTOgQG\r\nv52V+HwT+QI3svFgDQWEj+DKM999zjzjzpQlVe+Hq30ighbGv6dozUWrFscz\r\nCb1ErY5pdTArjjAAwSqFJ0B+ARByEc+vOjf40Wp9KPdkz0Q+kOu+J1PjP0Yq\r\nESBHAnLvWetiWOrABF6OzDXzP1yPuMp0cGaZ3jox8YOR5yQltivQwj1dpZM0\r\nmU+gO8nPfo+9ySPbKnkkcoiMUtkQdFU/stuBVXGrhIfkOmZf47faYQ+JX6/V\r\nOsRU6EtuYAdP1KM9vgz6HfbmjUNIYudlZoPU7wBpvlEnlb126pOMWRm7Zbg4\r\nkJs2wsKZSZiaQT506PmeWMFoa8wkb7a8dxfKaMgd/ar6rZMijRPv5q7nIKkr\r\nK5qLwyBl+TYLw2A6+X/8BAz8hpQ766AoCJBBZXSJEBHrqz/hIgYmgbMaXVdy\r\nsSe5NjnO6Hd6+bXBy4AvJdE12jYXUt+K4Pv4Je9qxTZ/g4ebH0xnfH9E55o4\r\n5RO5fkgfhBPVOne0H7mkhX/mhHCsYZH0f1c=\r\n=lrMt\r\n-----END PGP SIGNATURE-----\r\n", "size": 3404960}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.0_1670385320273_0.5977215788746095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T04:04:35.993Z"}, "0.16.1": {"name": "@esbuild/linux-ia32", "version": "0.16.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-rTiIs5ms38XUb5Bn7hbbkR45CS3rz/hC/IfRE8Uccgzo4qRkf3Zd0Re6IUoCP+DvcTzLPz1VLfDO8VyD7UUI0w==", "shasum": "e34635e929c17ff4f3cadcec656813574a654ef9", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.1.tgz", "fileCount": 3, "unpackedSize": 8208855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUwE2buW1tkyPRaCokKgbVdUfptjHYW3aohtehuEkcEAiBX7WyBI2zdnrx8ozbiVLq7XZWSSPFx0PopruD0LMTwfA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBswACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBrw//e+6fdQPVNoShdLY+Q7CoIxbHFXD+H/hIp7m403lgiZ8Po+Ph\r\nnxkItL5HMxetzw8RUb1gzhIV+RFYcUZVzzEwyN7mSOkmpF5QwejO8Mg7w7oI\r\n2HSVCJpzPxuh7qMeTcc0/iowt86yntip2TMuIMSkoke1A40XATO6KOoqJjgc\r\nw6QvYIB/SqvfALGOLkaL3DNO+Nk/8KobHdUJZKtXpL2thbkulZMWuJsMzDFd\r\nBxcoBnlWuVFY+sjIRJNeYyqLj6vYTgiWWI7dH/8NHM+EBwEU6ZN3V4yJjwHn\r\nXQ11JWXvlS4ftZqRByhrraZoSUbxYw+h4fMJEhlIrQae1z/06jNOnq0mXGF7\r\nWvDwejFpvedl77e4tkvsRWQguI53a2EOS5q6mWTqvtZmBhOvuSGwDQm29EQ2\r\nbPtrtWEDrXNSNmn94UZEEVF/eyhuRDYnICSnvZmRINlI00U/Qwr/QXIjd03o\r\na+gSjsZKIWQYYR2doAsxHLHQ3BMy3Yz2wnGDwupIW+rWnYI9ZbnJT7aoRCCh\r\noCVS+Cvn/yE8zzpHk5YW7xNuvBlZ/lEQGwKO6TqNe1NYhE4qqOzZP57wbZBh\r\nOFpYMvGeN4Ti9Y6AX7DaAD2Pi+ursFvyR1u3QKYoVL55YAVQhZanKCPwUXC6\r\ny1zf4HW+207b0hq/TQNxcLo1M8J5zsnJ360=\r\n=hZ/8\r\n-----END PGP SIGNATURE-----\r\n", "size": 3405042}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.1_1670388528237_0.04408891567316031"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T04:49:36.643Z"}, "0.16.2": {"name": "@esbuild/linux-ia32", "version": "0.16.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-TRz3MDvv65zXZ4NTJYi1yyVj17Qrsm8y6J8r4qIdd2qszRLPHmte4LAazPa7g+To6QfM2kL3gHmVhwV6GcYz0g==", "shasum": "6ec937d2c4a17db5c6bb0b898e86c42df97422f4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.2.tgz", "fileCount": 3, "unpackedSize": 8217047, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBWxcFWTqDR7O7j4G7r78+z4Xeb+lrblLfMaJGQHPo/eAiEAoN5lLrDCu8S3rGA3vqftF4PrfQEn5FI96ErJdUXYO2I="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYt5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorhQ/8DkK0C4yaF9wegW92Q3+ZdWIuGQi4moBaESCP4EW7Xw08iCQx\r\nZig9KX7mlD9KM0R6d8U+bpOq94b8x5nCr+86P3Ssjdm6oxrVYYEgT8cO/tQB\r\nrZIMQ0pjg932MnnI34Ji8y2o03uDGoMYcecdlM1H4HiF7m51oLYMp5miPhbG\r\nBlNWCZiTdCrms9TdfG6+Fo42roM+O2Nzcg1r6EH7X7EPmbO7oKlEViSgV5Dv\r\nlYKNI9EA/B2OQqy7OO4N7KBgASayOjcYIvQ0406cDjHYYsliC8mBuqkbMQVp\r\nHt7eqssnRzV5YOXciaLH6jlSIWRsLhtkjP2jqRQcWXz1HfFAx4scoUUKNW2J\r\n7f7zOExpmx8fYQQ9rm9JYsgDLX/aOKDFu/2biRJNHzdH6pqCAfZC7ADf59oA\r\nW2K9SeHEtdsnUv+NRxDazhZZZsMEAZvrYKXSvW8QPMP9EyOdR7QNLj3NlX11\r\nXZxEPxenJi3ZboZe2NpsRjXvB+8lDZtVPu/6U7+gpfmZ2IW605w/camPxqAj\r\nOsAb3FGGwXBoISYd/9o7TH1mt6oGUM8qN6sBbFcWYyb5PKEXUISf1IVN9SAd\r\nB8Uh5fUVa9TzpXhUfkce/p+mt8jbuXXgLQfKcuN3+uW/n+TQcH22OZvGhf22\r\n3RtZ2OifTzu1ncB5ANpf1zkvJ+W9hzbVnz0=\r\n=Hj0v\r\n-----END PGP SIGNATURE-----\r\n", "size": 3409319}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.2_1670482809477_0.8216534097573844"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T07:23:55.600Z"}, "0.16.3": {"name": "@esbuild/linux-ia32", "version": "0.16.3", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-X8FDDxM9cqda2rJE+iblQhIMYY49LfvW4kaEjoFbTTQ4Go8G96Smj2w3BRTwA8IHGoi9dPOPGAX63dhuv19UqA==", "shasum": "2ff3936b91bfff62f9ecf7f6411ef399b29ed22d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.3.tgz", "fileCount": 3, "unpackedSize": 8217047, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrVrR8oAK+rlvLaUm8BhQNgXoSA/dlAV4uK8iDWOIffgIhAP1VQRg+FpMIAVJ7TatwV5zGSSCIaf9Vp5N+Pe6mCoI+"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJPg//cWDwrD0untzwfxOPnJ7zOY2nbMDi9aYkQeB6Ja/hY90/PUD8\r\nmfOmuo207WAlMlf3Xdy6aw3IctNtjoUB0btOV0czBirq5xfQeRHgX8LQNbDK\r\nAcUCt4wA5zhZjz2loUoR2WpgEBkue0sLnuFfSw7nIACWOztKecmMqXRsxMUv\r\nxDcXh2Z1Bh53rxboEM3oruIqR50Kxhu1rmqrPCD1MeJ/4ResTB841a8/BNAP\r\nF8vRVDToBmCWK40j5hbsvCLp3MYez3d3xU393jb63bv6werNtPwH4c0FvpK5\r\naKQQ6FfO3TGJEkhtgzmxDwj1Q+bduWepDsCz3H4kfJTl0L4Yc2rK3AYYvSwm\r\nO0AqGPKudzB5iNh+m+NLEZGIJMUEZ3FdC76fEwPv9948PJIB+l3sogfVN4Fh\r\nnVkDr0x9LpfdvslH8Cnr3BwM/EaMd4sLj5WE912KYoAX+Maeo9Or0/opqKxc\r\n4cm+GTum6UFtMyuG3YfQGy2USeaDj85jvT9IJCOU3kuPR5thWYP3RgQU81E8\r\nvZofczDTzo21pB+/LCY+Xa86716YGQ4WdM32dTFBFnUZ+cx54w1ZTkzz2Wg+\r\nFsxxEMYBYPt5wA663DlgTZwqPO1WAAtVAQplEOpFyhPn5E1AeUXOQEeiltl1\r\nucj1RckN1gWd15/h7yWtodjnNf1DYYZrhOg=\r\n=bOzW\r\n-----END PGP SIGNATURE-----\r\n", "size": 3409164}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.3_1670530403401_0.3117118426457761"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T20:25:06.383Z"}, "0.16.4": {"name": "@esbuild/linux-ia32", "version": "0.16.4", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-uxdSrpe9wFhz4yBwt2kl2TxS/NWEINYBUFIxQtaEVtglm1eECvsj1vEKI0KX2k2wCe17zDdQ3v+jVxfwVfvvjw==", "shasum": "117e32a9680b5deac184ebee122f8575369fad1b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.4.tgz", "fileCount": 3, "unpackedSize": 8217047, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAhlBScK9FE0oD7XLg5ScJmRL/aU1vVgPKi4t1OUgFu9AiEAvnMlv8ekyAIvUP+unBFKWUx2gTPmitIoF9WCX8LhxLk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAIdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTZw//YnR5FwZsJ+LXvhZcNWDxTrpEVNM773CUG8EOgnkirPD/jv/k\r\nH/Nb/8c9ESYke3pDYEPeGwD9VTLP60JBQbmUGf7xaL5wPir5W1Joi2XaDwjQ\r\nR7nlC5/Kmr6/ptTbpDV6uhlf1QpCTMpCDUK3w4s1zwcEYlX6LNTqVgcrhogE\r\nBqcG0qdxzo79h8KtrnhSEeo/r0a2X3x74XFBgq/DgFqqIWVPdXrQKT2s5YYC\r\n8Ve8Zf3EjudQn0sQ3oiLrPtkQ1bsz3ITb1PRbZEJNL+gObVXgcSHSNapDCf6\r\nFDqj8oZqz0xh+Z/wgU138hI72XFyA547YtyA9XCf1qIusre9bhVJ8tTzeFYy\r\nol96kwWbuR1VpzyKTIceVfQvcVPESPhj4HHjQCGFQKZYQ/Z85jqNbsMA1yed\r\n+kMyJaeZmGFZHQ6MW9OfU6ju9yx3B2lQ6rsHr9vThbgALNaIZjP9OeamCbCa\r\n0/IPrgsRTGqd69801gt8GIyyuM6gEibg2RI5uUprjDXnmmOfRyfQ0vSosFak\r\n+odV+l8EAU4fDEH0bnZNbtPKe0qPMSssOU9cPoRrCdScBwWefp49SQ6mhVAr\r\nKRm06YIbJJnQi05B9vAau2z78Uk7ci9FtnNzzfmvX/8BKkRXwQs5gwEL37LD\r\nvWYWsWs9+lqMbUcAPBWKv2hJGsWXZslOYns=\r\n=gYGr\r\n-----END PGP SIGNATURE-----\r\n", "size": 3409779}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.4_1670644252773_0.8845170533742492"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-10T03:51:00.345Z"}, "0.16.5": {"name": "@esbuild/linux-ia32", "version": "0.16.5", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-P1WNzGqy6ipvbt8iNoYY66+qUANCiM80D8bGJIU8jqSZ613eG0lUWBePi4xQazcNgIi9tSiCa9Ba3f4krXtQDw==", "shasum": "9d897e7fff2f31433fa2c7b00048738dc9d47a69", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.5.tgz", "fileCount": 3, "unpackedSize": 8229335, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/Bl/lVPvNsAug5Hox1Ydq1awbJ3ue9A4/n9NEa1XeGAIgTMTiVFnRB/xDHaL/ZnkLOTcEX4zloJ+U7HeJJSb3yis="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLrRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZzg//bop3+EzoR4UtWF9oVMHJRQchiOUSNm3onKDNV5LBv+CVKbLZ\r\nvWKIFNjHIdmEW60eTQV3+7pjzqaqqdmEMja4mxMzpPjF3QYrqiMbX+EfTnaP\r\nr2glkzlYOgDB6OEYqU2ZxPWlb/qDnprFgqsq1w1zl01n3vMaVAAhQGgBVtax\r\n0Xcg/D8f13tugmyqH5rglNy1Kta8fuzO68oW3LNFM5wgpvuov0ySzmQ86iE8\r\nCtHsRcgJK154JKOwqN2oOBaC27gO3b4sTQoWH0RIHESawe3OoSK1r78hvCPp\r\nGFfQ0RVB+yNeAN6TKQjIe5TJn9G+Acv+JNnmdGn4SQKg/a5lgsrRJpzHZQbm\r\n86r6u6VnmV2hG5VGA8tRjbFMURMhVxsIjDnHDySNGl3/hCDq31BUbRwZvTdE\r\npTFsoEI/m8wCQjyRN0x8RgIguT++6/4YJAo1VZzC1K595eni2pdb3EHXspbF\r\ntYwAzBxxElRCXnx4bvGqNFlzAkUiggoTJ9si1ikWIyVo/qyQeS04Fytusz4u\r\nbJsPw2puLG1esnLfyHVEf21zzCwJtTtK3As2vR1C1gRCgZJyY1aMSsYJGyqs\r\n2hbnly66MD+nBF0CDF0uzcDawwxfzarZXXUQW7m5pobn1VPDtExzYlr9l+B3\r\nd3/eBugtnFEPJ+SlO3gj5znrbMeyKThaItM=\r\n=Vjqu\r\n-----END PGP SIGNATURE-----\r\n", "size": 3415763}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.5_1670953681090_0.4620441235343473"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-13T17:49:12.872Z"}, "0.16.6": {"name": "@esbuild/linux-ia32", "version": "0.16.6", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-MyBWPjAMAlnkYANHCjeun2QsOn5cY1RxXAqnG0hE+fEmeX/hJK9pj6wQ5QptAew7sKt9flcOLKEB/hn2mr/xUw==", "shasum": "916c13e4076d03cabbbfaa6735e6c195fe332932", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.6.tgz", "fileCount": 3, "unpackedSize": 8237527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2qndQio9vz8L6v8p9CIXm7ylG4WyLYYQJsx+wmdaAwgIhAJ6mJbXDZSiNxR9e3KVXZT1x/Dezlu59rZnKhiJJtxZI"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw8xAAmndgrS3OZikX0fYwMDBOzDk8M4jQlXZiusv4JTalL8WQqVv4\r\n6bBvcrOyTtGM9UgfXTt/mQRlh2laOrc7QGDsf8E1yUof20NeTqeejfVn/b8A\r\nquHlwZ6StIf0oD6fo6xBPxpMZOXug/A6nwpTWeS7vnC50u/G8ZbEtRIe4edG\r\nrtm2m5MFSRJeFN57unb9HDSc9BOGbMOZrTNAen76oxdX3u0j1N71Ve/KXupF\r\nNghKIBdFoRdKfBA+bqOCFInqsfmK5OmnMHBlaSDiftNbUeqiZ1S62L6KO0z1\r\ndMboUrnyJMQu99EY4NQR1PoDFq1Vfi4AXyMBYoLunDwDVcr1GAPAohH1HZz9\r\n+fUVzNUzaCromhCiOp+sgLko3fmo3u7nMgVXDFOHPwknR1lJBE9gAS/GwTkW\r\nlDCNiwvFGSWgE6sG85CusypOQsWVI8svoBQpTmYmdAQyRvzfpnXb96KsGU4r\r\nIxhFQYuHN+Jwr63gYf9rE8Rr4wZxtG/JJc/Qc4U0pa4oUeWHlb5MqiF0xN9t\r\nzerWK5qT8U6Ct2h3dpT1b69gjoR0L0yVspJO2uAkzoOXI+mkcelZMVMSD1z5\r\njZBDRbx++x49LJl15TRI4papzFXn6mbsHaw6ELvNlpfz/dGP5ooMJF4yS9Mo\r\nzJ9KkLNuxFFJbnsJ0fGRarngGte3d1yN3Bw=\r\n=sZBN\r\n-----END PGP SIGNATURE-----\r\n", "size": 3418477}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.6_1670995410880_0.8419391320080087"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-14T05:23:44.038Z"}, "0.16.7": {"name": "@esbuild/linux-ia32", "version": "0.16.7", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-APVYbEilKbD5ptmKdnIcXej2/+GdV65TfTjxR2Uk8t1EsOk49t6HapZW6DS/Bwlvh5hDwtLapdSumIVNGxgqLg==", "shasum": "a15dc3edf6953c5414add4264fd8335f48775490", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.7.tgz", "fileCount": 3, "unpackedSize": 8245719, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFgM6Q9B9aLk7sjnnpd4khVUbUzb1tKIJ5OhjBa4FgXAIhAJNiyKgtFA9Nc30pzIKP7YDG0qEhR9jhXFIv4vri05OR"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoy7Q/+L4dEO2T6uGZTVSU7Tv6x4bNilcafHgrJCaw6g5XRfePX34UY\r\nQlKuiyV6hZVdUsTBN21dti5MHlbMtPp4zr0h/Wq+ZXtHgkeGNLfxOiyEfjqY\r\nIuAhKu9hyazglyDP2zktnLeOo8WnrAbKbOpT/SuH8xEl5Gkyp+tXAzPGf7T0\r\nhNwNBQCTM6jem5iX7p5jQiR8lpc7Lbhznrz/ObPPASS7hrBlb/lnH7SjucSF\r\nNMFEr2kWhWQTqfKffyAe8u4Mur47XX/hOkIaS2Je2aExyp6w8tp+XUyaDDNZ\r\nfLUW+e33lRDoZrpVpbQZqBnDZ924lA1PhqXSghlhBJScp7/OXPMD6pmzde3R\r\n+lVp3TrP7F53A0gv4tCO10ITyg91EP3pecVlYzYMq5tz7SZd25kCI/vZLa3y\r\nv6JLaiaRviZXJop9Tb9sL1lDQMHSqTJjrTwVRieH3Sn+J4iAi3/e0iT7TJgh\r\nRgchumkPvlBUOH+jz6/HvgICfiTrkKtdTqf7csl6RXFXdr1F1O44FhXVp2/R\r\nMn7707tIF00Eflw6zPZjWxtcoznb503Ex01pdyCtsOOOy5LB3V3WlOnVHMMX\r\nee8QaBHsdqi4ubcBXfoYySYxw1Ra8I7fUc9ocw8eXFTqNaPuf7yk+FZZQDUk\r\nILSmVBR3MLoep846wTRp0n4tjh64s0htA+o=\r\n=OHJR\r\n-----END PGP SIGNATURE-----\r\n", "size": 3420698}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.7_1671058034982_0.010981409285699906"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-14T22:47:22.011Z"}, "0.16.8": {"name": "@esbuild/linux-ia32", "version": "0.16.8", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Jt+8YBFR2Pk68oS7E9z9PtmgJrDonGdEW3Camb2plZcztKpu/OxfnxFu8f41+TYpKhzUDm5uNMwqxRH3yDYrsQ==", "shasum": "7665de87f89bde21166da55f097f91d227945108", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.8.tgz", "fileCount": 3, "unpackedSize": 8249815, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBSq8po9r3vr7tN0WIgrgG8Q1ZKMEyQXfBTCfocw8aZPAiAVPn16LFar8j/rfOe6K5t2iGlR95tOWfXHDVPrQVjoBA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQGbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp01Q/6Axb3t7BQcXOb4bzq4md/PHPfOu5GxyfAxaFfG8Bu1qWcSdVq\r\n8IAViId3BRE+lmTkya1BqyQ79o4WxnZij4ufULoUzGnFgU5EqfECnzCHT/DT\r\n6RCc/5lI/M2ntScHXGcwX1vUpUFb2t0/Hp+EQELkTOkw+hc89BX2O9jo/ORK\r\nWhvllCXkgvc3SuYBrI4Qx3AHsSBlwvVgZ4z/0jCCay2wgeWoYD6WGj47cfv3\r\ndkP4K9ADJrNzxXe8vy3PNFGAw4rNAAerfMiBKK1Y8tH2iTPpYMQ4MWY/nfog\r\nCKco867x8NePWYDX+BLp75RIOA5FFUV9wYWiEprR40O9fe5yD6vK8LRHv2jb\r\nYQw5lGi0Tx1dTc3sHBSnA0wD5d0Rm2vxFIcMC4jF9krJsdB6AW5XsGx8JygH\r\ntdIlzgEUMKsHqlb2+8na21Ii58FuDunj3ULRqKxvo+DYZDiaQotPJBRzR5Vo\r\nzaxxnfce4tFy3+MjsLa21WWevR+cepHiddwK1X7YM7H/qb+yAcogixmaep46\r\n0nE98Amj8komQVfTDndn8z7kIztSiITeGFsiqKb4N7N/p6KSgAQWEdET+qmt\r\nb61UnyhBeGtc5AkfHvz2PYAUZUo3VeDYCuH0JqLftKsUHZRGiacmIe4+x21r\r\nmX5+H1Xblx28OHJlTDFFZvsBAsgA9CAtUMo=\r\n=68QG\r\n-----END PGP SIGNATURE-----\r\n", "size": 3423829}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.8_1671233947631_0.29496683664379764"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-16T23:49:10.581Z"}, "0.16.9": {"name": "@esbuild/linux-ia32", "version": "0.16.9", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-zb12ixDIKNwFpIqR00J88FFitVwOEwO78EiUi8wi8FXlmSc3GtUuKV/BSO+730Kglt0B47+ZrJN1BhhOxZaVrw==", "shasum": "bf0fda9f046e6c8332d7c8350b8a94d63acb4ceb", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.9.tgz", "fileCount": 3, "unpackedSize": 8258007, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB497oQzheZcc6F0sv18k/INa73/fLXIuGSsF3awLM5UAiEAif3LapLaDFYNo7FJPA6TU/gmXNwzl47J+qN8x6YH/5s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpeyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLShAAkelFUtmeO3W/Xmq+v21NlAVqsJQJwmRCDsjHfyk+bUCx4eLG\r\n7b3GHQQV5+wvCwQTL1k9cUeXtx96bL2h9bHVVsPF3Jwc6TP+xn/7r314FdNS\r\nFPhBnLPfLYuFYEO5Q+JB58sGEv3jHHEqp2O35mWZX4WdrI7Lz4w3ksxUQ7Mr\r\n1FF4ROhIex/0+OcQchOtY4WV5rvEn6uK/97up/DUgEcQ4igMCLjGE6qQILFf\r\ns6e5geh5h5QHcPFlc9Pp/VeCS5GaNTzO7fWw5JKt113I07gZ9mP9PcSldWgF\r\n0A59M9Ov/v2TvKbPkxGmKYPVXvwxpiE83mH0KuRDzoUtQMBkbS/i2QV8ALNm\r\naxgGaSYzDSMubu9V8iUFX93utJeE5lh3JJLcJyI1qVzHtItQQhvMaEL5fBZb\r\ngBQg8R3ooSFsr3gdQ3MkjfjnS6+BXInfe54l6OZ21CspSKHR65UygwWzqxWC\r\nrM5XGc8kPzOcyb4QoSOE2gJu4v2IBVh3gceGfQxQxjmDxBpxTtO+VUg+hgHA\r\nnmnO2qd5njhfy9CbCCSGOdJU5OQUgnfIt/c7BE0R6VeD4LRK8/R0wLncihCz\r\nmA1Lri1Jm8rELX3V4wkvZs07TYrB55msU2JT00UcpZEYap9igCdjHLXKaHs0\r\na+tDlM07U2IklEgA6fLLiPilM68eoCKSwGk=\r\n=cYVn\r\n-----END PGP SIGNATURE-----\r\n", "size": 3424889}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.9_1671337906368_0.8426895705090216"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-18T04:31:53.716Z"}, "0.16.10": {"name": "@esbuild/linux-ia32", "version": "0.16.10", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-sqMI<PERSON><PERSON>eyrLGU7J5RB5fTkLRIFwsgsQ7ieWXlDLEmC2HblPYGb3AucD7inw2OrKFpRPKsec1l+lssiM3+NV5aOw==", "shasum": "1e023478e42f3a01cad48f4af50120d4b639af03", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.10.tgz", "fileCount": 3, "unpackedSize": 8262104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDwmdV+05YGIaORx23KWgQy5zmxU5cuYTzAcZvzz9oFSAiEA0gGndvHvkx7aLB+Fc0a33/S4N5G+AS1m1wJkLd4JS3s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPM+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqR1g/+I1qF8d3GMD9dhnMV+fkm6yAz9CNLAl0T3Exn/U63519dGu/P\r\nYTCnge9doxxsF+HQz+U44OgQSGPT7q6j5jYAA9Sml0KE9ZaHyGFZ5AnSW/kB\r\n2jK6nKyQZKv3VYo8m/zoQostyAZ0U5qTjFg45akL37dEp+ze22ZaHP3fj66c\r\nsD+cybHJhWG82GCBhbNdfdBAUOMLBZeP8hXzKfRE1C+KcztreMgY+J94WYxV\r\nk7l/T4DYeVau08MvxeBgrERXxg183JdZtXMJHVAH+8DSi/kqnrbIqkI1X1SG\r\nUySpJR/H0a1RpeAlovB3AUQ3zqV7lnxadu/ey4ndiC0CEQ5uwH0PAk8qvf+H\r\nwj0FZyZZicVQmaY2N4nRuTOP2HLRlD5MfHDozHfmYCl1O+9cPgEiqKmQpJOy\r\nJJK+BHr6poe6Mu0rtjm0diwhcCNILu+9fAQOYq+dVpHtCQfjhDgm0lexLLkq\r\ni3td7Tg3+Lq/L8XZlMasdAg0t8Bd8hZj41wnNY7jKWQ3moccEO0r/MkNZ4bO\r\n9BLyiD+A7e1D1X9uVFCrT2Tu4zATVB/wwBlcFtBmceZ6/sCm6CIQPu0qYIHE\r\nmQmE66fJ23FoQvMWgPKaC2M2IuOA6lpVFHw/evlm5WltNJg+15bc2Dj3rVXQ\r\n9wjt4XsQSFuTRp5xfibiVggZ2N6rl/f8drg=\r\n=vFXG\r\n-----END PGP SIGNATURE-----\r\n", "size": 3428884}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.10_1671492414473_0.9687133163741242"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-19T23:27:02.442Z"}, "0.16.11": {"name": "@esbuild/linux-ia32", "version": "0.16.11", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-pr1/tdDfgQQ9hp2IskSKMuwkx2X4jR7iHLbqEmmj/lPLKeoa6AUulnglGY4y0OPo+0eAYd6DzWp7ve3KI4lOCA==", "shasum": "a4b14b90628ddbba1d76fb6ce5a9a673fe0408ea", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.11.tgz", "fileCount": 3, "unpackedSize": 8262104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWIFVaawpX89Mppndabc3OOlzqBABZbo2pqFukN0dKFgIhALvH/mMxzU8x7oDF5rNC5tFPdzKP5PeeQ6XWxlk9J5Z2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOgQ//aY5CJ/c4jQnHBJb3Fv+Fsh4TS6XOze2qnHVa3gC69n3EORQT\r\nE/n17jWl+1MuHTQiPszUfRbb5DFnQ39aQk0+HkAUTdXnZPdUU+YCagu9IY3a\r\nM6wydL6enDCJYVFk4bl9BZt+y8IS2L9EFWQOKC4EOugrw72EIbauh7a3b4go\r\nbSFCwfjK9+QG9KClNnqFIG/I7+7aQL3fdL41++M4b5ITjF+NRd3pYmWag6fX\r\n1ENAEvU02YuUmRfrHaO0ZlSWMmzXKeMmv1KcSvlEXv0OdAcEzdGurHYIS5ky\r\ntBPsXb+YC/eGPvP55FIayjMjx3qlVKRu+Ov9AlxMWYUeZEgIi0XGIsyTQ6rA\r\nLDhAHalXSzO2m8BV0Mj7Nte82w16Ku99xqHrXGQtKURYXALDgYD+q4sHFDim\r\nRZfymjiP7FjkD0MstFeVEsB5sfIZ7J8At+fcjSVuNE8L8C550WH7j6Nq7+vZ\r\nDGaHBtV2Mqxk+m8es+ls+Iih4YuO/Eo+1jp7ZsVdpRY2z1Wmj61+n8itKpbu\r\nHVyiQioqEtrDSirxGMNPoYbizsV+nIuvGt43mEX2/9SiTsOB6EnaAVH/CPJZ\r\nDvWLC+GWl2qake0o/xQ3oI3hqJ/3pwPcx39EhROP6BD+GAirinTjICqWC8Xb\r\nJMebJ8vwv0XH3O/KlsjYkvatvOFzcwpoi8k=\r\n=B8Qp\r\n-----END PGP SIGNATURE-----\r\n", "size": 3429281}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.11_1672105162969_0.07646243742355519"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-27T01:39:30.852Z"}, "0.16.12": {"name": "@esbuild/linux-ia32", "version": "0.16.12", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.12", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-JFDuNDTTfgD1LJg7wHA42o2uAO/9VzHYK0leAVnCQE/FdMB599YMH73ux+nS0xGr79pv/BK+hrmdRin3iLgQjg==", "shasum": "132e61b2124eee6033bf7f0d5b312c02524d39db", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.12.tgz", "fileCount": 3, "unpackedSize": 8262104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXnt7MIDFCXialT0Xm3Hz0Jic8xRX1xWjxZQPpWxw8+AiAbKcmJvTAcFZTn94YeqrCtc6XLs6rqdj2JGGS/oHu36g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6UTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY0Q//aS74SJng9utPN5gZ4EwwJcHJ8ccLXrFCuUtpk+MSc2vmi5vx\r\nDczSKYav7o5MTnOtZXB8FjKseOEEYOkAcdgB3oDP7OPkTbhBqf1jfAKm5OwZ\r\nT5Cp0nDo/EoBCVqREUcFhuCXeTcv2fcRaTxSIc2U53UIA/HvGYCbap7Gcwb9\r\nG0Rxy6howvZfmkNMr8Bzshwm/VztrA7FaM+aqm6R7759P1Lt3V/oNutwHUIh\r\nElR9xuziHmBWXevRbmHKbwhwEoyQx4I/8HXvTjj8C06tKFao5ecY7euK08BS\r\noXr9bofjgsIAJyKQzN3by550OiGMJnzKij5VNG+Rk8aTxwUcSarppGIknBlH\r\nCK7UGgAurAxJa+XyAAYNqIG3v1+mm5SffBFcRdvFnCNI7NL6qVhlOGVx8o7j\r\nlY4Fed/kSh0p/y0iptwx5c1dD9JOGry8botCOnkqGFEX4zKOL6nC10U9068r\r\nTIOdwOrSs0QFwAJVYCk9gK77SZLt2EhFRrBJTWhiyB6wnjXkr+84RLc4ZxC+\r\nl4zNDh3luFPt4AD/tZxs5NvnmbNLMs8BoLC3ZHyS/ysKLJkGseXnzM6k7K28\r\ny/MbYgaDGV1kXbpaeeHeCLVqoo9DEtYdzyIRG8AXi7upOEdN9viXnTquh2ex\r\nRMdG2T/zNxUyG8eBOxjBvizugke/UqEkQ2w=\r\n=lxJq\r\n-----END PGP SIGNATURE-----\r\n", "size": 3429533}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.12_1672193299367_0.8380134824004322"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-28T02:08:27.464Z"}, "0.16.13": {"name": "@esbuild/linux-ia32", "version": "0.16.13", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.13", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-2489Xad9sr+6GD7nB913fUqpCsSwVwgskkQTq4Or2mZntSPYPebyJm8l1YruHo7oqYMTGV6RiwGE4gRo3H+EPQ==", "shasum": "41ee9bd3b7161ab681fab6cb3990a3f5c08a9940", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.13.tgz", "fileCount": 3, "unpackedSize": 8262104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCM0AtAJIdoTgzR9CWSID8eziJQqEol/QL9PPTrRS5GMQIhALZHseuPVD2umjdGLxNjyBfmHER4MJeXIQoQJh4R9UM0"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtqA/9FXoZyvQGVO8yvHxPZPywr4RQExghSuSZ3Wdn3ehg1S6YISfI\r\n2SNjyIxBjn9cFdsaFz5q93Hm93mibKPa9UjZe0mduT9AUpwo86wvQiP2vitP\r\nVTX/AbLyk00sw+vXypFyYuhOFZ0u3qMSH8QeddFCrzOxzp9krgu62kbSzTPJ\r\nwuDIaadoPeiHVpEOgH4clUTfOBviar0V3XeWb1kTbn5oxkdhFqbe0pZo9qKh\r\ntnAyexgf0pHLzmLoqjCpV59NdDac+LA6yWbz/sCw0rxgvk4RBLUQBNBqUl1t\r\nTRpgnBMN7T1zo4UXJ0iZZQhD6kJqjQdDFi07J12FgGddNIQhJ7N8zRy9Z32t\r\n47qU32JdQ05NnxCV+LOOMAAkRp0Ko1YqUkHX4neWXng10SF1FetSZhqgnMCk\r\na2QYig+01TTkReIl1254YvkaT679NYDL7Z8HUHDqUDSlFnzB17xzu8OsaC1U\r\nql5e14FWghLu8/8wEU5c5cIiSUpBnX4x0p4g32szAI51T7224GzzboKSvw1v\r\nI+McnSBZWeQ+c7F6CnA6maNyEFLpeB3yPNc3d0gqkN+3FpYM1FhlaVNirDFz\r\nlX69g4UhRzybGwOJaWLQalKiRNVnYexKsPyY90z5z+4AlgHUajPo9QoWJlFB\r\nuDj8VPKUJtfiUPFoqe7UHBpsdIBxeJNFzmw=\r\n=8355\r\n-----END PGP SIGNATURE-----\r\n", "size": 3430083}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.13_1672700257445_0.296904593266617"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-02T22:57:45.941Z"}, "0.16.14": {"name": "@esbuild/linux-ia32", "version": "0.16.14", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.14", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-oBZkcZ56UZDFCAfE3Fd/Jgy10EoS7Td77NzNGenM+HSY8BkdQAcI9VF9qgwdOLZ+tuftWD7UqZ26SAhtvA3XhA==", "shasum": "f5f7886027cd61bed59178e981a0ef47ca5b72ef", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.14.tgz", "fileCount": 3, "unpackedSize": 8294872, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGy7Y3IZAKRpyg0TXpTqidN49tyuMTCPSPSQ5rpUj+SsAiEAkT401TA77cQZZ50RYv/PL/o8LRPPa02CI9JYbdFoP7U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9zQ//V+03E76UP3NdeRnUhjJULTs1OmOX+c0yAaQgy386vT0GO599\r\nLtwlA2d/H3bBd89jeXqi+fTKSxQdhfOEKo+QNh6+d7ClSibtmPpPXvoVZ5rA\r\niwsM0wi6/a0b2qIlm9yYLn/iRP83+GD3Is/yiZfqLYu4uIIJRxXsCJsKaQPU\r\nEj+DpW7cc5CTVsrIIfwq/rCXMtDV7xb46a3Ni3QIyIXko0+6QGzLwuwb0hdj\r\ndOTDwlJ8axO2VtKCgHmYwEXSR0hG5NqYyMn4B3OXbIk2H2KI+sZTraIfu6mg\r\nQ+gV5aDM7y0FNemXVPLC9TVAobTZ49IyxE/Yxmjg73B0ND9/S5ANnIGP0Kr9\r\neWbEq6ALkMpvy5Rd4tnaZxwvNFibqBAhBw0q6kI9vqNvidyEhfKRwVW8v74p\r\nvSmu+D5cNGDNQqvDl+2dulksL5o6XiD/8LCOh8k3Ij/S/kPK9VkA+bn2E41U\r\nmgtJU+14Z1XcBl+aLkgA3qBKWkECRkm7NSM2uwrV9TKdjx1FxKCcTMKBsMM1\r\niVyJ/zLwiqQiBQ4zMsEnkcmtzAnvjsSmiKgFutjLreXe4d7ecxpxiGJIHx6i\r\n2zgxslCRV+ePii5QmYMnypqywrx2qgSxO/OIj2IAfPdOhmZGyGqjdxIpkUW9\r\nPM91eCbaIPsx8Nr8+KRxXNlMqMl8+PgOmOc=\r\n=3C90\r\n-----END PGP SIGNATURE-----\r\n", "size": 3439804}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.14_1672863201603_0.21661192598694834"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-04T20:35:48.873Z"}, "0.16.15": {"name": "@esbuild/linux-ia32", "version": "0.16.15", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.15", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-puXVFvY4m8EB6/fzu3LdgjiNnEZ3gZMSR7NmKoQe51l3hyQalvTjab3Dt7aX4qGf+8Pj7dsCOBNzNzkSlr/4Aw==", "shasum": "2c63615bb87cb2d080f3dc7dd0e3174b9b977233", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.15.tgz", "fileCount": 3, "unpackedSize": 8294872, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIwGbxS6KuNCLrIn9n4s3fDksxpvhhbU5RtvLmKSV9xAiEAkkYXkC1VjAp7giNgqcDzGdraNNFv6h//KCJepwQxJz8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPLJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSHQ/5AKZDpnFW2v0eyS8COL63Bkos+gF7kzEw8uzkHJgJt1T9fRBT\r\nYL2bC+AB88lb8NbcMMrnY310RbNzR43nHubp+IznaRnnVD36C3bAkAuFIjSq\r\nA0RLSI4xwdj9G0taNQIUxLfLkmX13f1OnrtJDBEDgK9Nm6QlfNSHmlu1a7IQ\r\nkiN4MFR0TmE6mtnsEU/b/6669ANejg+qNVPa0NCvCLvh38JoDletMSCArjQP\r\n3oXrBAMWXhR5yBZkmCL4haGH2gLtdPsyeZopysWCJthDKT6qS5Tw9mzsB6jW\r\nMGCDyJG2jk58RY6CvK114SAlArdEmYuIYqcUPGe2WnLElo3lo2+rA/uqZqL7\r\nwzSn4neE3mu3gmjoX7EdDS6As8yfs9NsX1fxYFXoV/ILHaUQdaiKhHaKAQI2\r\nIeiRbVKr//go3J2Hqv2YY9aNvrat4ZOygVZ1bvS+A3cH6H1JmEWpcW1/W2Tj\r\nvH4SMX2AF8WcuHNdxkvF5GAyLTkrDFomHuJdoWHXwqiZkNgJn00tB4ucuhgX\r\nbLiNhFifnsEffwHuNJSMzWnrzf7kAgLideziG0wUfZ9etZNw7vqxWtp9KszE\r\n8KvX+RwQeCSf5+YVmD+QciXbnK5OaMI3idPB6R4z0oMpW9fYLixLbRARVQ2Y\r\nCLt8GWoFDU8cG/m5wXpUSM/zPYspymkbmcA=\r\n=JGsX\r\n-----END PGP SIGNATURE-----\r\n", "size": 3439965}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.15_1673065161038_0.3417071354521777"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-07T04:19:27.858Z"}, "0.16.16": {"name": "@esbuild/linux-ia32", "version": "0.16.16", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.16", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-dxjqLKUW8GqGemoRT9v8IgHk+T4tRm1rn1gUcArsp26W9EkK/27VSjBVUXhEG5NInHZ92JaQ3SSMdTwv/r9a2A==", "shasum": "9a0b0e926926f891a3e7f7c50bb38e3db49c2c9a", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.16.tgz", "fileCount": 3, "unpackedSize": 8294872, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAk72uFMYQg8jrEZvnb0y2svh3FBmJLLh52VFspLW6U4AiEA4RVglVhbDdUT0avzEoBFhZS7Ar8WMOPwvB/nVzuK110="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0c5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHOhAAgNl/+MpkGRDlZpXOLCJfN6VKEBeXNA0lBUUkZlVvlGQK9qHG\r\nKtWaanSXKNoUpwiH9y1+CleKD/91o4J5Q8JJDm6hfcrjg9xOTC4B/PiukXWw\r\n+z5+mUjNOp66HRZLHe6N3HQ5thW2g4qAY2Tq1uiIYHg+xAvSpGdKF3fGmffs\r\ncp04mGDaO7lfV5E9hOfEBPYFv8WSpr6sn5JgK8W0mYu3EIF4mR0mwSdHeGBf\r\nohCuAof4zVy65jbIrpAWtfKzh5DS8na54m4YHqpL3thoPsm9vnley0NVmyJQ\r\nvHuKAz5IwIhlh9kDSaMd2Euctbvs9usqgUoVjubBJPqHMf6RZSHP2Y9Wplmd\r\nQ62zzA17T+pOI+eY33b7NHZhWJfSv8niIwlv9KquOkvUIqNn4MkOhlUcjt0e\r\nPWKaSFHpKJJmrRX5S8iiYPsj4BB0gJsbOvkfpDVxYwd99EoBJdw25yKVNr9Z\r\n4VvR23PS/UCCsXLPAj3UA6BvTApRx2Dk3SbZ27TOz20zdcNakgNGU0hRzG5i\r\nDGtDncu9RIP/62T7iUkEiG3+67aInm1hweng2PRf+6XHopH8cVqwuBN04CoK\r\nlIQ2RjwesiXmMW7lowb9Xf5dFp5p0qFNflkm23omKJAbZ3KmRVQeKyt589mJ\r\n1G3t1cnZBPx1g07FHZyff3iK8KwRD9vBxsQ=\r\n=4tPD\r\n-----END PGP SIGNATURE-----\r\n", "size": 3440286}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.16_1673217848970_0.8060439269622091"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-08T22:44:09.190Z", "publish_time": 1673217849190}, "0.16.17": {"name": "@esbuild/linux-ia32", "version": "0.16.17", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.16.17", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-kiX69+wcPAdgl3Lonh1VI7MBr16nktEvOfViszBSxygRQqSpzv7BffMKRPMFwzeJGPxcio0pdD3kYQGpqQ2SSg==", "shasum": "24333a11027ef46a18f57019450a5188918e2a54", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.16.17.tgz", "fileCount": 3, "unpackedSize": 8311256, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrCjXeMHQciSmxMRwjoKf1jdM0PVxrPP33AdNGLIzxXgIgP7Ry1hustYP819y3ED+ELmZRaeCGwbhtW+Hcjuvx1BI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzD6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCrRAAofX6pwnYtloaxqDcFT8oeLk1hRcp26sa5ngGcLtQ/mSkG4yF\r\nVmbwz6XnN3qSBEHc5+D/mRjG0dlq8x7iAmDfth45H7ugDSSzPURBgOKRxmVV\r\nvlhYHcA1ZkAhk8zF31N/Qzu3K175tvVwp71rTxiiTELy1WmZ+5qpi5KQhlxx\r\nAttTyjYJN4bodKO13Ftv1YHVIq1cCpXMaDgoYIeaLwDNsPvwxGosPrDk7+O/\r\nOZKHWkSmGFoqdRl+O+GSLT81MI8vXOjfWdbFt2JU+kudJ9VAJy4Pi9jCvq+5\r\nIvukU4IVDEzwSaHX9ANbvG+TyISzJGmGJkSUDl/PkfqvtX2HUnJV2B3O/sid\r\nZjNxzTYTO3kbC13q5mGiES/JGULeeyeVEJ61e9qhQ0ssu7HiZB6Rprqh45d9\r\nK/I7yYyXsDmhbO0HXFBhGzSIMinC+vcWm8+4v4/QHgpdhLgvg3K00gu0OVUT\r\n2eL914C5PIdn1cdEnniH2dlM1RXrqi/zYMiqygJReXpmQXhmhpL9vH93BZJM\r\nxvLdHnJx+IZNWE1C58Ptsu2V7YdmBvbcJS5Qog7cM8wNtbFThh6MbekCF3YC\r\nfOylB1iopeMdFw7ugk0mczs0MJKH09gyrDsLskimqX1bboFwmmQHvjv2TImE\r\nixu7d5mTB1/W8+stKw+nHhjtFJnL6tq+MIY=\r\n=9XhB\r\n-----END PGP SIGNATURE-----\r\n", "size": 3445354}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.16.17_1673474298209_0.0692682170647938"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-11T21:58:18.497Z", "publish_time": 1673474298497}, "0.17.0": {"name": "@esbuild/linux-ia32", "version": "0.17.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-7tl/jSPkF59R3zeFDB2/09zLGhcM7DM+tCoOqjJbQjuL6qbMWomGT2RglCqRFpCSdzBx0hukmPPgUAMlmdj0sQ==", "shasum": "47baca8e733405a81952bcc475da1b8e5682915f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.0.tgz", "fileCount": 3, "unpackedSize": 8565207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCy2x27GlMynP9Iat2hCAGNaeGyO/FgLc0vJ4hD/CE6nAIhAJ04vCQj+sygjdRb4iGhxyGSfSxPe8lq4Fue3sqQMvQ5"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjF1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeDw/6AjsNAWDZY8dtJaYhshgDKLH7SL1+51MwajzpAvtdD9x61uNL\r\nzuqNchbblWQmw0CE9w/y44L5Z1+voih2CRn2SMKlXMp1PoXLxJ/CacXbyIgt\r\nFZt6sGvbHNsaBrx1Kgr8y/FE0p6hMsKtcRM87T0elCP+vrCS8cuCQyuqDmNZ\r\n/09SZKp0lhGsBVXS6TLJdnW1MK5aext0eNgIaFm+64Quu6bPyfTdD/VwPDSg\r\n5KEyN7bdBmHUc8Vi/fTrb/Bj71FS/BW180lkMZlDHH+geSZpRICOsVYthk2e\r\nN/aP6CuRdosuqdDd6//HTgLNhnDLrNUhoGouZwG0ZNzb3olGHFKEVcn4G/9m\r\nu+Nvg9K08mviTpLLIO49XrSQooikM8OSEBIBhY2gUjKqB4oYbOpa2CStVes7\r\nR5+2Q9RMntZpU0X2J6eec9isF8pVXJwjXuRiro/VcIKAxXV6V5/kBu/Hy8oa\r\nn8H13w2WNjpZXZVfcKT8fZ/MGr7J64XNlOft2FMoiazVOqhFpgwQ3+uPEdAX\r\nQKH+LgqHq3DxXf6MDMVzAElP+eebrJn8bxldMZoV9cF6huPBYPvCcBLo3eMU\r\nXgV2aq+3h7o6xQ7Ijjy1YoDWfXOKh7IJsrTxlB8VKzrkKzbcWTVtlwUMYuCr\r\nD+PZLpdVck4s8ovD8oOqNMYc0XP2u0/QDu8=\r\n=+4D2\r\n-----END PGP SIGNATURE-----\r\n", "size": 3553496}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.0_1673671028886_0.6971154425626387"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-14T04:37:09.163Z", "publish_time": 1673671029163}, "0.17.1": {"name": "@esbuild/linux-ia32", "version": "0.17.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-6HLCE3SA+qv1RWhBnj0AKUfZh2bH/D5wyvq2JaqgtK6WES6S/3B1u9g1b5lO5UT56Z74LAf2z2sqVdUwmfHbjQ==", "shasum": "765ab53b1ba4fda356437690cff20dffaa5b2c07", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.1.tgz", "fileCount": 3, "unpackedSize": 8573399, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZl7fHBafE3VmKUNy89QKMfyn6wKA3yyuNH++c8lsBBQIgHn78LkGUn2zt9/QIeagnX8HvLAjSXYTge5P2HREGTK0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZIIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWGRAAm/N5jRwttyjTEQUibV/CeIxbfmS7/X+C4lJoouNq/6Uf/9fW\r\n9K+2M/VBpWzATTXBuXwz0L+sFTKzNrCZUkw95bLOGr0jevuQkw8rNB15XjzO\r\n3HCD9vuMoZNs1okMcgHwNxZDr6twkIUQ97MmPSDKxP+YKend06qV+hLH7vIG\r\nFjVL0TPqaz795P+v0u6H/+mgZHMOmWv2jrEPqaM2tOyE/DGyjb4B+mhuRd4T\r\niAN+kc1L64tyYQmtKShndoIDib2R+tn7tsXRXaQzIxJH8XnYf+WNHoE8S+x5\r\nOE6KKiaWrnaZlchL6C30kkPwIQ38iGy8ugYb4yFIHIoww3s6d9VjgW8Rovul\r\nCBOTYdi4kMszmxwdExT0V/iVQXI080qAXGG3U7dENOJhgFxW5mnHlolYeF7n\r\noFlXSuo1r40BRLTJcsOU/cusuQwXaK25FRxEORtTzSA04tRquQz/kC01NzPD\r\nTARFbAJAKx4qxXHRVw97Bw2PukdEOqGg+JR1YTj/voNpKSn9hGiAN8jrlNeP\r\nAory7PSC+f4ZreQT+lyVQpl4ogEJehckWnzV6tvvXFiwxx8/97WMqFX06chd\r\n3DnXgCPA0DB5SAoYKjq0++He4V52wkx62qgiROBUbI5RGn36YeAtIYQ85c5b\r\ncCK9eHIWtIky7FJiJI2UpbpBuClg6ct6WuU=\r\n=AJis\r\n-----END PGP SIGNATURE-----\r\n", "size": 3556229}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.1_1673892359846_0.06685486075225389"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-16T18:06:00.110Z", "publish_time": 1673892360110}, "0.17.2": {"name": "@esbuild/linux-ia32", "version": "0.17.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-dXZ3m++zaRVD2fqOUPP8QTh1Lfg6WO6uZDo/QJ3KdfnIR7dDToDtaA12AgKYvCed9Nuzf/gpKs/7/f6I02b/sg==", "shasum": "21e123e2557236c847b38c2ea4dac3d8fbd1081c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.2.tgz", "fileCount": 3, "unpackedSize": 8573399, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmI+ftrs5nOjQpj/0mFFEhB6GUdyo8dKfM9iu59wRCkAiEAryg90gLP6gI9aPLJHrMuEqXl3yJkGyaavledIqhljBk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkLAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonCxAApMMZoiEJEIpW8bVAkZcMQ87SmLXtoOmbVCsWMFVHACjUDL3F\r\n5Mp0WN7q6kVsPEEJvWR91FI1Gk9Isrl4lFpmyNz6A0sHs9V41hTE2aaD2Gf6\r\n4XYwrF2OGj4lwdgLGqMHPs9VI91Ztl92qBVYoIZKGDpCEIe+X04NZdxZ0Qvi\r\nLCpEcNtSmq33ri6WNbMQYEHELW+1K+3XvFiAfbxnXxRnLz2AGWL4dz+GTi9g\r\nP95mCHXF+nOPJrCOjoZV6Ti8p5JCTNjafuRqyJ0ZfvSj0QT7rRRIwzpxCfql\r\nrkYImV/LirIkeFdMdgvY9Hp/wCY5f5LbAlUTJwPVBDswv01+1yP3vOzXtnkf\r\nd4hE8B7/NQRQ+l1WL0gyPwqrJGVsk0z6PtMLSc9SrMirWOpOq1X+/5hIYrTV\r\nj6WhxdZQsLBk3PahVJjSpiI/Z7OR8VwFNB+japNOeiKbtxJL0/VDOWN3S1iJ\r\nDSPlceuak8FJ6xURqb37srBaEeLycD2upaWziagZzQQW7TwzCDB8PlgmmnlX\r\nOdF22fErCriypG33aYC1COvjxCNuT+FiKLSDXdmqjWJAjh2zyui2TSVjhX91\r\n7/NPQgeSKzIPBjw9AZOvYNicM1Ng9MI2oPscQuJX1kdOHXDF0yPLdOWwmtea\r\nLfXRR0mtXEKV4lU+Pu8NVznkX/wzvHU+WXY=\r\n=R6kZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3557281}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.2_1673937599904_0.12717825268378036"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T06:40:00.193Z", "publish_time": 1673937600193}, "0.17.3": {"name": "@esbuild/linux-ia32", "version": "0.17.3", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-FyXlD2ZjZqTFh0sOQxFDiWG1uQUEOLbEh9gKN/7pFxck5Vw0qjWSDqbn6C10GAa1rXJpwsntHcmLqydY9ST9ZA==", "shasum": "6691f02555d45b698195c81c9070ab4e521ef005", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.3.tgz", "fileCount": 3, "unpackedSize": 8577495, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxY+iBXQ1vhMXDDekJwZO3NbCSFkVNsA37haUyLS88xwIgLiPaYZdfd1HYojM8k6WWYvxS6VOuSK1bKEgATdGWSUE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6ZQ/+JPU/W6QqF9devhKq9XU+QSKA5UWmxzb0VF7KRUz7/Jai9tZG\r\nQsLZRKBVFp96mf0nqXo2DgLeMg8Zc7tK3zAb8U7042rJ+1Si7B6Lu71kySuC\r\nzshUlzxeNjkjiR09ysBxrr0A7r0xI0rXJMB8g0w4FPTRCGYaE2xuzBr9owrL\r\n2NXnBjBiAkMcJZwlcV0/4GJV4zmN99B5RBqwC4ixyzQC+vDF7LpjgpNdaDDD\r\nwStPSocAPdDi+WYDn7DpfCeqvWLJuFFpP+6OxDYkQrcCnZ4sTUTKaREOexv+\r\nNfVlA6w5FG7HT6ZiHlUpga4DkYwkbZL1sl5hFHGkoV1AV0prCQc8T/4aEYBr\r\njxHhcKaW3Son/VgVsiS/8NhWvT4CbXmiNd3RdC0CaudE6Kn3aye94QwCTpRH\r\n+W2xusdTs4CZ1vfYShIEIprEIA+Xf/nMcPb2X12a+KCOCAgZ3wM8UF2/ezLS\r\nzS2K0MaQJ+sNWchIxkH6v0JF6CJopA+2CW8XL6CNfzM0J+4LzR+Lp6iKDNhG\r\nI/JcFgI7ewQLNVFr+gwTNm5hvr0zzWene6ZgkSypeZH8fauPDywUubC2+RlD\r\nlQMzx+cJamJlhWtwrXQ7kzkxg1BTe8ZtWSozQSYjT5BPDn2xedr0DYftD4bp\r\nVoMeDf06e0eVkSryVzhqAExWCcPtCwwl0rQ=\r\n=v4bf\r\n-----END PGP SIGNATURE-----\r\n", "size": 3557360}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.3_1674069288251_0.09524094098647784"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-18T19:14:48.519Z", "publish_time": 1674069288519}, "0.17.4": {"name": "@esbuild/linux-ia32", "version": "0.17.4", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-3lqFi4VFo/Vwvn77FZXeLd0ctolIJH/uXkH3yNgEk89Eh6D3XXAC9/iTPEzeEpsNE5IqGIsFa5Z0iPeOh25IyA==", "shasum": "3fc352bb54e0959fda273cd2253b1c72ca41b8c2", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.4.tgz", "fileCount": 3, "unpackedSize": 8585687, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOI2gAQBJHemxJ+acQPgFoeF1Wca7Lc17FIzK8nKIjVQIgeIGNWifvgjt8QoQPDiHuAw1Ydv8EbHgP8PdZwvDY99k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZzQ//RdnronBTraYx5vOYNTlErtZIdtJJECHcWFKgaKAQqtCmB/O/\r\n6OpuVrvxAHwtKCL6Z+iH2d2lqxbzPmBfdmpurmI9AKnMPbryhTOrvKwq69lf\r\nrc2boG/D9S8MCejnCQHucLF0OmTWY3kOh9iH279wazMw/Is/uxSZZII+3mAF\r\n1pPstgs8aNEEjBfAYqEcXDW1q57+7yv/DXa+tOWUwQcwYfBc/HnWX67UmgdD\r\nFVHVpQ9ZY34qnOF6IEkcWlb6JW8c0QQXhtf7HS4+nnEf8ALQKIh2nGHhxQNb\r\ny9PN9KwjRlPIFYIE9AJnO6xixXzWVgWzSPiEueWHdTCgAe+GH8JU8MI1nS1K\r\nBHLT/hAiJuxLezeIkV+AuVfbsXjQksLzhGyWsbtQR/0Uqu65ghkBGIiBsGlV\r\nc1bW+9PkQQuFQUNqIioiPzJnvOR1OA4PX9UnoYjiC9uaiaZYzMBNgJGf1+p/\r\nTvf7EqVYMFtiKP4cwPpdC5YsEV0Um06G1EbwR314k45pgFjZTSbYPJ4/bSPo\r\nlOyo9XNeudzwL+/YAD9s/YWp0UBU4kb6V1kDdPFNbFwSgBAUQNo9AV8Ul2mq\r\nvmKrwe0oKX0xwwriY7Q+bCbNl/SwZVhivZB8RjWyGnasK4/iB1lpDltMG0+G\r\nlUnSr6o8QflQIQ4eSRVrjT3cejk8TG67rco=\r\n=Jvht\r\n-----END PGP SIGNATURE-----\r\n", "size": 3560419}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.4_1674368032673_0.6834085040592373"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-22T06:13:52.943Z", "publish_time": 1674368032943}, "0.17.5": {"name": "@esbuild/linux-ia32", "version": "0.17.5", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-uCwm1r/+NdP7vndctgq3PoZrnmhmnecWAr114GWMRwg2QMFFX+kIWnp7IO220/JLgnXK/jP7VKAFBGmeOYBQYQ==", "shasum": "7bd9185c844e7dfce6a01dfdec584e115602a8c4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.5.tgz", "fileCount": 3, "unpackedSize": 8593879, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcY1BYJFmtwjXpuLjEMspbRDjNOMtfXby05ymESCkMRgIhAOntf+zBtRfiiS439AFpUJekRQM0V5dXT7JmFxVeE21H"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrpyg/+Ie+XQpp2a+yNmX01cAdUWKHJhZQ/tDlujvGAuGQ+C3ETRHgu\r\nZd+lz5j9+ISDL6bCHQhp3jFNBpBRnr9CfzrDWsZi34bC/KVzYpiWOrURH5Bf\r\n29p5Igi60w0xKWiTjc5XMtLgqMFOXmFNzMHX0YfF6RZk8xv/QEaoXce92dqt\r\nE0JvMuPCnw1OV+rO4YAf3FfzXCi1wpAOhFQ55l07hPmbw/MUbcgiRzrMa+Fo\r\nQQCaPyU9n9+iLnkmxypqY0CY5V73mxmh2K32h3vzp5c5fPdy05He+rzFx1Oi\r\nht8hftST2wlBQx1HjrlYXlw997Xf59FH9pVMDg8Z1t1iMqzcN65hcirktB/4\r\nN3ZZwua53xw1R1FsFoHv8g7tj7oTDX9bZePz1B91tnMjiayTB/rXi7xj7v5e\r\npHmqUX8PddVd03Tq9qUn4r1jHC2QSJYHRBxrucQ6/eLKoy0lTBYLDsB6Ma40\r\nFjVDKn7HG40kWM68BteXKe9SsR/n9qHUkVOEKSI9tMmpocv7iMx2YZj7mifq\r\nZZ4R8Wop5jlUWFARt1B5ig+pp+QHWsxhdt4Sbzu1fyjWVHZW5fHAXkbfqvpK\r\n8ipQtzJemchoFtgeKPrzbLunxvRV1/HX1HPXu+0UB4jcp8/xuvUuqM1Z6Bjx\r\nbs/T+qR/sV2YMNdHAWdMlmMqO/iMU2i/GME=\r\n=F7Dm\r\n-----END PGP SIGNATURE-----\r\n", "size": 3563469}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.5_1674837486798_0.3232115762436427"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-27T16:38:07.077Z", "publish_time": 1674837487077}, "0.17.6": {"name": "@esbuild/linux-ia32", "version": "0.17.6", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ujp8uoQCM9FRcbDfkqECoARsLnLfCUhKARTP56TFPog8ie9JG83D5GVKjQ6yVrEVdMie1djH86fm98eY3quQkQ==", "shasum": "a38a789d0ed157495a6b5b4469ec7868b59e5278", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.6.tgz", "fileCount": 3, "unpackedSize": 8561111, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvn11Cl9onaSJKuPY+TOw4G2hd4594aIjy1TekZek5VwIgVeF6kfHzQpPF1k7XdLVnDkjSeXGIYAkKO8NaE4PGYsE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmro0A/+NiPUyYn7xwkRemEIDaTaINAjtv81Di2nMfh1knPOUXWzvoh1\r\njTQKQaGHRQiSE+pKMqFjfsizKNg6dy7FYRoIugN0otlEO5KCJdoX295NzxxS\r\nWSeMQkHAEyU1YzSFKG4UCb1fhPHG7rDbn2miwqAUXX9AJ/+qcfrkSs9USDiE\r\nMi3OjvkoUo3kkrpBUrJZyPpLLo9CClBT9bzbo8ZxR0plTe/LzhkMEvqxty93\r\no1iMBw+O18M+mT2yIcpvx7A/wbqWRD/E4kMxBtP6usUy/Q1fb9GJ85UuNdzE\r\n03Sj53PFBdy+8b8dfSacgq5cWZX+Pt6whM8Ab/KPXwqVEvI5n7vxF8oOZcsb\r\nDymooD/xAHoKw/vyCIHyRACFd/1gBwsyj7hOGxR/NwUJ7XO0mYNHO5eIFJvV\r\n4ql7SC9Rlv+c/e5v67NuKdkuei3puwNrG1YVVA4XOWBhiv9+zf0HgfkXdQoF\r\nqOHrcY015IqhAu9eoWp7xPXb4+WBMtplBkFNC2xxO+ElX2rz5Hbc8+4N5+ds\r\nqRcln5b/0Tj4kepyPv1fGN2/rL2z5fIr+KIRIXFpRWkgbzcL11GNChtysHqA\r\n5+YuMjjlcBPjzvM1tZ2le+ByJt4w+j81/PTXFb3BnvzPSObm0/mNzsTgkNwL\r\nycznhv2BPfid87JmyCwT2EedzKOu9fewKI0=\r\n=4cBa\r\n-----END PGP SIGNATURE-----\r\n", "size": 3534805}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.6_1675702863259_0.25731931682216946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-06T17:01:03.555Z", "publish_time": 1675702863555}, "0.17.7": {"name": "@esbuild/linux-ia32", "version": "0.17.7", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ViYkfcfnbwOoTS7xE4DvYFv7QOlW8kPBuccc4erJ0jx2mXDPR7e0lYOH9JelotS9qe8uJ0s2i3UjUvjunEp53A==", "shasum": "7d36087db95b1faaee8df203c511775a4d322a2b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.7.tgz", "fileCount": 3, "unpackedSize": 8561111, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGX4fkoQ6BJOGgUelK3fNwVYXmRJa3SbxeMhIxai4bOJAiEAvjuZs1m2n/JVaZVzrVsBMcGZy2o3sybwszjN3PMEtIQ="}], "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptVw//RMZ9aY+3tiWJoKdiKzY8lrtZOCcCYyg8C8E8MrMyqcq9Spyb\r\ndLtMgknawZYW+ih0yXt/Io+SI0O8fiu7UZ/K2C2RML+D49eQ1KQoBJlX+JQJ\r\n5kxb+Xew7D9LMkT66INig7XS2kMX39k7tSZjnerfoNwOlDGTHayF9BZF10H5\r\nn+TWjhbNA67ABH0ezRMNsh3Qhfeh9GnN8MnIcOpHom6pekEgR1XdGoq25LQP\r\n8qU5YfKTnfjoD7AqW/31FPiwQ01gyMzKSBI9Z7qwjHVdCftuVeiJriyHm9Nv\r\nym2tuP5R+3yNNGY2OkL5Din6fyXmXdiAZtovsGwjim9I3swW+3+zg7w+w8TP\r\nbHbeAy/z3Gs+Sh2+x4jzmU3DnqvGujuJNTFUSTgEeDFwFqVIa1fopWPsi1nD\r\nRI6hbyvytSy7N1qDYaY3bfjdOBNw4p2Vthm3P3JTUwXhwpPPQLk2oGIifNe1\r\neL0ErOEuFEcGeweJB1Uf1bUF3ESrq27MnJ8swO2R02hsEvfisDvVVnVXYBKJ\r\nry+PmAlpbhPuoOz3T6JEroHYkfODB/YzcobXtGh05enqb3PWDpMsf75rNFW+\r\nCS2amU8j73VdUgfdiBO3nZgHgTFBP8zLh6ArPKQ65HtpV/8yPeyrEMWfuLSf\r\nZHmx4A0bJKuvRVWkBngqldA23CuQmPXgQn0=\r\n=bWlZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3535455}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.7_1675981619533_0.7020477660009532"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-09T22:26:59.845Z", "publish_time": 1675981619845}, "0.17.8": {"name": "@esbuild/linux-ia32", "version": "0.17.8", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-8svILYKhE5XetuFk/B6raFYIyIqydQi+GngEXJgdPdI7OMKUbSd7uzR02wSY4kb53xBrClLkhH4Xs8P61Q2BaA==", "shasum": "ee400af7b3bc69e8ca2e593ca35156ffb9abd54f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.8.tgz", "fileCount": 3, "unpackedSize": 8569303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTXQSxzmsakVgbOrNZZbbx5V86xGzojWrWXGxRoZptpQIhAJO1/A8LYjHMb1c6GKpOBV+wfbWbqrrft7hYHmmlJx6K"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dpMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2DA//UO8YGFGx7fed/EMiEYnZiiElhRybdOChEu0PAdynyIjLwobk\r\nMVUVh2MrsXEjo9wySiDRtTkHdSqd2gBhy0iyZgPicoKtFhsRGFdfpUXaOJJ1\r\nH42tuGqh2c8ApM691JRmMDxqFxpalt8vaZu6XFj+GRnKrXQnUIsvmLBsL9B6\r\nLzOQG7brunfmTdq0fzYU407KRBKwOrrDQrffODvVbzYe/Ym9lAOsGoBpMTgI\r\ni9m4ty8uXjKl5p2mx6ufQ8s8C4zXoMd2HThqQFz/FZbkgogbPeH8FKtasClk\r\nGvw7/EpYtx6VxFTI86DsU6de0TaCrnxD7Z98OWPa0u/LJMFh2xfY4g2X1TEf\r\nGQms7iFuCPmhGrzKZN4igkK/QNZ+ddfhR/fCVlu+40Mxi4AJY+BM+2d00RPm\r\nF50VZ9crHufz93GLizsGAjfCpKYDgYbW/DhsOldJjrt/1MJ8ZYxbOfoSN1TR\r\nFUnxLbmqxl7bMMOkhEYoql7TIz34iC2/BkVCweLZn4v0d/WN6jZIGmoi/nPM\r\npVDKsIxZN+cDX4PDeSPrTfA4hWtgsw84jF4fFXp4kk9oIpCG+7IsrAROnSdL\r\n9P/DesWAecJIMe3aEH/QWgWSQ6FUvH+3hAHpCkvQLuQXk7J0IzaWOqRCVwhc\r\n8lun4S5BGv44zxdZcyIR1qFDyL5WHUOYvbg=\r\n=Gsul\r\n-----END PGP SIGNATURE-----\r\n", "size": 3537594}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.8_1676270156228_0.8957742488186544"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-13T06:35:56.536Z", "publish_time": 1676270156536}, "0.17.9": {"name": "@esbuild/linux-ia32", "version": "0.17.9", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-fh3Eb+jMHDJUd08vEYL8swRT7zJo4lhrcG8NYuosHVeT49XQ0Bn9xLMtgtYXjCw5aB11aphAUwnzawvDqJCqTQ==", "shasum": "b6cf70ea799a16318fa492fcd996748f65c9b0c6", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.9.tgz", "fileCount": 3, "unpackedSize": 8569303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZ8N8z/7ooi4EKjyNNqkj/18gEIFn/wGqOgnXF+0oklQIhAPKWR9GKsMx5Exy4aNuAO0kfRY8xsOFqDdtQO//L0kt/"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mBCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQ/g/+NJPf1cOOYJjRgsdFWiEu9JcHSe4Y5bSkPmBOm6HJXMAIUMUq\r\noMS0yv0w+fCCrGEWAAR2VFFYK79WXmqbKnvWM6PoYcz+r/TJKQpzUsqL59Px\r\nNwXz840gwCDPGH0fjgJi9r/oTECVHVwYZzu1Z9AkeTH9byLLEvO1yCs4oxfL\r\nrv6IVpCHPNdCRI1VtAO+WXbxTFt5QA6BtPXSnd904jb+dWGA8rfHYrgqIqJQ\r\nlkjcdVELIfgq8ai1XJelfgyAnWYVWKt+Zd/3eTUlzGPC8F/XuQdX6XO18DCe\r\n5TSzPAMCmn7A+6EeuFlt8GQxYhBfm6ztLFFl+BB5jaOcCXDk1oC8gz4qq3Mq\r\nDt3WFAOtbK094xflmMArTi4gcTP41SqbmYbi97uQ0UzlOFQriMIoO701rEts\r\ny+bynv5uZr7o15Hb9Oi8iP0MRlAehVWj2Dx7o6tSYUgYUBRFqvv8Hgwo+12e\r\nxQihbuIJbqn0MtWoQyFp4hSZOt64WQUOEYZs7JAQu/RtZB7Vc+SrNx9n65q/\r\nM9iwfInWXnbKCPGtass0jNtBBBqoPA5V616mgGUIvKJbXufWmUO3iLaiJvxd\r\n+vZaa9dVoV9uDOQiPm6L2ZfJA31sswZ9Jr+O2waKq48QaIUphJYTE/5Ad6yS\r\nIPzYpIII7xUIWELMsrN9LLCrt2BMT7B4Sv4=\r\n=u5L2\r\n-----END PGP SIGNATURE-----\r\n", "size": 3538434}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.9_1676828738184_0.17984078815510296"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-19T17:45:38.442Z", "publish_time": 1676828738442}, "0.17.10": {"name": "@esbuild/linux-ia32", "version": "0.17.10", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-1vKYCjfv/bEwxngHERp7huYfJ4jJzldfxyfaF7hc3216xiDA62xbXJfRlradiMhGZbdNLj2WA1YwYFzs9IWNPw==", "shasum": "4d836f87b92807d9292379963c4888270d282405", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.10.tgz", "fileCount": 3, "unpackedSize": 8569304, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCn5UtWtDi/nrzYu3QsjZhpCm+DB7/Qo7WhC9Mf36ZqmwIhAOJfPjNAj16kDII4tmutU529kJ56GgjVcxXVQY2ghwLz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87QBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIgA//TwGLfXAGjVooYNYV+1tgVM4zbclBEXEpzpmln+ypEYn1ayDh\r\nK9a1AQ2VWJyLJwHxvwjVmWLC03QeCaIvZBJOoE7+/6T92MVNo9yrwWJdQlnB\r\nitebmsRjKJ6OqksjBU7aetY6EM1XNDzB5j1Ti0mJtQlaJ/A8OiQeQzrjZtlk\r\nHRR7cIC4S2IepBL9iMNXeXTUj6w8rfO9a4J2sIhFAfdhWP+Lmu2P2SW6ryKP\r\nP4V0h/PBX8NK2XSv8Zjyr7Gm1KIM2cJGjuSiF6WE8XzBklzj3Al29IVA1khh\r\npouqN47ewoG4XOVMMUVDwNCfOEy1abofGhBv80/QLMCLaJxgxcj8Mh3ecCUW\r\np06kPzrJIbTkxpf5p+P37ZRm1cU6opBQbzLnx6A2rYzzEQB5IuApuQl4OUKK\r\npQNTWgyFjCg2dLO1B946IAHsPxLjah0fnRpUk7mIpTl/VVnPwzJd/+32TWFb\r\nS/rTNpxnBK53nbEN0H0ikNJeivfa1iinsQ0ekBhEcJ+8Egp88Bjnd9n9s2Fr\r\ny1TYQNkVfKOIFi/YkcUpigT+ST3k7twNSpJ1yCi6aCiqYUS2b2ulPlrya8eh\r\nxLMfoXRaZ3rBpsFVmfDqeSbkKtn5Do8JU5Jn5e3tAfEnt79mQJiA054deRzV\r\nZOVowczOsRyhNx3UuKkDxsSdxT3Iv8wIA80=\r\n=/vk0\r\n-----END PGP SIGNATURE-----\r\n", "size": 3538099}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.10_1676915713399_0.35417813489248373"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-20T17:55:13.662Z", "publish_time": 1676915713662}, "0.17.11": {"name": "@esbuild/linux-ia32", "version": "0.17.11", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-dB1nGaVWtUlb/rRDHmuDQhfqazWE0LMro/AIbT2lWM3CDMHJNpLckH+gCddQyhhcLac2OYw69ikUMO34JLt3wA==", "shasum": "59fa1c49b271793d14eb5effc757e8c0d0cb2cab", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.11.tgz", "fileCount": 3, "unpackedSize": 8573400, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/1ex9dQ8pARz6tjF0bonNzz9dPdjoRUhHPXEBw5TsLQIgfqeRpwqWWh8Z4UCTceATKs3LLIydafi3kH5xs0TtfcI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmroxw/8CSFYG6zh0cNLSC28bEilQpcxIeFaeGir/B5+Izt/7SI9MQX4\r\nWvBFYwiu0SozHJqrxe2jjvhn+zb6HnyKha0xPk7Zcs+XhJLyV1B7dIzIFoqi\r\n2J14c9EjMwhw9EKTJvMjkO7d//3uyzk4e9q2kStbJFEALKjscsWpXQAyYBdT\r\nsTYstbEROKzukNkOc1JgYY/3mqttfzqdGPXAi+pDHVL1960bhACpGA8xtwYn\r\ngUF7GmEK7guC9dKHHHH3L4rrKSdkPzCViUyk/lQurRp4Of7rAC4Ohvok40BV\r\nV69Nrus45pqhzOLbn9fk9qUqUPY+dbJhJdfrh2MMaXPcKrf9s9MshIqxFMQ1\r\nQfqanWvdtTMAm3KKbqCVkyHBXJIDBg+C9pnlRousXN/lGvt6VegC14sTNzqX\r\natPlhHs4UeYV+kEI0DBi9xIxzwjXYi76bi1Xw6pgUu2oAMf0AmS+8Rf7Pgut\r\nr3ub5TI2eQuJK4A+WBoZIK//6q+zHVOlqfkCrlS55unWiBSbATPLqGrvV2KV\r\nUBOwKUI4y28KuJGUNr//PB0ZFs/706zz/eUTmGITJGLgv7dO5HYB9ZVWiX05\r\nwBQq1Dio+eYvnb6fCm/7gY96avDPWcf2u4Lc7sldjlGFCgudvjVLmSAQCirz\r\nyyUftqmBxWkUJxcIxy6ATZvNEuhtXPSTJzk=\r\n=uIFn\r\n-----END PGP SIGNATURE-----\r\n", "size": 3539981}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.11_1677883230939_0.6933371132787809"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-03T22:40:31.203Z", "publish_time": 1677883231203}, "0.17.12": {"name": "@esbuild/linux-ia32", "version": "0.17.12", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.12", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-jdOBXJqcgHlah/nYHnj3Hrnl9l63RjtQ4vn9+bohjQPI2QafASB5MtHAoEv0JQHVb/xYQTFOeuHnNYE1zF7tYw==", "shasum": "9e9357090254524d32e6708883a47328f3037858", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.12.tgz", "fileCount": 3, "unpackedSize": 8602072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7wq4e94xBGcuJg1RG3s23rk/r03n0Jy7+6y0Nbb5C7QIgNFqVD+lVlh0f6afuiGxiDEPIgdGCgZj0jPVgUiHwEtY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAX1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlYA//cmCWrX1R1nuaoFvowycnq0c05mB2R9Oz/Mf/a4pBT1PJg3/o\r\ncLZ0NQlV3/3hGnaWpMUOZ/pURdBWIr5BayyJ2InMD+MdTY6RPmTeENEK5S97\r\ngkj27tGvKoDnEULx9LCO2i1m5rkAN9wdpmvjIjt6dYqVn04Kb//E42OgOs+h\r\n4PNkxY5MrEEslj/T/uZfH9CJRapDKShMyVIKuIKS6f046qdNKby+MkEYACZ+\r\nT4kdptrLk81ZshdF1xD/7Vs+kHFq/nxokw+IQQvQfyHigvFnIRMKTdn9jcMz\r\nbaHT+pgzy5nsJZAvQUSNM8piMH3WXz1ESh6JS84U0VRvQS9JJgwlAhSgo0hh\r\nsrvSnlN2KvoLbHKAtFlrXodLDCA3WzGRwq80DKxfKP47KRZbeYcTnuDhk3MH\r\nVxjxC5/fwdx2RTCnBBL4Ne271SOVnuf5f6tY4/C8AaU3beripJVAxb/kpUdR\r\nfXGkGnIfnXnPoptSe/UDBlLJ2R/u8jZ2mqEqphX0FDt4vwU0Rp4kjffQFJlV\r\nJUYBLpsgKm6D4aJiTuWpkX69zqThI+DN6GYwQ6tHITVBu5pitmsp5KrZtSd6\r\nkCHaInSvdxkY3JU+41fc8v6/vCDSNJncMTbLfnhRsmu8m26Z3rjDcD2Q+Ca2\r\nCwbybsXXfZnMmWaF0vD8M1ushEmYM3fdJXc=\r\n=/CDq\r\n-----END PGP SIGNATURE-----\r\n", "size": 3551808}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.12_1679033844724_0.2905274669552613"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-17T06:17:25.006Z", "publish_time": 1679033845006}, "0.17.13": {"name": "@esbuild/linux-ia32", "version": "0.17.13", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.13", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ADHA1PqP5gIegehVP0RvxMmNPxpLgetI8QCwYOjUheGXKIKWSdUN8ZS3rusQv3NGZmFCpYdMZzFoI0QtzzGAdw==", "shasum": "7c10f44eed774d6e5ebe652af30e3703819314f6", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.13.tgz", "fileCount": 3, "unpackedSize": 8606168, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEVNJyKyGBOJxbCDDnpoEbk3RU9ce43RyHshDh12s9HoAiBXKusJo73qcdtZzbA31JBkeBGHArZPI7U1ppXyiZwh1g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwgRAAifEkBJjcmmXB8TsM11MfpebdxVsbN1heiUZ2Og36aVyu1z+w\r\nrPeTHE22d+***************************/FhUMF1oxOF4N9GXo6Il2q7\r\nvjJhdoIaTxw5eHfB45lEJQOUo9m5AybYF7qTTdGFQddH3IB/qv+8a3sPioIT\r\nRXlNXXfhT5e0qAaFKYsmX1hB5GeXV3XsYQ0pCXV4sUKh/oucnxFEdwDaZ0rz\r\ngrovTpZgxLxg5vSQwgqSljQuAajaabJW8zeh0aOLL7MwfJcFaslkYGe1XuQE\r\nybwq7yq0ygTpfrr6PJ2t+NapSNwGWQ/1NMTYd/zT4D9D2O5U5zAveZMpEiCn\r\nMIT28IQJwC2Be7w/iM23WeApYBfRbHMlrp/37kgq2fD6vN3c6kXHDvTCqxYx\r\nKT3PGUgy+6PLkmWotWPid6/eJ3Imdd6EZ0ZhEzi6gy9f3o3F+eroIz4abpBL\r\n2PgOgRzVwvlYYVK6Fr8NhiNA3IzhqaCRLuNEU2FwT8OqkKOb9tLmegOEj6e+\r\nD3m+oOH9UxRskfuAJQDHcAuPVbIi5iKouZDQyX/0KSrH9BxijWAdyT8WMAKv\r\nIorXo/oNYhJSnXgiujtxmhxgKq4nNhX3JS21Jqv/60J6KZX2ItItOANwy5TM\r\nmy6gda1hgLbblZUeuCaXJn5I0Fg+3sRv+bI=\r\n=AJpL\r\n-----END PGP SIGNATURE-----\r\n", "size": 3552774}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.13_1679684246040_0.9225928439355975"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-24T18:57:26.368Z", "publish_time": 1679684246368}, "0.17.14": {"name": "@esbuild/linux-ia32", "version": "0.17.14", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.14", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-91OK/lQ5y2v7AsmnFT+0EyxdPTNhov3y2CWMdizyMfxSxRqHazXdzgBKtlmkU2KYIc+9ZK3Vwp2KyXogEATYxQ==", "shasum": "f7f0182a9cfc0159e0922ed66c805c9c6ef1b654", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.14.tgz", "fileCount": 3, "unpackedSize": 8643032, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5YEz4Z8KwxAS7nHIlUtDn/iqDGxj0zYtQ2xeggIipWQIhAJt8XkGUKN2L8NtsmvPMKqucVnxrF1klY+eMUdzJUvEq"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHEg/8CISB+k773A8o4OzLDYtytqzKiBi2H4IGYQ1jmDb/1B0B6DUC\r\nDm3CNT6CjaPU3LiAPZLypNPdtbMAJxVVBgry6fS3hPawRq1/HaysQuMTKaxA\r\n0kp4VFzXlM18WXx+aGVwe533ouYSumQIPbv0UB6GVRweWpfyYXbbe81vfMqo\r\nBPeRS5vGe3Co10wXqVCwvj9nNq+fw2+sJKHdwOKVzzs3D3BUdmDC4F/4pJyF\r\nKkaXq/BzHATpuOCeOJBYeKl0WcplKRio0I8gj5fgCz4rIVw08dsTDpCy8u4J\r\nxX740y24YKKRkXuxmEV2xFqcoo3e1v0O6lyTT/okPRwragUdr6AYsAJtIP5O\r\nfnE76mc0rKwYzT2SHCP0pkLI0bXLu5WMcYxzVHmXgHTeESTJBX+u2F65v27A\r\nPktQWp9ZahlS8Iok5CXB0bs2VhutnrqvqAEJEjK0nISMl3FDZ8cZV1V0SnUn\r\nA4aUwX1m/6+f8lyexZwSQ797HFsYUfXqSO7C0bILyQ3BBWGvRBJJndE2+6lP\r\nXdSwA2Ul4Ji32zzJNzlJI2Hw65RLh82JQ1LgqjB59okBtfu+cCgmgApwncyu\r\nqZXso2ey/Ki7xTDsuhVjfo0d4pQ+hv8LdRWgk3qWXgoFQldk+t5wlauAGXXO\r\nqCGfJEOBSmThp5zCNwiNcm/Gp3BGZ0emN1o=\r\n=QQm5\r\n-----END PGP SIGNATURE-----\r\n", "size": 3569276}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.14_1679798879715_0.9120914852761419"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-26T02:48:00.019Z", "publish_time": 1679798880019}, "0.17.15": {"name": "@esbuild/linux-ia32", "version": "0.17.15", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.15", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-wp02sHs015T23zsQtU4Cj57WiteiuASHlD7rXjKUyAGYzlOKDAjqK6bk5dMi2QEl/KVOcsjwL36kD+WW7vJt8Q==", "shasum": "eb28a13f9b60b5189fcc9e98e1024f6b657ba54c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.15.tgz", "fileCount": 3, "unpackedSize": 8643032, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5xYlGqX1Q48JdkQ2bglqJV+yyij7Er4XxhKguMQTTgAiBpClaifsjVZ42RxM/gFE+tD529wfip6g4k7bzYf3/+0w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK/AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrv3A//YU3yfRj5jEUVNQMwu+1kTEmSeQqEKPOclU+dlAMK7r/yUEhk\r\nzGAa4k5FZi+ZgkY7StBSLv0UE3QOg1H5X4EmqywQv1LMlVdHA+OtY7foBxpY\r\nMJByvRuDsGNlt7HywYneA5gd50gZIEusxhTFfz4qGi+q2eDo2LGKmJ4Ahv3h\r\nyuJILPFfFX4710Mc8WxFFqN4MR9AVfBRTHWnjabuoCGLmR7EM3yS22FnqE5R\r\nyW8N2trElXgzmOWS6RijR9P72uO3tq0BpYjimKgJ5APV8vqXqES0UVp0578/\r\nxgDXxo16lNEmyAKpXQ3q5e+av2OHRQsSRiB0AV4AUUV3uChSdJoSqQv+7m1S\r\nNuDXGZuSRIR8HWGeXQE/J5pJqjcaaktcz75w57oMU7nTNtj+jyZEGPQtDQbt\r\n2Qv2c0TGyeCVfI2WlNSRGIqKhyPqyQXTZKa4F4sXST7SVUlFHXtn4I4YRmYf\r\ndpwyQHwYYbFT59vUW+c4fxDc+wX94mPXMP2qAyZlUc9VWD2OGf0QEdplMuE8\r\nWs4SVl8r1J+OI379/o244rhDpWMMJ5fRuFkpSRiHKPZpslU3UJRUiq6hvswr\r\nmSPQSXgBkGn5j7SDZn6xwGgtLHIp7KzS60AMkclmBXQs9Ha0qzYN62DE98Z6\r\nNGaAjttGssTbyA3iJnOv935gudrftL3xkOk=\r\n=1hJW\r\n-----END PGP SIGNATURE-----\r\n", "size": 3569616}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.15_1680388032513_0.50889769253826"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-01T22:27:12.795Z", "publish_time": 1680388032795}, "0.17.16": {"name": "@esbuild/linux-ia32", "version": "0.17.16", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.16", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-9ZBjlkdaVYxPNO8a7OmzDbOH9FMQ1a58j7Xb21UfRU29KcEEU3VTHk+Cvrft/BNv0gpWJMiiZ/f4w0TqSP0gLA==", "shasum": "673a68cb251ce44a00a6422ada29064c5a1cd2c0", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.16.tgz", "fileCount": 3, "unpackedSize": 8647128, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6UVDPesPhjNlLnHf58+oYH7DFvWyf000xCQ7Mrr9y1AIhAPLpfNmXEwrbTkofQRxaNKUie3y/LVZj9O9ZIJBDQTl/"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5IHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOyhAAjk+aKf219HPNcOYrseURBWwHS/wU1gwtEAvr3JCUqiBTemtZ\r\nEVxAT/mIwiKLjxSoV/IOKYwl4ZhnPIdflLcVBcVOIApU7EzCgCK42mD/+iV7\r\ndUkuBlC+3Sq5nnE8FscSpaZ6d2U2jpPeCx5xp2M4rbMBCO3rqYm+lGPrqd8O\r\nlUSnfXY30amia3I/nJbw8vWNTs9C2PwjrgWcFo3UO63qrR8LfiDCAvcpcm9w\r\nL0W1gU8n1DyaYKu5rSnJGwvwfQqEMs3iDVgdNjbk+x4sY/S6LXQvaqo+yS9/\r\nCghoy7Qm/t0Ee5Qb5k44bLUH/1TKBHC+sBh00Vba/n2sFfmKG18XZgUNR7Ax\r\nKTS7TqAa/OgRnooVCEWwf93ZuiwuYls7JNL8v6qXEV/udUeD4V5/be5SHsmb\r\n0jdqFX3xXEO2QZaGozHIrcXwsmWndndLjUG09Thn15M8NA94nQM78Hj330oN\r\ns8cqwun/+mSnIE0sX+ECz7jfzmIqhbNeKMauQBhIAdfZ2U2HNTxZUvBo/aJf\r\nsUHQMh8iUTmObpDusUD6Kxp4pRI4p+43PJsHI7iuIKPPv1VXe9HFLGs5Gd+n\r\nADvGrw8cdihfiJwEVlI7YLLnWUYjfgp0LqGRKt6MRTZdJDpMB7XZtWhYM1jD\r\n9eAMdPHb0Tks6ItDl+7OJU1rf1IvqOrOKNI=\r\n=AKi0\r\n-----END PGP SIGNATURE-----\r\n", "size": 3571071}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.16_1681101319667_0.4846332322791147"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-10T04:35:19.945Z", "publish_time": 1681101319945}, "0.17.17": {"name": "@esbuild/linux-ia32", "version": "0.17.17", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.17", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-IBTTv8X60dYo6P2t23sSUYym8fGfMAiuv7PzJ+0LcdAndZRzvke+wTVxJeCq4WgjppkOpndL04gMZIFvwoU34Q==", "shasum": "18a6b3798658be7f46e9873fa0c8d4bec54c9212", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.17.tgz", "fileCount": 3, "unpackedSize": 8651224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBMFbtViE/NXJzMFtgxcegDfOG+xT4GiwBbGvTYcYs+AAiBhjb6nJcQ6BzGwJAeoD6s8sPLHl87/RYXvIJPmbjQn7w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFrQ//TmTGKkCKKL0+0FID1FbRp6T8NRq93n/g35J6M4gjBxzH9LDu\r\n0deIFVN7rEFiB74cVz5KGdq5tvr/7zKAKWpazP29dL1gcnWR+qaPDW8xrujA\r\n33XbSgBOelw3JUslcyUL9YkWmLz1lu1ibkJgTgCG96+6pL1TJ8hYKwpcE/VB\r\nQrWm3oztr5YX7OtvGDwUXEdgyFo8cTeNaDzlV0AXXDt2TNsexqHvJ6U+Y2cm\r\n6wzUZdoaEYoenfKAWxgFR1VtevMlePjjzL7oW5aR8Lmh68emQxOZX1gj1IBv\r\nxPoysXXEiq2CPSwmPgrGcNiuIF9Zm+8dMNww8f0uuzIW6hFEnShoB6jJMKEx\r\nzgaTr06DoqVsJJmmyP/8T1UFAi1q3BDlyCrLPZ0r2rawLbIEwRGMaJ2bZvMO\r\nbT3r3B3NemlOG1L8KQ+Gu8Hr3MgkmIz0e5gCklXjIFOrXNeM6dN2mAIfVY0z\r\nNB6+H41N3BHtFGl7zX3QtM0ZxZhY2/gcAFDb4gSBv87xYwSAd039WT/9qLP+\r\nMult0w1ijxLfQMMByG8/EjhmPdsa18jjlRvRCED/uxBXpzovlc+Ox27JmGoq\r\nzj3vYiK3nlL5TqjaF/EG2TTIh8xh1jedyCIgAkN7tHygLYpwFQMeORdI2YWC\r\nBVfhweJ/Zs2ePfVOtwpHrvjpI2cfrz2Q/G4=\r\n=KMpZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3572380}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.17_1681680233670_0.3515846829045819"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T21:23:53.891Z", "publish_time": 1681680233891}, "0.17.18": {"name": "@esbuild/linux-ia32", "version": "0.17.18", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.18", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ygIMc3I7wxgXIxk6j3V00VlABIjq260i967Cp9BNAk5pOOpIXmd1RFQJQX9Io7KRsthDrQYrtcx7QCof4o3ZoQ==", "shasum": "47878860ce4fe73a36fd8627f5647bcbbef38ba4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.18.tgz", "fileCount": 3, "unpackedSize": 8651224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZTBg57KvRExcu/E+LC41DvBtKs0QTDXPZxi1XKFMxbAiEA9k2tTdBicoW60bwq+BMYReUR5GZxKs+FdcxXZgIwHBM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREaIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXtg//eXbirJFAgBVwbqj8nd1/3E3MXz43ylfotnGT+0HIZmXHS1u2\r\nBVCz2Tr3CdhJ+8kqvLvgBQPgqm2TYurV1gT8AwgWHivR+419F+/daD9eesSF\r\nqanIOc0iihxAnhpUCikwkwztVLpy2OclnrD/V1OWome/eYxBK8B8eexNRdZB\r\nXEu1BimH1EDQDxJXPPagEkkIIn8LKqqGKEAhtxcQvCH9D2n5r1+IV1hQpJ5z\r\nr+uB6Km6/DRk3BVavwcRlAM+FX171mwjgPLLXIQjJWw7aiPUVhpSWXVkkvBf\r\nX1aMasHCMM+Vt/Vb+F0gJ8Fj8CcdbFh84a69HeCP80YC/Ga3T+m+583ffrg+\r\nOlIU42N9/b8qVYbAgA1l0OWMj8wZ3UHOp3RxtOPDWaqSLjwyW3LCYlChgMye\r\ndX1PUO5wW8VbLQH4ChxbrnEAkn5U1RHYOTSRyjlB+/AgZvJVV9NUUgHlvCvi\r\n/Hq3cmFgHFhwYIQT4jvp6vaeQgEETYlMKLtLpb4JGLose0t92LFxF0Wy3z1+\r\nFjA8ol0svZ5sA4AjIp+T9FuAxSTnNgBVJzmgAOi8hSAgf45FWdxtHp6QwtCT\r\nm8Bla1wK4KQ8lUwAgkJF6Qip9wMjmyL7ltV31hEc1CTdcoV0AIFyfD6IU/YY\r\nuRrRVlSnuiO6VYRdqMuuX9AlT8vMM2osFZc=\r\n=D0XI\r\n-----END PGP SIGNATURE-----\r\n", "size": 3573352}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.18_1682196103769_0.10879604249061003"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-22T20:41:44.018Z", "publish_time": 1682196104018}, "0.17.19": {"name": "@esbuild/linux-ia32", "version": "0.17.19", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.17.19", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==", "shasum": "e28c25266b036ce1cabca3c30155222841dc035a", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.17.19.tgz", "fileCount": 3, "unpackedSize": 8659416, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAP3bHdGVZAIAbycdpO/f/ehfor0seqC9yOQopde0pcyAiEAlLeEPiOpxzcKV7MOIS+PWI1i59159tTsHThr9q14XAU="}], "size": 3575680}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.17.19_1683936409525_0.9265997297786528"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T00:06:49.821Z", "publish_time": 1683936409821, "_source_registry_name": "default"}, "0.18.0": {"name": "@esbuild/linux-ia32", "version": "0.18.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-WNDXgJdfDhN6ZxHU7HgR2BRDVx9iGN8SpmebUUGdENg4MZJndGcaQuf2kCJjMwoK0+es1g61TeJzAMxfgDcmcA==", "shasum": "cce1e382b91802a048b9ddf7c7b96fee0fac4a20", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.0.tgz", "fileCount": 3, "unpackedSize": 8671703, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTNZ3vP5nf/Ye5QeJt72zesYIKUYhTQCLEjjwXzv19VwIgFfdyVoC9qPqCq1d2i6B1kR0kOqgp+c8C8Zaso9RdNiI="}], "size": 3581640}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.0_1686345877688_0.3872706999965432"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-09T21:24:37.902Z", "publish_time": 1686345877902, "_source_registry_name": "default"}, "0.18.1": {"name": "@esbuild/linux-ia32", "version": "0.18.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-QHS4duBPuAsLZP82sNeoqTXAJ1mNU4QcfmYtBN/jNvQJXb6n0im8F4ljFSAQbivt1jl1OnKL8HhlLUeKY75nLg==", "shasum": "89cda727edeb1ae844b1cd7b88ae03c33197b1e2", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.1.tgz", "fileCount": 3, "unpackedSize": 8679895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLOBkxPj19bUkDoIJfvZeXzqbTafUA/TCiiLJjsV6BjgIhANn5MJEMSPzfLJYKduj+t/EAPmLeTpW5wdy/7rjvhTy1"}], "size": 3585240}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.1_1686545520901_0.5588420197765025"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-12T04:52:01.164Z", "publish_time": 1686545521164, "_source_registry_name": "default"}, "0.18.2": {"name": "@esbuild/linux-ia32", "version": "0.18.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-VEaK3Z+vJyDwwPsP0sovaEw1foDzrMs7XQNYEIFkOwMjSe2BipKRKUUyrznil0p8qqsK7U8W+T7oNqZpgdnD2Q==", "shasum": "6b9bef1ea1a8c4dc6562182abe7d45e34824a440", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.2.tgz", "fileCount": 3, "unpackedSize": 8683991, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1OBDaQ8gJEx+dmSyRjfitPTcV5yPazNo+CdFo3K/+rwIgHC+J+MDzHN1rd2QP9hOM1uZkTdtFhOO5jSX5wmzQMAc="}], "size": 3585872}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.2_1686624049834_0.3025558226217868"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-13T02:40:50.145Z", "publish_time": 1686624050145, "_source_registry_name": "default"}, "0.18.3": {"name": "@esbuild/linux-ia32", "version": "0.18.3", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-EyfGWeOwRqK5Xj18vok0qv8IFBZ1/+hKV+cqD44oVhGsxHo9TmPtoSiDrWn8Sa2swq/VuO5Aiog6YPDj81oIkA==", "shasum": "b6c8c55be4d472af77c060c31a52121187d52e71", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.3.tgz", "fileCount": 3, "unpackedSize": 8683991, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHmhNixBoHsEXuuwBnD1I6+WAZnxnYswh1FYaRHdRs89AiA7ujIpe7/guE7RShRRVJXff8yBrQ3XLqAJtD+xCS3bkA=="}], "size": 3585881}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.3_1686831693547_0.5421680739005292"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T12:21:33.824Z", "publish_time": 1686831693824, "_source_registry_name": "default"}, "0.18.4": {"name": "@esbuild/linux-ia32", "version": "0.18.4", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-4ksIqFwhq7OExty7Sl1n0vqQSCqTG4sU6i99G2yuMr28CEOUZ/60N+IO9hwI8sIxBqmKmDgncE1n5CMu/3m0IA==", "shasum": "9d22fa5bb59d7dfbff7d981d933b434585d96dae", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.4.tgz", "fileCount": 3, "unpackedSize": 8692183, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBGS9LmxOKwG7E/8UJOcZ6pk2kYb85ICznRYJ/odnvykAiEAgUpM5wVAStMslqe1e+j4UH9y+z0yuJT3Z9vJHMysNeo="}], "size": 3587273}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.4_1686929933118_0.5662274593160728"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-16T15:38:53.458Z", "publish_time": 1686929933458, "_source_registry_name": "default"}, "0.18.5": {"name": "@esbuild/linux-ia32", "version": "0.18.5", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ph6M9iEMc6BHgv2XuIE8qeQrQCH+2l116c8L9ysmmXYwpNXa3E7JNIu/O7hI0I9qDvh1P19AGbIh+/y0GAZijA==", "shasum": "8815b132fc83fff264ba43dd6c213569b7897412", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.5.tgz", "fileCount": 3, "unpackedSize": 8708567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDS1++v5OiIuxjkZLeaFb3pL10yZTEWA2zRYENJdtPrsAIhAJWA1DuThdk9yH/dhiBXL4F+EgwDkM1V3jkvKQlLpIGI"}], "size": 3594908}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.5_1687222378321_0.7337165045454481"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T00:52:58.592Z", "publish_time": 1687222378592, "_source_registry_name": "default"}, "0.18.6": {"name": "@esbuild/linux-ia32", "version": "0.18.6", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-AXazA0ljvQEp7cA9jscABNXsjodKbEcqPcAE3rDzKN82Vb3lYOq6INd+HOCA7hk8IegEyHW4T72Z7QGIhyCQEA==", "shasum": "4ceb486ad4aab4fbba702d21f40ab97bc3f3f0d4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.6.tgz", "fileCount": 3, "unpackedSize": 8720855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGAZttzdU7n2qtllvgfjVwkB8cPeh+yP0KuferutesXIAiEAk1c9BCHgIIvbK6xuy26sfg0dk9ujpxudqfsYQ34Prs0="}], "size": 3598825}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.6_1687303513062_0.15816130348110047"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T23:25:13.364Z", "publish_time": 1687303513364, "_source_registry_name": "default"}, "0.18.7": {"name": "@esbuild/linux-ia32", "version": "0.18.7", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-SsvsGStwbArBcB/XNh+2MvUtgOLp0CR6Hn1PBWcdApCuAaMibHCDyzHS06+u/YOD1UpeXFHJZphix61HeVMH/w==", "shasum": "d868c1fe7fb36afbf38a8334a3e203c1cdee4ed7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.7.tgz", "fileCount": 3, "unpackedSize": 8757719, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLA8ox4pHmzLWG2Po1YGm3k9rbNw18qwQLHt5EbeRw+AIhAP3y4vaINMHk7yqBmZTCUa0ieByrbb5HRHeT62DMTQCB"}], "size": 3614647}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.7_1687574802343_0.3194019264605521"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-24T02:46:42.579Z", "publish_time": 1687574802579, "_source_registry_name": "default"}, "0.18.8": {"name": "@esbuild/linux-ia32", "version": "0.18.8", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.8", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-mUDNdkY8mr4kZrekGLwZBFpvVX1VJLpwYUsbKTM/w0h4xVgsupc440nlsUfyz8OKeE92ZdMUUG8wrdOeZaONiQ==", "shasum": "a9841275da92e34a19fdff03702b060fdd530a0e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.8.tgz", "fileCount": 3, "unpackedSize": 8757719, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXRex2Vyjbsac/82Il87w5wTB1FNvghPTnXq0SFDMJZAIhAJCMUYluAbl2sjDMYfd2DZs5HIVa4gpensa1n3YmFdE1"}], "size": 3616268}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.8_1687663169439_0.3211575939962197"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-25T03:19:29.719Z", "publish_time": 1687663169719, "_source_registry_name": "default"}, "0.18.9": {"name": "@esbuild/linux-ia32", "version": "0.18.9", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.9", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-pTTBAGi2lrduXo4vATnqCtFi9zRbyXOlcV+euznW5EoFyjAIR+JCQgFDeFCMo343E2EI2MgV7ZQctO8IWcsdsA==", "shasum": "10f6d7001553c35f3b4cdda1cd0fa92b7bf14909", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.9.tgz", "fileCount": 3, "unpackedSize": 8778199, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICYuYLwspq0VyYf5Mwg7kZBU6mPzljHbMdrxs7XcdBOBAiAmu4FTygYY0D/W8SNtjqk+PpiWwnYDsMpqtaNrnrqlvA=="}], "size": 3623265}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.9_1687757306718_0.7853433248538455"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T05:28:27.026Z", "publish_time": 1687757307026, "_source_registry_name": "default"}, "0.18.10": {"name": "@esbuild/linux-ia32", "version": "0.18.10", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-otMdmSmkMe+pmiP/bZBjfphyAsTsngyT9RCYwoFzqrveAbux9nYitDTpdgToG0Z0U55+PnH654gCH2GQ1aB6Yw==", "shasum": "291aba2de93e8cdf77d607f54e554ce4ffd2f5ef", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.10.tgz", "fileCount": 3, "unpackedSize": 8778200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDzq7NtIxFxJudsydxWMQu00it2ljDv84NTzvGnNfzGeAiA3I/OpPpnXsCfVU0hQ3J/xHaQv9Z0uNnJ+CUXqGYUhkQ=="}], "size": 3623262}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.10_1687814444421_0.32286621871896193"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T21:20:44.703Z", "publish_time": 1687814444703, "_source_registry_name": "default"}, "0.18.11": {"name": "@esbuild/linux-ia32", "version": "0.18.11", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.11", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-S3hkIF6KUqRh9n1Q0dSyYcWmcVa9Cg+mSoZEfFuzoYXXsk6196qndrM+ZiHNwpZKi3XOXpShZZ+9dfN5ykqjjw==", "shasum": "20ee6cfd65a398875f321a485e7b2278e5f6f67b", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.11.tgz", "fileCount": 3, "unpackedSize": 8778200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFG+5qp9bdnUk3ee6MTSQQqMalye7bGVYyqL3edLaHfmAiBcf96GrM6RUfZCpJbo4X9ozzvab+QwCCRMG84ZqUxgTw=="}], "size": 3623387}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.11_1688191452393_0.4457686191184229"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-01T06:04:12.740Z", "publish_time": 1688191452740, "_source_registry_name": "default"}, "0.18.12": {"name": "@esbuild/linux-ia32", "version": "0.18.12", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.12", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-yoRIAqc0B4lDIAAEFEIu9ttTRFV84iuAl0KNCN6MhKLxNPfzwCBvEMgwco2f71GxmpBcTtn7KdErueZaM2rEvw==", "shasum": "78f0ae5068251831db012fb2dcdee6c37a54a92e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.12.tgz", "fileCount": 3, "unpackedSize": 8778200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnovYqq9A3Mic5nprOPePQNg8DUk3DqPnp+LRvnCph1QIhAIJi5otWQc35tT0EXL/DUihwp9kvlK2Myl1StXdfsPlA"}], "size": 3624157}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.12_1689212068525_0.16337967127553243"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-13T01:34:28.833Z", "publish_time": 1689212068833, "_source_registry_name": "default"}, "0.18.13": {"name": "@esbuild/linux-ia32", "version": "0.18.13", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.13", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-I3OKGbynl3AAIO6onXNrup/ttToE6Rv2XYfFgLK/wnr2J+1g+7k4asLrE+n7VMhaqX+BUnyWkCu27rl+62Adug==", "shasum": "a901a16349c58bf6f873bced36bdf46a5f4dac5d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.13.tgz", "fileCount": 3, "unpackedSize": 8774104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF4F91mdElcKWxgGAbc692RUxPD9FE8umia1aSIGDEbvAiAf7nj5UJkl4IaKFcnRTEr2XNPgvyrbQHs6M1z/ELdz1w=="}], "size": 3620879}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.13_1689388652566_0.7551421881652829"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-15T02:37:32.779Z", "publish_time": 1689388652779, "_source_registry_name": "default"}, "0.18.14": {"name": "@esbuild/linux-ia32", "version": "0.18.14", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.14", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-BfHlMa0nibwpjG+VXbOoqJDmFde4UK2gnW351SQ2Zd4t1N3zNdmUEqRkw/srC1Sa1DRBE88Dbwg4JgWCbNz/FQ==", "shasum": "91f1e82f92ffaff8d72f9d90a0f209022529031a", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.14.tgz", "fileCount": 3, "unpackedSize": 8810968, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgzB9dBE4wcSEmFCxnFgmsw/lxFbkQvk4t5ZKxZklKOAiA9WsngFkL81MpdMGmXfm1tY3jyEZiKAPJG10U7mNJXRg=="}], "size": 3634303}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.14_1689656438541_0.47416032953930043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-18T05:00:38.780Z", "publish_time": 1689656438780, "_source_registry_name": "default"}, "0.18.15": {"name": "@esbuild/linux-ia32", "version": "0.18.15", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.15", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-JPXORvgHRHITqfms1dWT/GbEY89u848dC08o0yK3fNskhp0t2TuNUnsrrSgOdH28ceb1hJuwyr8R/1RnyPwocw==", "shasum": "fa797051131ee5f46d70c65a7edd14b6230cfc2f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.15.tgz", "fileCount": 3, "unpackedSize": 8819160, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJ6cxH7zcIfYSnMrsv6+FqdJB8dt+YySsq39RZsF2eqQIgF/e21++aaYOIzjKHgz1BHKgIMY89O2e0ZfiwQO7mwg8="}], "size": 3637670}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.15_1689857625012_0.9409352912067239"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T12:53:45.279Z", "publish_time": 1689857625279, "_source_registry_name": "default"}, "0.18.16": {"name": "@esbuild/linux-ia32", "version": "0.18.16", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.16", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-no+pfEpwnRvIyH+txbBAWtjxPU9grslmTBfsmDndj7bnBmr55rOo/PfQmRfz7Qg9isswt1FP5hBbWb23fRWnow==", "shasum": "a8825ccea6309f0bccfc5d87b43163ba804c2f20", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.16.tgz", "fileCount": 3, "unpackedSize": 8819160, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmcaqE0qmDReL2BB4HhI1zhIn+U9b4gLFeTaIEdBxfOQIhAPCK+A1LLIfbdpKFhqtbPT53kvMVDdYBM8np9JnXxclg"}], "size": 3638251}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.16_1690087701149_0.7143810464699001"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-23T04:48:21.554Z", "publish_time": 1690087701554, "_source_registry_name": "default"}, "0.18.17": {"name": "@esbuild/linux-ia32", "version": "0.18.17", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.17", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-1DS9F966pn5pPnqXYz16dQqWIB0dmDfAQZd6jSSpiT9eX1NzKh07J6VKR3AoXXXEk6CqZMojiVDSZi1SlmKVdg==", "shasum": "6fed202602d37361bca376c9d113266a722a908c", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.17.tgz", "fileCount": 3, "unpackedSize": 8843736, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPmV9kYLnru2z0VPNJI3KoPlDnhvn6o6kDM5RBknIfCAiBt/PxjeKkHNudXAoRbjhXINiDk3/q1vfhbxosDFBsBXg=="}], "size": 3647866}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.17_1690335672404_0.8187149937854432"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T01:41:12.705Z", "publish_time": 1690335672705, "_source_registry_name": "default"}, "0.18.18": {"name": "@esbuild/linux-ia32", "version": "0.18.18", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.18", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-+VHz2sIRlY5u8IlaLJpdf5TL2kM76yx186pW7bpTB+vLWpzcFQVP04L842ZB2Ty13A1VXUvy3DbU1jV65P2skg==", "shasum": "9ef6c7eeb8c86c5c1b7234a9684c6f45cbc2ed57", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.18.tgz", "fileCount": 3, "unpackedSize": 8856024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFm5eBubKJioMmuzWXlAJRGoGXwaRwEdmQpsm1YIXihQIhALkv11f16q+Kyjot/EpkE7OhB7F72XotR7XGjsS0AE+O"}], "size": 3656010}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.18_1691255204613_0.4180810570814377"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-05T17:06:44.896Z", "publish_time": 1691255204896, "_source_registry_name": "default"}, "0.18.19": {"name": "@esbuild/linux-ia32", "version": "0.18.19", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.19", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-SAkRWJgb+KN+gOhmbiE6/wu23D6HRcGQi15cB13IVtBZZgXxygTV5GJlUAKLQ5Gcx0gtlmt+XIxEmSqA6sZTOw==", "shasum": "283cd3c3d8380e8fab90583fa86ca1fcc9b9ec57", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.19.tgz", "fileCount": 3, "unpackedSize": 8884696, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQDeNewwYmln0B4d5+tWQXWAVh+r48slIynVSK0zyXvLuQIfCdmIo5L+3wIIVGzOLq3K3RZ95HXe5QufDvEzTqRaGw=="}], "size": 3667777}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.19_1691376698305_0.22435294339944756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-07T02:51:38.559Z", "publish_time": 1691376698559, "_source_registry_name": "default"}, "0.18.20": {"name": "@esbuild/linux-ia32", "version": "0.18.20", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.18.20", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==", "shasum": "699391cccba9aee6019b7f9892eb99219f1570a7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.18.20.tgz", "fileCount": 3, "unpackedSize": 8896984, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCbq6QdnFZDnx1b+ASjm0KsBLSNHLgvu9EF6WmCXmdsQIhAO4wmqkGdpeUia+LW7x5Aw6YRnB0txxJqLWvTmHFUZ4d"}], "size": 3671429}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.18.20_1691468120359_0.4223293304404889"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-08T04:15:20.615Z", "publish_time": 1691468120615, "_source_registry_name": "default"}, "0.19.0": {"name": "@esbuild/linux-ia32", "version": "0.19.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.0", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-dz2Q7+P92r1Evc8kEN+cQnB3qqPjmCrOZ+EdBTn8lEc1yN8WDgaDORQQiX+mxaijbH8npXBT9GxUqE52Gt6Y+g==", "shasum": "dfcece1f5e74d0e7db090475e48b28d9aa270687", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.0.tgz", "fileCount": 3, "unpackedSize": 8946135, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkkwDQ8EYaW32O4T0TGTjohubut5uQeP+URYifGr34DAIhAImSmfN4NnDDQ6Jq5Ba+0yQ8/GxUIcpkH+WLDhcma7Rr"}], "size": 3692331}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.0_1691509968164_0.5491115152791597"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-08T15:52:48.452Z", "publish_time": 1691509968452, "_source_registry_name": "default"}, "0.19.1": {"name": "@esbuild/linux-ia32", "version": "0.19.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.1", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-8AKFBk9v/zBDsADvK/0BWZUxkjEc0QDwO8rvbHJKqAZx6DF/VSeBxTRmqWeecrJmx+n3kemEwML9z0eD9IHweQ==", "shasum": "860b8c9e15259117c2adc3c510eac1fcf4b96e9a", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.1.tgz", "fileCount": 3, "unpackedSize": 8958423, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAFLdYXdCrBf6/oJnUS7UnQ480752Ikh5QKmbkCdVBMgIhAI1JTYAFG7F6xDNXKdOetTpZk5/7KD8KobYKKHOTVzHI"}], "size": 3696923}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.1_1691769469139_0.5064322115773436"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-11T15:57:49.321Z", "publish_time": 1691769469321, "_source_registry_name": "default"}, "0.19.2": {"name": "@esbuild/linux-ia32", "version": "0.19.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.2", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-mLfp0ziRPOLSTek0Gd9T5B8AtzKAkoZE70fneiiyPlSnUKKI4lp+mGEnQXcQEHLJAcIYDPSyBvsUbKUG2ri/XQ==", "shasum": "3fc4f0fa026057fe885e4a180b3956e704f1ceaa", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.2.tgz", "fileCount": 3, "unpackedSize": 8954327, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFSprWmLnOpP24GP8n+YaZEC7T2dnU8ET4rqRL0t/yaoAiEA5VlpzfLXn/DYPXq3RVt0KJY3Q/nRckKXH5lDc1gUGNo="}], "size": 3697615}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.2_1691978316511_0.5032078362499057"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-14T01:58:36.779Z", "publish_time": 1691978316779, "_source_registry_name": "default"}, "0.19.3": {"name": "@esbuild/linux-ia32", "version": "0.19.3", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.3", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-7XlCKCA0nWcbvYpusARWkFjRQNWNGlt45S+Q18UeS///K6Aw8bB2FKYe9mhVWy/XLShvCweOLZPrnMswIaDXQA==", "shasum": "5813baf70e406304e8931b200e39d0293b488073", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.3.tgz", "fileCount": 3, "unpackedSize": 8962519, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDPrlh0L+fDuQrelwMHDTI81jBWdy5PhKxEcUxxYPejAAiEA6VwAaV/aU2FGqlC0E87S1IMcoN6dSCTlN6Y9pFKxH8s="}], "size": 3699971}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.3_1694653959312_0.6896982653058013"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-14T01:12:39.791Z", "publish_time": 1694653959791, "_source_registry_name": "default"}, "0.19.4": {"name": "@esbuild/linux-ia32", "version": "0.19.4", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.4", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-EGc4vYM7i1GRUIMqRZNCTzJh25MHePYsnQfKDexD8uPTCm9mK56NIL04LUfX2aaJ+C9vyEp2fJ7jbqFEYgO9lQ==", "shasum": "878cd8bf24c9847c77acdb5dd1b2ef6e4fa27a82", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.4.tgz", "fileCount": 3, "unpackedSize": 8962519, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC71Q89o6fvd+Ng6xGco/WxxyKLiDEJT6Q+cXPzW3VmpAiEAkU5BDLwpz8XIUq6+n/sA6Nrx5Jm8DRHNKx/oy1rm/0k="}], "size": 3700296}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.4_1695865673031_0.9384165010352541"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-28T01:47:53.386Z", "publish_time": 1695865673386, "_source_registry_name": "default"}, "0.19.5": {"name": "@esbuild/linux-ia32", "version": "0.19.5", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.5", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-MkjHXS03AXAkNp1KKkhSKPOCYztRtK+KXDNkBa6P78F8Bw0ynknCSClO/ztGszILZtyO/lVKpa7MolbBZ6oJtQ==", "shasum": "b6e5c9e80b42131cbd6b1ddaa48c92835f1ed67f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.5.tgz", "fileCount": 3, "unpackedSize": 8966615, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA2nFSxjnjj5JppQGLwq8XLEDyYyzCUx1A4IFIJYtdJ+AiEAoelQfjftK/KxVrcNlsTWFamqOZ7Ygc9pcdRvgKCUqQU="}], "size": 3701677}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.5_1697519451704_0.4565013273150993"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-17T05:10:52.061Z", "publish_time": 1697519452061, "_source_registry_name": "default"}, "0.19.6": {"name": "@esbuild/linux-ia32", "version": "0.19.6", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.6", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-22eOR08zL/OXkmEhxOfshfOGo8P69k8oKHkwkDrUlcB12S/sw/+COM4PhAPT0cAYW/gpqY2uXp3TpjQVJitz7w==", "shasum": "f99c48b597facf9cbf8e1a2522ce379b2ad7b0c4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.6.tgz", "fileCount": 3, "unpackedSize": 8991191, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+NSYiq5DgsbJWNtGC+r1Cns4SBu99ebCxHZvC0k48nAiEA2wQjt4o9zheZzUYsDJTpfVr9Bj47vlQ2YlHAyjIxNpU="}], "size": 3709564}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.6_1700377912846_0.320158798677473"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-19T07:11:53.138Z", "publish_time": 1700377913138, "_source_registry_name": "default"}, "0.19.7": {"name": "@esbuild/linux-ia32", "version": "0.19.7", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.7", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-2BbiL7nLS5ZO96bxTQkdO0euGZIUQEUXMTrqLxKUmk/Y5pmrWU84f+CMJpM8+EHaBPfFSPnomEaQiG/+Gmh61g==", "shasum": "01eabc2a3ad9039e115db650268e4f48f910dbe2", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.7.tgz", "fileCount": 3, "unpackedSize": 9028055, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBBEc1PgnGPB+lElpLWZSliTlfyH+kiutxrZCq3GhT/RAiBCwbjwCopZms4nNFLhKMYbERDOLI/OYIrAjOje2/1Pzw=="}], "size": 3723020}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.7_1700528486305_0.32312053032050225"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-21T01:01:26.765Z", "publish_time": 1700528486765, "_source_registry_name": "default"}, "0.19.8": {"name": "@esbuild/linux-ia32", "version": "0.19.8", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.8", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-1a8suQiFJmZz1khm/rDglOc8lavtzEMRo0v6WhPgxkrjcU0LkHj+TwBrALwoz/OtMExvsqbbMI0ChyelKabSvQ==", "shasum": "2379a0554307d19ac4a6cdc15b08f0ea28e7a40d", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.8.tgz", "fileCount": 3, "unpackedSize": 9028055, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGgUjh1BzGyw+/k2oNd38LFeNsPncDDt5i8AmHAM1JXvAiEA9p74mcgRan9Bv9JthxfoS7x8LBy/voGT+tPEVVKVOUs="}], "size": 3722697}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.8_1701040103589_0.29887885961658545"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-26T23:08:23.817Z", "publish_time": 1701040103817, "_source_registry_name": "default"}, "0.19.9": {"name": "@esbuild/linux-ia32", "version": "0.19.9", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.9", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-f37i/0zE0MjDxijkPSQw1CO/7C27Eojqb+r3BbHVxMLkj8GCa78TrBZzvPyA/FNLUMzP3eyHCVkAopkKVja+6Q==", "shasum": "18892c10f3106652b16f9da88a0362dc95ed46c7", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.9.tgz", "fileCount": 3, "unpackedSize": 9134551, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICqWhQuAuT6wnvStlBJJlkt4u/678Yqfg3dfDpbErMsOAiB96Gux1cZ6TmmVdu7yGbVXDwyP6YTrFNzBsDf2WnHRQw=="}], "size": 3761156}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.9_1702184981860_0.985749593790519"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-10T05:09:42.120Z", "publish_time": 1702184982120, "_source_registry_name": "default"}, "0.19.10": {"name": "@esbuild/linux-ia32", "version": "0.19.10", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.10", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-4ub1YwXxYjj9h1UIZs2hYbnTZBtenPw5NfXCRgEkGb0b6OJ2gpkMvDqRDYIDRjRdWSe/TBiZltm3Y3Q8SN1xNg==", "shasum": "d55ff822cf5b0252a57112f86857ff23be6cab0e", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.10.tgz", "fileCount": 3, "unpackedSize": 9138648, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7uSJFVpkFx8F9PSpdWKV94EGGncseJWmOPaeJ1FQmNwIhAK8g29y9ccYnDtrYkUvbK2u+xFVWNbY3Zq6EAQeoNTUI"}], "size": 3764222}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.10_1702945314652_0.5350103900321204"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-19T00:21:54.989Z", "publish_time": 1702945314989, "_source_registry_name": "default"}, "0.19.11": {"name": "@esbuild/linux-ia32", "version": "0.19.11", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_id": "@esbuild/linux-ia32@0.19.11", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-caHy++CsD8Bgq2V5CodbJjFPEiDPq8JJmBdeyZ8GWVQMjRD0sU548nNdwPNvKjVpamYYVL40AORekgfIubwHoA==", "shasum": "cbae1f313209affc74b80f4390c4c35c6ab83fa4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.11.tgz", "fileCount": 3, "unpackedSize": 9142744, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3ZBVhED4ojxKK5XsBoh6oKiGNPl9bq9ZcC8CjNiKbxQIhAKNrYDGguf6OaUFe+OukByQS6QGZ2zfLkmsxCsiq70CB"}], "size": 3764477}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.11_1703881946160_0.3679437741998868"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-29T20:32:26.493Z", "publish_time": 1703881946493, "_source_registry_name": "default"}, "0.19.12": {"name": "@esbuild/linux-ia32", "version": "0.19.12", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.19.12", "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-Thsa42rrP1+UIGaWz47uydHSBOgTUnwBwNq59khgIwktK6x60Hivfbux9iNR0eHCHzOLjLMLfUMLCypBkZXMHA==", "shasum": "d0d86b5ca1562523dc284a6723293a52d5860601", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.19.12.tgz", "fileCount": 3, "unpackedSize": 9142744, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBLL3QioSJaiodfE9nOwynzNlQMAoBAyVle8DN3wHqQfAiA/ttxN0Cg1rFvNBUVL4Pm5B/wEEdrX3aVveHqM2lN59g=="}], "size": 3822965}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.19.12_1706031650215_0.28679652942155154"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-23T17:40:50.429Z", "publish_time": 1706031650429, "_source_registry_name": "default"}, "0.20.0": {"name": "@esbuild/linux-ia32", "version": "0.20.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.20.0", "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-c88wwtfs8tTffPaoJ+SQn3y+lKtgTzyjkD8NgsyCtCmtoIC8RDL7PrJU05an/e9VuAke6eJqGkoMhJK1RY6z4w==", "shasum": "84ce7864f762708dcebc1b123898a397dea13624", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.20.0.tgz", "fileCount": 3, "unpackedSize": 9142787, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYbU56ZFxhYUYoNptilX1Ps6clYVDV6dtgoOpmg0uo+gIhALnN7agJ/A9aUZ+dH8SS/V8dr1Qa1OGcaY4qXaNTZO/O"}], "size": 3823575}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.20.0_1706374187642_0.8773968583871756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-27T16:49:47.894Z", "publish_time": 1706374187894, "_source_registry_name": "default"}, "0.20.1": {"name": "@esbuild/linux-ia32", "version": "0.20.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.20.1", "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-4H/sQCy1mnnGkUt/xszaLlYJVTz3W9ep52xEefGtd6yXDQbz/5fZE5dFLUgsPdbUOQANcVUa5iO6g3nyy5BJiw==", "shasum": "98c474e3e0cbb5bcbdd8561a6e65d18f5767ce48", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.20.1.tgz", "fileCount": 3, "unpackedSize": 9159171, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkBGRaPMJay1+HMsS4J4wWLHgz12Kpq43wxF7o4ykfkQIhAIroCcmzncT5tXaKr/tDfZdLEOAmimVgL65efsi4DvTz"}], "size": 3829046}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.20.1_1708324720472_0.4244221461732949"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-19T06:38:40.765Z", "publish_time": 1708324720765, "_source_registry_name": "default"}, "0.20.2": {"name": "@esbuild/linux-ia32", "version": "0.20.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.20.2", "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-o10utieEkNPFDZFQm9CoP7Tvb33UutoJqg3qKf1PWVeeJhJw0Q347PxMvBgVVFgouYLGIhFYG0UGdBumROyiig==", "shasum": "c0e5e787c285264e5dfc7a79f04b8b4eefdad7fa", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.20.2.tgz", "fileCount": 3, "unpackedSize": 9163267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRjmUV1REL2YB3I/6oQo1KrXJ0aSua0xUkGPBilFsTMgIgV01T+kaLg9GjN5RXYc7M4xy92oOubpFdbzh6WvIFAU4="}], "size": 3829843}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.20.2_1710445797522_0.8416695840776887"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-14T19:49:57.703Z", "publish_time": 1710445797703, "_source_registry_name": "default"}, "0.21.0": {"name": "@esbuild/linux-ia32", "version": "0.21.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.21.0", "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-7pVhVYBt3/R8x0Um9p4V8eMiQcnk6/IHkOo6tkfLnDqPn+NS6lnbfWysAYeDAqFKt6INQKtVxejh6ccbVYLBwQ==", "shasum": "e5c8b2efa017cf55608f27a8a78f9f2354f912e2", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.0.tgz", "fileCount": 3, "unpackedSize": 9236995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICcR3ahQX7V0BRATp8CrSsAcIalXSRXbJaXP5zWpIFZKAiAFrYaqcObSWK0By1cenuQa10/3+1NPFX5igevY8o7/FA=="}], "size": 3859381}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.21.0_1715050362162_0.7181195413247097"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T02:52:42.423Z", "publish_time": 1715050362423, "_source_registry_name": "default"}, "0.21.1": {"name": "@esbuild/linux-ia32", "version": "0.21.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.21.1", "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-tt/54LqNNAqCz++QhxoqB9+XqdsaZOtFD/srEhHYwBd3ZUOepmR1Eeot8bS+Q7BiEvy9vvKbtpHf+r6q8hF5UA==", "shasum": "8f2aef34a31c8d16dbce0b8679021f4881f38efe", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.1.tgz", "fileCount": 3, "unpackedSize": 9236995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDx79U2G+sfZe7zVES0K3T1daz22PWoTgSMt6egvaJ2GQIgZJQHvU7J3wt6m+0bDYYg+r5u3mXb/wTN0np2tdB7flE="}], "size": 3859188}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.21.1_1715100922772_0.23988942833228877"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T16:55:23.024Z", "publish_time": 1715100923024, "_source_registry_name": "default"}, "0.21.2": {"name": "@esbuild/linux-ia32", "version": "0.21.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.21.2", "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-m73BOCW2V9lcj7RtEMi+gBfHC6n3+VHpwQXP5offtQMPLDkpVolYn1YGXxOZ9hp4h3UPRKuezL7WkBsw+3EB3Q==", "shasum": "8ce387793eccdc28f5964e19f4dcbdb901099be4", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.2.tgz", "fileCount": 3, "unpackedSize": 9236995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICyD6+mhhsGz88KFucV5vm0UNxdBosXL15L37ITdthszAiEAyKCG2KsP4IvUQb93QpM/M1ESryHq5sPijM4b/7Sy82U="}], "size": 3859380}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.21.2_1715545988151_0.09849403265362722"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-12T20:33:08.406Z", "publish_time": 1715545988406, "_source_registry_name": "default"}, "0.21.3": {"name": "@esbuild/linux-ia32", "version": "0.21.3", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.21.3", "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-HjCWhH7K96Na+66TacDLJmOI9R8iDWDDiqe17C7znGvvE4sW1ECt9ly0AJ3dJH62jHyVqW9xpxZEU1jKdt+29A==", "shasum": "da713eb80ff6c011ed01aa4deebb5fc758906046", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.3.tgz", "fileCount": 3, "unpackedSize": 9232899, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBLuNeDqpdU4bdK28niR1aqziheupuUuStlw9CD3tivrAiBQEUdVBwkPmwLwALz9zgP2TgJffm8yPB09BEMENDKWQA=="}], "size": 3858142}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.21.3_1715806366328_0.13715470172202293"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T20:52:46.643Z", "publish_time": 1715806366643, "_source_registry_name": "default"}, "0.21.4": {"name": "@esbuild/linux-ia32", "version": "0.21.4", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.21.4", "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-pNftBl7m/tFG3t2m/tSjuYeWIffzwAZT9m08+9DPLizxVOsUl8DdFzn9HvJrTQwe3wvJnwTdl92AonY36w/25g==", "shasum": "c81b6f2ed3308d3b75ccefb5ac63bc4cf3a9d2e9", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.4.tgz", "fileCount": 3, "unpackedSize": 9245187, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsG28QAyTWo2Gtcaz+/iNIAHap133zR8+w71gRfPUZnQIhAONoGvPTSrQbzWOdl2ObvTPET4H8j3g3H3KedLq3C0Lm"}], "size": 3861350}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.21.4_1716603062547_0.6882102577672058"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-25T02:11:02.800Z", "publish_time": 1716603062800, "_source_registry_name": "default"}, "0.21.5": {"name": "@esbuild/linux-ia32", "version": "0.21.5", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=12"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.21.5", "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==", "shasum": "3271f53b3f93e3d093d518d1649d6d68d346ede2", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz", "fileCount": 3, "unpackedSize": 9249283, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdMm2s6IG2JKo6EWeZtcdCSUVWcqR79eF/t1wPXMe7CQIgKZ+o/z7gPMvWCc5JasUd1v5XoBMjT+LnmuJAFIfIriw="}], "size": 3862529}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.21.5_1717967834607_0.2687019633779184"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-09T21:17:14.861Z", "publish_time": 1717967834861, "_source_registry_name": "default"}, "0.22.0": {"name": "@esbuild/linux-ia32", "version": "0.22.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.22.0", "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-r2ZZqkOMOrpUhzNwxI7uLAHIDwkfeqmTnrv1cjpL/rjllPWszgqmprd/om9oviKXUBpMqHbXmppvjAYgISb26Q==", "shasum": "2d06d7b4abc443e05a820ff50d4c2d98cc04c22f", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.22.0.tgz", "fileCount": 3, "unpackedSize": 9450139, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFCURT4osZCkTS9j7wRAYlqdQmMsiY1CqHNEIELT64jnAiBUPCbGaa6cv3PgoXXldPp4etH4TdbfNDF7pn+U11yLOA=="}], "size": 3901507}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.22.0_1719779882542_0.11306163218555731"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T20:38:02.773Z", "publish_time": 1719779882773, "_source_registry_name": "default"}, "0.23.0": {"name": "@esbuild/linux-ia32", "version": "0.23.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.23.0", "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-P7O5Tkh2NbgIm2R6x1zGJJsnacDzTFcRWZyTTMgFdVit6E98LTxO+v8LCCLWRvPrjdzXHx9FEOA8oAZPyApWUA==", "shasum": "4269cd19cb2de5de03a7ccfc8855dde3d284a238", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.23.0.tgz", "fileCount": 3, "unpackedSize": 9450139, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEMcTx7JMs83ciKQYx2+Mf1jgfaQ2QvbN3g/kXQ8AxqIAiEAxIlextLD+e2h9kLNP5QOTGseiaK7VJizSYAqGGaaJL0="}], "size": 3901299}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.23.0_1719891239855_0.31963583996359635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T03:34:00.162Z", "publish_time": 1719891240162, "_source_registry_name": "default"}, "0.23.1": {"name": "@esbuild/linux-ia32", "version": "0.23.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.23.1", "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-VTN4EuOHwXEkXzX5nTvVY4s7E/Krz7COC8xkftbbKRYAl96vPiUssGkeMELQMOnLOJ8k3BY1+ZY52tttZnHcXQ==", "shasum": "3ed2273214178109741c09bd0687098a0243b333", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.23.1.tgz", "fileCount": 3, "unpackedSize": 9458331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqok2foF9f6Jp36InSM59h2eEnU4euIWoBN/UIPCmVxwIhANYlGORfBo+Zo3+IaCw86dFAq/q1OEL7v+1i3jOgakQr"}], "size": 3903730}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.23.1_1723846411627_0.6817775490339346"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-16T22:13:31.881Z", "publish_time": 1723846411881, "_source_registry_name": "default"}, "0.24.0": {"name": "@esbuild/linux-ia32", "version": "0.24.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.24.0", "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-K40ip1LAcA0byL05TbCQ4yJ4swvnbzHscRmUilrmP9Am7//0UjPreh4lpYzvThT2Quw66MhjG//20mrufm40mA==", "shasum": "8200b1110666c39ab316572324b7af63d82013fb", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.24.0.tgz", "fileCount": 3, "unpackedSize": 9589403, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJLREYLMQ7AFbOCN9kyu8yneVnYRhSe6G8+DaJBZr4fwIhAMzEvoNqrAXxrkbb7mGd5tRDdJdX4mJHfQ3D0uJo9Guy"}], "size": 4000172}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/linux-ia32_0.24.0_1726970800389_0.30893472733893423"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-22T02:06:40.665Z", "publish_time": 1726970800665, "_source_registry_name": "default"}, "0.24.1": {"name": "@esbuild/linux-ia32", "version": "0.24.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.24.1", "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-TwspFcPJpYyBqDcoqSLBBaoGRGiPWkjH5V4raiFQ6maAkBho/rfQvtVpNPkLHEwnPlVSdl4HkHZ3n7NvvtU10w==", "shasum": "9ab1765b837e53acbe1799c784e17996506ed1e6", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.24.1.tgz", "fileCount": 3, "unpackedSize": 9597595, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbDwBnbLyK5VIBDuYakSriJG0zW+KtTffbl1Pah04owAiARC2cjY2Byzf5RGk0ZgHvMf4dxJxrdaJk+woymvvFRyw=="}], "size": 4003050}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.24.1_1734673294575_0.18934043358183472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T05:41:34.866Z", "publish_time": 1734673294866, "_source_registry_name": "default"}, "0.24.2": {"name": "@esbuild/linux-ia32", "version": "0.24.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.24.2", "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-sfv0tGPQhcZOgTKO3oBE9xpHuUqguHvSo4jl+wjnKwFpapx+vUDcawbwPNuBIAYdRAvIDBfZVvXprIj3HA+Ugw==", "shasum": "f8a16615a78826ccbb6566fab9a9606cfd4a37d5", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.24.2.tgz", "fileCount": 3, "unpackedSize": 9597595, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCslIW6ZDY5hFV/jdnbT8xdDRkKvFS8mfEf+792ngP3nQIgXMDjHyCPOYooSUKPZBsw399AvAdzzbTGI0RPA1jQ9dk="}], "size": 4003099}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.24.2_1734717410613_0.6461173820386821"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T17:56:50.883Z", "publish_time": 1734717410883, "_source_registry_name": "default"}, "0.25.0": {"name": "@esbuild/linux-ia32", "version": "0.25.0", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.0", "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.0.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg==", "shasum": "3e0736fcfab16cff042dec806247e2c76e109e19", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.0.tgz", "fileCount": 3, "unpackedSize": 9654939, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCnTu1C/61mWTr/XIio/dgGZ72WoC2g5ffxw9zfi2OEWwIgQX3XCsY/woCJXbL8S8T+gVhC1HV6e3Nw+RGc1TUGumM="}], "size": 4027584}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.0_1738983758568_0.7637639127622773"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-08T03:02:38.856Z", "publish_time": 1738983758856, "_source_registry_name": "default"}, "0.25.1": {"name": "@esbuild/linux-ia32", "version": "0.25.1", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.1", "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-OJykPaF4v8JidKNGz8c/q1lBO44sQNUQtq1KktJXdBLn1hPod5rE/Hko5ugKKZd+D2+o1a9MFGUEIUwO2YfgkQ==", "shasum": "6f9527077ccb7953ed2af02e013d4bac69f13754", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.1.tgz", "fileCount": 3, "unpackedSize": 9654939, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGAiQ1NeuCyCcBSTdfWxFtRcaHV4goEhM1f/QSIX8qYGAiAXYb9/72r5uzFzX5Q8TnXZ352rJapbuQ5V28U65oWaCA=="}], "size": 4028766}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.1_1741578350320_0.3350417904996461"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-10T03:45:50.594Z", "publish_time": 1741578350594, "_source_registry_name": "default"}, "0.25.2": {"name": "@esbuild/linux-ia32", "version": "0.25.2", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.2", "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-bBYCv9obgW2cBP+2ZWfjYTU+f5cxRoGGQ5SeDbYdFCAZpYWrfjjfYwvUpP8MlKbP0nwZ5gyOU/0aUzZ5HWPuvQ==", "shasum": "337a87a4c4dd48a832baed5cbb022be20809d737", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.2.tgz", "fileCount": 3, "unpackedSize": 9659035, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG1FPNS3QdBVHqdCX/IiKOzC/wdXRfg3MvsHbeRgmURvAiAQZziJ2na/Gl3B7Iu+PFxQ/u9PeSX+xFxoLcvYNW/doQ=="}], "size": 4030489}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.2_1743355994878_0.8482534805799977"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-30T17:33:15.111Z", "publish_time": 1743355995111, "_source_registry_name": "default"}, "0.25.3": {"name": "@esbuild/linux-ia32", "version": "0.25.3", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.3", "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-yplPOpczHOO4jTYKmuYuANI3WhvIPSVANGcNUeMlxH4twz/TeXuzEP41tGKNGWJjuMhotpGabeFYGAOU2ummBw==", "shasum": "81025732d85b68ee510161b94acdf7e3007ea177", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.3.tgz", "fileCount": 3, "unpackedSize": 9667227, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG6r39J0iYR7dx2qhFHIwizSbNMMimdrCSjvQnI8Gg82AiAwNadeUk3KD1jn9TaT+suRTEBUIhDSjJOUDMWPQTOfOA=="}], "size": 4032825}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.3_1745380587641_0.1243279622867981"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-23T03:56:27.886Z", "publish_time": 1745380587886, "_source_registry_name": "default"}, "0.25.4": {"name": "@esbuild/linux-ia32", "version": "0.25.4", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.4", "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-yTEjoapy8UP3rv8dB0ip3AfMpRbyhSN3+hY8mo/i4QXFeDxmiYbEKp3ZRjBKcOP862Ua4b1PDfwlvbuwY7hIGQ==", "shasum": "be8ef2c3e1d99fca2d25c416b297d00360623596", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.4.tgz", "fileCount": 3, "unpackedSize": 9671323, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDKaqrj5mMK5oF6pr2H6y6C5qhO6RC+ActchVkNY34GfQIhAI2rAzOeBD6+5i6kVrwukIRYyjk3l3bws3M+eBM0PZ4/"}], "size": 4034283}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.4_1746491463473_0.7742780389452231"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T00:31:03.764Z", "publish_time": 1746491463764, "_source_registry_name": "default"}, "0.25.5": {"name": "@esbuild/linux-ia32", "version": "0.25.5", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==", "shasum": "763414463cd9ea6fa1f96555d2762f9f84c61783", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9671323, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIC9kcW5bZFF62sJ+A3WwDYXG/nAjOQQjQRJm7MBmZQ10AiEAhZ2CGK388wKyXbMZJ6IS3ILJw2E1LjCR05VWAUCxEJ4="}], "size": 4035382}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.5_1748315590513_0.4490662461400534"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-27T03:13:10.790Z", "publish_time": 1748315590790, "_source_registry_name": "default"}, "0.25.6": {"name": "@esbuild/linux-ia32", "version": "0.25.6", "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["ia32"], "_id": "@esbuild/linux-ia32@0.25.6", "gitHead": "d38c1f0bc580b4a8a93f23559d0cd9085d7ba31f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-aHWdQ2AAltRkLPOsKdi3xv0mZ8fUGPdlKEjIEhxCPm5yKEThcUjHpWB1idN74lfXGnZ5SULQSgtr5Qos5B0bPw==", "shasum": "****************************************", "tarball": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.6.tgz", "fileCount": 3, "unpackedSize": 9679515, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDNnm75Zcym6JcR1RdLoWw5NEbaQAKM0otpK+q9O9VtzwIgbOntWDOGQY1oDgKSPAtsZyscNc1WqEjy9GkhmnjR5D0="}], "size": 4037776}, "_npmUser": {"name": "evanw", "email": "<EMAIL>", "actor": {"name": "evanw", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-ia32_0.25.6_1751907560706_0.8959167302588398"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T16:59:20.921Z", "publish_time": 1751907560921, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "description": "The Linux 32-bit binary for esbuild, a JavaScript bundler.", "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the Linux 32-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "_source_registry_name": "default"}