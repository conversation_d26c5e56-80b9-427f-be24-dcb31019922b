'Log files:
D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_41_493Z-debug-0.log

# npm resolution error report

While resolving: @vue/eslint-config-typescript@7.0.0
Found: eslint-plugin-vue@8.7.1
node_modules/eslint-plugin-vue
  dev eslint-plugin-vue@"^8.0.3" from the root project

Could not resolve dependency:
peer eslint-plugin-vue@"^5.2.3 || ^6.0.0 || ^7.0.0" from @vue/eslint-config-typescript@7.0.0
node_modules/@vue/eslint-config-typescript
  dev @vue/eslint-config-typescript@"^7.0.0" from the root project

Conflicting peer dependency: eslint-plugin-vue@7.20.0
node_modules/eslint-plugin-vue
  peer eslint-plugin-vue@"^5.2.3 || ^6.0.0 || ^7.0.0" from @vue/eslint-config-typescript@7.0.0
  node_modules/@vue/eslint-config-typescript
    dev @vue/eslint-config-typescript@"^7.0.0" from the root project

Fix the upstream dependency conflict, or retry
this command with --force or --legacy-peer-deps
to accept an incorrect (and potentially broken) dependency resolution.
