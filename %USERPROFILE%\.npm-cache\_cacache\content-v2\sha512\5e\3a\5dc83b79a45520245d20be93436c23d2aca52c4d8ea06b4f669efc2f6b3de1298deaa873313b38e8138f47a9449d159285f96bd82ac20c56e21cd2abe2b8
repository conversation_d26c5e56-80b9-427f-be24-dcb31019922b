{"_attachments": {}, "_id": "@babel/preset-typescript", "_rev": "3991-61f14e7a4ce7cf8f58270451", "author": "The Babel Team (https://babel.dev/team)", "description": "Babel preset for TypeScript.", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "license": "MIT", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "name": "@babel/preset-typescript", "readme": "# @babel/preset-typescript\n\n> Babel preset for TypeScript.\n\nSee our website [@babel/preset-typescript](https://babeljs.io/docs/babel-preset-typescript) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen) associated with this package.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/preset-typescript\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/preset-typescript --dev\n```\n", "time": {"created": "2022-01-26T13:36:58.446Z", "modified": "2025-07-02T09:35:19.755Z", "7.16.5": "2021-12-13T22:28:36.427Z", "7.16.0": "2021-10-29T23:48:02.730Z", "7.15.0": "2021-08-04T21:13:18.347Z", "7.14.5": "2021-06-09T23:13:29.171Z", "7.13.0": "2021-02-22T22:50:37.862Z", "7.12.17": "2021-02-18T15:13:55.630Z", "7.12.16": "2021-02-11T22:47:17.931Z", "7.12.13": "2021-02-03T01:12:22.760Z", "7.12.7": "2020-11-20T21:05:42.681Z", "7.12.1": "2020-10-15T22:42:18.500Z", "7.12.0": "2020-10-14T20:03:38.015Z", "7.10.4": "2020-06-30T13:13:42.519Z", "7.10.1": "2020-05-27T22:08:42.152Z", "7.9.0": "2020-03-20T15:39:49.334Z", "7.8.3": "2020-01-13T21:42:52.101Z", "7.8.0": "2020-01-12T00:17:45.539Z", "7.7.7": "2019-12-19T00:53:06.610Z", "7.7.4": "2019-11-22T23:34:03.925Z", "7.7.2": "2019-11-06T23:27:30.548Z", "7.7.0": "2019-11-05T10:54:13.698Z", "7.6.0": "2019-09-06T17:33:53.484Z", "7.3.3": "2019-02-15T21:14:34.673Z", "7.1.0": "2018-09-17T19:30:29.140Z", "7.0.0": "2018-08-27T21:44:34.201Z", "7.0.0-rc.4": "2018-08-27T16:46:04.829Z", "7.0.0-rc.3": "2018-08-24T18:09:21.243Z", "7.0.0-rc.2": "2018-08-21T19:25:40.006Z", "7.0.0-rc.1": "2018-08-09T20:09:35.214Z", "7.0.0-rc.0": "2018-08-09T15:59:38.363Z", "7.0.0-beta.56": "2018-08-04T01:08:05.819Z", "7.0.0-beta.55": "2018-07-28T22:07:48.976Z", "7.0.0-beta.54": "2018-07-16T18:00:23.682Z", "7.0.0-beta.53": "2018-07-11T13:40:41.950Z", "7.0.0-beta.52": "2018-07-06T00:59:42.716Z", "7.0.0-beta.51": "2018-06-12T21:20:33.350Z", "7.0.0-beta.50": "2018-06-12T19:47:51.739Z", "7.0.0-beta.49": "2018-05-25T16:03:58.198Z", "7.0.0-beta.48": "2018-05-24T19:24:25.631Z", "7.0.0-beta.47": "2018-05-15T00:17:27.943Z", "7.0.0-beta.46": "2018-04-23T04:32:28.958Z", "7.0.0-beta.45": "2018-04-23T01:58:11.051Z", "7.0.0-beta.44": "2018-04-02T22:20:29.731Z", "7.0.0-beta.43": "2018-04-02T16:48:50.326Z", "7.0.0-beta.42": "2018-03-15T20:51:55.671Z", "7.0.0-beta.41": "2018-03-14T16:26:41.480Z", "7.0.0-beta.40": "2018-02-12T16:42:24.789Z", "7.0.0-beta.39": "2018-01-30T20:28:39.164Z", "7.0.0-beta.38": "2018-01-17T16:32:32.464Z", "7.0.0-beta.37": "2018-01-08T16:03:34.543Z", "7.0.0-beta.36": "2017-12-25T19:05:27.003Z", "7.0.0-beta.35": "2017-12-14T21:48:19.565Z", "7.0.0-beta.34": "2017-12-02T14:40:05.572Z", "7.0.0-beta.33": "2017-12-01T14:29:08.687Z", "7.0.0-beta.32": "2017-11-12T13:33:52.134Z", "7.0.0-beta.31": "2017-11-03T20:04:10.616Z", "7.0.0-beta.5": "2017-10-30T20:57:26.512Z", "7.0.0-beta.4": "2017-10-30T18:35:47.570Z", "7.16.7": "2021-12-31T00:23:19.631Z", "7.17.12": "2022-05-16T19:33:14.378Z", "7.18.6": "2022-06-27T19:50:53.878Z", "7.21.0": "2023-02-20T15:31:22.789Z", "7.21.4": "2023-03-31T09:01:56.939Z", "7.21.4-esm": "2023-04-04T14:10:04.662Z", "7.21.4-esm.1": "2023-04-04T14:22:02.173Z", "7.21.4-esm.2": "2023-04-04T14:40:08.038Z", "7.21.4-esm.3": "2023-04-04T14:56:50.079Z", "7.21.4-esm.4": "2023-04-04T15:13:57.793Z", "7.21.5": "2023-04-28T19:50:31.699Z", "7.22.5": "2023-06-08T18:21:51.693Z", "8.0.0-alpha.0": "2023-07-20T14:00:34.955Z", "8.0.0-alpha.1": "2023-07-24T17:53:10.215Z", "8.0.0-alpha.2": "2023-08-09T15:15:29.965Z", "7.22.11": "2023-08-24T13:08:53.023Z", "7.22.15": "2023-09-04T12:25:28.839Z", "7.23.0": "2023-09-25T08:11:48.599Z", "8.0.0-alpha.3": "2023-09-26T14:57:42.151Z", "7.23.2": "2023-10-11T18:51:23.785Z", "8.0.0-alpha.4": "2023-10-12T02:06:56.226Z", "7.23.3": "2023-11-09T07:04:16.375Z", "8.0.0-alpha.5": "2023-12-11T15:19:58.436Z", "8.0.0-alpha.6": "2024-01-26T16:14:56.824Z", "8.0.0-alpha.7": "2024-02-28T14:05:53.958Z", "7.24.1": "2024-03-19T09:49:41.778Z", "8.0.0-alpha.8": "2024-04-04T13:20:25.626Z", "7.24.6": "2024-05-24T12:25:16.067Z", "8.0.0-alpha.9": "2024-06-03T14:05:57.125Z", "8.0.0-alpha.10": "2024-06-04T11:20:50.019Z", "7.24.7": "2024-06-05T13:16:03.023Z", "8.0.0-alpha.11": "2024-06-07T09:16:13.411Z", "8.0.0-alpha.12": "2024-07-26T17:34:08.784Z", "7.25.7": "2024-10-02T15:15:40.309Z", "7.25.9": "2024-10-22T15:21:55.394Z", "7.26.0": "2024-10-25T13:30:09.805Z", "8.0.0-alpha.13": "2024-10-25T13:54:55.697Z", "8.0.0-alpha.14": "2024-12-06T16:54:44.599Z", "8.0.0-alpha.15": "2025-01-10T17:25:15.045Z", "8.0.0-alpha.16": "2025-02-14T11:59:48.446Z", "8.0.0-alpha.17": "2025-03-11T18:25:43.043Z", "7.27.0": "2025-03-24T17:41:57.077Z", "7.27.1": "2025-04-30T15:09:37.243Z", "8.0.0-beta.0": "2025-05-30T15:51:53.998Z", "8.0.0-beta.1": "2025-07-02T09:04:55.469Z"}, "versions": {"7.16.5": {"name": "@babel/preset-typescript", "version": "7.16.5", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.16.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/preset-typescript@7.16.5", "dist": {"shasum": "b86a5b0ae739ba741347d2f58c52f52e63cf1ba1", "integrity": "sha512-lmAWRoJ9iOSvs3DqOndQpj8XqXkzaiQs50VG/zESiI9D3eoZhGriU675xNCr0UwvsuXrhMAGvyk1w+EVWF3u8Q==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.16.5.tgz", "fileCount": 5, "unpackedSize": 13635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kUCRA9TVsSAnZWagAAbz8P/1Zc3n08mkt41qpE8oia\nYDqKxb5hZkaASbtaV9xENhT/0pIJwzv58t0G22v7qbxgrdb+nSHjY3JAY5w6\nq+46EHSu3UPC7VevQJan+fbpkIlABpPwMXWWWwIBHHPbOR23NvTPGiHBfoE1\nXx4XwAoBqdflg799X2JTQaekFRDXLJ45Vp+v2stU/TRbzeWV/4zJ2Ycmcky5\n/xcQgTpj3fdk2sRq6A1YpZav+mpZBDHOq0y8Q1ivjQ4OZ1JKZ1inOZSIc45k\nt/WFxLdD4fKpZNpG08UGhAyNUY/vcMOaZRFW6MMS9ruDCzy/Hsa0G5idsN2o\nfXXLWoTX2e6sYs/GnVJ1IExCEwWVEeYiqpRs+KIJmKUEjJbS/4LXKorSDGbD\nX+HWJqrkzIpAjhpjiCZmb97M4mJVVT4z6o3ZC27EU4JfEkehk8kS2UNsND+2\nI0LhtwjJAtYBhgJJ22it5whZkeeSN4HmjxsIpvM/Og5tegArERMNqSNiVyGj\n021RQdCmi6Vcw1X9YN4zqeMKLvpGkm6F3m9mAbAcsHhApXBn+ehMmTCoWmMd\njunfLtcr9Cw+90ioLeznZ9XtnT4ALSFvA07Oh+sb8s9qFvUnbDb4PaPVkKBs\nBpgQVWr8KuvBCqbDGjn/toaB2E3QHx3IrFNuV6w5R8pmh2/fZdFudzdYxEaE\n1N0P\r\n=u8hU\r\n-----END PGP SIGNATURE-----\r\n", "size": 4150, "noattachment": false}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.16.5_1639434516306_0.3434602422305786"}, "_hasShrinkwrap": false, "publish_time": 1639434516427, "_cnpm_publish_time": 1639434516427, "_cnpmcore_publish_time": "2021-12-14T05:22:41.360Z"}, "7.16.0": {"name": "@babel/preset-typescript", "version": "7.16.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/preset-typescript@7.16.0", "dist": {"shasum": "b0b4f105b855fb3d631ec036cdc9d1ffd1fa5eac", "size": 4139, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.16.0.tgz", "integrity": "sha512-txegdrZYgO9DlPbv+9QOVpMnKbOtezsLHWsnsRF4AjbSIsVaujrq1qg8HK0mxQpWv0jnejt0yEoW1uWpvbrDTg=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.16.0_1635551282593_0.5269271714156214"}, "_hasShrinkwrap": false, "publish_time": 1635551282730, "_cnpm_publish_time": 1635551282730, "_cnpmcore_publish_time": "2021-12-14T05:22:41.580Z"}, "7.15.0": {"name": "@babel/preset-typescript", "version": "7.15.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.15.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.15.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/preset-typescript@7.15.0", "dist": {"shasum": "e8fca638a1a0f64f14e1119f7fe4500277840945", "size": 3718, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.15.0.tgz", "integrity": "sha512-lt0Y/8V3y06Wq/8H/u0WakrqciZ7Fz7mwPDHWUJAXlABL5hiUG42BNlRXiELNjeWjO5rWmnNKlx+yzJvxezHow=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.15.0_1628111598183_0.7116029171714056"}, "_hasShrinkwrap": false, "publish_time": 1628111598347, "_cnpm_publish_time": 1628111598347, "_cnpmcore_publish_time": "2021-12-14T05:22:41.758Z"}, "7.14.5": {"name": "@babel/preset-typescript", "version": "7.14.5", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/preset-typescript@7.14.5", "dist": {"shasum": "aa98de119cf9852b79511f19e7f44a2d379bcce0", "size": 3442, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.14.5.tgz", "integrity": "sha512-u4zO6CdbRKbS9TypMqrlGH7sd2TAJppZwn3c/ZRLeO/wGsbddxgbPDUZVNrie3JWYLQ9vpineKlsrWFvO6Pwkw=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.14.5_1623280409052_0.05052902749423538"}, "_hasShrinkwrap": false, "publish_time": 1623280409171, "_cnpm_publish_time": 1623280409171, "_cnpmcore_publish_time": "2021-12-14T05:22:42.001Z"}, "7.13.0": {"name": "@babel/preset-typescript", "version": "7.13.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-validator-option": "^7.12.17", "@babel/plugin-transform-typescript": "^7.13.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "_id": "@babel/preset-typescript@7.13.0", "dist": {"shasum": "ab107e5f050609d806fbb039bec553b33462c60a", "size": 3409, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.13.0.tgz", "integrity": "sha512-LXJwxrHy0N3f6gIJlYbLta1D9BDtHpQeqwzM0LIfjDlr6UE/D5Mc7W4iDiQzaE+ks0sTjT26ArcHWnJVt0QiHw=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.13.0_1614034237739_0.8330901389307077"}, "_hasShrinkwrap": false, "publish_time": 1614034237862, "_cnpm_publish_time": 1614034237862, "_cnpmcore_publish_time": "2021-12-14T05:22:42.198Z"}, "7.12.17": {"name": "@babel/preset-typescript", "version": "7.12.17", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-validator-option": "^7.12.17", "@babel/plugin-transform-typescript": "^7.12.17"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "_id": "@babel/preset-typescript@7.12.17", "dist": {"shasum": "8ecf04618956c268359dd9feab775dc14a666eb5", "size": 3390, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.12.17.tgz", "integrity": "sha512-T513uT4VSThRcmWeqcLkITKJ1oGQho9wfWuhQm10paClQkp1qyd0Wf8mvC8Se7UYssMyRSj4tZYpVTkCmAK/mA=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.12.17_1613661235454_0.3120719444726794"}, "_hasShrinkwrap": false, "publish_time": 1613661235630, "_cnpm_publish_time": 1613661235630, "_cnpmcore_publish_time": "2021-12-14T05:22:42.392Z"}, "7.12.16": {"name": "@babel/preset-typescript", "version": "7.12.16", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-validator-option": "^7.12.16", "@babel/plugin-transform-typescript": "^7.12.16"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "_id": "@babel/preset-typescript@7.12.16", "dist": {"shasum": "b2080ce20b7095c049db2a0410f1e39bc892f7ca", "size": 2720, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.12.16.tgz", "integrity": "sha512-IrYNrpDSuQfNHeqh7gsJsO35xTGyAyGkI1VxOpBEADFtxCqZ77a1RHbJqM3YJhroj7qMkNMkNtcw0lqeZUrzow=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.12.16_1613083637804_0.8259833651476651"}, "_hasShrinkwrap": false, "publish_time": 1613083637931, "_cnpm_publish_time": 1613083637931, "_cnpmcore_publish_time": "2021-12-14T05:22:42.589Z"}, "7.12.13": {"name": "@babel/preset-typescript", "version": "7.12.13", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-validator-option": "^7.12.11", "@babel/plugin-transform-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "_id": "@babel/preset-typescript@7.12.13", "dist": {"shasum": "c859c7c075c531d2cc34c2516b214e5d884efe5c", "size": 3387, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.12.13.tgz", "integrity": "sha512-gYry7CeXwD2wtw5qHzrtzKaShEhOfTmKb4i0ZxeYBcBosN5VuAudsNbjX7Oj5EAfQ3K4s4HsVMQRRcqGsPvs2A=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.12.13_1612314742649_0.8758887544685747"}, "_hasShrinkwrap": false, "publish_time": 1612314742760, "_cnpm_publish_time": 1612314742760, "_cnpmcore_publish_time": "2021-12-14T05:22:42.798Z"}, "7.12.7": {"name": "@babel/preset-typescript", "version": "7.12.7", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-validator-option": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.7", "@babel/helper-plugin-test-runner": "7.10.4"}, "_id": "@babel/preset-typescript@7.12.7", "dist": {"shasum": "fc7df8199d6aae747896f1e6c61fc872056632a3", "size": 1928, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.12.7.tgz", "integrity": "sha512-nOoIqIqBmHBSEgBXWR4Dv/XBehtIFcw9PqZw6rFYuKrzsZmOQm3PR5siLBnKZFEsDb03IegG8nSjU/iXXXYRmw=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.12.7_1605906342548_0.16027846932673606"}, "_hasShrinkwrap": false, "publish_time": 1605906342681, "_cnpm_publish_time": 1605906342681, "_cnpmcore_publish_time": "2021-12-14T05:22:43.021Z"}, "7.12.1": {"name": "@babel/preset-typescript", "version": "7.12.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "_id": "@babel/preset-typescript@7.12.1", "dist": {"shasum": "86480b483bb97f75036e8864fe404cc782cc311b", "size": 1913, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.12.1.tgz", "integrity": "sha512-hNK/DhmoJPsksdHuI/RVrcEws7GN5eamhi28JkO52MqIxU8Z0QpmiSOQxZHWOHV7I3P4UjHV97ay4TcamMA6Kw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.12.1_1602801738307_0.9789432890667651"}, "_hasShrinkwrap": false, "publish_time": 1602801738500, "_cnpm_publish_time": 1602801738500, "_cnpmcore_publish_time": "2021-12-14T05:22:43.196Z"}, "7.12.0": {"name": "@babel/preset-typescript", "version": "7.12.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.12.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "_id": "@babel/preset-typescript@7.12.0", "dist": {"shasum": "3fe6b4958ca6fb0e086dc5574a27d0ced99185e8", "size": 1908, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.12.0.tgz", "integrity": "sha512-2XVy4sy/zkP4gqmXW0TzSh/QwOniN2Cy3srhsD0TRBlMTOmjaYnWCWA6aWopwpcwfYkEKD6jKLLjYMq15zDNWg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.12.0_1602705817821_0.8264422405008944"}, "_hasShrinkwrap": false, "publish_time": 1602705818015, "_cnpm_publish_time": 1602705818015, "_cnpmcore_publish_time": "2021-12-14T05:22:43.465Z"}, "7.10.4": {"name": "@babel/preset-typescript", "version": "7.10.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/preset-typescript@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"shasum": "7d5d052e52a682480d6e2cc5aa31be61c8c25e36", "size": 1907, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.10.4.tgz", "integrity": "sha512-SdYnvGPv+bLlwkF2VkJnaX/ni1sMNetcGI1+nThF1gyv6Ph8Qucc4ZZAjM5yZcE/AKRXIOTZz7eSRDWOEjPyRQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.10.4_1593522822414_0.8702709810945155"}, "_hasShrinkwrap": false, "publish_time": 1593522822519, "_cnpm_publish_time": 1593522822519, "_cnpmcore_publish_time": "2021-12-14T05:22:43.660Z"}, "7.10.1": {"name": "@babel/preset-typescript", "version": "7.10.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/plugin-transform-typescript": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/preset-typescript@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"shasum": "a8d8d9035f55b7d99a2461a0bdc506582914d07e", "size": 1907, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.10.1.tgz", "integrity": "sha512-m6GV3y1ShiqxnyQj10600ZVOFrSSAa8HQ3qIUk2r+gcGtHTIRw0dJnFLt1WNXpKjtVw7yw1DAPU/6ma2ZvgJuA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.10.1_1590617321998_0.5237821644615872"}, "_hasShrinkwrap": false, "publish_time": 1590617322152, "_cnpm_publish_time": 1590617322152, "_cnpmcore_publish_time": "2021-12-14T05:22:43.882Z"}, "7.9.0": {"name": "@babel/preset-typescript", "version": "7.9.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-typescript": "^7.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_id": "@babel/preset-typescript@7.9.0", "_nodeVersion": "13.11.0", "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "dist": {"shasum": "87705a72b1f0d59df21c179f7c3d2ef4b16ce192", "size": 1900, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.9.0.tgz", "integrity": "sha512-S4cueFnGrIbvYJgwsVFKdvOmpiL0XGw9MFW9D0vgRys5g36PBhZRL8NX8Gr2akz8XRtzq6HuDXPD/1nniagNUg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.9.0_1584718789179_0.642135633019596"}, "_hasShrinkwrap": false, "publish_time": 1584718789334, "_cnpm_publish_time": 1584718789334, "_cnpmcore_publish_time": "2021-12-14T05:22:44.057Z"}, "7.8.3": {"name": "@babel/preset-typescript", "version": "7.8.3", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/preset-typescript@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"shasum": "90af8690121beecd9a75d0cc26c6be39d1595d13", "size": 1880, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.8.3.tgz", "integrity": "sha512-qee5LgPGui9zQ0jR1TeU5/fP9L+ovoArklEqY12ek8P/wV5ZeM/VYSQYwICeoT6FfpJTekG9Ilay5PhwsOpMHA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.8.3_1578951771937_0.4422081764724508"}, "_hasShrinkwrap": false, "publish_time": 1578951772101, "_cnpm_publish_time": 1578951772101, "_cnpmcore_publish_time": "2021-12-14T05:22:44.247Z"}, "7.8.0": {"name": "@babel/preset-typescript", "version": "7.8.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/plugin-transform-typescript": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/preset-typescript@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"shasum": "f3bcb241e530e5acd424659e641189f06401a7ad", "size": 1890, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.8.0.tgz", "integrity": "sha512-mvu4OmrLK6qRPiXlOkE4yOeOszHzk9itwe6aiMN0RL9Bc5uAwAotVTy4kKl17evLMd1WsvWT1O3mZltynuqxXg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.8.0_1578788265405_0.538370387685978"}, "_hasShrinkwrap": false, "publish_time": 1578788265539, "_cnpm_publish_time": 1578788265539, "_cnpmcore_publish_time": "2021-12-14T05:22:44.460Z"}, "7.7.7": {"name": "@babel/preset-typescript", "version": "7.7.7", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "12da0941c898987ae30045a9da90ed5bf58ecaf9", "_id": "@babel/preset-typescript@7.7.7", "_nodeVersion": "13.4.0", "_npmVersion": "lerna/3.19.0/node@v13.4.0+x64 (linux)", "dist": {"shasum": "69ddea54e8b4e491ccbf94147e673b2ac6e11e2e", "size": 1879, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.7.7.tgz", "integrity": "sha512-Apg0sCTovsSA+pEaI8efnA44b9x4X/7z4P8vsWMiN8rSUaM4y4+Shl5NMWnMl6njvt96+CEb6jwpXAKYAVCSQA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.7.7_1576716786509_0.04001177308412607"}, "_hasShrinkwrap": false, "publish_time": 1576716786610, "_cnpm_publish_time": 1576716786610, "_cnpmcore_publish_time": "2021-12-14T05:22:44.632Z"}, "7.7.4": {"name": "@babel/preset-typescript", "version": "7.7.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/preset-typescript@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"shasum": "780059a78e6fa7f7a4c87f027292a86b31ce080a", "size": 2949, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.7.4.tgz", "integrity": "sha512-rqrjxfdiHPsnuPur0jKrIIGQCIgoTWMTjlbWE69G4QJ6TIOVnnRnIJhUxNTL/VwDmEAVX08Tq3B1nirer5341w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.7.4_1574465643803_0.4958011153225723"}, "_hasShrinkwrap": false, "publish_time": 1574465643925, "_cnpm_publish_time": 1574465643925, "_cnpmcore_publish_time": "2021-12-14T05:22:44.814Z"}, "7.7.2": {"name": "@babel/preset-typescript", "version": "7.7.2", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_id": "@babel/preset-typescript@7.7.2", "_nodeVersion": "13.0.1", "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "dist": {"shasum": "f71c8bba2ae02f11b29dbf7d6a35f47bbe011632", "size": 2949, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.7.2.tgz", "integrity": "sha512-1B4HthAelaLGfNRyrWqJtBEjXX1ulThCrLQ5B2VOtEAznWFIFXFJahgXImqppy66lx/Oh+cOSCQdJzZqh2Jh5g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.7.2_1573082850422_0.6649097627600886"}, "_hasShrinkwrap": false, "publish_time": 1573082850548, "_cnpm_publish_time": 1573082850548, "_cnpmcore_publish_time": "2021-12-14T05:22:45.014Z"}, "7.7.0": {"name": "@babel/preset-typescript", "version": "7.7.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.7.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_id": "@babel/preset-typescript@7.7.0", "_nodeVersion": "13.0.1", "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "dist": {"shasum": "5d7682d938160ceaf51c3d4239e9521ef893474c", "size": 2950, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.7.0.tgz", "integrity": "sha512-WZ3qvtAJy8w/i6wqq5PuDnkCUXaLUTHIlJujfGHmHxsT5veAbEdEjl3cC/3nXfyD0bzlWsIiMdUhZgrXjd9QWg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.7.0_1572951253509_0.25708117913142337"}, "_hasShrinkwrap": false, "publish_time": 1572951253698, "_cnpm_publish_time": 1572951253698, "_cnpmcore_publish_time": "2021-12-14T05:22:45.207Z"}, "7.6.0": {"name": "@babel/preset-typescript", "version": "7.6.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.6.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_id": "@babel/preset-typescript@7.6.0", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "dist": {"shasum": "25768cb8830280baf47c45ab1a519a9977498c98", "size": 2980, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.6.0.tgz", "integrity": "sha512-4xKw3tTcCm0qApyT6PqM9qniseCE79xGHiUnNdKGdxNsGUc2X7WwZybqIpnTmoukg3nhPceI5KPNzNqLNeIJww=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.6.0_1567791233365_0.4542017978934523"}, "_hasShrinkwrap": false, "publish_time": 1567791233484, "_cnpm_publish_time": 1567791233484, "_cnpmcore_publish_time": "2021-12-14T05:22:45.428Z"}, "7.3.3": {"name": "@babel/preset-typescript", "version": "7.3.3", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.3.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.3.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/preset-typescript@7.3.3", "dist": {"shasum": "88669911053fa16b2b276ea2ede2ca603b3f307a", "size": 2910, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.3.3.tgz", "integrity": "sha512-mzMVuIP4lqtn4du2ynEfdO0+RYcslwrZiJHXu4MGaC1ctJiW2fyaeDrtjJGs7R/KebZ1sgowcIoWf4uRpEfKEg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.3.3_1550265274547_0.4836570815753152"}, "_hasShrinkwrap": false, "publish_time": 1550265274673, "_cnpm_publish_time": 1550265274673, "_cnpmcore_publish_time": "2021-12-14T05:22:45.605Z"}, "7.1.0": {"name": "@babel/preset-typescript", "version": "7.1.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/preset-typescript@7.1.0", "dist": {"shasum": "49ad6e2084ff0bfb5f1f7fb3b5e76c434d442c7f", "size": 3151, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.1.0.tgz", "integrity": "sha512-LYveByuF9AOM8WrsNne5+N79k1YxjNB6gmpCQsnuSBAcV8QUeB+ZUxQzL7Rz7HksPbahymKkq2qBR+o36ggFZA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.1.0_1537212629028_0.5992624590798317"}, "_hasShrinkwrap": false, "publish_time": 1537212629140, "_cnpm_publish_time": 1537212629140, "_cnpmcore_publish_time": "2021-12-14T05:22:45.793Z"}, "7.0.0": {"name": "@babel/preset-typescript", "version": "7.0.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-typescript": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "readmeFilename": "README.md", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/preset-typescript@7.0.0", "dist": {"shasum": "1e65c8b863ff5b290f070d999c810bb48a8e3904", "size": 3111, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0.tgz", "integrity": "sha512-rFq0bsJjXJo+PyRyBeHDIUGD7+4btHzYcNbL8kgk/7UOxuC9s53ziXxaIL7Ehz4zbsLMREXmUzeamuHwh7BHZg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0_1535406274132_0.20327953249946962"}, "_hasShrinkwrap": false, "publish_time": 1535406274201, "_cnpm_publish_time": 1535406274201, "_cnpmcore_publish_time": "2021-12-14T05:22:45.982Z"}, "7.0.0-rc.4": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/plugin-transform-typescript": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/preset-typescript@7.0.0-rc.4", "dist": {"shasum": "28e5c8a83b9e434c0eda3958cc88a0a9bd11ba6d", "size": 3116, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.4.tgz", "integrity": "sha512-wZiNhb5MmBprTDjgayDoF1uJpKeEUdKaMbybW2KC5yvJ1B5nFuOxK86jRtTc8WsKRjGAnmbDfhUGIKM/uiFwRA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-rc.4_1535388364721_0.12631747804088245"}, "_hasShrinkwrap": false, "publish_time": 1535388364829, "_cnpm_publish_time": 1535388364829, "_cnpmcore_publish_time": "2021-12-14T05:22:46.202Z"}, "7.0.0-rc.3": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.3", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/plugin-transform-typescript": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/preset-typescript@7.0.0-rc.3", "dist": {"shasum": "7dc31c23d5b5dca7ed9c32e3f7ed9a703d1b5a53", "size": 3117, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.3.tgz", "integrity": "sha512-pDxAKCjBPJEOtCX0e84Jvl3fFeRUDH++l+daJuERmm0fyR95k4Cufc6llYvXa36J1lN4KG/Hm8WpnYxSqab3CA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-rc.3_1535134161190_0.31462740052564886"}, "_hasShrinkwrap": false, "publish_time": 1535134161243, "_cnpm_publish_time": 1535134161243, "_cnpmcore_publish_time": "2021-12-14T05:22:46.445Z"}, "7.0.0-rc.2": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.2", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/plugin-transform-typescript": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "_id": "@babel/preset-typescript@7.0.0-rc.2", "dist": {"shasum": "570594e784c864993ad5e479ad27f16c30af5870", "size": 2395, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.2.tgz", "integrity": "sha512-wTuoyiK4yQx+NP3Ll90lZzCro5AKgzeiQ8xzo6r/EpD8XfNXstEkUQRyuihKTmPrkL/c+J7ro67Ruhr0GcFZJQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-rc.2_1534879539894_0.04250946978181225"}, "_hasShrinkwrap": false, "publish_time": 1534879540006, "_cnpm_publish_time": 1534879540006, "_cnpmcore_publish_time": "2021-12-14T05:22:46.652Z"}, "7.0.0-rc.1": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/plugin-transform-typescript": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "_id": "@babel/preset-typescript@7.0.0-rc.1", "dist": {"shasum": "bd22fc7158fde7c1dd5bb7853246b5cf5a801c9c", "size": 2395, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.1.tgz", "integrity": "sha512-CxP+7usKH/yalTKhO+P9CbEM+3tJMJQjabeBfg+I/UaPxOFh7MP3ZcFXDe1/eHVEjkyTEWDEbm6VgiYAhyImSQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-rc.1_1533845375142_0.005899863118425053"}, "_hasShrinkwrap": false, "publish_time": 1533845375214, "_cnpm_publish_time": 1533845375214, "_cnpmcore_publish_time": "2021-12-14T05:22:46.877Z"}, "7.0.0-rc.0": {"name": "@babel/preset-typescript", "version": "7.0.0-rc.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/plugin-transform-typescript": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "_id": "@babel/preset-typescript@7.0.0-rc.0", "dist": {"shasum": "5b851d197afde3f5313f9d7dfab162d219e78e92", "size": 2403, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-rc.0.tgz", "integrity": "sha512-fERYik3V5DAyxJUhMpta255pv50HRDQ1CEfHWdfwtLrCzCY1r8bg4Oln4nIGYAjgLVMQKeMlF6h3XlJNgSon2g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-rc.0_1533830378321_0.21204707967838177"}, "_hasShrinkwrap": false, "publish_time": 1533830378363, "_cnpm_publish_time": 1533830378363, "_cnpmcore_publish_time": "2021-12-14T05:22:47.079Z"}, "7.0.0-beta.56": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.56", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/plugin-transform-typescript": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "_id": "@babel/preset-typescript@7.0.0-beta.56", "dist": {"shasum": "eff0414072f018014f857c072a07e524088568b0", "size": 2406, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.56.tgz", "integrity": "sha512-9W4dRiUUjX4hQHa9a82d54wyODor+CRkdPXmXR3L6PoHijCox5SV5JAXYx2KcmlYLaS9jefIDRjRhruqNjPfKg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.56_1533344885594_0.8967463984227066"}, "_hasShrinkwrap": false, "publish_time": 1533344885819, "_cnpm_publish_time": 1533344885819, "_cnpmcore_publish_time": "2021-12-14T05:22:47.301Z"}, "7.0.0-beta.55": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.55", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/plugin-transform-typescript": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "_id": "@babel/preset-typescript@7.0.0-beta.55", "dist": {"shasum": "b1eeffe0b59bc5cfd2a7ace9303b478a783fbe2d", "size": 2408, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.55.tgz", "integrity": "sha512-pZhMdmvTSJP9DFDAGyV6SsWfXLgivfrz0IXV9ZAkoIcxptNWSOWMTDf0qShgKhTCE4mEYwQeR/zUv+LwH51yEQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.55_1532815668921_0.4516327294178544"}, "_hasShrinkwrap": false, "publish_time": 1532815668976, "_cnpm_publish_time": 1532815668976, "_cnpmcore_publish_time": "2021-12-14T05:22:47.549Z"}, "7.0.0-beta.54": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.54", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/plugin-transform-typescript": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "_id": "@babel/preset-typescript@7.0.0-beta.54", "dist": {"shasum": "7491df7c0f20d08ca63c41f78d2c722c92470391", "size": 2405, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.54.tgz", "integrity": "sha512-9pFL8K194CDrNeZ14wnqcF1zOCBv8Yn+YSTvhdZtD3STJb9N6a3whRvFmbbC2nWdRrxugzq8PUl4g8IX+TwgBg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.54_1531764023624_0.40672037010501083"}, "_hasShrinkwrap": false, "publish_time": 1531764023682, "_cnpm_publish_time": 1531764023682, "_cnpmcore_publish_time": "2021-12-14T05:22:47.771Z"}, "7.0.0-beta.53": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.53", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/plugin-transform-typescript": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "_id": "@babel/preset-typescript@7.0.0-beta.53", "dist": {"shasum": "15faddecce27b9062ba50e1a13c201e2ce2ab9b0", "size": 2407, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.53.tgz", "integrity": "sha512-M135AzJKxVHAMx0XBCjVtVXm2EWXN1RdIrwJfgepnBJJUWqyNWRnr0lV2RQU2xSpVZ2h/xGPepSaFrjfcB5+8Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.53_1531316441881_0.024067009638654158"}, "_hasShrinkwrap": false, "publish_time": 1531316441950, "_cnpm_publish_time": 1531316441950, "_cnpmcore_publish_time": "2021-12-14T05:22:47.991Z"}, "7.0.0-beta.52": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.52", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/plugin-transform-typescript": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "_id": "@babel/preset-typescript@7.0.0-beta.52", "dist": {"shasum": "34778e100bc69411463d44ffc4d53aea798b9921", "size": 2408, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.52.tgz", "integrity": "sha512-U+tp6qYvKaROAL4oo5FgfGFrGzs/qqN3z2h4RqoPtSCIyWrJlQxlwQZisIHZuhE+XFiztbmsKkqyZxhOUqv1hA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.52_1530838782676_0.7263500244673333"}, "_hasShrinkwrap": false, "publish_time": 1530838782716, "_cnpm_publish_time": 1530838782716, "_cnpmcore_publish_time": "2021-12-14T05:22:48.178Z"}, "7.0.0-beta.51": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.51", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/plugin-transform-typescript": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "_id": "@babel/preset-typescript@7.0.0-beta.51", "dist": {"shasum": "95510b2b1493c6b210a499b73e80cd8a9e2f8ad1", "size": 2396, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.51.tgz", "integrity": "sha512-IXSJFN5F7jKWiFItzXQXaDQvjrJjrg2AiRihudU0Q0Jxc5hRQgKr+kszlpmEP0PiVgpFFSlKVv3m86zEvE374Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.51_1528838433278_0.6832368777164255"}, "_hasShrinkwrap": false, "publish_time": 1528838433350, "_cnpm_publish_time": 1528838433350, "_cnpmcore_publish_time": "2021-12-14T05:22:48.418Z"}, "7.0.0-beta.50": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.50", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/plugin-transform-typescript": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "_id": "@babel/preset-typescript@7.0.0-beta.50", "dist": {"shasum": "4e96b32eb660017af1fe717f3fb64e5b7bb77455", "size": 2388, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.50.tgz", "integrity": "sha512-K4WBTw9H24dgLEKALs8ti6gXLB8KYPvxXvSPvbcSXng+ewWkN8hxNft7IPKfKdGbJK291sU0QfAsJc0brIMBNQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.50_1528832871539_0.5642187435421406"}, "_hasShrinkwrap": false, "publish_time": 1528832871739, "_cnpm_publish_time": 1528832871739, "_cnpmcore_publish_time": "2021-12-14T05:22:48.631Z"}, "7.0.0-beta.49": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.49", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/plugin-transform-typescript": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "_id": "@babel/preset-typescript@7.0.0-beta.49", "scripts": {}, "_shasum": "20a3c4a2f815efe7416f756b433c0fd9907a47ad", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "20a3c4a2f815efe7416f756b433c0fd9907a47ad", "size": 2329, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.49.tgz", "integrity": "sha512-fQUOotg9wfZ4u41GGCTgBUpuFHav4Buv36sQ/7YTd504bLqgkjxGPsD4L5KPzI+A+FzUQp0xNTtHUTyZST1u9Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.49_1527264238095_0.19602893598199445"}, "_hasShrinkwrap": false, "publish_time": 1527264238198, "_cnpm_publish_time": 1527264238198, "_cnpmcore_publish_time": "2021-12-14T05:22:48.838Z"}, "7.0.0-beta.48": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.48", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/plugin-transform-typescript": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "_id": "@babel/preset-typescript@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "9b076baf1c3e2bb7eed266872326891245af615e", "size": 2321, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.48.tgz", "integrity": "sha512-/KIwT1xbpO6VbVawitEfka8DIcpeJkaTY6S1A34G9Q/u+jztkiEWp4/gbYhajm4dW7U3Rlms1hLRmEl3QD1QxA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.48_1527189865563_0.1853843909612034"}, "_hasShrinkwrap": false, "publish_time": 1527189865631, "_cnpm_publish_time": 1527189865631, "_cnpmcore_publish_time": "2021-12-14T05:22:49.039Z"}, "7.0.0-beta.47": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.47", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/plugin-transform-typescript": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/preset-typescript@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "d81fc37dca3bea1213f2818f9df79ea0ffadd6cb", "size": 1100, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.47.tgz", "integrity": "sha512-WBaoHi27WxzISZv/ZoWaJhwbgAvqYg8OXkFwWWqdPG0L0ZEhztqW5g/T6WoqFL1EzuIfloLYzw6pmORhlVM9jw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.47_1526343447824_0.7651556878257499"}, "_hasShrinkwrap": false, "publish_time": 1526343447943, "_cnpm_publish_time": 1526343447943, "_cnpmcore_publish_time": "2021-12-14T05:22:49.239Z"}, "7.0.0-beta.46": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.46", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/plugin-transform-typescript": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/preset-typescript@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ab14898e504c7f172cd0fbc03491f6a5685d8638", "size": 1100, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.46.tgz", "integrity": "sha512-IcwCQ8f1zoWP0i5xyrmKlXQaFEa+X2ogxaTGRfzPNBy9iQ97BMISR7oPJgPxquktk7WoImpGp+T5Ws1d4OcVRw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.46_1524457948649_0.2949379233928817"}, "_hasShrinkwrap": false, "publish_time": 1524457948958, "_cnpm_publish_time": 1524457948958, "_cnpmcore_publish_time": "2021-12-14T05:22:49.547Z"}, "7.0.0-beta.45": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.45", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/plugin-transform-typescript": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/preset-typescript@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "8a0f94439e1df6bdfe4aa43d164079d033edc869", "size": 1105, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.45.tgz", "integrity": "sha512-pbNK2NHYHCYI2m6AqTwS95IwNvUp91dJ+BdYlcT8ZsPd+wwoOzbTmgmPj0onvnoKzFacoehtQJNpYqz0W9yttQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.45_1524448690986_0.9445015361069842"}, "_hasShrinkwrap": false, "publish_time": 1524448691051, "_cnpm_publish_time": 1524448691051, "_cnpmcore_publish_time": "2021-12-14T05:22:49.744Z"}, "7.0.0-beta.44": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.44", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-transform-typescript": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/preset-typescript@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "2b5890bba7b21df6af11c0fc23b1251bd48b2c63", "size": 1113, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.44.tgz", "integrity": "sha512-wWt9JM2FKA1KUeNqO80h+fnMFR41dlhrb5RXuTS12sdBaufXtUJoN0Ku9L+i/Hh77cleK5EjXX0b+a/Om1UEtA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.44_1522707629640_0.8619449968285033"}, "_hasShrinkwrap": false, "publish_time": 1522707629731, "_cnpm_publish_time": 1522707629731, "_cnpmcore_publish_time": "2021-12-14T05:22:49.981Z"}, "7.0.0-beta.43": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.43", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-transform-typescript": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/preset-typescript@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "a69180a938dc44821161725d74cc87eae5aff075", "size": 1107, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.43.tgz", "integrity": "sha512-SUe+7dWLxyL6r0UfJ81x7j7kZ6tR6A7A/1PWaiXPY/OquI+EIZA2QI7GLaDYpBUyxGZAZkVc/qQ1ffvTXVusRA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.43_1522687730260_0.841939258793019"}, "_hasShrinkwrap": false, "publish_time": 1522687730326, "_cnpm_publish_time": 1522687730326, "_cnpmcore_publish_time": "2021-12-14T05:22:50.252Z"}, "7.0.0-beta.42": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.42", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-transform-typescript": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/preset-typescript@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "adb91d387a6eee7b45918de544d6c8fa122c2564", "size": 1051, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.42.tgz", "integrity": "sha512-z3ZZ+bsCKFnygo7owlk4vAXOdhjhRxgyH31A1wKwq2talWx3ZjbAsmqTMBcWK4lb8XrwS2QASGYAb4BfY7zWnA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.42_1521147115602_0.2993737511030343"}, "_hasShrinkwrap": false, "publish_time": 1521147115671, "_cnpm_publish_time": 1521147115671, "_cnpmcore_publish_time": "2021-12-14T05:22:50.483Z"}, "7.0.0-beta.41": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.41", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-transform-typescript": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/preset-typescript@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "9227cb31ba854d2454063a18334d755b49f03e80", "size": 1055, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.41.tgz", "integrity": "sha512-aWkout+4tBIXPX64U86gL6fZ7Ka67o4iy+iwtwbp6snhMeeLnHcnhqlZPiTDsF2e/VKXIaQRysg9kmcc100x/A=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.41_1521044801417_0.7389062076026982"}, "_hasShrinkwrap": false, "publish_time": 1521044801480, "_cnpm_publish_time": 1521044801480, "_cnpmcore_publish_time": "2021-12-14T05:22:50.668Z"}, "7.0.0-beta.40": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.40", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/preset-typescript@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "7f04a10dfe775c3939d38b6d1cdba4d087e1acc8", "size": 966, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.40.tgz", "integrity": "sha512-G74AcsTo4QPTn6RhZGxG2TapvbeA7SZ7+1dNtn2FZRD5LisJiC33AlBal6MMl3hqBL61xJVcAkNadbtC6PoC1Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.0.0-beta.40_1518453744399_0.668351091655645"}, "_hasShrinkwrap": false, "publish_time": 1518453744789, "_cnpm_publish_time": 1518453744789, "_cnpmcore_publish_time": "2021-12-14T05:22:50.921Z"}, "7.0.0-beta.39": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.39", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/preset-typescript@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "da4c7649ff77310ac5bfef465a78bbdb1eb9c556", "size": 968, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.39.tgz", "integrity": "sha512-YA2A0+mKl+o+PwZ7IRcc7yXj/oI3tEBJiNSfLTJZAja96TX3NZzCOXxovZ046e2Q/80nh9A1efxy1oM5BfZcaQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.39.tgz_1517344119088_0.7419545471202582"}, "directories": {}, "publish_time": 1517344119164, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517344119164, "_cnpmcore_publish_time": "2021-12-14T05:22:51.120Z"}, "7.0.0-beta.38": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.38", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/preset-typescript@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "3c46ad5129db53ea4a62186a59695d4343898149", "size": 965, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.38.tgz", "integrity": "sha512-yMVH+tUoH4jLbvGeAG6CRfxDVNUib4zoAVOveQ4+BFNLEPShYJl0aMnrKyGaBKVHqpjvxcq70/YuPA4TeE1Mng=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.38.tgz_1516206752288_0.6092016228940338"}, "directories": {}, "publish_time": 1516206752464, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516206752464, "_cnpmcore_publish_time": "2021-12-14T05:22:51.336Z"}, "7.0.0-beta.37": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.37", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/preset-typescript@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "fa844111bd7183be00b466aa913b0ddf085f460a", "size": 967, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.37.tgz", "integrity": "sha512-OHHMp+6JWV6vJxbdCd11sJ7JxNlJ+uIXv57kXg+sY98N0wy+FYyj8QomOL9L93dVYC8zHcAvjIMwegXKK2jQ2A=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.37.tgz_1515427414414_0.8398248662706465"}, "directories": {}, "publish_time": 1515427414543, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515427414543, "_cnpmcore_publish_time": "2021-12-14T05:22:51.559Z"}, "7.0.0-beta.36": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.36", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/preset-typescript@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "ae7df38f6ae889220ccb98afa8617a81e8f7f69d", "size": 962, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.36.tgz", "integrity": "sha512-Af4e5a4eHSSPutnAXCUQ9emv96we5Rd1Qk0lo+ierWvs9UpRM79XzOd3SNDCq1i6Vqf1w7bwyPCdMuhSs4VAdg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.36.tgz_1514228726841_0.5878757804166526"}, "directories": {}, "publish_time": 1514228727003, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514228727003, "_cnpmcore_publish_time": "2021-12-14T05:22:51.831Z"}, "7.0.0-beta.35": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.35", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/preset-typescript@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "b1315b0d599cdb3272f85a3a3d5bf724cd442815", "size": 966, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.35.tgz", "integrity": "sha512-XgC9xNZ9lr0qafkeQSx/UduQFY333lxyUREs458aIVfktem8mdVJSSecX3QwHs+ONiqg27V6ckZWyYubdxm7vQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.35.tgz_1513288099410_0.11422558361664414"}, "directories": {}, "publish_time": 1513288099565, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513288099565, "_cnpmcore_publish_time": "2021-12-14T05:22:52.027Z"}, "7.0.0-beta.34": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.34", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/preset-typescript@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "3df34f1de48291a63d5fde96d47ea11af018f560", "size": 966, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.34.tgz", "integrity": "sha512-KBTmjiWyOdwj0XmRrxzGTjR7JBSmYT2WQ+e+MhUubI5HH0JouVzl96sJoBrM1v7v0lpoJhOO+tYfwCBjTmiB+Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.34.tgz_1512225604568_0.7712397677823901"}, "directories": {}, "publish_time": 1512225605572, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512225605572, "_cnpmcore_publish_time": "2021-12-14T05:22:52.278Z"}, "7.0.0-beta.33": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.33", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/preset-typescript@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "43b60ea11d9a18f9d6de733bccc6abdda349563d", "size": 966, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.33.tgz", "integrity": "sha512-MxXfxyvh/IBwUmJqsBbQfpWNyKNBa4MEqFKCxaEh36ajT4mgmA/kZ+4f9x0ba2sYxX3Ww8W0eG9+7vrlKG2k0g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.33.tgz_1512138547760_0.7005888556595892"}, "directories": {}, "publish_time": 1512138548687, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512138548687, "_cnpmcore_publish_time": "2021-12-14T05:22:52.459Z"}, "7.0.0-beta.32": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.32", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/preset-typescript@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "699055734b588693dccd8f7ecd904eaccff62ad0", "size": 965, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.32.tgz", "integrity": "sha512-/BZgxKxgppF8w4r2ijQRag8p6RxOeHFyGUzx6yCPpFgX9aZ3X2AR6GHyjlKJc3nszZng1enNaCMVRU0Endxs5g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.32.tgz_1510493631185_0.6704312125220895"}, "directories": {}, "publish_time": 1510493632134, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510493632134, "_cnpmcore_publish_time": "2021-12-14T05:22:52.657Z"}, "7.0.0-beta.31": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.31", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/preset-typescript@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "ab6deef18b8f4b75c7828b94a0ee1f64fdc405e0", "size": 961, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.31.tgz", "integrity": "sha512-3LtHKWFVihRGtjK37prj0jV/fgYuAJQMYpDsLcpp5lVDBochhedNXRJrK0edHYISrcPfrrz/oggq/Xi8akuXAQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.31.tgz_1509739450207_0.0843143432866782"}, "directories": {}, "publish_time": 1509739450616, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509739450616, "_cnpmcore_publish_time": "2021-12-14T05:22:52.848Z"}, "7.0.0-beta.5": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.5", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_id": "@babel/preset-typescript@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "60bbee301663f41825463855d9cf292f0dc631cb", "size": 961, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.5.tgz", "integrity": "sha512-tLXVrKJ9JoMR0gctz2Wm3nj00vL4M84MaUBjfeGIcx+l/7Nclj2D++12fLSdBKFxPLzlkDiUXjLgxni8nxKrvA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.5.tgz_1509397046460_0.7988792902324349"}, "directories": {}, "publish_time": 1509397046512, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509397046512, "_cnpmcore_publish_time": "2021-12-14T05:22:53.023Z"}, "7.0.0-beta.4": {"name": "@babel/preset-typescript", "version": "7.0.0-beta.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-preset-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/plugin-transform-typescript": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_id": "@babel/preset-typescript@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "61b7040afdec58ff80ef762a9c919647c9a74358", "size": 955, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.0.0-beta.4.tgz", "integrity": "sha512-MLNHjJ/0+pFaGhkUcSPJQXBewimgM8UsXnQWIH7naWTqo5j7RWES8W7fJ1Nm6K8uz/Wbemka3fK8G1KKfcIZXg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript-7.0.0-beta.4.tgz_1509388547480_0.306059145135805"}, "directories": {}, "publish_time": 1509388547570, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509388547570, "_cnpmcore_publish_time": "2021-12-14T05:22:53.256Z"}, "7.16.7": {"name": "@babel/preset-typescript", "version": "7.16.7", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-transform-typescript": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/preset-typescript@7.16.7", "dist": {"shasum": "ab114d68bb2020afc069cd51b37ff98a046a70b9", "integrity": "sha512-WbVEmgXdIyvzB77AQjGBEyYPZx+8tTsO50XtfozQrkW8QB2rLJpH2lgx0TRw5EJrBxOZQ+wCcyPVQvS8tjEHpQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.16.7.tgz", "fileCount": 5, "unpackedSize": 13635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk13CRA9TVsSAnZWagAAUnUQAIx4zo5XVlMFZXqZ2mRA\nWPiDa4bWQugs1CeYgCbbtGNZdwE1fahz/zKyrCVwJONvLCdmejlc+vllVoTM\nEfCNLjGW/OIA3KVKwzugMufFDNjA5tYgdE4bQ7tVLv6HmtF9yuTsGyBScki0\n8TA0iSiPTJ8KjVbRF5WgnwIpcuZDefz9dvBwuXo/xhqsaKoJqbHTxARA78Zr\nrxPjjsxvEWPbIbzgqPGGbT4xdiSUAI2ATDuWJXxrUl4ivUOiBMynrUUwJTjB\nSmSdmsAhetFYNz/2QCDe9BircJsoBOaSDJArFOLP/l4ZyYETuA3IVKlljJQ9\n60rPHlc1MhfVi9OjJJMSIj93cMTZ3TPgk3YviJoUFjkmashSVJz9xuhs+LmM\n5lQI8vvk5/nDNurEDuaQtr74gvtvuIyrnMUN3OZDYGsGYL1iIrgAYVQ2MntN\nQLjR6uB+KHh90p/fjwcpb3oFr3WXGT2SfMxCu+0HnxoCBFhSRLd6txRZSYNa\nTarehQj3CDxt/HtuFIewuUVaANxE5yAqdQpkW+ss8ikUOsyQ8sta7YkvDIda\npkjzANEeX0dDJh4ww21w8rc1olM8KEYrBqWkECbvJbJvX5D8XtHH4BENG+wc\nC3OpoSCOWKQT0VwNEkHCc2E3oWM0DBn3wygbsujJpM4u6Ff5G4zDTwABOjKj\ndAJi\r\n=lH84\r\n-----END PGP SIGNATURE-----\r\n", "size": 4145}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.16.7_1640910199502_0.48449615365669674"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-31T00:24:00.291Z"}, "7.17.12": {"name": "@babel/preset-typescript", "version": "7.17.12", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-transform-typescript": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/preset-typescript@7.17.12", "dist": {"shasum": "40269e0a0084d56fc5731b6c40febe1c9a4a3e8c", "integrity": "sha512-S1ViF8W2QwAKUGJXxP9NAfNaqGDdEBJKpYkxHf5Yy2C4NPPzXGeR3Lhk7G8xJaaLcFTRfNjVbtbVtm8Gb0mqvg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.17.12.tgz", "fileCount": 5, "unpackedSize": 14276, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDjR0rmKNk4guxm6vnfVpiJ5KCHUm9VHLcD2a96DEdEAIhAI+R3R5AWdn4ztBrGhrEmiqDZ+pLFyMxhPyCDxqRqfDD"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrynA//X4NPfYfqyHk+CYRAq/F0td/y7dJvfvHWZk+Kd5xKH1I3zJSW\r\nmKU42RbaXRvbS+ZP5mcqcaLAHOSO2E1g0h8pohg05xajf8hDW6yTlcQPGjSN\r\nYTUe+wQkzDj4G+RFJdgfJqRuYlO93N69CXlp/mpz23OEy0g2JJty9jZ57FYV\r\n4c98X0Id9GApnRJdZRfIL06aVW7TXx74KB3oTpWtcZui9z1Daq0CF5UuWBjw\r\nBoHh0iNHp00lyA0fMFt7CV9pbFuI0cn1+vI0P3YHmFH9dBLRuai9sMKi6pIk\r\n3yR8ONxbpQkpO1nIyw+91qXHRWNwobkoxNVuthD+sXRLaeIGAFvFRonvfAv9\r\n4vLj0mxsL0jEl1CdVQ6WifKLvnQkiNWzUeOgwgA8EHM4CEu12fzkRce/4phf\r\nh1FKXn3NCPSH/We8vWGXMQ1KHdqsOJv6Vjxo2M3U129oz18bmRcHreXEVZRh\r\n5YwKvfbPJTls8srtupofIbmk2wLnk3y2TZ7kU9XweO9AIqnDQxntYBDID34I\r\nhZrVCmNjX2epEdgWxXTHNtBrReFsuTno2W9e9n1TE37ddsGW14fq3k7ldsDK\r\ni+9Eywbj3ozV3Dwm17LsE5cFlpBgUC5ryU3+0h7keXSx5dPO/4ntkdLLtHln\r\nWhGdoWI5kMrW+K7qKAZfquxu/4Jh/Dm0H+0=\r\n=0tJA\r\n-----END PGP SIGNATURE-----\r\n", "size": 4286}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.17.12_1652729594253_0.18225224068328716"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-16T20:35:23.786Z"}, "7.18.6": {"name": "@babel/preset-typescript", "version": "7.18.6", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-transform-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.18.6", "dist": {"shasum": "ce64be3e63eddc44240c6358daefac17b3186399", "integrity": "sha512-s9ik86kXBAnD760aybBucdpnLsAt0jK1xqJn2juOn9lkOvSHV60os5hxoVJsPzMQxvnUJFAlkont2DvvaYEBtQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.18.6.tgz", "fileCount": 5, "unpackedSize": 14110, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF5RnBa0tDR6yZUeUNOUs0X88PNKLG0DuABuu/4GbmybAiEAyEpzQdgOZnfT06zx75+tO9eHND00DNFOkrurzntjz7M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugodACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroyA//T9MEdCqJvG9QLpBodXDZmHTlQbahjqjAsTtPTrYhamcd2A8c\r\nC9nRiZzlnawiS15nWtDZIo1E9iynTkHex2M6sihiQQ9OU39EyHYrQkyO4yo7\r\nrCVMDVxyglUR3sPeG0nIgyZAMaGc39ooLCIIn6pZ/IYGmyusE9MQkgmbCUOI\r\nsvSz4dqoSdnpD9dujitjP3bzE908ld4xYQgMR3F6ko+lWZkeGsQ1uyKl5v9v\r\nI8MoPwY9ZxiC95JEt87oAjVo2G4FR8Skta36l37nI71dML3f/XJqRc382Fgl\r\nQLTN/BdX2fAfqW0+iPloRdtT4Yh9ynheBWCzhI+j2LZJ6SLT8GIxf8vxlHuV\r\njzNQTmGn7K1V5tX9TQLq44Ib27RurwU3mfFwrjNNV7Fru4o4CDq5vd9EjeJj\r\n2r0XRHgSROHv4WBUSmIoylDMUVHv0uuC4MbbcvKNJQLJ2OhyLA25IVwJwb6q\r\narIk2h18fRz2PkIo5gbWM2ygb/f/RX3z45f6zAiIUbR6GcOA015XP9RHwfiX\r\nrFDIonok6XvlcFP2VgZXXHEFuszHpqO3pz2n1xFdPad3Yk6HwdQp5iCWPhtI\r\nyKuWw4L4GnmUeXgnBzo2tWl++KnAwkPjBs1SbfFSCil+sCz+Y6udk3J8gAcd\r\nm+ftsMR08zjxTaURL5gTVq4CccXX2D+ltek=\r\n=Oj7/\r\n-----END PGP SIGNATURE-----\r\n", "size": 4304}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.18.6_1656359453687_0.5557091475516398"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-27T19:51:12.244Z"}, "7.21.0": {"name": "@babel/preset-typescript", "version": "7.21.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.21.0", "dist": {"shasum": "bcbbca513e8213691fe5d4b23d9251e01f00ebff", "integrity": "sha512-myc9mpoVA5m1rF8K8DgLEatOYFDpwC+RkMkjZ0Du6uI62YvDe8uxIEYVs/VCdSJ097nlALiU/yBC7//3nI+hNg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.0.tgz", "fileCount": 5, "unpackedSize": 14100, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwVQLk5s28cBamq3OkLq0XbnBbjX2aEbPjQqnF+jbEzAIgM/LngGWMMIIoUIbsRGRauROtmqEQLPgxV34nfJkldfo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWFw/8DmY7/rB7v5ed6EIuioGWYGKV17E53FqkVWXaTMutHHK+JqjL\r\nO5uR1HE7CXUFcmiHmjKwG3FwsJQVg3GgFIoBrbSd8prUnckBn8t0C+atqbFP\r\nDnmXxdO6O1X4nF75+prA2aYUO+5B3pY/qYgCz7eVrTLMbwnvyFhVfCtRPsoH\r\nnd4Rlt6aAzoixVA1dSzFfBAPDrO/47kZm6vCMwZiYt339LY6PtXL5NWXD4xO\r\nP1OpqQnelV+RCRxCjfqfPL/YIUH305jNqK1fMhBaswGGuHMGXZDD9x+UZPcy\r\nLTbLVywAnvEn0jOFh3/FG0NVlM6+CNLH+fERdpKlgaezJCPxbahN5dOPMa2c\r\nuFq+909yQhb2BP7BKq0n9tugfJbwKutBfK8lfakeEP1/XBtQLMiv4mujaE8J\r\nOq4KMzIE8FqWDwC2JwYpXrPu26dM6LClFyHu8JwBsnDAGImT00Iw0tDJ8Gzm\r\ncruNq/izT6/q4Ji0BB+BGZmB0so56/iVG5bojjvfAoe/fOb8993KO6lxygIm\r\nUNZUDWnZCbHBCjmNUGrc7HHjFkiahpiPmF1mL2s7Rdtgejkc217OzkW7so3+\r\n244+2wJ9MzVO/WoGsvJO+gZcNv3iaL7CesHFWB4pHdfJsRDUMImaEQtCb5fL\r\nHLFquQnU9QxD20WYPufLtSeqJC4/2JZoBOI=\r\n=baK/\r\n-----END PGP SIGNATURE-----\r\n", "size": 4070}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.0_1676907082612_0.3288576569368491"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-20T15:31:22.789Z", "publish_time": 1676907082789}, "7.21.4": {"name": "@babel/preset-typescript", "version": "7.21.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-typescript": "^7.21.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/helper-plugin-test-runner": "^7.18.6"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.21.4", "dist": {"shasum": "b913ac8e6aa8932e47c21b01b4368d8aa239a529", "integrity": "sha512-sMLNWY37TCdRH/bJ6ZeeOH1nPuanED7Ai9Y/vH31IPqalioJ6ZNFUWONsakhv4r4n+I6gm5lmoE0olkgib/j/A==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.4.tgz", "fileCount": 5, "unpackedSize": 17491, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICt+WRAb4HXaejUqAp6GrI7yMtnIMMlAASQK9L6fqDrPAiEAvMVf6eh2oo0XA8FWWk/EPtgYUdqKJMnudL6frssY3IM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeEg//alH3ui7QSy85jfyMbzYzVTStW6ZXD0NpcanuiLJWj2xgjk0e\r\nUlH4PQKaBM4WhOsjYYovcCz6Wi/Z3ddZe3tK1RFg2mw+LRfmsjuoy2sFUAV6\r\nRp6FB1D/SXf5mFGFKhlhJAPpGO2weDPmjeZP66z7cK2d64O+acI9FNZYGKtF\r\nsL3BPCwhL0mOy0s+12CYMKRZSjVCl8M4yWfx3OViQ4wUJHvn3CKQEZCwoUgl\r\n+I5dtGONbvUT7ye3PU3w0vxYjYDd+8D0YjJYz4D9AEJArUtaYewHMR6qxfKu\r\nKjixi5p6X8KjUkcKuJ0a5c7Jj7G/6UItmzHLF5UDE9NkIkdcwqwh04bb5ZbW\r\nlvzF0771vFWyhsE1/LKYmBnXNXPjqwRRn2w16sF77LrM6y8R7fcov/YigeTE\r\naIbfnMz+WaZ+2fRHlFfievmpmWw02Fndig7pKLNVcjmvDsmzyJ6QFZxtfyC5\r\nGWa132MaCb+ViiSxJ5fHKqfx8FDYtaJG6uCIRAjFm2eYV5N6djFmvZkcDdLB\r\nb/aP+7bdvq2r1Ja8uYIHl0/66rodui+04lT4WmJWVr5aCTm7ztdW+K68Mtrs\r\nUXn5jSzyWvMZyUCnxAXgEkNnSANYPj7ZRuJ8brxPU9VNWR7iJRah3q5PL1lf\r\n1S0n4hzabtSX7gHi74V4LBobBlMrKKdcdms=\r\n=k8AD\r\n-----END PGP SIGNATURE-----\r\n", "size": 5015}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.4_1680253316589_0.016781226260434767"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-31T09:01:56.939Z", "publish_time": 1680253316939}, "7.21.4-esm": {"name": "@babel/preset-typescript", "version": "7.21.4-esm", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-validator-option": "^7.21.4-esm", "@babel/plugin-syntax-jsx": "^7.21.4-esm", "@babel/plugin-transform-modules-commonjs": "^7.21.4-esm", "@babel/plugin-transform-typescript": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.21.4-esm", "dist": {"shasum": "fcf2b8f9707c88c5ad8a4c2e7b905abd1396eefd", "integrity": "sha512-WPDEV+qH6U0w6NmQMDZDgNDco+k36H14LCZ514G1EaWMhXOFpRzdEh7AEeY7rF2DaJNd9mK5foWpoBoWvWEtTA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.tgz", "fileCount": 6, "unpackedSize": 17557, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFCkW0pOytKK8iBuyoFTH8PMnZ+xWirT9bXk/QSShwF4AiEAjA2AtXBj5mvIDxcyi+SirHBfdDCm6Omrm9FG6eRFpHs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4lw/9EtSFwaoWcGxVFHQANSzucDSeceolMOF8OcNNDKLxC4u22uRA\r\nLKdpPEtWA+nmEKFLwJhCB62e8xH+lh/slGDf06RoDHXZFVoruXxeGSTzZpWH\r\nf8l7YHsdL2tXDXbsXCIoQkLfji2sbRcxYZcjh82zv/bkNBul21ycBD9huMG/\r\n4lLCaPXuJVNL6La/uza+n1FpGdMBRVKhYWMAfJ8QT5yzvLmDFZZu4GISld/1\r\n2ybt48ePbvaNfevKaCwunNBcOGtcdC1UDwjZSeS4XkGkLW59m6k5FaoiMoVk\r\nchH+sid2+03JLohW6YvRMfhGqA8kJbTXCoW9JZHh280nw+BHRGkY7rgSPqyz\r\n7Cn/uOKQbGK7yeNLD+0MVs5PLP+vmU7n5kCrmPneCCDw+PxcyMZUnw4x/XtU\r\nrW3QoZpFnnDV/3iBJPp4HMjDQM7nqZYSgUNhr3r4g8T0XbPiUwvGUsJuCodm\r\nOTkjDVj3OmxLPqTUNswpm7FeuBu/fQeVp38+jfTHlQriQ+IgfdliaUhGJZJH\r\nyWFRyZvm6olZUHRR3rnK5leYSXxo0Axj248cTpuVrbWx91FDi/xzu49QXmYw\r\nvmkqfAEg2ql6VIVEiyYjkd5htKxGFUD9GpS4snbKEiAe+8/Lqgi9Vi6l/hGy\r\nchBoc0Tx72SLW68FrsvW3j9RZX4VwPp3eLE=\r\n=l4Xa\r\n-----END PGP SIGNATURE-----\r\n", "size": 5043}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.4-esm_1680617404474_0.4719264079653229"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:10:04.662Z", "publish_time": 1680617404662}, "7.21.4-esm.1": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-validator-option": "^7.21.4-esm.1", "@babel/plugin-syntax-jsx": "^7.21.4-esm.1", "@babel/plugin-transform-modules-commonjs": "^7.21.4-esm.1", "@babel/plugin-transform-typescript": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/preset-typescript@7.21.4-esm.1", "dist": {"shasum": "0661065a6445ff2e3cae8e1a93ecd6260e57a38c", "integrity": "sha512-jfTtOnPlqHgQYWhS5xjyu87JK1M/aox8mqe8Ei+TfkjbJVHeQZTyxBt0PsundX7AOj9cNMjDrzzgM8fMgTe7IQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.1.tgz", "fileCount": 6, "unpackedSize": 17057, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGdtwLcrdal/OfARTMeT2IMzy6gQKAx1zAd/bNInQPk/AiB1Mmfdeasj44WhnBFmvmBUxRgTAFsBnK1zXrqI74NxqQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosdQ//T4DgM+qPyWw+7o8rxQ4jB1xB1VstK11Giam5v1AatOfooQp8\r\ndinaqu3rJ9e4ioxxsdL59ubufX68IZQueMI3uqDf6tSoUWQNMrGFGs2jn6+2\r\nRsWLNYmPJD2FOhRaawmBjVY2kFLtAXpZdQBEeI/y0Ch+7BIiQawCSFDDupHC\r\nJtH+pTnwXqHU9SvQgktP6tLqD/LbUx+d5spyZqnjRsybJgiID7f/hYeP5RH2\r\nXsABigLaxcjW17+YnFs7N/gIxVJnDYp1nUDkX2BOD2nsB6JR+ap8EcubR4j+\r\nlV2wRKb7+RLamfVTX1/AS7Pghr+TpP3tKAHVslcGvNsDIRm5vwlyUag4KSY4\r\n4CTKlXdzkGv4+6q8ae8754Df0owABm7aUe2/yNjNSM4bRe1lgTjJG6OZxNQZ\r\nq271lYj6CVrRLBUoDwHw/mGePRPqpuUmPDeokk89auuB1CwYe7N72xAf2ZNu\r\nO6b4C6RpU6B8J7DJrFGm6JayRY9L16uVqE2tb4GsF4qoxgQZlv1OzpCTrLsE\r\nZO8omR1pGhOrdQV2UJEhnC4vQVeipaKk+dcPDWKbhbDE4XfcN48tkk0PFSLS\r\nfkpeKsaoxj2Ur9esjbc1pQBx40eKK2fLJWmVu4UeLiox8ovuE9s/xCalsfzB\r\naNuea41r4n5bgq+xlVf6O0MJ7WMaqdRcuxQ=\r\n=iEcA\r\n-----END PGP SIGNATURE-----\r\n", "size": 4844}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.4-esm.1_1680618121988_0.7891224818761537"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:22:02.173Z", "publish_time": 1680618122173}, "7.21.4-esm.2": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.2", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-validator-option": "7.21.4-esm.2", "@babel/plugin-syntax-jsx": "7.21.4-esm.2", "@babel/plugin-transform-modules-commonjs": "7.21.4-esm.2", "@babel/plugin-transform-typescript": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/preset-typescript@7.21.4-esm.2", "dist": {"shasum": "5f9da65edbb3e70702abff25338d23dd10a14b88", "integrity": "sha512-c9CfcBCfSp/qCBPxvvyGFtEnQIA7fU+MmaTWH2nemLBTdIyig3STx/xUP604sLFYCONwoL9YobWPUtpHB1KYDg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.2.tgz", "fileCount": 5, "unpackedSize": 17031, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUaZDT5teoyUlokkxf256IqdDkli68qTop/rTe0/jWiwIhAIceNv4ea64mo992FxQHoGkDAaMwgOIX4lq5FHyZuI4G"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4LBAAkvFpCYLn3z/81/Uj3lvjVG6eJiiNSbJmQ0JoLg2TOM8c0P3L\r\nm1zxcPi6OjBz0ljx6B4c0oBrpvTSK+UEbwvNo38LGF4mf5x5jEjdTI+HCo6J\r\nMZdxiz4lNZNyPEMoEdAcHCYuDu3na4FbCUHZAh29mMGBYcBU9r12Ma1OaMap\r\nJINA2E2s6gJrGysYXJOF9sOMB4uXit+DgPOEgAe+Mth7sxKJht77W13JPQ9J\r\nQ7tKUgu0s5mbdsFtsvxuI8fCjGBUfoJYDHCYVu7dkZklt+jvDjRN72ShRNLR\r\n9z4oVV+TzmEQUMxVvJX5QA9PB82+WQ9KGlIEicsxYaMWUr7eRDVis8hIeuoq\r\nbL43Iim01/YbOY2UqaGi/HfTP0MfXGY2PtidtRO3WCfli03ffPoyWUtDObvb\r\nBmRcKCAlC2DirNPhtWv7HuPSC/DsbuyPd+EVX3gC/RmtdDFdsQ9tZ9bBLvEW\r\nX3PM5HV/R8GmtuinR30RnbYvCsqoLnaCdGmO/V3EDY9/YlVA/w9EyTLsfWWV\r\niIsGxgrC8GEgqYPW1cfyFjrcvhWHg3qsFqYCAbpwrhxXhKq8NlRPQi9mjQ0T\r\nxwFC8jFcgcKoOvciYvCWTrrwv4qCnK9WzXKCMyk96TKb5qEiFyEeDNswtZ8R\r\nckixdjauiSpBqf5rKAhX4U8KlhvYjCxKRVs=\r\n=euz6\r\n-----END PGP SIGNATURE-----\r\n", "size": 4810}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.4-esm.2_1680619207879_0.38527365679704273"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:40:08.038Z", "publish_time": 1680619208038}, "7.21.4-esm.3": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.3", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-validator-option": "7.21.4-esm.3", "@babel/plugin-syntax-jsx": "7.21.4-esm.3", "@babel/plugin-transform-modules-commonjs": "7.21.4-esm.3", "@babel/plugin-transform-typescript": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/preset-typescript@7.21.4-esm.3", "dist": {"shasum": "51014d3eef67eb22c4c950b0da94dd534eebc7c1", "integrity": "sha512-9Nl/iuW0wKeVvXzx6yntM5Yti+9JeIU/877508AQr1alPeLxUX17OCg+kwVrVyOvKvvStA98y5WynAha6CLGOA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.3.tgz", "fileCount": 5, "unpackedSize": 17547, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdqQ2VSetGZIVGgn3xwqa0lRbEzVsLfIbF+h7VJP/lTAIgMvLzKKq1T5QpOE+R0nHv3TvuDpNW+8/MOvmlCpk3XWc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprhA//f/oFJf/MWsIpeuXvor31L5Yg5yoE9nqIrhvLeoNsIAWop7pR\r\nm952G7SnoqgnyI+iQZNXmexCO5Z/GApniuBhXzzyzy+yLjYFVSJC/kGPnRTb\r\niHHQ2x11zuR7LVMSHt/SL5Iu10ugBIbQtumPXKw1an81Qq7jgVaBq0Wmz0//\r\nPaTQhNCF0QVdBZguUduy0D3kVp1LroBiB5u5shtvZNeRzCvCUn9MsNlTA7zV\r\n/IZSEQr+sOkcQGNPxZtgWTugD/ozruK3aIwpiO17iZiiRUxBCnmdTtRI1MbP\r\nHG05ygMasOnB2Jud76BXjAftPf5h5G+U9mcrmVxHTydOZN3L5Vhx5JTGbZmn\r\n60yZLr7gB26wtUMnAYEhclYGv7M7/7cHXnMFtAMByZ37uKHdBNnmOlvDU6iT\r\ngEPMwndk8PFOYwCYnmWcFVW6twniwsPePiLnvSCP2wiOGvz2Q1ETPaGSbBRi\r\nTbPPW7xynYCPj16leKzyxODHajhd6of0EGvoDh2Cx6awSJCFcVuZwbmd16xd\r\nVNFHSX52gxb7dp++AqSQjowrfuerrolaihpdyOpF/5uKUYloqB6DLfhSYlka\r\nShLX7/f49uwPozEyZFkiDi00UyC9HeRfQn+7wWjBjuP1JdJ1nLwONlyxTa+d\r\nhqqAkW5JI5I0ckOHs3TZ7jTzzVOHwmXuodE=\r\n=5SXY\r\n-----END PGP SIGNATURE-----\r\n", "size": 5011}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.4-esm.3_1680620209928_0.37172915820284014"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:56:50.079Z", "publish_time": 1680620210079}, "7.21.4-esm.4": {"name": "@babel/preset-typescript", "version": "7.21.4-esm.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-validator-option": "7.21.4-esm.4", "@babel/plugin-syntax-jsx": "7.21.4-esm.4", "@babel/plugin-transform-modules-commonjs": "7.21.4-esm.4", "@babel/plugin-transform-typescript": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/preset-typescript@7.21.4-esm.4", "dist": {"shasum": "6c3da3324607d858486ee6009ff2e8941d63070d", "integrity": "sha512-zLQMl2GJfMNtd9lbYMX4fWyuq3d3LU6g6nC+sEdyFelh/PWehXLkYP2rMNaeDKhFCeMmTtYBY/s7mlAJeiT6Lw==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.4-esm.4.tgz", "fileCount": 6, "unpackedSize": 17051, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF8q4P6q4XJNzE0oagp5STJBuZIwqzpDqumwIuoc/rHPAiBF+zm1cTkDepGZCkIExr+r0TegHSt8g3oKPoQiGG9GeA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD61ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBLQ/+OdYYArcnljS7yI5qd+C0CuiQgmfQNAnzUsOyPq57vnb8SSLD\r\nE0doIFQAf/jA9zOGOYfJvzBaXWdZixToexGFa9HX37xjBLxrOb0CxKhMltqC\r\nizKXFmRFckuhsWdyyyY38NXzA+ljBI0CXiEY7kjnEJigfczfPWRtpTspAQ44\r\nblrtZ5Dks+8JriYlj5qm+KDXiKNXngDbldIAuVqVuI0UK/MvIAPz30vghY4S\r\nIXh80QhoVHXEtpXdo0yKI71ny7HM5c718/8nYWDJOddEBe9uOxkAAju+qrp4\r\n0k2yE6eXNBCbkLI65gmsyT3ksYAG3rywTd0N3NTxk/0eFJWF37EgUI+sfY06\r\n9dklQQO34CTKPiMyuvYDnvDHMpLacgkOUppiw8niPaL0t2pt+++wWIjY/hZ7\r\nhw6O+zIEoKqecAjyntVJIVMWnxeawM0BoXmaKKXSj0ix0d4XiunD18DlCy77\r\nRTy+ihs/Be7EV1os745wXz0/D6BbvXqBST6anmY/JCthsv11lYzuf5tbOsTU\r\nSfWeWCNSOKIdunyh3TYJeKLb6o1fN4wel8OCZYm2IZrR3gQHUIZ0z6lHig7n\r\n6/qBWnhytv/pNIuD2kKZkGhYBiXBm77+hzsnUAa7guPpGWau/Ox/MUlvksMK\r\n4BdDhbNXnX/QzO83/xcrprfTCQUPqT2D7rA=\r\n=T6uP\r\n-----END PGP SIGNATURE-----\r\n", "size": 4846}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.4-esm.4_1680621237561_0.3120321186005681"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T15:13:57.793Z", "publish_time": 1680621237793}, "7.21.5": {"name": "@babel/preset-typescript", "version": "7.21.5", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/plugin-transform-modules-commonjs": "^7.21.5", "@babel/plugin-transform-typescript": "^7.21.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.21.5", "dist": {"shasum": "68292c884b0e26070b4d66b202072d391358395f", "integrity": "sha512-iqe3sETat5EOrORXiQ6rWfoOg2y68Cs75B9wNxdPW4kixJxh7aXQE1KPdWLDniC24T/6dSnguF33W9j/ZZQcmA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.21.5.tgz", "fileCount": 5, "unpackedSize": 17787, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHkX9rMA0rdsqkc1F/eyzxD5zVzjpms3ibehf2CVpVjQIgB0zykjcwKFJBDGgCOJGInKRys4SQiqco2p3MXcrChmk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5fA//UmCGS+wYZVL+d1QdT+CAutQO4SnAleGvlvVMHfrZgXRH5oTU\r\n+ARdtgjTr1ap7tToJdaVn6ujsdyQxmsmNM+GAUEHG79hq+knbiu6XtQtHgwk\r\nC/vgpJ6auelaaPScv/qPDHoG2BAZ8siysPLgeaQb9cnIJsHoIpBrmoNi1A+m\r\nUmPABrRtr/7jtHAlbAWq7HNr6KGS6gSJW8PmdyiJNrdkKUnOwvwPK1Ly12MV\r\nlALS3RjPJB1qb0EC8ul5KciABmQT/U+BB25WCjDQhSbjrw8HZ/B6Kgl5VAIQ\r\n5eHlDfK18qxuypZfaNTUojjtQ7nXhZTgkrNtNqlaf6tBLGb5qb0llNVGUzH+\r\nFJv1EvJHTgnrUDkObo8i4maMZbfeWh6sGcthpn0e9vWtyTWy0un0cvhlUJkt\r\n7wAkeFcgZkYdGQ+CYT2VAtAnTOUdOoDCi5hqp5ZFAX2EzTXjFfAZslKR4g63\r\nX0yzPyOBG+VDB/5Wau+objQAB0jafKvU5JxscGUqvkzCikAU3qSs8iGlJyhT\r\nJky02fLE0v2EJXT1KA2oES+Yo4KMDKQOamRch0mvtvc0lC0+SElWmd4tabEU\r\n/atTFOoE5FBXKhwGlbAeUzxtrGWxSs4C6NJykbMpGHGstCPuewvxbUpPbh8R\r\nviVeb+YdKEmWbN92TUWWbmGHiyW9Je8wLfM=\r\n=8f02\r\n-----END PGP SIGNATURE-----\r\n", "size": 5060}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.21.5_1682711431486_0.5592424411296586"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-28T19:50:31.699Z", "publish_time": 1682711431699, "_source_registry_name": "default"}, "7.22.5": {"name": "@babel/preset-typescript", "version": "7.22.5", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-typescript": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.22.5", "dist": {"shasum": "16367d8b01d640e9a507577ed4ee54e0101e51c8", "integrity": "sha512-YbPaal9LxztSGhmndR46FmAbkJ/1fAsw293tSU+I5E5h+cnJ3d4GTwyUgGYmOXJYdGA+uNePle4qbaRzj2NISQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.22.5.tgz", "fileCount": 7, "unpackedSize": 25664, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHC/Q/ob19gKB/E+cLdUYjD6iiWmWeQWXeo7d9vtgJFoAiBfUe+jtl3BCn8wrifu1wOPuOc9MXgKqNcmKUPdlLOSXw=="}], "size": 5508}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.22.5_1686248511554_0.8408813321125188"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T18:21:51.693Z", "publish_time": 1686248511693, "_source_registry_name": "default"}, "8.0.0-alpha.0": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-validator-option": "^8.0.0-alpha.0", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.0", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.0", "@babel/plugin-transform-typescript": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.0", "dist": {"shasum": "3726a43fa6850b8da1110ad2d46feb41f1fb62e6", "integrity": "sha512-VIxZ8H9L0+xdDZ+myfxe9FPJ8vBEV+OS9qZUARkhP5mzIt9oapozebwUl1wE02lhxZq9HQaKlEM3RYHgdOLkzw==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.0.tgz", "fileCount": 7, "unpackedSize": 26504, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4VwS6Il2xopmNQ3OespYccb9xUwCIP/f2atPXdZ+d0AiEAvUQg874CBFtjjk/HRcL4r649/4WZw06x7cxVNr58BnU="}], "size": 5483}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.0_1689861634727_0.5128687995719028"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T14:00:34.955Z", "publish_time": 1689861634955, "_source_registry_name": "default"}, "8.0.0-alpha.1": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-validator-option": "^8.0.0-alpha.1", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.1", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.1", "@babel/plugin-transform-typescript": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.1", "dist": {"shasum": "e5a1dd97c5c6e595da841497ee52c6927ac7cec4", "integrity": "sha512-HG0oOuVOktZ6FW2jqJ3Xa9z9wlnlRMLoBX60RLoHU935KLsTjRESbDdYljDMif226Sep1J1n5NcgcwmN+SPyOQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.1.tgz", "fileCount": 7, "unpackedSize": 26504, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBOcpkWqT6Nf/hxJx+BqsR4xNK33dpOw12r4FYBit7A7AiEA7kKtvrL34mK9LYo33rLAXIqx6G/DJo8MhCHsq/WH5NI="}], "size": 5485}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.1_1690221190029_0.8071445701724302"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-24T17:53:10.215Z", "publish_time": 1690221190215, "_source_registry_name": "default"}, "8.0.0-alpha.2": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.2", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-validator-option": "^8.0.0-alpha.2", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.2", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.2", "@babel/plugin-transform-typescript": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.2", "dist": {"shasum": "9f07f05809b26b14def62133a970a181d6a14577", "integrity": "sha512-Q5SIzYjAYFzoGoVZ4pdocveCaeJ+FcALYaa/2yghY2nPbBw9qIiKdgYzzUcb1ZS+yq2oEwZUu67fm7atdWAywA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.2.tgz", "fileCount": 7, "unpackedSize": 27379, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETb5OCC2mFmH8KdNaUy0tntz6d9lJC9SB1iznoBZPXgAiAjyfQsN/VLDA6AESWZcbPzdl1F5SZQ5VtXCknq/j8U2Q=="}], "size": 5581}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.2_1691594129795_0.2179221243581113"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-09T15:15:29.965Z", "publish_time": 1691594129965, "_source_registry_name": "default"}, "7.22.11": {"name": "@babel/preset-typescript", "version": "7.22.11", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.11", "@babel/plugin-transform-typescript": "^7.22.11"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.22.11", "dist": {"shasum": "f218cd0345524ac888aa3dc32f029de5b064b575", "integrity": "sha512-tWY5wyCZYBGY7IlalfKI1rLiGlIfnwsRHZqlky0HVv8qviwQ1Uo/05M6+s+TcTCVa6Bmoo2uJW5TMFX6Wa4qVg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.22.11.tgz", "fileCount": 5, "unpackedSize": 17920, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxonXwWjaQ6r4OHFCBec+97cbOXD2FbOvHqRKA3QnMxQIgb8MGViMdr6emo52ONWVHvGxpqXsYqjf3+HkJh5BOxBc="}], "size": 5073}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.22.11_1692882532803_0.8155480649495193"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-24T13:08:53.023Z", "publish_time": 1692882533023, "_source_registry_name": "default"}, "7.22.15": {"name": "@babel/preset-typescript", "version": "7.22.15", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.15", "@babel/plugin-transform-typescript": "^7.22.15"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.22.15", "dist": {"shasum": "43db30516fae1d417d748105a0bc95f637239d48", "integrity": "sha512-HblhNmh6yM+cU4VwbBRpxFhxsTdfS1zsvH9W+gEjD0ARV9+8B4sNfpI6GuhePti84nuvhiwKS539jKPFHskA9A==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.22.15.tgz", "fileCount": 5, "unpackedSize": 17927, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGguFJmAADafNEWcEm9ysRGXdX5URHtwj2ZRhgJOQDI4AiA6UVbeic0yUqXcv5sCYOF8xP4kF69W/DB/ob0R1dMlzg=="}], "size": 5074}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.22.15_1693830328638_0.820424877930394"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-04T12:25:28.839Z", "publish_time": 1693830328839, "_source_registry_name": "default"}, "7.23.0": {"name": "@babel/preset-typescript", "version": "7.23.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/plugin-transform-typescript": "^7.22.15"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.23.0", "dist": {"shasum": "cc6602d13e7e5b2087c811912b87cf937a9129d9", "integrity": "sha512-6P6VVa/NM/VlAYj5s2Aq/gdVg8FSENCg3wlZ6Qau9AcPaoF5LbN1nyGlR9DTRIw9PpxI94e+ReydsJHcjwAweg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.23.0.tgz", "fileCount": 5, "unpackedSize": 21346, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCx3rvlKEazRPRl2uhvNcNkO24sZAbHx651bYmBs+Lc1QIhAI6RbzcC0QwI6X3ojg4mj4+Qvn+R4PIxDCtAKrloGfyn"}], "size": 5932}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.23.0_1695629508444_0.3306452505943611"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-25T08:11:48.599Z", "publish_time": 1695629508599, "_source_registry_name": "default"}, "8.0.0-alpha.3": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.3", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-validator-option": "^8.0.0-alpha.3", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.3", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.3", "@babel/plugin-transform-typescript": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.3", "dist": {"shasum": "fca964f5d427d21002c371f31b2ba14a611f665e", "integrity": "sha512-jR96sTd1i3t5PQF45VCGcMoQPd8p6TboxIEcj8m210XUdkxlIf1s9TuadiuxHVO5cgM1Ruo2DfQS0zaAEOVJ4Q==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.3.tgz", "fileCount": 5, "unpackedSize": 22090, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICQcQeBxN7+20GSoDwUdhKy8VVAR6YwPvhOO1/ezF6lQAiBbdB1zfx7DsN4NiikxYapwgZBw+pS2YGMTftjqPGJNqg=="}], "size": 5937}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.3_1695740262018_0.23724186172244077"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T14:57:42.151Z", "publish_time": 1695740262151, "_source_registry_name": "default"}, "7.23.2": {"name": "@babel/preset-typescript", "version": "7.23.2", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/plugin-transform-typescript": "^7.22.15"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.23.2", "dist": {"shasum": "c8de488130b7081f7e1482936ad3de5b018beef4", "integrity": "sha512-u4UJc1XsS1GhIGteM8rnGiIvf9rJpiVgMEeCnwlLA7WJPC+jcXWJAGxYmeqs5hOZD8BbAfnV5ezBOxQbb4OUxA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.23.2.tgz", "fileCount": 5, "unpackedSize": 21466, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZFqO9OX3OB10BLxsV6IXDV9XapRABBcf4PEUwLhfWLAIhANsfeAiYzQ9Xsr8q7fLJPW2SefaY7NoLccOGaY0yQiep"}], "size": 5981}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.23.2_1697050283526_0.6794712002985703"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-11T18:51:23.785Z", "publish_time": 1697050283785, "_source_registry_name": "default"}, "8.0.0-alpha.4": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.4", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-validator-option": "^8.0.0-alpha.4", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.4", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.4", "@babel/plugin-transform-typescript": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.4", "dist": {"shasum": "e714e52103171849e62c0b70a7a5b899d1dad70d", "integrity": "sha512-m7X1i1MDVliNOxotpXRcHzBoani1HvJhazB76ipY7KyDClEiv5S1ZLFkJoyB9lYS6E0MItmsP9RPK8D70Ap3NA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.4.tgz", "fileCount": 5, "unpackedSize": 22210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7NcAHEraVXfucJNaw0YPolvqkLXRvI+vPCODA98BG6AiAthUHLGKakduq2kj92i+nQeJY/nCCOhVdXTVfWucjfnw=="}], "size": 5978}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.4_1697076415996_0.8511410428465733"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-12T02:06:56.226Z", "publish_time": 1697076416226, "_source_registry_name": "default"}, "7.23.3": {"name": "@babel/preset-typescript", "version": "7.23.3", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/plugin-transform-typescript": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.23.3", "dist": {"shasum": "14534b34ed5b6d435aa05f1ae1c5e7adcc01d913", "integrity": "sha512-17oIGVlqz6CchO9RFYn5U6ZpWRZIngayYCtrPRSgANSwC2V1Jb+iP74nVxzzXJte8b8BYxrL1yY96xfhTBrNNQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.23.3.tgz", "fileCount": 5, "unpackedSize": 21569, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFQX6N3AS7ABArGCrTDA4chtIUW83V4V4ykdImKTr/pbAiA1ICqH9P4R84YBtWU2fU8modr2rmiX3vMeLBtb/PkvJA=="}], "size": 6020}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.23.3_1699513456163_0.10494704125457877"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-09T07:04:16.375Z", "publish_time": 1699513456375, "_source_registry_name": "default"}, "8.0.0-alpha.5": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.5", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-validator-option": "^8.0.0-alpha.5", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.5", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.5", "@babel/plugin-transform-typescript": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.5", "dist": {"shasum": "2dd4c5dd915f3d57c113a46026cd860349f64f40", "integrity": "sha512-CHiMKYNKbsVAacZFvogpaNDVT42697cR4kpzorm4fkTGRYlliyoi0pepJH8I7dMyKG88Ip9JOUVvp3/1S3RwvQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.5.tgz", "fileCount": 5, "unpackedSize": 22323, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICXxYBHybz4GOQwsQBxuGavBfKxy2gHdtKlzLz/8nwaKAiEA29mwXQfdQfK59qEcEpYfntehNZlMamkg5bLgtgfSTJY="}], "size": 6076}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.5_1702307998275_0.4100345546752182"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-11T15:19:58.436Z", "publish_time": 1702307998436, "_source_registry_name": "default"}, "8.0.0-alpha.6": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.6", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-validator-option": "^8.0.0-alpha.6", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.6", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.6", "@babel/plugin-transform-typescript": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.6", "dist": {"shasum": "c6645330c6680051f35122c78cb5f8cf2a12c723", "integrity": "sha512-4MUtbokl1mpNPayy3o+DZ7EawtK3R1pWkvuPKahjKp4TiGx5rMXmjBjRBKzfp0GlhJgwLKK3mzTB0rCzbogXUg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.6.tgz", "fileCount": 5, "unpackedSize": 22323, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO6CRzo3JLIy3w9L/vZWUtTfGn5bE7RDeGZYifENSHzwIhAMuH3mH/UILcMS+uBiM/qfVUq6Hh99oHQT+t0ey/etcl"}], "size": 6075}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.6_1706285696561_0.17625165145460686"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-26T16:14:56.824Z", "publish_time": 1706285696824, "_source_registry_name": "default"}, "8.0.0-alpha.7": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.7", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-validator-option": "^8.0.0-alpha.7", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.7", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.7", "@babel/plugin-transform-typescript": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.7", "dist": {"shasum": "6849b6298e9271990f7802c585f123ef3dc4a029", "integrity": "sha512-qGC9oZp6n/1wqw5wrjKtqtjJ4CZpDA7zc3XMIq0ZmbAUnmP72ek+H//8LDnsPj8Lk58aPsBuSgpu2YldXMN/BQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.7.tgz", "fileCount": 5, "unpackedSize": 22323, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2iqPd+WsI+WIKv9PwcAhjamoetVwFYqQedFz0XhCT8AiEAyyIntj09aUXJ8KYLabMMQD1AnAhDQls4GRlODv8nMFc="}], "size": 6076}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.7_1709129153808_0.9951243201454658"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-28T14:05:53.958Z", "publish_time": 1709129153958, "_source_registry_name": "default"}, "7.24.1": {"name": "@babel/preset-typescript", "version": "7.24.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-validator-option": "^7.23.5", "@babel/plugin-syntax-jsx": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-typescript": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.24.1", "dist": {"shasum": "89bdf13a3149a17b3b2a2c9c62547f06db8845ec", "integrity": "sha512-1DBaMmRDpuYQBPWD8Pf/WEwCrtgRHxsZnP4mIy9G/X+hFfbI47Q2G4t1Paakld84+qsk2fSsUPMKg71jkoOOaQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.24.1.tgz", "fileCount": 5, "unpackedSize": 21483, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXjsfkBt+jntS6lwF5uoSeSVaAlb33B5HTGYlw1pTfHgIhAJnFAo6eWey3ZTGzMIkfRE6ZOyg81WFH/0UkW+hsNpZ+"}], "size": 6048}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.24.1_1710841781627_0.6805584322946918"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-19T09:49:41.778Z", "publish_time": 1710841781778, "_source_registry_name": "default"}, "8.0.0-alpha.8": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.8", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-validator-option": "^8.0.0-alpha.8", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.8", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.8", "@babel/plugin-transform-typescript": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.8", "dist": {"shasum": "91e6958679ae5e67eefd4ab861357080f33bbebb", "integrity": "sha512-oRINlOGVUUT0iIcNTX3QcABbhK2Q6txwJfN/tan+nvQ0cFaXSz8AQ8ylvtqCX60uvXD+5SjFWwwwWQVlTi7x5g==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.8.tgz", "fileCount": 5, "unpackedSize": 22237, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCok7qG59uPB6wdp2W25PVErBLktrz0IOFSbtfs7KVb6wIgLB31P4a+cGVZMpdz/oUdKrYmLs/BQTGTm+BMPMYC8aI="}], "size": 6054}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.8_1712236825432_0.7861934734679772"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-04T13:20:25.626Z", "publish_time": 1712236825626, "_source_registry_name": "default"}, "7.24.6": {"name": "@babel/preset-typescript", "version": "7.24.6", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-validator-option": "^7.24.6", "@babel/plugin-syntax-jsx": "^7.24.6", "@babel/plugin-transform-modules-commonjs": "^7.24.6", "@babel/plugin-transform-typescript": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.24.6", "dist": {"shasum": "27057470fb981c31338bdb897fc3d9aa0cb7dab2", "integrity": "sha512-U10aHPDnokCFRXgyT/MaIRTivUu2K/mu0vJlwRS9LxJmJet+PFQNKpggPyFCUtC6zWSBPjvxjnpNkAn3Uw2m5w==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.24.6.tgz", "fileCount": 7, "unpackedSize": 90917, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsLhgFpTPrr7q96OIkBlJL1QWFJQqHDVEiW0j/ZHGqYAiBvLf2d1eq4dnGUlbmcB2Y9PpSi+tWErWNsWbBFM2Qcyw=="}], "size": 30122}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.24.6_1716553515895_0.5114624453874381"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-24T12:25:16.067Z", "publish_time": 1716553516067, "_source_registry_name": "default"}, "8.0.0-alpha.9": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.9", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-validator-option": "^8.0.0-alpha.9", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.9", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.9", "@babel/plugin-transform-typescript": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.9", "dist": {"shasum": "d34bb74a0ec7cb904797c46992f1d87dd6c356b0", "integrity": "sha512-UmZpTnrYtF8g4fwUAhJmPe7qhpqdcnG3Bp12M9EB4KIQ4Lu+7c4QCbbMN2krCpTlxEourBs/Dr2RVjvrsvEZPA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.9.tgz", "fileCount": 8, "unpackedSize": 92254, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICF2rw6CPNcKLh3pk2y3V0vYIn7PEJHz9JxzygpkSeBHAiEAvjs3S4XuYGwiuv2pZMewhEi21xuT+Opd+5xXlxwTZoE="}], "size": 30321}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.9_1717423556949_0.2602415752694418"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-03T14:05:57.125Z", "publish_time": 1717423557125, "_source_registry_name": "default"}, "8.0.0-alpha.10": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.10", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-validator-option": "^8.0.0-alpha.10", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.10", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.10", "@babel/plugin-transform-typescript": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.10", "dist": {"shasum": "6474029c2317249aeb9de1263c01cdf7e00c973a", "integrity": "sha512-UbfJdPFIhVn2nZAbYroDMKhMDif4OP5ZbVkAdagTkArvQ+nlb6uS51H0xFakNi2OlXAzkh/AHTPugVm78J6ZgQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.10.tgz", "fileCount": 8, "unpackedSize": 92265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/7zLRJl0JNcvJGiWSejlelMRtL35dV1c0+qIdNmnuKAiANcN/JiQ8S/M/399SSHDRfu1GN4V7NoWKPS/hMp2Pqfg=="}], "size": 30324}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.10_1717500049797_0.7786720736504327"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-04T11:20:50.019Z", "publish_time": 1717500050019, "_source_registry_name": "default"}, "7.24.7": {"name": "@babel/preset-typescript", "version": "7.24.7", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-validator-option": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.24.7", "dist": {"shasum": "66cd86ea8f8c014855671d5ea9a737139cbbfef1", "integrity": "sha512-SyXRe3OdWwIwalxDg5UtJnJQO+YPcTfwiIY2B0Xlddh9o7jpWLvv8X1RthIeDOxQ+O1ML5BLPCONToObyVQVuQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.24.7.tgz", "fileCount": 7, "unpackedSize": 90837, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHPSuuumin5h/dufu1IF+s6Rw2VfKKOPgUuyrB7H6K4gIhAN+CvL/1NNdPzUIAYK6T1I2dcQRRLgywmu14KdJAaDjt"}], "size": 30115}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.24.7_1717593362875_0.23552571473710038"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T13:16:03.023Z", "publish_time": 1717593363023, "_source_registry_name": "default"}, "8.0.0-alpha.11": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.11", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-validator-option": "^8.0.0-alpha.11", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.11", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.11", "@babel/plugin-transform-typescript": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.11", "dist": {"shasum": "3727b85cb142c51ec1e7b57f3d09cca221116e17", "integrity": "sha512-slTmIJpABeQbjlUAeXn2/jvaPs913Bj2TfNwxRbTiiNXQmR+GSzjNAjqCQQwheBUwNRBEBtXSTCL54po+ZMGRg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.11.tgz", "fileCount": 8, "unpackedSize": 92156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLq8ZDYaJzC2PJTKPjkDV5UIvCm3NicUgI/YtlUx1BkAiAlsgIq3hRek2dKOzkF7JsJ+vZCU0/YGJqkWqw3hcMtSQ=="}], "size": 30288}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.11_1717751773246_0.02117760584805306"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T09:16:13.411Z", "publish_time": 1717751773411, "_source_registry_name": "default"}, "8.0.0-alpha.12": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.12", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-validator-option": "^8.0.0-alpha.12", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.12", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.12", "@babel/plugin-transform-typescript": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.12", "dist": {"shasum": "1ab4c3d69c7c3ca0df8644b941d7ef5d09d40827", "integrity": "sha512-DoUcYIJSk7G9fo5u2beyVZBJVesS/CorST5G0yCQa9QVMTAvU7ftGaDRExSx3zWBk7N+h9ARaRrpwuMmm5YRzA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.12.tgz", "fileCount": 8, "unpackedSize": 88721, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+9CDJSC4AaAR85vGhNyn3wTC9+4cy6CDV0g5cHHvHCwIhAMvNp1tlzCrSfmfGJoTS1kQR2iUxVS8WJOxuRJQstJdr"}], "size": 29874}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.12_1722015248562_0.6057672202067628"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-26T17:34:08.784Z", "publish_time": 1722015248784, "_source_registry_name": "default"}, "7.25.7": {"name": "@babel/preset-typescript", "version": "7.25.7", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-validator-option": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/plugin-transform-modules-commonjs": "^7.25.7", "@babel/plugin-transform-typescript": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.25.7", "dist": {"shasum": "43c5b68eccb856ae5b52274b77b1c3c413cde1b7", "integrity": "sha512-rkkpaXJZOFN45Fb+Gki0c+KMIglk4+zZXOoMJuyEK8y8Kkc8Jd3BDmP7qPsz0zQMJj+UD7EprF+AqAXcILnexw==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.25.7.tgz", "fileCount": 7, "unpackedSize": 95229, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICE6IZkAba5VJKHfnOKTVsNnDB90HG8YFj88Ic+J09n3AiBMgyQRnLOlGKoMpdst6LaL4IPjENnvFpjE8Oh7hrp7gg=="}], "size": 30258}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.25.7_1727882140084_0.7211433929157611"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-02T15:15:40.309Z", "publish_time": 1727882140309, "_source_registry_name": "default"}, "7.25.9": {"name": "@babel/preset-typescript", "version": "7.25.9", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.25.9", "dist": {"shasum": "bb82f26cda46dc2eb1ee10bf72fa994e759a08ba", "integrity": "sha512-XWxw1AcKk36kgxf4C//fl0ikjLeqGUWn062/Fd8GtpTfDJOX6Ud95FK+4JlDA36BX4bNGndXi3a6Vr4Jo5/61A==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.25.9.tgz", "fileCount": 5, "unpackedSize": 21441, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC57GNW3oYWBqwnCws6hvEgYU5vm8QMStpqwHl8W9YJMAIgQ7fpjfYZfd83rShUvWeFQM9/EqxyRjCrQl1vysXr4VM="}], "size": 6031}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_7.25.9_1729610515170_0.7293983095659742"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-22T15:21:55.394Z", "publish_time": 1729610515394, "_source_registry_name": "default"}, "7.26.0": {"name": "@babel/preset-typescript", "version": "7.26.0", "keywords": ["babel-preset", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-typescript@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "dist": {"shasum": "4a570f1b8d104a242d923957ffa1eaff142a106d", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-NMk1IGZ5I/oHhoXEElcm+xUnL/szL6xflkFZmoEU9xj1qSJXpiS7rsspYo92B4DRCDvZn2erT5LdsCeXAKNCkg==", "signatures": [{"sig": "MEQCIGWHCXXmLAJZUCZr1sC6q6XjneTaT3qaOFvJGqr9MJP3AiBlF/xUWmGg23n7I4ZvhSnKLjYZCqXzFWbgUcmOIgpqag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23591, "size": 6579}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-typescript"}, "description": "Babel preset for TypeScript.", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-typescript_7.26.0_1729863009620_0.2856311247583083", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-10-25T13:30:09.805Z", "publish_time": 1729863009805, "_source_registry_name": "default"}, "8.0.0-alpha.13": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.13", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-validator-option": "^8.0.0-alpha.13", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.13", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.13", "@babel/plugin-transform-typescript": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.13", "dist": {"shasum": "7d3b4a9e081ca594886b6c08295b788cd748425a", "integrity": "sha512-Z4KLrOg34y4E5aDME0Cfn1n2RLK44xjBwa3kQLriNlAgbhmXcRKmhXqVa3yjYS3bZtokHsJKJYtFm9n61QvE9A==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.13.tgz", "fileCount": 6, "unpackedSize": 25017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD30GVDr+5cKLikKTDfeMMykQL/EP5zqbHQ3GTi3O1s7gIgCldvZGgmV1OwLcY5kZGV4jKf5OOtn4RunQd0zIrsLsM="}], "size": 6783}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.13_1729864495505_0.24295956030207733"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-25T13:54:55.697Z", "publish_time": 1729864495697, "_source_registry_name": "default"}, "8.0.0-alpha.14": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.14", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-validator-option": "^8.0.0-alpha.14", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.14", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.14", "@babel/plugin-transform-typescript": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.14", "dist": {"shasum": "8a9a35d6856c1a47463089c0cfdb5cb754ce2f87", "integrity": "sha512-RpoAHfc6r7kSXzfT4JrX9v9QdGK6OPgSRlAJmaDufCWgnmj4UWTBtNIPa3DwWkkbKKfyV1RIT4LSC7dITAHK4g==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.14.tgz", "fileCount": 6, "unpackedSize": 25017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1zZm3HtPMHIFiezjfLOyOY7y716xK2sn1VCL3YmnjQAIgN/6ICJbWSotZD977pKJ2ltmk9WVwDTfkMaFjzgMW38c="}], "size": 6782}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/preset-typescript_8.0.0-alpha.14_1733504084416_0.3911134156204532"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T16:54:44.599Z", "publish_time": 1733504084599, "_source_registry_name": "default"}, "8.0.0-alpha.15": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.15", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-validator-option": "^8.0.0-alpha.15", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.15", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.15", "dist": {"shasum": "fbf0afc6e324e213e6c767187145722f4affa731", "integrity": "sha512-xAGZaLpyBVOtGRtCa7/keqCLDN/O/3dUfLwQd/pWVyWCLKWJBYL8NZnMfed4YeiumWOdOQgQPhV2p+PatXC6NQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.15.tgz", "fileCount": 6, "unpackedSize": 25017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHJ21XzecNUCYOkpc56G/cLfsXTQzsnme1VuAwe85kQTAiBL80Hq8JmmgSSbwxyJFIKyKekDZh8tenFR4rPGA3nc/w=="}], "size": 6783}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_8.0.0-alpha.15_1736529914856_0.6788088374445462"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-10T17:25:15.045Z", "publish_time": 1736529915045, "_source_registry_name": "default"}, "8.0.0-alpha.16": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.16", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-validator-option": "^8.0.0-alpha.16", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.16", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.16", "@babel/plugin-transform-typescript": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.16", "dist": {"shasum": "0af8ac0cf904ed4c21a8db855f5bde757cf2d0cc", "integrity": "sha512-385Jknt1SRxQ/6Uh/hAqf/sT5+sN9tCnHT8Fyl3SpOtFrjFJ5yot6nfu5Cx+Sb4Aq4t4k0HcmMGFhEyr3bVUSA==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.16.tgz", "fileCount": 6, "unpackedSize": 25017, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDBUSkxC2bLQW0YKL2GbEBM/yXc1Hjs/TQtphqF0UJzbAiA7d15Sw6E5dtaTgLxhGy0hnYyReuzQWlFHWN5lORz9mA=="}], "size": 6783}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_8.0.0-alpha.16_1739534388254_0.16983013097711064"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T11:59:48.446Z", "publish_time": 1739534388446, "_source_registry_name": "default"}, "8.0.0-alpha.17": {"name": "@babel/preset-typescript", "version": "8.0.0-alpha.17", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-validator-option": "^8.0.0-alpha.17", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.17", "@babel/plugin-transform-modules-commonjs": "^8.0.0-alpha.17", "@babel/plugin-transform-typescript": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-alpha.17", "dist": {"shasum": "7c669ce18321755332bda80f8c6e2cfca183ad8e", "integrity": "sha512-KtNvbmF9cyu0BD1T3F+e/4lGPIvy4tX1AuVG1P3FvQ67lV63CkdCwlkVhHO8mzz+2fDXVCaA7IpuC4aZp8nyrQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-alpha.17.tgz", "fileCount": 6, "unpackedSize": 25017, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCeSuRaPSh+FXhZ8z9Hg2al5gQ6/h9A8JfWofk+6J468wIgNnu62wsjJSkpCdF20Aunfce8vd9qnjCIMdYvVtHMvSk="}], "size": 6783}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_8.0.0-alpha.17_1741717542847_0.3009011146683447"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-11T18:25:43.043Z", "publish_time": 1741717543043, "_source_registry_name": "default"}, "7.27.0": {"name": "@babel/preset-typescript", "version": "7.27.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-typescript": "^7.27.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.27.0", "dist": {"shasum": "4dcb8827225975f4290961b0b089f9c694ca50c7", "integrity": "sha512-vxaPFfJtHhgeOVXRKuHpHPAOgymmy8V8I65T1q53R7GCZlefKeCaTyDs3zOPHTTbmquvNlQYC5klEvWsBAtrBQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.27.0.tgz", "fileCount": 5, "unpackedSize": 25779, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCHEtOB4ksFrajVvoXKlzp9IpNuWMcPH80ftxOUVEJ01wIgDRjWpp3ZL/pdzTbRLjYvssEOIi/1Mz8HhA7uzBBb+Z4="}], "size": 7269}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_7.27.0_1742838116886_0.6996667041390741"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-24T17:41:57.077Z", "publish_time": 1742838117077, "_source_registry_name": "default"}, "7.27.1": {"name": "@babel/preset-typescript", "version": "7.27.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/preset-typescript@7.27.1", "dist": {"shasum": "190742a6428d282306648a55b0529b561484f912", "integrity": "sha512-l7WfQfX0WK4M0v2RudjuQK4u99BS6yLHYEmdtVPP7lKV013zr9DygFuWNlnbvQ9LR+LS0Egz/XAvGx5U9MX0fQ==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz", "fileCount": 5, "unpackedSize": 25778, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEMCH1tl1vaBcyk3NYGBqRSCRJ/iNAVBMAAFxM5FWtuwkTgCIEuNoJIv0rUenL0i1Km9SmJ2BEIWMC53f89jaZjcRIAQ"}], "size": 7253}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_7.27.1_1746025777082_0.6806300016370468"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-30T15:09:37.243Z", "publish_time": 1746025777243, "_source_registry_name": "default"}, "8.0.0-beta.0": {"name": "@babel/preset-typescript", "version": "8.0.0-beta.0", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-validator-option": "^8.0.0-beta.0", "@babel/plugin-syntax-jsx": "^8.0.0-beta.0", "@babel/plugin-transform-modules-commonjs": "^8.0.0-beta.0", "@babel/plugin-transform-typescript": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-beta.0", "dist": {"shasum": "dd03a8ea10929cc8ad7bb3e8b308e59223c965da", "integrity": "sha512-OZwHnFE0OP8/571ZdupS5QwSUowCnHHr68oogdVnAQ9tOgzEVuPZUBqaMq13Nl/U2Qv59HiKNSMMzmmiCSq7kw==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-beta.0.tgz", "fileCount": 6, "unpackedSize": 26401, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDaECJXSiCp6/z6TqHB4AShH8xgqFsGJ+rGxXs0oJ05MwIhANVgPhbWc1dCmBmmxLVzMZMWF+ejBUaKR4F50Y1uEcJw"}], "size": 7267}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_8.0.0-beta.0_1748620313827_0.4886353056848023"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-30T15:51:53.998Z", "publish_time": 1748620313998, "_source_registry_name": "default"}, "8.0.0-beta.1": {"name": "@babel/preset-typescript", "version": "8.0.0-beta.1", "description": "Babel preset for TypeScript.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-validator-option": "^8.0.0-beta.1", "@babel/plugin-syntax-jsx": "^8.0.0-beta.1", "@babel/plugin-transform-modules-commonjs": "^8.0.0-beta.1", "@babel/plugin-transform-typescript": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-typescript@8.0.0-beta.1", "dist": {"shasum": "556c5f87cff59cfc5f23cbdb8a0fe5e0463c8821", "integrity": "sha512-wlrteo6GDpNyt6EkDdwnzGy5MpQgRa/T5TaVriYk98ToQSC2eOASMWgARa2owsvnvIwPzyoABouVdB17dxRtsg==", "tarball": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 26401, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD1xbEu8Wxt6MtKKqMjjpwqxWTBzbaOGRXdGGdq3kqhUQIgO+sNFkBJxGmTy6e4f0qJLr7OWMbPGG5BL2kUZ3U5N8w="}], "size": 7269}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-typescript_8.0.0-beta.1_1751447095290_0.27917440152660533"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T09:04:55.469Z", "publish_time": 1751447095469, "_source_registry_name": "default"}}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20typescript%22+is%3Aopen", "homepage": "https://babel.dev/docs/en/next/babel-preset-typescript", "keywords": ["babel-preset", "typescript"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-typescript"}, "_source_registry_name": "default"}