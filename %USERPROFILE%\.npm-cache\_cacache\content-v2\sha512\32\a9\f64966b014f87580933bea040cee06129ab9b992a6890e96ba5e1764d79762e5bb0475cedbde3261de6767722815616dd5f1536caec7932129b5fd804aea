{"_attachments": {}, "_id": "memoize-one", "_rev": "3732-61f14d9ca920628a7b6ff5a6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A memoization library which only remembers the latest invocation", "dist-tags": {"beta": "6.0.0-beta.1", "latest": "6.0.0"}, "license": "MIT", "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "memoize-one", "readme": "# memoize-one\n\nA memoization library that only caches the result of the most recent arguments.\n\n[![npm](https://img.shields.io/npm/v/memoize-one.svg)](https://www.npmjs.com/package/memoize-one)\n![types](https://img.shields.io/badge/types-typescript%20%7C%20flow-blueviolet)\n[![minzip](https://img.shields.io/bundlephobia/minzip/memoize-one.svg)](https://www.npmjs.com/package/memoize-one)\n[![Downloads per month](https://img.shields.io/npm/dm/memoize-one.svg)](https://www.npmjs.com/package/memoize-one)\n\n## Rationale\n\nUnlike other memoization libraries, `memoize-one` only remembers the latest arguments and result. No need to worry about cache busting mechanisms such as `maxAge`, `maxSize`, `exclusions` and so on, which can be prone to memory leaks. A function memoized with `memoize-one` simply remembers the last arguments, and if the memoized function is next called with the same arguments then it returns the previous result.\n\n> For working with promises, [@Kikobeats](https://github.com/Kikobeats) has built [async-memoize-one](https://github.com/microlinkhq/async-memoize-one).\n\n## Usage\n\n```js\n// memoize-one uses the default import\nimport memoizeOne from 'memoize-one';\n\nfunction add(a, b) {\n  return a + b;\n}\nconst memoizedAdd = memoizeOne(add);\n\nmemoizedAdd(1, 2);\n// add function: is called\n// [new value returned: 3]\n\nmemoizedAdd(1, 2);\n// add function: not called\n// [cached result is returned: 3]\n\nmemoizedAdd(2, 3);\n// add function: is called\n// [new value returned: 5]\n\nmemoizedAdd(2, 3);\n// add function: not called\n// [cached result is returned: 5]\n\nmemoizedAdd(1, 2);\n// add function: is called\n// [new value returned: 3]\n// ????\n// While the result of `add(1, 2)` was previously cached\n// `(1, 2)` was not the *latest* arguments (the last call was `(2, 3)`)\n// so the previous cached result of `(1, 3)` was lost\n```\n\n## Installation\n\n```bash\n# yarn\nyarn add memoize-one\n\n# npm\nnpm install memoize-one --save\n```\n\n## Function argument equality\n\nBy default, we apply our own _fast_ and _relatively naive_ equality function to determine whether the arguments provided to your function are equal. You can see the full code here: [are-inputs-equal.ts](https://github.com/alexreardon/memoize-one/blob/master/src/are-inputs-equal.ts).\n\n(By default) function arguments are considered equal if:\n\n1. there is same amount of arguments\n2. each new argument has strict equality (`===`) with the previous argument\n3. **[special case]** if two arguments are not `===` and they are both `NaN` then the two arguments are treated as equal\n\nWhat this looks like in practice:\n\n```js\nimport memoizeOne from 'memoize-one';\n\n// add all numbers provided to the function\nconst add = (...args = []) =>\n  args.reduce((current, value) => {\n    return current + value;\n  }, 0);\nconst memoizedAdd = memoizeOne(add);\n```\n\n> 1. there is same amount of arguments\n\n```js\nmemoizedAdd(1, 2);\n// the amount of arguments has changed, so underlying add function is called\nmemoizedAdd(1, 2, 3);\n```\n\n> 2. new arguments have strict equality (`===`) with the previous argument\n\n```js\nmemoizedAdd(1, 2);\n// each argument is `===` to the last argument, so cache is used\nmemoizedAdd(1, 2);\n// second argument has changed, so add function is called again\nmemoizedAdd(1, 3);\n// the first value is not `===` to the previous first value (1 !== 3)\n// so add function is called again\nmemoizedAdd(3, 1);\n```\n\n> 3. **[special case]** if the arguments are not `===` and they are both `NaN` then the argument is treated as equal\n\n```js\nmemoizedAdd(NaN);\n// Even though NaN !== NaN these arguments are\n// treated as equal as they are both `NaN`\nmemoizedAdd(NaN);\n```\n\n## Custom equality function\n\nYou can also pass in a custom function for checking the equality of two sets of arguments\n\n```js\nconst memoized = memoizeOne(fn, isEqual);\n```\n\nAn equality function should return `true` if the arguments are equal. If `true` is returned then the wrapped function will not be called.\n\n**Tip**: A custom equality function needs to compare `Arrays`. The `newArgs` array will be a new reference every time so a simple `newArgs === lastArgs` will always return `false`.\n\nEquality functions are not called if the `this` context of the function has changed (see below).\n\nHere is an example that uses a [lodash.isEqual](https://lodash.com/docs/4.17.15#isEqual) deep equal equality check\n\n> `lodash.isequal` correctly handles deep comparing two arrays\n\n```js\nimport memoizeOne from 'memoize-one';\nimport isDeepEqual from 'lodash.isequal';\n\nconst identity = (x) => x;\n\nconst shallowMemoized = memoizeOne(identity);\nconst deepMemoized = memoizeOne(identity, isDeepEqual);\n\nconst result1 = shallowMemoized({ foo: 'bar' });\nconst result2 = shallowMemoized({ foo: 'bar' });\n\nresult1 === result2; // false - different object reference\n\nconst result3 = deepMemoized({ foo: 'bar' });\nconst result4 = deepMemoized({ foo: 'bar' });\n\nresult3 === result4; // true - arguments are deep equal\n```\n\nThe equality function needs to conform to the `EqualityFn` `type`:\n\n```ts\n// TFunc is the function being memoized\ntype EqualityFn<TFunc extends (...args: any[]) => any> = (\n  newArgs: Parameters<TFunc>,\n  lastArgs: Parameters<TFunc>,\n) => boolean;\n\n// You can import this type\nimport type { EqualityFn } from 'memoize-one';\n```\n\nThe `EqualityFn` type allows you to create equality functions that are extremely typesafe. You are welcome to provide your own less type safe equality functions.\n\nHere are some examples of equality functions which are ordered by most type safe, to least type safe:\n\n<details>\n  <summary>Example equality function types</summary>\n  <p>\n\n```ts\n// the function we are going to memoize\nfunction add(first: number, second: number): number {\n  return first + second;\n}\n\n// Some options for our equality function\n// ↑ stronger types\n// ↓ weaker types\n\n// ✅ exact parameters of `add`\n{\n  const isEqual = function (first: Parameters<typeof add>, second: Parameters<typeof add>) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ tuple of the correct types\n{\n  const isEqual = function (first: [number, number], second: [number, number]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ❌ tuple of incorrect types\n{\n  const isEqual = function (first: [number, string], second: [number, number]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().not.toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ array of the correct types\n{\n  const isEqual = function (first: number[], second: number[]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ❌ array of incorrect types\n{\n  const isEqual = function (first: string[], second: number[]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().not.toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ tuple of 'unknown'\n{\n  const isEqual = function (first: [unknown, unknown], second: [unknown, unknown]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ❌ tuple of 'unknown' of incorrect length\n{\n  const isEqual = function (first: [unknown, unknown, unknown], second: [unknown, unknown]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().not.toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ array of 'unknown'\n{\n  const isEqual = function (first: unknown[], second: unknown[]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ spread of 'unknown'\n{\n  const isEqual = function (...first: unknown[]) {\n    return !!first;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ tuple of 'any'\n{\n  const isEqual = function (first: [any, any], second: [any, any]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ❌ tuple of 'any' or incorrect size\n{\n  const isEqual = function (first: [any, any, any], second: [any, any]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().not.toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ array of 'any'\n{\n  const isEqual = function (first: any[], second: any[]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ two arguments of type any\n{\n  const isEqual = function (first: any, second: any) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ a single argument of type any\n{\n  const isEqual = function (first: any) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n\n// ✅ spread of any type\n{\n  const isEqual = function (...first: any[]) {\n    return true;\n  };\n  expectTypeOf<typeof isEqual>().toMatchTypeOf<EqualityFn<typeof add>>();\n}\n```\n\n  </p>\n</details>\n\n## `this`\n\n### `memoize-one` correctly respects `this` control\n\nThis library takes special care to maintain, and allow control over the the `this` context for **both** the original function being memoized as well as the returned memoized function. Both the original function and the memoized function's `this` context respect [all the `this` controlling techniques](https://github.com/getify/You-Dont-Know-JS/blob/master/this%20%26%20object%20prototypes/ch2.md):\n\n- new bindings (`new`)\n- explicit binding (`call`, `apply`, `bind`);\n- implicit binding (call site: `obj.foo()`);\n- default binding (`window` or `undefined` in `strict mode`);\n- fat arrow binding (binding to lexical `this`)\n- ignored this (pass `null` as `this` to explicit binding)\n\n### Changes to `this` is considered an argument change\n\nChanges to the running context (`this`) of a function can result in the function returning a different value even though its arguments have stayed the same:\n\n```js\nfunction getA() {\n  return this.a;\n}\n\nconst temp1 = {\n  a: 20,\n};\nconst temp2 = {\n  a: 30,\n};\n\ngetA.call(temp1); // 20\ngetA.call(temp2); // 30\n```\n\nTherefore, in order to prevent against unexpected results, `memoize-one` takes into account the current execution context (`this`) of the memoized function. If `this` is different to the previous invocation then it is considered a change in argument. [further discussion](https://github.com/alexreardon/memoize-one/issues/3).\n\nGenerally this will be of no impact if you are not explicity controlling the `this` context of functions you want to memoize with [explicit binding](https://github.com/getify/You-Dont-Know-JS/blob/master/this%20%26%20object%20prototypes/ch2.md#explicit-binding) or [implicit binding](https://github.com/getify/You-Dont-Know-JS/blob/master/this%20%26%20object%20prototypes/ch2.md#implicit-binding). `memoize-One` will detect when you are manipulating `this` and will then consider the `this` context as an argument. If `this` changes, it will re-execute the original function even if the arguments have not changed.\n\n## Clearing the memoization cache\n\nA `.clear()` property is added to memoized functions to allow you to clear it's memoization cache.\n\nThis is helpful if you want to:\n\n- Release memory\n- Allow the underlying function to be called again without having to change arguments\n\n```ts\nimport memoizeOne from 'memoize-one';\n\nfunction add(a: number, b: number): number {\n  return a + b;\n}\n\nconst memoizedAdd = memoizeOne(add);\n\n// first call - not memoized\nconst first = memoizedAdd(1, 2);\n\n// second call - cache hit (underlying function not called)\nconst second = memoizedAdd(1, 2);\n\n// ???? clearing memoization cache\nmemoizedAdd.clear();\n\n// third call - not memoized (cache was cleared)\nconst third = memoizedAdd(1, 2);\n```\n\n## When your result function `throw`s\n\n> There is no caching when your result function throws\n\nIf your result function `throw`s then the memoized function will also throw. The throw will not break the memoized functions existing argument cache. It means the memoized function will pretend like it was never called with arguments that made it `throw`.\n\n```js\nconst canThrow = (name: string) => {\n  console.log('called');\n  if (name === 'throw') {\n    throw new Error(name);\n  }\n  return { name };\n};\n\nconst memoized = memoizeOne(canThrow);\n\nconst value1 = memoized('Alex');\n// console.log => 'called'\nconst value2 = memoized('Alex');\n// result function not called\n\nconsole.log(value1 === value2);\n// console.log => true\n\ntry {\n  memoized('throw');\n  // console.log => 'called'\n} catch (e) {\n  firstError = e;\n}\n\ntry {\n  memoized('throw');\n  // console.log => 'called'\n  // the result function was called again even though it was called twice\n  // with the 'throw' string\n} catch (e) {\n  secondError = e;\n}\n\nconsole.log(firstError !== secondError);\n\nconst value3 = memoized('Alex');\n// result function not called as the original memoization cache has not been busted\nconsole.log(value1 === value3);\n// console.log => true\n```\n\n## Function properties\n\nFunctions memoized with `memoize-one` do not preserve any properties on the function object.\n\n> This behaviour correctly reflected in the TypeScript types\n\n```ts\nimport memoizeOne from 'memoize-one';\n\nfunction add(a, b) {\n  return a + b;\n}\nadd.hello = 'hi';\n\nconsole.log(typeof add.hello); // string\n\nconst memoized = memoizeOne(add);\n\n// hello property on the `add` was not preserved\nconsole.log(typeof memoized.hello); // undefined\n```\n\nIf you feel strongly that `memoize-one` _should_ preserve function properties, please raise an issue. This decision was made in order to keep `memoize-one` as light as possible.\n\nFor _now_, the `.length` property of a function is not preserved on the memoized function\n\n```ts\nimport memoizeOne from 'memoize-one';\n\nfunction add(a, b) {\n  return a + b;\n}\n\nconsole.log(add.length); // 2\n\nconst memoized = memoizeOne(add);\n\nconsole.log(memoized.length); // 0\n```\n\nThere is no (great) way to correctly set the `.length` property of the memoized function while also supporting ie11. Once we [remove ie11 support](https://github.com/alexreardon/memoize-one/issues/125) then we will set the `.length` property of the memoized function to match the original function\n\n[→ discussion](https://github.com/alexreardon/memoize-one/pull/124).\n\n## Memoized function `type`\n\nThe resulting function you get back from `memoize-one` has *almost* the same `type` as the function that you are memoizing\n\n```ts\ndeclare type MemoizedFn<TFunc extends (this: any, ...args: any[]) => any> = {\n  clear: () => void;\n  (this: ThisParameterType<TFunc>, ...args: Parameters<TFunc>): ReturnType<TFunc>;\n};\n```\n\n- the same call signature as the function being memoized\n- a `.clear()` function property added\n- other function object properties on `TFunc` as not carried over\n\nYou are welcome to use the `MemoizedFn` generic directly from `memoize-one` if you like:\n\n```ts\nimport memoize, { MemoizedFn } from 'memoize-one';\nimport isDeepEqual from 'lodash.isequal';\nimport { expectTypeOf } from 'expect-type';\n\n// Takes any function: TFunc, and returns a Memoized<TFunc>\nfunction withDeepEqual<TFunc extends (...args: any[]) => any>(fn: TFunc): MemoizedFn<TFunc> {\n  return memoize(fn, isDeepEqual);\n}\n\nfunction add(first: number, second: number): number {\n  return first + second;\n}\n\nconst memoized = withDeepEqual(add);\n\nexpectTypeOf<typeof memoized>().toEqualTypeOf<MemoizedFn<typeof add>>();\n```\n\nIn this specific example, this type would have been correctly inferred too\n\n```ts\nimport memoize, { MemoizedFn } from 'memoize-one';\nimport isDeepEqual from 'lodash.isequal';\nimport { expectTypeOf } from 'expect-type';\n\n// return type of MemoizedFn<TFunc> is inferred\nfunction withDeepEqual<TFunc extends (...args: any[]) => any>(fn: TFunc) {\n  return memoize(fn, isDeepEqual);\n}\n\nfunction add(first: number, second: number): number {\n  return first + second;\n}\n\nconst memoized = withDeepEqual(add);\n\n// type test still passes\nexpectTypeOf<typeof memoized>().toEqualTypeOf<MemoizedFn<typeof add>>();\n```\n\n## Performance ????\n\n### Tiny\n\n`memoize-one` is super lightweight at [![min](https://img.shields.io/bundlephobia/min/memoize-one.svg?label=)](https://www.npmjs.com/package/memoize-one) minified and [![minzip](https://img.shields.io/bundlephobia/minzip/memoize-one.svg?label=)](https://www.npmjs.com/package/memoize-one) gzipped. (`1KB` = `1,024 Bytes`)\n\n### Extremely fast\n\n`memoize-one` performs better or on par with than other popular memoization libraries for the purpose of remembering the latest invocation.\n\nThe comparisons are not exhaustive and are primarily to show that `memoize-one` accomplishes remembering the latest invocation really fast. There is variability between runs. The benchmarks do not take into account the differences in feature sets, library sizes, parse time, and so on.\n\n<details>\n  <summary>Expand for results</summary>\n  <p>\n\nnode version `16.11.1`\n\nYou can run this test in the repo by:\n\n1. Add `\"type\": \"module\"` to the `package.json` (why is things so hard)\n2. Run `yarn perf:library-comparison`\n\n**no arguments**\n\n| Position | Library                                      | Operations per second |\n| -------- | -------------------------------------------- | --------------------- |\n| 1        | memoize-one                                  | 80,112,981            |\n| 2        | moize                                        | 72,885,631            |\n| 3        | memoizee                                     | 35,550,009            |\n| 4        | mem (JSON.stringify strategy)                | 4,610,532             |\n| 5        | lodash.memoize (JSON.stringify key resolver) | 3,708,945             |\n| 6        | no memoization                               | 505                   |\n| 7        | fast-memoize                                 | 504                   |\n\n**single primitive argument**\n\n| Position | Library                                      | Operations per second |\n| -------- | -------------------------------------------- | --------------------- |\n| 1        | fast-memoize                                 | 45,482,711            |\n| 2        | moize                                        | 34,810,659            |\n| 3        | memoize-one                                  | 29,030,828            |\n| 4        | memoizee                                     | 23,467,065            |\n| 5        | mem (JSON.stringify strategy)                | 3,985,223             |\n| 6        | lodash.memoize (JSON.stringify key resolver) | 3,369,297             |\n| 7        | no memoization                               | 507                   |\n\n**single complex argument**\n\n| Position | Library                                      | Operations per second |\n| -------- | -------------------------------------------- | --------------------- |\n| 1        | moize                                        | 27,660,856            |\n| 2        | memoize-one                                  | 22,407,916            |\n| 3        | memoizee                                     | 19,546,835            |\n| 4        | mem (JSON.stringify strategy)                | 2,068,038             |\n| 5        | lodash.memoize (JSON.stringify key resolver) | 1,911,335             |\n| 6        | fast-memoize                                 | 1,633,855             |\n| 7        | no memoization                               | 504                   |\n\n**multiple primitive arguments**\n\n| Position | Library                                      | Operations per second |\n| -------- | -------------------------------------------- | --------------------- |\n| 1        | moize                                        | 22,366,497            |\n| 2        | memoize-one                                  | 17,241,995            |\n| 3        | memoizee                                     | 9,789,442             |\n| 4        | mem (JSON.stringify strategy)                | 3,065,328             |\n| 5        | lodash.memoize (JSON.stringify key resolver) | 2,663,599             |\n| 6        | fast-memoize                                 | 1,219,548             |\n| 7        | no memoization                               | 504                   |\n\n**multiple complex arguments**\n\n| Position | Library                                      | Operations per second |\n| -------- | -------------------------------------------- | --------------------- |\n| 1        | moize                                        | 21,788,081            |\n| 2        | memoize-one                                  | 17,321,248            |\n| 3        | memoizee                                     | 9,595,420             |\n| 4        | lodash.memoize (JSON.stringify key resolver) | 873,283               |\n| 5        | mem (JSON.stringify strategy)                | 850,779               |\n| 6        | fast-memoize                                 | 687,863               |\n| 7        | no memoization                               | 504                   |\n\n**multiple complex arguments (spreading arguments)**\n\n| Position | Library                                      | Operations per second |\n| -------- | -------------------------------------------- | --------------------- |\n| 1        | moize                                        | 21,701,537            |\n| 2        | memoizee                                     | 19,463,942            |\n| 3        | memoize-one                                  | 17,027,544            |\n| 4        | lodash.memoize (JSON.stringify key resolver) | 887,816               |\n| 5        | mem (JSON.stringify strategy)                | 849,244               |\n| 6        | fast-memoize                                 | 691,512               |\n| 7        | no memoization                               | 504                   |\n\n  </p>\n</details>\n\n## Code health ????\n\n- Tested with all built in [JavaScript types](https://github.com/getify/You-Dont-Know-JS/blob/1st-ed/types%20%26%20grammar/ch1.md)\n- Written in `Typescript`\n- Correct typing for `Typescript` and `flow` type systems\n- No dependencies\n", "time": {"created": "2022-01-26T13:33:16.756Z", "modified": "2023-07-27T20:59:00.021Z", "6.0.0": "2021-10-20T02:33:02.439Z", "6.0.0-beta.1": "2021-10-15T05:26:45.577Z", "5.2.1": "2021-04-24T02:02:23.780Z", "5.2.0": "2021-04-22T21:28:53.956Z", "5.1.1": "2019-08-23T03:13:11.276Z", "5.1.0": "2019-08-21T01:37:33.154Z", "5.1.0-beta.6": "2019-08-21T01:09:06.622Z", "5.1.0-beta.5": "2019-08-21T00:06:17.638Z", "5.1.0-beta.4": "2019-08-20T05:27:07.650Z", "5.1.0-beta.3": "2019-08-20T04:07:51.854Z", "5.1.0-beta.2": "2019-08-20T01:41:39.662Z", "5.1.0-beta.1": "2019-08-20T00:50:28.890Z", "5.0.5": "2019-07-09T01:25:27.351Z", "5.0.4": "2019-04-08T04:48:18.959Z", "5.0.3": "2019-04-08T03:50:04.142Z", "5.0.2": "2019-03-27T22:25:33.961Z", "5.0.1": "2019-03-27T00:19:59.256Z", "5.0.0": "2018-12-17T04:12:34.765Z", "4.1.0": "2018-12-11T23:20:09.084Z", "4.0.3": "2018-11-07T02:15:06.018Z", "4.0.2": "2018-08-31T06:38:42.218Z", "4.0.1": "2018-08-31T01:42:24.701Z", "4.0.0": "2018-06-29T03:51:30.019Z", "3.1.1": "2018-03-22T08:10:23.672Z", "3.1.0": "2018-03-22T00:05:45.034Z", "3.0.1": "2017-05-27T10:03:38.832Z", "3.0.0": "2017-05-15T07:09:44.760Z", "2.0.1": "2017-05-05T14:53:20.381Z", "2.0.0": "2017-05-05T06:13:04.755Z", "1.0.2": "2017-02-14T00:21:47.997Z", "1.0.1": "2017-02-10T00:33:11.114Z", "1.0.0": "2017-02-08T22:02:46.011Z", "1.0.0-rc.1": "2017-02-06T22:32:39.852Z", "0.0.2-beta": "2017-02-06T08:51:39.181Z", "0.0.1-beta": "2017-02-06T07:58:39.215Z"}, "versions": {"6.0.0": {"name": "memoize-one", "version": "6.0.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "size-limit": [{"path": "dist/memoize-one.min.js", "limit": "234B"}, {"path": "dist/memoize-one.js", "limit": "234B"}, {"path": "dist/memoize-one.cjs.js", "limit": "230B"}, {"path": "dist/memoize-one.esm.js", "limit": "246B"}], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@size-limit/preset-small-lib": "^5.0.4", "@types/benchmark": "^2.1.1", "@types/jest": "^27.0.2", "@types/lodash.isequal": "^4.5.5", "@types/lodash.memoize": "^4.1.6", "@types/node": "^16.10.1", "@typescript-eslint/eslint-plugin": "^4.31.2", "@typescript-eslint/parser": "^4.31.2", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest": "^24.4.2", "eslint-plugin-prettier": "^4.0.0", "expect-type": "^0.12.0", "fast-memoize": "^2.5.2", "jest": "^27.2.2", "lodash.isequal": "^4.5.0", "lodash.memoize": "^4.1.2", "markdown-table": "^3.0.1", "mem": "^9.0.1", "memoizee": "^0.4.15", "moize": "^6.1.0", "nanocolors": "^0.2.9", "ora": "^6.0.1", "prettier": "2.4.1", "rimraf": "3.0.2", "rollup": "^2.57.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript": "^1.0.1", "size-limit": "^5.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "tslib": "^2.3.1", "typescript": "^4.4.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn prettier:check && yarn eslint:check && yarn typescript:check", "test": "yarn jest", "test:size": "yarn build && yarn size-limit", "typescript:check": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "eslint:check": "eslint $npm_package_config_prettier_target", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "yarn rimraf dist", "build:dist": "yarn rollup -c", "build:typescript": "yarn tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "yarn ts-node ./benchmarks/shallow-equal.ts", "perf:library-comparison": "yarn build && node ./benchmarks/library-comparison.js", "prepublishOnly": "yarn build"}, "gitHead": "d5acc5343970ee4b615d4d1d5da85c2436752a79", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@6.0.0", "_nodeVersion": "14.15.0", "_npmVersion": "7.11.1", "dist": {"shasum": "b2591b871ed82948aee4727dc6abceeeac8c1045", "size": 9585, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz", "integrity": "sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw=="}, "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_6.0.0_1634697182254_0.34471216509637514"}, "_hasShrinkwrap": false, "publish_time": 1634697182439, "_cnpm_publish_time": 1634697182439, "_cnpmcore_publish_time": "2021-12-15T16:53:33.435Z"}, "6.0.0-beta.1": {"name": "memoize-one", "version": "6.0.0-beta.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "size-limit": [{"path": "dist/memoize-one.min.js", "limit": "234B"}, {"path": "dist/memoize-one.js", "limit": "234B"}, {"path": "dist/memoize-one.cjs.js", "limit": "230B"}, {"path": "dist/memoize-one.esm.js", "limit": "246B"}], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@size-limit/preset-small-lib": "^5.0.4", "@types/benchmark": "^2.1.1", "@types/jest": "^27.0.2", "@types/lodash.isequal": "^4.5.5", "@types/lodash.memoize": "^4.1.6", "@types/node": "^16.10.1", "@typescript-eslint/eslint-plugin": "^4.31.2", "@typescript-eslint/parser": "^4.31.2", "benchmark": "^2.1.4", "cli-table": "^0.3.6", "cross-env": "^7.0.3", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest": "^24.4.2", "eslint-plugin-prettier": "^4.0.0", "expect-type": "^0.12.0", "fast-memoize": "^2.5.2", "jest": "^27.2.2", "lodash.isequal": "^4.5.0", "lodash.memoize": "^4.1.2", "mem": "^9.0.1", "memoizee": "^0.4.15", "moize": "^6.1.0", "nanocolors": "^0.2.9", "ora": "^6.0.1", "prettier": "2.4.1", "rimraf": "3.0.2", "rollup": "^2.57.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript": "^1.0.1", "size-limit": "^5.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "tslib": "^2.3.1", "typescript": "^4.4.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn prettier:check && yarn eslint:check && yarn typescript:check", "test": "yarn jest", "test:size": "yarn build && yarn size-limit", "typescript:check": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "eslint:check": "eslint $npm_package_config_prettier_target", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "yarn rimraf dist", "build:dist": "yarn rollup -c", "build:typescript": "yarn tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "yarn ts-node ./benchmarks/shallow-equal.ts", "perf:library-comparison": "yarn build && node ./benchmarks/library-comparison.js", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "2a2c84b4a93a556711b1ead47cfbb34b06c448b3", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@6.0.0-beta.1", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"shasum": "5b0e23c03f804dcb1556546cf8785c11213c1c9f", "size": 9246, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0-beta.1.tgz", "integrity": "sha512-Ru+EFMmPRAk+3XUb6XxWF8gjM63R1TohA+BSyn/WaFzN9owjL3NAX8PFfocGAz0qFOeXwW4bZ4SK32zN16v9Ag=="}, "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_6.0.0-beta.1_1634275605421_0.07515083282204915"}, "_hasShrinkwrap": false, "publish_time": 1634275605577, "_cnpm_publish_time": 1634275605577, "_cnpmcore_publish_time": "2021-12-15T16:53:33.683Z"}, "5.2.1": {"name": "memoize-one", "version": "5.2.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "size-limit": [{"path": "dist/memoize-one.min.js", "limit": "214B"}, {"path": "dist/memoize-one.js", "limit": "216B"}, {"path": "dist/memoize-one.cjs.js", "limit": "213B"}, {"path": "dist/memoize-one.esm.js", "limit": "218B"}], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@size-limit/preset-small-lib": "^4.10.2", "@types/jest": "^26.0.22", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "eslint": "7.24.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-jest": "^24.3.5", "eslint-plugin-prettier": "^3.3.1", "jest": "^26.6.3", "lodash.isequal": "^4.5.0", "prettier": "2.2.1", "rimraf": "3.0.2", "rollup": "^2.45.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript": "^1.0.1", "size-limit": "^4.10.2", "ts-jest": "^26.5.4", "ts-node": "^9.1.1", "tslib": "^2.2.0", "typescript": "^4.2.4"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "test:size": "yarn build && size-limit", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "gitHead": "6d5ad9f89455c0acc01af6bb313b39600023c9bb", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.2.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "8337aa3c4335581839ec01c3d594090cebe8f00e", "size": 7122, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.2.1.tgz", "integrity": "sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q=="}, "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.2.1_1619229743608_0.10378173501947363"}, "_hasShrinkwrap": false, "publish_time": 1619229743780, "_cnpm_publish_time": 1619229743780, "_cnpmcore_publish_time": "2021-12-15T16:53:33.907Z"}, "5.2.0": {"name": "memoize-one", "version": "5.2.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "size-limit": [{"path": "dist/memoize-one.min.js", "limit": "214B"}, {"path": "dist/memoize-one.js", "limit": "216B"}, {"path": "dist/memoize-one.cjs.js", "limit": "213B"}, {"path": "dist/memoize-one.esm.js", "limit": "218B"}], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@size-limit/preset-small-lib": "^4.10.2", "@types/jest": "^26.0.22", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "eslint": "7.24.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-jest": "^24.3.5", "eslint-plugin-prettier": "^3.3.1", "jest": "^26.6.3", "lodash.isequal": "^4.5.0", "prettier": "2.2.1", "rimraf": "3.0.2", "rollup": "^2.45.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript": "^1.0.1", "size-limit": "^4.10.2", "ts-jest": "^26.5.4", "ts-node": "^9.1.1", "tslib": "^2.2.0", "typescript": "^4.2.4"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "test:size": "yarn build && size-limit", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "gitHead": "1bafd90b35ad9635f2b9d971e1aff7f71aa96207", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.2.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "65259224c44b59b9d6648af917108e6867d64d54", "size": 7179, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.2.0.tgz", "integrity": "sha512-OiKwjWuxDPHRN5yL9gskqJHT1dr9N99AH95ceiwvpXjE6fpcwAtPHDRoe8CFL1+TMrLG9NzO5WerQ32Q35iD4w=="}, "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.2.0_1619126933796_0.5395531627759844"}, "_hasShrinkwrap": false, "publish_time": 1619126933956, "deprecated": "Addition of named import caused breaking change with CommonJS build. Named import has been reverted in 5.2.1", "_cnpm_publish_time": 1619126933956, "_cnpmcore_publish_time": "2021-12-15T16:53:34.269Z"}, "5.1.1": {"name": "memoize-one", "version": "5.1.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript": "^1.0.1", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "gitHead": "8fd7a64ae6d7505cffc0a7e97075b171f85faf06", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "047b6e3199b508eaec03504de71229b8eb1d75c0", "size": 6167, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.1.tgz", "integrity": "sha512-HKeeBpWvqiVJD57ZUAsJNm71eHTykffzcLZVYWiVfQeI1rJtuEaS7hQiEpWfVVk18donPwJEcFKIkCmPJNOhHA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.1_1566529991146_0.11033357372791275"}, "_hasShrinkwrap": false, "publish_time": 1566529991276, "_cnpm_publish_time": 1566529991276, "_cnpmcore_publish_time": "2021-12-15T16:53:34.482Z"}, "5.1.0": {"name": "memoize-one", "version": "5.1.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript": "^1.0.1", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "gitHead": "44c5cd4b610345e27affae00b1ee7c1d7b39b5c0", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "ce7af291c0e2fe041b709cac5e8c7b198c994286", "size": 6225, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0.tgz", "integrity": "sha512-p3tPVJNrjOkJ0vk0FRn6yv898qlQZct1rsQAXuwK9X5brNVajPv/y13ytrUByzSS8olyzeqCLX8BKEWmTmXa1A=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0_1566351453048_0.09574075164536744"}, "_hasShrinkwrap": false, "publish_time": 1566351453154, "_cnpm_publish_time": 1566351453154, "_cnpmcore_publish_time": "2021-12-15T16:53:34.796Z"}, "5.1.0-beta.6": {"name": "memoize-one", "version": "5.1.0-beta.6", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript": "^1.0.1", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "47a274f1095933cedb2d1957a2ee82d3db238eb6", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0-beta.6", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "e2c4109c02e4479a6f4874648549c72885a70104", "size": 6233, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0-beta.6.tgz", "integrity": "sha512-iQe4FX/rOoOKBaKRCdS3KpwH1KERxW/TMn9Y6hC+rEZzc3ePXHwm4P2l1Us2YF1VQxY/ZSeywAcK2a9HzyJ7qQ=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0-beta.6_1566349746489_0.19042184466453804"}, "_hasShrinkwrap": false, "publish_time": 1566349746622, "_cnpm_publish_time": 1566349746622, "_cnpmcore_publish_time": "2021-12-15T16:53:35.012Z"}, "5.1.0-beta.5": {"name": "memoize-one", "version": "5.1.0-beta.5", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript": "^1.0.1", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "a80095da6a3ad83a8caa55888e84c1470258830b", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0-beta.5", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "438b4c58cbf1a9f23ce6d04f0633c328daecf197", "size": 6181, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0-beta.5.tgz", "integrity": "sha512-QS9ULbGG4sr6Xl0b81RfVUUxXbd40BfdAbuhkK0lc+FXCb+bE63CkM8lp96Kud5wtc3aWbTIMdD63oSWYiz2nQ=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0-beta.5_1566345977473_0.308817424990518"}, "_hasShrinkwrap": false, "publish_time": 1566345977638, "_cnpm_publish_time": 1566345977638, "_cnpmcore_publish_time": "2021-12-15T16:53:35.225Z"}, "5.1.0-beta.4": {"name": "memoize-one", "version": "5.1.0-beta.4", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "flow-bin": "^0.105.2", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript": "^1.0.1", "rollup-plugin-uglify": "^6.0.2", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "b2ffc547f875b0b8e6b232cbea0e0ba7d730fced", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0-beta.4", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "a237ff450ff77879a038090f3d67198ed334f9ca", "size": 6288, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0-beta.4.tgz", "integrity": "sha512-W/E7r0eGc+SN/YDG61ePCIhAT7ptTx4uWx8apUnFMRhQnUFZEdttr9dB93Hm6wSO5Ga71w68m1eYoUzQIP31aw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0-beta.4_1566278827458_0.3084208499211827"}, "_hasShrinkwrap": false, "publish_time": 1566278827650, "_cnpm_publish_time": 1566278827650, "_cnpmcore_publish_time": "2021-12-15T16:53:35.431Z"}, "5.1.0-beta.3": {"name": "memoize-one", "version": "5.1.0-beta.3", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "flow-bin": "^0.105.2", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript": "^1.0.1", "rollup-plugin-uglify": "^6.0.2", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "9b2febe8b82927c115e92f9587aa6506c14d78e0", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0-beta.3", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "ffb4b6d54fb6af48dc50aa7dbcbf65cda8717a8e", "size": 6554, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0-beta.3.tgz", "integrity": "sha512-f/ZFA1HpAiE3Q/O61DBjZhKg77zustHIjnH+lic+Q+SKhcBD13/W1nNLOK5y/3ziSWb+VHqAV4hZaPKVJTYpPA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0-beta.3_1566274071702_0.8855702869511366"}, "_hasShrinkwrap": false, "publish_time": 1566274071854, "_cnpm_publish_time": 1566274071854, "_cnpmcore_publish_time": "2021-12-15T16:53:35.683Z"}, "5.1.0-beta.2": {"name": "memoize-one", "version": "5.1.0-beta.2", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "flow-bin": "^0.105.2", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript": "^1.0.1", "rollup-plugin-uglify": "^6.0.2", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "21e3715ce0c82d60fc5e73cbeb96ee612436a3c7", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0-beta.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "bbb4fe7d452708aa54ab04f1e1060a13c523c7d5", "size": 6458, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0-beta.2.tgz", "integrity": "sha512-0V4QdFSeQrJb2KbthcbZzTlUVNOl09OPpxFWbxGecz7Px8Ntickeue8I87Sc9nV/LDA3seiXwFNhi2SN9WQXig=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0-beta.2_1566265299483_0.5750751587036571"}, "_hasShrinkwrap": false, "publish_time": 1566265299662, "_cnpm_publish_time": 1566265299662, "_cnpmcore_publish_time": "2021-12-15T16:53:35.929Z"}, "5.1.0-beta.1": {"name": "memoize-one", "version": "5.1.0-beta.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "flow-bin": "^0.105.2", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript": "^1.0.1", "rollup-plugin-uglify": "^6.0.2", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}, "readmeFilename": "README.md", "gitHead": "2e83ad9ca8bc0a4aba4b66c2a5775bc2a9fb8cda", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.1.0-beta.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "e799e38e57a560f38352520995ecf77e8b04df35", "size": 6454, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.1.0-beta.1.tgz", "integrity": "sha512-gIaavk1kXPGif7sGbkPZadR/Bkf/S3lYI2FHBqQFMyk9v79sCW8zjoiLJjXf6UARO4I59rnZOP3UWhFkC/Hyjg=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.1.0-beta.1_1566262228713_0.1440641529175708"}, "_hasShrinkwrap": false, "publish_time": 1566262228890, "_cnpm_publish_time": 1566262228890, "_cnpmcore_publish_time": "2021-12-15T16:53:36.116Z"}, "5.0.5": {"name": "memoize-one", "version": "5.0.5", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/preset-env": "^7.5.2", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.2", "babel-jest": "^24.8.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-flowtype": "^3.11.1", "eslint-plugin-jest": "^22.7.2", "eslint-plugin-prettier": "^3.1.0", "flow-bin": "0.102.0", "jest": "^24.8.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.2"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "perf": "node ./benchmarks/shallowEqual.js", "prepublish": "yarn run build"}, "licenseText": "MIT License\n\nCopyright (c) 2019 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.", "_id": "memoize-one@5.0.5", "dist": {"shasum": "8cd3809555723a07684afafcd6f756072ac75d7e", "size": 6283, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.0.5.tgz", "integrity": "sha512-ey6EpYv0tEaIbM/nTDOpHciXUvd+ackQrJgEzBwemhZZIWZjcyodqEcrmqDy2BKRTM3a65kKBV4WtLXJDt26SQ=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.0.5_1562635527126_0.36029147481214174"}, "_hasShrinkwrap": false, "publish_time": 1562635527351, "_cnpm_publish_time": 1562635527351, "_cnpmcore_publish_time": "2021-12-15T16:53:36.321Z"}, "5.0.4": {"name": "memoize-one", "version": "5.0.4", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^24.7.1", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "5.16.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-flowtype": "^3.5.1", "eslint-plugin-jest": "^22.4.1", "eslint-plugin-prettier": "^3.0.1", "flow-bin": "0.96.0", "jest": "^24.7.1", "lodash.isequal": "^4.5.0", "prettier": "1.16.4", "rimraf": "2.6.3", "rollup": "^1.9.0", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-replace": "^2.1.1", "rollup-plugin-uglify": "^6.0.2"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "perf": "node ./benchmarks/shallowEqual.js", "prepublish": "yarn run build"}, "gitHead": "ac426ef9eb28f94feb8091dc31564992f4af651c", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.0.4", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "005928aced5c43d890a4dfab18ca908b0ec92cbc", "size": 6170, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.0.4.tgz", "integrity": "sha512-P0z5IeAH6qHHGkJIXWw0xC2HNEgkx/9uWWBQw64FJj3/ol14VYdfVGWWr0fXfjhhv3TKVIqUq65os6O4GUNksA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.0.4_1554698898798_0.14814520289263444"}, "_hasShrinkwrap": false, "publish_time": 1554698898959, "_cnpm_publish_time": 1554698898959, "_cnpmcore_publish_time": "2021-12-15T16:53:36.554Z"}, "5.0.3": {"name": "memoize-one", "version": "5.0.3", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^24.7.1", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "5.16.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-flowtype": "^3.5.1", "eslint-plugin-jest": "^22.4.1", "eslint-plugin-prettier": "^3.0.1", "flow-bin": "0.96.0", "jest": "^24.7.1", "lodash.isequal": "^4.5.0", "prettier": "1.16.4", "rimraf": "2.6.3", "rollup": "^1.9.0", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-replace": "^2.1.1", "rollup-plugin-uglify": "^6.0.2"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "perf": "node ./benchmarks/shallowEqual.js", "prepublish": "yarn run build"}, "gitHead": "da141a711e6f70395c34db9d1c7a343c7e670c46", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.0.3", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2f2ec6f21df48f41caa9f5e873b3dd09650d1d38", "size": 6235, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.0.3.tgz", "integrity": "sha512-hSFNx1nYJZ1fUmhBa1ZNI5ZEKsWGks6yp3yX0v2hHOF7D2VJw+fRkzkoA4djB5itv+Nk0piLOelgiPPKIVBBqQ=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.0.3_1554695404052_0.08966463574835615"}, "_hasShrinkwrap": false, "publish_time": 1554695404142, "deprecated": "New flow types can break consumers. Please use 5.0.4", "_cnpm_publish_time": 1554695404142, "_cnpmcore_publish_time": "2021-12-15T16:53:36.854Z"}, "5.0.2": {"name": "memoize-one", "version": "5.0.2", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^24.5.0", "cross-env": "^5.2.0", "eslint": "5.15.3", "eslint-config-prettier": "^4.1.0", "eslint-plugin-flowtype": "^3.4.2", "eslint-plugin-jest": "^22.4.1", "eslint-plugin-prettier": "^3.0.1", "flow-bin": "0.95.1", "jest": "^24.5.0", "lodash.isequal": "^4.5.0", "prettier": "1.16.4", "rimraf": "2.6.3", "rollup": "^1.7.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.2.2", "rollup-plugin-replace": "^2.1.1", "rollup-plugin-uglify": "^6.0.2"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "04094ecbc03a3873f6bb967fb302276e92cf637f", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6aba5276856d72fb44ead3efab86432f94ba203d", "size": 6008, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.0.2.tgz", "integrity": "sha512-o7lldN4fs/axqctc03NF+PMhd2veRrWeJ2n2GjEzUPBD4F9rmNg4A+bQCACIzwjHJEXuYv4aFFMaH35KZfHUrw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.0.2_1553725533738_0.1932929672873953"}, "_hasShrinkwrap": false, "publish_time": 1553725533961, "_cnpm_publish_time": 1553725533961, "_cnpmcore_publish_time": "2021-12-15T16:53:37.091Z"}, "5.0.1": {"name": "memoize-one", "version": "5.0.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^24.5.0", "cross-env": "^5.2.0", "eslint": "5.15.3", "eslint-config-prettier": "^4.1.0", "eslint-plugin-flowtype": "^3.4.2", "eslint-plugin-jest": "^22.4.1", "eslint-plugin-prettier": "^3.0.1", "flow-bin": "0.95.1", "jest": "^24.5.0", "lodash.isequal": "^4.5.0", "prettier": "1.16.4", "rimraf": "2.6.3", "rollup": "^1.7.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.2.2", "rollup-plugin-replace": "^2.1.1", "rollup-plugin-uglify": "^6.0.2"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "7f638eef73a4dcd9cb6925b616c533504a7a0ef2", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "35a709ffb6e5f0cb79f9679a96f09ec3a35addfa", "size": 6026, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.0.1.tgz", "integrity": "sha512-S3plzyksLOSF4pkf1Xlb7mA8ZRKZlgp3ebg7rULbfwPT8Ww7uZz5CbLgRKaR92GeXpsNiFbfCRWf/uOrCYIbRg=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.0.1_1553645999109_0.19381473128783688"}, "_hasShrinkwrap": false, "publish_time": 1553645999256, "_cnpm_publish_time": 1553645999256, "_cnpmcore_publish_time": "2021-12-15T16:53:37.484Z"}, "5.0.0": {"name": "memoize-one", "version": "5.0.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/preset-env": "^7.2.0", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^23.6.0", "cross-env": "^5.2.0", "eslint": "5.10.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-flowtype": "^3.2.0", "eslint-plugin-jest": "^22.1.2", "eslint-plugin-prettier": "^3.0.0", "flow-bin": "0.89.0", "jest": "^23.6.0", "lodash.isequal": "^4.5.0", "prettier": "1.15.3", "rimraf": "2.6.2", "rollup": "^0.68.0", "rollup-plugin-babel": "^4.1.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-uglify": "^6.0.0"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "d143f883ff81dba9c46f148d212337667b941a99", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@5.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d55007dffefb8de7546659a1722a5d42e128286e", "size": 6054, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.0.0.tgz", "integrity": "sha512-7g0+ejkOaI9w5x6LvQwmj68kUj6rxROywPSCqmclG/HBacmFnZqhVscQ8kovkn9FBCNJmOz6SY42+jnvZzDWdw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_5.0.0_1545019954655_0.4306266870504094"}, "_hasShrinkwrap": false, "publish_time": 1545019954765, "_cnpm_publish_time": 1545019954765, "_cnpmcore_publish_time": "2021-12-15T16:53:37.719Z"}, "4.1.0": {"name": "memoize-one", "version": "4.1.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/preset-env": "^7.2.0", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^23.6.0", "cross-env": "^5.2.0", "eslint": "5.10.0", "eslint-plugin-flowtype": "^3.2.0", "eslint-plugin-jest": "^22.1.2", "flow-bin": "0.88.0", "jest": "^23.6.0", "rimraf": "2.6.2", "rollup": "^0.67.4", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-uglify": "^6.0.0"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "5d9d0335bbdbda42b4fe820fb4081bfd318f158d", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@4.1.0", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a2387c58c03fff27ca390c31b764a79addf3f906", "size": 6150, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-4.1.0.tgz", "integrity": "sha512-2GApq0yI/b22J2j9rhbrAlsHb0Qcz+7yWxeLG8h+95sl1XPUgeLimQSOdur4Vw7cUhrBHwaUZxWFZueojqNRzA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_4.1.0_1544570408931_0.23987049956174755"}, "_hasShrinkwrap": false, "publish_time": 1544570409084, "deprecated": "New custom equality api does not play well with all equality helpers. Please use v5.x", "_cnpm_publish_time": 1544570409084, "_cnpmcore_publish_time": "2021-12-15T16:53:37.949Z"}, "4.0.3": {"name": "memoize-one", "version": "4.0.3", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.1.5", "@babel/preset-env": "^7.1.5", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^23.6.0", "cross-env": "^5.2.0", "eslint": "5.8.0", "eslint-plugin-flowtype": "^3.2.0", "eslint-plugin-jest": "^21.27.2", "flow-bin": "0.85.0", "jest": "^23.6.0", "rimraf": "2.6.2", "rollup": "^0.67.0", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-commonjs": "^9.1.6", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^6.0.0"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "8ae12150f9b77cd1022d3ff16e74675f981ee709", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@4.0.3", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cdfdd942853f1a1b4c71c5336b8c49da0bf0273c", "size": 5925, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-4.0.3.tgz", "integrity": "sha512-QmpUu4KqDmX0plH4u+tf0riMc1KHE1+lw95cMrLlXQAFOx/xnBtwhZ52XJxd9X2O6kwKBqX32kmhbhlobD0cuw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_4.0.3_1541556905883_0.27120733316890955"}, "_hasShrinkwrap": false, "publish_time": 1541556906018, "_cnpm_publish_time": 1541556906018, "_cnpmcore_publish_time": "2021-12-15T16:53:38.180Z"}, "4.0.2": {"name": "memoize-one", "version": "4.0.2", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "9.0.0", "babel-jest": "^23.4.2", "chai": "4.1.2", "cross-env": "^5.2.0", "eslint": "5.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jest": "^21.22.0", "flow-bin": "0.79.1", "jest": "^23.5.0", "rimraf": "2.6.2", "rollup": "^0.65.0", "rollup-plugin-babel": "^4.0.2", "rollup-plugin-commonjs": "^9.1.6", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^4.0.0"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "47fe762e2bf3d571f8e9d9d9ea1ca9b390dd9d1b", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@4.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3fb8db695aa14ab9c0f1644e1585a8806adc1aee", "size": 5920, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-4.0.2.tgz", "integrity": "sha512-ucx2DmXTeZTsS4GPPUZCbULAN7kdPT1G+H49Y34JjbQ5ESc6OGhVxKvb1iKhr9v19ZB9OtnHwNnhUnNR/7Wteg=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_4.0.2_1535697521617_0.5097505120339458"}, "_hasShrinkwrap": false, "publish_time": 1535697522218, "_cnpm_publish_time": 1535697522218, "_cnpmcore_publish_time": "2021-12-15T16:53:38.375Z"}, "4.0.1": {"name": "memoize-one", "version": "4.0.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "9.0.0", "babel-jest": "^23.4.2", "chai": "4.1.2", "cross-env": "^5.2.0", "eslint": "5.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jest": "^21.22.0", "flow-bin": "0.79.1", "jest": "^23.5.0", "rimraf": "2.6.2", "rollup": "^0.65.0", "rollup-plugin-babel": "^4.0.2", "rollup-plugin-commonjs": "^9.1.6", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^4.0.0"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "a71302104827951626d815577c32f315f896f857", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@4.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fdcc01bc764c50bb6f8a4e54d97f7e790b7bb42a", "size": 5841, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-4.0.1.tgz", "integrity": "sha512-EQ0hvTFVoBoPS8wo1UW6ORPURVMZnb2eWzfxSkIE6W+Auq4XY2P/2zesh+55+1aeX77Vt+48OOLNsCCrtU8hJg=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_4.0.1_1535679744534_0.24234818254767543"}, "_hasShrinkwrap": false, "publish_time": 1535679744701, "_cnpm_publish_time": 1535679744701, "_cnpmcore_publish_time": "2021-12-15T16:53:38.608Z"}, "4.0.0": {"name": "memoize-one", "version": "4.0.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "files": ["/dist", "/src"], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.3", "babel-eslint": "8.2.5", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "^1.7.0", "babel-preset-flow": "^6.23.0", "chai": "4.1.2", "cross-env": "^5.2.0", "eslint": "5.0.1", "eslint-plugin-flowtype": "^2.49.3", "eslint-plugin-jest": "^21.17.0", "flow-bin": "0.75.0", "jest": "^23.2.0", "rimraf": "2.6.2", "rollup": "^0.62.0", "rollup-plugin-babel": "^3.0.5", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^4.0.0"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/memoize-one.cjs.js.flow", "prepublish": "yarn run build"}, "gitHead": "3fe7a61c028d8dbfd2282b88229f15c19e6ed409", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@4.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fc5e2f1427a216676a62ec652cf7398cfad123db", "size": 5283, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-4.0.0.tgz", "integrity": "sha512-wdpOJ4XBejprGn/xhd1i2XR8Dv1A25FJeIvR7syQhQlz9eXsv+06llcvcmBxlWVGv4C73QBsWA8kxvZozzNwiQ=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_4.0.0_1530244289954_0.7810159951816047"}, "_hasShrinkwrap": false, "publish_time": 1530244290019, "_cnpm_publish_time": 1530244290019, "_cnpmcore_publish_time": "2021-12-15T16:53:38.825Z"}, "3.1.1": {"name": "memoize-one", "version": "3.1.1", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "module": "esm/index.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "files": ["/lib", "/esm", "/src"], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-eslint": "8.2.2", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "chai": "4.1.2", "cross-env": "^5.1.4", "eslint": "4.19.0", "eslint-plugin-flowtype": "^2.46.1", "eslint-plugin-jest": "^21.15.0", "flow-bin": "0.68.0", "flow-copy-source": "1.3.0", "jest": "^22.4.2", "rimraf": "2.6.2"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:esm && yarn run build:flow", "build:clean": "<PERSON><PERSON>f lib esm", "build:lib": "cross-env NODE_ENV=cjs babel src -d lib", "build:esm": "babel src --out-dir esm", "build:flow": "flow-copy-source --verbose src lib && flow-copy-source --verbose src esm", "prepublish": "yarn run build"}, "gitHead": "5b4a3ecc4913e5e6bf4b4da2776f3764ecbd80ba", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@3.1.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ef609811e3bc28970eac2884eece64d167830d17", "size": 4833, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-3.1.1.tgz", "integrity": "sha512-YqVh744GsMlZu6xkhGslPSqSurOv6P+kLN2J3ysBZfagLcL5FdRK/0UpgLoL8hwjjEvvAVkjJZyFP+1T6p1vgA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_3.1.1_1521706223561_0.526611674639234"}, "_hasShrinkwrap": false, "publish_time": 1521706223672, "_cnpm_publish_time": 1521706223672, "_cnpmcore_publish_time": "2021-12-15T16:53:39.041Z"}, "3.1.0": {"name": "memoize-one", "version": "3.1.0", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "module": "esm/index.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "files": ["/lib", "/esm", "/src"], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-eslint": "8.2.2", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "chai": "4.1.2", "cross-env": "^5.1.4", "eslint": "4.19.0", "eslint-plugin-flowtype": "^2.46.1", "eslint-plugin-jest": "^21.15.0", "flow-bin": "0.68.0", "flow-copy-source": "1.3.0", "jest": "^22.4.2", "rimraf": "2.6.2"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "cross-env NODE_ENV=test jest", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:esm && yarn run build:flow", "build:clean": "<PERSON><PERSON>f lib esm", "build:lib": "cross-env NODE_ENV=cjs babel src -d lib", "build:esm": "babel src --out-dir esm", "build:flow": "flow-copy-source --verbose src lib && flow-copy-source --verbose src esm", "prepublish": "yarn run build"}, "gitHead": "bd1edab004de85c5e4213409bb30010617c0729a", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@3.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "08c77942b642e6a99d3b984ddc87408080a3918e", "size": 5390, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-3.1.0.tgz", "integrity": "sha512-rpzyrDdq3TMd7PuwzUQ7IsWvIKjQs/YHMnnrsX7MyPUhmr5x3gsykwiNsW7SX/VgtltmQtujGeKQntxoRz40DA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one_3.1.0_1521677144939_0.019997431701371582"}, "_hasShrinkwrap": false, "publish_time": 1521677145034, "deprecated": "critical bug on ie11. please use 3.1.1", "_cnpm_publish_time": 1521677145034, "_cnpmcore_publish_time": "2021-12-15T16:53:39.258Z"}, "3.0.1": {"name": "memoize-one", "version": "3.0.1", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.24.1", "babel-core": "6.24.1", "babel-eslint": "7.2.3", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.23.0", "babel-preset-es2015": "6.24.1", "chai": "3.5.0", "codecov": "2.1.0", "eslint": "3.19.0", "eslint-plugin-flowtype": "2.32.1", "flow-bin": "0.46.0", "flow-copy-source": "1.1.0", "mocha": "3.3.0", "nyc": "10.3.0", "rimraf": "2.6.1", "sinon": "2.2.0"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "99601be7754c04216e70f6784533f195ac74e0da", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@3.0.1", "_shasum": "7b599850bb41be8beed305f4eefd963c8cea9a0a", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7b599850bb41be8beed305f4eefd963c8cea9a0a", "size": 37832, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-3.0.1.tgz", "integrity": "sha512-wOe9z0Uyaw1HTWC00+OR35KSjg+RZer4cxToq1CcnT5GxbpqfnjkPdAex9xJe3fQw86k/76R1JoGwA84LfWtPw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/memoize-one-3.0.1.tgz_1495879418749_0.07076057023368776"}, "directories": {}, "publish_time": 1495879418832, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495879418832, "_cnpmcore_publish_time": "2021-12-15T16:53:39.502Z"}, "3.0.0": {"name": "memoize-one", "version": "3.0.0", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.24.1", "babel-core": "6.24.1", "babel-eslint": "7.2.3", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.23.0", "babel-preset-es2015": "6.24.1", "chai": "3.5.0", "codecov": "2.1.0", "eslint": "3.19.0", "eslint-plugin-flowtype": "2.32.1", "flow-bin": "0.46.0", "flow-copy-source": "1.1.0", "mocha": "3.3.0", "nyc": "10.3.0", "rimraf": "2.6.1", "sinon": "2.2.0"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "b46dc8ee603046cafc123438cd1fa76dd7cb97d0", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@3.0.0", "_shasum": "0519584c64c8f28000030b46b2e3ad4af587e914", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0519584c64c8f28000030b46b2e3ad4af587e914", "size": 40342, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-3.0.0.tgz", "integrity": "sha512-O8rTlUFN8EKdZMsUvgyZm1uw5qZHL2xIlmJ5rimsmDD3YUK5B8PGU/IRHlX2Zr4Kxff0ijj+SP8EIblRwCRZBA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/memoize-one-3.0.0.tgz_1494832182423_0.6639867690391839"}, "directories": {}, "publish_time": 1494832184760, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494832184760, "_cnpmcore_publish_time": "2021-12-15T16:53:39.741Z"}, "2.0.1": {"name": "memoize-one", "version": "2.0.1", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.24.1", "babel-core": "6.24.1", "babel-eslint": "7.2.3", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.23.0", "babel-preset-es2015": "6.24.1", "chai": "3.5.0", "codecov": "2.1.0", "eslint": "3.19.0", "eslint-plugin-flowtype": "2.32.1", "flow-bin": "0.45.0", "flow-copy-source": "1.1.0", "mocha": "3.3.0", "nyc": "10.3.0", "rimraf": "2.6.1", "sinon": "2.2.0"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "5cb7ffcc168e2141c820d17b50000cbc5aafddbd", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@2.0.1", "_shasum": "c3f1adeaafb073f432affc99782fb327f7200322", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c3f1adeaafb073f432affc99782fb327f7200322", "size": 39818, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-2.0.1.tgz", "integrity": "sha512-BhOOcCZXv34TOLNKqR/jS0m89y38VywNLQ5LLFDIR9N9QcT76rNb95lsM2zLaAPtMhqy80sS4bPrhBg/KJeElw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/memoize-one-2.0.1.tgz_1493995998198_0.442872951971367"}, "directories": {}, "publish_time": 1493996000381, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493996000381, "_cnpmcore_publish_time": "2021-12-15T16:53:40.461Z"}, "2.0.0": {"name": "memoize-one", "version": "2.0.0", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "codecov": "1.0.1", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "nyc": "10.1.2", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "6b33404ba31c5de54ac30f810398255a4439d76b", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@2.0.0", "_shasum": "1cb00157be79da65fbb1405ab746c1b1ec290787", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1cb00157be79da65fbb1405ab746c1b1ec290787", "size": 39862, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-2.0.0.tgz", "integrity": "sha512-ejxzWthMn8zP9hdExn/NjGJhIoxtJ+17199JtuofpCMoGrx+EumR8R3Q+qUhJRZqI/whAOQpqoM19gX1M7Cenw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/memoize-one-2.0.0.tgz_1493964782817_0.7283691600896418"}, "directories": {}, "publish_time": 1493964784755, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493964784755, "_cnpmcore_publish_time": "2021-12-15T16:53:40.725Z"}, "1.0.2": {"name": "memoize-one", "version": "1.0.2", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "module": "src/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "codecov": "1.0.1", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "nyc": "10.1.2", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "237fa34bfc22f3ef35ac4357c7f31b455d06887e", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@1.0.2", "_shasum": "b91bb769d98debdeae1a33b981904d75c5d309f0", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b91bb769d98debdeae1a33b981904d75c5d309f0", "size": 39224, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-1.0.2.tgz", "integrity": "sha512-sqSNTl/GCCCxEOF8AFVIOZkjNJLxFNUuBwNDYbOLWz+XLKrZt7R2Qi0bChqw28GI1NFHE/QKjhqzEDZR8mstWw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/memoize-one-1.0.2.tgz_1487031705498_0.8509123160038143"}, "directories": {}, "publish_time": 1487031707997, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487031707997, "_cnpmcore_publish_time": "2021-12-15T16:53:40.942Z"}, "1.0.1": {"name": "memoize-one", "version": "1.0.1", "description": "A memoization library which only remembers the latest invocation", "main": "lib/index.js", "module": "src/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "codecov": "1.0.1", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "nyc": "10.1.2", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "86732690398f071c059f8e72986e45eb01eab721", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@1.0.1", "_shasum": "528fbf37deb20bde18f6d17eac1d172770b64cc0", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "528fbf37deb20bde18f6d17eac1d172770b64cc0", "size": 39191, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-1.0.1.tgz", "integrity": "sha512-r1x8HOtTpqy8liCWE+vUpJiTLhU3LR+JhOv+8kXt6s8hPSzdib4sweRsp45C2LMwdsuiU9MHp3JIuCVOahWZaw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/memoize-one-1.0.1.tgz_1486686790862_0.5217675589956343"}, "directories": {}, "publish_time": 1486686791114, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486686791114, "_cnpmcore_publish_time": "2021-12-15T16:53:41.200Z"}, "1.0.0": {"name": "memoize-one", "version": "1.0.0", "description": "A memoization library which only remembers the latest invokation", "main": "lib/index.js", "module": "src/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "codecov": "1.0.1", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "nyc": "10.1.2", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "keywords": ["memoize", "cache", "performance"], "gitHead": "1b1b7769e1aee57a6dd63b7651b5dcdc3fec94df", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@1.0.0", "_shasum": "0308fce7de5b867d268fef55a1da4826b782a18e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0308fce7de5b867d268fef55a1da4826b782a18e", "size": 39166, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-1.0.0.tgz", "integrity": "sha512-PuYqArKyKalHJF2TcZvXZD766jADwhO4jtzpEwJzp8AOmHrKH7Vo82+VFIxKNFfal/d5RoCG9Dby+ibBoANbZw=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/memoize-one-1.0.0.tgz_1486591364250_0.5564714095089585"}, "directories": {}, "publish_time": 1486591366011, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486591366011, "_cnpmcore_publish_time": "2021-12-15T16:53:41.420Z"}, "1.0.0-rc.1": {"name": "memoize-one", "version": "1.0.0-rc.1", "description": "A memoization library for memoizing a function with a cache size of one", "main": "lib/index.js", "module": "src/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "codecov": "1.0.1", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "nyc": "10.1.2", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "gitHead": "d14a1f4e738b46fa88162ac65e6f1879e80a4335", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@1.0.0-rc.1", "_shasum": "7e9a0b403aed1937fbf8fc94442e1bede25f88dc", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7e9a0b403aed1937fbf8fc94442e1bede25f88dc", "size": 37567, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-1.0.0-rc.1.tgz", "integrity": "sha512-AwnT9Pb3BVBcQCoUgryni5EaJpwZVOIh+tOLbXq2MYMvgzsn4c+SBfzIAxwsdWPAvg2WaCSJ/5W8LtxTxZo6LA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/memoize-one-1.0.0-rc.1.tgz_1486420359635_0.08447556383907795"}, "directories": {}, "publish_time": 1486420359852, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486420359852, "_cnpmcore_publish_time": "2021-12-15T16:53:41.656Z"}, "0.0.2-beta": {"name": "memoize-one", "version": "0.0.2-beta", "description": "A memoization library for memoizing a function with a cache size of one", "main": "lib/index.js", "module": "src/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "codecov": "1.0.1", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "nyc": "10.1.2", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "gitHead": "f58964a60d3ae55e31d873d1adfb3406763706f9", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@0.0.2-beta", "_shasum": "0874a534ea2b1c335851ef55c8a373df908e2f00", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0874a534ea2b1c335851ef55c8a373df908e2f00", "size": 35430, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-0.0.2-beta.tgz", "integrity": "sha512-KdlPJNvfaxj5YPJCVzNLXti+aqrXgbZRn+fFbFHMNsnIMkgcQS5U4cZlO8Ejt+r4iAX2DFPuv0O5vf7brqB3nA=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/memoize-one-0.0.2-beta.tgz_1486371097446_0.9224576589185745"}, "directories": {}, "publish_time": 1486371099181, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486371099181, "_cnpmcore_publish_time": "2021-12-15T16:53:41.915Z"}, "0.0.1-beta": {"name": "memoize-one", "version": "0.0.1-beta", "description": "A memoization library for memoizing a function with a cache size of one", "main": "lib/index.js", "module": "src/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-transform-flow-strip-types": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.22.0", "babel-preset-es2015": "6.22.0", "chai": "3.5.0", "eslint": "3.15.0", "eslint-plugin-flowtype": "2.30.0", "flow-bin": "0.38.0", "flow-copy-source": "1.1.0", "mocha": "3.2.0", "rimraf": "2.5.4", "sinon": "1.17.7"}, "scripts": {"test": "yarn run lint && yarn run typecheck && yarn run test:fast", "test:fast": "mocha test --compilers js:babel-core/register --globals global", "typecheck": "flow check", "lint": "eslint src test -", "lint:fix": "yarn run lint --fix", "build": "yarn run build:clean && yarn run build:lib && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> lib", "build:lib": "babel src --out-dir lib", "build:flow": "flow-copy-source --verbose src lib", "coverage": "yarn run coverage:analyise && yarn run coverage:report && yarn run coverage:publish", "coverage:analyise": "nyc --check-coverage --statements 100 --branches 100 --functions 100 --lines 100 yarn run test:fast", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "coverage:publish": "codecov", "prepublish": "yarn run build"}, "gitHead": "7636233d7043dc8ac8a64278b4fe84c7e36810cf", "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "_id": "memoize-one@0.0.1-beta", "_shasum": "b4e8797623da88d8ae76bad3a8b5a7954b50b80a", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b4e8797623da88d8ae76bad3a8b5a7954b50b80a", "size": 29116, "noattachment": false, "tarball": "https://registry.npmmirror.com/memoize-one/-/memoize-one-0.0.1-beta.tgz", "integrity": "sha512-Nu594VqNc2j6S6pec5RPrM9OO+n1X/JYDta010LSW46sOxZT8P8lA7rGSoLVEY0dsYgMx25oPPAva+2xpAEk7Q=="}, "maintainers": [{"name": "alex<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/memoize-one-0.0.1-beta.tgz_1486367917302_0.9043313993606716"}, "directories": {}, "publish_time": 1486367919215, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486367919215, "_cnpmcore_publish_time": "2021-12-15T16:53:42.151Z"}}, "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "homepage": "https://github.com/alexreardon/memoize-one#readme", "keywords": ["memoize", "memoization", "cache", "performance"], "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "_source_registry_name": "default"}