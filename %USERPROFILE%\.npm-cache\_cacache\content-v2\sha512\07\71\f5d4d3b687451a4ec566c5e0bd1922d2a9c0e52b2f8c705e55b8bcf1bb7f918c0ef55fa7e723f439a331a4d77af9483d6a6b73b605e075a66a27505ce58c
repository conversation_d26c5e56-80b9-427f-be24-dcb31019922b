{"_attachments": {}, "_id": "dayjs", "_rev": "275442-61f1b7f71d755a05a20b995f", "author": {"name": "i<PERSON><PERSON>n"}, "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "dist-tags": {"alpha": "2.0.0-alpha.4", "latest": "1.11.13"}, "license": "MIT", "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "name": "dayjs", "readme": "English | [简体中文](./docs/zh-cn/README.zh-CN.md) | [日本語](./docs/ja/README-ja.md) | [Português Brasileiro](./docs/pt-br/README-pt-br.md) | [한국어](./docs/ko/README-ko.md) | [Español (España)](./docs/es-es/README-es-es.md) | [Русский](./docs/ru/README-ru.md) | [Türkçe](./docs/tr/README-tr.md) | [සිංහල](./docs/si/README-si.md) | [עברית](./docs/he/README-he.md)\n\n<p align=\"center\"><a href=\"https://day.js.org/\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"\n                                                                             src=\"https://user-images.githubusercontent.com/17680888/39081119-3057bbe2-456e-11e8-862c-646133ad4b43.png\"\n                                                                             alt=\"Day.js\" /></a></p>\n<p align=\"center\">Fast <b>2kB</b> alternative to Moment.js with the same modern API</p>\n<p align=\"center\">\n    <a href=\"https://bundlephobia.com/package/dayjs\"><img\n            src=\"https://img.shields.io/bundlephobia/minzip/dayjs?style=flat-square&color=%2345cc11\"\n            alt=\"Gzip Size\"></a>\n    <a href=\"https://www.npmjs.com/package/dayjs\"><img src=\"https://img.shields.io/npm/v/dayjs.svg?style=flat-square&colorB=51C838\"\n                                                       alt=\"NPM Version\"></a>\n    <a href=\"https://github.com/iamkun/dayjs/actions/workflows/check.yml\"><img\n            src=\"https://img.shields.io/github/actions/workflow/status/iamkun/dayjs/check.yml?style=flat-square\" alt=\"Build Status\"></a>\n    <a href=\"https://codecov.io/gh/iamkun/dayjs\"><img\n            src=\"https://img.shields.io/codecov/c/github/iamkun/dayjs/master.svg?style=flat-square\" alt=\"Codecov\"></a>\n    <a href=\"https://github.com/iamkun/dayjs/blob/master/LICENSE\"><img\n            src=\"https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square\" alt=\"License\"></a>\n    <br>\n    <a href=\"https://saucelabs.com/u/dayjs\">\n        <img width=\"750\" src=\"https://user-images.githubusercontent.com/17680888/40040137-8e3323a6-584b-11e8-9dba-bbe577ee8a7b.png\" alt=\"Sauce Test Status\">\n    </a>\n</p>\n\n> Day.js is a minimalist JavaScript library that parses, validates, manipulates, and displays dates and times for modern browsers with a largely Moment.js-compatible API. If you use Moment.js, you already know how to use Day.js.\n\n```js\ndayjs().startOf('month').add(1, 'day').set('year', 2018).format('YYYY-MM-DD HH:mm:ss');\n```\n\n* 🕒 Familiar Moment.js API & patterns\n* 💪 Immutable\n* 🔥 Chainable\n* 🌐 I18n support\n* 📦 2kb mini library\n* 👫 All browsers supported\n\n---\n\n## Getting Started\n\n### Documentation\n\nYou can find more details, API, and other docs on [day.js.org](https://day.js.org/) website.\n\n### Installation\n\n```console\nnpm install dayjs --save\n```\n\n📚[Installation Guide](https://day.js.org/docs/en/installation/installation)\n\n### API\n\nIt's easy to use Day.js APIs to parse, validate, manipulate, and display dates and times.\n\n```javascript\ndayjs('2018-08-08') // parse\n\ndayjs().format('{YYYY} MM-DDTHH:mm:ss SSS [Z] A') // display\n\ndayjs().set('month', 3).month() // get & set\n\ndayjs().add(1, 'year') // manipulate\n\ndayjs().isBefore(dayjs()) // query\n```\n\n📚[API Reference](https://day.js.org/docs/en/parse/parse)\n\n### I18n\n\nDay.js has great support for internationalization.\n\nBut none of them will be included in your build unless you use it.\n\n```javascript\nimport 'dayjs/locale/es' // load on demand\n\ndayjs.locale('es') // use Spanish locale globally\n\ndayjs('2018-05-05').locale('zh-cn').format() // use Chinese Simplified locale in a specific instance\n```\n\n📚[Internationalization](https://day.js.org/docs/en/i18n/i18n)\n\n### Plugin\n\nA plugin is an independent module that can be added to Day.js to extend functionality or add new features.\n\n```javascript\nimport advancedFormat from 'dayjs/plugin/advancedFormat' // load on demand\n\ndayjs.extend(advancedFormat) // use plugin\n\ndayjs().format('Q Do k kk X x') // more available formats\n```\n\n📚[Plugin List](https://day.js.org/docs/en/plugin/plugin)\n\n### Usage Trend\n\n<a href=\"https://npm-compare.com/moment,dayjs/#timeRange=THREE_YEARS\" target=\"_blank\">\n  <img src=\"https://user-images.githubusercontent.com/3455798/270162667-c7bd2ebe-675e-45c6-a2c9-dc67f3b65d6e.png\">\n</a>\n\n## Sponsors\n\nSupport this project by becoming a sponsor. Your logo will show up here with a link to your website.\n\n[[Become a sponsor via Github](https://github.com/sponsors/iamkun/)] [[Become a sponsor via OpenCollective](https://opencollective.com/dayjs#sponsor)]\n\n<a href=\"https://toyokumo.co.jp\" target=\"_blank\">\n  <img width=\"70\" src=\"https://user-images.githubusercontent.com/17680888/197092231-2367b5eb-1e43-467e-a311-23f7cd97b086.png\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://github.com/ken-swyfft\" target=\"_blank\">\n  <img width=\"70\" src=\"https://avatars.githubusercontent.com/u/65305317?v=4\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://opencollective.com/sight-and-sound-ministries\" target=\"_blank\">\n  <img width=\"70\" src=\"https://user-images.githubusercontent.com/17680888/232316426-cb99b4cf-0ccb-4e73-a6ce-e16dba6aadf4.png\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://chudovo.com/\" target=\"_blank\">\n  <img width=\"70\" src=\"https://images.opencollective.com/chudovo/3c866f5/logo/256.png?height=256\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://www.honrev.com\" target=\"_blank\">\n  <img width=\"70\" src=\"https://github.com/user-attachments/assets/b3203350-34c1-4637-b8b1-d9b8bab346d3\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://github.com/alan-eu\" target=\"_blank\">\n  <img width=\"70\" src=\"https://avatars.githubusercontent.com/u/18175329?s=52&v=4\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://www.exoflare.com/open-source/?utm_source=dayjs&utm_campaign=open_source\" target=\"_blank\">\n  <img width=\"70\" src=\"https://user-images.githubusercontent.com/17680888/162761622-1407a849-0c41-4591-8aa9-f98114ec2092.png\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://github.com/storyblok\" target=\"_blank\">\n  <img width=\"70\" src=\"https://avatars.githubusercontent.com/u/13880908?s=200&v=4\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://bestkru.com/\" target=\"_blank\">\n  <img width=\"70\" src=\"https://avatars.githubusercontent.com/u/159320286\" alt=\"BestKru\">\n</a>\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n<a href=\"https://route4me.com/\" target=\"_blank\">\n  <img width=\"70\" src=\"https://github.com/user-attachments/assets/3fbc86c5-98a9-49c2-beae-1969026fcd76\" alt=\"Route Optimizer and Route Planner Software\">\n</a>\n\n\n## Contributors\n\nThis project exists thanks to all the people who contribute.\n\nPlease give us a 💖 star 💖 to support us. Thank you.\n\nAnd thank you to all our backers! 🙏\n\n<a href=\"https://opencollective.com/dayjs/backer/0/website?requireActive=false\" target=\"_blank\"><img width=\"35\" src=\"https://opencollective.com/dayjs/backer/0/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/dayjs/backer/1/website?requireActive=false\" target=\"_blank\"><img width=\"35\" src=\"https://opencollective.com/dayjs/backer/1/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/dayjs/backer/2/website?requireActive=false\" target=\"_blank\"><img width=\"35\" src=\"https://opencollective.com/dayjs/backer/2/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/dayjs/backer/3/website?requireActive=false\" target=\"_blank\"><img width=\"35\" src=\"https://opencollective.com/dayjs/backer/3/avatar.svg?requireActive=false\"></a>\n<br />\n<a href=\"https://opencollective.com/dayjs#backers\" target=\"_blank\"><img src=\"https://opencollective.com/dayjs/contributors.svg?width=890\" /></a>\n\n## License\n\nDay.js is licensed under a [MIT License](./LICENSE).\n", "time": {"created": "2022-01-26T21:07:03.974Z", "modified": "2024-08-20T15:23:43.781Z", "1.10.7": "2021-09-10T09:33:32.812Z", "1.10.6": "2021-07-06T07:41:30.824Z", "1.10.5": "2021-05-26T06:56:18.515Z", "1.10.4": "2021-01-22T10:05:16.755Z", "1.10.3": "2021-01-09T08:13:40.243Z", "1.10.2": "2021-01-05T23:55:03.420Z", "1.10.1": "2021-01-03T13:05:00.604Z", "1.10.0": "2021-01-03T08:27:32.973Z", "1.9.8": "2020-12-27T03:47:19.931Z", "1.9.7": "2020-12-05T14:57:42.065Z", "1.9.6": "2020-11-10T06:37:50.802Z", "1.9.5": "2020-11-05T06:13:09.326Z", "1.9.4": "2020-10-23T06:47:38.087Z", "1.9.3": "2020-10-13T09:48:32.220Z", "1.9.2": "2020-10-13T08:51:17.276Z", "1.9.1": "2020-09-28T12:37:21.940Z", "1.9.0": "2020-09-28T02:35:05.671Z", "1.8.36": "2020-09-17T09:31:26.222Z", "1.8.35": "2020-09-02T13:10:55.964Z", "1.8.34": "2020-08-20T03:30:00.334Z", "1.8.33": "2020-08-10T04:00:11.031Z", "1.8.32": "2020-08-04T09:38:52.182Z", "1.8.31": "2020-07-29T02:53:00.569Z", "1.8.30": "2020-07-22T02:33:07.343Z", "1.8.29": "2020-07-02T15:01:19.399Z", "1.8.28": "2020-05-28T03:43:42.185Z", "1.8.27": "2020-05-14T08:11:48.329Z", "1.8.26": "2020-04-30T04:11:43.185Z", "1.8.25": "2020-04-21T03:58:20.543Z", "1.8.24": "2020-04-10T03:06:34.263Z", "1.8.23": "2020-03-16T04:29:45.104Z", "1.8.22": "2020-03-08T05:19:28.850Z", "1.8.21": "2020-02-26T06:10:03.234Z", "1.8.20": "2020-02-04T11:00:46.476Z", "1.8.19": "2020-01-06T09:16:22.890Z", "1.8.18": "2019-12-18T06:19:03.435Z", "1.8.17": "2019-11-06T06:40:19.982Z", "1.8.16": "2019-08-27T06:22:51.977Z", "1.8.15": "2019-07-08T14:30:36.705Z", "1.8.14": "2019-05-07T18:43:04.765Z", "1.8.13": "2019-04-26T15:18:00.605Z", "1.8.12": "2019-04-02T10:09:14.408Z", "1.8.11": "2019-03-21T04:54:20.252Z", "1.8.10": "2019-03-10T12:54:24.171Z", "1.8.9": "2019-03-06T08:45:39.977Z", "1.8.8": "2019-02-25T04:04:38.389Z", "1.8.7": "2019-02-24T09:59:41.193Z", "1.8.6": "2019-02-14T03:37:08.932Z", "1.8.5": "2019-02-07T13:49:47.528Z", "1.8.4": "2019-02-05T14:29:24.696Z", "1.8.3": "2019-02-04T16:21:31.176Z", "1.8.2": "2019-02-02T08:11:11.363Z", "1.8.1": "2019-02-02T07:59:41.226Z", "1.8.0": "2019-01-14T02:40:43.236Z", "1.7.8": "2018-12-13T15:26:44.259Z", "1.7.7": "2018-09-26T07:13:22.713Z", "1.7.6": "2018-09-25T09:09:04.037Z", "1.7.5": "2018-08-10T03:18:23.096Z", "1.7.4": "2018-07-11T02:19:49.234Z", "1.7.3": "2018-07-10T02:54:00.388Z", "1.7.2": "2018-07-04T01:55:29.527Z", "1.7.1": "2018-07-03T07:41:38.586Z", "1.7.0": "2018-07-02T02:59:32.662Z", "1.6.10": "2018-06-25T14:57:30.045Z", "1.6.9": "2018-06-14T09:46:19.218Z", "1.6.8": "2018-06-14T02:51:13.974Z", "1.6.7": "2018-06-11T06:40:05.532Z", "1.6.6": "2018-06-06T07:55:38.804Z", "1.6.5": "2018-05-31T09:25:37.946Z", "1.6.4": "2018-05-25T07:03:38.923Z", "1.6.3": "2018-05-21T02:54:51.631Z", "1.6.2": "2018-05-18T09:40:34.966Z", "1.6.1": "2018-05-18T08:08:01.715Z", "1.6.0": "2018-05-15T08:19:30.488Z", "1.5.24": "2018-05-10T05:43:32.001Z", "1.5.23": "2018-05-09T01:24:50.895Z", "1.5.22": "2018-05-08T02:51:32.317Z", "1.5.21": "2018-05-07T16:19:32.071Z", "1.5.20": "2018-05-07T01:54:14.687Z", "1.5.19": "2018-05-04T01:20:48.643Z", "1.5.18": "2018-05-03T12:19:23.679Z", "1.5.17": "2018-05-03T06:05:43.332Z", "1.5.16": "2018-04-28T08:55:23.170Z", "1.5.15": "2018-04-26T17:01:45.899Z", "1.5.14": "2018-04-26T01:47:36.554Z", "1.5.13": "2018-04-25T16:56:12.315Z", "1.5.12": "2018-04-25T15:46:54.775Z", "1.5.11": "2018-04-24T05:36:46.749Z", "1.5.10": "2018-04-23T06:36:16.951Z", "1.5.9": "2018-04-21T11:23:12.857Z", "1.5.8": "2018-04-20T16:09:00.670Z", "1.5.6": "2018-04-20T07:25:37.088Z", "1.5.5": "2018-04-19T09:15:24.507Z", "1.5.4": "2018-04-19T02:18:41.722Z", "1.5.3": "2018-04-15T14:54:35.539Z", "1.5.2": "2018-04-15T13:31:15.005Z", "1.5.1": "2018-04-13T10:27:10.507Z", "1.5.0": "2018-04-13T08:09:30.854Z", "1.4.3": "2018-04-13T03:39:24.210Z", "1.4.2": "2018-04-12T15:43:38.104Z", "1.4.1": "2018-04-12T15:35:33.686Z", "1.4.0": "2018-04-12T09:09:39.708Z", "1.3.0": "2018-04-11T10:30:52.236Z", "1.2.0": "2018-04-11T08:31:18.316Z", "1.1.0": "2018-04-11T02:57:53.659Z", "1.0.1": "2018-04-11T02:54:24.606Z", "1.0.0": "2018-04-10T01:31:28.371Z", "1.10.8": "2022-02-28T12:50:57.644Z", "1.11.0": "2022-03-14T17:01:26.839Z", "1.11.1": "2022-04-15T03:26:08.699Z", "2.0.0-alpha.1": "2022-04-29T12:07:48.114Z", "2.0.0-alpha.2": "2022-05-03T15:18:19.011Z", "1.11.2": "2022-05-06T16:18:59.588Z", "1.11.3": "2022-06-06T02:55:47.801Z", "1.11.4": "2022-07-19T17:04:22.923Z", "1.11.5": "2022-08-12T12:57:19.792Z", "2.0.0-alpha.3": "2022-09-26T03:05:09.545Z", "2.0.0-alpha.4": "2022-09-26T04:32:21.015Z", "1.11.6": "2022-10-21T00:48:28.118Z", "1.11.7": "2022-12-06T15:11:37.718Z", "1.11.8": "2023-06-02T12:24:38.210Z", "1.11.9": "2023-07-01T03:14:00.677Z", "1.11.10": "2023-09-19T14:07:20.710Z", "1.11.11": "2024-04-28T11:59:08.743Z", "1.11.12": "2024-07-18T12:40:51.167Z", "1.11.13": "2024-08-20T14:59:52.917Z"}, "versions": {"1.10.7": {"name": "dayjs", "version": "1.10.7", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "9a4370812a47d8f5e8e32660f5f44be23b55e52e", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.7", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"shasum": "2cf5f91add28116748440866a0a1d26f3a6ce468", "size": 135203, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.7.tgz", "integrity": "sha512-P6twpd70BcPK34K26uJ1KT3wlhpuOAPoMwJzpsIWUxHZ7wpmbdZL/hQqBDfz7hGurYSa5PhzdhDHtt319hL3ig=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.7_1631266412586_0.149707763947045"}, "_hasShrinkwrap": false, "publish_time": 1631266412812, "_cnpm_publish_time": 1631266412812, "_cnpmcore_publish_time": "2021-12-15T14:59:20.948Z"}, "1.10.6": {"name": "dayjs", "version": "1.10.6", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "42e494b447cec61428d80fe258edf7ba7a7ec202", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.6", "_nodeVersion": "14.17.3", "_npmVersion": "6.14.13", "dist": {"shasum": "288b2aa82f2d8418a6c9d4df5898c0737ad02a63", "size": 134114, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.6.tgz", "integrity": "sha512-AztC/IOW4L1Q41A86phW5Thhcrco3xuAA+YX/BLpLWWjRcTj5TOt/QImBLmCKlrF7u7k47arTnOyL6GnbG8Hvw=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.6_1625557290503_0.028812704123890365"}, "_hasShrinkwrap": false, "publish_time": 1625557290824, "_cnpm_publish_time": 1625557290824, "_cnpmcore_publish_time": "2021-12-15T14:59:21.219Z"}, "1.10.5": {"name": "dayjs", "version": "1.10.5", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "aa5b524608c363a8b1e8b74b99c50ecb929075ce", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.5", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "5600df4548fc2453b3f163ebb2abbe965ccfb986", "size": 132858, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.5.tgz", "integrity": "sha512-BUFis41ikLz+65iH6LHQCDm4YPMj5r1YFLdupPIyM4SGcXMmtiLQ7U37i+hGS8urIuqe7I/ou3IS1jVc4nbN4g=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.5_1622012178354_0.3231366305492982"}, "_hasShrinkwrap": false, "publish_time": 1622012178515, "_cnpm_publish_time": 1622012178515, "_cnpmcore_publish_time": "2021-12-15T14:59:21.587Z"}, "1.10.4": {"name": "dayjs", "version": "1.10.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "2ab64ac3e84375e167fe1b23cb2282c9fdb5c930", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.4", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "8e544a9b8683f61783f570980a8a80eaf54ab1e2", "size": 130255, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.4.tgz", "integrity": "sha512-RI/Hh4kqRc1UKLOAf/T5zdMMX5DQIlDxwUe3wSyMMnEbGunnpENCdbUgM+dW7kXidZqCttBrmw7BhN4TMddkCw=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.4_1611309916496_0.3212535142677464"}, "_hasShrinkwrap": false, "publish_time": 1611309916755, "_cnpm_publish_time": 1611309916755, "_cnpmcore_publish_time": "2021-12-15T14:59:21.813Z"}, "1.10.3": {"name": "dayjs", "version": "1.10.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "907478e40511acad95b7ea5be04b9bc460a99785", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.3", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "cf3357c8e7f508432826371672ebf376cb7d619b", "size": 128979, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.3.tgz", "integrity": "sha512-/2fdLN987N8Ki7Id8BUN2nhuiRyxTLumQnSQf9CNncFCyqFsSKb9TNhzRYcC8K8eJSJOKvbvkImo/MKKhNi4iw=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.3_1610180020044_0.5537962979365516"}, "_hasShrinkwrap": false, "publish_time": 1610180020243, "_cnpm_publish_time": 1610180020243, "_cnpmcore_publish_time": "2021-12-15T14:59:22.046Z"}, "1.10.2": {"name": "dayjs", "version": "1.10.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "ac9076024507684ff50f1e9a67af1f9172b11ee1", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "8f3a424ceb944a8193506804b0045a773d2d0672", "size": 128439, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.2.tgz", "integrity": "sha512-h/YtykNNTR8Qgtd1Fxl5J1/SFP1b7SOk/M1P+Re+bCdFMV0IMkuKNgHPN7rlvvuhfw24w0LX78iYKt4YmePJNQ=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.2_1609890903222_0.41956484017922513"}, "_hasShrinkwrap": false, "publish_time": 1609890903420, "_cnpm_publish_time": 1609890903420, "_cnpmcore_publish_time": "2021-12-15T14:59:22.333Z"}, "1.10.1": {"name": "dayjs", "version": "1.10.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "esm/index.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "873b749e67f35f206bccdcd9cac367ee0b475203", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"shasum": "114f678624842035396667a24eb1436814bc16fd", "size": 128032, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.1.tgz", "integrity": "sha512-2xg7JrHQeLBQFkvTumLoy62x1siyeocc98QwjtURgvRqOPYmAkMUdmSjrOA+MlmL6QMQn5MUhDf6rNZNuPc1LQ=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.1_1609679100445_0.4401581479375698"}, "_hasShrinkwrap": false, "publish_time": 1609679100604, "_cnpm_publish_time": 1609679100604, "_cnpmcore_publish_time": "2021-12-15T14:59:22.672Z"}, "1.10.0": {"name": "dayjs", "version": "1.10.0", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "esm/index.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "b60466e42c66d14a3813dfbc75b0bd80a912af2c", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.0", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"shasum": "41ae4c1a5116c446738c4ec7e9f4f10e937e698b", "size": 127929, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.0.tgz", "integrity": "sha512-U/yusl5TQPvU1Tlhsu3A82Ebn4BNKHJMIk9AePYHCmGtTXcuPkODiEhkzqA8mnQ0bbgoR6+oha2ioZEewOiP2A=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.0_1609662452745_0.9785291297167815"}, "_hasShrinkwrap": false, "publish_time": 1609662452973, "_cnpm_publish_time": 1609662452973, "_cnpmcore_publish_time": "2021-12-15T14:59:22.988Z"}, "1.9.8": {"name": "dayjs", "version": "1.9.8", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "fb3dd1b692966fdc41839ab4962deca9715f2d50", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.8", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"shasum": "9a65fbdca037e3d5835f98672da6e796f757cd58", "size": 126290, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.8.tgz", "integrity": "sha512-F42qBtJRa30FKF7XDnOQyNUTsaxDkuaZRj/i7BejSHC34LlLfPoIU4aeopvWfM+m1dJ6/DHKAWLg2ur+pLgq1w=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.8_1609040839732_0.13756038870501874"}, "_hasShrinkwrap": false, "publish_time": 1609040839931, "_cnpm_publish_time": 1609040839931, "_cnpmcore_publish_time": "2021-12-15T14:59:23.305Z"}, "1.9.7": {"name": "dayjs", "version": "1.9.7", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "17221ec149161e5e87a7b34fb8562d3781913216", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.7", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"shasum": "4b260bb17dceed2d5f29038dfee03c65a6786fc0", "size": 125786, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.7.tgz", "integrity": "sha512-IC877KBdMhBrCfBfJXHQlo0G8keZ0Opy7YIIq5QKtUbCuHMzim8S4PyiVK4YmihI3iOF9lhfUBW4AQWHTR5WHA=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.7_1607180261804_0.6005153638329777"}, "_hasShrinkwrap": false, "publish_time": 1607180262065, "_cnpm_publish_time": 1607180262065, "_cnpmcore_publish_time": "2021-12-15T14:59:23.615Z"}, "1.9.6": {"name": "dayjs", "version": "1.9.6", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "ed9629b5ab2895652111fc854e6081422ed5c010", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.6", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "6f0c77d76ac1ff63720dd1197e5cb87b67943d70", "size": 124699, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.6.tgz", "integrity": "sha512-HngNLtPEBWRo8EFVmHFmSXAjtCX8rGNqeXQI0Gh7wCTSqwaKgPIDqu9m07wABVopNwzvOeCb+2711vQhDlcIXw=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.6_1604990270637_0.4504234588123861"}, "_hasShrinkwrap": false, "publish_time": 1604990270802, "_cnpm_publish_time": 1604990270802, "_cnpmcore_publish_time": "2021-12-15T14:59:23.893Z"}, "1.9.5": {"name": "dayjs", "version": "1.9.5", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "236a2d8c0b9b623a865cba67c15de68d0fe00257", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.5", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "fd49994ebe71639d2ce9575e97186642dfce9808", "size": 124354, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.5.tgz", "integrity": "sha512-WULIw7UpW/E0y6VywewpbXAMH3d5cZijEhoHLwM+OMVbk/NtchKS/W+57H/0P1rqU7gHrAArjiRLHCUhgMQl6w=="}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.5_1604556789204_0.3676469613821034"}, "_hasShrinkwrap": false, "publish_time": 1604556789326, "_cnpm_publish_time": 1604556789326, "_cnpmcore_publish_time": "2021-12-15T14:59:24.168Z"}, "1.9.4": {"name": "dayjs", "version": "1.9.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "e21aa170a31e4b4c4078adf1cdc4983bb98e6a4f", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.4", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"shasum": "fcde984e227f4296f04e7b05720adad2e1071f1b", "size": 123272, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.4.tgz", "integrity": "sha512-ABSF3alrldf7nM9sQ2U+Ln67NRwmzlLOqG7kK03kck0mw3wlSSEKv/XhKGGxUjQcS57QeiCyNdrFgtj9nWlrng=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.4_1603435657945_0.39753119588989394"}, "_hasShrinkwrap": false, "publish_time": 1603435658087, "_cnpm_publish_time": 1603435658087, "_cnpmcore_publish_time": "2021-12-15T14:59:24.838Z"}, "1.9.3": {"name": "dayjs", "version": "1.9.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "c0200e40ba7f4ae896fac03ec1dbab24d00f4e6f", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.3", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "b7f94b22ad2a136a4ca02a01ab68ae893fe1a268", "size": 116745, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.3.tgz", "integrity": "sha512-V+1SyIvkS+HmNbN1G7A9+ERbFTV9KTXu6Oor98v2xHmzzpp52OIJhQuJSTywWuBY5pyAEmlwbCi1Me87n/SLOw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.3_1602582512038_0.3038776164527315"}, "_hasShrinkwrap": false, "publish_time": 1602582512220, "_cnpm_publish_time": 1602582512220, "_cnpmcore_publish_time": "2021-12-15T14:59:25.084Z"}, "1.9.2": {"name": "dayjs", "version": "1.9.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "181c6470b0ae7fa00e15a767a1df47e72be56a43", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.2", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "544197b9e84e5193bcc55752cd38a88d7ccbfa79", "size": 116139, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.2.tgz", "integrity": "sha512-5l+y3PCqAe6LXEC/Tcx7O3t/qqESZO9siraIXZSrPRNqwFzJopYcaKSh271XbAUsdQ3dUBcJ5N//nEjTjF1ebw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.2_1602579077119_0.22527670952791645"}, "_hasShrinkwrap": false, "publish_time": 1602579077276, "_cnpm_publish_time": 1602579077276, "_cnpmcore_publish_time": "2021-12-15T14:59:25.424Z"}, "1.9.1": {"name": "dayjs", "version": "1.9.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "ae83edf56f7b9d24e6587b385fa650284426115f", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.1", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"shasum": "201a755f7db5103ed6de63ba93a984141c754541", "size": 113066, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.1.tgz", "integrity": "sha512-01NCTBg8cuMJG1OQc6PR7T66+AFYiPwgDvdJmvJBn29NGzIG+DIFxPLNjHzwz3cpFIvG+NcwIjP9hSaPVoOaDg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.1_1601296641829_0.7617126443332192"}, "_hasShrinkwrap": false, "publish_time": 1601296641940, "_cnpm_publish_time": 1601296641940, "_cnpmcore_publish_time": "2021-12-15T14:59:25.641Z"}, "1.9.0": {"name": "dayjs", "version": "1.9.0", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "de3e3fbb9cd0be9c0a4e055dc9cfd84f4e9f484a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.9.0", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"shasum": "ec6197d974143f5563913d08af71b3f21e008d5d", "size": 112897, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.9.0.tgz", "integrity": "sha512-Ym40L4ZN+LUMCbsYXWDh/RxEntfaWChc/M+iD5/tat8jylbwE1sa+7eoHxsZ+NB8al6D8VYBmP6eV6GsZ8i01A=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.9.0_1601260500555_0.10720348870096785"}, "_hasShrinkwrap": false, "publish_time": 1601260505671, "_cnpm_publish_time": 1601260505671, "_cnpmcore_publish_time": "2021-12-15T14:59:25.976Z"}, "1.8.36": {"name": "dayjs", "version": "1.8.36", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "a78956e8664afa61bb2744c7a1bbe01d2589a4fe", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.36", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"shasum": "be36e248467afabf8f5a86bae0de0cdceecced50", "size": 111622, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.36.tgz", "integrity": "sha512-3VmRXEtw7RZKAf+4Tv1Ym9AGeo8r8+CjDi26x+7SYQil1UqtqdaokhzoEJohqlzt0m5kacJSDhJQkG/LWhpRBw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.36_1600335085981_0.8590899499215714"}, "_hasShrinkwrap": false, "publish_time": 1600335086222, "_cnpm_publish_time": 1600335086222, "_cnpmcore_publish_time": "2021-12-15T14:59:26.178Z"}, "1.8.35": {"name": "dayjs", "version": "1.8.35", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "82b50343521ca5ec41a2faf2f05c4d9050801308", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.35", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "67118378f15d31623f3ee2992f5244b887606888", "size": 107945, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.35.tgz", "integrity": "sha512-isAbIEenO4ilm6f8cpqvgjZCsuerDAz2Kb7ri201AiNn58aqXuaLJEnCtfIMdCvERZHNGRY5lDMTr/jdAnKSWQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.35_1599052255852_0.8677361399003605"}, "_hasShrinkwrap": false, "publish_time": 1599052255964, "_cnpm_publish_time": 1599052255964, "_cnpmcore_publish_time": "2021-12-15T14:59:26.400Z"}, "1.8.34": {"name": "dayjs", "version": "1.8.34", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "e1890c969122c677350af2f457b7066d1fcf787a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.34", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "d3ad33cc43d6b0f24cb8686b90aad2c653708069", "size": 107740, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.34.tgz", "integrity": "sha512-Olb+E6EoMvdPmAMq2QoucuyZycKHjTlBXmRx8Ada+wGtq4SIXuDCdtoaX4KkK0yjf1fJLnwXQURr8gQKWKaybw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.34_1597894200155_0.9708045235792298"}, "_hasShrinkwrap": false, "publish_time": 1597894200334, "_cnpm_publish_time": 1597894200334, "_cnpmcore_publish_time": "2021-12-15T14:59:26.696Z"}, "1.8.33": {"name": "dayjs", "version": "1.8.33", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "6e5ffce3af00df88428ac3634d6a89c985d79217", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.33", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "18bc4a2b6c1c6f4d67b4c4f2536c0b97e5b766f7", "size": 107412, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.33.tgz", "integrity": "sha512-881TDLZCdpJFKbraWRHcUG8zfMLLX400ENf9rFZDuWc5zYMss6xifo2PhlDX0ftOmR2NRmaIY47bAa4gKQfXqw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.33_1597032010873_0.9588957934013667"}, "_hasShrinkwrap": false, "publish_time": 1597032011031, "_cnpm_publish_time": 1597032011031, "_cnpmcore_publish_time": "2021-12-15T14:59:26.902Z"}, "1.8.32": {"name": "dayjs", "version": "1.8.32", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "b18f73928c056b2903f4cdf5cf408592291432ad", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.32", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "66c48b95c397d9f7907e89bd29f78b3d19d40294", "size": 104644, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.32.tgz", "integrity": "sha512-V91aTRu5btP+uzGHaaOfodckEfBWhmi9foRP7cauAO1PTB8+tZ9o0Jec7q6TIIRY1N4q1IfiKsZunkB/AEWqMQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.32_1596533931984_0.41585742652652935"}, "_hasShrinkwrap": false, "publish_time": 1596533932182, "_cnpm_publish_time": 1596533932182, "_cnpmcore_publish_time": "2021-12-15T14:59:27.184Z"}, "1.8.31": {"name": "dayjs", "version": "1.8.31", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "7556d70b4a0ebd5c40eb815e1f50e2a832f4275f", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.31", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "0cd1114c2539dd5ad9428be0c38df6d4bb40b9d3", "size": 102857, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.31.tgz", "integrity": "sha512-mPh1mslned+5PuIuiUfbw4CikHk6AEAf2Baxih+wP5fssv+wmlVhvgZ7mq+BhLt7Sr/Hc8leWDiwe6YnrpNt3g=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.31_1595991180422_0.9236119888048231"}, "_hasShrinkwrap": false, "publish_time": 1595991180569, "_cnpm_publish_time": 1595991180569, "_cnpmcore_publish_time": "2021-12-15T14:59:27.412Z"}, "1.8.30": {"name": "dayjs", "version": "1.8.30", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "e3750ecb93b56d08a79e1f4cd666b9863f885a2c", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.30", "_nodeVersion": "12.18.2", "_npmVersion": "6.14.5", "dist": {"shasum": "d3b314d3ccdc179015d915fd3c6e14422c026378", "size": 103142, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.30.tgz", "integrity": "sha512-5s5IGuP5bVvIbOWkEDcfmXsUj24fZW1NMHVVSdSFF/kW8d+alZcI9SpBKC+baEyBe+z3fUp17y75ulstv5swUw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.30_1595385187125_0.4516770172736595"}, "_hasShrinkwrap": false, "publish_time": 1595385187343, "_cnpm_publish_time": 1595385187343, "_cnpmcore_publish_time": "2021-12-15T14:59:27.692Z"}, "1.8.29": {"name": "dayjs", "version": "1.8.29", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "f236aefde6ebb706a5afc4578597e99ff9511e87", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.29", "_nodeVersion": "12.18.2", "_npmVersion": "6.14.5", "dist": {"shasum": "5d23e341de6bfbd206c01136d2fb0f01877820f5", "size": 101848, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.29.tgz", "integrity": "sha512-Vm6teig8ZWK7rH/lxzVGxZJCljPdmUr6q/3f4fr5F0VWNGVkZEjZOQJsAN8hUHUqn+NK4XHNEpJZS1MwLyDcLw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.29_1593702079184_0.5203065767063779"}, "_hasShrinkwrap": false, "publish_time": 1593702079399, "_cnpm_publish_time": 1593702079399, "_cnpmcore_publish_time": "2021-12-15T14:59:27.975Z"}, "1.8.28": {"name": "dayjs", "version": "1.8.28", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "bc071568f12cdb3fc00f7f20552b86c80c853d3a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.28", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.4", "dist": {"shasum": "37aa6201df483d089645cb6c8f6cef6f0c4dbc07", "size": 101413, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.28.tgz", "integrity": "sha512-ccnYgKC0/hPSGXxj7Ju6AV/BP4HUkXC2u15mikXT5mX9YorEaoi1bEKOmAqdkJHN4EEkmAf97SpH66Try5Mbeg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.28_1590637421980_0.06192227841419151"}, "_hasShrinkwrap": false, "publish_time": 1590637422185, "_cnpm_publish_time": 1590637422185, "_cnpmcore_publish_time": "2021-12-15T14:59:28.271Z"}, "1.8.27": {"name": "dayjs", "version": "1.8.27", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "9c0523e4dc0fa3c1ae59065e48a18b606e708d28", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.27", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "a8ae63ee990af28c05c430f0e160ae835a0fbbf8", "size": 100984, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.27.tgz", "integrity": "sha512-Jpa2acjWIeOkg8KURUHICk0EqnEFSSF5eMEscsOgyJ92ZukXwmpmRkPSUka7KHSfbj5eKH30ieosYip+ky9emQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.27_1589443908152_0.4118977895926055"}, "_hasShrinkwrap": false, "publish_time": 1589443908329, "_cnpm_publish_time": 1589443908329, "_cnpmcore_publish_time": "2021-12-15T14:59:28.486Z"}, "1.8.26": {"name": "dayjs", "version": "1.8.26", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "c95be8aa66fed5f14b034c55af805ed08ac796a3", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.26", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "c6d62ccdf058ca72a8d14bb93a23501058db9f1e", "size": 98461, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.26.tgz", "integrity": "sha512-KqtAuIfdNfZR5sJY1Dixr2Is4ZvcCqhb0dZpCOt5dGEFiMzoIbjkTSzUb4QKTCsP+WNpGwUjAFIZrnZvUxxkhw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.26_1588219903041_0.15761848055161143"}, "_hasShrinkwrap": false, "publish_time": 1588219903185, "_cnpm_publish_time": 1588219903185, "_cnpmcore_publish_time": "2021-12-15T14:59:28.709Z"}, "1.8.25": {"name": "dayjs", "version": "1.8.25", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "bcea06730da2d19ddbad844de3557ee077af821c", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.25", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"shasum": "d09a8696cee7191bc1289e739f96626391b9c73c", "size": 97998, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.25.tgz", "integrity": "sha512-Pk36juDfQQGDCgr0Lqd1kw15w3OS6xt21JaLPE3lCfsEf8KrERGwDNwvK1tRjrjqFC0uZBJncT4smZQ4F+uV5g=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.25_1587441500096_0.34487992929912603"}, "_hasShrinkwrap": false, "publish_time": 1587441500543, "_cnpm_publish_time": 1587441500543, "_cnpmcore_publish_time": "2021-12-15T14:59:28.998Z"}, "1.8.24": {"name": "dayjs", "version": "1.8.24", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "3188e158ed4ebca6669b5464ff7d3d85bf1c8615", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.24", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"shasum": "2ef8a2ab9425eaf3318fe78825be1c571027488d", "size": 97588, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.24.tgz", "integrity": "sha512-bImQZbBv86zcOWOq6fLg7r4aqMx8fScdmykA7cSh+gH1Yh8AM0Dbw0gHYrsOrza6oBBnkK+/OaR+UAa9UsMrDw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.24_1586487994074_0.4216853589255629"}, "_hasShrinkwrap": false, "publish_time": 1586487994263, "_cnpm_publish_time": 1586487994263, "_cnpmcore_publish_time": "2021-12-15T14:59:29.349Z"}, "1.8.23": {"name": "dayjs", "version": "1.8.23", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "90eae156bf1f54070b31942ff649fa45f3d812a0", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.23", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"shasum": "07b5a8e759c4d75ae07bdd0ad6977f851c01e510", "size": 93210, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.23.tgz", "integrity": "sha512-NmYHMFONftoZbeOhVz6jfiXI4zSiPN6NoVWJgC0aZQfYVwzy/ZpESPHuCcI0B8BUMpSJQ08zenHDbofOLKq8hQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.23_1584332984885_0.8534998446794673"}, "_hasShrinkwrap": false, "publish_time": 1584332985104, "_cnpm_publish_time": 1584332985104, "_cnpmcore_publish_time": "2021-12-15T14:59:29.669Z"}, "1.8.22": {"name": "dayjs", "version": "1.8.22", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "193cf4be4c826129eaf554e5fc963eb6cdfd0154", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.22", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"shasum": "5e835d776b373e216678be8d12c336da71a25a9c", "size": 92125, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.22.tgz", "integrity": "sha512-N8IXfxBD62Y9cKTuuuSoOlCXRnnzaTj1vu91r855iq6FbY5cZqOZnW/95nUn6kJiR+W9PHHrLykEoQOe6fUKxQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.22_1583644768708_0.8161905226914961"}, "_hasShrinkwrap": false, "publish_time": 1583644768850, "_cnpm_publish_time": 1583644768850, "_cnpmcore_publish_time": "2021-12-15T14:59:30.003Z"}, "1.8.21": {"name": "dayjs", "version": "1.8.21", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org/", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "b9d64e1e7ab89f97520fb85794226c8352a739cd", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.8.21", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"shasum": "98299185b72b9b679f31c7ed987b63923c961552", "size": 91256, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.21.tgz", "integrity": "sha512-1kbWK0hziklUHkGgiKr7xm59KwAg/K3Tp7H/8X+f58DnNCwY3pKYjOCJpIlVs125FRBukGVZdKZojC073D0IeQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.21_1582697403076_0.8211839891323522"}, "_hasShrinkwrap": false, "publish_time": 1582697403234, "_cnpm_publish_time": 1582697403234, "_cnpmcore_publish_time": "2021-12-15T14:59:30.283Z"}, "1.8.20": {"name": "dayjs", "version": "1.8.20", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "0c385e177cc4078b67b58aae75158f4027ef26e4", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.20", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "dist": {"shasum": "724a5cb6ad1f6fc066b0bd9a800dedcc7886f19e", "size": 90811, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.20.tgz", "integrity": "sha512-mH0MCDxw6UCGJYxVN78h8ugWycZAO8thkj3bW6vApL5tS0hQplIDdAQcmbvl7n35H0AKdCJQaArTrIQw2xt4Qg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.20_1580814046199_0.12490373416764666"}, "_hasShrinkwrap": false, "publish_time": 1580814046476, "_cnpm_publish_time": 1580814046476, "_cnpmcore_publish_time": "2021-12-15T14:59:30.558Z"}, "1.8.19": {"name": "dayjs", "version": "1.8.19", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "731b9476f4a2f20c6cc89b4d6550edfff958b756", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.19", "_nodeVersion": "10.18.0", "_npmVersion": "6.13.4", "dist": {"shasum": "5117dc390d8f8e586d53891dbff3fa308f51abfe", "size": 87631, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.19.tgz", "integrity": "sha512-7kqOoj3oQSmqbvtvGFLU5iYqies+SqUiEGNT0UtUPPxcPYgY1BrkXR0Cq2R9HYSimBXN+xHkEN4Hi399W+Ovlg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.19_1578302182720_0.6664151203135829"}, "_hasShrinkwrap": false, "publish_time": 1578302182890, "_cnpm_publish_time": 1578302182890, "_cnpmcore_publish_time": "2021-12-15T14:59:30.895Z"}, "1.8.18": {"name": "dayjs", "version": "1.8.18", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "6b587dabc13b06a73c6bec9b77ec8aca890ebf0a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.18", "_nodeVersion": "10.18.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c9b3fcd5a8eca96ed20a907f4491516d6eda15c9", "size": 85383, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.18.tgz", "integrity": "sha512-JBMJZghNK8TtuoPnKNIzW9xavVVigld/zmZNpZSyQbkb2Opp55YIfZUpE4OEqPF/iyUVQTKcn1bC2HtC8B7s3g=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.18_1576649943235_0.6412803997014327"}, "_hasShrinkwrap": false, "publish_time": 1576649943435, "_cnpm_publish_time": 1576649943435, "_cnpmcore_publish_time": "2021-12-15T14:59:31.127Z"}, "1.8.17": {"name": "dayjs", "version": "1.8.17", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "579938380cd4402c7dbc91fcaa322aa3a5c5c1d7", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.17", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"shasum": "53ec413f2a7b02afbea1846d61bb260fa8567cea", "size": 84176, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.17.tgz", "integrity": "sha512-47VY/htqYqr9GHd7HW/h56PpQzRBSJcxIQFwqL3P20bMF/3az5c3PWdVY3LmPXFl6cQCYHL7c79b9ov+2bOBbw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.17_1573022419854_0.14089834706781934"}, "_hasShrinkwrap": false, "publish_time": 1573022419982, "_cnpm_publish_time": 1573022419982, "_cnpmcore_publish_time": "2021-12-15T14:59:31.325Z"}, "1.8.16": {"name": "dayjs", "version": "1.8.16", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "7241da91aab31613e316dce72cd723c162d2b54e", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.16", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "2a3771de537255191b947957af2fd90012e71e64", "size": 97432, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.16.tgz", "integrity": "sha512-XPmqzWz/EJiaRHjBqSJ2s6hE/BUoCIHKgdS2QPtTQtKcS9E4/Qn0WomoH1lXanWCzri+g7zPcuNV4aTZ8PMORQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.16_1566886971841_0.10000487508117128"}, "_hasShrinkwrap": false, "publish_time": 1566886971977, "_cnpm_publish_time": 1566886971977, "_cnpmcore_publish_time": "2021-12-15T14:59:31.599Z"}, "1.8.15": {"name": "dayjs", "version": "1.8.15", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "7f1a1e975d4e02ad90205d799d04a6c6514d272e", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.15", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"shasum": "7121bc04e6a7f2621ed6db566be4a8aaf8c3913e", "size": 94222, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.15.tgz", "integrity": "sha512-HYHCI1nohG52B45vCQg8Re3hNDZbMroWPkhz50yaX7Lu0ATyjGsTdoYZBpjED9ar6chqTx2dmSmM8A51mojnAg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.15_1562596236552_0.9189384539303123"}, "_hasShrinkwrap": false, "publish_time": 1562596236705, "_cnpm_publish_time": 1562596236705, "_cnpmcore_publish_time": "2021-12-15T14:59:31.880Z"}, "1.8.14": {"name": "dayjs", "version": "1.8.14", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "977d30571b471f693d52a91ac21ccd3d5dcbc984", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.14", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "43e4f62830c2c3342c3fcd25934dc9162dbe6f38", "size": 93934, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.14.tgz", "integrity": "sha512-AVhDmRTe541iWirnoeFSSDDGvCT6HWaNQ4z2WmmzXMGZj6ph6ydao2teKq/eUtR43GPJXlYFD+C/SotG1P9wUQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.14_1557254584593_0.5921265118917578"}, "_hasShrinkwrap": false, "publish_time": 1557254584765, "_cnpm_publish_time": 1557254584765, "_cnpmcore_publish_time": "2021-12-15T14:59:32.161Z"}, "1.8.13": {"name": "dayjs", "version": "1.8.13", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "68a04e15c404b2a693030e807b69ad2147fd24b8", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.13", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "51b5cdad23ba508bcea939a853b492fefb7fdc47", "size": 93736, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.13.tgz", "integrity": "sha512-JZ01l/PMU8OqwuUs2mOQ/CTekMtoXOUSylfjqjgDzbhRSxpFIrPnHn8Y8a0lfocNgAdBNZb8y0/gbzJ2riQ4WQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.13_1556291880421_0.3279232732582251"}, "_hasShrinkwrap": false, "publish_time": 1556291880605, "deprecated": "critical bug fixed in v1.8.14 .format API returns UTC offset when value is 0", "_cnpm_publish_time": 1556291880605, "_cnpmcore_publish_time": "2021-12-15T14:59:32.941Z"}, "1.8.12": {"name": "dayjs", "version": "1.8.12", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "566873acbdb562ff6354d255579eb323777d86f2", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.12", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "f9610fb9b115aa2fe2a07e742c130df418cee76a", "size": 79642, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.12.tgz", "integrity": "sha512-7keChCzzjU68sYJpk7kEI1Q9qbJyscyiW0STwEhqDFt+2js9pA/BSzmYE4PRxcMMoUMxNeY0TEMZHqV/JBR4OA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.12_1554199754203_0.24333423769443163"}, "_hasShrinkwrap": false, "publish_time": 1554199754408, "_cnpm_publish_time": 1554199754408, "_cnpmcore_publish_time": "2021-12-15T14:59:33.235Z"}, "1.8.11": {"name": "dayjs", "version": "1.8.11", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "be6b891b9640576216858952e576aba914d87726", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.11", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "1e0e681c9f1b6668d92dfe71289500ae0990529d", "size": 48519, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.11.tgz", "integrity": "sha512-eBvWmKn52ax/TBYg7f+tzzqARPl0D0/NUmf5QrZ0Pp+/vFoTP3Q7y4t6p438+5NPN+t8y8Sps3Ed34OV8sNFBQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.11_1553144060087_0.5802252407954831"}, "_hasShrinkwrap": false, "publish_time": 1553144060252, "_cnpm_publish_time": 1553144060252, "_cnpmcore_publish_time": "2021-12-15T14:59:33.463Z"}, "1.8.10": {"name": "dayjs", "version": "1.8.10", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "6e29a4aa793d48cc57635da6a19ef227fbe3e3b5", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.10", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "fc35a976ccac2a9c44b50485a06c4a5ecf5d1b37", "size": 47160, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.10.tgz", "integrity": "sha512-U+7kBBkJzPWww0vNeMkaBeJwnkivTACoajm+bTfwparjFcPI6/5JSQN40WVnX6yCsm20oGf1SkMkIIp4m/boAw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.10_1552222464003_0.8502737140190835"}, "_hasShrinkwrap": false, "publish_time": 1552222464171, "_cnpm_publish_time": 1552222464171, "_cnpmcore_publish_time": "2021-12-15T14:59:33.698Z"}, "1.8.9": {"name": "dayjs", "version": "1.8.9", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "3ca562a9150ad836f7f6faab4ee43b3344b7f311", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.9", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8b5fda2f995ff5f29705894e1d23240f7f674f61", "size": 44031, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.9.tgz", "integrity": "sha512-/LPzoQ77NiXf566p5babPBkqegpJ94koAQ0vUfkcfWYcuvzOTgr+N9V4IOnQ3H05Su/9dpFNOV1iPvEhAsRscw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.9_1551861939859_0.7800451544229221"}, "_hasShrinkwrap": false, "publish_time": 1551861939977, "_cnpm_publish_time": 1551861939977, "_cnpmcore_publish_time": "2021-12-15T14:59:33.989Z"}, "1.8.8": {"name": "dayjs", "version": "1.8.8", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "2e75247e687fde3889830224b9160f94d3c18bef", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.8", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "81c81a8bb2488ef859f6c9bead9b62d649072bfd", "size": 42603, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.8.tgz", "integrity": "sha512-qBY3kVAVJMxzS4e7DAwZZ/SSiO/DBXbvc/qqbyf1FbsZrxBq81QPdUEWhiPu8Fragf5RfHsLwtH8kCGwKL4qLQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.8_1551067478235_0.6540151753730277"}, "_hasShrinkwrap": false, "publish_time": 1551067478389, "_cnpm_publish_time": 1551067478389, "_cnpmcore_publish_time": "2021-12-15T14:59:34.224Z"}, "1.8.7": {"name": "dayjs", "version": "1.8.7", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "3671e366218de2d65528a90f5e121743e45bf76f", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.7", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "21f8593317830c8ba97bba5d53fc523cf4b7215f", "size": 43105, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.7.tgz", "integrity": "sha512-ugOPjw2XWFMVEV86KvwB25FW8+HLJohDdx5PSl9TatuZCiEAAoFzyGjEzi8taLTDpsbNwaH0rWPUcd5DwqXDhw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.7_1551002381018_0.9620395516448224"}, "_hasShrinkwrap": false, "publish_time": 1551002381193, "_cnpm_publish_time": 1551002381193, "_cnpmcore_publish_time": "2021-12-15T14:59:34.439Z"}, "1.8.6": {"name": "dayjs", "version": "1.8.6", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "dayjs.min.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "8a6a365527d2801e804406c7fbf2cde679534db2", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.6", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "b7a8ccfef173dae03e83a05a58788c9dbe948a35", "size": 40971, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.6.tgz", "integrity": "sha512-NLhaSS1/wWLRFy0Kn/VmsAExqll2zxRUPmPbqJoeMKQrFxG+RT94VMSE+cVljB6A76/zZkR0Xub4ihTHQ5HgGg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.6_1550115428733_0.39909340185705067"}, "_hasShrinkwrap": false, "publish_time": 1550115428932, "_cnpm_publish_time": 1550115428932, "_cnpmcore_publish_time": "2021-12-15T14:59:34.662Z"}, "1.8.5": {"name": "dayjs", "version": "1.8.5", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "./esm/index.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "f2514935eb83522306bc68f303db2923ed150ee8", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "0b066770f89a20022218544989f3d23e5e8db29a", "size": 39755, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.5.tgz", "integrity": "sha512-jo5sEFdsT43RqXxoqQVEuD7XL6iSIRxcjTgheJdlV0EHKObKP3pb9JcJEv/KStVMy25ABNQrFnplKcCit05vOA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.5_1549547387407_0.4210787035400687"}, "_hasShrinkwrap": false, "publish_time": 1549547387528, "_cnpm_publish_time": 1549547387528, "_cnpmcore_publish_time": "2021-12-15T14:59:34.860Z"}, "1.8.4": {"name": "dayjs", "version": "1.8.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "./src/index.js", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz && jest", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "9b434c959e99abde537a0871ca40622e6ec26b25", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e5a8fe080a7955e210045e6c26c5d1d4c88cf602", "size": 38697, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.4.tgz", "integrity": "sha512-Gd2W9A8f40Dwr17/O1W12cC61PSq8aUGKBWRVMs7n4XQNwPPIM9m7YzZPHMlW465Sia6t5KlKK52rYd5oT2T6A=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.4_1549376964523_0.21165326557760866"}, "_hasShrinkwrap": false, "publish_time": 1549376964696, "_cnpm_publish_time": 1549376964696, "_cnpmcore_publish_time": "2021-12-15T14:59:35.142Z"}, "1.8.3": {"name": "dayjs", "version": "1.8.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "./src/index.js", "scripts": {"test": "jest && TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "bdfb7c8528475c5e3f9aa7dd125ea61514a898d0", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "91a1ef672dc7dceb48794424fbe1571bfb8692c9", "size": 38273, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.3.tgz", "integrity": "sha512-e6wSnTA9R2fghIk5zXoOg9+uTFcmbonbf3wLXRAJtDApTTpNFWjFPyWUAsHonPBw36BgS/e+FCNf4RxsOYdo3Q=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.3_1549297291063_0.8839921135212694"}, "_hasShrinkwrap": false, "publish_time": 1549297291176, "_cnpm_publish_time": 1549297291176, "_cnpmcore_publish_time": "2021-12-15T14:59:35.370Z"}, "1.8.2": {"name": "dayjs", "version": "1.8.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "./src/index.js", "scripts": {"test": "jest && TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && npm run test-tz", "test-tz": "jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "9e8239163bf83146d054a213db1e7eea5ad7d2fa", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8668e50c1e048e1b1638ebe6c3552187a34ffa64", "size": 38072, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.2.tgz", "integrity": "sha512-iHNxe5kIbxmPgzJFjks9TFMokOu3TQcUUSagb/Ff7GZNi7ulYF0qaAZ61trZEFOONgrp4jvKVpBJ86qy4UsSoA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.2_1549095071191_0.2780756772063673"}, "_hasShrinkwrap": false, "publish_time": 1549095071363, "_cnpm_publish_time": 1549095071363, "_cnpmcore_publish_time": "2021-12-15T14:59:35.686Z"}, "1.8.1": {"name": "dayjs", "version": "1.8.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "module": "./src/index.js", "scripts": {"test": "jest", "test-dst": "TZ=Pacific/Auckland npm run dst-test && TZ=Europe/London npm run dst-test && npm run dst-test", "dst-test": "jest test/dst.test --coverage=false", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "9d5bc3058f782c440e33e95e680b2326becf14f3", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "663f3cb810716454669c2858db9c30069782d3bd", "size": 37096, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.1.tgz", "integrity": "sha512-BgRRN7+KoBn2z5b6J0craZ9GOo3ekiDDTltPCfa3skTyUJ6/0PU+wHsfyavBU6pSI2vIzJywAEXLoySj18ln8g=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.1_1549094381104_0.717182451805916"}, "_hasShrinkwrap": false, "publish_time": 1549094381226, "_cnpm_publish_time": 1549094381226, "_cnpmcore_publish_time": "2021-12-15T14:59:35.904Z"}, "1.8.0": {"name": "dayjs", "version": "1.8.0", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "test-dst": "TZ=Pacific/Auckland npm run dst-test && TZ=Europe/London npm run dst-test && npm run dst-test", "dst-test": "jest test/dst.test --coverage=false", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "97a60883f3ee99b1f42f9e8c0684b1f0f89db257", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.8.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "507963ac3023f4b1f9f399a278356946fc4c9ae9", "size": 19449, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.8.0.tgz", "integrity": "sha512-2ofInmfMKLLR5R02q3WEUuDt86UK33VQQTaEeJudF+C04ZUaekCP3VpB0NJPiyPDCGJWq9XYhHX2AemdxA8+dg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.8.0_1547433643116_0.8569487322624478"}, "_hasShrinkwrap": false, "publish_time": 1547433643236, "_cnpm_publish_time": 1547433643236, "_cnpmcore_publish_time": "2021-12-15T14:59:36.186Z"}, "1.7.8": {"name": "dayjs", "version": "1.7.8", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "e57378660eda04ce492fc716aec7860d5037e0b2", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.8", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.2", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "05d288f8d4b2140110cc1519cfe317d6f1f11a3c", "size": 17728, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.8.tgz", "integrity": "sha512-Gp4Y5KWeSri0QOWGzHQz7VrKDkfEpS92dCLK7P8hYowRFbaym1vj3d6CoHio3apSS4KSi/qb5Edemv26IN5Hfg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.8_1544714804143_0.29317260781970855"}, "_hasShrinkwrap": false, "publish_time": 1544714804259, "_cnpm_publish_time": 1544714804259, "_cnpmcore_publish_time": "2021-12-15T14:59:36.459Z"}, "1.7.7": {"name": "dayjs", "version": "1.7.7", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "38d52aa97cf63a7016665010fa1dd241c67e0404", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.7", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8df20f78ac2476e3f5348ef49f8f22ebc3016370", "size": 15066, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.7.tgz", "integrity": "sha512-Qlkiu0NNDpYwhk0syK4ImvAl/5YnsEMkvC2O123INviGeOA3Q8s5VyVkZzmN5SC7Wv9bb1+rfwO+uSqtHB4UWw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.7_1537946002488_0.4476843217740285"}, "_hasShrinkwrap": false, "publish_time": 1537946002713, "_cnpm_publish_time": 1537946002713, "_cnpmcore_publish_time": "2021-12-15T14:59:36.733Z"}, "1.7.6": {"name": "dayjs", "version": "1.7.6", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "d0b14194b83b1408685dc67a276f7059e37353d6", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.6", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "bbd0f39ef0aca9615043a1fd159809f056291590", "size": 14954, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.6.tgz", "integrity": "sha512-bVkHEhrSKAfuuF5z7isiRmYarcPsEvFM6NuA8MbOLJaAeL2JwKzen/QG4HuQYMjPJ4PRTi2rse71YHj38/1/+Q=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.6_1537866543887_0.020812366303974317"}, "_hasShrinkwrap": false, "publish_time": 1537866544037, "_cnpm_publish_time": 1537866544037, "_cnpmcore_publish_time": "2021-12-15T14:59:37.021Z"}, "1.7.5": {"name": "dayjs", "version": "1.7.5", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "012caa56659382bc39b5b1f0d793cf30f4a926d3", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "14715cb565d1f8cb556a8531cb14bf1fc33067cc", "size": 13917, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.5.tgz", "integrity": "sha512-OzkAcosqOgWgQF+dQTXO/iaSGa3hMs/sSkfzkxwWpZXqJEbaA0V6O1V+Ew2tGBlTz1r7Rb7opU3w8ympWb9d2Q=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.5_1533871103028_0.31842986499001813"}, "_hasShrinkwrap": false, "publish_time": 1533871103096, "_cnpm_publish_time": 1533871103096, "_cnpmcore_publish_time": "2021-12-15T14:59:37.231Z"}, "1.7.4": {"name": "dayjs", "version": "1.7.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "3c499f3a211f8bbdfe666095a6a4feef3d8ae12c", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.4", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "6001277372aec83021943157fa279ecf2b5f5cf8", "size": 13541, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.4.tgz", "integrity": "sha512-0uMiu5BH7AB9PUnw3KD9W2BqLB0/5yyfs5pBoF6Xp2JzEYNf35VOSbBKDD0RyMQ6p19M0qOmoGTdaAxkKJ/mqg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.4_1531275589116_0.11251752118894953"}, "_hasShrinkwrap": false, "publish_time": 1531275589234, "_cnpm_publish_time": 1531275589234, "_cnpmcore_publish_time": "2021-12-15T14:59:37.463Z"}, "1.7.3": {"name": "dayjs", "version": "1.7.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "d344fa3d1eb3f7989e1d3de6279a73e6f9148f50", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.3", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "c2c52e060dc134ac6e24dca1345f000bd41c10e0", "size": 13479, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.3.tgz", "integrity": "sha512-Fw5+RCJJg1GWEv9mZCxSJ06K1WN59EMSO6hYxI9ag6k7H+asXFaFUn3oQnh6sLrtXfCgXqevBkTeyxosHhWjhQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.3_1531191240318_0.4779372332717817"}, "_hasShrinkwrap": false, "publish_time": 1531191240388, "_cnpm_publish_time": 1531191240388, "_cnpmcore_publish_time": "2021-12-15T14:59:37.685Z"}, "1.7.2": {"name": "dayjs", "version": "1.7.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "89cdce1f3165041cdb15410b9450ca84d1ae1c7d", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "31b65d962a59a7e5bb2a20f01002a4b8d6d6f62c", "size": 13428, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.2.tgz", "integrity": "sha512-e6pe0/7Fon9HxwV1CP/kzPwMi985qC0VDKQ9AID71V88vsJ/0kw28IsyFAaBQyPf7agmtF9j8Zo73IQKKOiRqg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.2_1530669329439_0.6138849896357514"}, "_hasShrinkwrap": false, "publish_time": 1530669329527, "_cnpm_publish_time": 1530669329527, "_cnpmcore_publish_time": "2021-12-15T14:59:37.920Z"}, "1.7.1": {"name": "dayjs", "version": "1.7.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "2836fe5e226240287afdc705ca0103ed298d967a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "903c770465daba9bd7c3f6c5dd1cc97522d21e20", "size": 13391, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.1.tgz", "integrity": "sha512-u9O29qBpa0r3ydysGM0BBFiiudqDVODNh/vtfixR/PbAQvXQ96bf8eJ5LugVs89rifUjgBwAaNLkw3MxqIo0iQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.1_1530603697857_0.7574619870938502"}, "_hasShrinkwrap": false, "publish_time": 1530603698586, "_cnpm_publish_time": 1530603698586, "_cnpmcore_publish_time": "2021-12-15T14:59:38.149Z"}, "1.7.0": {"name": "dayjs", "version": "1.7.0", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "ba9c1059462a954005b67c1905a0a3a2a34e7501", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.7.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8a1d008928dc72da55c436ace13be2545ce2b2ea", "size": 13256, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.7.0.tgz", "integrity": "sha512-6Hz3dWjCBzDG85WYgjGe3btPlheoZC54jYfZLWaSnFn9C9h/EpYEUI2D9apDQbHVB82CSNlSZFLWwH2p9zShVQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.7.0_1530500372597_0.8904200596627885"}, "_hasShrinkwrap": false, "publish_time": 1530500372662, "_cnpm_publish_time": 1530500372662, "_cnpmcore_publish_time": "2021-12-15T14:59:38.356Z"}, "1.6.10": {"name": "dayjs", "version": "1.6.10", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "631e9a897e7fcf0a8a3bd7ce814544931bf1b45b", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.10", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8725191b9a7a5f8c2bf42d0cb6e8df1a041d3274", "size": 12716, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.10.tgz", "integrity": "sha512-Y4kGgxq1Or7uocVKk6BrhvhbLZC3Sbqi/KxXA+kGoTbicO/7OxNOBmFbI4z3U3ACY5nt6uiWlg5SvhGkt748HA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.10_1529938649987_0.6981854692813403"}, "_hasShrinkwrap": false, "publish_time": 1529938650045, "_cnpm_publish_time": 1529938650045, "_cnpmcore_publish_time": "2021-12-15T14:59:38.542Z"}, "1.6.9": {"name": "dayjs", "version": "1.6.9", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "254e6ba823bb0d16bdec3d6c046cadb9d6ceaa79", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.9", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "b3568220ac9d71fc295392aaaa5cf9e0e00232f2", "size": 12468, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.9.tgz", "integrity": "sha512-6e9R/dKeERbUuiRoEIcEBTFSnCDHAq7Hzw99fPZMJbee2ZlqDCpvrE9MWWW6QN6nZ2W+Dfa7el9HkeJk7ui9TQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.9_1528969579164_0.2657658475595208"}, "_hasShrinkwrap": false, "publish_time": 1528969579218, "_cnpm_publish_time": 1528969579218, "_cnpmcore_publish_time": "2021-12-15T14:59:38.768Z"}, "1.6.8": {"name": "dayjs", "version": "1.6.8", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "c2a487ca9ad7b96beee1bce1ed098770c9610776", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.8", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "77de7a1694e42c9ae16bd5539080dac4856e9fb1", "size": 12410, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.8.tgz", "integrity": "sha512-8avZ9oVL5C0EIu16Sao0h0NNFgLayhoy56vGeiSwAqzz+fB6fkSl1xx6E3EGTwUn8UJVT/+/p15jt0FmXc66qw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.8_1528944673907_0.4734316026785297"}, "_hasShrinkwrap": false, "publish_time": 1528944673974, "_cnpm_publish_time": 1528944673974, "_cnpmcore_publish_time": "2021-12-15T14:59:39.279Z"}, "1.6.7": {"name": "dayjs", "version": "1.6.7", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "043ea4ae03671e863e3511b12e9927ec5926effe", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.7", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "efebec11e979d83c21ebe575686c00a120d03bf7", "size": 12281, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.7.tgz", "integrity": "sha512-VgwKfKFxtx9WLBLYirrb8w3ngVy4YyAMlwTMrd8f5DGG74ZwuysEMwWeW14/kg65AsS9DZZKQvmdKsNI8k8tEg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.7_1528699205433_0.22881385000047927"}, "_hasShrinkwrap": false, "publish_time": 1528699205532, "_cnpm_publish_time": 1528699205532, "_cnpmcore_publish_time": "2021-12-15T14:59:39.491Z"}, "1.6.6": {"name": "dayjs", "version": "1.6.6", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "73495cb6c5670e35532a508547b122a23f003132", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.6", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "2bb0d2e14f56301d364339140afcac6fb7e6e0e2", "size": 11912, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.6.tgz", "integrity": "sha512-EyShuK6rO8pIkOvvpplNRBEkEU37+VmdQiOdQJjq9kjERtfU3zvlL+RDPcfHTcsTi01DLl9q9MUp7dg/GCK2/A=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.6_1528271738725_0.05930990898727995"}, "_hasShrinkwrap": false, "publish_time": 1528271738804, "_cnpm_publish_time": 1528271738804, "_cnpmcore_publish_time": "2021-12-15T14:59:39.800Z"}, "1.6.5": {"name": "dayjs", "version": "1.6.5", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "113cc2e613199461b5c2d9028b8fbc5604fa148a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.5", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "d7c18785990f6d839161b84f4cc924d9324dfa33", "size": 9967, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.5.tgz", "integrity": "sha512-XhLOghtKxv3YoITTDDHj1r3vjGK5jYWiNqrOIcHg/YE5Ec4qjz4TisRgP/7LwwJTcLNzdexcmUlP/+/hFEC1hA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.5_1527758737828_0.9467469642297792"}, "_hasShrinkwrap": false, "publish_time": 1527758737946, "_cnpm_publish_time": 1527758737946, "_cnpmcore_publish_time": "2021-12-15T14:59:40.048Z"}, "1.6.4": {"name": "dayjs", "version": "1.6.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit", "postpublish": "npm run test:sauce"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "dependencies": {}, "gitHead": "8d62dec5461263f8e6700271d05a32208c8ed4bc", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.4", "_npmVersion": "5.6.0", "_nodeVersion": "10.2.1", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "7e66f0a43344dfd7bada2e8f7113813dd9d0d08f", "size": 9687, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.4.tgz", "integrity": "sha512-qjcjjphSHFURrk1yS7Y6l/ArXSEo7uIAayLYwWhODroEGd4I3oZBmycPvM7za+m1NVPIukTVSxl1CHJ8kCdyIQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.4_1527231818832_0.9335822456315894"}, "_hasShrinkwrap": false, "publish_time": 1527231818923, "_cnpm_publish_time": 1527231818923, "_cnpmcore_publish_time": "2021-12-15T14:59:40.308Z"}, "1.6.3": {"name": "dayjs", "version": "1.6.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "BABEL_ENV=build node build", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size dayjs.min.js", "postpublish": "npm run test:sauce"}, "pre-commit": ["lint"], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "typescript": "^2.8.3"}, "gitHead": "c0ea9877474e2b03fc64d9507e3dc646c0de780a", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.3", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "5397bfc7981d0028b5c4b5d0deff350be835885f", "size": 8317, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.3.tgz", "integrity": "sha512-hHl+9R+DQsIRxjReedI3JGo3zkRNxlD3B1qqEKgwwSgnCYX0k3C/tgbq4uTZp5OdIAch6GE9z2Uw2QWOkbSnwQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.3_1526871291520_0.9786127415011527"}, "_hasShrinkwrap": false, "publish_time": 1526871291631, "_cnpm_publish_time": 1526871291631, "_cnpmcore_publish_time": "2021-12-15T14:59:40.577Z"}, "1.6.2": {"name": "dayjs", "version": "1.6.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "BABEL_ENV=build node build", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size dayjs.min.js", "postpublish": "npm run test:sauce"}, "pre-commit": ["lint"], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "typescript": "^2.8.3"}, "gitHead": "86ebcdf84e38cfaeff81944cc734cfd49378646f", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://github.com/iamkun/dayjs#readme", "_id": "dayjs@1.6.2", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e1fb7ca8a700c3ceec783589968a9a0c96352cf4", "size": 8496, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.2.tgz", "integrity": "sha512-2yrkr0eF8W7Bwfwd4rwZ7MKUEhxQ2cG7/Awo++ciZrVtWHgF2E3A+dyonVZ/Xk3C8VgtkZjeKzuYz6muWx5nuw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.2_1526636434798_0.6991458165469906"}, "_hasShrinkwrap": false, "publish_time": 1526636434966, "_cnpm_publish_time": 1526636434966, "_cnpmcore_publish_time": "2021-12-15T14:59:40.792Z"}, "1.6.1": {"name": "dayjs", "version": "1.6.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "BABEL_ENV=build node build", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size dayjs.min.js", "postpublish": "npm run test:sauce"}, "pre-commit": ["lint"], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0", "typescript": "^2.8.3"}, "gitHead": "7bcd6302349bf5ff3527245fcfe5c3aefff54ba7", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.6.1", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "d829bb62a6ad8a6e4c8f11fe64ac84855a3308d6", "size": 15219, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.1.tgz", "integrity": "sha512-fWI6IR0GZc5Os7YE7tzJmntk9Zr+l6yyMVVOrzOM/vmqtTsyPGAhPWh5hyau9wOlTzg9rW1nTkvHPg8zu+6QPQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.1_1526630881663_0.7013514930557743"}, "publish_time": 1526630881715, "_hasShrinkwrap": false, "_cnpm_publish_time": 1526630881715, "_cnpmcore_publish_time": "2021-12-15T14:59:41.089Z"}, "1.6.0": {"name": "dayjs", "version": "1.6.0", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/* build/*", "build": "BABEL_ENV=build node build", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, "@semantic-release/git"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "4dcdf8f9f77b47a508d6d79fd4a9777c654485d4", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.6.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "3360492f1643f0b52cc66189fa1f23f51e6ecc22", "size": 13226, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.6.0.tgz", "integrity": "sha512-UjZVqAse1kfwCgILQO+4ef6bUrJyiaiFEWu5sdNIHibQN87ZjZguTTVlxrCpD0JtEMjS2AiMyg2tKpt7Tga6wA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.6.0_1526372370343_0.5415363100623876"}, "_hasShrinkwrap": false, "publish_time": 1526372370488, "_cnpm_publish_time": 1526372370488, "_cnpmcore_publish_time": "2021-12-15T14:59:41.329Z"}, "1.5.24": {"name": "dayjs", "version": "1.5.24", "description": "2KB immutable date library alternative to Moment.js with the same modern API ", "main": "dist/dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size ./dist/*.min.js"}, "pre-commit": ["lint"], "jest": {"testRegex": "test/.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "e00b9432951b434331c0e04309cf4e59d8cbce71", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.24", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "840e8dce359a552beacc0ba38fb819153929833b", "size": 17866, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.24.tgz", "integrity": "sha512-XkasRNJN1GxVUovqt7Eypj9tWdIOwy286HCzDLHn4KGhPfEqHWWdKy3c0pjWCIf6Xf89h0NLvKQysMyyA2i8Lw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.24_1525931011939_0.25065775916101574"}, "_hasShrinkwrap": false, "publish_time": 1525931012001, "_cnpm_publish_time": 1525931012001, "_cnpmcore_publish_time": "2021-12-15T14:59:41.850Z"}, "1.5.23": {"name": "dayjs", "version": "1.5.23", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size ./dist/*.min.js"}, "pre-commit": ["lint"], "jest": {"testRegex": "test/.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "aad2fc7314f4658be291d33594cc240c6c2662c2", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.23", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "3a0f6cf873d62dbb8ffb5178c7d72919371a6b28", "size": 17753, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.23.tgz", "integrity": "sha512-XYEY+j6luRXQ6CZS8X83000LonzPLO5gdVBCtIfS7YV5EKWaAGJVnPhEPxWsSO3R/k+YNS7VD1555QrQWQTVZg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.23_1525829090812_0.7007007653492565"}, "_hasShrinkwrap": false, "publish_time": 1525829090895, "_cnpm_publish_time": 1525829090895, "_cnpmcore_publish_time": "2021-12-15T14:59:42.148Z"}, "1.5.22": {"name": "dayjs", "version": "1.5.22", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size ./dist/*.min.js"}, "pre-commit": ["lint"], "jest": {"testRegex": "test/.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "986f11741720eca1325c2d08da9a44218990ae40", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.22", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "fd705e5d9bf733aab7933bfdfecddb3d60931676", "size": 17758, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.22.tgz", "integrity": "sha512-hHLL+up2RFxz/nThS4XRfZJQZDLqXtkIhDK1E9pZ1aVFpkWpSyxm3jPw6le/uihb3HSHY/BgDhph0QkitaFYAQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.22_1525747892234_0.9013882926007428"}, "_hasShrinkwrap": false, "publish_time": 1525747892317, "_cnpm_publish_time": 1525747892317, "_cnpmcore_publish_time": "2021-12-15T14:59:42.361Z"}, "1.5.21": {"name": "dayjs", "version": "1.5.21", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "gzip-size": "gzip-size ./dist/*.min.js"}, "pre-commit": ["lint"], "jest": {"testRegex": "test/.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "6e299cc413a0a8e47cbe6b5d14299f75ddec81d6", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.21", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8e70668459cec6762bb27427c37e02704379d7cc", "size": 17705, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.21.tgz", "integrity": "sha512-VO/Jhh4BZvGsQMwNPwsDvAhgVOoe4X3qYZTWeDmXIKvxX5AoPeNMq6vdBt3wp8mWxCSjjHbGFAKf/a6fIDRQlg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.21_1525709971988_0.11301233992493853"}, "_hasShrinkwrap": false, "publish_time": 1525709972071, "_cnpm_publish_time": 1525709972071, "_cnpmcore_publish_time": "2021-12-15T14:59:42.632Z"}, "1.5.20": {"name": "dayjs", "version": "1.5.20", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2", "gzip-size": "gzip-size ./dist/*.min.js"}, "pre-commit": ["lint"], "jest": {"testRegex": "test/.*test.js$", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "52d19b4d02e23701c552607c5f07131e676323b6", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.20", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e554bb17d6b066250ea98fca141a05f0fd223924", "size": 16828, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.20.tgz", "integrity": "sha512-lca1s14h/9dmcXdPffkEZ1KjrQUmqiUEF7D6wUG7CgSV3TBrgia8zApPwp1hn/xCFAlwAj1vaCQr6C7564ZIDA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.20_1525658054563_0.8584112543551823"}, "_hasShrinkwrap": false, "publish_time": 1525658054687, "_cnpm_publish_time": 1525658054687, "_cnpmcore_publish_time": "2021-12-15T14:59:42.917Z"}, "1.5.19": {"name": "dayjs", "version": "1.5.19", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "c25bcafe0b77affb8aa69979b263d75b4fdb2042", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.19", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "adf878e774e42a787ae813d864d43a6cc574d84d", "size": 15611, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.19.tgz", "integrity": "sha512-GcbO6kd+SE+eqNunCK5KEK+wQkbFBmNy9yiPpo1eF0OOcmB7Hj54FIYrACoL/lL+c3qDEOnu48OrmZbW1RhXnQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.19_1525396848476_0.21099586593720843"}, "_hasShrinkwrap": false, "publish_time": 1525396848643, "_cnpm_publish_time": 1525396848643, "_cnpmcore_publish_time": "2021-12-15T14:59:43.256Z"}, "1.5.18": {"name": "dayjs", "version": "1.5.18", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "18c19bdd1d360021475297ad700898c6c200cb58", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.18", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e2aa2d8c14112d5da8b5fb9d1e342c3f607ca730", "size": 15567, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.18.tgz", "integrity": "sha512-9j801glCFvCHlMv5CU0PG2wMPsbTUn0S2jy8b1C79ZJPhlonlw2egiBgvKzlPdJFeaNbR1GlmGi3eyd4isL7gw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.18_1525349963625_0.5491062601933494"}, "_hasShrinkwrap": false, "publish_time": 1525349963679, "_cnpm_publish_time": 1525349963679, "_cnpmcore_publish_time": "2021-12-15T14:59:43.456Z"}, "1.5.17": {"name": "dayjs", "version": "1.5.17", "description": "English | [简体中文](./README.zh-CN.md)", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "faa04307148adf07ba4977ac734e32b68be29cfb", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.17", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "3d18026ec39464047f0265909f74ded11a470468", "size": 15448, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.17.tgz", "integrity": "sha512-0Dc5KNwaPJL4suqnJLpbUnsmTY9/4mWUpqj+ZoMcG/qxNOLexgfHxQB4VVbGJfHeLT+hBSDSOykoBKzuc+00cQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.17_1525327542893_0.5240024173129678"}, "_hasShrinkwrap": false, "publish_time": 1525327543332, "_cnpm_publish_time": 1525327543332, "_cnpmcore_publish_time": "2021-12-15T14:59:43.665Z"}, "1.5.16": {"name": "dayjs", "version": "1.5.16", "description": "English | [简体中文](./ReadMe.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "c4b64ebbfb261e99a3a6694227ca284982779b2e", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.16", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "f500ba23a573bca7b15b8e8e35c9c548bb527af7", "size": 14757, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.16.tgz", "integrity": "sha512-4OlCDalNpwC73b34EH8vGlrOMQafaqWsRiK8XDnSfngMFlNNi0KNOSuNWaQU2UJOEzMQQ09KdGTR0XbVlYDB/w=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.16_1524905723085_0.0682521512904215"}, "_hasShrinkwrap": false, "publish_time": 1524905723170, "_cnpm_publish_time": 1524905723170, "_cnpmcore_publish_time": "2021-12-15T14:59:43.876Z"}, "1.5.15": {"name": "dayjs", "version": "1.5.15", "description": "English | [简体中文](./ReadMe.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "0019982315bd4670730c55e23e82b665b4defc84", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.15", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "2395ec7e522dec0af12e6c55286121bb7649e2a4", "size": 14262, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.15.tgz", "integrity": "sha512-tFzqM7D4JDGjXPLZ/oJZ/EiEQgCmQNOllRNiYAJ9MMfIiDIjp1Vtyj3KDGelGF1o3mwmJhK16tNrDaMopE74Kw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.15_1524762105827_0.3331544788843739"}, "_hasShrinkwrap": false, "publish_time": 1524762105899, "_cnpm_publish_time": 1524762105899, "_cnpmcore_publish_time": "2021-12-15T14:59:44.144Z"}, "1.5.14": {"name": "dayjs", "version": "1.5.14", "description": "English | [简体中文](./ReadMe.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "ec1686e88fb72407a5c6c96e64a0fec97afb4ae6", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.14", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "3a5e5588f22031af8b81603e55f1aee961e3038c", "size": 14063, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.14.tgz", "integrity": "sha512-kLZ07qL6ICiGZtv4ZefB2mE3Z5fzLC0d01cnqcUOurxhGWXFGvz17QLQtWVePc1kxjAlfgRa6lfGdQzmjgqjOQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.14_1524707256386_0.4269117034449912"}, "_hasShrinkwrap": false, "publish_time": 1524707256554, "_cnpm_publish_time": 1524707256554, "_cnpmcore_publish_time": "2021-12-15T14:59:44.360Z"}, "1.5.13": {"name": "dayjs", "version": "1.5.13", "description": "English | [简体中文](./ReadMe.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "dbe15c29c51330d406445c6698cc408f2fbe1105", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.13", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "85c2fb01b5a5e91bb7dfa04ed6bad62bcd49286a", "size": 14053, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.13.tgz", "integrity": "sha512-N0wwReo4Cu+7OCz0hRp7Ct7cbnOmhUl6zokNbYgheJkQ/vdbsv56yrSsS0Jw3uF/VebBXqEEBqQPZET5LjusrQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.13_1524675372083_0.2596680615675204"}, "_hasShrinkwrap": false, "publish_time": 1524675372315, "_cnpm_publish_time": 1524675372315, "_cnpmcore_publish_time": "2021-12-15T14:59:44.641Z"}, "1.5.12": {"name": "dayjs", "version": "1.5.12", "description": "English | [简体中文](./ReadMe.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "4abede48bf7d647f8fca3e7751937e93c5215426", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.12", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "da8029fe995101d80f91d5762445fb25a4b4a2eb", "size": 13887, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.12.tgz", "integrity": "sha512-KmeYfS1TWGCvy48CzRIiwyKl7j800+VQfbp28RE19i7Z1lJd2TBlIuFlNjZC8XudA4dBww2fxybx7f5p9Cn2NA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.12_1524671214674_0.8848603491759881"}, "_hasShrinkwrap": false, "publish_time": 1524671214775, "_cnpm_publish_time": 1524671214775, "_cnpmcore_publish_time": "2021-12-15T14:59:44.872Z"}, "1.5.11": {"name": "dayjs", "version": "1.5.11", "description": "English | [简体中文](./ReadMe.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "1e01097b353e123eb3ce6dbb53ff6ec9ef0b604a", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.11", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "a546d562b140df1588b26c7880f209201c7c350b", "size": 13095, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.11.tgz", "integrity": "sha512-gtaLI8cVCBDlN/2Q7XKKVk5VW7Scw2IoeHsHYSMwJ96RGDt3GbgKfj5JnfoLWwGVwpt3ZHwpvfR1lqS0/zZvDw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.11_1524548206625_0.05084767498726328"}, "_hasShrinkwrap": false, "publish_time": 1524548206749, "_cnpm_publish_time": 1524548206749, "_cnpmcore_publish_time": "2021-12-15T14:59:45.087Z"}, "1.5.10": {"name": "dayjs", "version": "1.5.10", "description": "English | [简体中文](./README.zh-CN.md) <p align=\"center\"><a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"550\"                                                                              src=\"https://user-images.githubusercontent.com/176808", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "9734fd8a24b9be1a6824e4844797402246b44875", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.10", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "5b187904523093c540d50c4c759c46355065cf52", "size": 10938, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.10.tgz", "integrity": "sha512-tb4QOzRcQowEU/VFjqaJSx/cfT5NPDN7FM7nie+8K730QDidplYPln0pwE5Tv2HV32Ci0mGx8XgIoRjmsYSyDA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.10_1524465376809_0.892579347820476"}, "_hasShrinkwrap": false, "publish_time": 1524465376951, "_cnpm_publish_time": 1524465376951, "_cnpmcore_publish_time": "2021-12-15T14:59:45.321Z"}, "1.5.9": {"name": "dayjs", "version": "1.5.9", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/dayjs.min.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "50323384cdb64adb9e14748244e2621e38bfd747", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.9", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "929761e73b639e222fdb84115389b8dbdb89aeac", "size": 8241, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.9.tgz", "integrity": "sha512-MqOBY32IXAxAZ5tnpctkTeS0Gt5Bh7RfF9rN91wpP9JUynYXYhRSamGKKnXwETWCKs75I2ExYX1PAtz0ImTILw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.9_1524309792469_0.8735328978832448"}, "_hasShrinkwrap": false, "publish_time": 1524309792857, "_cnpm_publish_time": 1524309792857, "_cnpmcore_publish_time": "2021-12-15T14:59:45.554Z"}, "1.5.8": {"name": "dayjs", "version": "1.5.8", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "6de2c3d198742c17fa1ec45dab8aeb14ae9a61a4", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.8", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e4bceba025a8d9247db2c94f11614b761155097e", "size": 8243, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.8.tgz", "integrity": "sha512-BPDl3WZ99DhoYn/XrytDh/7mZwBiprgpsFCN/J8bXTibah/ibPPKNI25oSLEFT7KvKDEUGDgF4vSZrYLDmL1aw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.8_1524240540570_0.643565514441566"}, "_hasShrinkwrap": false, "publish_time": 1524240540670, "_cnpm_publish_time": 1524240540670, "_cnpmcore_publish_time": "2021-12-15T14:59:45.811Z"}, "1.5.6": {"name": "dayjs", "version": "1.5.6", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "aaab0c6da4c908b55b675cad14ef8db023486a25", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.6", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "acec67d9b74528a76dfba88ead495ecac43d7a7e", "size": 8247, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.6.tgz", "integrity": "sha512-lN9lXu0YjF3M4HV1+dAKhX1cTaDPwHreGA7sgvnsOfUeTLQBWgNKZo7xjJOESoWsl5aHxeJI0IrVU+4mt7jltA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.6_1524209137012_0.1624625419885668"}, "_hasShrinkwrap": false, "publish_time": 1524209137088, "_cnpm_publish_time": 1524209137088, "_cnpmcore_publish_time": "2021-12-15T14:59:46.041Z"}, "1.5.5": {"name": "dayjs", "version": "1.5.5", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "c1beb5ff5b96fd8addab6e4d1b680e44b88d4068", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.5", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "4cf5703994202bcad1ac958cdd7b1c9832505251", "size": 7545, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.5.tgz", "integrity": "sha512-MjDv/f2J0XEr7Hu7YUz2hyzkv0szEPOvU1L3gjN7v1BnsCR+zwwJcr72av+GmLTGEgcMXwVETPrLuoqF/mWPNw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.5_1524129324353_0.9252298671251225"}, "_hasShrinkwrap": false, "publish_time": 1524129324507, "_cnpm_publish_time": 1524129324507, "_cnpmcore_publish_time": "2021-12-15T14:59:46.278Z"}, "1.5.4": {"name": "dayjs", "version": "1.5.4", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "cfb0c034654c6d77ce54b622b66f8f093e9ad052", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "873eb0e4c3e5c34eedb06d6a81c57ccc4c523621", "size": 6128, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.4.tgz", "integrity": "sha512-ELF7zfX6IpIM8CgaEqduztNdoFutfTRSD+TLAgtt+UPNK2EHNbEpdCeBF98O3IbSeQDonYM8ZeAAyoaEAZZ5dw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.4_1524104321633_0.3745824091409533"}, "_hasShrinkwrap": false, "publish_time": 1524104321722, "_cnpm_publish_time": 1524104321722, "_cnpmcore_publish_time": "2021-12-15T14:59:46.480Z"}, "1.5.3": {"name": "dayjs", "version": "1.5.3", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "BABEL_ENV=build rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "16661f86f31afe7df028c08e15a116c0cac5dadd", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "5dd257491cf7a87a15d31e5ad6e2efefd7b74eac", "size": 5967, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.3.tgz", "integrity": "sha512-mcuq6HzZCXSlnYJBjWUVkvBZ0nfkgFhpC4AF6YxOKQAVo+JJQ+GfvZzI7uB+RY6aUy6OG9DTjQmBKaq+e4TI4A=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.3_1523804075461_0.44746560767984245"}, "_hasShrinkwrap": false, "publish_time": 1523804075539, "_cnpm_publish_time": 1523804075539, "_cnpmcore_publish_time": "2021-12-15T14:59:46.671Z"}, "1.5.2": {"name": "dayjs", "version": "1.5.2", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*"]}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "7a74d2e39c7c57309b4f33bb5413de3a2c1a9634", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "4c70c71ae3c55e03fd4227563188845f00366635", "size": 5674, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.2.tgz", "integrity": "sha512-dSMQZsnKgvfC7TJFvHu9Pf83mlmCEvP8bFKfhN9URdbHM94Czn8KFVKjke7cAgkS8okZmJykP0mkBUKJB2Z3tg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.2_1523799074905_0.982548212284122"}, "_hasShrinkwrap": false, "publish_time": 1523799075005, "_cnpm_publish_time": 1523799075005, "_cnpmcore_publish_time": "2021-12-15T14:59:46.894Z"}, "1.5.1": {"name": "dayjs", "version": "1.5.1", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/*", "!src/cloneDeep.js"]}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "dependencies": {}, "gitHead": "405c952081faf301db863664e3ab0ace00dc2b7b", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "e80f92c2e58b5f3e915fd0e925c1fef0d50c486b", "size": 18612, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.1.tgz", "integrity": "sha512-1Pr8S1h5P4xoLu8B1KWzuwDFMWmbnUJWEyMBDMSwBX53KdfGif5rtF5SB6z4eNCxXlCinmMAxmg38/6M3tI7pQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.1_1523615230394_0.6914256833479702"}, "_hasShrinkwrap": false, "publish_time": 1523615230507, "_cnpm_publish_time": 1523615230507, "_cnpmcore_publish_time": "2021-12-15T14:59:47.135Z"}, "1.5.0": {"name": "dayjs", "version": "1.5.0", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "mockdate": "^2.0.2", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "680d30225c2ca0fc2ff3b296d074983e1b3ab1aa", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "2809159d92d26dc0375a5a4cab3f2b1b3cabea14", "size": 5416, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.5.0.tgz", "integrity": "sha512-QGC+O1+MZXlWK0YXdWh5T6ygT5ZDeW2dV/T8UhKIhRlCnhXMq+vkB/aOb+9iugHAzOQAWKfQegS/NWLLmGrrzA=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.5.0_1523606970769_0.333243379377703"}, "_hasShrinkwrap": false, "publish_time": 1523606970854, "_cnpm_publish_time": 1523606970854, "_cnpmcore_publish_time": "2021-12-15T14:59:47.381Z"}, "1.4.3": {"name": "dayjs", "version": "1.4.3", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "74b0bc12eb82abf7a1691a34545bd90b4c1f0b31", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.4.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "78f4943cf43542bc296802ba21cde7f92fc59467", "size": 4865, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.4.3.tgz", "integrity": "sha512-q18eNa1kFVSR+dur+eN/KpLc2zVxBPZx/5FtBusIeo9NosLJe4tjiS/rsujlYoRPgKucWBmA0iEy/m19UtGv4w=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.4.3_1523590764114_0.4444510892217368"}, "_hasShrinkwrap": false, "publish_time": 1523590764210, "_cnpm_publish_time": 1523590764210, "_cnpmcore_publish_time": "2021-12-15T14:59:47.600Z"}, "1.4.2": {"name": "dayjs", "version": "1.4.2", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "dist/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "f65194ac112e7ef1d44c6c5d9dc4c2d5b0e84f73", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.4.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "4978d7c948e2af771e72e3937d1c93670fb4896f", "size": 4829, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.4.2.tgz", "integrity": "sha512-rIEOqEMoRIv+6um7vp4aibus2t6JY3mkW990pxwv3F0/BEv2akZht98BA71W1KJT+xIGoUteaHUl1PHftWQibg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.4.2_1523547818032_0.6157845572179128"}, "_hasShrinkwrap": false, "publish_time": 1523547818104, "_cnpm_publish_time": 1523547818104, "_cnpmcore_publish_time": "2021-12-15T14:59:47.853Z"}, "1.4.1": {"name": "dayjs", "version": "1.4.1", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "src/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*", "build": "rollup -c"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "moment": "^2.22.0", "pre-commit": "^1.2.2", "rollup": "^0.57.1", "rollup-plugin-babel": "^4.0.0-beta.4", "rollup-plugin-uglify": "^3.0.0"}, "gitHead": "fb1fc271cebab97fb6b9c1cf9c5bdf8f078a44cd", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "8509a5a3aef25a8b45ee51fa99efe474f57360d4", "size": 4825, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.4.1.tgz", "integrity": "sha512-UdT4X9ih4WplDW0nzQZvRbs9K5cqLqAGgmDnu1wzemWLQAb1r8E3CH9LqePAS6wFjXHQlou9EgV4kgTTkCKymg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.4.1_1523547333614_0.2518904420359873"}, "_hasShrinkwrap": false, "publish_time": 1523547333686, "_cnpm_publish_time": 1523547333686, "_cnpmcore_publish_time": "2021-12-15T14:59:48.302Z"}, "1.4.0": {"name": "dayjs", "version": "1.4.0", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "src/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "moment": "^2.22.0", "pre-commit": "^1.2.2"}, "gitHead": "881f6b61953992a56b20cbdc3544bd30e8e5cabb", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "6d74461d9054bf4b3be41fdf3a357e21c4e6a10c", "size": 3795, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.4.0.tgz", "integrity": "sha512-uzg40nSzaQhP4yXnpAtHtae0KVG9dHrkasUMFFQcZk1arkRNaJjrU+Cg5XxGs4wNPDMchkHkZDrztCys91lZ7g=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.4.0_1523524179567_0.026841007871684752"}, "_hasShrinkwrap": false, "publish_time": 1523524179708, "_cnpm_publish_time": 1523524179708, "_cnpmcore_publish_time": "2021-12-15T14:59:48.502Z"}, "1.3.0": {"name": "dayjs", "version": "1.3.0", "description": "[![NPM version](https://img.shields.io/npm/v/dayjs.svg?style=flat-square)](https://www.npmjs.com/package/dayjs) [![Build Status](https://img.shields.io/travis/xx45/dayjs/master.svg?style=flat-square)](https://travis-ci.org/xx45/dayjs) [![Codecov](https://", "main": "src/index.js", "scripts": {"test": "jest", "lint": "echo start lint && ./node_modules/.bin/eslint src/* test/*"}, "pre-commit": ["lint"], "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "jest": "^22.4.3", "moment": "^2.22.0", "pre-commit": "^1.2.2"}, "gitHead": "9fc5a8d75409abfb9a3063dd4ac9ff42b700422f", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "ab445e6b5e07d7059d98c1b704da79d831f8f1f9", "size": 2531, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.3.0.tgz", "integrity": "sha512-y9a76boZ8ypb5gBYN90IXsp3LAuetxeIzNSnxrona/Wx1SAwPE80Ojm4CaNKuUYbVxeTTToJBJ6ABrhgHW0IpQ=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.3.0_1523442652172_0.8348948103697769"}, "_hasShrinkwrap": false, "publish_time": 1523442652236, "_cnpm_publish_time": 1523442652236, "_cnpmcore_publish_time": "2021-12-15T14:59:48.726Z"}, "1.2.0": {"name": "dayjs", "version": "1.2.0", "description": "[![Build Status](https://travis-ci.org/xx45/dayjs.svg?branch=master)](https://travis-ci.org/xx45/dayjs)", "main": "src/index.js", "scripts": {"test": "jest", "lint": "eslint src/*"}, "pre-commit": ["lint"], "author": "", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "jest": "^22.4.3", "moment": "^2.22.0", "pre-commit": "^1.2.2"}, "gitHead": "0eaf0706f2bb60853599f90173f31192840e7d2b", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "ee03c6ac9fbeee8257e27ccc41a7b25cbeef1390", "size": 2012, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.2.0.tgz", "integrity": "sha512-IOaE6zy2JjQx5ou0JqbD5TP3UXbjlFA7Ect9CwuBQ7AeaDvdpsfUUhNd0rUWGefKwmc0O7s5WPuHvIt8r1Yt4w=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.2.0_1523435478186_0.7895305392025898"}, "_hasShrinkwrap": false, "publish_time": 1523435478316, "_cnpm_publish_time": 1523435478316, "_cnpmcore_publish_time": "2021-12-15T14:59:48.969Z"}, "1.1.0": {"name": "dayjs", "version": "1.1.0", "description": "", "main": "index.js", "scripts": {"test": "echo ok"}, "author": "", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {}, "gitHead": "2473d99bd48f51370487b7dff36c4e0433399537", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "133511c17774cb329142683ada6f1146bfca1a27", "size": 557, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.1.0.tgz", "integrity": "sha512-lkszMjCpEEZRCdGaAdscUszDUoqAVvEKFKmQLzvBqhesJsrxPWPJRnUuv0RA/bvaHiHVm3RjfbYUx2dI+xI8vg=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.1.0_1523415473560_0.8945485791198537"}, "_hasShrinkwrap": false, "publish_time": 1523415473659, "_cnpm_publish_time": 1523415473659, "_cnpmcore_publish_time": "2021-12-15T14:59:49.177Z"}, "1.0.1": {"name": "dayjs", "version": "1.0.1", "description": "", "main": "index.js", "scripts": {"test": "echo ok"}, "author": "", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/xx45/dayjs.git"}, "devDependencies": {}, "gitHead": "7b080dfb4193565a8bc948417bc0f476635df46e", "bugs": {"url": "https://github.com/xx45/dayjs/issues"}, "homepage": "https://github.com/xx45/dayjs#readme", "_id": "dayjs@1.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "fbab2a3a158fb79c51279b0115a28386c79f2f36", "size": 569, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.0.1.tgz", "integrity": "sha512-5vs/8M14Dypc6sf2Pq6s+Z8fEpJrgMYJMJ+r6XymGDx3xOFyqMSVBGvLMlCbDuYYaMuW2nStaXF4kbFZh0FzWw=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.0.1_1523415264492_0.9577933425603573"}, "_hasShrinkwrap": false, "publish_time": 1523415264606, "_cnpm_publish_time": 1523415264606, "_cnpmcore_publish_time": "2021-12-15T14:59:49.407Z"}, "1.0.0": {"name": "dayjs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "_id": "dayjs@1.0.0", "_shasum": "5c056ff9032647a6d570e8d3dbb65bf039fdab05", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "5c056ff9032647a6d570e8d3dbb65bf039fdab05", "size": 247, "noattachment": false, "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.0.0.tgz", "integrity": "sha512-Vd/01iVwNVDxDqs+6pEyNAC9pC9DiF3HNlW+C0Tlqo7zwZ2rh768aL2UZL9+rwPLlwNtDg3vUwkEzk7PXrMf2g=="}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.0.0_1523323888266_0.8185937185364711"}, "_hasShrinkwrap": false, "publish_time": 1523323888371, "_cnpm_publish_time": 1523323888371, "_cnpmcore_publish_time": "2021-12-15T14:59:49.615Z"}, "1.10.8": {"name": "dayjs", "version": "1.10.8", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "a9aa18ed3d4d7959fa5b8c1b048c21ff525f9296", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.10.8", "_nodeVersion": "14.19.0", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-wbNwDfBHHur9UOzNUjeKUOJ0fCb0a52Wx0xInmQ7Y8FstyajiV1NmK1e00cxsr9YrE9r7yAChE0VvpuY5Rnlow==", "shasum": "267df4bc6276fcb33c04a6735287e3f429abec41", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.8.tgz", "fileCount": 439, "unpackedSize": 623729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHMUxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLmxAAizHTfKpLV/1s9tPmNT60UBnPRFEuTuIxbxcg0upp1VxQgyS7\r\nPzT+eFbuj+EhPZrSEoSgyN93n+ZVhWQjob+A9ClcqgzP+3UW9pmULog5fK1/\r\nnzky1apGVye+FE4JyKniDSGQM/siiKtPVeZYMtzFCCPjHIWTOJ3amf66DOXU\r\nHQdf+HhzgufKhNCYie+5gvkYQ2DjX+bqL4h0M2OzFu7Fb8cP9hT9/7GO8ClK\r\n2HCarYK1gmUp89zM+OuTmzofqDV155qT3aouGgWUbgorDe/8p4pMWwbhojU/\r\nE1M2re0j1gY7NyLdVUIu+sbtlFIVOmFpRm8fSJ+qv2QOFRqGxcT5ks8yJfok\r\nbsTEnKxZVE23kzvj+5LuHBCH0x9NnyPpriGXfWBKAosEp5mrdzIYyvEMsMd6\r\nfwgEGH/GMPjOK+8oAwocuFH4cxwL2Z6OEZVajXhnB+D8DqU5t4H9EvOCemDB\r\nXyCpuqb/gfy9NGCO16CqnwRpkv7J/cLr1+pXQygOC0cHbp5H5yIsjpdqz/L8\r\nBGuCkGUkp4a7QFR7F9rmmnZyrkaayB7TWwGBaiRJ0yN2deWD60SX4s7TRErq\r\nZ+KAGdKQp9LPzGhPrAYEacwAWoFReLzHfJ7dDzoG2wH5SpZbbrmjL+v3+1B/\r\nlpDJIWbS2M+kH+upO2IIjejcHJuy8uQcg7A=\r\n=8Rzz\r\n-----END PGP SIGNATURE-----\r\n", "size": 135257}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.10.8_1646052657479_0.222705714088407"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-28T12:51:02.391Z"}, "1.11.0": {"name": "dayjs", "version": "1.11.0", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "4e3b4bf3a7bb055b5935b27fa90a6c6885b50457", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.0", "_nodeVersion": "14.19.0", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-JLC809s6Y948/FuCZPm5IX8rRhQwOiyMb2TfVVQEixG7P8Lm/gt5S7yoQZmC8x1UehI9Pb7sksEt4xx14m+7Ug==", "shasum": "009bf7ef2e2ea2d5db2e6583d2d39a4b5061e805", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.0.tgz", "fileCount": 441, "unpackedSize": 633639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3TmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDUg/7BzQvg6gT/Xtjt+tBVgx+lZL7ekpFOA1EN3tbLDPHHg7jQIee\r\n/KSY0X20QYMVg665jsx5YrurSTpNfGvHl6hFXrqCDhVIX7Im5xSyEHFU5A68\r\ndXXGuFGBz2Ad0OJ3mlhhWsGr2c8RuZVQOU9E0OlFiiCAs+tN8/LgdoNckVqK\r\nx+WADZCsf/gSYAxNVZ2qWIz2A5u2EA5bcnDUGVW3E/jIws7ukbKR8ynRTYsM\r\nTyUDjp1egDlPmbLC/JqDC8dzCzUBYKW9x9mXewdEe5Ien880xdhDRR2dX89t\r\nJAyHTfwXjmeEW6KkfwLgkPutI88oidpp7He4fcJaXiOIT8HkfP8vgyo3t//F\r\nXZ1qf/zMK4cGgQoruGqFw/bW28nyGwEEw42CN5T8rhk4S9061+TRAZIkaKFY\r\n8BIzAanibWlgOvFG9m6L+sUNEqU+sQdRrNAciln1vLcEAP2lBSgBYNbxJtRU\r\nkto4xXB6mEIApFFKtuXpAasXOeUUpFVIvwKHhnzgIahMzrWiI5Pzlor5+uvw\r\nPktCWR4vAJEwrj9vLdTK6RGWTZWciewPaSmVyDsZRXOfqaZwo9GIl6KBUqV6\r\njDRRyjOqm5dyINqeQSjZ5DtP3W3RFkrk7EBqzcK67CySAYGTogeIdp4XgebU\r\nnYxnxrNI6yW9SPBhYZQ6rWF7vGNNCsAGHXc=\r\n=Xfvj\r\n-----END PGP SIGNATURE-----\r\n", "size": 136922}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.0_1647277286534_0.9825820129380409"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-14T17:04:12.689Z"}, "1.11.1": {"name": "dayjs", "version": "1.11.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.27.0", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "4acbb7246887b5cf0923f5313f992b9478100cee", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.1", "_nodeVersion": "14.19.1", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-ER7EjqVAMkRRsxNCC5YqJ9d9VQYuWdGt7aiH2qA5R5wt8ZmWaP2dLUSIK6y/kVzLMlmh1Tvu5xUf4M/wdGJ5KA==", "shasum": "90b33a3dda3417258d48ad2771b415def6545eb0", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.1.tgz", "fileCount": 443, "unpackedSize": 642165, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCX7lBp4eVT763Ij75o7dbKZ/exEw8Ub6M3FqFfoGsENAIgMtM1nidsFgYsC6MmvNWApIvuL6Z0S8QSRizob3Bwcg4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWOXQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBtA/+JkQ3FPtjMIqKilInPpimOSqVJCm06fl5Lrpo9/ahiWmf+Ltt\r\nCAuEbMLTClnc1275PBeHymlKxUasECFy53lYacRIYhDPIOjB9E0z//T5/YFL\r\njsmAmYDBx71DJR8+nJ2912IuMd4fbAuLqa03hZC59j3FYWM6bx9V6wFggcsj\r\nkK0iB/wOimbb5wttJCT7/3IStnQvtSvuQJw2MQ6jvSTsbERvyBeYniKw+Vw7\r\nRKXSJ5M5aRf9o+xbmYQ/T9cDa1jIOxYbB3wc1p8vZgIQn1MvMkrhB/JMGbUy\r\nrd+qCPjNlkCDmMo9k8g95BdvAFUFKo4Br88sfGjeUDibKf7EVU2ecOFMiFus\r\nwLjRfixruk1vZ8sHvXHpk+wxg1RudFQ4V1m7tL4MK3ShWylbHiXjefg3SyRv\r\nXAVUebdTqpp5wZUIQyS41Ab4Pe8wPqRnoTr7UnBzb1W/nNOhwMHhornz+T9L\r\nvC5P3e9ezqMyngk4Zi0bfOIvphSe6hvVQvwLLcRjnm/c3BNkA/bO2/qaOodA\r\ng6i9px0Jp4pFa/8do0KLuiM9U2ukUtlu5PRIehsKbHou2Et52l+r62eHU2FJ\r\nslTaEpJCsm+EgbdjHayI9J8qs0yNTbPMtmBLDk8FL3cguh2+ra4mHqx5MJl9\r\nIFx8AqlvRpCWUOFIGEWX5lXaWOHD1mXhhog=\r\n=Kbn+\r\n-----END PGP SIGNATURE-----\r\n", "size": 138607}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.1_1649993168434_0.6564183451550836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-15T03:26:30.993Z"}, "2.0.0-alpha.1": {"name": "dayjs", "version": "2.0.0-alpha.1", "description": "2KB immutable date time library alternative to Moment.js with the same modern API.", "keywords": ["dayjs", "date"], "homepage": "https://day.js.org", "license": "MIT", "author": {"name": "i<PERSON><PERSON>n"}, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "funding": "https://opencollective.com/dayjs", "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs"}, "./*": {"require": "./dist/*.js", "import": "./dist/*.mjs"}}, "unpkg": "./dist/index.iife.min.js", "jsdelivr": "./dist/index.iife.min.js", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "esno build/index.ts", "size": "pnpm run build && size-limit", "lint": "eslint . --ext .js,.ts,.json", "lint:fix": "pnpm run lint -- --fix", "format": "prettier . --write", "test": "vitest"}, "devDependencies": {"@size-limit/file": "^7.0.8", "@sxzz/eslint-config-prettier": "^2.1.1", "@sxzz/eslint-config-ts": "^2.1.1", "@types/node": "*", "c8": "^7.11.2", "esbuild": "^0.14.36", "eslint": "^8.13.0", "eslint-define-config": "^1.3.0", "esno": "^0.14.1", "fast-glob": "^3.2.11", "moment": "^2.29.3", "prettier": "^2.6.2", "rimraf": "^3.0.2", "size-limit": "^7.0.8", "ts-morph": "^14.0.0", "typescript": "^4.6.3", "utility-types": "^3.10.0", "vitest": "^0.9.3"}, "engines": {"node": ">=14.17.0"}, "size-limit": [{"limit": "2.99 KB", "path": "dist/index.min.js"}, {"limit": "2.99 KB", "path": "dist/index.min.mjs"}, {"limit": "2.99 KB", "path": "dist/index.iife.min.js"}], "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "d1a9e60affe132bfaa975f643dd77d2659edbbec", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@2.0.0-alpha.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-GEduILVwuVMSRMv5L1RRP6pGLz5u1oKh7p/+NIJVI5qNQX0MlMwiTyAgrlFItGlTzMikj68ECQouZEqzCm0+xA==", "shasum": "d71be1f241d8aaceec1e5d188393335869593bf4", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-2.0.0-alpha.1.tgz", "fileCount": 40, "unpackedSize": 245594, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDRtZOApfgiQA+kzrNc+bdRTT/8wFcEDbTfLdaHm4R4NAiAgEGzGA4mFFlhB+F8ozWsG1z0tCtejXsCJU3d/+KvkGg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia9UUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjQA//QLAadgL1Bmcy0a9yKWQoTr2Za/9n81eAoU2vOPuAyIiFtC/J\r\n32pNrRkvfTdvnDzwsXFGx2Oh2OZaZx4S1QH19j7sbmJIwAQX4Dh/NB2Q1acX\r\ny46rv2yCFFdbKuO1I7aejkAqrrhZ4UqymLvUNP0xPXGAeRZhVw1SPTMv39yW\r\noZXUddg6bLhnMsf4idht3O2/+OL1EEmXhda7qOrl+G1BbehRflNLgdqtIVkL\r\nSzEaZBVOdmnJPUQqQ4dXfRSxobkWr4ez9qSAPgRlE03pLiZgxc1ePfDco3ys\r\n1OlPHAb04PerVAq0ozPEbbIEAbNQ42KFXqZ9yQ2CBlMNIutkPB0quRh1aAbp\r\nOsD0nzQIgRuIvX1OJ6ejKlDV92ZmG684rtmGjoYoo+5ohNAVw4hHdcABwLuu\r\nq6GqMFG/ZByqsrmsF5xAOh2wY+sriV6+ygzZXTGlPN+niU2uwRi9z/SDGU1r\r\nvjXlt8nzDJsKh1hUhdvKoyBcMLb2yJ/NqrHhg2eQ+v0K1jwN+oJhVzOt4eWt\r\nDhcsT4EOaQa5K4JqjTXFBixZnawok9pgP7Qo1Jxi6Q8VxiknwLdtuFkJdQPQ\r\n8A40mu50jjVVSJ5a3MxWmm4az85Mt/2BRTVkff3l1UtcaECXSEq5aPIBM0dA\r\nOUPsp8j8RM9ptT5TenvePBBjcjCr/myks9w=\r\n=BlVz\r\n-----END PGP SIGNATURE-----\r\n", "size": 70880}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_2.0.0-alpha.1_1651234067926_0.3240346821144704"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-29T12:18:47.198Z"}, "2.0.0-alpha.2": {"name": "dayjs", "version": "2.0.0-alpha.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API.", "keywords": ["dayjs", "date"], "homepage": "https://day.js.org", "license": "MIT", "author": {"name": "i<PERSON><PERSON>n"}, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "funding": "https://opencollective.com/dayjs", "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs"}, "./*": {"require": "./dist/*.js", "import": "./dist/*.mjs"}}, "unpkg": "./dist/index.iife.min.js", "jsdelivr": "./dist/index.iife.min.js", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "esno build/index.ts", "size": "pnpm run build && size-limit", "lint": "eslint . --ext .js,.ts,.json", "lint:fix": "pnpm run lint -- --fix", "format": "prettier . --write", "test": "vitest"}, "devDependencies": {"@size-limit/file": "^7.0.8", "@sxzz/eslint-config-prettier": "^2.1.1", "@sxzz/eslint-config-ts": "^2.1.1", "@types/node": "*", "c8": "^7.11.2", "esbuild": "^0.14.36", "eslint": "^8.13.0", "eslint-define-config": "^1.3.0", "esno": "^0.14.1", "fast-glob": "^3.2.11", "moment": "^2.29.3", "prettier": "^2.6.2", "rimraf": "^3.0.2", "size-limit": "^7.0.8", "ts-morph": "^14.0.0", "typescript": "^4.6.3", "utility-types": "^3.10.0", "vitest": "^0.9.3"}, "engines": {"node": ">=14.17.0"}, "size-limit": [{"limit": "2.99 KB", "path": "dist/index.min.js"}, {"limit": "2.99 KB", "path": "dist/index.min.mjs"}, {"limit": "2.99 KB", "path": "dist/index.iife.min.js"}], "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "feac057e623f4b138d2cb5140a7356f5448fd8e6", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@2.0.0-alpha.2", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-piZlTKTEwoMBbsxRJ77LDndaYQyyV2BmAJtrhNb1O1C19Uzc1A//bSJ4W3CuF7iChG+yuKay/B8t9WePSJENkQ==", "shasum": "2ba5d6001aaf58e0fd93a4dd6489bc1634f0a42d", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-2.0.0-alpha.2.tgz", "fileCount": 58, "unpackedSize": 231727, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIESS+oxZsHeONpYD/D1q5tRNf2349TwIdxdDn5PxJK9YAiEAqVfNLVTati3NFOHBodwsLzQfmRY1B2kBNZWszQqyss0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicUe7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmor8w//YHFmbIZCcFrsJVY4F2tYDDDWVkrK2nTMGaboqS3KBqQAB9EE\r\nb5raJR5eOlcldowp3uJohsC98vLiuMJYF/vpFbvWZvXCNMduodJEQS3ReFnR\r\nzR607BqYvUauyNMfveo7bTulfwSWoFKOGXblBACuZXrjp0Xz95sjLRIhEjyE\r\nagAqNwcUJqj435G1L6zQKlPHcYV1MnwzaLa38D/V6A4XCYsE7Fao0llOeg2K\r\nWNgOOVh3DwcHtR4DjovWpz5GUrDo96wr4+oXPhDP/Kp+TPGvMMJ4/7FHG8DJ\r\nvrtmV1EpBHOhXrkMxdl8VyqwDxLmAmDvngm9vJhV8qP7hzexAsrqJY/RPn8U\r\nbGhmknzwreMkxg2hJmBvQZvqynq9yYIQ1gGR1HB+GELBhjUsYd+JvIISuVYJ\r\nFsS25Z3euRySZXMIxaqXEswDEdk+Plq/516g3vwhaLLwSD9WnPmkLTCkQdnN\r\n8u2H4MUXePtmb5z88vQlcAgb1HqSgyOXDFgXqhDFsiNAvRSCbAjmiLnVMq2E\r\n/JHlY29ranPj9HpLKH98dS4ml5ISqt1KlH3t6/RXrGPDHO8l1OLB+zQAGbG3\r\nMP07kco4Aiot6IozK39zzXS4gZDUAQeZYgY1R96fwqdFsaksMBCgD6Be7jD5\r\npk0aQGjxIfWbEUzDkRiHjBya6KZoomjMz2Q=\r\n=uCfd\r\n-----END PGP SIGNATURE-----\r\n", "size": 35719}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_2.0.0-alpha.2_1651591098841_0.17466217776892745"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-03T15:18:56.059Z"}, "1.11.2": {"name": "dayjs", "version": "1.11.2", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "12ab114d8a6a1cf4cedded674b700606d88a39b6", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.2", "_nodeVersion": "14.19.2", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-F4LXf1OeU9hrSYRPTTj/6FbO4HTjPKXvEIC1P2kcnFurViINCVk3ZV0xAS3XVx9MkMsXbbqlK6hjseaYbgKEHw==", "shasum": "fa0f5223ef0d6724b3d8327134890cfe3d72fbe5", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.2.tgz", "fileCount": 443, "unpackedSize": 642940, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgMo5JTnpUb/nWVNsOnYhJolpY/zGvGzG4jxRANvJ6xgIgCpRDUSKJa8n+vRMpKbSUaKhvi2RnzW86ZxDLxTqHHsQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidUpzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBeA//fKA6vvEpQQLOW6BcpFGEPODt/0PR/5+Pu6TDOD1SnvAnyhoc\r\nyv47+1KFJ7zlFNsYDaRpnE77Q01V5vJj0y7YvnoPQjATyuCngBHsAjfytQhO\r\nit9MxRofEWrbtCz0lBZOKvs3ZXjnCxFHbmAdqMHr5zJdQyUgDoJBMCeTzpF9\r\nM7prZd7o40yY/mY+8jo4zMK8caJ5s6MizQ6xx9uxLXLGuIscOgHMyxkVCfTs\r\nTtJgP1F+g4W87ov+y7+6XZZmZIZOIWDaRjYj14368+DymaT2ZxnQzSgqu5Zp\r\nq10LCRD/WLgKUwYMBi8wpVlQDpbabq9R9RTcQmWIQ8kplZmRkN3CN9hA44fz\r\ndk6CemFOrm0ANDOBQZYvZqJyYRpYPomWLd2cIWszAOJOm82b5cszb3PWDcvG\r\noqyp+UmRpiu3V4U9acfHYxP5CIBqdb5Aw8R1WEdep8HLVAvVjxX8X7NpROIw\r\naSWic2cDo/ey6LtKmJT7f+JZzCfQHOUvt84F07K6CntGH/Pv03qT8bIR/aMT\r\nnPn/V/obd2FYVF7FGTsBSu7W5cqu1w6WMadRLdIBLeDyMmBlGqb7J9Xd+GoK\r\nEA6mmpOtqU2IW7+cABWLtcxBpMCirJX+z+LwvdqR3ZYYr6KuYJVnke/kiTDV\r\nvLdshMCS8r03atYH/9I3+tTJA1+fHNZS+Aw=\r\n=G/UP\r\n-----END PGP SIGNATURE-----\r\n", "size": 138817}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.2_1651853939325_0.536963759147006"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-06T16:43:48.708Z"}, "1.11.3": {"name": "dayjs", "version": "1.11.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "c0e8e6cddf671a30b0ad8130edfe607add3a4137", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.3", "_nodeVersion": "14.19.3", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-xxwlswWOlGhzgQ4TKzASQkUhqERI3egRNqgV4ScR8wlANA/A9tZ7miXa44vTTKEq5l7vWoL5G57bG3zA+Kow0A==", "shasum": "4754eb694a624057b9ad2224b67b15d552589258", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.3.tgz", "fileCount": 443, "unpackedSize": 643524, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmFJao3uminfIOt3gZFIcgVz6on19glfsi6y0l3VQjzQIgcA2yvyrAhNT9wkFtJYmJEcBQ4nTPG9lVqreylJo5Mlg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinWyzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorSw//f+W8Qngrm7KjaJ1JiYmvvf3qBH17o9OBiSzpU0F4TEvi9bbD\r\nooXYT22pgU2XE4Fit3x/U+6j4rlH6Kj5oJNXLdRjOSWCqXaKQ/IvX+P6Ku1w\r\nEAd2JX8LjONKFepT+Qi/yrjXBpEDZVulyuwRGl82bz7xihpIN1TKBFpxYdxS\r\nubRV8zBudwc47qyGVdRYL8vXihxhQDcYOE1cTAz6ryN6GvqUJxvsMwhoYxvg\r\nwJuJ9CxjK+ulQQe992AVUywSMpxSUux3tvCANxIzj9RFvkMXGzUkfsTvMMlS\r\nV+5MaACUSyeWdYi195sysW0UzaYK9PpSm54Xay6WzsQ7JKaorQb5U8NpklGf\r\n3XUufo+MQusM6qn9N0gGU6g3UXFVvu1YyqtLBNRiUOnDZJ3nCq0svOxbzmRb\r\nYzgD83pNA6o5pfczDur+lSpyyBFDeIC7PnEwYRV1/c1AryB6OZIkm2pEEBjD\r\nY3fwXkGtxa+QQROzz01ifwV8acTmKXbybBQfFLGm1eE77JEnQq6ly/sezam/\r\njNFGHS1xbUDki6FztY0ukXMT0puzPOXEBnv4IYM8Yeaa49VIIvrxh3BKXL8n\r\n1OviO9y9bZE/kdXWNCW357IyZowRK60tLyyOhqc/xH/9MbCTsQlma/rRobjk\r\nw9OPZc4x9sZFMADhWcBQY/4SnFh6iOcXuhM=\r\n=3KlS\r\n-----END PGP SIGNATURE-----\r\n", "size": 139005}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.3_1654484147578_0.6334004393936203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-06T03:03:34.008Z"}, "1.11.4": {"name": "dayjs", "version": "1.11.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "fdf648ba8a6312f565df3de7fed54460c6dc8c20", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.4", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-Zj/lPM5hOvQ1Bf7uAvewDaUcsJoI6JmNqmHhHl3nyumwe0XHwt8sWdOVAPACJzCebL8gQCi+K49w7iKWnGwX9g==", "shasum": "3b3c10ca378140d8917e06ebc13a4922af4f433e", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.4.tgz", "fileCount": 443, "unpackedSize": 647433, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB42PVue4XJQ9+Kx7DH7d78FYay4sjR5+Qg6B28y+yRbAiBiwSazAfz1vhMddmykbOtKdGVe8ZrEbq53Y4GSwHatLw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1uQWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHhg//dETnASSBn3pTtpngtyOIxQz7RHhBGk7Iy/cQ2G07G0ErW4Re\r\n8yavR1C36w4JibJh+JJzcexd23ZKbM4Vsf0q9LcAfFu/2K5pvlajSM6YPhYO\r\nz5QW44JLZdnW+lihva+txkQ9T6lwxawPjffuD0i+PnNapfq+D/hMSZ5j0Mij\r\nOwl9SIxwdJLVVO9yGIjKC7U6ELdwsxY5e6OwVgko2AWlvxyogCxnONW72ts9\r\nVZ657QNHi1QU7b4EsPNF9vj+G0JjVhXqx6mNjalCzPLk3qORe8gur64jvOF6\r\nSOZetw1JXUQ8VglKJAJTDNZJVOZNCee7QxIbgNMAnY5OyE6AqkkXF1pj8K+C\r\ncRNo2FvRZnWxGtZgBh1VvaWMNbZjc3Dv0tMMqydM1O8EwlaXZSzoqBlr9HM6\r\n1+kCAdW41+aAqST9L+5kgHxkMrWnUkAgGPIZ14Y1g9mnmE4NPwzD3WSyq2j3\r\nXFVn+PrsJr757ApyWhwJwX5bWwxeUzMdXYiiu8v+O8WK/a+3y8CqYb1vjFNy\r\nA0OjHVzWVb6eRXnsPoeGaWumIwnR6GvsMqWFXVeYOGWtCot3m7D5nmwKDAkm\r\nR/JSIxvg6Wm3wOJsYQNSHQk/Xx8/Rk2HcOg8FZgKCgiSV5w+cX4F5RTdjQJg\r\n9E15Or1bPM1rf9VDnpPgawfjePRvEptOoBs=\r\n=UITs\r\n-----END PGP SIGNATURE-----\r\n", "size": 137685}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.4_1658250262739_0.09655276181784389"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-19T17:05:57.804Z"}, "1.11.5": {"name": "dayjs", "version": "1.11.5", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "e3777ed0f8189ad8f3a36001ecc583f74edd81df", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.5", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-CAdX5Q3YW3Gclyo5Vpqkgpj8fSdLQcRuzfX6mC6Phy0nfJ0eGYOeS7m4mt2plDWLAtA4TqTakvbboHvUxfe4iA==", "shasum": "00e8cc627f231f9499c19b38af49f56dc0ac5e93", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.5.tgz", "fileCount": 443, "unpackedSize": 648087, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHSLt0GzyFBlCf0iXLf5jAzgsb6Em2aT2DwHGDSuDAOwIgRVuDawt3tqowq3Xmm09vdLmrgWiwro/p9JX7/BBQzN4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9k4vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9eg/+OCU+CbeFXylbonzwOOVeSyOrz74McEcmGRLt5oTDI8yz0A4X\r\nuCAL0UEw3/qBAzy7ssfUDP3Y1TrgPIrFqzOuuJTv+d4QVuBqNnxsrKrSGxdj\r\nQ96kj2IF+AIgcUC0L58QHQP7k1WjvzTIvmiWxFtPvvyaCma20W7BDuknhPYt\r\nSbImASYfyNOanc08jrmhvx6F1/Uo4uD2ZtjJb1v1/sbrQ39tOV605jmLmcWn\r\nG0fwYnE1R0xNvI/AUFdrKBrRIx6Jv+XkQAMfZPF9eoPe8eTDdeEnVBBgxMZc\r\nBjePu/d170VdMizYdSvFQtxNEw78fJ06Mrp8zLIISyqOh9Vj1Z1QtaYzvao+\r\nDZ9J8K3yPXd7zKRWCSQkj2tWDgAPUBjmtwvmkLlaC0TcjhkuITCS35v+PzLD\r\njYXCZF3aVOCyRAR0JvaoztJH6U2I5XXBNqIQv3bR+JYMjNq8m/l8Ao8gy4gx\r\npxEEAshM1hOkWhrYTd8fYyLFmp/A6dM315u1xRSEGEuUZtjkYH1H1ZIOijCD\r\nm0ipkQol/6snS1dzucqx6VEjpb1IZ5xA0ZQuoWjgh/IVTaEx4UBZWfyzfcIo\r\nNI9EAJYp8SBw9R8mUFgMKM6C1eUz/AtYy2jgIu+4IlbbVhl7w5oaEm+ZiiKF\r\n9eSW1PIY/T/4/T37W5pvK7PcL6e6VkOEZ5I=\r\n=hPkR\r\n-----END PGP SIGNATURE-----\r\n", "size": 137822}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.5_1660309039585_0.859260449680546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-12T12:57:25.915Z"}, "2.0.0-alpha.3": {"name": "dayjs", "version": "2.0.0-alpha.3", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "build": "cross-env BABEL_ENV=build node build/index.js && npm run build:esm && npm run size", "build:esm": "node build/esm.mjs", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dist/index.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "./dist/index.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-node-resolve": "^14.1.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "fast-glob": "^3.2.12", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.79.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "readmeFilename": "README.md", "gitHead": "da837ab99d7c379a10b6b0036e7d7c1ded269917", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@2.0.0-alpha.3", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-0VjnuXQ0c4HL4B0d+og/NMx47JH2mecTNTG3lhy/FjE8nV5yBACDqeTBkbj3QHDZRTqgd6Nj5XS0s3luzpRIyQ==", "shasum": "66c9a45568b3f7860abaa660f5f7f57eedfc0cd6", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-2.0.0-alpha.3.tgz", "fileCount": 402, "unpackedSize": 548448, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuevFUfbPadVHBT/OrCLSWTF/gfPGmJ3+AQdHy7k04MQIgbtcf9cgKM0u2jP0LBQdPsAivic6UAUCNwI5WkDKXJ0c="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMRblACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv1A/7BDe8g8ma8kqwloW/6NCwYa4x7a5rXux9kyA8i9O1usxjtuqk\r\nBScT0uZQRtdpbOSdyoxSjTVsEgY6Hww12cIUN81pRXGQ67UK1i09Ac5G8xVC\r\naIaX/VQ6SNHOM5v7YX2kR/w3Z16zRXKDvHzsaravL9h7VfY1TUuO3Mq7W4VX\r\nIxp2c60b0tixRkxD7Lb5PRpuCsZOdiiCmtzeUXRj1Z+aReGEF9iruhXNwV/g\r\nQdTxfADHrpMXz3lv+q467e05a2wnOX0p5auxOR7+8jSvkfr6C4RqhdP8Qdpl\r\nkHeXxeqHp/hYN1ha3B/K5qZRyJjVbnwQa+oYTMB5MXufMSQmpHinULsBgXXl\r\n2O6jZ+fgy63F/Pycq42BFdPvpr/SsIv3f1Z20F9834SMiCt8jla0VtWu8eSX\r\nd9rdT073bqGP9L2/JwsxZTYa6+q6DtgjCI9BkFlRKWH1Ic/KevvI1gwoz/hj\r\ndry0rcImTbO/sbsQo8Smrsv/pz7+dibxqGNxGbfnDCYPIzWi4JvlLWhWgvU7\r\nRVPI0Vclkv32LhZIzl7r3N31p45pPtkSIcXXK3Cej6oTmMbOSBFC/9/Rx0zL\r\nlEm7F1/qZqjXJ64CC4vSHZNTeAE3Xzj+zDLO4B8LzYJ6H6rPcnugp4KqFlPi\r\nPORoGdAEAlALQMNndkufu73rFBiBfPBHlvk=\r\n=XEzA\r\n-----END PGP SIGNATURE-----\r\n", "size": 131176}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_2.0.0-alpha.3_1664161509350_0.3827059478509822"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-26T03:06:27.011Z"}, "2.0.0-alpha.4": {"name": "dayjs", "version": "2.0.0-alpha.4", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "./dist/index.js", "module": "./dist/esm/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}, "./locale/*": {"require": "./dist/locale/*.js", "import": "./dist/locale/*.mjs", "types": "./dist/locale/*.d.ts"}, "./plugin/*": {"require": "./dist/plugin/*.js", "import": "./dist/plugin/*.mjs", "types": "./dist/plugin/*.d.ts"}}, "typesVersions": {"*": {"*": ["*", "dist/*"]}}, "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "build": "cross-env BABEL_ENV=build node build/index.js && npm run build:esm && npm run size", "build:esm": "node build/esm.mjs", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dist/index.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "./dist/index.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-node-resolve": "^14.1.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^8.24.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jest": "^21.15.0", "fast-glob": "^3.2.12", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.79.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "readmeFilename": "README.md", "gitHead": "1bf6e2d729c344ef25d908431f480ad4e76b5cc4", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@2.0.0-alpha.4", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-1iNcyGuh1/uip1b3tP2LSK8pWIO0CZuBC3XI4ShYO9oeJUJP7DfvDEOkr1I8aElvATTYKeY4J+N1b4d+qA5XEg==", "shasum": "7a3981964caf96ad58a6bec6a0cc508149c100f2", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-2.0.0-alpha.4.tgz", "fileCount": 583, "unpackedSize": 814880, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMprRitFxQ6D3ykR8a27ODK5Cdfh8iTpeown//JnYvnAiArCwoxM+VNC12ULyrE+BPHnC91HeamI287hCZ0rqaD6A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMStVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqmhw/+OlwsNKByUMvvRCtibEdr039aZMPJXm8Fcj9Y8kwUYvTFCKmx\r\no3d9YK2MPyDzrUyttqNkZ3/x3UK//Dk/UkxXu6elym3zlcYZTmFMszdsBg/W\r\nt/JcpL2e7x4HmX+pSvE5/fuPcwLi5577hHeUlhzIRq7EQeVHwK74n6/bO5V9\r\nHbysjBchDwAAUYTityobMzd/IvQ6/xRI6PYyO/FkRvG2skxj27kUEBTAevbx\r\nlsfvV4ZfHoRfEas5JGQnvlbK0GJ5tPPzw+3TY3EG3qUFe1JFWlKDra1IQeqz\r\nsZFDHZWN24YpJmU52Z06j9j5XIvG2n85hn1Bwj+gRXEqNRmv9vTpcIIu/dwR\r\n8Bbxj+mCSR93GyiP3jHB3PxLgP03hFTxOTgHUJrDqkIUZGrv7K/cSCzIux26\r\np76vmAyk0JPsyyAE1oZA40oT7NgV5mP9Z8JsB/vln6BqIbM/wDzQH8+f/TC7\r\nCxeyf2ibPmPzQDqktCCFbTN7s6+chAWpkAhRzYKCvbwxr7m7HOPmoii8cXs9\r\n8VWkUbS77x0Pls1QmJItse/SECQVznNLURvxpA5S0ITrFuVdeFOZx0VjF9ws\r\ng1eHDzFXXxWNKVDn87XoIiiuMIveYJus3T3QWSqxsZkbaA7ZZK2Pkuoxf/N1\r\npiyG7jYmaahivpsCGDhuQ7oxWS7ipPima5g=\r\n=vTr6\r\n-----END PGP SIGNATURE-----\r\n", "size": 141620}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_2.0.0-alpha.4_1664166740773_0.7296402101427639"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-26T04:33:55.033Z"}, "1.11.6": {"name": "dayjs", "version": "1.11.6", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "eaab7d5b675ae6fa61f706d2bc27da12dc8503d4", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.6", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-zZbY5giJAinCG+7AGaw0wIhNZ6J8AhWuSXKvuc1KAyMiRsvGQWqh4L+MomvhdAYjN+lqvVCMq1I41e3YHvXkyQ==", "shasum": "2e79a226314ec3ec904e3ee1dd5a4f5e5b1c7afb", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.6.tgz", "fileCount": 447, "unpackedSize": 650665, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG/Ob3Ln2Q4pDsF4yPIzO+iB7A5L9WY26kQXptS3BqlUAiEAkVhfQK1bb7qD5HbI/qAiC7+rzpk4nNS+QwvEYoiwyrg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUexcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+bA/+J0PKOKaQNNY8hiU3O3pnRxL/s2LxamYI216usC8ASdGmj5FK\r\n8rj+9yEVStKfEy9wxhOWSA3uXW2aqE0iWpu8RSCw7Z4JkKvcHFjDPqBfZK4j\r\n3hJOqEHrdtYsfWGZP2QTaxcSlB0UIVx/ecQAVCypsfSfcpEB4fdD3rDRAGDI\r\nywz4ja4Nnb7K3Elw5aNppCwkzBv0m8Z6b3+1FbWVFYlA7GWfPjLe7AwUHWVC\r\nzoUgEMseVWuo/l8Cgo4+3ZqiV9kDaCt/tp55TdQd9EGUT8ZmEiNIcndvqYFi\r\noYw3W8G5QJ9s1RBGG77eh3G8ZGIGYeaIg04D8nu9mkg7F5aBapP02T4F6VCJ\r\n8NjQifqBifKiI/fs+RQalylB7cEMRsZrFQtDs5/m1fkRUkUi9DIEbeb9TGko\r\nPScPv77w3m+kOJFS8Z9DDdgbjejK6bu8CwKYEF9jGv9DD3DERRohQrXsrIkF\r\n+OhB0CBOAipQ6/Hx00AKSqZA6tAfeQ8atKkeIe2M4TbYOiatsqOSvW/bnnPu\r\nXDGiIY97VvRU30TrMSMjtFff4fk2f+C6rHqB+Ld056trda6GyezVAQx3Mwdu\r\nhbupLSDysY8rKYqxNnZJk7osq0k6lZ/Ftb0HCLickeSKGAIkofUMEWWv4nV2\r\nq9/XUTuGG7lA3m/SsQxpldtUOAXf0RExTkM=\r\n=QEYj\r\n-----END PGP SIGNATURE-----\r\n", "size": 138327}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.6_1666313307799_0.23666113610937067"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-21T01:42:18.976Z"}, "1.11.7": {"name": "dayjs", "version": "1.11.7", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "release": {"prepare": [{"path": "@semantic-release/changelog"}, ["@semantic-release/git", {"assets": ["CHANGELOG.md"]}]]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "f72b537ad60d654e2eec44ff08896c183b14eb37", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.7", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-+Yw9U6YO5TQohxLcIkrXBeY73WP3ejHWVvx8XCk3gxvQDCTEmS48ZrSZCKciI7Bhl/uCMyxYtE9UqRILmFphkQ==", "shasum": "4b296922642f70999544d1144a2c25730fce63e2", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.7.tgz", "fileCount": 447, "unpackedSize": 651138, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzqscXDfjr5LthPz0B4wQr5EaHJVzj3dUX4Z8zgOUV3AIgb4fXkQoUVleJqqwuQcbEHaV8c8irU3WpTaurLfuodHY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjj1upACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWkQ//Wbr+wGJT8CmgOi+qbfQ5fea/wbUXtn+I6z0hhOLzpbIsOvmA\r\n754KGtBRw/2heA4POtXbD2b6htiv1u9jF7cTDgACnQU+goxgEf25ESpr6/P/\r\nQjoM1qUL/bY+q5VjLmZFebaKll2btJnNCYsQE97Aohwsc8mSXfCXg9GH6M+N\r\nfU1U4IblzmKb0XOF3lsRvImIBQG/ylMxTVZ596u/ALvM+J1gQIzy+B<PERSON>VAKim\r\n8oypN4BIWIichw9arlt+Ao3uGqMresVXy70MdRS4BVklFy40CqJvI0k9t7hY\r\n+FliQBiJ44Q3/2xXQUwBZ4zE/rmWzBKW3C0R+CgBY+g8GzNyp5BGfKFjCMTA\r\nwmEc3bNjqFO5OFO2hAOJEScRxVy+it1mIn8uioVy7KsgXxcseRkyehIv00+9\r\nQq8lANt2Bkr3I88+X1ImuwpnqG1XvjHEebXUZ6SUfLa8ynvHYivgWnrAYXdo\r\n6RPL3vBXGlK5eWKC5ApmbD/2IpCZDuUGiErY36X4B3n/Q/DIgPPf/l1orxIn\r\nm6ZQQNXH1U2Sji4moOJ4Ty2mSawqpld+ciZHyM7zn8CnlBt39p0bp234GiyC\r\nm6L9qClpkoCWrrv8Ry99lVOeJA+X3zLKZrjdU5zWboslCv2si2iwA9jNDmW9\r\nTcWCJtfa+ysFmxAVYOAEl25SG1OYVgsd1dU=\r\n=0AqG\r\n-----END PGP SIGNATURE-----\r\n", "size": 138420}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.7_1670339497498_0.49905452116173765"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-06T15:28:47.756Z"}, "1.11.8": {"name": "dayjs", "version": "1.11.8", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "b3624de619d6e734cd0ffdbbd3502185041c1b60", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.8", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-LcgxzFoWMEPO7ggRv1Y2N31hUf2R0Vj7fuy/m+Bg1K8rr+KAs1AEy4y9jd5DXe8pbHgX+srkHNS7TH6Q6ZhYeQ==", "shasum": "4282f139c8c19dd6d0c7bd571e30c2d0ba7698ea", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.8.tgz", "fileCount": 447, "unpackedSize": 652533, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyPRlcJeqNqsel8W6yQ/nkw6G0DijQzMUcUAM2SUqguQIgYs2pDIgXzJ7K+yVzTkxsuK0zrrsPlbv0+zzibNut+Z8="}], "size": 138710}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.8_1685708677978_0.1368239687384869"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-02T12:24:38.210Z", "publish_time": 1685708678210, "_source_registry_name": "default"}, "1.11.9": {"name": "dayjs", "version": "1.11.9", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "5044cc839779c4bd399943e8e764bacfce5eaffc", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.9", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-QvzAURSbQ0pKdIye2txOzNaHmxtUBXerpY0FJsFXUMKbIZeFm5ht1LS/jFsrncjnmtv8HsG0W2g6c0zUjZWmpA==", "shasum": "9ca491933fadd0a60a2c19f6c237c03517d71d1a", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.9.tgz", "fileCount": 447, "unpackedSize": 656635, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHXQz/oYj1oIW+RWHRov0TLo/MWZMwLTTWNrEOyD0i1QIgVmDXjfiiw6BJgfqRSRUskLPtxMKmAZ6ogLNignk8bMU="}], "size": 139583}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.9_1688181240356_0.06995397818070037"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-01T03:14:00.677Z", "publish_time": 1688181240677, "_source_registry_name": "default"}, "1.11.10": {"name": "dayjs", "version": "1.11.10", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "gitHead": "e56a8703dcc3820196972f53990010daf248726b", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_id": "dayjs@1.11.10", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==", "shasum": "68acea85317a6e164457d6d6947564029a6a16a0", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.10.tgz", "fileCount": 447, "unpackedSize": 663560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFEF1uehQO67scWMIezEG+92Q+Ok7enVXa4wRMCspj2aAiEA6ldTBR23fkumgW/NEgoU3cuf6WS/baxGhPOpXBpfeok="}], "size": 140922}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.10_1695132440436_0.678527530549146"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-19T14:07:20.710Z", "publish_time": 1695132440710, "_source_registry_name": "default"}, "1.11.11": {"name": "dayjs", "version": "1.11.11", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "_id": "dayjs@1.11.11", "gitHead": "f9790aec5efcd590feafd760ac9b1a83b13f08ce", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-okzr3f11N6WuqYtZSvm+F776mB41wRZMhKP+hc34YdW+KmtYYK9iqvHSwo2k9FEH3fhGXvOPV6yz2IcSrfRUDg==", "shasum": "dfe0e9d54c5f8b68ccf8ca5f72ac603e7e5ed59e", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.11.tgz", "fileCount": 447, "unpackedSize": 665770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2wkVChhaOFNj+I3/2Kf8UlB2LcJPkvcJWx7CkAK2m2wIhAL9qM9DdaVFIlazHhLYO7fRFBjiflhif+nQwmp0jCZUD"}], "size": 143883}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.11_1714305548522_0.6434633846548414"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-28T11:59:08.743Z", "publish_time": 1714305548743, "_source_registry_name": "default"}, "1.11.12": {"name": "dayjs", "version": "1.11.12", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "_id": "dayjs@1.11.12", "gitHead": "6cc12b7748117a5089153a6acfac9f83e44729b8", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_nodeVersion": "20.15.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-Rt2g+nTbLlDWZTwwrIXjy9MeiZmSDI375FvZs72ngxx8PDC6YXOeR3q5LAuPzjZQxhiWdRKac7RKV+YyQYfYIg==", "shasum": "5245226cc7f40a15bf52e0b99fd2a04669ccac1d", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.12.tgz", "fileCount": 451, "unpackedSize": 669061, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFupCM5s6QVM9Oj+p/9wsjgPl0dV8OMO+krHsdmbgjHhAiBRPHfgEWjUeZ8NypUup4JBOt0tkSNSdA5Flgb2LiwQig=="}], "size": 144739}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.12_1721306450880_0.8707440679140437"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-18T12:40:51.167Z", "publish_time": 1721306451167, "_source_registry_name": "default"}, "1.11.13": {"name": "dayjs", "version": "1.11.13", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": {"name": "i<PERSON><PERSON>n"}, "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}, "_id": "dayjs@1.11.13", "gitHead": "93c8fd0f807b8a8252f4cd65083bb1d6a49b90e7", "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "_nodeVersion": "20.16.0", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==", "shasum": "92430b0139055c3ebb60150aa13e860a4b5a366c", "tarball": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz", "fileCount": 451, "unpackedSize": 670277, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrM0NBltfa8D96WRdtY5sru9jom5x8e05os6Nd3M4rXAIhAMpRneqsgwya/YvBW9078D4mL49PtnvcAMCxQxC1ywDR"}], "size": 145109}, "_npmUser": {"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "i<PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dayjs_1.11.13_1724165992711_0.9671719474571252"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-20T14:59:52.917Z", "publish_time": 1724165992917, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/iamkun/dayjs/issues"}, "homepage": "https://day.js.org", "keywords": ["dayjs", "date", "time", "immutable", "moment"], "repository": {"type": "git", "url": "git+https://github.com/iamkun/dayjs.git"}, "_source_registry_name": "default"}