{"_id": "@esbuild/openharmony-arm64", "_rev": "5238395-686b1b4885efceeca2378da1", "dist-tags": {"latest": "0.25.6"}, "name": "@esbuild/openharmony-arm64", "time": {"created": "2025-07-07T00:56:40.182Z", "modified": "2025-07-07T17:29:29.243Z", "0.0.1": "2025-07-07T00:56:30.904Z", "0.25.6": "2025-07-07T16:59:09.718Z"}, "versions": {"0.0.1": {"name": "@esbuild/openharmony-arm64", "version": "0.0.1", "description": "A WebAssembly shim for esbuild on OpenHarmony ARM64.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openharmony"], "cpu": ["arm64"], "_id": "@esbuild/openharmony-arm64@0.0.1", "gitHead": "0231961d467d82f75818b642a4e48fce17516084", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-U5gvLV1lZfZACgJkbL7hEnLkXkm1H7I+GXM+xEuMgZSActbiIDO1/eXo0kUX72oodgz0BfVuMQi2ajy4I1qIaA==", "shasum": "1f2d61dde1a56fe22edf1c1d92da81fd970c1014", "tarball": "https://registry.npmmirror.com/@esbuild/openharmony-arm64/-/openharmony-arm64-0.0.1.tgz", "fileCount": 2, "unpackedSize": 503, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCAK0wkxxm3JRKX5um/YvF9E+ua/dDavXviJe6lJ2BbbAIgfkyBiWYFieTxU39fCT7ihwAViPh8V33L/1qAf5YuHEw="}], "size": 399}, "_npmUser": {"name": "esbuild", "actor": {"name": "esbuild", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openharmony-arm64_0.0.1_1751849790728_0.04255462743473304"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T00:56:30.904Z", "publish_time": 1751849790904, "_source_registry_name": "default"}, "0.25.6": {"name": "@esbuild/openharmony-arm64", "version": "0.25.6", "description": "A WebAssembly shim for esbuild on OpenHarmony ARM64.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openharmony"], "cpu": ["arm64"], "_id": "@esbuild/openharmony-arm64@0.25.6", "gitHead": "d38c1f0bc580b4a8a93f23559d0cd9085d7ba31f", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-+SqBcAWoB1fYKmpWoQP4pGtx+pUUC//RNYhFdbcSA16617cchuryuhOCRpPsjCblKukAckWsV+aQ3UKT/RMPcA==", "shasum": "2087a5028f387879154ebf44bdedfafa17682e5b", "tarball": "https://registry.npmmirror.com/@esbuild/openharmony-arm64/-/openharmony-arm64-0.25.6.tgz", "fileCount": 6, "unpackedSize": 12185939, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD5g/6cA+LRxxcxw7KyLUHRYNUJOnxfkRfOUIZLJGLXlgIgDKhksFPBHH4TGwbIL/Hv7/1ROyup8gS4+FJYfc3AnaM="}], "size": 3229208}, "_npmUser": {"name": "evanw", "email": "<EMAIL>", "actor": {"name": "evanw", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openharmony-arm64_0.25.6_1751907549428_0.6156538423620379"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T16:59:09.718Z", "publish_time": 1751907549718, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "description": "A WebAssembly shim for esbuild on OpenHarmony ARM64.", "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is a WebAssembly shim for esbuild on OpenHarmony ARM64. See https://github.com/evanw/esbuild for details.\n", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "_source_registry_name": "default"}