<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <header class="top-nav">
      <div class="system-info">
        <h1>项目系统</h1>
        <p>{{ currentTime }}</p>
      </div>

      <!-- 登录/退出按钮 -->
      <div class="auth-container">
        <span v-if="userInfo">{{ userInfo.username }}</span>
        <button v-if="!userInfo" @click="showLoginModal = true">登录</button>
        <button v-else @click="handleLogout">退出登录</button>
      </div>
    </header>

    <!-- 登录模态框 -->
    <LoginModal
      v-if="showLoginModal"
      @close="showLoginModal = false"
      @login-success="handleLoginSuccess"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单 -->
      <aside class="sidebar">
        <nav>
          <ul>
            <li v-for="item in menuItems" :key="item.path">
              <a
                href="javascript:void(0)"
                :class="{
                  'router-link-exact-active': $route.path === item.path,
                }"
                @click="handleMenuClick(item.path)"
              >
                {{ item.name }}
              </a>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- 右侧内容 -->
      <main class="content">
        <router-view></router-view>
      </main>
    </div>

    <!-- 底部固定信息 -->
    <footer class="footer">
      <p>© 2025 jooviyo 版权所有</p>
    </footer>
  </div>
</template>
<script>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { logout } from "@/utils/api";
import { showToast } from "@/utils/toast";
import LoginModal from "@/views/LoginModal.vue";

export default {
  name: "MainLayout",
  components: { LoginModal },
  setup() {
    const router = useRouter();
    const store = useStore();

    const currentTime = ref("");
    const timer = ref(null);
    const showLoginModal = ref(false);
    const userInfo = ref(null);

    const allMenuItems = [
      { path: "/dashboard", name: "仪表盘", requiresAuth: false },
      { path: "/projects", name: "项目管理", requiresAuth: true },
      { path: "/tasks", name: "任务管理", requiresAuth: true },
      { path: "/UserDashboard", name: "个人看板", requiresAuth: true },
      { path: "/users", name: "用户管理", requiresAuth: true },
    ];

    const menuItems = computed(() => {
      if (!userInfo.value) {
        // 未登录用户只显示仪表盘
        return allMenuItems.filter((item) => !item.requiresAuth);
      }
      // 已登录用户显示所有菜单
      return allMenuItems;
    });

    const updateTime = () => {
      const now = new Date();
      currentTime.value = now.toLocaleString("zh-CN");
    };

    const handleLogout = async () => {
      try {
        await logout();
        // 清除 Vuex 状态
        store.commit("clearUser");
        showToast("已成功退出登录", "success");
        // 跳转到登录页
        router.push("/login");
      } catch (error) {
        console.error("退出登录失败:", error);
        showToast("退出登录失败，请重试", "error");
      }
    };

    const handleMenuClick = (path) => {
      const menuItem = allMenuItems.find((item) => item.path === path);
      if (menuItem && menuItem.requiresAuth && !userInfo.value) {
        showToast("请先登录后再操作", "warning");
        showLoginModal.value = true;
        return;
      }
      router.push(path);
    };

    onMounted(() => {
      updateTime();
      timer.value = setInterval(updateTime, 1000);

      // 获取用户信息
      userInfo.value = store.state.userInfo;
    });

    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value);
      }
    });

    const handleLoginSuccess = () => {
      showLoginModal.value = false;
      showToast("登录成功", "success");
      // 获取最新的用户信息
      userInfo.value = store.state.userInfo;
    };

    return {
      currentTime,
      userInfo,
      menuItems,
      showLoginModal,
      updateTime,
      handleLogout,
      handleLoginSuccess,
      handleMenuClick,
    };
  },
};
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(to bottom, #0f0f23, #05051a);
  color: #00ffff;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #46597e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.system-info h1 {
  margin: 0;
  font-size: 1.8rem;
  text-shadow: 0 0 5px #00ffff;
}

.system-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #cccccc;
}

.auth-container {
  display: flex;
  align-items: center;
}

.auth-container span {
  margin-right: 1rem;
  color: #00ffff;
}

.auth-container button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 30px;
  background-color: #00ffff;
  color: #0f0f23;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.auth-container button:hover {
  background-color: #00dddd;
  transform: scale(1.05);
}

.main-content {
  flex: 1;
  display: flex;
  padding: 2rem;
  gap: 2rem;
}

.sidebar {
  width: 150px;
  background-color: #1a1a40;
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.sidebar nav ul {
  list-style-type: none;
  padding: 0;
}

.sidebar nav ul li {
  margin: 1rem 0;
}

.sidebar nav ul li a {
  color: #00ffff;
  text-decoration: none;
  font-weight: bold;
  display: block;
  padding: 0.5rem;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.router-link-exact-active {
  background-color: #00ffff;
  color: #0f0f23;
  box-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff;
}

.content {
  flex: 1;
  background-color: #1a1a40;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.footer {
  text-align: center;
  padding: 1rem;
  background-color: #1a1a40;
  color: #00ffff;
  font-size: 0.9rem;
  text-shadow: 0 0 5px #00ffff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.2);
}

/* 登录模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #1a1a40;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
  width: 100%;
  max-width: 400px;
  animation: fadeIn 0.3s ease-in-out;
}

.modal-content h3 {
  margin-top: 0;
  color: #00ffff;
  text-align: center;
  margin-bottom: 1.5rem;
}

.input-group {
  margin-bottom: 1rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #cccccc;
}

.input-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #00ffff;
  border-radius: 5px;
  background-color: #2a2a60;
  color: #00ffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 2.8rem;
  cursor: pointer;
  color: #00ffff;
  font-size: 0.9rem;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.button-group button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 5px;
  background-color: #00ffff;
  color: #0f0f23;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 0.5rem;
}

.button-group button:last-child {
  margin-right: 0;
  background-color: #666;
  color: white;
}

.button-group button:hover {
  background-color: #00dddd;
  transform: scale(1.02);
}

.button-group button:disabled {
  background-color: #00aaaa;
  cursor: not-allowed;
  transform: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
