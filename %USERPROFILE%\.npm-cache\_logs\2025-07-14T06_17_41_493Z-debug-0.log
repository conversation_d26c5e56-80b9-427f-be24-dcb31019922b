0 verbose cli C:\Program Files\nodejs\node.exe C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:C:\Program Files\nodejs\node_modules\npm\npmrc
4 silly config load:file:D:\车秘系统相关文档\projects\project_manage\client\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:D:\tools\nodejs\node_global\etc\npmrc
7 verbose title npm install
8 verbose argv "install"
9 verbose logfile logs-max:10 dir:D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_41_493Z-
10 verbose logfile D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_41_493Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 silly logfile done cleaning log files
14 silly idealTree buildDeps
15 silly fetch manifest eslint-plugin-vue@^5.2.3 || ^6.0.0 || ^7.0.0
16 silly packumentCache full:https://registry.npmmirror.com/eslint-plugin-vue cache-miss
17 http fetch GET 200 https://registry.npmmirror.com/eslint-plugin-vue 569ms (cache revalidated)
18 silly packumentCache full:https://registry.npmmirror.com/eslint-plugin-vue set size:595209 disposed:false
19 silly fetch manifest eslint@^5.0.0 || ^6.0.0 || ^7.0.0
20 silly packumentCache full:https://registry.npmmirror.com/eslint cache-miss
21 http fetch GET 200 https://registry.npmmirror.com/eslint 49ms (cache revalidated)
22 silly packumentCache full:https://registry.npmmirror.com/eslint set size:1695741 disposed:false
23 verbose stack Error: could not resolve
23 verbose stack     at PlaceDep.failPeerConflict (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\place-dep.js:503:25)
23 verbose stack     at new PlaceDep (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\place-dep.js:157:21)
23 verbose stack     at #buildDepStep (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\build-ideal-tree.js:920:18)
23 verbose stack     at async Arborist.buildIdealTree (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\build-ideal-tree.js:181:7)
23 verbose stack     at async Promise.all (index 1)
23 verbose stack     at async Arborist.reify (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\reify.js:131:5)
23 verbose stack     at async Install.exec (C:\Program Files\nodejs\node_modules\npm\lib\commands\install.js:150:5)
23 verbose stack     at async Npm.exec (C:\Program Files\nodejs\node_modules\npm\lib\npm.js:207:9)
23 verbose stack     at async module.exports (C:\Program Files\nodejs\node_modules\npm\lib\cli\entry.js:74:5)
24 error code ERESOLVE
25 error ERESOLVE could not resolve
26 error
27 error While resolving: @vue/eslint-config-typescript@7.0.0
27 error Found: eslint-plugin-vue@8.7.1[2m[22m
27 error [2mnode_modules/eslint-plugin-vue[22m
27 error   [34mdev[39m eslint-plugin-vue@"^8.0.3" from the root project
27 error
27 error Could not resolve dependency:
27 error [95mpeer[39m eslint-plugin-vue@"^5.2.3 || ^6.0.0 || ^7.0.0" from @vue/eslint-config-typescript@7.0.0[2m[22m
27 error [2mnode_modules/@vue/eslint-config-typescript[22m
27 error   [34mdev[39m @vue/eslint-config-typescript@"^7.0.0" from the root project
27 error
27 error Conflicting peer dependency: eslint-plugin-vue@7.20.0[2m[22m
27 error [2mnode_modules/eslint-plugin-vue[22m
27 error   [95mpeer[39m eslint-plugin-vue@"^5.2.3 || ^6.0.0 || ^7.0.0" from @vue/eslint-config-typescript@7.0.0[2m[22m
27 error   [2mnode_modules/@vue/eslint-config-typescript[22m
27 error     [34mdev[39m @vue/eslint-config-typescript@"^7.0.0" from the root project
27 error
27 error Fix the upstream dependency conflict, or retry
27 error this command with --force or --legacy-peer-deps
27 error to accept an incorrect (and potentially broken) dependency resolution.
28 error
28 error
28 error For a full report see:
28 error D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_41_493Z-eresolve-report.txt
29 silly unfinished npm timer reify 1752473862045
30 silly unfinished npm timer reify:loadTrees 1752473862045
31 silly unfinished npm timer idealTree:buildDeps 1752473862559
32 silly unfinished npm timer idealTree:node_modules/@vue/eslint-config-typescript 1752473862560
33 verbose cwd D:\车秘系统相关文档\projects\project_manage\client
34 verbose os Windows_NT 10.0.18363
35 verbose node v22.16.0
36 verbose npm  v10.9.2
37 verbose exit 1
38 verbose code 1
39 error A complete log of this run can be found in: D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_41_493Z-debug-0.log
