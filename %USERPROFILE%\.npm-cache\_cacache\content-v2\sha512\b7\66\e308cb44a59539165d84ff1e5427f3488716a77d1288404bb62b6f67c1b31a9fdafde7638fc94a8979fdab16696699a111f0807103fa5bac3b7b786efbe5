{"_attachments": {}, "_id": "sax", "_rev": "1959-61f1481523990e8a812f28ce", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "An evented streaming XML parser in JavaScript", "dist-tags": {"false": "0.4.0", "latest": "1.4.1"}, "license": "ISC", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "sax", "readme": "# sax js\n\nA sax-style parser for XML and HTML.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in\nthe browser or other CommonJS implementations.\n\n## What This Is\n\n* A very simple tool to parse through an XML string.\n* A stepping stone to a streaming HTML parser.\n* A handy way to deal with RSS and other mostly-ok-but-kinda-broken XML\n  docs.\n\n## What This Is (probably) Not\n\n* An HTML Parser - That's a fine goal, but this isn't it.  It's just\n  XML.\n* A DOM Builder - You can use it to build an object model out of XML,\n  but it doesn't do that out of the box.\n* XSLT - No DOM = no querying.\n* 100% Compliant with (some other SAX implementation) - Most SAX\n  implementations are in Java and do a lot more than this does.\n* An XML Validator - It does a little validation when in strict mode, but\n  not much.\n* A Schema-Aware XSD Thing - Schemas are an exercise in fetishistic\n  masochism.\n* A DTD-aware Thing - Fetching DTDs is a much bigger job.\n\n## Regarding `<!DOCTYPE`s and `<!ENTITY`s\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything\nwith that. If you want to listen to the `ondoctype` event, and then fetch\nthe doctypes, and read the entities and add them to `parser.ENTITIES`, then\nbe my guest.\n\nUnknown entities will fail in strict mode, and in loose mode, will pass\nthrough unmolested.\n\n## Usage\n\n```javascript\nvar sax = require(\"./lib/sax\"),\n  strict = true, // set to false for html-mode\n  parser = sax.parser(strict);\n\nparser.onerror = function (e) {\n  // an error happened.\n};\nparser.ontext = function (t) {\n  // got some text.  t is the string of text.\n};\nparser.onopentag = function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n};\nparser.onattribute = function (attr) {\n  // an attribute.  attr has \"name\" and \"value\"\n};\nparser.onend = function () {\n  // parser stream is done, and ready to have more stuff written to it.\n};\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n\n// stream usage\n// takes the same options as the parser\nvar saxStream = require(\"sax\").createStream(strict, options)\nsaxStream.on(\"error\", function (e) {\n  // unhandled errors will throw, since this is a proper node\n  // event emitter.\n  console.error(\"error!\", e)\n  // clear the error\n  this._parser.error = null\n  this._parser.resume()\n})\nsaxStream.on(\"opentag\", function (node) {\n  // same object as above\n})\n// pipe is supported, and it's readable/writable\n// same chunks coming in also go out.\nfs.createReadStream(\"file.xml\")\n  .pipe(saxStream)\n  .pipe(fs.createWriteStream(\"file-copy.xml\"))\n```\n\n\n## Arguments\n\nPass the following arguments to the parser function.  All are optional.\n\n`strict` - Boolean. Whether or not to be a jerk. Default: `false`.\n\n`opt` - Object bag of settings regarding string formatting.  All default to `false`.\n\nSettings supported:\n\n* `trim` - Boolean. Whether or not to trim text and comment nodes.\n* `normalize` - Boolean. If true, then turn any whitespace into a single\n  space.\n* `lowercase` - Boolean. If true, then lowercase tag names and attribute names\n  in loose mode, rather than uppercasing them.\n* `xmlns` - Boolean. If true, then namespaces are supported.\n* `position` - Boolean. If false, then don't track line/col/position.\n* `strictEntities` - Boolean. If true, only parse [predefined XML\n  entities](http://www.w3.org/TR/REC-xml/#sec-predefined-ent)\n  (`&amp;`, `&apos;`, `&gt;`, `&lt;`, and `&quot;`)\n* `unquotedAttributeValues` - Boolean. If true, then unquoted\n  attribute values are allowed. Defaults to `false` when `strict`\n  is true, `true` otherwise.\n\n## Methods\n\n`write` - Write bytes onto the stream. You don't have to do this all at\nonce. You can keep writing as much as you want.\n\n`close` - Close the stream. Once closed, no more data may be written until\nit is done processing the buffer, which is signaled by the `end` event.\n\n`resume` - To gracefully handle errors, assign a listener to the `error`\nevent. Then, when the error is taken care of, you can call `resume` to\ncontinue parsing. Otherwise, the parser will not continue while in an error\nstate.\n\n## Members\n\nAt all times, the parser object will have the following members:\n\n`line`, `column`, `position` - Indications of the position in the XML\ndocument where the parser currently is looking.\n\n`startTagPosition` - Indicates the position where the current tag starts.\n\n`closed` - Boolean indicating whether or not the parser can be written to.\nIf it's `true`, then wait for the `ready` event to write again.\n\n`strict` - Boolean indicating whether or not the parser is a jerk.\n\n`opt` - Any options passed into the constructor.\n\n`tag` - The current tag being dealt with.\n\nAnd a bunch of other stuff that you probably shouldn't touch.\n\n## Events\n\nAll events emit with a single argument. To listen to an event, assign a\nfunction to `on<eventname>`. Functions get executed in the this-context of\nthe parser object. The list of supported events are also in the exported\n`EVENTS` array.\n\nWhen using the stream interface, assign handlers using the EventEmitter\n`on` function in the normal fashion.\n\n`error` - Indication that something bad happened. The error will be hanging\nout on `parser.error`, and must be deleted before parsing can continue. By\nlistening to this event, you can keep an eye on that kind of stuff. Note:\nthis happens *much* more in strict mode. Argument: instance of `Error`.\n\n`text` - Text node. Argument: string of text.\n\n`doctype` - The `<!DOCTYPE` declaration. Argument: doctype string.\n\n`processinginstruction` - Stuff like `<?xml foo=\"blerg\" ?>`. Argument:\nobject with `name` and `body` members. Attributes are not parsed, as\nprocessing instructions have implementation dependent semantics.\n\n`sgmldeclaration` - Random SGML declarations. Stuff like `<!ENTITY p>`\nwould trigger this kind of event. This is a weird thing to support, so it\nmight go away at some point. SAX isn't intended to be used to parse SGML,\nafter all.\n\n`opentagstart` - Emitted immediately when the tag name is available,\nbut before any attributes are encountered.  Argument: object with a\n`name` field and an empty `attributes` set.  Note that this is the\nsame object that will later be emitted in the `opentag` event.\n\n`opentag` - An opening tag. Argument: object with `name` and `attributes`.\nIn non-strict mode, tag names are uppercased, unless the `lowercase`\noption is set.  If the `xmlns` option is set, then it will contain\nnamespace binding information on the `ns` member, and will have a\n`local`, `prefix`, and `uri` member.\n\n`closetag` - A closing tag. In loose mode, tags are auto-closed if their\nparent closes. In strict mode, well-formedness is enforced. Note that\nself-closing tags will have `closeTag` emitted immediately after `openTag`.\nArgument: tag name.\n\n`attribute` - An attribute node.  Argument: object with `name` and `value`.\nIn non-strict mode, attribute names are uppercased, unless the `lowercase`\noption is set.  If the `xmlns` option is set, it will also contains namespace\ninformation.\n\n`comment` - A comment node.  Argument: the string of the comment.\n\n`opencdata` - The opening tag of a `<![CDATA[` block.\n\n`cdata` - The text of a `<![CDATA[` block. Since `<![CDATA[` blocks can get\nquite large, this event may fire multiple times for a single block, if it\nis broken up into multiple `write()`s. Argument: the string of random\ncharacter data.\n\n`closecdata` - The closing tag (`]]>`) of a `<![CDATA[` block.\n\n`opennamespace` - If the `xmlns` option is set, then this event will\nsignal the start of a new namespace binding.\n\n`closenamespace` - If the `xmlns` option is set, then this event will\nsignal the end of a namespace binding.\n\n`end` - Indication that the closed stream has ended.\n\n`ready` - Indication that the stream has reset, and is ready to be written\nto.\n\n`noscript` - In non-strict mode, `<script>` tags trigger a `\"script\"`\nevent, and their contents are not checked for special xml characters.\nIf you pass `noscript: true`, then this behavior is suppressed.\n\n## Reporting Problems\n\nIt's best to write a failing test if you find an issue.  I will always\naccept pull requests with failing tests if they demonstrate intended\nbehavior, but it is very hard to figure out what issue you're describing\nwithout a test.  Writing a test is also the best way for you yourself\nto figure out if you really understand the issue you think you have with\nsax-js.\n", "time": {"created": "2022-01-26T13:09:41.982Z", "modified": "2024-05-28T02:58:17.083Z", "1.2.4": "2017-06-22T17:46:38.198Z", "1.2.3": "2017-06-22T17:21:19.940Z", "1.2.2": "2017-02-07T22:02:22.387Z", "1.2.1": "2016-03-19T04:06:46.554Z", "1.1.6": "2016-03-05T09:13:57.725Z", "1.1.5": "2016-01-28T05:23:25.517Z", "1.1.4": "2015-10-22T17:40:20.687Z", "1.1.3": "2015-09-15T00:16:23.256Z", "1.1.2": "2015-08-26T22:24:46.354Z", "1.1.1": "2015-05-20T07:16:27.722Z", "1.1.0": "2015-04-21T22:54:33.327Z", "1.0.0": "2015-04-21T01:49:35.272Z", "0.6.1": "2014-10-14T17:35:32.436Z", "0.6.0": "2014-01-02T16:32:00.285Z", "0.5.8": "2013-12-09T00:38:12.647Z", "0.5.7": "2013-12-05T23:50:02.652Z", "0.5.6": "2013-12-05T23:43:33.631Z", "0.5.5": "2013-08-21T16:10:42.555Z", "0.5.4": "2013-04-25T23:13:14.436Z", "0.5.3": "2013-04-22T21:31:32.371Z", "0.5.2": "2013-02-20T16:34:45.595Z", "0.5.1": "2013-01-07T21:11:45.506Z", "0.5.0": "2013-01-06T18:51:37.546Z", "0.4.3": "2012-12-28T05:44:38.410Z", "0.4.2": "2012-07-26T04:04:22.445Z", "0.4.0": "2012-07-10T19:27:12.052Z", "0.4.1": "2012-07-10T19:26:45.999Z", "0.3.5": "2011-12-30T03:56:14.892Z", "0.3.4": "2011-12-05T05:15:41.943Z", "0.3.3": "2011-11-11T16:44:00.293Z", "0.3.2": "2011-10-21T22:15:00.311Z", "0.3.1": "2011-10-21T00:04:36.763Z", "0.3.0": "2011-10-17T15:02:08.184Z", "0.2.5": "2011-10-06T17:13:49.723Z", "0.2.4": "2011-09-17T00:58:51.899Z", "0.2.3": "2011-08-01T16:51:52.531Z", "0.2.2": "2011-07-21T20:16:56.686Z", "0.2.1": "2011-07-21T01:34:08.297Z", "0.2.0": "2011-07-20T23:53:50.073Z", "0.1.5": "2011-07-08T23:49:39.930Z", "0.1.4": "2011-06-14T18:44:43.566Z", "0.1.3": "2011-06-14T01:26:18.836Z", "0.1.2": "2011-02-09T19:13:51.969Z", "0.1.1": "2011-02-09T19:13:51.969Z", "0.1.0": "2011-02-09T19:13:51.969Z", "1.3.0": "2023-09-26T19:45:54.056Z", "1.4.0": "2024-05-27T17:48:04.658Z", "1.4.1": "2024-05-27T23:46:17.431Z"}, "versions": {"1.2.4": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.2.4", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "posttest": "standard -F test/*.js lib/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "README.md"], "devDependencies": {"standard": "^8.6.0", "tap": "^10.5.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "5aee2163d55cff24b817bbf550bac44841f9df45", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.2.4", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2816234e2378bddc4e5354fab5caa895df7100d9", "size": 15162, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sax-1.2.4.tgz_1498153598031_0.6106507133226842"}, "directories": {}, "publish_time": 1498153598198, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498153598198, "_cnpmcore_publish_time": "2021-12-13T08:16:31.017Z"}, "1.2.3": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.2.3", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "posttest": "standard -F test/*.js lib/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "README.md"], "devDependencies": {"standard": "^8.6.0", "tap": "^10.0.2"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "84b994861770d37b4ac9aaafd0a6d28fbae997b8", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.2.3", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a6134f5f11259b9a563f2f976bcf759d144803e3", "size": 15161, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.2.3.tgz", "integrity": "sha512-U/jqm5gm+jcljJwhp8Be5/JhWjl1XGwjF65aKmihVaReZ7fU69w2xESMg5PMqRqYIb8lagU4/4g3+1QXk3fgdg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sax-1.2.3.tgz_1498152079765_0.4188503003679216"}, "directories": {}, "publish_time": 1498152079940, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498152079940, "_cnpmcore_publish_time": "2021-12-13T08:16:31.313Z"}, "1.2.2": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.2.2", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "posttest": "standard -F test/*.js lib/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "README.md"], "devDependencies": {"standard": "^8.6.0", "tap": "^10.0.2"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "27a70c4af4980323d8af47483a126289ea32e567", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.2.2", "_shasum": "fd8631a23bc7826bef5d871bdb87378c95647828", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "fd8631a23bc7826bef5d871bdb87378c95647828", "size": 15239, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.2.2.tgz", "integrity": "sha512-IN2coIooKl8T+3ca83BZTtWUiiYeFRsKCt9NazjxKR4SFXRrbpdR/iwr4B2zCMstVEg+5OFmY/FHh4JJKKS7xA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sax-1.2.2.tgz_1486504942139_0.281537250848487"}, "directories": {}, "publish_time": 1486504942387, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486504942387, "_cnpmcore_publish_time": "2021-12-13T08:16:31.632Z"}, "1.2.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.2.1", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov", "posttest": "standard -F test/*.js lib/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "LICENSE-W3C.html", "README.md"], "devDependencies": {"standard": "^5.3.1", "tap": "^5.2.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "9f9814f544f1c5b89dd1d79957c041f37e845b4a", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.2.1", "_shasum": "7b8e656190b228e81a66aea748480d828cd2d37a", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.6.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "7b8e656190b228e81a66aea748480d828cd2d37a", "size": 18907, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.2.1.tgz", "integrity": "sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/sax-1.2.1.tgz_1458360406158_0.19482766138389707"}, "directories": {}, "publish_time": 1458360406554, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458360406554, "_cnpmcore_publish_time": "2021-12-13T08:16:32.041Z"}, "1.1.6": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.6", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov", "posttest": "standard -F test/*.js lib/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "LICENSE-W3C.html", "README.md"], "devDependencies": {"standard": "^5.3.1", "tap": "^5.2.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "8dc23406a83992a22b8240abdd91185db2b4fb0c", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.6", "_shasum": "5d616be8a5e607d54e114afae55b7eaf2fcc3240", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.6.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5d616be8a5e607d54e114afae55b7eaf2fcc3240", "size": 18809, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.6.tgz", "integrity": "sha512-8zci48uUQyfqynGDSkUMD7FCJB96hwLnlZOXlgs1l3TX+LW27t3psSWKUxC0fxVgA86i8tL4NwGcY1h/6t3ESg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sax-1.1.6.tgz_1457169236375_0.0688094305805862"}, "directories": {}, "publish_time": 1457169237725, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457169237725, "_cnpmcore_publish_time": "2021-12-13T08:16:32.409Z"}, "1.1.5": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.5", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov", "posttest": "standard -F test/*.js lib/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "LICENSE-W3C.html", "README.md"], "devDependencies": {"standard": "^5.3.1", "tap": "^5.2.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "2e69674891189fead76299ba0b5e611a59cb1035", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.5", "_shasum": "1da50a8d00cdecd59405659f5ff85349fe773743", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1da50a8d00cdecd59405659f5ff85349fe773743", "size": 18789, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.5.tgz", "integrity": "sha512-z19WXQiOz8RBu3zDpOE9541RgB7Q5NecZ7SAgU3yUvqhMNvG4hTChbrutzpDyDSHmeHouR5Rqk4eup1o6C6dxQ=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453958605517, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453958605517, "_cnpmcore_publish_time": "2021-12-13T08:16:32.775Z"}, "1.1.4": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.4", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js", "posttest": "npm run lint", "lint": "standard -F test/*.js lib/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "LICENSE-W3C.html", "README.md"], "devDependencies": {"standard": "^5.3.1", "tap": "^2.1.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "ce6b2a2c20633482eeabb6d39cee734aef2e4da7", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.4", "_shasum": "74b6d33c9ae1e001510f179a91168588f1aedaa9", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "74b6d33c9ae1e001510f179a91168588f1aedaa9", "size": 18765, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.4.tgz", "integrity": "sha512-5f3k2PbGGp+YtKJjOItpg3P99IMD84E4HOvcfleTb5joCHNXYLsR9yWFPOYGgaeMPDubQILTCMdsFb2OMeOjtg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445535620687, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445535620687, "_cnpmcore_publish_time": "2021-12-13T08:16:33.152Z"}, "1.1.3": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.3", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "files": ["lib/sax.js", "LICENSE", "LICENSE-W3C.html", "README.md"], "devDependencies": {"tap": "^1.4.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "610a34d14107c2f80cc649480f576fe6db8b0f5a", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.3", "_shasum": "02fab475938311117f0e84a033380750da1a049e", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "02fab475938311117f0e84a033380750da1a049e", "size": 18782, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.3.tgz", "integrity": "sha512-CoeBkZN04mRUMoheR4LW020Xd4Njfp7AxYAA1nIH3yDk+18tMRVeuLaV+A8Ea0pb+1GeqKQHl/Kkpx3S+gjb7A=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1442276183256, "_hasShrinkwrap": false, "_cnpm_publish_time": 1442276183256, "_cnpmcore_publish_time": "2021-12-13T08:16:33.599Z"}, "1.1.2": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.2", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "afa7083425beafb4dd39829e006c52b5ef8cc4cd", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.2", "_shasum": "69271ffd151756bfc3f4a5f6ce2c84ea9f01fa37", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "69271ffd151756bfc3f4a5f6ce2c84ea9f01fa37", "size": 54809, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.2.tgz", "integrity": "sha512-+4tJSHjyQ4arGOycctQ/tSmmTrQVRqWLM9/8LWcZUS+rt5t/XUeArq/572ZKe/LP5C7QiMmsY9jKjFzDmYCQjA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1440627886354, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440627886354, "_cnpmcore_publish_time": "2021-12-13T08:16:34.019Z"}, "1.1.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.1", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "d5055a0ec458449c091ae47eb738c439a2688912", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.1", "_shasum": "f34427f9743559e269716735c01e18f0d7af1152", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f34427f9743559e269716735c01e18f0d7af1152", "size": 54388, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.1.tgz", "integrity": "sha512-qDRqSG5VF963HxgSpTGaPDyBhdCUmQyvWBqADM6xT1GAHhxXAiUGRLId/jo00l8z77e7hWoUo5PotzUo46kFPw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432106187722, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432106187722, "_cnpmcore_publish_time": "2021-12-13T08:16:34.411Z"}, "1.1.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.1.0", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "8ac6e401b5bd8c9e93c0dd6a800e59be9b7373b1", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.1.0", "_shasum": "aeff8e5f60334d120bce8b20b9e57f1d8df8e590", "_from": ".", "_npmVersion": "2.8.1", "_nodeVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "aeff8e5f60334d120bce8b20b9e57f1d8df8e590", "size": 55086, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.1.0.tgz", "integrity": "sha512-+8000y24JRK9/YHFkC9k9e0r00UfXdvxJIlH+DJDtkxuwyE+0U+j3CHPNpsmTaKrn5jAR9d02CY1Q5S9gGa8kw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1429656873327, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429656873327, "_cnpmcore_publish_time": "2021-12-13T08:16:34.849Z"}, "1.0.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.0.0", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "004da3f3fdf87b1f074e9aa4452f12ab749174e1", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_id": "sax@1.0.0", "_shasum": "b2aaee66454cd34dbbb63ae3c1d2bc60672e8d5a", "_from": ".", "_npmVersion": "2.8.1", "_nodeVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b2aaee66454cd34dbbb63ae3c1d2bc60672e8d5a", "size": 54926, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-1.0.0.tgz", "integrity": "sha512-1uF0Km+GPVRu5YkzBM30/pzlAQFNffI8iMxHtiqdlMaSu25auvo+0SkriIAeGNxN40L0T00YfUbfr+P4lMspLA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1429580975272, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429580975272, "_cnpmcore_publish_time": "2021-12-13T08:16:35.371Z"}, "0.6.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.6.1", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "d6f66de8782a00e5f72eb1b3b13e5796eddcf121", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js", "_id": "sax@0.6.1", "_shasum": "563b19c7c1de892e09bfc4f2fc30e3c27f0952b9", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "563b19c7c1de892e09bfc4f2fc30e3c27f0952b9", "size": 54191, "noattachment": false, "tarball": "https://registry.npmmirror.com/sax/-/sax-0.6.1.tgz", "integrity": "sha512-8ip+qnRh7m8OEyvoM1JoSBzlrepp3ajVR8nqgrfTig+TewfyvTijl0am8/anFqgbcdz62ofEUKE1hHNDCdbeSQ=="}, "directories": {}, "publish_time": 1413308132436, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413308132436, "_cnpmcore_publish_time": "2021-12-13T08:16:35.877Z"}, "0.6.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.6.0", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js", "_id": "sax@0.6.0", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.6.0.tgz", "shasum": "7a155519b712e3ec56f102ab984f15e15d3859f0", "size": 53831, "noattachment": false, "integrity": "sha512-00d3w3Dfj6e1mwDqVyZZE6CK8AESrSwQgZamCdXpsOX9HEb6RjTbrjEmTYEk0o/BtbWGHWJ/qCg0/7hRX66nsA=="}, "_from": ".", "_npmVersion": "1.3.22", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1388680320285, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388680320285, "_cnpmcore_publish_time": "2021-12-13T08:16:36.335Z"}, "0.5.8": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.8", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js", "_id": "sax@0.5.8", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.8.tgz", "shasum": "d472db228eb331c2506b0e8c15524adb939d12c1", "size": 53001, "noattachment": false, "integrity": "sha512-c0YL9VcSfcdH3F1Qij9qpYJFpKFKMXNOkLWFssBL3RuF7ZS8oZhllR2rWlCRjDTJsfq3R6wbSsaRU6o0rkEdNw=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386549492647, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386549492647, "_cnpmcore_publish_time": "2021-12-13T08:16:36.834Z"}, "0.5.7": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.7", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js", "_id": "sax@0.5.7", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.7.tgz", "shasum": "6b1f0fc9947ef2f2ffda8573e85899cd3269e654", "size": 52866, "noattachment": false, "integrity": "sha512-vXH0L9q/xQLVBGRDLm8PWaFwSzhONZQX0hgVa93Vo0PJfesXH3xU6mOvPxoQgNezKF3x8MVT9TEhqmSuzf/4KA=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386287402652, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386287402652, "_cnpmcore_publish_time": "2021-12-13T08:16:37.338Z"}, "0.5.6": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.6", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js", "_id": "sax@0.5.6", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.6.tgz", "shasum": "9da8f59fa7d80ea441acd4ef26d6212d23caae47", "size": 52506, "noattachment": false, "integrity": "sha512-joTY5gdjs0zRbZcqDTGU+hJG6pEdPe708bHq4KWN9bOPfwsHcDcL14RuSyFxTpM33a2pVq9T8VXGVg+wgykE2w=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386287013631, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386287013631, "_cnpmcore_publish_time": "2021-12-13T08:16:37.805Z"}, "0.5.5": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.5", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "_id": "sax@0.5.5", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.5.tgz", "shasum": "b1ec13d77397248d059bcc18bb9530d8210bb5d3", "size": 52426, "noattachment": false, "integrity": "sha512-UPKDoysZjAEECCUdDAJ9quLlvrtoWJvWEQmNMN8L0IRXQf5qh7qXU1L8EdhFRJVU2cwxo6M9krbgOgXTYh+MtQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377101442555, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377101442555, "_cnpmcore_publish_time": "2021-12-13T08:16:38.445Z"}, "0.5.4": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.4", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "_id": "sax@0.5.4", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.4.tgz", "shasum": "a3a4e1a9cf182bb547156c5232a49a1c3732ff7d", "size": 52060, "noattachment": false, "integrity": "sha512-sLmrSp5nGXBjA8QqrjKcW7b3J8blz4ECgZyMCXOCBzjpvrT3rJYJ351z/OaCoCItsK9N0UHUOcWvUBcwNjHfeg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1366931594436, "_hasShrinkwrap": false, "_cnpm_publish_time": 1366931594436, "_cnpmcore_publish_time": "2021-12-13T08:16:39.026Z"}, "0.5.3": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.3", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "_id": "sax@0.5.3", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.3.tgz", "shasum": "3773714a0d9157caaa7302971efa5c6dcda552d6", "size": 51813, "noattachment": false, "integrity": "sha512-yYLxVGfMM+XDjJtOU+pg8iHEuP83bSh1fjotG+pfHVGKp24ibCFNBFTFSHhd2CmFsT7k71/2X2Cty1nNZZnnXg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1366666292371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1366666292371, "_cnpmcore_publish_time": "2021-12-13T08:16:39.651Z"}, "0.5.2": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.2", "main": "lib/sax.js", "license": "BSD", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "_id": "sax@0.5.2", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.2.tgz", "shasum": "735ffaa39a1cff8ffb9598f0223abdb03a9fb2ea", "size": 51997, "noattachment": false, "integrity": "sha512-C1SZ6m6CbGBMGPPDfmx/lsgvtQqfLwPq4aRJmrlusZ27CYPaza0IK0/c+uCdvYvyKAsAg0eJFoQwSES7xj4XqA=="}, "_from": ".", "_npmVersion": "1.2.12", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1361378085595, "_hasShrinkwrap": false, "_cnpm_publish_time": 1361378085595, "_cnpmcore_publish_time": "2021-12-13T08:16:40.320Z"}, "0.5.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.1", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "_id": "sax@0.5.1", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.1.tgz", "shasum": "920206438bd102f2f4fe2594ecd1b962907bb733", "size": 48160, "noattachment": false, "integrity": "sha512-HIR4gqGtpUCbDdkjSzgUukngFhL1euTob+JKdwLYd4DT0TMSeOu3SlYUwxZpL5ykBN2Q9ZgjBgEPiXXMiGoNKQ=="}, "_npmVersion": "1.1.70", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1357593105506, "_hasShrinkwrap": false, "_cnpm_publish_time": 1357593105506, "_cnpmcore_publish_time": "2021-12-13T08:16:41.052Z"}, "0.5.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.5.0", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "_id": "sax@0.5.0", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.5.0.tgz", "shasum": "dfe3f476a2076ca1a8be1bd1d9efb2df264158a4", "size": 51946, "noattachment": false, "integrity": "sha512-F0PyiofB+dEI+8I7n9FhHg/fYp2/U1G7uCKtzagJsLb95yIA025dLBLdKYKyVzL2Qdx7noXg7uvo+XhYt0dAAQ=="}, "_npmVersion": "1.1.70", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1357498297546, "_hasShrinkwrap": false, "_cnpm_publish_time": 1357498297546, "_cnpmcore_publish_time": "2021-12-13T08:16:41.689Z"}, "0.4.3": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.4.3", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "_id": "sax@0.4.3", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.4.3.tgz", "shasum": "700e3a34eb2e792ce380791c71280f3731965ddc", "size": 45181, "noattachment": false, "integrity": "sha512-WvnHpLKMuEsJFV3LXuvxKY4sLdKev2tIeUxn+ljlQAhpx4ZEmJOW+nEa0uERX7XvfxoimvXvEqeo94p2jXoL6g=="}, "_npmVersion": "1.1.70", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1356673478410, "_hasShrinkwrap": false, "_cnpm_publish_time": 1356673478410, "_cnpmcore_publish_time": "2021-12-13T08:16:42.281Z"}, "0.4.2": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.4.2", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "_id": "sax@0.4.2", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.4.2.tgz", "shasum": "39f3b601733d6bec97105b242a2a40fd6978ac3c", "size": 45170, "noattachment": false, "integrity": "sha512-6Zsl4gnHjiTQfrOzsWdc0bHJepF5KJAVuM5fcyEJrqGyszkx2c55IclWP4D692rJrl1w0tExhbvYKjKNZl5wHg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1343275462445, "_hasShrinkwrap": false, "_cnpm_publish_time": 1343275462445, "_cnpmcore_publish_time": "2021-12-13T08:16:42.890Z"}, "0.4.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.4.0", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "_id": "sax@0.4.0", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.4.0.tgz", "shasum": "25dbcfa3ad15058152887ef5cf615e6d09ac6ec1", "size": 44850, "noattachment": false, "integrity": "sha512-EJUCh9gvesfyU9iIOvejRipk6jBgq8os3uq8QDOXb40d48YGnU1G+1ARpQuwlqN+xLLipiPmFuk309F2sr/J+A=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341948432052, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341948432052, "_cnpmcore_publish_time": "2021-12-13T08:16:43.470Z"}, "0.4.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.4.1", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "_id": "sax@0.4.1", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.4.1.tgz", "shasum": "424344ac8b8bf03ee14cf3ea1e5883858fb11318", "size": 44924, "noattachment": false, "integrity": "sha512-hKjg0ffc46yYstClEupeai/N/aYdYmMH6lODmQEUoc3I5Wsqnn5YhQmTj49d8dB/u34H5Zk75NsveZqsGrIyPQ=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341948405999, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341948405999, "_cnpmcore_publish_time": "2021-12-13T08:16:44.206Z"}, "0.3.5": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.3.5", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.3.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-7", "_nodeVersion": "v0.6.7-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.3.5.tgz", "shasum": "88fcfc1f73c0c8bbd5b7c776b6d3f3501eed073d", "size": 44609, "noattachment": false, "integrity": "sha512-b8euO9HV4Tf3a9lIfIb/uHQ6FHaw/wxYgASH8Q/pssuF+zifxzfnlMHHJTN94bhYbMKOF2ZWLMBscp6KKR2kmw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1325217374892, "_hasShrinkwrap": false, "_cnpm_publish_time": 1325217374892, "_cnpmcore_publish_time": "2021-12-13T08:16:44.866Z"}, "0.3.4": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.3.4", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.3.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.6.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.3.4.tgz", "shasum": "3689cd4862c6807d844e9bacacdf816cbde322fe", "size": 44348, "noattachment": false, "integrity": "sha512-UYoqb5Ont7Z2zXwa9Ar7e+nVP6Zt5lnjgWMh8caHwiXqIF0Sidwo465P0DJ1oLqgBwM7KlMrtVrvPGDdxJRrFA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1323062141943, "_hasShrinkwrap": false, "_cnpm_publish_time": 1323062141943, "_cnpmcore_publish_time": "2021-12-13T08:16:45.536Z"}, "0.3.3": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "0.3.3", "main": "lib/sax.js", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.3.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.3.3.tgz", "shasum": "e9228a36fee4819d0e57db18917c0eb7864db2aa", "size": 51200, "noattachment": false, "integrity": "sha512-G+mwd/8ELKq2+AKR2zJDJkATK8QGYVYVTzi4qDzJaT2dUkdQ7XMH/xl5i+peee5V1w/wxGbf9EbMb6+72FU8Fw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321029840293, "_hasShrinkwrap": false, "_cnpm_publish_time": 1321029840293, "_cnpmcore_publish_time": "2021-12-13T08:16:46.178Z"}, "0.3.2": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.3.2", "main": "lib/sax", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.3.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.101", "_nodeVersion": "v0.5.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.3.2.tgz", "shasum": "111f2ea7d78fed4228c38b8f0a9b7d203a97f168", "size": 30720, "noattachment": false, "integrity": "sha512-G9sSbz8ko60XAaO5MJbouV8dnujV2CjJ+Zib+eFqPvU2mLTTbnx4kmquF0UrsoaTlC1aI3daV/nO3flZJd3PNA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1319235300311, "_hasShrinkwrap": false, "_cnpm_publish_time": 1319235300311, "_cnpmcore_publish_time": "2021-12-13T08:16:46.853Z"}, "0.3.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.3.1", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.3.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.100", "_nodeVersion": "v0.5.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.3.1.tgz", "shasum": "aa504752f25fb6bec4f6355647eb6dfd59c311d3", "size": 30720, "noattachment": false, "integrity": "sha512-yIH83jEVN3/eTHRV5UBIzS7w55zrhFD182ggpRsb5SlqNHK119b7tLw4y8ed6CDW6dddPfvQfOj8a5lDd/CHeA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1319155476763, "_hasShrinkwrap": false, "_cnpm_publish_time": 1319155476763, "_cnpmcore_publish_time": "2021-12-13T08:16:47.474Z"}, "0.3.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.3.0", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.3.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.99", "_nodeVersion": "v0.5.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.3.0.tgz", "shasum": "6ae2527801d4b1e9fc3ad4ea22419bf2ae4e73ab", "size": 30720, "noattachment": false, "integrity": "sha512-KdCRcfDZGjJx4846B7x6YjMzczT/n+kYLR3os+NsZoHJ00fhnYvylwmE2+lhOwS7nyk7IcI0lvb/u8yuAhXUeg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1318863728184, "_hasShrinkwrap": false, "_cnpm_publish_time": 1318863728184, "_cnpmcore_publish_time": "2021-12-13T08:16:48.176Z"}, "0.2.5": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.2.5", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "sax@0.2.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.94", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.2.5.tgz", "shasum": "52f0ec9d1ad844d71b0d8ada4ae73af4fe2ce34c", "size": 30720, "noattachment": false, "integrity": "sha512-CTL1efqJmU1K9Yq0HqfVMICK/uqJVRJ4vFTPVECc962FX9ofqtiR3a4nRoybXRLOhdUVt3oL++/4Q8GcaSBI1g=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1317921229723, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317921229723, "_cnpmcore_publish_time": "2021-12-13T08:16:48.846Z"}, "0.2.4": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.2.4", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.2.4/package/package.json", "wscript": false, "serverjs": false, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]}, "_id": "sax@0.2.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.30", "_nodeVersion": "v0.5.7-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.2.4.tgz", "shasum": "f8ea0da594d1580e601b46c606981ffb0045da13", "size": 25428, "noattachment": false, "integrity": "sha512-iXaO+fVLj+hBFycOIrHGjVoM1ZLy/M1ZX3gpbQYPtL4ilWkgmZ7oJNc1B2JkEB5WR9Y+tyB649H89YbtZ7pb+g=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1316221131899, "_hasShrinkwrap": false, "_cnpm_publish_time": 1316221131899, "_cnpmcore_publish_time": "2021-12-13T08:16:49.726Z"}, "0.2.3": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.2.3", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.2.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "sax@0.2.3", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.2.3.tgz", "shasum": "bf474651670d0d0dcfcfc4b2a5f83d5e43dc8ea1", "size": 23106, "noattachment": false, "integrity": "sha512-i3O+0nqm/Yd3CJ9jFa0RLKq+lmGUkKmGNFOYDuKdyHlDQUbhWa17OvDsH1/K2FurpEFh/bgifwZM1jfaqvzvYw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1312217512531, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312217512531, "_cnpmcore_publish_time": "2021-12-13T08:16:50.478Z"}, "0.2.2": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.2.2", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.2.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "sax@0.2.2", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.2.2.tgz", "shasum": "14b2ae3579988681809918a995a737bc2ca0a843", "size": 22901, "noattachment": false, "integrity": "sha512-y6CEgtjXfNaGb2t5KA+/mpncfQEk2r1pkHRf7AaeSAeo3WW7KXQb/RK1XAnrkAigyB7YC4N6N2wpi7PMdH2RuA=="}, "directories": {}, "publish_time": 1311279416686, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311279416686, "_cnpmcore_publish_time": "2021-12-13T08:16:51.210Z"}, "0.2.1": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.2.1", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.2.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "sax@0.2.1", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.2.1.tgz", "shasum": "817de986952fbc0213cf2602970919d3cb958f1f", "size": 22731, "noattachment": false, "integrity": "sha512-yqcLYxdW2pwmwtNRLinCclBeBykOYbz1PqjvZj3XHMAYXzKkazQd2bA7QVLhotBtQ60/cdUaaEylhEJZacUOzQ=="}, "directories": {}, "publish_time": 1311212048297, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311212048297, "_cnpmcore_publish_time": "2021-12-13T08:16:52.022Z"}, "0.2.0": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.2.0", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.2.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "sax@0.2.0", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.2.0.tgz", "shasum": "f3f0088400db13a618f81b5abc9a361b5355d60e", "size": 22464, "noattachment": false, "integrity": "sha512-gwnGjbU25980poKTnmdDnd/djmpr/IGBXY3JiBtXpSqdtXwmVeXTx8aBQuMF6rBMUzmpleY2nBB+Jdf8vahEhA=="}, "directories": {}, "publish_time": 1311206030073, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311206030073, "_cnpmcore_publish_time": "2021-12-13T08:16:52.844Z"}, "0.1.5": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.1.5", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.1.5/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "sax@0.1.5", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.1.5.tgz", "shasum": "d1829a6120fa01665eb4dbff6c43f29fd6d61471", "size": 21874, "noattachment": false, "integrity": "sha512-w+8N4kgEKvjp/f5wpSPE62jrNN/839sGE0pa3+oZUjEFGiV/VbA3moJN7VxFnPuZnjI770VSMaYwGzU/SsIHdQ=="}, "directories": {}, "publish_time": 1310168979930, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1310168979930, "_cnpmcore_publish_time": "2021-12-13T08:16:53.679Z"}, "0.1.4": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.1.4", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/sax/0.1.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "sax@0.1.4", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.13", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.1.4.tgz", "shasum": "32bdddfe10ed1f1635f26306d7f304666a8841f7", "size": 21699, "noattachment": false, "integrity": "sha512-zAwH7R5B4m9YGYOCfOAQTjQgcpHtS2mSKwisSvvqmDs37qY+k5SGVFXJcNoJLcWS7S6g3wiNpMZEvN9+O7J0yg=="}, "directories": {}, "publish_time": 1308077083566, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1308077083566, "_cnpmcore_publish_time": "2021-12-13T08:16:54.551Z"}, "0.1.3": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.1.3", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_id": "sax@0.1.3", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.1.3.tgz", "shasum": "f06bbbfc11947497fd1e354e963eeb07ac52992c", "size": 31280, "noattachment": false, "integrity": "sha512-hUVmbbL/3gJ10UpLdcfq5ZqonozxgMC7RlP1wBORcukngeckqOS8jMZ80U31oitYa50N1uT5/YsAarcbLW4cyg=="}, "directories": {}, "publish_time": 1308014778836, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1308014778836, "_cnpmcore_publish_time": "2021-12-13T08:16:55.446Z"}, "0.1.2": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.1.2", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_id": "sax@0.1.2", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.0", "_nodeVersion": "v0.4.0-pre", "directories": {"lib": "./lib"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.1.2.tgz", "shasum": "781a6a19c3475563aa03da4ce37ccb8b3113d61a", "size": 20884, "noattachment": false, "integrity": "sha512-SuvBF94IG5GqbxE+CZJC85S2pxMyAobWa2iJLhyZkgmOg0ZNehyyicxT1NGkyO0Og2Gr9L5b94TS/E9E2mMwkQ=="}, "publish_time": 1297278831969, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297278831969, "_cnpmcore_publish_time": "2021-12-13T08:16:56.394Z"}, "0.1.1": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.1.1", "main": "lib/sax", "license": "MIT", "scripts": {"test": "node test/index.js"}, "_id": "sax@0.1.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.1.1.tgz", "shasum": "8fbfda000c2d03f6ab946d6acbbfca13ff7f32bf", "size": 21896, "noattachment": false, "integrity": "sha512-Ai+AtDgJw1fvUoUHwZO2TrI/3R0G76Y3jQ0Bf0riZL57ZPr7QvOm2FBe44S51C+qXoJjT2WKY0bZ60diGZsFZw=="}, "directories": {}, "publish_time": 1297278831969, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297278831969, "_cnpmcore_publish_time": "2021-12-13T08:16:57.308Z"}, "0.1.0": {"name": "sax", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.1.0", "main": "lib/sax", "scripts": {"test": "node test/index.js"}, "_id": "sax@0.1.0", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/sax/-/sax-0.1.0.tgz", "shasum": "eda2e16460d518b917dcd23b78e26f57e7c66810", "size": 19290, "noattachment": false, "integrity": "sha512-l9pP1YQvv5USkbeecL8elwiX3NJW1RBHS2toceBfhRcY1tny+pEekR4x0aMJYKAhOHvHSuB2k32NUCHFH+TkmA=="}, "directories": {}, "publish_time": 1297278831969, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297278831969, "_cnpmcore_publish_time": "2021-12-13T08:16:58.195Z"}, "1.3.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.3.0", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "devDependencies": {"tap": "^15.1.6"}, "tap": {"statements": 79, "branches": 75, "functions": 80, "lines": 79}, "_id": "sax@1.3.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "3af995934cbbb4edaea54dd3cc5e9c25150ddb04", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_nodeVersion": "20.7.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==", "shasum": "a5dbe77db3be05c9d1ee7785dbd3ea9de51593d0", "tarball": "https://registry.npmmirror.com/sax/-/sax-1.3.0.tgz", "fileCount": 4, "unpackedSize": 54961, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDd8C4X5oh5dycGrXYz4MfaR9SFH775EBY5CHv+PfmO4wIgauZClQP9uXM2bLs0xSmgiW4ukaJCL41AFY3FZHl3FZM="}], "size": 15346}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sax_1.3.0_1695757553897_0.36578396620741316"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T19:45:54.056Z", "publish_time": 1695757554056, "_source_registry_name": "default"}, "1.4.0": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.4.0", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "devDependencies": {"tap": "^15.1.6"}, "tap": {"statements": 79, "branches": 75, "functions": 80, "lines": 79}, "_id": "sax@1.4.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "afda2e95ba23f5e633d5c1053d45fd85d88ed026", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-G3nn4N8SRaR9NsCqEUHfTlfTM/Fgza1yfb8JP2CEmzYuHtHWza5Uf+g7nuUQq96prwu0GiGyPgDw752+j4fzQQ==", "shasum": "829d8c11eb6a8728d3b86cdeea4fc3893342b5e7", "tarball": "https://registry.npmmirror.com/sax/-/sax-1.4.0.tgz", "fileCount": 4, "unpackedSize": 55877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7tlNWXBRpE4gxVtd9vcnMorhn/Bw070+yaL7GXY/x2wIgRBBpah48Oj6wIavUGfMV5hqEPVX1Srz8pM6hXE7TbJ8="}], "size": 15607}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sax_1.4.0_1716832084481_0.07306194391151255"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-27T17:48:04.658Z", "publish_time": 1716832084658, "_source_registry_name": "default"}, "1.4.1": {"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "version": "1.4.1", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "devDependencies": {"tap": "^15.1.6"}, "tap": {"statements": 79, "branches": 75, "functions": 80, "lines": 79}, "_id": "sax@1.4.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "25ab118a3184d2070495e7eecf2e762f9044442c", "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "homepage": "https://github.com/isaacs/sax-js#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==", "shasum": "44cc8988377f126304d3b3fc1010c733b929ef0f", "tarball": "https://registry.npmmirror.com/sax/-/sax-1.4.1.tgz", "fileCount": 4, "unpackedSize": 56017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF6wMIS1WmHqWNHm1XzUplDOkDjhFLBfYMSdoODtCREwAiAa+iWClIL0cds4JQEYXxkQvtN3UBm15tAuad7xmLt+CA=="}], "size": 15623}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sax_1.4.1_1716853577253_0.7073672012775203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-27T23:46:17.431Z", "publish_time": 1716853577431, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/sax-js#readme", "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "_source_registry_name": "default"}