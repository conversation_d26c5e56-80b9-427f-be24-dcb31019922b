{"_attachments": {}, "_id": "frac", "_rev": "401966-61f1ff5c1d755a05a219b6f5", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "dist-tags": {"latest": "1.1.2"}, "license": "Apache-2.0", "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "name": "frac", "readme": "# frac\n\nRational approximation to a floating point number with bounded denominator.\n\nUses the [Mediant Method](https://en.wikipedia.org/wiki/Mediant_method).\n\nThis module also provides an implementation of the continued fraction method as\ndescribed by <PERSON><PERSON> in \"A method for exact computation with rational numbers\".\nThe algorithm is used in <a href=\"http://sheetjs.com\">SheetJS Libraries</a> to\nreplicate fraction formats.\n\n## Installation\n\n### JS\n\nWith [`npm`](https://www.npmjs.org/package/frac):\n\n```bash\n$ npm install frac\n```\n\nIn the browser:\n\n```html\n<script src=\"frac.js\"></script>\n```\n\nThe script will manipulate `module.exports` if available .  This is not always\ndesirable.  To prevent the behavior, define `DO_NOT_EXPORT_FRAC`\n\n### Python\n\nFrom [`PyPI`](https://pypi.python.org/pypi/frac):\n\n```bash\n$ pip install frac\n```\n\n## Usage\n\nIn all cases, the relevant function takes 3 arguments:\n\n - `x` the number we wish to approximate\n - `D` the maximum denominator\n - `mixed` if true, return a mixed fraction; if false, improper\n\nThe return value is an array of the form `[quot, num, den]` where `quot==0`\nfor improper fractions.  `quot <= x` for mixed fractions, which may lead to some\nunexpected results when rendering negative numbers.\n\n### JS\n\nThe exported `frac` function implements the Mediant method.\n\n`frac.cont` implements the Aberth algorithm\n\nFor example:\n\n```js\n> // var frac = require('frac'); // uncomment this line if in node\n> frac(1.3, 9);              // [  0,  9, 7 ] //  1.3 ~       9/7\n> frac(1.3, 9, true);        // [  1,  2, 7 ] //  1.3 ~  1 +  2/7\n> frac(-1.3, 9);             // [  0, -9, 7 ] // -1.3 ~      -9/7\n> frac(-1.3, 9, true);       // [ -2,  5, 7 ] // -1.3 ~ -2 +  5/7\n\n> frac.cont(1.3, 9);         // [  0,  4, 3 ] //  1.3 ~       4/3\n> frac.cont(1.3, 9, true);   // [  1,  1, 3 ] //  1.3 ~  1 +  1/3\n> frac.cont(-1.3, 9);        // [  0, -4, 3 ] // -1.3 ~      -4/3\n> frac.cont(-1.3, 9, true);  // [ -2,  2, 3 ] // -1.3 ~ -2 +  2/3\n```\n\n\n### Python\n\n`frac.med` implements Mediant method.\n\n`frac.cont` implements Aberth algorithm.\n\nFor example:\n\n```py\n>>> import frac\n>>> frac.med(1.3, 9)         ## [  0,  9, 7 ] ##  1.3 ~       9/7\n>>> frac.med(1.3, 9, True)   ## [  1,  2, 7 ] ##  1.3 ~  1 +  2/7\n>>> frac.med(-1.3, 9)        ## [  0, -9, 7 ] ## -1.3 ~      -9/7\n>>> frac.med(-1.3, 9, True)  ## [ -2,  5, 7 ] ## -1.3 ~ -2 +  5/7\n\n>>> frac.cont(1.3, 9)        ## [  0,  4, 3 ] ##  1.3 ~       4/3\n>>> frac.cont(1.3, 9, True)  ## [  1,  1, 3 ] ##  1.3 ~  1 +  1/3\n>>> frac.cont(-1.3, 9)       ## [  0, -4, 3 ] ## -1.3 ~      -4/3\n>>> frac.cont(-1.3, 9, True) ## [ -2,  2, 3 ] ## -1.3 ~ -2 +  2/3\n```\n\n## Testing\n\nThe test TSV baselines in the `test_files` directory have four columns:\n\n- Column A contains the raw values\n- Column B format \"Up to one digit (1/4)\" (`denominator = 9`)\n- Column C format \"Up to two digits (21/25)\" (`denominator = 99`)\n- Column D format \"Up to three digits (312/943)\" (`denominator = 999`)\n\n`make test` will run the node-based tests.\n\n`make pytest` will run the python tests against the system Python version.\n\n`make pypytest` will run the python tests against `pypy` if installed\n\n## License\n\nPlease consult the attached LICENSE file for details.  All rights not explicitly\ngranted by the Apache 2.0 License are reserved by the Original Author.\n\n## Badges\n\n[![Build Status](https://saucelabs.com/browser-matrix/frac.svg)](https://saucelabs.com/u/frac)\n\n[![Build Status](https://travis-ci.org/SheetJS/frac.svg?branch=master)](https://travis-ci.org/SheetJS/frac)\n\n[![Coverage Status](http://img.shields.io/coveralls/SheetJS/frac/master.svg)](https://coveralls.io/r/SheetJS/frac?branch=master)\n\n[![NPM Downloads](https://img.shields.io/npm/dt/frac.svg)](https://npmjs.org/package/frac)\n\n[![Dependencies Status](https://david-dm.org/sheetjs/frac/status.svg)](https://david-dm.org/sheetjs/frac)\n\n[![ghit.me](https://ghit.me/badge.svg?repo=sheetjs/js-xlsx)](https://ghit.me/repo/sheetjs/js-xlsx)\n\n[![Analytics](https://ga-beacon.appspot.com/***********-1/SheetJS/frac?pixel)](https://github.com/SheetJS/frac)\n", "time": {"created": "2022-01-27T02:11:40.439Z", "modified": "2023-05-12T18:15:16.713Z", "1.1.2": "2018-02-20T21:06:16.836Z", "1.1.1": "2018-01-19T05:13:19.743Z", "1.1.0": "2017-07-28T03:39:24.120Z", "1.0.6": "2017-04-29T17:34:27.059Z", "1.0.5": "2016-09-23T05:45:45.564Z", "1.0.4": "2016-01-16T06:46:34.747Z", "1.0.2": "2015-05-05T06:59:01.224Z", "1.0.1": "2015-04-22T05:45:30.092Z", "1.0.0": "2014-05-01T03:22:23.061Z", "0.3.1": "2014-01-12T16:53:49.492Z", "0.3.0": "2014-01-09T09:26:00.971Z", "0.2.1": "2013-12-26T04:14:34.602Z"}, "versions": {"1.1.2": {"name": "frac", "version": "1.1.2", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "./frac", "types": "types", "dependencies": {}, "devDependencies": {"voc": "~1.1.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "frac.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "4d295d7900ed6541d214ea1f81e479f9d2880fa3", "_id": "frac@1.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "3d74f7f6478c88a1b5020306d747dc6313c74d0b", "size": 4627, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz", "integrity": "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/frac_1.1.2_1519160776799_0.7796025952428534"}, "_hasShrinkwrap": false, "publish_time": 1519160776836, "_cnpm_publish_time": 1519160776836, "_cnpmcore_publish_time": "2021-12-16T11:32:25.511Z"}, "1.1.1": {"name": "frac", "version": "1.1.1", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "./frac", "types": "types", "dependencies": {"voc": "~1.0.0"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "frac.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9ef2c47f0408a538aa263ef9ff3352197a814257", "_id": "frac@1.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5f7f5c2ae029167d90153eea4b7dd2958d43d1f5", "size": 7166, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.1.1.tgz", "integrity": "sha512-UKV4N/r4PQQgdjrmiI5XOv+dII8OgwXQaEIUNj/vH1KtvaTwIBRwVobAWwEKPK7HLJmSwru/1a1A88TB/DtxZg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/frac-1.1.1.tgz_1516338799659_0.6169088410679251"}, "directories": {}, "publish_time": 1516338799743, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516338799743, "_cnpmcore_publish_time": "2021-12-16T11:32:25.705Z"}, "1.1.0": {"name": "frac", "version": "1.1.0", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": "~1.0.0"}, "devDependencies": {"mocha": "~2.5.3"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "frac.js"}}, "homepage": "http://oss.sheetjs.com/frac", "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "4cb7279d629dd4dd3d9cbe4aba9ff69375280187", "_id": "frac@1.1.0", "_shasum": "dc437e9c6a646b60b127d82ac4902464445cc1e3", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "dc437e9c6a646b60b127d82ac4902464445cc1e3", "size": 6728, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.1.0.tgz", "integrity": "sha512-B7gZ1CmLtadr6bzgrT7s5T4WdcCC++s46KtjTsVd+dB6RQDjHxASbCSRqe7+uWezas7x7uZWhVuoL+DDpUz3GQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/frac-1.1.0.tgz_1501213163212_0.19437494757585227"}, "directories": {}, "publish_time": 1501213164120, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501213164120, "_cnpmcore_publish_time": "2021-12-16T11:32:25.935Z"}, "1.0.6": {"name": "frac", "version": "1.0.6", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "frac.js"}}, "homepage": "http://oss.sheetjs.com/frac", "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "395ea31e951ac1863484fb4df34f1f0469cd0ea3", "_id": "frac@1.0.6", "_shasum": "9a0dfc23956852a8b320623bebcf1be9ea048229", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "9a0dfc23956852a8b320623bebcf1be9ea048229", "size": 6683, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.0.6.tgz", "integrity": "sha512-RXTHy3vhoqGKWLkk88tlD0n4d+lFyl5Oa1t1DsyHpR7DkXklWnPwnKhdCM+3kDR5fQuxJ3pr6dZ3yuG1YWGdQQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/frac-1.0.6.tgz_1493487266407_0.5516871849540621"}, "directories": {}, "publish_time": 1493487267059, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493487267059, "_cnpmcore_publish_time": "2021-12-16T11:32:26.197Z"}, "1.0.5": {"name": "frac", "version": "1.0.5", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test"}, "config": {"blanket": {"pattern": "frac.js"}}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "93ff7f307a73c8d5cac345e5172f5b87c5c7cc68", "_id": "frac@1.0.5", "_shasum": "8a7fa0c6e9e2aa6e86403abee4aa9d9d3841d701", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "8a7fa0c6e9e2aa6e86403abee4aa9d9d3841d701", "size": 10263, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.0.5.tgz", "integrity": "sha512-ndKoxqmWFz242nI0mCEJIn8bd3NomOtSZgvOSod/z9IeJEDk9sCX69niMXdnYYl7BJ29j5SPMq2ujyYs0E2N1w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/frac-1.0.5.tgz_1474609544659_0.1393533602822572"}, "directories": {}, "publish_time": 1474609545564, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474609545564, "_cnpmcore_publish_time": "2021-12-16T11:32:26.416Z"}, "1.0.4": {"name": "frac", "version": "1.0.4", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "mocha -R spec"}, "config": {"blanket": {"pattern": "frac.js"}}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "63e6cf4bb36be874f0a46bc9d0620df3b2b8619e", "homepage": "https://github.com/SheetJS/frac", "_id": "frac@1.0.4", "_shasum": "758907e5c6f7fc206dab14019dce1fb945427388", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.9", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "758907e5c6f7fc206dab14019dce1fb945427388", "size": 9383, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.0.4.tgz", "integrity": "sha512-RgVEEyICFX2E6jXBrPoQcepcqQO/0RcYPB6lyyPhGupWh1eHSoJRub4TAL/Etf/g3Zju43zHIOYM+irlBQpqNQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452926794747, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452926794747, "_cnpmcore_publish_time": "2021-12-16T11:32:26.627Z"}, "1.0.2": {"name": "frac", "version": "1.0.2", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "mocha -R spec"}, "config": {"blanket": {"pattern": "frac.js"}}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "9a9ed4d1fab9e05b56d2e7ae921325a2ad506330", "homepage": "https://github.com/SheetJS/frac", "_id": "frac@1.0.2", "_shasum": "efff143c5c6cf9438a2e59d06bbb4a3e1c318559", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "efff143c5c6cf9438a2e59d06bbb4a3e1c318559", "size": 9411, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.0.2.tgz", "integrity": "sha512-0qxJ3YAyq28OilQfWkIKfV/A/5Npr2FO6vVGzkxWhVp42FJIFIplpjrQtRUVLk5a661h+VgGicm6bGLtL7o1YQ=="}, "directories": {}, "publish_time": 1430809141224, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430809141224, "_cnpmcore_publish_time": "2021-12-16T11:32:26.811Z"}, "1.0.1": {"name": "frac", "version": "1.0.1", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "mocha -R spec"}, "config": {"blanket": {"pattern": "frac.js"}}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "2f54d2f5c1ba345b08c75f6101b6599630a2d4df", "homepage": "https://github.com/SheetJS/frac", "_id": "frac@1.0.1", "_shasum": "93bfed457e7ce44ea82a580d8890c39c650c6ed0", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "93bfed457e7ce44ea82a580d8890c39c650c6ed0", "size": 8481, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.0.1.tgz", "integrity": "sha512-ECOe3xGh4yDrXumpNPpLMT6ya2S2jmmUWUWU6h6UxQsWvApz7Si2rCSOg4UZ3MAWED0N0DVZwDZDEmIRdTyarQ=="}, "directories": {}, "publish_time": 1429681530092, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429681530092, "_cnpmcore_publish_time": "2021-12-16T11:32:27.049Z"}, "1.0.0": {"name": "frac", "version": "1.0.0", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "frac.js", "dependencies": {"voc": ""}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "mocha -R spec"}, "config": {"blanket": {"pattern": "frac.js"}}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/frac", "_id": "frac@1.0.0", "dist": {"shasum": "0d2264842ae8039858efea4c07bbb44fbf687110", "size": 5928, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-1.0.0.tgz", "integrity": "sha512-uY7TriXxNHBaofWVDNVI+s6AEZ4kp8aubkHn0wadrbkuLCsZyNzaVF55muVtcIIEGZXwzsXDVATpBilpQ9w5pw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398914543061, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398914543061, "_cnpmcore_publish_time": "2021-12-16T11:32:27.251Z"}, "0.3.1": {"name": "frac", "version": "0.3.1", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "./frac.js", "dependencies": {}, "devDependencies": {"mocha": "", "voc": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/frac", "_id": "frac@0.3.1", "dist": {"shasum": "577677b7fdcbe6faf7c461f1801d34137cda4354", "size": 4511, "noattachment": false, "tarball": "https://registry.npmmirror.com/frac/-/frac-0.3.1.tgz", "integrity": "sha512-1Lzf2jOjhIkRaa013KlxNOn2D9FemmQNeYUDpEIyPeFXmpLvbZXJOlaayMBT6JKXx+afQFgQ1QJ4kaF7Z07QFQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389545629492, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389545629492, "_cnpmcore_publish_time": "2021-12-16T11:32:27.455Z"}, "0.3.0": {"name": "frac", "version": "0.3.0", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "./frac.js", "dependencies": {}, "devDependencies": {"mocha": "", "voc": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/frac", "_id": "frac@0.3.0", "dist": {"tarball": "https://registry.npmmirror.com/frac/-/frac-0.3.0.tgz", "shasum": "3b2732e872d6c95659bb30298a8d95a33cc5f692", "size": 4434, "noattachment": false, "integrity": "sha512-DrallmYnH9NnWakhp3PxyTSqcrgV8XXSUbFLyCQWC5wUa5JHlcoDyLKymlraKEFXe4z6yECCE0/p7aNs3kTPXQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389259560971, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389259560971, "_cnpmcore_publish_time": "2021-12-16T11:32:27.676Z"}, "0.2.1": {"name": "frac", "version": "0.2.1", "author": {"name": "SheetJS"}, "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "./frac.js", "dependencies": {}, "devDependencies": {"mocha": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "engines": {"node": ">=0.8"}, "readmeFilename": "README.md", "homepage": "https://github.com/SheetJS/frac", "_id": "frac@0.2.1", "dist": {"tarball": "https://registry.npmmirror.com/frac/-/frac-0.2.1.tgz", "shasum": "27470b5850455b0b16b25522fc4dab464dc0ea18", "size": 1375337, "noattachment": false, "integrity": "sha512-ka7Z5755RrlB6oaDhK1LEBoIvoo8mVvJH7faLvpeQRIP3zKGJFd8oDdpn08I1vimwEe3nM9ZcZgoEWhcqWWJ9w=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1388031274602, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388031274602, "_cnpmcore_publish_time": "2021-12-16T11:32:30.828Z"}}, "_source_registry_name": "default"}