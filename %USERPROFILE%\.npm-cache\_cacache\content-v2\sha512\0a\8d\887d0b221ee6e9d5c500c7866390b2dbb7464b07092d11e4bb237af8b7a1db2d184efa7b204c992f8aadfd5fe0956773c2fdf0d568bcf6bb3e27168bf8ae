{"_id": "@vue/language-core", "_rev": "3522513-64586d8cc896423028e4446f", "dist-tags": {"latest": "3.0.1"}, "name": "@vue/language-core", "time": {"created": "2023-05-08T03:33:32.792Z", "modified": "2025-07-02T12:37:36.215Z", "1.7.0": "2023-05-08T03:14:36.853Z", "1.7.1": "2023-05-10T01:44:10.419Z", "1.7.3": "2023-05-13T07:36:52.396Z", "1.7.4": "2023-05-18T07:25:11.614Z", "1.7.5": "2023-05-18T16:46:29.143Z", "1.7.6": "2023-05-18T17:22:09.177Z", "1.7.7": "2023-05-22T00:46:20.568Z", "1.7.8": "2023-05-22T01:05:11.252Z", "1.7.9": "2023-06-08T17:17:16.209Z", "1.7.10": "2023-06-08T17:21:21.609Z", "1.7.11": "2023-06-08T18:34:27.542Z", "1.7.12": "2023-06-14T00:47:25.372Z", "1.7.13": "2023-06-14T16:50:00.806Z", "1.7.14": "2023-06-16T14:50:02.728Z", "1.8.0": "2023-06-16T22:58:11.841Z", "1.8.0-patch.1": "2023-06-18T01:25:03.950Z", "1.8.1": "2023-06-20T08:58:44.859Z", "1.8.2": "2023-06-26T18:09:30.734Z", "1.8.3": "2023-06-28T09:13:56.351Z", "1.8.4": "2023-07-05T04:51:55.498Z", "1.8.5": "2023-07-14T15:47:31.970Z", "1.8.6": "2023-07-21T18:11:52.797Z", "1.8.7": "2023-07-26T19:11:01.060Z", "1.8.8": "2023-07-26T20:47:47.102Z", "1.8.10": "2023-09-06T09:13:35.802Z", "1.8.11": "2023-09-12T19:15:11.629Z", "1.8.12": "2023-09-20T10:20:10.632Z", "1.8.13": "2023-09-20T13:19:00.997Z", "1.8.14": "2023-09-25T17:46:00.153Z", "1.8.15": "2023-09-26T11:18:11.349Z", "1.8.16": "2023-10-06T20:33:15.206Z", "1.8.17": "2023-10-08T17:42:58.784Z", "1.8.18": "2023-10-08T18:14:45.284Z", "1.8.19": "2023-10-11T09:57:29.660Z", "1.8.20": "2023-10-23T12:00:58.532Z", "1.8.21": "2023-10-25T10:47:54.930Z", "1.8.22": "2023-10-26T17:05:54.649Z", "1.9.0-alpha.0": "2023-10-31T11:41:37.299Z", "1.9.0-alpha.1": "2023-11-01T06:12:52.453Z", "1.9.0-alpha.2": "2023-11-01T06:48:53.952Z", "1.9.0-alpha.3": "2023-11-03T21:52:12.851Z", "1.8.24": "2023-11-29T09:41:08.026Z", "1.8.25": "2023-12-05T17:53:00.205Z", "1.8.26": "2023-12-21T16:51:12.531Z", "1.8.27": "2023-12-26T10:18:44.261Z", "2.0.0": "2024-03-01T21:14:20.170Z", "2.0.1": "2024-03-01T22:43:39.920Z", "2.0.2": "2024-03-02T07:37:11.710Z", "2.0.3": "2024-03-03T08:05:21.036Z", "2.0.4": "2024-03-04T04:19:14.258Z", "2.0.5": "2024-03-05T14:27:57.692Z", "2.0.6": "2024-03-07T05:53:46.979Z", "2.0.7": "2024-03-20T10:31:17.151Z", "2.0.10": "2024-04-04T15:06:32.673Z", "2.0.11": "2024-04-07T13:48:47.506Z", "2.0.12": "2024-04-10T09:39:29.015Z", "2.0.13": "2024-04-12T07:57:05.230Z", "2.0.14": "2024-04-22T01:45:40.403Z", "2.0.15": "2024-04-30T02:33:00.303Z", "2.0.16": "2024-05-01T03:38:38.106Z", "2.0.17": "2024-05-10T08:58:41.601Z", "2.0.18": "2024-05-15T12:06:12.327Z", "2.0.19": "2024-05-16T04:57:27.351Z", "2.0.20": "2024-06-08T07:33:29.913Z", "2.0.21": "2024-06-08T14:47:07.062Z", "2.0.22": "2024-06-22T13:27:03.703Z", "2.0.23-alpha.0": "2024-06-26T13:38:21.818Z", "2.0.23-alpha.1": "2024-06-26T16:40:53.150Z", "2.0.24": "2024-06-30T07:26:46.063Z", "2.0.26-alpha.0": "2024-07-02T02:13:29.329Z", "2.0.26-alpha.1": "2024-07-02T02:18:31.716Z", "2.0.26-alpha.2": "2024-07-02T04:59:04.625Z", "2.0.26": "2024-07-04T12:45:24.062Z", "2.0.28": "2024-07-22T06:35:30.437Z", "2.0.29": "2024-07-25T08:44:54.977Z", "2.1.0": "2024-08-29T00:29:56.047Z", "2.1.2": "2024-08-29T08:24:11.058Z", "2.1.4": "2024-08-31T18:56:14.184Z", "2.1.5": "2024-08-31T19:05:22.925Z", "2.1.6": "2024-09-04T19:07:22.133Z", "2.1.6-patch.1": "2024-09-13T11:34:19.434Z", "2.1.8": "2024-10-26T15:59:39.702Z", "2.1.10": "2024-10-30T19:02:20.642Z", "2.2.0": "2024-12-23T20:06:27.446Z", "2.2.2": "2025-02-15T16:08:00.011Z", "2.2.4": "2025-02-23T12:36:26.128Z", "2.2.6": "2025-03-01T09:54:47.304Z", "2.2.8": "2025-03-02T10:20:12.206Z", "3.0.0-alpha.0": "2025-03-07T21:31:47.944Z", "3.0.0-alpha.2": "2025-03-16T16:19:41.059Z", "3.0.0-alpha.4": "2025-04-08T14:35:57.733Z", "2.2.10": "2025-04-22T16:04:37.656Z", "3.0.0-alpha.6": "2025-05-05T09:06:11.392Z", "3.0.0-alpha.8": "2025-05-26T13:00:51.614Z", "3.0.0-alpha.10": "2025-06-07T16:24:45.975Z", "3.0.0-beta.1": "2025-06-14T22:45:57.492Z", "3.0.0-beta.2": "2025-06-15T15:17:54.737Z", "3.0.0-beta.3": "2025-06-22T09:58:32.284Z", "3.0.0-beta.4": "2025-06-25T13:36:18.633Z", "3.0.0-beta.5": "2025-06-27T18:07:32.752Z", "3.0.0": "2025-07-01T15:26:55.067Z", "2.2.12": "2025-07-02T00:57:15.733Z", "3.0.1": "2025-07-02T12:07:12.223Z"}, "versions": {"1.7.0": {"name": "@vue/language-core", "version": "1.7.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.5.4", "@volar/source-map": "1.5.4", "@vue/compiler-dom": "^3.3.0-beta.3", "@vue/compiler-sfc": "^3.3.0-beta.3", "@vue/reactivity": "^3.3.0-beta.3", "@vue/shared": "^3.3.0-beta.3", "minimatch": "^9.0.0", "muggle-string": "^0.2.2", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2"}, "types": "./out/index.d.ts", "gitHead": "f573d97a2b38e019f2b8803971e3ee853d5877c6", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.0", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-gaf2ZzA7TbbNCpmCUyM3k6ApPUf1xVINH31g5SC0t2VBriBF6+io2e4tNAd0q4kIm0NLM/yhIezFs2281juBng==", "shasum": "13603e36d1d9b326b789bb0299b7166953f856f1", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.0.tgz", "fileCount": 54, "unpackedSize": 234351, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA8fYIU/0gUghseogl3amcax7lWaj4LuvOsT7tDe5xHxAiEA858WxgCrr+/j4XzY/wc5SKdH5z3sWR6SYvxu/1XwSfM="}], "size": 42122}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.0_1683515676663_0.5856443352074636"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-08T03:14:36.853Z", "publish_time": 1683515676853, "_source_registry_name": "default"}, "1.7.1": {"name": "@vue/language-core", "version": "1.7.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.1", "@volar/source-map": "1.6.1", "@vue/compiler-dom": "^3.3.0-beta.3", "@vue/reactivity": "^3.3.0-beta.3", "@vue/shared": "^3.3.0-beta.3", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0-beta.3"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "8555895e2018cb871cceb19f0ff073fcf322d8e5", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-C+N4RWHYxx09uhJour/6Br2c3mqjol/zVUewCOKwFAG3CwIX7mWGRUCTpgeeBVtg1Evnr3h8JYP24fyy9VGMPA==", "shasum": "1f8c870259c70bf73f1135718a702ed310223b23", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.1.tgz", "fileCount": 54, "unpackedSize": 234351, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfqTaMAeplMDEj+OuWGJ233CrqQsQV6LIOk2RuikICGgIhANDINszUttp09YY73Wd4aU8dwkEyb9+AkKvxjQQrndzs"}], "size": 42998}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.1_1683683050153_0.30421287502711336"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-10T01:44:10.419Z", "publish_time": 1683683050419, "_source_registry_name": "default"}, "1.7.3": {"name": "@vue/language-core", "version": "1.7.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.3", "@volar/source-map": "1.6.3", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "0afe0eb39ed144513faddbfa4fddc667e8864cdf", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-VdFNOG/m0lwiy+H3HrAWrYSgwMsiGrYzSrTkh20gLC+dVYUgu/eBsWBHpJ3UzUwAD970ekbJQEoTUnk5RinIFQ==", "shasum": "c262dcef253eaf8cbd5677972eb6a8d72bdd424b", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.3.tgz", "fileCount": 54, "unpackedSize": 234325, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHw2I/UhApfvrJ+qRR9HpmTDUXlSE0OP2ZTTVEbdOH03AiAqH4OHFowANtIazLXj3Sy2+rbAlKhsA49OtjJudvEA/g=="}], "size": 42993}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.3_1683963412201_0.30134695357927854"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T07:36:52.396Z", "publish_time": 1683963412396, "_source_registry_name": "default"}, "1.7.4": {"name": "@vue/language-core", "version": "1.7.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.5", "@volar/source-map": "1.6.5", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "30fed93ac21d4d4fbf2351b85ebe65acb1216496", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.4", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-UK3BwhSWi5/UIRri822o/3AF9Vle3U7GCfFvLWkUwDEo1JZWQbJXyU4AlSy8CwhoCuWp1g/ajOaphBRhNEMZ/w==", "shasum": "3c6439cc858fe5f0fdad78f495d302deead58940", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.4.tgz", "fileCount": 54, "unpackedSize": 232899, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvJHmKs5xsKLu6/7KaL/Qnol+TRynswSdRnEpyEP5eowIgGnNXOL/VlSeLZ3q6EUT+pKH8xH54nFqnxnS+J5YMRYw="}], "size": 42732}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.4_1684394711419_0.047508716559641506"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T07:25:11.614Z", "publish_time": 1684394711614, "_source_registry_name": "default"}, "1.7.5": {"name": "@vue/language-core", "version": "1.7.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.6", "@volar/source-map": "1.6.6", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "9959597f7bc53e9bd09aeabb67f772bf776b0ad1", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.5", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-fnlVZexSwPPZLT/gHxi/wsS+y6RcoXC4xTjx2wInwIwH2XRYy9xbu4Wu/mjBuoM84VluXSvn6aKs3OrHKxWuvw==", "shasum": "7b3129374dda51684714fb6693b0296ef8281fa0", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.5.tgz", "fileCount": 54, "unpackedSize": 232885, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRph9ZZT0JkP4wTmK+lSG1e/XPZqqfHXEIjvLSaT0nLwIgR62lAzrU2uU2baKfagV2mFkwSLajFlKZUp/mjvB+180="}], "size": 42742}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.5_1684428388941_0.8159426375236094"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T16:46:29.143Z", "publish_time": 1684428389143, "_source_registry_name": "default"}, "1.7.6": {"name": "@vue/language-core", "version": "1.7.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.7", "@volar/source-map": "1.6.7", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "2a17efdae4ac994aea164dfcdfcfb27b4fa3f27d", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.6", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-3YDIrR7wN406nCu4xO5exQLcEiLi+fBORJ6EERuSHdmNcVvE5SezLwapx0L+EB+/OqA7L8TR0QreVognxM3nUQ==", "shasum": "cb4b0f5f2a0955ee8021666cca8a9102dba11803", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.6.tgz", "fileCount": 54, "unpackedSize": 232885, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICuzd4Ra2PQfOYEGjIMhoUsbAMRzQWtdIqZU5uUEN5ODAiBa+tR3ALC+SnjL3CzkQkr1kS10U4pLwGfztFNLyxwfOQ=="}], "size": 42741}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.6_1684430528941_0.8755804624975037"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T17:22:09.177Z", "publish_time": 1684430529177, "_source_registry_name": "default"}, "1.7.7": {"name": "@vue/language-core", "version": "1.7.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.9", "@volar/source-map": "1.6.9", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "0e9411252a4fa30cc999e30acc5db0d126c33daf", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.7", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-4hJ1HrxxxkwuVVUHcnkfQKBmSv/48kVi8ApsQQaTv64UPW1DqubZoXMvHdAeAeO0jhfjKOkMfSS8908yAisKPg==", "shasum": "5790bc4532fdaa5bb976c2f3c4dcf5c7434a7e8d", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.7.tgz", "fileCount": 54, "unpackedSize": 235542, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF8gp7kM032oAQmVnJak1og4XEMoMWVpMLQKmjpMG7TnAiEA1gprjyt6SurgY/VLHMvSSz3gsQdaTBEbwZ2WTdmDntM="}], "size": 43088}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.7_1684716380357_0.127746862928813"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-22T00:46:20.568Z", "publish_time": 1684716380568, "_source_registry_name": "default"}, "1.7.8": {"name": "@vue/language-core", "version": "1.7.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.6.9", "@volar/source-map": "1.6.9", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "9841dc5c7b19c2b1d24fcc95a808f29af39710b8", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.8", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-LcT6bEJDkcEMWpKtYm84HPTvUW9RonXw9lGTeaYOxx1g8MXyoRUf5pjGTsZR6Jm68uTWYw9yhYuSN+STXHRFAw==", "shasum": "c7addd33de8bbf2aa554e5b21e53b818d9e8a2f7", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.8.tgz", "fileCount": 54, "unpackedSize": 235622, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0cELq5OsM5w/qKGmuViJO1TpFAdD8Jg8rIsvSs54CkAiBtmAPWOPif3uDJyCHdgHSWmZpz4WO07BZ0SFM/1YoFVA=="}], "size": 43117}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.8_1684717511028_0.20850374794708237"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-22T01:05:11.252Z", "publish_time": 1684717511252, "_source_registry_name": "default"}, "1.7.9": {"name": "@vue/language-core", "version": "1.7.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.3", "@volar/source-map": "1.7.3", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "70f9ebedbc55a8d342ec425cc483dfe39c4b7489", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.9", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-ckmXj6I+KqWdip7c/qg8TFnB5UbyYK08OyhlmNXttNYeHPv7vvpJM5dg4pXvnP4CjtgxKqUSKTSa7G7HPOlHAQ==", "shasum": "726f3f57b48546d8319107bd3843f8c595b71d7d", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.9.tgz", "fileCount": 54, "unpackedSize": 234658, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHG6b5CuQZF0DnxaC/G0KKUtc4ACj87pj3OLrbZiW3NXAiAEimlmmwChUNq2tRi39dyZqmbukP4nmE7HFyGFaaFEpw=="}], "size": 43047}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.9_1686244636035_0.5000453231489876"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T17:17:16.209Z", "publish_time": 1686244636209, "_source_registry_name": "default"}, "1.7.10": {"name": "@vue/language-core", "version": "1.7.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.3", "@volar/source-map": "1.7.3", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "e395a70b33b2f09a58211eb55a1869db72c4fc19", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.10", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-cfSMGki5r8WEYNK2CSPHDJcQzKyXJkEIHxIKfNizV6BvILENt6pQNPFPnSPGPgN4LZ7XE3FvGOHuwuMrV6sq9Q==", "shasum": "1a9b036710eaeaa0ad322bb315b0b20c4f15937a", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.10.tgz", "fileCount": 54, "unpackedSize": 234659, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHIR3zdPDdmXrTcYceHzwxO0CFIOUIW9mMzQu4wdT3eWAiEAgo+fqiLqOFUXposIvI6i3j2zbeXnrmhNbNDG6bTmN9Q="}], "size": 43047}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.10_1686244881411_0.35410447350589913"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T17:21:21.609Z", "publish_time": 1686244881609, "_source_registry_name": "default"}, "1.7.11": {"name": "@vue/language-core", "version": "1.7.11", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.4", "@volar/source-map": "1.7.4", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "8ed51477115bf67f73eb74653b2770e5d9d586f0", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.11", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-hMe6ZSkhXz6AkVhGq8Z/J78sX/c+6755m342pPxI2opn9l68NIbKDaZ6LKInMES+LRFA68u3rFctR+whYQ5sPQ==", "shasum": "e1558b2c3ca30c8b13838a4513c209118791f542", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.11.tgz", "fileCount": 54, "unpackedSize": 234659, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAnopoPt+iZuIfcV47VL1gJ6pOOJxorM5xis+9EOvfVKAiAq0olPod09WU2oxZfPYky9P3EBkTLMukOhF6OWecx55Q=="}], "size": 43046}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.11_1686249267298_0.7258550392905043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T18:34:27.542Z", "publish_time": 1686249267542, "_source_registry_name": "default"}, "1.7.12": {"name": "@vue/language-core", "version": "1.7.12", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.4", "@volar/source-map": "1.7.4", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "9e712c2d603c690cd03502f5ec1a99bd80b800ac", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.12", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-pVRrnpZKckTXoi+RjShyWtdaFjVM2NbZAyN1gg2A0cEtr70j2sUFMBzSSH6r88Zr6whzs5yLUwTACgiP8okc5w==", "shasum": "45347257b4e201cf38eecfdf705faa8f9af8443f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.12.tgz", "fileCount": 54, "unpackedSize": 236457, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC3EDHpJd6bkrAkYhpmlYqHD3SCX5ZsZqZ0uRt0QEB6jAiB1I3aB0/Y2+73Q4XJorc6GQ1weqdNbZmtc5vSa7JcnYQ=="}], "size": 44211}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.12_1686703645097_0.9859263053459051"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-14T00:47:25.372Z", "publish_time": 1686703645372, "_source_registry_name": "default"}, "1.7.13": {"name": "@vue/language-core", "version": "1.7.13", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.5", "@volar/source-map": "1.7.5", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "32d5d5e4414f97b0dc28cd727a9cecf60acd4e97", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.13", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-ITkIz1QLu6z7YS9an2NMArRlnF+mQBRfpf/uh8hS10GosaGIxFMfu9UxejPfY2rCZ6mjhI849Due4oIHpLpmFg==", "shasum": "f1c8ecc15b9a7ae0f2a88911a9abe43a3e14c21a", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.13.tgz", "fileCount": 54, "unpackedSize": 236186, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDf3SLIi1Tc8bmohZIp4iX6v17E6NR5q9bNvlUfgWovywIgUHpx3TpdLuDPe5muE/Pg95rloUqfF1OPPV6KvASJba4="}], "size": 44252}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.13_1686761400549_0.6837673189296825"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-14T16:50:00.806Z", "publish_time": 1686761400806, "_source_registry_name": "default"}, "1.7.14": {"name": "@vue/language-core", "version": "1.7.14", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.6", "@volar/source-map": "1.7.6", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a8a05a278ab807590faeafdfd76646f5c41c0443", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.7.14", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.1/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-jl0qZjPyhBzLyGtLG6jGdKrq28VFc2qFR3283sfHIb2gY97uyvx+X29gafcHCBiILfYTF+Gi3phOVgQpdNkesQ==", "shasum": "dd53d6652e419916af5e1bcf9de019addd0ad872", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.7.14.tgz", "fileCount": 54, "unpackedSize": 236186, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFYKUUQPZRasY8kJNqOB40y0D4WX71KZHgbkdkrRT3VCAiEAvrjTclqJvK4JLC5hsZKjI4OkE4h98O9sWXc1326YnTc="}], "size": 44252}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.7.14_1686927002362_0.6834075615282862"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-16T14:50:02.728Z", "publish_time": 1686927002728, "_source_registry_name": "default"}, "1.8.0": {"name": "@vue/language-core", "version": "1.8.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.6", "@volar/source-map": "1.7.6", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "6e2e04ea2f0bfed9ab0fce23ad36abb04916b4f2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.0", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.1/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-rOAtqIRyyZ6OQreAkFDbbDt7L5BwvzrdbWaBAoEZjr4ImPBV9cRDBHxlMBU0SBOAZxIUQdjOvQ0uAl9uZDer0w==", "shasum": "3d106afdef859464435c90921fcce200a0b1d15c", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.0.tgz", "fileCount": 54, "unpackedSize": 236185, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8le4PdMFvbF3XHW7ch5R3QVfH0PdQV/L7LNr2bYgEHgIgZQ5IqhvUz8wyNHrLBPp3mtkfHUj5WOqkHcWXgJJnFQc="}], "size": 44253}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.0_1686956291624_0.21453513596310425"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-16T22:58:11.841Z", "publish_time": 1686956291841, "_source_registry_name": "default"}, "1.8.0-patch.1": {"name": "@vue/language-core", "version": "1.8.0-patch.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.7", "@volar/source-map": "1.7.7", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "5d5fc89edd2897b5eb2a143524a1f04775cffda3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.0-patch.1", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.1/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-MLKIVMEmNakZC+Wom3k1w3lJn2XmiXz6eZdKMcjpnfREuB38omDQJkc+uzD77PEaTU7+NwVIdnuKJue0gPpN8g==", "shasum": "93d9b349291af618707255feb23aca0de5cfa3b6", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.0-patch.1.tgz", "fileCount": 54, "unpackedSize": 236193, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBKwZBeouXK+8TF00fjTL37NOWahQKmbGt2qBAKdh2gLAiEAw/Geeu+vFNZ+z5VisA15fEFj+Y3kOXXWbHP+vOSi5Qs="}], "size": 44261}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.0-patch.1_1687051503677_0.7714869991527731"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-18T01:25:03.950Z", "publish_time": 1687051503950, "_source_registry_name": "default"}, "1.8.1": {"name": "@vue/language-core", "version": "1.8.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.8", "@volar/source-map": "1.7.8", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "3e31c6eb412a9e8145188190472f59c8b43aa9e6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.1", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-pumv3k4J7P58hVh4YGRM9Qz3HaAr4TlFWM9bnVOkZ/2K9o2CK1lAP2y9Jw+Z0+mNL4F2uWQqnAPzj3seLyfpDA==", "shasum": "86b709f803bcc0cab84fd14677c0fb909154c562", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.1.tgz", "fileCount": 54, "unpackedSize": 236082, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtPZTDVuKdGjpUySxImes2u8KIvzE9rGUCyVvQCrgUEgIgYq3joxY1c3nPu3MOPyUQHzmU941FB4hntJg/t/wIFnk="}], "size": 44219}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.1_1687251524672_0.7554063409577738"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T08:58:44.859Z", "publish_time": 1687251524859, "_source_registry_name": "default"}, "1.8.2": {"name": "@vue/language-core", "version": "1.8.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.9", "@volar/source-map": "1.7.9", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "de0fb3f1be30b495d17e2999aee24fb9ea3e7e0e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.2", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-QJujhmp89TRoWwzjn2sPMezG97+mNyaCTfznGHWNCE3LBsillZCBqAO7M7cxO8ee1V3r+qHjWytkoh3M4YkRJw==", "shasum": "560c1df639394a5df0043f2d7989dc2fba5bf4ca", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.2.tgz", "fileCount": 54, "unpackedSize": 236045, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtv9kJ6BWbCKIZbB5X8hnphccHOwvbPC3ezG26hDjeVwIgWNYd67Pc53Tr66mzBAI1lDbTUZcgIE/5hggmvvDGcUY="}], "size": 44217}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.2_1687802970555_0.9789000255286548"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T18:09:30.734Z", "publish_time": 1687802970734, "_source_registry_name": "default"}, "1.8.3": {"name": "@vue/language-core", "version": "1.8.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.7.10", "@volar/source-map": "1.7.10", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a02e928ae3f3a255366cdb334ac6fb49c3237ae1", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.3", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-AzhvMYoQkK/tg8CpAAttO19kx1zjS3+weYIr2AhlH/M5HebVzfftQoq4jZNFifjq+hyLKi8j9FiDMS8oqA89+A==", "shasum": "5d0d24b0290343c592c1e30712553804856b366a", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.3.tgz", "fileCount": 54, "unpackedSize": 236047, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIASWuiFpWRcsycUZqpZuo8tcKPpEaRaZrxZoxPMZ6w5oAiAWFwumo4KRbChRjRFunBOn3pvBBYddNLBcyCx7/qfLaw=="}], "size": 44217}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.3_1687943636192_0.1439544634449097"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-28T09:13:56.351Z", "publish_time": 1687943636351, "_source_registry_name": "default"}, "1.8.4": {"name": "@vue/language-core", "version": "1.8.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.8.0", "@volar/source-map": "~1.8.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a62aa882911de149ae65f4c28eddfd93e1dee99d", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.4", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-pnNtNcJVfkGYluW0vsVO+Y1gyX+eA0voaS7+1JOhCp5zKeCaL/PAmGYOgfvwML62neL+2H8pnhY7sffmrGpEhw==", "shasum": "9b34095987baaf3f2482ba5b34911d248d8ffcb4", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.4.tgz", "fileCount": 56, "unpackedSize": 244512, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJrRGxYT2XbgmpjiwXYbjW7fnDPT57v5R1kcBo6WFPxgIhAOHSKbSUk5dFJ4VhEYgjcx3Ez0hXI0bGPdke6w7rFv8e"}], "size": 44560}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.4_1688532715272_0.5190675295398997"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-05T04:51:55.498Z", "publish_time": 1688532715498, "_source_registry_name": "default"}, "1.8.5": {"name": "@vue/language-core", "version": "1.8.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.9.0", "@volar/source-map": "~1.9.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "971820b55ea42cb7e8c8ba7c35c8998d5572b420", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.5", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-DKQNiNQzNV7nrkZQujvjfX73zqKdj2+KoM4YeKl+ft3f+crO3JB4ycPnmgaRMNX/ULJootdQPGHKFRl5cXxwaw==", "shasum": "f1fc7ec87b46bdbaf0dbba29ce108f5e6f127d3b", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.5.tgz", "fileCount": 56, "unpackedSize": 245774, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDghIp03mSl9LihNzlzDQj+VI1xd5IxfdvmmX/w3HnGUgIgchSkf9gUV/ZislA4HHViu4K4fG9pSLWINhxopj5A9g4="}], "size": 44850}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.5_1689349651746_0.546914308111804"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-14T15:47:31.970Z", "publish_time": 1689349651970, "_source_registry_name": "default"}, "1.8.6": {"name": "@vue/language-core", "version": "1.8.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.9.0", "@volar/source-map": "~1.9.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "9da8afee02bc7251ae97716480ba31f8aff2794a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.6", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-PyYDMArbR7hnhqw9OEupr0s4ut0/ZfITp7WEjigF58cd2R0lRLNM1HPvzFMuULpy3ImBEOZI11KRIDirqOe+tQ==", "shasum": "afe2af57aa601770f4a5b263757e94e891954755", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.6.tgz", "fileCount": 56, "unpackedSize": 246015, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqkQz+QC4ge68aVptbDZiwuEXT2yYvb6hOSkZtw9JSGAiEA+qNbKulRvTe44W6lEUEgobwMWTmCRB3Js2CkrkqA7t8="}], "size": 44955}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.6_1689963112605_0.7510096044728787"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-21T18:11:52.797Z", "publish_time": 1689963112797, "_source_registry_name": "default"}, "1.8.7": {"name": "@vue/language-core", "version": "1.8.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "2b888901897fc9155bc12eb30ddae41afed5d942", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.7", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-VxZUGM77byVIoXmvIq0VufdBlmkvOToQC+yC6+6f4+sJ16W2R884mENpypHB+y74uzrn6cjBF8LMq244VQSIOg==", "shasum": "b5526a9bb1eb74a9a0cc4236dae9ab350c0d635e", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.7.tgz", "fileCount": 56, "unpackedSize": 250769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWEzGGgaPkQlkCztoe2rf6pVYVM3x4fRx6oN5J2+kgvgIhAMFvX7HLiqslRqEz79dcT6ijIQh0+LiKJyrEvkqdSddy"}], "size": 45813}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.7_1690398660871_0.18031932203187173"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T19:11:01.060Z", "publish_time": 1690398661060, "_source_registry_name": "default"}, "1.8.8": {"name": "@vue/language-core", "version": "1.8.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "62a08c35722319bcca90ea3ebd3236fbd986a6a6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.8", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-i4KMTuPazf48yMdYoebTkgSOJdFraE4pQf0B+FTOFkbB+6hAfjrSou/UmYWRsWyZV6r4Rc6DDZdI39CJwL0rWw==", "shasum": "5a8aa8363f4dfacdfcd7808a9926744d7c310ae6", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.8.tgz", "fileCount": 56, "unpackedSize": 250781, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcnkGK4afW88OYP/lhCc+U44rTYohwFtwCn/N0rTliwAIhAOlYeiUZ9BThqDwySGwdZc3O6sjb19lgNL1YB6z+wUNj"}], "size": 45822}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.8_1690404466933_0.48435615683248345"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T20:47:47.102Z", "publish_time": 1690404467102, "_source_registry_name": "default"}, "1.8.10": {"name": "@vue/language-core", "version": "1.8.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "97d60fa475b653fe435a7fae6e2a626821658351", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.10", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-db8PtM4ZZr7SYNH30XpKxUYnUBYaTvcuJ4c2whKK04fuAjbtjAIZ2al5GzGEfUlesmvkpgdbiSviRXUxgD9Omw==", "shasum": "e37409ac8686f30963a00662d51bf6d07c599ca9", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.10.tgz", "fileCount": 54, "unpackedSize": 256099, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGfS7k8wJNFu45ki6YZUGfuysiAaGiEt+78u0PuaRhzAiEAvVGedbKVYt5UxvtmVmDJFKpNYLSivNBGPYtQStPIToY="}], "size": 46681}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.10_1693991615618_0.7026126235508361"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-06T09:13:35.802Z", "publish_time": 1693991615802, "_source_registry_name": "default"}, "1.8.11": {"name": "@vue/language-core", "version": "1.8.11", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "cce193dcc182aad5d02f630fa3ae8a793d443680", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.11", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-+MZOBGqGwfld6hpo0DB47x8eNM0dNqk15ZdfOhj19CpvuYuOWCeVdOEGZunKDyo3QLkTn3kLOSysJzg7FDOQBA==", "shasum": "d10cc6f8f32e30991e0430f0d91db9416dc2e9a6", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.11.tgz", "fileCount": 54, "unpackedSize": 257532, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2t4hvXmSzOL3fh6GyaL+v260C7Con5zuN7dgE95RjFAiAqkaOSi11yyWDuOpWMnxsaPjPdfBA0e9lUHpw3/dxXDQ=="}], "size": 46882}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.11_1694546111190_0.6688886313855216"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-12T19:15:11.629Z", "publish_time": 1694546111629, "_source_registry_name": "default"}, "1.8.12": {"name": "@vue/language-core", "version": "1.8.12", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a065fd7516de02f1804f3394d6e2e0511a1e67a5", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.12", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-IYSr7ojQX2re7zMAKUKUYovuJ5IJoUI3PhgBRcpRBU29YnF/gDLf1c9ARtbnGPFvtDC+QP+/CfwVBxdPipBjEw==", "shasum": "9eea3936f76280da64da5b1faa38993e102d057d", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.12.tgz", "fileCount": 54, "unpackedSize": 259216, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHtcQVXHXLyqtdHPexOSWdjxoryWbBPDffE1NZQojff1AiAyo24TWlRgkFlcUcnUIJiHmp3Um/UFsldeB4dKjP9B/w=="}], "size": 47135}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.12_1695205210403_0.36686416986911397"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-20T10:20:10.632Z", "publish_time": 1695205210632, "_source_registry_name": "default"}, "1.8.13": {"name": "@vue/language-core", "version": "1.8.13", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "4d25501776edb5a0712c10c6921fffbdc9454a23", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.13", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-nata2fYBZAkl4QJrU+IcArJCMTHt1VP8ePL/Z7eUPC2AF+Cm7Qgo9ksNCPBzZRh1LYjCaSaqV7njqNogwpsMVg==", "shasum": "fc05f8f9034c04013000da5ed6aa7a6afe6ccc2f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.13.tgz", "fileCount": 54, "unpackedSize": 259218, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEh+cQ8Iz4pi8QrNf/WyduU9dxOgLdJrrmJ7p1oVz4PgIhAL6q5JtKSWjMf3XHKXQkhPUtRWBCaJnwesMYeqr0B+LK"}], "size": 47135}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.13_1695215940828_0.13590011484021525"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-20T13:19:00.997Z", "publish_time": 1695215940997, "_source_registry_name": "default"}, "1.8.14": {"name": "@vue/language-core", "version": "1.8.14", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "5febf2694e80621a7b852464edc76b74f73a5cb6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.14", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-15GGOkUP/AgE/jzGdUMtOaQ+XPokrP+Q5Z0DS3aun3i72E6MjFvwB7E2k/ap0mABjdRCRjoVPnsMF1+TkzGqQg==", "shasum": "c715940d2dbf88e13c28a5b76e77b313f3d56086", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.14.tgz", "fileCount": 54, "unpackedSize": 255545, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBJ9hBRc5NQEj3cnJzEwI8nfj4XQV2QNRuXrhPwCkLbyAiBDDutWaql+IsIDYMGtYcaP/rZz0Gjxv8GZLMSQdA+xaA=="}], "size": 46781}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.14_1695663959699_0.46363678903034455"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-25T17:46:00.153Z", "publish_time": 1695663960153, "_source_registry_name": "default"}, "1.8.15": {"name": "@vue/language-core", "version": "1.8.15", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.0", "@volar/source-map": "~1.10.0", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.0", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "24de4582131385e75f904bff7294df0e16ce1e01", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.15", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-zche5Aw8kkvp3YaghuLiOZyVIpoWHjSQ0EfjxGSsqHOPMamdCoa9x3HtbenpR38UMUoKJ88wiWuiOrV3B/Yq+A==", "shasum": "e84536f529f706c072037d495bfd610d4661fbae", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.15.tgz", "fileCount": 54, "unpackedSize": 255871, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNqboOoeU0YuvsrdLINGojjh2N26Hrx8sBU0LE22Z6eQIhAI94vrfFejiWoojpKgFL6ZmAZCNKzeFQbW4zA7gPi0AV"}], "size": 46846}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.15_1695727091085_0.24158979110611623"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T11:18:11.349Z", "publish_time": 1695727091349, "_source_registry_name": "default"}, "1.8.16": {"name": "@vue/language-core", "version": "1.8.16", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.3", "@volar/source-map": "~1.10.3", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "f9e281db3f47f9a3f94c79dbbf81102cba01eb5d", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.16", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-IAONyjgR3XImwgrtyQ7CCJlSXTlLesXG6/LpPjOBaXFiwknmGf1yDAXGa9fVH0lRplcnvOA7HNDI92OwWBi9qg==", "shasum": "dbbcee88c9f9a17faa6fc34f8357f0d5668435d5", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.16.tgz", "fileCount": 54, "unpackedSize": 255943, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDkV6CG0A4wRDzvm2KURyyGWSOoCfqYYr/NuTUDYqbI2AiAgcG7lJ5icALFfy5bbZRpCGV6UiMfdaxRevDCj6LXRcQ=="}], "size": 46858}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.16_1696624394895_0.3371567224582761"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T20:33:15.206Z", "publish_time": 1696624395206, "_source_registry_name": "default"}, "1.8.17": {"name": "@vue/language-core", "version": "1.8.17", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "1.10.2", "@volar/source-map": "1.10.2", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "81b41fe5daa15b01183459d036710ccc2491782e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.17", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-ZwYQm5fmHOBF30+HQ6Bd3i8BLS4MjrjvEBrW9gc5XM37vyy/QeToekAgIdawEurZN7tFEhcV47+a4OzeKRIkww==", "shasum": "65531ff8fa5d01acdbbbada462bb4b0211d057e9", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.17.tgz", "fileCount": 54, "unpackedSize": 256692, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8ptrYwEeuvqFaHbgROZ6L+1kd727q+AkqXpEqL8yfLgIgJlMnxRgecgGocTThzf0vuVT/bXV87rSOh+CWSZUNbfU="}], "size": 46967}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.17_1696786978574_0.8451399126808083"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-08T17:42:58.784Z", "publish_time": 1696786978784, "_source_registry_name": "default"}, "1.8.18": {"name": "@vue/language-core", "version": "1.8.18", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.3", "@volar/source-map": "~1.10.3", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "46ef0d608f43569a8e370d1105bfcf334fcaec13", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.18", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-byTi+mwSL7XnVRtfWE3MJy3HQryoVSQ3lymauXviegn3G1wwwlSOUljzQe3w5PyesOnBEIxYoavfKzMJnExrBA==", "shasum": "c111da12524bac3b12981786294b1ed5db2bd59f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.18.tgz", "fileCount": 54, "unpackedSize": 256694, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVrPrHSEmuXVyoWsuCvnFq01PTe5oYF0FGyxQDJ+VA4wIhAP/PEwHS1ogbhHV/e2GT4Ozv0R5xQBW8upl/x8CEEgJH"}], "size": 46968}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.18_1696788885044_0.4290692342696334"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-08T18:14:45.284Z", "publish_time": 1696788885284, "_source_registry_name": "default"}, "1.8.19": {"name": "@vue/language-core", "version": "1.8.19", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.4", "@volar/source-map": "~1.10.4", "@vue/compiler-dom": "^3.3.0", "@vue/reactivity": "^3.3.0", "@vue/shared": "^3.3.0", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "2e17f3c9cfa827c71e1ed07331730b3ee2596b76", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.19", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-nt3dodGs97UM6fnxeQBazO50yYCKBK53waFWB3qMbLmR6eL3aUryZgQtZoBe1pye17Wl8fs9HysV3si6xMgndQ==", "shasum": "c52cef2f89ad4b74caa29e97fd07a71d35ac9e60", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.19.tgz", "fileCount": 54, "unpackedSize": 257742, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDFPklz/aqAzvr0ytaO065jKnmcVvE8S8Jq0rKEJfDHwIhAND2Bn5B/notP+3GYK9zOyuvz/sTZBFk3pJeT9KJXKNz"}], "size": 47120}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.19_1697018249414_0.1666104089399747"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-11T09:57:29.660Z", "publish_time": 1697018249660, "_source_registry_name": "default"}, "1.8.20": {"name": "@vue/language-core", "version": "1.8.20", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/vue-language-core"}, "dependencies": {"@volar/language-core": "~1.10.4", "@volar/source-map": "~1.10.4", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "c5f0a7f2bec182880504e250fb3c8615a21ebd1e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.20", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-vNJaqjCTSrWEr+erSq6Rq0CqDC8MOAwyxirxwK8esOxd+1LmAUJUTG2p7I84Mj1Izy5uHiHQAkRTVR2QxMBY+A==", "shasum": "f7f83bad3f6a52f5d006b874630dd424233f1e08", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.20.tgz", "fileCount": 64, "unpackedSize": 261151, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFHhTJdwyfCguOEo8R+VFl9WM7C7rkogBFuYl+zWFJ6gIhAPXozGRMvOmB9wUSyeU8De/in7IndTejWEoTAIl8DKNL"}], "size": 48403}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.20_1698062458229_0.8632784863254568"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-23T12:00:58.532Z", "publish_time": 1698062458532, "_source_registry_name": "default"}, "1.8.21": {"name": "@vue/language-core", "version": "1.8.21", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.10.5", "@volar/source-map": "~1.10.5", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "46ae2ff1c155e51291fd584c0c3a32ed3dace7a5", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.21", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-dKQJc1xfWIZfv6BeXyxz3SSNrC7npJpDIN/VOb1rodAm4o247TElrXOHYAJdV9x1KilaEUo3YbnQE+WA3vQwMw==", "shasum": "109d890b341eaefd0c0140b2d7d0fb7ba88093a8", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.21.tgz", "fileCount": 64, "unpackedSize": 249839, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBgHhBdGS10ehDbimoqhF7Mc/wGKpUALmEqNW61ilUaIAiAaKE7YF7ru/6ll52qtnBWtrC3BgKEw86SK3fR1HuIIyQ=="}], "size": 46920}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.21_1698230874724_0.9549218518719595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-25T10:47:54.930Z", "publish_time": 1698230874930, "_source_registry_name": "default"}, "1.8.22": {"name": "@vue/language-core", "version": "1.8.22", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.10.5", "@volar/source-map": "~1.10.5", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "1e8d09af0282c42dd816671ffcd5a2321276e3c3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.22", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-bsMoJzCrXZqGsxawtUea1cLjUT9dZnDsy5TuZ+l1fxRMzUGQUG9+Ypq4w//CqpWmrx7nIAJpw2JVF/t258miRw==", "shasum": "1ef62645fb9b1f830c6c84a5586e49e74727b1e3", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.22.tgz", "fileCount": 64, "unpackedSize": 249925, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDV87X8qUojQ7RMrCZbxUMiDmdKUplv/q7NXGRzdmFQmQIhAJP5XuZ5cs9H1w+GUukQVglexE6TXJ8H9eOd5tfu3Os9"}], "size": 46928}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.22_1698339954390_0.7881792351953181"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-26T17:05:54.649Z", "publish_time": 1698339954649, "_source_registry_name": "default"}, "1.9.0-alpha.0": {"name": "@vue/language-core", "version": "1.9.0-alpha.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.10.9", "@volar/source-map": "~1.10.9", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "d6905e4d4d1208f560a544509494f33f431b602a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.9.0-alpha.0", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-Y5JfwjrOpSesDuSXtkCdH4LZ2wDSiY5J0ywI+SB4d28dj0VTqqwBOMBNeeqaBLbvYI+11wAV6Ye6K8/DwKte7Q==", "shasum": "ea4d6481300654241d83c0aab7ff1cfb7be93f1d", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.9.0-alpha.0.tgz", "fileCount": 64, "unpackedSize": 250008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHC8Qw1qwcudk6cTl2qlVne0YnH2RO2J+zYnP4lSHBmqAiBUxIQxy27MpARnXu0qErTaOEL+qz8fhVLno8HeoCC0PQ=="}], "size": 46964}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.9.0-alpha.0_1698752496494_0.9085643557562091"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-31T11:41:37.299Z", "publish_time": 1698752497299, "_source_registry_name": "default"}, "1.9.0-alpha.1": {"name": "@vue/language-core", "version": "1.9.0-alpha.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.10.9", "@volar/source-map": "~1.10.9", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "2ad355bbea9585df09843159848a2fba4bdc21f7", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.9.0-alpha.1", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-xLKs99E/z/iAdrNJQy0X0M1wHB67xB56OukYjhDs45DsAZ7pc8iWQwLA2R/mN4yRa8of5qpBQOCf0moQNBQV+w==", "shasum": "fe939faeaf1e5f02cf20a850f0b1fb5e19f0ef8d", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.9.0-alpha.1.tgz", "fileCount": 64, "unpackedSize": 250008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVRnE2TW+5ZLDUOyOaqukGiHX+g0bqq/1KH4kardZy4AiAjePR4NUdw5+iS0tKEiCr18qQPrqNLVu/qT8aFkc2Xmw=="}], "size": 46963}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.9.0-alpha.1_1698819172251_0.6591213675247014"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-01T06:12:52.453Z", "publish_time": 1698819172453, "_source_registry_name": "default"}, "1.9.0-alpha.2": {"name": "@vue/language-core", "version": "1.9.0-alpha.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.10.10", "@volar/source-map": "~1.10.10", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "f77140a9e3f75713534d55821d5b22432d832646", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.9.0-alpha.2", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-kkB70Qa4LkJibtAgqs7jaIwZ5HY1mNsCmlFRJgCzDZekCsRF28q4ZlcEomgfNrB5UlO15pnf2F70y5zAsZkKLg==", "shasum": "09882cf944275989f364c93f7d1dffa87697ab20", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.9.0-alpha.2.tgz", "fileCount": 64, "unpackedSize": 250010, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKEQI454kWDXV5pRtcNjXxLaS9E0c0JllShVtnPwKGogIgfHKMJr11kdKPl+0/hcR7ZwEaovz5voUpYyADsvUevsc="}], "size": 46963}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.9.0-alpha.2_1698821333684_0.7678186388148527"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-01T06:48:53.952Z", "publish_time": 1698821333952, "_source_registry_name": "default"}, "1.9.0-alpha.3": {"name": "@vue/language-core", "version": "1.9.0-alpha.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.11.0", "@volar/source-map": "~1.11.0", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "cf6564bc8c649151c2e72a93d0aa482d4a59a773", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.9.0-alpha.3", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-CtNlwQNP4qVdcwYFrMYsOBNrpkeWew6bpDYXODKTMVpmkDQY+8CFnThmVx21XYMuSdW1AOlVXOIY0zHKpLmZwg==", "shasum": "cb5bbd4db95599d7758bb5c3651348443bb31091", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.9.0-alpha.3.tgz", "fileCount": 64, "unpackedSize": 250008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHJKXjXzWc0wOtWvDUMQiGfNGNYOHjGYGs/3Msrc7PXgAiBi0D/VPt6VAlyBCgS9PsQ4wgfR66nvvKo5uL0+wUXTiQ=="}], "size": 46962}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.9.0-alpha.3_1699048332623_0.07929895814641852"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-03T21:52:12.851Z", "publish_time": 1699048332851, "_source_registry_name": "default"}, "1.8.24": {"name": "@vue/language-core", "version": "1.8.24", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.11.1", "@volar/source-map": "~1.11.1", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "6f850196d6b9cd1bee62104d3d92867cf0b6777e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.24", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-2ClHvij0WlsDWryPzXJCSpPc6rusZFNoVtRZGgGGkKCmKuIREDDKmH8j+1tYyxPYyH0qL6pZ6+IHD8KIm5nWAw==", "shasum": "ceb844b6dff6b7897d4ec8a2fd8eb6e9db8027fe", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.24.tgz", "fileCount": 64, "unpackedSize": 250961, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUEb5Tpo7dIKGvhSkvvWLpK0cGOSq7+asqDk8GgqxkxAiAoxCa5QDqfL9qZqUfF//mUyQDYQpdloEQORNE9SHAskw=="}], "size": 47119}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.24_1701250867834_0.09918648160783095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-29T09:41:08.026Z", "publish_time": 1701250868026, "_source_registry_name": "default"}, "1.8.25": {"name": "@vue/language-core", "version": "1.8.25", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.11.1", "@volar/source-map": "~1.11.1", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "6f8ca45025b8e38a86f6946bbc294a6a2d88b063", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.25", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-NJk/5DnAZlpvXX8BdWmHI45bWGLViUaS3R/RMrmFSvFMSbJKuEODpM4kR0F0Ofv5SFzCWuNiMhxameWpVdQsnA==", "shasum": "b44b4e3c244ba9b1b79cccf9eb7b046535a4676f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.25.tgz", "fileCount": 66, "unpackedSize": 252598, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1SBuF1Rdcp9weS8cKkcG14Qug0iUyb4Fk8WjObiWj9gIhAJN1UbNq5cx7aXKo9i83O0yQ80IBLXEvWRXGvBWentam"}], "size": 47531}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.25_1701798779988_0.050502130586389304"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-05T17:53:00.205Z", "publish_time": 1701798780205, "_source_registry_name": "default"}, "1.8.26": {"name": "@vue/language-core", "version": "1.8.26", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.11.1", "@volar/source-map": "~1.11.1", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "5849cada166bbd3faa03f21efd4d3cc2a2836d11", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.26", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.1.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-9cmza/Y2YTiOnKZ0Mi9zsNn7Irw+aKirP+5LLWVSNaL3fjKJjW1cD3HGBckasY2RuVh4YycvdA9/Q6EBpVd/7Q==", "shasum": "7edb6b51a6ed57b618928500c3cbda9757a9f5f0", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.26.tgz", "fileCount": 66, "unpackedSize": 253222, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEZFkRQI5U5UVcNdYaBZS8jqtarBLBcPtAsTJmwzFPm5AiEA4m9d3QRAeF4khr9K9Qk/k/6Ie3YaOxsYF3bFUH3bWc4="}], "size": 47711}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.26_1703177472319_0.03401940957136884"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-21T16:51:12.531Z", "publish_time": 1703177472531, "_source_registry_name": "default"}, "1.8.27": {"name": "@vue/language-core", "version": "1.8.27", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~1.11.1", "@volar/source-map": "~1.11.1", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.3.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "09c04807eb19f1261cc429af1b90c6561166ad4f", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@1.8.27", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.1.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-L8Kc27VdQserNaCUNiSFdDl9LWT24ly8Hpwf1ECy3aFb9m6bDhBGQYOujDm21N7EW3moKIOKEanQwe1q5BK+mA==", "shasum": "2ca6892cb524e024a44e554e4c55d7a23e72263f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.27.tgz", "fileCount": 64, "unpackedSize": 251053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEaKsK8njfhZFRk4wcCZ4e/boQpqDquikt8DdLCUkDeaAiEA8E072cqHcdb1Z82gyLpEQeU9Ke0OIfrcnNcmWnzyb/c="}], "size": 47142}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_1.8.27_1703585924070_0.9187474634342847"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-26T10:18:44.261Z", "publish_time": 1703585924261, "_source_registry_name": "default"}, "2.0.0": {"name": "@vue/language-core", "version": "2.0.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "aa47e5a7d8a6dae62cc80dbdb5db6a9bfa4f8715", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.0", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-NVF9aRtJ7KrT2U6gAj2Er3P4jmHVL58Erwc7eBAzU4k0lbFf/vEEg0IU1kriJrqYPadzlgokFvL1cfCD2pZl1Q==", "shasum": "991972f2927199a2e96505aefc137e72ba7176bb", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.0.tgz", "fileCount": 2, "unpackedSize": 1903, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5/Tru62iWhXxQ7enWZDCzYFUz6Tp//ZMoU9P+hFopPAIhANuj0MafI1V9kUT/dqZTxDQ4kdcyWbtcu4807QIPGtSs"}], "size": 1187}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.0_1709327659971_0.725131046955962"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-01T21:14:20.170Z", "publish_time": 1709327660170, "_source_registry_name": "default"}, "2.0.1": {"name": "@vue/language-core", "version": "2.0.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "adedfd0983c910370d080e955702cca7d2275420", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.1", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-TZ9xKj3WhZol2hWvXcCjGDiOVkmQ1aG7MS3YJkhVMeoLYyopP5XFn9Obc9qorxG6vZDWapRxBkyEzJ9d6xL3Zg==", "shasum": "c5477c9c5cf07cab8ffabd93d3354337ca7e4c09", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.1.tgz", "fileCount": 64, "unpackedSize": 257082, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICjpmuwpNd9AoWANDgf4fYoyBFYHT5u2wgNVbcX1laa3AiEA8o+XgKwar1/tQ9HGPN+STwH67ZJEm/YfuZ/GnkPaG3M="}], "size": 48131}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.1_1709333019683_0.01284414949970425"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-01T22:43:39.920Z", "publish_time": 1709333019920, "_source_registry_name": "default"}, "2.0.2": {"name": "@vue/language-core", "version": "2.0.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "b377d5f990ffe7ef44f0d1871fcb8b5c2deafad1", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.2", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-MT8pGTFouwDOS/ton1OqyW2MtTv02I8kEymvD05lW3NM8HJf63Xu9sZc0nh5SGZN35EFdnSsD+emhRKRzHLEug==", "shasum": "bd1544d9cfd78ba559894efe93ddfefcaebc7536", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.2.tgz", "fileCount": 64, "unpackedSize": 257082, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBVKNZ+DpIFCMCOFQU8PfQxNpRH+uaQSntMSRtenw8sRAiAbwVujhfHImAFqq/DhyojlyUJKnbAaotMJjMey4cYzmw=="}], "size": 48129}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.2_1709365031538_0.6367853640313947"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-02T07:37:11.710Z", "publish_time": 1709365031710, "_source_registry_name": "default"}, "2.0.3": {"name": "@vue/language-core", "version": "2.0.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "fc1e288c8c0c82e6730781006d84a2676b5266ff", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.3", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-hnVF/Q3cD2v+EFD4pD1YdITGBcdM38P18SYqilVQDezKw5RobWny4BwIckWGS1fJmUstsO9mTX30ZOyzyR2Q+Q==", "shasum": "49e290c928b216a5b0f07012ff6e1065a6e15258", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.3.tgz", "fileCount": 64, "unpackedSize": 257082, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHRW7aJ1kdniJO46bmV+lLUOpXCN2KjnIF5cuBm8q6EvAiBnEphhYDbnKok88Wi3y3zokMmLTihILjip3kSVPkL0jA=="}], "size": 48132}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.3_1709453120767_0.5905509438728951"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-03T08:05:21.036Z", "publish_time": 1709453121036, "_source_registry_name": "default"}, "2.0.4": {"name": "@vue/language-core", "version": "2.0.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "241300968fd3084c7c09139d05691a51a7800fdc", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.4", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-IYlVEICXKRWYjRQ4JyPlXhydU/p0C7uY5LpqXyJzzJHWo44LWHZtTP3USfWNQif3VAK5QZpdZKQ5HYIeQL3BJQ==", "shasum": "324ae3de55d34f926f43b85901f60a33c3c63211", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.4.tgz", "fileCount": 64, "unpackedSize": 257082, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+n8F8hJblmTg/ACbaDfHuV4iF7s1jMsWbLjrpOyUHCAiAi8eaLS7cHP4OoKpB6nJ0QkgZ4mBq7Da3h5GZJkHk80Q=="}], "size": 48133}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.4_1709525954067_0.45625390770907326"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-04T04:19:14.258Z", "publish_time": 1709525954258, "_source_registry_name": "default"}, "2.0.5": {"name": "@vue/language-core", "version": "2.0.5", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.1", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "62b4fcb0d3f7153b5b2f5571af32f519117d8466", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.5", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-knGXuQqhDSO7QJr8LFklsiWa23N2ikehkdVxtc9UKgnyqsnusughS2Tkg7VN8Hqed35X0B52Z+OGI5OrT/8uxQ==", "shasum": "bd3502604ea785f4171815005997988563f18469", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.5.tgz", "fileCount": 64, "unpackedSize": 258050, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHoV/KyobFySn6O5P6YFMaYNOf/EPPAJnKOpbK5idduKAiA9pqDzR+5LdG3+6QbMFtC5iHqMs0tUqj1tqcPbylLaBA=="}], "size": 48381}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.5_1709648877481_0.5781717765827084"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-05T14:27:57.692Z", "publish_time": 1709648877692, "_source_registry_name": "default"}, "2.0.6": {"name": "@vue/language-core", "version": "2.0.6", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.2", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "feb990ccec85f6330bba37c8b1d1287f0980274c", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.6", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-UzqU12tzf9XLqRO3TiWPwRNpP4fyUzE6MAfOQWQNZ4jy6a30ARRUpmODDKq6O8C4goMc2AlPqTmjOHPjHkilSg==", "shasum": "876f90622a3f801dce5cedcd6eae429d732152e2", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.6.tgz", "fileCount": 64, "unpackedSize": 259138, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtFAFI4W31DkpMD5S0vEU0lUC8I6uDJ8oFBp+33YR6bwIhAI7k6fFMUM84ie/jx3xQFDvH/ANZEfYhKhsyFfFUXZr9"}], "size": 48566}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.6_1709790826827_0.5626076294573779"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-07T05:53:46.979Z", "publish_time": 1709790826979, "_source_registry_name": "default"}, "2.0.7": {"name": "@vue/language-core", "version": "2.0.7", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.3", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "4a37e8f3ebcf31ecfd2ea627f7611d5990ec5df6", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.7", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-Vh1yZX3XmYjn9yYLkjU8DN6L0ceBtEcapqiyclHne8guG84IaTzqtvizZB1Yfxm3h6m7EIvjerLO5fvOZO6IIQ==", "shasum": "af12f752a93c4d2498626fca33f5d1ddc8c5ceb9", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.7.tgz", "fileCount": 64, "unpackedSize": 260204, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDD4TXhixq5QFE3nilMJfqumVWe76uFTiBVpE8PhRmaKQIhAPvPaAH9c7GFcGRAPPzfeH52W5FDKPywXIyW/g18XbaA"}], "size": 48677}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.7_1710930676979_0.2232934166482874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-20T10:31:17.151Z", "publish_time": 1710930677151, "_source_registry_name": "default"}, "2.0.10": {"name": "@vue/language-core", "version": "2.0.10", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.2.0-alpha.5", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a20a2ee950b63a949660b7e8faf0faed0e5bad33", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.10", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-3ULtX6hSPJNdNChi6aJ4FfdJNs5EShBLxnwLFTqrk2N1385WOwGVlbHeS2R6W9s9lXZ0+mC2bv4VlFSyeNPNGA==", "shasum": "81d90f9644b95283a3881f3cb594bef7c43831fb", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.10.tgz", "fileCount": 72, "unpackedSize": 266651, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFE+VrmoreQG2TsH8jjE0Qb1O9DkKsTCyH17gtvOQpG0AiEA9Is7IAPTk/CU46ej/1NUxQLNc8En6QassL4Xsgkg31k="}], "size": 50578}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.10_1712243192492_0.2983764499504269"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-04T15:06:32.673Z", "publish_time": 1712243192673, "_source_registry_name": "default"}, "2.0.11": {"name": "@vue/language-core", "version": "2.0.11", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.2.0-alpha.6", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "c89f25ffc32c760130adeeac796b9a5d20585bf7", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.11", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-5ivg8Vem/yckzXI3L3n0mdKBPRcHSlsGt6/dpbEx42PcH3MIHAjSAJBYvENXeWJxv2ClQc8BS2mH1Ho2U7jZig==", "shasum": "3673a69015f56c3abe8c53d603c0748244127202", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.11.tgz", "fileCount": 72, "unpackedSize": 267310, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHc3O4FZFqrYrWfI3wEPUWqV5grwIR2GWOnxaP4MufuAiA7eKdpOstRk62iCAosvsRVmzCu91ZbYnY+InxmcG3jRg=="}], "size": 50713}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.11_1712497727339_0.7695168707940845"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-07T13:48:47.506Z", "publish_time": 1712497727506, "_source_registry_name": "default"}, "2.0.12": {"name": "@vue/language-core", "version": "2.0.12", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.7", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "c1c4e1a2a6c32da59351641bd41bf7f5db0cac69", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.12", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-aIStDPt69SHOpiIckGTIIjEz/sXc6ZfCMS5uWYL1AcbcRMhzFCLZscGAVte1+ad+RRFepSpKBjGttyPcgKJ7ww==", "shasum": "c2e3fab1179a0b8560ebb184d38ea7f6479dfda7", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.12.tgz", "fileCount": 72, "unpackedSize": 267612, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG2q498UF6yY9RSICl59tU4nFiYxxTj+aGC+VHcdus+lAiEAy5hrREeOSG8CytcpPYc5O84B22Neu8oD0ixZ2oQhiAc="}], "size": 50795}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.12_1712741968873_0.13170413203085807"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-10T09:39:29.015Z", "publish_time": 1712741969015, "_source_registry_name": "default"}, "2.0.13": {"name": "@vue/language-core", "version": "2.0.13", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.8", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "591d019acd0d34e390880d69b31fbc7b794b806b", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.13", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-oQgM+BM66SU5GKtUMLQSQN0bxHFkFpLSSAiY87wVziPaiNQZuKVDt/3yA7GB9PiQw0y/bTNL0bOc0jM/siYjKg==", "shasum": "2d1638b882011187b4b57115425d52b0901acab5", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.13.tgz", "fileCount": 72, "unpackedSize": 267612, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbQC1+KfUhzquw78Bsu2edA+5aVdYeooKOCBR/jAtAJAIhAPm+DW8+gtvjIGhp3j42lsQruLoYQ7Ont6IsPZlGcFyN"}], "size": 50798}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.13_1712908625016_0.3615194933957806"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T07:57:05.230Z", "publish_time": 1712908625230, "_source_registry_name": "default"}, "2.0.14": {"name": "@vue/language-core", "version": "2.0.14", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.10", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "ce1412067f88b7f9af03a2d3e04c220b4921c363", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.14", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-3q8mHSNcGTR7sfp2X6jZdcb4yt8AjBXAfKk0qkZIh7GAJxOnoZ10h5HToZglw4ToFvAnq+xu/Z2FFbglh9Icag==", "shasum": "99d1dcd7df8a859e12606e80863b3cb4cf045f9e", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.14.tgz", "fileCount": 112, "unpackedSize": 283547, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF5CXrn4iJB5OCt2EksqTEdIOgdKHy6hEitvMQzvun4/AiALrB3MlHgGBecs1sm7+34A1jMzyAN2AHpmmeSHosDlTw=="}], "size": 54684}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.14_1713750340222_0.7158517914506504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T01:45:40.403Z", "publish_time": 1713750340403, "_source_registry_name": "default"}, "2.0.15": {"name": "@vue/language-core", "version": "2.0.15", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.12", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "095f44449d71cd5a4730306c9c8c40df4d44dce3", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.15", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-a2n5Oc+PkWPX5zhnTkddH/hzPCrQmwUz1EwmFje3mqd+c8Ux+yCVEnAE2XtGQZoELgSWvY7EmJfidRbs+nR19Q==", "shasum": "29777f05753048bb2e33277c57b3b1273c0274a2", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.15.tgz", "fileCount": 112, "unpackedSize": 290197, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8NYGdcdvIHZl8dozUHOM+OJdzdsPb1Hqb2oQwYpGzhwIgDv3mrnwJQVSi5rA+eJ4g2gUzctSloj3xahgltR5YwAU="}], "size": 55655}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.15_1714444380176_0.08336299417412296"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-30T02:33:00.303Z", "publish_time": 1714444380303, "_source_registry_name": "default"}, "2.0.16": {"name": "@vue/language-core", "version": "2.0.16", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.2.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "95b78c38cbf75481ebb59e11956b592346f01d92", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.16", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-Bc2sexRH99pznOph8mLw2BlRZ9edm7tW51kcBXgx8adAoOcZUWJj3UNSsdQ6H9Y8meGz7BoazVrVo/jUukIsPw==", "shasum": "c059228e6a0a17b4505421da0e5747a4a04facbe", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.16.tgz", "fileCount": 112, "unpackedSize": 290433, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkoxGILJBUvHomaQRypB7iIOP/F/8+XIoafvzFqug5jwIgP+IDTbiuPFNkCqBbATNRUZRP6VNLEoO6PW4nPXv7qCc="}], "size": 55685}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.16_1714534717967_0.0879574922350772"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-01T03:38:38.106Z", "publish_time": 1714534718106, "_source_registry_name": "default"}, "2.0.17": {"name": "@vue/language-core", "version": "2.0.17", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.2.2", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "968039cbb07961f318b4bf122bfa8e3e4a824277", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.17", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-tHw2J6G9yL4kn3jN5MftOHEq86Y6qnuohBQ1OHkJ73fAv3OYgwDI1cfX7ds0OEJEycOMG64BA3ql5bDgDa41zw==", "shasum": "25ba5c7f4450790cc6cfaccf9805a6b91ada1bd7", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.17.tgz", "fileCount": 112, "unpackedSize": 292465, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/GBWzvnkpTCCXJBJYExij/y6jlZxxKNICQcAT1BDTAwIgTHXRXGYJfrCfwLsa/dRR1HJgSI9naY+fkvUckCij+jo="}], "size": 55921}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.17_1715331521422_0.5272243518447777"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-10T08:58:41.601Z", "publish_time": 1715331521601, "_source_registry_name": "default"}, "2.0.18": {"name": "@vue/language-core", "version": "2.0.18", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.2.4", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "7aac2805f03b17e4c624335f509d502002bb75a8", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.18", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.4.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-MwKRQAReHN1z7P3/8k/ISC5MjDRjHxGyitn50jWrMmzW9FNySG/1NxMPgAHcVJ4zApJUolS9TexYzT4I6BKL5w==", "shasum": "e910953d9a8acd601ae2b4ab798e68ecfe3dc907", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.18.tgz", "fileCount": 114, "unpackedSize": 293845, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCX4bHe38QwpKD8VwqCnakpktOAh50GvUSQ+zOU1F283QIhAK2hPa0L1VSIzt3SC3ERW9DlgZlEq+fAFZ3XBRQ9F3wQ"}], "size": 56178}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.18_1715774772154_0.7837947122633326"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T12:06:12.327Z", "publish_time": 1715774772327, "_source_registry_name": "default"}, "2.0.19": {"name": "@vue/language-core", "version": "2.0.19", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.2.4", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a9fae154ad1efc4359866cfd10251d53e4b0faed", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.19", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.4.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-A9EGOnvb51jOvnCYoRLnMP+CcoPlbZVxI9gZXE/y2GksRWM6j/PrLEIC++pnosWTN08tFpJgxhSS//E9v/Sg+Q==", "shasum": "d55f9c1e92690c77ffd599688ba36c2b50c4303b", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.19.tgz", "fileCount": 114, "unpackedSize": 293790, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDocGCB78OgwCUs6ViycGUzUdRA5IAEmIG3VCkmLAr5HwIgexYeoGVGaJjcJTTjzKp/dAk1XOCKzl2S+mpkGtu197w="}], "size": 56171}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.19_1715835447161_0.8693204532536354"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-16T04:57:27.351Z", "publish_time": 1715835447351, "_source_registry_name": "default"}, "2.0.20": {"name": "@vue/language-core", "version": "2.0.20", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.3.0-alpha.14", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "e1a5d2f136bf60a772c9655f9f5474c7f71a2ff9", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.20", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-PudZnVVhZV9++4xndha6K8G1P+pa5WB4H926IK6Pn82EKD+7MEnBJ858t+cI5jpXqx1X/72+NfzRrgsocN5LrA==", "shasum": "32cbd60deada2e83f96614301af6b1e81adac3bf", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.20.tgz", "fileCount": 114, "unpackedSize": 293867, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICRtZR+IYWIfg5JK/4zF8qJNhMyLr0M2L6AIC6Zjnnc/AiA0ociT+acOU2tRpMuHYDTiJ9+ts9YfxcXG33QJY7es4w=="}], "size": 56132}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.20_1717832009762_0.33403472465865236"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-08T07:33:29.913Z", "publish_time": 1717832009913, "_source_registry_name": "default"}, "2.0.21": {"name": "@vue/language-core", "version": "2.0.21", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.3.0-alpha.15", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a5af80e3939a39694abd9dd09a5496bc5fbf6e06", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.21", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-vjs6KwnCK++kIXT+eI63BGpJHfHNVJcUCr3RnvJsccT3vbJnZV5IhHR2puEkoOkIbDdp0Gqi1wEnv3hEd3WsxQ==", "shasum": "882667d0c9f07bc884f163e75eed666234df77fe", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.21.tgz", "fileCount": 114, "unpackedSize": 293867, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTFIc+tJyxoqxlIZClq5ZTG4rUVtBh2QDV19YOs88v5AIgaQgtHM4bPPqxlngIUKY0skIJKuspTgN/1BbobfXk9CQ="}], "size": 56134}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.21_1717858026831_0.8522292484544702"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-08T14:47:07.062Z", "publish_time": 1717858027062, "_source_registry_name": "default"}, "2.0.22": {"name": "@vue/language-core", "version": "2.0.22", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.3.1", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "884c8a553d4fd240167fcb97c6a738564f9d697a", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.22", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-dNTAAtEOuMiz7N1s5tKpypnVVCtawxVSF5BukD0ELcYSw+DSbrSlYYSw8GuwvurodCeYFSHsmslE+c2sYDNoiA==", "shasum": "2f8164ecc83f85f27301521d0a6ce37cc59bb23a", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.22.tgz", "fileCount": 116, "unpackedSize": 292887, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhV1EcBTSQ8XQAdzt2xTP8w9XMynrIbRKJ/iDgoODGkwIhANfcGbgI1aC6f5hi+WwZejjspE885J3urYyOaajPg94f"}], "size": 56229}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.22_1719062823482_0.8838312828997095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-22T13:27:03.703Z", "publish_time": 1719062823703, "_source_registry_name": "default"}, "2.0.23-alpha.0": {"name": "@vue/language-core", "version": "2.0.23-alpha.0", "license": "MIT", "_id": "@vue/language-core@2.0.23-alpha.0", "homepage": "https://github.com/vuejs/language-tools#readme", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "dist": {"shasum": "00a85a982378a6aad5db869cb4d25b90957286af", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.23-alpha.0.tgz", "fileCount": 116, "integrity": "sha512-e1KyeTTLgbBR0VsDh2h6ZeXTvHnGVQa41De8b/AGTL+Ws+ppN5Id17F5eY1M6WxAZZ/GDr/hZgEPiy/2HXB23A==", "signatures": [{"sig": "MEYCIQCMo8s8eemXZoEDGTDHBoCnGVtUwbM09w0/6KQkX1IrkgIhALeZLnaZEwmXyqw4AfIM9vHto+fhIQ+l5u+Gc+26qBmw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293158, "size": 56312}, "types": "./index.d.ts", "gitHead": "c1b2f64df85617643c1b4b408d99447df3fa2d5f", "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vuejs/language-tools.git", "type": "git", "directory": "packages/language-core"}, "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"computeds": "^0.0.1", "minimatch": "^9.0.3", "@vue/shared": "^3.4.0", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "@vue/compiler-dom": "^3.4.0", "@volar/language-core": "~2.4.0-alpha.0", "vue-template-compiler": "^2.7.14"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "latest", "@types/minimatch": "^5.1.2", "@volar/typescript": "~2.4.0-alpha.0", "@vue/compiler-sfc": "^3.4.0", "@types/path-browserify": "^1.0.1"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/language-core_2.0.23-alpha.0_1719409101528_0.5526440004686162", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-26T13:38:21.818Z", "publish_time": 1719409101818, "_source_registry_name": "default"}, "2.0.23-alpha.1": {"name": "@vue/language-core", "version": "2.0.23-alpha.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.0", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.0", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "70cb34e9d58afd603d50cb5b870116b189f4662b", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_id": "@vue/language-core@2.0.23-alpha.1", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-6fR3AaeekF8nn4q4hmO925RpiZkVEO1cmgeCM4yfDza5z1q8z6o3F0wIFIZnnyhGy63fmodf7Gry1sRd4KYvEg==", "shasum": "4500f09080a7ea1a9b6c039f82cf43abf8d377df", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.23-alpha.1.tgz", "fileCount": 116, "unpackedSize": 293158, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHK9FXqCwgHAwzGPSl7XTDTygMDCE1llRwP9tcGwmZULAiBqAl4Wray//1lGY71nHQYScu8NagmyMM887BgHwDutAg=="}], "size": 56312}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.23-alpha.1_1719420052932_0.12173058668912473"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-26T16:40:53.150Z", "publish_time": 1719420053150, "_source_registry_name": "default"}, "2.0.24": {"name": "@vue/language-core", "version": "2.0.24", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.2", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.2", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "bca79db09e413ef29c17b910271c123a7a68806f", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.0.24", "dist": {"integrity": "sha512-997YD6Lq/66LXr3ZOLNxDCmyn13z9NP8LU1UZn9hGCDWhzlbXAIP0hOgL3w3x4RKEaWTaaRtsHP9DzHvmduruQ==", "shasum": "078c9e6c31228e31f981c149123a0e0488963daa", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.24.tgz", "fileCount": 116, "unpackedSize": 292906, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGSpomX7n8Dpyda7eR6hqkkqyKNCOLMUs7RUZo4BLR2aAiAJsclFJlC0aUCfal26UX35XV4z3LOxPS6vNliEjzBTmA=="}], "size": 56283}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.24_1719732405839_0.7539184425616487"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T07:26:46.063Z", "publish_time": 1719732406063, "_source_registry_name": "default"}, "2.0.26-alpha.0": {"name": "@vue/language-core", "version": "2.0.26-alpha.0", "license": "MIT", "_id": "@vue/language-core@2.0.26-alpha.0", "dist": {"shasum": "67d04f1e36a14d518e75060e021d07c9228c80ef", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.26-alpha.0.tgz", "fileCount": 116, "integrity": "sha512-62kPmnUullj7qROeKToMuszaqWw1dl7utksH/IcnnZCUPxxCaBKvEI5XB1GH/UgqZmnSsF7UoTcoptk/pirRiQ==", "signatures": [{"sig": "MEUCIQCrqa9iN7mJzzFNwmRjYzBqaG8G93LWVvtfPqlzDJ/wTgIgKiT0zzXSySqr9Vuv1xH3mYxQrUO+SDPcoTRPXDal/iU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 292916, "size": 56287}, "gitHead": "7ace5046de671cda22fac8dd7d71b3658489c5c6", "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/vuejs/language-tools.git", "type": "git", "directory": "packages/language-core"}, "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"computeds": "^0.0.1", "minimatch": "^9.0.3", "@vue/shared": "^3.4.0", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "@vue/compiler-dom": "^3.4.0", "@volar/language-core": "~2.4.0-alpha.11", "vue-template-compiler": "^2.7.14"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "latest", "@types/minimatch": "^5.1.2", "@volar/typescript": "~2.4.0-alpha.11", "@vue/compiler-sfc": "^3.4.0", "@types/path-browserify": "^1.0.1"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/language-core_2.0.26-alpha.0_1719886409153_0.43111641202451634", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-07-02T02:13:29.329Z", "publish_time": 1719886409329, "_source_registry_name": "default"}, "2.0.26-alpha.1": {"name": "@vue/language-core", "version": "2.0.26-alpha.1", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.11", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.11", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "38830a5e043a97158c7123995914bce6a875125a", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.0.26-alpha.1", "dist": {"integrity": "sha512-k5jpGhrCc6ZTVpFr3AK+lFIQhuUni2LhEGzZnPuh+OpYD/yWVIEeoSix9Y5sxj210CXJNSAVO/H7sUgjIHTdjw==", "shasum": "b8985cb51259123385a833acd3f8dd488bba36eb", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.26-alpha.1.tgz", "fileCount": 116, "unpackedSize": 292916, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKFwPpEg/HDkgYUaxOHYNwJ5FmztjZ54Ow1tzVRn3RAQIgJEOqddu3PiOCJyhpRSRu8XxiBMIBYS5CiWujZw02VNI="}], "size": 56288}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.26-alpha.1_1719886711520_0.2507715697585913"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T02:18:31.716Z", "publish_time": 1719886711716, "_source_registry_name": "default"}, "2.0.26-alpha.2": {"name": "@vue/language-core", "version": "2.0.26-alpha.2", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.12", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.12", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "e4e8c8ca14dc564bf9043a625dd704b32bdc69d0", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.0.26-alpha.2", "dist": {"integrity": "sha512-r4Sn6Eo/xjRAS0REPy0SDZGz9rhaWOZqffonXo4Q4wx6qcepWTxK9m9/DF2b9pUflh8pB1iTeDJ7mskhZvD4iQ==", "shasum": "d39b4f612af451b8d0d1f365ddc6f7a002a19905", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.26-alpha.2.tgz", "fileCount": 116, "unpackedSize": 292916, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFSBYQnD82q5QQEimIsNDkwC0tXGPLaRSNCrgFAkRDOKAiEA4CTWTPcJsYCiAvwaaVh0rMTMt0qRH5I9Tvn0wfiX32Q="}], "size": 56287}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.26-alpha.2_1719896344424_0.5462838393262002"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T04:59:04.625Z", "publish_time": 1719896344625, "_source_registry_name": "default"}, "2.0.26": {"name": "@vue/language-core", "version": "2.0.26", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.15", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.15", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "47924f61e9409501d11ab04e9d3417cc5c86232c", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.0.26", "dist": {"integrity": "sha512-/lt6SfQ3O1yDAhPsnLv9iSUgXd1dMHqUm/t3RctfqjuwQf1LnftZ414X3UBn6aXT4MiwXWtbNJ4Z0NZWwDWgJQ==", "shasum": "233793b2e0a9f33db6f4bdac030d9c164b3efc0f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.26.tgz", "fileCount": 116, "unpackedSize": 294760, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCj5bWQYqyBZAkfwT75HTBEtVZi9WCYAN1/tGE5+cUEUwIhANY6HBArxTtokzSddFUw0knnjz2396R1zAut95ta3FfE"}], "size": 56530}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.26_1720097123927_0.3845867617180374"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-04T12:45:24.062Z", "publish_time": 1720097124062, "_source_registry_name": "default"}, "2.0.28": {"name": "@vue/language-core", "version": "2.0.28", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.18", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.18", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "0cdbd70996f4fc7ac8d511b0d9fdbe20b7a4f6a3", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.0.28", "dist": {"integrity": "sha512-0z4tyCCaqqPbdyz0T4yTFQeLpCo4TOM/ZHAC3geGLHeCiFAjVbROB9PiEtrXR1AoLObqUPFHSmKZeWtEMssSqw==", "shasum": "7363f9e2eb133587dd7b662be2c86613b7db081a", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.28.tgz", "fileCount": 122, "unpackedSize": 307678, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAGNhav3mLhvuyo2T1SG05bnt4/nxGqX4EgZ792d+RZaAiBxtkVDkDRmBBLrY0v+98nUJd72lwvemOm2YncNqgCywg=="}], "size": 58518}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.28_1721630130259_0.28766468553341684"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-22T06:35:30.437Z", "publish_time": 1721630130437, "_source_registry_name": "default"}, "2.0.29": {"name": "@vue/language-core", "version": "2.0.29", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.0-alpha.18", "@vue/compiler-dom": "^3.4.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.0-alpha.18", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "49ad9563e6f2677595878a000179dfea83fb910c", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.7.1/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.0.29", "dist": {"integrity": "sha512-o2qz9JPjhdoVj8D2+9bDXbaI4q2uZTHQA/dbyZT4Bj1FR9viZxDJnLcKVHfxdn6wsOzRgpqIzJEEmSSvgMvDTQ==", "shasum": "19462d786cd7a1c21dbe575b46970a57094e0357", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.0.29.tgz", "fileCount": 122, "unpackedSize": 307672, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGBWAbJSVLwhAtcvZG2ZFGwmzgAQW0Y/39dG9KRl/EBvAiEAikimucPGM+KnCP+kptwlnxl/zjR5RTsv9mpfFvB05QY="}], "size": 58524}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.0.29_1721897094778_0.3577994525436783"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-25T08:44:54.977Z", "publish_time": 1721897094977, "_source_registry_name": "default"}, "2.1.0": {"name": "@vue/language-core", "version": "2.1.0", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.1", "@vue/compiler-dom": "^3.4.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "510063740b90b64caedaee1f0bde70974613a92c", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.8.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.0", "dist": {"integrity": "sha512-S7uUdQXn4aA7QloQeAIhKDkP243nCEktQu1Kxur1fJFfedf+izBTb9bAR2tHe0V7xhFYhIwkNCtQl2AlKFaW3w==", "shasum": "a6b3f48c09ac4f0af43a15c12af51a4f193c66ef", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.0.tgz", "fileCount": 124, "unpackedSize": 320715, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEtCIEHcxtRgH1B6Gech+8sRNmskVktNUjwd+LpXOTRgIhAMmR9VhkzdKQo7HTc0zrkaiPFBk2l0rJ2XJ/6FAVkNig"}], "size": 61847}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.0_1724891395839_0.1309149709652906"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-29T00:29:56.047Z", "publish_time": 1724891396047, "_source_registry_name": "default"}, "2.1.2": {"name": "@vue/language-core", "version": "2.1.2", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.1", "@vue/compiler-dom": "^3.4.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "4e4b839ea20ae11a2aef7ee9206465cb60a4be53", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.8.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.2", "dist": {"integrity": "sha512-tt2J7C+l0J/T5PaLhJ0jvCCi0JNwu3e8azWTYxW3jmAW5B/dac0g5UxmI7l59CQgCGFotqUqI3tXjfZgoWNtog==", "shasum": "98e7864ab4a928293a88ebf7b427d773e1a40e2e", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.2.tgz", "fileCount": 124, "unpackedSize": 320690, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDdMWSrOuvNPSUy7y5c2zmD2ob33rhGp2rU9YWc7xATWAiEAv7F4L5dNI/YoWg4MYXThrsL3ee0Y7bmswz3L4GWlKGY="}], "size": 61823}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.2_1724919850899_0.4683398552637197"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-29T08:24:11.058Z", "publish_time": 1724919851058, "_source_registry_name": "default"}, "2.1.4": {"name": "@vue/language-core", "version": "2.1.4", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.1", "@vue/compiler-dom": "^3.4.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "5e197d08eaef57209ff2927c943ba1db3bf4eff6", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.8.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.4", "dist": {"integrity": "sha512-i8pfAgNjTNjabBX1xRsuV6aRw2E8bdQXwd5H8m3cUkTVJju3QN5nfdoXET0uK+yXsuloNJPzo6PXFujRRPNmMA==", "shasum": "7be59322a87abbcd9aa23f45fbdfaa1f77bb6e22", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.4.tgz", "fileCount": 128, "unpackedSize": 332874, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1CK1CGPsRdmJflcG5E5L/CWsLPse+LreufS68jy/DzwIgEWCRTihDDwfiSuihCmoIGlkowPU3+ASDQwKFCby65Es="}], "size": 63549}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.4_1725130573994_0.8396963325403068"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-31T18:56:14.184Z", "publish_time": 1725130574184, "_source_registry_name": "default"}, "2.1.5": {"name": "@vue/language-core", "version": "2.1.5", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.1", "@vue/compiler-dom": "^3.4.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a95b51ac0b0db8825f77fbba37e29932b5be61e4", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.5", "dist": {"integrity": "sha512-YwF5+2v+rnue2/17wP84tnZgaB2hUM4C1U+115vVGrDAPGhBf7YG0nPv1SO0yUeEpYMYtED4ClamkXAwpoz2Yw==", "shasum": "239742775d7829062d06d1fd02fc102e4bf1ff25", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.5.tgz", "fileCount": 128, "unpackedSize": 332874, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEo5zXFcJTbeY5Uo+TOm3npjUmXvC3kkANe3azA7lMYnAiEA6uNsGR3DXXNT50cGGOPxPplDKbF5c091rb3VzydQAQw="}], "size": 63550}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.5_1725131122785_0.7920994318008185"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-31T19:05:22.925Z", "publish_time": 1725131122925, "_source_registry_name": "default", "deprecated": "use 2.1.4 instead"}, "2.1.6": {"name": "@vue/language-core", "version": "2.1.6", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.1", "@vue/compiler-dom": "^3.4.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "fd61953ce9eb924eeaf4df0bf8d2237267321194", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.6", "dist": {"integrity": "sha512-MW569cSky9R/ooKMh6xa2g1D0AtRKbL56k83dzus/bx//RDJk24RHWkMzbAlXjMdDNyxAaagKPRquBIxkxlCkg==", "shasum": "b48186bdb9b3ef2b83e1f76d5b1ac357b3a7ed94", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.6.tgz", "fileCount": 126, "unpackedSize": 328447, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHsFUladRkRDqxitdMAQJHIunppVgFEsOF3WbqQ1iXvyAiBQSnoBI4vzTjQrGcVVXIRkNNhu9xDHXSEkWDQ7P9gCaQ=="}], "size": 63198}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.6_1725476841966_0.621903913523076"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-04T19:07:22.133Z", "publish_time": 1725476842133, "_source_registry_name": "default"}, "2.1.6-patch.1": {"name": "@vue/language-core", "version": "2.1.6-patch.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.1", "@vue/compiler-dom": "^3.5.2", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.2", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.1", "@vue/compiler-sfc": "^3.5.2"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_id": "@vue/language-core@2.1.6-patch.1", "gitHead": "d8ed4c08389771668b6bb13b77e0c0965cc0e3df", "types": "./index.d.ts", "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-6RPnCRBwUc/gSsO62bYETeqoDIss2lHercnDD7/Aa4j288i6upVF5ihpcgjTTActpFSerpQ06Ig77L64y5QC8A==", "shasum": "5dce8fadd519e0cc360ab407e4641feb538e8d35", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.6-patch.1.tgz", "fileCount": 126, "unpackedSize": 331215, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF1WW0foeKhL73yGTEJ+pU9EX9ffxW0kcrBvdiMqPAe0AiAFEMLLQ/q8Krxurhy0nU102XjmM0n+YBtz66f8jS6QgQ=="}], "size": 62703}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.6-patch.1_1726227259215_0.7750957549851389"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-13T11:34:19.434Z", "publish_time": 1726227259434, "_source_registry_name": "default"}, "2.1.8": {"name": "@vue/language-core", "version": "2.1.8", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.8", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^0.2.0", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.8", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "25cccedc53e7361ed4e34296d6ecd43d7de2a095", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.10.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.8", "dist": {"integrity": "sha512-DtPUKrIRqqzY1joGfVHxHWZoxXZbCQLmVtW+QTifuPInfcs1R/3UAdlJXDp+lpSpP9lI5m+jMYYlwDXXu3KSTg==", "shasum": "1bc68852021f7972649c448c44cdaeeb801c2363", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.8.tgz", "fileCount": 128, "unpackedSize": 335367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGHzTuRChLvsDJb94fkaYvxifNXe8u77+hwW2EIK7U70AiACs91IbbWAKbShonTXB8kqm+xedaXy0O+u5AMQFflkDw=="}], "size": 64801}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.8_1729958379471_0.5337532549872528"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-26T15:59:39.702Z", "publish_time": 1729958379702, "_source_registry_name": "default"}, "2.1.10": {"name": "@vue/language-core", "version": "2.1.10", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.8", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^0.2.0", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.8", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "b0af30caee2f8dfb1a8393c1b400f38e31fa4883", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.10.0/node@v20.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.1.10", "dist": {"integrity": "sha512-DAI289d0K3AB5TUG3xDp9OuQ71CnrujQwJrQnfuZDwo6eGNf0UoRlPuaVNO+Zrn65PC3j0oB2i7mNmVPggeGeQ==", "shasum": "5988e9ea155f3e09ccbbb3b2a0ddd530dad912e6", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.1.10.tgz", "fileCount": 126, "unpackedSize": 335674, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCyGSg9TP5wRtsrKnRir7jWZrWAiZilxjgeD6g6OBtNwIhANDe0QHCERhShuCHoKSXqquH9kqbO1jS+cKyiiBGx0PK"}], "size": 64751}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/language-core_2.1.10_1730314940448_0.5243208795064991"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-30T19:02:20.642Z", "publish_time": 1730314940642, "_source_registry_name": "default"}, "2.2.0": {"name": "@vue/language-core", "version": "2.2.0", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^0.4.9", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "5babca774658d4b9afbe877ac7c8cafdaecf2c3e", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/3.10.1/node@v22.11.0+arm64 (darwin)", "_id": "@vue/language-core@2.2.0", "dist": {"integrity": "sha512-O1ZZFaaBGkKbsRfnVH1ifOK1/1BUkyK+3SQsfnh6PmMmD4qJcTU8godCeA96jjDRTL6zgnK7YzCHfaUlH2r0Mw==", "shasum": "e48c54584f889f78b120ce10a050dfb316c7fcdf", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.0.tgz", "fileCount": 128, "unpackedSize": 336321, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDU5jf+63/56R2b9arvYjbYL/D9OLMbNjN78v57HHQ7nAiEA2QfugJxLowqsBFHu4LRhqLOcQ7AmveZaBG/H76hdqBQ="}], "size": 65328}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.0_1734984387235_0.7077210190709977"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-23T20:06:27.446Z", "publish_time": 1734984387446, "_source_registry_name": "default"}, "2.2.2": {"name": "@vue/language-core", "version": "2.2.2", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "30757908b67f40f779c36795665163634fb81868", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.11.0/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@2.2.2", "dist": {"integrity": "sha512-QotO41kurE5PLf3vrNgGTk3QswO2PdUFjBwNiOi7zMmGhwb25PSTh9hD1MCgKC06AVv+8sZQvlL3Do4TTVHSiQ==", "shasum": "aa0e916bf18434077887e8ba269729b45564f625", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.2.tgz", "fileCount": 156, "unpackedSize": 361031, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC30Oy+ytjihIWhDwuqoFnmsCj4M3YTaBCz3Ra/9JJkYAIhAIxiTO4CiVaLwOxI8E2OMR0t0NwZqieQhWz02PJ/VA6P"}], "size": 70223}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.2_1739635679786_0.8611317844315434"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-15T16:08:00.011Z", "publish_time": 1739635680011, "_source_registry_name": "default"}, "2.2.4": {"name": "@vue/language-core", "version": "2.2.4", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "c28986596935cb43979c9d437c25f292bdb36cef", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.11.0/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@2.2.4", "dist": {"integrity": "sha512-eGGdw7eWUwdIn9Fy/irJ7uavCGfgemuHQABgJ/hU1UgZFnbTg9VWeXvHQdhY+2SPQZWJqWXvRWIg67t4iWEa+Q==", "shasum": "5cad43a4cd6f388bebc3e8c6f303df489f5e8829", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.4.tgz", "fileCount": 138, "unpackedSize": 362006, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHeDOL9GjUJoASdaZd3XhyHesMS1eJvmUQqhJl6UbSQOAiEAxamhDn47ahv7BDBCH4Lfvm04MoUjA6ufxrneFBytdDs="}], "size": 71990}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.4_1740314185927_0.5437644232780554"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-23T12:36:26.128Z", "publish_time": 1740314186128, "_source_registry_name": "default"}, "2.2.6": {"name": "@vue/language-core", "version": "2.2.6", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "f2088e256dc0220db9549d28d9f865a5711dcfbe", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.11.0/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@2.2.6", "dist": {"integrity": "sha512-7IQTvwVOvhYSzcizZ2hAdqJjI+SaJS2GO7EnDlSlQ77drwl5UzNa2IZm9pO9MdMxrlw24CGI/Lo3xpCjM26veg==", "shasum": "0f0256f7594d1825f84f91a868534700dea55252", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.6.tgz", "fileCount": 146, "unpackedSize": 366019, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICFpAYoqXI0PlWbP1o/XuWZK5wgBXSoXVA0Heg1bKar8AiEA2fBSrVml4Zb0xVTECpbRqsRzKGlZPK8qOM9hCcZrxrk="}], "size": 73121}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.6_1740822887060_0.628690969436384"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-01T09:54:47.304Z", "publish_time": 1740822887304, "_source_registry_name": "default"}, "2.2.8": {"name": "@vue/language-core", "version": "2.2.8", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "cdf00e6f19971260607ea2347924b94126a254fc", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.11.0/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@2.2.8", "dist": {"integrity": "sha512-rrzB0wPGBvcwaSNRriVWdNAbHQWSf0NlGqgKHK5mEkXpefjUlVRP62u03KvwZpvKVjRnBIQ/Lwre+Mx9N6juUQ==", "shasum": "05befa390399fbd4409bc703ee0520b8ac1b7088", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.8.tgz", "fileCount": 146, "unpackedSize": 364858, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCrE9x2yWyAmCKyj4zeXCqS5uhLvg2PwTHg1WXIgAjLWwIhAJz/NTykN7bDrBQHmx3Z3QWBmKk1OqY6OghK59h0NRe5"}], "size": 72976}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.8_1740910811969_0.5756741659028763"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-02T10:20:12.206Z", "publish_time": 1740910812206, "_source_registry_name": "default"}, "3.0.0-alpha.0": {"name": "@vue/language-core", "version": "3.0.0-alpha.0", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "4b49cbe09097e482def4603b90f6c3b93bb2e913", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/3.11.0/node@v22.11.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.0-alpha.0", "dist": {"integrity": "sha512-X480yCIrLgZ591y0mnCI51PsUx1jvJg4cbXUR9ZHF7ygvh4MbrYuoNniL9ijAutjlj4DO1C/ZpoVzyYGL8l4+g==", "shasum": "4aa5162e6c51af73dd1291dd3bf6678f8c045ccd", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-alpha.0.tgz", "fileCount": 142, "unpackedSize": 359835, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDoVbuw9nC4Qy0LcNCrFBunPQy1s1L4TWNwox3PIeHWuAiEA/74UANkxMzjBlRjGrjMdOAWQyu08Z9RrdlB+3cX5G9I="}], "size": 71925}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-alpha.0_1741383107740_0.42840610311078176"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-07T21:31:47.944Z", "publish_time": 1741383107944, "_source_registry_name": "default"}, "3.0.0-alpha.2": {"name": "@vue/language-core", "version": "3.0.0-alpha.2", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "79247b7c24b7202ec676723440fdb36c38e6d450", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.11.0/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@3.0.0-alpha.2", "dist": {"integrity": "sha512-GFbo3BM7sxkSNSZVz6+Pq3f/sEyRspGb+7Sqoxnm/rCjPtFOklcIADakhXFN4VmKgwOO1mfjCzuvTkZORv3BMw==", "shasum": "29b36008bd5b564b53caf0c4adc5bbd261abea63", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-alpha.2.tgz", "fileCount": 148, "unpackedSize": 364133, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFnviYE4MWs5QWO4UY9easyjuR9Ff2dG6WDJQI1LBeAjAiA+RpEcFyIUBQfB8aQnCWOUqiC0RnJLr8mhHPDy73rLjA=="}], "size": 73016}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-alpha.2_1742141980817_0.7161835601343529"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-16T16:19:41.059Z", "publish_time": 1742141981059, "_source_registry_name": "default"}, "3.0.0-alpha.4": {"name": "@vue/language-core", "version": "3.0.0-alpha.4", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "1769cd6b94ec9e0cc2681b8dbba904f35856ba1c", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.12.3/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@3.0.0-alpha.4", "dist": {"integrity": "sha512-trrw/32ZMf9VHCE2WDdOK0QfDJ2mQi1wB8Q/p80RHROm1YK8+t0vz7nLnM3Ai1/F1sPC3xVJu+nQekcFTyyGrw==", "shasum": "c38de2a514279e2ece1403af12e444d4b24b4a78", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-alpha.4.tgz", "fileCount": 146, "unpackedSize": 364659, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCf+E5p2b/s686IR/UWrS7xq9ahPcavzJzMFdNB9JKBQgIgFnpEujsGjuPNVSIQ5y/hMTnhfWpyA8BUlJL+63d3kBY="}], "size": 73290}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-alpha.4_1744122957522_0.6582717353597851"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-08T14:35:57.733Z", "publish_time": 1744122957733, "_source_registry_name": "default"}, "2.2.10": {"name": "@vue/language-core", "version": "2.2.10", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.11", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "0422c03ffa4958431c9cd3cd19ae51f726c30b07", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.12.3/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@2.2.10", "dist": {"integrity": "sha512-+yNoYx6XIKuAO8Mqh1vGytu8jkFEOH5C8iOv3i8Z/65A7x9iAOXA97Q+PqZ3nlm2lxf5rOJuIGI/wDtx/riNYw==", "shasum": "5ae1e71a4e16dd59d1e4bac167f4b9c8c04d9f17", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.10.tgz", "fileCount": 142, "unpackedSize": 361515, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDtsGDDWytXsbii1/477Ldr7lurzSXs352GUozbQ4aJsAIgO7ujQujcdpshTpIKHIYP3yhxt1HrEIjHn8Um683evPA="}], "size": 72396}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.10_1745337877474_0.007922817719520747"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-22T16:04:37.656Z", "publish_time": 1745337877656, "_source_registry_name": "default"}, "3.0.0-alpha.6": {"name": "@vue/language-core", "version": "3.0.0-alpha.6", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.13", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.13", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "a7b5649ab4957cd2228f4bbc9205b2008bff58a2", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/3.12.3/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@3.0.0-alpha.6", "dist": {"integrity": "sha512-aZoL7H7+5SGkx4uqp5tC1/50tROsFgVyk5XbfM+HGfTZMwvvoHMq4uTzRyyH5sH8eXs/zmjlZCg2Vs8khxUqjQ==", "shasum": "815c1ddefeae7e5e73ebee4666894188f44f7262", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-alpha.6.tgz", "fileCount": 144, "unpackedSize": 363784, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAkWrGOjb0gdmk733tZxD8QRCcpMxBTL9gBloM3WHm1bAiEA3Aa4lKhRPe0+Ye2TAB9/wMQkdM0Gz1X4Dzr8tOS8XVw="}], "size": 73162}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-alpha.6_1746435971184_0.5078226248464088"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-05T09:06:11.392Z", "publish_time": 1746435971392, "_source_registry_name": "default"}, "3.0.0-alpha.8": {"name": "@vue/language-core", "version": "3.0.0-alpha.8", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.13", "@vue/compiler-dom": "^3.5.0", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.13", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "d38cb93558fe8015c7ffe9ceacfdd3296e3692f6", "_nodeVersion": "23.11.0", "_npmVersion": "lerna/4.1.2/node@v23.11.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.0-alpha.8", "dist": {"integrity": "sha512-QZkChTY/jT6w1EDLpgJjKPjWECIvvmm1Pbh9cOHHec7jvoezf+T4oLEwNElVyTRP3kUIMeOAutyZc/OuS+3azg==", "shasum": "1e45ce016e24ed70cb0ba43191ba5330f86f3945", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-alpha.8.tgz", "fileCount": 142, "unpackedSize": 360211, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC28gH3ieuCOpLW/auCxejhBGE1Q4l7eyM/d+5ioRDIzwIhAMNxfSFy7u/fqzVJYmlY9QR+D8FW7GfAmGUlpBSL2sH9"}], "size": 72417}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-alpha.8_1748264451440_0.3475324650904692"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-26T13:00:51.614Z", "publish_time": 1748264451614, "_source_registry_name": "default"}, "3.0.0-alpha.10": {"name": "@vue/language-core", "version": "3.0.0-alpha.10", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.4.13", "@vue/compiler-dom": "^3.5.0", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "~2.4.13", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "28308b4f76cc80c7632f39ae7e0944f1889661a2", "_nodeVersion": "23.0.0", "_npmVersion": "lerna/4.1.2/node@v23.0.0+x64 (win32)", "_id": "@vue/language-core@3.0.0-alpha.10", "dist": {"integrity": "sha512-yTFidT1rb8H/nVnwEmeRWRi2XZF0vIfUg8nsb7xIapg3fLr3gJsrCNwsQcdR7gGbXGGDXt65AEQ1F9VR8B3Row==", "shasum": "452dc2b30bd468595c32b210ec1ec524316ebdbd", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-alpha.10.tgz", "fileCount": 142, "unpackedSize": 357723, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBU2o5kl71xfLzCw+iydu0XAQ+4YdE7T6GQ1W0ePJd6TAiBez3Bu8pEJHABO0N87NyqV2Gnh/YUOJBh8bUMvbxTJDA=="}], "size": 71769}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-alpha.10_1749313485706_0.3926491578082203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-07T16:24:45.975Z", "publish_time": 1749313485975, "_source_registry_name": "default"}, "3.0.0-beta.1": {"name": "@vue/language-core", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vue/language-core@3.0.0-beta.1", "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "dist": {"shasum": "bc1df1d4ad5f885907dcfbce0578144338998fb2", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-beta.1.tgz", "fileCount": 144, "integrity": "sha512-3S4e+zZ7vpAp4d4csOVlsetx1TydGTOvkjX6B5Rj+/ZaGSebT3bzWUa+5oQrS0BfPPzB6cH8cGdMySTi2LSbNQ==", "signatures": [{"sig": "MEYCIQDQXHYrEl5ERD44vJSXDMlDg+HzqH86PIcRfNK+WcKY5wIhAJd+B3Uih+kvH+a4SCPxf+oYicXsbzi3GgIGqHIqzaZr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 356364, "size": 71685}, "gitHead": "7a2ea48123679387d7095a81ac49cfc667aeeabb", "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/vuejs/language-tools.git", "type": "git", "directory": "packages/language-core"}, "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "dependencies": {"minimatch": "^10.0.1", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "@vue/compiler-dom": "^3.5.0", "@volar/language-core": "2.4.14"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^22.10.4", "@volar/typescript": "2.4.14", "@vue/compiler-sfc": "^3.5.0", "@types/path-browserify": "^1.0.1"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/language-core_3.0.0-beta.1_1749941157314_0.841869966217331", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-14T22:45:57.492Z", "publish_time": 1749941157492, "_source_registry_name": "default"}, "3.0.0-beta.2": {"name": "@vue/language-core", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vue/language-core@3.0.0-beta.2", "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "dist": {"shasum": "44eb0e1fa3998971d9e165396f00fa43f3aba10f", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-beta.2.tgz", "fileCount": 144, "integrity": "sha512-T6og/HbkzNy9WmCXnitboQWs8q+BxVk8kmMqdR8v2Lo/ZMrOA9KPljXM1ib8B/kP5pnoNpwPPgTLAp1dyzcYuQ==", "signatures": [{"sig": "MEUCIHKnOiYOEOcjgshwHNYvCPB4vtLcnKJsesVxU4JxeVY9AiEA655XGZ+K+V0s7wZBBkA/tkZN+gOt5+Nr9midBKi7QIo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 356419, "size": 71688}, "gitHead": "ea40288f6fceebb65346732b6de5859c300cf1ee", "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/vuejs/language-tools.git", "type": "git", "directory": "packages/language-core"}, "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "dependencies": {"minimatch": "^10.0.1", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1", "@vue/compiler-dom": "^3.5.0", "@volar/language-core": "2.4.14"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^22.10.4", "@volar/typescript": "2.4.14", "@vue/compiler-sfc": "^3.5.0", "@types/path-browserify": "^1.0.1"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/language-core_3.0.0-beta.2_1750000674514_0.49474887678262913", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-15T15:17:54.737Z", "publish_time": 1750000674737, "_source_registry_name": "default"}, "3.0.0-beta.3": {"name": "@vue/language-core", "version": "3.0.0-beta.3", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.14", "@vue/compiler-dom": "^3.5.0", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.14", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "17e3beabc13e9eb59a82fb1a9f0252fd6685e444", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.0-beta.3", "dist": {"integrity": "sha512-AIJrOoDCy9yTnWwoKGLlK9pTJ7dCEoo9Txy/s2ctwqaMU2a/n2ZqboXeypFtjro2FfC4oW4Sph7n5N4WQhEAGg==", "shasum": "9e90c5df907aec53ceb103b99de1a53ed4e0e8f5", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-beta.3.tgz", "fileCount": 144, "unpackedSize": 357193, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDxXw6jlbVgjbuTQZWbKt/w0msRw7jv6wFFZ99OR7kOSwIgX61O1wjThnRbG2SovUI6rs8GdbotFOF2/W8EJGw/ZCU="}], "size": 71776}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>", "actor": {"name": "johnsoncodehk", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-beta.3_1750586312073_0.7746345910662218"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-22T09:58:32.284Z", "publish_time": 1750586312284, "_source_registry_name": "default"}, "3.0.0-beta.4": {"name": "@vue/language-core", "version": "3.0.0-beta.4", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.15", "@vue/compiler-dom": "^3.5.0", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.15", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "5cb41faacbfe2d9e2d64637c6c1ae8769d9cba3f", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.0-beta.4", "dist": {"integrity": "sha512-vMXSuM1v65PYWmRP9ULZ2DRF34f8LKYmfjWvQ2sZ+yfTtHZa6EInQbKjzIHMAK6JIdy8k1V3ET7HeXaz+IkOLw==", "shasum": "e2ef77000424f7897a29df86166c6e256b446ec7", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-beta.4.tgz", "fileCount": 144, "unpackedSize": 359472, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDKwWwoqeN7L3loTN/BGThaDLb04IUH8dM6Ex48h2wglwIgCCnLsWs+OTiiTANHmbTbDWgcpiA7UeC0EF+dVo6TFiw="}], "size": 71968}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>", "actor": {"name": "johnsoncodehk", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-beta.4_1750858578435_0.4900714837714535"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-25T13:36:18.633Z", "publish_time": 1750858578633, "_source_registry_name": "default"}, "3.0.0-beta.5": {"name": "@vue/language-core", "version": "3.0.0-beta.5", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.15", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.15", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "3a4648914c60c90444d939cf762a016a4318ca09", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.0-beta.5", "dist": {"integrity": "sha512-WD7OTJSbiIDe7hpp4rlptW+8X7IDHLbuPwG17MGzezU6oSBjGKtbPrQm5n4gaWgEqcMaoffddT/djIPxE1NFMw==", "shasum": "5f412d45f39f17fde33a52fb6dafe0e2ea2cb7f6", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0-beta.5.tgz", "fileCount": 146, "unpackedSize": 365979, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEMr8Gfe5UJP7p5h8nOcg5TUp32JcsJtvdUvwolSdbK0AiA+4jCQpHQ0ac/DfDOQ3itPFQQTm3DXulJUTZFhxiCBSA=="}], "size": 73433}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>", "actor": {"name": "johnsoncodehk", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0-beta.5_1751047652563_0.1773711898515018"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-27T18:07:32.752Z", "publish_time": 1751047652752, "_source_registry_name": "default"}, "3.0.0": {"name": "@vue/language-core", "version": "3.0.0", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.16", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.16", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "620f050fd494cccd215781907c950e17dae4fd58", "_nodeVersion": "23.11.0", "_npmVersion": "lerna/4.1.2/node@v23.11.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.0", "dist": {"integrity": "sha512-V+bSGOIuE59SAVNwzpoyk3VoytmqKPd3RBaHb0ROmK5SNOe9+774t4zmWxzztZGAg3FOGQnb1ZhXECyXLPzWZA==", "shasum": "54c54df457f5ea66c6bf23eccb3aa6ed227eeb42", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.0.tgz", "fileCount": 146, "unpackedSize": 365984, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHa4Nj0uNePert+o6eYmxyztQTRtYWtnObnA3GS0jbzZAiADx9gBOscr9gq9/LLOvvSNLGMr5hcFrfiEe8eaXjM0cw=="}], "size": 73424}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>", "actor": {"name": "kazariex", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.0_1751383614861_0.9687654873442839"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T15:26:55.067Z", "publish_time": 1751383615067, "_source_registry_name": "default"}, "2.2.12": {"name": "@vue/language-core", "version": "2.2.12", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.15", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.15", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "0b13bf1966398ea3949b6b02d09b251ddc9a51eb", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/3.11.0/node@v22.14.0+arm64 (darwin)", "_id": "@vue/language-core@2.2.12", "dist": {"integrity": "sha512-IsGljWbKGU1MZpBPN+BvPAdr55YPkj2nB/TBNGNC32Vy2qLG25DYu/NBN2vNtZqdRbTRjaoYrahLrToim2NanA==", "shasum": "d01f7e865f593f968cb65c12a13d8337e65641f0", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.12.tgz", "fileCount": 142, "unpackedSize": 361513, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEzrKeogmpfQVp+c1IYxhhl799xmV3U+ExNMq2UX7tn9AiBbWpDXafQSYSG0V4SOLysJCzAEjuL9w2UfuaIKrh9EEA=="}], "size": 72396}, "_npmUser": {"name": "johnsoncodehk", "actor": {"name": "johnsoncodehk", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_2.2.12_1751417835562_0.7214377203942832"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T00:57:15.733Z", "publish_time": 1751417835733, "_source_registry_name": "default"}, "3.0.1": {"name": "@vue/language-core", "version": "3.0.1", "license": "MIT", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "2.4.17", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^2.0.5", "minimatch": "^10.0.1", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1", "@volar/typescript": "2.4.17", "@vue/compiler-sfc": "^3.5.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "43884409838dfdce44de51f6622926ac6ddd7318", "_nodeVersion": "23.11.0", "_npmVersion": "lerna/4.1.2/node@v23.11.0+arm64 (darwin)", "_id": "@vue/language-core@3.0.1", "dist": {"integrity": "sha512-sq+/Mc1IqIexWEQ+Q2XPiDb5SxSvY5JPqHnMOl/PlF5BekslzduX8dglSkpC17VeiAQB6dpS+4aiwNLJRduCNw==", "shasum": "809790739e5d0ce66b1a8c4b57255d3f43bdd73b", "tarball": "https://registry.npmmirror.com/@vue/language-core/-/language-core-3.0.1.tgz", "fileCount": 146, "unpackedSize": 365321, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIByr8eiN87PgWZKfLcJmUM6I7ppUmSFrick6YtFOllx4AiEA33QRIoWpXpvPx5/o3EDW07+G1oWFQ0OGhrD5i6hg1yA="}], "size": 73285}, "_npmUser": {"name": "kazariex", "email": "<EMAIL>", "actor": {"name": "kazariex", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/language-core_3.0.1_1751458032070_0.6034579149609012"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T12:07:12.223Z", "publish_time": 1751458032223, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/vuejs/language-tools/issues"}, "homepage": "https://github.com/vuejs/language-tools#readme", "license": "MIT", "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}, {"name": "kazariex", "email": "<EMAIL>"}], "readme": "", "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "_source_registry_name": "default"}