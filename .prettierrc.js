module.exports = {
  // 基础配置
  semi: true, // 语句末尾添加分号
  singleQuote: true, // 使用单引号
  quoteProps: 'as-needed', // 对象属性引号按需添加
  trailingComma: 'es5', // 尾随逗号

  // 缩进和空格
  tabWidth: 2, // 缩进宽度
  useTabs: false, // 使用空格而不是制表符

  // 换行
  printWidth: 80, // 每行最大字符数
  endOfLine: 'lf', // 换行符类型

  // Vue 特定配置
  vueIndentScriptAndStyle: false, // Vue 文件中 script 和 style 标签不缩进

  // HTML 配置
  htmlWhitespaceSensitivity: 'css', // HTML 空白敏感度

  // 其他
  bracketSpacing: true, // 对象字面量的大括号间添加空格
  bracketSameLine: false, // 多行元素的 > 放在下一行
  arrowParens: 'avoid', // 箭头函数参数括号
};
