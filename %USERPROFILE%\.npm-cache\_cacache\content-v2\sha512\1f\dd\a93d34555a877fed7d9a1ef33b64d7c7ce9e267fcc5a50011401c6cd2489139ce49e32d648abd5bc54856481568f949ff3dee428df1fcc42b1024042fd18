{"_attachments": {}, "_id": "prettier-linter-helpers", "_rev": "390-61f14475963ca28f5ee36c3a", "description": "Utilities to help expose prettier output in linting tools", "dist-tags": {"latest": "1.0.0"}, "license": "MIT", "maintainers": [{"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "prettier-linter-helpers", "readme": "# prettier-linter-helpers\n\nHelper functions for exposing prettier changes within linting tools.\n\nThis package contains:\n\n- `showInvisibles(string)` - Replace invisible characters with ones you can see for\n  for easier diffing.\n- `generateDifferences(source, prettierSource)` - Generate an array of\n  differences between two strings.\n\n## Inspiration\n\nThis code was extracted from [eslint-plugin-prettier v2.7.0](https://github.com/prettier/eslint-plugin-prettier/blob/v2.7.0/eslint-plugin-prettier.js#L85-L215)\n", "time": {"created": "2022-01-26T12:54:13.479Z", "modified": "2023-07-28T03:42:50.740Z", "1.0.0": "2018-10-01T04:28:34.826Z", "0.1.0": "2018-10-01T04:22:18.416Z"}, "versions": {"1.0.0": {"name": "prettier-linter-helpers", "version": "1.0.0", "description": "Utilities to help expose prettier output in linting tools", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "main": "index.js", "license": "MIT", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/prettier-linter-helpers.git"}, "bugs": {"url": "https://github.com/prettier/prettier-linter-helpers/issues"}, "homepage": "https://github.com/prettier/prettier-linter-helpers#readme", "dependencies": {"fast-diff": "^1.1.2"}, "devDependencies": {"eslint": "^5.6.1", "eslint-config-prettier": "^3.1.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-prettier": "^2.7.0", "mocha": "^5.2.0", "prettier": "^1.14.3"}, "engines": {"node": ">=6.0.0"}, "licenseText": "# The MIT License (MIT)\n\nCopyright © 2017 <PERSON><PERSON> and <PERSON>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the “Software”), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n", "_id": "prettier-linter-helpers@1.0.0", "dist": {"shasum": "d23d41fe1375646de2d0104d3454a3008802cf7b", "size": 4297, "noattachment": false, "tarball": "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "integrity": "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="}, "maintainers": [{"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prettier-linter-helpers_1.0.0_1538368114713_0.019988871500704164"}, "_hasShrinkwrap": false, "publish_time": 1538368114826, "_cnpm_publish_time": 1538368114826, "_cnpmcore_publish_time": "2021-12-16T13:17:55.646Z"}, "0.1.0": {"name": "prettier-linter-helpers", "version": "0.1.0", "description": "Utilities to help expose prettier output in linting tools", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "main": "index.js", "license": "MIT", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/prettier-linter-helpers.git"}, "bugs": {"url": "https://github.com/prettier/prettier-linter-helpers/issues"}, "homepage": "https://github.com/prettier/prettier-linter-helpers#readme", "dependencies": {"fast-diff": "^1.1.2"}, "devDependencies": {"eslint": "^5.6.1", "eslint-config-prettier": "^3.1.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-prettier": "^2.7.0", "mocha": "^5.2.0", "prettier": "^1.14.3"}, "engines": {"node": ">=6.0.0"}, "licenseText": "# The MIT License (MIT)\n\nCopyright © 2017 <PERSON><PERSON> and <PERSON>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the “Software”), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n", "_id": "prettier-linter-helpers@0.1.0", "dist": {"shasum": "0221c15f03025b722407d0ebfeee7dea960ec853", "size": 4291, "noattachment": false, "tarball": "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-0.1.0.tgz", "integrity": "sha512-j5e6zr7nDAiX/R9iBSxOKzbzLxJ1OWiCA2yovXNWv7djZOwrB6mPeIwxB+0hip2iXYi6CTWpG+iGmwY27CFBmQ=="}, "maintainers": [{"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prettier-linter-helpers_0.1.0_1538367738220_0.18175366566775875"}, "_hasShrinkwrap": false, "publish_time": 1538367738416, "_cnpm_publish_time": 1538367738416, "_cnpmcore_publish_time": "2021-12-16T13:17:56.090Z"}}, "bugs": {"url": "https://github.com/prettier/prettier-linter-helpers/issues"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/prettier/prettier-linter-helpers#readme", "repository": {"type": "git", "url": "git+https://github.com/prettier/prettier-linter-helpers.git"}, "_source_registry_name": "default"}