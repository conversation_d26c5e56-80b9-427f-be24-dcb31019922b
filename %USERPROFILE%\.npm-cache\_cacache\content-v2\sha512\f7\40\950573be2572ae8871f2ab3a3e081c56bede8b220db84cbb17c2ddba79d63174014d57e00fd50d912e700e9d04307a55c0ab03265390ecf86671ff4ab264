{"_attachments": {}, "_id": "@babel/plugin-syntax-typescript", "_rev": "4334-61f14ffeb77ea98a7491eb8d", "author": "The Babel Team (https://babel.dev/team)", "description": "Allow parsing of TypeScript syntax", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "license": "MIT", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "name": "@babel/plugin-syntax-typescript", "readme": "# @babel/plugin-syntax-typescript\n\n> Allow parsing of TypeScript syntax\n\nSee our website [@babel/plugin-syntax-typescript](https://babeljs.io/docs/babel-plugin-syntax-typescript) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-typescript\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-typescript --dev\n```\n", "time": {"created": "2022-01-26T13:43:26.266Z", "modified": "2025-07-02T09:34:30.911Z", "7.16.5": "2021-12-13T22:28:06.641Z", "7.16.0": "2021-10-29T23:47:28.023Z", "7.14.5": "2021-06-09T23:11:55.787Z", "7.12.13": "2021-02-03T01:10:44.295Z", "7.12.1": "2020-10-15T22:39:54.154Z", "7.10.4": "2020-06-30T13:11:53.874Z", "7.10.1": "2020-05-27T22:07:15.576Z", "7.8.3": "2020-01-13T21:41:26.954Z", "7.8.0": "2020-01-12T00:16:27.540Z", "7.7.4": "2019-11-22T23:32:04.817Z", "7.3.3": "2019-02-15T21:14:27.075Z", "7.2.0": "2018-12-03T19:01:07.431Z", "7.1.5": "2018-11-06T22:21:24.800Z", "7.0.0": "2018-08-27T21:42:54.027Z", "7.0.0-rc.4": "2018-08-27T16:43:51.196Z", "7.0.0-rc.3": "2018-08-24T18:07:38.239Z", "7.0.0-rc.2": "2018-08-21T19:23:39.699Z", "7.0.0-rc.1": "2018-08-09T20:07:43.667Z", "7.0.0-rc.0": "2018-08-09T15:57:58.344Z", "7.0.0-beta.56": "2018-08-04T01:05:06.732Z", "7.0.0-beta.55": "2018-07-28T22:07:10.688Z", "7.0.0-beta.54": "2018-07-16T18:00:03.257Z", "7.0.0-beta.53": "2018-07-11T13:40:12.465Z", "7.0.0-beta.52": "2018-07-06T00:59:22.209Z", "7.0.0-beta.51": "2018-06-12T21:19:39.900Z", "7.0.0-beta.50": "2018-06-12T19:47:10.761Z", "7.0.0-beta.49": "2018-05-25T16:01:43.855Z", "7.0.0-beta.48": "2018-05-24T19:22:00.856Z", "7.0.0-beta.47": "2018-05-15T00:08:36.894Z", "7.0.0-beta.46": "2018-04-23T04:30:58.848Z", "7.0.0-beta.45": "2018-04-23T01:56:36.032Z", "7.0.0-beta.44": "2018-04-02T22:20:02.742Z", "7.0.0-beta.43": "2018-04-02T16:48:20.652Z", "7.0.0-beta.42": "2018-03-15T20:50:37.304Z", "7.0.0-beta.41": "2018-03-14T16:26:01.199Z", "7.0.0-beta.40": "2018-02-12T16:41:28.955Z", "7.0.0-beta.39": "2018-01-30T20:27:29.530Z", "7.0.0-beta.38": "2018-01-17T16:31:47.288Z", "7.0.0-beta.37": "2018-01-08T16:02:26.435Z", "7.0.0-beta.36": "2017-12-25T19:04:30.562Z", "7.0.0-beta.35": "2017-12-14T21:47:39.090Z", "7.0.0-beta.34": "2017-12-02T14:39:10.946Z", "7.0.0-beta.33": "2017-12-01T14:28:11.888Z", "7.0.0-beta.32": "2017-11-12T13:33:09.343Z", "7.0.0-beta.31": "2017-11-03T20:03:20.538Z", "7.0.0-beta.5": "2017-10-30T20:56:11.462Z", "7.0.0-beta.4": "2017-10-30T18:34:30.095Z", "7.16.7": "2021-12-31T00:21:51.071Z", "7.17.10": "2022-04-29T16:37:38.053Z", "7.17.12": "2022-05-16T19:32:44.221Z", "7.18.6": "2022-06-27T19:49:57.789Z", "7.20.0": "2022-10-27T13:19:11.103Z", "7.21.4": "2023-03-31T09:01:53.405Z", "7.21.4-esm": "2023-04-04T14:09:20.325Z", "7.21.4-esm.1": "2023-04-04T14:21:09.839Z", "7.21.4-esm.2": "2023-04-04T14:39:13.315Z", "7.21.4-esm.3": "2023-04-04T14:56:04.107Z", "7.21.4-esm.4": "2023-04-04T15:13:16.863Z", "7.22.5": "2023-06-08T18:21:13.503Z", "8.0.0-alpha.0": "2023-07-20T13:59:47.586Z", "8.0.0-alpha.1": "2023-07-24T17:51:32.225Z", "8.0.0-alpha.2": "2023-08-09T15:14:47.579Z", "8.0.0-alpha.3": "2023-09-26T14:56:43.047Z", "8.0.0-alpha.4": "2023-10-12T02:06:09.308Z", "7.23.3": "2023-11-09T07:03:45.369Z", "8.0.0-alpha.5": "2023-12-11T15:18:31.707Z", "8.0.0-alpha.6": "2024-01-26T16:13:53.483Z", "8.0.0-alpha.7": "2024-02-28T14:04:41.651Z", "7.24.1": "2024-03-19T09:48:26.115Z", "8.0.0-alpha.8": "2024-04-04T13:19:43.124Z", "7.24.6": "2024-05-24T12:24:24.427Z", "8.0.0-alpha.9": "2024-06-03T14:04:03.210Z", "8.0.0-alpha.10": "2024-06-04T11:19:55.251Z", "7.24.7": "2024-06-05T13:15:15.625Z", "8.0.0-alpha.11": "2024-06-07T09:15:24.251Z", "8.0.0-alpha.12": "2024-07-26T17:33:20.609Z", "7.25.4": "2024-08-22T09:34:26.647Z", "7.25.7": "2024-10-02T15:14:39.601Z", "7.25.9": "2024-10-22T15:20:57.541Z", "8.0.0-alpha.13": "2024-10-25T13:54:01.328Z", "8.0.0-alpha.14": "2024-12-06T16:53:52.682Z", "8.0.0-alpha.15": "2025-01-10T17:24:16.918Z", "8.0.0-alpha.16": "2025-02-14T11:58:54.163Z", "8.0.0-alpha.17": "2025-03-11T18:24:45.945Z", "7.27.1": "2025-04-30T15:08:43.818Z", "8.0.0-beta.0": "2025-05-30T15:50:55.850Z", "8.0.0-beta.1": "2025-07-02T09:04:10.349Z"}, "versions": {"7.16.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.16.5", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-typescript@7.16.5", "dist": {"shasum": "f47a33e4eee38554f00fb6b2f894fa1f5649b0b3", "integrity": "sha512-/d4//lZ1Vqb4mZ5xTep3dDK888j7BGM/iKqBmndBaoYAFPlPKrGU608VVBz5JeyAb6YQDjRu1UKqj86UhwWVgw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.5.tgz", "fileCount": 13, "unpackedSize": 3912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j2CRA9TVsSAnZWagAA370P/0XQo0tq++5cJ7OCVXGx\n/YmikEuGDh5PXMbQCb9ejbQb1AIpyXiYCnLxWnINZkzFQwKmnZB1Y0EVoY4V\nodi1uc7pwrAiJdYkFXqlIrmtP50zG02Ce0jbR14tC6xyqNov6SrRfJtQjOX2\nmFzomVsmj1+5VG9ilE4Pg1Pi1xhazUKQUYp14Y8knb/58L+YpyV6i+nMixJ4\nW/hFkmnNVs2knsGguOJX/vdLvYTvpJeI7HwubQSYW8YDySnSCe7AsPg71TMq\n/0ULK9G4Om1DcWbm7ACbRxrlQ0B2OfMmAa8QPEKC/6egSFd/vBjH/u95WTtj\n+FXMn+uCe2hIGbhGddsmzln++/ugASnvl7AoLKGGVpnu9GTlXbauQUA8kDms\nacHP07a+suHEuFJb0MLBEY7MAcpSMFTWXpJgXuZDid4dXJeSs8t/GrsHTLwp\n4jI+5v8tWkOeMCQa8Xbi+w3iCAvpcOlhUuXXVS3cAy2a1bOSIvegFmJnl+SB\n1bG14t4LmOfI7RU7ynwb7MDAypF7kYCXnwq9oxqaP5Un2PXy+NLQ9i0UuDOv\nklAASLgV7cViOm+M1cP/ppRud+D2jv9esxlexG7WcnCmgEn0hjJWeRV364+s\n8hherRVSgzvvXJCApfotX/y0V+MAvrh08zFKj2062SubhuxnXkIgLSxt+x6A\n5RTu\r\n=KpoM\r\n-----END PGP SIGNATURE-----\r\n", "size": 2279, "noattachment": false}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.16.5_1639434486472_0.15860421647114586"}, "_hasShrinkwrap": false, "publish_time": 1639434486641, "_cnpm_publish_time": 1639434486641, "_cnpmcore_publish_time": "2021-12-14T05:46:22.001Z"}, "7.16.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.16.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-typescript@7.16.0", "dist": {"shasum": "2feeb13d9334cc582ea9111d3506f773174179bb", "size": 2248, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.0.tgz", "integrity": "sha512-Xv6mEXqVdaqCBfJFyeab0fH2DnUoMsDmhamxsSi4j8nLd4Vtw213WMJr55xxqipC/YVWyPY3K0blJncPYji+dQ=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.16.0_1635551247845_0.1252926664445393"}, "_hasShrinkwrap": false, "publish_time": 1635551248023, "_cnpm_publish_time": 1635551248023, "_cnpmcore_publish_time": "2021-12-14T05:46:22.242Z"}, "7.14.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.14.5", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-typescript@7.14.5", "dist": {"shasum": "b82c6ce471b165b5ce420cf92914d6fb46225716", "size": 1743, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.14.5.tgz", "integrity": "sha512-u6OXzDaIXjEstBRRoBCQ/uKQKlbuaeE5in0RvWdA4pN6AhqxTIwUsnHPU1CFZA/amYObMsuWhYfRl3Ch90HD0Q=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.14.5_1623280315659_0.5891920793000249"}, "_hasShrinkwrap": false, "publish_time": 1623280315787, "_cnpm_publish_time": 1623280315787, "_cnpmcore_publish_time": "2021-12-14T05:46:22.452Z"}, "7.12.13": {"name": "@babel/plugin-syntax-typescript", "version": "7.12.13", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13"}, "_id": "@babel/plugin-syntax-typescript@7.12.13", "dist": {"shasum": "9dff111ca64154cef0f4dc52cf843d9f12ce4474", "size": 1703, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.12.13.tgz", "integrity": "sha512-cHP3u1JiUiG2LFDKbXnwVad81GvfyIOmCD6HIEId6ojrY0Drfy2q1jw7BwN7dE84+kTnBjLkXoL3IEy/3JPu2w=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.12.13_1612314644134_0.1504054991481807"}, "_hasShrinkwrap": false, "publish_time": 1612314644295, "_cnpm_publish_time": 1612314644295, "_cnpmcore_publish_time": "2021-12-14T05:46:22.621Z"}, "7.12.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.12.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1"}, "_id": "@babel/plugin-syntax-typescript@7.12.1", "dist": {"shasum": "460ba9d77077653803c3dd2e673f76d66b4029e5", "size": 1690, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.12.1.tgz", "integrity": "sha512-UZNEcCY+4Dp9yYRCAHrHDU+9ZXLYaY9MgBXSRLkB9WjYFRR6quJBumfVrEkUxrePPBwFcpWfNKXqVRQQtm7mMA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.12.1_1602801594013_0.4010102423089741"}, "_hasShrinkwrap": false, "publish_time": 1602801594154, "_cnpm_publish_time": 1602801594154, "_cnpmcore_publish_time": "2021-12-14T05:46:22.807Z"}, "7.10.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.10.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-typescript@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"shasum": "2f55e770d3501e83af217d782cb7517d7bb34d25", "size": 1725, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.10.4.tgz", "integrity": "sha512-oSAEz1YkBCAKr5Yiq8/BNtvSAPwkp/IyUnwZogd8p+F0RuYQQrLeRUzIQhueQTTBy/F+a40uS7OFKxnkRvmvFQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.10.4_1593522713722_0.7295866277888756"}, "_hasShrinkwrap": false, "publish_time": 1593522713874, "_cnpm_publish_time": 1593522713874, "_cnpmcore_publish_time": "2021-12-14T05:46:23.035Z"}, "7.10.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.10.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-typescript@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"shasum": "5e82bc27bb4202b93b949b029e699db536733810", "size": 1724, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.10.1.tgz", "integrity": "sha512-X/d8glkrAtra7CaQGMiGs/OGa6XgUzqPcBXCIGFCpCqnfGlT0Wfbzo/B89xHhnInTaItPK8LALblVXcUOEh95Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.10.1_1590617235415_0.6185947997171151"}, "_hasShrinkwrap": false, "publish_time": 1590617235576, "_cnpm_publish_time": 1590617235576, "_cnpmcore_publish_time": "2021-12-14T05:46:23.228Z"}, "7.8.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.8.3", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-syntax-typescript@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"shasum": "c1f659dda97711a569cef75275f7e15dcaa6cabc", "size": 1717, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.8.3.tgz", "integrity": "sha512-GO1MQ/SGGGoiEXY0e0bSpHimJvxqB7lktLLIq2pv8xG7WZ8IMEle74jIe1FhprHBWjwjZtXHkycDLZXIWM5Wfg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.8.3_1578951686856_0.18047829097403745"}, "_hasShrinkwrap": false, "publish_time": 1578951686954, "_cnpm_publish_time": 1578951686954, "_cnpmcore_publish_time": "2021-12-14T05:46:23.444Z"}, "7.8.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.8.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-typescript@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"shasum": "8bdf202f573cd0e1231caea970f41fdf104f69d7", "size": 1725, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.8.0.tgz", "integrity": "sha512-LrvVrabb993Ve5fzXsyEkfYCuhpXBwsUFjlvgD8UmXXg3r/8/ceooSdRvjdmtPXXz+lHaqZHZooV1jMWer2qkA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.8.0_1578788187401_0.6107093638120846"}, "_hasShrinkwrap": false, "publish_time": 1578788187540, "_cnpm_publish_time": 1578788187540, "_cnpmcore_publish_time": "2021-12-14T05:46:23.648Z"}, "7.7.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.7.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-typescript@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"shasum": "5d037ffa10f3b25a16f32570ebbe7a8c2efa304b", "size": 1717, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.7.4.tgz", "integrity": "sha512-77blgY18Hud4NM1ggTA8xVT/dBENQf17OpiToSa2jSmEY3fWXD2jwrdVlO4kq5yzUTeF15WSQ6b4fByNvJcjpQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.7.4_1574465524677_0.9778952332036182"}, "_hasShrinkwrap": false, "publish_time": 1574465524817, "_cnpm_publish_time": 1574465524817, "_cnpmcore_publish_time": "2021-12-14T05:46:23.866Z"}, "7.3.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.3.3", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.3.3"}, "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-typescript@7.3.3", "dist": {"shasum": "a7cc3f66119a9f7ebe2de5383cce193473d65991", "size": 1741, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.3.3.tgz", "integrity": "sha512-dGwbSMA1YhVS8+31CnPR7LB4pcbrzcV99wQzby4uAfrkZPYZlQ7ImwdpzLqi6Z6IL02b8IAL379CaMwo0x5Lag=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.3.3_1550265266928_0.8381433755770451"}, "_hasShrinkwrap": false, "publish_time": 1550265267075, "_cnpm_publish_time": 1550265267075, "_cnpmcore_publish_time": "2021-12-14T05:46:24.094Z"}, "7.2.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.2.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-typescript@7.2.0", "dist": {"shasum": "55d240536bd314dcbbec70fd949c5cabaed1de29", "size": 1764, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.2.0.tgz", "integrity": "sha512-WhKr6yu6yGpGcNMVgIBuI9MkredpVc7Y3YR4UzEZmDztHoL6wV56YBHLhWnjO1EvId1B32HrD3DRFc+zSoKI1g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.2.0_1543863667327_0.17199273166895135"}, "_hasShrinkwrap": false, "publish_time": 1543863667431, "_cnpm_publish_time": 1543863667431, "_cnpmcore_publish_time": "2021-12-14T05:46:24.309Z"}, "7.1.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.1.5", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.1.5"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-typescript@7.1.5", "dist": {"shasum": "956a1f43dec8a9d6b36221f5c865335555fdcb98", "size": 1757, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.1.5.tgz", "integrity": "sha512-VqK5DFcS6/T8mT5CcJv1BwZLYFxkHiGZmP7Hs87F53lSToE/qfL7TpPrqFSaKyZi9w7Z/b/tmOGZZDupcJjFvw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.1.5_1541542884646_0.7033024187772186"}, "_hasShrinkwrap": false, "publish_time": 1541542884800, "_cnpm_publish_time": 1541542884800, "_cnpmcore_publish_time": "2021-12-14T05:46:24.534Z"}, "7.0.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-typescript@7.0.0", "dist": {"shasum": "90f4fe0a741ae9c0dcdc3017717c05a0cbbd5158", "size": 1740, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0.tgz", "integrity": "sha512-5fxmdqiAQVQTIS+KSvYeZuTt91wKtBTYi6JlIkvbQ6hmO+9fZE81ezxmMiFMIsxE7CdRSgzn7nQ1BChcvK9OpA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0_1535406173948_0.635398411458149"}, "_hasShrinkwrap": false, "publish_time": 1535406174027, "_cnpm_publish_time": 1535406174027, "_cnpmcore_publish_time": "2021-12-14T05:46:24.731Z"}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.4", "dist": {"shasum": "763f0c3ba6d978f3437e6a7bd560671706cde3ac", "size": 1735, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.4.tgz", "integrity": "sha512-6cUXVrpLUTIYsEmkW/pR55ISdH28VIEmojirMWlmL88lLkW3eVeVh7Z7gDgWCnCiWZBptidnqjv3jdsH5y6g8w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.4_1535388231117_0.5691733191636408"}, "_hasShrinkwrap": false, "publish_time": 1535388231196, "_cnpm_publish_time": 1535388231196, "_cnpmcore_publish_time": "2021-12-14T05:46:24.942Z"}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.3", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.3", "dist": {"shasum": "71006af99e419768089243b205b6f8792cab37f3", "size": 1741, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.3.tgz", "integrity": "sha512-R8363Wwh+mtRrI7lgRY4N/RLYopyMFPJ+pbzsoAgBWFVxHB5g6WNQipFrqBBBVS/HpOB33hM/9LySFhLg96otA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.3_1535134058119_0.8146035889670094"}, "_hasShrinkwrap": false, "publish_time": 1535134058239, "_cnpm_publish_time": 1535134058239, "_cnpmcore_publish_time": "2021-12-14T05:46:25.177Z"}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.2", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.2", "dist": {"shasum": "887e16d19dab3ab579bf63464dc2ef5ffb65b37c", "size": 1043, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.2.tgz", "integrity": "sha512-d4IDGO5D6vcVHU7BRoezHCXpXc+8mmNV9AN5iCBTNjyNlNdozkcldRt512jwdS3fxig6dKIYZ1DeVpnjg15mPA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.2_1534879419613_0.8086860742157076"}, "_hasShrinkwrap": false, "publish_time": 1534879419699, "_cnpm_publish_time": 1534879419699, "_cnpmcore_publish_time": "2021-12-14T05:46:25.376Z"}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.1", "dist": {"shasum": "f1e0afa67b62aa9659cf9995a8a46f0638faa05f", "size": 1048, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.1.tgz", "integrity": "sha512-eXhu1pl+xvQRdMdZ2wSwZ5KWl/ZmqkJbAD2CJwqJzrJNXjMWXxsBUZBrVtXZ6OKlIO7sBVh/eXu4npUj3sCnJA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.1_1533845263568_0.36993196809559814"}, "_hasShrinkwrap": false, "publish_time": 1533845263667, "_cnpm_publish_time": 1533845263667, "_cnpmcore_publish_time": "2021-12-14T05:46:25.578Z"}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.0", "dist": {"shasum": "aa5caf5e6230da4b1720944f3698f94e3b369e20", "size": 1053, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.0.tgz", "integrity": "sha512-W45fw4oCvn21dQVx8xzQ2x0X6FuhWh5ihdFR9MgPvMQes7OckiiSDDeb2TgkflTh5ZVBEi+kmwHTe2H4Miw8yA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.0_1533830278238_0.1187793290297865"}, "_hasShrinkwrap": false, "publish_time": 1533830278344, "_cnpm_publish_time": 1533830278344, "_cnpmcore_publish_time": "2021-12-14T05:46:25.807Z"}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.56", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.56", "dist": {"shasum": "836a166feb51f955540f815bf936f99e17a2681e", "size": 1057, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.56.tgz", "integrity": "sha512-mCDWTjWAyen93aX4mayxmkmhsSv5TfXNMtIphFfyjEsrqTYBgIiJabm+vTzgSU2rtHuS+TAgEE6RnFTuaIh7Hw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.56_1533344706687_0.6770163593820011"}, "_hasShrinkwrap": false, "publish_time": 1533344706732, "_cnpm_publish_time": 1533344706732, "_cnpmcore_publish_time": "2021-12-14T05:46:26.000Z"}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.55", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.55", "dist": {"shasum": "6648707176c44da87e4c409a1637941019a681d6", "size": 1052, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.55.tgz", "integrity": "sha512-e+cEYCqSBaq5NuHPg6+yXCG2XfB7HsAa9hSBS/8DNrK0MNCUXN1GzyoR7snPHXiTkdIvY9FsBmEdEhiTkcSQGg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.55_1532815630615_0.5821593715447189"}, "_hasShrinkwrap": false, "publish_time": 1532815630688, "_cnpm_publish_time": 1532815630688, "_cnpmcore_publish_time": "2021-12-14T05:46:26.192Z"}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.54", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.54", "dist": {"shasum": "7b01ddebccba8f78693bf2898e1f695bb8a76a7e", "size": 1053, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.54.tgz", "integrity": "sha512-GnkoBoPlKSK/3Ddcx0X6ovBMXEZlrORWRe5l9Y1widRMPEKqG4sYKad3JwwRLtJB8Xje+rOLGOjBP5+9O9FUfg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.54_1531764003211_0.1919436053660426"}, "_hasShrinkwrap": false, "publish_time": 1531764003257, "_cnpm_publish_time": 1531764003257, "_cnpmcore_publish_time": "2021-12-14T05:46:26.379Z"}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.53", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.53", "dist": {"shasum": "575a163cb144b094b4f7240e434badad831ec455", "size": 1058, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.53.tgz", "integrity": "sha512-K5Q02ydG7eHhclOw6S3kBn6OpobRka8WxvX/n+golXW2SgBLX0flQda2BUXv9feNfqnzhFRlDfBHzBC7PUGquQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.53_1531316412356_0.969003749903635"}, "_hasShrinkwrap": false, "publish_time": 1531316412465, "_cnpm_publish_time": 1531316412465, "_cnpmcore_publish_time": "2021-12-14T05:46:26.596Z"}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.52", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.52", "dist": {"shasum": "9864f409a692d051a76e19828036234325c752f5", "size": 1058, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.52.tgz", "integrity": "sha512-XJzJsWrev1VTZJL01yLOtM/LpGNFvITktgoGG6Nml4nYbMU5ZUkom9zkdAXWDjNAh71jt2Mq1d4l1AEn6ao3Xg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.52_1530838762172_0.08060277816542327"}, "_hasShrinkwrap": false, "publish_time": 1530838762209, "_cnpm_publish_time": 1530838762209, "_cnpmcore_publish_time": "2021-12-14T05:46:26.808Z"}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.51", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.51", "dist": {"shasum": "a2f2701ff65d006268cbc84ad1bb8eeab638b0c4", "size": 1045, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.51.tgz", "integrity": "sha512-LKCWiqzwm2l8CeBdl/JiNE7N62VQZ0HyzdKc6V3JlW4oaoiyT643LYS0UshKrzDujKv3s5SLSBQDjvtAyukOGg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.51_1528838379848_0.3235461228681009"}, "_hasShrinkwrap": false, "publish_time": 1528838379900, "_cnpm_publish_time": 1528838379900, "_cnpmcore_publish_time": "2021-12-14T05:46:27.042Z"}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.50", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.50", "dist": {"shasum": "478c7bfdd16365929d02110078633e93cf4a6752", "size": 1033, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.50.tgz", "integrity": "sha512-X0GBKpdX9YaXI9IbNsKXcokHe+HQGQhhGRLyHHCOHoCWnm9sKBM2yWmo8xkyV7iIMGl7hHjsG10kN4G/j81t4g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.50_1528832830717_0.3806837734953663"}, "_hasShrinkwrap": false, "publish_time": 1528832830761, "_cnpm_publish_time": 1528832830761, "_cnpmcore_publish_time": "2021-12-14T05:46:27.308Z"}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.49", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.49", "scripts": {}, "_shasum": "332b6d17c28904981465f69f111646ef7a5ede10", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "332b6d17c28904981465f69f111646ef7a5ede10", "size": 1073, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.49.tgz", "integrity": "sha512-HdA1fH0NemDA229WnzHkmxkpkbwVyy6c5yNwQPGnVL8lphtU5zM17LIEYmI34DMvFYHMPsACRNLT4SlMDfHivw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.49_1527264103781_0.9530370860124451"}, "_hasShrinkwrap": false, "publish_time": 1527264103855, "_cnpm_publish_time": 1527264103855, "_cnpmcore_publish_time": "2021-12-14T05:46:27.499Z"}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.48", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "21572159767506d2de96e41288dfd204fb060242", "size": 1016, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.48.tgz", "integrity": "sha512-HvCKpXvk8AYwI1vOLF4MhCqr8wbEigPrC/TtDWXZdVgAR73aE1+WZAvF670cP5jVEZF2s5fajsCcCZb9EaCmSg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.48_1527189720793_0.23284949243241093"}, "_hasShrinkwrap": false, "publish_time": 1527189720856, "_cnpm_publish_time": 1527189720856, "_cnpmcore_publish_time": "2021-12-14T05:46:27.711Z"}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.47", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "108d4c83ff48ddcb8f0532252a9892e805ddc64c", "size": 837, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.47.tgz", "integrity": "sha512-Xjq5oM92Cb6R1EHBYXPV4v15fm7m4uhBiBgihhixnBRjqVlTVHkEHr8YUlUYF0fdO2plU5FMT3WKJDVKKyl/GQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.47_1526342916793_0.6298246075185565"}, "_hasShrinkwrap": false, "publish_time": 1526342916894, "_cnpm_publish_time": 1526342916894, "_cnpmcore_publish_time": "2021-12-14T05:46:27.931Z"}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.46", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2a9e0e1f3bb3bd918571c5ee4db97bb2b00e8642", "size": 844, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.46.tgz", "integrity": "sha512-TAnXKBGD595lhW77JYj2zY/YQDzEnMnHYXxAyEbTar0/5yhV+Gx8fAgVzPy1Wm4TEKzGATMnI9xS0mr3kJiFZg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.46_1524457858792_0.10891449129979902"}, "_hasShrinkwrap": false, "publish_time": 1524457858848, "_cnpm_publish_time": 1524457858848, "_cnpmcore_publish_time": "2021-12-14T05:46:28.165Z"}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.45", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "40dc0a8929255b5785f43a141f40ddd347535bb6", "size": 845, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.45.tgz", "integrity": "sha512-LbqodKbfB+QZjBHJv/w6NcE6u1QqoiiTgYhxnkWKdRJW0vwywq94oY1G/6H4d53wnNQq7vlgdSfp5Dm64Yw9Tg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.45_1524448595915_0.549776525413334"}, "_hasShrinkwrap": false, "publish_time": 1524448596032, "_cnpm_publish_time": 1524448596032, "_cnpmcore_publish_time": "2021-12-14T05:46:28.379Z"}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.44", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "a2f1c4963be673ad8de792ba2a940a5e0c0e598b", "size": 845, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.44.tgz", "integrity": "sha512-3e/R7HoWD30qgUHSGOX4nZCvvgecep5/Gw7BOjM318pp4myHMPTiZeLvcBaySHpihcINUTIC7iqqlfdcA3YW2w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.44_1522707602653_0.6059350045773932"}, "_hasShrinkwrap": false, "publish_time": 1522707602742, "_cnpm_publish_time": 1522707602742, "_cnpmcore_publish_time": "2021-12-14T05:46:28.618Z"}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.43", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "0e49a3c826af0f180f87efa9bd63105d33c5b4ed", "size": 844, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.43.tgz", "integrity": "sha512-S3fcel5Clz7l3JfW6VnRwt4kvgsQf3BL2QDebD63sd5AsL4yoaZV2gVxuR1UyaIzmiXeZhbms2IdF8GVq3sD8w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.43_1522687700582_0.582140674449076"}, "_hasShrinkwrap": false, "publish_time": 1522687700652, "_cnpm_publish_time": 1522687700652, "_cnpmcore_publish_time": "2021-12-14T05:46:28.813Z"}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.42", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "ffc42945ca15e5ab369de6b9f5d9324499c623cf", "size": 795, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.42.tgz", "integrity": "sha512-Z<PERSON>ucpOPi8bPOtszKUeOCzFKsH+u3HsMKU87dP7obMzlQXuOvLbs20euk3GWkPY+OQrGzDo1g2k3SbwLgoTRaeQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.42_1521147037268_0.21762502885538804"}, "_hasShrinkwrap": false, "publish_time": 1521147037304, "_cnpm_publish_time": 1521147037304, "_cnpmcore_publish_time": "2021-12-14T05:46:29.062Z"}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.41", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "d188e0c2cdf37b60062c1a81176b05e795c3e5ff", "size": 793, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.41.tgz", "integrity": "sha512-5aZjjDIG3HXWCHtAF6Z+3DPqCjcpKdnaY6jMA/XCQko+zZ2XG0gwO8S+c1RW269FPolWES1E37CI12TsbYBaNQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.41_1521044761158_0.4607642993336398"}, "_hasShrinkwrap": false, "publish_time": 1521044761199, "_cnpm_publish_time": 1521044761199, "_cnpmcore_publish_time": "2021-12-14T05:46:29.255Z"}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.40", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "0cc8a5f8d03e5f5187e759ec8beb44ff251992a0", "size": 711, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.40.tgz", "integrity": "sha512-o4xi4GbvyV5sG6vLMahVMQbx2qQYpuaC4KCZrwTko/Pd/1ZTO3cSezTgWeTQpCksjt3ngkuqbvvREWwles1GEg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.40_1518453688891_0.4701336816592141"}, "_hasShrinkwrap": false, "publish_time": 1518453688955, "_cnpm_publish_time": 1518453688955, "_cnpmcore_publish_time": "2021-12-14T05:46:29.460Z"}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.39", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "899d8c6e0c243f4b1d4ca4b687492b0b12314063", "size": 715, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.39.tgz", "integrity": "sha512-kFR7hGT/EH1ssVmCMzcqDICZZPVCk2Iuf4aUjZ2JRWdVz8lkLId1wjmW/OsBLKRByutgj0xkgSeMxvdo1PBRqA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.39.tgz_1517344049458_0.6172956216614693"}, "directories": {}, "publish_time": 1517344049530, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517344049530, "_cnpmcore_publish_time": "2021-12-14T05:46:29.668Z"}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.38", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "c62b1148da53a071ac8e8013797d2783eefab631", "size": 714, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.38.tgz", "integrity": "sha512-QA9XUG4D1UEywrOxBds5RhkL6hZ7k6MvxloiTQBpwLl4yt3nSZRzOE2E/L1yK4dVnH15+Q+8l1uz4B8q4noGeQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.38.tgz_1516206707117_0.5201111796777695"}, "directories": {}, "publish_time": 1516206707288, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516206707288, "_cnpmcore_publish_time": "2021-12-14T05:46:29.888Z"}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.37", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "e2dcc2bdbd30e4333314fb2003fb5a7b34f799d3", "size": 713, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.37.tgz", "integrity": "sha512-dNRfnIkxnCZndYGOqpouKJPxNyJUyJdbjw7CyReTu9FXD04spigRulcDpeIBjCSeaLwhKb3+N8XT0gvJ6BdUYQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.37.tgz_1515427346324_0.4482850646600127"}, "directories": {}, "publish_time": 1515427346435, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515427346435, "_cnpmcore_publish_time": "2021-12-14T05:46:30.088Z"}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.36", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "0b29c1cfa090922b3d05e0c985d2102950da15ab", "size": 711, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.36.tgz", "integrity": "sha512-cAtquXrQ1SXPXwkPPjCpzcsSF7psE1I/vrrBM6fQripfCs0Mx4c3YP+N1SHnq2fIPZTJA+fjtNDhDvmS4sjIjA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.36.tgz_1514228669712_0.6584230551961809"}, "directories": {}, "publish_time": 1514228670562, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514228670562, "_cnpmcore_publish_time": "2021-12-14T05:46:30.308Z"}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.35", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "f47c5dea85d3bfa992de9e3070b6b19be9c42a0e", "size": 714, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.35.tgz", "integrity": "sha512-OufioM3m7MOkjO9Arz8H2cdWtirtBewWPiJvzgcfzIEICTbIgtBGTu/9D2CCrUAj7oyTHV1hiiqsXrZXnQOWXA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.35.tgz_1513288059022_0.9903956514317542"}, "directories": {}, "publish_time": 1513288059090, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513288059090, "_cnpmcore_publish_time": "2021-12-14T05:46:30.535Z"}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.34", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "a290cb23a8087cc2c8a124bdf01e8d36ad863000", "size": 713, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.34.tgz", "integrity": "sha512-NdCmEgdlyDfX0H3OMz/DuwZdHAxKBWJEdr3kcN2uqpP3W2ciZbu8GhQ+Tz+uDgrj1pwOSgHpE7WFnsXg+XSzoA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.34.tgz_1512225550849_0.7169859451241791"}, "directories": {}, "publish_time": 1512225550946, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512225550946, "_cnpmcore_publish_time": "2021-12-14T05:46:30.758Z"}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.33", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "c20ecf090a097966c69fa4de079f30afa8f8ce9a", "size": 712, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.33.tgz", "integrity": "sha512-HJixkVCZD3zQjQLQNFr4PmFGYIPOUxqXC5ZE0Zyo+vwNxgGhG7XBVIXTIBy6GYSMiOPaZpKXO1TTXNpzXzBqjA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.33.tgz_1512138490954_0.380929579725489"}, "directories": {}, "publish_time": 1512138491888, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512138491888, "_cnpmcore_publish_time": "2021-12-14T05:46:30.979Z"}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.32", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "c2791ac1bd865ce3240493351be07e45b13122c2", "size": 717, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.32.tgz", "integrity": "sha512-A5vcEi0YBc/tpmobridyBfcxFg8yYxcjs5So/8ll3ogqHEghM7BPcegWmO3j+bC75o1sspjqmhj8teyHqg18/g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.32.tgz_1510493588389_0.24508299096487463"}, "directories": {}, "publish_time": 1510493589343, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510493589343, "_cnpmcore_publish_time": "2021-12-14T05:46:31.180Z"}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.31", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "77369b541e2b6e83892a3476b13b8744e892e527", "size": 716, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.31.tgz", "integrity": "sha512-616cxSKNVYDU4nteZEbMLzVvrY3f5ooVCjCj/CUgcz9k4Ohd95domNpsvgkSoiISZ4Mtzr171y3bwbI2Z2d7rw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.31.tgz_1509739400480_0.471153850434348"}, "directories": {}, "publish_time": 1509739400538, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509739400538, "_cnpmcore_publish_time": "2021-12-14T05:46:31.393Z"}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.5", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "2784e1d8e6f1f10904d18c2769ea055a934bfb3c", "size": 685, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.5.tgz", "integrity": "sha512-kNAN6FC02Z4l8Y23Zkw+obBKvAmow4+xbE5U//xGUGi4sl2g/iLusKRPS0qdooRMv09WLU4Kwy91FSfoejU0xQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.5.tgz_1509396971249_0.6539474672172219"}, "directories": {}, "publish_time": 1509396971462, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509396971462, "_cnpmcore_publish_time": "2021-12-14T05:46:31.722Z"}, "7.0.0-beta.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "d850347e4f4dfa520a0bf9460f7c3c9f4f3e5e63", "size": 689, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.4.tgz", "integrity": "sha512-XQyQ3Ngl/hQMm5b9Pup09dxMZ9aPctJgpNgGJcFgwOlzbn5k1d//C/PGiNstr7pSc1KpG12DvlukmRSAEh4QWw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.4.tgz_1509388470028_0.49570025061257184"}, "directories": {}, "publish_time": 1509388470095, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509388470095, "_cnpmcore_publish_time": "2021-12-14T05:46:31.938Z"}, "7.16.7": {"name": "@babel/plugin-syntax-typescript", "version": "7.16.7", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-typescript@7.16.7", "dist": {"shasum": "39c9b55ee153151990fb038651d58d3fd03f98f8", "integrity": "sha512-YhUIJHHGkqPgEcMYkPCKTyGUdoGKWtopIycQyjJH8OjvRgOYsXsaKehLVPScKJWAULPxMa4N1vCe6szREFlZ7A==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.7.tgz", "fileCount": 13, "unpackedSize": 3912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0fCRA9TVsSAnZWagAAk2UP/R1fHFGnxWjpjH63v/vi\nAQRZAFamcfBeEjZ/C8z90eDWc5/rDVpeoddoQT15Odu9ahcF3VxmgMRZaqqc\n9WRJSIg74FgiP1e2k4jk3Fb4bQMGN8rMTfyJZCH2GnzVUEYGxAR0ys6uX+9q\nHJjYNxPh1b/fdH7cYnLTQNVj3OhijjVP8U3hPXRdudVHe3KxJCMiBDmt8SO7\ngUTjR5AKknQzY+edqsi+1IIK6pOcOI2ZhqQa3Vm6kCoYCn9uaVKnRiDeWpO2\nGwUCRn2pWzwG59QVNCn0opvi21MzLPK+Tq9RxWHnbA9r2GJft3m6Xl5Dl5nH\nSW0Vqj9sS19iRhg0dHHQR9x+iTciWh9Hy88aKAshc3V/wVUP3sm4aYJyx/IQ\nKAlrj7CQE4RzS+wCTasToNIj67sMHUBIBb7cErvh2H+TW+RoCJ5B9Xd1/POY\nO0/Wx/RLnAA3EA4O9KoXj7wjHJEGwYUgoKxI40axmZ6Rjzo2Ix7WL8NoxEGy\ngrDJgKPTJ+CuVps5JAklZ5nOtqLdLVk0OdVT4lqmE22ohsM0eheCFaM2/7v4\nAlcD6mX84ShPyL3p1ez2STku/KQy7KG9LNdrEjBCfmzilOXxD7439w2DXG4J\nGOFOALZIMASj5Y9lCMpToPFW55TLBikLU52GArTOtdiL3g24MLLgZYyj6t8X\n5QDg\r\n=AUTA\r\n-----END PGP SIGNATURE-----\r\n", "size": 2279}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.16.7_1640910110923_0.8847211484265585"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-31T00:22:05.402Z"}, "7.17.10": {"name": "@babel/plugin-syntax-typescript", "version": "7.17.10", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.17.10", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-typescript@7.17.10", "dist": {"shasum": "80031e6042cad6a95ed753f672ebd23c30933195", "integrity": "sha512-xJefea1DWXW09pW4Tm9bjwVlPDyYA2it3fWlmEjpYz6alPvTUjL0EOzNzI/FEOyI3r4/J7uVH5UqKgl1TQ5hqQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.17.10.tgz", "fileCount": 13, "unpackedSize": 3883, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDm1J0pg6IiWKkPbNjAFN/+acCMZnMbBxJJZgBoTC0YkQIgB439M+bao52VUoAEZJ83A2gxHljwGZv2p4qf4s52omM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDQ/9GNDMmdssk8et7OkA1fteAmMUH4P465NfvUWdG25MJUItdwKZ\r\nNF72HUMMCHBoIDYYW5O8KUES0RmLpKrDSnoFwVLUkvQLgEGk5+5D9C9nuI/A\r\nvYhLouKJqGX2Dka6D+Wc4CvcPTnBur+WnGIE004mCxeCch5b0TI4XY001I6t\r\nDGOEi98f21u4C9PZZxVS1aYbTFwPjnSPGd6e/yYjBzSD8jAhBPkDZLFC1s03\r\nrxgff8wbRQycZOCd1gT2ad4sfsBvuafG2qMITa8/HoPxXTK3FvSf36LWW0e/\r\nhdTouACkYAe3Q9Uwnb4ciMEq7JdXFI+zdH+3XMCptF4c2UnQ+teIh3gikeJY\r\njEH8K9vlOCDevJaw29YEL3/tyg6Gwk9i2nBUagTkZI36kthzEQfZmxHy13s9\r\nBqQtuSV6Utpi2tmXILFhtcFiNtqkw5XjC0/Yl+Bpk8/AmjZIIhSRA2zYRjjb\r\n39feVt7Usc1qJlONU17QT67BNjIE+1djgfXhpv1geCczOzJLWVll+yeyMu23\r\nVtPoeFIXOwaH4k0rAHxiHSe1BNwQRRX8Y3cRlvxk9YBL9B3hA1bnY8ILGNpB\r\n2r9W0OuIR1IWeJ8rGWcaayxOXVaDXHGkcrk+FtmpG9NINjPk0g3bvhrYJ3Z8\r\ncmUb/Ajie0X48CuGAtF1LleRg1e2QWTmsvg=\r\n=kbE2\r\n-----END PGP SIGNATURE-----\r\n", "size": 2276}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.17.10_1651250257909_0.8376504186882019"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-29T16:49:33.217Z"}, "7.17.12": {"name": "@babel/plugin-syntax-typescript", "version": "7.17.12", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-typescript@7.17.12", "dist": {"shasum": "b54fc3be6de734a56b87508f99d6428b5b605a7b", "integrity": "sha512-TYY0SXFiO31YXtNg3HtFwNJHjLsAyIIhAhNWkQ5whPPS7HWUFlg9z0Ta4qAQNjQbP1wsSt/oKkmZ/4/WWdMUpw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.17.12.tgz", "fileCount": 13, "unpackedSize": 3884, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGFyUrpVBjOVlff+9kIqCSg38eEkXXGpQz5r6ILJ4PYyAiAP6kmlO+TbYmb6sBQAQZFRH6FV55VFI9d28riLpJ+GVw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb+A//WahPkUFnSoBXWmC7dGt/j+rVV4ly9n3yWfPypLKJM6R9VKG8\r\nGVHgVMLOspYjjFrfWlzfXAQmOSA3mhzDksk5e6somm/T9Jyb34n02xasgBK+\r\neBdv4BlgBWhywMr/fIScxkZ/Xxlo2DWMNB+MhQ/HlJCqU9YoC8i/7ljFxMIm\r\nEe8jGfifVS/o7FhfniN8ntXMQRlPfZu9J6xA4hSIO3bZmIWNX85hUvyKMjWF\r\nWSm5ZfKvwViNj+Vl/CP5ZXlIe/f2N6i3lOfpeqxn+ROx2kyWlLnQiFWo2P3v\r\nARZ632HZIcQfqNKRmbAwfOfGsezNoBkkx7nVWRgwQOXVB3fArzX0Rj0wOWrA\r\nKXkc3yi2jFIQO0c5Hfa3urSnW1wkOfqwBb6SmSW+CWxGU08wdSSCAJAFalc/\r\n10iOS5Wu1TNioe5E1vrWlvOQstTIbcKtH2EmIcElRwTigj/VF6YO5/BYBEaz\r\nqB5oBRZwkre4FZB59JwxX8M33TuXlENifd9wpl46LMbgF7VbWBAtDBxG9u8P\r\nisegjV24bVFUWKUmFO0MspUPE4UPLs+J+Yurm22vZKDSYN6hatcmlvwUWCn5\r\nftFW0CRHMwXzouu4LdHB3mHKO4BKsU3H1KVL3lVJR0g2rRFr3KlYSQqMZQmm\r\nHeZfyzTaFmjskSCacpJtl2k81gLYPPELtPQ=\r\n=+RmD\r\n-----END PGP SIGNATURE-----\r\n", "size": 2278}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.17.12_1652729564016_0.8555952813990113"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-16T20:35:18.431Z"}, "7.18.6": {"name": "@babel/plugin-syntax-typescript", "version": "7.18.6", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.18.6", "dist": {"shasum": "1c09cd25795c7c2b8a4ba9ae49394576d4133285", "integrity": "sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.18.6.tgz", "fileCount": 13, "unpackedSize": 3903, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZCK5sDuyi/KhXnx42LiM0xA1X9NH8QjPYd5aorEj/XAiEAu0AtCEukTrNEJ7HbwbxoK/FqpVwZbEj1yZbJglPMTCA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS3g/9G33CKiq7eQsoG68+/9DbGJgtbLPGyWyNKNyLmGHgDEj/kWhx\r\nyGLzvIQNgHg6+B6W/Er2FGxg7H/voNADnxbdWs3mcNiZm52Af72tOLiqQpjA\r\noLdcN/BxhINZRD9vZr+ZvarJWlZHq3M1SsQVxknYs+Z5ZRYwa5uX1kVZZgT2\r\niuYh5Da5lETeTgEmoMzi/67UbxlpVGV7rYDsvY+NQrgEQ/ZcIL6rFpXdEH8T\r\no88trUdvbuXvdZs1CaArqI82E3l7KaN8EYOOR7lduJsqGPn2IUpPcqi57mFA\r\nVSwVUcmstp2eNXCcq92NU96o1sq3ZAyClKxkvltu+tWYmCU3LisYtL0y4JVE\r\nT8UUxvyLAcfkRmGN9mkJX3EQv9cpVicV4zXfMz3FuRaBPdYsg6NGNkN95UpU\r\nB9HSDQ6TPdhHUd0iucht6QxRAs/YRdTmMVLXfbf21SFSjcIryKUdS3RoOq1S\r\nQfdOPdhqAFjlZE9iMmqBXtqkjS8aHYFTVImcS/WqqM92dA20HghmYPgkiuRk\r\nxwToIgKF5gaokKuJwSSQlVUauLUyWZ+Hu5CDhNaqUD4ab+MnSPNp3wtGSMAt\r\n1Hl7Y7aoNobYXL4NvKhi18tvtWxKdnSnplD2RrqlbnyLyOcgpIKZmSEmBQsY\r\nTDHuYpUlf1oRi0M1xsv5h2cbpcJ+YNXu5mo=\r\n=B9kc\r\n-----END PGP SIGNATURE-----\r\n", "size": 2284}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.18.6_1656359397666_0.12786278988672328"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-27T19:50:11.239Z"}, "7.20.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.20.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.20.0", "dist": {"shasum": "4e9a0cfc769c85689b77a2e642d24e9f697fc8c7", "integrity": "sha512-rd9TkG+u1CExzS4SM1BlMEhMXwFLKVjOAFFCDx9PbX5ycJWDoWMcwdJH9RhkPu1dOgn5TrxLot/Gx6lWFuAUNQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.20.0.tgz", "fileCount": 5, "unpackedSize": 6264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIgoaApWsrh/+N89xxL95Dou2T7hpZnohMtVrU5Oa4uAIgAITVLHY5RFhT83Bpa95epJ5rK5GC1dbnrMlIw1H5RkQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqluQ//Sir4DFr5AzBbXr6VK6xZRU5IIJSMWrDJ+1D0I02stltIt0tN\r\nl8Quw+txXNhKwRLl/godJTuI1L7z7CV7K0cWieslLA+tn10tYPeJz8gd7GXy\r\n2ex+SXxCBOQuPUQ5myRhr5Ka52g9DIFKdWVEa+Xe4loW+Vk5uUbzvvfrTMRf\r\noSAiIIgQmqbz8qIc65mRFZ9lzor2N7qKCah+chLfjsnATY+xTS1qM9CFX9wz\r\nQIkHH+AB7F9hl7DsOREZht9CN3jvKSQmEFi+V0DClI70wfZiR/NzjFNOBfpa\r\n+6BsY+fYar0UvCpsgAAroKAdVa+sLTrcXvbdL3uLfi4FJwHy5wMNfXGlTalw\r\nXcCKwX7AgOJP7e4FFt4/nvYZXq5rZeAxgY2bq2ciZToCE0gyblVyukbGQU/g\r\nemr+3hPfYuvp4dgwp77x8Mt5nT3280hXE86Tcmh6xitGVVwJN2Acsq3OByt1\r\nqio17jz9ZghogywlEP2xHBX0lCWMCYJWuO9HzzNP5/x+IQrnnLjwnMm0J3dt\r\nnvXjlENoaIbsF8oPud05yaz6+cs1h3FhlRmD7dPqUcHJmbsiXKXrXIn3OVbP\r\npSUHQQYpMnhN6XjVaafnEIK17lht2NRzGoIGQDGEN6oMq0z5X6AbqNOvo/+n\r\nk9iLuK7020wj/Hd6uP6ihd6LmiY5ftzkZns=\r\n=PtS+\r\n-----END PGP SIGNATURE-----\r\n", "size": 2914}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.20.0_1666876750964_0.45208791510146207"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-27T13:37:39.758Z"}, "7.21.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.21.4", "dist": {"shasum": "2751948e9b7c6d771a8efa59340c15d4a2891ff8", "integrity": "sha512-xz0D39NvhQn4t4RNsHmDnnsaQizIlUkdtYvLs8La1BlfjQ6JEwxkJGeqJMW2tAXx+q6H+WFuUTXNdYVpEya0YA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4.tgz", "fileCount": 5, "unpackedSize": 6856, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGIPPZpTmYjudMVtStC/I/2WtwG321ayISyt/tBFDUZAIgadmcVQgz+lHCvTf4pj59HXfrbBTOTcCWCXng2MEqWAA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJjg/+Ml596pT3fYIYmLIvtbOPdKzxn9wSNaGjUcpvpTJfNMcBApcg\r\n/6X4Q+pag4j6fdOEmmUe5Wt34QpYqOXITITBAorze8ji82PjUa5AUZSw9vxj\r\n0HoXHJujmIYvbmjJQ3818hHat/A7qmmupvEcjYzNpeeko1EIhb6DPz8u1jfO\r\nKC+lV8ym1yWipKeofw0/TIwGu5xg+wzqPzcPRd8JR9DF0A4t9FczntfqC43b\r\nPUAlE7tRylkRBaypGb3bE1iM38EBTFeOwM/3/+FYBsrTuXQ4uCGjgNFPVrPq\r\n5NXgUi7S4LVMaYVxYtudSdqpmSBlgO4EY2E1lVDGI8mGsT5R+Tbn4p0+dUUz\r\npqxNChZj+pvpQzJcrpCSw9L+ZMInnjpzlkQAOJcnPnO+rB0eHyFQt3JbCxjY\r\nhEx8bvRkR2n52n9EKLeN8T6APLkS28twrWrvUGGGFDoDYPsaAmsr9ICNABBz\r\ntNEl8a1eeCZeDTJzPngq/INS8nfCKgv4VTCaReFzQcvl0Rk0riAHZj4dKVE2\r\nXBzNb6c0MPrlis9nTVr4gQ+E8Y5SVcQjpQ84flSmEI7KTXEEjmiYSkJBIzt1\r\nAseujUqa+o709CFbGrGR14Mqaf6GfhQ/cltZ7u/iAoGlTHMADWcL+lalHU+W\r\nCdrorbkdAx7i0O7Ndh9ZHJRD16vyqRXALCc=\r\n=yPgp\r\n-----END PGP SIGNATURE-----\r\n", "size": 3000}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.21.4_1680253313250_0.7355961887422251"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-31T09:01:53.405Z", "publish_time": 1680253313405}, "7.21.4-esm": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm", "dist": {"shasum": "ef96b9187ee49c891ec391b02ce8a52cabe48125", "integrity": "sha512-j8vYBkQdDnu1iwYWEHph6k2JIr/s6Sl6hKlAibgYSCKsHOMjFi+wD6NzDLit2ThHrMtBxuFHbVGpmEEEPqYUZg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.tgz", "fileCount": 6, "unpackedSize": 6906, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzGCFN0YHMsUDc0uREiIWXOR4eWmo/tKp9znadBROPtwIgCJL88ntP38p9q+5L5ryu8PAAW4LOKY9tGBwKEtTlMRk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqyqw//RomsoHfRZNcG0p7PWEC5MZnTwEbWzEWS/YLBx9MCAgAxTXYI\r\nsRZ1/1IaSo+0emkvPDUGnEUzy/emGp2nGllYG2l7x/8QOXZriJG/VDKlO4oL\r\n6i6s4p46t1UcNXZMPtIC8uVfcjiBVMkh450OjhHDWokHQFAmtOKW3wpnJIR9\r\nwS/GlIvZ92F7updLlraaRumfEaJ8KF/yB8fgeWMh5OJZb4euARuh2R0MFc28\r\nykB5f6k+XbXyVf3cn4wSFOqcCaCLXW+rebdjHTQPJFu8xkQV02RwFW/FPHGe\r\nDlfMYnSIxLDOr3bdCDCeGWOwc2O5HGKAv97tZxoiM1tcobORD38jNW0RyFmI\r\nMzGQqYHG0voASG5qAbFCy5wLKugeYGk8BJnewLEQx1ccPdGqDz7sRUhMCKN1\r\ngXZUnufW3qHqeFbTFWfVkKjsvPP7rrmrK6S3DXwJtELOnKxmwAH1hQLw0zMr\r\nPceRdDtdCsLgDg13E2/m3OzQpovGjfiTovCRwa9A7zFIuAhfhbtRDMPXFnb6\r\n5JBOzTMMlUwFsihxb/oMFi7pzt7huaV6Xeg2IrIeRDSrlOpmmmnFW+B3rzAW\r\n4Y/N3WO7kS4+Z4BB8VOfLEFkDWikKGOYr1gDK8jiW6zM9jqtw2UdhYVF0ZHs\r\nO1k9p2acymLaAafWK/0TEuZnP5xjbpuwd/M=\r\n=ld0d\r\n-----END PGP SIGNATURE-----\r\n", "size": 3040}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.21.4-esm_1680617360138_0.9752761936396166"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:09:20.325Z", "publish_time": 1680617360325}, "7.21.4-esm.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.1", "dist": {"shasum": "7a691f388943d9d52dc389cd6ba0db4070bc6ec9", "integrity": "sha512-S<PERSON>eueZ3EXREh7mNGItmH9bA2ZbDokxdJs2Y5y/Wvqx19VAZVpZuLZhJ+UbM8lygFid31DbnQzSOxM5tB+omLuQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.1.tgz", "fileCount": 6, "unpackedSize": 6628, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE3ZGi0NFLKUzvYJwJbeXnyYcY33hJ9n5FOIPeSXX7WzAiEA6AeYVL0iTtyvGxH5QIBXt2mZgvVLMF+iimjh+gD8/nM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAmw//eizUAm1f6DEVxiBywKYeS6QDAX7C7A0yjLTz0ccgDZ80CxdW\r\n3pVxkqspQyGhgokRU8seA8oP4ZxhrfewPhC2F4VxxyURaznYBtkr8wlWQj6C\r\n7VWg5TKF+esTulo/oZ1YrbsdrWmuCzhsQtDAcmf4c7zS7Tv5jbnOPoLncoXG\r\nLd6NdsC8I38/RLU/me7Ftd6nPYzuCsxgRB/6oWkTZ4ObEblvzUQTrZvcIswa\r\nErVTxAzxycPKhKQtw8OWtu71T3tkse2VPuwPrzn8GuHHJIUlejuo9trZb1V4\r\nxrLKaa1ct1vkIeB8gnCI+Cb+MuNQkGgbhs8Sw/A70+2LoFiEdD4PMYbe1nG7\r\nEuTybo+vnk4aMSVgUztBlwKrJ23edpFjKoWOaUdnUfSMpzPyg7p+7/F1w7ff\r\ncRCDb/XI9XxoMhzsWkORHL8EzfcNq9Aag4k4vwRRTOn4Bcu5M90GcC3aB78a\r\nW3WVgcdWMs5cek91TKVPbWFZ7C/3TiC8VlLN7qlo8moIud0l6A1E0yFKfvGa\r\npXrajfAilrYnS8hfpTCoPL9W/DsCOKeLSXwPluYds9n0lpGmCUynyFUOISI2\r\ngrjt83lMQQOLM41IIyyZOz8aIA5/tVXvr+liEb78PfUo/ReBwU9LmYiRtcj5\r\nhOGR94C6lgwb2U+ZePzFn0mlBRoDe1wD3hg=\r\n=9gTY\r\n-----END PGP SIGNATURE-----\r\n", "size": 2914}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.1_1680618069684_0.3724214047905283"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:21:09.839Z", "publish_time": 1680618069839}, "7.21.4-esm.2": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.2", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.2", "dist": {"shasum": "51cc9188a50146bfedb6c4439c2c8117781eee7c", "integrity": "sha512-0w8vFDfwxBi7K6gVq0NRk3IIz2Qg4X6UzEroVImrzpWcvW+EP58/X+PyowcFC0Vk5reT6ZGG9248KBzJOiREMw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.2.tgz", "fileCount": 5, "unpackedSize": 6606, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqXpdfdPuPwuK770zkNxPmjkmuVJv3yvILzvwRhMpUQAiARhDAJwO0mn+KlfofCMDfAYhKMbTuC7RJ/1GYe2Do6FQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1fA/+JqwWbFKU9GoEx6D/9a/t3kl0Foe0WmJEcHTjDxhD1BS6f4oj\r\nePcT+NAuIK1EiGBy3OxnawDSnVTqoONfKB3TcWNi5W2+xIqIQoImC0dTvldw\r\ncqlxjkbIYp21kR/D5apc401vDhPpUWA1ZEvoXkCdLT3g4hDzBtJ+jz4Fj0UB\r\nYaWUit4EmenmwV4tgEk0TpO9qSIosMTfMqzuHhhi2rDa5cuuty9JfgpD1fCb\r\nP02ExJYMjGC4TNBkgfEpluGCHbuHGQnQ5H5g2jyCGVPXnQEzGy5t7FDSWk6m\r\nKnumGHgx81fv3IVwgBEDtTcOuNoEVsVwUsDN21N+Gnv2Z6qih0Rl2HdMTEAn\r\nWEPBrHgvgCpw2PafwhngVsXyl/PGcto8Z46NSdHKgLPgFN2Sg/AOHlUwGNex\r\nCptCjGEjbmmEoMikghO0UzjEutEIo8HOoJdn8pUO2xZ9CF6eLdQ8Gi4Ooj9e\r\nnQ2dRVBwXwgS/rFiFZJL8gE4zYRPP5TUS8DHFhPyUEwPIpNwZ6rsk6b7bw3t\r\ndJi8kvFf8oeEXfJrJ5xzE+h8b8bFuGs3C2MYYtudjKlsHuvcW7bhGnkhjOT1\r\nOKVsrzs7HvwpKJFz+a9JuCwQ6drN2ZuVFMPK4vRNWgcvZcYcYvUigE4mET8+\r\nCtyoeRxcZ9JsYQI+wlpILAGAgIFjj2/cjAg=\r\n=EQxu\r\n-----END PGP SIGNATURE-----\r\n", "size": 2877}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.2_1680619153109_0.2675426930567455"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:39:13.315Z", "publish_time": 1680619153315}, "7.21.4-esm.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.3", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.3", "dist": {"shasum": "0e1d36a96a640507655752245727f0bfcbb9f154", "integrity": "sha512-x35LLzLUZm5svEj3hyXr3ZSfBrSsARd5TAfvKglGta8tIm0GmAGFlaJKkzSF2rkPseTKrYrt8ysl2gSBShtHcg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.3.tgz", "fileCount": 5, "unpackedSize": 6892, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDh/zUX1rbG1zgJ8jdpdQKZcUYtu14byIM7gFheRuFscAIgCwzs4WcWIc+O2TvHrkIjYlnXUdBZvCe4TAtKpYjYmLQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq23RAAg97y+K4d9lsLZXD+4HHz20svzm0JPOqYhTbGg7YPtbpfo+YW\r\nDJPU6sPca7K+anhDEpap3vpusQF5Q4G3O1Qjwci5sMAVMt3e5f33WKK8tOHH\r\nCqOe4ndGC/uwxr9SGX5RnbbRl5erfKMSaTqbGqD9Px5+WaJHaZW+EuOsaEV4\r\n65kYIf3uzDZ724qXwgRmNXHU1Yq5t5wrEEx5Klo1dYR7kaf5TL3BNzDcmCrt\r\nQpFQogLSz4LVxl/UUuSCaI37ZaWnCyh5jQYH3ngs5Ds92GF6Lqy/BJvV6z15\r\n2lSGjQi3KCIVO13MfR3+Y72I5jyR+nSPiPSpegWx4JdyIGH2oo/PGTqG/Npu\r\nPLGef0sLnjnlPF1jBKJJZRXFi+6MXrhdtm0K2HUz6THJ4skAvyXuw+sGDNR8\r\nL+R0l+vmV724T8jgRnhlAChZgf/9oOjN4dVZBMwAB12LDRjvmeAf62mK1sIF\r\n0HFtLjwsrucysiriEBk8QeIekGvuqUJVJTEfvErU0EcFznmI98uaIXxcGLtV\r\ngbeaI3ADGpwBhpUWvygF6jm8q5//gM3ikOtnDM2UYHZ00lvUYBbSRUV7v+JD\r\nkosWGfCjCy+IbBmq7eNTKPQMHel+05tHvlL5C2os5MTOyV4r+cuDKXbtTwgK\r\np+u/bN1VNJEAcbQjVOEQT7EBY6KW/S4w380=\r\n=j3G6\r\n-----END PGP SIGNATURE-----\r\n", "size": 3001}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.3_1680620163960_0.3132111669555009"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:56:04.107Z", "publish_time": 1680620164107}, "7.21.4-esm.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.4", "dist": {"shasum": "ba99276db7dc8c2a17c88d5de752cd6ee18845b0", "integrity": "sha512-ka<PERSON>lloUc9Tejk3ZoPvs6QsFqJF/OsMCyqfqVCQwwI3+YaRW81bRs7Vuc9/wRnCqyKRctghPLOA4+Nw07r49Cyg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.4.tgz", "fileCount": 6, "unpackedSize": 6626, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpK+lX9pffzoi8+/y0kslZ89TnhnpdIJUfd/1bc5Ll7AiB+WGCu4wxvBMM4fTTiKnjKOMAgWlMdwnZePG+aWg4hZw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorNA/+MYBw8ikeXdShgUI46Aps5yUUkggby4YT3P5tTX5HVCfPEHGK\r\n9Sib0OUlcNagKaPi9AzheJiHwljQ3YoDMk0FBu0jqatndkp2R9qLgmpZkBgd\r\nID77D+4fDnpfg+izjUU8UryDOX1MWNQHtCHrQ1ba51PRRyY2EnuPa7SiFCQv\r\ndLc6iyZIy9lZqucSitBBzN9Ymprtrxwm4SyoVGnx46XXx7/55kWFwUIQVAaX\r\n4sWd3OREJuWkWHSCkGVsFsdmPEYLzIcNYqzZXAsNUMkjP1J6OPGzjitFtOd6\r\nm0Ax2pvtYGUK6HzYcGrSlFrkuvNDoAOFV5FZK4jxaFo608cTGl3fe4KExSTa\r\neBT+DeH1BBHmCfSy87q6890N1s87p37TkQMFIgUm8zhLQ2+YgvbqvIkWQbXv\r\nC2p+1P7ZXnmOSCZchD55K82qIrmL9UWXNkbOXBY8pD5cDU/h2gt1Jz5dnAh/\r\nd8SggA9ybxSWks4fFoFqx1foP8bkAe6974bk7nKk2s2t7QvYbwHlQTom9Eut\r\nuMBhRaGpq3kykVw053DSV+tC6DP6wSyO/UhVWetsa8G5vCaKIg5meOZG0B4X\r\nhMQ3nUi2edUw66ZZYOtJ7XJ16YZXohzoY0I3dUf9jEZxZGehrANOI2wNRC6A\r\n/VpeHimcsSU+g5adKvnq+FPnYE6v5JBZScc=\r\n=+Ivz\r\n-----END PGP SIGNATURE-----\r\n", "size": 2917}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.4_1680621196715_0.023865083059742442"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T15:13:16.863Z", "publish_time": 1680621196863}, "7.22.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.22.5", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.22.5", "dist": {"shasum": "aac8d383b062c5072c647a31ef990c1d0af90272", "integrity": "sha512-1mS2o03i7t1c6VzH6fdQ3OA8tcEIxwG18zIPRp+UY1Ihv6W+XZzBCVxExF9upussPXJ0xE9XRHwMoNs1ep/nRQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.22.5.tgz", "fileCount": 5, "unpackedSize": 6856, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfJPQaysWNAA0jXKehOpBhMIsH4yWFa3ZGkQpuZ4IJ4wIgL9l8DQlt3fzAyFgdjg/2DwT/A3G0JaCk0EfHIe6XYPM="}], "size": 2992}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.22.5_1686248473250_0.769872511610352"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T18:21:13.503Z", "publish_time": 1686248473503, "_source_registry_name": "default"}, "8.0.0-alpha.0": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.0", "dist": {"shasum": "6d1e8ca14cfb52c8aa4e92d7541946b572bd9653", "integrity": "sha512-VX98Ww/P35dRg6YFd7j0AH+pUfvyrd0lFSnI67f/MJ4CaCJH9q9wnriZQkyKh12YvxzHQwjGkUYJ+Yi+0tp1Jw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.0.tgz", "fileCount": 5, "unpackedSize": 5345, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5vNJg/3BNGageo00zAvS7R3TcfHIZKRSI/35q5qO7rAIhAJIfkqrkjB+SEzV9Ba1e5HrXk6KJZ4sw5js4j6Vd+8X8"}], "size": 2528}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.0_1689861587412_0.5524486151264669"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T13:59:47.586Z", "publish_time": 1689861587586, "_source_registry_name": "default"}, "8.0.0-alpha.1": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.1", "dist": {"shasum": "a94289f910b2594720af126ff75fe671fe9b5684", "integrity": "sha512-iu/mszkJEcr09uIit7LkZOIryCshEVN41NTHBoj8KD7cVN8O55eSVTq+aG37W7dXGupap2irqkEXKYYzm+9mUQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.1.tgz", "fileCount": 5, "unpackedSize": 5345, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoksV/oYDcnIq5vQoW6a+y3ftQSyBD7mJyhPNzbgUaNgIhAPW0doLCvdRmZM27Yfz9KRmCv87MAwVv8ySL8zt4ucNa"}], "size": 2529}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.1_1690221092056_0.46698546725391754"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-24T17:51:32.225Z", "publish_time": 1690221092225, "_source_registry_name": "default"}, "8.0.0-alpha.2": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.2", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.2", "dist": {"shasum": "6bcf314ccfbaa6c21cbd701e894a9c9741c7f12f", "integrity": "sha512-ZlHEXuZB6fgrHtVnaQ3NVe2UcpPvMJfstp8xz8+qv7jsTcK3TDJF7nFw3mzKQ0yEGFQUMnnytuoGLTULy5UqeA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.2.tgz", "fileCount": 5, "unpackedSize": 5345, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCruYV+afuFqQZWn/EVjKS4WqGUd6ZdEmirucOOimdZ7AIhAJj2M+pySLszBqipj1gZow8kPhlJtueXIAHUPGG9jpt/"}], "size": 2530}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.2_1691594087398_0.5912882236535884"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-09T15:14:47.579Z", "publish_time": 1691594087579, "_source_registry_name": "default"}, "8.0.0-alpha.3": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.3", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.3", "dist": {"shasum": "5089bac312f717c13543380e4cca695d20975993", "integrity": "sha512-OHq+uMOUy+20BILe17SkdjNmZLSu8QYyk6201Qo3GNIcXmBFtEzUUq+VH53GpOIdqHILxJwHTpdwUlRN3SmFYA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.3.tgz", "fileCount": 5, "unpackedSize": 5345, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD43iiZZUAtuST7JHS2agiRE0tZoIu1lmsMo51oNxVfQAIhAMVdX4GvI7YDTp5D7vB93rlpSV49JozH6xH6ea/iZ2Xo"}], "size": 2530}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.3_1695740202825_0.8595313135074671"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T14:56:43.047Z", "publish_time": 1695740203047, "_source_registry_name": "default"}, "8.0.0-alpha.4": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.4", "dist": {"shasum": "7a13dfb205cd35c5973b5a9915f38b03312e6fea", "integrity": "sha512-2irZKx1DMic6epGVye9ibYemwKdpndTq5WhsekTQ6GXmUwL0JR+HFPCdogxlbhh2KNSXPpEj8EEL/L9YnE6/kQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.4.tgz", "fileCount": 5, "unpackedSize": 5345, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFYJhX3ktIXSbdI89uuWADSybnhPCqFnNK3HsprHEkVQIgYNer8AC/ZLhKckpqbaAiS3C85MkwRt32xzRdM2kPl44="}], "size": 2530}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.4_1697076369017_0.7465110718109056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-12T02:06:09.308Z", "publish_time": 1697076369308, "_source_registry_name": "default"}, "7.23.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.23.3", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.23.3", "dist": {"shasum": "24f460c85dbbc983cd2b9c4994178bcc01df958f", "integrity": "sha512-9EiNjVJOMwCO+43TqoTrgQ8jMwcAd0sWyXi9RPfIsLTj4R2MADDDQXELhffaUx/uJv2AYcxBgPwH6j4TIA4ytQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.23.3.tgz", "fileCount": 5, "unpackedSize": 6936, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBZqy2vuBFYjAYJr0kEe6jUGinByXKV7XytAwCSW4H00AiEA5PXOTIGNpCbDeccjBS5oJZxYK0JTPW7c4Oux8AdVuHA="}], "size": 3030}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.23.3_1699513425155_0.08160154339177339"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-09T07:03:45.369Z", "publish_time": 1699513425369, "_source_registry_name": "default"}, "8.0.0-alpha.5": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.5", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.5", "dist": {"shasum": "e76b5a6ac17211bc8b57100c940db2601a2d525e", "integrity": "sha512-9C8Li7VQnqKxRHhys2BsxnIeD77G1RhMo8ZLhw/B0puvp9eJGZEm6GtyFmejPhJ/lp9YnTRF7ScUj8USUXC9RA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.5.tgz", "fileCount": 5, "unpackedSize": 5458, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYkbdzI/rbAvd83adaURdjwnorwPJ1VoDHfrKZcMOrKQIhAPHCANHLmM5wv3sznHmSlsNEOLhNLO3Kc8Bm++H8ncLe"}], "size": 2602}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.5_1702307911507_0.12779459668361604"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-11T15:18:31.707Z", "publish_time": 1702307911707, "_source_registry_name": "default"}, "8.0.0-alpha.6": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.6", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.6", "dist": {"shasum": "0bcd77251a7f227e6b6b91901b3a9f44b1389170", "integrity": "sha512-Bb4mY9lHrPyeLB8LbBBwTyQ40oTP/LZi6Zs4+MwEzU2PhVg9gu264bq0nv7FSQY9G6juXp+AWvYhhOnTteuQUA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.6.tgz", "fileCount": 5, "unpackedSize": 5458, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4iTStsh3KX/8skPzoGv0X3Se6wIoM7XbA98D1+/tZkQIhAIDYfnJGsl6/GGs454mw0mO2VATKJJY5BXaYa2PiWugt"}], "size": 2602}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.6_1706285633301_0.5503814703813763"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-26T16:13:53.483Z", "publish_time": 1706285633483, "_source_registry_name": "default"}, "8.0.0-alpha.7": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.7", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.7", "dist": {"shasum": "843b69b5dbe11822ae3ccd793d2445868d3cdbf3", "integrity": "sha512-o2wzteoEa5kYnVL+fBVhGAX88pUt+KTi9EWuN24xOhl7/wD7Qz28aCItTaDRm6oUHTyfwabq+/jLNWftdHdmfA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.7.tgz", "fileCount": 5, "unpackedSize": 5458, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsFapLa4+IezecmvICBoeI5DNwyDk8VKI/3d0jt3fAUAIhANARCwfK1WZ56IOHMGZp+sl9IHrWFUYnaeWEdYhcTCmo"}], "size": 2602}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.7_1709129081510_0.25953897008351023"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-28T14:04:41.651Z", "publish_time": 1709129081651, "_source_registry_name": "default"}, "7.24.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.24.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.24.1", "dist": {"shasum": "b3bcc51f396d15f3591683f90239de143c076844", "integrity": "sha512-Yhnmvy5HZEnHUty6i++gcfH1/l68AHnItFHnaCv6hn9dNh0hQvvQJsxpi4BMBFN5DLeHBuucT/0DgzXif/OyRw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.1.tgz", "fileCount": 5, "unpackedSize": 6867, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGJtF/FzAPvLP+37q/O9Ic2qHr6ZBRLWxUuAZbuLxOGiAiBZr5sUgnc+Wv6swGL4z7BzQo30S3RGbMkLZ2ZzQCwp4w=="}], "size": 3046}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.24.1_1710841705955_0.8545301386829289"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-19T09:48:26.115Z", "publish_time": 1710841706115, "_source_registry_name": "default"}, "8.0.0-alpha.8": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.8", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.8", "dist": {"shasum": "6ab158d97a25c61cb614f1824588433c65a08f56", "integrity": "sha512-RDB1a9YOsqWO2DUFC8kr5P8GcsHED/DwSn2uv/2GlrjJ8unDLIKdbRgdvwCQkGOnrwjbd8bD0oaPs1WXCTdeew==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.8.tgz", "fileCount": 5, "unpackedSize": 5372, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJlR9RomtAwXIVv5oXukIzOXTPo9p/NfBDbVRoR1H//AiEAux6p+SXJmFDCprB6Gleh7fjnh9HzlM2PNPM3dJVyq8Q="}], "size": 2573}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.8_1712236782980_0.6857685756628236"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-04T13:19:43.124Z", "publish_time": 1712236783124, "_source_registry_name": "default"}, "7.24.6": {"name": "@babel/plugin-syntax-typescript", "version": "7.24.6", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.24.6", "dist": {"shasum": "769daf2982d60308bc83d8936eaecb7582463c87", "integrity": "sha512-TzCtxGgVTEJWWwcYwQhCIQ6WaKlo80/B+Onsk4RRCcYqpYGFcG9etPW94VToGte5AAcxRrhjPUFvUS3Y2qKi4A==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.6.tgz", "fileCount": 7, "unpackedSize": 72774, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoCkapLkTKQ3BwLg8iuBzf43dZy/sIhGHkhMqTNRShHgIhAKzISf798tfXvAKsJZY/wMorBxNyJTAKvpBDT25u2rAA"}], "size": 25842}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.24.6_1716553464267_0.9311300149635915"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-24T12:24:24.427Z", "publish_time": 1716553464427, "_source_registry_name": "default"}, "8.0.0-alpha.9": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.9", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.9", "dist": {"shasum": "0fa0d913ce7bb9508e1f05d34b47950787c74a72", "integrity": "sha512-vFA76I6u/MpsAKS7DYjyHJh0FkY9mi0l5dchzyrzun8JztilTnqSWB1KK79yUq7T2eFWKNG4mqvrBgTX3zq6YQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.9.tgz", "fileCount": 8, "unpackedSize": 71674, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQwgE0UbCb7cNa5lAsVRcLGG1Uvr23x9r+75CxMBLm8AIgOZc4ysL5VVJK2yFqHxPxVA1qLU5cOjx/372e1VKpYc4="}], "size": 25486}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.9_1717423443026_0.2195643674506882"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-03T14:04:03.210Z", "publish_time": 1717423443210, "_source_registry_name": "default"}, "8.0.0-alpha.10": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.10", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.10", "dist": {"shasum": "b61e14d56d4d03a4b6a333c0c316dc13fd9d5069", "integrity": "sha512-ADjQwMxEKhebJUjdWYVCwt/2fwLTQRAJjRnA7PdCLUxMPV32WDTh1uEBb4UNyFjHPf78Tw3qHn62Z1YW1Hlxfw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.10.tgz", "fileCount": 8, "unpackedSize": 71681, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3Kw63QqwQr6Mzmb/AlZrlfRCANTK4hQdkDZ6nKAOTkwIhAK6toBGErI/VlAZex6zUKus0K7kIhweXFciQ8aEwQriS"}], "size": 25487}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.10_1717499995068_0.9117603805365941"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-04T11:19:55.251Z", "publish_time": 1717499995251, "_source_registry_name": "default"}, "7.24.7": {"name": "@babel/plugin-syntax-typescript", "version": "7.24.7", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.24.7", "dist": {"shasum": "58d458271b4d3b6bb27ee6ac9525acbb259bad1c", "integrity": "sha512-c/+fVeJBB0FeKsFvwytYiUD+LBvhHjGSI0g446PRGdSVGZLRNArBUno2PETbAly3tpiNAQR5XaZ+JslxkotsbA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.7.tgz", "fileCount": 7, "unpackedSize": 72770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXFQGe9Z2FrtzxHcJDVtwSJ3nTl+V4+O4ZxmIO+VbjIAiBjblOd5Gyd5VNUMPVF0AYK4VvDgcnz6KXkHKRXd/GG9A=="}], "size": 25844}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.24.7_1717593315413_0.38629963790474897"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T13:15:15.625Z", "publish_time": 1717593315625, "_source_registry_name": "default"}, "8.0.0-alpha.11": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.11", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.11", "dist": {"shasum": "e9c8fff07d840dfa4cb9de7f878b6e24fd7547f4", "integrity": "sha512-BXxDfqD99hJlSU31+4Dnwecz2rB8IbilBKtuGca6olgKsgqzAVrj+pJ90HO6wK/X/6x8S/KL8XqdzNCJkF08eA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.11.tgz", "fileCount": 8, "unpackedSize": 71570, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBWaPZ1k2zkfkOB+Ic8pUHOkaDXF/r6pjpvAahFyMnnGAiEArpFP751J6VBhDGbp4WUFQc4dpc8NLTr9rXvTIDatKuE="}], "size": 25454}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.11_1717751724087_0.9962401357870776"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T09:15:24.251Z", "publish_time": 1717751724251, "_source_registry_name": "default"}, "8.0.0-alpha.12": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.12", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.12", "dist": {"shasum": "86b8b0479f5d4458ebb674f0bf4004e856139e54", "integrity": "sha512-5NRbx6VPWVqXNnMWYnsV/iuwuHNNw1+XAkeJri7Wn7pnoPfWhz4Ru2nzlDD705+JVq8mcj5tkZ0Ex+EDU8GVRQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.12.tgz", "fileCount": 8, "unpackedSize": 68362, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFZq7ky6rZl4aCOUy5yI0pVSOOoNgOqowgDXGdPh3ouVAiEAk91hHcMAY6bkzdNc3zi2lsh9WMULuMMAnqbBkM8hrKc="}], "size": 25087}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.12_1722015200401_0.44574376923825443"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-26T17:33:20.609Z", "publish_time": 1722015200609, "_source_registry_name": "default"}, "7.25.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.25.4", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.25.4", "dist": {"shasum": "04db9ce5a9043d9c635e75ae7969a2cd50ca97ff", "integrity": "sha512-uMOCoHVU52BsSWxPOMVv5qKRdeSlPuImUCB2dlPuBSU+W2/ROE7/Zg8F2Kepbk+8yBa68LlRKxO+xgEVWorsDg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.4.tgz", "fileCount": 7, "unpackedSize": 69677, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3Ath9F0fL4r4QzDgJRR9n6F5hC2ZP5YWCSaRj+uI3UgIhAKq5rOAp00ch3g95sjqtHFe8epOtRnN0atAATchgbyDP"}], "size": 25528}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.25.4_1724319266470_0.013396826971339593"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-22T09:34:26.647Z", "publish_time": 1724319266647, "_source_registry_name": "default"}, "7.25.7": {"name": "@babel/plugin-syntax-typescript", "version": "7.25.7", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.25.7", "dist": {"shasum": "bfc05b0cc31ebd8af09964650cee723bb228108b", "integrity": "sha512-rR+5FDjpCHqqZN2bzZm18bVYGaejGq5ZkpVCJLXor/+zlSrSoc4KWcHI0URVWjl/68Dyr1uwZUz/1njycEAv9g==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.7.tgz", "fileCount": 7, "unpackedSize": 77480, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAOz04mDTn6NueB2wMKXU0L7Y0V5L/HdAw3o7Qa9t5yrAiEAnkLZYN5zcxkUn1/nvh0eMJIf0IUh1RnGqzbjGm8a17E="}], "size": 26167}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.25.7_1727882079404_0.9431833436388812"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-02T15:14:39.601Z", "publish_time": 1727882079601, "_source_registry_name": "default"}, "7.25.9": {"name": "@babel/plugin-syntax-typescript", "version": "7.25.9", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.25.9", "dist": {"shasum": "67dda2b74da43727cf21d46cf9afef23f4365399", "integrity": "sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz", "fileCount": 5, "unpackedSize": 7039, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+0jPkqWDlDvRcZEwagANZPWRGBd5Rh4DtotlXGzZCDAiEA3jpc34h5VxT0vq+efB3ugj44BpuVR2QmZwB9dudV9Ww="}], "size": 3092}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_7.25.9_1729610457355_0.97506534685597"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-22T15:20:57.541Z", "publish_time": 1729610457541, "_source_registry_name": "default"}, "8.0.0-alpha.13": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.13", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.13", "dist": {"shasum": "b6e36ecf00f8a32f3b380838d289c7eea5e175ee", "integrity": "sha512-JLVXI1FxjMV5LIeume1kOX7LwlIC24NM2j4GX+UuBBvcuZrDMbEHVYziabquxbE1HvYTIijW5A1RGXFjlM/Y1g==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.13.tgz", "fileCount": 6, "unpackedSize": 5931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGlVWaXiSDO9v0Dl24Se1CvPekiAoPkCpIsMc0ART8z7AiEAzz2MThXYtRJIxASakNJaXH1GvCah+qAaVxpYVNG8qME="}], "size": 2778}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.13_1729864441137_0.9963781947492221"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-25T13:54:01.328Z", "publish_time": 1729864441328, "_source_registry_name": "default"}, "8.0.0-alpha.14": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.14", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.14", "dist": {"shasum": "c396a77b5e0ea7c60c5b06c53ac83151aeecaba6", "integrity": "sha512-H2IGgHidNtLSR22oFO5ttXz+Puqx8a4qsQFMaLpE9HmbbzqUJQZwn0wOOM7j6KV+BizAeBKLs0dzL8sTBScEvw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.14.tgz", "fileCount": 6, "unpackedSize": 5931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEAKO1pXlswlnGTrGqXBLnJCGhrC0Rv9iclChJ9yBUc5AiA2kA49RmhYx9TeCmHdr6pCHnFIds9aizzPtZxG60yPqw=="}], "size": 2778}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.14_1733504032529_0.9472153113271546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T16:53:52.682Z", "publish_time": 1733504032682, "_source_registry_name": "default"}, "8.0.0-alpha.15": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.15", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.15", "dist": {"shasum": "f29a31bebf4bcf9b2949b475bebaae5600372a36", "integrity": "sha512-xbV7jQdZ8QFzbT1Vf964azfhPcjUFSossFvHU0Q3ePyX4tYN6LjCf3s6MeOGNF9W3/VUSG2LsrVvLDeW0+odrA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.15.tgz", "fileCount": 6, "unpackedSize": 5931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtu6wVvFd/FyxvPWwhVM9lHGdjD6kL/H5a5UMJmmi8dgIgTWnrJ1+YbvpV5W5swj5bsFpLZa60BUKFeIbVZqqsq/Y="}], "size": 2778}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.15_1736529856721_0.27858015026004423"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-10T17:24:16.918Z", "publish_time": 1736529856918, "_source_registry_name": "default"}, "8.0.0-alpha.16": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.16", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.16", "dist": {"shasum": "dd51d10d1ed6774def4c3de33b181b6b72c5506e", "integrity": "sha512-oJo0qKKzDKWyJf/wsZB5tS4GyHf6b+WMBpWRWa/Kw78PU7Eypj5obtxcR4aDuXuE9pkY8+QoDpbYZJHnDQjMsA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.16.tgz", "fileCount": 6, "unpackedSize": 5931, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCLlwymBYaSI4DzWQ/K4iWdOuWevvJdP8td0IMY64lk6AIgT9MKfVPEQ60G4Jmu2NfQac+r3GyQDaCDloiKrNAdfVw="}], "size": 2778}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.16_1739534333985_0.8033350810558213"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T11:58:54.163Z", "publish_time": 1739534334163, "_source_registry_name": "default"}, "8.0.0-alpha.17": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.17", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.17", "dist": {"shasum": "441f89c30ef51c8c391d9551b6466d72b248990b", "integrity": "sha512-rgruFOxLnkyi6YuAQfVqgrDkcOgx6QttZnTCR7mB8QRwz+ezn+pD2VstsajGhwRlJZWZ5/wyYNkH4rAmsoTGjQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.17.tgz", "fileCount": 6, "unpackedSize": 5931, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICF3WJbGlfPR+BGqp2XikTBrMqg/UfBg6azVDKfIqxSWAiEApSG1gVqVvwyoax3Ex1J39TYlVRkfv5pBX3rjOmAkrIg="}], "size": 2779}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.17_1741717485757_0.220863639284693"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-11T18:24:45.945Z", "publish_time": 1741717485945, "_source_registry_name": "default"}, "7.27.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.27.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-syntax-typescript@7.27.1", "dist": {"shasum": "5147d29066a793450f220c63fa3a9431b7e6dd18", "integrity": "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "fileCount": 5, "unpackedSize": 7039, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB9phZ0xc12OijvnTMpxjzf5Ba+tq9oZuZOgaZUmKcOQAiAs4NbCJvxykGA8Eato/4Fjh7YciJ9bhYk7SruS2+re4w=="}], "size": 3091}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_7.27.1_1746025723609_0.46483866299970056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-30T15:08:43.818Z", "publish_time": 1746025723818, "_source_registry_name": "default"}, "8.0.0-beta.0": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-beta.0", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-beta.0", "dist": {"shasum": "7d32d905a9cab7efb462f99942cca6b9b6ff3025", "integrity": "sha512-xx0V90wBW99b5MURYA98CO+9vMXGK4U0Ld1cdiZNS3+Ez8aqdY01e0/NeG+bm0AogOsRMjvwoSn1B/+esoOU3Q==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-beta.0.tgz", "fileCount": 6, "unpackedSize": 5907, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDtUIEOAhcFjTrcBgthKu7c8dPyhdUBcCZIGPmOElEJUwIgJM8D/lbAHkymsf47n5gCpmuIkSBM6INsdJoS4lxsAq8="}], "size": 2766}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_8.0.0-beta.0_1748620255696_0.48154605135895134"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-30T15:50:55.850Z", "publish_time": 1748620255850, "_source_registry_name": "default"}, "8.0.0-beta.1": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-beta.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-beta.1", "dist": {"shasum": "718639238a062974c72e032d2c756d1271e09530", "integrity": "sha512-ZgfSbsSjOvdyPZOIC6ofjb8oukVgYncC+D1/SvahKtVDW0V0gI5KzZCYVNp6TeYrC/NZEgI9gEdyemv7srnRWg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5907, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHeCXXNw+KE+KpeJ0aOosJWfkjn10w2z+Ozdyb5ICIu3AiBveiXxQ2eUzdUdpQpqwwr28kyJRvG4qV0jdRvTD0tL5w=="}], "size": 2768}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_8.0.0-beta.1_1751447050192_0.9333325802847896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T09:04:10.349Z", "publish_time": 1751447050349, "_source_registry_name": "default"}}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "keywords": ["babel-plugin", "typescript"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "_source_registry_name": "default"}