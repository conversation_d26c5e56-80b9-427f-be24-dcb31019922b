0 verbose cli C:\Program Files\nodejs\node.exe C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:C:\Program Files\nodejs\node_modules\npm\npmrc
4 silly config load:file:D:\车秘系统相关文档\projects\project_manage\client\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:D:\tools\nodejs\node_global\etc\npmrc
7 verbose title npm install
8 verbose argv "install" "--legacy-peer-deps"
9 verbose logfile logs-max:10 dir:D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_50_468Z-
10 verbose logfile D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T06_17_50_468Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 silly logfile done cleaning log files
14 silly idealTree buildDeps
15 silly reify mark retired [
15 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\prettier',
15 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\prettier',
15 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\prettier.cmd',
15 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\prettier.ps1'
15 silly reify ]
16 silly reify mark retired [
16 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\sortablejs'
16 silly reify ]
17 silly reify mark retired [
17 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\yaml',
17 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\yaml',
17 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\yaml.cmd',
17 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\yaml.ps1'
17 silly reify ]
18 silly reify moves {
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\prettier': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.prettier-PuSsfcuX',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\prettier': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\.prettier-9bMojcgv',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\prettier.cmd': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\.prettier.cmd-SPBmaKyS',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\prettier.ps1': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\.prettier.ps1-OzmxGo6Y',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\sortablejs': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.sortablejs-M7GAFCvW',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\yaml': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.yaml-KaUIvSyO',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\yaml': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\.yaml-9Mu8unUt',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\yaml.cmd': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\.yaml.cmd-MDzF6zKn',
18 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\yaml.ps1': 'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\.bin\\.yaml.ps1-nStYyYgt'
18 silly reify }
19 silly audit bulk request {
19 silly audit   '@achrinza/node-ipc': [ '9.2.9' ],
19 silly audit   '@ampproject/remapping': [ '2.3.0' ],
19 silly audit   '@babel/code-frame': [ '7.27.1', '7.12.11' ],
19 silly audit   '@babel/compat-data': [ '7.28.0' ],
19 silly audit   '@babel/core': [ '7.28.0' ],
19 silly audit   '@babel/eslint-parser': [ '7.28.0' ],
19 silly audit   '@babel/generator': [ '7.28.0' ],
19 silly audit   '@babel/helper-annotate-as-pure': [ '7.27.3' ],
19 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
19 silly audit   '@babel/helper-create-class-features-plugin': [ '7.27.1' ],
19 silly audit   '@babel/helper-create-regexp-features-plugin': [ '7.27.1' ],
19 silly audit   '@babel/helper-define-polyfill-provider': [ '0.6.5' ],
19 silly audit   '@babel/helper-globals': [ '7.28.0' ],
19 silly audit   '@babel/helper-member-expression-to-functions': [ '7.27.1' ],
19 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
19 silly audit   '@babel/helper-module-transforms': [ '7.27.3' ],
19 silly audit   '@babel/helper-optimise-call-expression': [ '7.27.1' ],
19 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
19 silly audit   '@babel/helper-remap-async-to-generator': [ '7.27.1' ],
19 silly audit   '@babel/helper-replace-supers': [ '7.27.1' ],
19 silly audit   '@babel/helper-skip-transparent-expression-wrappers': [ '7.27.1' ],
19 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
19 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
19 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
19 silly audit   '@babel/helper-wrap-function': [ '7.27.1' ],
19 silly audit   '@babel/helpers': [ '7.27.6' ],
19 silly audit   '@babel/highlight': [ '7.25.9' ],
19 silly audit   'ansi-styles': [ '3.2.1', '4.3.0' ],
19 silly audit   chalk: [ '2.4.2', '4.1.2', '3.0.0' ],
19 silly audit   'color-convert': [ '1.9.3', '2.0.1' ],
19 silly audit   'color-name': [ '1.1.3', '1.1.4' ],
19 silly audit   'has-flag': [ '3.0.0', '4.0.0' ],
19 silly audit   'supports-color': [ '5.5.0', '8.1.1', '7.2.0' ],
19 silly audit   '@babel/parser': [ '7.28.0' ],
19 silly audit   '@babel/plugin-bugfix-firefox-class-in-computed-class-key': [ '7.27.1' ],
19 silly audit   '@babel/plugin-bugfix-safari-class-field-initializer-scope': [ '7.27.1' ],
19 silly audit   '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': [ '7.27.1' ],
19 silly audit   '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': [ '7.27.1' ],
19 silly audit   '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': [ '7.27.1' ],
19 silly audit   '@babel/plugin-proposal-class-properties': [ '7.18.6' ],
19 silly audit   '@babel/plugin-proposal-decorators': [ '7.28.0' ],
19 silly audit   '@babel/plugin-proposal-private-property-in-object': [ '7.21.0-placeholder-for-preset-env.2' ],
19 silly audit   '@babel/plugin-syntax-decorators': [ '7.27.1' ],
19 silly audit   '@babel/plugin-syntax-dynamic-import': [ '7.8.3' ],
19 silly audit   '@babel/plugin-syntax-import-assertions': [ '7.27.1' ],
19 silly audit   '@babel/plugin-syntax-import-attributes': [ '7.27.1' ],
19 silly audit   '@babel/plugin-syntax-jsx': [ '7.27.1' ],
19 silly audit   '@babel/plugin-syntax-typescript': [ '7.27.1' ],
19 silly audit   '@babel/plugin-syntax-unicode-sets-regex': [ '7.18.6' ],
19 silly audit   '@babel/plugin-transform-arrow-functions': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-async-generator-functions': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-async-to-generator': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-block-scoped-functions': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-block-scoping': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-class-properties': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-class-static-block': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-classes': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-computed-properties': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-destructuring': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-dotall-regex': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-duplicate-keys': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-duplicate-named-capturing-groups-regex': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-dynamic-import': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-explicit-resource-management': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-exponentiation-operator': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-export-namespace-from': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-for-of': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-function-name': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-json-strings': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-literals': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-logical-assignment-operators': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-member-expression-literals': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-modules-amd': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-modules-commonjs': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-modules-systemjs': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-modules-umd': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-named-capturing-groups-regex': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-new-target': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-nullish-coalescing-operator': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-numeric-separator': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-object-rest-spread': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-object-super': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-optional-catch-binding': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-optional-chaining': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-parameters': [ '7.27.7' ],
19 silly audit   '@babel/plugin-transform-private-methods': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-private-property-in-object': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-property-literals': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-regenerator': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-regexp-modifiers': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-reserved-words': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-runtime': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-shorthand-properties': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-spread': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-sticky-regex': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-template-literals': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-typeof-symbol': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-typescript': [ '7.28.0' ],
19 silly audit   '@babel/plugin-transform-unicode-escapes': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-unicode-property-regex': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-unicode-regex': [ '7.27.1' ],
19 silly audit   '@babel/plugin-transform-unicode-sets-regex': [ '7.27.1' ],
19 silly audit   '@babel/preset-env': [ '7.28.0' ],
19 silly audit   '@babel/preset-modules': [ '0.1.6-no-external-plugins' ],
19 silly audit   '@babel/preset-typescript': [ '7.27.1' ],
19 silly audit   '@babel/runtime': [ '7.27.6' ],
19 silly audit   '@babel/template': [ '7.27.2' ],
19 silly audit   '@babel/traverse': [ '7.28.0' ],
19 silly audit   '@babel/types': [ '7.28.0' ],
19 silly audit   '@ctrl/tinycolor': [ '3.6.1' ],
19 silly audit   '@discoveryjs/json-ext': [ '0.5.7' ],
19 silly audit   '@element-plus/icons-vue': [ '2.3.1' ],
19 silly audit   '@esbuild/aix-ppc64': [ '0.25.6' ],
19 silly audit   '@esbuild/android-arm': [ '0.25.6' ],
19 silly audit   '@esbuild/android-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/android-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/darwin-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/darwin-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/freebsd-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/freebsd-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-arm': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-ia32': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-loong64': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-mips64el': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-ppc64': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-riscv64': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-s390x': [ '0.25.6' ],
19 silly audit   '@esbuild/linux-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/netbsd-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/netbsd-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/openbsd-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/openbsd-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/openharmony-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/sunos-x64': [ '0.25.6' ],
19 silly audit   '@esbuild/win32-arm64': [ '0.25.6' ],
19 silly audit   '@esbuild/win32-ia32': [ '0.25.6' ],
19 silly audit   '@esbuild/win32-x64': [ '0.25.6' ],
19 silly audit   '@eslint/eslintrc': [ '0.4.3' ],
19 silly audit   ignore: [ '4.0.6', '5.3.2' ],
19 silly audit   '@floating-ui/core': [ '1.7.2' ],
19 silly audit   '@floating-ui/dom': [ '1.7.2' ],
19 silly audit   '@floating-ui/utils': [ '0.2.10' ],
19 silly audit   '@hapi/hoek': [ '9.3.0' ],
19 silly audit   '@hapi/topo': [ '5.1.0' ],
19 silly audit   '@humanwhocodes/config-array': [ '0.5.0' ],
19 silly audit   '@humanwhocodes/object-schema': [ '1.2.1' ],
19 silly audit   '@isaacs/balanced-match': [ '4.0.1' ],
19 silly audit   '@isaacs/brace-expansion': [ '5.0.0' ],
19 silly audit   '@jridgewell/gen-mapping': [ '0.3.12' ],
19 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
19 silly audit   '@jridgewell/source-map': [ '0.3.10' ],
19 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.4' ],
19 silly audit   '@jridgewell/trace-mapping': [ '0.3.29' ],
19 silly audit   '@leichtgewicht/ip-codec': [ '2.0.5' ],
19 silly audit   '@nicolo-ribaudo/eslint-scope-5-internals': [ '5.1.1-v1' ],
19 silly audit   '@node-ipc/js-queue': [ '2.0.3' ],
19 silly audit   '@nodelib/fs.scandir': [ '2.1.5' ],
19 silly audit   '@nodelib/fs.stat': [ '2.0.5' ],
19 silly audit   '@nodelib/fs.walk': [ '1.2.8' ],
19 silly audit   '@pkgr/core': [ '0.2.7' ],
19 silly audit   '@polka/url': [ '1.0.0-next.29' ],
19 silly audit   '@sxzz/popperjs-es': [ '2.11.7' ],
19 silly audit   '@rolldown/pluginutils': [ '1.0.0-beta.19' ],
19 silly audit   '@rollup/rollup-android-arm-eabi': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-android-arm64': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-darwin-arm64': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-darwin-x64': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-freebsd-arm64': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-freebsd-x64': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-arm-gnueabihf': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-arm-musleabihf': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-arm64-gnu': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-arm64-musl': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-loongarch64-gnu': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-powerpc64le-gnu': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-riscv64-gnu': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-riscv64-musl': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-s390x-gnu': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-x64-gnu': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-linux-x64-musl': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-win32-arm64-msvc': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-win32-ia32-msvc': [ '4.45.0' ],
19 silly audit   '@rollup/rollup-win32-x64-msvc': [ '4.45.0' ],
19 silly audit   '@sideway/address': [ '4.1.5' ],
19 silly audit   '@sideway/formula': [ '3.0.1' ],
19 silly audit   '@sideway/pinpoint': [ '2.0.0' ],
19 silly audit   '@soda/friendly-errors-webpack-plugin': [ '1.8.1' ],
19 silly audit   '@soda/get-current-script': [ '1.0.2' ],
19 silly audit   '@trysound/sax': [ '0.2.0' ],
19 silly audit   '@types/body-parser': [ '1.19.6' ],
19 silly audit   '@types/bonjour': [ '3.5.13' ],
19 silly audit   '@types/connect': [ '3.4.38' ],
19 silly audit   '@types/connect-history-api-fallback': [ '1.5.4' ],
19 silly audit   '@types/eslint': [ '8.56.12' ],
19 silly audit   '@types/eslint-scope': [ '3.7.7' ],
19 silly audit   '@types/estree': [ '1.0.8' ],
19 silly audit   '@types/express': [ '4.17.23' ],
19 silly audit   '@types/express-serve-static-core': [ '5.0.7', '4.19.6' ],
19 silly audit   '@types/html-minifier-terser': [ '6.1.0' ],
19 silly audit   '@types/http-errors': [ '2.0.5' ],
19 silly audit   '@types/http-proxy': [ '1.17.16' ],
19 silly audit   '@types/json-schema': [ '7.0.15' ],
19 silly audit   '@types/lodash': [ '4.17.20' ],
19 silly audit   '@types/lodash-es': [ '4.17.12' ],
19 silly audit   '@types/mime': [ '1.3.5' ],
19 silly audit   '@types/minimist': [ '1.2.5' ],
19 silly audit   '@types/node': [ '24.0.12' ],
19 silly audit   '@types/node-forge': [ '1.3.12' ],
19 silly audit   '@types/normalize-package-data': [ '2.4.4' ],
19 silly audit   '@types/parse-json': [ '4.0.2' ],
19 silly audit   '@types/qs': [ '6.14.0' ],
19 silly audit   '@types/range-parser': [ '1.2.7' ],
19 silly audit   '@types/retry': [ '0.12.0' ],
19 silly audit   '@types/send': [ '0.17.5' ],
19 silly audit   '@types/serve-index': [ '1.9.4' ],
19 silly audit   '@types/serve-static': [ '1.15.8' ],
19 silly audit   '@types/sockjs': [ '0.3.36' ],
19 silly audit   '@types/web-bluetooth': [ '0.0.16' ],
19 silly audit   '@types/webpack-env': [ '1.18.8' ],
19 silly audit   '@types/ws': [ '8.18.1' ],
19 silly audit   '@typescript-eslint/eslint-plugin': [ '4.33.0' ],
19 silly audit   semver: [ '7.7.2', '5.7.2', '6.3.1' ],
19 silly audit   '@typescript-eslint/experimental-utils': [ '4.33.0' ],
19 silly audit   'eslint-utils': [ '3.0.0', '2.1.0' ],
19 silly audit   '@typescript-eslint/parser': [ '4.33.0' ],
19 silly audit   '@typescript-eslint/scope-manager': [ '4.33.0' ],
19 silly audit   '@typescript-eslint/types': [ '4.33.0' ],
19 silly audit   '@typescript-eslint/typescript-estree': [ '4.33.0' ],
19 silly audit   '@typescript-eslint/visitor-keys': [ '4.33.0' ],
19 silly audit   '@vitejs/plugin-vue': [ '6.0.0' ],
19 silly audit   '@volar/language-core': [ '2.4.17' ],
19 silly audit   '@volar/source-map': [ '2.4.17' ],
19 silly audit   '@volar/typescript': [ '2.4.17' ],
19 silly audit   '@vue/babel-helper-vue-jsx-merge-props': [ '1.4.0' ],
19 silly audit   '@vue/babel-helper-vue-transform-on': [ '1.4.0' ],
19 silly audit   '@vue/babel-plugin-jsx': [ '1.4.0' ],
19 silly audit   '@vue/babel-plugin-resolve-type': [ '1.4.0' ],
19 silly audit   '@vue/babel-plugin-transform-vue-jsx': [ '1.4.0' ],
19 silly audit   '@vue/babel-preset-app': [ '5.0.8' ],
19 silly audit   '@vue/babel-preset-jsx': [ '1.4.0' ],
19 silly audit   '@vue/babel-sugar-composition-api-inject-h': [ '1.4.0' ],
19 silly audit   '@vue/babel-sugar-composition-api-render-instance': [ '1.4.0' ],
19 silly audit   '@vue/babel-sugar-functional-vue': [ '1.4.0' ],
19 silly audit   '@vue/babel-sugar-inject-h': [ '1.4.0' ],
19 silly audit   '@vue/babel-sugar-v-model': [ '1.4.0' ],
19 silly audit   '@vue/babel-sugar-v-on': [ '1.4.0' ],
19 silly audit   '@vue/cli-overlay': [ '5.0.8' ],
19 silly audit   '@vue/cli-plugin-babel': [ '5.0.8' ],
19 silly audit   '@vue/cli-plugin-eslint': [ '5.0.8' ],
19 silly audit   '@vue/cli-plugin-router': [ '5.0.8' ],
19 silly audit   '@vue/cli-plugin-typescript': [ '5.0.8' ],
19 silly audit   '@vue/cli-plugin-vuex': [ '5.0.8' ],
19 silly audit   '@vue/cli-service': [ '5.0.8' ],
19 silly audit   '@vue/cli-shared-utils': [ '5.0.8' ],
19 silly audit   'lru-cache': [ '6.0.0', '4.1.5', '5.1.1' ],
19 silly audit   yallist: [ '4.0.0', '2.1.2', '3.1.1' ],
19 silly audit   '@vue/compiler-core': [ '3.5.17' ],
19 silly audit   '@vue/compiler-dom': [ '3.5.17' ],
19 silly audit   '@vue/compiler-sfc': [ '3.5.17' ],
19 silly audit   '@vue/compiler-ssr': [ '3.5.17' ],
19 silly audit   '@vue/compiler-vue2': [ '2.7.16' ],
19 silly audit   '@vue/component-compiler-utils': [ '3.3.0' ],
19 silly audit   'hash-sum': [ '1.0.2', '2.0.0' ],
19 silly audit   picocolors: [ '0.2.1', '1.1.1' ],
19 silly audit   postcss: [ '7.0.39', '8.5.6' ],
19 silly audit   prettier: [ '2.8.8' ],
19 silly audit   '@vue/devtools-api': [ '6.6.4' ],
19 silly audit   '@vue/eslint-config-typescript': [ '7.0.0' ],
19 silly audit   acorn: [ '7.4.1', '8.15.0' ],
19 silly audit   'eslint-visitor-keys': [ '1.3.0', '2.1.0', '3.4.3' ],
19 silly audit   espree: [ '6.2.1', '7.3.1', '9.6.1' ],
19 silly audit   'vue-eslint-parser': [ '7.11.0', '8.3.0' ],
19 silly audit   '@vue/language-core': [ '3.0.1' ],
19 silly audit   minimatch: [ '10.0.3', '3.1.2' ],
19 silly audit   '@vue/reactivity': [ '3.5.17' ],
19 silly audit   '@vue/runtime-core': [ '3.5.17' ],
19 silly audit   '@vue/runtime-dom': [ '3.5.17' ],
19 silly audit   '@vue/server-renderer': [ '3.5.17' ],
19 silly audit   '@vue/shared': [ '3.5.17' ],
19 silly audit   'vue-loader': [ '15.11.1', '17.3.1' ],
19 silly audit   '@vue/web-component-wrapper': [ '1.3.0' ],
19 silly audit   '@vueuse/core': [ '9.13.0' ],
19 silly audit   '@vueuse/metadata': [ '9.13.0' ],
19 silly audit   '@vueuse/shared': [ '9.13.0' ],
19 silly audit   '@webassemblyjs/ast': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/floating-point-hex-parser': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/helper-api-error': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/helper-buffer': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/helper-numbers': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/helper-wasm-bytecode': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/helper-wasm-section': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/ieee754': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/leb128': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/utf8': [ '1.13.2' ],
19 silly audit   '@webassemblyjs/wasm-edit': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/wasm-gen': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/wasm-opt': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/wasm-parser': [ '1.14.1' ],
19 silly audit   '@webassemblyjs/wast-printer': [ '1.14.1' ],
19 silly audit   '@xtuc/ieee754': [ '1.2.0' ],
19 silly audit   '@xtuc/long': [ '4.2.2' ],
19 silly audit   accepts: [ '1.3.8' ],
19 silly audit   negotiator: [ '0.6.3', '0.6.4' ],
19 silly audit   'acorn-jsx': [ '5.3.2' ],
19 silly audit   'acorn-walk': [ '8.3.4' ],
19 silly audit   address: [ '1.2.2' ],
19 silly audit   'adler-32': [ '1.3.1' ],
19 silly audit   ajv: [ '6.12.6', '8.17.1' ],
19 silly audit   'ajv-formats': [ '2.1.1' ],
19 silly audit   'json-schema-traverse': [ '1.0.0', '0.4.1' ],
19 silly audit   'ajv-keywords': [ '3.5.2', '5.1.0' ],
19 silly audit   'alien-signals': [ '2.0.5' ],
19 silly audit   'ansi-colors': [ '4.1.3' ],
19 silly audit   'ansi-escapes': [ '3.2.0' ],
19 silly audit   'ansi-html-community': [ '0.0.8' ],
19 silly audit   'ansi-regex': [ '5.0.1', '3.0.1' ],
19 silly audit   'any-promise': [ '1.3.0' ],
19 silly audit   anymatch: [ '3.1.3' ],
19 silly audit   arch: [ '2.2.0' ],
19 silly audit   argparse: [ '1.0.10' ],
19 silly audit   'array-flatten': [ '1.1.1' ],
19 silly audit   'array-union': [ '2.1.0' ],
19 silly audit   'astral-regex': [ '2.0.0' ],
19 silly audit   async: [ '3.2.6' ],
19 silly audit   'async-validator': [ '4.2.5' ],
19 silly audit   asynckit: [ '0.4.0' ],
19 silly audit   'at-least-node': [ '1.0.0' ],
19 silly audit   autoprefixer: [ '10.4.21' ],
19 silly audit   axios: [ '1.10.0' ],
19 silly audit   'babel-loader': [ '8.4.1' ],
19 silly audit   'loader-utils': [ '2.0.4', '1.4.2' ],
19 silly audit   'babel-plugin-dynamic-import-node': [ '2.3.3' ],
19 silly audit   'babel-plugin-polyfill-corejs2': [ '0.4.14' ],
19 silly audit   'babel-plugin-polyfill-corejs3': [ '0.13.0' ],
19 silly audit   'babel-plugin-polyfill-regenerator': [ '0.6.5' ],
19 silly audit   'balanced-match': [ '1.0.2' ],
19 silly audit   'base64-js': [ '1.5.1' ],
19 silly audit   batch: [ '0.6.1' ],
19 silly audit   'big.js': [ '5.2.2' ],
19 silly audit   'binary-extensions': [ '2.3.0' ],
19 silly audit   bl: [ '4.1.0' ],
19 silly audit   bluebird: [ '3.7.2' ],
19 silly audit   'body-parser': [ '1.20.3' ],
19 silly audit   debug: [ '2.6.9', '4.4.1' ],
19 silly audit   ms: [ '2.0.0', '2.1.3' ],
19 silly audit   'bonjour-service': [ '1.3.0' ],
19 silly audit   boolbase: [ '1.0.0' ],
19 silly audit   'brace-expansion': [ '1.1.12' ],
19 silly audit   braces: [ '3.0.3' ],
19 silly audit   browserslist: [ '4.25.1' ],
19 silly audit   buffer: [ '5.7.1' ],
19 silly audit   'buffer-from': [ '1.1.2' ],
19 silly audit   bytes: [ '3.1.2' ],
19 silly audit   'call-bind': [ '1.0.8' ],
19 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
19 silly audit   'call-bound': [ '1.0.4' ],
19 silly audit   callsites: [ '3.1.0' ],
19 silly audit   'camel-case': [ '4.1.2' ],
19 silly audit   camelcase: [ '5.3.1' ],
19 silly audit   'caniuse-api': [ '3.0.0' ],
19 silly audit   'caniuse-lite': [ '1.0.30001727' ],
19 silly audit   'case-sensitive-paths-webpack-plugin': [ '2.4.0' ],
19 silly audit   cfb: [ '1.2.2' ],
19 silly audit   chokidar: [ '3.6.0' ],
19 silly audit   'glob-parent': [ '5.1.2', '6.0.2' ],
19 silly audit   'chrome-trace-event': [ '1.0.4' ],
19 silly audit   'ci-info': [ '1.6.0' ],
19 silly audit   'clean-css': [ '5.3.3' ],
19 silly audit   'cli-cursor': [ '3.1.0', '2.1.0' ],
19 silly audit   'cli-highlight': [ '2.1.11' ],
19 silly audit   'cli-spinners': [ '2.9.2' ],
19 silly audit   clipboardy: [ '2.3.0' ],
19 silly audit   cliui: [ '7.0.4' ],
19 silly audit   clone: [ '1.0.4' ],
19 silly audit   'clone-deep': [ '4.0.1' ],
19 silly audit   codepage: [ '1.15.0' ],
19 silly audit   colord: [ '2.9.3' ],
19 silly audit   colorette: [ '2.0.20' ],
19 silly audit   'combined-stream': [ '1.0.8' ],
19 silly audit   commander: [ '8.3.0', '7.2.0', '2.20.3' ],
19 silly audit   commondir: [ '1.0.1' ],
19 silly audit   compressible: [ '2.0.18' ],
19 silly audit   compression: [ '1.8.0' ],
19 silly audit   'concat-map': [ '0.0.1' ],
19 silly audit   'connect-history-api-fallback': [ '2.0.0' ],
19 silly audit   consolidate: [ '0.15.1' ],
19 silly audit   'content-disposition': [ '0.5.4' ],
19 silly audit   'content-type': [ '1.0.5' ],
19 silly audit   'convert-source-map': [ '2.0.0' ],
19 silly audit   cookie: [ '0.7.1' ],
19 silly audit   'cookie-signature': [ '1.0.6' ],
19 silly audit   'copy-anything': [ '2.0.6' ],
19 silly audit   'copy-webpack-plugin': [ '9.1.0' ],
19 silly audit   'schema-utils': [ '3.3.0', '4.3.2', '2.7.0', '2.7.1' ],
19 silly audit   'core-js': [ '3.44.0' ],
19 silly audit   'core-js-compat': [ '3.44.0' ],
19 silly audit   'core-util-is': [ '1.0.3' ],
19 silly audit   cosmiconfig: [ '7.1.0', '6.0.0' ],
19 silly audit   yaml: [ '1.10.2' ],
19 silly audit   'crc-32': [ '1.2.2' ],
19 silly audit   'cross-spawn': [ '6.0.6', '7.0.6', '5.1.0' ],
19 silly audit   'css-declaration-sorter': [ '6.4.1' ],
19 silly audit   'css-loader': [ '6.11.0' ],
19 silly audit   'css-minimizer-webpack-plugin': [ '3.4.1' ],
19 silly audit   'css-select': [ '4.3.0' ],
19 silly audit   'css-tree': [ '1.1.3' ],
19 silly audit   'css-what': [ '6.2.2' ],
19 silly audit   cssesc: [ '3.0.0' ],
19 silly audit   cssnano: [ '5.1.15' ],
19 silly audit   'cssnano-preset-default': [ '5.2.14' ],
19 silly audit   'cssnano-utils': [ '3.1.0' ],
19 silly audit   csso: [ '4.2.0' ],
19 silly audit   csstype: [ '3.1.3' ],
19 silly audit   dayjs: [ '1.11.13' ],
19 silly audit   'de-indent': [ '1.0.2' ],
19 silly audit   debounce: [ '1.2.1' ],
19 silly audit   'deep-is': [ '0.1.4' ],
19 silly audit   deepmerge: [ '1.5.2', '4.3.1' ],
19 silly audit   'default-gateway': [ '6.0.3' ],
19 silly audit   execa: [ '5.1.1', '1.0.0', '0.8.0' ],
19 silly audit   'get-stream': [ '6.0.1', '4.1.0', '3.0.0' ],
19 silly audit   'is-stream': [ '2.0.1', '1.1.0' ],
19 silly audit   'npm-run-path': [ '4.0.1', '2.0.2' ],
19 silly audit   'path-key': [ '3.1.1', '2.0.1' ],
19 silly audit   'shebang-command': [ '2.0.0', '1.2.0' ],
19 silly audit   'shebang-regex': [ '3.0.0', '1.0.0' ],
19 silly audit   which: [ '2.0.2', '1.3.1' ],
19 silly audit   defaults: [ '1.0.4' ],
19 silly audit   'define-data-property': [ '1.1.4' ],
19 silly audit   'define-lazy-prop': [ '2.0.0' ],
19 silly audit   'define-properties': [ '1.2.1' ],
19 silly audit   'delayed-stream': [ '1.0.0' ],
19 silly audit   depd: [ '2.0.0', '1.1.2' ],
19 silly audit   destroy: [ '1.2.0' ],
19 silly audit   'detect-node': [ '2.1.0' ],
19 silly audit   'dir-glob': [ '3.0.1' ],
19 silly audit   'dns-packet': [ '5.6.1' ],
19 silly audit   doctrine: [ '3.0.0' ],
19 silly audit   'dom-converter': [ '0.2.0' ],
19 silly audit   'dom-serializer': [ '1.4.1' ],
19 silly audit   entities: [ '2.2.0', '4.5.0' ],
19 silly audit   domelementtype: [ '2.3.0' ],
19 silly audit   domhandler: [ '4.3.1' ],
19 silly audit   domutils: [ '2.8.0' ],
19 silly audit   'dot-case': [ '3.0.4' ],
19 silly audit   dotenv: [ '10.0.0' ],
19 silly audit   'dotenv-expand': [ '5.1.0' ],
19 silly audit   'dunder-proto': [ '1.0.1' ],
19 silly audit   duplexer: [ '0.1.2' ],
19 silly audit   'easy-stack': [ '1.0.1' ],
19 silly audit   echarts: [ '5.6.0' ],
19 silly audit   tslib: [ '2.3.0', '2.8.1', '1.14.1' ],
19 silly audit   'ee-first': [ '1.1.1' ],
19 silly audit   'electron-to-chromium': [ '1.5.180' ],
19 silly audit   'element-plus': [ '2.10.4' ],
19 silly audit   'emoji-regex': [ '8.0.0' ],
19 silly audit   'emojis-list': [ '3.0.0' ],
19 silly audit   encodeurl: [ '2.0.0', '1.0.2' ],
19 silly audit   'end-of-stream': [ '1.4.5' ],
19 silly audit   'enhanced-resolve': [ '5.18.2' ],
19 silly audit   enquirer: [ '2.4.1' ],
19 silly audit   errno: [ '0.1.8' ],
19 silly audit   'error-ex': [ '1.3.2' ],
19 silly audit   'error-stack-parser': [ '2.1.4' ],
19 silly audit   'es-define-property': [ '1.0.1' ],
19 silly audit   'es-errors': [ '1.3.0' ],
19 silly audit   'es-module-lexer': [ '1.7.0' ],
19 silly audit   'es-object-atoms': [ '1.1.1' ],
19 silly audit   'es-set-tostringtag': [ '2.1.0' ],
19 silly audit   esbuild: [ '0.25.6' ],
19 silly audit   escalade: [ '3.2.0' ],
19 silly audit   'escape-html': [ '1.0.3' ],
19 silly audit   'escape-string-regexp': [ '1.0.5', '4.0.0' ],
19 silly audit   eslint: [ '7.32.0' ],
19 silly audit   'eslint-config-prettier': [ '10.1.5' ],
19 silly audit   'eslint-plugin-prettier': [ '5.5.1' ],
19 silly audit   'eslint-plugin-vue': [ '8.7.1' ],
19 silly audit   'eslint-scope': [ '5.1.1', '7.2.2' ],
19 silly audit   'eslint-webpack-plugin': [ '3.2.0' ],
19 silly audit   'jest-worker': [ '28.1.3', '27.5.1' ],
19 silly audit   esprima: [ '4.0.1' ],
19 silly audit   esquery: [ '1.6.0' ],
19 silly audit   estraverse: [ '5.3.0', '4.3.0' ],
19 silly audit   esrecurse: [ '4.3.0' ],
19 silly audit   'estree-walker': [ '2.0.2' ],
19 silly audit   esutils: [ '2.0.3' ],
19 silly audit   etag: [ '1.8.1' ],
19 silly audit   'event-pubsub': [ '4.3.0' ],
19 silly audit   eventemitter3: [ '4.0.7' ],
19 silly audit   events: [ '3.3.0' ],
19 silly audit   express: [ '4.21.2' ],
19 silly audit   'fast-deep-equal': [ '3.1.3' ],
19 silly audit   'fast-diff': [ '1.3.0' ],
19 silly audit   'fast-glob': [ '3.3.3' ],
19 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
19 silly audit   'fast-levenshtein': [ '2.0.6' ],
19 silly audit   'fast-uri': [ '3.0.6' ],
19 silly audit   fastq: [ '1.19.1' ],
19 silly audit   'faye-websocket': [ '0.11.4' ],
19 silly audit   figures: [ '2.0.0' ],
19 silly audit   'file-entry-cache': [ '6.0.1' ],
19 silly audit   'file-saver': [ '2.0.5' ],
19 silly audit   'fill-range': [ '7.1.1' ],
19 silly audit   finalhandler: [ '1.3.1' ],
19 silly audit   'find-cache-dir': [ '3.3.2' ],
19 silly audit   'find-up': [ '4.1.0' ],
19 silly audit   flat: [ '5.0.2' ],
19 silly audit   'flat-cache': [ '3.2.0' ],
19 silly audit   flatted: [ '3.3.3' ],
19 silly audit   'follow-redirects': [ '1.15.9' ],
19 silly audit   'fork-ts-checker-webpack-plugin': [ '6.5.3' ],
19 silly audit   tapable: [ '1.1.3', '2.2.2' ],
19 silly audit   'form-data': [ '4.0.3' ],
19 silly audit   forwarded: [ '0.2.0' ],
19 silly audit   frac: [ '1.1.2' ],
19 silly audit   'fraction.js': [ '4.3.7' ],
19 silly audit   fresh: [ '0.5.2' ],
19 silly audit   'fs-extra': [ '9.1.0' ],
19 silly audit   'fs-monkey': [ '1.0.6' ],
19 silly audit   'fs.realpath': [ '1.0.0' ],
19 silly audit   fsevents: [ '2.3.3' ],
19 silly audit   'function-bind': [ '1.1.2' ],
19 silly audit   'functional-red-black-tree': [ '1.0.1' ],
19 silly audit   gensync: [ '1.0.0-beta.2' ],
19 silly audit   'get-caller-file': [ '2.0.5' ],
19 silly audit   'get-intrinsic': [ '1.3.0' ],
19 silly audit   'get-proto': [ '1.0.1' ],
19 silly audit   glob: [ '7.2.3' ],
19 silly audit   'glob-to-regexp': [ '0.4.1' ],
19 silly audit   globals: [ '13.24.0' ],
19 silly audit   'type-fest': [ '0.20.2', '0.8.1', '0.6.0' ],
19 silly audit   globby: [ '11.1.0' ],
19 silly audit   gopd: [ '1.2.0' ],
19 silly audit   'graceful-fs': [ '4.2.11' ],
19 silly audit   'gzip-size': [ '6.0.0' ],
19 silly audit   'handle-thing': [ '2.0.1' ],
19 silly audit   'has-property-descriptors': [ '1.0.2' ],
19 silly audit   'has-symbols': [ '1.1.0' ],
19 silly audit   'has-tostringtag': [ '1.0.2' ],
19 silly audit   hasown: [ '2.0.2' ],
19 silly audit   he: [ '1.2.0' ],
19 silly audit   'highlight.js': [ '10.7.3' ],
19 silly audit   'hosted-git-info': [ '2.8.9' ],
19 silly audit   'hpack.js': [ '2.1.6' ],
19 silly audit   'readable-stream': [ '2.3.8', '3.6.2' ],
19 silly audit   'safe-buffer': [ '5.1.2', '5.2.1' ],
19 silly audit   string_decoder: [ '1.1.1', '1.3.0' ],
19 silly audit   'html-entities': [ '2.6.0' ],
19 silly audit   'html-escaper': [ '2.0.2' ],
19 silly audit   'html-minifier-terser': [ '6.1.0' ],
19 silly audit   'html-tags': [ '2.0.0' ],
19 silly audit   'html-webpack-plugin': [ '5.6.3' ],
19 silly audit   htmlparser2: [ '6.1.0' ],
19 silly audit   'http-deceiver': [ '1.2.7' ],
19 silly audit   'http-errors': [ '2.0.0', '1.6.3' ],
19 silly audit   'http-parser-js': [ '0.5.10' ],
19 silly audit   'http-proxy': [ '1.18.1' ],
19 silly audit   'http-proxy-middleware': [ '2.0.9' ],
19 silly audit   'human-signals': [ '2.1.0' ],
19 silly audit   'iconv-lite': [ '0.4.24', '0.6.3' ],
19 silly audit   'icss-utils': [ '5.1.0' ],
19 silly audit   ieee754: [ '1.2.1' ],
19 silly audit   'image-size': [ '0.5.5' ],
19 silly audit   'import-fresh': [ '3.3.1' ],
19 silly audit   imurmurhash: [ '0.1.4' ],
19 silly audit   inflight: [ '1.0.6' ],
19 silly audit   inherits: [ '2.0.4', '2.0.3' ],
19 silly audit   'ipaddr.js': [ '2.2.0', '1.9.1' ],
19 silly audit   'is-arrayish': [ '0.2.1' ],
19 silly audit   'is-binary-path': [ '2.1.0' ],
19 silly audit   'is-ci': [ '1.2.1' ],
19 silly audit   'is-core-module': [ '2.16.1' ],
19 silly audit   'is-docker': [ '2.2.1' ],
19 silly audit   'is-extglob': [ '2.1.1' ],
19 silly audit   'is-file-esm': [ '1.0.0' ],
19 silly audit   'is-fullwidth-code-point': [ '3.0.0', '2.0.0' ],
19 silly audit   'is-glob': [ '4.0.3' ],
19 silly audit   'is-interactive': [ '1.0.0' ],
19 silly audit   'is-number': [ '7.0.0' ],
19 silly audit   'is-plain-obj': [ '3.0.0' ],
19 silly audit   'is-plain-object': [ '2.0.4' ],
19 silly audit   'is-unicode-supported': [ '0.1.0' ],
19 silly audit   'is-what': [ '3.14.1' ],
19 silly audit   'is-wsl': [ '2.2.0' ],
19 silly audit   isarray: [ '1.0.0' ],
19 silly audit   isexe: [ '2.0.0' ],
19 silly audit   isobject: [ '3.0.1' ],
19 silly audit   'javascript-stringify': [ '2.1.0' ],
19 silly audit   joi: [ '17.13.3' ],
19 silly audit   'js-message': [ '1.0.7' ],
19 silly audit   'js-tokens': [ '4.0.0' ],
19 silly audit   'js-yaml': [ '3.14.1' ],
19 silly audit   jsesc: [ '3.1.0', '3.0.2' ],
19 silly audit   'json-buffer': [ '3.0.1' ],
19 silly audit   'json-parse-better-errors': [ '1.0.2' ],
19 silly audit   'json-parse-even-better-errors': [ '2.3.1' ],
19 silly audit   'json-stable-stringify-without-jsonify': [ '1.0.1' ],
19 silly audit   json5: [ '2.2.3', '1.0.2' ],
19 silly audit   jsonfile: [ '6.1.0' ],
19 silly audit   keyv: [ '4.5.4' ],
19 silly audit   'kind-of': [ '6.0.3' ],
19 silly audit   klona: [ '2.0.6' ],
19 silly audit   'launch-editor': [ '2.10.0' ],
19 silly audit   'launch-editor-middleware': [ '2.10.0' ],
19 silly audit   less: [ '4.3.0' ],
19 silly audit   'less-loader': [ '12.3.0' ],
19 silly audit   'make-dir': [ '2.1.0', '3.1.0' ],
19 silly audit   levn: [ '0.4.1' ],
19 silly audit   lilconfig: [ '2.1.0' ],
19 silly audit   'lines-and-columns': [ '1.2.4' ],
19 silly audit   'loader-runner': [ '4.3.0' ],
19 silly audit   'locate-path': [ '5.0.0' ],
19 silly audit   lodash: [ '4.17.21' ],
19 silly audit   'lodash-es': [ '4.17.21' ],
19 silly audit   'lodash-unified': [ '1.0.3' ],
19 silly audit   'lodash.debounce': [ '4.0.8' ],
19 silly audit   'lodash.defaultsdeep': [ '4.6.1' ],
19 silly audit   'lodash.kebabcase': [ '4.1.1' ],
19 silly audit   'lodash.mapvalues': [ '4.6.0' ],
19 silly audit   'lodash.memoize': [ '4.1.2' ],
19 silly audit   'lodash.merge': [ '4.6.2' ],
19 silly audit   'lodash.truncate': [ '4.4.2' ],
19 silly audit   'lodash.uniq': [ '4.5.0' ],
19 silly audit   'log-symbols': [ '4.1.0' ],
19 silly audit   'log-update': [ '2.3.0' ],
19 silly audit   'mimic-fn': [ '1.2.0', '2.1.0' ],
19 silly audit   onetime: [ '2.0.1', '5.1.2' ],
19 silly audit   'restore-cursor': [ '2.0.0', '3.1.0' ],
19 silly audit   'string-width': [ '2.1.1', '4.2.3' ],
19 silly audit   'strip-ansi': [ '4.0.0', '6.0.1' ],
19 silly audit   'wrap-ansi': [ '3.0.1', '7.0.0' ],
19 silly audit   'lower-case': [ '2.0.2' ],
19 silly audit   'magic-string': [ '0.30.17' ],
19 silly audit   'math-intrinsics': [ '1.1.0' ],
19 silly audit   'mdn-data': [ '2.0.14' ],
19 silly audit   'media-typer': [ '0.3.0' ],
19 silly audit   memfs: [ '3.5.3' ],
19 silly audit   'memoize-one': [ '6.0.0' ],
19 silly audit   'merge-descriptors': [ '1.0.3' ],
19 silly audit   'merge-source-map': [ '1.1.0' ],
19 silly audit   'merge-stream': [ '2.0.0' ],
19 silly audit   merge2: [ '1.4.1' ],
19 silly audit   methods: [ '1.1.2' ],
19 silly audit   micromatch: [ '4.0.8' ],
19 silly audit   mime: [ '1.6.0' ],
19 silly audit   'mime-db': [ '1.52.0' ],
19 silly audit   'mime-types': [ '2.1.35' ],
19 silly audit   'mini-css-extract-plugin': [ '2.9.2' ],
19 silly audit   'minimalistic-assert': [ '1.0.1' ],
19 silly audit   minimist: [ '1.2.8' ],
19 silly audit   minipass: [ '3.3.6' ],
19 silly audit   'module-alias': [ '2.2.3' ],
19 silly audit   mrmime: [ '2.0.1' ],
19 silly audit   'muggle-string': [ '0.4.1' ],
19 silly audit   'multicast-dns': [ '7.2.5' ],
19 silly audit   mz: [ '2.7.0' ],
19 silly audit   nanoid: [ '3.3.11' ],
19 silly audit   'natural-compare': [ '1.4.0' ],
19 silly audit   needle: [ '3.3.1' ],
19 silly audit   'neo-async': [ '2.6.2' ],
19 silly audit   'nice-try': [ '1.0.5' ],
19 silly audit   'no-case': [ '3.0.4' ],
19 silly audit   'node-fetch': [ '2.7.0' ],
19 silly audit   'node-forge': [ '1.3.1' ],
19 silly audit   'node-releases': [ '2.0.19' ],
19 silly audit   'normalize-package-data': [ '2.5.0' ],
19 silly audit   'normalize-path': [ '3.0.0', '1.0.0' ],
19 silly audit   'normalize-range': [ '0.1.2' ],
19 silly audit   'normalize-url': [ '6.1.0' ],
19 silly audit   'normalize-wheel-es': [ '1.2.0' ],
19 silly audit   'nth-check': [ '2.1.1' ],
19 silly audit   'object-assign': [ '4.1.1' ],
19 silly audit   'object-inspect': [ '1.13.4' ],
19 silly audit   'object-keys': [ '1.1.1' ],
19 silly audit   'object.assign': [ '4.1.7' ],
19 silly audit   obuf: [ '1.1.2' ],
19 silly audit   'on-finished': [ '2.4.1' ],
19 silly audit   'on-headers': [ '1.0.2' ],
19 silly audit   once: [ '1.4.0' ],
19 silly audit   open: [ '8.4.2' ],
19 silly audit   opener: [ '1.5.2' ],
19 silly audit   optionator: [ '0.9.4' ],
19 silly audit   ora: [ '5.4.1' ],
19 silly audit   'p-finally': [ '1.0.0' ],
19 silly audit   'p-limit': [ '2.3.0' ],
19 silly audit   'p-locate': [ '4.1.0' ],
19 silly audit   'p-retry': [ '4.6.2' ],
19 silly audit   'p-try': [ '2.2.0' ],
19 silly audit   'param-case': [ '3.0.4' ],
19 silly audit   'parent-module': [ '1.0.1' ],
19 silly audit   'parse-json': [ '5.2.0' ],
19 silly audit   'parse-node-version': [ '1.0.1' ],
19 silly audit   parse5: [ '5.1.1', '6.0.1' ],
19 silly audit   'parse5-htmlparser2-tree-adapter': [ '6.0.1' ],
19 silly audit   parseurl: [ '1.3.3' ],
19 silly audit   'pascal-case': [ '3.1.2' ],
19 silly audit   'path-browserify': [ '1.0.1' ],
19 silly audit   'path-exists': [ '4.0.0' ],
19 silly audit   'path-is-absolute': [ '1.0.1' ],
19 silly audit   'path-parse': [ '1.0.7' ],
19 silly audit   'path-to-regexp': [ '0.1.12' ],
19 silly audit   'path-type': [ '4.0.0' ],
19 silly audit   picomatch: [ '2.3.1', '4.0.2' ],
19 silly audit   pify: [ '4.0.1' ],
19 silly audit   'pkg-dir': [ '4.2.0' ],
19 silly audit   portfinder: [ '1.0.37' ],
19 silly audit   'postcss-calc': [ '8.2.4' ],
19 silly audit   'postcss-colormin': [ '5.3.1' ],
19 silly audit   'postcss-convert-values': [ '5.1.3' ],
19 silly audit   'postcss-discard-comments': [ '5.1.2' ],
19 silly audit   'postcss-discard-duplicates': [ '5.1.0' ],
19 silly audit   'postcss-discard-empty': [ '5.1.1' ],
19 silly audit   'postcss-discard-overridden': [ '5.1.0' ],
19 silly audit   'postcss-loader': [ '6.2.1' ],
19 silly audit   'postcss-merge-longhand': [ '5.1.7' ],
19 silly audit   'postcss-merge-rules': [ '5.1.4' ],
19 silly audit   'postcss-minify-font-values': [ '5.1.0' ],
19 silly audit   'postcss-minify-gradients': [ '5.1.1' ],
19 silly audit   'postcss-minify-params': [ '5.1.4' ],
19 silly audit   'postcss-minify-selectors': [ '5.2.1' ],
19 silly audit   'postcss-modules-extract-imports': [ '3.1.0' ],
19 silly audit   'postcss-modules-local-by-default': [ '4.2.0' ],
19 silly audit   'postcss-selector-parser': [ '7.1.0', '6.1.2' ],
19 silly audit   'postcss-modules-scope': [ '3.2.1' ],
19 silly audit   'postcss-modules-values': [ '4.0.0' ],
19 silly audit   'postcss-normalize-charset': [ '5.1.0' ],
19 silly audit   'postcss-normalize-display-values': [ '5.1.0' ],
19 silly audit   'postcss-normalize-positions': [ '5.1.1' ],
19 silly audit   'postcss-normalize-repeat-style': [ '5.1.1' ],
19 silly audit   'postcss-normalize-string': [ '5.1.0' ],
19 silly audit   'postcss-normalize-timing-functions': [ '5.1.0' ],
19 silly audit   'postcss-normalize-unicode': [ '5.1.1' ],
19 silly audit   'postcss-normalize-url': [ '5.1.0' ],
19 silly audit   'postcss-normalize-whitespace': [ '5.1.1' ],
19 silly audit   'postcss-ordered-values': [ '5.1.3' ],
19 silly audit   'postcss-reduce-initial': [ '5.1.2' ],
19 silly audit   'postcss-reduce-transforms': [ '5.1.0' ],
19 silly audit   'postcss-svgo': [ '5.1.0' ],
19 silly audit   'postcss-unique-selectors': [ '5.1.1' ],
19 silly audit   'postcss-value-parser': [ '4.2.0' ],
19 silly audit   'prelude-ls': [ '1.2.1' ],
19 silly audit   'prettier-linter-helpers': [ '1.0.0' ],
19 silly audit   'pretty-error': [ '4.0.0' ],
19 silly audit   'process-nextick-args': [ '2.0.1' ],
19 silly audit   progress: [ '2.0.3' ],
19 silly audit   'progress-webpack-plugin': [ '1.0.16' ],
19 silly audit   'proxy-addr': [ '2.0.7' ],
19 silly audit   'proxy-from-env': [ '1.1.0' ],
19 silly audit   prr: [ '1.0.1' ],
19 silly audit   pseudomap: [ '1.0.2' ],
19 silly audit   pump: [ '3.0.3' ],
19 silly audit   punycode: [ '2.3.1' ],
19 silly audit   qs: [ '6.13.0' ],
19 silly audit   'queue-microtask': [ '1.2.3' ],
19 silly audit   randombytes: [ '2.1.0' ],
19 silly audit   'range-parser': [ '1.2.1' ],
19 silly audit   'raw-body': [ '2.5.2' ],
19 silly audit   'read-pkg': [ '5.2.0' ],
19 silly audit   'read-pkg-up': [ '7.0.1' ],
19 silly audit   readdirp: [ '3.6.0' ],
19 silly audit   regenerate: [ '1.4.2' ],
19 silly audit   'regenerate-unicode-properties': [ '10.2.0' ],
19 silly audit   regexpp: [ '3.2.0' ],
19 silly audit   'regexpu-core': [ '6.2.0' ],
19 silly audit   regjsgen: [ '0.8.0' ],
19 silly audit   regjsparser: [ '0.12.0' ],
19 silly audit   relateurl: [ '0.2.7' ],
19 silly audit   renderkid: [ '3.0.0' ],
19 silly audit   'require-directory': [ '2.1.1' ],
19 silly audit   'require-from-string': [ '2.0.2' ],
19 silly audit   'requires-port': [ '1.0.0' ],
19 silly audit   resolve: [ '1.22.10' ],
19 silly audit   'resolve-from': [ '4.0.0' ],
19 silly audit   retry: [ '0.13.1' ],
19 silly audit   reusify: [ '1.1.0' ],
19 silly audit   rimraf: [ '3.0.2' ],
19 silly audit   rollup: [ '4.45.0' ],
19 silly audit   'run-parallel': [ '1.2.0' ],
19 silly audit   'safer-buffer': [ '2.1.2' ],
19 silly audit   sax: [ '1.4.1' ],
19 silly audit   'select-hose': [ '2.0.0' ],
19 silly audit   selfsigned: [ '2.4.1' ],
19 silly audit   send: [ '0.19.0' ],
19 silly audit   'serialize-javascript': [ '6.0.2' ],
19 silly audit   'serve-index': [ '1.9.1' ],
19 silly audit   setprototypeof: [ '1.1.0', '1.2.0' ],
19 silly audit   statuses: [ '1.5.0', '2.0.1' ],
19 silly audit   'serve-static': [ '1.16.2' ],
19 silly audit   'set-function-length': [ '1.2.2' ],
19 silly audit   'shallow-clone': [ '3.0.1' ],
19 silly audit   'shell-quote': [ '1.8.3' ],
19 silly audit   'side-channel': [ '1.1.0' ],
19 silly audit   'side-channel-list': [ '1.0.0' ],
19 silly audit   'side-channel-map': [ '1.0.1' ],
19 silly audit   'side-channel-weakmap': [ '1.0.2' ],
19 silly audit   'signal-exit': [ '3.0.7' ],
19 silly audit   sirv: [ '2.0.4' ],
19 silly audit   slash: [ '3.0.0' ],
19 silly audit   'slice-ansi': [ '4.0.0' ],
19 silly audit   sockjs: [ '0.3.24' ],
19 silly audit   'source-map': [ '0.6.1', '0.7.4' ],
19 silly audit   'source-map-js': [ '1.2.1' ],
19 silly audit   'source-map-support': [ '0.5.21' ],
19 silly audit   'spdx-correct': [ '3.2.0' ],
19 silly audit   'spdx-exceptions': [ '2.5.0' ],
19 silly audit   'spdx-expression-parse': [ '3.0.1' ],
19 silly audit   'spdx-license-ids': [ '3.0.21' ],
19 silly audit   spdy: [ '4.0.2' ],
19 silly audit   'spdy-transport': [ '3.0.0' ],
19 silly audit   'sprintf-js': [ '1.0.3' ],
19 silly audit   ssf: [ '0.11.2' ],
19 silly audit   ssri: [ '8.0.1' ],
19 silly audit   stable: [ '0.1.8' ],
19 silly audit   stackframe: [ '1.3.4' ],
19 silly audit   'strip-eof': [ '1.0.0' ],
19 silly audit   'strip-final-newline': [ '2.0.0' ],
19 silly audit   'strip-indent': [ '2.0.0' ],
19 silly audit   'strip-json-comments': [ '3.1.1' ],
19 silly audit   stylehacks: [ '5.1.1' ],
19 silly audit   'supports-preserve-symlinks-flag': [ '1.0.0' ],
19 silly audit   'svg-tags': [ '1.0.0' ],
19 silly audit   svgo: [ '2.8.0' ],
19 silly audit   synckit: [ '0.11.8' ],
19 silly audit   table: [ '6.9.0' ],
19 silly audit   terser: [ '5.43.1' ],
19 silly audit   'terser-webpack-plugin': [ '5.3.14' ],
19 silly audit   'text-table': [ '0.2.0' ],
19 silly audit   thenify: [ '3.3.1' ],
19 silly audit   'thenify-all': [ '1.6.0' ],
19 silly audit   'thread-loader': [ '3.0.4' ],
19 silly audit   thunky: [ '1.1.0' ],
19 silly audit   tinyglobby: [ '0.2.14' ],
19 silly audit   fdir: [ '6.4.6' ],
19 silly audit   'to-regex-range': [ '5.0.1' ],
19 silly audit   toidentifier: [ '1.0.1' ],
19 silly audit   totalist: [ '3.0.1' ],
19 silly audit   tr46: [ '0.0.3' ],
19 silly audit   'ts-loader': [ '9.5.2' ],
19 silly audit   tsutils: [ '3.21.0' ],
19 silly audit   'type-check': [ '0.4.0' ],
19 silly audit   'type-is': [ '1.6.18' ],
19 silly audit   typescript: [ '5.8.3' ],
19 silly audit   'undici-types': [ '7.8.0' ],
19 silly audit   'unicode-canonical-property-names-ecmascript': [ '2.0.1' ],
19 silly audit   'unicode-match-property-ecmascript': [ '2.0.0' ],
19 silly audit   'unicode-match-property-value-ecmascript': [ '2.2.0' ],
19 silly audit   'unicode-property-aliases-ecmascript': [ '2.1.0' ],
19 silly audit   universalify: [ '2.0.1' ],
19 silly audit   unpipe: [ '1.0.0' ],
19 silly audit   'update-browserslist-db': [ '1.1.3' ],
19 silly audit   'uri-js': [ '4.4.1' ],
19 silly audit   'util-deprecate': [ '1.0.2' ],
19 silly audit   utila: [ '0.4.0' ],
19 silly audit   'utils-merge': [ '1.0.1' ],
19 silly audit   uuid: [ '8.3.2' ],
19 silly audit   'v8-compile-cache': [ '2.4.0' ],
19 silly audit   'validate-npm-package-license': [ '3.0.4' ],
19 silly audit   vary: [ '1.1.2' ],
19 silly audit   vite: [ '7.0.4' ],
19 silly audit   'vscode-uri': [ '3.1.0' ],
19 silly audit   vue: [ '3.5.17' ],
19 silly audit   'vue-demi': [ '0.13.11' ],
19 silly audit   'vue-draggable-next': [ '2.2.1' ],
19 silly audit   'vue-echarts': [ '7.0.3' ],
19 silly audit   'vue-hot-reload-api': [ '2.3.4' ],
19 silly audit   'vue-router': [ '4.5.1' ],
19 silly audit   'vue-style-loader': [ '4.1.3' ],
19 silly audit   'vue-template-es2015-compiler': [ '1.9.1' ],
19 silly audit   'vue-tsc': [ '3.0.1' ],
19 silly audit   vuex: [ '4.1.0' ],
19 silly audit   watchpack: [ '2.4.4' ],
19 silly audit   wbuf: [ '1.7.3' ],
19 silly audit   wcwidth: [ '1.0.1' ],
19 silly audit   'webidl-conversions': [ '3.0.1' ],
19 silly audit   webpack: [ '5.99.9' ],
19 silly audit   'webpack-bundle-analyzer': [ '4.10.2' ],
19 silly audit   'webpack-chain': [ '6.5.1' ],
19 silly audit   'webpack-dev-middleware': [ '5.3.4' ],
19 silly audit   'webpack-dev-server': [ '4.15.2' ],
19 silly audit   ws: [ '8.18.3', '7.5.10' ],
19 silly audit   'webpack-merge': [ '5.10.0' ],
19 silly audit   'webpack-sources': [ '3.3.3' ],
19 silly audit   'webpack-virtual-modules': [ '0.4.6' ],
19 silly audit   'websocket-driver': [ '0.7.4' ],
19 silly audit   'websocket-extensions': [ '0.1.4' ],
19 silly audit   'whatwg-fetch': [ '3.6.20' ],
19 silly audit   'whatwg-url': [ '5.0.0' ],
19 silly audit   wildcard: [ '2.0.1' ],
19 silly audit   wmf: [ '1.0.2' ],
19 silly audit   word: [ '0.3.0' ],
19 silly audit   'word-wrap': [ '1.2.5' ],
19 silly audit   wrappy: [ '1.0.2' ],
19 silly audit   xlsx: [ '0.18.5' ],
19 silly audit   y18n: [ '5.0.8' ],
19 silly audit   yargs: [ '16.2.0' ],
19 silly audit   'yargs-parser': [ '20.2.9' ],
19 silly audit   yorkie: [ '2.0.0' ],
19 silly audit   zrender: [ '5.6.1' ]
19 silly audit }
20 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\fsevents
21 silly reify mark deleted [
21 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\fsevents'
21 silly reify ]
22 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-win32-ia32-msvc
23 silly reify mark deleted [
23 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-win32-ia32-msvc'
23 silly reify ]
24 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-win32-arm64-msvc
25 silly reify mark deleted [
25 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-win32-arm64-msvc'
25 silly reify ]
26 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-x64-musl
27 silly reify mark deleted [
27 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-x64-musl'
27 silly reify ]
28 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-x64-gnu
29 silly reify mark deleted [
29 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-x64-gnu'
29 silly reify ]
30 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-s390x-gnu
31 silly reify mark deleted [
31 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-s390x-gnu'
31 silly reify ]
32 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-riscv64-musl
33 silly reify mark deleted [
33 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-riscv64-musl'
33 silly reify ]
34 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-riscv64-gnu
35 silly reify mark deleted [
35 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-riscv64-gnu'
35 silly reify ]
36 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-powerpc64le-gnu
37 silly reify mark deleted [
37 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-powerpc64le-gnu'
37 silly reify ]
38 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-loongarch64-gnu
39 silly reify mark deleted [
39 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-loongarch64-gnu'
39 silly reify ]
40 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-arm64-musl
41 silly reify mark deleted [
41 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-arm64-musl'
41 silly reify ]
42 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-arm64-gnu
43 silly reify mark deleted [
43 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-arm64-gnu'
43 silly reify ]
44 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-arm-musleabihf
45 silly reify mark deleted [
45 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-arm-musleabihf'
45 silly reify ]
46 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-linux-arm-gnueabihf
47 silly reify mark deleted [
47 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-linux-arm-gnueabihf'
47 silly reify ]
48 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-freebsd-x64
49 silly reify mark deleted [
49 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-freebsd-x64'
49 silly reify ]
50 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-freebsd-arm64
51 silly reify mark deleted [
51 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-freebsd-arm64'
51 silly reify ]
52 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-darwin-x64
53 silly reify mark deleted [
53 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-darwin-x64'
53 silly reify ]
54 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-darwin-arm64
55 silly reify mark deleted [
55 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-darwin-arm64'
55 silly reify ]
56 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-android-arm64
57 silly reify mark deleted [
57 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-android-arm64'
57 silly reify ]
58 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@rollup\rollup-android-arm-eabi
59 silly reify mark deleted [
59 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@rollup\\rollup-android-arm-eabi'
59 silly reify ]
60 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\win32-ia32
61 silly reify mark deleted [
61 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\win32-ia32'
61 silly reify ]
62 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\win32-arm64
63 silly reify mark deleted [
63 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\win32-arm64'
63 silly reify ]
64 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\sunos-x64
65 silly reify mark deleted [
65 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\sunos-x64'
65 silly reify ]
66 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\openharmony-arm64
67 silly reify mark deleted [
67 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\openharmony-arm64'
67 silly reify ]
68 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\openbsd-x64
69 silly reify mark deleted [
69 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\openbsd-x64'
69 silly reify ]
70 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\openbsd-arm64
71 silly reify mark deleted [
71 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\openbsd-arm64'
71 silly reify ]
72 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\netbsd-x64
73 silly reify mark deleted [
73 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\netbsd-x64'
73 silly reify ]
74 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\netbsd-arm64
75 silly reify mark deleted [
75 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\netbsd-arm64'
75 silly reify ]
76 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-x64
77 silly reify mark deleted [
77 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-x64'
77 silly reify ]
78 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-s390x
79 silly reify mark deleted [
79 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-s390x'
79 silly reify ]
80 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-riscv64
81 silly reify mark deleted [
81 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-riscv64'
81 silly reify ]
82 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-ppc64
83 silly reify mark deleted [
83 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-ppc64'
83 silly reify ]
84 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-mips64el
85 silly reify mark deleted [
85 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-mips64el'
85 silly reify ]
86 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-loong64
87 silly reify mark deleted [
87 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-loong64'
87 silly reify ]
88 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-ia32
89 silly reify mark deleted [
89 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-ia32'
89 silly reify ]
90 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-arm64
91 silly reify mark deleted [
91 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-arm64'
91 silly reify ]
92 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\linux-arm
93 silly reify mark deleted [
93 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\linux-arm'
93 silly reify ]
94 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\freebsd-x64
95 silly reify mark deleted [
95 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\freebsd-x64'
95 silly reify ]
96 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\freebsd-arm64
97 silly reify mark deleted [
97 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\freebsd-arm64'
97 silly reify ]
98 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\darwin-x64
99 silly reify mark deleted [
99 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\darwin-x64'
99 silly reify ]
100 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\darwin-arm64
101 silly reify mark deleted [
101 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\darwin-arm64'
101 silly reify ]
102 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\android-x64
103 silly reify mark deleted [
103 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\android-x64'
103 silly reify ]
104 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\android-arm64
105 silly reify mark deleted [
105 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\android-arm64'
105 silly reify ]
106 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\android-arm
107 silly reify mark deleted [
107 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\android-arm'
107 silly reify ]
108 verbose reify failed optional dependency D:\车秘系统相关文档\projects\project_manage\client\node_modules\@esbuild\aix-ppc64
109 silly reify mark deleted [
109 silly reify   'D:\\车秘系统相关文档\\projects\\project_manage\\client\\node_modules\\@esbuild\\aix-ppc64'
109 silly reify ]
110 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/advisories/bulk 741ms
111 silly audit bulk request failed [object Object]
112 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/audits/quick 35ms
113 verbose audit error HttpErrorGeneral: 404 Not Found - POST https://registry.npmmirror.com/-/npm/v1/security/audits/quick - [NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet
113 verbose audit error     at C:\Program Files\nodejs\node_modules\npm\node_modules\npm-registry-fetch\lib\check-response.js:103:15
113 verbose audit error     at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
113 verbose audit error     at async [getReport] (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:336:21)
113 verbose audit error     at async AuditReport.run (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:106:19)
113 verbose audit error     at async Arborist.reify (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\reify.js:268:24)
113 verbose audit error     at async Install.exec (C:\Program Files\nodejs\node_modules\npm\lib\commands\install.js:150:5)
113 verbose audit error     at async Npm.exec (C:\Program Files\nodejs\node_modules\npm\lib\npm.js:207:9)
113 verbose audit error     at async module.exports (C:\Program Files\nodejs\node_modules\npm\lib\cli\entry.js:74:5) {
113 verbose audit error   headers: [Object: null prototype] {
113 verbose audit error     server: [ 'Tengine' ],
113 verbose audit error     date: [ 'Mon, 14 Jul 2025 06:17:52 GMT' ],
113 verbose audit error     'content-type': [ 'application/json' ],
113 verbose audit error     'transfer-encoding': [ 'chunked' ],
113 verbose audit error     connection: [ 'keep-alive' ],
113 verbose audit error     'strict-transport-security': [ 'max-age=5184000' ],
113 verbose audit error     via: [ 'kunlun14.cn8001[,404666]' ],
113 verbose audit error     'timing-allow-origin': [ '*' ],
113 verbose audit error     eagleid: [ 'dcb5a62217524738722415925e' ],
113 verbose audit error     'x-fetch-attempts': [ '1' ]
113 verbose audit error   },
113 verbose audit error   statusCode: 404,
113 verbose audit error   code: 'E404',
113 verbose audit error   method: 'POST',
113 verbose audit error   uri: 'https://registry.npmmirror.com/-/npm/v1/security/audits/quick',
113 verbose audit error   body: {
113 verbose audit error     error: '[NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet'
113 verbose audit error   },
113 verbose audit error   pkgid: undefined
113 verbose audit error }
114 silly audit error [object Object]
115 silly audit report null
116 silly ADD
117 silly ADD
118 silly ADD
119 silly ADD
120 silly ADD
121 silly ADD
122 silly ADD
123 silly ADD
124 silly ADD
125 silly ADD
126 silly ADD
127 silly ADD
128 silly ADD
129 silly ADD
130 silly ADD
131 silly ADD
132 silly ADD
133 silly ADD
134 silly ADD
135 silly ADD
136 silly ADD
137 silly ADD
138 silly ADD
139 silly ADD
140 silly ADD
141 silly ADD
142 silly ADD
143 silly ADD
144 silly ADD
145 silly ADD
146 silly ADD
147 silly ADD
148 silly ADD
149 silly ADD
150 silly ADD
151 silly ADD
152 silly ADD
153 silly ADD
154 silly ADD
155 silly ADD
156 silly ADD
157 silly ADD
158 silly ADD
159 silly ADD
160 silly ADD
161 silly REMOVE node_modules/yaml
162 silly REMOVE node_modules/sortablejs
163 silly REMOVE node_modules/prettier
164 verbose cwd D:\车秘系统相关文档\projects\project_manage\client
165 verbose os Windows_NT 10.0.18363
166 verbose node v22.16.0
167 verbose npm  v10.9.2
168 verbose exit 0
169 info ok
