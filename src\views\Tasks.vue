<template>
  <div class="tasks">
    <div class="page-header">
      <h2>任务管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建任务
        </el-button>
        <el-button @click="refreshTasks">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索任务标题"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="任务状态" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="待办" value="todo" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="审核中" value="review" />
            <el-option label="已完成" value="done" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="priorityFilter" placeholder="优先级" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="projectFilter" placeholder="所属项目" @change="handleFilter">
            <el-option label="全部项目" value="" />
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="assigneeFilter" placeholder="负责人" @change="handleFilter">
            <el-option label="全部人员" value="" />
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 任务表格 -->
    <el-table
      :data="filteredTasks"
      style="width: 100%"
      @row-click="openTaskDetail"
    >
      <el-table-column prop="title" label="任务标题" min-width="200">
        <template #default="{ row }">
          <div class="task-title-cell">
            <span>{{ row.title }}</span>
            <el-tag
              v-if="isOverdue(row.dueDate)"
              type="danger"
              size="small"
              class="overdue-tag"
            >
              逾期
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="project.name" label="所属项目" width="150" />

      <el-table-column prop="assignee.name" label="负责人" width="120">
        <template #default="{ row }">
          <div v-if="row.assignee" class="assignee-cell">
            <el-avatar :size="24" :src="row.assignee.avatar">
              {{ row.assignee.name?.charAt(0) }}
            </el-avatar>
            <span>{{ row.assignee.name }}</span>
          </div>
          <span v-else class="text-placeholder">未分配</span>
        </template>
      </el-table-column>

      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="{ row }">
          <el-tag :type="getPriorityType(row.priority)" size="small">
            {{ getPriorityText(row.priority) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="dueDate" label="截止日期" width="120">
        <template #default="{ row }">
          <span :class="{ 'text-danger': isOverdue(row.dueDate) }">
            {{ formatDate(row.dueDate) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="创建时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click.stop="editTask(row)">
            编辑
          </el-button>
          <el-button size="small" type="danger" @click.stop="deleteTask(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
