{"_attachments": {}, "_id": "crc-32", "_rev": "3267-61f14bac830fd08f52a2f40d", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "dist-tags": {"latest": "1.2.2"}, "license": "Apache-2.0", "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "name": "crc-32", "readme": "# crc32\n\nStandard CRC-32 algorithm implementation in JS (for the browser and nodejs).\nEmphasis on correctness, performance, and IE6+ support.\n\n## Installation\n\nWith [npm](https://www.npmjs.org/package/crc-32):\n\n```bash\n$ npm install crc-32\n```\n\nWhen installed globally, npm installs a script `crc32` that computes the\nchecksum for a specified file or standard input.\n\n<details>\n  <summary><b>CDN Availability</b> (click to show)</summary>\n\n|    CDN     | URL                                        |\n|-----------:|:-------------------------------------------|\n|    `unpkg` | <https://unpkg.com/crc-32/>                |\n| `jsDelivr` | <https://jsdelivr.com/package/npm/crc-32>  |\n|    `CDNjs` | <https://cdnjs.com/libraries/crc-32>       |\n\n</details>\n\n\n## Integration\n\nUsing NodeJS or a bundler:\n\n```js\nvar CRC32 = require(\"crc-32\");\n```\n\nIn the browser, the `crc32.js` script can be loaded directly:\n\n```html\n<script src=\"crc32.js\"></script>\n```\n\nThe browser script exposes a variable `CRC32`.\n\nThe script will manipulate `module.exports` if available .  This is not always\ndesirable.  To prevent the behavior, define `DO_NOT_EXPORT_CRC`.\n\n### CRC32C (Castagnoli)\n\nThe module and CDNs also include a parallel script for CRC32C calculations.\n\nUsing NodeJS or a bundler:\n\n```js\nvar CRC32C = require(\"crc-32/crc32c\");\n```\n\nIn the browser, the `crc32c.js` script can be loaded directly:\n\n```html\n<script src=\"crc32c.js\"></script>\n```\n\nThe browser exposes a variable `CRC32C`.\n\nThe script will manipulate `module.exports` if available .  This is not always\ndesirable.  To prevent the behavior, define `DO_NOT_EXPORT_CRC`.\n\n## Usage\n\nIn all cases, the relevant function takes an argument representing data and an\noptional second argument representing the starting \"seed\" (for rolling CRC).\n\nThe return value is a signed 32-bit integer.\n\n- `CRC32.buf(byte array or buffer[, seed])` assumes the argument is a sequence\n  of 8-bit unsigned integers (nodejs `Buffer`, `Uint8Array` or array of bytes).\n\n- `CRC32.bstr(binary string[, seed])` assumes the argument is a binary string\n  where byte `i` is the low byte of the UCS-2 char: `str.charCodeAt(i) & 0xFF`\n\n- `CRC32.str(string[, seed])` assumes the argument is a standard JS string and\n  calculates the hash of the UTF-8 encoding.\n\nFor example:\n\n```js\n// var CRC32 = require('crc-32');               // uncomment this line if in node\nCRC32.str(\"SheetJS\")                            // -1647298270\nCRC32.bstr(\"SheetJS\")                           // -1647298270\nCRC32.buf([ 83, 104, 101, 101, 116, 74, 83 ])   // -1647298270\n\ncrc32 = CRC32.buf([83, 104])                    // -1826163454  \"Sh\"\ncrc32 = CRC32.str(\"eet\", crc32)                 //  1191034598  \"Sheet\"\nCRC32.bstr(\"JS\", crc32)                         // -1647298270  \"SheetJS\"\n\n[CRC32.str(\"\\u2603\"),  CRC32.str(\"\\u0003\")]     // [ -1743909036,  1259060791 ]\n[CRC32.bstr(\"\\u2603\"), CRC32.bstr(\"\\u0003\")]    // [  1259060791,  1259060791 ]\n[CRC32.buf([0x2603]),  CRC32.buf([0x0003])]     // [  1259060791,  1259060791 ]\n\n// var CRC32C = require('crc-32/crc32c');       // uncomment this line if in node\nCRC32C.str(\"SheetJS\")                           // -284764294\nCRC32C.bstr(\"SheetJS\")                          // -284764294\nCRC32C.buf([ 83, 104, 101, 101, 116, 74, 83 ])  // -284764294\n\ncrc32c = CRC32C.buf([83, 104])                  // -297065629   \"Sh\"\ncrc32c = CRC32C.str(\"eet\", crc32c)              //  1241364256  \"Sheet\"\nCRC32C.bstr(\"JS\", crc32c)                       // -284764294   \"SheetJS\"\n\n[CRC32C.str(\"\\u2603\"),  CRC32C.str(\"\\u0003\")]   // [  1253703093,  1093509285 ]\n[CRC32C.bstr(\"\\u2603\"), CRC32C.bstr(\"\\u0003\")]  // [  1093509285,  1093509285 ]\n[CRC32C.buf([0x2603]),  CRC32C.buf([0x0003])]   // [  1093509285,  1093509285 ]\n```\n\n### Best Practices\n\nEven though the initial seed is optional, for performance reasons it is highly\nrecommended to explicitly pass the default seed 0.\n\nIn NodeJS with the native Buffer implementation, it is oftentimes faster to\nconvert binary strings with `Buffer.from(bstr, \"binary\")` first:\n\n```js\n/* Frequently slower in NodeJS */\ncrc32 = CRC32.bstr(bstr, 0);\n/* Frequently faster in NodeJS */\ncrc32 = CRC32.buf(Buffer.from(bstr, \"binary\"), 0);\n```\n\nThis does not apply to browser `Buffer` shims, and thus is not implemented in\nthe library directly.\n\n## Testing\n\n`make test` will run the nodejs-based test.\n\nTo run the in-browser tests, run a local server and go to the `ctest` directory.\n`make ctestserv` will start a python `SimpleHTTPServer` server on port 8000.\n\nTo update the browser artifacts, run `make ctest`.\n\nTo generate the bits file, use the `crc32` function from python `zlib`:\n\n```python\n>>> from zlib import crc32\n>>> x=\"foo bar baz٪☃🍣\"\n>>> crc32(x)\n1531648243\n>>> crc32(x+x)\n-218791105\n>>> crc32(x+x+x)\n1834240887\n```\n\nThe included `crc32.njs` script can process files or standard input:\n\n```bash\n$ echo \"this is a test\" > t.txt\n$ bin/crc32.njs t.txt\n1912935186\n```\n\nFor comparison, the included `crc32.py` script uses python `zlib`:\n\n```bash\n$ bin/crc32.py t.txt\n1912935186\n```\n\nOn OSX the command `cksum` generates unsigned CRC-32 with Algorithm 3:\n\n```bash\n$ cksum -o 3 < IE8.Win7.For.Windows.VMware.zip\n1891069052 **********\n$ crc32 --unsigned ~/Downloads/IE8.Win7.For.Windows.VMware.zip\n1891069052\n```\n\n## Performance\n\n`make perf` will run algorithmic performance tests (which should justify certain\ndecisions in the code).\n\nThe [`adler-32` project](http://git.io/adler32) has more performance notes\n\n## License\n\nPlease consult the attached LICENSE file for details.  All rights not explicitly\ngranted by the Apache 2.0 license are reserved by the Original Author.\n\n## Badges\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/crc32.svg)](https://saucelabs.com/u/crc32)\n\n[![Build Status](https://travis-ci.org/SheetJS/js-crc32.svg?branch=master)](https://travis-ci.org/SheetJS/js-crc32)\n[![Coverage Status](http://img.shields.io/coveralls/SheetJS/js-crc32/master.svg)](https://coveralls.io/r/SheetJS/js-crc32?branch=master)\n[![Dependencies Status](https://david-dm.org/sheetjs/js-crc32/status.svg)](https://david-dm.org/sheetjs/js-crc32)\n[![NPM Downloads](https://img.shields.io/npm/dt/crc-32.svg)](https://npmjs.org/package/crc-32)\n[![ghit.me](https://ghit.me/badge.svg?repo=sheetjs/js-xlsx)](https://ghit.me/repo/sheetjs/js-xlsx)\n[![Analytics](https://ga-beacon.appspot.com/***********-1/SheetJS/js-crc32?pixel)](https://github.com/SheetJS/js-crc32)\n", "time": {"created": "2022-01-26T13:25:00.200Z", "modified": "2023-07-28T06:37:21.846Z", "1.2.0": "2018-01-18T00:55:23.204Z", "1.1.1": "2017-09-14T05:45:26.818Z", "1.1.0": "2017-07-28T07:48:54.210Z", "1.0.2": "2017-04-27T22:00:39.780Z", "1.0.1": "2016-10-12T20:04:36.085Z", "1.0.0": "2016-10-08T19:45:36.675Z", "0.4.1": "2016-06-16T22:26:38.608Z", "0.4.0": "2016-01-13T19:40:03.150Z", "0.3.0": "2015-05-08T05:34:37.436Z", "0.2.2": "2014-07-06T02:17:27.247Z", "0.2.1": "2014-06-21T02:45:23.088Z", "0.2.0": "2014-06-17T23:36:53.285Z", "0.1.0": "2014-06-16T21:29:47.454Z", "1.2.1": "2022-01-25T05:49:07.268Z", "1.2.2": "2022-04-04T23:05:52.784Z"}, "versions": {"1.2.0": {"name": "crc-32", "version": "1.2.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc", "crc32", "checksum"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "types": "types", "dependencies": {"printj": "~1.1.0", "exit-on-epipe": "~1.0.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "crc32.js"}}, "homepage": "http://sheetjs.com/opensource", "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md", "types/index.d.ts", "types/*.json"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "13f8e4f8f4f4eeb7fbdf0aadd6bb9d696c07190a", "_id": "crc-32@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "cb2db6e29b88508e32d9dd0ec1693e7b41a18208", "size": 5346, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.0.tgz", "integrity": "sha512-1uBwHxF+Y/4yF5G48fwnKq6QsIXheor3ZLPT80yGBV1oEUwpPojlEhQbWKVw1VwcTQyMGHK1/XMmTjmlsmTTGA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/crc-32-1.2.0.tgz_1516236922360_0.022918323520570993"}, "directories": {}, "publish_time": 1516236923204, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516236923204, "_cnpmcore_publish_time": "2021-12-16T10:35:56.153Z"}, "1.1.1": {"name": "crc-32", "version": "1.1.1", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "types": "types", "dependencies": {"printj": "~1.1.0", "exit-on-epipe": "~1.0.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "crc32.js"}}, "homepage": "http://sheetjs.com/opensource", "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md", "types/index.d.ts", "types/*.json"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "2253617eaedb9c4e774faecd3e38cae801311430", "_id": "crc-32@1.1.1", "_shasum": "5d739d5e4c6e352ad8304d73223d483fe55adb8d", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "5d739d5e4c6e352ad8304d73223d483fe55adb8d", "size": 5323, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.1.1.tgz", "integrity": "sha512-DWXuRN3Wtu43YRfYZ9r17720WZqM0caEjIfT6Dk1J/3sAxIyyXbUWqIACbz3cjV8l7guJRW+9pZlYMluKJ69wg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/crc-32-1.1.1.tgz_1505367925823_0.6244659626390785"}, "directories": {}, "publish_time": 1505367926818, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505367926818, "_cnpmcore_publish_time": "2021-12-16T10:35:56.326Z"}, "1.1.0": {"name": "crc-32", "version": "1.1.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "dependencies": {"printj": "~1.1.0", "exit-on-epipe": "~1.0.1"}, "devDependencies": {"mocha": "~2.5.3", "codepage": "~1.10.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/js-crc32#readme", "_id": "crc-32@1.1.0", "_shasum": "301ef4a1c2217adc8463b6e6e96c4e44310e10c7", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "301ef4a1c2217adc8463b6e6e96c4e44310e10c7", "size": 4667, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.1.0.tgz", "integrity": "sha512-iVJCmBLXWEYWAdZotfSPWuYaxjeKNW6ugEOGyjAkX1RycbFvBrNmFoqvbVTtF1IgsbsaMXI0XRJvfO0ZS7HQwg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/crc-32-1.1.0.tgz_1501228133311_0.17897338210605085"}, "directories": {}, "publish_time": 1501228134210, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501228134210, "_cnpmcore_publish_time": "2021-12-16T10:35:56.523Z"}, "1.0.2": {"name": "crc-32", "version": "1.0.2", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "dependencies": {"printj": "", "exit-on-epipe": ""}, "devDependencies": {"mocha": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "63ef494ee799a59432f2df3a62304c5e4961a893", "homepage": "https://github.com/SheetJS/js-crc32#readme", "_id": "crc-32@1.0.2", "_shasum": "09507984ee9bcce3bd1b8861f0de8ab10ae8187d", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "09507984ee9bcce3bd1b8861f0de8ab10ae8187d", "size": 4598, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.0.2.tgz", "integrity": "sha512-VAF9DaYBjIN1FTholAkIhW75P2c5Sy9AXva/WQ0zo2lVznpCbwAAB5DY+do7mSvXP4br2DPHET9uXST1C6F8IQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/crc-32-1.0.2.tgz_1493330437912_0.2430312354117632"}, "directories": {}, "publish_time": 1493330439780, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493330439780, "_cnpmcore_publish_time": "2021-12-16T10:35:56.725Z"}, "1.0.1": {"name": "crc-32", "version": "1.0.1", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "dependencies": {"concat-stream": "", "printj": "", "exit-on-epipe": ""}, "devDependencies": {"mocha": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5a7e4db9732178f9829afe6edabc128324e062e0", "_id": "crc-32@1.0.1", "_shasum": "2efbddb7ccbb7beb4d181803b10e33a29c9fd214", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "2efbddb7ccbb7beb4d181803b10e33a29c9fd214", "size": 4465, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.0.1.tgz", "integrity": "sha512-HPIHoNgcs9DpyZnv7bwOhwQx+nJ0YfvmxMFAZIaOzYdni85dvUT7aA2CbcewapBGKUfk1e4TWc6XbMEYNekmGg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/crc-32-1.0.1.tgz_1476302674503_0.7585965490434319"}, "directories": {}, "publish_time": 1476302676085, "_hasShrinkwrap": false, "_cnpm_publish_time": 1476302676085, "_cnpmcore_publish_time": "2021-12-16T10:35:56.997Z"}, "1.0.0": {"name": "crc-32", "version": "1.0.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "dependencies": {"concat-stream": "", "printj": "", "exit-on-epipe": ""}, "devDependencies": {"mocha": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "1045f4f8e85fa592358df2ee0300e77e47f4b56d", "_id": "crc-32@1.0.0", "_shasum": "636a4c5ff470f24230d850b28dac13a4abbec99e", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "636a4c5ff470f24230d850b28dac13a4abbec99e", "size": 4463, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.0.0.tgz", "integrity": "sha512-V+/Z/eSjVU3beiseX4KXpBLdKZLNy2S++0USgZZ7KPQ0UKneCDnkZ6Bf4izA0YsVudb2oaeROWsR7ZwzsPS2eg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/crc-32-1.0.0.tgz_1475955936090_0.5158190473448485"}, "directories": {}, "publish_time": 1475955936675, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475955936675, "_cnpmcore_publish_time": "2021-12-16T10:35:57.213Z"}, "0.4.1": {"name": "crc-32", "version": "0.4.1", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "bin": {"crc32": "./bin/crc32.njs"}, "main": "./crc32", "dependencies": {"concat-stream": "", "exit-on-epipe": ""}, "devDependencies": {"mocha": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "files": ["crc32.js", "bin/crc32.njs", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "e1c9c5e5cd21d7836bc524881a4aa41df3d79877", "_id": "crc-32@0.4.1", "_shasum": "cd43fa1e5625381e35e85ca2064a52c03b5706ed", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "cd43fa1e5625381e35e85ca2064a52c03b5706ed", "size": 2187, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.4.1.tgz", "integrity": "sha512-iOT9nhY84S9Ae7ceinH/g4N2ZwlhGkgenM16z63JZOZESiGussu7t2QqcHrfy0WGr2fwgf8lO3uZ0G3qDJXH8w=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/crc-32-0.4.1.tgz_1466115996261_0.9728287416510284"}, "directories": {}, "publish_time": 1466115998608, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466115998608, "_cnpmcore_publish_time": "2021-12-16T10:35:57.412Z"}, "0.4.0": {"name": "crc-32", "version": "0.4.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "main": "./crc32", "devDependencies": {"codepage": "", "mocha": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "79a265b6622ec3547a371928e5f1c4714f48e91f", "homepage": "https://github.com/SheetJS/js-crc32#readme", "_id": "crc-32@0.4.0", "_shasum": "9990fd4006768817ffd0de3cc762e63d854e829c", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.0.0", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "dist": {"shasum": "9990fd4006768817ffd0de3cc762e63d854e829c", "size": 3042, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.4.0.tgz", "integrity": "sha512-dMYPumfqKbXPKt8ycPGz/sawfviiHkDRQ6ORiHEf51qdd4PRt5Pvm8FqECYsjpGEN++1rL40UVDr9JqbpnYaJg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452714003150, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452714003150, "_cnpmcore_publish_time": "2021-12-16T10:35:57.589Z"}, "0.3.0": {"name": "crc-32", "version": "0.3.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "main": "./crc32", "devDependencies": {"mocha": "", "uglify-js": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "b1d8309717f3cb8ced8e1b2830fbd44dd149b7e8", "homepage": "https://github.com/SheetJS/js-crc32", "_id": "crc-32@0.3.0", "_shasum": "6a3d3687f5baec41f7e9b99fe1953a2e5d19775e", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "6a3d3687f5baec41f7e9b99fe1953a2e5d19775e", "size": 3238, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.3.0.tgz", "integrity": "sha512-kucVIjOmMc1f0tv53BJ/5WIX+MGLcKuoBhnGqQrgKJNqLByb/sVMWfW/Aw6hw0jgcqjJ2pi9E5y32zOIpaUlsA=="}, "directories": {}, "publish_time": 1431063277436, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431063277436, "_cnpmcore_publish_time": "2021-12-16T10:35:57.826Z"}, "0.2.2": {"name": "crc-32", "version": "0.2.2", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "main": "./crc32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "5cc2f712b112b8ba030f89467150cb7834d8eabd", "homepage": "https://github.com/SheetJS/js-crc32", "_id": "crc-32@0.2.2", "_shasum": "f440568a0c6a45f0eebbb57c33b156015f4f04b6", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "f440568a0c6a45f0eebbb57c33b156015f4f04b6", "size": 2723, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.2.2.tgz", "integrity": "sha512-gf4WfQoFmAETv30Pz0lnlKUXT0yv1RBHCvF7FTdOTeYkYk8AUr0WLk8Bh4ZB/LA+iMmreVgtuD3wH9uY9Yk4QA=="}, "directories": {}, "publish_time": 1404613047247, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404613047247, "_cnpmcore_publish_time": "2021-12-16T10:35:58.028Z"}, "0.2.1": {"name": "crc-32", "version": "0.2.1", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "main": "./crc32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "homepage": "https://github.com/SheetJS/js-crc32", "_id": "crc-32@0.2.1", "_shasum": "a5b1d883a1317b8d0f4edf66e8149714614f4236", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "a5b1d883a1317b8d0f4edf66e8149714614f4236", "size": 44117, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.2.1.tgz", "integrity": "sha512-tP3Vk6YEz40Olr85+0pCwzY5NtXztQAkx1b4CNm2nFR7CLKVJckVSx28d4hSt0vhPjuqjKw4cLf+o9xXXbkctA=="}, "directories": {}, "publish_time": 1403318723088, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403318723088, "_cnpmcore_publish_time": "2021-12-16T10:35:58.294Z"}, "0.2.0": {"name": "crc-32", "version": "0.2.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "main": "./crc32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "c79a8fb6c93e51a0026aafdfacbe0c88e82ba9f5", "homepage": "https://github.com/SheetJS/js-crc32", "_id": "crc-32@0.2.0", "_shasum": "8b57570d06cbd730d857bf3620161ce7eab4e646", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "8b57570d06cbd730d857bf3620161ce7eab4e646", "size": 43404, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.2.0.tgz", "integrity": "sha512-Asx/dL8tAdjmoz+KphcgbwQZIMs+Rs/86M6uRhl6mS3EWSNNYWw+kEX0C474eZheyOeAZLq3UUQcNd0d4Gx1Gw=="}, "directories": {}, "publish_time": 1403048213285, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403048213285, "_cnpmcore_publish_time": "2021-12-16T10:35:58.578Z"}, "0.1.0": {"name": "crc-32", "version": "0.1.0", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc32", "checksum", "crc"], "main": "./crc32", "devDependencies": {"mocha": "", "xlsjs": "", "uglify-js": "", "codepage": ""}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test"}, "config": {"blanket": {"pattern": "crc32.js"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "35003c5bdaf0024537a778dcfcce6da143250e90", "homepage": "https://github.com/SheetJS/js-crc32", "_id": "crc-32@0.1.0", "_shasum": "c6bf75bb5df548606be4d899315314bd4d1dba2d", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "dist": {"shasum": "c6bf75bb5df548606be4d899315314bd4d1dba2d", "size": 40297, "noattachment": false, "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-0.1.0.tgz", "integrity": "sha512-DMyk3QnWS8GtMuL/fbMFBC/kiCnT0g//EovbtpPR9Q5Jnma6SpR/z3Sahk3rlISXkE+HeAThrv1RapwumgJrMg=="}, "directories": {}, "publish_time": 1402954187454, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402954187454, "_cnpmcore_publish_time": "2021-12-16T10:35:58.802Z"}, "1.2.1": {"name": "crc-32", "version": "1.2.1", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc", "crc32", "checksum"], "bin": {"crc32": "bin/crc32.njs"}, "main": "crc32.js", "types": "types/index.d.ts", "typesVersions": {"*": {"*": ["types/index.d.ts"]}}, "dependencies": {"printj": "~1.3.1", "exit-on-epipe": "~1.0.1"}, "devDependencies": {"mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "crc32.js"}}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "d64513c9525ff50dda0eeb538ed0535a61ae7243", "_id": "crc-32@1.2.1", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-Dn/xm/1vFFgs3nfrpEVScHoIslO9NZRITWGz/1E/St6u4xw99vfZzVkW0OSnzx2h9egej9xwMCEut6sqwokM/w==", "shasum": "436d2bcaad27bcb6bd073a2587139d3024a16460", "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.1.tgz", "fileCount": 9, "unpackedSize": 30417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh749TCRA9TVsSAnZWagAA5QUQAKND9a/xh3X5j4tsN/Hm\nePhunZj3lngezCJW5PZxKZSi/Snf14dulrX9TF1jqvz8JQEaVeMMntuttsIW\neioBCFLL3Dm71bswMNuek9WVWZNVNv/r4lSys+HbzUDfODAIPUGSzOLGMUzl\nSNt8yK0A/qpv1NEB3OhH8aWHvp1aWKp5BA4Hy+DVAvwGz4qxT59T61y66lKK\nhS287fhHuMiR4rFlx02DOkiFjJC34qz6/a6TBYpGmGM+3k0qH3yjR9j7d+08\n3Knnb3O5ZiUkkjneOH1NIxAO99lzjHq+OsGtyfg9StfP+ac94J+ukPh4nPpb\nkKeEjJTVwna9JE1sEBr/YpDhVmOK6jqYKISbTPWkR+e13tqxeL3GB7cHF3VL\no0fQoI3ca4PbwvcpydfICQzx769ucK+jgo1cPfud1QNLEGaJBRaza8ymyh++\n7zs22GTnd3VvDpwNG7Gdr87yyNG2dcPUnXniFyfMv84ck6WcYobDL2zOeG4Y\nvEOHLu7IR9M/TafRkAuouaV7+28Ful2WvChwOOTkeJkk2ovE9q8jBAgcti9Y\nxQ0hhoUMAosZfvmUU/xmHKHfJO1f4MpmtP7QptAAiH0YFdXfKSHoa+cepdoM\nSeYsFgEN+qXTsDjOp/rsv7PL2CFPfdDBijeUq9hErX8NSxn/yL92IxlCfwfM\nbrrh\r\n=WvYP\r\n-----END PGP SIGNATURE-----\r\n", "size": 9921}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/crc-32_1.2.1_1643089747113_0.5468606355532541"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T05:49:57.812Z"}, "1.2.2": {"name": "crc-32", "version": "1.2.2", "author": {"name": "sheetjs"}, "description": "Pure-JS CRC-32", "keywords": ["crc", "crc32", "checksum"], "bin": {"crc32": "bin/crc32.njs"}, "main": "crc32.js", "types": "types/index.d.ts", "typesVersions": {"*": {"*": ["types/index.d.ts"]}}, "dependencies": {}, "devDependencies": {"printj": "~1.3.1", "exit-on-epipe": "~1.0.1", "mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "crc32.js"}}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "6a752a8568e37e824e85da3abf45ff883ae2f651", "_id": "crc-32@1.2.2", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==", "shasum": "3cad35a934b8bf71f25ca524b6da51fb7eace2ff", "tarball": "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.2.tgz", "fileCount": 9, "unpackedSize": 31044, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGgCP6ojqQzwXRxfSvACf+Pv2zDixA+JGBjbrpSLI+A1AiAPWTzSbS6j4MeIrEqVZ04jnTdaN8FsgR9Q/WgGclcDzA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiS3nQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKzA//fQpf4x4N/uJSk3QRBAPMechq1IdBrOJv62eU2FSgGi0hf5ti\r\n0lVmnOqlUhN72NnOCXZOFh3D8Py/dIOGyLOO5112mopNLp/dybX82ezjl5RK\r\nE8TeOKr1bwqVHEK4LJlsnY2bTLBjNQvdaKmOWY2TdVkJ1wdNSwLkEXAd2sQO\r\nCvET285NOEggd6f00qNSU/liZYC91uq/ZoiBIItk3W1YM6whMVwjCFHtvLVP\r\nb/0tq1/oFd9T6NzWv5aLlZxSqj1ZTUr/lSXNSlVsuKJQugtRB1v/wiztVZ37\r\nXYBl0axeqXOZr1txZnL38GBpd11a/1CkUAq/HqDjym8e81zV6PDttqAwl69d\r\nHtM+RGH3yEwibKyBbfkgye0Aj418wfR+nW02VF8bM5oa1DMuO2HJH3JVVUOS\r\nCxYlr6PoFdNsU8QJrXYxkQOrYRl1J0mZCwR8izO+QC6Z4DNxmrEoT9JaUEG9\r\nzKwcmJIorfIirxhF8VUBSxIv/VWsixlwgLWSaTjLWQuoDs/MWlG/bu1e3iKz\r\nns/7Crq0RamoxUo0zWDRKogok2+gXy2BCmm47RwT5tfbcrEC+UkStEhLIjEr\r\nFSkSIcXk98HyJiqVgA49VOdD77OLptNUH+np+6WL6qZrQdGW5e9Hkwa6zZ4Q\r\nxc9YfVfYuUdS34IWprc3XFyKuXSp7k+cD+M=\r\n=IOJo\r\n-----END PGP SIGNATURE-----\r\n", "size": 10107}, "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/crc-32_1.2.2_1649113552639_0.6220005205275192"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-05T01:16:00.686Z"}}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "homepage": "https://sheetjs.com/", "keywords": ["crc", "crc32", "checksum"], "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "_source_registry_name": "default"}