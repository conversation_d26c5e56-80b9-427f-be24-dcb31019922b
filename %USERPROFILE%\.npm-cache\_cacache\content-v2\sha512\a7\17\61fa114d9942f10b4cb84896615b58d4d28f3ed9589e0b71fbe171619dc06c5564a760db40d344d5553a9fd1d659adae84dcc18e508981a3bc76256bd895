{"_id": "@volar/typescript", "_rev": "2247841-633fc742e0ea69609e0a839a", "dist-tags": {"latest": "2.4.18", "next": "2.4.0-alpha.20"}, "name": "@volar/typescript", "time": {"created": "2022-10-07T06:29:22.286Z", "modified": "2025-07-09T09:44:56.479Z", "1.0.0-rc.4": "2022-10-07T06:29:13.559Z", "1.0.0-rc.5": "2022-10-07T07:38:41.619Z", "1.0.0": "2022-10-07T13:04:49.710Z", "1.0.1": "2022-10-08T17:59:51.531Z", "1.0.2": "2022-10-09T13:43:55.070Z", "1.0.3": "2022-10-09T19:13:17.735Z", "1.0.4": "2022-10-11T20:48:50.185Z", "1.0.5": "2022-10-11T22:31:47.576Z", "1.0.6": "2022-10-12T08:40:14.143Z", "1.0.7": "2022-10-13T01:23:45.903Z", "1.0.8": "2022-10-15T03:31:30.827Z", "1.0.9": "2022-10-23T11:35:39.750Z", "1.0.10": "2022-11-29T13:38:38.892Z", "1.0.11": "2022-12-03T05:27:28.567Z", "1.0.12": "2022-12-08T22:32:59.818Z", "1.0.13": "2022-12-12T02:31:48.618Z", "1.0.14": "2022-12-18T07:29:38.297Z", "1.0.16": "2022-12-20T08:28:51.538Z", "1.0.17": "2022-12-25T18:39:21.976Z", "1.0.18": "2022-12-26T09:56:42.480Z", "1.0.19": "2022-12-30T23:54:33.658Z", "1.0.20": "2023-01-03T23:35:27.637Z", "1.0.21": "2023-01-04T22:54:22.875Z", "1.0.22": "2023-01-05T01:51:31.426Z", "1.0.24": "2023-01-08T13:30:43.874Z", "1.1.0-alpha.0": "2023-01-17T04:20:35.119Z", "1.1.0-alpha.1": "2023-01-17T07:10:45.253Z", "1.1.0-alpha.2": "2023-01-28T17:12:26.263Z", "1.1.0-1.2.0-alpha.1.0": "2023-01-31T07:25:18.094Z", "1.2.0-alpha.1": "2023-01-31T07:26:52.839Z", "1.2.0-alpha.2": "2023-01-31T08:12:28.942Z", "1.2.0-alpha.3": "2023-01-31T13:20:51.812Z", "1.2.0-alpha.4": "2023-01-31T14:09:31.094Z", "1.2.0-alpha.5": "2023-02-03T08:51:39.264Z", "1.2.0-alpha.6": "2023-02-03T19:27:07.645Z", "1.2.0-alpha.7": "2023-02-03T19:37:29.169Z", "1.2.0-alpha.8": "2023-02-03T20:57:49.901Z", "1.2.0-alpha.9": "2023-02-06T01:51:58.819Z", "1.2.0-alpha.10": "2023-02-15T06:04:24.237Z", "1.2.0-alpha.11": "2023-02-15T16:34:53.418Z", "1.2.0-alpha.12": "2023-02-16T15:39:01.876Z", "1.2.0-alpha.13": "2023-02-16T16:11:23.976Z", "1.2.0-alpha.14": "2023-02-17T21:47:24.859Z", "1.2.0-alpha.15": "2023-02-19T22:48:02.987Z", "1.2.0-alpha.16": "2023-02-19T23:11:48.557Z", "1.2.0-alpha.17": "2023-02-20T18:58:05.391Z", "1.2.0-alpha.18": "2023-02-21T23:31:46.348Z", "1.2.0-alpha.19": "2023-02-22T03:11:29.935Z", "1.3.0-alpha.0": "2023-02-24T23:45:30.110Z", "1.3.0-alpha.1": "2023-03-02T00:34:58.760Z", "1.3.0-alpha.2": "2023-03-02T16:03:39.929Z", "1.3.0-alpha.3": "2023-03-03T18:16:29.732Z", "1.4.0-alpha.0": "2023-03-05T09:13:35.865Z", "1.4.0-alpha.1": "2023-03-05T23:44:22.533Z", "1.4.0-alpha.2": "2023-03-10T10:05:18.133Z", "1.4.0-alpha.3": "2023-03-13T18:06:05.509Z", "1.4.0-alpha.4": "2023-03-20T02:02:18.763Z", "1.4.0-alpha.5": "2023-03-26T08:44:21.731Z", "1.4.0-alpha.6": "2023-04-01T05:31:02.849Z", "1.4.0-alpha.7": "2023-04-02T14:38:13.518Z", "1.4.0-alpha.8": "2023-04-15T18:47:42.987Z", "1.4.0-alpha.9": "2023-04-16T01:07:41.863Z", "1.4.0-alpha.10": "2023-04-16T07:43:48.480Z", "1.4.0-alpha.11": "2023-04-17T12:55:46.663Z", "1.4.0-alpha.12": "2023-04-18T07:11:48.885Z", "1.4.0": "2023-04-20T15:23:03.641Z", "1.4.1": "2023-04-22T15:25:33.585Z", "1.5.0-alpha.0": "2023-04-26T12:08:20.980Z", "1.5.0": "2023-05-01T13:51:35.536Z", "1.5.1": "2023-05-04T23:00:23.558Z", "1.5.2": "2023-05-05T01:07:37.177Z", "1.5.3": "2023-05-06T01:25:02.514Z", "1.5.4": "2023-05-06T19:18:40.811Z", "1.6.0": "2023-05-09T15:40:51.887Z", "1.6.1": "2023-05-10T01:26:08.222Z", "1.6.2": "2023-05-11T08:02:34.003Z", "1.4.1-patch.1": "2023-05-13T05:37:27.600Z", "1.4.1-patch.2": "2023-05-13T05:55:53.690Z", "1.6.3": "2023-05-13T07:33:14.468Z", "1.6.4": "2023-05-16T10:55:04.355Z", "1.6.5": "2023-05-18T07:15:03.197Z", "1.6.6": "2023-05-18T16:40:16.459Z", "1.6.7": "2023-05-18T17:17:48.343Z", "1.6.8": "2023-05-20T07:34:12.161Z", "1.6.9": "2023-05-22T00:09:25.385Z", "1.7.0": "2023-06-02T15:52:55.568Z", "1.7.1": "2023-06-04T17:07:12.359Z", "1.7.1-patch.1": "2023-06-05T23:21:04.330Z", "1.7.2": "2023-06-07T13:42:59.284Z", "1.7.3": "2023-06-08T10:49:12.261Z", "1.7.4": "2023-06-08T18:28:57.210Z", "1.7.5": "2023-06-14T13:41:41.943Z", "1.7.6": "2023-06-16T14:35:58.393Z", "1.7.7": "2023-06-18T01:14:31.854Z", "1.7.8": "2023-06-20T08:29:44.201Z", "1.7.9": "2023-06-26T16:17:42.102Z", "1.7.10": "2023-06-28T09:05:32.165Z", "1.8.0": "2023-07-03T18:51:05.641Z", "1.8.1": "2023-07-11T17:35:04.886Z", "1.8.2": "2023-07-13T00:41:51.205Z", "1.8.3": "2023-07-13T00:54:32.718Z", "1.9.0": "2023-07-14T14:44:54.117Z", "1.9.1": "2023-07-21T17:51:36.452Z", "1.9.2": "2023-07-24T15:43:56.865Z", "1.10.0": "2023-07-26T18:04:31.557Z", "1.10.1": "2023-08-17T16:54:13.655Z", "1.10.2": "2023-10-06T13:49:42.766Z", "1.10.3": "2023-10-06T14:08:52.931Z", "1.10.4": "2023-10-11T08:45:17.683Z", "1.10.5": "2023-10-25T10:30:12.979Z", "1.10.6": "2023-10-27T08:24:14.197Z", "1.10.7": "2023-10-27T17:51:47.994Z", "1.10.8": "2023-10-30T20:33:53.999Z", "1.10.9": "2023-10-30T20:47:34.818Z", "1.10.10-alpha.0": "2023-11-01T06:02:39.001Z", "1.10.10-alpha.1": "2023-11-01T06:37:17.468Z", "1.10.10": "2023-11-01T06:45:22.699Z", "1.11.0": "2023-11-03T21:41:00.673Z", "1.11.1": "2023-11-04T17:51:38.289Z", "2.0.0-alpha.0": "2023-12-05T18:03:06.988Z", "2.0.0-alpha.1": "2023-12-07T17:22:21.440Z", "2.0.0-alpha.2": "2023-12-07T17:27:09.759Z", "2.0.0-alpha.3": "2023-12-09T21:53:05.736Z", "2.0.0-alpha.4": "2023-12-12T22:03:36.932Z", "2.0.0-alpha.5": "2023-12-14T10:50:36.407Z", "2.0.0-alpha.6": "2023-12-14T20:14:38.589Z", "2.0.0-alpha.7": "2023-12-15T09:01:52.596Z", "2.0.0-alpha.8": "2023-12-19T18:37:40.840Z", "2.0.0-alpha.9": "2023-12-20T05:20:48.004Z", "2.0.0-alpha.10": "2023-12-20T06:53:54.340Z", "2.0.0-alpha.11": "2023-12-21T20:45:14.672Z", "2.0.0-alpha.12": "2023-12-21T21:10:54.426Z", "2.0.0-alpha.13": "2023-12-22T12:35:58.326Z", "2.0.0-alpha.14": "2024-01-17T06:32:31.791Z", "2.0.0": "2024-01-21T05:51:27.998Z", "2.0.1": "2024-02-04T19:45:44.101Z", "2.0.1-patch.1": "2024-02-04T22:45:18.057Z", "2.0.2": "2024-02-07T20:22:36.327Z", "2.0.3": "2024-02-09T18:50:43.658Z", "2.0.4": "2024-02-13T00:55:13.514Z", "2.1.0": "2024-02-26T04:41:53.689Z", "2.1.1": "2024-03-05T14:01:14.016Z", "2.1.2": "2024-03-07T04:58:31.874Z", "2.1.3": "2024-03-20T06:00:26.234Z", "2.1.4": "2024-03-21T23:54:25.276Z", "2.1.5": "2024-03-22T20:51:52.147Z", "2.2.0-alpha.0": "2024-03-24T00:37:07.065Z", "2.1.5-patch.1": "2024-03-26T11:38:25.242Z", "2.1.5-patch.2": "2024-03-26T15:30:59.728Z", "2.1.6": "2024-03-28T02:00:30.468Z", "2.2.0-alpha.1": "2024-03-28T02:24:13.752Z", "2.2.0-alpha.2": "2024-03-31T04:40:52.317Z", "2.2.0-alpha.3": "2024-04-01T03:42:40.661Z", "2.2.0-alpha.4": "2024-04-02T10:12:17.083Z", "2.2.0-alpha.5": "2024-04-04T12:06:39.355Z", "2.2.0-alpha.6": "2024-04-07T13:16:24.024Z", "2.2.0-alpha.7": "2024-04-10T06:05:15.555Z", "2.2.0-alpha.8": "2024-04-12T07:36:32.181Z", "2.2.0-alpha.9": "2024-04-14T17:46:32.393Z", "2.2.0-alpha.10": "2024-04-22T00:23:30.337Z", "2.2.0-alpha.11": "2024-04-30T01:14:17.346Z", "2.2.0-alpha.12": "2024-04-30T01:43:00.647Z", "2.2.0": "2024-05-01T03:16:50.932Z", "2.2.1": "2024-05-06T10:58:58.874Z", "2.2.2": "2024-05-10T08:18:14.443Z", "2.2.3": "2024-05-15T10:55:26.108Z", "2.2.4": "2024-05-15T11:52:23.877Z", "2.2.5": "2024-05-23T12:43:33.556Z", "2.3.0-alpha.0": "2024-05-28T22:57:06.136Z", "2.3.0-alpha.1": "2024-05-30T20:42:21.401Z", "2.3.0-alpha.2": "2024-05-30T22:48:43.064Z", "2.3.0-alpha.3": "2024-05-31T16:34:02.892Z", "2.3.0-alpha.4": "2024-05-31T20:33:24.294Z", "2.3.0-alpha.5": "2024-06-02T00:49:28.004Z", "2.3.0-alpha.6": "2024-06-05T02:18:45.463Z", "2.3.0-alpha.7": "2024-06-05T02:38:04.015Z", "2.3.0-alpha.8": "2024-06-06T19:37:19.187Z", "2.3.0-alpha.9": "2024-06-06T20:42:15.758Z", "2.3.0-alpha.10": "2024-06-07T04:49:17.928Z", "2.3.0-alpha.11": "2024-06-07T05:42:52.081Z", "2.3.0-alpha.12": "2024-06-07T05:54:18.719Z", "2.3.0-alpha.13": "2024-06-07T16:20:55.904Z", "2.3.0-alpha.14": "2024-06-08T05:03:36.333Z", "2.3.0-alpha.15": "2024-06-08T14:41:57.082Z", "2.3.0": "2024-06-09T00:09:29.103Z", "2.3.1": "2024-06-22T02:00:40.817Z", "2.3.2": "2024-06-23T19:22:11.549Z", "2.3.3": "2024-06-24T08:25:28.945Z", "2.3.4": "2024-06-24T19:42:07.855Z", "2.3.5-alpha.0": "2024-06-24T20:34:48.002Z", "2.3.5-alpha.1": "2024-06-26T04:06:38.653Z", "2.3.5-alpha.2": "2024-06-26T06:35:10.358Z", "2.4.0-alpha.0": "2024-06-26T13:10:58.888Z", "2.4.0-alpha.1": "2024-06-29T04:14:58.118Z", "2.4.0-alpha.2": "2024-06-29T04:27:06.178Z", "2.4.0-alpha.3": "2024-06-30T20:03:39.976Z", "2.4.0-alpha.4": "2024-06-30T22:12:48.195Z", "2.4.0-alpha.5": "2024-06-30T23:23:44.384Z", "2.4.0-alpha.6": "2024-07-01T14:08:07.045Z", "2.4.0-alpha.7": "2024-07-01T15:48:50.109Z", "2.4.0-alpha.8": "2024-07-01T22:18:59.883Z", "2.4.0-alpha.9": "2024-07-01T23:39:48.169Z", "2.4.0-alpha.10": "2024-07-02T00:34:20.310Z", "2.4.0-alpha.11": "2024-07-02T01:11:08.039Z", "2.4.0-alpha.12": "2024-07-02T04:54:58.908Z", "2.4.0-alpha.13": "2024-07-03T17:37:54.240Z", "2.4.0-alpha.14": "2024-07-04T06:11:01.142Z", "2.4.0-alpha.15": "2024-07-04T10:15:42.460Z", "2.4.0-alpha.16": "2024-07-15T12:15:56.124Z", "2.4.0-alpha.17": "2024-07-19T18:05:55.855Z", "2.4.0-alpha.18": "2024-07-22T03:51:51.543Z", "2.4.0-alpha.19": "2024-08-18T10:34:12.607Z", "2.4.0": "2024-08-18T16:56:48.148Z", "2.4.0-alpha.20": "2024-08-19T02:50:17.023Z", "2.4.1": "2024-08-28T23:28:08.092Z", "2.4.2": "2024-09-04T13:46:06.598Z", "2.4.3": "2024-09-07T20:04:17.952Z", "2.4.4": "2024-09-07T20:33:45.162Z", "2.4.5": "2024-09-14T08:02:54.794Z", "2.4.6": "2024-10-07T05:33:15.780Z", "2.4.7": "2024-10-25T10:26:34.113Z", "2.4.8": "2024-10-26T11:38:26.534Z", "2.4.9": "2024-11-06T21:24:55.940Z", "2.4.10": "2024-11-08T06:16:08.884Z", "2.4.11": "2024-12-14T00:02:55.827Z", "2.4.12": "2025-03-06T19:33:28.739Z", "2.4.13": "2025-04-25T16:20:24.046Z", "2.4.14": "2025-05-16T09:49:37.150Z", "2.4.15": "2025-06-24T09:08:07.877Z", "2.4.16": "2025-07-01T10:16:43.492Z", "2.4.17": "2025-07-02T06:57:18.036Z", "2.4.18": "2025-07-09T09:44:33.414Z"}, "versions": {"1.0.0-rc.4": {"name": "@volar/typescript", "version": "1.0.0-rc.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.0-rc.4", "@volar/typescript-faster": "1.0.0-rc.4"}, "types": "./out/index.d.ts", "gitHead": "7cb162cf2a52c482bd5bd2bb170254d45c11ed98", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.0-rc.4", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-G0AJcMoI65SCFQ7R8SGPCJUdoiNAqNaxvbTSP3lSJzOJw1bhYJLVntFbe4ku4xAx41Z9gQVnhcA2iZjJprPDOQ==", "shasum": "36d25a7b2da53b56ffa6dd870b1711403bb08b04", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.0-rc.4.tgz", "fileCount": 6, "unpackedSize": 19927, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDttb4DNQqH5VTtlMQMd08+puaQNuuBvSrgCQiR/2xu0wIgSh5NPT27ZyAND7tUhTAfT1cYL9u1zgzlIz/0r8Cjgts="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP8c5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQWhAAgYzAZpsbFYXCv+w5B+3N4cq0w1MsQtRECMdWP8XMKTJtYyDb\r\nmRplhTC6NIHY2D5VBY7J4tPK0+Lh65Iu9gBDocRLtyQ1TEqI+pSw2cj2fj9J\r\n15l5THnpsOCcH31yXQMXGMYgjGEjpqTTifZCL91k961Tbfd1UKr+FCnhDFJe\r\ncs69ICoHkOK/TalKiOqNqtIU6Ho1Olv6o9y3w/pdko/7Yc8c4AVdj4P/ORNm\r\nBji/lMtD0qOB3chvVWvvuFqVB+FXFKcRmE/D9hauIvyj4nvSFdZQK57yoMzU\r\nMKI46XzYGZHWdHl9I0gdPSH0II6Wb9RZ+rZsNLLfLS9ARdcs+dUd7s0sY+Xa\r\nBbXresrkUqPgJNojuBewmFgQ3NEcg+qj7NCOqNlpGYUBvpxYTiEXPghzMYW8\r\nOI3RwZSVekpq6Xa301gHY2PdvzTqYelwLYVnsFRoJH6xhoW0l0fVamSZxWrg\r\ngV0Ck998in1Yp9BRmGd1NMydZp6B3TQPqHBvmozXAz2q4HrLkCoRQAC92JoW\r\nVcBISvqOJEH4HLssZmKJLQMLzXw9pDp9kOTQcuMr17wbYO1zhc0pq510Z3q8\r\nsc54Q8/hhgQApW0HQcGD5Lh2b+if2bDUq1e3whq9ffKymp2AhK1i9R1Zy6W1\r\nk1vjd8gS8qL620BK+x6EQhbK0fWn+EkinF8=\r\n=gsyL\r\n-----END PGP SIGNATURE-----\r\n", "size": 4526}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.0-rc.4_1665124153386_0.3631426148031969"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-07T06:29:16.970Z"}, "1.0.0-rc.5": {"name": "@volar/typescript", "version": "1.0.0-rc.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.0-rc.5", "@volar/typescript-faster": "1.0.0-rc.5"}, "gitHead": "21006891299e4a81b0be2e179129f0d0983c70d3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.0-rc.5", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-dM8ubhb0XlqzQIENjwvu2xHcx8nUODZY06lE3Rzv1t8h74C9KZpf8TvQmt4/0tWZMeI02kNRLEXi5aV81gJ7gA==", "shasum": "7f9ab1002974df0b9bbfaa8a93efe55b9cd20179", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.0-rc.5.tgz", "fileCount": 6, "unpackedSize": 19732, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICPM04zVzTzLzlLwETajgHe/awIBvO8mEECh1lXULV7PAiEA6RiqU1UBm+uUM8d1uyJve/fmIg2bhCmTbn1lz/AyHM8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP9eBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSYQ/+MQo+egX9wtZUor7KSFKG+IXM0ZffL6jcpvtRutpf/wywMNk4\r\nyoOzNTCGL3eG51pVGm8N2URJ5gjR5j820cUc5wIndVzpxeKLNEtZWZJ7pyT5\r\n5yjUkOOKBE/F+DvmMeDbN0heVSxnnGkUTPli7oWUa+ly3cPJlBYgOKKImJ1s\r\n+u3pBoGvf9dCPSKT3/U0Ko8I3+0RpKIWScwhhrdvS7BCVZtY3db3EHEpD8wI\r\nuw4t0ZfX+NRYlnnKQRNkj7NfBCbGNDa0Snqyv7eTjNELnwI88F6Ggij5/+Xt\r\n3WlQwYu1qwHdR84xevH+b9vajRL2V81oTErJp6WIYDAJNwe0k1gkW+7BcpUw\r\n/nhrvUvVoM2dC5i+Jw0PRe71b85ZRhvRF6K35mrl0+iJcWj2Q8VVVkP2k73Q\r\nK8PSZjXPKNpArnF+mN7ZHJk2vtJ1/rDx9wxudDV8urk5zjuOx+v1xEITPfd2\r\nNtlb2fzerxzMF89YNKbqKO4MrDvxfAQm94PdnvUeu2uoUM1Ca1vxtbpxh/UJ\r\n3WMyDOCLOvmtC4iqU7EsvKo2IjcpjEaWNCtQbU3Rd3SHM/qaTdMGsblpeuL5\r\n+fRzWiJkM03YAEKK2ROhfgP7oQjgH7ZuG2dlPgV/03b28DPGBoHyJHm/joM0\r\n+66IJ2nHoKsVOUzUmaFfD8qKGgYZzzLqlZU=\r\n=qaS8\r\n-----END PGP SIGNATURE-----\r\n", "size": 4552}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.0-rc.5_1665128321468_0.39762097028509746"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-07T07:38:51.360Z"}, "1.0.0": {"name": "@volar/typescript", "version": "1.0.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.0", "@volar/typescript-faster": "1.0.0"}, "gitHead": "5496c1ecc0ae6207d6fa7da745f047c44c32db81", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.0", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-0BsNJnN/VuQ3WQ3RmdJo7Xf8pwT0JCV0xdtgH9okEMeuXBLPZjg7tKwDHT3TY8ord1mVk0tjNnzyQJAhaQ8t0w==", "shasum": "281567d955285cdddd9bd8f396e88acdb7d7ac03", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.0.tgz", "fileCount": 6, "unpackedSize": 19717, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGvkSEvn0uYHDuLzZ+Cbqd7ZeiPUJyFnenUVL0WckZjuAiEA1pQlq7UppW8QCA63Cf05c0TJU22dP/uAoPuS+XALiWw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQCPxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouFA/9EhuYrniPj8y5hm3hG786FxX0h/64KZH+Yp0O1bX9fxDP6kQ7\r\nGMokK41jhG5Tm/jKMgDe6x1/6k93PAzEaGizgaTV1kJFqFpeWx3CkvFVXFf8\r\ngBKuJ7f43dYAbHunIyvYRxR0INkVMAwhHuYm1cnNCc1t9NDnJPkEfT+fHZKV\r\n5F7LrIhla/557VNLEYoo9kIBsm8p2k6OfpY+GmtBYa9EaLRBYH1nLB9l/sZO\r\n+xbfeY8n0L4F3VkpPXHK584VgNH5WewodpB/rUrJvfKAkZX18FWxPCtZ2UKR\r\nfQinRKNlrsun3L/8aaSLhYfai1L2nMn00ZuLO3YPifUjawfk2PkLtmGqgWV8\r\nQAGsTKuQNOaRYCH0pwG5g7UiO+tWOgT+YLynHDyOhloGQxVFCL1OUvsjIhV5\r\nRNmPXlHE4pfotvpov0NOsQK6lBZR2S9Yq1DKwRZMKyLwRMwT0T93N9Kv9pmq\r\naLhuwCynSKVm6y/0xb1ZG+xVdBap1VnjgnJNlteVk8WeHQs7ASJNEbUbDcwH\r\nyOvpXWqxmCyf8VSGP3v/sWLCbyG7eVSuS851K02nW6ab8hJqX8ciLy32w+NY\r\nIxYlY4T0CuwnzNPHnbGndsUZfEnsZWpvi3efbNrYF0GwUNb5vFAmcpiyl4Tg\r\nA+75Bxz9tiBt9yTQYO3bPOEKE7pMECM8+KE=\r\n=wDe2\r\n-----END PGP SIGNATURE-----\r\n", "size": 4549}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.0_1665147889574_0.5003836602526848"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-07T13:18:59.578Z"}, "1.0.1": {"name": "@volar/typescript", "version": "1.0.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.1"}, "gitHead": "62a5dce528c505fcadb609fb391ad7f3f8302fb2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.1", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-/TT6gxK8oz2AdAYSJ73e6MmT0t6r6bfkt989c2QmzDBzF3VToKCjmfIXk9r2AbZ7z24cYVM6KJ6fj1HTHb2pAQ==", "shasum": "9c167813e9d85259987dc08a45ac9b0740590ceb", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.1.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAEdwp7tc0IARVQG1N4Zi81aLINVQVerTruNhwSZWtljAiEA/mTwjHCyjIBe9t0bbDLDEdOCis/8YFSCI2iHuFu3b1Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQbqXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3ZhAAneQ2buPflEzDcQ3ax4dMKJwfAKBj5CGF07pqirULvTi/HOjm\r\nzp/m7S8kiYH8b+tmueQ2NK31HDDuZhF3a8zTuVDGyn23PX26zrMwPB6KJ+ce\r\neH+AOsbQiz7N/6vdAjMt8Jge4FAclfzIOVKpMZtwaEpX6H9ktRZW0wF4np/k\r\nMkgEgJJ3eQhD85x0/XTDj4cOhfV+yRpIKzixhbGLlQCr0dSAl7YEuWZ/ecJD\r\noZTsnjlWiBuLTEEhFiqIvfUyifYhfxR7VP0sff8k48qYgldSalO1ngM8omWy\r\nuxyzhtX64Wt9boZxWy/HtrL7tCwpmFDprkBvHofdlHvuVSB4wqzaRKrLH5Gz\r\ndi4GDHUSzNkFEJ+AvXS4gbUTgedHqZn7DzLqxM8UjnweoInvlyynzlN5CWWr\r\nDNbS7n4juUwiKvZrfXSgNm4Tmcm1B/ka+PgYAktYbLCLEMITJ6eqO0ignbHF\r\n5SX6MI1gyDXPrsT4q/bmwhHahrk2VxbGKW0WtJAaI+ixnUW6JR1nJ8syBD6o\r\nE2FYm+M1BOACShwvuTTEZEOrOX3YWU5y+SboAWQHE1IzQR9TocU3yFGkX2oP\r\nC3lqKBsDQPebkOPMWSierfliLPzwG306oq4GqCgp4hl4Dw9ZMKJX3T0JoA8W\r\nMCw9zdFFdKm4Rd1jusI5jNUNUAc9oJRDtQ0=\r\n=n2DK\r\n-----END PGP SIGNATURE-----\r\n", "size": 4528}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.1_1665251991375_0.7015194209632825"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-08T17:59:57.338Z"}, "1.0.2": {"name": "@volar/typescript", "version": "1.0.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.2"}, "gitHead": "4e750f808450c0f70b0a439cb6954cf29345e8f2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.2", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-yQ6ze+gmf+jYTT/Y+IIH9cBE2YF3I92N6vGfdrVgwnDOj1jnulvJ5viwdTYcAIeEQRB0qtwYfRpSHOrGG2FL2w==", "shasum": "5b8dbee444db0593fa35340a3e77719ebcc4368d", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.2.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAljQ8G0VliZPwnBBy+l5TIYmWa5K3/B73mnSdw9AnxcAiAaBR44qmc5GnvHtZ7GnPpSt2Sz1lf0u+j/9O8Q9mrvmg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQtAbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxOw/7ByQsbpfd7+FRZtYSVCTZX1atGys4BiFbPAS/WG4dFvWmXOHc\r\nG1S8jjVeL8uHuTAQ2IocD2AnSyna8P5hv6lCmEGF6HR0rrFpg2uYyi3S3OGP\r\nclJotGuv0gCi9Qm+p/IMuSTcHfYjZevBMjLmVr7qQKCnPeZfSNlex/f9s7Ej\r\naGpWKx+basAVB7cP+SCJx8KGrNw5OR09CC3+hV33Eex7aHQ4A7b42k6D1MZn\r\ngClC359twv2AKzC5CrYj+f7oVuPWAJ8YrFzaHegi8tuTbKiVHWkk2whAbCAk\r\npGJwWcblf1ZQnU0Uj/gqcy83OQoasT6uRVttrLzJpOcsMojDrl5ZuwA5Sa5s\r\n+xdYUYRMO8ED0SdVEULXNNllLmsRlBN2DZzTuXwUd0NMEiSQ0q62IqqfLeZi\r\nlZuMZuVJ9vKtiH/F6wS188Dg7nruqCvwIpwYYfAkgyTuhAAF6JLWcSOcbkIf\r\njSicw4wppt2zzWqmX/PWwUoQNEjjZKjWQg0TC2OWmqeUNXaI+KLkrTiWHKy3\r\nnbWLsCGEtqMnhvmYQaqonjUFEWUSVJYLR38hUCkpyMXesLRoW1GitMQtF0Ou\r\nCzccCsXBrlAUL+/ygKJDq4oBTnaJ2GbwjGFkP5/nzDLUOzISPJBmt7tMnsFf\r\nGRP6lSvx86Gii1VoYVt6lbLYcOPakU6NAi4=\r\n=vb93\r\n-----END PGP SIGNATURE-----\r\n", "size": 4529}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.2_1665323034900_0.18561768385860722"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-09T13:44:01.088Z"}, "1.0.3": {"name": "@volar/typescript", "version": "1.0.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.3"}, "gitHead": "2576804e9479aec58f049bad2ea5d2a81f9cba28", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.3", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-OevIxAbdgfVxwWYI6ht5tmt8GqBBVQWPWn/51MNlqu5fVltFGRkOBmv3hwuvLn5N05pj2GpbkaaLZSjpj4iT9w==", "shasum": "3ce25d16c92cb4dd1e4a50c1b985e4856ecbecf5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.3.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICTiLHVxwWMTraGyQr56gJLmgL0Qhz3h5/bkea0OZC4kAiEAu2e3WqUWRRcIAmYdV5u74JzBWCd5iP3B5eP8KvPySSE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQx1NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr14BAAg3bSgAdzdrk6EKwQ3QH1uBj6vjxQlrzh+9fgB0jEJbmlCMxF\r\ntaX8xQoOoAunlHvBv4CTDaXOY20D3I3UhFAgf9x18p16YYJBeVhBtmhH6HJv\r\nYxgUAQzW4YzCjXUSrE8LyqOisP9fJ/h0YVWuGzhCC8N6m7iAWtpo6umTY2ZJ\r\nQiZr9dzu3Jzmlgl6tp7iGV6fqsk1qUGDCgJjS3x3oskzGH2+hHuP0bXHk108\r\nMreKrLGVcxlQvITe+rciuQpUyKl9S0K74YnOqWnwZjuGRHtd+o261MD+A8p2\r\nncuVAV6cptd2GAN2rniRmsXBElxJT3EhNHQ2AsXm4eY46h6NZGrK45jnf2lp\r\nB+xjU49mM450StMg1F5Iz3yqMKK8MILksO1tCXhCQtqZ2T+ovWJQL7qI2hdS\r\n1iZmgqE9eatdkFo+w89nznsgnkLAwKao4wQ/aMiLQCGQwgtSd17rputyZtXT\r\nL4XiZGDJS26E+pHCCVfHaXsdVXxFUAm3Yo9bxrCGk31ShNAkMmxDFoVhuGPr\r\n44qj00A/OK7zPYaVnN1TyYwe8IxhBi7RiibrQqt1y7aJ3JqJPzdIM4E3zO4/\r\nXfIpfGujQFKLJIOG8X+6nTQIVxWUsxBoMY8CnGpNZ0/RdtKUKdxj5+6TvP4I\r\nOQ8xlxXSSdLQuVZOD/vcHp2oHaLsqzWierA=\r\n=MG+e\r\n-----END PGP SIGNATURE-----\r\n", "size": 4527}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.3_1665342797531_0.*********38122362"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-09T19:13:23.492Z"}, "1.0.4": {"name": "@volar/typescript", "version": "1.0.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.4"}, "gitHead": "1394533d1822a588136364ab33a02c1bdf89cac9", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.4", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-K6S4iGs1di8uGrbgBHut17xY3OPSG+JFoaW7E/RR/BbhfebiZTlFRKMA17UzEq8fNbiODBZTCYkPxXkMrOMSew==", "shasum": "2600786c0f196ab53c61cceada93263ed2546beb", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.4.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD33GmRxr5m0BFbvCOeAGORrY0VITU2lwG21gGzGKTuIAIgJAHNuBZ5Wg1WPlCKu9slsYd+n3BMdRJzo081AFzqNAY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRdayACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqrg//S5gJEzF4ZrwR1M1QwW1ysRpLpENN1OJ0YIxwTtPMBgYeKFV3\r\n+gxdtKOWetFVF+Jfzcq+YsHbAnLHErLH1ZJqdvoIUd/SA+MbA0B1imfOwkdg\r\ncl6Xjb5j06CAwaEv+U9m/snkJ12KYhUqO7QF2lfmZSrudZq7boRGM91VWsU5\r\n2AymeW/cb9b9wzLxF5nf+jeWJlpnPtOuWeQ14pONk5oXNJMpVcV0O3fj/9Vx\r\netDoqZ5F0B1kwlbZD5Z1p7Ln2MCIoQEw3PQA7DA1PiOdqvu5VGiFqgWfQ7Uf\r\njt+kGycsiw+KNFlzGPVyvvuvJr7xtnFzOSlPYzrZ/tiWOfLaoGxlOaNYCkTq\r\n/QpaaRq0JpAF+2uEHH0AQ9gYzuOXtmUHkU5q7LKexQ+40Q8eq/x4du+XG2Fd\r\nfoiZubDrgbPLCZ/koabuhDNC01qXGvff4E8VbWSyzC3aEGnULyk3rWMuBDqr\r\nF/SRyy3lurOKZezOS6TPzv2B1q1sHkZWh1Iqi/DWWzP/V0L3xeN40jvECxVX\r\nLXVf9tm4tA2QR1gn0yclmiHIeiz0ptrv+f5WFy1ijypS+yozL9DQdOSA6kMM\r\nGjlUNikqbR+bOeOksfChPbMyoCHUDGsoZ4cNVul1g8oD7ldRNnN5RkyPf0uB\r\nTOapJDmuI228k+nqRz/ISviDWmWEypdEXbY=\r\n=YNXp\r\n-----END PGP SIGNATURE-----\r\n", "size": 4529}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.4_1665521329936_0.5451474673492711"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-11T20:48:55.863Z"}, "1.0.5": {"name": "@volar/typescript", "version": "1.0.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.5"}, "gitHead": "9735e4138e400b49369334279a8b354e9418338e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.5", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-P+nDr/KUtWi/HHqIO+cTmjgQVuDn8LNhvBYUph7ae+8LhePWvoweVibkO9Dp/lkIo2L26BAa/1XmtHv666GnJg==", "shasum": "646bb0018c270266cd6a48102f02ca1037dabada", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.5.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGCaLZW5VqplXfi1yo8A7+WzkNLWGi4VjvKgjMLIoE0UAiEAmIC1jMyhMEP0fpvaMIZJMk0pFQIFRqbrVB3DKeEEnfA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRe7TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryMg/8CVkTv04YDtFYsg3iJcpztge2l33clItqvu/A90f38Ezm1ww5\r\nrEEcWtGw0gLpQL20Gv80YLU0E03C1U8Ln9FAWe6H3pXokFOeRnB62Siq5ywK\r\nw4Ig7k0QUSFSbaRNHJzsPsQYzKcABMRHh3w/76AO6TQ1OV9daz53ZokYbmCn\r\n1FV6SxVWB3lZ6SJzy87vKt8N7J+zTf6CnEa/dPFk/j24Fur+4QHS9q2U8Ht1\r\nR/Q2zr4yFaYVlK+CNXPrGPAYZTx2XuBUCTxdppQM09gfxDrXWQJhgX6tti9I\r\n9awPzoRBIBiMlpZHR1Hl0IVT2XPAaeMxx0spuA+e96oG+n0R4327ltUH55bE\r\n7L1bAABYvWfIlWaQmUV5MoP+TTyMabFPX1kwRtFjPlghFxRGs/5FOAKanSAf\r\nlVtuDrMB7dbgdPiC9nRSZC+fwTFBE3wlNQruqpJFjZGewJ+Tqgm3SR2bMNng\r\nhugW2ai0K9iUQxhYKWe2c1m76FFn95XMhOoGq8cryHNkn/F4OR7gjq0+1v1i\r\n7WrSoXVJdd2yBbY3Rxu2XoWEI6xGt/B+FomP7bMwoLIUKO6ZBLSScIuS8y8z\r\nQo21UTx+aHEG8P3wv4TEiRla50C7+c5BiSHiRTKqqH0gI1cMVA/CRmkgy2QN\r\nucsojHSCrp5aPPPnylyRf3pPFxpdZUsbhUo=\r\n=1tYG\r\n-----END PGP SIGNATURE-----\r\n", "size": 4528}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.5_1665527507314_0.8048908533773274"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-11T22:31:56.372Z"}, "1.0.6": {"name": "@volar/typescript", "version": "1.0.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.6"}, "gitHead": "21df3435df506fede5c1b078dc652d0782923af3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.6", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-qfeqQZ2fCRnk8JUlIbpUlypdfm1D6uLSi8e4DtVHYlg7hWU5diZypyZdQX32CBrZ7mYzMmYe3C2czkrRCGoO3g==", "shasum": "b7f7fec94d48249c12f106634843ba5309ffca9f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.6.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnWZ3gRZfT7c2MJPP0ht2e9xXitLC4hV/Fxp8fuglyBgIhAJmSQYAEJ4R4WXtCYE+G8A0mcgSbTf3H3OhwxCJb0YVC"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRn1uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4uRAAoNvXgoF6pMe1vTSyuWNJuMltw3B8QNQa4weC4zf2P443F/lm\r\n43Zpn/JC23S3BXncOKadnvv1SOFWH07zgSI6MLq4ttQplVy7QkpFt0/YGgLX\r\nugE0gMLqciBB/0GdqmWAD3cbf+qkv2bnYpPLzYqAn/d0ldvzLnU744zPlEtx\r\nSbwPGYJ4rtGunEQ/4p2yfTWI47QGL7gcNb7luMTuymHU3qgpKjV4bTtNSFIT\r\nbZvXAOt3cHDXFhkG4Q23G18wZ9xx394fCrud5eKCC8V+ggDnXtbGr29DGGx/\r\niZ9X7CU1X1cc4Aohd/t4Adlxh4fAgaQ6gUxMoWzX3+U94Jm/p4O4ZmQHEQ4p\r\nXAShmToJdr3h77HQdb1zt+9THnDjl3dLjk1Rv/4NFNDtT0hzukRkpfGGZWh+\r\nQ8p5f51cN8Dd+SHRQFw4rpRdNwLSs50nvJcw8J4L1ECXCnwFcOotcP9/1M8o\r\nlNEwmAoymuddWIHcUzAA3C9bBrZlcWgETQmE++8V8ghoa7fixd/o3xKPLuD/\r\nTzpQSxTHJeqxrygfpUIz6+qFw/HVGWatYx05ZTiLrFiBv3tgRgRYtIuaAds5\r\nyeai6br2GOPyix1u8AlJaBrvVaxVxzovutmwelfYLQsPmtHZ/DnusYdqsfeW\r\n0evOKFm5RkFy1v+YOJqU6qn9DRXCIwmzHXk=\r\n=R36S\r\n-----END PGP SIGNATURE-----\r\n", "size": 4526}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.6_1665564014000_0.32341144195119753"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-12T08:57:53.950Z"}, "1.0.7": {"name": "@volar/typescript", "version": "1.0.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.7"}, "gitHead": "96cc1acd9c16ad9cb46638a54f088199f7dddfad", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.7", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-VH3dMxTqkdUqjmdVwIytBjppH5iy18qK9peb3E9CNbvzf/+b3TpdX0zoyglaKmC5IeHG68KHXK2yps734yZ4nQ==", "shasum": "e98cd7fb780bb7c642c0ec9a9fbe633dbfdc13a2", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.7.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDSu4vrbX8RcIycPKAk7OFl9sLcqVPuU5oTeZiMMJ4M5AiBeO9464qhMlOQjq4Ds0qsA6xQDFAAIqtGOuDGRVcBJfg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjR2ihACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrytQ//ftLp2aiCF48R9AxJNr7gY+E/DBzEt2ohXn9+hhOZNhBCRmpU\r\nLbdZjUOWNZ95Ani/e3auSclhyleJnvKN6X/Of2rhQJbWHYYjq5teskJ2qiCo\r\nBf6Co9kDW2315uDGuxHSOxIKKTruZYkdXAhc3+59uyTdU+WNPvX0em1kHOzJ\r\npIvVrJhTEOyleLbPikmbK9xYbIr+yFIQCTHVqq5EEzn86iJFwECZvKsIFVSo\r\n6Y7qVU3EOZvqj+80WbT2qTKmqxYVZbZY3JBPB3KsZDI42MjaZdPKZUuEY6Zs\r\nDN+1yV2WsbSTdFdFtEt9ONEKdQjohJGFM3e9Tv8VfqljZONgBdR/byPQhnRG\r\nmSeBTyGGPwtwguFSjpC19N4UFqCn6Ajk4gn2YST3M54UAkug9MZBp9yBmb7c\r\nl0yuezV5Zx3Q2TNE1qK6ZhJL6chjShFGROpPU+xIDAv3gPXobUQDx58PfBfx\r\nz0WY64pG30MJmD1cI06nRBKFsGXQ7VtKjAyk6XP3hpS8BP9zVW5o32NBwuqy\r\nnDmCAMcp7NHoPis1THjf/7SGuo1aUFNgATpr49/ufVXAwTk2SdlCub7GcyKC\r\n0DalPtj2WEg0X0miBpDOs9/8nwH2TO1Xim2AS2KZWKXwkgYk28MAaG/Q4drk\r\nNAGmTeAxQ/cvnlywY4lXsmKvTRb94EqV+pE=\r\n=wbtE\r\n-----END PGP SIGNATURE-----\r\n", "size": 4525}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.7_1665624225656_0.3111337756711665"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-13T01:24:37.646Z"}, "1.0.8": {"name": "@volar/typescript", "version": "1.0.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.8"}, "gitHead": "10d1a6a8b4011adee0a7c309da25caae7fb4fea9", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.8", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.11.3/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-2oY1Apvzcs/5tAn7p1tRlDxNgal5ezaK0h9cutcWALeimsaQBAEE2NAirCrLMHl8DneuDce0tzJqHaQeHw9RmQ==", "shasum": "45674506471c3ee8cfabb0d98f75f8e2b2f18936", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.8.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBnyavmE3NvQCkmWTbh6RWTEg958iF0+9js0TeBMIhgYAiEAmpE2iV2KWONejVg567ArdN3gjcFnTsrEXq5tyEO9K6U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSimSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBSRAAo1ueZrUE5rQYFmFeMCNz3vbTQx8Yba8s7ALU9kDhmWg7ByS5\r\n00laXA9XdtKnnVnY4kWuiG1CK8rAzoVfWssyLqVzCjlWG76Gf13XYOEJY1Hj\r\nHeRmBCOV9WhMZO7Y/9EWyE64BvcvHSn/1oJA/efdZGI6TxkfQOw1pp+3KeZa\r\nrFg+RjzQuiZ4anWbVk3+iolvtgnkmYO7F2LmR9CDtQnfsHFSv90y/cA3JRV4\r\nkLjVNgc8MFk07IzyVBDOta6mIJhXHDdFnbEQ8bgg9GmW35jstscsXkUCZ1tN\r\nfvjHNGYU9TkW0fTwLC8VcldIafOcXyszR2aPg+zSMrL6n3SumMBVP2urdnyk\r\nKz5vQ7NZ8DDB9VS6RBJBhA7AJnuMndRsSw7+VZ+w0Wm0WCkq3rSm9ze/BKgO\r\n+3MhbeGsZWxYGUxLq5DmK4y+EJLx3tj2fRujIkmPvYGqftftW0/nsVPAZzBG\r\nOGz9qgLOdlxsqvnORXx0QCthlChpMPk1J7P6yi2SfJo8bSrmS/+1zr7vCEMb\r\nbez0pfWDESGRfD2PnkbPQLoTcu4jtPqWsu6bFc8ylloO7nyosPo1iMksMgpY\r\nWqinmCmBwpCWXlotHH2zhZ7z9t+JUZJHlS9CWXFok6qz5nPsL6lFterg6FSJ\r\nmiF97UbdmQ5EfnqZUPHeS+xtK0cFV+9bfAQ=\r\n=x/sj\r\n-----END PGP SIGNATURE-----\r\n", "size": 4523}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.8_1665804690638_0.5937832210291487"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-15T03:31:48.490Z"}, "1.0.9": {"name": "@volar/typescript", "version": "1.0.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.9"}, "gitHead": "be2081f56ce4608324795b8a0ae83c288a3a784d", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.9", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.12.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-dVziu+ShQUWuMukM6bvK2v2O446/gG6l1XkTh2vfkccw1IzjfbiP1TWQoNo1ipTfZOtu5YJGYAx+o5HNrGXWfQ==", "shasum": "9c0a8b5d79c0a03413755499d211c1c8001ac0cc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.9.tgz", "fileCount": 6, "unpackedSize": 19607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMWijDbo0AoE/LHGuNNZQubaoahJDG0gkvO39ttarUrgIhALFTqvj6aPr7jsJ5i/wgjg1vnzDgDyCx9W7EzmaBdlh/"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVScLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+ng//eUDOoOX3D1ZfE3qJhzUOD35/JiSpz7Pbn+jaD+j5b9rXi4rm\r\n/cbWoq1D9EbhgtsJNeT/qWJxkevJWNEhIe/SJvm6j0TV58H+ezwww5JA/yw5\r\nBTAOq01CJsDvqXf1eYeMvr6FKaHMRt/ETkaEWiLICqVQllNQOGh0z2oTByvC\r\nfG5Y31GCqrNuJNqBlEQcmBFLC1TeEejc/sMCCrNh5k6v5fJKFHJdfhiFlZVV\r\nNB0GvdHvxz2hHWDF2uuJwTVRQqa40o/IutaazBLUmlcG7LdyN0xiZGoqCPc6\r\nd8asO1K0mkWLicmPxnW9oEX/OpOImcvHI1s2qyVvZVs+fz96LcSKcwz3t0vc\r\nU6OOk01MjaHpVd6qIqbRGt7adudnZZ+CHTWeuax/naiWrk8zpYsd4MsngKjW\r\nl3SXyJjAwdOj78yF7jTQr+W08SIUPQ6qHFcm8No/s3ateLju/7oRpvjm6NUW\r\naQjlCnLRTzNEnY74rrK5ZnoX4jRA12PSiZ2wJg+2ss2L7JPIyTCT6QEGa3vu\r\nhNRXSoitrfAcH9VLrQAYeWGGpdHb/Zm0EpnLvKVU09p3o93U3Q0RT+MEh0wu\r\n044UO8wj03+ILm5tDMa23exysLinWCxtx2cbCcugcRYLLCmng+wEEgezMWvR\r\nl7nfdN1h2HcY0ORzedCBLYs4UQ9ppCOIgdI=\r\n=G8AC\r\n-----END PGP SIGNATURE-----\r\n", "size": 4528}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.9_1666524939647_0.01934477629632081"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-23T11:35:43.634Z"}, "1.0.10": {"name": "@volar/typescript", "version": "1.0.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.10"}, "gitHead": "89b82f92fdc30674b03941c5f17c60df8c46211c", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.10", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-Nd+u2Z2P1V+KiNBMLLK6wV4sswOOYBsjEHmgK29eENXtos1+gF2GWB908vvwmT75dmCtlYZ8No14lvCqXUAVdg==", "shasum": "339d51cca61c5f562803cde130a1c0090c225cab", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.10.tgz", "fileCount": 6, "unpackedSize": 19609, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAMDCP112dqq10go3Ti4PiOHQ12FRVFbJVX6o1syt2h1AiEAvPxIG15TIO1x5tyek3QiCsI+11cr6h/x/NfBXWA9JBA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhgteACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDIw//UdjMtmQkCg6ezWknfPZMG//TkRAb2nBj1GYDDytRwSsOpgxi\r\new0WLX2eZDDlLO5ChZqEYCwQY+xBuqATCgrnjKEZkayT8lcZDUysM+ebphr9\r\nibQQ4w6kX9n1/uWIHswQiCHgQNFOsm+maOHhH+j2oRlFY3kFnb2yycL7rEca\r\ne/RIQ+gkCBw7LS2lgat0FqzWAlGhF6waprGK53er5IvBQXt1bP0mQXrP3J44\r\nXlgqWNsvQOZOEbl9mCECvwMh0+0KnvlAdfgStrZjRaqpOTFJkd9i2n5Jx2AO\r\nq6WzYb8nTtQjsXedPqezJpjozud9o6VHEbM1S9qGIJQ3hOzEBbQ4CLO1pPLm\r\nenl+tWlCAJcL1IAgJAAilZ4ASHClrg6Y6tXrd70Np6AUJHn6NjiSaRFVCdI2\r\nP2zl3i6aiXmSkD6tq2WgtchvN3rfn0zvx1qADOQ0wC9rmuQHFLpme3ntfCpk\r\nAcfPqIyWaqgMTlYFCh3tJiY+CtN9CKUOg1mUvAJo6bxp6JLeui2wjjh4QZ0P\r\nFuj7tQppM8MhBjD1pFTobeBiCDW6jy0NPsg0WgL3Mc8KgDpe49pAiOymzur2\r\ndXDFnbpGdGsWcMFmFjC96We5vcO4AJYXqgafE2RJdZ2Atav3JdIACEm+B48x\r\nutP7c+pUMgkVXZWYx8df1T+viVotkHCUOlY=\r\n=8c4m\r\n-----END PGP SIGNATURE-----\r\n", "size": 4530}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.10_1669729118715_0.19237574711967675"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-29T13:52:45.275Z"}, "1.0.11": {"name": "@volar/typescript", "version": "1.0.11", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.11"}, "gitHead": "aff3d7c0896a391412a605597adca7d796e9accf", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.11", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-mq7wDDAs0Eb43jev2FxbowuiwWqvL3kb+tar1we8VQbdabpyQ5dmbWPwo/IglevMmW3SKo1Et+6rqAeZpXNnPQ==", "shasum": "e127e5598f9bc7e39cf69c8dd6e2f43070a3fd72", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.11.tgz", "fileCount": 6, "unpackedSize": 19609, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEXnizoUL0OY4Ak3PUMv2e6vYyV5fIKl8/nmEOJpIWf4AiAzaG6izBLrok6STc7b0JCOfILgAtMwxBAUvtBx4om4sw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjit5AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRExAAj1lp512aeFynsUDP9IyVSr9p8YvzGyhNzwc66RrZBCZfuJOh\r\ndf1IJFPIzgrVLfihYGPZDDAJoQn54SxL8Ek2LqAIG/p1EJIgpHYZHlHzhp8S\r\n5uHI71IONJJUTMc75I24+/YAbdMgs6MzV0/+VT0/KcJUMJEtkvYGJVOr+yJH\r\nP/F9CKfq38du3u+T+f/FXJlfo7IuWBU/5t0toKNPONZ5tAHH+LOuSnJL1oi1\r\nHSIrVGDZrRDV2m7IWMZmjoy7TrhXwg0Av2V8eEUTNbFD2WBaN+iKME76l60i\r\nqL0u1frS/XGovsz5pG3mnMu/YT/eD12iE7l0IdvBknhYTv1/ArtqRAuJRb4e\r\nZJ+WpAUiLs3vd+Cl/tvokaUhlJww1BShOPHFu6hbNBIlTxaNW3uEbzCtHlO2\r\nTg8DfdOISPWKP0GH3mAm6xqgQh7hWybTmSfjD7G8tRcMSWG7POJMZcfG/7Kk\r\nuTG9jvp1QjbqjoDUmpLQc+FDICEPEoBxsQm8rqVKKtQKnUn1BJHdIHFOHH3/\r\ny6RUyTukUAJgtP/Qex7t/vhD1VkK8Ee0Cm7UPglhkTiT+MK3sMPUd9V31Rsj\r\nYUqLi51Sm7du49BWjH3RGdbaQ/8fNTBksK14DayRuptXu7znnu/DEJSBFQvb\r\ncDKcuXec5e0asEyIQJloJzX+kkSYTgKQFXs=\r\n=cA5V\r\n-----END PGP SIGNATURE-----\r\n", "size": 4528}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.11_1670045248402_0.01651032379396633"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-03T05:27:34.126Z"}, "1.0.12": {"name": "@volar/typescript", "version": "1.0.12", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.12"}, "gitHead": "2426aa8ec6731a6f6d88086da7021a32d9306de3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.12", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-7PR4Fwg3EyuwmagodaGntKxDqzie5Ywiq7evx5UvNIY0BP3yXrCADxBMFYLJykb+ECSE+GbTClsyXTnJQ9xi1Q==", "shasum": "d2c3dcda1632324a7a8b5ed6b1ad0ae7247cd8de", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.12.tgz", "fileCount": 6, "unpackedSize": 19609, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzGzHnLgqjg58ke4LocJ31RZC1DMtTnjv16tQhmQAwFgIhAOuYxIir3n4t8Bffbs5cklY6EhdhWqqTTIbTwuNWN+VR"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkmYbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDYRAAhX4+HbnhQURP7WvH3Ogx4sHbmyhN00hpM1JFnsol1HCUPjKd\r\nG4QFsVHnY9liixjY0AfISiPZLYlHo/iq70g2GlB9yk4lcCyBG3TwPHACyKn9\r\njpj2He1DZK2QC5KpE4szIncdWfFmsLBTtj/8zMtJy/8i5MxZbQXU/1N8OV/O\r\nTFPT02+k+NLZuR/AXsoHqiUuSRhDVlQgOPZdi5N3GWqWqJC2OMJYLOZUY0dj\r\nKQBw+7uZoCmq6B8y8BZbTTA9vxNQ1uNiiFXothhR25yFp5P7MtAEX9I6sKu4\r\nEn9VrBXrYGXzP7zWPdsNhuGIYMkh/Y/k3sa5abeDEO+d+XzhlFSQmPcM3FgZ\r\n8RqT1GuO9IuHIfzEP5n/bXCv2kL+/IswqSDwvV642kvDdlBcfJ9MhaBlZjS3\r\nTyCCGITGl0Qpk9XIoOAKzA9lxYcJhngjKDWbRj/Jf0Mr+FcfaIA49+Ap1J9N\r\nbo5bXrS3faR4NHKcxAuyJVuPcbYMoShkCXq+CO++VcWEOqNz5x9ok3QEVCmR\r\najp9FVxF/snmgyIFbsbKuMwGojtx46pPRPULKYHZ7uIS4J7JgWaP3xbFqxvX\r\nPqFFzcs0Q/B90YjkabvTQ+TKZNCK/DtbWVMHz2YvF5uOWOOR9qFQINKZkUI6\r\nuMfwwOeIR5ODn+6QqdP1MMc4rzLavdQ7VAk=\r\n=KV8A\r\n-----END PGP SIGNATURE-----\r\n", "size": 4530}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.12_1670538779645_0.7090359329068538"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-08T22:33:32.833Z"}, "1.0.13": {"name": "@volar/typescript", "version": "1.0.13", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.13"}, "gitHead": "cba2eba187b2d82e1d45dd86b3edeab0ca7caad6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.13", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-CfJ4higRZrLDAHVGY84gZ444ZUcA3ktPqVMW0fM3mgHDbzYViB3/tsvXOtZk76D3HK2ap6n4cDwBSv3cY4xqlg==", "shasum": "d70a1a1f240967035476b669bad508966897265f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.13.tgz", "fileCount": 6, "unpackedSize": 19609, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGY6H9uB5LgUYijGOQ0h+1mHMNuws6Ryh8ku60QdYKIvAiEAukAJO6i77zAi5qUfY4ml13q6KIv41w4xlUk4gaq0FBk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlpKUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBZhAAj8gR+C0BBeopRbr2m+/z9KwFzOBJcgEt+YUJ01ie/zTb54Nt\r\n6babmi4HYKNDUVcoJzDaeDfA6o/eDsnUA9TX13H8E4lI7XC52SHnDxGVhoyS\r\nowpBsqSWh4ixr8DGB751G+qoKPdjjkdH6lxxFJ4MVv3Fo7/qeCy8gy7IRxuJ\r\nbYJmWlZEhXUM625550V8lbpU7/2ZK9KBSsQP59imUo05SJgJ2s0R9md2t8UU\r\nawBOn4ZwsPo3XDpQyVsZJOEo2JYw+o6ZI7wG6Ii+oslLx0r99qgmMpECxE2X\r\nv1+A2JkbmgeRJJK/OB740lv8VESqInzNoh1ih6S4gtOQXjBzahk0XJS+Yy1t\r\njdP5t8fc7oJ1y/4Ixz+umcVoTIjPz4oCb4ze0tkAQ/8HKJh+czChhg0zyOxr\r\nHrVKNkLCajKEtAmCECMC4ntj7YUMFzeQrhUsMOR/ihWvKj9ZhBTFnhRMmLWl\r\no99pOFPdH5yDL/75lkf1Q1a+o9ubK9rHtlhrWP7s6Q67/KSVXgysZBzd7Vx7\r\njJz8xQwm27Nr4dCxDY6yVA0GI+7dIBjmDtIgp71j/LI2sfMRzOrFhT6Klxca\r\n/yUdfh1wp5jOyzyxQ4YE/ICzSTGvVDAp/SWAQy+9MEKvGqBGBJy+D3peWlS4\r\nHDJowi91vWn9JBWsyHcnbAkd7vUjo8DVPmc=\r\n=UWVS\r\n-----END PGP SIGNATURE-----\r\n", "size": 4525}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.13_1670812308504_0.002324915077095202"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-12T02:32:43.047Z"}, "1.0.14": {"name": "@volar/typescript", "version": "1.0.14", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.14"}, "gitHead": "ce4d48b37db784400e15fe08282edc836e24d4cd", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.14", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-67qcjjz7KGFhMCG9EKMA9qJK3BRGQecO4dGyAKfMfClZ/PaVoKfDvJvYo89McGTQ8SeczD48I9TPnaJM0zK8JQ==", "shasum": "0e30ff880180e05019d4f9d64012f6df39ce4c6e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.14.tgz", "fileCount": 6, "unpackedSize": 19829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfbGsXs2qhi4ndn2NlVU1SoaMFhcILwufV0/PHqeTKtwIhAOZ+WmivcNmUOnR8sEf2VzIrkHEb3SLMS+9cLRIrnurz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnsFiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9VQ//aPgcGc4YwAWIVoNsbf7S71mD8eUyFARGa/OHnLUTz+9C6J1b\r\nR7Lwf3LtGtSxc+pNPzqtvOGmXa22AVyp+/V6v6frRWaQSuQd7LAxrp1fph9F\r\n5yAMl5Js83QeSLqQnsEev080hWPQPyefGEkUiOGlk1Ml2L/Zz3JPKLzlT3VD\r\n3j5l5hhgWfpib+BMwIVF3W+364MHmOaRKnYEILyidjiT+K5ufqJJTMpZp+Lx\r\nFZ2jrDdLXwMz+5LxV4JTwd63mehgYNIEIFUNax54OlOk3lCW7CcwvQ0ku4lW\r\n20k2aZ+Kjfw/SyXcQI3QTKvZEK9bHgn1OKHJz9QFKXkWY+QyzvoWYCF6cjQo\r\nEmqZzKwTlj5yj+sVSkJRhb5oOGZGs8wS33CHtgXJ2Uss4iL4zw+UoEzPDyH6\r\nxChVR75QZdP6Hv93GW6f5/fqGr2ea+DIHOhNpOz4fPamIsVZwBIsSOE4MHg+\r\nh+gP2TV5Yp+dJGGh00l66gCqJKESKyRNRxrtJjfYAeebCf59QpXUyzyj2RNu\r\njvd8LvoBniGBP5mH/l78MSAGUwWShm6cjPALbxfq07NHzuAyxatDegRCVT60\r\ngSqVm7EvDE1dQAxFNJDsO7jUJ1siVS2HFA/OTYPPsxO2AhhW5vsH2M82gjNm\r\n8Aot6WoNyU45aGfd2Vfh7a2gdjpFoSbW5Jo=\r\n=BidQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 4587}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.14_1671348578062_0.03782832538427239"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-18T07:29:45.793Z"}, "1.0.16": {"name": "@volar/typescript", "version": "1.0.16", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.16"}, "gitHead": "af476047d8859652254ed36f8aee184f1972a97a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.16", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-Yov+n4oO3iYnuMt9QJAFpJabfTRCzc7KvjlAwBaSuZy+Gc/f9611MgtqAh5/SIGmltFN8dXn1Ijno8ro8I4lyw==", "shasum": "d6226dfe685d36f2b37350b312de5b77a12d8156", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.16.tgz", "fileCount": 6, "unpackedSize": 19829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbR1FRzhNeWEwKGeci6DqB432hu65wkFcGKCm0r1hKTQIgaTIQZ26KpEV22Xt4P+Qnn08bKdw6ssn3FOUQ1VDZ2Hs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoXJDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHWw//XI+hN+1cm4XdJbHRxbDqJOOAWfIXQQDjoS+ImPsxvKr60KNw\r\nMsLfQtONYMYZ5dCfk1BEahR9iQ3ckvClktZ1saKyIoCy0fULGawsAEzo5Yhr\r\nWNOr0x+PJoUrbheyLryaBDZ8weVb1emitjrC0RS4rJoJRwSPvTphg46nxWED\r\nmRnB0Gt9qbs3nqcqTVNMciUEoJLw7nvhV2LaIKpcSmjI7giHeXNkaqkhAmDF\r\nuy3by1f6vve+AZ/0UpRkLaHVbipzw3/jnFKqVfwp4b7kLlkCiSmqha5j6m9b\r\nu9Z/fMnw4q3x+7JgR/+7UNlh05Rc3vMoti31InFJG0SZBmaknRLOeQqMYluI\r\nMMgiTDjWnGBI6+TUVHka8Tvm9cBsZysT+X44ZayIiFZpy8X2PdIMYCYsAdp7\r\nOri4sAnGEtwEnNwoAv6HS+q7DtBflcJpADeq60owNftqlCEPYEqZcrygrnvJ\r\nDHXuhbeItRdSuZrXPHni3zcRYIXF0J+vIsMks9BmmgWCsl0xuDQnXbkKHyCu\r\nqhC87NpYmC2yESbr/YvEI07f6ikKBUGF075UG80p56w0Xwkf2NRKya1qdFB6\r\npZBoZ+RNQ1uD1kyMXV7MK6p02hHiKkYExBfPLYO/DNiUFwnt02MnFHZ3amE9\r\njw1T1R601xO3VF+L1oeZ2Mxn+vwmCnc1Rsk=\r\n=2G14\r\n-----END PGP SIGNATURE-----\r\n", "size": 4590}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.16_1671524931348_0.2786664950841382"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-20T08:29:26.806Z"}, "1.0.17": {"name": "@volar/typescript", "version": "1.0.17", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.17"}, "gitHead": "b66b64f579b5cafee9b8d70aa9f9303f39b6df49", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.17", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-TvJ/pSC5B2fFNIry68q2tW4xbbyNtW+AEUHG/OZ9uBExOz3rc+rYYdm9aRWpxBkPTJNRIYnuFWmo/ClQdHzkcQ==", "shasum": "d9ce779c6d48ee94da88218066f0ca84d0fb4192", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.17.tgz", "fileCount": 6, "unpackedSize": 21819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDARgWqLoYTGlcrBpJp4YP0LnLxOZiPh/3R7NBhicciEAiEA6y/HEGUKy+IybcFnEiCta+ILmSX32DtadbGAY0JjIJk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqJjaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPWQ/+P9phMl2UQ4X4m1ZQIxTeCp0/5CeWwi41WGOOd0oydziSnn+B\r\nwFf0HPGGqxNIGv8VTFRGtRKH9XUQYZpftJoVm0SZdR0vlM//rTjiiaEYH7oI\r\nQQTDj9oYjRqHe5F3LNw11ODwKuOVcxqdR1Eymta0BoJZI5jrgSPiuWqL+E6M\r\nxh3JOa6sS6PXcdq0n38xF391Oy3+fxpVnXI0yZ8UqCOsTGa1eh9Pyut3fO0f\r\nbTUF5Mi/ULmR8jIWoBI/eDF22breyeNkQWDvPvgvGoA9EnOgwB81MFc+sagN\r\nm4X+0b6Ln7pkgDgjSgG3sCDR35B6b7yw6xV6cGwVZN+PUt/tBRsv4DjWgHOK\r\n/iCPOsrEL0DqpoqbmbAWwTHq6Zbv4psqDX5gP7kpIrGx3pY1X0LeBkwHo/eE\r\nLsaxEwGdGoDoYO/VWS/HVq3+yedvd2MhIiycXL0udDClIDH1hIzg6f5rZeD/\r\nWO1+RyS82oxMtMZlfismHKggAPFW9wc0ZcjlkM6vYBMVbjp6CaEKl8/gXzIF\r\nYhmty5ooqqd4mLFSm4uWIpGjhd0JLQiAPXJ7GDPj8Qz+U1pU/5MyoqlphncV\r\nP97TEu+/iBGmTxd01hkfo6BE7aq+gC/ZbfxjQdaApwDSC/yj43jC0GC1I7fD\r\nnIl/V5YwglpNuatmj6jk/Gi8JBwWJ8IsYP0=\r\n=2+Xb\r\n-----END PGP SIGNATURE-----\r\n", "size": 4752}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.17_1671993561815_0.6344584502060107"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-25T18:39:28.242Z"}, "1.0.18": {"name": "@volar/typescript", "version": "1.0.18", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.18"}, "gitHead": "f102ee7bfc96792c63746a5dcb9742e3346d5cc7", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.18", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-xpH1Ij+PKtbIKEEYU2bF0llBRmu+ojjm/UA1WHNpi/dvsFWTIZcPniuqYEpPc32Zq/f8OPk98HbM2Oj5eue+vA==", "shasum": "c4ceb4baf8ad701b79d94cfc688fbf2c040610d6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.18.tgz", "fileCount": 6, "unpackedSize": 21819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTw6H707rQE/zPYUCgOL0mSTzJ9bhdZI92BOoxEeNYtAiEAlzn2bh9D/9RkRzGj6//VfgNx1DkfhfoBO3dtQSv+YX4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqW/aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmolqw/9E0gEbEpRDBoUwB1ZCJ5QlDa5jbOKbti3JLzVIFOmFWMWNPMz\r\ntqAfAOOdI1n+ju8QCRZjKVPPoGxijDT1KhXWiGABxbMCeL1gGXyO5Rs/ahP+\r\nAKTPkZgIKyspYUdNygnXWGG4w1R40WLcZLALm/zq0K3bbYI/LcSeTNRk3Dwd\r\n1FImE8EHstPVRXng5omKaFijAq/PvBeCIp9Z/8teH6+o/LwBL25FU4hGBFIw\r\nj7xGu7Ep8EQds41SkUPopBXC98bYhO/BQbQ8QYAQ9eaQr8C6YcC50j09i5tp\r\npzGsAdCvkFwrYbkA8DMOlEc/bG5Y8G+wRWSda7FEWMNzvp6uSZMZsRcJv4kR\r\nD/X38yRaufop6UuTfDb6ewW8cSXnj138mJtUj7SbWbjP6ZwQRq8jqoV42W/J\r\nyjqeCVrPzp6PPmC446tsdgkVNTl0p56k6suC5SelndogP6RRsTsb5XS0QXKv\r\nMi2PYm+neEW+36XgeZSHPZyz4gMx3fCJkeQKFIWQJaVvCzZBPoqj0w9sgM9v\r\neECnrOlR7Sy4HlQOd4Sm7FzWojkjfPKLxxF8xAkIlLn2GxNvdfGqET6esHQ1\r\nObmSdUKBlx35r4FDRVwM5KXXU4ewfP0t0djPi4NpUDdSWzEnJf4odsnchgjB\r\nEhKZpOOhBADVQKE4ZF0yZofEfVp9EUYeqTw=\r\n=x7or\r\n-----END PGP SIGNATURE-----\r\n", "size": 4752}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.18_1672048602285_0.05755507959749617"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-26T09:58:26.435Z"}, "1.0.19": {"name": "@volar/typescript", "version": "1.0.19", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.19"}, "gitHead": "02267fe83a5567b1cae7025e834cf084c8248546", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.19", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-S6n945uhpc5J1qCVXVV4tz4k1nyxWaoG+wqy9TYdRDazPHeq9l45WDg58g/ehblUWux85TZN8i3zdsLRLkFrdw==", "shasum": "e2dd5b9868c6233df5dafcf514d76f095c6c0233", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.19.tgz", "fileCount": 6, "unpackedSize": 21819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCxMhKuNyrhKoOLpQHvVH+OfZLgWObN2hyi5I0u650kgIgCppC5hr5Pkpsmh5GzvVDlX7FTh/C2iT54Jw9rqoSEhc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjr3o5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj9A//SjkqZpR7dG76DVCHSDCR9Mp2Sb6ANM54EYnUUE3GMVoztQzL\r\nb396Pk0SdHjm4EVTmJYfT7xU6E99HP76jtqjrb9ij1tPwj8Z9gUvRsW6p5A6\r\nSbw5AbjfLRbt8xcYdQtORsnWtwgrrUJOy7wlwfUsYuYmIn+r7/w8hyDmT36H\r\nyDltbwcCwnVJnnmx8K9afIU+bIvoXpFYg59Az38eTY7RzRq7A0STzoAk0T5I\r\nz86IXn1aD2J5rFeTUMLuJyg0SoeuWivGsdX2XimEOH6UXZp1+EszEizTABA1\r\nn+ru849QqSY3/OUIRZIGM6vFuv3Q8fWbrDqtZ7rRptoxdB4ehbgMRnlfEhB1\r\nQMCeizItiN3whurfuN4oIPWC+KVihwddn8b3uauvaZneYtFWfz2+Aezpoq8+\r\nhCxVwZ5mrwy/r9c0o3eXD2NweXXhcwb2A78rasKq3AN92rJzjqT+gL3Apgy9\r\ng2EEGBVvgYoVxOqE8ajKYJGxazI74AKXnZZ4AwUZwnxWlAUATers99hff841\r\nC34o0a6VUkp4kFXyIe/vcfo5MZRCQArQk0Rk2F5dK8tl95KjH9jZxJeHppq+\r\nT/vz2qt4+eFYlctct2bUWBZ8pkS2tYQ5We2Ts7hhcQXG49chDXwjIKMcJ1Vc\r\nJfc30iXaaxG76s75jAJ2gYH6e/jAQFwlU1A=\r\n=mTBI\r\n-----END PGP SIGNATURE-----\r\n", "size": 4751}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.19_1672444473495_0.26278558724616885"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-30T23:54:45.303Z"}, "1.0.20": {"name": "@volar/typescript", "version": "1.0.20", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.20"}, "gitHead": "e00f068e812677791c93efe9cf20995764350ec6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.20", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-98D2+rC4igqPL7emqIf0NtIx3UYXZ8xqILiP/ihwP7G2T4oyoGr2vKEOwo49sUzvgUvQl2AI5p8ZQ71mFJfP7w==", "shasum": "5f94c899fd26372b321618ed626a45d3c6de8585", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.20.tgz", "fileCount": 6, "unpackedSize": 21819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/wUPXm6oF1gQ8xVhjB8M2uuNZw/n+5tML0p4+MsksbAIhAOPjtCV4Q7Nbpyta6sxsPyyII3QmJjMO7Rwxjufa/ifS"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtLu/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVCA//cwQCdLuTFy5ugiKxjeJf7WztCtBY6ab50FYJmOoZYpfBrPHH\r\ndxGDFF6V1s6Zqs5syUOS/a579yW2U3299DUO6tmOiWeomMe70ZRtwIjyEmtL\r\nytw5TTDId7S3L4wtfpf1lgc3AQWybMumXJQeD4X8jIczB0ZL7Yy5byZXABAo\r\nDqpjN6H/cQos008glgLOKP1zp624Snzt3JE1IQiolvbCYZ5QU6b80pPEe/Q4\r\nGDpX2/MJ4hdmg+wRdeR3y8/BqwjQiZjPfOt9MlJ+2fBzspv+cOnVJCNr81DF\r\nPOSJnIPtBouy8q0i7PkxZAf061InkCmtsKnKpdK9eN6t2e/rZxos9DLTels/\r\nx8BH35eSrIjq+eZTmih5a6t0BV91dKVF+eQfDBv3doVfbaD1hO7TBckNUCHq\r\n2Cs+sRNrTq4mIUIPBcFjICjpWDgxUWTGSdR/VWruy6n5w8aQInYWmohO94VF\r\nMlYTC5iE3uZFCRpxZfqm7djrtH17lP2uHHVWn8zgHfnBVSfmZo4OuWgQdL+Z\r\nrA2Znsy7s25CxelfP0XwtXH/yCtwFWGCndvFJrzi19uEWodSmx9lDWobiywX\r\ngP8kc5CuBSmeX+soB6lW+oEqewUgg6Pf5CUerVAlchX65WFaR/faOcGUcjp9\r\nwFdlB2mq7Hxom2CjoMvfAhFcNUiSatKt6jc=\r\n=pKfd\r\n-----END PGP SIGNATURE-----\r\n", "size": 4752}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.20_1672788927508_0.9947974227335614"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-03T23:42:24.790Z"}, "1.0.21": {"name": "@volar/typescript", "version": "1.0.21", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.21"}, "gitHead": "72332760759e06eed18e62026185cbad70250367", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.21", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-wXYgCMLAyFMOsCEuJB6oDtMPpSpxhTbG+mNg0HtT1Ni4lx9EBdG03ALR4/FLXW0jJzGngK7fS2wbRFP+XRobiQ==", "shasum": "dce086e2c3e2b8ded8950264f484fb503919d912", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.21.tgz", "fileCount": 6, "unpackedSize": 21819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDq1TT5E7/I/0B1y2qWKGdOkBVFEfHKY1YihN7qaTfbywIhAP7aZGqLB3W6AUvqRSKGM5/TbhRLmDeJp2eL3rp8yEvK"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtgOeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0fA//UZuLucp1uc/vxUF9EG/zdlep4L6a63hZ7NWOFNTdCz5+XYBR\r\nFf7V8WoJ0i/3DM6Yhy37t92+zFUWBK4v/BCq3XysNiXDanlxXtNnnl0bqHvD\r\nDgKMg265yMHGEeNXi9VvGyN3K3l0W0gy5gx6Kn6akMttMe2o1hFyLc3IPDxz\r\nZln2nc2eGqg3Qn8Ypwi5jYA7y5mJ79OnGbtV1x/5BedKeymqW6i9enPODA42\r\nf0WykkoiqkAN8TKaaamq36MfkyzLoJuTM9IDMbyn025oeVWhtKzE5saLXf8b\r\n3vJxO82sO5M98yyNoglmLrOqrML1yeetuqNN9p6SVw6RNdjKiL63TO8forQR\r\nOxPPoDV7C7UAuXZtA9yZgyj9eb5m6dcaXDCQFR3wHwQCmiXnX8rR0lUM0yvs\r\nI5Dok9n4Tf/blnmCtzN+n/2OOsqausGkwQEE/ARe+kPULfJTSIPe25zLF8Qp\r\nBFkhDpLO122AvPR62Nk2FlmDx+t+9QLgk2G90auBxdJcRcrnw/OmLOYAc4Uc\r\nqoWOX8vkMDOcwQFJPhs4zlWKk64Ko6TQqgVkSA4dKma+s11YpbiGcNrUwEVJ\r\nmIcpdTnv4hS5SuFm3Q67TXqvZC+a3OUH7hw6RzBACne0c8ydWdPEdt0YfrAV\r\n+6CTIKONHQM7esXlNQm/okB3cjVHq+0jwNM=\r\n=ifG+\r\n-----END PGP SIGNATURE-----\r\n", "size": 4751}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.21_1672872862721_0.8847812778176758"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-04T22:58:26.294Z"}, "1.0.22": {"name": "@volar/typescript", "version": "1.0.22", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.22"}, "gitHead": "eed05323d6fe1d8eda712f63bb765ba57bdd40ae", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.22", "_nodeVersion": "16.15.0", "_npmVersion": "lerna/1.13.0/node@v16.15.0+x64 (darwin)", "dist": {"integrity": "sha512-VPyEicealSD4gqlE5/UQ1j3ietsO6Hfat40KtUEh/K+XEZ7h02b1KgFV64YEuBkBOaZ5hgvRW/WXKtQgXCl7Iw==", "shasum": "2738b7dd803f836a7d67bbd26b043b64a7bf50de", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.22.tgz", "fileCount": 6, "unpackedSize": 21819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAI+ZCPqMpNjuvfr+uyatWV7Z9JXIN+oPgSBNHdAjJLeAiEAje/vG097GBiGhaFxvXGonyDx8oCF78bVKC66TI1FUaM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjti0jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbPw/8CABkocUsJYiTaIl5pVa3Kbs9as9dUt4K9si2JsD32OURllb/\r\n2QcXAazO3h5mS/mKYE/oShlMSQscK9NCPA6wT4bm/98DNsHjpQAlz1+QSZDP\r\nQ5mEUF8SGUTf2m4EOcvVbSYdiu8EdhxE4paREkhhZsSZFCn7ftNieKVMnwsB\r\nCfnrpKL4Z7x1ImcL6vgGdRwPxxJgJwzMqq3/5D+39eI7jgr2K7xodEpcfHGj\r\nNhmHCcyaJ4JZs6262i7fi14j+HuYcD1BRmbmxQs8jDI46DFCEiR05Htyc5UG\r\n7jzNPZgpcMJGKhSyauqvItQByOaf4WYkm5CzFwnOeXZ7IQV3ZbqOJoX4tGxV\r\noJAyxNwtpqNytqVqDtL3JSo0nUPIK3XAAjBz5mRBckKxCgqSTXe0ofTUJ3VS\r\n1IoLTDFodd9iDomNR76X9ZZ2nVhZwsA/0TFMgw62kjBxo6+OboYHL9OeqPDT\r\nbzIWmf53ZnxVg3Lv7vprVfPXiektivG13s9a5Xl0b/xcgsjF80Y1TcogZvEv\r\nn+vJCT5Colkf82D5lae8LHNzNlrq65aytFQHZFnJ6Rfu9M0vvOJf794H9joN\r\nezRCY8g8CugMivWpX/TPTyeENVMzLs8D84LfNuVD5IX1lUvvDLogLm6lWyKq\r\nrGxpVy1LyPUzQ+OwUMBrnNL8TV9WWPSdsA0=\r\n=9Y79\r\n-----END PGP SIGNATURE-----\r\n", "size": 4748}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.22_1672883491271_0.4322652113261125"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-05T01:57:32.019Z"}, "1.0.24": {"name": "@volar/typescript", "version": "1.0.24", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/johnsoncodehk/volar.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.0.24"}, "gitHead": "46da609e8914e29642f4707dec31507ad51b03fc", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/johnsoncodehk/volar/issues"}, "homepage": "https://github.com/johnsoncodehk/volar#readme", "_id": "@volar/typescript@1.0.24", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.13.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-f8hCSk+PfKR1/RQHxZ79V1NpDImHoivqoizK+mstphm25tn/YJ/JnKNjZHB+o21fuW0yKlI26NV3jkVb2Cc/7A==", "shasum": "f934eda9774b31abdff53efc56782cd2623723d5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.0.24.tgz", "fileCount": 6, "unpackedSize": 21320, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGgMI/1R45NxEJc9qByr8AtAU2Z54L4aF3TXUbr2k50hAiBVPk7Yi3Tp5mqMJLCeTAkro4rYfFGHVwCT/4rlkbyS9g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjusWDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzJw//Ss8drwyU2Dn9yacqhGhu94lGhM3QsBEIVaMdgCUMzQFYbwme\r\nvXTbF88cqDne8pfWaDGD71NqNqzGY4nkxQRnbWZV0BVPUG3MSXR2SvVx87AN\r\n+SD4qTcrxw+o16ITL9DsJi7qPhoTvoO3Y8sw3M45hl1x8Kp4hV+Wc9aoJF2c\r\n9Rh5gUwWjvusFEgTrbN4JG7pRvaJIykmsR7XdWQEr78QkUIv6Q1LCSzxL97r\r\nQOg15lFkPuMEaCxHWu3UsmbZKkbl9JrM+g2nSwndVtbUs/UkenshrvlyRhAq\r\npWnu4MbBoKknMYrM4kqEs6ZoQHpGxgG07bLCgMp+VdtzQsHQFx9HrOSSEDvo\r\naJmCKvKS+jDYUczIV0rmfW7Y+MEZFhkswePmQWfdX4HSvn9tb+QBgCBtihm3\r\nD8UbTU8sSkpOKLWwlAOCggLISk2yhS3ikmx2JvlCDoP9O1Sr52wpVeM+qvXl\r\njzKg/15YJd+6sIyR/UDe8EcodBGd5GnJzs/DSuIH9cbrhpDQro5huveofFRI\r\nVAF+GSix/3yaM9XaWkNlNbv6mOrz24HgMeIHtEUc6SvvGofBWy3U8J89JuQB\r\nIHj05NjhOHm103eKLgs7DxsaulbMAWIh4Opq0HjekKUILSkymeP8MDeYSc5S\r\n8UCQFhCmxBq8GoduCirWFf2aKIqpRaONObw=\r\n=SUmU\r\n-----END PGP SIGNATURE-----\r\n", "size": 4660}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.0.24_1673184643701_0.7148942626099974"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-08T13:30:43.874Z", "publish_time": 1673184643874}, "1.1.0-alpha.0": {"name": "@volar/typescript", "version": "1.1.0-alpha.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/framework.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.1.0-alpha.0"}, "gitHead": "1cb85f17f5b90e40633e26710be7ecd4fd2994fb", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/framework/issues"}, "homepage": "https://github.com/volarjs/framework#readme", "_id": "@volar/typescript@1.1.0-alpha.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.13.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-toGoOv2QX8C9ObFokDUbz/CcFcf8nYwTEcXCCpsQZcgcrioB0Yss7uW2hUubVan+MGJT1EwzKPwAMF28mh7EPA==", "shasum": "03e16417569a8f66e291ada2565f68a45123d75a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.1.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 21538, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDx19A9IXRSRwQLNauSz6Fff8NVWegs0JLhQGAbMUkhOgIgOB3fzNr0UOtXi2EAt1PPKrAII+F5SlyO5MkI3rYwhhw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxiITACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVyw/+JDgC/pouua3H0hwXEssn1xKZzY/sfE0/+pvCjjoG3LF8TNBH\r\nCz/5YudheZc0Qa3wy5C5wrXNKBMc38N87kPHDh2Wol1LDqBbky5e6wImg2Y+\r\nC7fKLzmEo667lhOtL/6icd8oH5N9j+W0KhAZnG0s/Vyj02gBPIFCDG+LeTkz\r\n3qCWJGMPGT564zCCqpPsPCJ3SmWA8EjhX7nzzsFg/hKs1AK4rpPs4O8eIV3T\r\n+PNHSMByUWRX0Mn3aLi2lpLdJJs0JwvW0A0x97Ude1xECCxdWTSLMbT4A0Ss\r\nqGesFYJ7KumqfvV3Wqs5gVyMdcvqB0Tjge4hPkMj+PWvLtZPT8Gaso5K7r1m\r\nqmDFogJEGwOeOahHtETqMlcqVlnuAbgDkDdu20TMqLlTfKdYsMtLR8qx/4gp\r\nrJSHjISbBAOAtQ2F7/s9cG7VQu4VBdZYX4LZvB80TKY+HMzqzlsVbo9Izkg1\r\nIKs8xYRJ0L9+MDN99Fd6bWr+sNkpuWG1OnYqkGLiCQ0+q2RHQfGMFS0WFH3Z\r\n31YTXKcCP8dAP3P53pw2+T1qcSWM+nsuYKVc2T6/zaxXMmCXQAVMIG37EGNY\r\nAZ5X2Cn1bj6qPHFY1mQ5G+Z3I4JFVSom9sNpPZayT971r+sGH8tduJqiMYnm\r\nfFqZU950KZzto4Kftphn6c48bSvw6I7rPf8=\r\n=cZIg\r\n-----END PGP SIGNATURE-----\r\n", "size": 4730}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.1.0-alpha.0_1673929234962_0.44557736172277673"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T04:20:35.119Z", "publish_time": 1673929235119}, "1.1.0-alpha.1": {"name": "@volar/typescript", "version": "1.1.0-alpha.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/framework.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.1.0-alpha.1"}, "gitHead": "85666d83fafb0be442c55defb64fb0128b73bc0b", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/framework/issues"}, "homepage": "https://github.com/volarjs/framework#readme", "_id": "@volar/typescript@1.1.0-alpha.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.13.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-f+Cu79M8nuT79WwmuWWOV5wgfY/omRGXqMOKIkFIHr8Q+/uvc88EoitNAXIZDmDgR/nXKQcceQ4vrmX9K6bMtg==", "shasum": "732a6c5a1a9459673c9e5ab4a8b38be3b1b85a88", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.1.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 21538, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCf+x2UDgrolQ+vMn0lqg+yK/Om1G1AAK1e3FXJrew9DwIhAM4kNFdZk3x0UZ2g7c0lipMkH0ZwQySDjJu7aufuhNMK"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkn1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS+Q/+Krub//5/11G36ilRYD0ImaeckIM6b7EmTh0BVzFQF9co46pr\r\nzeYbONtAiSr2gHQBQYAO9e2sHSsncEcNxLwspwCfZ3PhVtWLaOH+j6YXL+7L\r\nxJo3zb+VWwu9LlmabnGWgzsUgfNCZ3iqY+BICBI0nVWSoXVGuq9C6lop6bVV\r\nFHyiVuntcLs+AYt3Bg3Bp/xOwRhn9HgAEx5Uo0bwDTbrQ43vxfdNLOh/D9iM\r\n9N1yGpzuNYH9VaIg3lA65/9MZBuXmjhV5Qs0+rA8Q0brS8bjkLEwscw3C4s6\r\nQS1H+yegZGjbsFEjUGf+z8IkCN6UUDAI7ndqKyuOBGKtJZGtx6hkT0XdJLod\r\n1/fkM9k0P6xDlMG1osujnY4z1btJ+Bi96W3NvSSiLYD2tt3OrDOYO4PLEFtL\r\nhPAfAfBocUl6R093V50AtU9vWh9NCXY7yhtG+dugnB0NPYi3NgXUH89hd2Me\r\nFIzndXI4zfxTqMGHSdaTyZGI4HNUEcJbuqasjXvIfm8jl3d/AYg3bdWzltCw\r\nJ1bAzU0/zWF9zLaHPt2nmpKIg9dSUrNnd9vcUpZFLwMrWDQwrAh27HiQiUuM\r\ncSK7eXnI+BPPDHQBonBF3ihF6vwx/mnaI336Ci1FGoDsJAG4vvX6+XKcpshp\r\nkVmPh3oWvq22pHe46Z78/nv5aN8XXnRoClU=\r\n=P85d\r\n-----END PGP SIGNATURE-----\r\n", "size": 4729}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.1.0-alpha.1_1673939445133_0.6651817485782854"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T07:10:45.253Z", "publish_time": 1673939445253}, "1.1.0-alpha.2": {"name": "@volar/typescript", "version": "1.1.0-alpha.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.1.0-alpha.2"}, "gitHead": "2a5ff13854187a26c1b5e36c5eab8b94cbd08375", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.1.0-alpha.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-iZyqWgCG0lcncWX4VeKKEgtYpOLyQNqMRiP8RCFIhyn9Q4Ptlq0eCSpXgTIY2RZWaaVn0PnlqQLZn1C5xNG4oQ==", "shasum": "207a61b39358f13b2a64b352b18974ffbbb8d1a9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.1.0-alpha.2.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDV36J/C7X27WEr8pagmrA6qtEKgpCO6O+pjl8BZ+ZEqwIhAMtieZi5NECchYcPfoM1Tb/1RwYMgttCWxGApK56tE4N"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1Vd6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqA3g/8DiN9AeuD3fN3DZpy/jq/5obiXALxCLBtUwarFlNDNzpB2uG2\r\nB8h0ocFkvXqw+btAPiTp69YPGTL8ZdNjCzBrnPEvU2XmPM25iYTM9Rb/7Nk7\r\n4u4FJkDwG0mzbymrBj4KK2JwgGKxddfe/78C0DeXdCAvgAoE7xH0fJzff33A\r\nSWHgoc943EANDnv4uXZvL5kCNTdGG0s7lEmI+/FarMV4N1HRWvtSXi90APP/\r\nXGbD361B+5NEHVrVfdaJIkMiPrm7bTuJF58lVP+vwHOmzOXFfldjbmo387zE\r\nrih7BT98Zm5LXzgzCQWsCkAvJQWTdsvLL0sWOZpgHm96KJJ86bURPZ36nzxH\r\nI3gW3Xl+Hzu2fa9FsHamZ3zF7fLlcv+5zDhY35L+ZsPH/6tmlM62OkZUTXrt\r\n6S999UZVhfPw79ucGCiV2YFDUmdsOXp6JDRBU4ceN2M7Duc5FxD/xUb122K0\r\n6Uzun/p+wtPSyyuNotpT+dY8cKW0e45TntOLBk0D1DVNcIsk5PxmM0VvSUpL\r\n2PW6inc0dkqckO6Spe8n26zpIxjy93Nx0h+ZnWf7NaaMAsJMQevq5biYXbUn\r\nA/3EeEMk57G9KlPTaUzfZjZrIhiF0BT1BS69nUIyq3oLo4tTWfPTBIP+Wv6V\r\n0c10gRdaBJ6ZgqsCeszxb/xO51JqTpK33/A=\r\n=aAMx\r\n-----END PGP SIGNATURE-----\r\n", "size": 4768}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.1.0-alpha.2_1674925946135_0.6354052869979236"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-28T17:12:26.263Z", "publish_time": 1674925946263}, "1.1.0-1.2.0-alpha.1.0": {"name": "@volar/typescript", "version": "1.1.0-1.2.0-alpha.1.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.1.0-1.2.0-alpha.1.0"}, "gitHead": "ffc1267f2766722751551182ce1e717be661247a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.1.0-1.2.0-alpha.1.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-4O08G/MjgaJ+KVl8is24XwgFFvXbRCI6oYH6kuEbRq9GOaKtgnkifdAOOV4tWRdlPaOsYwDYLG508DH2YMN7sw==", "shasum": "d97ec75ba12fbc3829ac30dd8a59feaf46004097", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.1.0-1.2.0-alpha.1.0.tgz", "fileCount": 6, "unpackedSize": 21721, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaHZxH2GA6sbcZFzCAJ1ZKysnVV0NCZGR62jw2F0aLPAIgdIFba426GXhCU8VdIzCCZV/KRjzNcmFYHXbaz7eyC6c="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2MJeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMDw//UM7InE68MyfAEZBqE3pKkONdnx4J7IusVPP8suatZ8aK3Sz8\r\nzA3K9RnKVvVRrlMtJHztsI84VRaxDt93f8GPAR9DU0vgFCC9TRu0oq9ZmaBz\r\n19wfKEjRMwUbQ0lrFRWST/ndkFvS+mC9P8/vN1WtYvLaJRCRpArJE2KAjXuA\r\nY7cY9zPqE1zgsepcDA4378BkvsUJ5ozJHroz82Kjj9GK52aZrZ2SrqRQ/wwK\r\ndIMjEdWs5tanfdddhg25lc3d2wnndhO+FJbsNvtDohQuy5Jq3Mb4fvOcC3c/\r\nN3AAdYTD4Uic6+Hd4W+DtKTlbiGsfNwFt0ohDodk+7XRZCu5hJrbqxggTfYj\r\nBShnKrPcHXg/0VQo+JEO5kyEilAwDZ1N+pfxAd8mEk8eQBZU1Mo7Zy0e90Jt\r\nhvqJjHEUKFHR3f0NbJ+c+NeBU0nUyd5vc6qFCBJ6Qobzn1g8ltsDM+UN6hlR\r\nc9XUA3GUsSOhvrLTTBste+ITaMZnIeRmugDnrH+i1j56SfTHL7GKqHABbsws\r\nlcYy2OydV1bQL8dOffk/MQKkf8Ri1MPvNSvfeEroDChHZSBATvzSjIG5Viat\r\nhSS0vD/do/FzLyuptFDnmP1hjtAb1tVWkkxa7ySnc4nZ19M583vriKmavZv3\r\n9dvJQ+TSYs+SplkZhmeoiMJpYysQsKfdgps=\r\n=C8jI\r\n-----END PGP SIGNATURE-----\r\n", "size": 4765}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.1.0-1.2.0-alpha.1.0_1675149917951_0.7593605931906693"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-31T07:25:18.094Z", "publish_time": 1675149918094}, "1.2.0-alpha.1": {"name": "@volar/typescript", "version": "1.2.0-alpha.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.1"}, "gitHead": "08f0fd7c8a0ac7ba92de978c3ee7338d1919ebd7", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-W1dWhJyDYOEVYpWrCWpyjEegCcxcEh+K8ECZrOsyNxZEWTUjmvdl7f6Rz0XzoPnEpIJCHDRB+YMQLfn0ptK/6g==", "shasum": "fe1cc1dc5ae83e0559e7742a2a10381254dd6ec1", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEJw9ovegWg7eDIbu7Y/w2JGDWh6rhmAqy+a9wq8PMumAiAxynWnsNURvzAdb88KTTZ6C/QNlJo82kKhtTzpYtD0Pg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2MK8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJQw/5ASgqFukENj5ndSFEVi1i8WAtVs29O6t6ckdZP0Iry/9zn+TD\r\nQP2AaSr0iR0hM0TXiC7Tw7vefij7NBwD9HysptA3prvXb927TFyw2PuR0FGW\r\n1VJXOV9aAxS4kkOP4AWtLDgwRshEl9FfNdtfCmOVZ3UtfFc0IyUeKGgsApF+\r\nN85KFUYg0xij3LBS48yMbemOUKxqydC7UUzjr7zGhoRaFuTG7zIytee2YZqe\r\nZyzQCJFYapQ7/lAU9wV8UDBgeBghbmSnlTL4GQV4v7uv4la/MCVnPKogYbgq\r\n7KzcEjCIYjk/yAAq0TNdJ1tZUw/3muxb64SCF5JU4s8peILO726RGMbh1QQi\r\ntG0716dRcK5oQeI0/FOLsk+x6TnvLXkcHSt/WA7+Vq2oP4N7OcucmdjYgluZ\r\nmgi2JgMSO9r3kCdXcaxBkZNsvR07YAOx/j1IJHcqwktm972NuvBdmW3poo2E\r\nfCGQLj6ENVqK0TErfdePtcsWz702/CygBmcwyOawLTQYKgsIFRXBKdxrcj2h\r\nFYZki+U1e3v7JLQZ/gspjGILZULBkuAF8PjbB9/hNqXzV8oVkFFwdzpuRB+9\r\n4V5Bf3MLJVJUbvJP+10qXusEzFMX60NYX6lkqeHoF69JGGON53BIGs54fXnz\r\nLHngxYc265xXACdG3cU5h4uouci4ne7sXpY=\r\n=3vuy\r\n-----END PGP SIGNATURE-----\r\n", "size": 4767}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.1_1675150012676_0.9765844167254241"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-31T07:26:52.839Z", "publish_time": 1675150012839}, "1.2.0-alpha.2": {"name": "@volar/typescript", "version": "1.2.0-alpha.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.2"}, "gitHead": "cc4da3728cbbc0933c9957c298d0a36090fbedc1", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-e49nxpdIbvwSS4LJbMcy6pgxHZnH2yZLPD+km/g6PD1YFmn2vwrVapAe+IvGuDYti/cJHao00LerDnpc41qibg==", "shasum": "57f163ce2909ca19b13ff93d3bd07d9beb524e29", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.2.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyh6xKyRn/g8N4bJIEmyF3Nm19XtQW3ZJhLttftzjtdgIhAMwaRpT4YXnaV8Dz+ph65f6Kh0olSGQV+sx2F66Vei8i"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2M1sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokdQ//UeJmR3Vwuz2FRV1zgVoM+6Z2qEQyp7QA+IecoKNqir7n/Ewl\r\nkjSPfoDHhDe2rBkWAPvQrZSjpb+RUsR3VPiog/NOTy/tuqIfOh+RxXk7iht5\r\nbBlCF1w99WMimZ8Xb/JIZdfxgFuc/PKd3mrKu06z98ZWrJlBBz08fxCjloiF\r\nIR5EWm6AMrWMSVj6oi63qG4DZLQGQupJU/xMNgKiOXPyDSRI+EZL//RGbPP3\r\nq5A47LAkrgx79FbMq9A0IU7VZufuWoNgXi3Oxuq8GbiyJH+rxP5sK4ByXgME\r\n6ErU6x62o92ykXZFvHEq5miiQpyWv84wOZA4O5exYrP0h7G+qxz9YFRgqC+s\r\nVdvpgNT2LEA7yiBb79jo7MlXgqoHDrUGyn8ebm/6tEphZM8t5WHHnsl74e0a\r\naBpOwdaHq/9siQoA+6ZUHd+ZvcGzyFanB/Qbnun7b4bhWZqxLrCHv9xTxjA8\r\nd1CoDFKOMeGMupXyGGhsizzV9Kv4Brk0TmLDPlouZPGkB5LDxL5pEwcbbJSZ\r\nI9LWFUckhMEEDu6f2krUxvghDqt7iTeb8NYNhNnUKsj3GVVgdlPIynngVBGC\r\nwLcQ/bHPTrJHIswFmuAsiJYtX/XcrCGm8TKsgihuizgQd4LSo+P324J7288U\r\nJWo/GMH+MXr7qyWgpLXB52HT8eugOPu+jsQ=\r\n=NAFI\r\n-----END PGP SIGNATURE-----\r\n", "size": 4765}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.2_1675152748774_0.2714264519022105"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-31T08:12:28.942Z", "publish_time": 1675152748942}, "1.2.0-alpha.3": {"name": "@volar/typescript", "version": "1.2.0-alpha.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.3"}, "gitHead": "651fb9568432d0bfcab86fc1cd9fcfaae87e1d6d", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-Y6lLXeM+SbUB133NYDETB3eK3l8Q6pSvtdyvkCQHoc32+mnFVnVp8gavfASx86lv/FI3Otvlh8YVA4huIcZMyw==", "shasum": "5f2184db22f72ea2bad9925989e97bc3819a0795", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.3.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNTyi6OgdqvOv8VgdFmL34XmjRrAezllqpW32jdKah/wIgAvPoYs0blCwUZQSw/Dm/sMNr32Epu2l6uls8Q3ihT4k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2RWzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbYw//TAQ1tWaQa3hMkq2QVAW/9HxPq88BUeTwdEfxxlvwz+ATtIVM\r\ntnY02WaD2EYkG8VfVvncdiWNZFyKE6dWgr8sAKMhSSiIB//ucl2BUgeXani+\r\ni0r38EI3T2aeH2P/YnDrftHoGIPFoiFnKlsGC7aw3j5yIdOwEnvmwv6IP90I\r\n//KWmJSr3J6LLRPA0Y5raOD+g9AneAvOUjEwMfViQPyXoxKI3eNAh6EMto6E\r\n+E4N0td7lxNeP14RWLrmXuszxmxNnbNhz26OxMhjBTkpawVEuvQWgu9CM1ab\r\nvFlKp+VBARHbV4/wp8/iazVhYBOCGYHF0d2QvBZeAxiz19wUp8Qdtt1ez30p\r\nVCu/LpoKpn1y6Cgw0OpSd+7PHMBPA5wnUT1tT1H+t4/6YbeyKyg6ECU3n8Fa\r\n6mQkqwBQmZi8YxQvOyyNcCh4m2jT88VdmQ4l4c/Nj0COscV3EBrQzkDDU81a\r\nfl5V+I/8ZijX8pqpnlSHDido394bQ38nqS2A/sYdLO3gD0zh6BObb4cQM0YH\r\nLH/c8PwAdUJdhlErkwWu0QJD0p3VM6v5J3Do1rSY/rq9c3q5b8Nj+CdGs8GE\r\nNe79BNj7RK4CwdNZAA3vvql6zUlZzKxc2yMl5JqtNVV6KoLsYoaCeTi4hGdr\r\nqHCCbHNok9SAZFTw4mHANNzvtCZ3uAiSCeI=\r\n=E8/T\r\n-----END PGP SIGNATURE-----\r\n", "size": 4766}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.3_1675171251621_0.13138736674892537"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-31T13:20:51.812Z", "publish_time": 1675171251812}, "1.2.0-alpha.4": {"name": "@volar/typescript", "version": "1.2.0-alpha.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.4"}, "gitHead": "cce402aa84061635c951e27c4423408d0b56e46d", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.4", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-BLKd11GsLNAxQYCkek9ogmJm99lA8jkk5/exSOlY35RgjXE7iZnTFko2Em5Y99NmKeTLqSYZ7rMzTCURQux40A==", "shasum": "165f4264e02f26c4bbbcbe780c179e3705015f43", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.4.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCknBHWcWEw5m/g3Bn2U0F3gu/uOPCnW6mOdfvW0BgcNAIhAL9SFyQKbMBkHryvQ55F1oLzPKDQYa+FYYE+qFOEr8na"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2SEbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxdQ/+OmovjVpvvqT86iiZILZWOvfdTeUydC1XA28PKsjNXgSljdWg\r\ngq1JyvZApoimNBzqzB+vj1iG7EDgvHYsAnWKnvuNWKJ6TE/OEb3Ya+lK3Dh6\r\nCdn9miWNPUJQJQpdkN5Xio3r/O1y4yEa8+JvqWpkw66Cv+XDn4Tb0S89HDN4\r\n/rmOuSp6p60JsiMTJu1f+ST5jJM7fO5APGrh7oU5AzqOFf8TCBNdCjqYJix2\r\nh9vwCdI9Or+hW2glhscWFa5oUbMEs4eH/0DsBZIIQ0FkRjKJh0b7a4zzZVot\r\n4KcgLtnKTVgePUGZhXtj2n9XNPkv+CMYdwXiojQrquq1Iil/Wi1Pc8vAmp+k\r\nW5tHtwE1itV3qiOdGOYm28A8utHDUS/73unTrd4vUvbGLhB/k0xvZcsdi6bq\r\neWGlxYcXV4L7yeKeyp0CwcBtOO0xGO7Nf3DebQkJ5vLNG0qVvNsmRJ7YlfVG\r\nNLQlHaYYhbTD4YBO2pI0nKyqy04dNXneOXwdBIAq6J7CIDIZxHvEWClnezH4\r\noQy5vUkQUuPj44dHYipqh8v7RXopnXvFkCD+ZYPp/Y9jh1Ixx5zWwh3UzlPR\r\n+qyL02K9S8SgiNZP3asO4r5E/xtocgDrI3iZXFOM2nXmJQ+vsSZuZN48z8nF\r\n8dhYzxKhmEcwrIGCK6SwhPCmotNV5GYWgOs=\r\n=BJnD\r\n-----END PGP SIGNATURE-----\r\n", "size": 4766}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.4_1675174170984_0.6942626320574239"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-31T14:09:31.094Z", "publish_time": 1675174171094}, "1.2.0-alpha.5": {"name": "@volar/typescript", "version": "1.2.0-alpha.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.5"}, "gitHead": "a31d022987c454762d1ba3c9ef8e7d74a5bca06a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.5", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-63fB+DK8WGSD8G/as2q8C/CYqg1FVT4eMt8Dl/lh3Om2IxZZCLVMDwuPE7t3IZdLH/gf48MTADQGnekSaZatPg==", "shasum": "7911dc8804541c519fbd1f62cb1fe984c2c0d62e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.5.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICn5X6r+wp8KKucuhFq/yx7aYCIcUymPEJlCl+/yB/8AAiEA5b6E6Tg46qP9IE3Z8LHDVp7DIKcS1iiHSLf5PiQW0jM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3MsbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9uw//b7HlcGL/GRMMhrstvcpfRk2BAYJnbB/bVUjgxiBJOa7C105J\r\n885Vdi07rzNdcR7+eKEjm0Yj8DzTomL5YnoAAHeuwm/hiX3jvp5sLy01Yq9W\r\nDzftyQ9VzqujDby4vA894PuhbnLeR35o0p64f0xZ/LaZmDtaGo9GnbngzFUH\r\n+mGxAiPULViGeacFS1lk8eF3BTpeN8KMDOdtxSBNuK0Vw1BUliL5iYiEQ9Yq\r\nYrQP0Kcp/IpLDu9xxdA+C+2w/aQnsY9AzaCM35T/n9XjMIvLyA4m5zyhlCJQ\r\ncpSoJdSHRSZ1qoq/oyDYa5cte/DQFS1VT7KB0dG4eOeURoO/ZHYa/lOJ/Ccm\r\nSTT3zSOi/NQssoy1RRdG3wHM//xF5MpQSzhp1FfDjRX1EAaCDERIx8c2S2yn\r\n8W4iWaOocqJxErZcM4Oy5EHVASbAnbPXdfqdMfvPjkFOcgJzxYuuIwzjsgDG\r\nUnsbU9UvLxq0Qk74L7Cpp0Mjyes6PF8nfc53nutRgOkW2Xa+Le9OSosgp1ZD\r\npWEOfz2XXit1rnnoCUu8ckCyNUgQBB/HDF+M5XFPaNqR0J12u+YeDYZHTRfe\r\neVcJAd8CVdVDK8gyHsmXM5xMg/LkC0EDwh8y9JajIHu2+Kghe7xc1Yk+atId\r\nfatdqB5QcKKIOBdAOIiDdo0O8HowVhysht4=\r\n=AvdX\r\n-----END PGP SIGNATURE-----\r\n", "size": 4767}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.5_1675414299108_0.8340584097083079"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-03T08:51:39.264Z", "publish_time": 1675414299264}, "1.2.0-alpha.6": {"name": "@volar/typescript", "version": "1.2.0-alpha.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.6"}, "gitHead": "a3eb3ce03430eddb58be999bf7799d01cefbf787", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.6", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-08hM/jJ3aXxeBRBTerDNZNB19r0F0Yylfb+Jufe+Hz5NiVpLuzMRYo13xfvJHocOlPngjhjxCGHPhgu6fvIfow==", "shasum": "3941847dd0718e1c4284ab7ae33b303238a0cafa", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.6.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGrErL63S19l+sFYLp6KZQrpTOLX8Y7nALy5TRV8VCucAiAakYJeP3RFqOxsthUn22efZVoR5BBVEDEqfIDpFpPcsg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3WALACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYmQ/8DdtbLyAo5odgPQ5mgmL199Wowdbd1cpmEFzKM2Ec2O7uSySz\r\nNzo6v89qev8YkLNOzv0rz8HqwCcUhmhUAyY2l/FORCyguErvNTbQqVp0ePGj\r\n3nqrlhac0I0pNY/YdrKxy2kHZq4U8EHYiNl2/Lcf/gZSz1s0sX2WtK4XB7qM\r\nrlZu8Q2RX/SA5FwjDgE7sYon1312lypAVX+tSNQYDKa51DD5qheBTRXAWFcR\r\nIGNjnugkz2u64zuAaFmHgg6n3jmUqMtN9raFkK0LYdwndbL7hS6DmpOrYNIM\r\nkN9MZQDOZHHRXjzjP4KGEyBuFaDfwRs1uLOPgWiksxM3cV4Ts0G9x7Ts+RkF\r\nFXcqv+nvaFzjfnZPDQoddLMYq2YeXv1wH4pp4kZ+YUpqkYnlaPNG7BksVcFK\r\nMCd493MLQaoLGCByHr5wDgoRRzrdC0Oa562mRg2jfrOe5ojHAq/tn3KrdWzY\r\nF8NaawRPrXLEgmagHtgAbwybgyXmr3AY5QmH4Gf1rEAUap5TUmT6F4UAOhv8\r\n//sY59n9fiFZGzu4KnSpOoccByjQDaPpGGT8e/K2X6f2RG4Or1rFbca47c5g\r\n1/W53Jr6bA9ENtT8qb41bkB/TBfV1t7kOqfopTbi8vRKEPPQtRtppOHCyAhB\r\n/h3uHlYej7VMFNclmHQ6bo1TngXr0kCox2A=\r\n=Xkcp\r\n-----END PGP SIGNATURE-----\r\n", "size": 4765}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.6_1675452427487_0.12280540989526756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-03T19:27:07.645Z", "publish_time": 1675452427645}, "1.2.0-alpha.7": {"name": "@volar/typescript", "version": "1.2.0-alpha.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.7"}, "gitHead": "5811cdb90f68a2db0166aafadcce3f953156c9a3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.7", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-f4IS+P4zwGifO3B8f6jPODueOAx4jEdUdgdONxvttofB9XSg6J1SBN+7VnzKE1FeJ+nnYiIMwotg8QN5z0dNEQ==", "shasum": "5935d831d4f0d68b0b8d556e62c15de397fa3ad9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.7.tgz", "fileCount": 6, "unpackedSize": 21705, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCe6/WfkUJLckzwWRuZpA+SGplz/auOQoaE4YrjvlBiMAIgFwKCMKnobDxDanQHNSeFrNnNrK6M+W5Bc4b/CvI1PZM="}], "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3WJ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrclBAAi7AehSBp07JgOsPKk8OlYe0tzLQyvRtyVVv7EAR9NIFpu1r+\r\nzs53sYKAIxMXaOqWigRGXPZtvQ9uTJACzMIAo6e130wwpYjUFnHWQxdP0A30\r\nDYssfe2PVDPrxtt0HKa3OTlUIcEALbtRmBuvGD/aaO9QOIVqKtw3XbhCeeIa\r\noMLj9uYaB2A2HTQx804qdc6XJQ2Ho2eFhV5F119+WnZyrpzBEZCZD8wZ3DQV\r\nLA9IZJjO63YqQPozI2EroS02aK56M3tlHlL4XDoHb1osThMVrqyWDp/VkI14\r\nNo3AFHaN6M6jtqOwsTgyRFktJBI6EYBRCgXeT+xNKbbVCQgnG/2ndcqbAKxQ\r\nITQBsQwNO6INm4fLZGcd8uRN8wckaw3Hm7nx5T7P1eqmAtD9c4DL8ljQV7M2\r\nalbtZWRJYXNjptXupiZG6S9B5KVCAu8rIzd2tKoPm+d/8w4hgZZPaTYF2mgq\r\nGlmRU80UYZzIwT+lSbVg7c7jjpCPgCmike/S4jm/ujbKQ3FtRnqp0vG+EelA\r\nWkTgMa3RqwrME4nKU9YjUvqKZ/Wd6Tp6rfsKQYk2wyGZH8OCuAqR3mI28b09\r\ngum86dyc7xZRaAA3De9IrG1I9jcCD/zAihECQ3V+oZTKBJcLVz5hEEae5rXB\r\njSTg3baAeflKLs12wT+CkDmu4aqK6jBnPkY=\r\n=UpE8\r\n-----END PGP SIGNATURE-----\r\n", "size": 4765}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.7_1675453049019_0.003044982383998862"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-03T19:37:29.169Z", "publish_time": 1675453049169}, "1.2.0-alpha.8": {"name": "@volar/typescript", "version": "1.2.0-alpha.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.8"}, "gitHead": "9437b4560f35d2a153a9e2ae0dd0f64285bbb3b3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.8", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.14.2/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-AodarfkPzhEigz/o7/cLUQioLWa3R3S4ilWeu7sNAGwnQQKBc3DZVuA2q3muTjefJi/gqd0nCCrUiJu6DWkNqQ==", "shasum": "7b11f7ca78b729f6b4ac5ce668415c2d85117e60", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.8.tgz", "fileCount": 6, "unpackedSize": 22154, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnG6KBv+fTrbDpX29qTeTSBsWGOMn4pueqwPSBDjN7/AIgXV/b9vRcsXJv38i8erglgtcKPUKZgsMJz1uPDZ9HkF8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3XVNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2NxAAjHAR9QFHhBrxozT6g8bIROrMBpfgMpotQwXRcUvwLkB5sSYG\r\negXldURHvlEIZqeZEleFsz4TJBPm8mTNMy4UTPmuqRa2aGH2YoA/DKI2rQqK\r\nm7jgM9NECGuJ/+l795OVzbarCd5ns8aXPwNG/HA0iZulkbH+hBkRehTrTysJ\r\n13PnpAn/gFmtTbiWCqvT6KtH4jndJf++eN7tGWHh+bYek/0E1GvqiFj9kot+\r\nWL+7HCDtgBWQKPcHgkSg8KUTM1XYl0zIUImEACwnWrk0+8iUwYTh6n754trU\r\n4Nye9DW1P7Jx4LPlYyFGf7WYcaeqcgc23HFVpaX/JsvoftSpK0mJej+ZhbA0\r\nhuR0YnEZlCG7jXwOK+zcmRS6iTtHpPjRdE1CreRI1iOb4H72M4fX2wH7umyF\r\nGn/VXG9PCjhTLh3PJmVaULxsIwNRrq2PIHri9B0AL8IrNTE9FIvI/UZe6LiR\r\nYncdCSivv/xI0x9bC8+H518hlKG4sgka2N4373SbKiuSqPGRujNnGoYvX+uB\r\nZ791TvxJ9bpISrzM1F4F+3If1ZHohmMuR+XswblywOBUM7lnOUtS5xR9xBkS\r\nQlvNiL08ZD7KC+OnkwSmeHhpPusJ4WXX9z41aeu03A/uFv+ozw43vrb0HZKs\r\n4S1+XuTXmEerOGjqZW6jTObzf7cGN9fPIEo=\r\n=dMaV\r\n-----END PGP SIGNATURE-----\r\n", "size": 4801}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.8_1675457869745_0.98314585726942"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-03T20:57:49.901Z", "publish_time": 1675457869901}, "1.2.0-alpha.9": {"name": "@volar/typescript", "version": "1.2.0-alpha.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.9"}, "gitHead": "ac8ebe9dd13bb6881769afb8612d3a9489ba7799", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.9", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-bQXWV/4RrqASubEwnlpW/lQ0y28cb8/gzLgzgbBRgthQW3Yn5GGC0weL3NqHaZ8oy6HemOhvdL004S1DNF9xeA==", "shasum": "4966a8ac2850090b78b2e2e522bd7dcd3f0f9995", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.9.tgz", "fileCount": 6, "unpackedSize": 22154, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrLNrnA7i8Ux4eoJnMvVSTzHT5AvMsVgTwiSzekj6y3AIgMusFBpkD3grNiHjbBNswVnltPxJvIA90YxI56AlYWzc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4F0+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqOA//c+I1NvGuYtNXyGqnkBF52PgCMa2xjrT4AYGd1BuK97+b2wS/\r\nc6z0a7nNGLcggmIobkxZh4SAG2dSkJ07JSHGZwWsVBS/qRBT6gwOvPAC/TYG\r\n0j/eVF8U0V33fmGSZnvR4VpxW40A6FmSiyqnx04BozN9rpzQIng1K2N9yoJm\r\n1zyRYZBm7LoTRJubnq0r/GzW6dUoPRICVqjiXGAheNPjzTmBQPLLNfAjrW9s\r\nF5gGi35SNvMlvdS4L2l0LUFn8Cz0Liw/tHnVrBKyo8uJE3zKHafBJhmjQdCA\r\n6lTzhyVfTgW2ne70YtmDwh23DiDb+/e8aQ8TxRePujGxZ7Ll58UmohGhfWqA\r\nuH1WRJ30niodKuLRHd0LR7jawEb9RW/NnlWWJMH2sgaKz4DMPyN1mxjEGOXC\r\nMfL1pwOgwBdNNtMb04k0njUmCgPIHEj7kQUHjdpYYYAJsjJsnUKmfPXcUeTA\r\no8CPwp57Xd8do88JdNnQ//OK9aXOVTUTsfgUR8hxjni1GVABrPqibWAuH5Lc\r\nJhAOWIx0+mNFpj+7rGjZCv2smCv/VPiSlVe4aUzdhhkKQAHzywVu6C+SG7G7\r\nISa2E8NIWFdQdKHfg9NtWYAS5GuEIvZUErbfwiw1mU5QlORrhmCggA4TZoYB\r\n8X9Zi34uf6Dx6ld3jkZYBp7O1X7099bTCJk=\r\n=v2zb\r\n-----END PGP SIGNATURE-----\r\n", "size": 4802}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.9_1675648318662_0.488185153814515"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-06T01:51:58.819Z", "publish_time": 1675648318819}, "1.2.0-alpha.10": {"name": "@volar/typescript", "version": "1.2.0-alpha.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.10"}, "gitHead": "c0b7580b14966d28d7c6df5da9baa41111f9f8d9", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.10", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-KTbjyvo0bb02LB7Wpig5SKXoW1O/824htgQfYlfUs5wwGFV/oA5ev7OVe/X9Ws3XlKMyJysylVuzMq4CeHaVJw==", "shasum": "b81be46a7d4c5376a7301a83dd51e7361a385fad", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.10.tgz", "fileCount": 6, "unpackedSize": 22156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG9INFeTYMYY0nIOsv4bsV+ruAAyrrEXYYK/f6clmlcWAiEA3ZP6wFMKuPPiJ8qJqwgPK+nfny5eifsLtDdZ0nyr1Z8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7HXoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4fg/8DcidxiNDOzOO0jC3CmuecIDSp0bPYwGcZhzhMkNu8WKFAKkB\r\njyycJKJ3GSE6d64BjXNsNKs7OgzNJWNMyw/OsGwJYqe7hWh0lgQ9rR4k/8gW\r\nZ+UdhWypMqo17XFMu+N01TsN7BD03nfBIA/7YdbgMMqC1vUW+zGvjYbRut6j\r\nucW/ZrFHHX2lnWIfXrbsiPeQB9QLHNb0nFx8LcMbZTqtAXMuMN8TWTu4pbt8\r\nE79tW68jyJztG0cmm3lfl3MLakswkW3mh84dusS3tsyEIqTvlLjuIxCS/oLU\r\nADNOWriVdnB1KZGrIULbU+aprqESQVDUOLHx64FDkYDq1t9etWwIETuMwqcR\r\nZwSZCJIrpBD/qjJOkyKltg38qgrq5Lx5eNqDBHVRpq4F8VI0azcJGmpiXjIu\r\nYqeCwZlujHSYIlzrOGyoxtZsBk7h1vPFKR5xG+w+yjcBIp+E1e/pIG6dDvbl\r\nK5/4a6zfCPyQLWJkqVD3TeYtweXoiDsY6rqBf2XqrDrEtc9c0gBbgdlYUZ8L\r\ns+ayJiiu279U7W4ET9ae7n7ZdNzUR2MGlv+J9tDPoL9+pMZBxi5J4vM2kZdB\r\nLUfi0vY0SbUB6YFblibxurTnQA0c82dFFLgsM9CdNIsPIJQZg/1j3oKLXkae\r\n+lo+t184UeEmTbDjXIcTZbqpRD02deo2ij8=\r\n=xKGf\r\n-----END PGP SIGNATURE-----\r\n", "size": 4801}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.10_1676441064086_0.9496804836449511"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-15T06:04:24.237Z", "publish_time": 1676441064237}, "1.2.0-alpha.11": {"name": "@volar/typescript", "version": "1.2.0-alpha.11", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.11"}, "gitHead": "b9b3d607df5772c00edda17cfda05281c3e7c06f", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.11", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-tJ20326E/Xi1lvvuWX57boVJtzhStNF3HjBu4orjl9PqCXUbhqWwP+jRYzyb+nLbHqGPmEBvHKYjAO3GsJ/YXg==", "shasum": "7331e363f1b44caf23402b93afc4f4345248b262", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.11.tgz", "fileCount": 6, "unpackedSize": 22156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGlEbg+aiXgduowj6x95GxP4SLi/ZKOCWRL41SPonQ9hAiB4Ysp1iAb7p3oCYLgMTaZCbyMlapR3xXeMpnbz7U8W/Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7QmtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVmxAAjON3OvauQiPOnH5jRHAxu8ZI9+c78Puu+roPztU9EUdOd0xt\r\nKlIziMRTPEyzuYri8WORv9W5KVaX68cI4JGyvDc1fr40XcD5uNeHDtIdzBl8\r\nAreBdqRLs8I47c6Ni7TWzQBePcs5KZOmW12mrOzs2Cip/feL+kEpAnZMBRCJ\r\nopQ2XXI1DZFrotpCKtC18dKrTADzNOZTZT0mvVcNcXYC7ExnvWdL9IXiZLv8\r\nEv9p0VJl/VkeCMFxxVYZniiNrv/Lw/ixmK1LEj5XTm5yD6adCvM9/SenmLqd\r\n9IR/ULPFhXTg78bNqNSigIaNr1B6DAUIaA3nDWLSHaO22VakwXOfIaseosgR\r\n0qrPIVxzWnEwXGf2y0xF81NVkYalx80SFH0qs9EB81ionHxZLwGtq5cd64Wi\r\njvUAKfkaOzxPVy1Mjm9ksqLlDWxkuGLMkmRfGSwKxPq48+Dn8e+32wZpoI15\r\nkJMyLeXRW/ow5Sr26G1QNBMn9mSiYabVHixcELWpCR7E3XE/FaDKVbJbV03G\r\nDy4P+voEHcOZn9t5gmxErmwTiD/YAZGPv8wcb/Ea70oPXd0CybKtgSXWvmSe\r\n20Sq6uut2Hyxk5oAEoS3aBUJO85qvsrfrTKnw4U+36Owoh75Mm83jz9UeWQt\r\nE+kVkAadD5tlBOYabTraXBJ/PRvX0bFLGfw=\r\n=qxTY\r\n-----END PGP SIGNATURE-----\r\n", "size": 4799}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.11_1676478893199_0.5231186287179168"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-15T16:34:53.418Z", "publish_time": 1676478893418}, "1.2.0-alpha.12": {"name": "@volar/typescript", "version": "1.2.0-alpha.12", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.12"}, "gitHead": "cac0a7e75c45bd217f98b8d2f66a03860cc89e0c", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.12", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-l8l+P5TvxJjH+9FxQsIh8tyNkkS5QZ3//Csg2WOfGHB7aLZLeJyaBimVkNLinzzfbGIYf1ALO3ituA6ByPFg9A==", "shasum": "ec8ecc572945fdcf925b80010312b0945882b478", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.12.tgz", "fileCount": 6, "unpackedSize": 22156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNAR7yJGC1XxOO0G8b615OClDZ3nFta9dk9Y2O88Px7AiAZzSAfn360Up5r1L35/zJpjeLQwwzn81jdqzodsfZBow=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7k4VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7BQ/9EMseHZnPP2pJ09HxuKoPn9GHgKx2VYQp5gDOZJq9iJJ1gOtC\r\nWzRrgr64ZHENm41v1E152QYfO0XcncehI2/+WPpYYAhkaPLZupV8s4eY3G1r\r\njAqgcneUXbJDCh6NOHsypfNx3Qr5wLnuKGVxfC3bl0q3ecd++js20lOfgkUg\r\n1VghmfZxTLEsDUVGEXNny2yRZG+VZsuPUzWTW4zkXt/v31d4+DfSyYDt2UVp\r\nTT2gFhve9gJJ0Se97Y9tEvbjnQaReygVAIfHqu7EwEpn3ytrnliudPVE67VC\r\n9SmIjteZbUTd6iksz9EZ3/hZCbChsJd2xul2fuQfAb8HlN/NohAQXazTfvAV\r\ncdlQiZWnie73g1DegbNyTmBKUFTLEZSt4k4TlEgS+Caw5f7+bYJAsQqLnGy3\r\nOK4aZrDYYxRDrf5EykAFASWDUi+3VU+hVcD9Pmb/vorh5eUqJr/lXXytQCA9\r\nlCMF4brCwpV/XKX0pbY4W+bSOF4w+s0BQH2+zvhaONmvOHQ8RGFmXTMpK342\r\nqaZ893JJcVJuCQHwBd1gAOwFxznO9AbLJuncb8NkDttQV9oeQ0D9XersGMhc\r\nY/7ZeJQu1Ig9cTwa0UqIBBFOVxq5c766Zutwc4e11GPC33JwyW7Iaqo6djex\r\nyI/H17UK1U8si6tPRb6xQ+55z9NYP5UyKwM=\r\n=RRKI\r\n-----END PGP SIGNATURE-----\r\n", "size": 4799}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.12_1676561941718_0.2932092188279347"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-16T15:39:01.876Z", "publish_time": 1676561941876}, "1.2.0-alpha.13": {"name": "@volar/typescript", "version": "1.2.0-alpha.13", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.13"}, "gitHead": "2b935afad2e4e4e307ae525387f66516e496ba24", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.13", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-aSW6BWiFEU80zD4ElyOjsaXCwatJD+NkcywZjFZSX+zDOwFxDwUWcvtFpZ/VNGFsSOXRqe+lG55mHk1HIUu0hg==", "shasum": "d692b8043eebd7f264d3f3f4e94a2be0782d671d", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.13.tgz", "fileCount": 6, "unpackedSize": 22156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFmQWWU2+dyg5Uo+fNQTr/jiXmUQa9V2vPaSynfmDykwIhAJgv0ulkmJnV7kR/SjnxUv73SuJo0CUt1Nbt5DSoZPjn"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7lWsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1Sg/6AzglMcLfCB60uS46WroSl0O6ssfXSOFLPTqLTszI6q6JAMFP\r\nwPnIt3ph/VgsCFIxf8tpFg+fBxEUfzvnO1Qtu6GrPaviTbn9Svh9m4O1Wnq0\r\nG0s0IGZxYS4wcRs55v3xMya6EOXa7MCHj9L+sNoKXxP2vizEVorFo1pwXw/0\r\nhSP4sGsSQlwihXtGvYveuujJj0jx9o8yXzmL7DdWglgf79ZV8N+rTDHU33C2\r\nQZDlA4PTWHn2i7YXIMQx/HuhD65AzM9ji85TYudqvz4NdlH1UqT0UYK01fXg\r\nB48R3UIRGcQ49xoxlMv+Z0joqLrByjpJ4lgaLOaA1pq2QFlh9M78z8dgwWxl\r\nLoY0PTmdvnIAn8m0cE3eAthYCA9FwnsGsrVroYBCqm+wrOnJ6wzI7WVqA5AT\r\nPQZHzzGZfQlh/UDR2eRhzA178OAQ9wA5pJFWBI3cqWO4KfobZcmzQMhrhObc\r\nI1GgETDwkduWDwAWC/g/3HmdnuXmTh5m0s5QnIqn6RXK9IIo/v2I6GJRrtGv\r\nIPhkwr2kOvb8CrXXVGLitAgS+yyz+e9rwX5ZDJem6juxQEbXAWsbuPXA1y/z\r\nSQPXjlKK/GvGNSGyqv63Mlxrf9HRd2jjUPmOGLy3ypomPlw9eDH3+u4TgoUO\r\nPTpc6NQfSfbJjVIAVw1a4cM87bsMYdlE7FA=\r\n=QUaF\r\n-----END PGP SIGNATURE-----\r\n", "size": 4801}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.13_1676563883781_0.9282120887702132"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-16T16:11:23.976Z", "publish_time": 1676563883976}, "1.2.0-alpha.14": {"name": "@volar/typescript", "version": "1.2.0-alpha.14", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.14"}, "gitHead": "8257fccecf0ace51d89fff6d6f47a3f95b4ddd10", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.14", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-QAC0mBEt8jUhZsS8PAuZoNd2pYPBwgPxXG4SDD1mr2NvSMOhTfvzcyY5Y9voLmzIlYYr9AJMJWUwQQ4VzTtTaw==", "shasum": "ebf51f0ffd9a9581d746259c87e99b2ebc5f384a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.14.tgz", "fileCount": 6, "unpackedSize": 22266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB17a+aAzXUKAV0ARNCQTvQf+sqKNAkUvlqSyM36Uo7ZAiBAxQCnrCMLNNoo/eeScEBMb5XN4yLXoDXTe7IFPOFVCA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7/XsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7RQ//dDA379mEtbD/dBgz9XCLdqRZSRoFtqPeacLj4aLWuSz63wQ1\r\ndr2q0ktiiXaq5TpMTvDbolLeqFO5rDHPxmp+aYo6TsgVBz+qbhdJhfx3Ie2D\r\nAcCcMAIR8YGF9DIS4qN3maztILBYw7RUDhA6raZJVVlvX6QeWbv4U2Fe0rDS\r\n49DbfXzCDMwbep/nLnjVBfpkbjKxbdR4kbkSSwCXqk2/KLvWarGpDP7FWp6t\r\nmzbcvPX0mVEsIUJTcBft4XqX9I3R8RtyLyXfOUyFGLuaoWL3ESmBnAJ/QaZs\r\noPrVYPuEcqdCOFAKbbO3SwcKhOwZJZb6K9Skb2xUidadwVF99sbNNYrhNJOe\r\nRU9FrTpi7lxSdPxeXf0ivWVXIKliGqs6NSZvOn0lvOWahqX8PlOcwUzwVopW\r\ncdb6BVGvmWZEe1Bo+w5ssibJYvccXnLvEO0jYBF1kyRIDFFEe2mbYljpgzXv\r\ngVD2Yhu9Rm7EcQbdeSYkBCPX5uYxeT1I++GMw8Fv5glTKY+nAogNjw0zH6J6\r\nXOFaiSGWyMImail3VR8z2C+uRnVptyUpFBeR+o5qon3nSCe/2ZTxU9168yhT\r\nfa6tymXzAOcJnK8pujRMZX3Ej61uCdU7bB6vTRXlFElgV5JzvMGezWSwL3iY\r\ncU70+/5NuLsho/bjGRx2USpoRrMiz79iQKg=\r\n=hdxR\r\n-----END PGP SIGNATURE-----\r\n", "size": 4858}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.14_1676670444643_0.08279081817806189"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-17T21:47:24.859Z", "publish_time": 1676670444859}, "1.2.0-alpha.15": {"name": "@volar/typescript", "version": "1.2.0-alpha.15", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.15"}, "gitHead": "cfd47cc6a882a96dd8a35bdcc26a4607bd0347d2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.15", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-PUbsBXyb3TrAzOsGVjvRfrXYFN3Qskh/2KJKjU8Wa2D9B8A5q6HremYXjvJF0aVIHOJF142+6KfbIDEmQYYsGw==", "shasum": "f96fad4ed8ed9fb068eed5598305df3ad02895fd", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.15.tgz", "fileCount": 6, "unpackedSize": 22266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuMDofRRK+egRhqEmkjCLT/GvHtODjTdPlgnk36GpmKwIgf2tbe6wdRcFIsnUxksMEqn8Xvn7O33gtwW9wV+Saq48="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8qcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+VQ/9FI6yrfKYQ5kzTCS1ewB5W9bq7PdwUvajRCj38md50ZjdWc5e\r\n2rdVMXJsqRCn+nG/ffQf3uzeroyQxVKMH/DBLIcH75ljmoFT0YQtlcKeUOpE\r\nazwpOc/fYGFc/yo3LweMpwQAC35X7kaPQKniCtz0X2pr9qPYMMnRYA8THBhS\r\nxd44Vmz+eALjdWrUOUWlRUQvgus2sgO4jp7s99Mh5rfzjjhv1dBlwN8RzJd5\r\nVEjqtvv5o8DMYXBiZSOiNqaJfVXLEybUTkoQWyADuCX5fA/L2/nKoBTpO//m\r\ngA6y7oW/lcGSYb5gwnGAbMsRNHUc/ZZLitt9/0SO56n3D1Md50cwdG7ltpxm\r\nMKZ+5UMB13F6GOlxZGBqtcUF28evHIuhzzUf+djBPvUJTBbvuuiO5p0GaejN\r\n1pzd8ux5gU7rhrUeVMcnJczpkyna236fb59AAgnu1unNTsVJqisIrpIENhv/\r\n0gisKw0zxzAjgmPJS7FhPIzp38Fwp71vwQoyn/b1VVrfeoibpom6b0yTRGtz\r\nLBRZfgnuLAj9fqAh3ZXxI3hji5dIOyp9J/Vu5hRY3JzF01EYALAI3R+iFBiI\r\nuvYQMv+3X+uaHorFaMPv1RqWEnz4f6NELSIPn5spaGMrhrFVPC8nHPIEifjl\r\nDTsQXJkIAH3bEfU6XNT1kTfq3P/QMNAnc8Y=\r\n=rHMi\r\n-----END PGP SIGNATURE-----\r\n", "size": 4858}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.15_1676846882820_0.9713669187248322"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-19T22:48:02.987Z", "publish_time": 1676846882987}, "1.2.0-alpha.16": {"name": "@volar/typescript", "version": "1.2.0-alpha.16", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.16"}, "gitHead": "8e6256c3b927a3399d1aa08c39a068fcbb8d7e71", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.16", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-ltlTLHIkLxgmTVBZmOnhmnlNzEj2lpvlBmmaV2GWYTrBUMt0z1OgeCq0Utlj9HjjrGPhwWxZNkv86ZABgrMA3Q==", "shasum": "30ff89a784d9151a07aecaeab1f49658c1d21da2", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.16.tgz", "fileCount": 6, "unpackedSize": 22266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJ5ZH0ow8eilfFxRDOt9BswM763PcBdaXxIFpME83gPAIgIDj7YPcNiB+czwndaQv8OCEyQ3xH13nFuOsSBiNSWFU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8qy0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7Bw/8ClPNi+fUa4flThH6yEWmM/tqqnRP8E8Ip6Hy77+NxE0kVZ6s\r\nbMEVwrMLiJLeLdi10B0AbnkSBdi1icssn+Wu0pFjjcvo3tN6qr4oDFpoRqYi\r\nc7k2tD5vuMe6GSqwd0yU+2yULC3hZ348eyDQ8/CYXgpqLDH6wF7XjXRV56K7\r\nl7fG0edv4Fg2+2sWU8FqvpjkaPx6mxwEm9yGdCFq108ZyQjVrynpJ9U7GVwk\r\nCMYkiVUazwnglCkEnSPMKiAD5G/Jwfardr5oTe4poS9vpMlPtISPpYj06Phs\r\nyDF4vvHN14G7Zie15q1Z3OSAwRAV9JgfOzxhopVVz2XJiTlAacybwWIkRI6N\r\nhJLWC1JnDgr81kPPgZ7aibMPmbdSm0+asEAh125JELgPIngvE0cLN3YVfdZA\r\n5t9c8VMGNRVegbTdinUl6Wo49+mUdQ1dPJnL8FdShzhVh197At07pdeKnywc\r\n7T1Q+19qpWqbxXbFl63uQNgUcjqx+EbIliIsU1WIwYpkS6DSYGx8wTWSyDt6\r\nHeTl/1x0DcTNVqQ67jccXDrdxfPgVN/7U4XwJEB1WWEDUZk5dGJMykTm1Bvi\r\n/mQO3QfXkBbfAAqWs/LMBiFG1Fa+GPcTtI5Dgj3bbrUjhf48JJLspmRNs6+G\r\n/SQu4T7L6e4dMhfuewT3Y4OLJ4YrVpg6hn8=\r\n=JbrO\r\n-----END PGP SIGNATURE-----\r\n", "size": 4860}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.16_1676848308406_0.529155321529899"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-19T23:11:48.557Z", "publish_time": 1676848308557}, "1.2.0-alpha.17": {"name": "@volar/typescript", "version": "1.2.0-alpha.17", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.17"}, "gitHead": "8deee6903bb1f536d099d358953d3b79fe983dd3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.17", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-1bJnlwfew0f738eqLjIoXEgNCFBAstzorn84GB96MLcdGPpEf2upQ/7FGSpgaJaFi9LTJBggkqBhuegxjqOQKg==", "shasum": "756c509ef6f6a8eada2f8cf6c59a746f3d8f3a2a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.17.tgz", "fileCount": 6, "unpackedSize": 22266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFpo+bkoC5LfUZmpTqdf2/Lj/QWi5eMvXdWyF3m09G4GAiEA+u5tKy1q+BXZsEETS3vHley54gIpTAlnG9TPlWXmX3A="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj88K9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFpw/+M+5VZoOCn7oGuMlS/b8UPrH/rJC5RYjTtg0fGEHdlIDt2hxu\r\n1Un0Nlugku1P9wZEebxe7L5iQSMGuhRA+AgVfi6gmRRgiBwYcZ9/o70pQtCe\r\nyv7CjlXonddYFkC1U90OOXa8NagiW8MjaX3fN/XlQtJCnP5gDeX8EzNsKf5X\r\nqfCN4thF6D43Nj7ideuccNZ82+kAu/fKBQjUbw1JpRzfZ6wXr4D5ABoknNxG\r\nL1uh84kw6sDE/Hr7RJlruqpiP8ZbeUPALckHtbj2YLi3hyTfscgs5GccYJP8\r\nQSooO96g9cTzAODgLk+lnjsxxcHE0ETwbBeKgtw839Mbi7wldug+vKz0rBXa\r\nPoq3SelSXqqXm0bOg6mU0UFCKXI8Sli7aKiy1kd8Mfvse6cJbeaFsUHGukjf\r\ndBtB5Cwj5vZ1G5dObtYWLMW2fLADV//wWbL4nY17eWcsoEDA8Wah9pq59umP\r\nZz9PTGGqJ2jLTJe/RnpNX0B9vr3rwrd5wpfMdAr6gXUuQa7XhvR+mFvC2ean\r\nPd83BAmcXmfyit2bnIAzaNspXkLl5XVi2YB8Gubd+4SA+tR81LJOHx2xshDY\r\nNShkJuTDJDtXIm8uCj2IQZXIIvGydETym/FdutYTJIgEyc74NkjBHMHcEsc7\r\n+QlPsYK6JppidGl55pvEqTpTGMcq1daXIhs=\r\n=EhTa\r\n-----END PGP SIGNATURE-----\r\n", "size": 4858}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.17_1676919485245_0.996644103727605"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-20T18:58:05.391Z", "publish_time": 1676919485391}, "1.2.0-alpha.18": {"name": "@volar/typescript", "version": "1.2.0-alpha.18", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.18"}, "gitHead": "7f63794bb8ed5a20f7b8838d46ff8da243cb9dec", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.18", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-Jq5kPyB6a2p0pSSolTzIajypyPWZAx5gsDx0liFrdv2LWluVo/hpoCGOnP+SXb/1mts4NLTNpPpgXASG4/Z0yg==", "shasum": "6e37e35c5001779cbc9768d0ce1ee5a695452db9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.18.tgz", "fileCount": 6, "unpackedSize": 22266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvBZgB01FS/T9w138M+TexAMNOG/WFO5PRJSSaYvAAaAIhANfa1cBoJMTo94jCgk7WAKkOh3XcDL81wqwhWMKVRjaa"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9VRiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4rw/7BR5mf4uoJNapOQlE65VNwnrccCi/C7F6aYmkrs8SH9AQ/o1e\r\n82Ipcfj/zlq3KNe9U9gi9OTZNl6RGLD7vq+T+e8eCQnUJ+3lLW9lxBgdQ85v\r\nYMlMV25942yYr1zk92Jv5EwUrL93elO2z9XJWMV/q5VS0WixAhj4wMZfGthh\r\nISlXymAFjtxxVNBARX/hxrfDQr9pLd7tYBP3ukvoHKoYSab0ZBQ/yZ109zs2\r\n+mGtz+/skXBDVb5ZvonEiVKJQb9jXJDjNezI972szDqAbWoU84SeaTONaHbM\r\nS5mpgnYGtiDiYSQvH7tNAU72nuNlTBxwRl/yMN9JbAAicavWqSxPpWkD0QHS\r\n2zlGHWqG0QQpwE9GeUX/1zoplZti0T4roO1KlbCJap+7iKnBbK7RCCitwd8Q\r\nFZ9ETuy6UWuPten2DpvHKcrpPi5CcP2uPQaGwS8DM1gr1X3Zo4C2kGktVRmi\r\nGzJqc1YPTJVfszrTBfWcI64J9Q8oIXcyMvKE+u1H/BftsIsuYcjcp7fDb+nX\r\nFx1cWN3SLP4AGAOvHUWV9pH03xOdPY/9ctIQcZscDRrMUWe38hdXCKatGTgG\r\n4oo/WpZ+ol0p23p4/twFxzqA3GSOkLEQkJ7nwc8b7IuLffecRmvPddQpwCBr\r\nc7Kpu89arIxpi5CB8PX5YCePvNAXzhncorY=\r\n=cZkO\r\n-----END PGP SIGNATURE-----\r\n", "size": 4860}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.18_1677022306161_0.574511370916317"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-21T23:31:46.348Z", "publish_time": 1677022306348}, "1.2.0-alpha.19": {"name": "@volar/typescript", "version": "1.2.0-alpha.19", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.2.0-alpha.19"}, "gitHead": "b0ae65a419cfb91bd85cc4174efa4eb91cadd040", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.2.0-alpha.19", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-KRObKfbQ5930m1nIWBsV3I8uJAaJpqkM76ArsG3cOURuJ1dQSkFm3820Ge/9Ps8Wgf/NDpLPiy21CWDXP1KjnA==", "shasum": "01b84dce3a38c772b8c7eeb515e0b30fdaf017bb", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.2.0-alpha.19.tgz", "fileCount": 6, "unpackedSize": 22266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVcMpl6v4v1w9JFBLOlg2Yt0GA4pUr/6x/KYGVzfjG5gIgNo2e1gsJoXT/QVc8mRvuAGmrAzjburTEV9br3dW1pGg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9YfhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGwQ/+M5sSgoDGZUSL359IgeBhrzyaNHRQ/kjNLK+cAWyrkK0E9F6J\r\nn+QTtE4ExCg2t4t4iEpqGqbaZ4TZd4Nb1ny3Jl1CwTFl2GumWrxjuASMkGLG\r\nKr7JdQ5oUMKUpki4vyTMwMy4hfTn3S4/swCrFF9bCn9JRVxN+kh7f1QFN+pQ\r\nwNsWDI0CbQoGuIyHWYHcjo6BPSr/Rjs8DW83pw3bVX5rr9gG2YyVG511TYW3\r\n717qTjb0T4mm5dU/I4XJecfBlgqxC+Ky60/ZvCKbon+Z5LTiwnyS0eAkKjzc\r\nxPf2hLXT4hDRzj8nIVI9Q6xOLSyxFyKx8jqCx+YpqCqlBguyNhY4vTEp/Hdk\r\nOIWlLZd8SEiG1JRtkqXxioTo2q3e/b8w1AfMZ4mInFYUqv2e5UaUYHl9MykX\r\nJtDlIa+TnIERR9TD7ofP+K3gAxTQOp5WKca7qJIP+ag/IcYn6jnLHqTV1df/\r\nglp3oqoTg7R9UdRbwuBlnRbPw5EW7IdI5rpg1Z/b97P3tY2uMt6JB1an6mzr\r\niJNeVuYOOAXal+ZACdxWkwFOFfdhtMfYpT17jIEcjxjfBAjLXUc+w+s8YAVV\r\nf1oNbkUJKDE2oAp8JODW1iwPi3RYSrd8WyjIod9QiKTY7YNUDNR1rIFmBo/R\r\nRwhJMAiyfmaNPKsuCoBlf3YlaCGJLpeu1RE=\r\n=7tdz\r\n-----END PGP SIGNATURE-----\r\n", "size": 4857}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.2.0-alpha.19_1677035489786_0.4145098270452674"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-22T03:11:29.935Z", "publish_time": 1677035489935}, "1.3.0-alpha.0": {"name": "@volar/typescript", "version": "1.3.0-alpha.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.3.0-alpha.0"}, "gitHead": "7582403cc521d33f642183a32da44fb1f64e65ef", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.3.0-alpha.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-5UItyW2cdH2mBLu4RrECRNJRgtvvzKrSCn2y3v/D61QwIDkGx4aeil6x8RFuUL5TFtV6QvVHXnsOHxNgd+sCow==", "shasum": "f79bbc9939016700812b18191c47eb035913c6c3", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.3.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 22264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcUd3Vm0fIJHDS224VfEinQKL/8RbHBvsqE6whCgLabAiEA4D+dBz0aSkgX/TjGmiCZI8TBVOe5aul5yizo0E+Wi5A="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+UwaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOMw/+O/wmAyNkNCRQmYsmdsnAzt3tn8KXAsik2D3GrAc/eyvykNdv\r\nSUT7CGruNF0Vh74lp/U+O3K0T0L/6VDaQo2kf0+yvUWF7oPyLIRzdjmVbkZc\r\nJqmX8E4XzxlTPlsZJMNxUN8mX9ja0ZAQSf3uvdEiHZ97u5IRqfKMgXeI68Jw\r\na5SsKUDQSdUDgvpOHBsP4pfgGAtM0v0cQNCsnP1kpeZBNS8E5JnoY3pkP7lH\r\nCQafifVFKJD+LTrqmWN062crMRWxyVYsHdAIS991cSPU5FzwHb2lBu3+jyml\r\nTNlMrH+ER9XPWeOgO619M4N1DA1FLA0vEp8ISXeb7+VPXOpw30GHpGEgyg/o\r\nlQznuD6C1/Uj0GtJuF4XgQi09aRGL6dDv4siQaipoWNZBXcVzY7dkSD0vJMr\r\nNGHpi6B5Fxfqj+pDfz2B3peT8KQXeKTvwEq17Bm1LPECfsDgE7KOaAAwZOV2\r\nd9562qUWN8yq0nMZNShsi07eCMlLgZm/mFSfExF+SD/GBRTejh+yLU3X6jNN\r\nWeeG648PPoRodPGhBH52KYBnnflrhqit0KERtyXrN9mtxBRcYp5cTE1Nd/3i\r\n2sO2DNhwenyt4yZBI1Kyzo2l29LNsx81cmPWiJt7YaYGT4ev3pMJEelLOI5z\r\nxyL7ypiOjCFZtuiNPSzCEyV+dw6RJxkf+rI=\r\n=1OqJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 4857}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.3.0-alpha.0_1677282329764_0.4586144345542329"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-24T23:45:30.110Z", "publish_time": 1677282330110}, "1.3.0-alpha.1": {"name": "@volar/typescript", "version": "1.3.0-alpha.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.3.0-alpha.1"}, "gitHead": "2926dc4af31890e01f6218773e88578ddf0de658", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.3.0-alpha.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-MwwauaTgZi8K8Xg2xJPCZPWyHVSuMq0ffFHQEFliWHzqPNNuvt/7OqiSRL/kP3Al8TgZyGaArWqTjxxItpZpzA==", "shasum": "ff01a9d021b765c4640679520f65ca22e05e8a17", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.3.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 22264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG4zCYA4uWHh4BkUTtVY21KIUKB3RIvaCyqypVZa/MKdAiALpNkYkPWonV1/OGzOl8SBMBO++Qw3mYhM4Te7SPDunQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/+8yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+eBAAk3gyFr217c89VmXMCkyN+2F05+MlRLR7P4VuhPzOyqYvc9v9\r\nRzfCvrMrDIG0TVUiy74zTxkKSMURIglehVPmnN1iPtRIC6+dxn2ZcsIjfv72\r\nbyyCCJV8BYhH5btvF0SLIhp5m8BDgAmV8qud/Tmm9rgFzwKmdCa2bdSbRxCt\r\nZn9mqAje1A1r/UC4iNLaQELi8SH3WndzMu0RDmlt4Hix1avGZn8gjGl8OkKa\r\nXGqMiaHbayyl/EqCHrU8EfZRYi0zApVoeIRWXl2j3Q+UerMTarsBvAhbTAvq\r\nAVrizZOp4J4IJAeGmh+s51dS/EGGb4pIimzBAAJg8Tc6eafMroDdVoCAxxFt\r\nKyloSPeISTb2UyyZc5wzfxEXXNDFeauy278+bKVggmCbmDamcLJbsWy0j8X/\r\nfVVy3US21pY4u8Cg0q7V2LleefxYmHrpEWC0O10JRsONQfcz7CU7SlRIiTCb\r\nM0In7mC9qNuxicj6EwCiOxwbQOz7GpXY/Hj/gZqwc7yhOFDjmTfjsGletRfM\r\nxtN+/2MJQtQ/5wcPuxLJuAi+ZJVeBivVtkwlD/aW8f+3RUd2x5k0ViGLJDKj\r\nSohdYnpcbcIhsLJnuEJs2MY0cexKcMTPt/hyCpa/a0EVQyfaDUl6+wjh2v5z\r\njCEaAY+AXKW5ce0GB2inpjTNI6+JoNva6mM=\r\n=APAC\r\n-----END PGP SIGNATURE-----\r\n", "size": 4859}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.3.0-alpha.1_1677717298571_0.8251849708688246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-02T00:34:58.760Z", "publish_time": 1677717298760}, "1.3.0-alpha.2": {"name": "@volar/typescript", "version": "1.3.0-alpha.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.3.0-alpha.2"}, "gitHead": "84111017f289c36dfe4541159cad8a7e13c2be41", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.3.0-alpha.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-i6lUGZOr+LbhURbifyqjgbNJF0XqvndSW0nmwJyGiDH3ExpvYJbndU276+lC2TRNE8DCxONx7N8gwbJ2C1MZDg==", "shasum": "f4dec23b635f171ac38254a7c29820fb20f2dbfe", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.3.0-alpha.2.tgz", "fileCount": 6, "unpackedSize": 22264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDV/ZDZQ2JSJtd/lSoVLzOpbMwrT03K19KwY6mtXvLjFwIhAMvYY7ubDIok9Fx/xBxHxpf0zCmwIiqgcCJNsst3A/MR"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAMjbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPKw//WWe9if/djdkY7QtmB2Ug5SC/s28QCzz67fMEJ0przxrl9Bu+\r\nqZUE46da+giKdgaDFwqXJYu2kp7WTJimxl2JBpsyJY+H6D4XbBNwhonPIX/T\r\ng9IAr6iUu99dNtnyoevslN+H4KLTTC2HepsjJClxUYaHBCEHWsp0BQMFF0+l\r\nZM2keWi8ISJEzyjk4Xnr1vz4wTyXlSAXwx+nbrZWcuaYog5iTh4GbULg7eDD\r\nMGd3hdzYxgqN1BfK1QTN5YnB9qTXoQtjKO/WsBEPDkqjwJER1YiBJS3+mrNe\r\nB2T5w4zlO/AjRlcmynmWM/4+x9aX1QjzTsIPrpaGY6nR6ZwiT+ADd5yuDMu0\r\nEFCIY/JVnLmdU343LgXc7OXazK2kKiQZGs8hRcbpRzgAOzyPH5hU/6jw2VWF\r\nSI9UW38xqcnSoNC20WXp6i0Q/NRbdcPZ+6yMz4+cvT8Was6rgpNa4i2uw6V3\r\npILmf2Jn2Mrgu9VAIp7HPNAA6JCStka8tFlqrmHshTwtf8KtOBU7kUvXgr+Z\r\n8PhBvhh+6EB2dcpDi9qLp6Hp4tARGClfbJRTVoClYbik7bGmBq5hFLVxNk3C\r\n18f8mUAZDVo6hoFTWoRORA1Etyyu206MxEMHfKfMZcD+PAwmtAFY6lMTDR4j\r\nXXIYo06blT35U/DgAEYErTgfXog8fXPODNc=\r\n=/5aD\r\n-----END PGP SIGNATURE-----\r\n", "size": 4858}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.3.0-alpha.2_1677773019790_0.9409409318839486"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-02T16:03:39.929Z", "publish_time": 1677773019929}, "1.3.0-alpha.3": {"name": "@volar/typescript", "version": "1.3.0-alpha.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.3.0-alpha.3"}, "gitHead": "03c8fff84531d8740863da5f9350e0d9382b5784", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.3.0-alpha.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.15.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-YeWx9yS/mv2zF9eUO4Z/JSoVBddojs9JHXjFGG1ETtqDqgGiG+ypaTe6Xi8ZMjcj01wpwxlbq765O+1BTBJIhg==", "shasum": "775560f78fccac2f05c15902a9de368d3381e5d5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.3.0-alpha.3.tgz", "fileCount": 6, "unpackedSize": 22264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfoMS4vr9PVy6d/H0wAezwAajXyorUIFTN/jMkB20/bQIhANtAoWG6zu6zLQlLPEXDiKjbGnxdNULjxHrMnFyZzB6F"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAjl9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqR9BAAhZO3EWx+lZkkkZYK5UNCKpW7zDSfON3N91qUxkku/cCHBbyq\r\nW7HVkTI+DYCAo/xthZLr+DsLtCf3HKK9E5QvQ/WNn0LYyO9sgAyza4+Q+NTp\r\nB0ZYd/xq/T2ARWLhtJsT/KrdV5o1C097qYenIxeTmoFDsFji1gXfBh0H4ztQ\r\noj8kDIhSmfRBSLFf/SblxxldNcTTM2sGSYY+UoOqYWzecRFUwSX6692dPhZV\r\nvAZsrCwtYplnKsjcHeXugRcEkFzP783Tm3avYbRa8PYUYf3Yloljng4qKNvw\r\ncnRjGXOdJGs6YuRQRc0/LJ1a3pHScBq9Z7s9TomG6cbJKlyiPg3dxnfvM20I\r\n7RaK8aSuiWkeDyE5arXgeC2cuyes1KLFJ4UUB9tVGEK1OUGg6EEYSFrlWPFS\r\nmGYFRVy1qCn0CDIP7chFseEh86rWqqy6N3BLyXanKbsTikDd2Haggkf8L2yn\r\nZ5XnIDw7IuGGWpmsMTpi+GZ0nhwxiPha2r+P49oYY15ZzYnUJZaLFhL7SugV\r\n0wxnPHKzXpTIVYkxj6G3+fwMUqf34bgKAlNWovouk/0mkIAwo58s7RfNn3A4\r\n2aAEsytK+YHURj+EAyZZFstbMRTSqNJttrXk2dhJYIVcDKss5Y2XjG8kHqsV\r\nW+VF1GqV3iD3biWUMDuhcGCRorBJNHSV16I=\r\n=NUPH\r\n-----END PGP SIGNATURE-----\r\n", "size": 4861}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.3.0-alpha.3_1677867389592_0.6867904697556946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-03T18:16:29.732Z", "publish_time": 1677867389732}, "1.4.0-alpha.0": {"name": "@volar/typescript", "version": "1.4.0-alpha.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.0"}, "gitHead": "2607410cae341cf802b446062d446ad497be2057", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.16.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-88srxp3vzjbUUHc+dicZgjibQmfgTp/dQsrl4KdEwekY0GGRSCXF4ieupfWPq3wIHjMFtoqDLhRMwUDHT3/i7Q==", "shasum": "d245024f699c0e434c0c6a9dc4f52d247fa85092", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 22264, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcvA8E3VuJ8crai3qGOQebzX83l5Pe7DCGpNTM742IxgIgVQBdmfVpPKgiBQ3TtI2bZK2CjNMxdKNRNX2cOpWiU9E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBF0/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxZA//eLmKkbhIrPpoPGlfOr95KiOepzUTC3fMOGzD2wsnhqAuK4QK\r\naL5EhvoUIsQaD6if5HkhkPYrwFhWIhc3qlhtCiI8qmju1ljuhqbyIdqQTu3o\r\npu1h/Pigh9+mKxtKCxFZZfG7y47z0ccjMdgE6gEJ4XXNqMZ7X6L0Q0Dqgleh\r\nXoRstG3xBvFw83LRhCmKtjEYKvvdNi2iDSKPiJ86m314sNdPtArzmsxJZxlo\r\n/wd3QDP0RmDpOtwDIZT41Yaa7sifm/jz7xAQ3HzpHkugHGyrqUyBnqnIZ230\r\nD5rf7oPcNS1fwanLpHTfASkvx8XPFpgAD9uQfgyP+wGLaKTy6nyaKV9KWVLd\r\n/AP4h2q7BvJP7d3Rvj9eQPgvfixczj6UFeRtRijgZdg5ivZu4qai2rrxYhax\r\nZ0A6pSuyxAy9hoNvF8LVm5eIeqFlZ4qrbR0JXhP9blSzY+2U6f5J/9Zgvv3b\r\n4YZcBQzThs/JEl7ZgYA6xZIGLaUsNimylAUv4N1pdvtz0j7f+YnjlBODqpFJ\r\nfZDucqJ6vWuVWjNhOj9Tqn1baQlzMfLihVS4LC9mnXwv9wdMsx8+p035RmJN\r\nb8Es+WbeaW60GyWAzKM8z90LLEhPgWIwn4VQK35blQY9OLWmBXA6uwnQWOln\r\nebdjZLxkGyJ9XCTkXHi3qdMmLMcYyMdli1w=\r\n=y0sw\r\n-----END PGP SIGNATURE-----\r\n", "size": 4856}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.0_1678007615676_0.06718274239156385"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-05T09:13:35.865Z", "publish_time": 1678007615865}, "1.4.0-alpha.1": {"name": "@volar/typescript", "version": "1.4.0-alpha.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.1"}, "gitHead": "41ea915b137aea284e2bf5edd98a788ce21b779c", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.16.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-d3oVIbZjBT5Gc+r6DOif63Nm++az/EQdylWjEgSkXSs6Z8rSB9Bd0+ffjl4QY94FlAbBcXOYKjaQrnFGHNIaFQ==", "shasum": "82e236e780722d9b58af992725501d4774187412", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 21539, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChel9/b7SF5cyAG15+DxOlPnkOtrvyfcYJ4hTG+7BkvAIgAJN5RsRrm0kxHjijm74IoNbFKCKab6mVplup0ycAmII="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBSlWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprdA//WRGo+MQvvY5thyraOPLX8XGu6Z5YOC3tCUgrQO2O2PVB3ndO\r\nEn/jnhMQ6TdcRpFf/d7ehyNUX/SabfOEAqEA1LdecyMVIEV9XToKJUEg99Gd\r\n1RnoIYN5n7GIj+ojqPM9ueyktgYSR12+UOrui9zzz2M9H6mNgdKfNJwZLGa+\r\n0geoU3QUJC5KNNIWw993atNR/iNooY37Fz2mQseQmiRO9zRUYK3hOP5yGhnJ\r\n2WG1lhpC5BywVFCt9hAfgiaIzkB9N5Srfc5dr20VqhgCbH3J3BGWwi1npjM2\r\niVrDbkmOfroVVooq8qbhL3eZpGeZKa0o93aGd6co09PzPyOJrmTGCPatpvQZ\r\nbhSb29rdw7ubtsH9ld8DZW6L41Bd5WGhDkzMG8UYzpeHuMEXg+P+lUo2fIX4\r\nINzvuJLy0MZvhOj94ONFrKLsJG3/7tMCLPrPIVO1A2H89iU/XPO5AdozoI+H\r\nWXkyAsz2J5hE24opjrZNhS8260lpm9/gEBkxvatM7HU9DaWo/sgRPgPq3MsW\r\niuw1QLmHb0YhJ3l4T9inZbOB5h7VicxOmU+jVBBjkC0Ipf/rw19K7Xbn0MVY\r\nD+k+aWpBFhRjkBFj9KHOp1f+qv+jmQqBcmbPl2kYF7utmeodWYGXzGZXNlMe\r\nKGvF85otnHHg6Rz6iS+PoDW4oY8gSMWkOHU=\r\n=a5VM\r\n-----END PGP SIGNATURE-----\r\n", "size": 4710}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.1_1678059862362_0.8053345714438691"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-05T23:44:22.533Z", "publish_time": 1678059862533}, "1.4.0-alpha.2": {"name": "@volar/typescript", "version": "1.4.0-alpha.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.2"}, "gitHead": "034b230da17794a6fcf5a0b07668710f98ff84e3", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.16.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-3Fi/5zCjpOpFkoFJAgbb+A4U0xaWzi8QC41rHr3BM7ms+EmdFFYAJWeGyzOphxGjfDlGa8KLYzNLV/Vdh6l9qQ==", "shasum": "55a778704a8f3a1e410d105ab29a67dd0ebcd59a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.2.tgz", "fileCount": 6, "unpackedSize": 21539, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeBiL+Zw41A6wUvwleXLkaw/tMs8C8N3vTM5gnW2DzBwIgGc6zjKVqRy0PVQcsAI2yY25yYf1lMXuWw6VzYK4Abqk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCwDeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH8w//XovrMMkrk+sA9bEx7dt2RoKWc67s9m9dZ90x0MJGSTc2cJsm\r\n7Iu/lpjbsRFHUS1x3EKGygAiJGdxSPwH+H1aFrHAUdAfjC9x4z0k/aYnEf5q\r\nlBp8/sH7cJ9DmYTSuyatL+rXPuxaPMqnmH7Wkf1aFI61bH6YJRC+D5TOP7dv\r\nedChVP9I/yzt5yflzuIbUFVd3Ov8iekcNTmv2i4q+m6hPcZvgBvJPSvtBicp\r\nDYbHw3IqrRteT8BOuO2Dcq3pu//jGcV4aEvvDNqYd5d5BTxgThkjL/b7Ab8T\r\nV+qs3IrMB5a30VgjOlyaja9EldagAXBU5JMylRTtoMV2L+SQmaKw2Lici+PC\r\n368fLw9qa+H3LuaX08sr54HByHsLoIHQNi9KdkQjJ9uH7sHHvkSV4BP2vE7v\r\nCe3MhDqDcyCAdXsqeoT430uBvIGVs15NrZhc/YfC67EX16D8QSeAomKwj9xM\r\n6SSACIkaKKXdLNA2DQR1KKmWD9cEesyHOFHqczO+P06S6QVrnn0HWGJPhPqh\r\nCYBiJDtE1kHLf1Kd4anrzJOnu1xxJeQlFaBOfy+aMCee4yW30N2izJ9MN3gJ\r\nggPoRUf928FYQXP3GdtulgZObEZ7+WKgfZFDlu7TxvXpsdT2QeWOUh6z3RSw\r\nbJdBL/ftW0IMMAUkjIZqevN8ZCzjt0LuFrI=\r\n=H3v9\r\n-----END PGP SIGNATURE-----\r\n", "size": 4710}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.2_1678442717963_0.1720732405889649"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-10T10:05:18.133Z", "publish_time": 1678442718133}, "1.4.0-alpha.3": {"name": "@volar/typescript", "version": "1.4.0-alpha.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.3"}, "gitHead": "033129d71b355a0f5bbf514c6887ed3a6e083c21", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.16.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-O8LTFLp9rw2nhJfHqcikx/im69r9Pfih+qHc2lCgaNlvwFmk7VGdWGghWlPmdGd1XE48gu9MmdCZP3NZ5hqCKg==", "shasum": "93daaae971fbe2b0cf1ba921b5c4c75d2909b21f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.3.tgz", "fileCount": 6, "unpackedSize": 21533, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAqOqTdPcodpc73P7AmcPgPcxbj607mUTkhQ7MNc1He4AiEA+ktUQOMyw1LttzzeMBFrO0iVIKztQoUPRLqgJTwnK8Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkD2YNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeKw/8C87+PGE5ZJaICrqLI0Tz2k1yaIK1u2SE4uL62qMGOZaxwNc1\r\nVh0uIYEbS7IjwvzPRA1vSOSO+J2yrHff6FtoQiyOJgZpEtB/y/d55q8PPNWU\r\nYVJhhjwULmZ4ssIFpyMf1IcCQG5OoytkaS67xpQIf4cOBZ5CzvBq6h18UuML\r\nqC4Im6MqFM/y5Fg8H1zc8nHn0LmXtwzdS0oAx7JTwVJjByePqKDnHK6Vfxfv\r\nZiqGuyyJmQF1bxgwuvQYMPhGLtAnY5dsB7TNXLuZXljYVvwaRkIAh52Zuve8\r\nc+EuVTCY8wTmF5iSYr3hVzCVnkYVg/k4QdI8JjXpNq8CiYgMlNoUtr5x/JHk\r\nFXWP6a4xm0AEAwTUHd06h6FGdCWz0ooSDIDnjLmC8qeS52kCXaUoqoupSICI\r\nVpLzJ8yr4UQvei8sMllUbU0M33Efmnlp38kzJIYn0BA+kcF7gcqNWM0GDL4e\r\nFngj9miGg6clSL3gxW5rXt/HV+2t/JtrCycansMpWeUouXHT0f54K8CW/5E2\r\n4sU5rwlUVR72S6rg9t9nGgLNxQPDiQx+Or3AJzKlZnHG1+dr7OaF+GlfcG4S\r\njM6+219WCAPI9S+rFLWkzOb2vjvZaR86zlt1Rfk12d88Ih//5kYNdMoeKPSq\r\nahnVgz2YYGtiT56aQbNoDs8OVIKY+cmIWM0=\r\n=bzo8\r\n-----END PGP SIGNATURE-----\r\n", "size": 4689}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.3_1678730765283_0.586172102518941"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-13T18:06:05.509Z", "publish_time": 1678730765509}, "1.4.0-alpha.4": {"name": "@volar/typescript", "version": "1.4.0-alpha.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.4"}, "gitHead": "b78ceb0a7d185d1e2073c960486d7924f9cda403", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.4", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.17.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-06VnL1uvKlQJxhVQC4FPwg2gydY97HB5H0zDO00s/tZkiY+cY01usO8pcbnDcZly6jHFD5HxEcRni/kbjpUNSA==", "shasum": "5ddb3a88c5b8b84a2de5d16af41ee752d3214410", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.4.tgz", "fileCount": 6, "unpackedSize": 21561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNsP5yhphKRG0VPYNlEQkYtkfFju9zh7bFMh9s0p1acAiEAmFMxT5aKpi9MVZ40BMH2YW1eoTuWK4DaVY7oVwTou5o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkF76qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrL5A//cug7CKIflLMfqcBLjG82BpAg0G+XYXcZ/05wWVWjQHKtOWYr\r\nY4gZHmaKPa/2d/A2xzyDzDr342j2n1iET3s1GtY6kx5GrciRNWtOhdQp2tXl\r\nIVBx15TQ/M6fSdW/XVhUJ6r6wFE5T3N1iBiCvvUdl09Jb/Kn/8Iw5/lVzHln\r\nEjG2AjvOnIw7+yQcjOYG9RSqr0it0rbuiYaZc4ytop0FzttNa1FPAaS6ucXv\r\ntF0i8dnSdjZ4E3gJyA/r/u5V9SuMNRkVWfCnDWbqeqg+it6STt1EHvNwGj9k\r\n+HNrQpYdVJoF9pS286mbUWS3ie2/IxvOBXnP36c/pOvOlA6Lhutg59j9DrcN\r\nQRrEgMyltcLir7cUIXIWggDKrxAnQFXJZZ5nHhuZnRNGf6Ppwi3GdNqH721+\r\nd9aJJ/JuNz8R0wY8lIZrMQC/B9UATyP2vVTGOz7NN/oz+mtrQwLX3UOtf5P7\r\n1HMf1IiZx222S3M0KjBRQVvNCjHgUgO6VZodynI4IDXyZ2DK5+tQdMcwkYxx\r\npLei5rPeankr9TRNe0QEuzr7D4B1wltxxsYwr4MSEofJ82D/ca/fWAlFEQFJ\r\nQd2LnYhU0jIqKjQj5twv8HaUbEEcVQuvRs6+FZQZrgYMSZfDJk7Lc8iUDYcP\r\nY5QI3b7NHM7uGt3p0XY0Kaq1L/Jg7nL8eYM=\r\n=oZmt\r\n-----END PGP SIGNATURE-----\r\n", "size": 4699}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.4_1679277738593_0.44470988995704164"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-20T02:02:18.763Z", "publish_time": 1679277738763}, "1.4.0-alpha.5": {"name": "@volar/typescript", "version": "1.4.0-alpha.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.5"}, "gitHead": "85378fe538a34f18fa3f45784d5d5a72cbf2a54c", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.5", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.17.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-AEm68uua8mYEpI27ApCWCVmtCMYBBj0Z8Lbp7uBRf0Tpprbr/xs/xGhq1TqcuSqxpAnEARt4Y25yZBPfPTy2lg==", "shasum": "3e92b1b9142e2fcde6a7c0b4ff009574c49f06c4", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.5.tgz", "fileCount": 6, "unpackedSize": 21561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO+CO7RIoawC3Oy1KYMve5z9QJNql+/w1q36WhfrgCQAIhAIAzrO3nUEA1kmzgPn7iqIeQ+GSoBqAmSu2zWNIGhAF8"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIAXlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9qxAAk9L8sY2uFYfY2pd0a+BbmZni0x25uW+AkC/THNbEemssxNQ9\r\ncrAeLjWRnrN1ws4UeeK55mhEHHdGUt0oGbzPz1p1jI50uFOUYdUUQoo7plPO\r\nsIsEm6O5h5bmP5/Mple9lAkV7To3uMUmoIheJJrW8cwryznvfBARgL+NOBES\r\nABEQCMM0GFb7ZUkx5PDVxp3gOnrED3Xc9ld2pm3dK+NXBrOq9Xk6GSUq3yRb\r\nhKl+NV+3Kp6frItCELJsgHjO9bafDSvR3vYkC/qVK2e9g/j84d1WiyVyhRM3\r\nhcydBiWaWL5zcQj0RVT/RP3cxB7feg+7S/s+FEs55OCY37cmPB8/h2ToaIhT\r\nZJMRKMdIC9MV0nxYoIskjljXlttBxSoS8ABVBXOojyo6aOZJucoifF6nwQpP\r\nTMXBGCC2H6gIOXmYnx7RmvxB0WLhPMLUPPA/43nDdgE5wA3CBZtC7timiOcc\r\nky7exNxmIUKFU+snA3oPw6pb+yNPsa5xXfs0gENFCN3KRaG9a9L511NqAZ5z\r\nMlGQ7shBNh86tnGvgbI6JRyMTpwht08dOJDJnwt/JhqDeCh2mz5fci4HMenH\r\njFD7t9FqdGwAdPzJZIoAU7eteEdyeMTcXB61zrOGlS/VxvnOIKn22Tctuzou\r\nG3N//8QUqai1TLYhSjRbLPbTGxmFx/HST1I=\r\n=eLWb\r\n-----END PGP SIGNATURE-----\r\n", "size": 4699}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.5_1679820261563_0.20657240426045642"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-26T08:44:21.731Z", "publish_time": 1679820261731}, "1.4.0-alpha.6": {"name": "@volar/typescript", "version": "1.4.0-alpha.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.6"}, "gitHead": "e975c5cbd4230afaa97155c9498b8887ab58eec6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.6", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.17.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-GHiE0PPVqt5SJ6v6pt0j+8k934CrM1p79FfqCIWbrDUmRUBRnJ5TPxoDsq6mt2Hty7BD7/kVeDjdWSJMgN3PlA==", "shasum": "7be4f93faeabcf3b74de9c66a413910953a3333e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.6.tgz", "fileCount": 6, "unpackedSize": 21561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2fesFVPDICLvpgzPfPHEoFav4qKwBh9/kN2mEqF8eHAiBl3p0QCFgt/InoPNuOQdcnbN4/Qvx/txXkbnaPXeOthw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJ8GWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXpxAAhaXfdvrPL3i5DDHjzGkXZwj0uV5nC2XzRwSw0jAVkhwRGHrm\r\ntEl0mR1sMc3kqzqA5va+YhLemwe2H4VWAuJtmJ/Swwm+Sle20dNvuUzdh9kM\r\ngR0JpXuG7PrGdDVT1TzFDrvB6N1JOttT6Sn/kRqehC/sFLFKxgAl5I0oJeDz\r\noWW2Trr4RRW9tE0hn8vdRDktLCvCUrEokv5VnIGnmsYdkkjZfr1xqLd52taA\r\n0pq4KVUrLfmj4IxOTRsqbRXnDsC/epOd4zJ8NJMBCN3tFgXOzIYMd3MtivBm\r\nlGkukNUAyn/XJwb2SxWWuTj6hH0RwRjPp8B6wyGq2cegpcbcw17cWHajHzjE\r\nY6kqE7Lk+jkcC5vNxm31vSD/zLvkxTrUnI3EcHaFVheedTr/uljt0YAUB/Ce\r\n0AIbe4C2BNH+v3BnZuu+43voO8TDKHm15HNYR19T75fUIBgMwKnVf1lHpFWv\r\nAXMxH5+gGYsF1b6hZgH4RlyXOjDBKXW5h8fCAM+O46X8hxuqclpyRguXK8nE\r\nybhK57hAvZMVeT9vB++wvp8bdSliLSOCVXWyqJLwbZ2OmiNIqIjfF4jfVIIA\r\nnng1cerOo/mplSfNNhZqlZN0JbS4hWcTfmAtjzofnF2MGp0l6oEoIznjJ2lP\r\njNDa8DBKoHQCNkamwPJXOacHAXj2n7b9j8I=\r\n=fbH0\r\n-----END PGP SIGNATURE-----\r\n", "size": 4698}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.6_1680327062649_0.5433205416747786"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-01T05:31:02.849Z", "publish_time": 1680327062849}, "1.4.0-alpha.7": {"name": "@volar/typescript", "version": "1.4.0-alpha.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.7"}, "gitHead": "3d4412419e4ca483eb856e9bd6b74e5e387d0d55", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.7", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/1.17.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-tf7n/bNW9aTLbCpgi3E4VUpxgkOjECWRCgd4O+TLxBro1QaBzsjad2fakX4Gp9EwkhFD/WqeeHrgVpeLh5FCRw==", "shasum": "69be8c000b9aa81e3092013c178cd851dc67904d", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.7.tgz", "fileCount": 6, "unpackedSize": 21561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHAPL7hl5kbMeiJhhX5Go1D15jgtnGvhdsZqVOGSeCSAiBIfxaukDGqEWSIbTX1zSXHqequqMqh89KWinIshmYsfA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKZNVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrACw//TYeFVBWLPbixVvVM2kDVY1N6d+tmQMbSGBx+BO4TgEZqvXW2\r\nVArnWDnwD4u7Wdg+yE2KUpp3GK9cdjqBLRABCMLXh33drwDdOWsvc+6VAHuM\r\nF3ASIRtOmEA/WyiKdswgOXVqx441JDuTmT89CaiPiiY51QNOg4A34aNdCaSx\r\n9KRJzsaykauE3QfJ77Ex9uMa3Qm9M9xfjTpfGv3Wn07/vh7KxG8wT8byaCJj\r\nAbPzSYAzWGGSHWWLsiLWrYKbVUVAh3tDFSqV1OJiAtmZ3Nn7Icx4gI+Rprf+\r\nrIvQATCiep3ax3+4hK8Y92UFOxR5s8DnHpWa+jFWb2XzYxcMabwg67jlXc+S\r\noy9JFfnDFKSF8DnRXbIpIYgotJfsVLLTuZhZIQb1wcsGLEuGs/miRURopRQj\r\nkurF0SRpJ4VhF4DehfTh/OUa9zOARvvycGBeQQnTbPMYahIHcXTGgfHXs09a\r\nONCf5ay6ifA+pH0503IxloZhkvfsJy8N/5EYUnNUy0dmd8h73oGcIj8prT2V\r\nKcIXSqR8v8p2pF2KTvFhVd+dTveV44ouyiFpT2qE9Cc8L8prYlmaVmQmbTnS\r\nHBmBi1yUQGsk/7K5EcevhEHyTe6otK5cLHl4BQSBMCUf0U35D6qW/zyt+src\r\nFsf4U1yRhsXjdiV72jpldI2Exc1kIkwzra0=\r\n=MK08\r\n-----END PGP SIGNATURE-----\r\n", "size": 4701}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.7_1680446293338_0.8359078656189527"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-02T14:38:13.518Z", "publish_time": 1680446293518}, "1.4.0-alpha.8": {"name": "@volar/typescript", "version": "1.4.0-alpha.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.8"}, "types": "./out/index.d.ts", "gitHead": "58131bf4c76bff982ed6c8ee78e71ec07b11436e", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.8", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-1GnrX5cHc4hrk1OQpvrQlbehFCH6UN5bVJWxsjTfHT7jZATLTHPz1cnhFn36EpGrBHRMwWKlCXrf4J0cFBSmlQ==", "shasum": "ae15da9ff6ecf766431689e27b0edccc723a1072", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.8.tgz", "fileCount": 6, "unpackedSize": 21505, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEC2Vz6FQdDIii9aTVF8oLTxKjiSbsoWb9Au4XOTGiuoAiBgxaeBTetQhuAf8e6qEQOAVEvP5NT0JD9xRNBVC9qleQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOvFPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXag//ZdfrkCoKPCQb7X44cPOuwg1q7h5GgLzSIIJydwTQSsJsTYCW\r\nbx9ZhdSCKomZ0A9y6dU0ddyvzZZ7/z0uFVeBqM3WVvDX0OdFVlRgU8WMew8l\r\nev+ENrYNgk3M/LmtBKsKnnCyi9iNMZNHNMxwaD933qXTWPT5zIBtC+tWbXLm\r\noKZlxtFies0H9AyeFjmiTLpHY83CD0NMGP0S87kKY5ABMhrwhdMQj3arJkUB\r\nBoMlgfyWGPPITs9l+E3pR404U+9Xzyt0szSERVRIH2uQBWdajaseSm3Pf4C4\r\nWIeD3NzC56hGwBLpJ3fWGfRUtBMFcxcLcYFdCdd3jQraN4CuxlZAqv1E1z/x\r\n6QJXvZqkJqq2VRh9e8U2sCOiJYZySsAL1ktQ6SxrtvDwQoHZWYnWE2wq8oqh\r\nKIfLOCpG8SaHPXyH6gjdNo/Hsu8NX5z+XHwB0/vqlKJiB+7RERZlkmw8yBfZ\r\ngHTREr5o4wIe3Nb1dddWPtu/TjHOWk28kKbCKE7GdHyf8CeAdcLNZa+E1Xgj\r\n0VMQ1YG8ZjD+OXY+LO9aZWWqaTTtpqCRsHb0A6HJvfDYBybCoUCQ3qKRnvIG\r\nQojPWD8QgCxFJU9MOQPpmKjOrhr7THW+O7XMjQqyzAK+4DhDpGbaNDvEJYih\r\nCowf+RAfBEWajcgx0f8WGKAqB0Obi5qlRF4=\r\n=yUix\r\n-----END PGP SIGNATURE-----\r\n", "size": 4590}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.8_1681584462768_0.6027365074873914"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-15T18:47:42.987Z", "publish_time": 1681584462987}, "1.4.0-alpha.9": {"name": "@volar/typescript", "version": "1.4.0-alpha.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.9"}, "peerDependencies": {"typescript": "*"}, "gitHead": "d3993bafc6a8fac5ab34cee2ab5d682a5b405831", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.9", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.0.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-DpXzBDNKPWK4v6pcbczcmkil1tHyCSIS47jmzldf5k7BK3hH/qf1KfcwT5h6P8Mh/7cW6fcvcvtIoPcniby/VA==", "shasum": "6a2b7c5244a784f935ff02571641fe23889361ba", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.9.tgz", "fileCount": 6, "unpackedSize": 21621, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGGgnNB8h+lCRCpy9TJ/FXO7i0LQDs16HHegC8BVHx2AiApYBF4KHteM8ckVl7l0r5FCsPxT2oVkcif2X/pfXm60Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO0pdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrBhAAhVFpnNzZiyqvOQj80E40XvbG5Pel2yv/sdKPAilBmX5LA9Da\r\nDPCxWV83JS5RGE5PLlpXJiQrbuQG7nprL1JW+WN2fJvLvFZ4pdDP22l0TkI2\r\nALTn106wKVtjtp2BcScqXeUC5SVXGAx+4H4iFCYdhAZAp307v4k/c3wviIMM\r\nHyaYTzq1qmIiVRfYRXiwkC+G69LuRfqxqRevdF2IOnRqTz2vjnAVMy+ZxxPV\r\nQvdyxBrx1X17EgClQbZ2FxN4QE+D3fwVXPmFyGkbpOIx8QBXnRBV8AXTrCaO\r\nwcpEKguXD4qEfkC+jCbzr+tPUghNUnGgf64wQXuVFDgdByiKEhxK/6RvpLQc\r\nI66Y0477IK4Ty2m7OVGlZMLjUPEcPb0p7UAX/gdhs1A4TyeBT/axSRCgBhkf\r\nxlJsmpMRpPvvvBOLiPvMqBy85YdF4CIu17uZLgUfw6nY2p+7ot5iXMnySU5l\r\nexaDCYy/onCYKBuaAMcu4fluCj2hWw+16CkrPkXem+KEMs2HV6w8swe88jKX\r\nfJh1VOLiSeSoc8tTFzJ/x1Ao2BF+50hdjH6DXYouPLl9Q/pxH6osyykrtGdt\r\nJWTiPbfAOwPnrhn6abey2m82qTB6Aqrc+G0BlTROsorpv0jAhO/iUtxvQ8Pz\r\nW8/KVxga0JqG8Eloj2BwhF4avFatU6K/pgc=\r\n=tq9Y\r\n-----END PGP SIGNATURE-----\r\n", "size": 4731}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.9_1681607261676_0.7904555096286643"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T01:07:41.863Z", "publish_time": 1681607261863}, "1.4.0-alpha.10": {"name": "@volar/typescript", "version": "1.4.0-alpha.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.10"}, "peerDependencies": {"typescript": "*"}, "gitHead": "715bc5f17a0d1cadd4190712dcaff732989eb330", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.10", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.0.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-8HK3dpiPMFGNT+R8Xh4Iuapn/BXGuuGGTJfScscfqkLIXsBKO9Sesfv+CwQ6aXm5XYdmxBYktAZjAQhHFHHBAw==", "shasum": "6411a49c2dfa31f9c231384dfb63cda686f73c29", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.10.tgz", "fileCount": 6, "unpackedSize": 21623, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyzs3SfNS33HZ17eX2OnkFFI+J5rM5ThRz7gvY85Z1mAIgO6/9GNH/cw81FHccgVcqqYVsgY+buBE8n00bO8T4mQY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6c0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonww/+N7uXMW45//8K0v0EN1WipKMDTvkm7BC9qoTG743jSDO5OylN\r\nzb1OIpDnrpcrMKdDI8GZz3UFjKH+acJYm520eopuvmt23pcBkXeBcNfhO9wK\r\nSeFQ+HufyMZH4GaqKm9b71arMTJfQX/ZBueTRmsNUW8+ewVFEUSjC/NAyvMp\r\n8VC9mlzNMrWX18GnvvrXXMl/NuNPsNPsWGXvCgsy8B/LK0+9SJX6C8XSy5cC\r\nAHHYR+enlxnjSbx6woTpvP5P2A8Yg15dPEiUDOcNvOVPddMhCDL/Qdwj7ina\r\nDV5BbgjZp9p2Ih5HmbYgELWBGSNZQnbc8INp8hH7J475jgWUrpgRLjNXBryZ\r\nzYA8gpEAE44WNYAoS7H6d6FYoy7jspu/FjFeuHyo4cHV7J1saKnjOtKJ6eGW\r\njcb5/ZzNwEp0vb4Ck7KMtGwOjdN3JmOMmchnNd+NHOu0MxEuiZdNq+buZft0\r\nJyA8Ojb328tGvtoxN3tyQnYIX/UehaI/GNlaMaJtFXj64qXT5iMBMHdO6/IJ\r\n/7ZNK88IdQLoDSiiAL8THeoq+mol9IcRadl496HPYksRrBOrzzZ05vZ8MjKm\r\nPZp66y9O3PmSzl9eyrUY+aIqZC8KIWRy1q+dp9O2OGgQgr/34vYFRkBEpRTW\r\nrPRhbzCxIjGt0n86TLn6WSZAcWdpU1B3k+0=\r\n=p0/N\r\n-----END PGP SIGNATURE-----\r\n", "size": 4732}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.10_1681631028323_0.3649340241694623"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T07:43:48.480Z", "publish_time": 1681631028480}, "1.4.0-alpha.11": {"name": "@volar/typescript", "version": "1.4.0-alpha.11", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.11"}, "peerDependencies": {"typescript": "*"}, "gitHead": "56ab336278ed7f06851c3dfe1bbcd5b9a3112846", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.11", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.0.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-O3ppG3euXXRTPsmLaWuDUQ3yhMJK+fvRdTRrfcMj5utqWcIdNZvY5D5D5XB+QjwvqO2O0PD3cX7lGd1x7fB3Rg==", "shasum": "dcdb25e3e94f6bbc871ef3206ad776fe386d7667", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.11.tgz", "fileCount": 6, "unpackedSize": 21623, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfTpxXtmeaHT9TH0ePBnZZYw9+/SXPDRpoP45JV1ejzQIgKysj7MmbHkZsuhPI+/hb0e4a77YwScKegVCUR5RtmQg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPUHSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr30Q//eNb+KZu8jR6ZvH3FAeESbgAQzhzafyC7GwU7xCFCXzLdalKP\r\nw+mQSE/PayEWyn4sHgQqgf+WcpXVkbEs9xEYuV37s/RyHkB9jvrcqMFzivO1\r\nkFtc89KHvv4HdkqLYAFIQR0gr08Jo0AZ6dN8CL6C4s8nW/Q3wfCGZY09EaAp\r\nyNnMkxPuhPVClEFcGTtXuzz6j24Ln1FaUZzRhvNxFD9qSwCZZCGaXDJ42TDy\r\n3rBrHcH/90Bl5tVGcveq/LyRo1APol4j2TCTPUSw+sSbWeSUrQbv+sdyrJ1x\r\nMocCz2KNPC/94vmZzWN+xQREN0weZsh9MSbBUqRN8evzwKQjmzbQODmGrNqR\r\nJJzZsT5e5AWysgR48t/i7PEoxNfjv0Hspxinsn7wXitn3Lmu8MyktuWFLjR3\r\nzcTP/aP+U3x/KU0GgRuyHK3ACbP/sX7LigC1JwYhc5E+z5MW0dGRNXfoNcDF\r\nq/UYCF8j0p3onZFoqxm0mvfnaR3SYN1vn9LqFrBIOebTooFAgEuzBwHitx1r\r\nUCwHVZZ3BY5H5PinwSEvNgHSZcEW1Scy1K+Qj1eGD8g3kbN8RBZLNeBq7pK+\r\nZ4FFvpzCLL1qVpUvBWcBFz7LaSAoHnjrCxyyW15GeZhLcJaNZCCQOIUV4vEL\r\nG8gbOIBAF9EjAp8HlPMhr9+5EDjm4OP+90c=\r\n=qghK\r\n-----END PGP SIGNATURE-----\r\n", "size": 4733}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.11_1681736146460_0.8693703282707717"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-17T12:55:46.663Z", "publish_time": 1681736146663}, "1.4.0-alpha.12": {"name": "@volar/typescript", "version": "1.4.0-alpha.12", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0-alpha.12"}, "peerDependencies": {"typescript": "*"}, "gitHead": "5ae44d90936045b92ad9debcfb9ae952bb311fb8", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0-alpha.12", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.0.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-O1605kA2H0k9TBqupGjD7/2qk5At3c9kluej1wSjINcgninpB4kSVF/wn0Tp5uWYK2Xn49usEBeL2fBux/fceA==", "shasum": "5b3e2a029f3f9c9c7c61e8ec6a8288b4fb74cfe9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0-alpha.12.tgz", "fileCount": 6, "unpackedSize": 21623, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF0Xm4jQSMt690jcccK6W2sxtR2o4974f90COe6pC6SwIgJAyQPEag16pzwxPVD+GnYwRiPSuoschgLBkc5zXLckk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPkK0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrezQ/6AoMfxVjgjxAnDPHpv7wDVBYnHV1RJBj1r2qzOu3p5YZH7jqQ\r\nlg/KJ99RTT0XfI4b4PvFXqmiS4Ey9rbz2XAIE3qNNVkyOYijGehAWQGsEu2H\r\nD1PbimyyOYHlIqONQhfC0qUq1Dqz4nBFP8UZGoc+3yo0jqPQdmzdAPIueTja\r\nk4mGT/9SAyRG1IH+y6DWxbA6dzYpIXe2ZqtomhMJ1xyyMBH74TiNgSXer7PQ\r\nCh09gJPR94hs72HTOdXa81J/Owgd/ZUWNarYc7FddrwJDrDjZZc80s0VgETH\r\nDifSoxFPnZBpb7NSvh/xWv3jSlTm1hamfZI+fXG9w1FeYQZb+ceTZ3QXeuWq\r\nTItYP1ugAAgngFybCfuHRm/QqSsSYSipJX5mkC6sQZDh0jTV8/T9i7vdEyA3\r\nkDIISWK+Stc9t3Hjp40CHwI7Aq3KM0nze0VNlZJxctY6b/IhhpJ45D2nKRta\r\napBWxk7V0qny0oucAm2wctOjuSARaYNy1ANKaWgy9lh6QNO/+syvZvaxdetW\r\nmkOhAXK2yyqywEp4YXeDrU5p9Fnt13GuEwe+rowYkEJCpmNEri+V2+gRYowg\r\nGhSiEemc52D8TS2PymujHhVkIRce8CmoJ3CAnVvzHDho/Y90MVu1dccv+d5s\r\nOoHuO01qp8sZszPH6b2zi/tCDXfGZcXu2JQ=\r\n=g2Bq\r\n-----END PGP SIGNATURE-----\r\n", "size": 4730}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0-alpha.12_1681801908742_0.16661028710448433"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-18T07:11:48.885Z", "publish_time": 1681801908885}, "1.4.0": {"name": "@volar/typescript", "version": "1.4.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.0"}, "peerDependencies": {"typescript": "*"}, "gitHead": "9bf14ef0d08c53f9ae32cf538d9748fa91cc4ab7", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.0.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-r6OMHj/LeS86iQy3LEjjS+qpmHr9I7BiH8gAwp9WEJP76FHlMPi/EPDQxhf3VcMQ/w6Pi5aBczqI+I3akr9t4g==", "shasum": "11473a68b09d8e662f98c83ce64e0e614deb2ac9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.0.tgz", "fileCount": 6, "unpackedSize": 21605, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHaV1LxfAhsA+cH7MtvkLdSYeFTMyie1pYd834O2maLwAiB2/018MseHgwTj+sXHm6IDiGMaDibGvH/rSlLUHSzX8Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQVjXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr49A/+LsHwrrB2nxu3mQtVYhSlll6DiohBIUs3dUsETYBQ1kYXFmKt\r\nQU5r954tspd6FbR++dH6bDurIFiHZpWUqi8DedjHi1bK4vfXsNILnxNe85bf\r\n1UvMTSLHOYtx+/9PlmTm0eQMoupOolKmzspxty0ynMrG1QCGJCkMmhlvY+EE\r\nA0u6htzaUuAxjSVKuJlQzpSCK/gUdkJDWtiCQ5agvs1zeWDLl2FSZdurUdE2\r\nN+hrMuWeeuPWCeRQnpTTSQhwyaT49iy8wkvU+5Aipj5hGaouy1UNDB3Gngsx\r\ndrw976oS+Mga3Ubfi/jVZZLlFFlpCxvZSWdfGT0tg5/O5P32FLWnqf1bHsSv\r\njYLNiIGWixZhe5/h87Q2SmtlXpoH0w3ADOz2F25L4jNysNcMI4Qs5JjNN+Mu\r\nCzYf/nMg6nQnY37IJMeo/GAjy7l8zEXWB5nDSBju1gxfB4QEwoavJYDVEc+G\r\n/bxsEDfEKcVwXe3Sim7Sl+3TEgm3VXV6A3MHuqO54kiZU8ttKNvLOSM5K7+b\r\nGubcL9cNC0UxzGRQThYn671+hOwnrIr02tlU3CYid6w0AtjzlhZ29Y2Ig8ly\r\ndhrODTxT1Znm7I59SxQCYrESA1XrbxR1c4Bu7CGKEkKacQ4slqdm557Lgwnp\r\n2xlIn0PfEy9bK/pHbrxvZBNGiZ4hFKfEeQQ=\r\n=sKJM\r\n-----END PGP SIGNATURE-----\r\n", "size": 4726}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.0_1682004183504_0.4848704471176919"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-20T15:23:03.641Z", "publish_time": 1682004183641}, "1.4.1": {"name": "@volar/typescript", "version": "1.4.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.1"}, "peerDependencies": {"typescript": "*"}, "gitHead": "2c8a783542ace3292649a04490919b515e387b9e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.1.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-phTy6p9yG6bgMIKQWEeDOi/aeT0njZsb1a/G1mrEuDsLmAn24Le4gDwSsGNhea6Uhu+3gdpUZn2PmZXa+WG2iQ==", "shasum": "a013419e6f029155e5467443f3ab72815da608b5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.1.tgz", "fileCount": 6, "unpackedSize": 21605, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHu46mKqDykMHhMzVDo0Rr4jeIJDEhQ0Aua7VA2mwDjQIhAJaQpm6oKxG+GfYv6GtlEuFA1nUovHGgtJlVngqDnVlz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQ/xtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotOQ/9G9getKPubRaKnPDuKtM49dPIXWpZxRA6eH7x7BVcds+wZMco\r\n2wwCbawBlhmlT9FyBWwcRwHXBwuCbU+Xd1zfydnTpfVOU7wb2qN8lshBOJIR\r\nm4Q5OuUYdPqTJE1TMWwCDFK0YirUCoFlRuGiGGY0hC/QW93EwdHvt07z9Ohb\r\nrJnSMoIW16N7MwSDHMKdVGoBuZ8W3oevLPa68GRCFvXKR6medQPpmvwIKLs1\r\nspoAxpMcQd5ElQy8wAkOYqVIMBAK/S1+07Z7cPws2wFkBoagaD5g7lbb7RED\r\ndHUkj4cKw3fH6o5lVGvSgOOlFQhZXJD6msKY+Ya9JyPdlVfBGq7qGYWrV0Sv\r\nokHZ6t/RfaFjAQ8v2aLHK10VL7kZPmANVQ2iAedZWT1f5Y6F9GMIcMmvqSGo\r\nIAQfIiLP4dHrb83OExd/gHUxmI2SUjNOwHPSCLcaZhjHzp+KAhJGiCUhD4HY\r\n5q+mbWjxCqVNfZy1YgwgAHVvXYBjRwidBNL9KIVjoDVYuuiVv5qaTNOsBqHU\r\nCL1cMTSJoNZMBnTmiQuyQidxk1iYkeqC17E47c0OrDFLzgL+C+kONp2jULhl\r\nL0mLMFSV4gc5efbNZ6Q70u9Ht9ZulNiQmYcHGqHLGYdyq/yxUJCoEwr+u6q8\r\n6gO0nBF5q63WLKMpifwOyUv1SQwem+mvggk=\r\n=Ey5j\r\n-----END PGP SIGNATURE-----\r\n", "size": 4727}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.1_1682177133453_0.6538261742327338"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-22T15:25:33.585Z", "publish_time": 1682177133585}, "1.5.0-alpha.0": {"name": "@volar/typescript", "version": "1.5.0-alpha.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.5.0-alpha.0"}, "peerDependencies": {"typescript": "*"}, "gitHead": "a4e1b3766ccb5affe1613cad7630513b9073d275", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.5.0-alpha.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.1.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-CjbRx6tV91sffbWsv74YElEIzLtFW5bfkLydw0Qk6rs+sGCEMvjqJpQzRhr0hcocQHrvQAytGUq6CkgF9IemgQ==", "shasum": "a53b0dafc7e55375f79263e24d34b6bf5b256806", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.5.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 21615, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCySVSDSmwCEWdpwCdsB4qpndQwxLDVj24uQTqDyV7wWgIhAOMOa8JQ277IJK89H32S/XTnWB5axMLcPnq7vyLsDopC"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSRQ1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJHhAAkmRC8+ZBNmZJ9Ej2CEYoft/ylgh+Kq5F6o9ezk3JNm+tHrHr\r\nCz0XgvW/emdBd13E9I422NnqjNiB64bAqgriFPxmEwgZGj5DLWlkvuyl6xv2\r\n3O7fb3PObTjdHW0mjqI+PNFYGwCZgbSGv+kSN4LfUgE3xwltYloKf+xsnse1\r\n2p27dmNvsSJJw0K2QOv/7fgrYBypunXLHGam409QxFR8ESkOjN9giSwj119V\r\nqIyL9x07wnGNzBCH3tCGfmHt3L+lRuR8mj1PelkqZ8yctei2n+mqHGjGvimo\r\njNdXlH80Fow2x5KZAI+6vRbIqs+j4EcNVOGTafjopi5j/sxoYv82ghJ5wbS1\r\n2+qWUvs6dYAdH6wjilkU6BrkJXae/mNiTPT7YUvPqmIflp2C1Fv7DWgOqqAs\r\nMsnEtvhZTzylv6PwWUJYkjdcRURDsjD7+xDk6w2Tp8KohqhIkphiaRyDjUNV\r\ndJor2ezFWZDdvucoGxXSVkZqZixeTpF7Axarc1lNod4HNHJ4wB/gyx/pTboS\r\n1g/BHCmQl3bNGNepxO3zfbmVrdCJYUK6rV5Ps7fZ7WZiW0LTzfk41Xy4YfUK\r\na6iUaFkJkP4LiU/c+U1IahaX6B983NOx2khIXoQADW18LReMmzQ/DjLJ+2LO\r\nY15qvhXSh4FRaI9jSnWDzpWqlueEVuxl2+w=\r\n=/jI5\r\n-----END PGP SIGNATURE-----\r\n", "size": 4725}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.5.0-alpha.0_1682510900862_0.8169149133377998"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-26T12:08:20.980Z", "publish_time": 1682510900980, "_source_registry_name": "default"}, "1.5.0": {"name": "@volar/typescript", "version": "1.5.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.5.0"}, "peerDependencies": {"typescript": "*"}, "gitHead": "1b9b96204eda517c325e7e8a35b28b5dd220d482", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.5.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.1.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-bYw/sXQc9QX7hhSPbtQkBBXkWUw++ajVQyshWcFVAPkil5G2WfV/K2ANHPJvwXsoXOAFtsbTxcs+VD0aFFZjyg==", "shasum": "34dfc5b757117d7c158b631dd6ab0418cf3c888e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.5.0.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDp5D3IK6kDLQEfQ3vrdSECtVX9YbyN92EqBB6Ums41PwIhAOnB25HY7iVO5/GWSBSEu05Bqpk/GEMkKpdihs3//Uzh"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkT8PnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVfg/+M60zT8tN0tWsmxZh+P/UesFUcaLpNsvVoAvUtmR7Y81Ybpl+\r\nHxb/C0p3UW1zYwBfhRnJytckMcFLLhuTU6VAGUlylyySB8OB2ou5djhuNf12\r\nDKTWoyqm3boUnaW3r0feDsia7nya5ZedMcXPQSWoqVpyo3Yb6PEN9zWWiwfB\r\nVjt7sPL5oNBOF+/zuHhh+QxlUium8dO2+JB5JeHnOivfsZN2TzGqegn9YUS0\r\nmXl/P6de0h90stMfp4rim374xmD8/5Uica2xul3Xmv6KPXMpvOMOd2auDWU5\r\nNgEINBaO1y2OyiFvuPEoJgqJZSa4lBas28hJdnPujgwejtlOoFJvGVFcz4ip\r\nPM0TorXW65O5IZK9+odQ06YbVyZ2+B7xKeo3dlwUbCdDhWwZl0/TSKGf35ZT\r\nCW2t3hkkIUcCiTg/K3sqoZbC63tLeaOot9AOl86Nuu8e2pZY3qWi6yOpXOzK\r\n6BoViXnesJO90k4AbTk12jHSbk663go3CRZ6E903ANjYuoKVPZJAYEjQFWGF\r\nKnwNsEB4927KE2n9rynBJZxXrAswI/EUzJkLFo0bIKk/D9mZ8Y+7y6UfX6MN\r\npnr0rNVF/3vwk3RQx8rPZjlf2IsQDfweEd/no/Xqbr4NBpTahsA590K94hOn\r\nc2oWsk4b90QR0DzEhj8fZFYXxW3lJmo7bZc=\r\n=IC2Z\r\n-----END PGP SIGNATURE-----\r\n", "size": 4720}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.5.0_1682949095350_0.7257852849555286"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-01T13:51:35.536Z", "publish_time": 1682949095536, "_source_registry_name": "default"}, "1.5.1": {"name": "@volar/typescript", "version": "1.5.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.5.1"}, "peerDependencies": {"typescript": "*"}, "gitHead": "5f65e85adee6030451f15f1ebb5dae40b38e9e40", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.5.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.2.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-CPYMGkAz+u7dIqdQGhMEsUSZHD76rekBanGbUM45XfFo2+nj+h/u0xvE+/rfbzxCzoFGe0j6PMh30kRSx5d3Iw==", "shasum": "5a43b9b0cd0a63fb17d0f2942ab006fcdd862299", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.5.1.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDqw+ynG7ZQLQGpejBzWR6gN/UNnhRscgAiY14pA12ANAiEAw7frCYnIzRUo50Y9OocegnhNsfI1LHChV23dT1xLyl0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVDkHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquQw//S29PBqOyjhZtf7YlAnRSLqeBOb5LIzYY1uVwlorCFXs893z9\r\nWyN12fUOISVPaplOe31oaGgmBlkKC7cRtNrz39gh2zzXGW786husqmpcVi/N\r\nhiEDDkp/0MLqy84mxXIMhxeRRJYQsK67Q5JBkxoOGSdxVajsdP7A0MK/f4UI\r\n1v+EhNeIc9Bp3WRiXlWgu1KGlcysgVor60CN039xMD71XzMBVfCmjbP4jp8l\r\ndlR+sfytXLwyOmQAeCwF1JAZ1TOnAnADCL1y64lxLB/RMSMYu85QynMHnjVC\r\nCRTYZM8B7eAzavOZfFlDaIRrgBqgMsEf74VP7XVfcO4UMcgoM9TVTuXwzVhc\r\nf2urGsx36p7EqEBpaNCCJeUo298x5ETbfd6vgBItIkbzOJvKswVEgxuv5Pvh\r\nVnwASRzQ7PTev/BANZjEJXjixcLmalZbJptlUcUMA1NwySOksZkYFBlJe9D6\r\ntSnXJcnqHaO1lXM4WEASryRMYdlUTJjObK37LOr8Qs4OJQN7AGLw6k7uodbV\r\nx6WqbGBFISAPzS/2qgWQaKzDFFtCoj0xCPQC3HdjTpSoLSjSOrVEQARfxmwj\r\ny2tbIlLN5aDMVw8ItPkHS5KzAQerTDUgGRb4THsFHDSCpzEmTJp/UFc8sNtt\r\nuYgTJGzuUcwvEgWGDwOcv9A/m982jR197sc=\r\n=YgFH\r\n-----END PGP SIGNATURE-----\r\n", "size": 4717}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.5.1_1683241223411_0.19382155131620804"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-04T23:00:23.558Z", "publish_time": 1683241223558, "_source_registry_name": "default"}, "1.5.2": {"name": "@volar/typescript", "version": "1.5.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.5.2"}, "peerDependencies": {"typescript": "*"}, "gitHead": "e58503bf9700e96557e583f95e38a21c8f7b4a0a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.5.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.2.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-u5iMTebNF2Bte7xjxrBZawbwa6kq78cWk2HLTYqzYGaiZix33yKmkvweo3cPt7J017jSZADNg8Q47bYGGne8rg==", "shasum": "7fccd446400487bd977db1184131146e0e0be058", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.5.2.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHtMxMth26HzxMun3eXZm+hjMM0EhMub9EG15tiBD4yJAiB8n+u30CLSFImElJs7PXTFhapli3g9uv0TVMIPhtMfYA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVFbZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWOg/9EkNTrNqFb05roteGyVvDTzUW93vHZfb1ogs8Y5jY5f71+km5\r\n3n+qDjpC12lSX8sUvD08klEhRC3BM32SXBVF/ktXAyW5Ofl9lG5gFiGNWGEq\r\ngP8MnHKaAd86ohpr6GFtSfW4TBXvT1gmg6sEwIR4+n68iKPGt+cS6K8FqFPH\r\nDUtGdCKPnLf1a51gcwG9nloP/Mdk3SHzy+FdeWsGjvb52OWDgj0bKsrQtf7T\r\nSyQg/7QaO7FCTC52FcKKXZFKHmw3sef8U3QJNq+RPEHtwQ600Tonk+EjePSl\r\n7mECZ/Sn9IEl+2hUOhJsyEmSLBmcveLmcV7801J9ajMOuhrK0nsZbVTZyKG3\r\n50zI98B3K0cJekMI59pe6nyuXmer18ffSj4gUe1eufsCV2icMV3nfDn2ASAx\r\nOhzeUmZWD3z+MQcQ/foH+sbW1IEflRExzFcyb3EGX57mjtoX2MBDZbMNtRo+\r\nEA7fPlCbf0if39A6yiygmMr2GOiIMdVIt6axr8uwfDQ9xk3jmU5oN5sQQj00\r\nLaN1w793bYhynuKW6cXeaAtIeUnv6xYWbenVRXD317AH+NY2dzqOHPXIJsS7\r\ne7DPO39GUP/FtI8WdreMmIosTCyy1LMEd+uAehrw02JAU4mdVllFKFOBLADL\r\nT2eKoj1FdhFBtuHQ3Ks4y7XMwvfSjl9RJ/c=\r\n=U0PN\r\n-----END PGP SIGNATURE-----\r\n", "size": 4719}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.5.2_1683248856986_0.015121750952864499"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-05T01:07:37.177Z", "publish_time": 1683248857177, "_source_registry_name": "default"}, "1.5.3": {"name": "@volar/typescript", "version": "1.5.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.5.3"}, "peerDependencies": {"typescript": "*"}, "gitHead": "4d4849277f76523ad1f2719cc94e09abb8c7c77c", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.5.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-NZ4W44MNicMMm1wmQMZE+FHFPwUhWmxvVAQEw0kQtoFCZ9M1hGESTPKyOZbnuSb28nN0DgS7c2FNXGB4tLU2KA==", "shasum": "17c7a46278286fbfd96cc2e05ea8161a1fa7b31f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.5.3.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCHx5eu+Mr0mNKWlPtsj6x3GMhTRDfqWwnWHs67VxPb9UCIDxP4Z8DoTzfeztriLULKmSfIPxUmxXCOuhxc4qs+PHd"}], "size": 4722}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.5.3_1683336302276_0.5898742005603774"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-06T01:25:02.514Z", "publish_time": 1683336302514, "_source_registry_name": "default"}, "1.5.4": {"name": "@volar/typescript", "version": "1.5.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.5.4"}, "peerDependencies": {"typescript": "*"}, "gitHead": "9b96450d7e4d87d1b77af45a5ce796724fc606ce", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.5.4", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-mVJJ7ZGY2RBDj6PAircUvLBLbFJJRNmqN5IYNpIUyFwnxdKxn1t3NhxaLYSPi/A0gdMDbCBtq0a4Azc0IuIOfQ==", "shasum": "90c315152cb099cc072055313ded9a16b2a50fca", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.5.4.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDowJbXuC0qgfHnVVX7qkhtz99Ml8SK8Zu1EPljiUcGAIhAI4uL57r+Kq9xtKCXG69iMlsG1DSlxCzvuLNPJBMxVVH"}], "size": 4721}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.5.4_1683400720580_0.8100249040323004"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-06T19:18:40.811Z", "publish_time": 1683400720811, "_source_registry_name": "default"}, "1.6.0": {"name": "@volar/typescript", "version": "1.6.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.0"}, "peerDependencies": {"typescript": "*"}, "gitHead": "3ef1b023cb1f77662fd7bdb0f1099ab726f772d6", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-zS3Kx+GeEZfdCwCj6pOjsLzE+p19U4p1NJSMPRMZQn09XvJkY7AQuJMCoGDpUWT0GJtpySe0SXRLXSRbSb97xQ==", "shasum": "34e6f5fe706c4ba7505bf44fb1296f438be46e3c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.0.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICWUvLzktH/sN6PApGWzkzCiTR0Dz6TmiNTRhvKAKLTZAiAu52ZpqDjlJEZBLz9CncXPH1GM0GdBDL15MCA0UKNAKA=="}], "size": 4717}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.0_1683646851732_0.46989151537839646"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-09T15:40:51.887Z", "publish_time": 1683646851887, "_source_registry_name": "default"}, "1.6.1": {"name": "@volar/typescript", "version": "1.6.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.1"}, "peerDependencies": {"typescript": "*"}, "gitHead": "1e1e99f43ff39db5432a33c8013eda706e765001", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-6MGXCy6Y7yZaZHTZa3qPwLdGV425+l31X476ixUDBtUlK8R9xokkNXT8Jbm06zGESSkKuxUTcIT3JSDZmOfhsg==", "shasum": "9b201f6736cb5d5769d255b522a2e994ace8a939", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.1.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIyMv7BG1/Omd5/kq2S3o52KGplH2j+VLgNOyTKzz/jAiEAlr9SQpJ8ttqu9s6NiBbOxhNOuQsOZSVm33ya1RdBrCg="}], "size": 4720}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.1_1683681968040_0.3381696237552516"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-10T01:26:08.222Z", "publish_time": 1683681968222, "_source_registry_name": "default"}, "1.6.2": {"name": "@volar/typescript", "version": "1.6.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.2"}, "peerDependencies": {"typescript": "*"}, "gitHead": "1a044d2d633de5beb7e1a14689141037854ea441", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-ONXS2DT3uJm9KEheykdhlgViv2miZbuKfCBqTRSIB7bTFpMv87nEWACyslzKu7yyO0B8rH2W0UixTrIW0D1ZRw==", "shasum": "0bf09b9eddd5f0fb12086ef29aaf9ca66ff7942c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.2.tgz", "fileCount": 6, "unpackedSize": 21599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGOFwh8CQ5XPfHnEJ84wrqjFXG4sR943S4rBUimroOIcAiEA8h9fH8bjUWOvcf9hOvWKaToR80+WZBfvgn4vyZysuy4="}], "size": 4719}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.2_1683792153705_0.6541475656109814"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-11T08:02:34.003Z", "publish_time": 1683792154003, "_source_registry_name": "default"}, "1.4.1-patch.1": {"name": "@volar/typescript", "version": "1.4.1-patch.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.1"}, "peerDependencies": {"typescript": "*"}, "types": "./out/index.d.ts", "gitHead": "2c8a783542ace3292649a04490919b515e387b9e", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.1-patch.1", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-oBFudGqb666bsF2UOU3CAgN+dWtF/mJftMe97qQMmZw9E3n1kP0+nxltV0qkUTVFbxY0Pm9pP93PhsP0QjaROQ==", "shasum": "ec66504e666632ee92adb96d98cfaeeba9bb1640", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.1-patch.1.tgz", "fileCount": 6, "unpackedSize": 21219, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFu060t8uxAcjtn8ChR24X6+yh+QrUFcurUyAih5CqywIhAKZuNNZhAuNQ/kHlXY6ytTTqjdTN/8FxFlxPhgzBDl/G"}], "size": 4610}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.1-patch.1_1683956247408_0.4914623224973038"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T05:37:27.600Z", "publish_time": 1683956247600, "_source_registry_name": "default"}, "1.4.1-patch.2": {"name": "@volar/typescript", "version": "1.4.1-patch.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.4.1"}, "peerDependencies": {"typescript": "*"}, "types": "./out/index.d.ts", "gitHead": "2c8a783542ace3292649a04490919b515e387b9e", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.4.1-patch.2", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-lPFYaGt8OdMEzNGJJChF40uYqMO4Z/7Q9fHPQC/NRVtht43KotSXLrkPandVVMf9aPbiJ059eAT+fwHGX16k4w==", "shasum": "89f4bd199ca81a832d86d1449b01f49f2b72137c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.4.1-patch.2.tgz", "fileCount": 6, "unpackedSize": 21298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDG9GOzKtdByGSE3LnorDCyFrNbjNu/BgRx/YFh02yI6wIgIyeexLYt67kZ9G19w5VU3v6znMvuHxR36621KXa88dI="}], "size": 4621}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.4.1-patch.2_1683957353497_0.8060473822587926"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T05:55:53.690Z", "publish_time": 1683957353690, "_source_registry_name": "default"}, "1.6.3": {"name": "@volar/typescript", "version": "1.6.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.3"}, "peerDependencies": {"typescript": "*"}, "gitHead": "79c29a4632028168280819ecd6c0d68e31c08848", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-mW6WUyddV0xm99N4b3A5DhAWD9NCtOU8YMP27hMoggQbu+MkJ8QEyU6sAbUi8lJUJcRzurMy8DMSTdRmGqAFfA==", "shasum": "375b0b6bade6af67f07ea511924d8db7b9481145", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.3.tgz", "fileCount": 6, "unpackedSize": 21340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9wqd0AMa/l0YTBbgMwwRG1A1dn231uBtU4r6kp4nqQgIgXHysLrxy3cpbe9SovX8+x0VlKJG/bZIvcd1ZqzWxH1w="}], "size": 4729}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.3_1683963194309_0.8466527022541843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-13T07:33:14.468Z", "publish_time": 1683963194468, "_source_registry_name": "default"}, "1.6.4": {"name": "@volar/typescript", "version": "1.6.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.4"}, "peerDependencies": {"typescript": "*"}, "gitHead": "653d792a5b1498f5b063f6a81a500a1db27b1ca7", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.4", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-//D3ubbddIW21ci59qUbtXuy9G9eR2z40Vp1EwN7vE0utssgN5Axn+1douWk8T3br3iT+wNDHdWzwL+8haUHrg==", "shasum": "04809f232b64378a9db37b40a4ef1d327e96f040", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.4.tgz", "fileCount": 6, "unpackedSize": 21340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2/Q/+aTXIMR8qZqhALzcDSjFOyEXuv96KuWQlsTkoMgIhANQp9wHRBtOlnsNqrTpIQZeDAmRhV6E/lMRMuGAVfILo"}], "size": 4727}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.4_1684234504187_0.6930724364743284"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-16T10:55:04.355Z", "publish_time": 1684234504355, "_source_registry_name": "default"}, "1.6.5": {"name": "@volar/typescript", "version": "1.6.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.5"}, "peerDependencies": {"typescript": "*"}, "gitHead": "7fa98d2d3bf1fde8aabb39f62d8522bea0c23098", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.5", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.3.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-U94iBb4B56p0DuVBkoUMRWG9IV4U51JjJ7S6nPazuqi4I3p0z//2sunPUTZJ0nd+eoLXy87X2Xpv5o8p+uuOXw==", "shasum": "483ad33578e4e1948faaff47c5f8f452e9e7e478", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.5.tgz", "fileCount": 6, "unpackedSize": 21340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+PCQKMptvhYgwwlNRYl/HGzp7tiZqvyH5fh6VdoLI8gIgaPN+/FNdWxZZDkBpfzU7bPHeXXUvzi/YvU2AECtbN2A="}], "size": 4727}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.5_1684394103040_0.3176803240081869"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T07:15:03.197Z", "publish_time": 1684394103197, "_source_registry_name": "default"}, "1.6.6": {"name": "@volar/typescript", "version": "1.6.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.6"}, "peerDependencies": {"typescript": "*"}, "gitHead": "7d4814e11d9646df4a197866107db5b6c335f964", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.6", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-2KkWVINirmj4vUVt3znbKdp1YcXaF6tbnqazSezxs7ejrpynvbuTLiOvhYSrXPxEMkpJrLvnqAAOOmuMAkTxww==", "shasum": "a2891e7d81f008fb4ee200dc92608850b495b076", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.6.tgz", "fileCount": 6, "unpackedSize": 21340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA83hVNUde8b2JBDX2ovKspqXGjg5jl+HQepGKbm7tXwAiEApAg4wxyv7yKuQl616LBMOUGDvkHyT71J/vzVlay1g9A="}], "size": 4726}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.6_1684428016284_0.05170842234360329"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T16:40:16.459Z", "publish_time": 1684428016459, "_source_registry_name": "default"}, "1.6.7": {"name": "@volar/typescript", "version": "1.6.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.7"}, "peerDependencies": {"typescript": "*"}, "gitHead": "17154cd7d468c5b45baf50ea4b2e0f83406b6c03", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.7", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-RZ/6sOxS4zpSohAWFB1LGY+WfEia7r6RYsh2V/6hZRc1i0nFTjLfA9yu0IxwPalZFbx3DJ36gLEcx0F0urKZEg==", "shasum": "1a99fe6df222cad78fd5558c8334c30a6b170418", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.7.tgz", "fileCount": 6, "unpackedSize": 21340, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDupTqqVmmZxus6fxmKUmq5uHl5TrvaAo0Pn1pbXqNxOwIgN1v2o4+TudcYmp2La5zijZL3XuUG2YjeyVJQBrdB36k="}], "size": 4727}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.7_1684430268136_0.6343267629723257"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T17:17:48.343Z", "publish_time": 1684430268343, "_source_registry_name": "default"}, "1.6.8": {"name": "@volar/typescript", "version": "1.6.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.8"}, "gitHead": "3d269c7e69850ac5497469bede13f057db8a4980", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.8", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-Hy0dVtrfwehCtWjSN+WLBVz8QGbffhTww0HuigS+hqDBv+I28BitErTKkUZo9nExGqqkFlt6h1VIMR5+9QAS5A==", "shasum": "0882f38037b6a242f90fd4cb886ddf91c564ccb6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.8.tgz", "fileCount": 6, "unpackedSize": 21312, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH09opywq2nlIY5dszabbY38ECMCxmBN25MOTqKRSzIuAiAYBhv39CoWNVcWCQv12MbTlNV1y3iAU8No8JN0tDig0g=="}], "size": 4690}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.8_1684568052025_0.3185412607340947"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-20T07:34:12.161Z", "publish_time": 1684568052161, "_source_registry_name": "default"}, "1.6.9": {"name": "@volar/typescript", "version": "1.6.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.6.9"}, "gitHead": "e676fa08e4186bd2f8cc14861ef65f8d8c855ea1", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.6.9", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-L3WivzKVK5h/esfYdvnMXiE0wcH+wNalgFrpknuYgGraXNw+wGDaizEdVzKxyQYjez4bz/Z+X19YT2vTUGNV7Q==", "shasum": "3916f2f0c5e2896edbc28a6255674e011f4b3a9b", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.6.9.tgz", "fileCount": 6, "unpackedSize": 21626, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFLNjNJFpO4Qbt+iLOMr9dASEOLwlJjVLMi94Ro5XORAiA4odQ/AuBah9NfEwAT7XzmzPEtrPI/zOI4LTjG5YS/Jw=="}], "size": 4765}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.6.9_1684714165203_0.6446997574702871"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-22T00:09:25.385Z", "publish_time": 1684714165385, "_source_registry_name": "default"}, "1.7.0": {"name": "@volar/typescript", "version": "1.7.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.0"}, "devDependencies": {"@volar/language-service": "1.7.0"}, "gitHead": "38ec4f727b32dbf2cf3284ab02695e3a8effacc5", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-yDFBoptoa12GKsvJmNWaNYJcdECwpSKw0e7pp4CUHRX8hSzu49JZQgEmsymHR7dIkLvV9RfwlHux9/4MXnGJBA==", "shasum": "658fdf6668c964a69ccbb0cf6f72ca43805cc3da", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.0.tgz", "fileCount": 24, "unpackedSize": 113655, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG5fo5p631LFOXz67yFhde1FodBjSHKs8Brb92+KT4rIAiEAupYsuH7eeYrdMR++ZF1de6DlSatcrSYxPDXW/LBrs0g="}], "size": 25737}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.0_1685721175405_0.8376683002969334"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-02T15:52:55.568Z", "publish_time": 1685721175568, "_source_registry_name": "default"}, "1.7.1": {"name": "@volar/typescript", "version": "1.7.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.1"}, "devDependencies": {"@volar/language-service": "1.7.1"}, "gitHead": "6d092d684c65ce43af8a401bc90d0b2769a2bf49", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.1", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-yuasRxO5b7KSGGHRBPZ9BydO2DrgktJkBWnp21iJqeLyyfm+wzgjuP0qpR1qKLhUZ5EM2z86I0g8R2nFgMJEWw==", "shasum": "ac672ce8475f28d3fe6e8eddeb871168d76e8c38", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.1.tgz", "fileCount": 26, "unpackedSize": 114674, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF2CFY72kZbiffGOfOTPs9y1uFsSE/8oBdFhDpoqJ0IBAiAGmmqw6BunmWytEUNiZcoNnqUy/i1JmNQJTayP9b3vpg=="}], "size": 25921}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.1_1685898432182_0.6240255977065421"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-04T17:07:12.359Z", "publish_time": 1685898432359, "_source_registry_name": "default"}, "1.7.1-patch.1": {"name": "@volar/typescript", "version": "1.7.1-patch.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.1"}, "devDependencies": {"@volar/language-service": "1.7.1"}, "types": "./out/index.d.ts", "gitHead": "a30d446e8d4047a0a41c2afef04c9ff4e26e90c6", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.1-patch.1", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-MtqRi7LRAAsWBGdMnLuRDRbDZyEaRat96k2CoBy6CBxUioJqGd87TZXsmxZHE/h1dEwA9hzo+h1BW09QYMtyfA==", "shasum": "09758050ebd8b023227e7f0758a9e6fbbf113305", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.1-patch.1.tgz", "fileCount": 26, "unpackedSize": 114742, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHJPgBcA/ExlKOoHbZYrl5N0bCoMNAszHN32VSVQcH/2AiEAhsprnc2vyrau2e4io/rakb7ry0AHV//jQS/Q9Xhh86o="}], "size": 25703}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.1-patch.1_1686007264118_0.46575267230477135"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-05T23:21:04.330Z", "publish_time": 1686007264330, "_source_registry_name": "default"}, "1.7.2": {"name": "@volar/typescript", "version": "1.7.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.2"}, "devDependencies": {"@volar/language-service": "1.7.2"}, "gitHead": "e6406ffb254a1be0abbdb3eb101d8cc7bf862b18", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.2", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.0/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-i9RmP7c1zugYDPup39XZr7EwFkya86NyYQvLUt2AzazyG5D9u1ornUSZKmCemBmLnCXwWDWuDXYS6AXL0HgIVg==", "shasum": "fdadded89a2936273ceff84377f960d36df859e6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.2.tgz", "fileCount": 26, "unpackedSize": 114790, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiaIvzHtWLQbJDo2FkLwT8AYdmsAh1ppKNymg5L999+QIgPp0xtgYf4iqLzg/FC3UdsHXs9dSAwCsUtFNeLVscZKc="}], "size": 25940}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.2_1686145379086_0.3553265913067458"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-07T13:42:59.284Z", "publish_time": 1686145379284, "_source_registry_name": "default"}, "1.7.3": {"name": "@volar/typescript", "version": "1.7.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.3"}, "devDependencies": {"@volar/language-service": "1.7.3"}, "gitHead": "21b448e8f2f4942e49b66d7b852f4346a838cb8f", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.3", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-mMeBfYBw2mnSQ8QXXI5fgDHgYSn3dLI8B7+3s9IteELSKRfrvRde4hRkWWR6VLuRZRG1A6SXweAyVpwfMPROQA==", "shasum": "bb6c656899dbfb6f28823575787558505406ceea", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.3.tgz", "fileCount": 26, "unpackedSize": 115235, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHfNquwcZM1hQOtRCibv6NKOWoUDbGCvTxuszXcY2v1gAiEA+L18YgOen9f9pE522f+A3v2wDraU/ZdioDej9IbyUkM="}], "size": 26016}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.3_1686221352020_0.7132229532245324"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T10:49:12.261Z", "publish_time": 1686221352261, "_source_registry_name": "default"}, "1.7.4": {"name": "@volar/typescript", "version": "1.7.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.4"}, "devDependencies": {"@volar/language-service": "1.7.4"}, "gitHead": "65ed950a3440d8abd6d189cea6ff48c0e23a6384", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.4", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-xHiQoKAwXdLt1NXYZZ3uD32PKJ8KpZ5raBU6uCgCbhUh+7uWsk+cyFqDAboAPPm3oBCyCEGnnbrsJvimPEWyrA==", "shasum": "b323d57c9d9a17cb77008455a2c95dc6c36fabe7", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.4.tgz", "fileCount": 26, "unpackedSize": 115235, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIB+FC/lDDYV/FsmHyoEVQvMsH3icndMkPT3QXCEqmhNcAh9bTEq5dglbRzNU36RnO7xaoKVPELAbD1Iw9WgN+mTy"}], "size": 26015}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.4_1686248936939_0.8527740614893962"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T18:28:57.210Z", "publish_time": 1686248937210, "_source_registry_name": "default"}, "1.7.5": {"name": "@volar/typescript", "version": "1.7.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.5"}, "devDependencies": {"@volar/language-service": "1.7.5"}, "gitHead": "57fa4f05df4c593aa4a7b15c159f864d407bd1ac", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.5", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/2.4.1/node@v18.13.0+x64 (darwin)", "dist": {"integrity": "sha512-rfwL0WwES+Z2axXzJAaUtuNRy0dxZ+C1kHWZOufODwPU45MaZmukuXrLoHwsoLyYNFJgBjGGRyCcqjczPR/ODA==", "shasum": "91b4506e89df011dba8718c95e4b3009791cdbb9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.5.tgz", "fileCount": 26, "unpackedSize": 115232, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdtfoy6I+kowricKjgwmdrVaLzI+rmv4HRxeRIYmGxNgIgcPug7KqPa6iOhqk9rp+hUQX5YmLdCgGv92+EBiJ1KdA="}], "size": 26046}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.5_1686750101764_0.48613912887647914"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-14T13:41:41.943Z", "publish_time": 1686750101943, "_source_registry_name": "default"}, "1.7.6": {"name": "@volar/typescript", "version": "1.7.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.6"}, "devDependencies": {"@volar/language-service": "1.7.6"}, "gitHead": "b2dbe05318c06658ae97a65f9bc6d1badc55a81b", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.6", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.1/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-JkBRQe2GYSEgamW84tDk4XQ/7abQJw09czLQCgL1jfjndhaV4DuAet2I3pvQv41OjodVc59W0+E3hylrlNsgWA==", "shasum": "2cf652831cf8b77915104eca215909677b3d9a0e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.6.tgz", "fileCount": 26, "unpackedSize": 115440, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDd+Y7Sek49lY5ANH1e4mWvVpk0d/eaNm1hYhMxCRpaEgIgc9v/o9PcTVPbz9IkKjJeozOo22k7Uwptl75/vkX3qqM="}], "size": 26149}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.6_1686926158136_0.47421879563106195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-16T14:35:58.393Z", "publish_time": 1686926158393, "_source_registry_name": "default"}, "1.7.7": {"name": "@volar/typescript", "version": "1.7.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.7"}, "devDependencies": {"@volar/language-service": "1.7.7"}, "gitHead": "ad13290da9f223f6d198d858faf5349933112200", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.7", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.1/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-7cdFJru6nQrAao2WYxwDGeyMIOjDwUsJ/Y3ArDvZ4kLFPJY7RGisBNDdZBko1nomBzhbMAAW8JQZBbtawXSc3A==", "shasum": "98455b7946caf6c9c4bb8ebb099e36946aa1e090", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.7.tgz", "fileCount": 26, "unpackedSize": 116693, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDwwrUbRx9vbsVcptnzdg+H/45JRjcyYeu+fjoB2GQC6AiA3y0VqhMDpS44kUJpYTIbCy2lJ/RCUz6dVcnNGycbDQA=="}], "size": 26259}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.7_1687050871625_0.9344890441752534"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-18T01:14:31.854Z", "publish_time": 1687050871854, "_source_registry_name": "default"}, "1.7.8": {"name": "@volar/typescript", "version": "1.7.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.8"}, "devDependencies": {"@volar/language-service": "1.7.8"}, "gitHead": "e7c1b16e5c78702de6986eafa17ae96000ffe0cb", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.8", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-NDcI5ZQcdr8kgxzMQrhSSWIM8Tl0MbMFrkvJPTjfm2rdAQZPFT8zv3LrEW9Fqh0e9z2YbCry7jr4a/GShBqeDA==", "shasum": "6997b3c7637292a6dc6e4a3737e45f3c4e49ef12", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.8.tgz", "fileCount": 28, "unpackedSize": 122436, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBoFJ4HstcVMN1AnVFKxVEeuHy4Yf4RhZTQi5FQiRzQQIhAKyAmWA30KwzuUHJpk424aMT3JG7Ck8ujeyy1RsSheG7"}], "size": 27239}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.8_1687249784001_0.22107925937035056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T08:29:44.201Z", "publish_time": 1687249784201, "_source_registry_name": "default"}, "1.7.9": {"name": "@volar/typescript", "version": "1.7.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.9"}, "devDependencies": {"@volar/language-service": "1.7.9"}, "gitHead": "c28b1b3fc725f38d335f4341179678c78828528f", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.9", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-cXGg7lgvdjpRjYfz52cXKo6ExBi8k3pWeBe6Gckf4+9zmTfEwEFfKWMj0H/IyUYO+S2rjyE9jytdsu1Imk+Azw==", "shasum": "28f3597a391a90036c9e90c770654cd45201f572", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.9.tgz", "fileCount": 28, "unpackedSize": 122136, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEYw6Qk4SEvVFSNM7Jrsc2gGc2jicV+oPJl80LeZUGKOAiACh+KMLZ0mcwBuMz6re+1uKnjc2fa4hxFBPoaSRKSL+w=="}], "size": 27152}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.9_1687796261862_0.789073039814552"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-26T16:17:42.102Z", "publish_time": 1687796262102, "_source_registry_name": "default"}, "1.7.10": {"name": "@volar/typescript", "version": "1.7.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.7.10"}, "devDependencies": {"@volar/language-service": "1.7.10"}, "gitHead": "53c67c3fd1d11be3dba0ef199c282e605c980638", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.7.10", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-yqIov4wndLU3GE1iE25bU5W6T+P+exPePcE1dFPPBKzQIBki1KvmdQN5jBlJp3Wo+wp7UIxa/RsdNkXT+iFBjg==", "shasum": "d1b11e8cbc5ed0da69f9b8cd3d04cbbc4f06227e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.7.10.tgz", "fileCount": 28, "unpackedSize": 122657, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBUk4oje6UoGJIY0PhtKRHn2etAhE34dMZccO3LPMEhQIhAKd6wv9p03XcbnuLwbSgfBcIv3gmgc1p44+ELfoz7xYi"}], "size": 27265}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.7.10_1687943132004_0.20579215337891066"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-28T09:05:32.165Z", "publish_time": 1687943132165, "_source_registry_name": "default"}, "1.8.0": {"name": "@volar/typescript", "version": "1.8.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.8.0"}, "devDependencies": {"@volar/language-service": "1.8.0"}, "gitHead": "967ac805a29ba46bde1b5b5587d5cbd0e89a6d62", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.8.0", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.4.2/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-T/U1XLLhXv6tNr40Awznfc6QZWizSL99t6M0DeXtIMbnvSCqjjCVRnwlsq+DK9C1RlO3k8+i0Z8iJn7O1GGtoA==", "shasum": "ef231b9b54b192c1e50b9c2cf0142a975dd81651", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.8.0.tgz", "fileCount": 28, "unpackedSize": 125702, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmmW2KzI3Fks5HJ4uD0YEbXDkPwod5Qs3OBsDiVsjnxAiEAgBSiEznqRXSmHVLCFflGd5EEVFmpnZ714AkvQ3RUgJI="}], "size": 27642}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.8.0_1688410265445_0.058648762567608426"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-03T18:51:05.641Z", "publish_time": 1688410265641, "_source_registry_name": "default"}, "1.8.1": {"name": "@volar/typescript", "version": "1.8.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.8.1"}, "devDependencies": {"@volar/language-service": "1.8.1"}, "gitHead": "1fec004d7e3c5a28c0ad1d6a1d7a41ab98381abd", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.8.1", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-V9eFI+KNzGApdfPIvVULt/mXWcGMTLVLPfea3GvUwaHn9mgLB5chBLjbx0u3LsA+vIwvQxKZ818jmhyBlAmdkA==", "shasum": "7e22110f881b620d34728be8f419b2e4dd73574e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.8.1.tgz", "fileCount": 28, "unpackedSize": 125699, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG2dQ2XPNnQh3WrmuLAlgHe5S2jLiK9An4pn2hDuCQ+WAiA178y02zVhILB6EhPJENTCK4wLv2uz/rYi3GSEg2ZCXw=="}], "size": 27638}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.8.1_1689096904698_0.7639321109373396"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-11T17:35:04.886Z", "publish_time": 1689096904886, "_source_registry_name": "default"}, "1.8.2": {"name": "@volar/typescript", "version": "1.8.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.8.2"}, "devDependencies": {"@volar/language-service": "1.8.2"}, "gitHead": "b552c1f827294da5d36cbe4e7c1f7f2729b2b82e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.8.2", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-e3lWjlf43rb39MDPOmOzXFq83L958v6GP+y4CPaA2PS5GhN0bSJCwAQ3gRDSYpWcghtVW7MbOHVvq6LH+8tZOg==", "shasum": "b49fce36efaec1407c57f56be260926905305491", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.8.2.tgz", "fileCount": 28, "unpackedSize": 127782, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDCY/75g8Sn5hZ1sBaTRAejwczxbuPyrgnMZliXVHaqmAiEAu1AqG/9ZB7M/uCnVJaMDmIY7pdFymaya6PlUiKjtGyw="}], "size": 28026}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.8.2_1689208911016_0.2801962715438966"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-13T00:41:51.205Z", "publish_time": 1689208911205, "_source_registry_name": "default"}, "1.8.3": {"name": "@volar/typescript", "version": "1.8.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.8.3"}, "devDependencies": {"@volar/language-service": "1.8.3"}, "gitHead": "dabfccbbc4c730d9c6f941fb6cd284a6c1178b0c", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.8.3", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-PUHlrZjTf+PY97GVH9VPF55Z62lfqBRGCtjSkLKvQsm0kvUK+CgihjUzwx8ABwaeIXgoR5AKPJf9zeqlH3i4hQ==", "shasum": "199b8628b333d7c038527b2e860442c763c65730", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.8.3.tgz", "fileCount": 28, "unpackedSize": 128135, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICk3DBh/CMbknYGv7JO67GgXe0GGhF3r8pwmZlxAajjhAiEA9irh7ejgCIWDI2XS05AjtK+MC29BJ99p/aPU4whnnaw="}], "size": 28077}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.8.3_1689209672537_0.027936246782776397"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-13T00:54:32.718Z", "publish_time": 1689209672718, "_source_registry_name": "default"}, "1.9.0": {"name": "@volar/typescript", "version": "1.9.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.9.0"}, "devDependencies": {"@volar/language-service": "1.9.0"}, "gitHead": "37453f868329df9c666e6ee44919a55b36484b93", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.9.0", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-B8X4/H6V93uD7zu5VCw05eB0Ukcc39SFKsZoeylkAk2sJ50oaJLpajnQ8Ov4c+FnVQ6iPA6Xy1qdWoWJjh6xEg==", "shasum": "26eecf082ffca4bb7afc26a19a071d336d13fa95", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.9.0.tgz", "fileCount": 28, "unpackedSize": 128122, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGTCIDccuT0wgwNwiuRpfy/CI4XOQIIPScV6OEywoN2xAiAhXjacu/MSIsvTMrLRoNdgnjYCwpcHrHWNqFT1zoDAUw=="}], "size": 28095}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.9.0_1689345893925_0.0716670028270836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-14T14:44:54.117Z", "publish_time": 1689345894117, "_source_registry_name": "default"}, "1.9.1": {"name": "@volar/typescript", "version": "1.9.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.9.1"}, "devDependencies": {"@volar/language-service": "1.9.1"}, "gitHead": "68f064d587ef230b2f8a90157b1f7133b33f1d33", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.9.1", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-UO8bhflYMNuOpXeGYHSm3xItU4kEVhJQNGjwgw1ZqLr/sm1C7Y+pVQ/S01NpsojhFC8S+P6/p+jOTK6DO214kQ==", "shasum": "5ba42698ba55be7b1202c29ea84bf9796173a9dc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.9.1.tgz", "fileCount": 28, "unpackedSize": 128370, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvBOwBAOpzWrgdZhRbcXJ6V1pchMxuGR9hxzYT5vibFAIhAJCLsajsTamqr9SsKWJW/OrLQ058pd40xbqteElaUmAc"}], "size": 28096}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.9.1_1689961896266_0.14277495093073944"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-21T17:51:36.452Z", "publish_time": 1689961896452, "_source_registry_name": "default"}, "1.9.2": {"name": "@volar/typescript", "version": "1.9.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.9.2"}, "devDependencies": {"@volar/language-service": "1.9.2"}, "gitHead": "c838ba22be287308e3fcf27f65af672749816df4", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.9.2", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-l4DA+S3ZVOWGACDdRNVSYZ41nuTWOH8OMS/yVeFV2fTmr/IuD37+3wzzGnjIPwCUa0w+fpg8vJPalzYetmlFTQ==", "shasum": "767098ec2896c5e035078f545c903a2e6d920293", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.9.2.tgz", "fileCount": 28, "unpackedSize": 128837, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDm6G96iHU4hnIoyAb07B6zluqFES3LXHM5snUideldtgIhAJ6fApeiHMdDX1RDhB1O/d9lYArpSHZGGkgqf5fvXQFn"}], "size": 28245}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.9.2_1690213436665_0.675344194558118"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-24T15:43:56.865Z", "publish_time": 1690213436865, "_source_registry_name": "default"}, "1.10.0": {"name": "@volar/typescript", "version": "1.10.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.0"}, "devDependencies": {"@volar/language-service": "1.10.0"}, "gitHead": "c25bdec20ee11d278049a738743c694431f08796", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.0", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-OtqGtFbUKYC0pLNIk3mHQp5xWnvL1CJIUc9VE39VdZ/oqpoBh5jKfb9uJ45Y4/oP/WYTrif/Uxl1k8VTPz66Gg==", "shasum": "3b16cf7c4c1802eac023ba4e57fe52bdb6d3016f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.0.tgz", "fileCount": 28, "unpackedSize": 131500, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBRfDChyZN4mw+WY2aWcHb03Yv/yrCM/kFGTY0YRIYeQAiEAyHOqoY8+qYZKf6Xpud6hEqrz95mdwIV9grre9IFpIv4="}], "size": 28786}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.0_1690394671419_0.3822951972929074"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T18:04:31.557Z", "publish_time": 1690394671557, "_source_registry_name": "default"}, "1.10.1": {"name": "@volar/typescript", "version": "1.10.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.1"}, "devDependencies": {"@volar/language-service": "1.10.1"}, "gitHead": "65775648936d786847511f3d8bdd8a3d1dca2e4e", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.1", "_nodeVersion": "18.16.0", "_npmVersion": "lerna/2.5.0/node@v18.16.0+arm64 (darwin)", "dist": {"integrity": "sha512-+iiO9yUSRHIYjlteT+QcdRq8b44qH19/eiUZtjNtuh6D9ailYM7DVR0zO2sEgJlvCaunw/CF9Ov2KooQBpR4VQ==", "shasum": "b20341c1cc5785b4de0669ea645e1619c97a4764", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.1.tgz", "fileCount": 28, "unpackedSize": 131500, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFqtGvQ9f1H9YoSSxlvsSTyWyg+Su2F93Yvxii61BxJVAiAJGAWCELGaJt03HvCGD8tuVTY2Eb2qyWdhtv1/bhcIxw=="}], "size": 28786}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.1_1692291253446_0.6002446539664739"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-17T16:54:13.655Z", "publish_time": 1692291253655, "_source_registry_name": "default"}, "1.10.2": {"name": "@volar/typescript", "version": "1.10.2", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.2"}, "devDependencies": {"@volar/language-service": "1.10.2"}, "gitHead": "15d5e9129c034ef839f6b4646e495b5e80890764", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.2", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-3zDAg3dnpJzFCAZnmpahyVGlm1mVKRSJOSPCUHLene0SSd/SHHVOe37C1+ueSNSxDVX31YXgMJXnXGm6a8oRhg==", "shasum": "5a8ccb564b647e4b24f45ad99cd9a9b894210e76", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.2.tgz", "fileCount": 26, "unpackedSize": 121776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIfGNOa7wTkhLmhefTYzU/+V4eRTT2Wmiv0CETBJGwBQIgfnh8QrpMBp29kGfYG1QH/fUHkhS3XBP9tIXDY/Eide0="}], "size": 26972}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.2_1696600182481_0.5839895464327303"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T13:49:42.766Z", "publish_time": 1696600182766, "_source_registry_name": "default"}, "1.10.3": {"name": "@volar/typescript", "version": "1.10.3", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.3"}, "devDependencies": {"@volar/language-service": "1.10.3"}, "gitHead": "5dd492d94d3e5aa620448e0b951dead341676dac", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.3", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-n0ar6xGYpRoSvgGMetm/JXP0QAXx+NOUvxCaWCfCjiFivQRSLJeydYDijhoGBUl5KSKosqq9In5L3e/m2TqTcQ==", "shasum": "5e95d277e83bef3fc5f7df429c20a959391d2ce4", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.3.tgz", "fileCount": 26, "unpackedSize": 121776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQCEt+wuvUYuviTSgfJc/RGk6QBU1QVEccU+FVFP9FAAiBUUIIGZ1CuctlrzIfvsojjWJPE52BeOM8AQnCj8F6VSw=="}], "size": 26970}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.3_1696601332656_0.26054917244399123"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T14:08:52.931Z", "publish_time": 1696601332931, "_source_registry_name": "default"}, "1.10.4": {"name": "@volar/typescript", "version": "1.10.4", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.4"}, "devDependencies": {"@volar/language-service": "1.10.4"}, "gitHead": "fd5ba2d370b2b866a5e74b1fee76a187ac7471bc", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.4", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.5.1/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-BCCUEBASBEMCrz7qmNSi2hBEWYsXD0doaktRKpmmhvb6XntM2sAWYu6gbyK/MluLDgluGLFiFRpWgobgzUqolg==", "shasum": "78c8b56c1a4cc406fed50fc88d92874f947f82d6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.4.tgz", "fileCount": 26, "unpackedSize": 122052, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICdpcAyYlLxlVUX8lcb7Yosqq6j3x/QlGVuOO5N0ILvDAiEAlGvZ7aEMco9Hy9QltQyuhhhdXChrZsWnE4djZWSQq64="}], "size": 27060}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.4_1697013917453_0.7471500126514212"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-11T08:45:17.683Z", "publish_time": 1697013917683, "_source_registry_name": "default"}, "1.10.5": {"name": "@volar/typescript", "version": "1.10.5", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.5"}, "devDependencies": {"@volar/language-service": "1.10.5"}, "gitHead": "f087b2cb990108e71ed66727588b3e19c421b4c2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.5", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-kfDehpeLJku9i1BgsFOYIczPmFFH4herl+GZrLGdvX5urTqeCKsKYlF36iNmFaADzjMb9WlENcUZzPjK8MxNrQ==", "shasum": "40f1d2ab36c875d501d94c1a6766e9dc1adfaf25", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.5.tgz", "fileCount": 28, "unpackedSize": 128926, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnz5VjsDHjfEVmtjg1nCbUPPzwGSc6/xwtu1lwUp4/wgIgfFtXumeFCcWVPHaxIjcBLmAPgtIYXCLsTzc7X39iGN0="}], "size": 28485}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.5_1698229812632_0.8648952077673755"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-25T10:30:12.979Z", "publish_time": 1698229812979, "_source_registry_name": "default"}, "1.10.6": {"name": "@volar/typescript", "version": "1.10.6", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.6"}, "devDependencies": {"@volar/language-service": "1.10.6"}, "gitHead": "4ce05a1173da54611bd766d25d173e3bd4cab2c2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.6", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-22hjyhdSl6uU/4zJI4ceDCSUrkIAvp0Zm1mV0phFVqeAIpQmIweWKmk7nRTXfnNqu6u05phR+NMzOnKkNC5yDQ==", "shasum": "29d64f1cb940e70d2fdb5e5599506999295484e0", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.6.tgz", "fileCount": 28, "unpackedSize": 128685, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHaUUR4UpcGd1n4+PuHHKdpuRvLibrKub/aeuGsUM+K8AiEAxhk+EHDgOlCglN6LfG4VN9d4dWOec594GJmXWDI3vdM="}], "size": 28434}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.6_1698395053943_0.6901578591589184"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-27T08:24:14.197Z", "publish_time": 1698395054197, "_source_registry_name": "default"}, "1.10.7": {"name": "@volar/typescript", "version": "1.10.7", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.7", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "^1.0.1", "@volar/language-service": "1.10.7"}, "gitHead": "6350232bcf1e2fdf34bf6bc6fc87f46facb03730", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.7", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-2hvA3vjXVUn1vOpsP/nWLnE5DUmY6YKQhvDRoZVfBrnWwIo0ySxdTUP4XieXGGgSk43xJaeU1zqQS/3Wfm7QgA==", "shasum": "2ed47e3260d4161445099ba89c7471fbc51133b6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.7.tgz", "fileCount": 26, "unpackedSize": 121773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEkGCJZrpj/+kfn6AX83nrRsARET4d0TSrilefHMtLCQIhAPEx3qQcto95iiIXNJhKXLf/VLf08lMZthoLS7XXCEqt"}], "size": 27072}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.7_1698429107755_0.40287060235263716"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-27T17:51:47.994Z", "publish_time": 1698429107994, "_source_registry_name": "default"}, "1.10.8": {"name": "@volar/typescript", "version": "1.10.8", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.8", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "^1.0.1", "@volar/language-service": "1.10.8"}, "gitHead": "fbad6fbc5bd3cc75d40492272bb97c5d1b01be2a", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.8", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-XZLR4Td6UzCRdPTUBh5L8LxYWPnowZWm3Hs79mClcOn8ky++/v8a4aDyyl6T9V55u/NgGndGhPqsp7Qg52pr4w==", "shasum": "8fe802c6ae7d3b833aeeb1486531fc71faab7696", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.8.tgz", "fileCount": 26, "unpackedSize": 121773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0CmFVD0p4s0w1Qzw72DcoFcVU7dAsfPWjVZ3FBH4MwAiEAz+a/JuiZhgYDKmuVvgA1My30GG2d92tK4iRH8Czgq20="}], "size": 27071}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.8_1698698033757_0.9027389400877635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-30T20:33:53.999Z", "publish_time": 1698698033999, "_source_registry_name": "default"}, "1.10.9": {"name": "@volar/typescript", "version": "1.10.9", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.9", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "^1.0.1", "@volar/language-service": "1.10.9"}, "gitHead": "dbd802ab416c78f7b2425d10d00075fa3fbcd6c1", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.9", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-5jLB46mCQLJqLII/qDLgfyHSq1cesjwuJQIa2GNWd7LPLSpX5vzo3jfQLWc/gyo3up2fQFrlRJK2kgY5REtwuQ==", "shasum": "0c019f94f33fe0f2ab9e0c32afc9dbfa391ad32a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.9.tgz", "fileCount": 26, "unpackedSize": 121773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWW9169Rpwm7cnl4mBT4grsXchWtYeGYCGnZPNLFcxhwIhAIjCZSWkWx8bZEgBnSSUECoIndRUFj91u94YKNoUxllf"}], "size": 27072}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.9_1698698854438_0.7772063016590955"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-30T20:47:34.818Z", "publish_time": 1698698854818, "_source_registry_name": "default"}, "1.10.10-alpha.0": {"name": "@volar/typescript", "version": "1.10.10-alpha.0", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.10-alpha.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "1.10.10-alpha.0"}, "gitHead": "82c2a3fbd190fad9b9d8293b34587b5d17bed545", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.10-alpha.0", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-3WqUyOoXtGz92SNbbmguagnpxLNXeqoxgp0Rc7y6ERN7WhwF5gQtApKovTpzS2Oy9LH5FbnKNgPPDes066dQ/Q==", "shasum": "4f52e9e848b98e52b2c9c43e24bfedb1b10e792c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.10-alpha.0.tgz", "fileCount": 26, "unpackedSize": 121800, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHsOVhGHW/BCLN0l+8luki4QkBTbkMOcFTypvEfMwo//AiBAUfYM/OAOrQZiy+GgafdCak/hj4WmZuhioPqt7Oz+Tg=="}], "size": 27088}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.10-alpha.0_1698818558778_0.9587165454387603"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-01T06:02:39.001Z", "publish_time": 1698818559001, "_source_registry_name": "default"}, "1.10.10-alpha.1": {"name": "@volar/typescript", "version": "1.10.10-alpha.1", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.10-alpha.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "1.10.10-alpha.1"}, "gitHead": "7bf9da2be16293e5dd5d7ac886d0193c00749ff2", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.10-alpha.1", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-+KZ3X+bQ+hA/Ely/OzpbaqzYAmR+4pdlLVih+M/MtSfaC15/3k8d2XCd+RxeZQBBA7kEYHYHRqXIDLnhWEgsZw==", "shasum": "7c8afee0f4b9c8b835b9f1fb8fdf93f365326edd", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.10-alpha.1.tgz", "fileCount": 26, "unpackedSize": 121800, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC05RCZVDTwPVPodA+4F/okCFyPV9s0N0iyf2GzSz4G4AIgZYxNrni80w/aYgGj2WVwEcSV9PWkVbig4ocNLLV1aZg="}], "size": 27086}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.10-alpha.1_1698820637218_0.842552302877595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-01T06:37:17.468Z", "publish_time": 1698820637468, "_source_registry_name": "default"}, "1.10.10": {"name": "@volar/typescript", "version": "1.10.10", "main": "out/index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.10.10", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "1.10.10"}, "gitHead": "c3f1b9e52b738e5e5340ad1aeb827f7ca51bc573", "types": "./out/index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.10.10", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-4a2r5bdUub2m+mYVnLu2wt59fuoYWe7nf0uXtGHU8QQ5LDNfzAR0wK7NgDiQ9rcl2WT3fxT2AA9AylAwFtj50A==", "shasum": "1f88202c63988ddfcee154a93050312041b83329", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.10.10.tgz", "fileCount": 26, "unpackedSize": 121776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzqTfUXScMAgXvZp4vw53mp5yZdVxybTT/V5mfSC1QVwIgTPxlyraqPXCiRyi1kmRJGw7+ih8yeY0gIyfB2BhPCUc="}], "size": 27079}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.10.10_1698821122449_0.14626254774555214"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-01T06:45:22.699Z", "publish_time": 1698821122699, "_source_registry_name": "default"}, "1.11.0": {"name": "@volar/typescript", "version": "1.11.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.11.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "1.11.0"}, "gitHead": "e3f563cbbf4cc03c058f5df38df0498034e5feb5", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.11.0", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-GzcHIt31pdS+fuvgoDxFjBd6/wkjrNkneVnUjkzHs9ghrhYl1jyIJaZJvjc/Hbxn2DdQvm39As0mXYPNrWK6Tg==", "shasum": "99f39b991a0ed997ed8a6281ad0a5fa19c3a7def", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.11.0.tgz", "fileCount": 26, "unpackedSize": 121788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXy6e1Bp90IAMjxIa0JyzO0g0ob2AwZ/ooAGF7OKIRFQIgXDy0A12QObcoblRgrYpzIS2eqtteWtauKfVrPCCskTo="}], "size": 27050}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.11.0_1699047660400_0.26665574316514395"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-03T21:41:00.673Z", "publish_time": 1699047660673, "_source_registry_name": "default"}, "1.11.1": {"name": "@volar/typescript", "version": "1.11.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "1.11.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "1.11.1"}, "gitHead": "188f49ee79bd2ea8e8fc32b80003c85f79868f9d", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@1.11.1", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/2.6.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-iU+t2mas/4lYierSnoFOeRFQUhAEMgsFuQxoxvwn5EdQopw43j+J27a4lt9LMInx1gLJBC6qL14WYGlgymaSMQ==", "shasum": "ba86c6f326d88e249c7f5cfe4b765be3946fd627", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.11.1.tgz", "fileCount": 26, "unpackedSize": 121788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGzQQFoIW9nMEfPz/yhuG0JnocH5Elkxx7MpfKtZhxPwIhAOd2lWGC0tBrvLda2TvYdnwzj6i1T1pmPpTxcvUJL2Vi"}], "size": 27049}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_1.11.1_1699120298081_0.9467917432497288"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-04T17:51:38.289Z", "publish_time": 1699120298289, "_source_registry_name": "default"}, "2.0.0-alpha.0": {"name": "@volar/typescript", "version": "2.0.0-alpha.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.0"}, "gitHead": "6a6be45a3b7983148d90541e7c68ef84c9155a7a", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.0", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-U4Kx/Z+GmDdyClS6QjRFdgeSrFxH95Be+lkjU9uciR0KrCczc2HX0KhT6a+AsUd8kKECYuQkykXTRT3rpU9h8w==", "shasum": "47c684bf6ae94bd855db4501afdb4a1041972fa2", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.0.tgz", "fileCount": 28, "unpackedSize": 143835, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAo5roMzxImSacjwct4Vesn2HSMny58iyRDkWZsIVF/zAiACrXgkWadD3zbCxyNnxxBAgXBb/+u0106atEJnIgjqNw=="}], "size": 29439}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.0_1701799386774_0.6947781104092075"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-05T18:03:06.988Z", "publish_time": 1701799386988, "_source_registry_name": "default"}, "2.0.0-alpha.1": {"name": "@volar/typescript", "version": "2.0.0-alpha.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.1"}, "gitHead": "3af80b55081397c58cc5c0b6ed44d1810aa0dea1", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.1", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-mkD3RYc5HuLu11HaKeJG3tsUEvW9FRr1bXfnEOjJCBmU5yGXB4TJChB4D27WGIY4qFygpJ5XzXu8aNzJseqGvQ==", "shasum": "8046adbc62e7343b08ec514d9c0817f95f1fe4bc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.1.tgz", "fileCount": 28, "unpackedSize": 143907, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPTUOibTBDzYThiVZ/8nByxenr4A2ZiJo76e9rBpDcwQIhALC+Hc/y7rJdVcIeZDhb62xF79gX3eRbBK/vpoKnL7Bb"}], "size": 29435}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.1_1701969741197_0.6167883202342412"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-07T17:22:21.440Z", "publish_time": 1701969741440, "_source_registry_name": "default"}, "2.0.0-alpha.2": {"name": "@volar/typescript", "version": "2.0.0-alpha.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.2", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.2"}, "gitHead": "88e80b9f00541ab9478b9c7b7af213813fc8cb20", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.2", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-Re3q21/w8ytW9Oia7bYUvbdh1pij6gfVOv9Of4ymlHLGWQiHfUXY7Gy1V3HjdKG6tTe7Sm3UtXhI8jyGtQptKg==", "shasum": "29789596632c9a59509f5acbb5953af6513bb145", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.2.tgz", "fileCount": 28, "unpackedSize": 143290, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOu/YZRbDCfPIq3XoM8PxW4uQ2LMCHS1yS3VRa41jikwIgCVdsFg24wnKpXEtdCJLs95bQwgBLpJ8o/HLsZ0yBMW0="}], "size": 29283}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.2_1701970029585_0.22684609558232216"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-07T17:27:09.759Z", "publish_time": 1701970029759, "_source_registry_name": "default"}, "2.0.0-alpha.3": {"name": "@volar/typescript", "version": "2.0.0-alpha.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.3", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.3"}, "gitHead": "0bb685a72cff180bb9b3420aaf3136c8e899c908", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.3", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-3f6oJvcJed5Ovvip7ge3iSm8MeVSZk+uwQp9+k3JPHz0rpGdgBVMW0/Gn36by65lrxumCDhj6HPUV05oSAPgzQ==", "shasum": "9b0aa8f3b70f0c5396dd2017438e5b7d7d44be19", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.3.tgz", "fileCount": 28, "unpackedSize": 143944, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCniQMua7HXqb9HYE7dvJ3CaICc6ZVP1HAehMerti2chQIhANQdcHWhXOY0jO9jH2dMvLhyGxV/uMmUcwQJZz+KD0Ah"}], "size": 29389}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.3_1702158785552_0.972236933541917"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-09T21:53:05.736Z", "publish_time": 1702158785736, "_source_registry_name": "default"}, "2.0.0-alpha.4": {"name": "@volar/typescript", "version": "2.0.0-alpha.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.4"}, "gitHead": "4d07249613d0cc5de4197e90c741a64adf92b84b", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.4", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-ibqa3zJ3KlpIx6owmr9HjuwZbRaynETGqJOT0Nv0j1s7q1WkMC1RWoiE0OYWVJfEEs7UcIq96FBdq4n2obMD9A==", "shasum": "191a25b02e949e7ebdfb2faf282e7b9ca9b2a780", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.4.tgz", "fileCount": 42, "unpackedSize": 166484, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoTsI1S6VmchXfQLrSfJb+bWyq1buccyZJ1+IfKz0eCwIhAJGyy2vfbp6PWTeMDVNFKuFIvbYK+KFI51RyieXF6414"}], "size": 33645}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.4_1702418616655_0.9688341234988145"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-12T22:03:36.932Z", "publish_time": 1702418616932, "_source_registry_name": "default"}, "2.0.0-alpha.5": {"name": "@volar/typescript", "version": "2.0.0-alpha.5", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.5", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.5"}, "gitHead": "80f4f8791c304fbcf4a0a8425eae8ea0b5ab739c", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.5", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-27nguq6k0Y9wv3lyf2gM20JTbxx/SOXDpUBoQ7Z1tLeOUZ50b3Ru+3rQxKf4XjHIIsVaHetG4FkL9qfXvHxAEg==", "shasum": "30dcb40dc2da8417f7bda257ddb79772298df2ac", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.5.tgz", "fileCount": 42, "unpackedSize": 166541, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCL4EzSgyeksQWA0FleFjIxPrLl/VVNfG0sUd7eCBFO8AIgf+WQs8r5ONrk5rCiiSjfSyVifrL4HjZ4jeGJqZ1T+P0="}], "size": 33670}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.5_1702551036213_0.6676414393927435"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-14T10:50:36.407Z", "publish_time": 1702551036407, "_source_registry_name": "default"}, "2.0.0-alpha.6": {"name": "@volar/typescript", "version": "2.0.0-alpha.6", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.6", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.6"}, "gitHead": "e81e8f70e54967242922891b7e825a5e40b56459", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.6", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-NEv8VE7xKAhQH76lv6P3uh5Q8zsse47N6mhC17pI+noB+MsBLPHS62TGTypbpgXPjrIXnnXahKpKEzddVS9X9w==", "shasum": "403bae27b0bd4e0d89dd50c5d38b7c6613934dfc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.6.tgz", "fileCount": 42, "unpackedSize": 166541, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtlUAFzluiGabXkAWuF7861NWx/X+IRffvRLOmtx9gkwIhAL7nG5x1GCKLxmUorvb6cp9HEF5NI9YR1IoKV+iy2aah"}], "size": 33673}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.6_1702584878324_0.10384957814452789"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-14T20:14:38.589Z", "publish_time": 1702584878589, "_source_registry_name": "default"}, "2.0.0-alpha.7": {"name": "@volar/typescript", "version": "2.0.0-alpha.7", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.7", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.7"}, "gitHead": "5b45f34fb8da1795a9c45242a0e184323b6b14c8", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.7", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-WmnYnWHxXHbjDd+03y+fbrCAqKaDDEAryE8vQOJwgczK1y/9eXg4Aa+/lMNUDMhDYh9xcLw2N0zdjkxeGFD4lQ==", "shasum": "52687cb5dcd5fb4d4e53717f9360b2630f0190cc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.7.tgz", "fileCount": 42, "unpackedSize": 167123, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCySOj92c1Dsx+CJeq9I427UhLKNl4atvRyjrEATkTAgQIhAJL+V/PUqj5Qa3ql0WPepY3RpHG+Bxs5n0UG+5Z9fHU7"}], "size": 33716}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.7_1702630912409_0.5329111158630135"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-15T09:01:52.596Z", "publish_time": 1702630912596, "_source_registry_name": "default"}, "2.0.0-alpha.8": {"name": "@volar/typescript", "version": "2.0.0-alpha.8", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.8", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.8"}, "gitHead": "4573af5545a28a90f3d2b1b30cf6ad74ae0851b1", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.8", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-IyoDUgHBz8W7d0/vnOKBZyvLhkEj/vIPKz7QPHn3I9mFCzJcrWQh9X6w7Gun7TjTohMAoPfN/wiRSSn+6tTv3g==", "shasum": "254c9f55c020a6fd3015fdb0cb770cd31b995ef9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.8.tgz", "fileCount": 42, "unpackedSize": 167123, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2fpbYYXOfbYeQeJSNEBEje04L+eNloPhcGsJ643esZwIhAMq81BNr3l3AjL634W6CaEUSxCojshCYUiYy57ijfqSN"}], "size": 33714}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.8_1703011060616_0.4488429424771325"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-19T18:37:40.840Z", "publish_time": 1703011060840, "_source_registry_name": "default"}, "2.0.0-alpha.9": {"name": "@volar/typescript", "version": "2.0.0-alpha.9", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.9", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.9"}, "gitHead": "dcd70c78b0b6901901dc0e68223e34dd922f5fdc", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.9", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-WEW7WqKRWEDyMk35RcCp4g+F+w/3srBfRpCJpZWmVVVOxxenulfKDVZJ8HlHcgQU5RVULkuj3+LRhpp8ZLHVnA==", "shasum": "1bcba9144b782724b5c5d81ca0a3d22422d83038", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.9.tgz", "fileCount": 42, "unpackedSize": 167123, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICdJoPvY8nxwx3kL8m12iGy8R8EXX8VKNHfReeLkydqwAiByRx5mQET/4qtJnd6joIq1AnBwopSRH5s2nEh0Lulb2Q=="}], "size": 33713}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.9_1703049647728_0.7969478030584691"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-20T05:20:48.004Z", "publish_time": 1703049648004, "_source_registry_name": "default"}, "2.0.0-alpha.10": {"name": "@volar/typescript", "version": "2.0.0-alpha.10", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.10", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.10"}, "gitHead": "f320632bcffa06981de169ee3cdad79c29ecc1b5", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.10", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-3T9NMtaDh76zKvbMGCuqgik8E0DNelFzvByUmceGqFSJREnQbBaRWIV5qg3+bFsHFH0CYFIXvIsy44gJxS34WA==", "shasum": "8b04658d74ff29f51b962d3ce795530374f11031", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.10.tgz", "fileCount": 42, "unpackedSize": 167126, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCThoS9cdNATbY30dJYGJ4ON2UGjInsg3khC8ifvs1T3AIgRQx/xzsWLO7DQZZJsu7dNIyMlVLosCuv3H3dcIfWifU="}], "size": 33714}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.10_1703055234178_0.5261931642182811"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-20T06:53:54.340Z", "publish_time": 1703055234340, "_source_registry_name": "default"}, "2.0.0-alpha.11": {"name": "@volar/typescript", "version": "2.0.0-alpha.11", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.11", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.11"}, "gitHead": "4abdbd921dd5c351ba1394af90fb3b53f5d78fde", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.11", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-yFS1qengueXypHCMxt/xpa+RJyKSp4U4OIb2qxvcYmI9//JbOAEQvketndu3Q6NteJFPzxol0g+KK1+xprXYUg==", "shasum": "b11a6ca3211857be9e4c8dd1f5fc05e195cddcd4", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.11.tgz", "fileCount": 42, "unpackedSize": 166706, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGWkwrFt0pvNIOtTHdCwDXc4BBIY8uzaijqitMhznv/wAiEAjZG7I3THRa5WHPNHlsGMmb6xCJJvZ64hTatglwQCdWg="}], "size": 33723}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.11_1703191514442_0.321246045877444"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-21T20:45:14.672Z", "publish_time": 1703191514672, "_source_registry_name": "default"}, "2.0.0-alpha.12": {"name": "@volar/typescript", "version": "2.0.0-alpha.12", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.12", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.12"}, "gitHead": "1c15dd486f6e6174aa940c21f0ef45215ea2b854", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.12", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-aIksyYHaOMNynB03d5ab6gO6spYYdkS5rarl4trccOLoTtxGjpW5dsOs1ln7WErgLa6VkxmsxSPp3GtmOaLQtA==", "shasum": "bdedc82b4a93c3999daaebbffac0c94859579835", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.12.tgz", "fileCount": 40, "unpackedSize": 159850, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH6mTC5FtV2ZSKHonMs4AD17GkROVaBK5yVwyqGEUi0PAiAUGyFIlAgKL1OOcqAZtyfGdts5BmUUz1TdtwQ6HIiEMg=="}], "size": 32585}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.12_1703193054151_0.4608709599941956"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-21T21:10:54.426Z", "publish_time": 1703193054426, "_source_registry_name": "default"}, "2.0.0-alpha.13": {"name": "@volar/typescript", "version": "2.0.0-alpha.13", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.13", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.13"}, "gitHead": "b052e3d47235325ac614257be1cebd541d421134", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.13", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-w8BzSpqqek+Xu5IfWGRkXek1hxfRPuOZUOCL5/yyTQRY84XDn3+oyXTd157TXhtD0TpWKKPCVj6ETjS4qdmVBw==", "shasum": "173ce513bd5688e0584c8e986c4c63e35bac2622", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.13.tgz", "fileCount": 40, "unpackedSize": 159850, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQbyBwZDgrmjc0g/esaHZpBipqvW/+1yIUqXN9eelZHQIhANXzLNFPz1p9CDer/hFxdtsiw9TKW3zmcAHYJJn+4x3H"}], "size": 32585}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.13_1703248558146_0.8370988064282316"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-22T12:35:58.326Z", "publish_time": 1703248558326, "_source_registry_name": "default"}, "2.0.0-alpha.14": {"name": "@volar/typescript", "version": "2.0.0-alpha.14", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0-alpha.14", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0-alpha.14"}, "gitHead": "83eb6a4a4475a3680f14fb3808e48ec4dbe62cc1", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0-alpha.14", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.0.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-IH8BvoRg45Fy03tKrOiSMbbwkrvAdZRHmv5IwfP1nRY0lt5Xkxwx0wfW9xhbRQ6DUEsh41zxa1HSsPTBAiWWRg==", "shasum": "37ba28da208dd1fdc40a8547490410e81899685c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0-alpha.14.tgz", "fileCount": 42, "unpackedSize": 162866, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCf8dsFSs/GGUUHWCd8SuxsFVRa9f4t+fG4KXOvYeSkAwIgWnoOtO4/S6BuZK9yIvKm7kY4vFYhAS4hqBBExUjn0j8="}], "size": 33136}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0-alpha.14_1705473151572_0.2585960183081415"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-17T06:32:31.791Z", "publish_time": 1705473151791, "_source_registry_name": "default"}, "2.0.0": {"name": "@volar/typescript", "version": "2.0.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.0"}, "gitHead": "95217136d2727bb7304443d91cfde3dbe711369c", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.0", "_nodeVersion": "20.6.1", "_npmVersion": "lerna/3.1.0/node@v20.6.1+arm64 (darwin)", "dist": {"integrity": "sha512-6HGiCOMe612taDDRTX1N5938hY2ThjdLdW3TXHsIgcQz8Y+2H8o94BDwXnMOGf+XksektuXMZkDBalgLPFhBbg==", "shasum": "434a4e634fba94ef703e668ae219c5592d8fb70c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.0.tgz", "fileCount": 46, "unpackedSize": 172137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBH0/KF31xmTBQpsqT0TP24P+NZk6wp09u5joCcL2Vc6AiBj5/StjKfjCVlqIyKpokhcX0udiqifhW4pYDnCrJjFAA=="}], "size": 33282}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.0_1705816287842_0.18308472100328332"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-21T05:51:27.998Z", "publish_time": 1705816287998, "_source_registry_name": "default"}, "2.0.1": {"name": "@volar/typescript", "version": "2.0.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.1"}, "gitHead": "b1fbf6eed522624ccccfb17a8999edfbc1d8d9bb", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.1", "_nodeVersion": "20.11.0", "_npmVersion": "lerna/3.0.0/node@v20.11.0+arm64 (darwin)", "dist": {"integrity": "sha512-k3GJMVv38npuaV+O/kYUylDW0QhM6/ItcdIwtvQ74ZS5j8Toqpr+dYc5Hr+5c4vQo33FStBX0C8lgCoISMEP6g==", "shasum": "e6aa0541f763934e4f48ca74ea95dd5e3488f8cf", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.1.tgz", "fileCount": 40, "unpackedSize": 162218, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID9HxOPa2gR7UUaHUNhEAoDiqMb3FgLthaDwSiE0IKA4AiEAhc3QsffzMIwD6B2jECsWHfmpgWf2LP6bS3BrHbzUFcE="}], "size": 32799}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.1_1707075943915_0.266624597737936"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-04T19:45:44.101Z", "publish_time": 1707075944101, "_source_registry_name": "default"}, "2.0.1-patch.1": {"name": "@volar/typescript", "version": "2.0.1-patch.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.1"}, "_id": "@volar/typescript@2.0.1-patch.1", "gitHead": "9df3adab1cf70ffe515d88a26368625d3ecee2ee", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-sVHDdVYLGcTGuvwugw72YoQJdqMO/3tobWYO2w/9iDE/dLK6S3lwEobHDyH19wM/YVM3aBkZAQde5MPdwDyvlQ==", "shasum": "652e65990f6f3501a903275459aab78ce542dbb6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.1-patch.1.tgz", "fileCount": 40, "unpackedSize": 162161, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDblge1OFFqzjjgV83jL7+9LnIEXuSqybcWBk3peabtgAiEAh/ACWOTUJ/3gB3ZABscSZ+95mt67pAK7pMXTu1a9qgA="}], "size": 32435}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.1-patch.1_1707086717854_0.5609643181825146"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-04T22:45:18.057Z", "publish_time": 1707086718057, "_source_registry_name": "default"}, "2.0.2": {"name": "@volar/typescript", "version": "2.0.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.2", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.2"}, "gitHead": "ae8bccc570e6ee752693b1bfd9e815668b5f32b9", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.2", "_nodeVersion": "20.11.0", "_npmVersion": "lerna/3.0.0/node@v20.11.0+arm64 (darwin)", "dist": {"integrity": "sha512-lcCrYdg1ZgKZVm0mnk7pOxBGrojZk/YaeFJdxLH0gd/Kd13Go7uNvfotlYSGQshwHKcbJ0zaqY0et9w9oW1yyA==", "shasum": "6ad4c77b08971b4ff71b935f0a18de2afc803175", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.2.tgz", "fileCount": 40, "unpackedSize": 163201, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEyFYaeQZXCn2sfsRmI2E3nRG62w4e/nAp5kpMGkgV21AiEA2Qj7uV93axnEycCk66+xWcwnnMkTTULs8Noyd7ZSbEE="}], "size": 32969}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.2_1707337356039_0.6551514214090697"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-07T20:22:36.327Z", "publish_time": 1707337356327, "_source_registry_name": "default"}, "2.0.3": {"name": "@volar/typescript", "version": "2.0.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.3", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.3"}, "gitHead": "d8a6dc056d37779ff6486f545f45bc57b22c02e4", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.3", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-EQ+s7sDb1s3arGAUHpbPY4h3zm4/tOTPLr+srwxJZ8W2xYHwKGt/7JeUiG10nbYibF/YJn/JinDKLHkUKIMoHg==", "shasum": "c9953f3f77da90eef527e59c9367fd7dbb011aff", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.3.tgz", "fileCount": 40, "unpackedSize": 163428, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5LzGwI7xU2Dl0qkcVELDBaLoMIMEnywI5ApVAUxlUkQIgB1jPmE/MCW5XG1H2CyK7+3gxPBgxrbme6tXa7XDlZes="}], "size": 32998}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.3_1707504643499_0.015073850047037096"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-09T18:50:43.658Z", "publish_time": 1707504643658, "_source_registry_name": "default"}, "2.0.4": {"name": "@volar/typescript", "version": "2.0.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.0.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.0.4"}, "gitHead": "b3dc544424c95f8b72e832fbaa0f85a34442389b", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.0.4", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-KF7yh7GIo4iWuAQOKf/ONeFHdQA+wFriitW8LtGZB4iOOT6MdlRlYNsRL8do7XxmXvsBKcs4jTMtGn+uZRwlWg==", "shasum": "dcc851bec2737ae69e0ecb8f43c9302172fc9ffc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.0.4.tgz", "fileCount": 42, "unpackedSize": 164650, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYbUsS//WyaDJOWe/5SpT7pXzAUPqLgbnAobwKSmgyqAIhAJ2s6Ez/KLoydxyafM8xU/Q1aTfd7hLiD+9cPHggSQG/"}], "size": 33354}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.0.4_1707785713366_0.5245741024304842"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-13T00:55:13.514Z", "publish_time": 1707785713514, "_source_registry_name": "default"}, "2.1.0": {"name": "@volar/typescript", "version": "2.1.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.0"}, "gitHead": "09e1792f0adafb02caf89a5a45a6fcaaf3177808", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.0", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-2cicVoW4q6eU/omqfOBv+6r9JdrF5bBelujbJhayPNKiOj/xwotSJ/DM8IeMvTZvtkOZkm6suyOCLEokLY0w2w==", "shasum": "640abcdcb6b822f9860006d090e1d5252c655e37", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.0.tgz", "fileCount": 42, "unpackedSize": 164552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMnMwOhbYnbBPbYyMNDdqMmq/qtz//AvSd/YETLySaaAIgShbm8XggvkhnbGwl3PwuH9Q/W6F/gYdLliu+M+FTUy8="}], "size": 33344}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.0_1708922513422_0.4328090717365425"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-26T04:41:53.689Z", "publish_time": 1708922513689, "_source_registry_name": "default"}, "2.1.1": {"name": "@volar/typescript", "version": "2.1.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.1"}, "gitHead": "e56916097fe7be2920aab6592a564a8e2ceafd14", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.1", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-5K41AWvFZCMMKZCx8bbFvbkyiKHr0s9k8P0M1FVXLX/9HYHzK5C9B8cX4uhATSehAytFIRnR4fTXVQtWp/Yzag==", "shasum": "b3dddaf39140cc0e00d67bad943496e2470a3882", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.1.tgz", "fileCount": 42, "unpackedSize": 165260, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA5QGnaBpI+jVDOdkoy31GOIW2b9EuhtEvLOmX8cZx3nAiBc1OjESLwctB5B5dP/LsLts5NrsSj8Mpy90S2TC8BA8A=="}], "size": 33462}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.1_1709647273788_0.09343342980771463"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-05T14:01:14.016Z", "publish_time": 1709647274016, "_source_registry_name": "default"}, "2.1.2": {"name": "@volar/typescript", "version": "2.1.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.2", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.2"}, "gitHead": "12b960d5d524fd5f5521374c579cb139dfc4c365", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.2", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-lhTancZqamvaLvoz0u/uth8dpudENNt2LFZOWCw9JZiX14xRFhdhfzmphiCRb7am9E6qAJSbdS/gMt1utXAoHQ==", "shasum": "61f838cf4410e328a7ba638fadc41bb814772508", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.2.tgz", "fileCount": 42, "unpackedSize": 164552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxZeAO9jd/dc+1502Ts6OdgMsxep1VOTb+yTYZ+36o5wIgfx8PdBUtn/scUdMLtqcb3nw6s/FoU5IMOxV2JInp5r8="}], "size": 33343}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.2_1709787511744_0.9559435108866332"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-07T04:58:31.874Z", "publish_time": 1709787511874, "_source_registry_name": "default"}, "2.1.3": {"name": "@volar/typescript", "version": "2.1.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.3", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.3"}, "gitHead": "0f861d59faa19cbeadef182ee079be88f9629fc7", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.3", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-ZZqLMih4mvu2eJAW3UCFm84OM/ojYMoA/BU/W1TctT5F2nVzNJmW4jxMWmP3wQzxCbATfTa5gLb1+BSI9NBMBg==", "shasum": "bfdc901afd44c2d05697967211aa55d53fb8bf69", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.3.tgz", "fileCount": 42, "unpackedSize": 164329, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4Byi60EoSVfFDz3/pbJbwCqE+gqb2zgxXcMuLrKXK+gIgbQDH95e4CLsfYd3ovdwnAMKyHTg40NZCGLZoBXy7+HM="}], "size": 33278}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.3_1710914426037_0.8835660669528118"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-20T06:00:26.234Z", "publish_time": 1710914426234, "_source_registry_name": "default"}, "2.1.4": {"name": "@volar/typescript", "version": "2.1.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.4"}, "gitHead": "cc46289ff6be015ee41a1896fdaef6aa68cee755", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.4", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-Mt7wOLPkomFnUfVpb5IHlPhSpD7FJAn+FHSsovePmqFNQzFLz16wrpHjAkorPiAnP0847w71NL5fIJyWbAsR8Q==", "shasum": "361578b01dc20fe02b1a38e156dea997280dcd1c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.4.tgz", "fileCount": 42, "unpackedSize": 165564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/qOBs6W6Vt/3Z9+LrmhQOnNy9yAgm1Ccf1fk5oEQm6AiBIKafVNoHun/qQadinoojbJWTPXWJY8QdjQDncV6vC2A=="}], "size": 33414}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.4_1711065265092_0.16054834495698378"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-21T23:54:25.276Z", "publish_time": 1711065265276, "_source_registry_name": "default"}, "2.1.5": {"name": "@volar/typescript", "version": "2.1.5", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.5", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.5"}, "gitHead": "1b7f456660134891d91608f36cfc6dd2eaea6f70", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.5", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-zo9a3NrNMSkufIvHuExDGTfYv+zO7C5p2wg8fyP7vcqF/Qo0ztjb0ZfOgq/A85EO/MBc1Kj2Iu7PaOBtP++NMw==", "shasum": "756a8626af1c5ce3c9d8df3e79d49bfdd0adf39e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.5.tgz", "fileCount": 42, "unpackedSize": 165370, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEhzoM2ntmDARWQ9CJLAJiHn/on8XiqoqL2YVcr2z7eAiA4qzHhq/LTYAGodwOTB9pxvOp8/fMqfgDfPFl21G4M8A=="}], "size": 33410}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.5_1711140711897_0.9291477305403562"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-22T20:51:52.147Z", "publish_time": 1711140712147, "_source_registry_name": "default"}, "2.2.0-alpha.0": {"name": "@volar/typescript", "version": "2.2.0-alpha.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.0"}, "gitHead": "95ffe51f944ee87f570be113541e17ddfe75f588", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.0", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-T+zku9Ajb9sxe8slJuD1H64c4w2sYqSohj5UWIS264GcA0v66YjhJ1kOifBPZcQQ72KrYbLug8NY9CLiGvDWIw==", "shasum": "7dbea8499eb4523f9dfabe8aff0e308ecd6b44f6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.0.tgz", "fileCount": 40, "unpackedSize": 165126, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDQA3B8aw04DBJVKOhnFUNREQblYArYkstP/eZF6rM3AiBIUcrtVYKnqVk7TVARRjUQoktgzf+lxgH8IlTp2dGO3A=="}], "size": 32987}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.0_1711240626859_0.4678748371779249"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-24T00:37:07.065Z", "publish_time": 1711240627065, "_source_registry_name": "default"}, "2.1.5-patch.1": {"name": "@volar/typescript", "version": "2.1.5-patch.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.5", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.5"}, "_id": "@volar/typescript@2.1.5-patch.1", "gitHead": "79e50fa553cd4a32f583c91433e6ace33eb9b649", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-FgbOcx+J3faVMiBM4XI/4LokIGI3k3k//rstmlS5445excVsCdbIfswvQ6XngNSmnRQNK586BfuZgW+eaGx3Nw==", "shasum": "c14e12e294a13f5960faee38fdce7359f228b60c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.5-patch.1.tgz", "fileCount": 42, "unpackedSize": 165301, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUQGcpPxLeabfUbcm0lYU+boxyEseVplrWMXkKEavehAiBL+wunSKuazuEVM40LLMqJtrxeFyuMMdar3RWn/+WubA=="}], "size": 33058}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.5-patch.1_1711453105056_0.10659996385037429"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-26T11:38:25.242Z", "publish_time": 1711453105242, "_source_registry_name": "default"}, "2.1.5-patch.2": {"name": "@volar/typescript", "version": "2.1.5-patch.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.5", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.5"}, "_id": "@volar/typescript@2.1.5-patch.2", "gitHead": "d4fbbd7d9cbbbdd8b12981b7ce05da85b2e24c3e", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-35o7e9DCZNZwirGkXQiRC17k1ZuePp3s9u/r+mGs+K7uRSq12wNzsI91SVOW8eB7kgA9oR8u6OJskp55ipi4cA==", "shasum": "8fe394b1f2a6c67ed526efd2a15d0e743218efc6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.5-patch.2.tgz", "fileCount": 42, "unpackedSize": 170185, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6TNAwMlW7X/7xXLHryzDNqMK0kndQHXAJbZrEUVoqlAiBnsYsiSGGxd2/AjrtaippCrpDL8u8dH6gtRKFDuzF7AA=="}], "size": 33840}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.5-patch.2_1711467059579_0.589315504053731"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-26T15:30:59.728Z", "publish_time": 1711467059728, "_source_registry_name": "default"}, "2.1.6": {"name": "@volar/typescript", "version": "2.1.6", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.1.6", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.1.6"}, "gitHead": "7feafa54fd3540569d492c2ab582c10c0cdc84d2", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.1.6", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-JgPGhORHqXuyC3r6skPmPHIZj4LoMmGlYErFTuPNBq9Nhc9VTv7ctHY7A3jMN3ngKEfRrfnUcwXHztvdSQqNfw==", "shasum": "8893c3f045171223cb65add5e7f910d57021802c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.1.6.tgz", "fileCount": 42, "unpackedSize": 170334, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFUgFGF4aj0xdDSoq0TTOfbBZQ6hQuRLJsxepS3kybzQIgXTn23xm140qjknvqQ5bNB2VVypPpfLmoL7qOuGjlcaA="}], "size": 34264}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.1.6_1711591230297_0.697731375041512"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-28T02:00:30.468Z", "publish_time": 1711591230468, "_source_registry_name": "default"}, "2.2.0-alpha.1": {"name": "@volar/typescript", "version": "2.2.0-alpha.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.1"}, "gitHead": "e6dcb83ecadcfcc9e22083eea61e1ee817a9d483", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.1", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-XhbOpb+sr3TUjcphjLTxzs+BEuPgsK5OgZBuJ+JRoe+KVRttHLWd2jFH6+PEmQOBB20Z4H0zPYhqVC9LAFKi+Q==", "shasum": "5c05f62cb66f3f2ccc2dc070fc000fd9e12c4b33", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.1.tgz", "fileCount": 42, "unpackedSize": 171138, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUYSivieltvsGn/TOjN0iHW2CD5ADdR0WmXVj3aNREOQIhAPiLpQKKhJYrdVXLSPyWE+9jWVCKNBRb9EA6VdoC6szX"}], "size": 34185}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.1_1711592653602_0.8921927609235596"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-28T02:24:13.752Z", "publish_time": 1711592653752, "_source_registry_name": "default"}, "2.2.0-alpha.2": {"name": "@volar/typescript", "version": "2.2.0-alpha.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.2", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.2"}, "gitHead": "c6a538c915cc8b32ad9a7ca1092a29a326d49161", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.2", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-7sFBz4R4OkrYxUfmp/TzCF6MkrIbhrYHty49FognIZ1ok8bDAFfC8BVDQo7v+TlCECJ6mAP6JNxBBlosA+Vgag==", "shasum": "ff888a7591186f6650e8ef173aaa7b4caaec9784", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.2.tgz", "fileCount": 42, "unpackedSize": 172433, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBWq4h+XXMvINe0etMYim4Jvq9b1gYTkmO/0tCUPasxqAiAEgvGXZlQZ+2fO0M5vcLzjMf32x1J3C0OzQrp1Buyfmg=="}], "size": 34376}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.2_1711860052127_0.9386270800161305"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-31T04:40:52.317Z", "publish_time": 1711860052317, "_source_registry_name": "default"}, "2.2.0-alpha.3": {"name": "@volar/typescript", "version": "2.2.0-alpha.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.3", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.3"}, "gitHead": "9f3432fabfbcdb2bdda6b7d015653706cfdf108d", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.3", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-k0OWc7D0mZbQ4CP6uLW7/dca3zvXHZnTVFYg3Ox0hGQ0CoC9xcNSA01QLUBniOnGrmFA9MCIFIHQUiiUd9A3Ow==", "shasum": "3b2261608b5c18656569893a5f5bef0d19ecf4c6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.3.tgz", "fileCount": 42, "unpackedSize": 172269, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiZ1h64MOiVyBkj/FgNUHuCgdKRPCLs5ArvqqS7JCWDQIgMprMxb2WC/pv09gR5JhHJASX8yoMZy6fGX3BgEygfk8="}], "size": 34433}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.3_1711942960486_0.009927479985996746"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-01T03:42:40.661Z", "publish_time": 1711942960661, "_source_registry_name": "default"}, "2.2.0-alpha.4": {"name": "@volar/typescript", "version": "2.2.0-alpha.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.4"}, "gitHead": "bd85b9c56d71c411fa3342696014c4b5f7436568", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.4", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-YG63Rr3M3B+eTkdxJRK6XewVemMCCOi5WGJyfay7U9M0n950aK6ZPJPJ2pxyCAjXkK0acR6P68TzGIiAjqBErw==", "shasum": "559d7abd210f309883eed97b67492e454983ac91", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.4.tgz", "fileCount": 42, "unpackedSize": 172269, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvgf83/VvH28hyzdFTRV4yBthlxTn9ZNZpd/LebyzAaAIhAIjp1ZTCsjKl8v3mTCNWJD+2TLqJeATVSLltaIvlotYy"}], "size": 34436}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.4_1712052736894_0.9172306875240277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-02T10:12:17.083Z", "publish_time": 1712052737083, "_source_registry_name": "default"}, "2.2.0-alpha.5": {"name": "@volar/typescript", "version": "2.2.0-alpha.5", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.5", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.5"}, "gitHead": "d28a26415df929b2cc949df276fc27e7961b0925", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.5", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-9UKZSDTcgvKMXz9TiU1kHmu3uMuH8+M7oZ6/CzBt8LvFda+ec/ZDcvBjQg2rU5EVn4d+YPYcqenkeHre3tO7Og==", "shasum": "e42fb813f8164ecf1c18e7085821640df4950d87", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.5.tgz", "fileCount": 42, "unpackedSize": 172252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5niCRPXODIES45sgzn0sl/s5PJ7TnmUFVbX4tNKuWGAiEA4PasjLDB0qvExmqpcFu9Z66jaEIXQSuFGr4uM9VMcys="}], "size": 34462}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.5_1712232399197_0.6715878185975492"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-04T12:06:39.355Z", "publish_time": 1712232399355, "_source_registry_name": "default"}, "2.2.0-alpha.6": {"name": "@volar/typescript", "version": "2.2.0-alpha.6", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.6", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.6"}, "gitHead": "c9eb6bc56a5bf0c7c1fddfc66d35717daf0a630e", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.6", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-wTr0jO3wVXQ9FjBbWE2iX8GgDoiHp1Nttsb+tKk5IeUUb6f1uOjyeIXuS4KfeMBpCufthRO2st2O2uatAs/UXQ==", "shasum": "f78458a064b3fbb2126eb7d5aa69dd4a84160c81", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.6.tgz", "fileCount": 42, "unpackedSize": 171853, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHrnKwVR8BRU4Q0ZtqJ3DbEmlVJlHu7d0MzCDNsaxdZvAiBrSd2dFMFd0IhLw30gvERx0cMqXmp08rkx3rcT3FzKKg=="}], "size": 34448}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.6_1712495783799_0.28038672463169956"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-07T13:16:24.024Z", "publish_time": 1712495784024, "_source_registry_name": "default"}, "2.2.0-alpha.7": {"name": "@volar/typescript", "version": "2.2.0-alpha.7", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.7", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.7"}, "gitHead": "c6a2483c541437bb770ca114653d21ce61ccf9bc", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.7", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-qy04/hx4UbW1BdPlzaxlH60D4plubcyqdbYM6Y5vZiascZxFowtd6vE39Td9FYzDxwcKgzb/Crvf/ABhdHnuBA==", "shasum": "19a0ea23233ad7eb3c04cfdea2944d97f87f5071", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.7.tgz", "fileCount": 42, "unpackedSize": 171929, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFqxfEb1EdfmF9bZViFfbE78YbcGAvQvGm4q+S+wSzNbAiEA1c2/ftNeLwV38jXpQONZBe0WJ183GoAr7rA/DnRXZ+g="}], "size": 34459}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.7_1712729115414_0.6684218247018785"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-10T06:05:15.555Z", "publish_time": 1712729115555, "_source_registry_name": "default"}, "2.2.0-alpha.8": {"name": "@volar/typescript", "version": "2.2.0-alpha.8", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.8", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.8"}, "gitHead": "a48e2549a5bf216ab655e4ef7e6d8c545d10d558", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.8", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-RLbRDI+17CiayHZs9HhSzlH0FhLl/+XK6o2qoiw2o2GGKcyD1aDoY6AcMd44acYncTOrqoTNoY6LuCiRyiJiGg==", "shasum": "83a056c52995b4142364be3dda41d955a96f7356", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.8.tgz", "fileCount": 42, "unpackedSize": 174011, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkwAzZ6P8c16BKeaUaSY9XWCPt5p4t4fBpGrka7XrgugIgCxFW2STh6900Mx8HJoSjoZ/oihG3D2jbjaLbvt/kTv0="}], "size": 34772}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.8_1712907391992_0.9155773686017841"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T07:36:32.181Z", "publish_time": 1712907392181, "_source_registry_name": "default"}, "2.2.0-alpha.9": {"name": "@volar/typescript", "version": "2.2.0-alpha.9", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.9", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.9"}, "gitHead": "7373fb794012e219aae3948c730c004827b03021", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.9", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-IApDOj4Bqkqg7xlX//rCKlVC8rw7YKX04h9E4iZCGyxzsj1JgOzfUKk/c/tUEux4Sgzoilnk+0CPqLs55sJmOA==", "shasum": "9d5137d64254d641a13645b3376583a051369a3f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.9.tgz", "fileCount": 42, "unpackedSize": 174011, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSc0JNLOlkS5moN02nc36kVSHSBaZtJDlM+iCXoszuVAiEA+nvgBVvEIYmR5xC0GRnVeGf/hTrIvXa74BP0kskDKCs="}], "size": 34773}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.9_1713116792251_0.700659361903591"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-14T17:46:32.393Z", "publish_time": 1713116792393, "_source_registry_name": "default"}, "2.2.0-alpha.10": {"name": "@volar/typescript", "version": "2.2.0-alpha.10", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.10", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.10"}, "gitHead": "aedd2230883c457f703be93ed150917a3efde75c", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.10", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.1.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-GCa0vTVVdA9ULUsu2Rx7jwsIuyZQPvPVT9o3NrANTbYv+523Ao1gv3glC5vzNSDPM6bUl37r94HbCj7KINQr+g==", "shasum": "14c002a3549ff3adcf9306933f4bf81e80422eff", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.10.tgz", "fileCount": 42, "unpackedSize": 175761, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAF3ISNyfXCYzq2DNuW1shAK3T/RrjWdKWwclzIrSmJnAiEAgulMKyoQHzVYRiH00XBKpjj3R/7sD/n54vfRJkbm/4U="}], "size": 34959}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.10_1713745410153_0.5870701850038342"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T00:23:30.337Z", "publish_time": 1713745410337, "_source_registry_name": "default"}, "2.2.0-alpha.11": {"name": "@volar/typescript", "version": "2.2.0-alpha.11", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.11", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.11"}, "gitHead": "b3e82d02777af164097a7f268e602bc7f94ea1cd", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.11", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-7Oj7j32fbaBcbqIUAgfNkW1/lVCOQT8b2xgY9n+F+y7ECUh6VtVF3z1rjMrs4lo79XvsRIt6SQMQium9Zv0G0w==", "shasum": "cd3834a716e48c77291c9d14b4843129eb1ffa6e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.11.tgz", "fileCount": 42, "unpackedSize": 179477, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwLzNwwSFDeHXWZaPskQIyTZEUmtmwqF2m5DUVeCVh9AIhAJnvvIFF9cTXJ/VBLmQlrm1ugnX6QKkrF5rhtmuxD717"}], "size": 35252}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.11_1714439657191_0.4815074102097139"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-30T01:14:17.346Z", "publish_time": 1714439657346, "_source_registry_name": "default"}, "2.2.0-alpha.12": {"name": "@volar/typescript", "version": "2.2.0-alpha.12", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0-alpha.12", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0-alpha.12"}, "gitHead": "bf61f7a5a4a51669621e951f32ddf102f03a333d", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0-alpha.12", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-Ie4/Pj7NcIZWss+kteREZUYRU0jjiAmWCNoUJ7ViYQsYCrtiLMgPthha09V9zAyhk1rUGErF7/TLtAAX1VuflA==", "shasum": "7a517acf3d702e833c9888a48ae77e6fc465457a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0-alpha.12.tgz", "fileCount": 42, "unpackedSize": 179184, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIUHTqXBnXt9SDUPQQwxc5OpAHIiWf2IBJE99wkL46gQIgeiRk3H7MvjUlpNcsdr4dMVU5x9rWh28t6GhSk1W5918="}], "size": 35214}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0-alpha.12_1714441380477_0.2319404156133169"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-30T01:43:00.647Z", "publish_time": 1714441380647, "_source_registry_name": "default"}, "2.2.0": {"name": "@volar/typescript", "version": "2.2.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.0", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.0"}, "gitHead": "c6e8d2765bbd2ec4a37374fd3a3a31c02661e8b3", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.0", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-wC6l4zLiiCLxF+FGaHCbWlQYf4vMsnRxYhcI6WgvaNppOD6r1g+Ef1RKRJUApALWU46Yy/JDU/TbdV6w/X6Liw==", "shasum": "826a901cefbe9f6c2ca295291eab48ed3eaeef80", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.0.tgz", "fileCount": 42, "unpackedSize": 179157, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBIUIOBQU95t8sRNeMk5+kXPl0TGpTx7+UyRiGdM3beoAiAcE/lNr0t18OqjeW1z2BR0eEXCG76K0WIfUscDg4JmsQ=="}], "size": 35206}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.0_1714533410723_0.5191506392149756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-01T03:16:50.932Z", "publish_time": 1714533410932, "_source_registry_name": "default"}, "2.2.1": {"name": "@volar/typescript", "version": "2.2.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.1", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.1"}, "gitHead": "629f686bda694077b236a0bcd0304437bb2eabf5", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.1", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-Z/tqluR7Hz5/5dCqQp7wo9C/6tSv/IYl+tTzgzUt2NjTq95bKSsuO4E+V06D0c+3aP9x5S9jggLqw451hpnc6Q==", "shasum": "21585b46cd61c9d63715642ee10418b144b12321", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.1.tgz", "fileCount": 42, "unpackedSize": 179069, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaMb5bk4iDE3jO0FZJE6WSeBrCflr4BpJ3mwPiI27vdgIhAOHhoF7XMRUaoEA0Nt6q8TOv77uwxF2LBfB0mMpvFkvB"}], "size": 35189}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.1_1714993138707_0.4883804531773259"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-06T10:58:58.874Z", "publish_time": 1714993138874, "_source_registry_name": "default"}, "2.2.2": {"name": "@volar/typescript", "version": "2.2.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.2", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.2"}, "gitHead": "8aa41e4c2ab1e954b8c777f789b7a948972e77aa", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.2", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-WcwOREz7+uOrpjUrKhOMaOKKmyPdtqF95HWX7SE0d9hhBB1KkfahxhaAex5U9Bn43LfINHlycLoYCNEtfeKm0g==", "shasum": "b133919b5d1ef99f882916ddb62297766500843c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.2.tgz", "fileCount": 42, "unpackedSize": 180753, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCty2PrEc94VpUUsZIB77pISb9+x5ZrlRCr2fxiWec5JwIgEO4gsGJ463lhIjm5gQA5RWROqUrRvKwqlQeKDVRVGDY="}], "size": 35473}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.2_1715329094267_0.2686090872302178"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-10T08:18:14.443Z", "publish_time": 1715329094443, "_source_registry_name": "default"}, "2.2.3": {"name": "@volar/typescript", "version": "2.2.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.3", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.3"}, "gitHead": "189ea5c663630358a9ad965c50d2bee69a9d84eb", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.3", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-/0zAf1Rn5x0Pw5kKJp1n522CRp6ptPhvECH3250d3NBo0qwxP183brcVO+0ourqGWQy39vcfB3VwQnze6w4mgQ==", "shasum": "07766e718407871c1da59b5d2900c5f8287f0312", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.3.tgz", "fileCount": 42, "unpackedSize": 181591, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG//8Q1LxcQ5ZSZneiqTyMzAS+kXwKsxrK873Rofx9jAAiEAwZxh+Im7SvUYOUNR6K1gLhQqsvNGj4sVYexZ/vBSBWw="}], "size": 35619}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.3_1715770525905_0.5574270624398552"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T10:55:26.108Z", "publish_time": 1715770526108, "_source_registry_name": "default"}, "2.2.4": {"name": "@volar/typescript", "version": "2.2.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.4"}, "gitHead": "71a58c8b9c3a3e420b95df9ffb4a50be37cb31bc", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.4", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.3.3/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-uAQC53tgEbHO62G8NXMfmBrJAlP2QJ9WxVEEQqqK3I6VSy8frL5LbH3hAWODxiwMWixv74wJLWlKbWXOgdIoRQ==", "shasum": "8f283d8e9769fed03d9e632a159152e2b3295af0", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.4.tgz", "fileCount": 42, "unpackedSize": 181618, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGydBcU1nMEDIxm9/KmhLxzEex0ngXtq2F6jK9TjvCMqAiAN1kTYyZTBR9PLwT8wXOR/Gem279hEZgkf18MyyMAatQ=="}], "size": 35634}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.4_1715773943509_0.09637283542912556"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-15T11:52:23.877Z", "publish_time": 1715773943877, "_source_registry_name": "default"}, "2.2.5": {"name": "@volar/typescript", "version": "2.2.5", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.2.5", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.2.5"}, "gitHead": "ee4aaa9da58c4c942d6cb74f9028d19b7ef4465d", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.2.5", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.4.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-eSV/n75+ppfEVugMC/salZsI44nXDPAyL6+iTYCNLtiLHGJsnMv9GwiDMujrvAUj/aLQyqRJgYtXRoxop2clCw==", "shasum": "4c1270a5a0508d88299e37caa59849e86b57cac4", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.2.5.tgz", "fileCount": 42, "unpackedSize": 181678, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC56GiDbxXxWc2K3s9jc59L4LGjdaXlRa8mOiHFVyQBxAIgKzl6H3CmFxZ0RSJyprY8yknvRE7c4da3am7YSn4m1iQ="}], "size": 35643}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.2.5_1716468213375_0.4126485457429947"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-23T12:43:33.556Z", "publish_time": 1716468213556, "_source_registry_name": "default"}, "2.3.0-alpha.0": {"name": "@volar/typescript", "version": "2.3.0-alpha.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.0", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.0"}, "gitHead": "f17c19f712651acde33cc2171a112e64db0b460e", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.0", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.4.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-+nIR27GQNzJmpKXSuDX4RVKwbDrB262sYJwbh4uCB7ckgr63GuOtvq+VX6DSwv4hzLbU/A9vu2KbjaTLF9G8aw==", "shasum": "6b7e7df70a506ab51e87454d99af570b9c250e3c", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.0.tgz", "fileCount": 42, "unpackedSize": 180599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcl3yR1IsmMU4srn37MTa/do8Y9tsTqNfABguiLhLZ4gIgCNNcDZ2WlqnAwn9dooCvE6owxLpBaTBra0zPQwYZim8="}], "size": 35689}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.0_1716937025955_0.6122043661267202"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-28T22:57:06.136Z", "publish_time": 1716937026136, "_source_registry_name": "default"}, "2.3.0-alpha.1": {"name": "@volar/typescript", "version": "2.3.0-alpha.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.1", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.1"}, "gitHead": "173be13e1d51c62cfe5ddc9926933d30a7aa1d11", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.1", "_nodeVersion": "18.19.0", "_npmVersion": "lerna/3.4.0/node@v18.19.0+arm64 (darwin)", "dist": {"integrity": "sha512-lV9fwijt2vQRuZhrED2RWZ8m3lXq9B0pL7lW/qlks06jhJM3OEMk1MgIXENZjQnN67HwYAvL1TC8AEPBwGXbow==", "shasum": "4b3b08d1d1b54b98b6d97626a367f7874c0fc682", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.1.tgz", "fileCount": 42, "unpackedSize": 180657, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTzoHfUvW+CqXoghfHslBKjwsKJP5w1CTOuul08NB3CgIhANENElfX9xjC4jIY33Mei6Q6DdVQNjIYavdWlzENBPef"}], "size": 35697}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.1_1717101741221_0.7013471889655551"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-30T20:42:21.401Z", "publish_time": 1717101741401, "_source_registry_name": "default"}, "2.3.0-alpha.2": {"name": "@volar/typescript", "version": "2.3.0-alpha.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.2", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.2"}, "gitHead": "25e56612551d56037f4064c599532b4c7c185118", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.2", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.4.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-bE3sZvWqiil4HqLL3KphoFIetHeRsX/KDqU7ablAsx1Ubzh9Q652OBwX0nEIVyhrLYBHpEi/aA5jinb1vphhQQ==", "shasum": "8ad8f282b94a781de2499df1e89222a9e0b0b95a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.2.tgz", "fileCount": 43, "unpackedSize": 181093, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWEnRdCQlCSH+la7f3UagLIpFJES6Vv4Dw51c3Mn0YjQIhAJzeSOVdFZtebrJxvN3ELAlGt1AIyu9mYve+oezxEBlR"}], "size": 35840}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.2_1717109322888_0.45580492419436647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-30T22:48:43.064Z", "publish_time": 1717109323064, "_source_registry_name": "default"}, "2.3.0-alpha.3": {"name": "@volar/typescript", "version": "2.3.0-alpha.3", "license": "MIT", "_id": "@volar/typescript@2.3.0-alpha.3", "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "homepage": "https://github.com/volarjs/volar.js#readme", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "dist": {"shasum": "8afab51306c9481b916dba7328ca443a004cb563", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.3.tgz", "fileCount": 43, "integrity": "sha512-4lkb18o8JlNzlcXDcx4ldNL118ruVekm2mwuB4uTl9kloiz3HZHTohmQAvjOXijaom2vzY40Pyesp+doQTEDTA==", "signatures": [{"sig": "MEQCIA6wCqXEQAkvWLO9wHXYhinCkylvFjISnYBdDHTBGY9RAiB7qHpbXRHJKRDz3i5b2X/LaZxrxi0lCDxjJWDK858vqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180646, "size": 35804}, "types": "./index.d.ts", "gitHead": "d322a122012dc339a3ac5139220e91649d508522", "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/volarjs/volar.js.git", "type": "git", "directory": "packages/typescript"}, "_npmVersion": "lerna/3.4.0/node@v20.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"vscode-uri": "^3.0.8", "path-browserify": "^1.0.1", "@volar/language-core": "2.3.0-alpha.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/typescript_2.3.0-alpha.3_1717173242699_0.8666745817172485", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-05-31T16:34:02.892Z", "publish_time": 1717173242892, "_source_registry_name": "default"}, "2.3.0-alpha.4": {"name": "@volar/typescript", "version": "2.3.0-alpha.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.4", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.4"}, "gitHead": "8ab3ce02b06a410e9321eea3e9f23d36f97fedda", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.4", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.4.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-dQtdczowQCnGlJckYsfUXyieFgTpBNXYPY/EU+UzVmaA2QAmuTQPmJdx9R2CwUtbxDZAA1Zi5gNP0qscUPk/CA==", "shasum": "1998dd38d96624b949d8321a40773ce9fc9477cd", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.4.tgz", "fileCount": 43, "unpackedSize": 181740, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtVRYON5W2YrogWd5IOwNfSgOmEpdrcMTxlJ6dKF0gIQIhAJvqeZ6EsGtw/ttysODUqFUlFinqH/RT3U7wkjHAtwrA"}], "size": 35871}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.4_1717187604102_0.6414503597161618"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-31T20:33:24.294Z", "publish_time": 1717187604294, "_source_registry_name": "default"}, "2.3.0-alpha.5": {"name": "@volar/typescript", "version": "2.3.0-alpha.5", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.5", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.5"}, "gitHead": "c3f6867de39095913ceaa2256ad35a9ca1a90487", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.5", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.4.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-lFfSIQMxx5IWpJ15v2cetxzsJh2egc/2I8tn2LPcl/1vtMStoIlX6T0h/R/5yUu2S+9vLMjKXwd3RPu/V9j6Ww==", "shasum": "0c1bce04995821357e6832d507c83aeb72173417", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.5.tgz", "fileCount": 42, "unpackedSize": 181229, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAtvcE13UGSQJxPYdg7ObI/fkLHw62eFBLBho+Kxv7gHAiAg/VWmUMWNBPOSZWgyN2aWd4l/FfFlpuW0Mrtd0mHf1g=="}], "size": 35722}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.5_1717289367804_0.3994757381794036"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-02T00:49:28.004Z", "publish_time": 1717289368004, "_source_registry_name": "default"}, "2.3.0-alpha.6": {"name": "@volar/typescript", "version": "2.3.0-alpha.6", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.6", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.6"}, "gitHead": "f3103e86be8d80cb36f66be4c054ef9a255c29b1", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.6", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.4.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-bBC0yLlm6UrnXYV/fnA2dSf6k9pdFbp3iVPWd1Cb2OWs3IUL1BjHscYfiEL8JHdJIvya/HnqEdTfr7hnEVIsBg==", "shasum": "80dec71070335a376f1337be0ec452a9d96939c8", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.6.tgz", "fileCount": 42, "unpackedSize": 183216, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaohwSYiKWAuCthhTLxUCIEZqqZbuE33J8/lJh8ryfGgIgcboe7b8EDHP/5fXCCHd+dbq0r7jU0YIbfHSjd8uui6s="}], "size": 35858}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.6_1717553925307_0.3762874144370105"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T02:18:45.463Z", "publish_time": 1717553925463, "_source_registry_name": "default"}, "2.3.0-alpha.7": {"name": "@volar/typescript", "version": "2.3.0-alpha.7", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.7", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.7"}, "gitHead": "3cc2a62516113c5789e4f54766e25063099a13a5", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.7", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-0ZNWNKhF9bSM828vfxXvRS4LfHRRIuIsjBLb8zVPPtSiEEGjs0obJKs/vYtnxbCgm3DvJoJYaJ8YC7oM6GzVsA==", "shasum": "799c04d339b2066ff67e8083008f8df8f18f7dde", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.7.tgz", "fileCount": 42, "unpackedSize": 183205, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEKEacbB26iSztLaW6zRfHJMXf01MrSWgYTJXyQBcoS8AiEAvs4hM0KmJ3ueAT/ETPfNHdgFdf9A6YoISMZhg2Iwk9w="}], "size": 35854}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.7_1717555083736_0.37710440098293363"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T02:38:04.015Z", "publish_time": 1717555084015, "_source_registry_name": "default"}, "2.3.0-alpha.8": {"name": "@volar/typescript", "version": "2.3.0-alpha.8", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.8", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.8"}, "gitHead": "377a0083ac697a476509805a6777a2339391dcaf", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.8", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-KoEAQQ9eVGh9W+dc5JSt7tXeIFlmabbp5vYhXaMufLDUmpjpOGwV0nZohfWZxf7OXeNPeAepnvPI6wjCVj2kGw==", "shasum": "78cb51afd835e4c0cf8e3910fc8eece5871cf1d9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.8.tgz", "fileCount": 42, "unpackedSize": 192406, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBfMwjzhk5SZexrgoO7Mt9sFAjWFpWF5x/GjHJVl1qz7AiAmOBvKAuuWRhZ9z78zpSaKhPlm8llNpQRDiZF9fqSztQ=="}], "size": 36970}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.8_1717702639015_0.9818446086285606"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-06T19:37:19.187Z", "publish_time": 1717702639187, "_source_registry_name": "default"}, "2.3.0-alpha.9": {"name": "@volar/typescript", "version": "2.3.0-alpha.9", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.9", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.9"}, "gitHead": "3f741930343896dfc464a893ebe5f3619bb1a1aa", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.9", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-+9/xcK9dtZOdAw5hBrdZaeBB2xjyloAEshDIcuGqY3L6QbHYyGZ2oYWHOHLPB0ANjHMtmaZNbA4UxozB4mYzpg==", "shasum": "c30ab486f0e3cb7ae269c5f21fae1e7139aad592", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.9.tgz", "fileCount": 42, "unpackedSize": 190451, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0cPmD+aTJck9Z55Mt6K3grdhPfABS8pZN7tbTZUjAuAIhANXWbC8RwedRlHujviW4frJdEzK/fe+RCyhMcBo092n1"}], "size": 36792}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.9_1717706535525_0.6954661929709647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-06T20:42:15.758Z", "publish_time": 1717706535758, "_source_registry_name": "default"}, "2.3.0-alpha.10": {"name": "@volar/typescript", "version": "2.3.0-alpha.10", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.10", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.10"}, "gitHead": "44fffdc323ca3ff55818a8656a2726867df53a76", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.10", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-4Bb3OKd+vktVSNEBYoC9iJUGnS1vy/NVvS02ktH3ZffQpHdLFGm8TmLhHGVeOZT33Zu5n7uHWkYiQ8ULddmcbg==", "shasum": "cd5539fb824aa17836c2c7a7d07b4044bcd6c5b9", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.10.tgz", "fileCount": 42, "unpackedSize": 190520, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFrS3FrLPfCwZoXtz1pk0xMc4jNZU6fOCteLdrOgl2nZAiASvmL4i9NEsDu4za/Ex0KEnoj9kFq1UwCM6vCQZEjrCg=="}], "size": 36838}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.10_1717735757778_0.5540296170766597"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T04:49:17.928Z", "publish_time": 1717735757928, "_source_registry_name": "default"}, "2.3.0-alpha.11": {"name": "@volar/typescript", "version": "2.3.0-alpha.11", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.11", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.11"}, "gitHead": "c83f835629496d3a4cc38f4b789bb676d07a2118", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.11", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-DdqHf0JUsl+p5DbyI63JFrJzmFrFbrN7goaBzRGpt0RAzXB/FBWSFGSoiwZcnzkvTlixI1Y8ZvimDGvRDfjcCg==", "shasum": "decc464feb985e73a5c951571b1ebc48d5adb5fe", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.11.tgz", "fileCount": 42, "unpackedSize": 191034, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFvtymr9H8Q+QoA3KHi/uQ1mcGsnfXQqiWdBmVSzgVh0AiEAhcJ/w29YPF//Rr3b2aNdGT4qJTjMmnxwZVEBlYiAK30="}], "size": 36862}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.11_1717738971916_0.6835695277979796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T05:42:52.081Z", "publish_time": 1717738972081, "_source_registry_name": "default"}, "2.3.0-alpha.12": {"name": "@volar/typescript", "version": "2.3.0-alpha.12", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.12", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.12"}, "gitHead": "750f23e9cebbc2262904c91600e631036eaec4d8", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.12", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-MpvZnNbkkQdRmjYtd8+hTxth5zlyTSJ/KAORGxBPrssMMkFv6ak0YhHLo5rh3myZaTvK4UQt+A86AqqsBkU7YQ==", "shasum": "cb91a4719920d1bb7abff5a94603cf99af579348", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.12.tgz", "fileCount": 42, "unpackedSize": 191034, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBKc4iwtQWl6qF2i95lG89uk72rq+e00o1LflFSmCheOAiB12N0m4EAOqN7SljE8Pm3ucE5c+K8S8NmyhzNRrhCCcg=="}], "size": 36862}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.12_1717739658549_0.7579591039243978"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T05:54:18.719Z", "publish_time": 1717739658719, "_source_registry_name": "default"}, "2.3.0-alpha.13": {"name": "@volar/typescript", "version": "2.3.0-alpha.13", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.13", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.13"}, "gitHead": "4effe4689ac845b17b7c821d50deda62c124f23b", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.13", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.0/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-DYd1hMG/tZkTql0nXB85qkD+NSjLCzgfWCmtuzQdMUfnRdMKlIiISVFylEgJ41YH7p1r7KvSpmmADiMgXWgSLQ==", "shasum": "97f9eb72f7c833e1b844b3dc9dce056f858530f5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.13.tgz", "fileCount": 42, "unpackedSize": 191726, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3q2dWi1xn38OD/Tk7ErLLCATjGlQd5Eub9/3WYSaccAIgbc6GYVmRuOI2xR5pegA0spe9pGfkLENuqWVqGJb+gUw="}], "size": 36913}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.13_1717777255632_0.8775147382554225"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T16:20:55.904Z", "publish_time": 1717777255904, "_source_registry_name": "default"}, "2.3.0-alpha.14": {"name": "@volar/typescript", "version": "2.3.0-alpha.14", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.14", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.14"}, "gitHead": "9a8179277fe224de1d13e6331115918b79b30721", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.14", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-YnaivvHu/HlVgFRUFPh3X42GXYawSIXPvkIGND/RZXJ1iyrj9CB/UEtsMUV55TOULbfJyc92F2EpOMn/lMyqwA==", "shasum": "65a7ea478b558deaa47dfe85498420dbca24df47", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.14.tgz", "fileCount": 42, "unpackedSize": 192381, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkP6ma5z7soHsmF5CXB/Uu5iy+d3IIcWD/wE8Dot3LJgIgCQ/5AXCdB29Mg7TdeENg/3C18/2SfAzW7igLIq9qnR4="}], "size": 37039}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.14_1717823016116_0.28067914154687834"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-08T05:03:36.333Z", "publish_time": 1717823016333, "_source_registry_name": "default"}, "2.3.0-alpha.15": {"name": "@volar/typescript", "version": "2.3.0-alpha.15", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0-alpha.15", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0-alpha.15"}, "gitHead": "25ab3f45d1c74f52904c703378b04a6ecbda9535", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0-alpha.15", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-sTzUyqGC1wkzVyY0XZBO5smCPDRvIqxlMTUw7bZebuD/7sGaVfyk9ryE29aG6CLpuYDev/ugpQsKoFVhFaQQ8A==", "shasum": "b13d027bd58628bef1031ad939cb80a5f6317b34", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0-alpha.15.tgz", "fileCount": 42, "unpackedSize": 192483, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC0CtFLSpiMNqR+CaJNtSK/PtB34yNM+PZIo2Z2Bcf2gAiEAwiNIZSyd/mAvgCLiEVf9NxdRVJRkrwasKFgJnPDjcks="}], "size": 37045}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0-alpha.15_1717857716871_0.42633547694072815"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-08T14:41:57.082Z", "publish_time": 1717857717082, "_source_registry_name": "default"}, "2.3.0": {"name": "@volar/typescript", "version": "2.3.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.0", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.0"}, "gitHead": "4f6488605e22e0f76ea877460848a443fd3e8762", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.0", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-PtUwMM87WsKVeLJN33GSTUjBexlKfKgouWlOUIv7pjrOnTwhXHZNSmpc312xgXdTjQPpToK6KXSIcKu9sBQ5LQ==", "shasum": "00306942e95e2e22fed8daf73ec386cd72601ecf", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.0.tgz", "fileCount": 42, "unpackedSize": 192456, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAxOiQWdsikeWvd8wnk9mEB2ofAQ1ZUcpGWfqxL/qKJIAiEAgMN9Tz+5CQl+1UgT3wUsoWp3P7BHTQRdfAqT/FXX/LQ="}], "size": 37041}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.0_1717891768869_0.5800648571074223"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-09T00:09:29.103Z", "publish_time": 1717891769103, "_source_registry_name": "default"}, "2.3.1": {"name": "@volar/typescript", "version": "2.3.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.1", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.1"}, "gitHead": "51742317a2950abd97e1a1a266b2c137bede4ad3", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.1", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-OrUV6dYt/1h92+aWElexra6dp++gF/IEddvwyxeobyYfKAoKDUMsWU0iJCj0clZlfdyYaLmNEAkulJlVimxnOw==", "shasum": "ffdbd361844ad9b3b6f45a4a173b3d384385792e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.1.tgz", "fileCount": 42, "unpackedSize": 192038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDADjZA0cSYYDaz/V+6v+SOwFtE8VWULE57z8grsQP1AiEAvn67ND9NS1bLISjmky/p6YlFQLpK7OALIS+BoofCjzM="}], "size": 36849}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.1_1719021640615_0.7045711583739025"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-22T02:00:40.817Z", "publish_time": 1719021640817, "_source_registry_name": "default"}, "2.3.2": {"name": "@volar/typescript", "version": "2.3.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.2", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.2"}, "gitHead": "d07e7d7c4176eec4905e7a629ae6f278d1c72b48", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.2", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-HJ1mjiEU/R1Wg3lrBp9jqNZvMOkNLA8+ryHhrzHAjV7pv214mQT/mB/1msu3mduh1Q2iDETU4Vttl5RA7ZPezg==", "shasum": "a5ccf061917f381554507e0f1305b98e2cbb337f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.2.tgz", "fileCount": 42, "unpackedSize": 192038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGWMolKo/IaBV0QAAourH+scMqqO4ufla+GtujFdSz+7AiBa6Ca7jHAmuHeAYgOlG2F5PbHBg571VC8ENPCLo9juzg=="}], "size": 36854}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.2_1719170531349_0.053903512589465796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-23T19:22:11.549Z", "publish_time": 1719170531549, "_source_registry_name": "default"}, "2.3.3": {"name": "@volar/typescript", "version": "2.3.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.3", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.3"}, "gitHead": "08802f144de6ff05a86b99df1f55a5ba243cc926", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.3", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-cwGMSwqIR54Hngu5dYmUJPHFpo6KGj7BlpwT2G9WTKwEjFlH4g/0IPeq064428DAi/VMP42vqAQSBiEHi5Gc4A==", "shasum": "098dd2e2a9634acff584004bf4ff8957419612be", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.3.tgz", "fileCount": 42, "unpackedSize": 192038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDUED4RUF8GZTEoE8Nv3Dq5sONI5KQmGHnHaesKnJVSAIgNVCYFwH5PmScYvYSbN+vYGoxo6u+UEQNJWx4KB9Mnis="}], "size": 36854}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.3_1719217528780_0.7737532410561294"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-24T08:25:28.945Z", "publish_time": 1719217528945, "_source_registry_name": "default"}, "2.3.4": {"name": "@volar/typescript", "version": "2.3.4", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.4", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.4"}, "gitHead": "ba5b381923bbc8a565e84de72fa98474501fe817", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.4", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-acCvt7dZECyKcvO5geNybmrqOsu9u8n5XP1rfiYsOLYGPxvHRav9BVmEdRyZ3vvY6mNyQ1wLL5Hday4IShe17w==", "shasum": "bfa2834c79bd0b9a38cdfdf220fea0afa8ed64b0", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.4.tgz", "fileCount": 42, "unpackedSize": 192038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+MM+fAUSyHgGDiwzDsubsP5oS0ssVYORowEUc7XDyDAiEAjGPmieLxEwz+uY+ZA6gPIZylECqLiGgbjWcWWbaYXwo="}], "size": 36853}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.4_1719258127634_0.36458650135687476"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-24T19:42:07.855Z", "publish_time": 1719258127855, "_source_registry_name": "default"}, "2.3.5-alpha.0": {"name": "@volar/typescript", "version": "2.3.5-alpha.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.4", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.4"}, "_id": "@volar/typescript@2.3.5-alpha.0", "gitHead": "dc6e926910d55121ae45f628a7ff03735a29d1fa", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-hJKHwqkfwBmv69kJ2b9fQBCYRrVnujv7MupeIMFOZSd8LLpqUYPcZS0bMAlQ+Sz6UfIC9Adu6RdAmA7zK1OELg==", "shasum": "8d93d64fece02cf15c1537924e0f820ce98f5e09", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.5-alpha.0.tgz", "fileCount": 42, "unpackedSize": 191996, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlSierPsKgEwo+eKdtKVOTGq2V65Whu9BHPLSSDcVV5AiEA5Si+yqYkOwDtoXp1UVVbER9nNH8PHV1Tlnhli892t8Y="}], "size": 36416}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.5-alpha.0_1719261287798_0.4434874688018562"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-24T20:34:48.002Z", "publish_time": 1719261288002, "_source_registry_name": "default"}, "2.3.5-alpha.1": {"name": "@volar/typescript", "version": "2.3.5-alpha.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.5-alpha.1", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.5-alpha.1"}, "gitHead": "111bfae316c66a087a497e12740d46fc2c74cab0", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.5-alpha.1", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-Ytwj/ZICSY0yW18YG8e2fugdOiBf8zZu3JOpHzi9lREZtdvR2aByZbXeKaZpK+nVxkx4EyJvDiVzZN+8ZzL6YA==", "shasum": "54dbae701425eab385293a1a3f0aef5d82bdd9b5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.5-alpha.1.tgz", "fileCount": 42, "unpackedSize": 192068, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEUQC0BP4003874Xfa713Fk0srL3ZL2CBcgvRQZFWIGdAiEAuZoJ9bSjpcl7AQFhOHsZAuK5ry7vcRmubK7bfMA+MU8="}], "size": 36832}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.5-alpha.1_1719374798450_0.5965329365241498"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-26T04:06:38.653Z", "publish_time": 1719374798653, "_source_registry_name": "default"}, "2.3.5-alpha.2": {"name": "@volar/typescript", "version": "2.3.5-alpha.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.3.5-alpha.2", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.3.5-alpha.2"}, "gitHead": "875106ba581210ab30829170585205cdb69b73ec", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.3.5-alpha.2", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-rmyseI0p80vUloN5fVrfZhy8U6PlwwbKmuYdTxnoV5FqErI3dXX1xhLnpFRDQA3H3mg7eZqfo60MEup9+rhIGw==", "shasum": "8d8b5af7d8116fbba4536c4ae29de386925c2f14", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.3.5-alpha.2.tgz", "fileCount": 42, "unpackedSize": 193849, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGh5YsfbAUyOZU0Sl3t346dezxyPeCIy3VMe1Ga3JaJGAiBaIxC5rzIpqfQro/pHKK8xPjTphSiaJ8+Qe44hyJmn+w=="}], "size": 37247}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.3.5-alpha.2_1719383710154_0.6696191053611344"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-26T06:35:10.358Z", "publish_time": 1719383710358, "_source_registry_name": "default"}, "2.4.0-alpha.0": {"name": "@volar/typescript", "version": "2.4.0-alpha.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.0", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.0"}, "gitHead": "007fc74c461e2fd3fb269bf4f3924cc23c35ba56", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.4.0-alpha.0", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-uBmX5+53dnfX5QD7FIARb2xhlBKUp8ejUwjKjt1df3zNDDo/GuPckRbXf6f8LhtXa9dP5FQxU4dC9FpBpzo5mw==", "shasum": "bf8ab94509e980c205dad2b368f9ca8cf702562b", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.0.tgz", "fileCount": 42, "unpackedSize": 194343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDNSlsuUNicsTfhIVjjTOVfwuTEx7tnt7n+Y+4PXWKW/AiEAm/vnxnk3BoLp4YmRllIRd2UQkeU/ncfPp+UL1x8YPMI="}], "size": 37321}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.0_1719407458654_0.8750620438642007"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-26T13:10:58.888Z", "publish_time": 1719407458888, "_source_registry_name": "default"}, "2.4.0-alpha.1": {"name": "@volar/typescript", "version": "2.4.0-alpha.1", "license": "MIT", "_id": "@volar/typescript@2.4.0-alpha.1", "homepage": "https://github.com/volarjs/volar.js#readme", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "dist": {"shasum": "ea95b05e3eb26e24edc6a6476b7ad78776c1c4f6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.1.tgz", "fileCount": 44, "integrity": "sha512-2TEqUBNs28LYPaBwf9bRqashmiuWwxdMockqvcWatniXLrAvHCASmB9Prci0P6MElB+kcAxJU6chsQfrVTTJAg==", "signatures": [{"sig": "MEYCIQCFkyIv4LtGROq2EicI09+mOnNkcOfM65GSVe67sjt3+QIhAPm7r+NVSB+eZc6M5g4DyIIjG0SkmVbYOMtTUoOsTJOM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241284, "size": 42701}, "types": "./index.d.ts", "gitHead": "c096c457fea026fcd432b82224026f747fab571b", "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/volarjs/volar.js.git", "type": "git", "directory": "packages/typescript"}, "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"vscode-uri": "^3.0.8", "path-browserify": "^1.0.1", "@volar/language-core": "2.4.0-alpha.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/typescript_2.4.0-alpha.1_1719634497856_0.527724405188126", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-29T04:14:58.118Z", "publish_time": 1719634498118, "_source_registry_name": "default"}, "2.4.0-alpha.2": {"name": "@volar/typescript", "version": "2.4.0-alpha.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.2", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.2"}, "gitHead": "62914047f925b79391fee6e4f697705b77641baf", "types": "./index.d.ts", "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "_id": "@volar/typescript@2.4.0-alpha.2", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.5.1/node@v20.14.0+arm64 (darwin)", "dist": {"integrity": "sha512-CPqp+QLOQRlaEW9jXXq6grr6v/vZWpsC6yv7TuKEHMly9K0m2afn31VI19/rohPFBUXBB7YnmBFbZyH4FT4Pmg==", "shasum": "87268239f5a0243c383357a6ddc9f5bbb3ad6e88", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.2.tgz", "fileCount": 44, "unpackedSize": 241289, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBB9UQPqFIbXET5lJx5tTz6M8YeTh3UC8wynpdvd83goAiBmYgOQoxmFTJKzrONRClxmpHySO06mBUJmpGdChCgWxg=="}], "size": 42702}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.2_1719635225959_0.15047597730077933"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-29T04:27:06.178Z", "publish_time": 1719635226178, "_source_registry_name": "default"}, "2.4.0-alpha.3": {"name": "@volar/typescript", "version": "2.4.0-alpha.3", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.3", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.3"}, "gitHead": "cb556d64b389cf2ab637f0cb97fee35f3579cfad", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.3", "dist": {"integrity": "sha512-fjWaXUlPLedDCyTc4YpDN5c6T7JK/mA7tmSSSirpQOX5CW/gdy3vpC+XbQta/KjVrRRFNYC2pncTBPyethdyxA==", "shasum": "faf3881f50b2d7c0eeabd76e86a094e7138e492a", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.3.tgz", "fileCount": 42, "unpackedSize": 199326, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAu2GFBd3HXlyW5bJIb6ceGLSMDgoCDKJtTKvV65YbVKAiEAruTEHNCZMcN6Gfm+f46FOvWwpOza0DSHg58k8WNAHIQ="}], "size": 37615}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.3_1719777819796_0.9489162430856362"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T20:03:39.976Z", "publish_time": 1719777819976, "_source_registry_name": "default"}, "2.4.0-alpha.4": {"name": "@volar/typescript", "version": "2.4.0-alpha.4", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.4", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.4"}, "gitHead": "8a21a352c664a57eddceb87c5ca6a087d12cba58", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.4", "dist": {"integrity": "sha512-6ZDhhd5xp5jxjRbiySmLQNN5k1nioe+XDUTOlkFCw90NYv7AKeOTWDCeTBNXW/itdmzvV2geGAz/k1sr7OJGMA==", "shasum": "47d9f720ede890ae1ca0ac26fc01b22af450c209", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.4.tgz", "fileCount": 42, "unpackedSize": 199326, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC50YpWHIybS/QvG7yRr14AuqYSS2urelQ+OY2CS3oglAiAyr3ug88dNru60h4+IPwo2Xh+Pukas9fa/J9TAJtHj/A=="}], "size": 37614}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.4_1719785567984_0.5311641614921443"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T22:12:48.195Z", "publish_time": 1719785568195, "_source_registry_name": "default"}, "2.4.0-alpha.5": {"name": "@volar/typescript", "version": "2.4.0-alpha.5", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.5", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.5"}, "gitHead": "bef4f4bf123264f3b0ce3da11adf1e0da387ce6d", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.5", "dist": {"integrity": "sha512-D9nzGP09afyLlsXC5rzVeLzEaMLOmW1GGPyOiuXRRGTLshX+/cp+MNsUbwUd3pih0OhRmpUFl4VHpUGA2M4iBw==", "shasum": "948d727af9f2e1a09c0717a2a35884c52b916aa4", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.5.tgz", "fileCount": 42, "unpackedSize": 199326, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGjuv9smPXKrSguAzLkPl9PcVnRHi9OOtjKTLTik6CrAAiAEJhRVQjvkCOnk1fMots46T6girj08BP40K6yH2P70lg=="}], "size": 37614}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.5_1719789824215_0.6185854191839217"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-30T23:23:44.384Z", "publish_time": 1719789824384, "_source_registry_name": "default"}, "2.4.0-alpha.6": {"name": "@volar/typescript", "version": "2.4.0-alpha.6", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.6", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.6"}, "gitHead": "7e6c0b8fb66d09037b3540bbac2c4d976b1110ce", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.6", "dist": {"integrity": "sha512-4wzXpPE2f2x3QQU5IIkg7urcrnL41WRTLpZAzjxRhjXhz+O7PgsAqiqshmEB8aBpHjH3MYCs1u0bA6BJ3dqLIw==", "shasum": "b91512ad9ad956f63b880b02c344df8ab2ffcaa6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.6.tgz", "fileCount": 42, "unpackedSize": 200056, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDPnzj7KaYOre/GIUj8oGV8+5kvrV0wREMq8LPYez8+1AiAbpjeu8aRJrOtsviOi2uJDlwYaExgNjZXalgANNYoRlw=="}], "size": 37749}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.6_1719842886816_0.7028321804535629"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-01T14:08:07.045Z", "publish_time": 1719842887045, "_source_registry_name": "default"}, "2.4.0-alpha.7": {"name": "@volar/typescript", "version": "2.4.0-alpha.7", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.7", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.7"}, "gitHead": "890a891306835b992269ef1225f71a42f53d83e8", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.7", "dist": {"integrity": "sha512-MjvkhcIYPDZS5vbE4bSvbWi8z/VI47AY8MkPVgfa6xqTDLLxV6+QulJ8hIC3wYxh3dfVmA5MOoL5c3cZAsWbDQ==", "shasum": "2597a1f1f5e3e158348fac40e330c458d1b32d67", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.7.tgz", "fileCount": 42, "unpackedSize": 200056, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCa7/k+lbPYxRcvRY36bVGInQo120Rls9qTRgLuSWJ4sgIhAK2rMgwje2tjpO4auKOj9jXgTbCX+Kaxjt1ghedQ/RpN"}], "size": 37751}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.7_1719848929914_0.5070251211008052"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-01T15:48:50.109Z", "publish_time": 1719848930109, "_source_registry_name": "default"}, "2.4.0-alpha.8": {"name": "@volar/typescript", "version": "2.4.0-alpha.8", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.8", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.8"}, "gitHead": "2b5644585168133d5a5c5144e15e1ee52b265d0b", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.8", "dist": {"integrity": "sha512-tyhApg+G8z+VsNSgYN1+9W/Wdr1HEnG97bFoAQjtjoxvegBbmlBfFrESxSpUpUdsH26bJld+N79KoZZ1igiheA==", "shasum": "678e37e114c9d9362b6aa63df50b817cb6cc9013", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.8.tgz", "fileCount": 42, "unpackedSize": 200056, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARUespd0WQOvfun8pni0Z2o8yH7fXVQ8vA6EXquZSiRAiAsbfWQBeQcToMD6AlU2pwp/fXn4jCyPbnnETNsuZQcJQ=="}], "size": 37748}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.8_1719872339678_0.990792458289373"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-01T22:18:59.883Z", "publish_time": 1719872339883, "_source_registry_name": "default"}, "2.4.0-alpha.9": {"name": "@volar/typescript", "version": "2.4.0-alpha.9", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.9", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.9"}, "gitHead": "4a3fc1bf36eb12ccd58bc8cb25f11f1a9f7b2300", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.9", "dist": {"integrity": "sha512-CyXDpIkLQK+mco0CuwpQ5JD7lrFZe8OnyK1NQqyXKlpB8kjIANJJFTMNY1p29bvN8958F+1Cx7DVFgDNREr++A==", "shasum": "6413d112424c2a8c2ddef2d997f67f83e588fd77", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.9.tgz", "fileCount": 42, "unpackedSize": 200024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHykVpDHESuR8yHmovRsaP1Sy8aSLPieuEyzTjnyjEmMAiBrhMhQmSlGthKviWczYD2s6L/bkTDvzdPjve4eF82NPA=="}], "size": 37732}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.9_1719877188010_0.31729750303319837"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-01T23:39:48.169Z", "publish_time": 1719877188169, "_source_registry_name": "default"}, "2.4.0-alpha.10": {"name": "@volar/typescript", "version": "2.4.0-alpha.10", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.10", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.10"}, "gitHead": "9f0cb9f2e41167baeb503e8f0eca88e9bea06269", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.10", "dist": {"integrity": "sha512-64TIrrrbV1UQ7PlaHYBdFjJkscKEzPFUO1oblEvyvP9SJ7IWY61UqNIpqIanxQLojKDvpL7WOZm6vqnpO/2f+w==", "shasum": "b957ba4c4895284fed6b39d1d9cd7eeb55194851", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.10.tgz", "fileCount": 42, "unpackedSize": 200027, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcHXSJpY6VbLnTQ1OF+KX3dS5b5TDOj3sWJn5IHK3hnAiEAsirSpOMctjI68TFXExsD2gfoOIv0WnH8KdSrrmI4aSc="}], "size": 37733}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.10_1719880460051_0.916242765030818"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T00:34:20.310Z", "publish_time": 1719880460310, "_source_registry_name": "default"}, "2.4.0-alpha.11": {"name": "@volar/typescript", "version": "2.4.0-alpha.11", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.11", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.11"}, "gitHead": "f4520e175d25305fc930982a8188da94104625a6", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.11", "dist": {"integrity": "sha512-N/v+wSddhtsNtfv2w0Bxj2QQWURN5budGzpyBTrlcXxz2dnvB0eAMqrEQbBi6rCOVHlRaXbh+wyTRdAcB/FHrg==", "shasum": "9925ffed8033da3c3e5e00ef5d53057592172410", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.11.tgz", "fileCount": 42, "unpackedSize": 200078, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWmDljolecnjhvXMwvQ50BBc8/0MC65Hv8Bkx5Lwd9ZAIgdLA7rMoEEjggH8QX34k1TACkVai+0HHw5J5wtU+j8vs="}], "size": 37741}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.11_1719882667860_0.7474444810224381"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T01:11:08.039Z", "publish_time": 1719882668039, "_source_registry_name": "default"}, "2.4.0-alpha.12": {"name": "@volar/typescript", "version": "2.4.0-alpha.12", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.12", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.12"}, "gitHead": "9418f0c81cef5ca13b21bf43a49bef67c6cb2993", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.12", "dist": {"integrity": "sha512-mLg+OQauMTv/+08a7WBWJo1sev/wc8t2is0zhBZIlFU+j5mG89FM4+4089c2p/zoUFZ400Q/VNg2BPfhpZ8wSA==", "shasum": "702d6966b6b3e4c46333879fac95a73c827bf4b0", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.12.tgz", "fileCount": 42, "unpackedSize": 200078, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFTb9IQhNfGsW3Y5Fh5h/FqLJMNeAjb1+m4yYq8Qf5dHAiAVpwKnNxuVNwaHaUjoir5Gkct446ybqWkutkwJTfogRA=="}], "size": 37742}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.12_1719896098676_0.09722676744947756"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-02T04:54:58.908Z", "publish_time": 1719896098908, "_source_registry_name": "default"}, "2.4.0-alpha.13": {"name": "@volar/typescript", "version": "2.4.0-alpha.13", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.13", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.13"}, "gitHead": "5f6d5d8ec51a5dda702d071c283e9133254cae97", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.13", "dist": {"integrity": "sha512-zW/MOPA9SwkCuuVPqADDYfAEPAh68aJQG3/EAqDYozSuK2YNYHEAC0BWYZESSNZC6jxwwx7w0U82fBkDZ9hHEw==", "shasum": "960eaa0f188ea7ffc5597319df2347725e40ce79", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.13.tgz", "fileCount": 42, "unpackedSize": 200766, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDflGAhnCdv26khPli7vBaVMroNM+9K4aJC5N6d0dHEKAIgIEsFdeX1neoob9Y54KJRF0PFJQZ9sCwHtFNtMlpoaBA="}], "size": 37850}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.13_1720028273926_0.07897471028391201"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-03T17:37:54.240Z", "publish_time": 1720028274240, "_source_registry_name": "default"}, "2.4.0-alpha.14": {"name": "@volar/typescript", "version": "2.4.0-alpha.14", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.14", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.14"}, "gitHead": "fc61afbf59f54fd6e92776a8569d6ce591e9b9d5", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.14", "dist": {"integrity": "sha512-FQtQruOc7qQwcq5Q666pxF6ekRqZG5ILL3sS40Oac1V69QdAZ7q+IOQ2+z6SHJDENY49ygBv0hN9HrxRLtk15Q==", "shasum": "70618cedcf764d140439cc0b9675be43b927ada6", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.14.tgz", "fileCount": 42, "unpackedSize": 201825, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNSkTzmrluT4sS3Q5XKbC+B3VVhu07Z+xDfxG954kYxgIgJGlJavrDgyAAaMGfHPX9wHXt1PPs3IAZ3VUVPUDNjfU="}], "size": 38074}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.14_1720073460933_0.2609841014063772"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-04T06:11:01.142Z", "publish_time": 1720073461142, "_source_registry_name": "default"}, "2.4.0-alpha.15": {"name": "@volar/typescript", "version": "2.4.0-alpha.15", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.15", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.15"}, "gitHead": "01867e962d6147c93ed4386d2a5c66bd08d2ef53", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.15", "dist": {"integrity": "sha512-U3StRBbDuxV6Woa4hvGS4kz3XcOzrWUKgFdEFN+ba1x3eaYg7+ytau8ul05xgA+UNGLXXsKur7fTUhDFyISk0w==", "shasum": "407e3ca2134188ab77a6c5505b9ccccb9465f3c2", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.15.tgz", "fileCount": 42, "unpackedSize": 201825, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMs1AwrWcb7C0vHoBXtGBjeUyZROEFAURFG47/2e0rfgIhAKGyFtQYPtMphgRvvriLeZsqI/sg3BXW5TBQ/9nLrKsQ"}], "size": 38074}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.15_1720088142207_0.29707163089449606"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-04T10:15:42.460Z", "publish_time": 1720088142460, "_source_registry_name": "default"}, "2.4.0-alpha.16": {"name": "@volar/typescript", "version": "2.4.0-alpha.16", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.16", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.16"}, "gitHead": "ad1f693295aa0a7b1a800e0a0d05f414c6fef351", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.6.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.16", "dist": {"integrity": "sha512-WCx7z5O81McCQp2cC0c8081y+MgTiAR2WAiJjVL4tr4Qh4GgqK0lgn3CqAjcKizaK1R5y3wfrUqgIYr+QeFYcw==", "shasum": "bd267e9389207761e9dcda61ace619a8943384e5", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.16.tgz", "fileCount": 42, "unpackedSize": 202250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfG8sgilHyIBcRtK533FIWC/ZtqCV0AxqpEiHewqta9QIhANNZ2Owf8R6c+HaaUNJ9fzkQUTafeUOT0fvzji0fhdl+"}], "size": 38128}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.16_1721045755969_0.28641973629888695"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-15T12:15:56.124Z", "publish_time": 1721045756124, "_source_registry_name": "default"}, "2.4.0-alpha.17": {"name": "@volar/typescript", "version": "2.4.0-alpha.17", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.17", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.17"}, "gitHead": "d0c3954d27f67f9867ea1e7c89bbf7f8365cab7a", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.7.1/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.17", "dist": {"integrity": "sha512-oJlz5xJd0O1Xe/I7AV3kPpV6gXlcyxfpMcj/w4/wGY5AxFHxyy5i7VhaE/BVk99zsT6M2KxcZyUSsL55RlNXlQ==", "shasum": "4c7c0e90e6db024c7792042061ef6141edda44cf", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.17.tgz", "fileCount": 42, "unpackedSize": 201377, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCanmsTo1OcwdS7XTYIwT3Et+uGb3hyRFJdS6yPguG0NAIgatUgW9FEGsR2pPZzbi5pdRI98o9u68mAkV6zm2wXSeQ="}], "size": 38105}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.17_1721412355534_0.11887670577523224"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-19T18:05:55.855Z", "publish_time": 1721412355855, "_source_registry_name": "default"}, "2.4.0-alpha.18": {"name": "@volar/typescript", "version": "2.4.0-alpha.18", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.18", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.18"}, "gitHead": "ea2e22ca17116b4274d661a014cff040c1d206cb", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.7.1/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.18", "dist": {"integrity": "sha512-sXh5Y8sqGUkgxpMWUGvRXggxYHAVxg0Pa1C42lQZuPDrW6vHJPR0VCK8Sr7WJsAW530HuNQT/ZIskmXtxjybMQ==", "shasum": "806aca9ce1bd7c48dc5fcd0fcf7f33bdd04e5b35", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.18.tgz", "fileCount": 42, "unpackedSize": 202279, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXqtJmKcZRZ6gA+M9IQCSNBoJT/AC6h7H7MqWxaAUHvgIgeqc7jt0X0cShBQ8hruXQeouAFukknAt1nC7NTPAdfaE="}], "size": 38202}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.18_1721620311390_0.48841352053210985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-22T03:51:51.543Z", "publish_time": 1721620311543, "_source_registry_name": "default"}, "2.4.0-alpha.19": {"name": "@volar/typescript", "version": "2.4.0-alpha.19", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.19", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.19"}, "gitHead": "cbb14a44f72c365c1e8d52eff9580fb4e9765f15", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.7.1/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.19", "dist": {"integrity": "sha512-Q7tfVVL3HmvUlqjcoTEk/6LiwlDF13XP8avAARovK4Qc6Djsckc+COLovr+ZX6kIxxTdM+Ggv9C0nCNFjMeiSQ==", "shasum": "3c53840b6db3db3d818eac52247a47b2b50417a1", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.19.tgz", "fileCount": 42, "unpackedSize": 203514, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGY6+FETG333pdCf0gYCeJmrpJO4+VOGecKwj2gvLnxlAiBowOBqhwY8m31ByNOVgby4oPMVqQ4GY/xfGpElLktO/g=="}], "size": 38556}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.19_1723977252379_0.8329514515529568"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-18T10:34:12.607Z", "publish_time": 1723977252607, "_source_registry_name": "default"}, "2.4.0": {"name": "@volar/typescript", "version": "2.4.0", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0"}, "gitHead": "7e98885cfe284451e655cf1c3954786b51aea2f8", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.8.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0", "dist": {"integrity": "sha512-9zx3lQWgHmVd+JRRAHUSRiEhe4TlzL7U7e6ulWXOxHH/WNYxzKwCvZD7WYWEZFdw4dHfTD9vUR0yPQO6GilCaQ==", "shasum": "f909d20dfe43dd846d30695f6e5467276ff4418e", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0.tgz", "fileCount": 42, "unpackedSize": 203358, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFTyTscWZ5SPFEewSwMc5DHnDOBoZ8VButrBSnbhkNS4AiAscrEhPfm2GtgHdPv2ZzBeXU0olBNjNIqDdcWj4sn2ew=="}], "size": 38545}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0_1724000207967_0.5970316421070134"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-18T16:56:48.148Z", "publish_time": 1724000208148, "_source_registry_name": "default"}, "2.4.0-alpha.20": {"name": "@volar/typescript", "version": "2.4.0-alpha.20", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.0-alpha.20", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.0-alpha.20"}, "gitHead": "63ec0dfd91333c91a7c6443cedc1ae074b317867", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.7.1/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.0-alpha.20", "dist": {"integrity": "sha512-B7RMZSamv2rC6RXYAmeXCi5quE0m3jD5Imtuy/FRhZgb/4x/40Fti7Dyfdbh7xwCWo2N4cOTgN3s2sEiyCgw4Q==", "shasum": "9608d7f2ee2fd5e008c490759ef7e67d66b7daf0", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.0-alpha.20.tgz", "fileCount": 42, "unpackedSize": 202279, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBPoe1o1fnJieeHeocRdmLCxQ7Y7oMLbKsO3J0rZxLJnAiEAwZyj55RB47IAqNI5W5Sm0spnMIx2UYhYRFUbpVE3A9w="}], "size": 38201}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.0-alpha.20_1724035816818_0.5929027879119675"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-19T02:50:17.023Z", "publish_time": 1724035817023, "_source_registry_name": "default"}, "2.4.1": {"name": "@volar/typescript", "version": "2.4.1", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.1", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.1"}, "gitHead": "b920b6c4a3e4b2d8f46c676ea414e94c437cb5b7", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.8.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.1", "dist": {"integrity": "sha512-UoRzC0PXcwajFQTu8XxKSYNsWNBtVja6Y9gC8eLv7kYm+UEKJCcZ8g7dialsOYA0HKs3Vpg57MeCsawFLC6m9Q==", "shasum": "6285f29b36c58769ccc14153f329d11e89ee13bc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.1.tgz", "fileCount": 42, "unpackedSize": 204320, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCf7lgH5/csGtZTPaigIsaFdDgqo1GhY394XcC+c89UlQIgbAuBM2JSmTY0qhXU8IfjX2dCG2hYfkjUFr8w2mO8gTs="}], "size": 38668}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.1_1724887687902_0.03514905210226282"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-28T23:28:08.092Z", "publish_time": 1724887688092, "_source_registry_name": "default"}, "2.4.2": {"name": "@volar/typescript", "version": "2.4.2", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.2", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.2"}, "gitHead": "f384d55ef4ae21e9f9a6796c9676a4a710458ed9", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.8.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.2", "dist": {"integrity": "sha512-m2uZduhaHO1SZuagi30OsjI/X1gwkaEAC+9wT/nCNAtJ5FqXEkKvUncHmffG7ESDZPlFFUBK4vJ0D9Hfr+f2EA==", "shasum": "4674d9dc9a39434fa65b956cbe1cc2a75d5c9054", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.2.tgz", "fileCount": 42, "unpackedSize": 205091, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLu53FPCQFDnFtrvat75ssQtOmCFENg49LVTYEqsgPegIhAJbCI5E6MUrvE79D//ggs1Iz/4qtjSt6ckcSrTmul/Dx"}], "size": 38840}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.2_1725457566394_0.10041962384170011"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-04T13:46:06.598Z", "publish_time": 1725457566598, "_source_registry_name": "default"}, "2.4.3": {"name": "@volar/typescript", "version": "2.4.3", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.3", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.3"}, "gitHead": "74d897fa2e2b81aa2a71e7c95c869cd8415b50b7", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.3", "dist": {"integrity": "sha512-hhbDbeVFfi3BGI39X6W6oywar7h21hNW4H4wUwX3CU/FYp9oZGZOXU/AYA4g9ig51Y5benYtNp+4x5i9jl60VA==", "shasum": "97731cf4c24ab9eb9499157f4fa2d6b7ee3e1584", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.3.tgz", "fileCount": 42, "unpackedSize": 205091, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFwMgoipxSpfTCgtsySnvAAjy3m8s2JIvOmxc0reRfOAiAXSGtGw0RTyetEx3nCl1EOnOhszgYI24UZkw6PPE4uBA=="}], "size": 38841}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.3_1725739457759_0.7806207383542347"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-07T20:04:17.952Z", "publish_time": 1725739457952, "_source_registry_name": "default"}, "2.4.4": {"name": "@volar/typescript", "version": "2.4.4", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.4", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.4"}, "gitHead": "765adc510da0089e8c24689c716607804ae4cd5c", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.4", "dist": {"integrity": "sha512-QQMQRVj0fVHJ3XdRKiS1LclhG0VBXdFYlyuHRQF/xLk2PuJuHNWP26MDZNvEVCvnyUQuUQhIAfylwY5TGPgc6w==", "shasum": "c6679dc6bc1e86483f1d34f22ed2c2b0100f9838", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.4.tgz", "fileCount": 42, "unpackedSize": 205091, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHJu0SOqviUTETB3jWW19dM9J6kom+RJJTWV/RZawkvAiEA05bbhNgVIPvISLMV8NfJ+bl9CAnDWkLqiC9pmpM7Pao="}], "size": 38839}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.4_1725741224946_0.3232051930030757"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-07T20:33:45.162Z", "publish_time": 1725741225162, "_source_registry_name": "default"}, "2.4.5": {"name": "@volar/typescript", "version": "2.4.5", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.5", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.5"}, "gitHead": "eb9864da6a36535195b906ae33aca3c0f95286a5", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.5", "dist": {"integrity": "sha512-mcT1mHvLljAEtHviVcBuOyAwwMKz1ibXTi5uYtP/pf4XxoAzpdkQ+Br2IC0NPCvLCbjPZmbf3I0udndkfB1CDg==", "shasum": "1210c1e8561ac20af46348ceaf8e6e96c797063f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.5.tgz", "fileCount": 42, "unpackedSize": 205437, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHWBf4537EpyXY6+EGt2KMZjT9RQt+0aFLscR95Mt/irAiB2C387vw1PMYz7FyovUqX+QT2FeNeQK8T+RWLh10wYcA=="}], "size": 38920}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.5_1726300974636_0.9180195123349699"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-14T08:02:54.794Z", "publish_time": 1726300974794, "_source_registry_name": "default"}, "2.4.6": {"name": "@volar/typescript", "version": "2.4.6", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.6", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.6"}, "gitHead": "f4cec29bc563378069be05cf6a92713914c8608b", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.2/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.6", "dist": {"integrity": "sha512-NMIrA7y5OOqddL9VtngPWYmdQU03htNKFtAYidbYfWA0TOhyGVd9tfcP4TsLWQ+RBWDZCbBqsr8xzU0ZOxYTCQ==", "shasum": "6a4611b9fae793ad0d4c66d11d765f2731d93a12", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.6.tgz", "fileCount": 42, "unpackedSize": 205437, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGJHMyhDdXL6fR0571HJhz7gAm2bGHRKYVKrIJHEZ6AjAiBCfo0fxevjFyjxFMl9+h+C3M5N4NTyG84P5Tz1FV2vUw=="}], "size": 38922}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.6_1728279195545_0.065751534788274"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-07T05:33:15.780Z", "publish_time": 1728279195780, "_source_registry_name": "default"}, "2.4.7": {"name": "@volar/typescript", "version": "2.4.7", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.7", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.7"}, "gitHead": "73a48189f9066883c90c6b29040a3301c6dc3195", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.9.2/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.7", "dist": {"integrity": "sha512-sp3mFLmMtXY47S8GrMwFnwjGiW7aVtCLMAwnePRJA4P7CfSkrRj2DjoSxl//0pt+KR7oGG/48T2q413b8TvPbg==", "shasum": "084d3079bcf3945ca8c55791fdaef65217c01226", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.7.tgz", "fileCount": 42, "unpackedSize": 205693, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF5V0WM35Wxvg01IJxUqbUdcFMQaL3MWMu6ndKXBpb+mAiBCUogAQirM1OgPU8Y1QzUGfqSgL3x925fn8r8VaBqllg=="}], "size": 38974}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.7_1729851993903_0.0022693009218111015"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-25T10:26:34.113Z", "publish_time": 1729851994113, "_source_registry_name": "default"}, "2.4.8": {"name": "@volar/typescript", "version": "2.4.8", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.8", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.8"}, "gitHead": "bfa90aec50b975189f574b47affb619b9e1d679d", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.10.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.8", "dist": {"integrity": "sha512-6xkIYJ5xxghVBhVywMoPMidDDAFT1OoQeXwa27HSgJ6AiIKRe61RXLoik+14Z7r0JvnblXVsjsRLmCr42SGzqg==", "shasum": "4cfb90b3226e04d781d48fa519fed0838d7b1504", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.8.tgz", "fileCount": 42, "unpackedSize": 206497, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDReYF0P9giHmQe0K9F5G8dXzGdmC80BhN1zcllNgXpAAiBAhOyuwNA6On1gbLDm7k+peCrHXQXyjST57YTMKrH5Bw=="}], "size": 39055}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.8_1729942706331_0.7289123602688556"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-26T11:38:26.534Z", "publish_time": 1729942706534, "_source_registry_name": "default"}, "2.4.9": {"name": "@volar/typescript", "version": "2.4.9", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.9", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.9"}, "gitHead": "798b36aa660a3831dcc79a5fb7c024f64d4748db", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.10.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.9", "dist": {"integrity": "sha512-Zmh3Bq8CFD6OANKYsi4vs/l7togwfjFH0kgrT12uAsDff2AJQjbEUKTVUnxmHbnbH2B9ja7Lb6Mu/Wj9wBuJlg==", "shasum": "6e27f268bdfda65cdceb2c0402d10c87d131dd4d", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.9.tgz", "fileCount": 42, "unpackedSize": 206899, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICK8PEEUI6IjuIrUIFsEedsx6/vPZ1PjAPfwMg3+B7zDAiEAodmKSVYIDjGHl0Y7FPINuUgmKYO1Do4EuiqqV2/erFo="}], "size": 39112}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.9_1730928295769_0.13756175762107747"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-06T21:24:55.940Z", "publish_time": 1730928295940, "_source_registry_name": "default"}, "2.4.10": {"name": "@volar/typescript", "version": "2.4.10", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.10", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.10"}, "gitHead": "03d1e8b07e1e64921b76b635c7064d7b4fcf63b5", "_nodeVersion": "20.14.0", "_npmVersion": "lerna/3.10.0/node@v20.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.10", "dist": {"integrity": "sha512-F8ZtBMhSXyYKuBfGpYwqA5rsONnOwAVvjyE7KPYJ7wgZqo2roASqNWUnianOomJX5u1cxeRooHV59N0PhvEOgw==", "shasum": "5d0d6476c98a3e0820731f5be36e859d48ed135f", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.10.tgz", "fileCount": 42, "unpackedSize": 207022, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/EpRsRCmZ3uhKqvkRqtL4p/wW4wl/A7WZC7ShOiNYqQIgbgl7IdnrzSpJAWGLHSFs9c1xIGW7hP3lFiBUZwGvzFg="}], "size": 39127}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typescript_2.4.10_1731046568676_0.40661219755806965"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-08T06:16:08.884Z", "publish_time": 1731046568884, "_source_registry_name": "default"}, "2.4.11": {"name": "@volar/typescript", "version": "2.4.11", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.11", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.11"}, "gitHead": "42ccae005cc8516e07ad38f4d7730cab9b723340", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/3.10.1/node@v22.11.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.11", "dist": {"integrity": "sha512-2DT+Tdh88Spp5PyPbqhyoYavYCPDsqbHLFwcUI9K1NlY1YgUJvujGdrqUp0zWxnW7KWNTr3xSpMuv2WnaTKDAw==", "shasum": "aafbfa413337654db211bf4d8fb6670c89f6fa57", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.11.tgz", "fileCount": 42, "unpackedSize": 209547, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuPDH54ln/fegoQaKu66gXbNJBq5fBjdsxKehDxmxe2QIhAO+jt998gAj6JfO6IAK3KkzZyqjTuSDRJzNpuHViB8gS"}], "size": 39534}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.11_1734134575578_0.13536754590015465"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-14T00:02:55.827Z", "publish_time": 1734134575827, "_source_registry_name": "default"}, "2.4.12": {"name": "@volar/typescript", "version": "2.4.12", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.12", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.12"}, "gitHead": "17b9b8a1f522afd1aad1e598d2fd935680d8a8d7", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/3.11.0/node@v22.11.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.12", "dist": {"integrity": "sha512-HJB73OTJDgPc80K30wxi3if4fSsZZAOScbj2fcicMuOPoOkcf9NNAINb33o+DzhBdF9xTKC1gnPmIRDous5S0g==", "shasum": "8c638c23cab89ab131cdcd2d6f2a51768caaa015", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.12.tgz", "fileCount": 44, "unpackedSize": 213481, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCX1gI5j41ebtsT5iUBsw0wp7XC3SQbw9VNnhQMffl22wIhAIOXvm78dd2stwOYnZmM0BqbRzGpnECgXN0+uIl3Dhle"}], "size": 42122}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.12_1741289608519_0.3744081112234239"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-06T19:33:28.739Z", "publish_time": 1741289608739, "_source_registry_name": "default"}, "2.4.13": {"name": "@volar/typescript", "version": "2.4.13", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.13", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.13"}, "gitHead": "c5d727b9e73d0c6a45c64b38d74ca5c52363818f", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/3.12.1/node@v22.11.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.13", "dist": {"integrity": "sha512-Ukz4xv84swJPupZeoFsQoeJEOm7U9pqsEnaGGgt5ni3SCTa22m8oJP5Nng3Wed7Uw5RBELdLxxORX8YhJPyOgQ==", "shasum": "1f5b6f1aa1e68a800298ba217c297cbc5318f8ae", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.13.tgz", "fileCount": 44, "unpackedSize": 213015, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA+iiFPBcYfI+Z62Rv9rlEwZ0b64JhbYEad8pAGLqyGCAiEA2y6FhQyiBt9NDyKpx/BO241FxAdfpI3DKp/ObHYZ1ds="}], "size": 42106}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.13_1745598023871_0.8840937847614276"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-25T16:20:24.046Z", "publish_time": 1745598024046, "_source_registry_name": "default"}, "2.4.14": {"name": "@volar/typescript", "version": "2.4.14", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.14", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.14"}, "gitHead": "e8280897a7bc0e0366f657b6aefd4230ad5ee027", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.0.0/node@v22.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.14", "dist": {"integrity": "sha512-p8Z6f/bZM3/HyCdRNFZOEEzts51uV8WHeN8Tnfnm2EBv6FDB2TQLzfVx7aJvnl8ofKAOnS64B2O8bImBFaauRw==", "shasum": "b99a1025dd6a8b751e96627ebcb0739ceed0e5f1", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.14.tgz", "fileCount": 44, "unpackedSize": 213015, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICApgy2SlKg/sBDR74Kv8UeFHpWptFlfcg4P6Pm66A7VAiEAyid2Bw/cVhxL+FztogFzEGy66JL58rllFzHx5kGSfQA="}], "size": 42106}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.14_1747388976954_0.9116375053201129"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-16T09:49:37.150Z", "publish_time": 1747388977150, "_source_registry_name": "default"}, "2.4.15": {"name": "@volar/typescript", "version": "2.4.15", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.15", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.15"}, "gitHead": "adcdcbc801b284cce3239fec231c7f4cebfd8688", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.15", "dist": {"integrity": "sha512-2aZ8i0cqPGjXb4BhkMsPYDkkuc2ZQ6yOpqwAuNwUoncELqoy5fRgOQtLR9gB0g902iS0NAkvpIzs27geVyVdPg==", "shasum": "1445d23f8e4f9ad821b6bfa58cf4a2b980dc5f97", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.15.tgz", "fileCount": 44, "unpackedSize": 211748, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC8+QYXLJgw787/0e/vgzBiZkxes6EZCVlF44d7NeFpTQIhAPtUZ+V/Ummm9wLSEvK1ii1E16vm1DqFfwq5GmExGOB2"}], "size": 41813}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>", "actor": {"name": "johnsoncodehk", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.15_1750756087697_0.8484857619871295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-24T09:08:07.877Z", "publish_time": 1750756087877, "_source_registry_name": "default"}, "2.4.16": {"name": "@volar/typescript", "version": "2.4.16", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.16", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.16"}, "gitHead": "e0233d53110b4d59ecf6f40844bf3aa53b155653", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.1.2/node@v22.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.16", "dist": {"integrity": "sha512-CrRuG20euPerYc4H0kvDWSSLTBo6qgSI1/0BjXL9ogjm5j6l0gIffvNzEvfmVjr8TAuoMPD0NxuEkteIapfZQQ==", "shasum": "b46ef6287744eee96a5b7cffc48843449db83fbc", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.16.tgz", "fileCount": 44, "unpackedSize": 211873, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDlTmAeITPNZ7r1F/DVLSkhuln+0OKADcjTQnnXvfcQ6gIgceS53zCzrTCWtR7emMHVkw0t1p3/z+vcH6a6jMPReuE="}], "size": 41827}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>", "actor": {"name": "johnsoncodehk", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.16_1751365003309_0.13211164556576538"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T10:16:43.492Z", "publish_time": 1751365003492, "_source_registry_name": "default"}, "2.4.17": {"name": "@volar/typescript", "version": "2.4.17", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.17", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.17"}, "gitHead": "5a80a17afd76ee4788263043cf758280ad87d567", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.5.1/node@v22.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.17", "dist": {"integrity": "sha512-3paEFNh4P5DkgNUB2YkTRrfUekN4brAXxd3Ow1syMqdIPtCZHbUy4AW99S5RO/7mzyTWPMdDSo3mqTpB/LPObQ==", "shasum": "be252f650ff83e18363db50ed5a42cfbc230ae00", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.17.tgz", "fileCount": 44, "unpackedSize": 212115, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA7XCLqH9W2HU6wK8zTKE778mDh0M2HiW2TsDGhsHgdsAiEAwOF6rFFRWm8ZJDx6oRBJb3Pra7gGjyRggvilPnlkR38="}], "size": 41905}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>", "actor": {"name": "johnsoncodehk", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.17_1751439437849_0.6003197591456926"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T06:57:18.036Z", "publish_time": 1751439438036, "_source_registry_name": "default"}, "2.4.18": {"name": "@volar/typescript", "version": "2.4.18", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "dependencies": {"@volar/language-core": "2.4.18", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/node": "latest", "@types/path-browserify": "latest", "@volar/language-service": "2.4.18"}, "gitHead": "2093bc370bcea22576f54ad2ab453b00fd1948e3", "_nodeVersion": "22.14.0", "_npmVersion": "lerna/4.5.1/node@v22.14.0+arm64 (darwin)", "_id": "@volar/typescript@2.4.18", "dist": {"integrity": "sha512-xcbsMG8m/yhvO1VIKnTtc+llZxw3YtWkZiV7/F1qNpTORdPExkZRcBxJ5d19MXLpkeiQ+DG5JURHh1SV0bcWRA==", "shasum": "f51c466e6b7eaee833cd44f5b24e3b584f404044", "tarball": "https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.18.tgz", "fileCount": 44, "unpackedSize": 212115, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDponff1/zzKopn9gSWCMTKWH4jGsefm3U2wMhWCqGOtwIhAItQEUA4ufQ3ND2DZ2+wXqrc0kbiMPZsi0QHxwmLmd8m"}], "size": 41904}, "_npmUser": {"name": "johnsoncodehk", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/typescript_2.4.18_1752054273239_0.7807612176106022"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-09T09:44:33.414Z", "publish_time": 1752054273414, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/volarjs/volar.js/issues"}, "homepage": "https://github.com/volarjs/volar.js#readme", "license": "MIT", "maintainers": [{"name": "johnsoncodehk", "email": "<EMAIL>"}], "readme": "", "repository": {"type": "git", "url": "https://github.com/volarjs/volar.js.git", "directory": "packages/typescript"}, "_source_registry_name": "default"}