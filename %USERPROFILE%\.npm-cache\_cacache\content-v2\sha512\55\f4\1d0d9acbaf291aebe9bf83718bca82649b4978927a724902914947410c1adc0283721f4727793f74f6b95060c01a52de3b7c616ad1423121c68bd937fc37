{"_attachments": {}, "_id": "@babel/plugin-transform-typescript", "_rev": "306921-61f1d720ccf50598880d53d6", "author": "The Babel Team (https://babel.dev/team)", "description": "Transform TypeScript into ES.next", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.28.0", "next": "8.0.0-beta.1"}, "license": "MIT", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "name": "@babel/plugin-transform-typescript", "readme": "# @babel/plugin-transform-typescript\n\n> Transform TypeScript into ES.next\n\nSee our website [@babel/plugin-transform-typescript](https://babeljs.io/docs/babel-plugin-transform-typescript) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-transform-typescript\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-transform-typescript --dev\n```\n", "time": {"created": "2022-01-26T23:20:00.589Z", "modified": "2025-07-13T16:10:55.301Z", "7.16.1": "2021-10-30T21:33:24.758Z", "7.16.0": "2021-10-29T23:48:01.765Z", "7.15.8": "2021-10-06T20:54:56.854Z", "7.15.4": "2021-09-02T21:40:04.061Z", "7.15.0": "2021-08-04T21:13:15.341Z", "7.14.6": "2021-06-14T21:57:05.622Z", "7.14.5": "2021-06-09T23:13:24.561Z", "7.14.4": "2021-05-28T17:00:09.592Z", "7.14.3": "2021-05-17T20:44:40.571Z", "7.13.0": "2021-02-22T22:50:34.596Z", "7.12.17": "2021-02-18T15:13:37.859Z", "7.12.16": "2021-02-11T22:47:14.000Z", "7.12.13": "2021-02-03T01:12:19.686Z", "7.12.1": "2020-10-15T22:42:12.732Z", "7.12.0": "2020-10-14T20:03:32.057Z", "7.11.0": "2020-07-30T21:28:16.000Z", "7.10.5": "2020-07-14T18:18:22.105Z", "7.10.4": "2020-06-30T13:13:35.610Z", "7.10.3": "2020-06-19T20:54:45.727Z", "7.10.1": "2020-05-27T22:08:34.374Z", "7.10.0": "2020-05-26T21:43:52.886Z", "7.9.6": "2020-04-29T18:38:31.792Z", "7.9.4": "2020-03-24T08:31:18.114Z", "7.9.0": "2020-03-20T15:39:29.961Z", "7.8.7": "2020-03-05T01:56:01.232Z", "7.8.3": "2020-01-13T21:42:38.827Z", "7.8.0": "2020-01-12T00:17:37.051Z", "7.7.4": "2019-11-22T23:33:58.850Z", "7.7.2": "2019-11-06T23:27:22.779Z", "7.7.0": "2019-11-05T10:54:08.626Z", "7.6.3": "2019-10-08T19:49:31.565Z", "7.6.0": "2019-09-06T17:33:49.675Z", "7.5.5": "2019-07-17T21:21:58.087Z", "7.5.2": "2019-07-08T19:51:15.212Z", "7.5.1": "2019-07-06T08:12:48.299Z", "7.5.0": "2019-07-04T12:58:09.334Z", "7.4.5": "2019-05-21T17:45:40.469Z", "7.4.4": "2019-04-26T21:04:08.795Z", "7.4.0": "2019-03-19T20:44:36.676Z", "7.3.2": "2019-02-04T22:23:05.433Z", "7.2.0": "2018-12-03T19:02:52.250Z", "7.1.0": "2018-09-17T19:30:17.648Z", "7.0.0": "2018-08-27T21:44:16.920Z", "7.0.0-rc.4": "2018-08-27T16:45:30.934Z", "7.0.0-rc.3": "2018-08-24T18:09:00.724Z", "7.0.0-rc.2": "2018-08-21T19:25:18.598Z", "7.0.0-rc.1": "2018-08-09T20:09:11.305Z", "7.0.0-rc.0": "2018-08-09T15:59:24.204Z", "7.0.0-beta.56": "2018-08-04T01:07:37.766Z", "7.0.0-beta.55": "2018-07-28T22:07:43.817Z", "7.0.0-beta.54": "2018-07-16T18:00:20.482Z", "7.0.0-beta.53": "2018-07-11T13:40:38.254Z", "7.0.0-beta.52": "2018-07-06T00:59:39.061Z", "7.0.0-beta.51": "2018-06-12T21:20:22.598Z", "7.0.0-beta.50": "2018-06-12T19:47:45.967Z", "7.0.0-beta.49": "2018-05-25T16:03:38.142Z", "7.0.0-beta.48": "2018-05-24T19:24:08.186Z", "7.0.0-beta.47": "2018-05-15T00:10:10.771Z", "7.0.0-beta.46": "2018-04-23T04:32:13.197Z", "7.0.0-beta.45": "2018-04-23T01:57:56.667Z", "7.0.0-beta.44": "2018-04-02T22:20:25.269Z", "7.0.0-beta.43": "2018-04-02T16:48:44.774Z", "7.0.0-beta.42": "2018-03-15T20:51:49.833Z", "7.0.0-beta.41": "2018-03-14T16:26:36.105Z", "7.0.0-beta.40": "2018-02-12T16:42:13.659Z", "7.0.0-beta.39": "2018-01-30T20:27:53.105Z", "7.0.0-beta.38": "2018-01-17T16:32:23.320Z", "7.0.0-beta.37": "2018-01-08T16:02:56.332Z", "7.0.0-beta.36": "2017-12-25T19:05:16.511Z", "7.0.0-beta.35": "2017-12-14T21:48:11.817Z", "7.0.0-beta.34": "2017-12-02T14:39:54.004Z", "7.0.0-beta.33": "2017-12-01T14:28:53.914Z", "7.0.0-beta.32": "2017-11-12T13:33:45.286Z", "7.0.0-beta.31": "2017-11-03T20:03:59.445Z", "7.0.0-beta.5": "2017-10-30T20:57:07.413Z", "7.0.0-beta.4": "2017-10-30T18:35:30.306Z", "7.16.7": "2021-12-31T00:23:17.031Z", "7.16.8": "2022-01-10T21:18:26.487Z", "7.17.12": "2022-05-16T19:33:12.967Z", "7.18.0": "2022-05-19T18:16:37.819Z", "7.18.1": "2022-05-19T20:37:55.207Z", "7.18.4": "2022-05-29T21:50:12.522Z", "7.18.6": "2022-06-27T19:50:52.152Z", "7.18.8": "2022-07-08T09:32:35.430Z", "7.18.10": "2022-08-01T18:46:42.075Z", "7.18.12": "2022-08-05T13:42:58.560Z", "7.19.0": "2022-09-05T19:02:22.216Z", "7.19.1": "2022-09-14T15:29:11.715Z", "7.19.3": "2022-09-27T18:36:45.344Z", "7.20.0": "2022-10-27T13:19:19.779Z", "7.20.2": "2022-11-04T18:51:08.792Z", "7.20.7": "2022-12-22T09:45:43.688Z", "7.20.13": "2023-01-21T14:30:40.366Z", "7.21.0": "2023-02-20T15:31:20.727Z", "7.21.3": "2023-03-14T14:59:35.340Z", "7.21.4-esm": "2023-04-04T14:10:03.292Z", "7.21.4-esm.1": "2023-04-04T14:22:00.841Z", "7.21.4-esm.2": "2023-04-04T14:40:06.396Z", "7.21.4-esm.3": "2023-04-04T14:56:48.042Z", "7.21.4-esm.4": "2023-04-04T15:13:56.199Z", "7.22.0": "2023-05-26T13:45:50.768Z", "7.22.3": "2023-05-27T10:11:01.030Z", "7.22.5": "2023-06-08T18:21:50.445Z", "7.22.9": "2023-07-12T16:54:00.162Z", "8.0.0-alpha.0": "2023-07-20T14:00:33.319Z", "8.0.0-alpha.1": "2023-07-24T17:53:06.149Z", "7.22.10": "2023-08-07T17:25:22.072Z", "8.0.0-alpha.2": "2023-08-09T15:15:28.258Z", "7.22.11": "2023-08-24T13:08:47.592Z", "7.22.15": "2023-09-04T12:25:23.953Z", "8.0.0-alpha.3": "2023-09-26T14:57:40.754Z", "8.0.0-alpha.4": "2023-10-12T02:06:54.445Z", "7.23.3": "2023-11-09T07:04:14.508Z", "7.23.4": "2023-11-20T14:22:10.018Z", "7.23.5": "2023-11-29T10:25:45.054Z", "7.23.6": "2023-12-11T13:10:03.028Z", "8.0.0-alpha.5": "2023-12-11T15:19:48.726Z", "8.0.0-alpha.6": "2024-01-26T16:14:53.909Z", "8.0.0-alpha.7": "2024-02-28T14:05:51.690Z", "7.24.1": "2024-03-19T09:49:36.095Z", "7.24.4": "2024-04-03T16:53:53.011Z", "8.0.0-alpha.8": "2024-04-04T13:20:24.206Z", "7.24.5": "2024-04-29T18:34:28.378Z", "7.24.6": "2024-05-24T12:25:14.651Z", "8.0.0-alpha.9": "2024-06-03T14:05:51.895Z", "8.0.0-alpha.10": "2024-06-04T11:20:48.221Z", "7.24.7": "2024-06-05T13:16:00.310Z", "8.0.0-alpha.11": "2024-06-07T09:16:10.991Z", "7.24.8": "2024-07-11T14:54:59.088Z", "7.25.0": "2024-07-26T16:59:34.331Z", "8.0.0-alpha.12": "2024-07-26T17:34:06.611Z", "7.25.2": "2024-07-30T02:54:49.632Z", "7.25.7": "2024-10-02T15:15:37.159Z", "7.25.9": "2024-10-22T15:21:52.326Z", "8.0.0-alpha.13": "2024-10-25T13:54:52.771Z", "7.26.3": "2024-12-04T12:35:36.046Z", "8.0.0-alpha.14": "2024-12-06T16:54:41.721Z", "7.26.5": "2025-01-10T17:11:52.301Z", "8.0.0-alpha.15": "2025-01-10T17:25:11.789Z", "7.26.7": "2025-01-24T15:04:53.286Z", "7.26.8": "2025-02-08T09:59:23.351Z", "8.0.0-alpha.16": "2025-02-14T11:59:45.849Z", "8.0.0-alpha.17": "2025-03-11T18:25:40.145Z", "7.27.0": "2025-03-24T17:41:55.397Z", "7.27.1": "2025-04-30T15:09:35.173Z", "8.0.0-beta.0": "2025-05-30T15:51:51.816Z", "7.28.0": "2025-07-02T08:38:17.918Z", "8.0.0-beta.1": "2025-07-02T09:04:53.464Z"}, "versions": {"7.16.1": {"name": "@babel/plugin-transform-typescript", "version": "7.16.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.16.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/types": "^7.16.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.16.1", "dist": {"shasum": "cc0670b2822b0338355bc1b3d2246a42b8166409", "size": 8070, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.1.tgz", "integrity": "sha512-NO4XoryBng06jjw/qWEU2LhcLJr1tWkhpMam/H4eas/CDKMX/b2/Ylb6EI256Y7+FVPCawwSM1rrJNOpDiz+Lg=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.16.1_1635629604628_0.5229451135406407"}, "_hasShrinkwrap": false, "publish_time": 1635629604758, "_cnpm_publish_time": 1635629604758, "_cnpmcore_publish_time": "2021-12-14T05:37:36.385Z"}, "7.16.0": {"name": "@babel/plugin-transform-typescript", "version": "7.16.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.15.8", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/traverse": "7.15.4", "@babel/types": "7.15.6"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.15.8", "dist": {"shasum": "ff0e6a47de9b2d58652123ab5a879b2ff20665d8", "size": 7916, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.8.tgz", "integrity": "sha512-ZXIkJpbaf6/EsmjeTbiJN/yMxWPFWvlr7sEG1P95Xb4S4IBcrf2n7s/fItIhsAmOf8oSh3VJPDppO6ExfAfKRQ=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.15.8_1633553696739_0.7018489207836631"}, "_hasShrinkwrap": false, "publish_time": 1633553696854, "_cnpm_publish_time": 1633553696854, "_cnpmcore_publish_time": "2021-12-14T05:37:36.742Z", "deprecated": "[WARNING] Use 7.15.8 instead of 7.16.0, reason: binding may be undefined, cause: Cannot read property 'path' of undefined"}, "7.15.8": {"name": "@babel/plugin-transform-typescript", "version": "7.15.8", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.15.8", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/traverse": "7.15.4", "@babel/types": "7.15.6"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.15.8", "dist": {"shasum": "ff0e6a47de9b2d58652123ab5a879b2ff20665d8", "size": 7916, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.8.tgz", "integrity": "sha512-ZXIkJpbaf6/EsmjeTbiJN/yMxWPFWvlr7sEG1P95Xb4S4IBcrf2n7s/fItIhsAmOf8oSh3VJPDppO6ExfAfKRQ=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.15.8_1633553696739_0.7018489207836631"}, "_hasShrinkwrap": false, "publish_time": 1633553696854, "_cnpm_publish_time": 1633553696854, "_cnpmcore_publish_time": "2021-12-14T05:37:36.742Z"}, "7.15.4": {"name": "@babel/plugin-transform-typescript", "version": "7.15.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/traverse": "7.15.4", "@babel/types": "7.15.4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.15.4", "dist": {"shasum": "db7a062dcf8be5fc096bc0eeb40a13fbfa1fa251", "size": 7899, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.4.tgz", "integrity": "sha512-sM1/FEjwYjXvMwu1PJStH11kJ154zd/lpY56NQJ5qH2D0mabMv1CAy/kdvS9RP4Xgfj9fBBA3JiSLdDHgXdzOA=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.15.4_1630618803883_0.18169114685535792"}, "_hasShrinkwrap": false, "publish_time": 1630618804061, "_cnpm_publish_time": 1630618804061, "_cnpmcore_publish_time": "2021-12-14T05:37:36.954Z"}, "7.15.0": {"name": "@babel/plugin-transform-typescript", "version": "7.15.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.15.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.15.0", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/traverse": "7.15.0", "@babel/types": "7.15.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.15.0", "dist": {"shasum": "553f230b9d5385018716586fc48db10dd228eb7e", "size": 7785, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.0.tgz", "integrity": "sha512-WIIEazmngMEEHDaPTx0IZY48SaAmjVWe3TRSX7cmJXn0bEv9midFzAjxiruOWYIVf5iQ10vFx7ASDpgEO08L5w=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.15.0_1628111595222_0.22210284864747654"}, "_hasShrinkwrap": false, "publish_time": 1628111595341, "_cnpm_publish_time": 1628111595341, "_cnpmcore_publish_time": "2021-12-14T05:37:37.146Z"}, "7.14.6": {"name": "@babel/plugin-transform-typescript", "version": "7.14.6", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.14.6", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.6", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/traverse": "7.14.5", "@babel/types": "7.14.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.14.6", "dist": {"shasum": "6e9c2d98da2507ebe0a883b100cde3c7279df36c", "size": 7172, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.6.tgz", "integrity": "sha512-XlTdBq7Awr4FYIzqhmYY80WN0V0azF74DMPyFqVHBvf81ZUgc4X7ZOpx6O8eLDK6iM5cCQzeyJw0ynTaefixRA=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.14.6_1623707825426_0.11653203407297785"}, "_hasShrinkwrap": false, "publish_time": 1623707825622, "_cnpm_publish_time": 1623707825622, "_cnpmcore_publish_time": "2021-12-14T05:37:37.363Z"}, "7.14.5": {"name": "@babel/plugin-transform-typescript", "version": "7.14.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-typescript": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/traverse": "7.14.5", "@babel/types": "7.14.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.14.5", "dist": {"shasum": "5b41b59072f765bd1ec1d0b694e08c7df0f6f8a0", "size": 7168, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.5.tgz", "integrity": "sha512-cFD5PKp4b8/KkwQ7h71FdPXFvz1RgwTFF9akRZwFldb9G0AHf7CgoPx96c4Q/ZVjh6V81tqQwW5YiHws16OzPg=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.14.5_1623280404397_0.29876592107987343"}, "_hasShrinkwrap": false, "publish_time": 1623280404561, "_cnpm_publish_time": 1623280404561, "_cnpmcore_publish_time": "2021-12-14T05:37:37.555Z"}, "7.14.4": {"name": "@babel/plugin-transform-typescript", "version": "7.14.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.14.4", "@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.3", "@babel/helper-plugin-test-runner": "7.13.10", "@babel/traverse": "7.14.2", "@babel/types": "7.14.4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "_id": "@babel/plugin-transform-typescript@7.14.4", "dist": {"shasum": "1c48829fa6d5f2de646060cd08abb6cda4b521a7", "size": 7146, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.4.tgz", "integrity": "sha512-WYdcGNEO7mCCZ2XzRlxwGj3PgeAr50ifkofOUC/+IN/GzKLB+biDPVBUAQN2C/dVZTvEXCp80kfQ1FFZPrwykQ=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.14.4_1622221209455_0.7714506985210463"}, "_hasShrinkwrap": false, "publish_time": 1622221209592, "_cnpm_publish_time": 1622221209592, "_cnpmcore_publish_time": "2021-12-14T05:37:37.751Z"}, "7.14.3": {"name": "@babel/plugin-transform-typescript", "version": "7.14.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.14.3", "@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.3", "@babel/helper-plugin-test-runner": "7.13.10", "@babel/traverse": "7.14.2", "@babel/types": "7.14.2"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "_id": "@babel/plugin-transform-typescript@7.14.3", "dist": {"shasum": "44f67f725a60cccee33d9d6fee5e4f338258f34f", "size": 6956, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.14.3.tgz", "integrity": "sha512-G5Bb5pY6tJRTC4ag1visSgiDoGgJ1u1fMUgmc2ijLkcIdzP83Q1qyZX4ggFQ/SkR+PNOatkaYC+nKcTlpsX4ag=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.14.3_1621284280428_0.9127471420297733"}, "_hasShrinkwrap": false, "publish_time": 1621284280571, "_cnpm_publish_time": 1621284280571, "_cnpmcore_publish_time": "2021-12-14T05:37:37.961Z"}, "7.13.0": {"name": "@babel/plugin-transform-typescript", "version": "7.13.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.13.0", "@babel/helper-plugin-utils": "^7.13.0", "@babel/plugin-syntax-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "_id": "@babel/plugin-transform-typescript@7.13.0", "dist": {"shasum": "4a498e1f3600342d2a9e61f60131018f55774853", "size": 7015, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.13.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>uzaU8R8dbVuW2Q2Y8Nznf7hnjM7+DSCd14Lo5fF63C9qNLbwZYbmZrtV9/ySpSUpkRpQXvJb6xyu4hCQ=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.13.0_1614034234473_0.5432341991043497"}, "_hasShrinkwrap": false, "publish_time": 1614034234596, "_cnpm_publish_time": 1614034234596, "_cnpmcore_publish_time": "2021-12-14T05:37:38.130Z"}, "7.12.17": {"name": "@babel/plugin-transform-typescript", "version": "7.12.17", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.17", "@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "_id": "@babel/plugin-transform-typescript@7.12.17", "dist": {"shasum": "4aa6a5041888dd2e5d316ec39212b0cf855211bb", "size": 7013, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.17.tgz", "integrity": "sha512-1bIYwnhRoetxkFonuZRtDZPFEjl1l5r+3ITkxLC3mlMaFja+GQFo94b/WHEPjqWLU9Bc+W4oFZbvCGe9eYMu1g=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.12.17_1613661217710_0.9756007300651028"}, "_hasShrinkwrap": false, "publish_time": 1613661217859, "_cnpm_publish_time": 1613661217859, "_cnpmcore_publish_time": "2021-12-14T05:37:38.324Z"}, "7.12.16": {"name": "@babel/plugin-transform-typescript", "version": "7.12.16", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.16", "@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "_id": "@babel/plugin-transform-typescript@7.12.16", "dist": {"shasum": "3f30b829bdd15683f71c32fa31330c2af8c1b732", "size": 6266, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.16.tgz", "integrity": "sha512-88hep+B6dtDOiEqtRzwHp2TYO+CN8nbAV3eh5OpBGPsedug9J6y1JwLKzXRIGGQZDC8NlpxpQMIIxcfIW96Wgw=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.12.16_1613083633834_0.14386968878557926"}, "_hasShrinkwrap": false, "publish_time": 1613083634000, "_cnpm_publish_time": 1613083634000, "_cnpmcore_publish_time": "2021-12-14T05:37:38.520Z"}, "7.12.13": {"name": "@babel/plugin-transform-typescript", "version": "7.12.13", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.13", "@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-syntax-typescript": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "_id": "@babel/plugin-transform-typescript@7.12.13", "dist": {"shasum": "8bcb5dd79cb8bba690d6920e19992d9228dfed48", "size": 6799, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.13.tgz", "integrity": "sha512-z1VWskPJxK9tfxoYvePWvzSJC+4pxXr8ArmRm5ofqgi+mwpKg6lvtomkIngBYMJVnKhsFYVysCQLDn//v2RHcg=="}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.12.13_1612314739561_0.18574161988503035"}, "_hasShrinkwrap": false, "publish_time": 1612314739686, "_cnpm_publish_time": 1612314739686, "_cnpmcore_publish_time": "2021-12-14T05:37:38.739Z"}, "7.12.1": {"name": "@babel/plugin-transform-typescript", "version": "7.12.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "_id": "@babel/plugin-transform-typescript@7.12.1", "dist": {"shasum": "d92cc0af504d510e26a754a7dbc2e5c8cd9c7ab4", "size": 6771, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.1.tgz", "integrity": "sha512-VrsBByqAIntM+EYMqSm59SiMEf7qkmI9dqMt6RbD/wlwueWmYcI0FFK5Fj47pP6DRZm+3teXjosKlwcZJ5lIMw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.12.1_1602801732565_0.6796065215548808"}, "_hasShrinkwrap": false, "publish_time": 1602801732732, "_cnpm_publish_time": 1602801732732, "_cnpmcore_publish_time": "2021-12-14T05:37:38.939Z"}, "7.12.0": {"name": "@babel/plugin-transform-typescript", "version": "7.12.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.0", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "_id": "@babel/plugin-transform-typescript@7.12.0", "dist": {"shasum": "bd6422833a56e4268d8d599238f0b3c5e170078a", "size": 6765, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.12.0.tgz", "integrity": "sha512-gahRNAWgE76hjI3TZPVEfV7vGjOCJi5ACd4eSoAItk/ErC114i2UHnk+1ScS2dOour0p6J6kB99hNFX2vzL2Ww=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.12.0_1602705811930_0.6499995343769973"}, "_hasShrinkwrap": false, "publish_time": 1602705812057, "_cnpm_publish_time": 1602705812057, "_cnpmcore_publish_time": "2021-12-14T05:37:39.155Z"}, "7.11.0": {"name": "@babel/plugin-transform-typescript", "version": "7.11.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.5", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.11.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-transform-typescript@7.11.0", "_nodeVersion": "14.6.0", "_npmVersion": "6.14.7", "dist": {"shasum": "2b4879676af37342ebb278216dd090ac67f13abb", "size": 6658, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.11.0.tgz", "integrity": "sha512-edJsNzTtvb3MaXQwj8403B7mZoGu9ElDJQZOKjGUnvilquxBA3IQoEIOvkX/1O8xfAsnHS/oQhe2w/IXrr+w0w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.11.0_1596144495854_0.4208001134846491"}, "_hasShrinkwrap": false, "publish_time": 1596144496000, "_cnpm_publish_time": 1596144496000, "_cnpmcore_publish_time": "2021-12-14T05:37:39.363Z"}, "7.10.5": {"name": "@babel/plugin-transform-typescript", "version": "7.10.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.5", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-transform-typescript@7.10.5", "_nodeVersion": "14.5.0", "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "dist": {"shasum": "edf353944e979f40d8ff9fe4e9975d0a465037c5", "size": 6690, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.5.tgz", "integrity": "sha512-YCyYsFrrRMZ3qR7wRwtSSJovPG5vGyG4ZdcSAivGwTfoasMp3VOB/AKhohu3dFtmB4cCDcsndCSxGtrdliCsZQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.10.5_1594750701997_0.7398040033985804"}, "_hasShrinkwrap": false, "publish_time": 1594750702105, "_cnpm_publish_time": 1594750702105, "_cnpmcore_publish_time": "2021-12-14T05:37:39.587Z"}, "7.10.4": {"name": "@babel/plugin-transform-typescript", "version": "7.10.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-transform-typescript@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"shasum": "8b01cb8d77f795422277cc3fcf45af72bc68ba78", "size": 6662, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.4.tgz", "integrity": "sha512-3WpXIKDJl/MHoAN0fNkSr7iHdUMHZoppXjf2HJ9/ed5Xht5wNIsXllJXdityKOxeA3Z8heYRb1D3p2H5rfCdPw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.10.4_1593522815454_0.9465910506596309"}, "_hasShrinkwrap": false, "publish_time": 1593522815610, "_cnpm_publish_time": 1593522815610, "_cnpmcore_publish_time": "2021-12-14T05:37:39.812Z"}, "7.10.3": {"name": "@babel/plugin-transform-typescript", "version": "7.10.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.3", "@babel/helper-plugin-utils": "^7.10.3", "@babel/plugin-syntax-typescript": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-transform-typescript@7.10.3", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"shasum": "b3b35fb34ef0bd628b4b8329b0e5f985369201d4", "size": 6664, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.3.tgz", "integrity": "sha512-qU9Lu7oQyh3PGMQncNjQm8RWkzw6LqsWZQlZPQMgrGt6s3YiBIaQ+3CQV/FA/icGS5XlSWZGwo/l8ErTyelS0Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.10.3_1592600085563_0.27337589940798623"}, "_hasShrinkwrap": false, "publish_time": 1592600085727, "_cnpm_publish_time": 1592600085727, "_cnpmcore_publish_time": "2021-12-14T05:37:40.013Z"}, "7.10.1": {"name": "@babel/plugin-transform-typescript", "version": "7.10.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1", "@babel/plugin-syntax-typescript": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-transform-typescript@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"shasum": "2c54daea231f602468686d9faa76f182a94507a6", "size": 6668, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.1.tgz", "integrity": "sha512-v+QWKlmCnsaimLeqq9vyCsVRMViZG1k2SZTlcZvB+TqyH570Zsij8nvVUZzOASCRiQFUxkLrn9Wg/kH0zgy5OQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.10.1_1590617314293_0.20759241526704764"}, "_hasShrinkwrap": false, "publish_time": 1590617314374, "_cnpm_publish_time": 1590617314374, "_cnpmcore_publish_time": "2021-12-14T05:37:40.217Z"}, "7.10.0": {"name": "@babel/plugin-transform-typescript", "version": "7.10.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.0", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_id": "@babel/plugin-transform-typescript@7.10.0", "_nodeVersion": "14.3.0", "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "dist": {"shasum": "00273cddb1f5321af09db5c096bb865eab137124", "size": 6658, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.10.0.tgz", "integrity": "sha512-BGH4yn+QwYFfzh8ITmChwrcvhLf+jaYBlz+T87CNKTP49SbqrjqTsMqtFijivYWYjcYHvac8II53RYd82vRaAw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.10.0_1590529432780_0.6599002558638154"}, "_hasShrinkwrap": false, "publish_time": 1590529432886, "_cnpm_publish_time": 1590529432886, "_cnpmcore_publish_time": "2021-12-14T05:37:40.397Z"}, "7.9.6": {"name": "@babel/plugin-transform-typescript", "version": "7.9.6", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.9.6", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_id": "@babel/plugin-transform-typescript@7.9.6", "_nodeVersion": "14.0.0", "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "dist": {"shasum": "2248971416a506fc78278fc0c0ea3179224af1e9", "size": 6622, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.9.6.tgz", "integrity": "sha512-8OvsRdvpt3Iesf2qsAn+YdlwAJD7zJ+vhFZmDCa4b8dTp7MmHtKk5FF2mCsGxjZwuwsy/yIIay/nLmxST1ctVQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.9.6_1588185511650_0.111626903151125"}, "_hasShrinkwrap": false, "publish_time": 1588185511792, "_cnpm_publish_time": 1588185511792, "_cnpmcore_publish_time": "2021-12-14T05:37:40.596Z"}, "7.9.4": {"name": "@babel/plugin-transform-typescript", "version": "7.9.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "d3cf5fb5f4b46a1f7e9e33f2d24a466e3ea1e50f", "_id": "@babel/plugin-transform-typescript@7.9.4", "_nodeVersion": "13.11.0", "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "dist": {"shasum": "4bb4dde4f10bbf2d787fce9707fb09b483e33359", "size": 6619, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.9.4.tgz", "integrity": "sha512-yeWeUkKx2auDbSxRe8MusAG+n4m9BFY/v+lPjmQDgOFX5qnySkUY5oXzkp6FwPdsYqnKay6lorXYdC0n3bZO7w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.9.4_1585038677900_0.5675220065554749"}, "_hasShrinkwrap": false, "publish_time": 1585038678114, "_cnpm_publish_time": 1585038678114, "_cnpmcore_publish_time": "2021-12-14T05:37:40.790Z"}, "7.9.0": {"name": "@babel/plugin-transform-typescript", "version": "7.9.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_id": "@babel/plugin-transform-typescript@7.9.0", "_nodeVersion": "13.11.0", "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "dist": {"shasum": "8b52649c81cb7dee117f760952ab46675a258836", "size": 6576, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.9.0.tgz", "integrity": "sha512-GRffJyCu16H3tEhbt9Q4buVFFBqrgS8FzTuhqSxlXNgmqD8aw2xmwtRwrvWXXlw7gHs664uqacsJymHJ9SUE/Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.9.0_1584718764774_0.7774265903444413"}, "_hasShrinkwrap": false, "publish_time": 1584718769961, "_cnpm_publish_time": 1584718769961, "_cnpmcore_publish_time": "2021-12-14T05:37:40.985Z"}, "7.8.7": {"name": "@babel/plugin-transform-typescript", "version": "7.8.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_id": "@babel/plugin-transform-typescript@7.8.7", "_nodeVersion": "13.7.0", "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "dist": {"shasum": "48bccff331108a7b3a28c3a4adc89e036dc3efda", "size": 6525, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.7.tgz", "integrity": "sha512-7O0UsPQVNKqpHeHLpfvOG4uXmlw+MOxYvUv6Otc9uH5SYMIxvF6eBdjkWvC3f9G+VXe0RsNExyAQBeTRug/wqQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.8.7_1583373361122_0.6054933009914758"}, "_hasShrinkwrap": false, "publish_time": 1583373361232, "_cnpm_publish_time": 1583373361232, "_cnpmcore_publish_time": "2021-12-14T05:37:41.180Z"}, "7.8.3": {"name": "@babel/plugin-transform-typescript", "version": "7.8.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-typescript": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-transform-typescript@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"shasum": "be6f01a7ef423be68e65ace1f04fc407e6d88917", "size": 6520, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.3.tgz", "integrity": "sha512-Ebj230AxcrKGZPKIp4g4TdQLrqX95TobLUWKd/CwG7X1XHUH1ZpkpFvXuXqWbtGRWb7uuEWNlrl681wsOArAdQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.8.3_1578951758679_0.49810724990409794"}, "_hasShrinkwrap": false, "publish_time": 1578951758827, "_cnpm_publish_time": 1578951758827, "_cnpmcore_publish_time": "2021-12-14T05:37:41.365Z"}, "7.8.0": {"name": "@babel/plugin-transform-typescript", "version": "7.8.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0", "@babel/plugin-syntax-typescript": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-transform-typescript@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"shasum": "485c9d12f011697e0a762d683897711c3daad9c9", "size": 6531, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.0.tgz", "integrity": "sha512-RhMZnNWcyvX+rM6mk888MaeoVl5pGfmYP3as709n4+0d15SRedz4r+LPRg2a9s4z+t+DM+gy8uz/rmM3Cb8JBw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.8.0_1578788256936_0.6224206586827357"}, "_hasShrinkwrap": false, "publish_time": 1578788257051, "_cnpm_publish_time": 1578788257051, "_cnpmcore_publish_time": "2021-12-14T05:37:41.588Z"}, "7.7.4": {"name": "@babel/plugin-transform-typescript", "version": "7.7.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-transform-typescript@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"shasum": "2974fd05f4e85c695acaf497f432342de9fc0636", "size": 6522, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.4.tgz", "integrity": "sha512-X8e3tcPEKnwwPVG+vP/vSqEShkwODOEeyQGod82qrIuidwIrfnsGn11qPM1jBLF4MqguTXXYzm58d0dY+/wdpg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.7.4_1574465638648_0.32563928923153473"}, "_hasShrinkwrap": false, "publish_time": 1574465638850, "_cnpm_publish_time": 1574465638850, "_cnpmcore_publish_time": "2021-12-14T05:37:41.811Z"}, "7.7.2": {"name": "@babel/plugin-transform-typescript", "version": "7.7.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.7.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_id": "@babel/plugin-transform-typescript@7.7.2", "_nodeVersion": "13.0.1", "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "dist": {"shasum": "eb9f14c516b5d36f4d6f3a9d7badae6d0fc313d4", "size": 6524, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.2.tgz", "integrity": "sha512-UWhDaJRqdPUtdK1s0sKYdoRuqK0NepjZto2UZltvuCgMoMZmdjhgz5hcRokie/3aYEaSz3xvusyoayVaq4PjRg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.7.2_1573082842586_0.33168513914552733"}, "_hasShrinkwrap": false, "publish_time": 1573082842779, "_cnpm_publish_time": 1573082842779, "_cnpmcore_publish_time": "2021-12-14T05:37:42.010Z"}, "7.7.0": {"name": "@babel/plugin-transform-typescript", "version": "7.7.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.7.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_id": "@babel/plugin-transform-typescript@7.7.0", "_nodeVersion": "13.0.1", "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "dist": {"shasum": "182be03fa8bd2ffd0629791a1eaa4373b7589d38", "size": 6505, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.7.0.tgz", "integrity": "sha512-y3KYbcfKe+8ziRXiGhhnGrVysDBo5+aJdB+x8sanM0K41cnmK7Q5vBlQLMbOnW/HPjLG9bg7dLgYDQZZG9T09g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.7.0_1572951248420_0.5096740989664812"}, "_hasShrinkwrap": false, "publish_time": 1572951248626, "_cnpm_publish_time": 1572951248626, "_cnpmcore_publish_time": "2021-12-14T05:37:42.195Z"}, "7.6.3": {"name": "@babel/plugin-transform-typescript", "version": "7.6.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.6.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.6.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_id": "@babel/plugin-transform-typescript@7.6.3", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "dist": {"shasum": "dddb50cf3b8b2ef70b22e5326e9a91f05a1db13b", "size": 6250, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.6.3.tgz", "integrity": "sha512-aiWINBrPMSC3xTXRNM/dfmyYuPNKY/aexYqBgh0HBI5Y+WO5oRAqW/oROYeYHrF4Zw12r9rK4fMk/ZlAmqx/FQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.6.3_1570564171427_0.12907159793464373"}, "_hasShrinkwrap": false, "publish_time": 1570564171565, "_cnpm_publish_time": 1570564171565, "_cnpmcore_publish_time": "2021-12-14T05:37:42.401Z"}, "7.6.0": {"name": "@babel/plugin-transform-typescript", "version": "7.6.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.6.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_id": "@babel/plugin-transform-typescript@7.6.0", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "dist": {"shasum": "48d78405f1aa856ebeea7288a48a19ed8da377a6", "size": 6306, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.6.0.tgz", "integrity": "sha512-yzw7EopOOr6saONZ3KA3lpizKnWRTe+rfBqg4AmQbSow7ik7fqmzrfIqt053osLwLE2AaTqGinLM2tl6+M/uog=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.6.0_1567791229497_0.2774025877211006"}, "_hasShrinkwrap": false, "publish_time": 1567791229675, "_cnpm_publish_time": 1567791229675, "_cnpmcore_publish_time": "2021-12-14T05:37:42.603Z"}, "7.5.5": {"name": "@babel/plugin-transform-typescript", "version": "7.5.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.5.5", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_id": "@babel/plugin-transform-typescript@7.5.5", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "dist": {"shasum": "6d862766f09b2da1cb1f7d505fe2aedab6b7d4b8", "size": 6324, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.5.tgz", "integrity": "sha512-pehKf4m640myZu5B2ZviLaiBlxMCjSZ1qTEO459AXKX5GnPueyulJeCqZFs1nz/Ya2dDzXQ1NxZ/kKNWyD4h6w=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.5.5_1563398517948_0.3157584797423636"}, "_hasShrinkwrap": false, "publish_time": 1563398518087, "_cnpm_publish_time": 1563398518087, "_cnpmcore_publish_time": "2021-12-14T05:37:42.791Z"}, "7.5.2": {"name": "@babel/plugin-transform-typescript", "version": "7.5.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.5.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "0dbf99bedb1d82b8414685181416c19336dd0d96", "_id": "@babel/plugin-transform-typescript@7.5.2", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "dist": {"shasum": "ea7da440d29b8ccdb1bd02e18f6cfdc7ce6c16f5", "size": 6322, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.2.tgz", "integrity": "sha512-r4zJOMbKY5puETm8+cIpaa0RQZG/sSASW1u0pj8qYklcERgVIbxVbP2wyJA7zI1//h7lEagQmXi9IL9iI5rfsA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.5.2_1562615475053_0.17698500094869818"}, "_hasShrinkwrap": false, "publish_time": 1562615475212, "_cnpm_publish_time": 1562615475212, "_cnpmcore_publish_time": "2021-12-14T05:37:43.000Z"}, "7.5.1": {"name": "@babel/plugin-transform-typescript", "version": "7.5.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.5.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "d0519fa9076ad2b50fe0a4d745c863c61c226117", "_id": "@babel/plugin-transform-typescript@7.5.1", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "dist": {"shasum": "6e3d4e62ba2b7b596f9bbfa658fc24ee62461bdd", "size": 5864, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.1.tgz", "integrity": "sha512-dzJ4e/vIFjQOucCu6d+s/ebr+kc8/sAaSscI35X34yEX0gfS6yxY9pKX15U2kumeXe3ScQiTnTQcvRv48bmMtQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.5.1_1562400768165_0.8398476009869804"}, "_hasShrinkwrap": false, "publish_time": 1562400768299, "_cnpm_publish_time": 1562400768299, "_cnpmcore_publish_time": "2021-12-14T05:37:43.223Z"}, "7.5.0": {"name": "@babel/plugin-transform-typescript", "version": "7.5.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.5.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_id": "@babel/plugin-transform-typescript@7.5.0", "_nodeVersion": "12.6.0", "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "dist": {"shasum": "a0855287eec87fe83c11e8dad67d431d343b53b1", "size": 5844, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.5.0.tgz", "integrity": "sha512-z3T4P70XJFUAHzLtEsmJ37BGVDj+55/KX8W8TBSBF0qk0KLazw8xlwVcRHacxNPgprzTdI4QWW+2eS6bTkQbCA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.5.0_1562245089243_0.9118911646645482"}, "_hasShrinkwrap": false, "publish_time": 1562245089334, "_cnpm_publish_time": 1562245089334, "_cnpmcore_publish_time": "2021-12-14T05:37:43.416Z"}, "7.4.5": {"name": "@babel/plugin-transform-typescript", "version": "7.4.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "33ab4f166117e2380de3955a0842985f578b01b8", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.4.5", "dist": {"shasum": "ab3351ba35307b79981993536c93ff8be050ba28", "size": 5164, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.4.5.tgz", "integrity": "sha512-RPB/YeGr4ZrFKNwfuQRlMf2lxoCUaU01MTw39/OFE/RiL8HDjtn68BwEPft1P7JN4akyEmjGWAMNldOV7o9V2g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.4.5_1558460740320_0.11540746616675568"}, "_hasShrinkwrap": false, "publish_time": 1558460740469, "_cnpm_publish_time": 1558460740469, "_cnpmcore_publish_time": "2021-12-14T05:37:43.594Z"}, "7.4.4": {"name": "@babel/plugin-transform-typescript", "version": "7.4.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.4.4", "dist": {"shasum": "93e9c3f2a546e6d3da1e9cc990e30791b807aa9f", "size": 4909, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.4.4.tgz", "integrity": "sha512-rwDvjaMTx09WC0rXGBRlYSSkEHOKRrecY6hEr3SVIPKII8DVWXtapNAfAyMC0dovuO+zYArcAuKeu3q9DNRfzA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.4.4_1556312648680_0.7013568561378145"}, "_hasShrinkwrap": false, "publish_time": 1556312648795, "_cnpm_publish_time": 1556312648795, "_cnpmcore_publish_time": "2021-12-14T05:37:43.793Z"}, "7.4.0": {"name": "@babel/plugin-transform-typescript", "version": "7.4.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.4.0", "dist": {"shasum": "0389ec53a34e80f99f708c4ca311181449a68eb1", "size": 4856, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.4.0.tgz", "integrity": "sha512-U7/+zKnRZg04ggM/Bm+xmu2B/PrwyDQTT/V89FXWYWNMxBDwSx56u6jtk9SEbfLFbZaEI72L+5LPvQjeZgFCrQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.4.0_1553028276556_0.2641007494124108"}, "_hasShrinkwrap": false, "publish_time": 1553028276676, "_cnpm_publish_time": 1553028276676, "_cnpmcore_publish_time": "2021-12-14T05:37:43.997Z"}, "7.3.2": {"name": "@babel/plugin-transform-typescript", "version": "7.3.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "d896ce2b53f64742feeea27dd33ee45934cd041a", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.3.2", "dist": {"shasum": "59a7227163e55738842f043d9e5bd7c040447d96", "size": 4861, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.3.2.tgz", "integrity": "sha512-Pvco0x0ZSCnexJnshMfaibQ5hnK8aUHSvjCQhC1JR8eeg+iBwt0AtCO7gWxJ358zZevuf9wPSO5rv+WJcbHPXQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.3.2_1549318985272_0.49177200681316147"}, "_hasShrinkwrap": false, "publish_time": 1549318985433, "_cnpm_publish_time": 1549318985433, "_cnpmcore_publish_time": "2021-12-14T05:37:44.202Z"}, "7.2.0": {"name": "@babel/plugin-transform-typescript", "version": "7.2.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.2.0", "dist": {"shasum": "bce7c06300434de6a860ae8acf6a442ef74a99d1", "size": 4762, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.2.0.tgz", "integrity": "sha512-EnI7i2/gJ7ZNr2MuyvN2Hu+BHJENlxWte5XygPvfj/MbvtOkWor9zcnHpMMQL2YYaaCcqtIvJUyJ7QVfoGs7ew=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.2.0_1543863772117_0.7869054438384104"}, "_hasShrinkwrap": false, "publish_time": 1543863772250, "_cnpm_publish_time": 1543863772250, "_cnpmcore_publish_time": "2021-12-14T05:37:44.414Z"}, "7.1.0": {"name": "@babel/plugin-transform-typescript", "version": "7.1.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.1.0", "dist": {"shasum": "81e7b4be90e7317cbd04bf1163ebf06b2adee60b", "size": 4758, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.1.0.tgz", "integrity": "sha512-TOTtVeT+fekAesiCHnPz+PSkYSdOSLyLn42DI45nxg6iCdlQY6LIj/tYqpMB0y+YicoTUiYiXqF8rG6SKfhw6Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.1.0_1537212617533_0.0724524912159692"}, "_hasShrinkwrap": false, "publish_time": 1537212617648, "_cnpm_publish_time": 1537212617648, "_cnpmcore_publish_time": "2021-12-14T05:37:44.645Z"}, "7.0.0": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "readmeFilename": "README.md", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.0.0", "dist": {"shasum": "71bf13cae08117ae5dc1caec5b90938d8091a01e", "size": 4612, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0.tgz", "integrity": "sha512-UNhEa+Wt8tq3bfLKJWsuZplYEdwdX2y8Cn1gHeIwnL7OqT6L+NZzVe6VTkR1AKqAhH7z3Krs61TpxgVkp5aHCg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0_1535406256838_0.5286492079671454"}, "_hasShrinkwrap": false, "publish_time": 1535406256920, "_cnpm_publish_time": 1535406256920, "_cnpmcore_publish_time": "2021-12-14T05:37:44.831Z"}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/plugin-syntax-typescript": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.4", "dist": {"shasum": "41fd4ca88a6c7abddccd950412ecf1d5aa186ffa", "size": 4617, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.4.tgz", "integrity": "sha512-mm+cR2UsLtEYGd6p1Vfp1jmfU3HxvpBsKIvSdzhRskFgNi1I6ypdIKIerFc2qrQ4wgGOLuGuH0NKByC/a0iVHg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-rc.4_1535388330868_0.5367072565092343"}, "_hasShrinkwrap": false, "publish_time": 1535388330934, "_cnpm_publish_time": 1535388330934, "_cnpmcore_publish_time": "2021-12-14T05:37:45.037Z"}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/plugin-syntax-typescript": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-transform-typescript@7.0.0-rc.3", "dist": {"shasum": "439d97371a55504031a11ee2f606f5153ac1603d", "size": 4619, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.3.tgz", "integrity": "sha512-fu+Bc5iT4WjAS6tor4GOzcr3JNPva096Bnke60HaWIkzZs6LeVPsUrCgmmEauTJwB62CEV/FIQ/SNKnYajGh/Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-rc.3_1535134140662_0.6431951904808528"}, "_hasShrinkwrap": false, "publish_time": 1535134140724, "_cnpm_publish_time": 1535134140724, "_cnpmcore_publish_time": "2021-12-14T05:37:45.227Z"}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/plugin-syntax-typescript": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "_id": "@babel/plugin-transform-typescript@7.0.0-rc.2", "dist": {"shasum": "dcaec299ab601909c4942dff419a6c5206d54443", "size": 3906, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.2.tgz", "integrity": "sha512-r6N6cdkGAAdxhtC1xJEseVUynBfmnNSYuO61zgsJyO9aZ7t3JB60kIH2PhwSQK1iYOkUWbeUiN8jI8K8yYLr8Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-rc.2_1534879518529_0.0684577082065172"}, "_hasShrinkwrap": false, "publish_time": 1534879518598, "_cnpm_publish_time": 1534879518598, "_cnpmcore_publish_time": "2021-12-14T05:37:45.499Z"}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/plugin-syntax-typescript": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "_id": "@babel/plugin-transform-typescript@7.0.0-rc.1", "dist": {"shasum": "5b9726c6f9ab1f8f0abd1971577da5accd232dbc", "size": 3902, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.1.tgz", "integrity": "sha512-0V1avRnl9mVOCoDXNd3NTPUncc5qgeqL4W6BdHglXjEUrk82MEg0sZJqMRXUfTCp/tmxxt0B7u9GfFg3N0xXAA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-rc.1_1533845351195_0.32295488995198873"}, "_hasShrinkwrap": false, "publish_time": 1533845351305, "_cnpm_publish_time": 1533845351305, "_cnpmcore_publish_time": "2021-12-14T05:37:45.720Z"}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-rc.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/plugin-syntax-typescript": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "_id": "@babel/plugin-transform-typescript@7.0.0-rc.0", "dist": {"shasum": "63e00fba60319e5fa752c3bda2d7257ebbac3f8f", "size": 3907, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-rc.0.tgz", "integrity": "sha512-4vcXrkCgHGV/icfMSxp0O6bhi167vBRx05pSqyoh5cvTQCPfrhhVWcUDoRrw5r217VLsKaLILjM5HhdqzXTiXA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-rc.0_1533830364077_0.24543217404246453"}, "_hasShrinkwrap": false, "publish_time": 1533830364204, "_cnpm_publish_time": 1533830364204, "_cnpmcore_publish_time": "2021-12-14T05:37:45.977Z"}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.56", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/plugin-syntax-typescript": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.56", "dist": {"shasum": "1357852a7ef303ca002b6ed7e4712578148edbeb", "size": 3913, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.56.tgz", "integrity": "sha512-ZVP1um14KoIrB2cYbW/ddmO45EppRX8IiZq6/VHG93N8HKMkexcO2U9LsZ+SMfoJqnhGRiZ3S0VnHbyILYk3Eg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.56_1533344857693_0.7943617589741097"}, "_hasShrinkwrap": false, "publish_time": 1533344857766, "_cnpm_publish_time": 1533344857766, "_cnpmcore_publish_time": "2021-12-14T05:37:46.168Z"}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.55", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/plugin-syntax-typescript": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.55", "dist": {"shasum": "290b5a6dfb7730bc167c57ffeb471267b4182557", "size": 3913, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.55.tgz", "integrity": "sha512-kUrIJIZJbRUT3nQsrZXJ+q9+swVZb5GlqUqrBn1gGa+0KHt9xl8KPyONY30jE61Kcb0VJr3rqXCcQwlvMKVZZQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.55_1532815663672_0.6184685295147816"}, "_hasShrinkwrap": false, "publish_time": 1532815663817, "_cnpm_publish_time": 1532815663817, "_cnpmcore_publish_time": "2021-12-14T05:37:46.420Z"}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.54", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/plugin-syntax-typescript": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.54", "dist": {"shasum": "7b614ba0dbea88b70ae82df9c429e128928c9251", "size": 3896, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.54.tgz", "integrity": "sha512-nwWQ4ptm7UAbiEfEpq6c+80pt9qY2syq0Z7tP+08TDYL87dzpobc3KF0jrFNL0vjHt6MlEek67rtO/L9PQ9vVA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.54_1531764020445_0.18208688379954374"}, "_hasShrinkwrap": false, "publish_time": 1531764020482, "_cnpm_publish_time": 1531764020482, "_cnpmcore_publish_time": "2021-12-14T05:37:46.705Z"}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.53", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/plugin-syntax-typescript": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.53", "dist": {"shasum": "775d2a53e46fd4a7562f314fe674a6ad2f8373c0", "size": 3896, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.53.tgz", "integrity": "sha512-iaxP1/sSjIZ5wok4IUyk+fiJNT+wjIN9eSzSnrzs0/uz/XRwhsv+rXOP6ZQORFk0zUlSdzjwUkCdfvO4ABmPgg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.53_1531316438166_0.9914818223505206"}, "_hasShrinkwrap": false, "publish_time": 1531316438254, "_cnpm_publish_time": 1531316438254, "_cnpmcore_publish_time": "2021-12-14T05:37:46.909Z"}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.52", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/plugin-syntax-typescript": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.52", "dist": {"shasum": "8361c4c002040d4e4b5f38416757c0fe4741a1cb", "size": 3887, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.52.tgz", "integrity": "sha512-8ihVdBfxQX01fHEYpBWJWw2cK0idbf8lpGcLUjKnV7Y58XHUmz4eHGA/4k4DxIBmbQn/uueQjd8A7Udvj+NNkQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.52_1530838778995_0.8259274856536412"}, "_hasShrinkwrap": false, "publish_time": 1530838779061, "_cnpm_publish_time": 1530838779061, "_cnpmcore_publish_time": "2021-12-14T05:37:47.131Z"}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.51", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/plugin-syntax-typescript": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.51", "dist": {"shasum": "1d9e5ba7bf93fe37483cc321dbc9a7ebba5ff35b", "size": 3877, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.51.tgz", "integrity": "sha512-vcQnn6KTxZYecOacMBCNpWoaf3n2sAUmYkT+Ia3nx27xw+zkJS220YXprLdgIvs0H6mHpYD3yDi+V47DIOhRkA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.51_1528838422503_0.05239254011079941"}, "_hasShrinkwrap": false, "publish_time": 1528838422598, "_cnpm_publish_time": 1528838422598, "_cnpmcore_publish_time": "2021-12-14T05:37:47.340Z"}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.50", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/plugin-syntax-typescript": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.50", "dist": {"shasum": "8576afd88144bdddbca8da3a500f5c3a318ac45e", "size": 3863, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.50.tgz", "integrity": "sha512-6fFNpSCTDt2DKNvnK/128FDkq1Eb63UCwglq4HtMRgM6YXpWNGmkdzeZdA5DjQ8pQGo7DQ9mRtqfnGNIw3KnLg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.50_1528832865915_0.07627277677290967"}, "_hasShrinkwrap": false, "publish_time": 1528832865967, "_cnpm_publish_time": 1528832865967, "_cnpmcore_publish_time": "2021-12-14T05:37:47.560Z"}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.49", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/plugin-syntax-typescript": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.49", "scripts": {}, "_shasum": "1f81fb756e033df6c396b2fffc1ba573a97c8a19", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1f81fb756e033df6c396b2fffc1ba573a97c8a19", "size": 4369, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.49.tgz", "integrity": "sha512-IDIzWFU9JnjGZtznPZ+iHDn1IQnl5IG6Umv90yTYBQysP3tPVvfqHrhazid098Xhw6whpjGlWVpn15xqYuNAfA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.49_1527264218086_0.41922773537111424"}, "_hasShrinkwrap": false, "publish_time": 1527264218142, "_cnpm_publish_time": 1527264218142, "_cnpmcore_publish_time": "2021-12-14T05:37:47.731Z"}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.48", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/plugin-syntax-typescript": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "b93f770df8aeac19d3d4f14da0504df5e90777bb", "size": 4296, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.48.tgz", "integrity": "sha512-GnmUKhjMjzWxRtMuXyeUYYR0NHus0cNhlyDJd98GoEqGUm9f/yb6DP4dH1YPdCum9g7IWQdxBUG8L/n/RFvfgw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.48_1527189848051_0.9982673788480472"}, "_hasShrinkwrap": false, "publish_time": 1527189848186, "_cnpm_publish_time": 1527189848186, "_cnpmcore_publish_time": "2021-12-14T05:37:47.955Z"}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.47", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/plugin-syntax-typescript": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "f6f46bd4dd22edef4ae42d9d3770044eddb84dfa", "size": 4685, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.47.tgz", "integrity": "sha512-UcwdRWpLcs+a/Xe8iJXtkMnU8y+wKUA7CDMmUvxHrrCc9UFoy7xv9CLEWdmNLBDze2VHW+joXis0WP7do8PqOw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.47_1526343010643_0.7627107506288682"}, "_hasShrinkwrap": false, "publish_time": 1526343010771, "_cnpm_publish_time": 1526343010771, "_cnpmcore_publish_time": "2021-12-14T05:37:48.151Z"}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.46", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/plugin-syntax-typescript": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bcb57558c87de7ae2f4f3a36322934c799bf3774", "size": 4587, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.46.tgz", "integrity": "sha512-ZGz0fu3marpLquKgZvr9eN6yBdnjPOJmEKBRZ/s5kplrvaIMlYA/1AbWn1zTVCv4NV4V/zg/nQRIXrx2wjSjjA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.46_1524457933120_0.012937106198006587"}, "_hasShrinkwrap": false, "publish_time": 1524457933197, "_cnpm_publish_time": 1524457933197, "_cnpmcore_publish_time": "2021-12-14T05:37:48.346Z"}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.45", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/plugin-syntax-typescript": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "183749e00f1e06f1fa2cf9bf5a0f58a853f4a0a0", "size": 4585, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.45.tgz", "integrity": "sha512-yGaTteFZMPf7tzk1qHIu1ZJKT38V9LM4CV6WpzTC7bIxJP49TJyTX8SI3fRj2I9EC4wev8u3llDutbj49FSPQg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.45_1524448676578_0.8189905785777902"}, "_hasShrinkwrap": false, "publish_time": 1524448676667, "_cnpm_publish_time": 1524448676667, "_cnpmcore_publish_time": "2021-12-14T05:37:48.581Z"}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.44", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-syntax-typescript": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "85f326ccef4a903581098b2cdcd0fddf78c7dd47", "size": 4589, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.44.tgz", "integrity": "sha512-9Y4ONucWed/cNM0xgdfC5AZdvV4gwfmVemNySLM+a7KdgTBQ2ZoSKQUVdXPIGWUJnfpnSpXp56Wzn0POKGXZrA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.44_1522707625197_0.3150456630466969"}, "_hasShrinkwrap": false, "publish_time": 1522707625269, "_cnpm_publish_time": 1522707625269, "_cnpmcore_publish_time": "2021-12-14T05:37:48.810Z"}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.43", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-syntax-typescript": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "1122ef721ef0602de4fb81bc8a85966533b4181a", "size": 4156, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.43.tgz", "integrity": "sha512-uNbnrXNOUnWYNVfw8ZQIdGpWwWSChOn3kCcxnihg67syxiO7DDBZME8nkHaem5+Aos40jx53cNSEnGuKDWN2Lw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.43_1522687724724_0.5314081169682681"}, "_hasShrinkwrap": false, "publish_time": 1522687724774, "_cnpm_publish_time": 1522687724774, "_cnpmcore_publish_time": "2021-12-14T05:37:49.040Z"}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.42", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-syntax-typescript": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "e3a2d46014fd26e0729fd574b521fca4eb21144f", "size": 4522, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.42.tgz", "integrity": "sha512-gPuKyZJlfsoxYE+MSqYPcWgDanJKBu28TMFVcPML6d308tsxtv14pGrx504KkOLcO67yeFIsosBeg8h+JtM51Q=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.42_1521147109776_0.673434875043982"}, "_hasShrinkwrap": false, "publish_time": 1521147109833, "_cnpm_publish_time": 1521147109833, "_cnpmcore_publish_time": "2021-12-14T05:37:49.241Z"}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.41", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-syntax-typescript": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "abb9e900af77aeca7441afccd72634172d48de4d", "size": 4522, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.41.tgz", "integrity": "sha512-XNm8kN8Ka7UE/o5/chZnkEi6fGiHBGohwGl9tkB4NFnuSEvyJMml8AC/yI+BL0T3njJJbA8ZiH2Nmslq1hjeQw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.41_1521044796030_0.8554786039560496"}, "_hasShrinkwrap": false, "publish_time": 1521044796105, "_cnpm_publish_time": 1521044796105, "_cnpmcore_publish_time": "2021-12-14T05:37:49.418Z"}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.40", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "143c5e108bbf20d3e5cedacee035ea6656dc0794", "size": 4128, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.40.tgz", "integrity": "sha512-7MLJ5DU6j8B76yYrfhQvV4LKXOX5AQz4Z+SlZy7tUEiARilztqE1vBymju5FGBNCsWaE27lkHSaSXhtIBCjn6g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.0.0-beta.40_1518453733614_0.15267372944637114"}, "_hasShrinkwrap": false, "publish_time": 1518453733659, "_cnpm_publish_time": 1518453733659, "_cnpmcore_publish_time": "2021-12-14T05:37:49.644Z"}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.39", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "44282884d76056c2f1a05105bc3f3d8cbf1a7780", "size": 4134, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.39.tgz", "integrity": "sha512-cUJl11G2iNVdywj3h87EphCM5x7Wy6dBtXVtopqpSPZv98EyYTY7vpEavMmPaoQ24LjT0guyMAHHWAe2VwPfVA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.39.tgz_1517344073027_0.20317129138857126"}, "directories": {}, "publish_time": 1517344073105, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517344073105, "_cnpmcore_publish_time": "2021-12-14T05:37:49.854Z"}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.38", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "ff08fdcef11dc76915b7194524503b17820bf95c", "size": 3949, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.38.tgz", "integrity": "sha512-kt6rfuHHxfq3WVTMDAVAlOKWlvOMPwlXkp0gBzN2JwZBTiJUVI0c7bChoZfWhDhHZS6FBdAp/fv9lGbGAPp7Ag=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.38.tgz_1516206743265_0.20987737155519426"}, "directories": {}, "publish_time": 1516206743320, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516206743320, "_cnpmcore_publish_time": "2021-12-14T05:37:50.080Z"}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.37", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "9dc4f006376ca64b1eb5f14ceb2e40d290770c38", "size": 3949, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.37.tgz", "integrity": "sha512-S/Iw7m1ZTB7LP7jbPSdnIaWFC8WnBWmLwnR90fcHS3GdUv9ehzE4x2Jc4F6iklKZGs8nHNQKiKufr0FJE73ukQ=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.37.tgz_1515427376258_0.49959438294172287"}, "directories": {}, "publish_time": 1515427376332, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515427376332, "_cnpmcore_publish_time": "2021-12-14T05:37:50.300Z"}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.36", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "ba35f7ef924a2adcb66c8e7a2c22f3d696363977", "size": 3943, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.36.tgz", "integrity": "sha512-pUQAM7p4FAn17GfiAyr1CevO6n+lnLrB72JGEAEdHiGdWbqq8raaFhMF23+hlhPLnt3tX5xOgsMFOGvT77qkfw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.36.tgz_1514228715585_0.7264833939261734"}, "directories": {}, "publish_time": 1514228716511, "_hasShrinkwrap": false, "_cnpm_publish_time": 1514228716511, "_cnpmcore_publish_time": "2021-12-14T05:37:50.516Z"}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.35", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "0602f98d4e0cb291296e1b91e826ab123094e9bf", "size": 3875, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.35.tgz", "integrity": "sha512-GnEqbmT+JeJXCf0TKT7+8jzAflSOGqEgo6e3nXLSaF9Juc5dh11SsYvP2LkFvzwa1+AlL1rKvNWBTt9acZWjEw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.35.tgz_1513288091764_0.6218197874259204"}, "directories": {}, "publish_time": 1513288091817, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513288091817, "_cnpmcore_publish_time": "2021-12-14T05:37:50.714Z"}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.34", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "5589d964cd1f343047339a8a2ccd4f5acc61b5de", "size": 3874, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.34.tgz", "integrity": "sha512-mLk8PKXgbkMitC/3XOL9WVlgUMYndYPWgE7nvvmgrmdJDYB/L1ckobRKNkyTTFq2mAEmEMMdZQ0r7YvBSdGW+g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.34.tgz_1512225593128_0.0017356581520289183"}, "directories": {}, "publish_time": 1512225594004, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512225594004, "_cnpmcore_publish_time": "2021-12-14T05:37:50.969Z"}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.33", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "2b43897d5be048aa33bea479d1623104fb6564e3", "size": 3873, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.33.tgz", "integrity": "sha512-ZqXvLBksZZSvadae2+aRMsq9ffTBLHqOzJGT8/6f1tJHaNjYnITzF9WYQc5kNZMm9DTBgbwHo3d2CwhELfaBcA=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.33.tgz_1512138533834_0.47572432341985404"}, "directories": {}, "publish_time": 1512138533914, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512138533914, "_cnpmcore_publish_time": "2021-12-14T05:37:51.260Z"}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.32", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "f26a27addac7ecebee89dcbd5b058e7a864d0d49", "size": 3875, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.32.tgz", "integrity": "sha512-6xsDzdBrnvdouFVmmZLdUAttI0KNjalgfJd/GBheFoEq1SzEM3v4xZZrWXXfhZgECycltIumCJg1T7mKCl0q8g=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.32.tgz_1510493624463_0.07497515249997377"}, "directories": {}, "publish_time": 1510493625286, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510493625286, "_cnpmcore_publish_time": "2021-12-14T05:37:51.489Z"}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.31", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "ac8f039860be56c77c3ceac78af1a1fd2e945ffb", "size": 3854, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.31.tgz", "integrity": "sha512-rJ39b6IlN7lwfB5tRAKKiUOBBb1OXv6JmcLp7UENLJRf2F3V9W9AnJRbkOGqqRpoaPsCm0bFLB6QHjS6hui8Ig=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.31.tgz_1509739439391_0.4076822097413242"}, "directories": {}, "publish_time": 1509739439445, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509739439445, "_cnpmcore_publish_time": "2021-12-14T05:37:51.670Z"}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "db9e7ee75eaaa1e19d400e3a4829c5d8623a8ce8", "size": 3863, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.5.tgz", "integrity": "sha512-Y0DXzBwNZnvVpYrmCvlpyCEvL93UOkDO4WS5YsFiP5jIc8yIIG16rGCmdBubSF/ZbBjx5e1QPMQIMXgMIl5MGw=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.5.tgz_1509397027347_0.04835393629036844"}, "directories": {}, "publish_time": 1509397027413, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509397027413, "_cnpmcore_publish_time": "2021-12-14T05:37:51.861Z"}, "7.0.0-beta.4": {"name": "@babel/plugin-transform-typescript", "version": "7.0.0-beta.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typescript"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/plugin-syntax-typescript": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "_id": "@babel/plugin-transform-typescript@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"shasum": "5b16524fcd41dba7bfebfa4e6d9231fd691cd758", "size": 3855, "noattachment": false, "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.0.0-beta.4.tgz", "integrity": "sha512-X0/7kxMrV2BTkzl4NnM/6D5RYsaOoU0khLXQ5PtyECVtg19YMuWaefcIujwkksng8LI75JHVsQkJfJ7Pz1gtzg=="}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript-7.0.0-beta.4.tgz_1509388530245_0.33997616870328784"}, "directories": {}, "publish_time": 1509388530306, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509388530306, "_cnpmcore_publish_time": "2021-12-14T05:37:52.087Z"}, "7.16.7": {"name": "@babel/plugin-transform-typescript", "version": "7.16.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/plugin-syntax-typescript": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/types": "^7.16.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.16.7", "dist": {"shasum": "33f8c2c890fbfdc4ef82446e9abb8de8211a3ff3", "integrity": "sha512-Hzx1lvBtOCWuCEwMmYOfpQpO7joFeXLgoPuzZZBtTxXqSqUGUubvFGZv2ygo1tB5Bp9q6PXV3H0E/kf7KM0RLA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.7.tgz", "fileCount": 7, "unpackedSize": 32090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk11CRA9TVsSAnZWagAAUYYP/0VqWbO3GRBVLgFA/mYP\n9lx4r+3CYypF6rWl16l9JwOBrB04R1ZWgy6d1NSnDAKyhyLEl81o3tiU42wf\n01osWBWqElz3v/pBZU4m8lEo++T801fojSno5dRsuPqc5eXFyXgsBvOSsTDI\nf4BBqhxasTNpIbrzYjwBpIbI8WTI6oWEDZmlZumBLxRL2dlp7b/PH7kbvGL8\nn1ri/owlpBp6gveGoRI1g2V00bkdkfjuKXpQTWphsTf/utt8D9fyDGNfQU+N\n0Kzc9FsSwMU7TmVxjzM76GTZN6msYcCrNGHgUHp6bD+Zc6oW1/O78jfTtLov\nrfJ2d2bVH5M8g2gyZ5K6cnsgLWku2gab8snHGMewqKTA8UaMvKcfKol1g5dw\n/r72fW4Jry1XGXA6doEkcvqQYy+FGZAbPv3s+VrqRiqh5ApToYllo/UCJyPi\nm5iqXexUnmDoaH4p4zV72dyuRF4ikdLUvR1w6K/p3yS16xPISILbVkQ4+IYh\nXpvKy9bVLSn6a14Zt3q7zh9ecWIGF/7D/rCagAS66mRURuVq4BOpAbLs1cJ3\nC4Rjj8AecIM1HB+1BXHUyJ2jtRoK7scq3U/DkmGszCp9uxmvma6noRuoRN82\nUHqffES35+f8T3GrVgYedCSZus5UtIjpr4UczpSEB/8MFTjv6xHoT/PAEUiZ\ndMuY\r\n=ePwo\r\n-----END PGP SIGNATURE-----\r\n", "size": 8067}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.16.7_1640910196652_0.3685598352658148"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-31T00:24:01.615Z"}, "7.16.8": {"name": "@babel/plugin-transform-typescript", "version": "7.16.8", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/plugin-syntax-typescript": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/traverse": "^7.16.8", "@babel/types": "^7.16.8"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.16.8", "dist": {"shasum": "591ce9b6b83504903fa9dd3652c357c2ba7a1ee0", "integrity": "sha512-bHdQ9k7YpBDO2d0NVfkj51DpQcvwIzIusJ7mEUaMlbZq3Kt/U47j24inXZHQ5MDiYpCs+oZiwnXyKedE8+q7AQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz", "fileCount": 7, "unpackedSize": 32962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKiCRA9TVsSAnZWagAANHwP/0N1LcdwyEEm5tumGQ94\npgXrMWTfSwHbRC9LnF3suZGApgmOCJsSvsISZtHHoV0pB8tdvaz2g3L0whcZ\nQXxDyV7jceWw+y4f4dDNskOrqjsUSuuoUt2EUcdJS6cno4+48pM4tu1QAGV2\n5SX+T/JiZg2JXo5Zh2rRRXE1GeQKTP8XvHPsSVpoRSzliR9m3/qKL+eql/zm\nT16+rw/Nb3YSX0WYkcHcOjg6/C9ir/d1sdaWIifmxRF6det1tQDgnV4mF3j7\ntWv0DhZWAQIMjD2jnFY8PntWYqVsxJvOhnaq0/Us8H/jEWvS2fvK6l1Md2Jy\ndS2A/sexcuZ1ZEzOhXET3QwkzFXL0daHkiGYSafN49xsnElAkqyUTaFZp7BG\n6XaNo+MQQ/GIHQz8jWnehNGD16S+w50JXXSc5vxx+zdZy4dbEoPHVrxbPTV+\nw7YqLdIHTp95hqHODe9ikc0CIcdNHyXVPUKVerBLI/95awK3Fe/xOzBqgY1O\nlt0fOGMobkZy2SQFe9jF9Ic9o8peJeHtc9/oV3x5MPEYgXrQrJJKep6WMdMl\n4z3ncT3BraL2khbKlWEapCjM+EYBzD5bnctz8lu4qtpWSz93HGRtfwMcZ7zK\nRF1R917bkSSpUsKv6e78q6hReOni6a0f+QflvXM6dZ7NEiwdwe54WWtyEEI2\npXxT\r\n=tXe5\r\n-----END PGP SIGNATURE-----\r\n", "size": 8261}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.16.8_1641849506364_0.35163891822665994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-10T21:18:47.844Z"}, "7.17.12": {"name": "@babel/plugin-transform-typescript", "version": "7.17.12", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.17.12", "@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/traverse": "^7.17.12", "@babel/types": "^7.17.12"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.17.12", "dist": {"shasum": "9654587131bc776ff713218d929fa9a2e98ca16d", "integrity": "sha512-ICbXZqg6hgenjmwciVI/UfqZtExBrZOrS8sLB5mTHGO/j08Io3MmooULBiijWk9JBknjM3CbbtTc/0ZsqLrjXQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.17.12.tgz", "fileCount": 7, "unpackedSize": 32969, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH0EaGM6busBSRyj25RlIfukPjUw8L/fTOHtFU2K1wUWAiEA0CS0z7wfQWJtKxvh+/xAq1hVDQdZd/Y74bIUEs0ODgw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGFA/+NBSN7i6yddKV5CBQWPODaj3po6HmoZymXhAGzq7yVxBLgkwA\r\nF0glsPPjul+le1Iz0j6f3WXQkS/fM1IwMMyiOIqblyyQ1CnTuVMfZWF54tLQ\r\nCeb39BdhJgLyOBilTz4jSuElAOkETvNThnJOLxH07lh9agMhEXnybS2LAy2S\r\nGdA7LyJC2IIp0Gpxv3lxwQdg0UuP1bWsxwSu0VdvcQg1+3xhAgQBfDjmW2R9\r\n/tkrWmfdyp2xtcRzSzR49jU6xuHkhrfykFNWIUZdDSYAHavsLkCpDxUL4qHB\r\nN/zucDZtaekYTHC69enpSBoDTknvYvWbUGCqNB3oZ1rOv6KqWKgyu+P+mMlf\r\nRwK4BwLTVMfMB6ZbWQW3gyMylD8jybPRG3yEPkn4Ypk8lLBZ1Ht1pScUMmmw\r\nxDpNfDJzKHKZyKSivjDUqGm7qbHAQiEw3CiSOExdDdcKjkSesIxP+q6pZUnj\r\ntJB/IfanbQwfUDPzRWR9Xu2G1gzzu6btJ9/7B+D7pvViwQ0QQRKjhS2IAH80\r\ngoNK4PCefunFzsXMSFT38+DXK+oJel3lui1jttOhOYKCVhE58Jpo4zVBOPXh\r\nmNItKsYmkyR0oFAfFwxqKql1wGJ9HpfUi5KMqd3NKhcGjY0Dq9QjcJaXw3Kh\r\n5xEAyuIIEFMmc/a3kwIGEgdDalZ8IiVw7LU=\r\n=hD9/\r\n-----END PGP SIGNATURE-----\r\n", "size": 8262}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.17.12_1652729592795_0.23189991381609754"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-16T20:35:30.768Z"}, "7.18.0": {"name": "@babel/plugin-transform-typescript", "version": "7.18.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/traverse": "^7.18.0", "@babel/types": "^7.18.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.18.0", "dist": {"shasum": "af4ee9fd68db4ca9c7fe3b8ac4ac4eebcf662a8f", "integrity": "sha512-x0DUMnDsoRNnaeWlD1JFviIpbTJUNSDRSctEvF/LIeLMsroJ5+Qa11RnAbZX9dEhnhHOJOle4S15o+/xvM7lKQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.0.tgz", "fileCount": 7, "unpackedSize": 32845, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQoe+Gutw4IsIab0s/eUQWiuQi7ooP4GN+MFC+rZH3vAIgRQe9PkEy5mx+6swqlLtnQFZifX56k4gpJfRSSwPAmhI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqatw//Zl7y4HLz1tYZ4i2JKktAPbrRQNCkxj5un5R7VGhroJe84VWE\r\n0tIiuAQdSdkDDqlOYSvB2Muk3VrYH+NjOta0g8VLcPwoETTn0GiqMpx7I7Gk\r\nVuLcHi1VjSMgftfJgivWSleqwwdxR9sbXfJx0nqEvLLW/Bexez0x62ccPM64\r\nX4pt3BnJOJ6EHfhmuTFAztG9OTwwY80iNHIwKZemXeO8VcxdRe0jF+1eTJF3\r\nT3yq6KWEMqON+1G3+gpXDsdWsXAYrwyfwwh/e6nb8zbiAVVP0stZxp/Tvp4m\r\nrRvYTm0XMXbXVIE/kqb8+wsgHbb5et64o4UR7B2maXQkDMLyKNi9qlZvvaZO\r\ndKc6PoOhu97tCgfXyX4vcP/da/Afd/ZGAJ5AD1eUANJ22iwA/fKL+NUuDzfV\r\nPWIoUEvZnsygMg5Yuayqjyrd2nQJGVuOIagpMRDDJkqa6r5oZlawqqS4W16D\r\nzU9zRFqrX7LSdP2F6ChqjTsbQYBjChyCz4a4wZHdIDXE+Y8qROLroDL0CMIH\r\n4boWc+OGMf1TssG5+eAbocfLC8PhcHzaqH81wE60M7mzyHwlidQk8O0WoioB\r\n519cxFLJZVpwgCgO2C1+gpxrd0LqoFgG12eWcE40R5A785LIazq+ckV4X3Ov\r\n7W6pbErEyydxwhsal1fDinmGKHS9nX/bjgg=\r\n=KNjL\r\n-----END PGP SIGNATURE-----\r\n", "size": 8245}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.0_1652984197695_0.3565612957282738"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-19T18:17:59.516Z"}, "7.18.1": {"name": "@babel/plugin-transform-typescript", "version": "7.18.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/traverse": "^7.18.0", "@babel/types": "^7.18.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.18.1", "dist": {"shasum": "5fd8b86130bad95c4a24377b41ab989a9ccad22d", "integrity": "sha512-F+RJmL479HJmC0KeqqwEGZMg1P7kWArLGbAKfEi9yPthJyMNjF+DjxFF/halfQvq1Q9GFM4TUbYDNV8xe4Ctqg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.1.tgz", "fileCount": 7, "unpackedSize": 32911, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3rdplH9TDL25Wc29q9HSYm3YKnpa7JmmEOggKzS4j+wIgUv7i6v5klycdccsCjDbl/k9F4yGttviZ9unLbpcaXB4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihqqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8FA//dhQWnx48ez/IdlAHE+mwQIWMsMVTsK4DdzzSmopB2T48TID6\r\nrhxiu9cOk1+1+kqFOHaXTgfMP8Lh1J/kNyx2fbR9TtKP4fFCm7AygUG+CC2W\r\nD/hQLjNWaZ7hwlkrHywZ8dR0e23dSIf7hGezfE1tV3pJKMfAfT426FP09+cE\r\n4gEqcvSW5gpz4tZ3TGz0HzpCUpVeC9eLK2zhwwJbPN3B8kg5Ecl6eQeuc3Qz\r\nYSOWZ/NgoEIB/x3Us4nHR7p+YWh9o1D+4Vxym/zG+vm2zHm0oEdPKaiIQuks\r\ngy2f/HJEKNI+ay8yZimCscwhC1gdJbM8ip+ypuYKW0zhQwdlxrKfAU6BMiqz\r\nUqNVgQltFWuRFKJEJGGkA7szjsitXz9ZbNxTMmORsC/GcvBerQS2GP/PYysN\r\nPA1m0ysK8WyxiaLD4rjpxGZZHvPGVQ+cPZG7M41rQygy38kx0PxddX280vuA\r\nc1cq7y0JmTENdi+8DSh7oNae57kRbHZeJV2umr8vngT61P4o3TQ10LVZtQ+1\r\nH6VGTXhUIL8BY57oQhXhkOdlo8nw8hYQ321zPmE/GzoeoeNxKnUxEm2exAwi\r\nOIDNu1+VD9LIIt4RlOSlvaWa4rdWtTwnxWKr2NqcoMen5K4YGXklc7ZEOFi1\r\nfq5xFS05mCUslR2GMZ/msLnRf3lXyuYfZ9A=\r\n=lIXc\r\n-----END PGP SIGNATURE-----\r\n", "size": 8260}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.1_1652992675101_0.06745721189995502"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-19T20:40:10.694Z"}, "7.18.4": {"name": "@babel/plugin-transform-typescript", "version": "7.18.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.0", "@babel/helper-plugin-utils": "^7.17.12", "@babel/plugin-syntax-typescript": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.2", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/traverse": "^7.18.2", "@babel/types": "^7.18.4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-transform-typescript@7.18.4", "dist": {"shasum": "587eaf6a39edb8c06215e550dc939faeadd750bf", "integrity": "sha512-l4vHuSLUajptpHNEOUDEGsnpl9pfRLsN1XUoDQDD/YBuXTM+v37SHGS+c6n4jdcZy96QtuUuSvZYMLSSsjH8Mw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.4.tgz", "fileCount": 7, "unpackedSize": 32909, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAj/Bbq4ap5aNBMTP7X9GZ+8QbDw+MVBFLRQafkOIxHHAiBgWypFNXVAQj8DsCTLmbweBVUnW/UxlM0o8WTmLg12nQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqagg/8D95TYOd2cgzhDXFoc+A+Ya+A7EtGZ9lCg7MV8AnxbrFVJynV\r\nO3/hxSmo+NZGdCbrFDmGAyyhk9907rzJrWhswRoh0L++Y0gYMJg2fZkMdO79\r\n2cQxtsodhsDd6fRYLTug+lm3QKaKC8b290kNkfUXePhN8U0tRbL3BLhNB6kT\r\neBNnG1lgzJWdhMZ0qfG8DP4jXBnS6FHT62X3VaG9p7IKHwfxIQdkxmvFKt49\r\nUi89cDB4+h5qvNVA/TzHHk1VAebfJ00Y1d64tKhF6tXCGq0HhFMoxa9icUU2\r\nxklr0GiUZu1ddyXHO+MgE9Ub5XOkkLEyLvmijYSHjUkea4sf31b0KnHSq6yW\r\nltnKCzWEYITZfZ3BxnadzSqUSJAm2B8QzTuqM/uMYp8AA+fTHssRrwQs5+jR\r\nt1kmj4JJMBHnP8Y+EL7lcSNcML0067vshfpC75A4A0fgJhdch8S45o08gGe6\r\n8z4lO9pcTY+YXDFSfeEZ+MzbKy9NVrX6YnXs7p53J0PcnqlDznflFGcQslxV\r\nYZCc9oCSdVAhdusGCuDx6uy5Ylr3BdHvX1ufv4SBgow8ENMH0X0fS5olbC9R\r\n/LdmwDE5qqlXw9Tfg1Dt2nq4U2dPQtBKHDdQcnd8oVdyZPcsGLI8Ux7ChY/I\r\nD/bx0DATasXl2lcZRd8Z3SOEXy1ohTzBPyE=\r\n=6LKg\r\n-----END PGP SIGNATURE-----\r\n", "size": 8270}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.4_1653861012362_0.631258982257213"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-29T21:50:16.859Z"}, "7.18.6": {"name": "@babel/plugin-transform-typescript", "version": "7.18.6", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/types": "^7.18.6"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.18.6", "dist": {"shasum": "8f4ade1a9cf253e5cf7c7c20173082c2c08a50a7", "integrity": "sha512-ijHNhzIrLj5lQCnI6aaNVRtGVuUZhOXFLRVFs7lLrkXTHip4FKty5oAuQdk4tywG0/WjXmjTfQCWmuzrvFer1w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.6.tgz", "fileCount": 7, "unpackedSize": 33080, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyn/e4kqbYuVjQgfXFSoW/DAPvcyhHYigwDY56oDXmFwIgTG8DtSYN7P7ALZaaEp99wgcQcXDSB/f2ZuIRsqK2Cd0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugocACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSrg//Rf/lYzXqNQCAzoGrP9mRDya/7TMvcl1xhJIFMwft1UZccpbM\r\n5+naYDAWMUE8z2acvNMX+SVVWu+wuKlXSQ2q61jMbobZ76J2B8J/cnlaUDbx\r\nsizKTOZV3l9uRAyPsGIaCYcx4TnnDSyhZ/hpyMC6fduo06ixEcn69yv5qyBy\r\nQIxmF0pgv+LLRyTP/B88jhGt7ocOjldaUa3SMcrer3bLKuwTamA9p2fACea/\r\noUFYH/ds4g9ecPYOcujmdlA8npnydwM8ILy6IrHFWmwmQZIPRwcII/Woy1is\r\nTFbsZnaj92RVuEMGonpX/CPWVRpEm4DrQAYtd3aStRrChDWCqCIhMc1MdP8p\r\ndFmwYBlojO7HBWDOqVcKelDBmhhabi51YZn8dmZpwiYkN+VImqnyucP3k4f0\r\nzQCkJOUT/Tl4pZUVHDUwycbXmGbmJZ1hASobZIHT0MTPrydXVDLu5VZ+2L6p\r\n9SofQl6U+iZDx1A0KZrMf2mE9t+/syZjW3c5uuzwGYG3JVA0kTJ8zJXs0tSe\r\ntxfW2sgjA1O4bXlEkoWFb/m6SMCay0FfjCSTApiAyDdOijmhWkX/NAQM/1dK\r\ns3Uii//6mDV7XT3warzIf3+T1ylp+erCJoi3mk0exgZk9VBh9Yz0bLbwgB8l\r\nFksurcM61+RyjXCVKdqYNFkGz8MAXD8jMk4=\r\n=n1sz\r\n-----END PGP SIGNATURE-----\r\n", "size": 8255}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.6_1656359451974_0.7779778246522508"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-27T19:51:02.166Z"}, "7.18.8": {"name": "@babel/plugin-transform-typescript", "version": "7.18.8", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.18.8", "@babel/types": "^7.18.8"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.18.8", "dist": {"shasum": "303feb7a920e650f2213ef37b36bbf327e6fa5a0", "integrity": "sha512-p2xM8HI83UObjsZGofMV/EdYjamsDm6MoN3hXPYIT0+gxIoopE+B7rPYKAxfrz9K9PK7JafTTjqYC6qipLExYA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.8.tgz", "fileCount": 7, "unpackedSize": 33140, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC1KOpG9A/l4Ln2CcRMdTc6u34GFaq7yMsVLYYh6D/Q1AiEAwkMTUuF+azyEJ+cT+00Shx9Ktd4kq1R58tm3dfVOBtU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/mzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnMA//TpTCa9xKCalHlYkvNENR0tTSOKXZ1enYc7VMKNwBROns5Qx2\r\nO+/5hdZ8umtKW3lRXTCW4DYYbCsQDcQAh5I/uEyaZyknI/M/tAmVXgwPDVtk\r\ntxHsBKo+/IBm2K6KmLbOsJ2CvGwNoRxdWe8+LJftD1lbZwJOJ+IeBQ3JJ1d4\r\n+EGLl6M4zErmEVgKqpGKHLxZrzqrc5aAsdBmHH8hKAB58A9uq3ovFD808wjN\r\ntprv8N1eJXkQyGgpW1SvAbRYv55MhSYUcG2fRRi2bkc0U2lkGgh8N3sv0tTs\r\n27psHfxSYfTpQv7qaGheBrXnMeFmWGcGwYR26KjLGUiQqMa3k7RJq/Esoizn\r\nHaIH2eYBgx7Bh8lQHhJzU1sf4rVLjusXxvNTYXwaBRJA7G6+k0RmTOFDL32D\r\nEEpaqNUhg+wUuBsOJ1O4b60uRbpTke4OYqe+trSP9o80aUxLuD1AzNaT+vOc\r\nctLqiFP3c883z3xkxeCc+2BuuxATjtAMW5ASDKKlWHJVfjuL9nNoVqBa5OjP\r\nu659OvQJJfkj0d2vuDKXSO49DUZIU4EYuqRpnRu7dVA+h4pYqgJtMu1Cija9\r\n3gsxXjJ/uuGRy81bPQ0Y/vS3dabCA/rMkLZVq6flN6YpNFjjLH3d8wMAPnA8\r\njTOH7c7KBxrkn7M87KrJIlkObJi7ztwSAqE=\r\n=wSMu\r\n-----END PGP SIGNATURE-----\r\n", "size": 8270}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.8_1657272755181_0.5724061553891795"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-08T09:39:11.088Z"}, "7.18.10": {"name": "@babel/plugin-transform-typescript", "version": "7.18.10", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.9", "@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.10", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.18.10", "@babel/types": "^7.18.10"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.18.10", "dist": {"shasum": "b23401b32f1f079396bcaed01667a54ebe4f9f85", "integrity": "sha512-j2HQCJuMbi88QftIb5zlRu3c7PU+sXNnscqsrjqegoGiCgXR569pEdben9vly5QHKL2ilYkfnSwu64zsZo/VYQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.10.tgz", "fileCount": 7, "unpackedSize": 33263, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAlinOqBGB2xhqQhltjqoUFL8OvV/BXGJnxXf7uZ7R1TAiBJ5F1wKeFPRWZZraHRMpzBopXEhy1nrKVlQt8okO55Gw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBHw//dYv1O84cAmr/6y5p8gW2QOTlU/9yua8wVeXHt2Rusje95Lqo\r\n3vZ+PWyFZGqxjvH1D9yiv8erOcCOZPmvU/GFcDJgEG31OZC6ZelmFxru9V4Z\r\ntcHjJTLkp5VdOKVP47wTwZYMaxGnYRHdYe4QzR6s6lfXY2C7GCWIv0pnGGnn\r\n2fd0SBrNMu43YmVOIc1GdBAgmzFfTBAYY+mLUpKwHt7Q49bwJTvQmrc9pxZN\r\neeZNpon8/ysRVot26/rokOxbpPOX2eFJ57qd298L4bx4ijShrdB/GggHR9L5\r\nTF55mpEHgAMDtAs0tSWgHn7t/sq2La7AWj+eOt1Fxglhfpj/O/jeHnqOlVOe\r\nk4YwfnjQEw4phbhQlFUBKdsBRzxrGxVZWC5og4S042eZmxXP4lGURX6u/Aoc\r\nNQfk1owDbPx7xNXn/WItVNcFOKQqdSDJ6eBATWGe58kv8p3U9rtXvV+U7yXq\r\njVdndJpRkBRFqrTMnQBGxuugdxJ8xLQ4O6PjlpAaoslNa5pFGSD9gQ6uXs0B\r\n74bOTt+A1s7/INaF7ufyRfl02l+/HN1fYfWqKzMahr2Jcgo0weD3l2FvqDYn\r\neZxTt/mn286qpOFyuZoN7FwVTCG15jC68bQYvs4hD7oOcc5zZKxeCH7epYSZ\r\nvgrYv5ZWqxhZ5U0UIrd/NejmTMP11SZno4I=\r\n=/4r4\r\n-----END PGP SIGNATURE-----\r\n", "size": 8294}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.10_1659379601914_0.122840853900815"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-01T18:46:53.146Z"}, "7.18.12": {"name": "@babel/plugin-transform-typescript", "version": "7.18.12", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.9", "@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.10", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.18.11", "@babel/types": "^7.18.10"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.18.12", "dist": {"shasum": "712e9a71b9e00fde9f8c0238e0cceee86ab2f8fd", "integrity": "sha512-2vjjam0cum0miPkenUbQswKowuxs/NjMwIKEq0zwegRxXk12C9YOF9STXnaUptITOtOJHKHpzvvWYOjbm6tc0w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.18.12.tgz", "fileCount": 7, "unpackedSize": 33210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSp4jcj0zyDegC0lbq0hoSsMiD12Y8lo7zGaP+Dr7k3QIhAKLB+2FwKULknWSwg36C+fdGpQv68ll8cvPSb+0FJWQC"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7R5iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDw/7BYIGE1A1QDauUzMokWwTm08mqNBiG+Audt3zghBvHIs1i+DZ\r\nA5jJmwBQkFtiRqtG/yH4rMYbRQNDfX5rTSfQ/zB8HNMfq7Jl1ojJm7rx9Yx0\r\nL9+iJqbYdnBE+1e1fbTZf4L9ZEXdhNIaQqL++JnpbAStAGSil1KzrVp0htxe\r\nvWOLUNfeTPQVdtj6bnAORdv+21zn1zsBPGhA49nEt3Il7KHvGW0Fx82z3xnN\r\nFePNTFPEP86SAHzmOd30hCDiZitxZy9KtF2/LVeCSkJmmJHx4XyoqVG4vsFW\r\nrC+XRSb7LCmbuiXc/qk6o4Oe8vswOl5uZe762XRJsGKi0DDvEQe7LtrByLgt\r\niNGJh2Oorr9T6ixeH7m3jfHpOXCqCZAciMXfssT+lfqwzuu+2jO2eDsBnmZs\r\nUscaruNuTyf+kYziu5Lx7/OYHv659ZIDxE2X7y2NwctOXV2Pn9hGE5WT9EfO\r\n+mAavfMY+MgJBlmVldQ6x1aWHxgN9/eKDQciJj53toNVkPDgOa4xZjnghIiB\r\nwqUoAPr0zhaWB61XG6g67jqDs6QoiAu6MIQa7CML5FYTPvYyrKjlHBXg7RlD\r\n/GgqmpJUy2MK6Qg+T3TFhbkgpqsIWYBueGKX3Mz8DFUS8Aw11M70JmHM46c9\r\nOKLwsV3IXQR1AiG+w32takOae4M0MKqhdEA=\r\n=n+w2\r\n-----END PGP SIGNATURE-----\r\n", "size": 8291}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.18.12_1659706978402_0.3319068332893156"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-05T13:47:35.275Z"}, "7.19.0": {"name": "@babel/plugin-transform-typescript", "version": "7.19.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.19.0", "@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.19.0", "@babel/types": "^7.19.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.19.0", "dist": {"shasum": "50c3a68ec8efd5e040bde2cd764e8e16bc0cbeaf", "integrity": "sha512-DOOIywxPpkQHXijXv+s9MDAyZcLp12oYRl3CMWZ6u7TjSoCBq/KqHR/nNFR3+i2xqheZxoF0H2XyL7B6xeSRuA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.0.tgz", "fileCount": 11, "unpackedSize": 110793, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhDXStJHz8sup2iW6fbEWaqbZYU930bv+IeLkbmqhXyAiEAmwv6aAz/X+PF8QyTI4mOFX3+bhT8HSsPoryi0ZsVFl0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVLw//bvlUcwwOAqkVudKie8POPU4VhEf3WzvQAUQs221rBtfez88d\r\nnue0SdHojZwAXrHNlKOOK3S8rneDu3MGEsolQEUubuX3fKY6MDYBBdJLCPKD\r\noFXa+fiQ+58zm+6Pi7p4PHOE1tg9tE0vDsa+ad6C3SoLZRV2vfE7RmQdLIsf\r\nFKoxegrze8v1S3FgbQfKE5/liXAsdBxn4aS/WqcdQ1gGQC/EPJxfPjG0jgty\r\nlSj3FuKBwuM+us/Mohzday5hz9ptreiHz6D2MT5iqp2Gxxlm3sZKoB8wM5x1\r\nUiEUbNoWlfb7+l16iuOjCE9rnADSMVGnPUIzBsSa3rfSBcy/aJSA2Ez4oiPi\r\noCn4dvdvJatzqizcHW98ySr8aLvrX4kNrnF98oTQOMRdTRYy23/f+kTcAj0G\r\n6jvUiYyrykayrqAdLt1km0IeKWCibmm83UzzCa2qRKsEx0IX1zpAQ57ECJ5r\r\nBy3v2jFCsx78SuHIE8kas61XdR7/skQNfzoOcZqoSDCnRXvxoG4k+otLahi9\r\neTienQoh2hfbW5TMycVXr8XNEGbhDeUZJjynuXGygaD1IHkJ+8BieDh0OD9L\r\npzHRPWcF3Dk9t2GceT+V82f7+2D3E+uW+uDAX+irsPOSOr01SeHTUf8gcvCX\r\nvRDAJGYeQfHkxRR9K3f43ZiDujjuzfUPS28=\r\n=imDS\r\n-----END PGP SIGNATURE-----\r\n", "size": 27916}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.19.0_1662404542049_0.0507361616190809"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-05T19:03:45.821Z"}, "7.19.1": {"name": "@babel/plugin-transform-typescript", "version": "7.19.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.19.0", "@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.19.1", "@babel/types": "^7.19.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.19.1", "dist": {"shasum": "adcf180a041dcbd29257ad31b0c65d4de531ce8d", "integrity": "sha512-+ILcOU+6mWLlvCwnL920m2Ow3wWx3Wo8n2t5aROQmV55GZt+hOiLvBaa3DNzRjSEHa1aauRs4/YLmkCfFkhhRQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.1.tgz", "fileCount": 11, "unpackedSize": 111375, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCY0ICt/GpL/YzkY9B2mS34b4O1RKodSN2gqmc+TJwPKQIhAJ60A+tqlDUDZCQtZiKs/nB/vmtKJAshmjTg69eSvgSs"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsYhAAhi1VBs4wXXdAk56pxB5o+00XqYYZaW7XfN1GKN+CWzCXotk6\r\nyaPGeUv9TA8B86StTVr3ls99IpEx80lMYiUEPTukd1Rby2kp9DeTQaY99eVG\r\nfyNClovcRVKGLxrQgi3VDJllDB6zHmOD716MAF5sUgML/4kfU+H593zoRc8V\r\nsawGrK4rbeI2Ip+0RVGaZOPfDSVJDGq40L0FuXcaV2xz1k+zm+6Cek7yvVnE\r\nXKvwUWFUtFz9qfg1RK/BIitJSJ6waVup8htviCOAJtjRmRd/rqi06wr+2Ogv\r\nAsmXoaU6dTLTRkFGNil4ksQEgeem+I1yhBCfJgHSRE11gpWJ04syPKcugFYT\r\nmKWkIwN7LGCp6l73hp2zJJLFhNMgCCiZHYlIvioXpQrEgYBj1T8HzshlqGKw\r\nQScHsWb/I7L25PspYssc2y/BqL8Pvs5X4+kAQNbtxiw0ejksRmh2fYNU+ds9\r\npl+aJO+qtHMF87izYxZxzzdlMt/SfI0WGboUVHa2fY4hCth+BXCE9Vg46ljV\r\noExX+R5sWltMbwP383xIEhTgo795R+YiLAmltqcVdFBdituVcil/048v9ecU\r\nc5m0DY0BCDoCil6hOUQrr+zZfXBaFO5AlKgsnyDi7Cckqce5cVA2gPSyHxO/\r\nThM1tsdv1YChnkjwgZV/VbQtL5nqgPZ05ZY=\r\n=quKX\r\n-----END PGP SIGNATURE-----\r\n", "size": 28095}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.19.1_1663169351578_0.5008348114480827"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-14T15:54:59.264Z"}, "7.19.3": {"name": "@babel/plugin-transform-typescript", "version": "7.19.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.19.0", "@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.19.3", "@babel/types": "^7.19.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.19.3", "dist": {"shasum": "4f1db1e0fe278b42ddbc19ec2f6cd2f8262e35d6", "integrity": "sha512-z6fnuK9ve9u/0X0rRvI9MY0xg+DOUaABDYOe+/SQTxtlptaBB/V9JIUxJn6xp3lMBeb9qe8xSFmHU35oZDXD+w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.19.3.tgz", "fileCount": 11, "unpackedSize": 112580, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVLfKFout+XLxZBItRu/ruEw/pNn1Gxk3j4V/VsYXPUwIgUZmTS/Nc4Y2EpDpi4tJO7vdqwmjsdivtBkDrTPL3cog="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0K9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9ng/+KGbPCNuMMdc/46njytoGBWXtT0+igtys0qpnPpW7/JnsHiE2\r\n1N1gAMOU2lJWBmtxp3Lvpq2y+FKIzBMA4l6mwzaBDV0bkhXcNF2Qyd0J+RPB\r\nm10EIlIm+GTF0ffTKzmO2KnGboNXzPGG27LxuitOnIK+ISze8myi47wkiqJ9\r\npLgvycCDxTXetH18pox8LER8Fvi8edpQmcnujVb813NsSKzKjw1tuOtOtaHQ\r\nA73bTlT1BbvTry6U59Mn5sKncm+M61QEgxbIB+mI6qjJ0DtyCGC5ajGbAuGX\r\nsZzBLKQysAt59LcGyS1GKGQrrMrMJgy79OvREQgJKslVQGyjhICfZPVRxHIS\r\n+elEVdzeTfL2XEepvcH5HQJcoPfHiGLjf4g9SHMIjQTP+Hz7jylkp+Zcm8aO\r\n5AE/rfpfKFue2DcEVso+JR8bZD3JzNVq/rQAaOAMNqjsrlmn5XupxQzq1qq1\r\niIyi8gENi8S3FccQ0OsaZMgzMR84CHUQvIVbx4KY1kcUoJOVWIxyC5jEC5AJ\r\nQk2pkCtjB9P7KnzA91+qxC5r8Vi3OpS1z5NEB4ze0Fg7Wfam5g2SAfFOnCzj\r\nKFZVXc6mJQVL+PCmwY7QTuFxZHix4qT3P/ojnDf81hG7ZtLhVUzvBbZS47pA\r\nT1k0Y5X5daVfyVcCb+fYveW482+3BvNNn+c=\r\n=2/MK\r\n-----END PGP SIGNATURE-----\r\n", "size": 28498}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.19.3_1664303805119_0.8105025575037652"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-27T18:39:56.674Z"}, "7.20.0": {"name": "@babel/plugin-transform-typescript", "version": "7.20.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.19.0", "@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-typescript": "^7.20.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.20.0", "dist": {"shasum": "2c7ec62b8bfc21482f3748789ba294a46a375169", "integrity": "sha512-xOAsAFaun3t9hCwZ13Qe7gq423UgMZ6zAgmLxeGGapFqlT/X3L5qT2btjiVLlFn7gWtMaVyceS5VxGAuKbgizw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.0.tgz", "fileCount": 11, "unpackedSize": 113109, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICIs8/INMGo9mQXG/SYYV3WkfrNyGzt/U+o1ZGLPOZlOAiEA3qVL1Z96ZkJA1CqyuASBra2OWry98wBnOPzBWvuNBuQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoL7Q/9HAxHwSdbDAZXwjGw6h4jbkf/xWunjfbEgwq3fP3OzPlFmb3D\r\njoMUhTiWVngB//5OEW+6dhX+VVXboODjyFNdImF4frLKqsy8Vbzp02gT6eUP\r\n0ZxGcTbSXJ81XJQ2IVtr6A/khEfr27c6K6aFvdzmH//JcpH09Hh5b0FAsfcb\r\ndSEQtVNX/a575OIg+eoS343e2RdSJWv5T4S1KeMOUO6+4dKu9P8ropV/USQC\r\nL041C5DrxcVjqBQ3UIaXh9WFsssL+ZRsbTjXblxLpgDXUI4GV6CPu9ZCR/RV\r\nIkXBp24Lzn6HCGQwdPCxbuIows+VA4HlWKQ+MPAnVrsM+Gx/jJVCvT5LCZRE\r\nOsDbMohv0DOPg6P09doDimwcL0PCoG6F9m3YzF4m2KmuZ3wB9Y6rGsPyF+WR\r\nZb4M342K+jXkhCwywSHJywl7nf6pDTl+BOGZ6uCz1W2ozpHrKbXdAfigo80/\r\nATSwaH1FgitTLN13y+jdx3oNyNk4XwITQ1HGeCxBha6rleDOkL3YkbKDLCR3\r\nRwT4MBDIGE6wzqVehMQu/TfJ5JeAMf+esyC3zQXpiMfhts5EaKp1x/UmmNr0\r\nrE3y5+CzmcfvcDykzdPWo15/KSy8ZRwUzTQHkbYvPMgd2nHxN8CiGsWRLky2\r\nX75G7qc8tkfxXtSS20hROCiS4c2lMCFiWTQ=\r\n=BVzW\r\n-----END PGP SIGNATURE-----\r\n", "size": 28655}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.20.0_1666876759592_0.36088295565643946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-27T13:39:16.592Z"}, "7.20.2": {"name": "@babel/plugin-transform-typescript", "version": "7.20.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.20.2", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.20.1", "@babel/types": "^7.20.2"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.20.2", "dist": {"shasum": "91515527b376fc122ba83b13d70b01af8fe98f3f", "integrity": "sha512-jvS+ngBfrnTUBfOQq8NfGnSbF9BrqlR6hjJ2yVxMkmO5nL/cdifNbI30EfjRlN4g5wYWNnMPyj5Sa6R1pbLeag==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.2.tgz", "fileCount": 11, "unpackedSize": 113026, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQ84bDItDePfe/IqluZsPo1G+0JHG4vXnMTHKtdi6jawIhAJdwWY//gZzxC8+jCyKIXTY/hBc1QcGltr8hmQ1a1oPP"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrN0hAAgptIKhrh/fxToVTFPsQFVhNVG8dNSuIXXrBE4ah9gZJAFChK\r\nLGWHX8KCR1LubTnLdWx3O5RCkfNwNPFwiPJW3WS3qSjRj+e30PpAp+Ud5Usl\r\ntuJvZZw8e4wzzbmJKy0IErlNpV4C+vXyebJKMxx9Gy+kfUmRBVkPeMjRptio\r\nYcAQZnV8JdZ1UvkO+fd/td5cQZxc3F851KKExLgOZ6wrhwoNfb8fxkq9lHnr\r\ntxI7o+IrJ4+grI9CQng0TSMmacc/2dSF4y1YFAzwUUIdWrad0KneNSn3w++j\r\nPvJTwY5MC6AUrRSNx7BerHED12b7GXdysiUtuSSCAxpKyfojIr+m7zC5si8t\r\nfAoelAWt78zo3JII9+lpcqlnWWXfHP+zjJZkxBaRUrHk++RbybkKxbsn+iF/\r\n9/uOGRnyHaBXeGgFV7lud5GAjFaqAaqCBtwYEPjA2ROdyBRnaf8ONMLdOOVa\r\nAdWMbzb/gjcfKQvy/yFw1jbpzzZ6kZg4AEALR9vzHIaNTVxgo+zQBaLeERC5\r\nXVE0V+un5pic0KcBv6nhGrTj0PVvENhsDyMlWBN7a1zgZVk89V98iGsdbhXF\r\n4jBn1f45mDe6paDA2anncvis0Cul3BtEpvs+WhH4lkqqoN/1GTjKqTjgO8PO\r\nZLasu2HXMqAeYEriu3G+tdWlA1gp957t2tY=\r\n=9u3z\r\n-----END PGP SIGNATURE-----\r\n", "size": 26777}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.20.2_1667587868678_0.1906536256000595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-04T18:51:49.499Z"}, "7.20.7": {"name": "@babel/plugin-transform-typescript", "version": "7.20.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.20.7", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.20.7", "@babel/types": "^7.20.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.20.7", "dist": {"shasum": "673f49499cd810ae32a1ea5f3f8fab370987e055", "integrity": "sha512-m3wVKEvf6SoszD8pu4NZz3PvfKRCMgk6D6d0Qi9hNnlM5M6CFS92EgF4EiHVLKbU0r/r7ty1hg7NPZwE7WRbYw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.7.tgz", "fileCount": 11, "unpackedSize": 113311, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpV3ycqV8UnpzxW3Si/H45DHENuRnW8lZRfW6PcnlelgIhAMGast/1jD3H1JLneT+H1FjJnnidDs7y05ThdqxlE2SR"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovew//YD3YORs96ouLkXP5Je4PppCY7M8ZWpYH1OTsKv8BF5QRgVGN\r\ndHT10p7lr1j4Kd4TK4Hpu4I7GhYh/9/YLyxOaeo7PWYqn1vWXDULc7rm319Y\r\nFw+ahNYNamqTxRfTx5JhxmCemVzDKRIypnkUDLR0R4XgG2F74wRyXJsXuUBd\r\nWUVl3qGBPzdpaJdKHGUvgGRsiZPjfz3ACOAX1Lc9ZHnkWRoqvqEeavuNt2aW\r\nctK04GVjmW+nbng6sfXdNN3wha3KWHgHGV8rTAbe4jD/IvS0/fcWPd5uT0Zw\r\nWbaRCt70gxKnDSRnpIbl+raLhJdS6IkBcD81yiHCbcsY3qLb0EKy5Gvw3yBz\r\nYgsrdd2qC7RBfiQ70TnC4Jw4ZH7h5ID9kP6dnqvN9GylUuKcvqJlVV0rXFfG\r\nGz0umY8YgEJgtZ5+7mtE3j5n+yhGC/NsOMZeCEu6sb/G1XndOP9JYqe3e7eV\r\n4xMpZwQFnKtJIKbn7o+YsjOYzvwQ0x0WOc/Bggc/DK8sgtL5/Hcbfv3XZn6o\r\nBHdFMk7akUU5im2XJA8BaZL+rJN3voKdoowZDEkAoPXW31pUFsG2g0E9zDT6\r\nM49gRKHLDkzpVVL2TypURURdNaJCykhV0nZEwC49Tq2mrIrTlX5gefa8cHBi\r\nt6pP0lCjJnEXaQiHUF81+2foIA1IPB5gU9M=\r\n=cnD/\r\n-----END PGP SIGNATURE-----\r\n", "size": 26868}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.20.7_1671702343498_0.4275623737428471"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-22T10:03:48.870Z"}, "7.20.13": {"name": "@babel/plugin-transform-typescript", "version": "7.20.13", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.20.12", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.20.13", "@babel/types": "^7.20.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.20.13", "dist": {"shasum": "e3581b356b8694f6ff450211fe6774eaff8d25ab", "integrity": "sha512-O7I/THxarGcDZxkgWKMUrk7NK1/WbHAg3Xx86gqS6x9MTrNL6AwIluuZ96ms4xeDe6AVx6rjHbWHP7x26EPQBA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.20.13.tgz", "fileCount": 11, "unpackedSize": 113465, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFUn+SqGVa++3mn6dU4SMVg2DFaCV8Zso0mS4gKTMX20AiBthnuGt6ZW+i8JwyFse1LX6MWtXrFNtzyOSfvGI+7q3w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy/cQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7nQ/+L8pCzkn7frAUID8VQxe4nzhV7PJ80sBL6jnWKz3DZB/FKc6e\r\nX1AZKK1J68RIcIrR2l3H6nbngze4QzXKkcTEAa0X7Ty2pUOmuRyySoPuPi/M\r\n9ot9zeD1U043S8QTWABsQmnMoHDlR9GMub20ijamcRfsM2Kv09b85zeebOwd\r\ncv0l3dyVTn7UYOYmfcRTNdqlwdUjdIiShDfIiZ4IwWTWSR5HLWC0ebliZ2bg\r\n1OwnN+ZBl83pi5xwd9crHM8ngBwUTFPQHzx1Jx8AYGVSg6g7lI/bjoRDplpb\r\nifftIUP51bCBtUsDMMumVZQnNfnV78hoW7EwRRl/lpxVTWFRiaV9zsPWb8jw\r\nCtIB0rpEKbBHHCz+Y9zPXi8cMQ8h3il9O/ocWL55TLBCOIHqhJEsjwGZzwYe\r\npBopyaz0X+D1uDYnHSH3G5WjqSCNPPcm1yEFi6HsG1cuxi1578R9bnorPVdO\r\nBCMbBjpOWfeQuTwfJyP7o1RSdu+F3+iZJloD+cfGdbBZTw8xFjn33nRgBnHZ\r\nteLy7Sh/MCfBjuhD4heohOKEhaS9Z1LuHyKLtOh+9dOIpy9Cxw0tCIYMI4Is\r\ngnzP4N5IKqGMHRLAaizYLpPGiSLmIND8lsao0SOo7Z+77Ctko90FGiyvZwe4\r\n//S4QcbJc463uqzsj1Kx+CkoV1/iaPeb8ak=\r\n=QeBf\r\n-----END PGP SIGNATURE-----\r\n", "size": 26825}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.20.13_1674311440174_0.5510030747915879"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-21T14:30:40.366Z", "publish_time": 1674311440366}, "7.21.0": {"name": "@babel/plugin-transform-typescript", "version": "7.21.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.21.0", "@babel/types": "^7.21.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.21.0", "dist": {"shasum": "f0956a153679e3b377ae5b7f0143427151e4c848", "integrity": "sha512-xo///XTPp3mDzTtrqXoBlK9eiAYW3wv9JXglcn/u1bi60RW11dEUxIgA8cbnDhutS1zacjMRmAwxE0gMklLnZg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.0.tgz", "fileCount": 11, "unpackedSize": 121076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEzQOBbE6TXxYMktzgmJn5/BsZLKOpm3Xl29IfwJz8F6AiBmZHF5AueJPrd14o4l8y07C93vYKCe+5nyaU2Jl4htlQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohCxAAi/2EEeV2AT0KOXcs2TOl3p7r8mxWquOi617BUJHv70W9+qYG\r\nFVYIcs0MR97ShZhhP+FmW6b+zsiR/5yeqx/7ldBSyMcUVvwgIEzkJe2Ug1h1\r\nF5BKYWjNEh8tv0T+l+q0J8RBjQGVVSCeeBMuwpk8wQDlYBfkoUgo4PBOrtbk\r\nhlA8EPUwsyCd5AhrpNIQaW5+HBbv8wV3q2ld/q0yXsbNw2NF43b+MuStmTb9\r\nGkDFPZLejlfCUvhq6jc8oCdM+IcQJ53X8ZT/X0WWqePlnLenZcfJ4dqxn6Us\r\nZ9qyGaQ2xPVC3g8W5BmW8PBT04HhrzIpa4BUgfFHjcKLbbZrqCj7MLtAkiko\r\n+s7Hh867eewzVrKlUdUr0agqo9OBDPxv5h+3sYXWRlUykV+nkJ807wHS5Rcb\r\n/TTk6wWILgzz1W8dD7zuY3lyH+1ORzLl8gBKe1KxtYUDU6PfMss09+eqymza\r\nqRCpS+x3EnBOCbnszdEeMgdZnJ2hKIomrDg4KY/BgO0FmFCgCOQldjtHVmKf\r\nMrQnNYZyrpO03Dt546DMiZGA0OyoXr3Je9t8+e4KnyNBrYRzP0hCe2bVe5hs\r\n6IaI93W3Jtjdb6NU0W0tHRdFRET4L7HWhgbNBczGURJzkwI1+O7lx0LCHuZt\r\nA0cP6sTX4o85G0qEE8aIXP2J+sHaaMuY7as=\r\n=Jf97\r\n-----END PGP SIGNATURE-----\r\n", "size": 28428}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.0_1676907080549_0.9313094524411993"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-20T15:31:20.727Z", "publish_time": 1676907080727}, "7.21.3": {"name": "@babel/plugin-transform-typescript", "version": "7.21.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.21.3", "@babel/types": "^7.21.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.21.3", "dist": {"shasum": "316c5be579856ea890a57ebc5116c5d064658f2b", "integrity": "sha512-RQxPz6Iqt8T0uw/WsJNReuBpWpBqs/n7mNo18sKLoTbMp+UrEekhH+pKSVC7gWz+DNjo9gryfV8YzCiT45RgMw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.3.tgz", "fileCount": 11, "unpackedSize": 124095, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDp2G5BYTTrWpQVYaSFdMCUQaU5eO9mIfZNRGbbGVn8fAiBLPcM+xvKGGflkiQj3ssQMHq2VW2pevppetEepdFEfng=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIvXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq55g//f/da2QJu7AavNFLK1JztF9rcXEuoW5IN0s9vsfMQSg4ndNwv\r\nySdlBoZA2xV/oswiy0KpKOQ9aQ/dLP0ykEp8gSuDzwRwvBrhzViqeCns8UX6\r\nXR9fzlHb1xitsDFZWqO6xyCLfyRViDtOTqoIGS4PonjIKS2OGMtcg9bP0Vh3\r\nMbxh9eX+EALdWX1gn75E0I7X1QMNx1Y4atAi+gx4JW2JbX0WZsBB2c+KIgbB\r\n8mHezscU2Dww3byJaeTRUiEhpEqzTDvlm4ZhVlRAwLx3COWiWm8ST/L2NQz5\r\nIcRs5f0WnUH3nnT/LPR9JxMR9b5aYF/WB+BTjNeAO0McxHsXEc/GAKBJpvDV\r\ns9pcHDtobFAyITCezVW6+gcEK9R9iLtfPsWuM6q0wMW8PDcFNPVaWdp7oCj6\r\nZN+lzoVON6kOBSBtnJTmpfSWlXWCqdcekhYVLXmwsHeA0FKcjV7MyVF1GyrJ\r\ngsmF6APP4F0Y++XuhnyNU0JqnV6xbZOk4Bf38Ct1s9BVgkoorhgHsEDpn4JA\r\nvvm0YgUziFj8h3KaVmCYshS1YPplw2U8JuYrn73kPdznz4n38sPKs5y/z26e\r\nFD2EAP3obBe9oDWQ3mRrjGWQ0OJ07SDlYzM0Z/JJIBVEOFlQ6X6MOGZsXOdm\r\nGeN40ncx3ZJ+YPzuyBt08586K8CATjExb+s=\r\n=W42o\r\n-----END PGP SIGNATURE-----\r\n", "size": 29388}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.3_1678805975148_0.919250608078888"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-14T14:59:35.340Z", "publish_time": 1678805975340}, "7.21.4-esm": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.21.4-esm", "@babel/helper-create-class-features-plugin": "^7.21.4-esm", "@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/plugin-syntax-typescript": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/types": "^7.21.4-esm"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.21.4-esm", "dist": {"shasum": "f4184330f666978dad1be7f3d341ce19cf7cf3ff", "integrity": "sha512-dqZP5ygdMzWFQwPdCpir6i6Q0IrfSNAJEL5zG4w1xAciLIaK0DiqpxBBIY87iCO5Pj2IOfQghw+RzX05vtgnIQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.tgz", "fileCount": 12, "unpackedSize": 124165, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAYEdEfZnJtTObi0ppfW5lTTkin8GZkQLD35Rh6r4TB1AiAI6qawrSnanEp3xFTkWzfruEfi/2GNnCs8uCWALTyF3g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfdw/+MBZxGmC0QaKVV6rvmhIbuHcVrbDEy3IOGWnjqt1ye8yh8D6o\r\ngI3gY87fBfaIQIBjSzyHmh5P9Zf4PSIOMmEWulFIinnIErqsTPcv5kYuueNS\r\nkM3Do7VKVpbHPsR18+wxTCuGfxRGkh89wcpDtlvfGoyOSfG6CF+qPEQQtz+P\r\nzhwzhd/Ce664sFR2ChifUCN00q8hmQhk5LEnGBU624JQwu4MHksM0Wz/Ma6p\r\nVq89R0xxe6sdI338DSm53zyCL0bxTayS0/FaNO3K98EVzQvqH1MiqgmAofSC\r\nRzMrrWCTZcxFTZeQeaDAQrPPtamTUCh5pwqt0BND+O5aufvyJaGIfKwyCt6H\r\ngxl/qTvE3xVkuEdz8EuSiuAsDuD0jam1MLIovYZNSYPg+BoHWp+fDyAtQeVM\r\ngpys9dCJ0b4wtto4qXNIJ5Vghf0CJBe+sR8vilaUUPqhDu8OYNuqflGztOlc\r\nJBzVgvM9oB31Fl7P4PeRldEzlWLliiWqJnmx9FQ2egnTv80IfJ7OF2xpEeUB\r\nbAYcljDOYOecSIQeI+4lx3xCJTQJY8/2cSMm+N/b4HOIa36AzahtZupFVyfI\r\nBPQ7qTq+T4UTYmEvGFKUKAgSA05MYCw/AdVNjTJVqBYFToSMxXRPJVT2HlvF\r\nk+gtgDnNbTiLHzqTN7L86CpJ/fmxMC3EWrM=\r\n=hlEK\r\n-----END PGP SIGNATURE-----\r\n", "size": 29417}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.4-esm_1680617403024_0.843202779672577"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:10:03.292Z", "publish_time": 1680617403292}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.21.4-esm.1", "@babel/helper-create-class-features-plugin": "^7.21.4-esm.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/plugin-syntax-typescript": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/types": "^7.21.4-esm.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.1", "dist": {"shasum": "d9cea427be531883bf6ca96028cf5c3aafb91c40", "integrity": "sha512-NIpg8j5LbiC8em+R+03nBZ638R5HRfTAFHvZmw40CAZYwKeCfQkokLI7Uux/XZNAnsol3Mmau2B0Tlwr+BqxzQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.1.tgz", "fileCount": 12, "unpackedSize": 122995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAueMLW89dGy6eTIZpn+08vqQs6zlk36fZRYWNw1Wo2UAiB7BZWPvcjit8xCjh4hKloKlYayXxVVgAJu+SGgvT3F1Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFVA//eTwL8zrPj/qsKUQmJgLbvMT00t3wPmuj7zdQ7i1qPyGF+PjN\r\naFyhB5mWMKJgKx1lUnI4FnNbjeDgZXNLuUTEaGkz7YdkRpuDkVzfmMJLORQa\r\n4yjA6YfE5lhxKpdKMqOoqY33kCax2oCzLRcXj0S1ZtHSz3ZA41N8+IljXcnP\r\nqzSk8nAflAnv0eeXzKr1Vib2fo5CE5pY6WXUwfW/B16BdMK7ApauP2lCQtBw\r\nVB02gmVmf4Nby2wrCHPTNYyfimibE3Ent3UqF3Ow2colHlMV023f6yml9t5U\r\nJmpahkrfpXw4F6bdEB9psPqONeCD2h7+CBCUjbkcApHQkg57cWZYr0Xt24pU\r\ngaWzhQFWNcwPaSHyznePOTUN0cQ3Sg7i1c3YPs1qk4G1brPBIdIlRJc+YpiM\r\nC5LTxpovkWTFBD87VvRXMa8gsAOrpx85UCURbaRi0UL0Vi8RB4EWTszMjoo+\r\n/EWgFSfAUGhIKOPOKyP2LKJ3dRMgjGe/o5vwkniG1sTYVRfdtrvRIJzOSme9\r\nxq8tEMpuvhWfB5q83tzQ+0zrZYkGml2eVQ1eEh+lqLD0dF8ZoEADOFuYU0Mh\r\nKynvNauaiGzx4K+gQiso+/GEQDkKcYEaEQd/dCRlCQuVsaIwnhWtLjF5BaG/\r\nO7yD6101h6qLqWlqGP7gaYtThxP4esP9wFc=\r\n=BO0/\r\n-----END PGP SIGNATURE-----\r\n", "size": 28960}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.4-esm.1_1680618120717_0.31704455566706913"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:22:00.841Z", "publish_time": 1680618120841}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "7.21.4-esm.2", "@babel/helper-create-class-features-plugin": "7.21.4-esm.2", "@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/plugin-syntax-typescript": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/types": "7.21.4-esm.2"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.2", "dist": {"shasum": "561337e0388ef1e639ddacd2151e73ba98a821bc", "integrity": "sha512-o66Vr80JO2l8NFcCP9RVWWxhdi98HMB1CBv+HxONcr4dFNUt9mWyV0iWM3fhWupPax85dqk6oGp7+WVZzF9hkA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.2.tgz", "fileCount": 11, "unpackedSize": 122968, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC1Etx3PNqT6TjDAthAdrLF3g87gW37VjRO9gr3CnK9BAiAwn9dM5nDd9+CiOvHWhbpPuxTES0+/QPok7ADszjGoog=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTPA/+PaKn5+kknDgFhL16hFiP/fYUiF+iO4V6ptM6rx8rpGSWfcH9\r\n8T339hbv/FSj4Blx/kCqbVPAktkxb3FwMFpIHvFzW1oKtMa5EMCknOhG27Ew\r\nllr3YHQ4DNiS6h5EEZCiOkFSp21g40iIQBx9AQpN1phXT/kEArzJMuTrVwmo\r\nkF2liCnqNuaGxEDoHUsPEBehnp0fj3Myms8G6rCWdTwZYvjOlKdeQQx+sl/g\r\nNFuEmAaw7vQrNvZr5PmVAkOdc9vebKA3jxcHIFqc+Ebz23OgeIh6tnMYBK6t\r\nVrVRIxdJM115N8bKCuk8oFJUutIGzTtEJoDSsBr9R9hm14qd70UiSYkHd1Xw\r\ncCqfF+6tUQtTyAaxTOFYbZ4QyP4/m4M2O9HQ+uOgUAUKTpI64TNitEsRqRN8\r\nAsMU0UVYM0IuvpZ/e6O94sMbztzvHQ7jyJiRjRS2bCCsZSwyUu3LFVM5PSTA\r\nbscf5l/AOb+AKxj9K0NhlLGU8kxD4F7R/OfUe+np27Eep3QpsYWT1iX2qfUI\r\nbFiLQvjJyxPy4W4k/CHnSnFNFCf+wifG6rwe5JUKJPjDVOsSC516XVkSsQGV\r\nrrhW6VLw2Vi4PWtdNsApVXStdUjh6Lgd+iQ5hu9cn5s/G0oLeusriPeSbvtc\r\nJTB0jy5nuKMFeDaljCHP7YvsHWrg6rqjN24=\r\n=e2UX\r\n-----END PGP SIGNATURE-----\r\n", "size": 28931}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.4-esm.2_1680619206251_0.6226019400760361"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:40:06.396Z", "publish_time": 1680619206396}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "7.21.4-esm.3", "@babel/helper-create-class-features-plugin": "7.21.4-esm.3", "@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/plugin-syntax-typescript": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/types": "7.21.4-esm.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.3", "dist": {"shasum": "d514dc5f9028e88419623f4d1f10301bd2216f88", "integrity": "sha512-hVIQMKd/hJ7gcT6HD9oWgFu57DH8+fNFK8zSJ9bCw+PhAoNNbmsYuLppPsHGNx5hOCoyoth954tbISqFUg+I3w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.3.tgz", "fileCount": 11, "unpackedSize": 124156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICEBtBxCxbLTDngSSXHOdNPAHVrBjZXgHgztE4HMlwr1AiEA23smu2975+ZLSPIjf5rgw2pxF1kLgu58jKPR9aXtZaY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXqQ//eCkvWYbe6w9vl0Ki9RX9DQ9lR609pZWEq1nnUUbS7L/xypE0\r\nm5Sv0F6wEqCVU14Og40159DrlITzP6qTpLemR77gtKEqeDGtuOex1VkyyVZF\r\nRM0TZt977wYLuTDdwN0tEsZa1C9M60osLBiIXr1/1lylRw97nbHHSR/cNKnL\r\n5R5AKxvfQUX2MxsE3kpAuynF8TBAhBiagFxG+fe7zwKb7inX1ga3z4QIMXgm\r\nc311Qhwc/vDh35EE/P3BQEpXJJNxN1grTyj9Exk3B/PhZP6E0Ea0MNKpB83b\r\nCrveMP4ojC8ITeOaBBvSoxO9bf2KWoFk2v4H4SusmAE2NSkNxbQx6xr5lYOS\r\ndQdWAP2iwsdQwP+H470r1fhs3jh9eAEucBitsovB7VDdmmtDPrFklUJ7AUYp\r\n//WRq0lXdaDXG91i58Gp0+/s8lFlIPrabLD3eLVu6soLIr8Nh0tl/r9fovKS\r\niepuunIiWfD/qeQhaV86LANeZL08Z9zBYwxxUtbiXx5KVXxa2IGfyVTItx+u\r\n7a3w2Vjc6HQFxYqx5T0WoQ9g8DQXIsWPNpvfnDLprz3owah+DY3p2uzFqqZB\r\nehLPiavEk3pch3/WJJMfEK0vyqJ369fGXYo6hCn2jwVVG+Hh+8prHcE+MRR6\r\n7Ef7yyuC0YZVIKs5kq+KUWNRDUSaVduI7rA=\r\n=aTj/\r\n-----END PGP SIGNATURE-----\r\n", "size": 29384}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.4-esm.3_1680620207824_0.10618473637738335"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T14:56:48.042Z", "publish_time": 1680620208042}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-typescript", "version": "7.21.4-esm.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "7.21.4-esm.4", "@babel/helper-create-class-features-plugin": "7.21.4-esm.4", "@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/plugin-syntax-typescript": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/types": "7.21.4-esm.4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-transform-typescript@7.21.4-esm.4", "dist": {"shasum": "e060203317e26c703947df3bedb30fa94f882870", "integrity": "sha512-WL7QXRgL3I/E4WuXPoegxt2kdyyPg0zIGFVw5TKcRStgduj3XmzQZKUl5izHjP0JFAMPzgHk+xkY6otUg5QAsQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.21.4-esm.4.tgz", "fileCount": 12, "unpackedSize": 122988, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEErc8toVzrVoeVFHXbASgfkLeJnO+krBmnKgVWIjuRgIhALejU0QGqxjjrUivut0eiGT4wXkB55uXfLo21PZWoyfy"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD60ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDpQ//V6MpEIMK7I8S0A3k4idOGNNB+jKJo5q1hWruDb2SeRIuiA8/\r\nAx7n9GNKNu64kt/fzZMBjPg2xUfRuWzk9OMS/0hJWIvlCIWuhDWrw1unYxuI\r\n1x2qYttZ00z14Z5EU0HeqwAYa8Nx8pZv7PLP66T+bVssfX/CbKO5K7X0lnta\r\nEH8a2Q+e1As9Zu6X7Tm4ZWdrolqrzEX42TjvOKvL2oNsITwh49xs6Cc8/0Ql\r\ndm3D/KRsg071TYw2E5vuqxSzlWWycGW2qZ9gpa2bZmqPPtQKEPHs01v9tDRw\r\ngwxWi7bjFeg7i+0rNGjBrAJvHjsMVHv2AjL6/gqPoInP+ATbtGiAPtFKXMTE\r\nRQkb8WweCrcvg+8DLgdmP5hvCreMGd+UCTbhp3JLiXwHWC5GTTTI2vBAx45S\r\nKwOp827iW5cv4Fa+0Vyt/rZOG5ku90yCA86PIv9AmzBCRgw4qMZKjUQLVQwy\r\nvg/C78onng3n9EYi/lLX4+Q1GSweWEPTCJpocYZbrMoRS2m3+AV7A90wMEwW\r\njxvhPqLo/Ik8e7dEjDSAxyqwcZVg3PC07/JDbCncTjGgfu0qenSNZEsK+WTq\r\njTnlKt7WFWPo3gsTVYHm+U1d2DECQhNLpsaiLa+XqvPSoMpyzeR3j+vGQCKX\r\nlFSMZI3EI2N5no54N8Xfgh4+o35lSlP9FmA=\r\n=NuHf\r\n-----END PGP SIGNATURE-----\r\n", "size": 28963}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.21.4-esm.4_1680621235973_0.8017598241974426"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T15:13:56.199Z", "publish_time": 1680621236199}, "7.22.0": {"name": "@babel/plugin-transform-typescript", "version": "7.22.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.22.0", "@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-typescript": "^7.21.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.22.0", "@babel/types": "^7.22.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.0", "dist": {"shasum": "d545f6d491e639078d9629f300f9363fe74e7a10", "integrity": "sha512-gb4e3dCt39wymMSfvR+6S7roQ+OBBeBXVgCpttb+FZC5GPGJ5DkqncRupirCD36nnNt7gwNLaV3Gf+iHgt/CMQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.0.tgz", "fileCount": 12, "unpackedSize": 125906, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBou48BkZu97vv27Yq2CAon8Sw1zPYcSieLf8E4rUl+uAiEAzX7a68qECwi/NlmyxiRZYxjoOnPIOFX2LWTvCmti2ng="}], "size": 30033}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.0_1685108750510_0.7211225822786902"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-26T13:45:50.768Z", "publish_time": 1685108750768, "_source_registry_name": "default"}, "7.22.3": {"name": "@babel/plugin-transform-typescript", "version": "7.22.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.22.1", "@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-typescript": "^7.21.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.22.1", "@babel/types": "^7.22.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.3", "dist": {"shasum": "8f662cec8ba88c873f1c7663c0c94e3f68592f09", "integrity": "sha512-pyjnCIniO5PNaEuGxT28h0HbMru3qCVrMqVgVOz/krComdIrY9W6FCLBq9NWHY8HDGaUlan+UhmZElDENIfCcw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.3.tgz", "fileCount": 11, "unpackedSize": 126155, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFWuH82ED/FPuW2+0uTpNyj/mr9e1czq/SBTSw4dYbrLAiEAj8NE80N2h4+ZnOCF/eObsb7EGbAGAbc9H+ZSaY25s7k="}], "size": 30052}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.3_1685182260861_0.28243337475352415"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-27T10:11:01.030Z", "publish_time": 1685182261030, "_source_registry_name": "default"}, "7.22.5": {"name": "@babel/plugin-transform-typescript", "version": "7.22.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/types": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.5", "dist": {"shasum": "5c0f7adfc1b5f38c4dbc8f79b1f0f8074134bd7d", "integrity": "sha512-SMubA9S7Cb5sGSFFUlqxyClTA9zWJ8qGQrppNUm05LtFuN1ELRFNndkix4zUJrC9F+YivWwa1dHMSyo0e0N9dA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.5.tgz", "fileCount": 11, "unpackedSize": 126155, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQV0XonJJGbtvj655lQjpb1GM1zk265eovDqo6q+4jwAiEA7a8e6ArUp5jAFJsF5WhEjV/aAfNGzu/935d3zzWMoyo="}], "size": 30035}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.5_1686248510250_0.8348550128748353"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T18:21:50.445Z", "publish_time": 1686248510445, "_source_registry_name": "default"}, "7.22.9": {"name": "@babel/plugin-transform-typescript", "version": "7.22.9", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.9", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.22.8", "@babel/types": "^7.22.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.9", "dist": {"shasum": "91e08ad1eb1028ecc62662a842e93ecfbf3c7234", "integrity": "sha512-BnVR1CpKiuD0iobHPaM1iLvcwPYN2uVFAqoLVSpEDKWuOikoCv5HbKLxclhKYUXlWkX86DoZGtqI4XhbOsyrMg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.9.tgz", "fileCount": 11, "unpackedSize": 126461, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAuYBbleMW4oTi74V2mXNGAz4antxybXXQ0YOQcuN60FAiBf5gmljTbeVHjUgtOAZzofvqODGZuTfIhnUwsAsYC1ww=="}], "size": 30136}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.9_1689180839905_0.9853650117297896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-12T16:54:00.162Z", "publish_time": 1689180840162, "_source_registry_name": "default"}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.0", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/types": "^8.0.0-alpha.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.0", "dist": {"shasum": "45bab58ce166fb94d2e89cc5858a6dfd4b92a7b3", "integrity": "sha512-MoAcsHFWGW9lAziHTLhDWydjpba30+4canTa4kWTwla8pIxD5AHZac/+UEHiU7n9aye5uBgRxgIxEoZc6kl+1A==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.0.tgz", "fileCount": 11, "unpackedSize": 189181, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDalopG4ygbZ0Gu0F6FJwvdNNzyXOLIclQKdS9UZTD+0AIhAOVQIJU6qFtb1eaeX6UhxHQqSgSQApL8qaVDlMe8Wf1M"}], "size": 43992}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.0_1689861633156_0.580786281124829"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T14:00:33.319Z", "publish_time": 1689861633319, "_source_registry_name": "default"}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.1", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.1", "@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/types": "^8.0.0-alpha.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.1", "dist": {"shasum": "33cd486d4eeaf0ce0a4d3ac85723139f355440fa", "integrity": "sha512-fCV+eG3OIxOOUWn0MGwKnPaEeAwoascjBtII4jKOMq6hFm86CkaZqmeSCAju/AgyuiqhabuoXwJANK1gZ20CTg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.1.tgz", "fileCount": 11, "unpackedSize": 191231, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChu9aKm4GjZde3OxCPqmhpCfXjgPMenSawmQ4o1c/0zgIhAJZvfFWhodK50H5o5sC4ijwPIToTGnbDiwc0VEYNce/R"}], "size": 45729}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.1_1690221185932_0.9489391741783839"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-24T17:53:06.149Z", "publish_time": 1690221186149, "_source_registry_name": "default"}, "7.22.10": {"name": "@babel/plugin-transform-typescript", "version": "7.22.10", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.10", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.22.10", "@babel/types": "^7.22.10"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.10", "dist": {"shasum": "aadd98fab871f0bb5717bcc24c31aaaa455af923", "integrity": "sha512-7++c8I/ymsDo4QQBAgbraXLzIM6jmfao11KgIBEYZRReWzNWH9NtNgJcyrZiXsOPh523FQm6LfpLyy/U5fn46A==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.10.tgz", "fileCount": 11, "unpackedSize": 130034, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9oPwsBE16ftvR6fetpqnFq9tBht3pqn1g6FMLrdmymgIhAPExKiBI5MqZxAq+6aGbdef/3e+zQEJxMIxBOZAddG8J"}], "size": 30963}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.10_1691429121925_0.8488380383663334"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-07T17:25:22.072Z", "publish_time": 1691429122072, "_source_registry_name": "default"}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.2", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/types": "^8.0.0-alpha.2"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.2", "dist": {"shasum": "81e0438a2e97bc05d4f12818cc5f312b5fe76eeb", "integrity": "sha512-jg1KMBs6HaeBpTV+psU7M7WGGyD/7bGO6kXSbaogrFZx2nFsCVEaXhtj5GvGFnGgDPae1tgkecQOexLyQ2AV6w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.2.tgz", "fileCount": 11, "unpackedSize": 191231, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCuDjoZOs7CFZ5HVhV4f8fEeEaVvXaxu/7u89B9DuBrAIgaKVxi+p5aZoJfA9mtn4JMvqbHTlxtt8Ef5OXdvR/95s="}], "size": 45729}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.2_1691594128002_0.7275784016801976"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-09T15:15:28.258Z", "publish_time": 1691594128258, "_source_registry_name": "default"}, "7.22.11": {"name": "@babel/plugin-transform-typescript", "version": "7.22.11", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.11", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.22.11", "@babel/types": "^7.22.11"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.11", "dist": {"shasum": "9f27fb5e51585729374bb767ab6a6d9005a23329", "integrity": "sha512-0E4/L+7gfvHub7wsbTv03oRtD69X31LByy44fGmFzbZScpupFByMcgCJ0VbBTkzyjSJKuRoGN8tcijOWKTmqOA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.11.tgz", "fileCount": 11, "unpackedSize": 130273, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEtRaDDa81GksEzAE+Rj4jIwAf/AToaicd4hzQHh6naQIhALOmpBi51YAk4PDV5lbRQVU8RoAAy8P0dM8dFvI0s2Ql"}], "size": 31009}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.11_1692882527377_0.3517122347717412"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-24T13:08:47.592Z", "publish_time": 1692882527592, "_source_registry_name": "default"}, "7.22.15": {"name": "@babel/plugin-transform-typescript", "version": "7.22.15", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.22.15", "@babel/types": "^7.22.15"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.22.15", "dist": {"shasum": "15adef906451d86349eb4b8764865c960eb54127", "integrity": "sha512-1uirS0TnijxvQLnlv5wQBwOX3E1wCFX7ITv+9pBV2wKEk4K+M5tqDaoNXnTH8tjEIYHLO98MwiTWO04Ggz4XuA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.22.15.tgz", "fileCount": 11, "unpackedSize": 130300, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICNPsHoH7BaAAlgzRw1Mcf65dv9A2721cIGJjfUf29M3AiBquP7QjntoWIb5Ik4Hyn8xjlDi5sloZtjKXaA35mB00w=="}], "size": 31034}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.22.15_1693830323745_0.5848341439998186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-04T12:25:23.953Z", "publish_time": 1693830323953, "_source_registry_name": "default"}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.3", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.3", "@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/types": "^8.0.0-alpha.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.3", "dist": {"shasum": "cd65edb2ba633fe6f6915f7fa106c7952f9016e5", "integrity": "sha512-2TBJnzMCjosnYJK8ufOanfqj64d/OD0Tp5+xhKbrgZ165ak5m3KDPCEK5N+OzptoiBhki8ynS39glOBb4xXuDQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.3.tgz", "fileCount": 5, "unpackedSize": 130511, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAF5fjiF9EQhxBp17tCkLimE6ADTsTCyJ1oOEN9OnXpyAiArm4XpMoLotyUY1Fqb3gTYhSFE+6z0kK5+N9RJDz8mHA=="}], "size": 31915}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.3_1695740260553_0.14293112087236803"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T14:57:40.754Z", "publish_time": 1695740260754, "_source_registry_name": "default"}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.4", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.4", "@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/types": "^8.0.0-alpha.4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.4", "dist": {"shasum": "461da146f68ec2833e2c44b353a8e05437159bde", "integrity": "sha512-Ebkzhhu4n+5Ls7GCEX6DLMADpg77EuTEHGcSnws5WKUmbZ3udbtVpr7czOkbuCUtnMFs7gVVgXlnjJ4aXUSfzA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.4.tgz", "fileCount": 5, "unpackedSize": 130511, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXnV+qvRVjRBzJYNbM7E+wQAIbyNbrB2292PbTxkK0ngIgAv8xYJpdBsP1T1EBRpX17vaC1GvPyLAImHwRC6gWwHg="}], "size": 31916}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.4_1697076414134_0.401574193420005"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-12T02:06:54.445Z", "publish_time": 1697076414445, "_source_registry_name": "default"}, "7.23.3": {"name": "@babel/plugin-transform-typescript", "version": "7.23.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.3", "@babel/types": "^7.23.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.23.3", "dist": {"shasum": "ce806e6cb485d468c48c4f717696719678ab0138", "integrity": "sha512-ogV0yWnq38CFwH20l2Afz0dfKuZBx9o/Y2Rmh5vuSS0YD1hswgEgTfyTzuSrT2q9btmHRSqYoSfwFUVaC1M1Jw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.3.tgz", "fileCount": 13, "unpackedSize": 132066, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0YfisP/uxmjl9Uez7agFLsQKgkvC5cTkEOHhO5pocVgIhANaVEgEbkUIzqY/BnA8dBo5wJViL0eiBvQZZ2jTicomG"}], "size": 31530}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.23.3_1699513454270_0.20939220500418365"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-09T07:04:14.508Z", "publish_time": 1699513454508, "_source_registry_name": "default"}, "7.23.4": {"name": "@babel/plugin-transform-typescript", "version": "7.23.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.4", "@babel/types": "^7.23.4"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.23.4", "dist": {"shasum": "da12914d17b3c4b307f32c5fd91fbfdf17d56f86", "integrity": "sha512-39hCCOl+YUAyMOu6B9SmUTiHUU0t/CxJNUmY3qRdJujbqi+lrQcL11ysYUsAvFWPBdhihrv1z0oRG84Yr3dODQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.4.tgz", "fileCount": 13, "unpackedSize": 132072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaNk0U41q8K+XAme/LoGhktNlKbRUCkns+4ismth7G+AIhAIQXnDUIFgWHT79dNZ7Uql87+aMsq/eWCVuKsVhYg9Z4"}], "size": 31540}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.23.4_1700490129826_0.6459056858685859"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-20T14:22:10.018Z", "publish_time": 1700490130018, "_source_registry_name": "default"}, "7.23.5": {"name": "@babel/plugin-transform-typescript", "version": "7.23.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.23.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.5", "@babel/types": "^7.23.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.23.5", "dist": {"shasum": "83da13ef62a1ebddf2872487527094b31c9adb84", "integrity": "sha512-2fMkXEJkrmwgu2Bsv1Saxgj30IXZdJ+84lQcKKI7sm719oXs0BBw2ZENKdJdR1PjWndgLCEBNXJOri0fk7RYQA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.5.tgz", "fileCount": 13, "unpackedSize": 132189, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn0vJp3ubAKzN/DN72FmrbkEIkixSC4RN/eXruAMsYagIhAPy8TYQ9Vb0CLs1Wd6dFBSXsLA06zWIj94Dres6f+BOi"}], "size": 31563}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.23.5_1701253544870_0.4261027431044735"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-29T10:25:45.054Z", "publish_time": 1701253545054, "_source_registry_name": "default"}, "7.23.6": {"name": "@babel/plugin-transform-typescript", "version": "7.23.6", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.23.6", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-typescript": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.6", "@babel/types": "^7.23.6"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.23.6", "dist": {"shasum": "aa36a94e5da8d94339ae3a4e22d40ed287feb34c", "integrity": "sha512-6cBG5mBvUu4VUD04OHKnYzbuHNP8huDsD3EDqqpIpsswTDoqHCjLoHb6+QgsV1WsT2nipRqCPgxD3LXnEO7XfA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.23.6.tgz", "fileCount": 13, "unpackedSize": 132370, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSnoo3Qpyy29EPqPvdKc5ufCMLr8TwT0zTpcYBUyR2cAIhAOhItuYNqAWV2ZBITnwrKPQD9v6pCDZJQdkXizx3TM1C"}], "size": 31726}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.23.6_1702300202735_0.7714935728110235"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-11T13:10:03.028Z", "publish_time": 1702300203028, "_source_registry_name": "default"}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.5", "@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/types": "^8.0.0-alpha.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.5", "dist": {"shasum": "8d7baf5b733437242f9a163d9e488ba02efef8e4", "integrity": "sha512-qtq9rG8COdWEdL0XEh5zeu6XCpotCskIi9wwK3Ozj29xjIHJdF5n/2NL8Blzfrf/Q+hjtRarwUlI6zRxJp04Ug==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.5.tgz", "fileCount": 5, "unpackedSize": 131590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFdRzDycNspwHANrY6l8ByERFpyQGG6P2ncsQ8PF0P8eAiAICyy0XzGYk9yoxG0zT3Vwuq5zPlu7mEOzGXvd8yu7Fg=="}], "size": 32268}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.5_1702307988515_0.9212563174683863"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-11T15:19:48.726Z", "publish_time": 1702307988726, "_source_registry_name": "default"}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.6", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.6", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.6", "@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/types": "^8.0.0-alpha.6"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.6", "dist": {"shasum": "208d58341b1124793fb2acbd41490b092f1c8dcd", "integrity": "sha512-WqqOOu8i+Q4qxS+8iNk2ShO3mA1yBRVMRshgvI69D/Ny6DIIPPzZ6oLIIVkYYuO1LCXgFDRQm0T89o2XVDxtrQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.6.tgz", "fileCount": 5, "unpackedSize": 131590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCj3eaHuTWkIybd6tYp6KkR3+z5Do9SiGB7Zctbsf07OAIhALdkz7VEWb8811lS10dHCvZfuA1Dq9/A64gd+NKidyzs"}], "size": 32268}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.6_1706285693768_0.08197997016126646"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-26T16:14:53.909Z", "publish_time": 1706285693909, "_source_registry_name": "default"}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.7", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.7", "@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/types": "^8.0.0-alpha.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.7", "dist": {"shasum": "5f202d316594d3b6cbc7ca764ad8cc659b1a8648", "integrity": "sha512-TEBL5hN+mSgbBjLtuGBy+CfBHMNimEjugApjTHavH/jCjkhnN2sDKEUAMCqf+7kL9XpMalYoKILJvMGYvbISsA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.7.tgz", "fileCount": 5, "unpackedSize": 131590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHQrXytyrPLRZAUngMRQA56F/h8pQByoqnLzIp223vrAIgZJQV/yWDXCbXcIKKpIuzE8jMLWW59MgZjDAICxLTJYQ="}], "size": 32267}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.7_1709129151542_0.07715475555776408"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-28T14:05:51.690Z", "publish_time": 1709129151690, "_source_registry_name": "default"}, "7.24.1": {"name": "@babel/plugin-transform-typescript", "version": "7.24.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.24.1", "@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-typescript": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/types": "^7.24.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.24.1", "dist": {"shasum": "5c05e28bb76c7dfe7d6c5bed9951324fd2d3ab07", "integrity": "sha512-liYSESjX2fZ7JyBFkYG78nfvHlMKE6IpNdTVnxmlYUR+j5ZLsitFbaAE+eJSK2zPPkNWNw4mXL51rQ8WrvdK0w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.1.tgz", "fileCount": 13, "unpackedSize": 132365, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF+8RdhgyuqOt2bSvN5FlBJ1eeUjroncwLt78u2RgLPiAiBLgIfxIeg1/y2q3LonP+sWge0UWVc2kSvbQmHCzqt0GQ=="}], "size": 31747}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.24.1_1710841775905_0.07680753358386161"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-19T09:49:36.095Z", "publish_time": 1710841776095, "_source_registry_name": "default"}, "7.24.4": {"name": "@babel/plugin-transform-typescript", "version": "7.24.4", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.24.4", "@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-typescript": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/types": "^7.24.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.24.4", "dist": {"shasum": "03e0492537a4b953e491f53f2bc88245574ebd15", "integrity": "sha512-79t3CQ8+oBGk/80SQ8MN3Bs3obf83zJ0YZjDmDaEZN8MqhMI760apl5z6a20kFeMXBwJX99VpKT8CKxEBp5H1g==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.4.tgz", "fileCount": 13, "unpackedSize": 132433, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZXVFPUn5ZaTnEPy+In6BhdEI/eH28TPdgrcwOvo0TwAIgUF9eEYAbHqY8JdcIsFXdr5Xpi48rov98UhwwucFx+qA="}], "size": 31791}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.24.4_1712163232847_0.17228938739969668"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-03T16:53:53.011Z", "publish_time": 1712163233011, "_source_registry_name": "default"}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.8", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.8", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.8", "@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/types": "^8.0.0-alpha.8"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.8", "dist": {"shasum": "45f5baaa8292c424c386ce7e9a60e98c032d24f6", "integrity": "sha512-RZgXwkjnIRwjyv9pHni2fLZMYUdzKsA/fjO+aghlkptjrNK2vzRZzdw+cSGpbsyJYJ7D4zPCTPECdiCaVGfAsA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.8.tgz", "fileCount": 5, "unpackedSize": 131574, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHX4nvCjT0vCAQm6bhQ63OmTba2O7Kf+27eDtayimG0QAiEAoXiobYWS9J0bDN7nVdYdjozLceBHiyjBW2qeHUg+OOc="}], "size": 32265}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.8_1712236823996_0.12910280868639035"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-04T13:20:24.206Z", "publish_time": 1712236824206, "_source_registry_name": "default"}, "7.24.5": {"name": "@babel/plugin-transform-typescript", "version": "7.24.5", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.24.5", "@babel/helper-plugin-utils": "^7.24.5", "@babel/plugin-syntax-typescript": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/traverse": "^7.24.5", "@babel/types": "^7.24.5"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.24.5", "dist": {"shasum": "bcba979e462120dc06a75bd34c473a04781931b8", "integrity": "sha512-E0VWu/hk83BIFUWnsKZ4D81KXjN5L3MobvevOHErASk9IPwKHOkTgvqzvNo1yP/ePJWqqK2SpUR5z+KQbl6NVw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.5.tgz", "fileCount": 15, "unpackedSize": 200605, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJyCHLQV3lY5YsaoEiilArwzG6nsHzY8XRaUpuRsYhdAiBoLo8VFmUwjcwriSn0lrHFNcZZjn3vp/Ktwrh3kfCI8g=="}], "size": 55412}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.24.5_1714415668201_0.9884793440295327"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-29T18:34:28.378Z", "publish_time": 1714415668378, "_source_registry_name": "default"}, "7.24.6": {"name": "@babel/plugin-transform-typescript", "version": "7.24.6", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.6", "@babel/helper-create-class-features-plugin": "^7.24.6", "@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-typescript": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/types": "^7.24.6"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.24.6", "dist": {"shasum": "339c6127a783c32e28a5b591e6c666f899b57db0", "integrity": "sha512-H0i+hDLmaYYSt6KU9cZE0gb3Cbssa/oxWis7PX4ofQzbvsfix9Lbh8SRk7LCPDlLWJHUiFeHU0qRRpF/4Zv7mQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.6.tgz", "fileCount": 15, "unpackedSize": 200772, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEMvJlZZH88XGpnIBYAHw3t5aXMvu/ARTso04RhHppnCAiEAutp926GQ3ZybGTC9N8dYNpKCcRCt/p0Qvj/GgfY/CCY="}], "size": 55457}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.24.6_1716553514484_0.5679498360779247"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-24T12:25:14.651Z", "publish_time": 1716553514651, "_source_registry_name": "default"}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.9", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.9", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.9", "@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/types": "^8.0.0-alpha.9"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.9", "dist": {"shasum": "4c529508800986c2759e0cc4886f0b4d997edbee", "integrity": "sha512-ZpPjVrivSNk0CZD1frBgfmTHfU9cRmHuv9v0LS2416h2xyv6RO7Td7zbBe3SEH8/30fGQoJnu1eGz7RfH7u3Gw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.9.tgz", "fileCount": 8, "unpackedSize": 200456, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDAZDhJmKxn2gIbQbnYOnUsI+D2bQXxsE5L1+XVoAoF1AiBgt3IHl04ahJjqD0L1h2hYLpmhwPd4UbuGPFhzYemiBQ=="}], "size": 56271}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.9_1717423551648_0.7228346344046792"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-03T14:05:51.895Z", "publish_time": 1717423551895, "_source_registry_name": "default"}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.10", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.10", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.10", "@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/types": "^8.0.0-alpha.10"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.10", "dist": {"shasum": "7859014f19e17d04d4978890d4a7d53aafab0211", "integrity": "sha512-6cf600GEGvv7JqquB4N+pf3b3hsNs+RpF/Sf0PBBXOXHCF/Xm+QARNovo3C0wqXzFn21M3fiNq0Zk8LZ2RChdQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.10.tgz", "fileCount": 8, "unpackedSize": 200468, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqk3yv7tuBuePWGIHSH4bXwM9pJh4pBLAbHDpM1jcZ2wIgdfcQbjKa8LSTGbnZGA9v09Uc5yHtB4+E590WfSUOTuA="}], "size": 56269}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.10_1717500048051_0.024362385748515125"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-04T11:20:48.221Z", "publish_time": 1717500048221, "_source_registry_name": "default"}, "7.24.7": {"name": "@babel/plugin-transform-typescript", "version": "7.24.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/types": "^7.24.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.24.7", "dist": {"shasum": "b006b3e0094bf0813d505e0c5485679eeaf4a881", "integrity": "sha512-iLD3UNkgx2n/HrjBesVbYX6j0yqn/sJktvbtKKgcaLIQ4bTTQ8obAypc1VpyHPD2y4Phh9zHOaAt8e/L14wCpw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.7.tgz", "fileCount": 15, "unpackedSize": 200584, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBf6LISm2Hjb7+vEF1SJTB1JIUTE461NpFleBkyahHKQIhAJFY1ny8lSbgeNIrR8jdcP1FRBkOvSAl99tks0Be9aMR"}], "size": 55423}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.24.7_1717593360120_0.47607866102723784"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T13:16:00.310Z", "publish_time": 1717593360310, "_source_registry_name": "default"}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.11", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.11", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.11", "@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/types": "^8.0.0-alpha.11"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.11", "dist": {"shasum": "710fbcf83dc5f524def2f85285de26973f7a15ec", "integrity": "sha512-3UCIMvVdGkC4DNQ9JG0aoE7YmhYIsiFBhTGcRXg6otC55zwk3wKFleweCAefHRaAl5c8RCy++rSlgwJniyaZ5Q==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.11.tgz", "fileCount": 8, "unpackedSize": 200359, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhVQyUmo5UCJK/Y3REPDuJbCmPlL6IuXPflrlKpQ7/EAIhAIU+Amy55TIQ9zmCFeVTO92cA6MD7tJS76gDAMSJpeDl"}], "size": 56240}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.11_1717751770750_0.7251078451616764"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-07T09:16:10.991Z", "publish_time": 1717751770991, "_source_registry_name": "default"}, "7.24.8": {"name": "@babel/plugin-transform-typescript", "version": "7.24.8", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.8", "@babel/helper-plugin-utils": "^7.24.8", "@babel/plugin-syntax-typescript": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/traverse": "^7.24.8", "@babel/types": "^7.24.8"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.24.8", "dist": {"shasum": "c104d6286e04bf7e44b8cba1b686d41bad57eb84", "integrity": "sha512-CgFgtN61BbdOGCP4fLaAMOPkzWUh6yQZNMr5YSt8uz2cZSSiQONCQFWqsE4NeVfOIhqDOlS9CR3WD91FzMeB2Q==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.8.tgz", "fileCount": 15, "unpackedSize": 197197, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEGMjSr4VZyeyqWAXqv4mU6x93CasHMkaZvnCDDMJAk8AiEAr0nSyRi4pX6vNaQqnVKNd4IEPaUuvfqQszA8or5g+Pc="}], "size": 55042}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.24.8_1720709698911_0.29801601411368517"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-11T14:54:59.088Z", "publish_time": 1720709699088, "_source_registry_name": "default"}, "7.25.0": {"name": "@babel/plugin-transform-typescript", "version": "7.25.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "56f47fb87b86a97caa9c7770920a1967d40ac86e", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.0.tgz", "fileCount": 15, "integrity": "sha512-LZicxFzHIw+Sa3pzgMgSz6gdpsdkfiMObHUzhSIrwKF0+/rP/nuR49u79pSS+zIFJ1FeGeqQD2Dq4QGFbOVvSw==", "signatures": [{"sig": "MEYCIQCOfdS/sk65VH/cD+IUdk9SeOIYradZCRMvt9uJWfPMVAIhANPLBW/2VcPKnTXYtal5P09HYP5CZ79m45MajyJuLEwe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199729, "size": 55654}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.25.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/types": "^7.25.0", "@babel/traverse": "^7.25.0", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.25.0_1722013174146_0.8318536586939653", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-07-26T16:59:34.331Z", "publish_time": 1722013174331, "_source_registry_name": "default"}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.12", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.12", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.12", "@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.12", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/types": "^8.0.0-alpha.12"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.12", "dist": {"shasum": "02fc4a5a08c52b3ed5365c284efbcd011edc82c4", "integrity": "sha512-tv3Q0cCHzJuh4LaSqsjLNCqY733RvCehfCIxq1bqPxybb9bJR2mA60BOKwayYMZPEoBxMzVvqTDtPoZgiSmfmw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.12.tgz", "fileCount": 8, "unpackedSize": 199563, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4u+k049pxzggD36XRcaVHw0QcUOeECawump4UqeAMlAIgYrqCgB1dnwc3SMVPvC/Nx/q/A7V7BkXhxnn6GpFvA2A="}], "size": 56597}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.12_1722015246406_0.032663125037052376"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-26T17:34:06.611Z", "publish_time": 1722015246611, "_source_registry_name": "default"}, "7.25.2": {"name": "@babel/plugin-transform-typescript", "version": "7.25.2", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7", "@babel/plugin-syntax-typescript": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/traverse": "^7.25.2", "@babel/types": "^7.25.2"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.25.2", "dist": {"shasum": "237c5d10de6d493be31637c6b9fa30b6c5461add", "integrity": "sha512-lBwRvjSmqiMYe/pS0+1gggjJleUJi7NzjvQ1Fkqtt69hBa/0t1YuW/MLQMAPixfwaQOHUXsd6jeU3Z+vdGv3+A==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.2.tgz", "fileCount": 15, "unpackedSize": 199727, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjy6gVH9Svdb6ElYxmzNjoB8oNYhiKIG420tGBNiTV1wIhAIXinrXB2/u+Hsxboh5LWA+GKE6FtAaVj4K1kpTgKKNX"}], "size": 55657}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.25.2_1722308089424_0.7322936410790839"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-30T02:54:49.632Z", "publish_time": 1722308089632, "_source_registry_name": "default"}, "7.25.7": {"name": "@babel/plugin-transform-typescript", "version": "7.25.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7", "@babel/plugin-syntax-typescript": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/types": "^7.25.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.25.7", "dist": {"shasum": "8fc7c3d28ddd36bce45b9b48594129d0e560cfbe", "integrity": "sha512-VKlgy2vBzj8AmEzunocMun2fF06bsSWV+FvVXohtL6FGve/+L217qhHxRTVGHEDO/YR8IANcjzgJsd04J8ge5Q==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.7.tgz", "fileCount": 15, "unpackedSize": 207736, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEBtzG7z7YHM94w0GyOwjnTIln4krthjw0NwM4xeXVrDAiEAqr4wXqDVAA6dwddLOIsmIowszNCyFYY3s2RI9L1avcw="}], "size": 56243}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.25.7_1727882136925_0.7056501145118295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-02T15:15:37.159Z", "publish_time": 1727882137159, "_source_registry_name": "default"}, "7.25.9": {"name": "@babel/plugin-transform-typescript", "version": "7.25.9", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.25.9", "dist": {"shasum": "69267905c2b33c2ac6d8fe765e9dc2ddc9df3849", "integrity": "sha512-7PbZQZP50tzv2KGGnhh82GSyMB01yKY9scIjf1a+GfZCtInOWqUH5+1EBU4t9fyR5Oykkkc9vFTs4OHrhHXljQ==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.9.tgz", "fileCount": 13, "unpackedSize": 134864, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIERgJV+++VJKGxMg6jrIWd8JNYrIrsmOAWE6GRCMvdmoAiEAgeg2FoQJr4Bfuul+WDY4/ElvafqpxGhmggg1Tl5x+4A="}], "size": 32340}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.25.9_1729610512123_0.5890910267114629"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-22T15:21:52.326Z", "publish_time": 1729610512326, "_source_registry_name": "default"}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.13", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.13", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.13", "@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.13", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/types": "^8.0.0-alpha.13"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.13", "dist": {"shasum": "7dafbb449f931e8b3851f5a74c43b89359827200", "integrity": "sha512-ImJsxfU9x3peo0IwDo11mL0QP5kSF7M6D82JSF0VccXQZj+BF3qXvNdYpp7Yx5plv8oPIVyUH4dx7olHAyd2Kw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.13.tgz", "fileCount": 6, "unpackedSize": 134698, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJANAZyX3UVURt6deg2cHflvJTYDZN1pBkvvMEbh/bsAIgTXonyx7b+fA+TE5/uLUiQ3OQMhh20vj07tIHXpLmRLw="}], "size": 33189}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.13_1729864492572_0.9267034249354438"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-25T13:54:52.771Z", "publish_time": 1729864492771, "_source_registry_name": "default"}, "7.26.3": {"name": "@babel/plugin-transform-typescript", "version": "7.26.3", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/traverse": "^7.26.3", "@babel/types": "^7.26.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.26.3", "dist": {"shasum": "3d6add9c78735623317387ee26d5ada540eee3fd", "integrity": "sha512-6+5hpdr6mETwSKjmJUdYw0EIkATiQhnELWlE3kJFBwSg/BGIVwVaVbX+gOXBCdc7Ln1RXZxyWGecIXhUfnl7oA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.3.tgz", "fileCount": 13, "unpackedSize": 136629, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDRhSNguiHmmZdHaZ9+B+occCJc8HKVdlg8Gqf7AYeyCAiBPwqcvmjHnAT9r6ltbemxF7BBENJds8/y38Aj+FxJIJQ=="}], "size": 32786}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_7.26.3_1733315735827_0.132748689530636"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-04T12:35:36.046Z", "publish_time": 1733315736046, "_source_registry_name": "default"}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.14", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.14", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.14", "@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.14", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/types": "^8.0.0-alpha.14"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.14", "dist": {"shasum": "d342f19550a45e1f59fab81768f40472d566a84c", "integrity": "sha512-nwhE0sG+N2RxtvmUtHLTUUsatMA7TdeGsZFWrvq3QtqHG6RxUnJRLt0dnczH+FdnO8FHltbd2BNlBx4sX+NzSA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.14.tgz", "fileCount": 6, "unpackedSize": 137464, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxZiMOPM2YhYTW2NH7n0q8ayAeJYRoxDkaVr2Pgz7EHQIhAPck8EBXnxh5xzimTF3L+sLxZWKo7s+KWp14ZGAR4nDP"}], "size": 33722}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.14_1733504081521_0.6213235640725094"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T16:54:41.721Z", "publish_time": 1733504081721, "_source_registry_name": "default"}, "7.26.5": {"name": "@babel/plugin-transform-typescript", "version": "7.26.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typescript@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "dist": {"shasum": "6d9b48e8ee40a45a3ed12ebc013449fdf261714c", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.5.tgz", "fileCount": 13, "integrity": "sha512-GJhPO0y8SD5EYVCy2Zr+9dSZcEgaSmq5BLR0Oc25TOEhC+ba49vUAGZFjy8v79z9E1mdldq4x9d1xgh4L1d5dQ==", "signatures": [{"sig": "MEQCIDcHy7zytDwdI+AQHl6bOURVqP2XXAKRds1p8LTEbmBRAiB9nlF4dd1GlRGKd6wYTlYP6bCNYCyxpS2/k2AC2vBnTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140231, "size": 33429}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typescript"}, "description": "Transform TypeScript into ES.next", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/types": "^7.26.5", "@babel/traverse": "^7.26.5", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typescript_7.26.5_1736529112073_0.9371759047813097", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-01-10T17:11:52.301Z", "publish_time": 1736529112301, "_source_registry_name": "default"}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.15", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.15", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.15", "@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.15", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/types": "^8.0.0-alpha.15"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.15", "dist": {"shasum": "4ea10af74686c4afac8cab4651a3937f06465279", "integrity": "sha512-pVxcxHsrS2r89L6cQIx67R3YLWVuAdPIUm5tkb1eAcz+mnCji1hRG4HNlscj5RHC6lvc0p24fePxJPhmJ7GurA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.15.tgz", "fileCount": 6, "unpackedSize": 140680, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD8ETz2XWvZThlKl3yBfs5exobkNy7mi/YLaRG8FiwNgIgJqbPsSJVhI4RdVMUlIkw3lwVnQ7mQLzCBgDu5tnKCmE="}], "size": 34217}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.15_1736529911576_0.5591697268554547"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-10T17:25:11.789Z", "publish_time": 1736529911789, "_source_registry_name": "default"}, "7.26.7": {"name": "@babel/plugin-transform-typescript", "version": "7.26.7", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/traverse": "^7.26.7", "@babel/types": "^7.26.7"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.26.7", "dist": {"shasum": "64339515ea3eff610160f62499c3ef437d0ac83d", "integrity": "sha512-5cJurntg+AT+cgelGP9Bt788DKiAw9gIMSMU2NJrLAilnj0m8WZWUNZPSLOmadYsujHutpgElO+50foX+ib/Wg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.7.tgz", "fileCount": 13, "unpackedSize": 142860, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCUufLJnKvMiEVk2VbeCyTZFzwvVc0r+sBICZYTEEpkoQIgeToF7Cts44t5WJDX5HyM8Uhu/PFdk3U8/IVpAtawJ8s="}], "size": 33988}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_7.26.7_1737731093027_0.21922213676848679"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-24T15:04:53.286Z", "publish_time": 1737731093286, "_source_registry_name": "default"}, "7.26.8": {"name": "@babel/plugin-transform-typescript", "version": "7.26.8", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.8", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/traverse": "^7.26.8", "@babel/types": "^7.26.8"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.26.8", "dist": {"shasum": "2e9caa870aa102f50d7125240d9dbf91334b0950", "integrity": "sha512-bME5J9AC8ChwA7aEPJ6zym3w7aObZULHhbNLU0bKUhKsAkylkzUdq+0kdymh9rzi8nlNFl2bmldFBCKNJBUpuw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.8.tgz", "fileCount": 13, "unpackedSize": 143367, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHz3mYHwp2QF4T5j67PnKBoMCnZoImPpU+/Jj+dT3s8sAiEA4PKOhrmnpbg3TUEQF1TCQupoDX2AobVjZmCZiEd0yIM="}], "size": 34093}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_7.26.8_1739008763122_0.8949434278865906"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-08T09:59:23.351Z", "publish_time": 1739008763351, "_source_registry_name": "default"}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.16", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.16", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.16", "@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.16", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/types": "^8.0.0-alpha.16"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.16", "dist": {"shasum": "d36401d343940ed144d2828b87f7d31508e856a4", "integrity": "sha512-hQt6oM5egh5RePZoIDp0hX3JnQxORgTY5ACq5P5f/39Sbpe7rNkDc/oJcReYVPWAZTgIs7/LC1F1GKbIeLY7FA==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.16.tgz", "fileCount": 6, "unpackedSize": 143893, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCmIpSc7tAkmgOKACiifNi7ZSrjAD8I87ayrtpYivKaLgIhANRWez1rG6BRXIc/fnGqMkURuINIih8xS60oYahZXJ+S"}], "size": 34805}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.16_1739534385646_0.6762372440747901"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T11:59:45.849Z", "publish_time": 1739534385849, "_source_registry_name": "default"}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-alpha.17", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-alpha.17", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.17", "@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.17", "@babel/plugin-syntax-typescript": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/types": "^8.0.0-alpha.17"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-alpha.17", "dist": {"shasum": "4182bd183e42bd53f9b2e37dcb231a10d88384e1", "integrity": "sha512-r1ZPPswYyHUwR4xfdYG6AETL8J7D4JgfmfPJvvr2t5d08HToTBDtzmtkz/JIj5BLLQo5Edg5xQ3GlxklKlikhg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-alpha.17.tgz", "fileCount": 6, "unpackedSize": 143893, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCVGIvrRVDlTtg9vEhW6Zn/RXbDAuelEr8uCPIqlqZA4QIgMY7Euw0TsmZDMG+iC3HwLw8NhjdbvSdYzWJ6BIqE3ng="}], "size": 34805}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_8.0.0-alpha.17_1741717539937_0.5891665193056053"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-11T18:25:40.145Z", "publish_time": 1741717540145, "_source_registry_name": "default"}, "7.27.0": {"name": "@babel/plugin-transform-typescript", "version": "7.27.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.27.0", "@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.27.0", "dist": {"shasum": "a29fd3481da85601c7e34091296e9746d2cccba8", "integrity": "sha512-fRGGjO2UEGPjvEcyAZXRXAS8AfdaQoq7HnxAbJoAoW10B9xOKesmmndJv+Sym2a+9FHWZ9KbyyLCe9s0Sn5jtg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.0.tgz", "fileCount": 13, "unpackedSize": 143556, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDjtg5C+VKi20tN2nlFPV4AQFI7N+3jIRwV5IW5aiOJJQIhAPgEO/Jwz5fdSNNCHMK/UgNoYdti0NSfT2cDcrGSf+KI"}], "size": 34142}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_7.27.0_1742838115192_0.357916109206343"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-24T17:41:55.397Z", "publish_time": 1742838115397, "_source_registry_name": "default"}, "7.27.1": {"name": "@babel/plugin-transform-typescript", "version": "7.27.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.27.1", "dist": {"shasum": "d3bb65598bece03f773111e88cc4e8e5070f1140", "integrity": "sha512-Q5sT5+O4QUebHdbwKedFBEwRLb02zJ7r4A5Gg2hUoLuU3FjdMcyqcywqUrLCaDsFCxzokf7u9kuy7qz51YUuAg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz", "fileCount": 13, "unpackedSize": 143560, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCUWt4r53fgx4L8+q607yCp3tcPANpoQxSHbVl5lSHPOgIhAP4RliOP6qZvVTtIXNxBU7qdD/+c47FBKjKvlIO/yZbd"}], "size": 34130}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_7.27.1_1746025774981_0.8056683398621258"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-30T15:09:35.173Z", "publish_time": 1746025775173, "_source_registry_name": "default"}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-beta.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.0", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.0", "@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.0", "@babel/plugin-syntax-typescript": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/types": "^8.0.0-beta.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-beta.0", "dist": {"shasum": "96e13424dbc53fffde6f9d8daab36af78a8f7711", "integrity": "sha512-cojpbrnQ6LYbcpFR+NdZ+VK1Gq+ObK6OHFY2NeSdbfAo59jBoVAQizRxwtF7W7GvOhr8BEOV8rMfW+XG71ku+w==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-beta.0.tgz", "fileCount": 6, "unpackedSize": 143927, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHeI/ywlaMe51a/G0+kE+rhiV6wJKOvSpYcziarA3F0SAiBMiixOLigeylDfHCCcU8KbMvmXzCYWzYulM2LO6mObtQ=="}], "size": 34803}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_8.0.0-beta.0_1748620311634_0.34941208418960157"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-30T15:51:51.816Z", "publish_time": 1748620311816, "_source_registry_name": "default"}, "7.28.0": {"name": "@babel/plugin-transform-typescript", "version": "7.28.0", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-transform-typescript@7.28.0", "dist": {"shasum": "796cbd249ab56c18168b49e3e1d341b72af04a6b", "integrity": "sha512-4AEiDEBPIZvLQaWlc9liCavE0xRM0dNca41WtBeM3jgFptfUOSG9z0uteLhq6+3rq+WB6jIvUwKDTpXEHPJ2Vg==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.28.0.tgz", "fileCount": 13, "unpackedSize": 143630, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEqrNQiXvdcF+/puRfGHL6czqHxWkLz4Px83mQVyo4WMAiEA/PRt9m6lvqL5s+DhfFVfP7oztDohHEdCwjrmJ5kyy3w="}], "size": 34159}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_7.28.0_1751445497734_0.14653200312635262"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T08:38:17.918Z", "publish_time": 1751445497918, "_source_registry_name": "default"}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-typescript", "version": "8.0.0-beta.1", "description": "Transform TypeScript into ES.next", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.1", "@babel/plugin-syntax-typescript": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typescript@8.0.0-beta.1", "dist": {"shasum": "a638e38bb50623a2eebfcc09dfc8c2e3623dee24", "integrity": "sha512-E3ctyFhsPxhf+V2pzufJO344pWPsKRsuJ312EqC9CRVueJ81RzChjoltrd0WSCde/8E5iv71/8MPVRzO0odKIw==", "tarball": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 143997, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCg0MUuqeqOVPvjucte8riMrIVgTKFPk9qMVnkGScSXLAIhAL38M5Fw3EjlTZDlJcKCf2C/7IVsgsU/3yRtsnHDxGnj"}], "size": 34827}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typescript_8.0.0-beta.1_1751447093247_0.5616408860006987"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-02T09:04:53.464Z", "publish_time": 1751447093464, "_source_registry_name": "default"}}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typescript", "keywords": ["babel-plugin", "typescript"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typescript"}, "_source_registry_name": "default"}