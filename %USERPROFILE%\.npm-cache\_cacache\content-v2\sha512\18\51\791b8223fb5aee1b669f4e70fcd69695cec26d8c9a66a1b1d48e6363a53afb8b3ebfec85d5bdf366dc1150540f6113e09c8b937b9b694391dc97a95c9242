{"_attachments": {}, "_id": "parse-node-version", "_rev": "3551-61f14c6da920628a7b6fbecb", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "description": "Turn node's process.version into something useful.", "dist-tags": {"latest": "2.0.0"}, "license": "MIT", "maintainers": [{"name": "phated", "email": "<EMAIL>"}], "name": "parse-node-version", "readme": "<p align=\"center\">\n  <a href=\"http://gulpjs.com\">\n    <img height=\"257\" width=\"114\" src=\"https://raw.githubusercontent.com/gulpjs/artwork/master/gulp-2x.png\">\n  </a>\n</p>\n\n# parse-node-version\n\n[![NPM version][npm-image]][npm-url] [![Downloads][downloads-image]][npm-url] [![Build Status][ci-image]][ci-url] [![Coveralls Status][coveralls-image]][coveralls-url]\n\nTurn node's process.version into something useful.\n\n## Usage\n\n```js\nvar nodeVersion = require('parse-node-version')(process.version);\n\nconsole.log(\n  nodeVersion.major,\n  nodeVersion.minor,\n  nodeVersion.patch,\n  nodeVersion.pre,\n  nodeVersion.build\n);\n```\n\n## API\n\n### parseVersion(nodeVersionString)\n\nTakes a node version string (usually `process.version`) and returns an object with the `major`/`minor`/`patch` (which will all be numbers) and `pre`/`build` keys (which will always be a string). If the version doesn't contain any pre-release or build information, the properties will be returned as empty string.\n\n## License\n\nMIT\n\n<!-- prettier-ignore-start -->\n[downloads-image]: http://img.shields.io/npm/dm/parse-node-version.svg?style=flat-square\n[npm-url]: https://www.npmjs.com/package/parse-node-version\n[npm-image]: http://img.shields.io/npm/v/parse-node-version.svg?style=flat-square\n\n[ci-url]: https://github.com/gulpjs/parse-node-version/actions?query=workflow:dev\n[ci-image]: https://img.shields.io/github/workflow/status/gulpjs/parse-node-version/dev?style=flat-square\n\n[coveralls-url]: https://coveralls.io/r/gulpjs/parse-node-version\n[coveralls-image]: http://img.shields.io/coveralls/gulpjs/parse-node-version/master.svg?style=flat-square\n<!-- prettier-ignore-end -->\n", "time": {"created": "2022-01-26T13:28:13.839Z", "modified": "2024-04-05T00:02:21.161Z", "2.0.0": "2021-11-22T23:24:06.342Z", "1.0.1": "2019-01-30T02:58:43.628Z", "1.0.0": "2018-11-29T22:43:34.943Z"}, "versions": {"2.0.0": {"name": "parse-node-version", "version": "2.0.0", "description": "Turn node's process.version into something useful.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/parse-node-version.git"}, "license": "MIT", "engines": {"node": ">= 10.13.0"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.3.1", "mocha": "^8.4.0", "nyc": "^15.1.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["process.version", "node version", "version parse"], "gitHead": "425815d36bfc3360fa30bd60bfd6dc277ed72f9a", "bugs": {"url": "https://github.com/gulpjs/parse-node-version/issues"}, "homepage": "https://github.com/gulpjs/parse-node-version#readme", "_id": "parse-node-version@2.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "7.24.2", "dist": {"integrity": "sha512-/jc2J3E6IazoQF4RlwJg4E4FByHqXlcoaHVrWQvjr7LEQd8QVLBT3AO5dqZlFPIzluYUosVTLAM0t8OTEc2AdQ==", "shasum": "784ca2d72a9d450d2ffb5538edb8d03410f9d00e", "tarball": "https://registry.npmmirror.com/parse-node-version/-/parse-node-version-2.0.0.tgz", "fileCount": 4, "unpackedSize": 4262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnCaWCRA9TVsSAnZWagAAuMwP/1oPrsoARle1Ips/lMNY\noUjf21TLj11wvmKcB+ij7shrcpqJf+Rc4NvKCIVoO3uFeABTwKmWT15a1Ik+\nmMJ+l3BzC4RehJeOSurfxO/GF60Qz59X45dnE9ZXmeIFW1vc0hs6PEEMsrVP\nNhirl9CxAHBb2+ZaJqsi2l1eKqb/2VFjWjNVZ+Uo26cXIoDkr2fpYO1PNX6/\nCduSiV8YQtEoN+g98dnlt8pz2S0sCwj8xedyGkTCUbD1ft8Vg97wUsaJOFHZ\n1GCOcS7IkU2fkoLdmTcjRQiW85M5EzJrGZKr/XinsB6Bviskwbl406jBQ87B\n6c/vCGhSQx/53qeHhtafKHt1gahJl0wC97gzAxCZwF76LB17aKnphMjcNQIu\nqXjEVQ96n1JdlR2vTaWNkycRZtDdwRmFgFof181FERS+SyNvjNYoksE2XjR0\nUwbU35FJa/mfy0U0Ou82Az1ATCqW8cy91E93uBWgFAVfH1iauRlSKMqBx2JL\nQn8Sust/wfb+OBaN6oNiNf+UoR4Q6sz6X8PFZXXEzHp+AnHe6GuYREc/J9yZ\nKCzQU3ek4Y5XCDAu8XKWfdolJsP3RRJRXmNVMgWnptiZTK1bMJFCFsNkIxAk\nwk5VxUgXQW3IQazoYqT0gaW2rzK5OwqIlMx5Gl0jPun5j1RVhwtjQ5VE5szz\nQ6v9\r\n=4YE5\r\n-----END PGP SIGNATURE-----\r\n", "size": 2197, "noattachment": false}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "sttk", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parse-node-version_2.0.0_1637623446222_0.5084342366150996"}, "_hasShrinkwrap": false, "publish_time": 1637623446342, "_cnpm_publish_time": 1637623446342, "_cnpmcore_publish_time": "2021-12-13T14:25:40.599Z"}, "1.0.1": {"name": "parse-node-version", "version": "1.0.1", "description": "Turn node's process.version into something useful.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/parse-node-version.git"}, "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "mocha --async-only", "cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "keywords": ["process.version", "node version", "version parse"], "gitHead": "69cad9e2396a98e9ce3390ba451508852be7065e", "bugs": {"url": "https://github.com/gulpjs/parse-node-version/issues"}, "homepage": "https://github.com/gulpjs/parse-node-version#readme", "_id": "parse-node-version@1.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"shasum": "e2b5dbede00e7fa9bc363607f53327e8b073189b", "size": 2235, "noattachment": false, "tarball": "https://registry.npmmirror.com/parse-node-version/-/parse-node-version-1.0.1.tgz", "integrity": "sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA=="}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "sttk", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parse-node-version_1.0.1_1548817123479_0.28315296242072785"}, "_hasShrinkwrap": false, "publish_time": 1548817123628, "_cnpm_publish_time": 1548817123628, "_cnpmcore_publish_time": "2021-12-13T14:25:40.827Z"}, "1.0.0": {"name": "parse-node-version", "version": "1.0.0", "description": "Turn node's process.version into something useful.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/parse-node-version.git"}, "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "mocha --async-only", "cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "keywords": ["process.version", "node version", "version parse"], "gitHead": "29457631b4cbf8659519027fe5f85ba65abfd210", "bugs": {"url": "https://github.com/gulpjs/parse-node-version/issues"}, "homepage": "https://github.com/gulpjs/parse-node-version#readme", "_id": "parse-node-version@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"shasum": "33d9aa8920dcc3c0d33658ec18ce237009a56d53", "size": 2092, "noattachment": false, "tarball": "https://registry.npmmirror.com/parse-node-version/-/parse-node-version-1.0.0.tgz", "integrity": "sha512-02GTVHD1u0nWc20n2G7WX/PgdhNFG04j5fi1OkaJzPWLTcf6vh6229Lta1wTmXG/7Dg42tCssgkccVt7qvd8Kg=="}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "sttk", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parse-node-version_1.0.0_1543531414819_0.06446458938034505"}, "_hasShrinkwrap": false, "publish_time": 1543531414943, "_cnpm_publish_time": 1543531414943, "_cnpmcore_publish_time": "2021-12-13T14:25:41.138Z"}}, "bugs": {"url": "https://github.com/gulpjs/parse-node-version/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/gulpjs/parse-node-version#readme", "keywords": ["process.version", "node version", "version parse"], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/parse-node-version.git"}, "_source_registry_name": "default"}