{"_attachments": {}, "_id": "less", "_rev": "1227-61f14610b677e08f5113e361", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Leaner CSS", "dist-tags": {"alpha": "3.13.0-alpha.3", "beta": "3.10.0-beta.2", "canary": "3.13.1-next.1", "latest": "4.3.0"}, "license": "Apache-2.0", "maintainers": [{"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}], "name": "less", "readme": "# [Less.js](http://lesscss.org)\n\n> The **dynamic** stylesheet language. [http://lesscss.org](http://lesscss.org).\n\nThis is the JavaScript, official, stable version of Less.\n\n\n## Getting Started\n\nAdd Less.js to your project:\n```sh\nnpm install less\n```\n", "time": {"created": "2022-01-26T13:01:04.923Z", "modified": "2025-07-11T05:35:12.998Z", "4.1.2": "2021-10-03T23:18:07.340Z", "4.1.1": "2021-01-31T03:54:37.855Z", "4.1.0": "2021-01-10T02:41:06.442Z", "4.0.0": "2020-12-18T18:07:18.959Z", "3.13.1": "2020-12-18T15:51:23.184Z", "3.13.0-alpha.3": "2020-12-17T20:29:08.078Z", "3.13.0-alpha.2": "2020-12-17T20:02:51.138Z", "3.13.1-next.1": "2020-12-17T18:06:36.107Z", "3.13.0": "2020-12-12T02:05:29.995Z", "3.13.0-alpha.12": "2020-12-08T11:34:16.560Z", "3.12.1-alpha.12": "2020-12-08T11:31:31.020Z", "3.13.0-alpha.10": "2020-12-08T03:12:18.124Z", "4.0.1-alpha.2": "2020-12-05T22:32:42.757Z", "4.0.1-alpha.0": "2020-12-05T17:18:27.775Z", "3.12.1-alpha.13": "2020-12-05T17:09:30.718Z", "3.13.1-alpha.1": "2020-09-29T14:13:12.414Z", "3.12.2": "2020-07-16T16:04:53.105Z", "3.12.1": "2020-07-16T14:34:23.101Z", "3.12.0": "2020-07-13T15:18:07.371Z", "3.11.3": "2020-06-05T18:47:08.418Z", "3.11.2": "2020-06-01T01:54:27.210Z", "3.11.1": "2020-02-11T05:58:29.909Z", "3.11.0": "2020-02-09T22:05:14.114Z", "3.10.3": "2019-08-23T00:09:24.075Z", "3.10.2": "2019-08-21T04:37:25.880Z", "3.10.1": "2019-08-18T01:38:38.561Z", "3.10.0": "2019-08-17T03:04:31.616Z", "3.10.0-beta.2": "2019-08-08T04:32:10.571Z", "3.10.0-beta": "2019-08-03T18:04:12.861Z", "3.9.0": "2018-11-29T06:09:55.074Z", "3.8.1": "2018-08-08T03:47:58.773Z", "3.8.0": "2018-07-23T06:56:55.955Z", "3.7.1": "2018-07-11T22:31:55.521Z", "3.7.0": "2018-07-11T06:27:16.322Z", "3.6.0": "2018-07-10T14:22:34.816Z", "3.5.3": "2018-07-06T15:56:30.014Z", "3.5.2": "2018-07-06T02:58:54.774Z", "3.5.1": "2018-07-05T19:01:52.723Z", "3.5.0": "2018-07-05T14:35:23.334Z", "3.5.0-beta.7": "2018-07-04T07:19:00.240Z", "3.5.0-beta.6": "2018-07-03T03:27:20.181Z", "3.5.0-beta.5": "2018-07-02T03:06:21.282Z", "3.5.0-beta.4": "2018-06-30T16:38:54.083Z", "3.5.0-beta.3": "2018-06-30T07:42:24.308Z", "3.5.0-beta.2": "2018-06-27T15:37:28.967Z", "3.5.0-beta": "2018-06-25T03:17:04.532Z", "3.0.4": "2018-05-07T01:53:09.069Z", "3.0.2": "2018-04-21T18:25:44.432Z", "3.0.1": "2018-02-15T00:40:37.924Z", "3.0.0": "2018-02-11T05:42:39.191Z", "3.0.0-RC.2": "2018-02-11T01:06:56.808Z", "3.0.0-RC.1": "2018-02-04T18:15:19.366Z", "3.0.0-alpha.4": "2017-10-24T03:09:24.873Z", "2.7.3": "2017-10-24T02:43:40.462Z", "3.0.0-alpha.3": "2017-10-09T01:34:54.921Z", "3.0.0-alpha.2": "2017-01-11T01:50:04.774Z", "2.7.2": "2017-01-05T01:58:13.133Z", "3.0.0-alpha.1": "2017-01-01T21:02:55.313Z", "3.0.0-pre.4": "2016-10-21T23:40:32.107Z", "3.0.0-pre.3": "2016-07-18T21:30:25.637Z", "3.0.0-pre.2": "2016-07-14T19:29:33.859Z", "3.0.0-pre.1": "2016-07-13T19:04:21.870Z", "2.7.1": "2016-05-09T20:38:49.919Z", "2.7.0": "2016-05-08T01:35:24.988Z", "2.6.1": "2016-03-04T16:39:38.745Z", "2.6.0": "2016-01-29T21:06:12.560Z", "2.5.3": "2015-09-25T11:50:51.988Z", "2.5.2": "2015-09-24T19:23:37.098Z", "2.5.1": "2015-05-21T11:30:14.931Z", "2.5.0": "2015-04-03T08:55:25.437Z", "2.4.0": "2015-02-08T11:47:53.977Z", "2.3.1": "2015-01-28T17:32:57.582Z", "2.3.0": "2015-01-27T00:24:02.877Z", "2.2.0": "2015-01-04T11:55:39.961Z", "2.1.2": "2014-12-20T14:53:59.229Z", "2.1.1": "2014-11-27T06:17:05.282Z", "2.1.0": "2014-11-23T11:16:09.502Z", "2.0.0": "2014-11-09T14:31:21.045Z", "2.0.0-b3": "2014-11-01T17:43:51.023Z", "2.0.0-b2": "2014-10-26T12:19:34.079Z", "2.0.0-b1": "2014-10-19T20:58:35.331Z", "1.7.5": "2014-09-03T07:05:37.254Z", "1.7.4": "2014-07-27T20:05:55.367Z", "1.7.3": "2014-06-22T15:14:19.211Z", "1.7.1": "2014-06-08T16:27:25.288Z", "1.7.0": "2014-02-27T20:17:32.652Z", "1.6.3": "2014-02-08T16:53:31.138Z", "1.6.2": "2014-02-02T18:38:31.668Z", "1.6.1": "2014-01-12T12:00:41.207Z", "1.6.0": "2014-01-01T17:36:53.002Z", "1.5.1": "2013-11-17T17:11:59.226Z", "1.5.0": "2013-10-21T13:22:02.415Z", "1.5.0-b4": "2013-10-04T06:13:19.287Z", "1.5.0-b3": "2013-09-17T05:16:45.721Z", "1.5.0-b2": "2013-09-09T21:34:20.103Z", "1.5.0-b1": "2013-09-03T19:42:41.174Z", "1.4.2": "2013-07-20T20:20:54.906Z", "1.4.1": "2013-07-05T05:28:15.796Z", "1.4.0": "2013-06-05T20:19:38.635Z", "1.4.0-b4": "2013-05-04T06:36:10.635Z", "1.4.0-b3": "2013-04-30T14:43:44.435Z", "1.4.0-b2": "2013-03-18T12:28:36.352Z", "1.4.0-b1": "2013-03-08T08:18:54.802Z", "1.3.3": "2012-12-30T09:43:43.761Z", "1.3.2": "2012-12-28T22:36:23.199Z", "1.3.1": "2012-10-17T19:35:00.101Z", "1.3.0": "2012-03-10T19:29:45.907Z", "1.2.2": "2012-02-11T17:18:47.901Z", "1.2.1": "2012-01-23T10:40:28.587Z", "1.2.0": "2012-01-10T22:08:15.843Z", "1.1.6": "2011-12-10T13:20:53.491Z", "1.1.5": "2011-11-14T11:02:34.213Z", "1.1.4": "2011-07-19T23:10:19.340Z", "1.1.2": "2011-05-24T20:46:53.915Z", "1.1.1": "2011-05-18T00:54:06.341Z", "1.1.0": "2011-05-11T19:49:37.162Z", "1.0.44": "2011-05-10T14:05:51.397Z", "1.0.5": "2011-01-23T22:28:07.816Z", "1.0.41": "2011-01-23T22:28:07.816Z", "1.0.40": "2011-01-23T22:28:07.816Z", "1.0.14": "2011-01-23T22:28:07.815Z", "1.0.36": "2011-01-23T22:28:07.815Z", "1.0.32": "2011-01-23T22:28:07.815Z", "1.0.21": "2011-01-23T22:28:07.815Z", "1.0.19": "2011-01-23T22:28:07.815Z", "1.0.18": "2011-01-23T22:28:07.815Z", "1.0.11": "2011-01-23T22:28:07.815Z", "1.0.10": "2011-01-23T22:28:07.815Z", "4.1.3": "2022-06-08T19:30:07.532Z", "4.2.0": "2023-08-05T17:18:15.731Z", "4.2.1": "2024-11-25T17:06:25.428Z", "4.2.2": "2025-01-19T18:46:59.651Z", "4.3.0": "2025-04-05T14:52:22.026Z"}, "versions": {"4.1.2": {"name": "less", "version": "4.1.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.1.0", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^9.1.1", "typescript": "^4.3.4", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}, "_id": "less@4.1.2", "_nodeVersion": "12.22.4", "_npmVersion": "6.14.14", "dist": {"shasum": "6099ee584999750c2624b65f80145f8674e4b4b0", "size": 628179, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-4.1.2.tgz", "integrity": "sha512-EoQp/Et7OSOVu0aJknJOtlXZsnr8XE8KwuzTHOLeVSEx8pVWUICc8Q0VYRHgzyjX78nMEyC/oztWFbgyhtNfDA=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.1.2_1633303087124_0.3487690614287575"}, "_hasShrinkwrap": false, "publish_time": 1633303087340, "_cnpm_publish_time": 1633303087340, "_cnpmcore_publish_time": "2021-12-13T13:27:44.495Z"}, "4.1.1": {"name": "less", "version": "4.1.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.1.0", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.34.1", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^4.1.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}, "_id": "less@4.1.1", "_nodeVersion": "10.22.0", "_npmVersion": "6.14.6", "dist": {"shasum": "15bf253a9939791dc690888c3ff424f3e6c7edba", "size": 627675, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-4.1.1.tgz", "integrity": "sha512-w09o8tZFPThBscl5d0Ggp3RcrKIouBoQscnOMgFH3n5V3kN/CXGHNfCkRPtxJk6nKryDXaV9aHLK55RXuH4sAw=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.1.1_1612065277561_0.8750537617279655"}, "_hasShrinkwrap": false, "publish_time": 1612065277855, "_cnpm_publish_time": 1612065277855, "_cnpmcore_publish_time": "2021-12-13T13:27:44.991Z"}, "4.1.0": {"name": "less", "version": "4.1.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.1.0", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.34.1", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^4.1.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}, "_id": "less@4.1.0", "_nodeVersion": "10.22.0", "_npmVersion": "6.14.6", "dist": {"shasum": "a12708d1951239db1c9d7eaa405f1ebac9a75b8d", "size": 627549, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-4.1.0.tgz", "integrity": "sha512-w1Ag/f34g7LwtQ/sMVSGWIyZx+gG9ZOAEtyxeX1fG75is6BMyC2lD5kG+1RueX7PkAvlQBm2Lf2aN2j0JbVr2A=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.1.0_1610246466282_0.38356357054671464"}, "_hasShrinkwrap": false, "publish_time": 1610246466442, "_cnpm_publish_time": 1610246466442, "_cnpmcore_publish_time": "2021-12-13T13:27:45.474Z"}, "4.0.0": {"name": "less", "version": "4.0.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.0.0", "@less/test-import-module": "^4.0.0", "@rollup/plugin-json": "^4.1.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "_id": "less@4.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "d238cc25576c1f722794dbca4ac82e5e3c7e9e65", "size": 626947, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-4.0.0.tgz", "integrity": "sha512-av1eEa2D0xZfF7fjLJS/Dld7zAYSLU7EOEJvuOELeaNI3i6L/81AdjbK5/pytaRkBwi7ZEa0433IDvMLskKCOw=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.0.0_1608314838795_0.20071932900747735"}, "_hasShrinkwrap": false, "publish_time": 1608314838959, "_cnpm_publish_time": 1608314838959, "_cnpmcore_publish_time": "2021-12-13T13:27:45.941Z"}, "3.13.1": {"name": "less", "version": "3.13.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "_id": "less@3.13.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "0ebc91d2a0e9c0c6735b83d496b0ab0583077909", "size": 627828, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.1.tgz", "integrity": "sha512-SwA1aQXGUvp+P5XdZslUOhhLnClSLIjWvJhmd+Vgib5BFIr9lMNlQwmwUNOjXThF/A0x+MCYYPeWEfeWiLRnTw=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.1_1608306682952_0.6268203311751019"}, "_hasShrinkwrap": false, "publish_time": 1608306683184, "_cnpm_publish_time": 1608306683184, "_cnpmcore_publish_time": "2021-12-13T13:27:46.342Z"}, "3.13.0-alpha.3": {"name": "less", "version": "3.13.0-alpha.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "_id": "less@3.13.0-alpha.3", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "10e82d5e58342313e9ffa0712fea734a59eb2d61", "size": 627851, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.0-alpha.3.tgz", "integrity": "sha512-WKf/hVhP0qPtUc+v6AA5kCAHNPLPx/19p4p+xHWCBduYAldDJbzAVUyYlvsWUZ4o/RFjLy99/0Rz4hnuINAEaQ=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.0-alpha.3_1608236947819_0.9201976158861755"}, "_hasShrinkwrap": false, "publish_time": 1608236948078, "_cnpm_publish_time": 1608236948078, "_cnpmcore_publish_time": "2021-12-13T13:27:46.849Z"}, "3.13.0-alpha.2": {"name": "less", "version": "3.13.0-alpha.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "_id": "less@3.13.0-alpha.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "0f88792ac26fd1efa3c9942d20efa8fbf1e2b6f1", "size": 627848, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.0-alpha.2.tgz", "integrity": "sha512-mjoxQ2qF0wXA7QXeqYDA29qvOakrDOy6owAauq+CWgjsHzhLdoGpIHLnCiz9+28737jInf4H+hh63BdJHQ6g7g=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.0-alpha.2_1608235370918_0.4620683385668176"}, "_hasShrinkwrap": false, "publish_time": 1608235371138, "_cnpm_publish_time": 1608235371138, "_cnpmcore_publish_time": "2021-12-13T13:27:47.292Z"}, "3.13.1-next.1": {"name": "less", "version": "3.13.1-next.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "458cf99cfe6fb3fab57f2998729b0d1a8dec358a", "readmeFilename": "README.md", "_id": "less@3.13.1-next.1", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "96366adc2daa774d75325743aa0124e936074502", "size": 631300, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.1-next.1.tgz", "integrity": "sha512-vfwpjNVbump4H6MshQw67GwoeU7DBibULeha7MZRqObeXSAmIp/q6nV+eRTH7OCOtmQc5zCUwZcwbmPytHYuJA=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.1-next.1_1608228395910_0.8294547584594298"}, "_hasShrinkwrap": false, "publish_time": 1608228396107, "_cnpm_publish_time": 1608228396107, "_cnpmcore_publish_time": "2021-12-13T13:27:47.815Z"}, "3.13.0": {"name": "less", "version": "3.13.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "_id": "less@3.13.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "6a47bb19d97edcf7a53d444b099275dd6b17c85a", "size": 623515, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.0.tgz", "integrity": "sha512-uPhr9uoSGVKKYVGz0rXcYBK1zjwcIWRGcbnSgNt66XuIZYrYPaQiS+LeUOvqedBwrwdBYYaLqSff5ytGYuT7rA=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.0_1607738729784_0.233459613710034"}, "_hasShrinkwrap": false, "publish_time": 1607738729995, "_cnpm_publish_time": 1607738729995, "_cnpmcore_publish_time": "2021-12-13T13:27:48.313Z"}, "3.13.0-alpha.12": {"name": "less", "version": "3.13.0-alpha.12", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "e8d05c610f6e15b0ae9d49d662767a609236fd49", "readmeFilename": "README.md", "_id": "less@3.13.0-alpha.12", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "9a82c89c83ff8519bbe419ec8d60d610e8fb8941", "size": 627253, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.0-alpha.12.tgz", "integrity": "sha512-JB5p7lJcbeKUXZSvxr5Oy18ErsyAO1/B44SsOxXJ0Hg64NCWu1EaABBa70a32PmuU+iSmTnzW91VtWKp7BHrdA=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.0-alpha.12_1607427256331_0.7474565520657184"}, "_hasShrinkwrap": false, "publish_time": 1607427256560, "_cnpm_publish_time": 1607427256560, "_cnpmcore_publish_time": "2021-12-13T13:27:48.904Z"}, "3.12.1-alpha.12": {"name": "less", "version": "3.12.1-alpha.12", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "e8d05c610f6e15b0ae9d49d662767a609236fd49", "readmeFilename": "README.md", "_id": "less@3.12.1-alpha.12", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "9690f49b9622de2200ae22def120a3fa17c8e429", "size": 627250, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.12.1-alpha.12.tgz", "integrity": "sha512-VgGSqggObTC8QeTF4Puv/u/gAk3iDV59GvsrRItqOG7efYZ6mMygVMJ5I56zQPUeDxgjnwkvPvTGztWiiZw67A=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.12.1-alpha.12_1607427090783_0.3620052622266128"}, "_hasShrinkwrap": false, "publish_time": 1607427091020, "_cnpm_publish_time": 1607427091020, "_cnpmcore_publish_time": "2021-12-13T13:27:49.506Z"}, "3.13.0-alpha.10": {"name": "less", "version": "3.13.0-alpha.10", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "b1390a54589f7addae08550288f775eeb18f7061", "readmeFilename": "README.md", "_id": "less@3.13.0-alpha.10", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "c52f49e3255bfbc860282460bda99a450773608d", "size": 626031, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.0-alpha.10.tgz", "integrity": "sha512-Ia74kTKKD+8FWHpCYd+YNJjr+4f/lFF1lHi9yvSDHG3OX2heArM80XfqFajBAgDE2wLm3YCWp37PSOuZOYHwBg=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.0-alpha.10_1607397137833_0.15432987333835269"}, "_hasShrinkwrap": false, "publish_time": 1607397138124, "_cnpm_publish_time": 1607397138124, "_cnpmcore_publish_time": "2021-12-13T13:27:50.119Z"}, "4.0.1-alpha.2": {"name": "less", "version": "4.0.1-alpha.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "b2049010b387ae69abf4e656a415b1affb2e89d7", "readmeFilename": "README.md", "_id": "less@4.0.1-alpha.2", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "b689a8f16e9837e1f91fbfcc053f9d579ce5b071", "size": 623673, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-4.0.1-alpha.2.tgz", "integrity": "sha512-2Tj5SJh1k5rySBiblxP2Ck3wxBHUoSLFffwLP4R6GqLg12clj9K/hu+4RCCWkFcn/kY0mGL8OoPOUzRqqpf/oQ=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.0.1-alpha.2_1607207562604_0.7129609031544031"}, "_hasShrinkwrap": false, "publish_time": 1607207562757, "_cnpm_publish_time": 1607207562757, "_cnpmcore_publish_time": "2021-12-13T13:27:50.718Z"}, "4.0.1-alpha.0": {"name": "less", "version": "4.0.1-alpha.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "70e242c5bdb2245696f7ca63845b50a8d00cf5ac", "readmeFilename": "README.md", "_id": "less@4.0.1-alpha.0", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "c2059a13ea0ceb0a8e36633fd6a97528e7dded2a", "size": 633012, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-4.0.1-alpha.0.tgz", "integrity": "sha512-2gH5xvy68Ex0AKXl7BVgu0YniuS9rvUcZeo9CgvnPhcmmEc+pzqds+VgnoqDrm5v2auw6VYWkKXslkPNqUU11w=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.0.1-alpha.0_1607188707535_0.8437988316839111"}, "_hasShrinkwrap": false, "publish_time": 1607188707775, "_cnpm_publish_time": 1607188707775, "_cnpmcore_publish_time": "2021-12-13T13:27:51.297Z"}, "3.12.1-alpha.13": {"name": "less", "version": "3.12.1-alpha.13", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.12.2", "@less/test-import-module": "^3.12.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "ed7340982be9404298c8713cf00f12b8a5ae78c3", "readmeFilename": "README.md", "_id": "less@3.12.1-alpha.13", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "6944168e8dc342e12c0928be542c2c89a49f0fdd", "size": 633101, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.12.1-alpha.13.tgz", "integrity": "sha512-wU8nac1ehDa7KoUbZzozaiqM5tOfOXkyz94nrfIg9EBBMl8Wi6DcM1N+vXP22bLMa8VnMMuIEJROL8a+ME38ug=="}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.12.1-alpha.13_1607188170471_0.5025158099554847"}, "_hasShrinkwrap": false, "publish_time": 1607188170718, "_cnpm_publish_time": 1607188170718, "_cnpmcore_publish_time": "2021-12-13T13:27:51.890Z"}, "3.13.1-alpha.1": {"name": "less", "version": "3.13.1-alpha.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.13.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.27.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^2.0.1", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "84d40222b65701cbda0d5a4c218c6fb35f0ac368", "readmeFilename": "README.md", "_id": "less@3.13.1-alpha.1", "_nodeVersion": "10.22.0", "_npmVersion": "lerna/3.22.1/node@v10.22.0+x64 (darwin)", "dist": {"shasum": "1bea2e7f58434befe1b6ca43244e7a467c3222cd", "size": 628526, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.13.1-alpha.1.tgz", "integrity": "sha512-SNMq5YVUnl/AlztL1bfqH8EuZISImHzMEXSPk4cktstoBBdZ5M34Kbog6mW9/kKasvqn5pfVOmPx5kPmoz4SaQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.13.1-alpha.1_1601388792217_0.5467929505178257"}, "_hasShrinkwrap": false, "publish_time": 1601388792414, "_cnpm_publish_time": 1601388792414, "_cnpmcore_publish_time": "2021-12-13T13:27:52.490Z"}, "3.12.2": {"name": "less", "version": "3.12.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.12.2", "@less/test-import-module": "^3.12.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "185762a999519708fd914c9fad99b61720f34281", "_id": "less@3.12.2", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "157e6dd32a68869df8859314ad38e70211af3ab4", "size": 630937, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.12.2.tgz", "integrity": "sha512-+1V2PCMFkL+OIj2/HrtrvZw0BC0sYLMICJfbQjuj/K8CEnlrFX6R5cKKgzzttsZDHyxQNL1jqMREjKN3ja/E3Q=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.12.2_1594915492912_0.5597479664300238"}, "_hasShrinkwrap": false, "publish_time": 1594915493105, "_cnpm_publish_time": 1594915493105, "_cnpmcore_publish_time": "2021-12-13T13:27:53.234Z"}, "3.12.1": {"name": "less", "version": "3.12.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.12.1", "@less/test-import-module": "^3.12.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "707e97f69c422ab6550b1184ecf76d862d6a431a", "_id": "less@3.12.1", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "833a628d60a5dbed3865c1b42d9d8aaeeb8d8c2a", "size": 630934, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.12.1.tgz", "integrity": "sha512-lI1s2TO4BbMwS/S45/V7K0UuKlu87ie6stg58b0PTPD7x9MDXWwyOp+xQORin2lyYwn/HX7r2o9mjPJ4rYvUIA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.12.1_1594910062830_0.09756961425021715"}, "_hasShrinkwrap": false, "publish_time": 1594910063101, "_cnpm_publish_time": 1594910063101, "_cnpmcore_publish_time": "2021-12-13T13:27:53.937Z"}, "3.12.0": {"name": "less", "version": "3.12.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^3.12.0", "@less/test-import-module": "^3.12.0", "@typescript-eslint/eslint-plugin": "^3.3.0", "@typescript-eslint/parser": "^3.3.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "copy-anything": "^2.0.1", "diff": "^3.2.0", "eslint": "^6.8.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}, "gitHead": "e4f755112198b758a3b22252a9f4f290b4f81df9", "_id": "less@3.12.0", "_nodeVersion": "10.16.3", "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "dist": {"shasum": "f23f9bd94ba72495994865d84e3b5ac6ee8a0363", "size": 630291, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.12.0.tgz", "integrity": "sha512-3mmSHFRP9hGxxQgAKgChfau1LO3ksV/zyZf1qd2ENyBV778NA9Ids99wFRA20jE+5prT7oScKod8PoGlxSe1gg=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.12.0_1594653487178_0.4668049871251829"}, "_hasShrinkwrap": false, "publish_time": 1594653487371, "_cnpm_publish_time": 1594653487371, "_cnpmcore_publish_time": "2021-12-13T13:27:54.629Z"}, "3.11.3": {"name": "less", "version": "3.11.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "changelog": "github-changes -o less -r less.js -a --only-pulls --use-commit-body -m \"(YYYY-MM-DD)\""}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.3.3", "@typescript-eslint/parser": "^2.3.3", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "github-changes": "^1.1.2", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "6238bbcd3b7c23ac095d90a7a0baaaeccc10b8b1", "_id": "less@3.11.3", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "2d853954fcfe0169a8af869620bcaa16563dcc1c", "size": 849192, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.11.3.tgz", "integrity": "sha512-VkZiTDdtNEzXA3LgjQiC3D7/ejleBPFVvq+aRI9mIj+Zhmif5TvFPM244bT4rzkvOCvJ9q4zAztok1M7Nygagw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.11.3_1591382828135_0.6363315447946214"}, "_hasShrinkwrap": false, "publish_time": 1591382828418, "_cnpm_publish_time": 1591382828418, "_cnpmcore_publish_time": "2021-12-13T13:27:55.343Z"}, "3.11.2": {"name": "less", "version": "3.11.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "changelog": "github-changes -o less -r less.js -a --only-pulls --use-commit-body -m \"(YYYY-MM-DD)\""}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.3.3", "@typescript-eslint/parser": "^2.3.3", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "github-changes": "^1.1.2", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "53bf8771305fc7228991b3847eefaa1b9f7f63fa", "_id": "less@3.11.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "51a484e9017287f5ac3db921cb86970eb7506e81", "size": 947564, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.11.2.tgz", "integrity": "sha512-ed8Lir98Tu6a+LeU7+8ShpRLSUdk//lWf1sh+5w7tNju4wGItztqDHp03Z+a2o1nzU6pObVxw1n4Gu7VzQYusQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.11.2_1590976466852_0.4255448140274478"}, "_hasShrinkwrap": false, "publish_time": 1590976467210, "_cnpm_publish_time": 1590976467210, "_cnpmcore_publish_time": "2021-12-13T13:27:56.075Z"}, "3.11.1": {"name": "less", "version": "3.11.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.3.3", "@typescript-eslint/parser": "^2.3.3", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "tslib": "^1.10.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "842386b8db5cb5afa4edf5a4c81d40bcbf47a6a2", "_id": "less@3.11.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "c6bf08e39e02404fe6b307a3dfffafdc55bd36e2", "size": 2868338, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.11.1.tgz", "integrity": "sha512-tlWX341RECuTOvoDIvtFqXsKj072hm3+9ymRBe76/mD6O5ZZecnlAOVDlWAleF2+aohFrxNidXhv2773f6kY7g=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.11.1_1581400709637_0.8072737893063786"}, "_hasShrinkwrap": false, "publish_time": 1581400709909, "_cnpm_publish_time": 1581400709909, "_cnpmcore_publish_time": "2021-12-13T13:27:56.931Z"}, "3.11.0": {"name": "less", "version": "3.11.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.3.3", "@typescript-eslint/parser": "^2.3.3", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "1adaadb0fac1104296da297a2cfc1b91e5e9f7c2", "_id": "less@3.11.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "ce3b6010d4ecf00710d5a68915207de7541b4d73", "size": 2869501, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.11.0.tgz", "integrity": "sha512-dAui5qzfxuWY7BIEt9/gy5EbDhwDb44rqaIUFYeu8wEE8huMZ/PzB+gNFONEG5DUPrOrOGcAjGeYVg6AFiA9KQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.11.0_1581285913870_0.890537362225144"}, "_hasShrinkwrap": false, "publish_time": 1581285914114, "_cnpm_publish_time": 1581285914114, "_cnpmcore_publish_time": "2021-12-13T13:27:57.753Z"}, "3.10.3": {"name": "less", "version": "3.10.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/preset-env": "^7.5.5", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "semver": "^6.3.0", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "66a839de9fd28e5192591a6299fb92578f9145f2", "_id": "less@3.10.3", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "417a0975d5eeecc52cff4bcfa3c09d35781e6792", "size": 1065316, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.10.3.tgz", "integrity": "sha512-vz32vqfgmoxF1h3K4J+yKCtajH0PWmjkIFgbs5d78E/c/e+UQTnI+lWK+1eQRE95PXM2mC3rJlLSSP9VQHnaow=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.10.3_1566518963892_0.1967870715901745"}, "_hasShrinkwrap": false, "publish_time": 1566518964075, "_cnpm_publish_time": 1566518964075, "_cnpmcore_publish_time": "2021-12-13T13:27:58.436Z"}, "3.10.2": {"name": "less", "version": "3.10.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/preset-env": "^7.5.5", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "semver": "^6.3.0", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "f454ab78c476f9478732003a9232627303341f9a", "_id": "less@3.10.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "49b94da22e18ed112d9500bdb2340969dafee57f", "size": 1062707, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.10.2.tgz", "integrity": "sha512-crOb5r8AnIYkWss6sJNccgb20UqvDfdZhopuDG9LGfcSQY+fAKIA/W4as5+ELnfUxnDruqC7iYyeDZmC44nMnw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.10.2_1566362245652_0.8840894877628445"}, "_hasShrinkwrap": false, "publish_time": 1566362245880, "_cnpm_publish_time": 1566362245880, "_cnpmcore_publish_time": "2021-12-13T13:27:59.178Z"}, "3.10.1": {"name": "less", "version": "3.10.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/preset-env": "^7.5.5", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "semver": "^6.3.0", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "62d9d60dcb32338ec0f192b464f46369eddf8c30", "_id": "less@3.10.1", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"shasum": "a1c97e3436acdfd1757916333b9906f3af7d559e", "size": 1054633, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.10.1.tgz", "integrity": "sha512-xlPx6vt5nAm1DGDnuv06SNQba4WnMKY5vZkgCrnyKXkDbs09kl9cc2nSS7OMufLT60IWsxUSnwr1Rkh4rkMFcg=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.10.1_1566092318308_0.43145836454336894"}, "_hasShrinkwrap": false, "publish_time": 1566092318561, "_cnpm_publish_time": 1566092318561, "_cnpmcore_publish_time": "2021-12-13T13:27:59.952Z"}, "3.10.0": {"name": "less", "version": "3.10.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "200169471231550d26e895594ac694ac5dc1a366", "_id": "less@3.9.0", "_npmVersion": "6.2.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "b7511c43f37cf57dc87dffd9883ec121289b1474", "size": 671112, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.9.0.tgz", "integrity": "sha512-31CmtPEZraNUtuUREYjSqRkeETFdyEHSEPAGq4erDlUXtda7pzNmctdljdIagSb589d/qXGWiiP31R5JVf+v0w=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.9.0_1543471794655_0.9449090389427963"}, "_hasShrinkwrap": false, "publish_time": 1543471795074, "_cnpm_publish_time": 1543471795074, "_cnpmcore_publish_time": "2021-12-13T13:28:02.817Z", "deprecated": "[WARNING] Use 3.9.0 instead of 3.10.0, reason: https://github.com/less/less.js/issues/3414"}, "3.10.0-beta.2": {"name": "less", "version": "3.10.0-beta.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/preset-env": "^7.5.5", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "semver": "^6.3.0", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "c0f5e976967db742b3ccb064cd6db00466cbfc40", "_id": "less@3.10.0-beta.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"shasum": "6fc369097a2d8c018200d967b33f1f463a8f71e3", "size": 758733, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.10.0-beta.2.tgz", "integrity": "sha512-cTaPFTRO+c1pXtegrDkmdGQs9owJoQRfosXptArMGNfpPqMdzo6D5bk5m7pnzzI1zQwsNxYWtVHJuPjZ+9F7pw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.10.0-beta.2_1565238730373_0.23872111156958975"}, "_hasShrinkwrap": false, "publish_time": 1565238730571, "_cnpm_publish_time": 1565238730571, "_cnpmcore_publish_time": "2021-12-13T13:28:01.355Z"}, "3.10.0-beta": {"name": "less", "version": "3.10.0-beta", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "f077e2116877585336d138718566184af2afca59", "_id": "less@3.10.0-beta", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"shasum": "61f40684a4de93ca6ac4688ba3f003383690f062", "size": 928146, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.10.0-beta.tgz", "integrity": "sha512-RtKJIQ8Xxn89JKzUtPGnA9CdE3kPSHkjvX5CWNSrx3IfR9YFqCZ9cEOJzSuXPAIofo4/ufnkcC3xfYtVCUSI8A=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.10.0-beta_1564855452254_0.8433328877698438"}, "_hasShrinkwrap": false, "publish_time": 1564855452861, "_cnpm_publish_time": 1564855452861, "_cnpmcore_publish_time": "2021-12-13T13:28:02.093Z"}, "3.9.0": {"name": "less", "version": "3.9.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "200169471231550d26e895594ac694ac5dc1a366", "_id": "less@3.9.0", "_npmVersion": "6.2.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "b7511c43f37cf57dc87dffd9883ec121289b1474", "size": 671112, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.9.0.tgz", "integrity": "sha512-31CmtPEZraNUtuUREYjSqRkeETFdyEHSEPAGq4erDlUXtda7pzNmctdljdIagSb589d/qXGWiiP31R5JVf+v0w=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.9.0_1543471794655_0.9449090389427963"}, "_hasShrinkwrap": false, "publish_time": 1543471795074, "_cnpm_publish_time": 1543471795074, "_cnpmcore_publish_time": "2021-12-13T13:28:02.817Z"}, "3.8.1": {"name": "less", "version": "3.8.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap": "3.3.7", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-polyfill-object-assign": "0.0.2", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "52304860c7933de3f219325cbc8ebc112850e4be", "_id": "less@3.8.1", "_npmVersion": "6.2.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "f31758598ef5a1930dd4caefa9e4340641e71e1d", "size": 720421, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.8.1.tgz", "integrity": "sha512-8HFGuWmL3FhQR0aH89escFNBQH/nEiYPP2ltDFdQw2chE28Yx2E3lhAIq9Y2saYwLSwa699s4dBVEfCY8Drf7Q=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.8.1_1533700078620_0.45030875084988575"}, "_hasShrinkwrap": false, "publish_time": 1533700078773, "_cnpm_publish_time": 1533700078773, "_cnpmcore_publish_time": "2021-12-13T13:28:03.617Z"}, "3.8.0": {"name": "less", "version": "3.8.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap": "3.3.7", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "59e919b3fc968a403405e39cf15237936b1a6b46", "_id": "less@3.8.0", "_npmVersion": "6.2.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "44785e40c23841c15ba3be741d36bd2775dd0596", "size": 681636, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.8.0.tgz", "integrity": "sha512-746DPDyL+Wsjo7h/Z3t+A3Mg/mpDTaxW4puZyLhCQJjWJJvHggN735orjuCLIYgo7jKqv1zWLiQrxkuUOg5oGA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.8.0_1532329015823_0.35875916577753886"}, "_hasShrinkwrap": false, "publish_time": 1532329015955, "_cnpm_publish_time": 1532329015955, "_cnpmcore_publish_time": "2021-12-13T13:28:04.560Z"}, "3.7.1": {"name": "less", "version": "3.7.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap": "3.3.7", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "c9772a0f0549dcff88c0fb2687ff60a04b7d2d65", "_id": "less@3.7.1", "_npmVersion": "5.8.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "192e9dcef456ba3181a4e8d78a200f72a75e5c30", "size": 678711, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.7.1.tgz", "integrity": "sha512-Cmf5XJlzNklkBC8eAa+Ef16AHUBAkApHNAw3x9Vmn84h2BvGrri5Id7kf6H1n6SN74Fc0WdHIRPlFMxsl0eJkA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.7.1_1531348315355_0.02260317338704443"}, "_hasShrinkwrap": false, "publish_time": 1531348315521, "_cnpm_publish_time": 1531348315521, "_cnpmcore_publish_time": "2021-12-13T13:28:05.450Z"}, "3.7.0": {"name": "less", "version": "3.7.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap": "3.3.7", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "3b5dfbbb8d19b60f50901d90d1e13976bfa90431", "_id": "less@3.7.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "dc0429b4595a14a4cf7e7efd33f7378fe3edb614", "size": 679132, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.7.0.tgz", "integrity": "sha512-U<PERSON><PERSON><PERSON>UjkUjKmieLDb7o7GiMdD7xtBte2Ix0wGcvuVcHjm8z59CWvczoiUxRF5D1l3PqO9kYlvVHMx6eOLnQWTjQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.7.0_1531290436116_0.08841739397873294"}, "_hasShrinkwrap": false, "publish_time": 1531290436322, "_cnpm_publish_time": 1531290436322, "_cnpmcore_publish_time": "2021-12-13T13:28:06.285Z"}, "3.6.0": {"name": "less", "version": "3.6.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"bootstrap": "3.3.7", "bootstrap-less-port": "0.3.0", "diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "36ec7b85a675286d65d3b09c2a8d50b042b807ca", "_id": "less@3.6.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "6a079e954256a3bebf33ab00d25fa39409005701", "size": 670066, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.6.0.tgz", "integrity": "sha512-9jCc6kgJ36E2EjZrx+V+UXUTOb4JgFO5l7y9VetoRUtoaDIS4+yJ0XML9Fdr006zE9ZgHIk7tdO+SMa0PLY0mQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.6.0_1531232554685_0.8307881901963441"}, "_hasShrinkwrap": false, "publish_time": 1531232554816, "_cnpm_publish_time": 1531232554816, "_cnpmcore_publish_time": "2021-12-13T13:28:07.211Z"}, "3.5.3": {"name": "less", "version": "3.5.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "91c2ddb2ffbaebdf799f2be8132b1aa2cf6badd5", "_id": "less@3.5.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "108f656b5ee09a281b757a603a80aa091acf6384", "size": 603441, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.3.tgz", "integrity": "sha512-MMGXfVYMORHyfwWGOP04HNnDxX874A329FMZEiMuho+QvOXJMyjlsmnf3qUxr1KmwM1E8mVo2bOPbT4A8aF61w=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.3_1530892589897_0.9230916356257355"}, "_hasShrinkwrap": false, "publish_time": 1530892590014, "_cnpm_publish_time": 1530892590014, "_cnpmcore_publish_time": "2021-12-13T13:28:08.181Z"}, "3.5.2": {"name": "less", "version": "3.5.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "c20e8ce51a1b792069b81a8478e7b442162f5831", "_id": "less@3.5.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "bf33feaaf75e0f6448c5672ce024d33e18d54e85", "size": 603390, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.2.tgz", "integrity": "sha512-KCFAmfjIxcmsf7JJuxki4XEJDRg2d08a1VnJa3CTKYjIyd90wcZmPc7NTQPgfSCvGKT3t5KW68HVOrQ8numg3A=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.2_1530845934690_0.9874940802427912"}, "_hasShrinkwrap": false, "publish_time": 1530845934774, "_cnpm_publish_time": 1530845934774, "_cnpmcore_publish_time": "2021-12-13T13:28:09.129Z"}, "3.5.1": {"name": "less", "version": "3.5.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "1bba66b52bdee09a75e215faac7c7ecf49341d0b", "_id": "less@3.5.1", "_npmVersion": "5.8.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "3158ea5ef6cfcd6c6c3e2f584454a24033402a7d", "size": 605017, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.1.tgz", "integrity": "sha512-QkAcQ8rrIUn6LQajUsroVP3H6WWp7gmUXdCZi5HdSzv0VyGp815epPB5spJOf+sNYDyynbs45+hAmekdkJ+jTA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.1_1530817312515_0.3010938420010463"}, "_hasShrinkwrap": false, "publish_time": 1530817312723, "_cnpm_publish_time": 1530817312723, "_cnpmcore_publish_time": "2021-12-13T13:28:10.105Z"}, "3.5.0": {"name": "less", "version": "3.5.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "21ea0547ecf85edb349834010acf51aae3a852c2", "_id": "less@3.5.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "86fba7899d00d2e35a78c9f911e177d368d85cae", "size": 602713, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0.tgz", "integrity": "sha512-fLbJGxtoCV2FTDGeXlUXXUZ8sgMej2c5u8om8inOZt5M3tzXqF/aS7W0txWhCN6iA6qPtPGGzj0sp/vd8ztl6Q=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0_1530801322824_0.34111388266365994"}, "_hasShrinkwrap": false, "publish_time": 1530801323334, "_cnpm_publish_time": 1530801323334, "_cnpmcore_publish_time": "2021-12-13T13:28:11.151Z"}, "3.5.0-beta.7": {"name": "less", "version": "3.5.0-beta.7", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "b56ed27f0d1fe486bd9240501775f61b8a2a85f6", "_id": "less@3.5.0-beta.7", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "1886d9266713c452f4f8e71aa23bb1c77beeef3a", "size": 602692, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.7.tgz", "integrity": "sha512-vVN6DRe/o7g7ET2ekaLp6s6bLqX+pHgqE/12GRYttD/zWuUuiOmfiNHSYso+dYArDPL4XO0wRiaU6lDQPtoxAQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta.7_1530688740125_0.14971037732791048"}, "_hasShrinkwrap": false, "publish_time": 1530688740240, "_cnpm_publish_time": 1530688740240, "_cnpmcore_publish_time": "2021-12-13T13:28:12.183Z"}, "3.5.0-beta.6": {"name": "less", "version": "3.5.0-beta.6", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "d821a3754fdc5d8b338877329e74778f6457677a", "_id": "less@3.5.0-beta.6", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "e4802eee4e211d1ff54cef76027951c3b80f4e37", "size": 505832, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.6.tgz", "integrity": "sha512-cWuGB3NUnHB3O3XvEOm1xff+cYXkbAjrvrv0cpd5NATQ4kDW1X4vAECy2tPgysIw9YuO/y8aOv+2222baV0cCA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta.6_1530588440012_0.31560051683289636"}, "_hasShrinkwrap": false, "publish_time": 1530588440181, "_cnpm_publish_time": 1530588440181, "_cnpmcore_publish_time": "2021-12-13T13:28:13.102Z"}, "3.5.0-beta.5": {"name": "less", "version": "3.5.0-beta.5", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.16", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "29468bffcd8a9f2f58b1a0f46c131129b7dfaace", "_id": "less@3.5.0-beta.5", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "7f6a45bcd56e440f6d02664e4d4c763c3f5e4aee", "size": 601159, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.5.tgz", "integrity": "sha512-gPA42UHuIGuJeyXJ2IKatqG8rbuEty+UdEoO20/2fvgfp/wEJmUKHmNWBxVm+wxPDMZfQAcJECKiU1MSCC1itw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta.5_1530500781183_0.02010831732484064"}, "_hasShrinkwrap": false, "publish_time": 1530500781282, "_cnpm_publish_time": 1530500781282, "_cnpmcore_publish_time": "2021-12-13T13:28:14.238Z"}, "3.5.0-beta.4": {"name": "less", "version": "3.5.0-beta.4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "f11f8b077ce2ae0ff1e250d6b3101ac6d8178055", "_id": "less@3.5.0-beta.4", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "0b821a02041b7b2aef1d51f65f2b778e4d1ebaa3", "size": 596541, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.4.tgz", "integrity": "sha512-VQRyWHvP18YrN+cHaV7YJmpGVLPFW51s03j40zj8sHJXotiuY5uxLQEuVIPMEvS8A0h+ASqowrZB8l6hSPJVRg=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta.4_1530376733946_0.8432385540243785"}, "_hasShrinkwrap": false, "publish_time": 1530376734083, "_cnpm_publish_time": 1530376734083, "_cnpmcore_publish_time": "2021-12-13T13:28:15.252Z"}, "3.5.0-beta.3": {"name": "less", "version": "3.5.0-beta.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "b94f041c65401630229fa0b6a480dca78f89e48d", "_id": "less@3.5.0-beta.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "b4bbc565682a9e68dbf4fae5687901079f069335", "size": 590411, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.3.tgz", "integrity": "sha512-Ce16Uvg9esBALgNq3xVBMQNVtpPTG/NSwgvVAe7sS3J9OCctPl0Jb6GON8hZfLMbc75hEk7JTU1XV9Nn1vb0Rg=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta.3_1530344544207_0.8359483293443228"}, "_hasShrinkwrap": false, "publish_time": 1530344544308, "_cnpm_publish_time": 1530344544308, "_cnpmcore_publish_time": "2021-12-13T13:28:16.304Z"}, "3.5.0-beta.2": {"name": "less", "version": "3.5.0-beta.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "b968e66f5f5bda234307476987abcd6010b18696", "_id": "less@3.5.0-beta.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "21aa7939ffe9807e4af6e6dd2283fb72c50552bf", "size": 589160, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.2.tgz", "integrity": "sha512-4/DGURGCoqi477ppMLLQrMQnJUAUKdLiBjf2s2qPkHxHr7Le00R8baNDfqenmxyaZSNuGhlR1votJ2yGwYHVbw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta.2_1530113848865_0.4415451113432569"}, "_hasShrinkwrap": false, "publish_time": 1530113848967, "_cnpm_publish_time": 1530113848967, "_cnpmcore_publish_time": "2021-12-13T13:28:17.326Z"}, "3.5.0-beta": {"name": "less", "version": "3.5.0-beta", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "readmeFilename": "README.md", "gitHead": "42fd7dca20c011336bf616020654a1fd575cde5b", "_id": "less@3.5.0-beta", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "2ab3c82573474f54b2b3abf368cec606b53fa187", "size": 583700, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.5.0-beta.tgz", "integrity": "sha512-1WLgGZ8lHzGZmm7KT0JXebnAS7ZBP8f9wrOksU5doNXwWpJX3IiGYWKKUloH5yXwae6qd8BxDZa55nK/uUNhIw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.5.0-beta_1529896624422_0.21108653856425974"}, "_hasShrinkwrap": false, "publish_time": 1529896624532, "_cnpm_publish_time": 1529896624532, "_cnpmcore_publish_time": "2021-12-13T13:28:18.357Z"}, "3.0.4": {"name": "less", "version": "3.0.4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "gitHead": "e41a17b3ed4c09e3865a7e5e5580afeabb5f22cb", "_id": "less@3.0.4", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "d27dcedbac96031c9e7b76f1da1e4b7d83760814", "size": 578540, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.4.tgz", "integrity": "sha512-q3SyEnPKbk9zh4l36PGeW2fgynKu+FpbhiUNx/yaiBUQ3V0CbACCgb9FzYWcRgI2DJlP6eI4jc8XPrCTi55YcQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.0.4_1525657988914_0.13207110471112737"}, "_hasShrinkwrap": false, "publish_time": 1525657989069, "_cnpm_publish_time": 1525657989069, "_cnpmcore_publish_time": "2021-12-13T13:28:19.421Z"}, "3.0.2": {"name": "less", "version": "3.0.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "^0.5.3"}, "gitHead": "b873737f5aa19c7b48f88743cafcf5aa47ac0ed2", "_id": "less@3.0.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "1bcb9813bb6090c884ac142f02c633bd42931844", "size": 486152, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.2.tgz", "integrity": "sha512-konnFwWXpUQwzuwyN3Zfw/2Ziah2BKzqTfGoHBZjJdQWCmR+yrjmIG3QLwnlXNFWz27QetOmhGNSbHgGRdqhYQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.0.2_1524335144286_0.1462443646228988"}, "_hasShrinkwrap": false, "publish_time": 1524335144432, "_cnpm_publish_time": 1524335144432, "_cnpmcore_publish_time": "2021-12-13T13:28:20.506Z"}, "3.0.1": {"name": "less", "version": "3.0.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "2.81.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "2.81.0"}, "gitHead": "4272871e078f9961561bbe9c1a866c982c48237b", "_id": "less@3.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "ba2fea24a5632ccb8c84230d6043c0bf91855e37", "size": 673141, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.1.tgz", "integrity": "sha512-qUR4uNv88/c0mpnGOULgMLRXXSD6X0tYo4cVrokzsvn68+nuj8rskInCSe2eLAVYWGD/oAlq8P7J/FeZ/euKiw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.0.1_1518655237796_0.7513382874344328"}, "_hasShrinkwrap": false, "publish_time": 1518655237924, "_cnpm_publish_time": 1518655237924, "_cnpmcore_publish_time": "2021-12-13T13:28:21.621Z"}, "3.0.0": {"name": "less", "version": "3.0.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "2.81.0"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "2.81.0"}, "gitHead": "4962988db3c2b6ebc5c90da2ba08386b7d9a46e7", "_id": "less@3.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "d942ff9fe9f2c53b8b4e6b5c5f1ac434681f7aa5", "size": 577245, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0.tgz", "integrity": "sha512-9H0u5xo49oycfj4iSqoPt3ypHCO01bMEbOhChaNPUeeYazEZb0PVYYmp0JXQF8ZS4aU2Bkb7+aMPxELit65/DQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.0.0_1518327758939_0.26933493741291037"}, "_hasShrinkwrap": false, "publish_time": 1518327759191, "_cnpm_publish_time": 1518327759191, "_cnpmcore_publish_time": "2021-12-13T13:28:22.632Z"}, "3.0.0-RC.2": {"name": "less", "version": "3.0.0-RC.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "readmeFilename": "README.md", "gitHead": "a48c24c4dd3c13e00a20ece80323768496a96b36", "_id": "less@3.0.0-RC.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "926c4ca1094f4366b97e425824df5d18bec42aa3", "size": 577445, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-RC.2.tgz", "integrity": "sha512-7vTWRh35axT3+rt2YtMB5X1rk8gLcHdL1+pxZ8Lnm9jnCU3IKWtOGAteNTL7nPZ1UWB9KnEn+2yqpKsqf2RO8w=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_3.0.0-RC.2_1518311216602_0.7590387543009658"}, "_hasShrinkwrap": false, "publish_time": 1518311216808, "_cnpm_publish_time": 1518311216808, "_cnpmcore_publish_time": "2021-12-13T13:28:23.883Z"}, "3.0.0-RC.1": {"name": "less", "version": "3.0.0-RC.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "gitHead": "8b4524f644734c5c57c00ea0ce484bf49c886346", "_id": "less@3.0.0-RC.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "0f25d4f8e7cad8e940169e1ceae03ecbd0e19b7e", "size": 577388, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-RC.1.tgz", "integrity": "sha512-S48XmVxyNm5h3AXFmxuW4QFrmAvg0wquuJgMLmfx3ay2EnKvxUc5tIuRYG2z+plMvARbVaSuWaOYgCosG9Cd3w=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-3.0.0-RC.1.tgz_1517768119215_0.5253662359900773"}, "publish_time": 1517768119366, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517768119366, "_cnpmcore_publish_time": "2021-12-13T13:28:25.146Z"}, "3.0.0-alpha.4": {"name": "less", "version": "3.0.0-alpha.4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "phin": "^2.2.3", "promise": "^7.1.1", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "gitHead": "691f0f1aca76fa2c527fec6a34ebbb2556a3626e", "_id": "less@3.0.0-alpha.4", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "8b14efb1c1d21efa2253abeb2047c778d8bd8842", "size": 577379, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-alpha.4.tgz", "integrity": "sha512-ggOYMz2yAFUuDl68/GEhbTvrCkIH60HwUVbwqDYKk599nyHpdKO8svGzXbuXEI8v+iGuRLvJ8X//c8G6kHd8yQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-3.0.0-alpha.4.tgz_1508814564754_0.5816749583464116"}, "publish_time": 1508814564873, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508814564873, "_cnpmcore_publish_time": "2021-12-13T13:28:26.411Z"}, "2.7.3": {"name": "less", "version": "2.7.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "2.81.0"}, "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^8.6.2", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "phantomjs-prebuilt": "^2.1.7", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "60a5c3bd1f6807615d017a5019031da47e1f480d", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "2.81.0"}, "_id": "less@2.7.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "cc1260f51c900a9ec0d91fb6998139e02507b63b", "size": 502247, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.7.3.tgz", "integrity": "sha512-KPdIJKWcEAb02TuJtaLrhue0krtRLoRoo7x6BNJIBelO00t/CCdJQUnHW5V34OnHMWzIktSalJxRO+FvytQlCQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-2.7.3.tgz_1508813020373_0.544631906086579"}, "publish_time": 1508813020462, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508813020462, "_cnpmcore_publish_time": "2021-12-13T13:28:27.609Z"}, "3.0.0-alpha.3": {"name": "less", "version": "3.0.0-alpha.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "less-plugin-clean-css": "^1.5.1", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "promise": "^7.1.1", "phin": "^2.2.3", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "gitHead": "488e0fcbdff2b7dc369837ee3baeaea18d855164", "_id": "less@3.0.0-alpha.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "d3da9234fd16e60a47d6291e772e65a841ec8410", "size": 576649, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-alpha.3.tgz", "integrity": "sha512-CtLgi5FCi2bhVsea0rAH9dap/ORMZYeHBjFUrKVj53RytQqdMsTRrmBZm8dWZ+XJPNBoqOs46QEIoJoFxrp3gA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less-3.0.0-alpha.3.tgz_1507512894703_0.5878921463154256"}, "publish_time": 1507512894921, "_hasShrinkwrap": false, "_cnpm_publish_time": 1507512894921, "_cnpmcore_publish_time": "2021-12-13T13:28:28.861Z"}, "3.0.0-alpha.2": {"name": "less", "version": "3.0.0-alpha.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "promise": "^7.1.1", "request": "^2.73.0", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "gitHead": "ff5760e0994fd1ecc45bf50c242eaece9f109603", "_id": "less@3.0.0-alpha.2", "_shasum": "f9fae6de065e0c76cc47b325de29726e695ad680", "_from": ".", "_npmVersion": "4.0.3", "_nodeVersion": "4.4.4", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "f9fae6de065e0c76cc47b325de29726e695ad680", "size": 529388, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-alpha.2.tgz", "integrity": "sha512-xS+QTUk6UJ4nP8N82rYV+j1Cvo2hAi8R5S+RdGmNvLuMSakN6ao4pckdg6P1WQtfvOpXEfZ87u6a41HmhCHfbQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-3.0.0-alpha.2.tgz_1484099404507_0.8402867745608091"}, "publish_time": 1484099404774, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484099404774, "_cnpmcore_publish_time": "2021-12-13T13:28:30.110Z"}, "2.7.2": {"name": "less", "version": "2.7.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "^2.72.0"}, "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^8.6.2", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "phantomjs-prebuilt": "^2.1.7", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "ceb54053e7964342e6d9be44e24dad701d818098", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3", "request": "^2.72.0"}, "_id": "less@2.7.2", "_shasum": "368d6cc73e1fb03981183280918743c5dcf9b3df", "_from": ".", "_npmVersion": "4.0.3", "_nodeVersion": "4.4.4", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "368d6cc73e1fb03981183280918743c5dcf9b3df", "size": 502579, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.7.2.tgz", "integrity": "sha512-rMNVkQrv/ySmHJdSoXfmnVIJFJbkd77MQ7UzflrT6qynF1E7/gmQ+L020WNgStW2bnGYL7+zpu10ZEHRrz22hg=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/less-2.7.2.tgz_1483581489990_0.6281389200594276"}, "publish_time": 1483581493133, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483581493133, "_cnpmcore_publish_time": "2021-12-13T13:28:31.244Z"}, "3.0.0-alpha.1": {"name": "less", "version": "3.0.0-alpha.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^3.2.0", "git-rev": "^0.2.1", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-uglify": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "promise": "^7.1.1", "request": "^2.73.0", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "gitHead": "c60c3225624da08cfa6937651a3160ef0ced5ba1", "_id": "less@3.0.0-alpha.1", "_shasum": "9615c66ab6ab1eaad9075906c9d976ea173835b3", "_from": ".", "_npmVersion": "4.0.3", "_nodeVersion": "4.4.4", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "9615c66ab6ab1eaad9075906c9d976ea173835b3", "size": 526338, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-alpha.1.tgz", "integrity": "sha512-pquyLsQkF0w0A9rENvfpU/brxNkFQHWJ+3eagB90K5dJrUnldYqCGykHgpEqoJtjO/bjtq1eluLH5vyZ12PACw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-3.0.0-alpha.1.tgz_1483304575042_0.1714104707352817"}, "publish_time": 1483304575313, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483304575313, "_cnpmcore_publish_time": "2021-12-13T13:28:32.373Z"}, "3.0.0-pre.4": {"name": "less", "version": "3.0.0-pre.4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "promise": "^7.1.1", "request": "^2.73.0", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "26041d9aa577688b27ed276dabf49fbf162a6429", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "_id": "less@3.0.0-pre.4", "_shasum": "67507b12e6dc93746f73d231ae38e3c043f1e380", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "67507b12e6dc93746f73d231ae38e3c043f1e380", "size": 521149, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-pre.4.tgz", "integrity": "sha512-GC7q4oxSAaKoYbdITVqTAceE0mpbp28drYpxKZ1C+e/p85qD/I+zjR3O19ZgUCuS3CV821nXEdRHAYAHpMb9UQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-3.0.0-pre.4.tgz_1477093231853_0.10495221707969904"}, "publish_time": 1477093232107, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477093232107, "_cnpmcore_publish_time": "2021-12-13T13:28:33.884Z"}, "3.0.0-pre.3": {"name": "less", "version": "3.0.0-pre.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^9.0.0", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "performance-now": "^0.2.0", "phantomjs-prebuilt": "^2.1.7", "promise": "^7.1.1", "request": "^2.73.0", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "a38f8a1eb7beed589d2fa734fcf411cf4461d231", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "_id": "less@3.0.0-pre.3", "_shasum": "831b3761c5865db758785b6d516c0d3b7502cfc8", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "831b3761c5865db758785b6d516c0d3b7502cfc8", "size": 518863, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-pre.3.tgz", "integrity": "sha512-Fh7b8mE+Ib15mOn2/UAoNL2D+45egZjwKyEBYvHfZDBUsYNwvceEA+R4yJXtDh5g2dkDDYFV9k5Jirm6sDZT7g=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/less-3.0.0-pre.3.tgz_1468877422831_0.4808636319357902"}, "publish_time": 1468877425637, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468877425637, "_cnpmcore_publish_time": "2021-12-13T13:28:35.256Z"}, "3.0.0-pre.2": {"name": "less", "version": "3.0.0-pre.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^8.6.2", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "phantomjs-prebuilt": "^2.1.7", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "1136a9c050ea31544109cf4370fc08b7ff52fb7b", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "_id": "less@3.0.0-pre.2", "_shasum": "fc03c44a5c13691dbd3adfe43f3540c083ffa335", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "fc03c44a5c13691dbd3adfe43f3540c083ffa335", "size": 518959, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-pre.2.tgz", "integrity": "sha512-mxPvapOl/aE/onshMdqvpZ5cGQqHIb2F7jO9KjeS5OuTpBRT/S4y2V5v1Oh5yWBXhsfk5RNTrio1MvK49Z3YHw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-3.0.0-pre.2.tgz_1468524573368_0.21090799942612648"}, "publish_time": 1468524573859, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468524573859, "_cnpmcore_publish_time": "2021-12-13T13:28:36.568Z"}, "3.0.0-pre.1": {"name": "less", "version": "3.0.0-pre.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^8.6.2", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "phantomjs-prebuilt": "^2.1.7", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "b39b36f7009d1825fd3d851d9a7bfe38c99db91a", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "_id": "less@3.0.0-pre.1", "_shasum": "a00679751f075f538ad80f024fd4e8c58d7963ae", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "a00679751f075f538ad80f024fd4e8c58d7963ae", "size": 516923, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-3.0.0-pre.1.tgz", "integrity": "sha512-KzVpgevxZejjOGlWl8Epyw0wQuJ6Bz2sXpDROy+5myA4hYsQvR/qzMHm/D6qzkdtp5/yz+nenKnjbGzNWaqliw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-3.0.0-pre.1.tgz_1468436661311_0.9046596137341112"}, "publish_time": 1468436661870, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468436661870, "_cnpmcore_publish_time": "2021-12-13T13:28:37.827Z"}, "2.7.1": {"name": "less", "version": "2.7.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.2", "grunt": "^1.0.1", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^8.6.2", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "d3e1531370a36dd6e213c8b4da955d0a0e1d6eb7", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "_id": "less@2.7.1", "_shasum": "6cbfea22b3b830304e9a5fb371d54fa480c9d7cf", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "6cbfea22b3b830304e9a5fb371d54fa480c9d7cf", "size": 502361, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.7.1.tgz", "integrity": "sha512-C7dSI8hOBbZ7qmnpjMFjpwWRs6bVcrJsRmap7onnTy4N14ye6KtB7UY0ZqY1ejHf/3a2JxzVYAd+yulIRdT1nw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/less-2.7.1.tgz_1462826329455_0.6074283130001277"}, "publish_time": 1462826329919, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462826329919, "_cnpmcore_publish_time": "2021-12-13T13:28:39.316Z"}, "2.7.0": {"name": "less", "version": "2.7.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.2", "grunt": "^1.0.1", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-jscs": "^2.8.0", "grunt-saucelabs": "^8.6.2", "grunt-shell": "^1.3.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "gitHead": "b76db1799dd0e91a1042d6f2620d0f64f1758f73", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "source-map": "^0.5.3"}, "_id": "less@2.7.0", "_shasum": "0ebda1046f1c24efc696e52d353bd8c18ef07b10", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "0ebda1046f1c24efc696e52d353bd8c18ef07b10", "size": 502537, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.7.0.tgz", "integrity": "sha512-pDNeWa3emRQrHXBw55qecnNX5tvg8LJk76gnEofz9kwlEoLmpg36lvU5ftSv/OBHnrKLStI6Dyef+hpHvl6f5w=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/less-2.7.0.tgz_1462671322090_0.2874688785523176"}, "publish_time": 1462671324988, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462671324988, "_cnpmcore_publish_time": "2021-12-13T13:28:41.016Z"}, "2.6.1": {"name": "less", "version": "2.6.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.4.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.51.0", "source-map": "^0.5.3"}, "devDependencies": {"diff": "^2.2.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.0", "grunt-contrib-connect": "^0.11.2", "grunt-contrib-jasmine": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^0.11.0", "grunt-jscs": "^2.7.0", "grunt-shell": "^1.1.1", "grunt-browserify": "~4.0.1", "jit-grunt": "^0.9.1", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "b550b2081f0af3b8905d31a2a03f595ad7b299a8", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.4.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.51.0", "source-map": "^0.5.3"}, "_id": "less@2.6.1", "_shasum": "658e01ec9ac3149959c6b6dfbcfbc0a170afda7a", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.4.1", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "658e01ec9ac3149959c6b6dfbcfbc0a170afda7a", "size": 493458, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.6.1.tgz", "integrity": "sha512-iumo0OjAUoM/KCZm0TDudGWneR9lkjS1e7yhEJSYK0KXBzaXxzd6bnhYLdcTlPekTOlgWnc6AoJuphbvW47SyA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/less-2.6.1.tgz_1457109575213_0.41725030448287725"}, "publish_time": 1457109578745, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457109578745, "_cnpmcore_publish_time": "2021-12-13T13:28:42.592Z"}, "2.6.0": {"name": "less", "version": "2.6.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-browserify": "~3.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-jscs": "^1.6.0", "grunt-saucelabs": "^8.3.2", "grunt-shell": "^1.1.1", "jit-grunt": "^0.9.1", "time-grunt": "^1.0.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "633e499566a05c5cb667a2183eb1478d31e6e92c", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "_id": "less@2.6.0", "_shasum": "ba4cbf4ce5a208f266ef59eef2ec409ad1044966", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.4.1", "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "dist": {"shasum": "ba4cbf4ce5a208f266ef59eef2ec409ad1044966", "size": 492453, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.6.0.tgz", "integrity": "sha512-L1SU80cRxLmCzg3fEo5hF6gNVFhXXRvPQwAMommvRZo3DRzXUhH8b+ANGxIn1s3qMmiM9n9VblaLJwCdL/gogw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1454101572560, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454101572560, "_cnpmcore_publish_time": "2021-12-13T13:28:44.208Z"}, "2.5.3": {"name": "less", "version": "2.5.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-jscs": "^1.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "~3.5.0", "jit-grunt": "^0.9.1", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "33fa72767e7474f3aedbbff83d8343217c08f58a", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "_id": "less@2.5.3", "_shasum": "9ff586e8a703515fc18dc99c7bc498d2f3ad4849", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.2", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "dist": {"shasum": "9ff586e8a703515fc18dc99c7bc498d2f3ad4849", "size": 551890, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.5.3.tgz", "integrity": "sha512-aQAnfDvIzYC3NsAuvhOKnGJ5Yvu5b/CNvDVa1DY/KTHEuaK+Ca/piJadvnanFDXu9e1wHczy/LOtVLI0m5ccCA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1443181851988, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443181851988, "_cnpmcore_publish_time": "2021-12-13T13:28:45.770Z"}, "2.5.2": {"name": "less", "version": "2.5.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-jscs": "^1.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "~3.5.0", "jit-grunt": "^0.9.1", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "f0c454bc68599a9ecd5fe7de843b3ca1ae7b28ea", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "_id": "less@2.5.2", "_shasum": "fddc11a748beaeda431e646c1e2da380ce0467a5", "_from": ".", "_npmVersion": "3.1.3", "_nodeVersion": "0.12.6", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "dist": {"shasum": "fddc11a748beaeda431e646c1e2da380ce0467a5", "size": 490707, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.5.2.tgz", "integrity": "sha512-ftD+An7op9IeMEVSFQbuPF6uXb0PTZUvbTWNjP0eqLP0A7VxjgU9Ga1L0QOB2aWJsGN1xgsAFaPIGrv6UC88Kw=="}, "publish_time": 1443122617098, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443122617098, "_cnpmcore_publish_time": "2021-12-13T13:28:47.141Z"}, "2.5.1": {"name": "less", "version": "2.5.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-jscs": "^1.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "~3.5.0", "jit-grunt": "^0.9.1", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "59c012c16a71eca2fd9f354b8db8fffaa5e14d6d", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "_id": "less@2.5.1", "_shasum": "8b489cc01d021e49360fd5ae03581896e722b726", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "dist": {"shasum": "8b489cc01d021e49360fd5ae03581896e722b726", "size": 456762, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.5.1.tgz", "integrity": "sha512-RrSVIVLNpQEwliRYYURp/436vdBJPJe9xw3kVojGkalldAz8eno3gqMRD7kmnR0JJSKbsPHU51Hgdj3sadYjbg=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1432207814931, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432207814931, "_cnpmcore_publish_time": "2021-12-13T13:28:48.475Z"}, "2.5.0": {"name": "less", "version": "2.5.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-jscs": "^1.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "~3.5.0", "jit-grunt": "^0.9.1", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "9b7021a309a9010b04ddc89d5fd86f1d2d0b3433", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.4.2"}, "_id": "less@2.5.0", "_shasum": "11d6d611586de6d5f808220649bc9bbfe7ad5e17", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "dist": {"shasum": "11d6d611586de6d5f808220649bc9bbfe7ad5e17", "size": 462746, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.5.0.tgz", "integrity": "sha512-dQOHAh55lvWtP4rCXoCzrOIDdVHi/Qemb8lQ5UFuOv+pnIBc58g21fAGq+399YV5I4mS/lL02SlymdLVCmmlhA=="}, "publish_time": 1428051325437, "_hasShrinkwrap": false, "_cnpm_publish_time": 1428051325437, "_cnpmcore_publish_time": "2021-12-13T13:28:49.965Z"}, "2.4.0": {"name": "less", "version": "2.4.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.2.0"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.7.0", "grunt-jscs": "^1.2.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "gitHead": "6fd2a5751cc8313481913bcb1623bf6c50089df8", "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.2.0"}, "_id": "less@2.4.0", "_shasum": "ce51b38f1c05a0cdd47982fac40dd0a39cec2031", "_from": ".", "_npmVersion": "2.5.0", "_nodeVersion": "0.10.25", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "dist": {"shasum": "ce51b38f1c05a0cdd47982fac40dd0a39cec2031", "size": 375035, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.4.0.tgz", "integrity": "sha512-g/l/5w2Aw2d1SKd42ImHk/9hvcV3ADL0sb5RkOrDNMIxA5oGQC2SO8iIqispyHc4sV7SD1N7vJn3A+iLa05yWQ=="}, "publish_time": 1423396073977, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423396073977, "_cnpmcore_publish_time": "2021-12-13T13:28:51.615Z"}, "2.3.1": {"name": "less", "version": "2.3.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.2.0"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.7.0", "grunt-jscs": "^1.2.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.2.0"}, "_id": "less@2.3.1", "dist": {"shasum": "c10ec082b2f2950f6e1c750e4da14bcc543ed845", "size": 444783, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.3.1.tgz", "integrity": "sha512-wl8v3Pj6Ke4EmIUoUwRHxW+aPj0t+GsqJ1Hv+3R2q7DVBsMo5lV8qv4KoOiqUKXlpzxBvlDnJcKs7K7y0r5dqQ=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1422466377582, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422466377582, "_cnpmcore_publish_time": "2021-12-13T13:28:53.406Z"}, "2.3.0": {"name": "less", "version": "2.3.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.1.x"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.7.0", "grunt-jscs": "^1.2.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"errno": "^0.1.1", "graceful-fs": "^3.0.5", "image-size": "~0.3.5", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "source-map": "^0.1.x"}, "_id": "less@2.3.0", "dist": {"shasum": "b5054bd1bfd9e94acb800b6cd1107065979abb39", "size": 378639, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.3.0.tgz", "integrity": "sha512-dAwj6WJzHTu13Q5WJs7IP0BpWh2dJVVqEqLMDTVTRMym4/nxnWADR2cj6BFt4t7tau4ZymSYboR3pVxKMD9n+A=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1422318242877, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422318242877, "_cnpmcore_publish_time": "2021-12-13T13:28:55.029Z"}, "2.2.0": {"name": "less", "version": "2.2.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1", "image-size": "~0.3.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1", "image-size": "~0.3.5"}, "_id": "less@2.2.0", "dist": {"shasum": "86eec2cad0e4c2a979929292d15750394056a7af", "size": 413286, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.2.0.tgz", "integrity": "sha512-A/wcgYrrE00BoJvVhjRn2P/cTlcjSH5rebItpizNNHo2bIvNsSu2gnsV4VExRXfRCzKYgIItUkgaOa4yHlHkMg=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1420372539961, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420372539961, "_cnpmcore_publish_time": "2021-12-13T13:28:56.714Z"}, "2.1.2": {"name": "less", "version": "2.1.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1"}, "_id": "less@2.1.2", "dist": {"shasum": "29b1ff230323f7a5dddf451b731a3d9460e8e908", "size": 442120, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.1.2.tgz", "integrity": "sha512-o9ZZyX5zgZJ5pSI4zx2wbO3OGXYxgCNPuguL/TyxX5WR0vrekhmFqSX0HAm8VFjfCDdBVwuxrnTr/LO3kt6wpQ=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1419087239229, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419087239229, "_cnpmcore_publish_time": "2021-12-13T13:28:58.358Z"}, "2.1.1": {"name": "less", "version": "2.1.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1"}, "_id": "less@2.1.1", "dist": {"shasum": "7ba1fd7698e7b4eb46286c3954aed43e54da41b6", "size": 441151, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.1.1.tgz", "integrity": "sha512-SuxVDgZ6+Vm6vPvoIPgj7u1lS5t+V3V+9yvPOKoTmsY2IdCKT9jt2ZhvaT32JvIAF4WgAQWagXpEBSo3aNyUog=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1417069025282, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417069025282, "_cnpmcore_publish_time": "2021-12-13T13:29:00.181Z"}, "2.1.0": {"name": "less", "version": "2.1.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-shell": "^1.1.1", "grunt-browserify": "^3.2.0", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-saucelabs": "^8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "^3.0.4", "mime": "^1.2.11", "request": "^2.48.0", "mkdirp": "^0.5.0", "source-map": "^0.1.x", "promise": "^6.0.1"}, "_id": "less@2.1.0", "dist": {"shasum": "535b37390b21d904ee1c4c78d4c676b9f3c8fb7e", "size": 440671, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.1.0.tgz", "integrity": "sha512-piT1mBh/I98aZMM/QhSDFFB71FEgBqN61YQ7H6ABfHeUPD4ia0vUDcuwEx2Ko8CSD3LAavWdvLdAw06NfzItCQ=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1416741369502, "_hasShrinkwrap": false, "_cnpm_publish_time": 1416741369502, "_cnpmcore_publish_time": "2021-12-13T13:29:01.769Z"}, "2.0.0": {"name": "less", "version": "2.0.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.47.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-connect": "~0.9.0", "grunt-contrib-jasmine": "~0.8.1", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-shell": "~1.1.1", "grunt-browserify": "~3.2.0", "matchdep": "~0.3.0", "time-grunt": "~1.0.0", "grunt-saucelabs": "~8.3.2"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.47.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "_id": "less@2.0.0", "dist": {"shasum": "a94c0aee11868729644d92a5eed6ea6dc7bb9741", "size": 438919, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.0.0.tgz", "integrity": "sha512-jT3SSFYAL4EdRfeW/jTPsUgpHoG0yFLjtIwD1WyDJJnOhKyo9AUCMerbhqi+tHRf0GuYxZ9FBlIaDXavlflLrg=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1415543481045, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415543481045, "_cnpmcore_publish_time": "2021-12-13T13:29:03.302Z"}, "2.0.0-b3": {"name": "less", "version": "2.0.0-b3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less-node/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.47.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.8.1", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-shell": "~1.1.1", "grunt-browserify": "~3.1.0", "matchdep": "~0.3.0", "time-grunt": "~1.0.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.47.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "_id": "less@2.0.0-b3", "dist": {"shasum": "61869a02b29a88bda7b99e239e7646fe301f4500", "size": 431719, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.0.0-b3.tgz", "integrity": "sha512-QXvVGX4bAB9UhZf9gUVnOhUokXooDS4e5VAZRkNxSNbmgWkSXEeNEeS3oitYuGPEtSHonpMKKuCQmXDH+vL9VA=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1414863831023, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414863831023, "_cnpmcore_publish_time": "2021-12-13T13:29:05.183Z"}, "2.0.0-b2": {"name": "less", "version": "2.0.0-b2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less-node/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.46.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.8.1", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-shell": "~1.1.1", "grunt-browserify": "~3.1.0", "matchdep": "~0.3.0", "time-grunt": "~1.0.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.46.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "_id": "less@2.0.0-b2", "dist": {"shasum": "0cc866e0bf0093556b34879aee8ff7fa2d8c1ad8", "size": 430265, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.0.0-b2.tgz", "integrity": "sha512-UoH18VFYXPbtRpsK5H0XZSvqyKzt5BU/n2Xh9y4dDHaGDs6ncAauUF3M0x5Lw7KF9qRU4rUYXJ6VTOFvr130BQ=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1414325974079, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414325974079, "_cnpmcore_publish_time": "2021-12-13T13:29:07.264Z"}, "2.0.0-b1": {"name": "less", "version": "2.0.0-b1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less-node/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.45.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.8.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-shell": "~1.1.1", "matchdep": "~0.3.0", "time-grunt": "~1.0.0", "grunt-browserify": "~3.0.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~3.0.4", "mime": "~1.2.11", "request": "~2.45.0", "mkdirp": "~0.5.0", "source-map": "0.1.x", "promise": "~6.0.1"}, "_id": "less@2.0.0-b1", "dist": {"shasum": "3dcb5815b2052c89435c00def2aeb58ddb321b6a", "size": 424290, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-2.0.0-b1.tgz", "integrity": "sha512-QPKHCRrvQrDqzf9KvGY9MYER2/QequHWEc+euH39bHIblvZaILcRzJAhC2d01ESUfEMDEShvkiB7jKW4zXpZtA=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1413752315331, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413752315331, "_cnpmcore_publish_time": "2021-12-13T13:29:09.053Z"}, "1.7.5": {"name": "less", "version": "1.7.5", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.7.5.js"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~3.0.2", "mime": "~1.2.11", "request": "~2.40.0", "mkdirp": "~0.5.0", "clean-css": "2.2.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "matchdep": "~0.3.0", "time-grunt": "~0.3.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~3.0.2", "mime": "~1.2.11", "request": "~2.40.0", "mkdirp": "~0.5.0", "clean-css": "2.2.x", "source-map": "0.1.x"}, "_id": "less@1.7.5", "dist": {"shasum": "4f220cf7288a27eaca739df6e4808a2d4c0d5756", "size": 3202787, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.7.5.tgz", "integrity": "sha512-+cddvg94fFlyJUijF+jO8Dvrr1RWIAf3l/kXDUiQ65za+o468iA9jwal2dcKnmJTHTFqnQQGo2jT1pJqAlI73Q=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1409727937254, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409727937254, "_cnpmcore_publish_time": "2021-12-13T13:29:10.714Z"}, "1.7.4": {"name": "less", "version": "1.7.4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.6.3.js"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~2.0.3", "mime": "~1.2.11", "request": "~2.34.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "matchdep": "~0.3.0", "time-grunt": "~0.3.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~2.0.3", "mime": "~1.2.11", "request": "~2.34.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "_id": "less@1.7.4", "dist": {"shasum": "1db03afd0d1b848d898d10d2690d79ee3834026d", "size": 2950058, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.7.4.tgz", "integrity": "sha512-yqg3LfXitKducFfYR/rPJZXhYWaWC7ezb/xeh9UK0VYZMJGuTS5NTPMcxReK+2Aw3YDNEFxU+3tOx9VPsYS5+A=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1406491555367, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406491555367, "_cnpmcore_publish_time": "2021-12-13T13:29:12.847Z"}, "1.7.3": {"name": "less", "version": "1.7.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.6.3.js"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~2.0.3", "mime": "~1.2.11", "request": "~2.34.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "matchdep": "~0.3.0", "time-grunt": "~0.3.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~2.0.3", "mime": "~1.2.11", "request": "~2.34.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "_id": "less@1.7.3", "dist": {"shasum": "89acc0e8adcdb1934aa21c7611d35b97b52652a6", "size": 2720411, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.7.3.tgz", "integrity": "sha512-kHskFSbe46tgXgpusTiUm1UT/9kK0lAzCAWDXNfEcfO2ow2Kkqv7g2VPnUczerp24H3KSazPOO8gg3VqBy1m0Q=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1403450059211, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403450059211, "_cnpmcore_publish_time": "2021-12-13T13:29:14.865Z"}, "1.7.1": {"name": "less", "version": "1.7.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.6.3.js"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"graceful-fs": "~2.0.3", "mime": "~1.2.11", "request": "~2.34.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "matchdep": "~0.3.0", "time-grunt": "~0.3.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"graceful-fs": "~2.0.3", "mime": "~1.2.11", "request": "~2.34.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "_id": "less@1.7.1", "dist": {"shasum": "6f5580029b52c3ac590e85e2b079827a66f6cc74", "size": 2449540, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.7.1.tgz", "integrity": "sha512-vNh1BX/bd61p1pfBHkV4eaC5fMNqIktb23nVMgilU/aku9zTQry5Nhzi2zOHToNuosZ6EJnBypsA/7PW2EQL2g=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1402244845288, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402244845288, "_cnpmcore_publish_time": "2021-12-13T13:29:16.810Z"}, "1.7.0": {"name": "less", "version": "1.7.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.6.3.js"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.33.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.6.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.8.0", "grunt-contrib-uglify": "~0.3.2", "grunt-shell": "~0.6.4", "http-server": "~0.6.1", "matchdep": "~0.3.0", "time-grunt": "~0.2.9"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"mime": "1.2.x", "request": ">=2.33.0", "mkdirp": "~0.3.5", "clean-css": "2.1.x", "source-map": "0.1.x"}, "_id": "less@1.7.0", "dist": {"shasum": "6f1293bac1f402c932c2ce21ba7337f7c635ba84", "size": 2272597, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.7.0.tgz", "integrity": "sha512-q3gcDV/z2DnBZCALaveJgurAngH23DQ8Sy9OPFkkTtUxca6ShMIes0327AsXK23ZVjMs4A3cnaVTyBn/y+e9/g=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1393532252652, "_hasShrinkwrap": false, "_cnpm_publish_time": 1393532252652, "_cnpmcore_publish_time": "2021-12-13T13:29:18.597Z"}, "1.6.3": {"name": "less", "version": "1.6.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.6.3.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "_id": "less@1.6.3", "dist": {"shasum": "71ce89ec30b774b3567f254c67958f2f2c193bde", "size": 2096995, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.6.3.tgz", "integrity": "sha512-3PkBx2CaCayPp9wiLImkCWD5b5LcbkAqEdtMO4x2VSaOVAjJVk7zTnx7Ypjly+sFNIAXa3M1Lo7TvtfNfSkQQw=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1391878411138, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391878411138, "_cnpmcore_publish_time": "2021-12-13T13:29:20.428Z"}, "1.6.2": {"name": "less", "version": "1.6.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.5.0.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "_id": "less@1.6.2", "dist": {"shasum": "86556e6ab8f9af4d8b853db16c5f262e94fc98a0", "size": 1938497, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.6.2.tgz", "integrity": "sha512-c76/CPoW0zRBpUwBq+E9X1IufXrBf4b7WGuYw6ycxPVKFybMGstjtfwiWdvKxKwc7+IeRdCTmA8QydAQ4e6XFw=="}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1391366311668, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391366311668, "_cnpmcore_publish_time": "2021-12-13T13:29:22.129Z"}, "1.6.1": {"name": "less", "version": "1.6.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.5.0.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.6.1", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "dist": {"shasum": "54e5e8d7b4fc43a14f8a32aaf0bae2d45ac287ca", "size": 1650620, "noattachment": false, "tarball": "https://registry.npmmirror.com/less/-/less-1.6.1.tgz", "integrity": "sha512-jRBEOVHKFSYVZh7KmYLktt+SbtlhTUsveKXOQ6E/HSgrcD1ikdOGcX6JFlaFnnhA60qzIdlfpJd8PAfkeXftWQ=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1389528041207, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389528041207, "_cnpmcore_publish_time": "2021-12-13T13:29:24.137Z"}, "1.6.0": {"name": "less", "version": "1.6.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.5.0.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.6.0", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.6.0.tgz", "shasum": "f0e7d314e53df05d9d52592c159f31bca4e62bb3", "size": 1558246, "noattachment": false, "integrity": "sha512-gg4Mw3IWiTBiaitDaLo6SKNWETbjrrH+LWJOu53Xd+HH5SkQvOxbOWdPUrc1SASm8r2VzLo3X405b3wPDXhqGQ=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1388597813002, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388597813002, "_cnpmcore_publish_time": "2021-12-13T13:29:26.064Z"}, "1.5.1": {"name": "less", "version": "1.5.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.5.0.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.5.1", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.5", "clean-css": "2.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.5.1.tgz", "shasum": "633313130efd12a3b78c56aa799dab3eeffffff4", "size": 1460518, "noattachment": false, "integrity": "sha512-F4OM28EGxbZKH1FHEKAWNC6LfdbzLzA0AVpiEFwkh4a3NpSYZ0zqToehlmBxAnbXMUphnaakjecZUgPHEemkRA=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1384708319226, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384708319226, "_cnpmcore_publish_time": "2021-12-13T13:29:27.875Z"}, "1.5.0": {"name": "less", "version": "1.5.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.5.0.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.5.0", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.5.0.tgz", "shasum": "a97be1d2607577f5f33632a7e098024ab828f699", "size": 1458417, "noattachment": false, "integrity": "sha512-8am+d7V0BwIMW8R8bduNdv7v4C4/j49GQghLllIsLAFZv8eu+1/JwCUyrJbcKpYFfarseNd83GtqdLfMO0dMsw=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1382361722415, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382361722415, "_cnpmcore_publish_time": "2021-12-13T13:29:29.884Z"}, "1.5.0-b4": {"name": "less", "version": "1.5.0-b4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.2.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.5.0-b4", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.5.0-b4.tgz", "shasum": "337b9443b43ff2b7bcd069d211a1ea430b026e62", "size": 1289654, "noattachment": false, "integrity": "sha512-9j/Cu0fmOmHo5NJcPowdcDnqX7b1/LUMNmgDqeWbA1l9Xi5aTtVH9ZXBpcqG7kEOV3dIci7EnfQV5rjJLl4rbA=="}, "_resolved": "less-1.5.0-b4.tar.gz", "_from": "less-1.5.0-b4.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1380867199287, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380867199287, "_cnpmcore_publish_time": "2021-12-13T13:29:31.639Z"}, "1.5.0-b3": {"name": "less", "version": "1.5.0-b3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.2.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.5.0-b3", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.5.0-b3.tgz", "shasum": "6b7a910a1b23181d6b314c78be1f60dc696b3b72", "size": 1286823, "noattachment": false, "integrity": "sha512-xJmhC8GQaXT3cABcvI3Rn+NXHX8mDMqP9stXbK1EGX1eROVhybNX6MhfL8hdXlUx0Vnq9XxLc6vAp9oKGnXsig=="}, "_resolved": "less-1.5.0-b3.tar.gz", "_from": "less-1.5.0-b3.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1379395005721, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379395005721, "_cnpmcore_publish_time": "2021-12-13T13:29:33.735Z"}, "1.5.0-b2": {"name": "less", "version": "1.5.0-b2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.2.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "matchdep": "~0.1.2", "time-grunt": "~0.1.1"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.5.0-b2", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.5.0-b2.tgz", "shasum": "de5ede5587013986ab1841c8ac1517db447df7f9", "size": 1285338, "noattachment": false, "integrity": "sha512-W17QZlFzywfHGMyHHbeimNYnPGpu+DW6GohExOqIFDkdHCk5fDnrlJ6Nlo6NPQ+Wax/G+1hVv7s7R6sjF5UVvQ=="}, "_resolved": "less-1.5.0-b2.tar.gz", "_from": "less-1.5.0-b2.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1378762460103, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378762460103, "_cnpmcore_publish_time": "2021-12-13T13:29:35.880Z"}, "1.5.0-b1": {"name": "less", "version": "1.5.0-b1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.2.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"pretest": "make jshint", "test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "jshint": "~2.1.4", "http-server": "~0.5.5"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "readmeFilename": "README.md", "_id": "less@1.5.0-b1", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "clean-css": "1.0.x", "source-map": "0.1.x"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.5.0-b1.tgz", "shasum": "2231ce259ef13193cf888bdac56e853e388775fa", "size": 1273458, "noattachment": false, "integrity": "sha512-DrWv+m6imCBNbKPH4BHjZNTcVN2hd8vEyEe9HDjrsBzcL/JSNRADEWAD1LnJ/h1Bf7bxGJ0468A6AUakt6mfoA=="}, "_resolved": "less-1.5.0-b1.tar.gz", "_from": "less-1.5.0-b1.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1378237361174, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378237361174, "_cnpmcore_publish_time": "2021-12-13T13:29:37.802Z"}, "1.4.2": {"name": "less", "version": "1.4.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.1.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "licenses": [{"type": "Apache v2", "url": "https://github.com/less/less.js/blob/master/LICENSE"}], "readmeFilename": "README.md", "_id": "less@1.4.2", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.2.tgz", "shasum": "b7deefe98a3a87bee364411b3df2d1efe5a412d0", "size": 1185473, "noattachment": false, "integrity": "sha512-mJy2f4cQ5cjog3xErRXkUziR7X+99wpH1Zzt/KLEplVhWzCb5fCY3hwBhbA2xEHRVBR/rKCB0yyMUNiaKEpzgg=="}, "_resolved": "./less-1.4.2.tar.gz", "_from": "./less-1.4.2.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1374351654906, "_hasShrinkwrap": false, "_cnpm_publish_time": 1374351654906, "_cnpmcore_publish_time": "2021-12-13T13:29:39.881Z"}, "1.4.1": {"name": "less", "version": "1.4.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.1.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "licenses": [{"type": "Apache v2", "url": "https://github.com/cloudhead/less.js/blob/master/LICENSE"}], "readmeFilename": "README.md", "_id": "less@1.4.1", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.1.tgz", "shasum": "4a93e97abb5c33514434a8e80749fe9f5eb871f6", "size": 1112508, "noattachment": false, "integrity": "sha512-noEb1n/T/kWQsID13jxD6Tk/AgUj1f4AtuIljeYD43sQ9wx2EqWhZBwomH+NjYwLZGDseSO1LW8K9G1H+3SbiA=="}, "_resolved": "less-1.4.1.tar.gz", "_from": "less-1.4.1.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1373002095796, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373002095796, "_cnpmcore_publish_time": "2021-12-13T13:29:41.961Z"}, "1.4.0": {"name": "less", "version": "1.4.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "licenses": [{"type": "Apache v2", "url": "https://github.com/cloudhead/less.js/blob/master/LICENSE"}], "readmeFilename": "README.md", "_id": "less@1.4.0", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.0.tgz", "shasum": "63e02a0d41ccf4f26b3feb40021d945d6aa48b14", "size": 1045878, "noattachment": false, "integrity": "sha512-TlIEl0H5pkZ/fB9lG035O4B4GAAIqh+r84ljlKHsZXH4NcttqGmulLL6m3Oxmr1HBLXWC44QAtgFPvI2DMCI7g=="}, "_resolved": "less-1.4.0.tar.gz", "_from": "less-1.4.0.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1370463578635, "_hasShrinkwrap": false, "_cnpm_publish_time": 1370463578635, "_cnpmcore_publish_time": "2021-12-13T13:29:44.256Z"}, "1.4.0-b4": {"name": "less", "version": "1.4.0-b4", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "licenses": [{"type": "Apache v2", "url": "https://github.com/cloudhead/less.js/blob/master/LICENSE"}], "readmeFilename": "README.md", "_id": "less@1.4.0-b4", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.0-b4.tgz", "shasum": "508782a7c1432f206e8a2c48ad6275da8d96c3d4", "size": 971503, "noattachment": false, "integrity": "sha512-raitVlCBLZrtG3LA1L4R+4eUYceA++Je6ii0d/XzSbu8uo5RAxRaF/ZMVMRntmbnbQFHmTb0BZZCBQMV54v+LA=="}, "_resolved": "less-1.4.0-b4.tar.gz", "_from": "less-1.4.0-b4.tar.gz", "_npmVersion": "1.2.11", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1367649370635, "_hasShrinkwrap": false, "_cnpm_publish_time": 1367649370635, "_cnpmcore_publish_time": "2021-12-13T13:29:46.297Z"}, "1.4.0-b3": {"name": "less", "version": "1.4.0-b3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "licenses": [{"type": "Apache v2", "url": "https://github.com/cloudhead/less.js/blob/master/LICENSE"}], "readmeFilename": "README.md", "_id": "less@1.4.0-b3", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.0-b3.tgz", "shasum": "06f6fb3e676f3fd1b42173ab7a4a34f765e47f2e", "size": 975058, "noattachment": false, "integrity": "sha512-+F+z/aWwFf0zT/kc9aWkbjQG5fc+v3UVIHHci/q2gMsaza522Pj5i6DnJ3DCpZVaSCq5VkQMa4TULKQpxRdzmQ=="}, "_npmVersion": "1.1.69", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1367333024435, "_hasShrinkwrap": false, "_cnpm_publish_time": 1367333024435, "_cnpmcore_publish_time": "2021-12-13T13:29:48.503Z"}, "1.4.0-b2": {"name": "less", "version": "1.4.0-b2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "readmeFilename": "README.md", "_id": "less@1.4.0-b2", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.0-b2.tgz", "shasum": "8bb858b02956ca8001e9e222b2ee00f050f8ec3e", "size": 974227, "noattachment": false, "integrity": "sha512-c3tm9ENaRiOx7fUxBeTsCZKgV5nzAroykDzA91S9zZNp/U8dagrhhXh1d/Kxyv3iuPnje5Kc8Ge/JiHJny4dMw=="}, "_npmVersion": "1.1.69", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1363609716352, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363609716352, "_cnpmcore_publish_time": "2021-12-13T13:29:50.560Z"}, "1.4.0-b1": {"name": "less", "description": "Leaner CSS", "homepage": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "version": "1.4.0-b1", "jam": {"main": "./dist/less-1.4.0-beta.js"}, "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.2"}, "optionalDependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "readmeFilename": "README.md", "_id": "less@1.4.0-b1", "dependencies": {"mime": "1.2.x", "request": ">=2.12.0", "mkdirp": "~0.3.4", "ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.4.0-b1.tgz", "shasum": "527744c9711d2cd7cc1eed32a39bce802314cfb2", "size": 972694, "noattachment": false, "integrity": "sha512-ZRsuHy4UDqsJqLBYmciPov6msMBweeGBeHQE8n5sJlRCLOLQuUD9wSIwS4LJ4n15JXgTzrYUL+agqXchwYOi2A=="}, "_npmVersion": "1.1.69", "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1362730734802, "_hasShrinkwrap": false, "_cnpm_publish_time": 1362730734802, "_cnpmcore_publish_time": "2021-12-13T13:29:52.496Z"}, "1.3.3": {"name": "less", "description": "Leaner CSS", "homepage": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.3.3", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.2"}, "optionalDependencies": {"ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "_id": "less@1.3.3", "dependencies": {"ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.3.3.tgz", "shasum": "7ee8f300a41080f3544c80c7a70cdf6a61280cf9", "size": 115797, "noattachment": false, "integrity": "sha512-y9el4KFU/bjCb0IvDoPX9ncwZcIZZtxKlYlNcXXqcfYbqzjfKM+TjLutrE9HwMQ+PqzBAPFI3NI6KRNbMBvc3Q=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1356860623761, "_hasShrinkwrap": false, "_cnpm_publish_time": 1356860623761, "_cnpmcore_publish_time": "2021-12-13T13:29:54.586Z"}, "1.3.2": {"name": "less", "description": "Leaner CSS", "homepage": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.3.2", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.2"}, "optionalDependencies": {"ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudhead/less.js.git"}, "_id": "less@1.3.2", "dependencies": {"ycssmin": ">=1.0.1"}, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.3.2.tgz", "shasum": "25e86b65c487a6311a181f869337af3ad3081a2b", "size": 115722, "noattachment": false, "integrity": "sha512-YkiZDzQk+MSX5dVk0WbTZhuxMpZZ/CR5e7Gqj/uY7mI+WWCJUPCTQOK8u2n+RQwz5ZQ6pImnh2m+YTJORKZLXA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1356734183199, "_hasShrinkwrap": false, "_cnpm_publish_time": 1356734183199, "_cnpmcore_publish_time": "2021-12-13T13:29:56.600Z"}, "1.3.1": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.3.1", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"diff": "~1.0.2"}, "scripts": {"test": "make test"}, "_id": "less@1.3.1", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.3.1.tgz", "shasum": "4cf72898b2081a96463f9c38dca5c89fc20a9ba8", "size": 594522, "noattachment": false, "integrity": "sha512-ebyiYk9IdtoPThEDAu3PPhmslq+KKZfdLPtkKgHF5QqDQo0U52y67Qd6cfhNVPDhwalF3E6IervYMupuBOks7g=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1350502500101, "_hasShrinkwrap": false, "_cnpm_publish_time": 1350502500101, "_cnpmcore_publish_time": "2021-12-13T13:29:58.909Z"}, "1.3.0": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.3.0", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_id": "less@1.3.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.3.0.tgz", "shasum": "5726170cb5661d7449942c78fb3a894d74e8103c", "size": 512000, "noattachment": false, "integrity": "sha512-8BwS8thAMufSfrttJjur2UqwiNS/J7Z12QhrhU3kKpOK1bxYkz0Xf2aP0zb/PfPMRX0xycnj1+SELHtev1nJlQ=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1331407785907, "_hasShrinkwrap": false, "_cnpm_publish_time": 1331407785907, "_cnpmcore_publish_time": "2021-12-13T13:30:00.926Z"}, "1.2.2": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.2.2", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_id": "less@1.2.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-10", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.2.2.tgz", "shasum": "0d6a59d640cc79d2a228b477c99cc4a45bd2c443", "size": 515262, "noattachment": false, "integrity": "sha512-BvoSQd9WKn86SikksNzeRkcxrm4IzRey2VgBA5dZwWd2QTX5VqEJHvGTefhptegb8qiU38/vxjwz0GJA/SBQ9Q=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1328980727901, "_hasShrinkwrap": false, "_cnpm_publish_time": 1328980727901, "_cnpmcore_publish_time": "2021-12-13T13:30:02.796Z"}, "1.2.1": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.2.1", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_id": "less@1.2.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-10", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.2.1.tgz", "shasum": "b76fd02c2ebc5d0805c59b9529951f22a45b65e7", "size": 417664, "noattachment": false, "integrity": "sha512-aajDwOS9Z59LjgD6B2BMeekVh1MIZXaRwHGUX5mSJMH6rx++m4S079S3OTBw5T1cajRLCAfuCoaBgvtWRe6iaw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1327315228587, "_hasShrinkwrap": false, "_cnpm_publish_time": 1327315228587, "_cnpmcore_publish_time": "2021-12-13T13:30:05.075Z"}, "1.2.0": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.2.0", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_id": "less@1.2.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.2.0.tgz", "shasum": "2762a23b042438cd0ced0cc2a74b66f7725e419f", "size": 389120, "noattachment": false, "integrity": "sha512-PEDRVKj3Af3cZ1SPnac67/6nCIMUfXCH1PhGIyQ7lTlg1BlCSlozw9RQQefhOyHoYoN0vA1575utKTyvvAjvwA=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1326233295843, "_hasShrinkwrap": false, "_cnpm_publish_time": 1326233295843, "_cnpmcore_publish_time": "2021-12-13T13:30:07.146Z"}, "1.1.6": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.1.6", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_id": "less@1.1.6", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.1.6.tgz", "shasum": "9609e99324286bd7049bc35649e997b6f90fcc78", "size": 337920, "noattachment": false, "integrity": "sha512-SCMDyUIMlwrIQHvjlbrAPps+H+1z1V2p14FPcBTwx50jMOfoB8gfw8YTrRQFrj1uC1C0eOyxcVYbr1rUdM7BFw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1323523253491, "_hasShrinkwrap": false, "_cnpm_publish_time": 1323523253491, "_cnpmcore_publish_time": "2021-12-13T13:30:09.234Z"}, "1.1.5": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.1.5", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_id": "less@1.1.5", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.1.5.tgz", "shasum": "802e9ceedd6b221bae57e1adb930eeb9efd31d6a", "size": 296960, "noattachment": false, "integrity": "sha512-EVKkXwcdx1VrBEYeLZJxgUQWH8yeWZZT4VsIO294E2xRyXhXHcKIqWC8VBjXeTcKOKfUrh4ZWuJKSjTmygrkcw=="}, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "publish_time": 1321268554213, "_hasShrinkwrap": false, "_cnpm_publish_time": 1321268554213, "_cnpmcore_publish_time": "2021-12-13T13:30:11.363Z"}, "1.1.4": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.1.4", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.4.0"}, "dependencies": {}, "devDependencies": {}, "_id": "less@1.1.4", "_engineSupported": true, "_npmVersion": "1.0.1rc7", "_nodeVersion": "v0.4.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.1.4.tgz", "shasum": "cbc5714f77ff209d89db569cb01dc4b7266de135", "size": 216022, "noattachment": false, "integrity": "sha512-MqCZPYvIwtyuXUIrcF2TDhmTcOeu44Ylku5PZxf1aly0kLrTXNt/4/B+jYYdOPKPPA5Fb/hzYve9KSRCL92NKQ=="}, "publish_time": 1311117019340, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311117019340, "_cnpmcore_publish_time": "2021-12-13T13:30:13.478Z"}, "1.1.2": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.1.2", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test", "lib": "./lib", "bin": "./bin"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.1.2", "_engineSupported": true, "_npmVersion": "0.2.14-6", "_nodeVersion": "v0.4.0", "modules": {"less/browser.js": "lib/less/browser.js", "less/functions.js": "lib/less/functions.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/tree.js": "lib/less/tree.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/javascript.js": "lib/less/tree/javascript.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/url.js": "lib/less/tree/url.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/variable.js": "lib/less/tree/variable.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.1.2.tgz", "shasum": "be31f2285d8534ef7a2b48532f0727eb190bdde9", "size": 174458, "noattachment": false, "integrity": "sha512-gwEgEuPLN+DWSN+aF9iFRChKIvriqJG2habioonGrmU/APuGd5A7AgsfXqZo5w2avNcJw+lhqkKpHKJPRzIoIg=="}, "publish_time": 1306270013915, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306270013915, "_cnpmcore_publish_time": "2021-12-13T13:30:15.421Z"}, "1.1.1": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.1.1", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test", "lib": "./lib", "bin": "./bin"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.1.1", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.5", "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.1.1.tgz", "shasum": "7bda147c7cdb16f1fc638ca59f2a29e21f7b20f1", "size": 140679, "noattachment": false, "integrity": "sha512-/u8yE3by4NOkkgVsvW2GZRtvYGyjDvnQXPvHyf2E18328Wn3AvHKcDTXeJP9F2upKzLDQyQJuBDSqU7pdccYqw=="}, "publish_time": 1305680046341, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305680046341, "_cnpmcore_publish_time": "2021-12-13T13:30:17.811Z"}, "1.1.0": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.1.0", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test", "lib": "./lib", "bin": "./bin"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.1.0", "_engineSupported": true, "_npmVersion": "0.2.14-6", "_nodeVersion": "v0.4.0", "modules": {"less/browser.js": "lib/less/browser.js", "less/functions.js": "lib/less/functions.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/tree.js": "lib/less/tree.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/javascript.js": "lib/less/tree/javascript.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/url.js": "lib/less/tree/url.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/variable.js": "lib/less/tree/variable.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.1.0.tgz", "shasum": "7c7dbdf1541158bf525d51a8eb8357400b72c888", "size": 109788, "noattachment": false, "integrity": "sha512-Zj9<PERSON>umbwoEarCKpM2fRD18bIPmpJWcQjJ7oOJwYzHafk593/ZLsJ0dopMiavBjgnbQtr6ZJpwVyPcf7lvykOCQ=="}, "publish_time": 1305143377162, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305143377162, "_cnpmcore_publish_time": "2021-12-13T13:30:20.324Z"}, "1.0.44": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.44", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test", "lib": "./lib", "bin": "./bin"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.0.44", "_engineSupported": true, "_npmVersion": "0.2.14-6", "_nodeVersion": "v0.4.0", "modules": {"less/browser.js": "lib/less/browser.js", "less/functions.js": "lib/less/functions.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/tree.js": "lib/less/tree.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/javascript.js": "lib/less/tree/javascript.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/url.js": "lib/less/tree/url.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/variable.js": "lib/less/tree/variable.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.44.tgz", "shasum": "f5438d1955e1bfbc3beae4f4266907de9a721ccb", "size": 107858, "noattachment": false, "integrity": "sha512-ZfmeOL16uHaUBvzXr7Yhqm6ddlXk15uxtWo6hxY02zqrqX1m+KERbQql57y/2ffRquI6wUuJZbZEqt0i3Quq8w=="}, "publish_time": 1305036351397, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305036351397, "_cnpmcore_publish_time": "2021-12-13T13:30:22.764Z"}, "1.0.5": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": "lib", "version": "1.0.5", "_id": "less@1.0.5", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.5.tgz", "shasum": "4aa5f1e3005ca582480e33b90831d67f80b88536", "size": 51056, "noattachment": false, "integrity": "sha512-q0LX9p1xyFQmwI9Mk0BlPLnuVknphJcwg/rgdwi037sLXyiER78mO/Pyfvj/Pv/YAurY8Sq4rX1pTqRo9COYDw=="}, "directories": {}, "publish_time": 1295821687816, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687816, "_cnpmcore_publish_time": "2021-12-13T13:30:24.714Z"}, "1.0.41": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.41", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test", "lib": "./lib", "bin": "./bin"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.0.41", "_engineSupported": true, "_npmVersion": "0.2.15", "_nodeVersion": "v0.3.7-pre", "modules": {"less/index.js": "lib/less/index.js", "less/tree.js": "lib/less/tree.js", "less/functions.js": "lib/less/functions.js", "less/parser.js": "lib/less/parser.js", "less/browser.js": "lib/less/browser.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/url.js": "lib/less/tree/url.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/javascript.js": "lib/less/tree/javascript.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/variable.js": "lib/less/tree/variable.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/expression.js": "lib/less/tree/expression.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.41.tgz", "shasum": "c108f97a105887d6559be8ed6a0ce922c58da1bf", "size": 86760, "noattachment": false, "integrity": "sha512-ME4EXthpaadkHfXBom1Q0o6LxMtpP0Qx4kzxPA/a2BEMR2ULmEr+UGq+5rJKx7GyKliAwCMdjmZWoNcKpcFADw=="}, "publish_time": 1295821687816, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687816, "_cnpmcore_publish_time": "2021-12-13T13:30:26.577Z"}, "1.0.40": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.40", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.0.40", "_nodeSupported": true, "_npmVersion": "0.2.8", "_nodeVersion": "v0.2.5", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.40.tgz", "shasum": "02eebb15194173507b421af668713063099e08cf", "size": 232340, "noattachment": false, "integrity": "sha512-EPR74JpWW08zf1OaC8i/o34ajc3xEDIC5OIdfmAw06aKL4cevA+PCTnUqMVCW+K6GuMGJ6AA9NFWrAtDKVmVQg=="}, "publish_time": 1295821687816, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687816, "_cnpmcore_publish_time": "2021-12-13T13:30:28.774Z"}, "1.0.14": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.14", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.14", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.14.tgz", "shasum": "0c7c203d4fb59d987a417b6c2f6b516df4b9d66a", "size": 74295, "noattachment": false, "integrity": "sha512-3A+9Kd+vFr7OALIEyGpPE/mTq1IZymgVJ71juT4YD+AUN0YoV3SvAUUy3bdwocv9ekAnaZhjyP8r4s3Jb0PVpQ=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:30.691Z"}, "1.0.36": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.36", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"test": "./test"}, "engines": {"node": ">=0.2.0"}, "_id": "less@1.0.36", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.36.tgz", "shasum": "d64632580d90ac278a857dbf27ab98017797bc21", "size": 229443, "noattachment": false, "integrity": "sha512-Cyu/HvFkV+xE3fMuZVDhwLP3EjjQ9rQ/dK7q5c051laYHXQg5rgy4FQVVZXHi+y4ZgtpaU4y/qGTR2Vx4a2/ww=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:32.704Z"}, "1.0.32": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.32", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.32", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.32.tgz", "shasum": "0079c9d3b87b31e5d24926da5cded54730085797", "size": 81362, "noattachment": false, "integrity": "sha512-2CWeUw6H9y69U9IBMdvfGBlbOlVlX9yejqdBAdVMfVgPPsyD/+njxIMnc/X50XnMcrUgtfl9K0zoFCfLSZEiWw=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:34.885Z"}, "1.0.21": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.21", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.21", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.21.tgz", "shasum": "a9728759e192ebf9270666e2d8c2805fc72e22b0", "size": 76878, "noattachment": false, "integrity": "sha512-NdjJwcqZxPcw1APUbiNgytsfq38bAGtDTuLA/Ivw+SsLV+xLzEoATwBOttsrU/OqBD/umywj4U31BwPVSqmB7w=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:36.759Z"}, "1.0.19": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.19", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.19", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.19.tgz", "shasum": "8db3980ddabcbf830e40a07ead46b079ce442639", "size": 76159, "noattachment": false, "integrity": "sha512-YeYieywDYhk3YCnygU42nb7UVnsd43Jv7YVbHInvXv+2vGUaGT/Rn6lt1m0ArN+qSEDzRSwfaJOCleo/5lr6kA=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:39.274Z"}, "1.0.18": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.18", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.18", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.18.tgz", "shasum": "30d5f6144b0b7148ac9732616c0016757bb436d1", "size": 75934, "noattachment": false, "integrity": "sha512-uynxAsi6LoDzcpgJeEmcAHq84h42DYsnLP+lZragDUMYlZY3SOu/EmkVJVXV5/KKEz7i1jzh6ydcjEpRIMi0SQ=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:41.832Z"}, "1.0.11": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.11", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.11", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.11.tgz", "shasum": "f15edd673538d681f4a764759efb574776030efb", "size": 73246, "noattachment": false, "integrity": "sha512-RJER4EOhMEgf24t0dLPnbw3X/JUypwYewXtrtjB5+cYYCUwgy0r/1aRPZS222bnEgvp49nEXXm+9rjPQ2Qe2Bg=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:43.735Z"}, "1.0.10": {"name": "less", "description": "Leaner CSS", "url": "http://lesscss.org", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "version": "1.0.10", "bin": {"lessc": "./bin/lessc"}, "main": "./lib/less/index", "directories": {"lib": "./lib/less", "test": "./test"}, "engines": {"node": ">=0.1.93"}, "_id": "less@1.0.10", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/less/-/less-1.0.10.tgz", "shasum": "07e3b9bf39e5d780ec1232a6f99a389f1e59cbf2", "size": 71943, "noattachment": false, "integrity": "sha512-ILPuVu20MO5MN5epx+Rjimg35A851CQG0uGU4T5AayT4EMJaOLz9ocpC59vJpc1sE8EBB0KetgNXwpmS/dmquQ=="}, "publish_time": 1295821687815, "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1295821687815, "_cnpmcore_publish_time": "2021-12-13T13:30:45.388Z"}, "4.1.3": {"name": "less", "version": "4.1.3", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.1.0", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^9.1.1", "typescript": "^4.3.4", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "_id": "less@4.1.3", "_nodeVersion": "14.19.0", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==", "shasum": "175be9ddcbf9b250173e0a00b4d6920a5b770246", "tarball": "https://registry.npmmirror.com/less/-/less-4.1.3.tgz", "fileCount": 317, "unpackedSize": 2782784, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoTWs0FfxRqbiy3yot/HwfI5UnakcUT34JoiuT23HqVwIgP7x0ayOzmEVp2DFvDTu9pi0IVqEANzWDKQFqXegOwjM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioPi/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxMQ//fFs8UKQkMKKoWk5njMqbFqY+S25xDJY5BQOb2+ItNYC93cMK\r\nCBTVsSDZ0TDbHbtnLolV/kMTwniOD61hrurSHDxjItGm5Uz8e28pyKAGp42A\r\n0KQ5bMr4JhWucptO73levKmTCC7G5Gi9mxQ5Cjcw9dEs0LzKchN7KX/kIJSs\r\nNxyzXauiHm2rcSoRR+kB00bZUBke44B79wLUBIRE7G6LpreKHHIGZkaVqRg7\r\n06DtK/VyHYna2bCZzkBrT4onWRN/WIubiu6+8GvlBMYwpBkyuo2fm9Ab0xiq\r\nHXeC9Gr/bEdabYoKlXgCCyvhg9zYzMFgRqjba/+XV49fNc1mBvb+UmmEAg2E\r\n9S3ARZm6lLYiHbAporGv8AvubJtGuvIcPP4ix/KZ7ewPH61wwDhl6hJRcDdl\r\nRHkQA6YVD00D5Qx+xcWS8aH+WokBimHQeF/eLv1LhvI5+OjrurC83QL97+Pa\r\nYRrWXquXQF6oJnnAnU9z36ifRTyDSdLzvBXfa4igu/XGl5JBNjAm1x3c2CeF\r\n+WBCifM/MAOZ20TDUn84ek8fhE7IViv9xyjMyAwNUqxyBXlF9BDVoTblafz5\r\nMzPXRxFc1Xl2Rylfz2NQmWwV4G+LIogCvOFTDQS4FCCQ+/lkKSFp/LSOHrKX\r\n3pt6apwCuLuEZFNH4BDzT7JNs56feicVzS4=\r\n=cQPX\r\n-----END PGP SIGNATURE-----\r\n", "size": 629874}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.1.3_1654716607254_0.4279865668758507"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-08T20:15:32.084Z"}, "4.2.0": {"name": "less", "version": "4.2.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "lint": "eslint '**/*.{ts,js}'", "lint:fix": "eslint '**/*.{ts,js}' --fix", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.build.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.build.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.2.0", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^4.0.0", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^9.1.1", "typescript": "^4.3.4", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "_id": "less@4.2.0", "_nodeVersion": "18.13.0", "_npmVersion": "lerna/7.1.4/node@v18.13.0+arm64 (darwin)", "dist": {"integrity": "sha512-P3b3HJDBtSzsXUl0im2L7gTO5Ubg8mEN6G8qoTS77iXxXX4Hvu4Qj540PZDvQ8V6DmX6iXo98k7Md0Cm1PrLaA==", "shasum": "cbefbfaa14a4cd388e2099b2b51f956e1465c450", "tarball": "https://registry.npmmirror.com/less/-/less-4.2.0.tgz", "fileCount": 328, "unpackedSize": 2835245, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGzbnDfEnux0fLT7bMnteU/QFYG/0HA0hiin09RAky1AiEAipIbenf2XMzabY38IWWozlG3hbL1vAzRuAapgiXZF1I="}], "size": 643360}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.2.0_1691255895467_0.8835848059926328"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-05T17:18:15.731Z", "publish_time": 1691255895731, "_source_registry_name": "default"}, "4.2.1": {"name": "less", "version": "4.2.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "lint": "eslint '**/*.{ts,js}'", "lint:fix": "eslint '**/*.{ts,js}' --fix", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.build.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.build.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.2.1", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^4.0.0", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^10.9.1", "typescript": "^4.3.4", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "_id": "less@4.2.1", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-CasaJidTIhWmjcqv0Uj5vccMI7pJgfD9lMkKtlnTHAdJdYK/7l8pM9tumLyJ0zhbD4KJLo/YvTj+xznQd5NBhg==", "shasum": "fe4c9848525ab44614c0cf2c00abd8d031bb619a", "tarball": "https://registry.npmmirror.com/less/-/less-4.2.1.tgz", "fileCount": 330, "unpackedSize": 2856343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqRiApA0Pi6oJY+6dbzY01sTWmSE2niODqgbqDSkrSlAiEAgdfWNJfqqPaNIG6nSnW0sQBcO81caD/3SdWyjQ8IgX8="}], "size": 636341}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/less_4.2.1_1732554385163_0.24856165099652205"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-25T17:06:25.428Z", "publish_time": 1732554385428, "_source_registry_name": "default"}, "4.2.2": {"name": "less", "version": "4.2.2", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt", "lint": "eslint '**/*.{ts,js}'", "lint:fix": "eslint '**/*.{ts,js}' --fix", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.build.json", "copy:root": "shx cp -rf ./dist ../../", "dev": "tsc -p tsconfig.build.json -w", "prepublishOnly": "grunt dist"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "devDependencies": {"@less/test-data": "^4.2.2", "@less/test-import-module": "^4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.6.0", "minimist": "^1.2.0", "mocha": "^6.2.1", "playwright": "^1.49.0", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^10.9.1", "typescript": "^4.3.4", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "_id": "less@4.2.2", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-tkuLHQlvWUTeQ3doAqnHbNn8T6WX1KA8yvbKG9x4VtKtIjHsVKQZCH11zRgAfbDAXC2UNIg/K9BYAAcEzUIrNg==", "shasum": "4b59ede113933b58ab152190edf9180fc36846d8", "tarball": "https://registry.npmmirror.com/less/-/less-4.2.2.tgz", "fileCount": 331, "unpackedSize": 2877698, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8RafHmstDNAuJhXiZZUe7z8Hma9YAxVR65E7y8m1l5AiBbkkjAHIz4RaouWqmA/xtcprmg7JDFhEJiPu+W7p0xUQ=="}], "size": 640753}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/less_4.2.2_1737312419368_0.020686435163520978"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-19T18:46:59.651Z", "publish_time": 1737312419651, "_source_registry_name": "default"}, "4.3.0": {"name": "less", "version": "4.3.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=14"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.6.0", "minimist": "^1.2.0", "mocha": "^6.2.1", "playwright": "1.50.1", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^10.9.1", "typescript": "^4.3.4", "uikit": "2.27.4", "@less/test-data": "4.3.0", "@less/test-import-module": "4.0.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "scripts": {"test": "grunt test", "grunt": "grunt", "lint": "eslint '**/*.{ts,js}'", "lint:fix": "eslint '**/*.{ts,js}' --fix", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.build.json", "dev": "tsc -p tsconfig.build.json -w"}, "_id": "less@4.3.0", "_integrity": "sha512-X9RyH9fvemArzfdP8Pi3irr7lor2Ok4rOttDXBhlwDg+wKQsXOXgHWduAJE1EsF7JJx0w0bcO6BC6tCKKYnXKA==", "_resolved": "/private/var/folders/fp/sdnhgkpn4c12nbb913bxnpym0000gn/T/2b785adbaa2d8929eaa4758170138074/less-4.3.0.tgz", "_from": "file:less-4.3.0.tgz", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-X9RyH9fvemArzfdP8Pi3irr7lor2Ok4rOttDXBhlwDg+wKQsXOXgHWduAJE1EsF7JJx0w0bcO6BC6tCKKYnXKA==", "shasum": "ef0cfc260a9ca8079ed8d0e3512bda8a12c82f2a", "tarball": "https://registry.npmmirror.com/less/-/less-4.3.0.tgz", "fileCount": 332, "unpackedSize": 2940538, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHHpH8n18rTXElEmJZ75fzuPTTUi4g98xQebeUHidhq2AiB3lUu1/SHPEZ8Kn7Z+mq8ObHmqSXFNaX7SldvWetJYSw=="}], "size": 666417}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/less_4.3.0_1743864741811_0.058989322317403836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-05T14:52:22.026Z", "publish_time": 1743864742026, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/less/less.js/issues"}, "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "_source_registry_name": "default"}