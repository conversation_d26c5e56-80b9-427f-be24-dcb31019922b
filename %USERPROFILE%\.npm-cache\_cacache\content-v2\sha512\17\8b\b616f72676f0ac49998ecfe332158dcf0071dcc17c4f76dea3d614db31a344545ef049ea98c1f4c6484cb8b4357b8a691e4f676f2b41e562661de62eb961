{"_attachments": {}, "_id": "@types/eslint", "_rev": "7636-61f160824ce7cf8f5828cf5c", "description": "TypeScript definitions for eslint", "dist-tags": {"latest": "9.6.1", "ts2.2": "6.1.3", "ts2.3": "6.1.3", "ts2.4": "6.1.3", "ts2.5": "6.1.3", "ts2.6": "6.1.3", "ts2.7": "6.1.3", "ts2.8": "6.8.0", "ts2.9": "6.8.0", "ts3.0": "7.2.0", "ts3.1": "7.2.2", "ts3.2": "7.2.5", "ts3.3": "7.2.6", "ts3.4": "7.2.6", "ts3.5": "7.2.12", "ts3.6": "7.28.0", "ts3.7": "8.2.0", "ts3.8": "8.4.1", "ts3.9": "8.4.3", "ts4.0": "8.4.6", "ts4.1": "8.4.10", "ts4.2": "8.21.2", "ts4.3": "8.44.2", "ts4.4": "8.44.2", "ts4.5": "8.44.7", "ts4.6": "8.56.5", "ts4.7": "8.56.10", "ts4.8": "9.6.1", "ts4.9": "9.6.1", "ts5.0": "9.6.1", "ts5.1": "9.6.1", "ts5.2": "9.6.1", "ts5.3": "9.6.1", "ts5.4": "9.6.1", "ts5.5": "9.6.1", "ts5.6": "9.6.1", "ts5.7": "9.6.1"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/eslint", "readme": "# Installation\r\n> `npm install --save @types/eslint`\r\n\r\n# Summary\r\nThis package contains type definitions for eslint (https://eslint.org).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint.\r\n\r\n### Additional Details\r\n * Last updated: Mon, 26 Aug 2024 07:08:02 GMT\r\n * Dependencies: [@types/estree](https://npmjs.com/package/@types/estree), [@types/json-schema](https://npmjs.com/package/@types/json-schema)\r\n\r\n# Credits\r\nThese definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/pmdartus), [<PERSON>](https://github.com/j-f1), [<PERSON><PERSON>](https://github.com/saadq), [<PERSON>](https://github.com/JasonHK), [<PERSON>](https://github.com/bradzacher), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON><PERSON>), and [<PERSON>](https://github.com/bmish).\r\n", "time": {"created": "2022-01-26T14:53:54.786Z", "modified": "2024-08-26T13:36:00.339Z", "8.2.1": "2021-12-03T18:31:31.603Z", "8.2.0": "2021-11-17T21:01:58.560Z", "7.29.0": "2021-11-15T07:31:44.601Z", "7.28.2": "2021-10-21T23:02:13.835Z", "7.28.1": "2021-10-08T17:01:53.867Z", "7.28.0": "2021-07-13T00:01:49.704Z", "7.2.14": "2021-07-06T19:12:03.733Z", "7.2.13": "2021-06-01T21:32:16.975Z", "7.2.12": "2021-05-27T01:01:40.561Z", "7.2.11": "2021-05-21T02:31:51.050Z", "7.2.10": "2021-04-14T18:31:31.208Z", "7.2.9": "2021-04-09T22:01:41.465Z", "7.2.8": "2021-03-31T00:31:36.025Z", "7.2.7": "2021-03-07T10:09:37.212Z", "7.2.6": "2020-12-02T09:03:50.959Z", "7.2.5": "2020-11-16T10:03:08.663Z", "7.2.4": "2020-10-08T16:14:16.555Z", "7.2.3": "2020-09-23T19:18:10.724Z", "7.2.2": "2020-08-27T18:14:56.405Z", "7.2.1": "2020-08-21T17:07:31.377Z", "7.2.0": "2020-06-11T23:32:29.535Z", "6.8.1": "2020-05-15T05:08:03.950Z", "6.8.0": "2020-04-06T20:24:29.003Z", "6.1.9": "2020-03-31T00:34:09.286Z", "6.1.8": "2020-02-04T17:48:05.935Z", "6.1.7": "2020-01-25T01:02:39.418Z", "6.1.6": "2020-01-23T18:25:34.895Z", "6.1.5": "2020-01-22T19:53:09.173Z", "6.1.4": "2020-01-22T00:52:26.870Z", "6.1.3": "2019-10-23T22:11:03.766Z", "6.1.2": "2019-10-02T22:03:59.002Z", "6.1.1": "2019-08-23T15:40:46.247Z", "6.1.0": "2019-08-20T19:18:03.388Z", "4.16.8": "2019-08-09T15:29:49.133Z", "4.16.7": "2019-08-08T22:50:40.834Z", "4.16.6": "2019-01-28T20:34:16.369Z", "4.16.5": "2018-12-03T21:37:15.664Z", "4.16.4": "2018-10-24T15:38:31.181Z", "4.16.3": "2018-06-30T02:50:41.663Z", "4.16.2": "2018-05-08T16:42:00.364Z", "4.16.1": "2018-04-24T23:33:06.813Z", "4.16.0": "2018-02-13T20:55:38.797Z", "8.2.2": "2022-01-07T00:01:43.831Z", "8.4.0": "2022-01-19T23:31:58.151Z", "8.4.1": "2022-01-22T19:01:43.908Z", "8.4.2": "2022-05-03T16:32:02.900Z", "8.4.3": "2022-06-08T19:31:59.735Z", "8.4.4": "2022-06-30T19:02:45.546Z", "8.4.5": "2022-06-30T21:32:23.538Z", "8.4.6": "2022-08-19T02:32:33.336Z", "8.4.7": "2022-10-20T11:03:10.398Z", "8.4.8": "2022-10-25T09:32:43.610Z", "8.4.9": "2022-10-28T21:02:46.731Z", "8.4.10": "2022-11-04T15:03:13.775Z", "8.21.0": "2023-02-02T13:02:44.034Z", "8.21.1": "2023-02-14T07:32:37.448Z", "8.21.2": "2023-03-13T19:36:27.415Z", "8.21.3": "2023-03-19T19:34:41.798Z", "8.37.0": "2023-03-29T21:03:09.062Z", "8.40.0": "2023-05-23T02:02:48.588Z", "8.40.1": "2023-06-08T20:33:48.777Z", "8.40.2": "2023-06-13T07:02:46.838Z", "8.44.0": "2023-07-07T16:02:44.553Z", "8.44.1": "2023-07-25T10:03:05.849Z", "8.44.2": "2023-08-04T20:19:55.645Z", "8.44.3": "2023-09-23T17:22:10.040Z", "8.44.4": "2023-10-10T18:13:11.413Z", "8.44.5": "2023-10-18T01:57:18.620Z", "8.44.6": "2023-10-18T18:18:11.464Z", "8.44.7": "2023-11-07T02:21:57.842Z", "8.44.8": "2023-11-30T01:31:19.955Z", "8.44.9": "2023-12-12T23:35:55.815Z", "8.56.0": "2023-12-20T01:21:05.015Z", "8.56.1": "2024-01-02T23:06:57.910Z", "8.56.2": "2024-01-11T02:18:24.796Z", "8.56.3": "2024-02-22T22:35:18.133Z", "8.56.4": "2024-02-27T20:07:22.330Z", "8.56.5": "2024-02-29T00:50:16.453Z", "8.56.6": "2024-03-19T09:36:07.855Z", "8.56.7": "2024-04-01T12:42:35.272Z", "8.56.8": "2024-04-11T10:07:34.780Z", "8.56.9": "2024-04-12T05:07:20.085Z", "8.56.10": "2024-04-18T18:35:26.656Z", "9.6.0": "2024-07-22T16:39:09.306Z", "8.56.11": "2024-07-22T16:39:18.967Z", "9.6.1": "2024-08-26T07:08:11.087Z", "8.56.12": "2024-08-26T07:08:20.669Z"}, "versions": {"8.2.1": {"name": "@types/eslint", "version": "8.2.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "870a79c2a1e496009b4a6a1995b6cc4e13b98506af6ba0ca7882e395181e1ccf", "typeScriptVersion": "3.8", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.2.1", "dist": {"integrity": "sha512-UP9rzNn/XyGwb5RQ2fok+DzcIRIYwc16qTXse5+Smsy8MOIccCChT15KAwnsgQx4PzJkaMq4myFyZ4CL5TjhIQ==", "shasum": "13f3d69bac93c2ae008019c28783868d0a1d6605", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.2.1.tgz", "fileCount": 16, "unpackedSize": 162400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqmKDCRA9TVsSAnZWagAAcQ0P/3AD26IHWs9NTCCqnhd9\nzVAjEtRG+Im6+OIhtLDbB8gui/bWkYNL1TRefED5MGGz6drKppA6URH/0vMx\nptKSISGYJQIihKqQxWp3nDZ2RQn1N8GyA86GnzWHRx03xYMtNeO+WhSDNvMM\niF8OgtHnpfU7k3Ax3l4YWTha5Arot94rUoQtmfkHfVzt/Z0dEkXd+timCXl9\nnHSL6CCYy1teWG6+gqYEY3zDjZR1x3r+OWoNKV8qKHui8r8+s53tlaYmv6es\nUaG/nOjDQIUC8nNawP8KEBXSv4B4nA+qmwRoKQZ1VWibos2cuJ1ZoFSfWLKR\nlHwuS2AtaX6GiwxyNfr2QyCo+3y0/SfR5GSXwUVOX/U3u/Ngtc1T3gBuxsn1\nLT+aQsPobnosGzpJxUDGKgD0m6mfDXtPKAbnENUGnQZX20fY2L6TqGcZLKfr\niqOwtxJkT88SnjSnOVo2xHd5I1woEOCLm75ntqpMYe/BNcnsii8fVsq0ji+I\nZhx1QK03hKx11H5a1+8hiB4GGPEgndScGMpmN+C98/6DcH6jE/o2J87Qpg8V\nDT1xhrwfywuXOaTlU0G3RA0BjHkTap6DWZvX/vDzvf57qbTR6aRaWxfqwlYo\nAGN00aRZal/0Irkv9Qzw/8WCMQRpyaKc3vjRnPbYKyY/lhhdeGa/5pEojhrm\nKo7t\r\n=LQSh\r\n-----END PGP SIGNATURE-----\r\n", "size": 22549, "noattachment": false}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.2.1_1638556291408_0.40602680650898026"}, "_hasShrinkwrap": false, "publish_time": 1638556291603, "_cnpm_publish_time": 1638556291603, "_cnpmcore_publish_time": "2021-12-16T10:12:42.448Z"}, "8.2.0": {"name": "@types/eslint", "version": "8.2.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "75f8630ee832c7873360b05069ae241180c3b9cfcda4af43a1647926b09232ac", "typeScriptVersion": "3.7", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}}, "_id": "@types/eslint@8.2.0", "dist": {"integrity": "sha512-74hbvsnc+7TEDa1z5YLSe4/q8hGYB3USNvCuzHUJrjPV6hXaq8IXcngCrHkuvFt0+8rFz7xYXrHgNayIX0UZvQ==", "shasum": "afd0519223c29c347087542cbaee2fedc0873b16", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.2.0.tgz", "fileCount": 16, "unpackedSize": 162217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlW3GCRA9TVsSAnZWagAAv3oP/2Q9Ujm1Y3OBGAF3tNeb\nHgk5SuicplN4WL+b1GJyrP4a9eRlrhbmiEQOurgsX5Ll0d+S7JimAPVLVLHo\n8xPw+YGnk9aVzs1Zdu1cEHtQsF3tBqofRejbZuo/sT1JDxyDY6ClhwNb+TZJ\nYHF6xVla8qj9NA7NidG+71CFuaw7XzD+8+MiCwxt55TQp9eRpfDQ59k+Mtmf\nMFTbp6jcsAeBjPaPhkP52+qdaylNRxeKzQidV+Xv2cqLNRH44x4QiwgvJXwu\ntRzvnh950lThei+kwcp/f/a5fzvK/n8dJDsz7We4L5pjgpacQn9AjS1y2KQ/\nzacGsXuuYDORVp5wB8Ywmyihrm7a0begXK8DqonHitgGJ535J3ZRee7Af7JS\nYn8IOWuuzvlrH3ODBIIn7/y0mxJ3XMWvOK+7tCo0I0H/K6jPouIkapsvSTcb\nropOsJmHmCcDRYd9FShXDY3tvU9kKugW7eLPNkl7ticHOmKvnChj6ZbBvSE3\nM6fHPN8EnYSC+0N7g75sOVoWZnbsfxLMSc9krdNTEAg7vvSwa+C9H/F4uMta\nRFUwjVN50IERxY4LHx+uaUILLfRW9OIKQ9NvXuCHOnrK38AXLMUaSlOJ4jHX\nybxJTaouUPqloYO5N8ETkQ3mUaxSzvR2F/TF9dyiUsBGtqCdJW76zjkLiR7N\nxGNj\r\n=hL2P\r\n-----END PGP SIGNATURE-----\r\n", "size": 22533, "noattachment": false}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.2.0_1637182918402_0.5936297768360583"}, "_hasShrinkwrap": false, "publish_time": 1637182918560, "_cnpm_publish_time": 1637182918560, "_cnpmcore_publish_time": "2021-12-16T10:12:43.120Z"}, "7.29.0": {"name": "@types/eslint", "version": "7.29.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "e2557f89ae9e7e511e390df57120d359d2dcefc845227cc2c4e484114cf82209", "typeScriptVersion": "3.7", "_id": "@types/eslint@7.29.0", "dist": {"integrity": "sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng==", "shasum": "e56ddc8e542815272720bb0b4ccc2aff9c3e1c78", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.29.0.tgz", "fileCount": 18, "unpackedSize": 164528, "size": 22791, "noattachment": false}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.29.0_1636961504428_0.12696568614185177"}, "_hasShrinkwrap": false, "publish_time": 1636961504601, "_cnpm_publish_time": 1636961504601, "_cnpmcore_publish_time": "2021-12-16T10:12:43.318Z"}, "7.28.2": {"name": "@types/eslint", "version": "7.28.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "d010b55521e5c72f41e22c0a65ea40789ef44c108b1fed8557cd62a3be6df537", "typeScriptVersion": "3.7", "_id": "@types/eslint@7.28.2", "dist": {"shasum": "0ff2947cdd305897c52d5372294e8c76f351db68", "size": 22734, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.28.2.tgz", "integrity": "sha512-KubbADPkfoU75KgKeKLsFHXnU4ipH7wYg0TRT33NK3N3yiu7jlFAAoygIWBV+KbuHx/G+AvuGX6DllnK35gfJA=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.28.2_1634857333638_0.27588465167798293"}, "_hasShrinkwrap": false, "publish_time": 1634857333835, "_cnpm_publish_time": 1634857333835, "_cnpmcore_publish_time": "2021-12-16T10:12:43.541Z"}, "7.28.1": {"name": "@types/eslint", "version": "7.28.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "ead3341b417b1fe8a2825d96ddb954f3dad2633c5eb166e057c130a2c7db3605", "typeScriptVersion": "3.7", "_id": "@types/eslint@7.28.1", "dist": {"shasum": "50b07747f1f84c2ba8cd394cf0fe0ba07afce320", "size": 22726, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.28.1.tgz", "integrity": "sha512-XhZKznR3i/W5dXqUhgU9fFdJekufbeBd5DALmkuXoeFcjbQcPk+2cL+WLHf6Q81HWAnM2vrslIHpGVyCAviRwg=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.28.1_1633712513694_0.6106098714235553"}, "_hasShrinkwrap": false, "publish_time": 1633712513867, "_cnpm_publish_time": 1633712513867, "_cnpmcore_publish_time": "2021-12-16T10:12:44.150Z"}, "7.28.0": {"name": "@types/eslint", "version": "7.28.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "adf659c6b648623f69f4a421e6eebb913ca1a4713274e38e6a716eb9ffad2b07", "typeScriptVersion": "3.6", "_id": "@types/eslint@7.28.0", "dist": {"shasum": "7e41f2481d301c68e14f483fe10b017753ce8d5a", "size": 22693, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.28.0.tgz", "integrity": "sha512-07XlgzX0YJUn4iG1ocY4IX9DzKSmMGUs6ESKlxWhZRaa0fatIWaHWUVapcuGa8r5HFnTqzj+4OCjd5f7EZ/i/A=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.28.0_1626134509449_0.2454516728790943"}, "_hasShrinkwrap": false, "publish_time": 1626134509704, "_cnpm_publish_time": 1626134509704, "_cnpmcore_publish_time": "2021-12-16T10:12:44.484Z"}, "7.2.14": {"name": "@types/eslint", "version": "7.2.14", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "3d5ef47c35b83925c83d1dd33014c385878c067dd44dcf0d6bba8ea0deb45ceb", "typeScriptVersion": "3.6", "_id": "@types/eslint@7.2.14", "dist": {"shasum": "088661518db0c3c23089ab45900b99dd9214b92a", "size": 22677, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.14.tgz", "integrity": "sha512-pESyhSbUOskqrGcaN+bCXIQDyT5zTaRWfj5ZjjSlMatgGjIn3QQPfocAu4WSabUR7CGyLZ2CQaZyISOEX7/saw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.14_1625598723587_0.22959501031656337"}, "_hasShrinkwrap": false, "publish_time": 1625598723733, "_cnpm_publish_time": 1625598723733, "_cnpmcore_publish_time": "2021-12-16T10:12:44.797Z"}, "7.2.13": {"name": "@types/eslint", "version": "7.2.13", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "72e22eab0ed876b82bb3671e338db1dce1d167cd46bde44964712aa7a51c0252", "typeScriptVersion": "3.6", "_id": "@types/eslint@7.2.13", "dist": {"shasum": "e0ca7219ba5ded402062ad6f926d491ebb29dd53", "size": 22596, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.13.tgz", "integrity": "sha512-LKmQCWAlnVHvvXq4oasNUMTJJb2GwSyTY8+1C7OH5ILR8mPLaljv1jxL1bXW3xB3jFbQxTKxJAvI8PyjB09aBg=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.13_1622583135786_0.5958203297765592"}, "_hasShrinkwrap": false, "publish_time": 1622583136975, "_cnpm_publish_time": 1622583136975, "_cnpmcore_publish_time": "2021-12-16T10:12:45.147Z"}, "7.2.12": {"name": "@types/eslint", "version": "7.2.12", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "432517d2280762636905b40ad0cfa9f0d69797b0ca4768cadc568b62d969f72f", "typeScriptVersion": "3.5", "_id": "@types/eslint@7.2.12", "dist": {"shasum": "fefaa48a4db2415b621fe315e4baeedde525927e", "size": 22542, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.12.tgz", "integrity": "sha512-HjikV/jX6e0Pg4DcB+rtOBKSrG6w5IaxWpmi3efL/eLxMz5lZTK+W1DKERrX5a+mNzL78axfsDNXu7JHFP4uLg=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.12_1622077300427_0.6095869823142819"}, "_hasShrinkwrap": false, "publish_time": 1622077300561, "_cnpm_publish_time": 1622077300561, "_cnpmcore_publish_time": "2021-12-16T10:12:45.464Z"}, "7.2.11": {"name": "@types/eslint", "version": "7.2.11", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "748035a04c4ab49d176ca29428d102e8149c51da71d79ac5c2849c44cd8b09b1", "typeScriptVersion": "3.5", "_id": "@types/eslint@7.2.11", "dist": {"shasum": "180b58f5bb7d7376e39d22496e2b08901aa52fd2", "size": 22522, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.11.tgz", "integrity": "sha512-WYhv//5K8kQtsSc9F1Kn2vHzhYor6KpwPbARH7hwYe3C3ETD0EVx/3P5qQybUoaBEuUa9f/02JjBiXFWalYUmw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.11_1621564310921_0.6664059441984764"}, "_hasShrinkwrap": false, "publish_time": 1621564311050, "_cnpm_publish_time": 1621564311050, "_cnpmcore_publish_time": "2021-12-16T10:12:45.724Z"}, "7.2.10": {"name": "@types/eslint", "version": "7.2.10", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "b8878deb1bf1812b107e38165a72f68d01fdc7046d4fe273360a0dbb8aaa80ff", "typeScriptVersion": "3.5", "_id": "@types/eslint@7.2.10", "dist": {"shasum": "4b7a9368d46c0f8cd5408c23288a59aa2394d917", "size": 22187, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.10.tgz", "integrity": "sha512-kUEPnMKrqbtpCq/KTaGFFKAcz6Ethm2EjCoKIDaCmfRBWLbFuTcOJfTlorwbnboXBzahqWLgUp1BQeKHiJzPUQ=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.10_1618425090986_0.6555309481397991"}, "_hasShrinkwrap": false, "publish_time": 1618425091208, "_cnpm_publish_time": 1618425091208, "_cnpmcore_publish_time": "2021-12-16T10:12:45.917Z"}, "7.2.9": {"name": "@types/eslint", "version": "7.2.9", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "cbb8b23e30ca3c0b720420232e4a90a3c2e8844a354c7929d2253fd71b42c211", "typeScriptVersion": "3.5", "_id": "@types/eslint@7.2.9", "dist": {"shasum": "5d26eadbb6d04a225967176399a18eff622da982", "size": 22170, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.9.tgz", "integrity": "sha512-SdAAXZNvWfhtf3X3y1cbbCZhP3xyPh7mfTvzV6CgfWc/ZhiHpyr9bVroe2/RCHIf7gczaNcprhaBLsx0CCJHQA=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.9_1618005701321_0.7941446376885821"}, "_hasShrinkwrap": false, "publish_time": 1618005701465, "_cnpm_publish_time": 1618005701465, "_cnpmcore_publish_time": "2021-12-16T10:12:46.239Z"}, "7.2.8": {"name": "@types/eslint", "version": "7.2.8", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "6d6ab93e859c2754df367816155f29e76f9dc24036819af1e34be426b4abc33b", "typeScriptVersion": "3.5", "_id": "@types/eslint@7.2.8", "dist": {"shasum": "45cd802380fcc352e5680e1781d43c50916f12ee", "size": 21248, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.8.tgz", "integrity": "sha512-RTKvBsfz0T8CKOGZMfuluDNyMFHnu5lvNr4hWEsQeHXH6FcmIDIozOyWMh36nLGMwVd5UFNXC2xztA8lln22MQ=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.8_1617150695890_0.6502837380009825"}, "_hasShrinkwrap": false, "publish_time": 1617150696025, "_cnpm_publish_time": 1617150696025, "_cnpmcore_publish_time": "2021-12-16T10:12:46.570Z"}, "7.2.7": {"name": "@types/eslint", "version": "7.2.7", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "d808c693bc6d512919f19dbfe82ae442a207b2ff2dda435918659115682c381c", "typeScriptVersion": "3.5", "_id": "@types/eslint@7.2.7", "dist": {"shasum": "f7ef1cf0dceab0ae6f9a976a0a9af14ab1baca26", "size": 20768, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.7.tgz", "integrity": "sha512-EHXbc1z2GoQRqHaAT7+grxlTJ3WE2YNeD6jlpPoRc83cCoThRY+NUWjCUZaYmk51OICkPXn2hhphcWcWXgNW0Q=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.7_1615111777093_0.4846978878024353"}, "_hasShrinkwrap": false, "publish_time": 1615111777212, "_cnpm_publish_time": 1615111777212, "_cnpmcore_publish_time": "2021-12-16T10:12:46.794Z"}, "7.2.6": {"name": "@types/eslint", "version": "7.2.6", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "cc723455fed3f546efa58b85cf25e998118c610b26cbe13b5edfbd7012a0aac5", "typeScriptVersion": "3.3", "_id": "@types/eslint@7.2.6", "dist": {"shasum": "5e9aff555a975596c03a98b59ecd103decc70c3c", "size": 20766, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.6.tgz", "integrity": "sha512-I+1sYH+NPQ3/tVqCeUSBwTE/0heyvtXqpIopUUArlBm0Kpocb8FbMa3AZ/ASKIFpN3rnEx932TTXDbt9OXsNDw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.6_1606899830774_0.5136710869262406"}, "_hasShrinkwrap": false, "publish_time": 1606899830959, "_cnpm_publish_time": 1606899830959, "_cnpmcore_publish_time": "2021-12-16T10:12:47.014Z"}, "7.2.5": {"name": "@types/eslint", "version": "7.2.5", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "776c80c13d42d7d1018d5963f202cd6dd228c3bd0952fef43ba15d80e05a3f2b", "typeScriptVersion": "3.2", "_id": "@types/eslint@7.2.5", "dist": {"shasum": "92172ecf490c2fce4b076739693d75f30376d610", "size": 20773, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.5.tgz", "integrity": "sha512-Dc6ar9x16BdaR3NSxSF7T4IjL9gxxViJq8RmFd+2UAyA+K6ck2W+gUwfgpG/y9TPyUuBL35109bbULpEynvltA=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.5_1605520988511_0.42190693510683963"}, "_hasShrinkwrap": false, "publish_time": 1605520988663, "_cnpm_publish_time": 1605520988663, "_cnpmcore_publish_time": "2021-12-16T10:12:47.358Z"}, "7.2.4": {"name": "@types/eslint", "version": "7.2.4", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "bf571f2e37bf41295b99006b5e957d64e4e0a755589762d5b1a671e342ad5607", "typeScriptVersion": "3.2", "_id": "@types/eslint@7.2.4", "dist": {"shasum": "d12eeed7741d2491b69808576ac2d20c14f74c41", "size": 20757, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.4.tgz", "integrity": "sha512-YCY4kzHMsHoyKspQH+nwSe+70Kep7Vjt2X+dZe5Vs2vkRudqtoFoUIv1RlJmZB8Hbp7McneupoZij4PadxsK5Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.4_1602173656436_0.1522062028451856"}, "_hasShrinkwrap": false, "publish_time": 1602173656555, "_cnpm_publish_time": 1602173656555, "_cnpmcore_publish_time": "2021-12-16T10:12:47.564Z"}, "7.2.3": {"name": "@types/eslint", "version": "7.2.3", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "5ce9377002c09156e27646b19471c7c94bfcbec2d7f33b720fc233e741de3070", "typeScriptVersion": "3.2", "_id": "@types/eslint@7.2.3", "dist": {"shasum": "a3731b7584fe1a847a34e67ac57a556afd9b0c0e", "size": 20680, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.3.tgz", "integrity": "sha512-SPBkpC+awgFfyAn4sjt0JBZ3vzACoSp2zhGBJkkrs09EzPqLbxkzaE8kJs3EsRRgkZwWk9zyXT/swvhnJYX8pQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.3_1600888690530_0.9074952950792119"}, "_hasShrinkwrap": false, "publish_time": 1600888690724, "_cnpm_publish_time": 1600888690724, "_cnpmcore_publish_time": "2021-12-16T10:12:47.795Z"}, "7.2.2": {"name": "@types/eslint", "version": "7.2.2", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "27ed71a5a3f4200b9e33b6cb9443e68fd3de3785a55f1614aab1be1fdce30903", "typeScriptVersion": "3.1", "_id": "@types/eslint@7.2.2", "dist": {"shasum": "c88426b896efeb0b2732a92431ce8aa7ec0dee61", "size": 20043, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.2.tgz", "integrity": "sha512-psWuwNXuKR2e6vMU5d2qH0Kqzrb2Zxwk+uBCF2LsyEph+Nex3lFIPMJXwxfGesdtJM2qtjKoCYsyh76K3x9wLg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.2_1598552096192_0.9195838006450103"}, "_hasShrinkwrap": false, "publish_time": 1598552096405, "_cnpm_publish_time": 1598552096405, "_cnpmcore_publish_time": "2021-12-16T10:12:48.125Z"}, "7.2.1": {"name": "@types/eslint", "version": "7.2.1", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "5d19d6be0b4a635e15be6d4c39c206e3861ac07c7613dd3cd3dffe2dccc7e379", "typeScriptVersion": "3.1", "_id": "@types/eslint@7.2.1", "dist": {"shasum": "fcfbcccaecfb487b0fc5d44686807cc625f358b6", "size": 19831, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.1.tgz", "integrity": "sha512-<PERSON>jKivjZyeL65Qt8HLLGwyhC3NkhoPVgCvxPcCMb02k8fCSJH1vvDRefXmSHA4U9TcjOj024yCOxFgCPKC0gMXQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.1_1598029651230_0.46150165297640644"}, "_hasShrinkwrap": false, "publish_time": 1598029651377, "_cnpm_publish_time": 1598029651377, "_cnpmcore_publish_time": "2021-12-16T10:12:48.386Z"}, "7.2.0": {"name": "@types/eslint", "version": "7.2.0", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "f6fdb4744c9a02a8830d3b4a5d5663b7521bf6948af349c29c40ae75cec95220", "typeScriptVersion": "3.0", "_id": "@types/eslint@7.2.0", "dist": {"shasum": "eb5c5b575237334df24c53195e37b53d66478d7b", "size": 20280, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-7.2.0.tgz", "integrity": "sha512-LpUXkr7fnmPXWGxB0ZuLEzNeTURuHPavkC5zuU4sg62/TgL5ZEjamr5Y8b6AftwHtx2bPJasI+CL0TT2JwQ7aA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_7.2.0_1591918349433_0.924637986172721"}, "_hasShrinkwrap": false, "publish_time": 1591918349535, "_cnpm_publish_time": 1591918349535, "_cnpmcore_publish_time": "2021-12-16T10:12:48.794Z"}, "6.8.1": {"name": "@types/eslint", "version": "6.8.1", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "9cf7aaaf70c45cd153987d7f25f0f75fe405985c16163ed3bf7318dcc9e75e3b", "typeScriptVersion": "3.0", "_id": "@types/eslint@6.8.1", "dist": {"shasum": "e26f365a5dda12445d1d5a17eb70efd7c844a3d8", "size": 19840, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.8.1.tgz", "integrity": "sha512-eutiEpQ4SN7kdF8QVDPyiSSy7ZFM+werJVw6/mRxLGbG4oet6/p81WFjSIcuY9PzM+dsu25Yh5EAUmQ9aJC1gg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.8.1_1589519283778_0.437569533921609"}, "_hasShrinkwrap": false, "publish_time": 1589519283950, "_cnpm_publish_time": 1589519283950, "_cnpmcore_publish_time": "2021-12-16T10:12:49.458Z"}, "6.8.0": {"name": "@types/eslint", "version": "6.8.0", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "5eb18ac3f55ca30a4ede69ebc87a424020d9d37f3bc6dc393b75d4dabb1140d2", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.8.0", "dist": {"shasum": "5f2289b9f01316da7cf31c9e63109a10602a23cb", "size": 19931, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.8.0.tgz", "integrity": "sha512-hqzmggoxkOubpgTdcOltkfc5N8IftRJqU70d1jbOISjjZVPvjcr+CLi2CI70hx1SUIRkLgpglTy9w28nGe2Hsw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.8.0_1586204668857_0.7834266411835002"}, "_hasShrinkwrap": false, "publish_time": 1586204669003, "_cnpm_publish_time": 1586204669003, "_cnpmcore_publish_time": "2021-12-16T10:12:49.677Z"}, "6.1.9": {"name": "@types/eslint", "version": "6.1.9", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "d7c9ec8e998f122ad4910e588a439e9ef5758f0d2f675b6274f37ec79ae5740a", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.1.9", "dist": {"shasum": "f43351d8830b3273d4eb4c8c24d0d6e38fcb0fa8", "size": 19698, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.9.tgz", "integrity": "sha512-iGnyI+jgHqGr+Hgcvnc9KIGT2A0h6bx2wslVlVFxTBkfIMMAEWWoVa8SV0havS8ITNo8ayzzgj9TFclwhw//8Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.9_1585614849159_0.22047015717215368"}, "_hasShrinkwrap": false, "publish_time": 1585614849286, "_cnpm_publish_time": 1585614849286, "_cnpmcore_publish_time": "2021-12-16T10:12:49.970Z"}, "6.1.8": {"name": "@types/eslint", "version": "6.1.8", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "ce621f1a88f750a55ffd20e54f5f66c63e6ae18b41764c254299e4a541955b61", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.1.8", "dist": {"shasum": "7e868f89bc1e520323d405940e49cb912ede5bba", "size": 19683, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.8.tgz", "integrity": "sha512-CJBhm9pYdUS8cFVbXACWlLxZWFBTQMiM0eI6RYxng3u9oQ9gHdQ5PN89DHPrK4RISRzX62nRsteUlbBgEIdSug=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.8_1580838485826_0.661358819695818"}, "_hasShrinkwrap": false, "publish_time": 1580838485935, "_cnpm_publish_time": 1580838485935, "_cnpmcore_publish_time": "2021-12-16T10:12:50.210Z"}, "6.1.7": {"name": "@types/eslint", "version": "6.1.7", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "4006a9d81a3426f2db8b4c6596571489bc6e8ea1bb20b8ad068ef243a9872d7b", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.1.7", "dist": {"shasum": "81ce3a730bbe84bd34e60cf2accd9dc9e60fbcc2", "size": 19668, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.7.tgz", "integrity": "sha512-SP9WDPZeOfX0VFXylehXoKGtuHx7Cm4JaXSiJj6dHbgLW503i95MAs22zIQgciof3LXWIAejZXp5bOgbWC/Faw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.7_1579914159309_0.13557711231105518"}, "_hasShrinkwrap": false, "publish_time": 1579914159418, "_cnpm_publish_time": 1579914159418, "_cnpmcore_publish_time": "2021-12-16T10:12:50.442Z"}, "6.1.6": {"name": "@types/eslint", "version": "6.1.6", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "0015997ca5397d675ed90eb571a1d89295a60cdd68c20aca97f638246dfaa543", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.1.6", "dist": {"shasum": "1b54c949c270dd0e12809e4d5aa265953aa5cc22", "size": 19661, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.6.tgz", "integrity": "sha512-bTZQc4vx5zjlVWI8f+Ce1Ioc7o4RRi+qLhDXlHWIadmieyb6dZU4lxNFbWXd2l16+Izxw1Y6G0bG3ksi8W79dg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.6_1579803934713_0.4760901030919016"}, "_hasShrinkwrap": false, "publish_time": 1579803934895, "_cnpm_publish_time": 1579803934895, "_cnpmcore_publish_time": "2021-12-16T10:12:50.689Z"}, "6.1.5": {"name": "@types/eslint", "version": "6.1.5", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "adee12112a73d30125cf92eea3cb46b1bdc5ba74d164e37cbf460c8b5dac429b", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.1.5", "dist": {"shasum": "1e1c0f6349504091a8bb71a8a38eb663d401f9ab", "size": 19653, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.5.tgz", "integrity": "sha512-DDNhspp3xrOUnPQtHeuwqnAGEv6uLNjUPjKI6MUHh34iMHa8xu808YdVsCdyrB6q8ZSudZ5Hbhndt62/bwwUVA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.5_1579722788990_0.14145388837626172"}, "_hasShrinkwrap": false, "publish_time": 1579722789173, "_cnpm_publish_time": 1579722789173, "_cnpmcore_publish_time": "2021-12-16T10:12:50.966Z"}, "6.1.4": {"name": "@types/eslint", "version": "6.1.4", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "8a7cae7fa959a8f57a280912d9b7829059a91b90cb6b304f6c8b13d351afe029", "typeScriptVersion": "2.8", "_id": "@types/eslint@6.1.4", "dist": {"shasum": "2b03daed7fc310d02a9099b67b7c248c3bb482c6", "size": 19623, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.4.tgz", "integrity": "sha512-Y6<PERSON><PERSON>LSSFdpaXvyOncYfT7Ir8qp9oZZaEQnQIDENztqYJfIdLcXLQ6S52f7ZPEtrdzWQhes4JZXKJs5mpOFCUA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.4_1579654346705_0.4166785054427551"}, "_hasShrinkwrap": false, "publish_time": 1579654346870, "_cnpm_publish_time": 1579654346870, "_cnpmcore_publish_time": "2021-12-16T10:12:51.195Z"}, "6.1.3": {"name": "@types/eslint", "version": "6.1.3", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "07b08b7b542f5e189cbe33bf29ba1b813e530e915152b344d623119f97b376ff", "typeScriptVersion": "2.2", "_id": "@types/eslint@6.1.3", "dist": {"shasum": "ec2a66e445a48efaa234020eb3b6e8f06afc9c61", "size": 19547, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.3.tgz", "integrity": "sha512-llYf1QNZaDweXtA7uY6JczcwHmFwJL9TpK3E6sY0B18l6ulDT6VWNMAdEjYccFHiDfxLPxffd8QmSDV4QUUspA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.3_1571868663617_0.42488237095391335"}, "_hasShrinkwrap": false, "publish_time": 1571868663766, "_cnpm_publish_time": 1571868663766, "_cnpmcore_publish_time": "2021-12-16T10:12:51.517Z"}, "6.1.2": {"name": "@types/eslint", "version": "6.1.2", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "5105f3ea914800ddda4d36b99873a720d82bfd27595aa419dff9683cf7eaf36b", "typeScriptVersion": "2.2", "_id": "@types/eslint@6.1.2", "dist": {"shasum": "297ece0f3815f93d699b18bdade5e6bee747284f", "size": 19531, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.2.tgz", "integrity": "sha512-t+smTKg1e9SshiIOI94Zi+Lvo3bfHF20MuKP8w3VGWdrS1dYm33A7xrSoyy9FQv6oE2TwYqEXVJ50I0or8+FWQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.2_1570053838841_0.09385785573392691"}, "_hasShrinkwrap": false, "publish_time": 1570053839002, "_cnpm_publish_time": 1570053839002, "_cnpmcore_publish_time": "2021-12-16T10:12:51.818Z"}, "6.1.1": {"name": "@types/eslint", "version": "6.1.1", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "84dadff55d605143c5c648ac51805948d8fb4f1abbfef69afb53f71c0b4a73b4", "typeScriptVersion": "2.2", "_id": "@types/eslint@6.1.1", "dist": {"shasum": "62a59e00800e954b1c57b2d7148db68adb94363a", "size": 19527, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.1.tgz", "integrity": "sha512-ImT/b1lN/nOvhYpm4knz25xV8mDm5Xnc1Y1cobdDfTnK1oC/kBfuZxnfJCoLHKs8WRj6SU7CE5ExK+a2gwwwvQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.1_1566574846051_0.9371051849790897"}, "_hasShrinkwrap": false, "publish_time": 1566574846247, "_cnpm_publish_time": 1566574846247, "_cnpmcore_publish_time": "2021-12-16T10:12:52.101Z"}, "6.1.0": {"name": "@types/eslint", "version": "6.1.0", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "f4fc837bbb8d54416d13347697854efd913649657d0a30dfc45d28bf7ef50e56", "typeScriptVersion": "2.2", "_id": "@types/eslint@6.1.0", "dist": {"shasum": "8877f938dc843e641c11fd56790944905b16156f", "size": 19509, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-6.1.0.tgz", "integrity": "sha512-AQT8DimJSk6euBWT4w3xJd8R77Yev4uANuOSNdSYRKZ4azJxMKC8Nm0k8e43edZ3CZkr2C4bqtkcps4oN0yNKg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_6.1.0_1566328683141_0.1370204845024212"}, "_hasShrinkwrap": false, "publish_time": 1566328683388, "_cnpm_publish_time": 1566328683388, "_cnpmcore_publish_time": "2021-12-16T10:12:52.331Z"}, "4.16.8": {"name": "@types/eslint", "version": "4.16.8", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "4796167e2c4c8145986e81b5959b7acd5118e9f4948b288b9fe3950c49b0c326", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.8", "dist": {"shasum": "856f0eb8a312d25a7989b6d0ab708e8d5f8cc7ee", "size": 5286, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.8.tgz", "integrity": "sha512-n0ZvaIpPeBxproRvV+tZoCHRxIoNAk+k+XMvQefKgx3qM3IundoogQBAwiNEnqW0GDP1j1ATe5lFy9xxutFAHg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.8_1565364588997_0.4358192399927532"}, "_hasShrinkwrap": false, "publish_time": 1565364589133, "_cnpm_publish_time": 1565364589133, "_cnpmcore_publish_time": "2021-12-16T10:12:52.522Z"}, "4.16.7": {"name": "@types/eslint", "version": "4.16.7", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "b733f11c4cfa021af6dbeb0cc71cf2d80229d80bc8f3a7648627fd7e76a997c9", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.7", "dist": {"shasum": "723a2e53c6884204323ee44e80ea036857e34ef8", "size": 5260, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.7.tgz", "integrity": "sha512-Xr50GH0b4cc/65IVQOJtbH8Ilob8HSmr9HW/OmxpNrqJwEp+FX8TL5V5lv6GJEXIlLd4eiWFt439hlImZ9PgdA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.7_1565304640680_0.2118004248360943"}, "_hasShrinkwrap": false, "publish_time": 1565304640834, "_cnpm_publish_time": 1565304640834, "_cnpmcore_publish_time": "2021-12-16T10:12:52.769Z"}, "4.16.6": {"name": "@types/eslint", "version": "4.16.6", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "4fc11d5ef10a3c1da42fe67b10ba449a64b7d609a799cc9cc7f3f682dcab1c1c", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.6", "dist": {"shasum": "96d4ecddbea618ab0b55eaf0dffedf387129b06c", "size": 5177, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.6.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>tGJig55FeclpOytU7nCCqtR143jBoC7AUdH0DO9xBSIFiNNUFCY/S3KNWsHeQJuU3hjw/OC1+kRTFNXqUZQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.6_1548707656262_0.8531026875836019"}, "_hasShrinkwrap": false, "publish_time": 1548707656369, "_cnpm_publish_time": 1548707656369, "_cnpmcore_publish_time": "2021-12-16T10:12:52.998Z"}, "4.16.5": {"name": "@types/eslint", "version": "4.16.5", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "a4a65111fdd9fe0f9ec163d8d0c67e642a58b34c48e84250cd08e882b63639d5", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.5", "dist": {"shasum": "f0b56e011a3f7d01a380a568776f93ec56d7c911", "size": 5173, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.5.tgz", "integrity": "sha512-103C3Adr8UaucsjYBDdKzL9AZk6c6F2gKIlyZOxv48KtmnvNcXoDsEddF4N/DZJNoVFg6NGSbzYodvxix3Md2g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.5_1543873035562_0.2868427525782973"}, "_hasShrinkwrap": false, "publish_time": 1543873035664, "_cnpm_publish_time": 1543873035664, "_cnpmcore_publish_time": "2021-12-16T10:12:53.229Z"}, "4.16.4": {"name": "@types/eslint", "version": "4.16.4", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/json-schema": "*", "@types/estree": "*"}, "typesPublisherContentHash": "3a79b5066ece052d16b3b6f2a085a7b70e0a34278c0e788063d61a5f0bb05f35", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.4", "dist": {"shasum": "9a0d80208b22f0fd1a9d174e76dc675f3802c011", "size": 5169, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.4.tgz", "integrity": "sha512-g+PV8wktZLJrSPrdCZj6aWfa0xqTrGdx3FywlGE8YH13am8PhkhjtA+Wu1fLdV45gtpM1FM8JWnyFwOsLZI4VA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.4_1540395511017_0.34274377054288263"}, "_hasShrinkwrap": false, "publish_time": 1540395511181, "_cnpm_publish_time": 1540395511181, "_cnpmcore_publish_time": "2021-12-16T10:12:53.603Z"}, "4.16.3": {"name": "@types/eslint", "version": "4.16.3", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/json-schema": "*", "@types/estree": "*"}, "typesPublisherContentHash": "afce84195c933160583178ea20240cf7c56c3b7ce57549d42ffc5c11be65d3c0", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.3", "dist": {"shasum": "6ed2a9c68f73162fa57ba84582d63dcd8ee2d473", "size": 5178, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.3.tgz", "integrity": "sha512-3/RbuVmefwKXGWxq6/+F9UKx8z6Wm8Pa7e4428r7a8L5plqdPzMAuYkvQBLf+wqDPqLQ93TsYi3j3cnql1P5Qw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.3_1530327041618_0.5350750121984198"}, "_hasShrinkwrap": false, "publish_time": 1530327041663, "_cnpm_publish_time": 1530327041663, "_cnpmcore_publish_time": "2021-12-16T10:12:53.932Z"}, "4.16.2": {"name": "@types/eslint", "version": "4.16.2", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git"}, "scripts": {}, "dependencies": {"@types/json-schema": "*", "@types/estree": "*"}, "typesPublisherContentHash": "f157cc0a3a4f91de8ac565804d622ffbfba4e204d809033674c8c7bf01325b53", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.2", "dist": {"shasum": "30f4f026019eb78a6ef12f276b75cd16ea2afb27", "size": 5168, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.2.tgz", "integrity": "sha512-gCqhoFlyLic8Ux1OQt9cjlPbXk/dS7zPpofazBkie6SWCl+e1IEZBgLqyakm27nh0/uSZYW2TqkBusV9fLmztw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.2_1525797720266_0.35447566613165793"}, "_hasShrinkwrap": false, "publish_time": 1525797720364, "_cnpm_publish_time": 1525797720364, "_cnpmcore_publish_time": "2021-12-16T10:12:54.180Z"}, "4.16.1": {"name": "@types/eslint", "version": "4.16.1", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/json-schema": "*", "@types/estree": "*"}, "typesPublisherContentHash": "d1cfcc37afd2f22ec260034dfdd0305148f273aedcbc93e15bd24e5bb4451d28", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.1", "dist": {"shasum": "19730c9fcb66b6e44742d12b27a603fabfeb2f49", "size": 5140, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.1.tgz", "integrity": "sha512-lRUXQAULl5geixTiP2K0iYvMUbCkEnuOwvLGjwff12I4ECxoW5QaWML5UUOZ1CvpQLILkddBdMPMZz4ByQizsg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.1_1524612786669_0.041304197757032535"}, "_hasShrinkwrap": false, "publish_time": 1524612786813, "_cnpm_publish_time": 1524612786813, "_cnpmcore_publish_time": "2021-12-16T10:12:54.427Z"}, "4.16.0": {"name": "@types/eslint", "version": "4.16.0", "description": "TypeScript definitions for eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/json-schema": "*", "@types/estree": "*"}, "typesPublisherContentHash": "35561d43e4bd8749657b0430b29bdcc74965dccf3dc2d6d93a079e114cb1ae42", "typeScriptVersion": "2.2", "_id": "@types/eslint@4.16.0", "dist": {"shasum": "e7ed874c0d664a18cc2165481875daba5aaa9e73", "size": 5088, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-4.16.0.tgz", "integrity": "sha512-DtdMTe0EyDuPLIv3e28djIqDUgst7PQdDyiHGgGbTtRtVK67bazN8KYf7qsbzIZr1F4RS+vgpF8Yhl1qBoqeew=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_4.16.0_1518555338747_0.09881877387701876"}, "_hasShrinkwrap": false, "publish_time": 1518555338797, "_cnpm_publish_time": 1518555338797, "_cnpmcore_publish_time": "2021-12-16T10:12:54.639Z"}, "8.2.2": {"name": "@types/eslint", "version": "8.2.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "c034072516d3d807903933c496a3783507f24e70819bcc02df69fd522a214219", "typeScriptVersion": "3.8", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.2.2", "dist": {"integrity": "sha512-nQxgB8/Sg+QKhnV8e0WzPpxjIGT3tuJDDzybkDi8ItE/IgTlHo07U0shaIjzhcvQxlq9SDRE42lsJ23uvEgJ2A==", "shasum": "b64dbdb64b1957cfc8a698c68297fcf8983e94c7", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.2.2.tgz", "fileCount": 16, "unpackedSize": 162423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh14LnCRA9TVsSAnZWagAAo90P/2iaSO+iektouqo4cN7z\nvuXF99zpMqI5AynVH5BZux46JGT5gugB8wH9pQIP40h3utLziSu0hfsJBNH3\nP0mjEPZtSMkuXqC32ao3pYweKDmRGIvala02pcT8iLcZznS5I3m3eUMm0Kz2\n96yvvjbRm4jjyPxXJ0wbbK0Ir61ZKADcjr6K9SfFEc66f6brSo9OVVdvAsfA\nekGPzJhbC1S/gfBbc1XkJRMylF5qY1z17k9/a68FLnf7GgGKq8kgsEPQlyIF\nuRht7MViWsHu9ARTJs9LirDJFsJmqvU5X6mNe2r/3PjgLgyiXpVeq7rXk9Ph\nt01Y3PXzfQQbrIlu30DAKban3I/7pU4HccrKyoKl5z9CbQQJyftW3n+P9bmB\nSAdwAF+D5XfV4BPuLqj0IpAJ8f9qSqcEa4obL4H7EjvYtJ/L9u5A1t742C3d\nJ1fvGx6djeq8JjNGDeI8qPXBJmjCrEEdPHt7LH8RBzDZk6Bt+uNwwL/ro1Fu\nthw6Pgs14T0t1dQ9q/i5bVuzwVLdaIREpJZ38Q3KY0lfiZAgynqqfQ7UKl5r\nQHvdkumY1ZAQE7Wj2YjMaoGhZMywr5PCUbNlAqk1UouBuEOK4IZXHqtzqOnt\nG4sgKm4TyzTmM1b725Oxc9ojAYAx42kG+hiDOrS8nNluWkQQgXp8v6GI6cM1\niRFb\r\n=cfxF\r\n-----END PGP SIGNATURE-----\r\n", "size": 22557}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.2.2_1641513703693_0.4054905540515612"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-07T00:01:51.420Z"}, "8.4.0": {"name": "@types/eslint", "version": "8.4.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "bb3f45a0b828b9f301a19a376dbb2a1a47d21f16406fe21aabddd476295c8bec", "typeScriptVersion": "3.8", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.0", "dist": {"integrity": "sha512-JUYa/5JwoqikCy7O7jKtuNe9Z4ZZt615G+1EKfaDGSNEpzaA2OwbV/G1v08Oa7fd1XzlFoSCvt9ePl9/6FyAug==", "shasum": "95712d5b32fd99a0d9493c31c6197ea7471c3ba6", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.0.tgz", "fileCount": 16, "unpackedSize": 162444, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6J9uCRA9TVsSAnZWagAAmR4P/3j4BKpBzov7WA/eXwSi\nsLdZMaREHXp+qfiFX+QN3clKKqEtEtBAwhnt0768rV+xnG+m9GCulU2Wasuu\nRspsMMkIMA7IZZd0zK2QCVeWkxd7l86OpSysDu5p2fwKiZHfL8qu4UT9hx7j\nzriO60d0e7Dyx1PLcCZ31V0ETA8o1b6zdKoD95DDMLL0Znz7ZB9WioaL4+Sc\nVuIb/nett5qU+9+xKW6P+qx06bhCDx4u05nA1lsy0LJs44yi8dnHhpUxbMLX\nelScQ2UB46z+6BStP2A+edM3tY9yRti/YjeG1jiCiGgn/ZAfdk4Dqjsbl/6F\nhHFmWfhOwdXKcJJOqeSgnGtSVINe3bt+kw0epiaFlmlEgpSvePv+1f+FjDMw\n+054Q26thBf0veyf2eccC6yepu1ZHZF1n+LUx24aAJ5RAfVC29hWR+YIbww3\nRXbxNOdhhrUhx0TmgNdb9DJHTvdE50Xc3+cIJDCKrEJ/xDL2MzNmC1q8qQPK\nsGfBRgugXiSGiTeX1LkyixOIeOFrgyTyM9XFnmmi5IGQsggQ8xvgL+AxTVSc\n25x7uw7J1cnMr/QEhe2q0oZUyIuhXc/+g1YjVyKcGryrsQQwdRHrYj8uRS29\nMKttNpSqnyuISBBMvBt71/HZUlqEo9WwqkGPH8QKfQs3UKqEIMtFFa8+tZ9P\nRM7D\r\n=gZfE\r\n-----END PGP SIGNATURE-----\r\n", "size": 22568}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.0_1642635117964_0.7507775862175394"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-19T23:32:06.538Z"}, "8.4.1": {"name": "@types/eslint", "version": "8.4.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "90cb9cf20ae7859ce7f641c07d2d274a142d14ed7b4824a89b6abc48c961d03f", "typeScriptVersion": "3.8", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.1", "dist": {"integrity": "sha512-GE44+DNEyxxh2Kc6ro/VkIj+9ma0pO0bwv9+uHSyBrikYOHr8zYcdPvnBOp1aw8s+CjRvuSx7CyWqRrNFQ59mA==", "shasum": "c48251553e8759db9e656de3efc846954ac32304", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.1.tgz", "fileCount": 16, "unpackedSize": 162470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7FSXCRA9TVsSAnZWagAA8K8P/jHuN/oQEb3QLmYgYeW3\nt0L+zArArrAGzf0VIlhjS2D8g7JaCviUhvPyeT0zUvOBdXC/HL65dE/5S7v4\n7M+ted2+NgsPU/xBMIPNLOT4XXnZwqOtrxGgB5wxSK3Qv/GRLmW4dmlJlMx+\nWH6aiSc1aV+m3DkoF1Y3Ms0WTWqieZjKKTK7SNhbaCT+kNeP4t/Zl+TkWUqn\nGqLVNHkRsEKCtVLZTiCTrJFTjNT6qncXVRDJuRzUptvlKzXOlPyW2/Eq9RaB\namO7OHw+IDf5W5YYnEh3F066rxKSNmBc0pgyc1OSG95gjDEN0UADdvEHifsi\nm33FiWlHMol9QU8/ot6waEjZyeV4n8fg10M5sTFDQcYtYbI3qYZJv7uG8Vx9\nkCJPvAu9yV2/sX/5KP6XSDlbodVAJHzJs/kNb8hksDj1VlwQPrz5+cgRZG+0\n8PLrE+7Of/Y+89BTAjHl/tzRyth7romynWPVQ3v8lIaaTew1lLPzofzvpBwV\nV3YyUHo3QPtUEmrJgl66Qb4es5p7Eq9bxoC4OwgtVfldUgcH6+WLEqiLDQsc\nWwOl8CtguHuLPJmi1XBaoSeRERAJkkjKS8q6/BDNiCtGGdEsv3O5vV0DQvsy\npPefX87Po5FvVL3cdD1fTsoUnrxc3t0/usGKW0oAsFrPDu0ho4A41xfAKNeq\nGdm4\r\n=avZK\r\n-----END PGP SIGNATURE-----\r\n", "size": 22567}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.1_1642878103697_0.8818703103500907"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-22T19:01:53.340Z"}, "8.4.2": {"name": "@types/eslint", "version": "8.4.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "b97952c30948d31732e0c17e856b7fe0edb8caf7f6caf56912ddd42bb3962909", "typeScriptVersion": "3.9", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.2", "dist": {"integrity": "sha512-Z1nseZON+GEnFjJc04sv4NSALGjhFwy6K0HXt7qsn5ArfAKtb63dXNJHf+1YW6IpOIYRBGUbu3GwJdj8DGnCjA==", "shasum": "48f2ac58ab9c631cb68845c3d956b28f79fad575", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.2.tgz", "fileCount": 16, "unpackedSize": 162728, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFS4j2JTm22HwNYaraFleFfB1XfNxcdDOIueEPFMj0rOAiEA6HnwH4UYeTH81XSdubpkLody0afUfVnDh+93cJOsQpY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVkCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWtQ/7BW+/NN/CuyQye/gnuKFK/O4kj3o7Y9wrrs0Mh3Wz7/HGUUM+\r\ncrXw7Gze2FXiF6OXz3dzty80bCnTb38e8kEvwyqpYXuZGJYkAzcAPsPRXlSb\r\nq3AnwzOuBy5UpkM6PUxY2/tACoZGZ+C81UVBCy2II5P23oz6GXE7DrcJuXtt\r\n56ATQ6NFqhjlQrh/ge7CXmNdKPSk6cA1u4pRPmeWP5FBsc/znj5KU4Au66Rm\r\n0ve7/Z0f57Szq0542CEQA9TOyTPTZAB+DriYZrPU7hh2P7+sNEZ8beUkoEZl\r\nsmxr96vboinbb4nEeqA5TvOZdM0p+emi3FCukcOBGqL5UFqCSiPnlj5Is11n\r\nULbFwPFhvg2ke/dPV/OWJe06g2JmNmEbX93nkZ0LKC/KeYsk+jaPSuXoKitN\r\neeTC3+qHyrq43EVVgarefXzM8BeUO0v7ywLyMQad9LtEYPcIypuAAVxhFhAl\r\n5gi0MZlnT/O9SCU80cO9WU8X/A1CtvHEFKtT2qicU/3Py1ewb2amX/gt9vFW\r\ns/Rph40kEIzEpK34g6JUxfuMjW3rL+U/sRa5+X0LUblmvX2OiMUD9QvnGrQb\r\ncUj3ooHP/UGEkDAtez4mdZzszGba4Ri9e9XZungkhVTKoKcVfrWx195Urhy+\r\nRYE8St7FIlRyT3xGU9YfAYGMQPuqy/aqvSo=\r\n=ZRSg\r\n-----END PGP SIGNATURE-----\r\n", "size": 22621}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.2_1651595522719_0.7460577949273997"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-03T16:32:43.145Z"}, "8.4.3": {"name": "@types/eslint", "version": "8.4.3", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "120b790d5df1df2c9717166df96469dc8db5106f32f3268461e9d0e41991d603", "typeScriptVersion": "3.9", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.3", "dist": {"integrity": "sha512-YP1S7YJRMPs+7KZKDb9G63n8YejIwW9BALq7a5j2+H4yl6iOv9CB29edho+cuFRrvmJbbaH2yiVChKLJVysDGw==", "shasum": "5c92815a3838b1985c90034cd85f26f59d9d0ece", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.3.tgz", "fileCount": 16, "unpackedSize": 163309, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAifK09tf7lRL3fELu+kCeT0Bl31VC/tdiJqkSyOslBuAiEAk8yvAbJpodsIxN28LJg7LydVSg3QyZDr2//oSdY8IsI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioPkvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqecA/+LlzOqLfehfDHL7ZOWi2jwXX69zNuOnrm/BF+/KczR9Zlnz+1\r\nJXhFdeBgnOWrya8kQPSrSYVyvvaYMm2yKqu6xJVF8JjZMaqO/g3V9HskQryB\r\nzWDjRTuC2xmwROBouEyLetkNHH6uToQaA8asALBVPSH9gxiyanB/GKrG/YtW\r\nWyGmzkcuvxzf5WYsqD0OX4G9jMWKMW9TAwTd7k6aUdr8Lc3w2cNI1haDYZ4x\r\nBhPxu/cOAz2DL6i7+5aliKH0N+vPTyXPLRj1YUWpMRiq75bbff3pIWR7ihK1\r\n9QxmmVU+flKDOpt1PeamZJu5nol0Opbt98EYbij/EqF4wRzXwIDCxoMim0l5\r\n2xkxYW5eW8oKrjc3SPC4SGwYUHQF3sJ0fxrJg83CKLpIP92pYUFVaPwFyiP1\r\n16b5tf5v+xUyrp5DrpkVyDKJRpoSWURiX0mKk9i7EJ54jXNLMU805KJM96is\r\ng8KDGROg3IC/PhACx7zugjYK027CyS5makBvn8J8bdLARhtWMBo7if7r+E16\r\nrH6ERgR3Ck0RntMfELHFGdsc3vRxQRxpKvOgQJWnn7hw4Oiaz/jMV1QzGTdu\r\nf7KQGaTXyOgu5CtNtOC2I7m+EsuXZb+N6FV81NX3MPZoPJjQt9qMaevN3C7v\r\nAvBwI9YwC136c1kCg8pbeHXX+l4OKWSiuZc=\r\n=FrZh\r\n-----END PGP SIGNATURE-----\r\n", "size": 22738}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.3_1654716719569_0.1351666492178134"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-08T20:16:02.159Z"}, "8.4.4": {"name": "@types/eslint", "version": "8.4.4", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "4255333edf60da2fccfa64cb4f7eb5df252263c2727cc5605ccaf68bf039fde8", "typeScriptVersion": "4.0", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.4", "dist": {"integrity": "sha512-agaBbw9wzMWtQaS0l0R6I0pewCZNVE23QA6jJUkg7F0HFdMvPrT75fFOtG4+cxstalGqHBkYVXFHP0hTpDnhfw==", "shasum": "6a242a86448fede7742a0ed1f444677017db56dd", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.4.tgz", "fileCount": 17, "unpackedSize": 163331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPahj5ITWQbh9GIu3WLcsB8dmz7CzFutvd0gxvYrTrPAiBLa6LxtDfRdlJShMQpSPArD21Ge8/DbLN8PNEftIuefg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivfNVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpllBAAi7iniv2HldYRm/E8VVoDxvw7KPN9neWP3F6bR5O5P79uIfUe\r\nyDuvkPUzlgX140jQ4ZrUk8us/VoeY7TcmRN6qmZhXTaoiZnShY8SThiWe0dM\r\nDFmPtvgG+wjLhIQdLaGub68qGO8un3RxN7hqxngua5b8WsJex3Wp4n923J+A\r\nvJFgqv/Sz8k5boBVcVNt4Ew9IFx5FvwVhDSFyGPEGcI2sd35xWbHT7UQY6Ts\r\n041I2kCnZm9iob3Vs1tVc8S0xKk3FUHrfwmXTiz08TMeXsGrXBakgadauX29\r\noJlDFJtrdeeKJMgsChCj2+h/gMJoB2CPzU513RIDlvElMRT7EanMPGgglzqv\r\nWWFO3nZP8o7HCqNuIXMLonZC+2EMr3HR0B+8moi90QKW6WxxOMyqQOj6MKb0\r\niZfPYmgM0v01X2xgGRcxzNLYK8tGtDuX6s8RZwyJSe1bta6/HHSssTAFLrBV\r\nggT9f8eR9Q1Pwm97c3N3VGh5OnXzW54Rnoc4TbkScsuKinP3yeeHp7hl6jc9\r\nbTqYPu/N6LXvyDfQMKXs5hZY16PadCKlJqez5nSzwLAj6AgLpw1mjCAEKbWX\r\n2MfJT9QMFqEH3KTo17KjhvlZCUzkRVujV0hkG9RBnx+TaLyHNnH78N6QaQwG\r\n/9dwKyvFe+8gs8yBu+QDg4UXETVxCQ+sl4s=\r\n=m7p3\r\n-----END PGP SIGNATURE-----\r\n", "size": 22703}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.4_1656615765356_0.12053975621832635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-30T22:59:13.897Z"}, "8.4.5": {"name": "@types/eslint", "version": "8.4.5", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "b0eb8273aab0241131ce07d2a5be9928c33a435e8ae28f856c74035c96114c9b", "typeScriptVersion": "4.0", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.5", "dist": {"integrity": "sha512-dhsC09y1gpJWnK+Ff4SGvCuSnk9DaU0BJZSzOwa6GVSg65XtTugLBITDAAzRU5duGBoXBHpdR/9jHGxJjNflJQ==", "shasum": "acdfb7dd36b91cc5d812d7c093811a8f3d9b31e4", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.5.tgz", "fileCount": 17, "unpackedSize": 163331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVNezubNWY6MTmC+5UfT0l4oZOLBE9t13EUeZUqLnl/AiEA3OOt8RBdS6Ayy2MNYP0llJoH73Uh8ZBlD7KrepoPAGA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivhZnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZig/5AAVpOCVL5gBp5RAgSSM9EM67onqfGSZ8CbiRMua5ZrIj3zzP\r\nyU1BcujSyaxxu+TrHdhWthgXVxQbaL0HWq7Od142JAFgutqyXFCKqZzzJqla\r\n+teOd/Bxptzcr1ijNsTTukhqQagseU/hF5LEIc7s/GVTwXZUYdaUeEk+lDck\r\n29TV1FX6GoDHgflhG0UROgc28BPZeqML/myAYBMat03+2qSPK/lFXDHr8y9t\r\njzl4QTSwn5bPuWiadn7ZENSf7uncSv0lFpRbKEC2V0H4TzYv4T5Z38LS02Nn\r\nAicFi7iY02rBVChyCr517oQf58mO3jbxpBcxx/pFkF+gsyeP8isz16Xov36R\r\niIO7FMzXOSvLFvZ1LTbLUlROc4AnuZbwigYJNrLrIPuu3KqihPSFmv3OlPNZ\r\ndg34KyW+w/8z9eJhffH3xj+rpH9Z+PGm5zC7/O9bvsTlDGH68qZ6L8RHk6fZ\r\nWiUT4ymUBGHA2xlvnvd9E5EbfSoSssGQfwGoDD7GyluP/gmulY8VoUv/gCtV\r\nydiQQLQxUMkLuOQSHQpfTr8mVFMmx8PXis5tmuECYLJlwJGINl/CDyWoGPXy\r\nKbucvJ1FJmAZ0RKJ/Zkh6c94cNb8Pip2XyKcnQnYZsEmfIMaWa6iB8a0OxH5\r\n9rnwSwNWTtlWl1oV+96FvtBtnkumBcPNdKk=\r\n=Lyff\r\n-----END PGP SIGNATURE-----\r\n", "size": 22699}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.5_1656624743353_0.7491348484247073"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-30T22:59:14.001Z"}, "8.4.6": {"name": "@types/eslint", "version": "8.4.6", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "ea7a3930aee1a8f352a631e32f29e147c28e2916edc41c5459f608b82f73e211", "typeScriptVersion": "4.0", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.6", "dist": {"integrity": "sha512-/fqTbjxyFUaYNO7VcW5g+4npmqVACz1bB7RTHYuLj+PRjw9hrCwrUXVQFpChUS0JsyEFvMZ7U/PfmvWgxJhI9g==", "shasum": "7976f054c1bccfcf514bff0564c0c41df5c08207", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.6.tgz", "fileCount": 17, "unpackedSize": 164748, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+8gTQHmrvN6xBljAjR8snSkJm1M9EydFc+10vWQgJ8gIhALYzQel5m73e8+ImECV+u+LdufwplO8NnbcK3IKVnnKs"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/vZBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmot6xAAj9h1o+43xJtsStpeQ9jguHpMF6exE0u6ZLFXtjfcJRm0wU0t\r\nBw++wTK1Zqqfqi4PkmC0XiKAB+/37SQCOQSKfO4hh4mHLXq/2BdIZ1Xw0thW\r\ns2yoZySjB/bPoAQAfWjjoJBs5QkA7pwTb2jRj/+3lJlGfIUdpRQV8sZCl1tZ\r\nkf1gTtdxXkOGRnB5ww7NiT8Pkeosu623z2nN6D6XNv7SmzhpKNaSKb9FXhmS\r\nComkNvW5lXPFl4S4/4tPOgDgBb75KduRCFeLj0yNLU+KHlp3n2b8wvqZwKvp\r\n7uHbOM0B4Gqn6VYeFcbPMwboAIAX8dzJnMsTlAzCylJn96Fn9UV5nWKrZnXT\r\nCEXKF1NGP26Jiw4kTZmdDxROAlSZeI9i/3en2+HFGgcjRYGOM5ZdcFJjB7XF\r\nlbVLQAhhP2LsQJsj+1mmjReOFNuQKXr+9tWXF7w/F+XrRcSNqDkVmDs3ukgr\r\nDor6yEvdz3iGuKpYnBbYRJpSQLa/7n+g4K7jdKXpaYxzbBE5KcGHKpHwdQw8\r\nkdqxqA2HRYQjMGDQckRpqVly185RN2KRSKhXjrTgfGArqbuFx8Ncn1h5AW+Q\r\nC7t99aDUAvkNaJOGtTnekXKMuAdVoUDxOCWCMMhtui///xLwn3YuT/8K0bZM\r\nghQ0jCfaNU9pDZKmVDvRmObr0dKcZ8+fBqY=\r\n=21v/\r\n-----END PGP SIGNATURE-----\r\n", "size": 23234}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.6_1660876353078_0.4684701178832078"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-19T02:32:47.570Z"}, "8.4.7": {"name": "@types/eslint", "version": "8.4.7", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "2ce1261187fe337756cfcc8219b56121c9f9b54dd0a208a3f7327d8738d93c71", "typeScriptVersion": "4.1", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.7", "dist": {"integrity": "sha512-ehM7cCt2RSFs42mb+lcmhFT9ouIlV92PuaeRGn8N8c98oMjG4Z5pJHA9b1QiCcuqnbPSHcyfiD3mlhqMaHsQIw==", "shasum": "0f05a2677d1a394ff70c21a964a32d3efa05f966", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.7.tgz", "fileCount": 17, "unpackedSize": 164869, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGpk0q8ZUvtXqZI+z7NhLiRkMGmX1TTYzE0AlBgWjBt9AiBKz/C5xzQb/iSWpAVXhvGC/uxx3tUq/AnstA8JzDKf7Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUSruACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxVA/+Mk5oVxisShdQ6LtYSPtr/WG7+/SjaGll7Gh+7PWtbD1vfnpn\r\nyt9Rj7a1kTUoFPvK1dNT/Kf+HwCH0xwTM/R98KtP5/JxOywmUn6hzQUQTzQN\r\nkgn7qrQd7JMLRzJD5I/XLXEMNAJFA5up0Su1t6Z4pTI85neVWaGE+JEUQVqA\r\n6y+bMfu00njdQTEDKryjbfkRZkT7dx16lJnkmINiQG+CQVJzgosNir4noGVx\r\nmdaP1RlYbAo+SzDDC2rAixcfZiPRri33SOeotWQRJrP/ELbAFSI43NtQDqjN\r\nX0AR4YZ2W+OrfufamBxiiUYXK7i1LLPsG9RObM074mdDQx3ERTj3dqd5W9v5\r\nEo4InHgesbqXp5w0FdtfIT20FlscKyPKf3sQ4DOGunYHTVhB+MBMwlhVf+Ye\r\n7hkZRJJpI2jb8xyuU1oAFMPtm88m+3dmSPVVdCQzSpwik2KFWNwGMIQl4DDB\r\nA4poPCh3FTEwutxiUgk5xvkFt3tUl7RBh3bas8HFU8qBvV39HpiCgg3/XyaR\r\np/9uhbwzTiuIhwycbJc1Y9ojTKkJeFyu5c18k2YnqqVrLBLgC2BOXapiP9lC\r\nYI1zHkZDImYDpNSF12xsCQoB1yZodNppEh8kzli8c/qYYuuT8bA01f48w1AM\r\n5x2xDtVJEjQdp5nIQF3FWH0oARqohv6hhUE=\r\n=CE1c\r\n-----END PGP SIGNATURE-----\r\n", "size": 23271}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.7_1666263790205_0.7521789983579934"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-20T11:07:20.118Z"}, "8.4.8": {"name": "@types/eslint", "version": "8.4.8", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "caa10b61f4a10b758a2c91ec9a85194fb23be54d529bcb371e54c8c73555a255", "typeScriptVersion": "4.1", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.8", "dist": {"integrity": "sha512-zUCKQI1bUCTi+0kQs5ZQzQ/XILWRLIlh15FXWNykJ+NG3TMKMVvwwC6GP3DR1Ylga15fB7iAExSzc4PNlR5i3w==", "shasum": "720dd6a32b8219b9aba1a07b13e9d03b622695fd", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.8.tgz", "fileCount": 17, "unpackedSize": 164999, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3HhTJ4Q/eLdgPdj0fYfv62z1iHfNdf4OI/EZdbKPAZQIgDax2XmmaG+W/k/Kx1X2z19N0LpMz/wD2pv7/6bNOW+Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjV607ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOvA/+LkUyvF2iye9S8/+SmRW5zXuJAURnCYTPJX81Fr2mi13Cs7J+\r\nYTeANy3GH1sTEZlVpiITMtUsdv+tYB6m0PAUssyIzbZ0if1qIQ9WYRPCiex0\r\nNJo9vbITGVbKD45o68dzHeyloHCi5TPIjyu95Z/VXOaDP2shkg/O8tpcyfRh\r\nIp7YtIRM189OULZHLW8BLQuGqqqOR7gVkktWz7zuNMq/iluxVhdJ4TRE4x7n\r\ngJOhFwN/FGPSYuDPe82eQDlrsUH+H4VCY3IeV16FaGjoh0N3PPtkecYdQK0i\r\nFxsm3tja4rG4yDOplH1UdToPyIHXpI0mkZMkbTe7JcyI9RPAmdshl6xo4gr6\r\ndVCWWxBsrRDFomV0Hsez83CvN/DeKo21VFZTtwTdz36VRW/oLdyZQv+lR8ad\r\n8KxBB9242OdUcwC4EayWxVeK24GXnanpJ+NViVMaNdnfeMe/sKj7xPH6obzF\r\nGuXElASOs8nUHWX8P1A13W6/uP3hoSfn9yf3ONLskIHFRjxKz6f1qYfeEBkI\r\n3G1H9dNd0bUR/2bO2vmp4V+MtMi9jaWHwaI7rsdwCTSORL7sYuHs+IPoQAoX\r\nLMToIZcoHInX9N72kN7y15dRsT0Xh5Z3MW2lHfRf81625ISnqLaRivvT65BJ\r\ncZeot3Psbr7xI6ROnA+8Ir3rZkvu7ajZmz0=\r\n=tGFV\r\n-----END PGP SIGNATURE-----\r\n", "size": 23314}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.8_1666690363281_0.36274748882189667"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-25T09:56:36.025Z"}, "8.4.9": {"name": "@types/eslint", "version": "8.4.9", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "f022ab2f6ad689a82abfcc9665b330d71a8a265ab37bc44e2f2d7c5207606542", "typeScriptVersion": "4.1", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.9", "dist": {"integrity": "sha512-jFCSo4wJzlHQLCpceUhUnXdrPuCNOjGFMQ8Eg6JXxlz3QaCKOb7eGi2cephQdM4XTYsNej69P9JDJ1zqNIbncQ==", "shasum": "f7371980148697f4b582b086630319b55324b5aa", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.9.tgz", "fileCount": 17, "unpackedSize": 165007, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrguZbgmtKf+yCsnduIcct4NH2tg8VuvPX4A87BNHUrwIgJYtLIm2v0M6iYdxHCD1pA308S/B7I1Bw95vc4NkSVf8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjXEN2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTgw/9Ee8LZT/uUnynnXaDzPfGLH4DThXjIGUYKFNw07wQhVq/xg0M\r\nDKRrHNxGbB+mKk4TBBeAOiWZ+C29LG+/Ra/d+s2NNSGU6I98Q82DhDDvY+uD\r\nHvS7Z9iVh+tw7oeKshSOADT2Es4iEtvqvM+eHdJBgXEQUrsUCy8JMPcENc0d\r\n6iRUPfx9UMXfyezyioEOfeuaJNZf+cNNhdg2eiUHRRfcKaEgxdx3FEFRqIdm\r\nfYBuBlA3D1EywFeDcQRGACBS4/O4sfpLd3FZkRghS/huYaMKoB0v8J9SifSE\r\nBDORh2FXvjRtB8YjfPKHaFA6cjrvLlY3QO3loCoYuelIXHLnGF4Cx4eOn4SJ\r\nFHvrYS2JFgx+ymX0QAV3Y7BXGKYWuUpXe/FqirtBHllW2SoRwUc4TsS6QPAh\r\nKnoZ9PIAaNsSCgr27xR8VrnTATINROCT4ktQdrHp6VAQgXbU0WxQL4mRoz6F\r\ndAAkyGFNaBzcBKbWWahllxbU6gevypnWGod7tfdOfCamyqhjWDnMcWXvEnc4\r\nl+Gi+AFKH5U+UTkEgyfQjjEzzYf83a+WoGZICi4Orzz8tuYeNm1zScdsIlsV\r\nPk3ykOGjYNESWvR9vQGO7sr6Mt4myWI5UcUJ9UTJfZpL5yxD4SNPfX4FGCqH\r\niPPFZSdjQCE34/g/lLMZGfc4ulvedRngRII=\r\n=4Gf3\r\n-----END PGP SIGNATURE-----\r\n", "size": 23300}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.9_1666990966538_0.2695256010088416"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-28T21:02:59.284Z"}, "8.4.10": {"name": "@types/eslint", "version": "8.4.10", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "72eebaf6c36095f8ff4f5da33b8e88702e5319be7c8fcf478305800aa5fbc8de", "typeScriptVersion": "4.1", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.4.10", "dist": {"integrity": "sha512-Sl/HOqN8NKPmhWo2VBEPm0nvHnu2LL3v9vKo8MEq0EtbJ4eVzGPl41VNPvn5E1i5poMk4/XD8UriLHpJvEP/Nw==", "shasum": "19731b9685c19ed1552da7052b6f668ed7eb64bb", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.10.tgz", "fileCount": 17, "unpackedSize": 165133, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8/alhZb2siksQ+vxIWiGzwsRFZ0CHJ1lU9gDsYufJiQIgVvVENR1KB2mkKj1L8ZBn6bHbga2KTfPXE4hmz3FkOZc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZSmxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAdg/+OLzD7tOxV/rAqMZ0oOFTagJndHQeFuMfEGSdxCHn9oerqaFd\r\nnGBBWu/UezR9vsg+c8EuCNhDCLu+fofKMPVYnflhkhIaMZwuZ4FE3DyjQaYQ\r\nf8+tyQhp38ZDTFYud4TElV1PA6RtgeiZ/3Zqcnsa4fYwd4vn1nDnpf5RnNAn\r\nH3/qbUbhFBuUo4DOtM6zl4UfpkJki4r/stDR7bqyJ7O8sZ1xndk/+HD06AJU\r\n7FWgbm3LIHYaSK8HDT1rhWWSrinRs6Dlpe8Eg4FmLL/5dD2WLftGCFpy7hfu\r\nBtzV68qPRfaNAgPypT7lAhsOe7KGmqBAPC23RZV4H9tUmZfsgCbrqUoZCYEg\r\nAjT8b6Mrdy6J6+EnCJUX1/HuNzC9kjM06yj3Y/fR29ihYbzPs1N18wXaVSu6\r\nkfF8VHvuCE1wvTfW1Ku6TzhFghNRt7guUtxJZsToL/uFVXiD2raBXVkNFccZ\r\nWxyr/NuURXq1JXXyJighHIGF8GOISZP0sV7Iqb26lUMUm+/8tvEzOFsc7BFj\r\nysuA0Hqro/Q/EJu3kAvDb+o8I5x6DGCTevaVszenTMi7toLaRaqHMDrW23h4\r\nn5BRn0Oi0nbu3AMFYgofg7Qe1iYyoIo4lNbCy1T/G3YL2U0BX2amqrMd5hFW\r\nvENcxHJAwgu281Hj+iM6t4UPCoqVMrHy4NM=\r\n=tWq/\r\n-----END PGP SIGNATURE-----\r\n", "size": 23315}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.4.10_1667574193641_0.5349881024091183"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-04T15:07:30.377Z"}, "8.21.0": {"name": "@types/eslint", "version": "8.21.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "0d536ccb26f0c052f34f17f33a8d0c976d305d9886db1c6d371d8629114477d7", "typeScriptVersion": "4.2", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.21.0", "dist": {"integrity": "sha512-35EhHNOXgxnUgh4XCJsGhE7zdlDhYDN/aMG6UbkByCFFNgQ7b3U+uVoqBpicFydR8JEfgdjCF7SJ7MiJfzuiTA==", "shasum": "21724cfe12b96696feafab05829695d4d7bd7c48", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.21.0.tgz", "fileCount": 17, "unpackedSize": 168463, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQD359oxFFkem1En3MxHLVzTJNxuYqiTrE3236GQXg7IeQIfcIz/6gnf4F/eREo7q43NQbWmx4LVTfTg/q7imeW3eQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj27R0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVRw/+N9ab68yXyVaEqKz+tZIqh2ebr09R8//pQ1a29wpOqK/AjvrH\r\ny3purn7jucc21FoJLCReDALxz/Y7D7+DQAZsyimprf+oM1YvGwR2/Ez2El3o\r\ns1lhWnN4k/nT+pMAXQ3m8C2vB4z0oUsKEilYXCSCsf1eq3fvNetCnpuc/BCk\r\nz0KuALxoM1zKqO6HvqS/IJPbcSC+KtFmXvR6h8eLf0nNP3P6dVzjc/v4kWaE\r\neOMUFBP7OVBca/J2li3i1sDnngaMBTantS4eQ+qRRo9gfW7kF1wJegJCPqHn\r\nT8eDT4DstPlogJUhzCye009NBZD2nbK1lKT2eRyXJpCq+D2itkvxkzcVd88l\r\nNr7kjRhKvA9V1X0fpnIga8iBwXQHF+5OBzMb3BsYcvhjqjYYLmXdgEn3R7si\r\nNKTwnbRXYDlM/O1pn22l73UFJCRLnnTH3DMu9Ke6jCBJXqpfvFBRGWezbYMb\r\nm69pkFqw+pvrYnpKCAjHXIhhRhoic77u6NJR9IVw/fyY4L+9CngxupQK9CZH\r\noF5u9iKUZ0mkMUbkOjfM5KY0c2H1HG4dLDQjdwOkP5FQjaAiqypSCWWAVkDh\r\n0T6dBZ4SY2OCUaE2cpCOenDDG2+qZ/npG98OtvQUfkXdPVG1Uc0L2Dny9u+Z\r\ncUg/p+iF66m0ynIn+js5ITV2INT6dZTwto0=\r\n=7Bzs\r\n-----END PGP SIGNATURE-----\r\n", "size": 24143}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.21.0_1675342963783_0.34388518992489314"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-02T13:02:44.034Z", "publish_time": 1675342964034}, "8.21.1": {"name": "@types/eslint", "version": "8.21.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "cca49eb72557cdfcd45521e5f29108fd935fdc15936071968d79a0e7fae5c7f6", "typeScriptVersion": "4.2", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.21.1", "dist": {"integrity": "sha512-rc9K8ZpVjNcLs8Fp0dkozd5Pt2Apk1glO4Vgz8ix1u6yFByxfqo5Yavpy65o+93TAe24jr7v+eSBtFLvOQtCRQ==", "shasum": "110b441a210d53ab47795124dbc3e9bb993d1e7c", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.21.1.tgz", "fileCount": 17, "unpackedSize": 168771, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEZFCdwsrlYxfatu7GeQE07AEeLJQrv1ev06rC88S839AiBJcMWSkf53HbJouDlxQbSHXw9Qcy6S+5LDOD+qTn3v5g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6zkVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Gg/9Gf87chVtz4pPpP6TKNhMG2H4jbXDOnNCrtLxuNJi5IUx5aaF\r\nHVmLEhv3z2SLOQI+Sv8KBwQ7kLg3gZDnxm6tIp97DDnZ+Gv6dMoJAi+JukFw\r\n86ibqdfjloYLQOgTW8YIhw6+bUmgdIJkCIRahkfL+JdgzlRb61wZfazNgxzc\r\nTzVJh3IYt8KtbQUTvReKbrRwO/KK3+j8q3Vt6KCSTRcn3wt5a54j6NH7mJZD\r\nzn53kde6H5P4L7lAqykylYh92TlVqXPE8NSzHCBFZYmJsOT6Y3evE0whIirU\r\nWyfQsYlNjxncD1d4RW3v8qUasIeby7VZ3TFrpTvSbmEIe2AYLmPxMTtC5KKD\r\nnFzIiiNQQ9w5LO/mfCjL7P9G9AJ9rZlRZia2mnRKrWKdx6x8+RSFlnUtqmp5\r\n7mdnkplS0gu7TPDB1STloCy7e5ZfZjct9fS4FoWDPw6ETCpIthqDZN763wUM\r\nx8VnPwnBGpbRIOH7/QpOc/51LfsbBo23Diq9DVxOpu0caFsGFAcOkTRuMiwg\r\nfWOWM/pbKDBL8NTD+cZiEXsS0qJDvCMGVkB8FCsyYWCbLGXjc+Fm44kiz1im\r\n+1187x//0DXSxHHBJoi9gcD7HBVkU9D7/z9g9ro1S4Gj2D9bcg+UvIUdWxsR\r\nkEU6Q2EGqs6EO6W+Qkaq8cscwYoP3m0TLT8=\r\n=gl3w\r\n-----END PGP SIGNATURE-----\r\n", "size": 24195}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.21.1_1676359957285_0.48509890168505154"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-14T07:32:37.448Z", "publish_time": 1676359957448}, "8.21.2": {"name": "@types/eslint", "version": "8.21.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "d5f11c1b5a0a563ac8446d6057e6138c6c2626962d90ceca137a07fea994af87", "typeScriptVersion": "4.2", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.21.2", "dist": {"integrity": "sha512-EMpxUyystd3uZVByZap1DACsMXvb82ypQnGn89e1Y0a+LYu3JJscUd/gqhRsVFDkaD2MIiWo0MT8EfXr3DGRKw==", "shasum": "2b61b43a8b0e66006856a2a4c8e51f6f773ead27", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.21.2.tgz", "fileCount": 17, "unpackedSize": 176520, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+zsB75ZJb4C6cPaPWRpklhqZYZgsy/TFHa1pM2OwuZAIhAK4HrPquAPoB5WpuQsS7WJa3mZU5eUyDhefzF2ydJWf3"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkD3s7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxsA//emfy15rJwdv4CWklWK4nyAwdpkxUV7erp1wqe/lUPiPcKbjb\r\ny8wJGq++JNmZ84lxlTxX7SibwC8YahTIkAxTrTByM/nVK++zwhZrMhFUDaZA\r\nVCpAgqfPs+l5WVklfoPwjLy0XD/g44X7YLl+T1D13XxhNP7qVrZItTQ+tEvm\r\nxp6sBf7OA/IgTrjc4obgzG1zD4X8bDAmwrpPQEQds9G/q9QYed1m277p0M4R\r\n/lShJcF0BRZCWm/8ZG6AI19UkNhZj7yyoHPoVXUCPDLPasPZMAxsVeFriXg7\r\neLvG3xO+IcAQUn/SK+Pad+cMAhKe0ZPuaYTYeYUNZ1acA6B3bFcag+Q0xcjX\r\nbqsSXfCJRS8pSsJpOwj/khUPpxNEqbcnFbcUpGjQy1H+Az6bHVn33p9SUhBa\r\nYpoVDC7QQzafH1YFBXFEjO9ry5OYsJCG/nbA+yelcdz1AsdgrLS2cToAxqt5\r\nRt7+bZeFyJZnx8kBhC/c9KfdyY7Qq9dXz/F3Qi4/CNR6tB7RyFiHBHNjPLT2\r\n04pfkzaxqjum4lUBR6cZVKoIyrvWNsg5n9f1brJS7xfo78oqVCHAzvcxTxUU\r\nwqv/K29+MzuspPis+a59rLw1J0IBUC6vvvRH4CapUFjVz5dW8Sop/kLUpY1A\r\nVjWIce4vHnKFkSi19B13RevDX4m4/bH2qcs=\r\n=8sdy\r\n-----END PGP SIGNATURE-----\r\n", "size": 24845}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.21.2_1678736187219_0.7279266846660293"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-13T19:36:27.415Z", "publish_time": 1678736187415}, "8.21.3": {"name": "@types/eslint", "version": "8.21.3", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "492598a5134b6fba0687dc3aac61537d01ed91aa816c0516a6e8b0b9d08cca2f", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.21.3", "dist": {"integrity": "sha512-fa7GkppZVEByMWGbTtE5MbmXWJTVbrjjaS8K6uQj+XtuuUv1fsuPAxhygfqLmsb/Ufb3CV8deFCpiMfAgi00Sw==", "shasum": "5794b3911f0f19e34e3a272c49cbdf48d6f543f2", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.21.3.tgz", "fileCount": 17, "unpackedSize": 176567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiKQsJHB1Yve4Qej0rN7lNXmdVKwJ/Opsb1y+GMS0pmAiEA6DX/rhdZBFZDx+U822/yFFhsf2KqbJRkrkAU3namohc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkF2PRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMgw/+I4Chz3I9VH18Ew8p44Ulzd2q1CYbVTZSSX8n6Gepu3/fPHTa\r\nm/VWMWRXsAPZC95yC6FsPkv+84Tb3k/eJbjTdOB1X+M1MIalnY7nb+aVVZ6w\r\nY7o+VPNaW7DyHgAYV0lSokBZx94TXyEkUSGHxMin9mJMjK5YHSTMPH1wZuG2\r\nMKZuxZa+XPeXdBeWwh+D0UypuzNdDVKb2HuAxUBkcTLsbDrERMT5D2FkbqBd\r\nTAn6+sJJiFaE3iCz4TpHViv+XUEBIgS+djO6N0uN66bYAnf//pJd4NhVVPZy\r\niItuQ/Ig8GuZ20G/Kl2udSrv37IZEGcIU1gaj3AuGVylPpUyRjIQCDbzF/M8\r\n9QF9MGf+yAnfHV3qku3RZq6Ynpn/i1oyAS2WdEZgRkj+Evpz3M1b1rV6LNmT\r\nRVGIiIuWVqwdFP+ivrKtApLEJjhSICMq27m0IC/fVKhITO/hxiukIEXiZRQv\r\njIDoIFIwy4NOYgN+6tUt9/QqGvUQFyO3KPXD7seE5xJhW16kIofCKC8y3Luo\r\nadrqGrTWCK0nSag1pY5Yags4wn2fFHVPKRETws7VQqVKYE6XXNrzFeAW4aRj\r\n0WC3INESOa+qCffQCdilo/9ghn2SfCmvAcJdl5ye7EcYwQF/nM5b6/jZt16P\r\nG2m9N2HgPsAVqCyvP6QipnMsGK/yoNcPsIs=\r\n=DZeb\r\n-----END PGP SIGNATURE-----\r\n", "size": 24852}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.21.3_1679254481581_0.5915850907691322"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-19T19:34:41.798Z", "publish_time": 1679254481798}, "8.37.0": {"name": "@types/eslint", "version": "8.37.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "8607f52da34dff65e10f67e2ad7cd8fc3bbedfe25c655fbaa8cc271103c7e2d3", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.37.0", "dist": {"integrity": "sha512-Piet7dG2JBuDIfohBngQ3rCt7MgO9xCO4xIMKxBThCq5PNRB91IjlJ10eJVwfoNtvTErmxLzwBZ7rHZtbOMmFQ==", "shasum": "29cebc6c2a3ac7fea7113207bf5a828fdf4d7ef1", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.37.0.tgz", "fileCount": 17, "unpackedSize": 176528, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGo+IXzTPcZyIX9cqjT54GhCaOtdqgXKMCL8LZ86+UyzAiBpa5LAyz6XuWNx18t6vwWNOgRRod4R9e62RBusb9lynw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJKeNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogHw//U/Rx+o00GTxQE30TNpVL7d0/V8BcQK86xFUi9KoiYTMmchY8\r\nEZNcMgTbJANitpZvu35/wTq1S3fcd6UOh4zSIvJg0jQvtMc8kdrWsO9drAIj\r\nGISJZmRx6zy8esfdniPTlGEoj24OlYzk7MXV77hSOfIjOFdxlSVQGi/lknDn\r\n18xuJC2ilkzx7tvpvKMWHrVyzilyp8Lga3udwYMuPqgotsjpfyLM06fp2Giu\r\nRcz0z2bORhIlfkr7R4HqPfBOGSao0i3fQRlEV5E4aOX+oqkynBxci54g3SQD\r\nhA0hele3AD/n2eqsaBuKx+utjUq6DMubYU37Vp3c0yDX0WAwndWbHF8hzI72\r\n6vsY+0/U2ykBQb+/Iiowvx8qXx3vkL0zzOUsN0BA/4GQ0esFoE9YUJqeSf7O\r\neiUB+BGeVrfdbcMi9LPGIUgxwhg7woDSWp4YMK2GYMnd1V+rJ3D2K/bV73Qz\r\n+vu+JWw4NAqvWA559dLgRFLjXQMM6jpYx+Raen3QS3HL9ZSxqdRyNB0/2lc6\r\no5ANX854Ltm4ywKImVno+Iyqp3oXQK4yQ0Itwnf0TjoOEUiBFrfv6Btjvk7L\r\n0DK+bVXOAk8Y84W2Mky8mTxWjSMKtEHyWoveClHtF04/gwH3VZIKXzj8prRT\r\nCNuwDsRxZ/0cr2uv9DCbqkl71Z7ZYtkmfrM=\r\n=YKLh\r\n-----END PGP SIGNATURE-----\r\n", "size": 24837}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.37.0_1680123788902_0.7311727779143522"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-29T21:03:09.062Z", "publish_time": 1680123789062}, "8.40.0": {"name": "@types/eslint", "version": "8.40.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "b717fba7cffe300cc388302745bf2f03fdcd331285a3967fcb4f023f784805bc", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.40.0", "dist": {"integrity": "sha512-nbq2mvc/tBrK9zQQuItvjJl++GTN5j06DaPtp3hZCpngmG6Q3xoyEmd0TwZI0gAy/G1X0zhGBbr2imsGFdFV0g==", "shasum": "ae73dc9ec5237f2794c4f79efd6a4c73b13daf23", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.40.0.tgz", "fileCount": 17, "unpackedSize": 176965, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhfpYGp/K9vEUbXKcDWlEIbqErtgUDYliCLID7RC3wBAiEApA3uGT7VU+d/wyphx88FbHLo5nWbva2JwMgdPVAtVus="}], "size": 24899}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.40.0_1684807368407_0.5787333615822849"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-23T02:02:48.588Z", "publish_time": 1684807368588, "_source_registry_name": "default"}, "8.40.1": {"name": "@types/eslint", "version": "8.40.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "b85271fef36c21bcbaeff3d56591566b7605fd812ddbeb1cf3d0b321f023b4d1", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.40.1", "dist": {"integrity": "sha512-vRb792M4mF1FBT+eoLecmkpLXwxsBHvWWRGJjzbYANBM6DtiJc6yETyv4rqDA6QNjF1pkj1U7LMA6dGb3VYlHw==", "shasum": "92edc592c3575b52a8e790cd5ec04efe28f3d24c", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.40.1.tgz", "fileCount": 17, "unpackedSize": 177660, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDaomGbdU0i/TnjEPvQqFdytzhImO/ALZ2qvqp6Dm2ZCAiEAkE8Hb3DDFShktp6/6nDgIa6UjViPMfmto6zNkPx9MDw="}], "size": 25084}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.40.1_1686256428587_0.41207723424199205"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-08T20:33:48.777Z", "publish_time": 1686256428777, "_source_registry_name": "default"}, "8.40.2": {"name": "@types/eslint", "version": "8.40.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "9d5b2f4c143177ad28847203107d65015359c5f11d59cdc97431565dfd4832cc", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.40.2", "dist": {"integrity": "sha512-PRVjQ4Eh9z9pmmtaq8nTjZjQwKFk7YIHIud3lRoKRBgUQjgjRmoGxxGEPXQkF+lH7QkHJRNr5F4aBgYCW0lqpQ==", "shasum": "2833bc112d809677864a4b0e7d1de4f04d7dac2d", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.40.2.tgz", "fileCount": 17, "unpackedSize": 177702, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqipwkF15RFS3mvGw94czh1GQkXDpvzGmsMQyySs2/xQIgROPu+vBgbfnS1VtP0gjOT3gNLM3AGeM+VPQKvPQiRr4="}], "size": 25106}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.40.2_1686639766623_0.38931509553463783"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-13T07:02:46.838Z", "publish_time": 1686639766838, "_source_registry_name": "default"}, "8.44.0": {"name": "@types/eslint", "version": "8.44.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "9e16a632cfab5cd2650f2a60ed8203bee106910f700a1eb681bef358444f9279", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.44.0", "dist": {"integrity": "sha512-gsF+c/0XOguWgaOgvFs+xnnRqt9GwgTvIks36WpE6ueeI4KCEHHd8K/CKHqhOqrJKsYH8m27kRzQEvWXAwXUTw==", "shasum": "55818eabb376e2272f77fbf5c96c43137c3c1e53", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.0.tgz", "fileCount": 17, "unpackedSize": 177731, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6GdgQ2sYsIxX50eWXkc2WWAPutZVDb/UupdD1//Z5PQIgFPXVPMgc6hF2uX3G4Kgxh+3uPcetEO1b73gQaDwm4Nc="}], "size": 25106}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.0_1688745764257_0.802808229992348"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-07T16:02:44.553Z", "publish_time": 1688745764553, "_source_registry_name": "default"}, "8.44.1": {"name": "@types/eslint", "version": "8.44.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "7a1d8175f2c5c67bd1abece469e3cd87596a8f19ebf261bdc2eadd128b9db275", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.44.1", "dist": {"integrity": "sha512-XpNDc4Z5Tb4x+SW1MriMVeIsMoONHCkWFMkR/aPJbzEsxqHy+4Glu/BqTdPrApfDeMaXbtNh6bseNgl5KaWrSg==", "shasum": "d1811559bb6bcd1a76009e3f7883034b78a0415e", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.1.tgz", "fileCount": 17, "unpackedSize": 178032, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvB/El1BsNsPqwrke54nSEmvZC7bXdpP9x+ph12QkfAAIgNMWDppPMJomozByzlWycvJxRrly1iAwzcNqV6ks2JH8="}], "size": 25155}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.1_1690279385653_0.3468975365237723"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-25T10:03:05.849Z", "publish_time": 1690279385849, "_source_registry_name": "default"}, "8.44.2": {"name": "@types/eslint", "version": "8.44.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "97b0176e71d5a8db5e07c2bd8d35d2ed6738aa1eaa3f7175881f9942d304c0ba", "typeScriptVersion": "4.3", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.44.2", "dist": {"integrity": "sha512-sdPRb9K6iL5XZOmBubg8yiFp5yS/JdUDQsq5e6h95km91MCYMuvp7mh1fjPEYUhvHepKpZOjnEaMBR4PxjWDzg==", "shasum": "0d21c505f98a89b8dd4d37fa162b09da6089199a", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.2.tgz", "fileCount": 17, "unpackedSize": 178426, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCX1tOGMoMx7PNoxTqf30IdDFyPSoWjeJ9Vqt3FjE7pUAIhAKedOafhlcyUigOjoK6eDlhaXgmCaGeHkXIEaSluZX3a"}], "size": 25218}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.2_1691180395442_0.47068000456949055"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-04T20:19:55.645Z", "publish_time": 1691180395645, "_source_registry_name": "default"}, "8.44.3": {"name": "@types/eslint", "version": "8.44.3", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "438e9f01edd3565ee2172ed7cf0d179893e4cc5540348a0931b210317ae1694c", "typeScriptVersion": "4.5", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.44.3", "dist": {"integrity": "sha512-iM/WfkwAhwmPff3wZuPLYiHX18HI24jU8k1ZSH7P8FHwxTjZ2P6CoX2wnF43oprR+YXJM6UUxATkNvyv/JHd+g==", "shasum": "96614fae4875ea6328f56de38666f582d911d962", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.3.tgz", "fileCount": 17, "unpackedSize": 179272, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPnCs9Z5C3YaL1QCiGUP9/8fnY+89sZxa2kcpobMufggIgBWkjqTfvFvAvTCme7mFAPN5KBpN/kpL3yJCvZZbmiUI="}], "size": 25245}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.3_1695489729913_0.45060048459768565"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-23T17:22:10.040Z", "publish_time": 1695489730040, "_source_registry_name": "default"}, "8.44.4": {"name": "@types/eslint", "version": "8.44.4", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "ca9bb88d04679d2bf69c0ba7cded70c86c2a6841f82bf16b396eceb8ee3be986", "typeScriptVersion": "4.5", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "_id": "@types/eslint@8.44.4", "dist": {"integrity": "sha512-lOzjyfY/D9QR4hY9oblZ76B90MYTB3RrQ4z2vBIJKj9ROCRqdkYl2gSUx1x1a4IWPjKJZLL4Aw1Zfay7eMnmnA==", "shasum": "28eaff82e1ca0a96554ec5bb0188f10ae1a74c2f", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.4.tgz", "fileCount": 17, "unpackedSize": 179272, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4LyQdZ468Up/9wyN+yEeej134DDJcWB0jjjKdP+25owIgAKrb11GISCAbpSDWtauqWWzRr1Qe4bzaS82THECmegc="}], "size": 25245}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.4_1696961591222_0.7530710989591689"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-10T18:13:11.413Z", "publish_time": 1696961591413, "_source_registry_name": "default"}, "8.44.5": {"name": "@types/eslint", "version": "8.44.5", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "4efdeafda3e3df2fd2ca0c7707128874c06eac9ad49e35a38b5790a20e07c02e", "typeScriptVersion": "4.5", "_id": "@types/eslint@8.44.5", "dist": {"integrity": "sha512-Ol2eio8LtD/tGM4Ga7Jb83NuFwEv3NqvssSlifXL9xuFpSyQZw0ecmm2Kux6iU0KxQmp95hlPmGCzGJ0TCFeRA==", "shasum": "24d7f3b07aff47a13b570efd5c52d96f38cd352e", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.5.tgz", "fileCount": 7, "unpackedSize": 58717, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFkSCCe+8B5c2SwfVRgefUHRFDL1F+ZeukMnAuoYZaKjAiEAr8g7wVq5iAlcFNtASJ6IimTdG/1uXqBmGCHvktTzN1U="}], "size": 10843}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.5_1697594238422_0.10147011579175635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T01:57:18.620Z", "publish_time": 1697594238620, "_source_registry_name": "default"}, "8.44.6": {"name": "@types/eslint", "version": "8.44.6", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "e8cf8bd8970811ea6b47a44c09359ab57e44a2ba55645c1ead4db99730bce601", "typeScriptVersion": "4.5", "_id": "@types/eslint@8.44.6", "dist": {"integrity": "sha512-P6bY56TVmX8y9J87jHNgQh43h6VVU+6H7oN7hgvivV81K2XY8qJZ5vqPy/HdUoVIelii2kChYVzQanlswPWVFw==", "shasum": "60e564551966dd255f4c01c459f0b4fb87068603", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.6.tgz", "fileCount": 17, "unpackedSize": 178755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBs2ut19y+05i46YBLPeAanGeEyXcSUfjXWemaxDr1qcAiBB6LVGR23zPzQ/A9OBSPLpVgibvc4kZygzr1ff8wVJAw=="}], "size": 25135}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.6_1697653091029_0.87819588922931"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T18:18:11.464Z", "publish_time": 1697653091464, "_source_registry_name": "default"}, "8.44.7": {"name": "@types/eslint", "version": "8.44.7", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "facea692714088a94ef6a014a4a7fc3cb11893272d6f0cdea27862748165784a", "typeScriptVersion": "4.5", "_id": "@types/eslint@8.44.7", "dist": {"integrity": "sha512-f5ORu2hcBbKei97U73mf+l9t4zTGl74IqZ0GQk4oVea/VS8tQZYkUveSYojk+frraAVYId0V2WC9O4PTNru2FQ==", "shasum": "430b3cc96db70c81f405e6a08aebdb13869198f5", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.7.tgz", "fileCount": 17, "unpackedSize": 178755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWbKXX7l1n3Kn/qjiU+IhXHvrvOfWCfC6aJ5aISAkXcAIhAJ1b+gXNKvVCfkM8CCE1B9EWKI0K6mDrV9OS8BGs9hrj"}], "size": 25154}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.7_1699323717623_0.20411300889498185"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T02:21:57.842Z", "publish_time": 1699323717842, "_source_registry_name": "default"}, "8.44.8": {"name": "@types/eslint", "version": "8.44.8", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "44bd32a5a3f42ae64eebe679c666f41132e6db4a5fdfd5ce128c6746524195d0", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.44.8", "dist": {"integrity": "sha512-4K8GavROwhrYl2QXDXm0Rv9epkA8GBFu0EI+XrrnnuCl7u8CWBRusX7fXJfanhZTDWSAL24gDI/UqXyUM0Injw==", "shasum": "f4fe1dab9b3d3dd98082d4b9f80e59ab40f1261c", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.8.tgz", "fileCount": 17, "unpackedSize": 185006, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2Y7+54T7gcII+sWZ6jc27pW19WMdkn1E4PIai7Yhl1AIgUDhRjyNPk0EX/5XZ6EsZxE2jQKZb4DhPiHUWFWKn+7s="}], "size": 26297}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.8_1701307879767_0.23158173232443624"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-30T01:31:19.955Z", "publish_time": 1701307879955, "_source_registry_name": "default"}, "8.44.9": {"name": "@types/eslint", "version": "8.44.9", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "f6e532cb5bf4e4483722208cb77d29b7174deb81b16ed4260f364eb30428c612", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.44.9", "dist": {"integrity": "sha512-6yBxcvwnnYoYT1Uk2d+jvIfsuP4mb2EdIxFnrPABj5a/838qe5bGkNLFOiipX4ULQ7XVQvTxOh7jO+BTAiqsEw==", "shasum": "5799663009645637bd1c45b2e1a7c8f4caf89534", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.44.9.tgz", "fileCount": 17, "unpackedSize": 185084, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkjZrtMrOGXdPxHb5/0ky03/ERTVctt7F+UAiQdsWL6AIhAJVgrk0p6SE4F0tqBXDk6lTJMrhH4zJLlr6rJKsMeSua"}], "size": 26322}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.44.9_1702424155625_0.08018883462881177"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-12T23:35:55.815Z", "publish_time": 1702424155815, "_source_registry_name": "default"}, "8.56.0": {"name": "@types/eslint", "version": "8.56.0", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "36b0447598c69c84a38605d034a18888c3c3b3c3bccece5182e61527cfcedfff", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.56.0", "dist": {"integrity": "sha512-FlsN0p4FhuYRjIxpbdXovvHQhtlG05O1GG/RNWvdAxTboR438IOTwmrY/vLA+Xfgg06BTkP045M3vpFwTMv1dg==", "shasum": "e28d045b8e530a33c9cbcfbf02332df0d1380a2c", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.0.tgz", "fileCount": 17, "unpackedSize": 185121, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFQlEUO9fux10xwr6dD+VNBbuKxC9mJVXXtnDD31b1sQAiAuABzbKUoBx+mi2UV7lGscLr1ymLZlPsFsYdOxe/Cg6g=="}], "size": 26328}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.0_1703035264827_0.09783465601877195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-20T01:21:05.015Z", "publish_time": 1703035265015, "_source_registry_name": "default"}, "8.56.1": {"name": "@types/eslint", "version": "8.56.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "5545125a504ee82ab59763afeb3fb37985d62dd308b124fa210b44af204a903b", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.56.1", "dist": {"integrity": "sha512-18PLWRzhy9glDQp3+wOgfLYRWlhgX0azxgJ63rdpoUHyrC9z0f5CkFburjQx4uD7ZCruw85ZtMt6K+L+R8fLJQ==", "shasum": "988cabb39c973e9200f35fdbb29d17992965bb08", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.1.tgz", "fileCount": 17, "unpackedSize": 185485, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmWMa2ifHsO/k3T//m32OsrX/HjxPHKjhf1fmUBDBllAiEAyRPQuhx8Ua+q8Yw+Df/mYgbbOWWNP53qq+C4waTVHoA="}], "size": 26390}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.1_1704236817725_0.7086957750158966"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-02T23:06:57.910Z", "publish_time": 1704236817910, "_source_registry_name": "default"}, "8.56.2": {"name": "@types/eslint", "version": "8.56.2", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "1f593f127b3936d8611ca577ae65c2c00c4c9f56d078c7e541904c95e572775b", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.56.2", "dist": {"integrity": "sha512-uQDwm1wFHmbBbCZCqAlq6Do9LYwByNZHWzXppSnay9SuwJ+VRbjkbLABer54kcPnMSlG6Fdiy2yaFXm/z9Z5gw==", "shasum": "1c72a9b794aa26a8b94ad26d5b9aa51c8a6384bb", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.2.tgz", "fileCount": 17, "unpackedSize": 185560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzdKOe8tiBuF7S7nFS69fDFA7jKvKQGaxfbMI9t+K/iQIgW0t/YNCK2ANTnkgmhzDMque1Huj8OMt+BsCjjsCQSns="}], "size": 26397}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.2_1704939504615_0.6970107381952342"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-11T02:18:24.796Z", "publish_time": 1704939504796, "_source_registry_name": "default"}, "8.56.3": {"name": "@types/eslint", "version": "8.56.3", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "db61d0c45ee279ce72fec6d0a714d75c6f8212d6a92954dada5d010c1170058a", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.56.3", "dist": {"integrity": "sha512-PvSf1wfv2wJpVIFUMSb+i4PvqNYkB9Rkp9ZDO3oaWzq4SKhsQk4mrMBr3ZH06I0hKrVGLBacmgl8JM4WVjb9dg==", "shasum": "d1f6b2303ac5ed53cb2cf59e0ab680cde1698f5f", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.3.tgz", "fileCount": 17, "unpackedSize": 185738, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOz5CA1vjlRMxPvMPJJjiQ8ZlxLW3wAbXvvEUvvsxDvAIgLLbFAPYeJk/R5lKkE1uG1VdcodxxNRDrgbHTG5cAfnY="}], "size": 26477}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.3_1708641317930_0.30806671882038894"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-22T22:35:18.133Z", "publish_time": 1708641318133, "_source_registry_name": "default"}, "8.56.4": {"name": "@types/eslint", "version": "8.56.4", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "8ae0e5cc4b09608bfecdba6f13af7bde23b0995c86abf6d9f3d612694594ea55", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.56.4", "dist": {"integrity": "sha512-lG1GLUnL5vuRBGb3MgWUWLdGMH2Hps+pERuyQXCfWozuGKdnhf9Pbg4pkcrVUHjKrU7Rl+GCZ/299ObBXZFAxg==", "shasum": "1ce772b385cf23982d048c3ddadba6ff5787c761", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.4.tgz", "fileCount": 17, "unpackedSize": 186191, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICoU7Lg8xYq2/LgwvSzMcbqft6JaqVC9GcFljYbdrRzLAiEAgLb53p7UxcpvP8YahOqMX1PnHEjxy+lgWPqrmbh9ucs="}], "size": 26594}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.4_1709064442194_0.7282289064343368"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-27T20:07:22.330Z", "publish_time": 1709064442330, "_source_registry_name": "default"}, "8.56.5": {"name": "@types/eslint", "version": "8.56.5", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "c7fc31d592811debbc973a368f8cfa078403473c797bb1af36cb9866859f2517", "typeScriptVersion": "4.6", "_id": "@types/eslint@8.56.5", "dist": {"integrity": "sha512-u5/YPJHo1tvkSF2CE0USEkxon82Z5DBy2xR+qfyYNszpX9qcs4sT6uq2kBbj4BXY1+DBGDPnrhMZV3pKWGNukw==", "shasum": "94b88cab77588fcecdd0771a6d576fa1c0af9d02", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.5.tgz", "fileCount": 17, "unpackedSize": 187465, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIGItN0lqZjRtf2K9f25HORAQwwlG8Ibkh6PPZNcNLIwIhAJh+Ynt3Ku5zcmbDLOhErRCD4Nw99SqE8AmV3iw20bEu"}], "size": 26732}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.5_1709167816277_0.6655377222078918"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-29T00:50:16.453Z", "publish_time": 1709167816453, "_source_registry_name": "default"}, "8.56.6": {"name": "@types/eslint", "version": "8.56.6", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "bfce2c99dc7236752e3b1b9610a7f210f49fe944f07b5a0eb303f1993afed76c", "typeScriptVersion": "4.7", "_id": "@types/eslint@8.56.6", "dist": {"integrity": "sha512-ymwc+qb1XkjT/gfoQwxIeHZ6ixH23A+tCT2ADSA/DPVKzAjwYkTXBMCQ/f6fe4wEa85Lhp26VPeUxI7wMhAi7A==", "shasum": "d5dc16cac025d313ee101108ba5714ea10eb3ed0", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.6.tgz", "fileCount": 17, "unpackedSize": 192337, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHoAYA2YLCBpQvcuY7by7aWsJYvU5/dNjonofIt34lwQIhAJOYxB/90FsNi+w4bpbr5/ZOrhatGDdCMDIOTDl+3jSz"}], "size": 27447}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.6_1710840967673_0.38130223080127745"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-19T09:36:07.855Z", "publish_time": 1710840967855, "_source_registry_name": "default"}, "8.56.7": {"name": "@types/eslint", "version": "8.56.7", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "030f7747b677c429470413a3dd9bd02933c602941386d52abbdfe11a9ebba260", "typeScriptVersion": "4.7", "_id": "@types/eslint@8.56.7", "dist": {"integrity": "sha512-SjDvI/x3zsZnOkYZ3lCt9lOZWZLB2jIlNKz+LBgCtDurK0JZcwucxYHn1w2BJkD34dgX9Tjnak0txtq4WTggEA==", "shasum": "c33b5b5a9cfb66881beb7b5be6c34aa3e81d3366", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.7.tgz", "fileCount": 17, "unpackedSize": 192500, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQL54GTykKJJQqdQYQ7ksxcKaK4b8wOROe140C/uDcVgIgWocypEFVRmP0ioIx0tWO2BW9w8oCgvdcybs1btc8AfE="}], "size": 27484}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.7_1711975355111_0.5081384348201075"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-01T12:42:35.272Z", "publish_time": 1711975355272, "_source_registry_name": "default"}, "8.56.8": {"name": "@types/eslint", "version": "8.56.8", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "62d57f5386075275b6b9bf8341410be06c06e489af963b0ae16a9be3ff14c307", "typeScriptVersion": "4.7", "_id": "@types/eslint@8.56.8", "dist": {"integrity": "sha512-LdDdQVDzDXf3ijhhMnE27C5vc0QEknD8GiMR/Hi+fVbdZNfAfCy2j69m0LjUd2MAy0+kIgnOtd5ndTmDk/VWCA==", "shasum": "e927fdc742a98fc3195a9d047631e6ab95029b50", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.8.tgz", "fileCount": 17, "unpackedSize": 192544, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyPAfoBCiPWX/c7+WEofpVAXbUaWi8dhYkppUP15mpKgIhAM9tcI5HFf4BMaNxjIZdM3bUbryNP4DBsWUK5ibCqu8m"}], "size": 27484}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.8_1712830054622_0.5754483982686183"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-11T10:07:34.780Z", "publish_time": 1712830054780, "_source_registry_name": "default"}, "8.56.9": {"name": "@types/eslint", "version": "8.56.9", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "fca313ad4cad791fcfba0e6d32d0588e2935013152bb3b926c09b41fbbbcced8", "typeScriptVersion": "4.7", "_id": "@types/eslint@8.56.9", "dist": {"integrity": "sha512-W4W3KcqzjJ0sHg2vAq9vfml6OhsJ53TcUjUqfzzZf/EChUtwspszj/S0pzMxnfRcO55/iGq47dscXw71Fxc4Zg==", "shasum": "403e9ced04a34e63f1c383c5b8ee1a94442c8cc4", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.9.tgz", "fileCount": 17, "unpackedSize": 192435, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBzT38zHU0UkiomD1EIA+JIrtCDsyJEad42COcprDDmNAiAQ2KPLA0sn672PJP8Tyz1oBmFzQpXfS6gFjX7nQqe72Q=="}], "size": 27464}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.9_1712898439907_0.7788473996806216"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T05:07:20.085Z", "publish_time": 1712898440085, "_source_registry_name": "default"}, "8.56.10": {"name": "@types/eslint", "version": "8.56.10", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "522d3c76ab9a1be04075a38852c1f9c8c64f6722d17428f2cbbddc55e0477b81", "typeScriptVersion": "4.7", "_id": "@types/eslint@8.56.10", "dist": {"integrity": "sha512-Shavhk87gCtY2fhXDctcfS3e6FdxWkCx1iUZ9eEUbh7rTqlZT0/IzOkCOVt0fCjcFuZ9FPYfuezTBImfHCDBGQ==", "shasum": "eb2370a73bf04a901eeba8f22595c7ee0f7eb58d", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.10.tgz", "fileCount": 17, "unpackedSize": 192449, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE4WgFNDe9vHmOJ4jSOBmOyi1O4XRnQP6hYBoe8CPBOWAiB0iNf0XMn+mO0t32Vo2B+h8xWVAX6WUU9EGjG0Bx8r1w=="}], "size": 27469}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint_8.56.10_1713465326393_0.47442692850603185"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-18T18:35:26.656Z", "publish_time": 1713465326656, "_source_registry_name": "default"}, "9.6.0": {"name": "@types/eslint", "version": "9.6.0", "license": "MIT", "_id": "@types/eslint@9.6.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pmdartus", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/j-f1", "name": "<PERSON>", "githubUsername": "j-f1"}, {"url": "https://github.com/saadq", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JasonHK", "name": "<PERSON>", "githubUsername": "JasonHK"}, {"url": "https://github.com/bradzacher", "name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>"}, {"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bmish", "name": "<PERSON>", "githubUsername": "bmish"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "dist": {"shasum": "51d4fe4d0316da9e9f2c80884f2c20ed5fb022ff", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-9.6.0.tgz", "fileCount": 16, "integrity": "sha512-gi6WQJ7cHRgZxtkQEoyHMppPjq9Kxo5Tjn2prSKDSmZrCz8TZ3jSRCeTJm+WoM+oB0WG37bRqLzaaU3q7JypGg==", "signatures": [{"sig": "MEUCIGB9Crw9eaGqxRE0eS3fA1VHvBMGGCnc1H5nY8ioLQkQAiEAvJVt9hLxbW9Krs6ad2/21UU7GeYGudzMzRjKknaBV3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195449, "size": 27652}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json", "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint"}, "description": "TypeScript definitions for eslint", "directories": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/eslint_9.6.0_1721666349156_0.9180728984084623", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b9dc2442a6de9477777294005e6b8dcf6c0dee1bdcf1fe79fea879291a522175", "_cnpmcore_publish_time": "2024-07-22T16:39:09.306Z", "publish_time": 1721666349306, "_source_registry_name": "default"}, "8.56.11": {"name": "@types/eslint", "version": "8.56.11", "license": "MIT", "_id": "@types/eslint@8.56.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pmdartus", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/j-f1", "name": "<PERSON>", "githubUsername": "j-f1"}, {"url": "https://github.com/saadq", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JasonHK", "name": "<PERSON>", "githubUsername": "JasonHK"}, {"url": "https://github.com/bradzacher", "name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>"}, {"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bmish", "name": "<PERSON>", "githubUsername": "bmish"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "dist": {"shasum": "e2ff61510a3b9454b3329fe7731e3b4c6f780041", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.11.tgz", "fileCount": 17, "integrity": "sha512-sVBpJMf7UPo/wGecYOpk2aQya2VUGeHhe38WG7/mN5FufNSubf5VT9Uh9Uyp8/eLJpu1/tuhJ/qTo4mhSB4V4Q==", "signatures": [{"sig": "MEQCIEFHDAiy6YQ0/a2BcnnpojOmVvKP7e031vGtDJl7u7tyAiA4kR2sBxLi1YiXCVbmP5AX0BFuuZJcksfB8HbTQk0KJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192575, "size": 27549}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json", "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint"}, "description": "TypeScript definitions for eslint", "directories": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/eslint_8.56.11_1721666358761_0.12947396639794229", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f6b09fbfa18c11a366df42487ac6d2f35282e2b73174827ab401cbbec841c061", "_cnpmcore_publish_time": "2024-07-22T16:39:18.967Z", "publish_time": 1721666358967, "_source_registry_name": "default"}, "9.6.1": {"name": "@types/eslint", "version": "9.6.1", "license": "MIT", "_id": "@types/eslint@9.6.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pmdartus", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/j-f1", "name": "<PERSON>", "githubUsername": "j-f1"}, {"url": "https://github.com/saadq", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JasonHK", "name": "<PERSON>", "githubUsername": "JasonHK"}, {"url": "https://github.com/bradzacher", "name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>"}, {"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bmish", "name": "<PERSON>", "githubUsername": "bmish"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "dist": {"shasum": "d5795ad732ce81715f27f75da913004a56751584", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-9.6.1.tgz", "fileCount": 16, "integrity": "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==", "signatures": [{"sig": "MEYCIQD3xsjGD7kPv9RbaC4bETopskOcH8d74df7pkn9K4GfzwIhAM57Ka1fWFmsIQ3Dez9JrE4H7JR5Huy7KzFKrcER/Wg4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195627, "size": 27661}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json", "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint"}, "description": "TypeScript definitions for eslint", "directories": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/eslint_9.6.1_1724656090912_0.9951118577159497", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bc2620143f844d291da2d199e7b8e2605e3277f1941a508dc72ac92843b149b6", "_cnpmcore_publish_time": "2024-08-26T07:08:11.087Z", "publish_time": 1724656091087, "_source_registry_name": "default"}, "8.56.12": {"name": "@types/eslint", "version": "8.56.12", "license": "MIT", "_id": "@types/eslint@8.56.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pmdartus", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/j-f1", "name": "<PERSON>", "githubUsername": "j-f1"}, {"url": "https://github.com/saadq", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JasonHK", "name": "<PERSON>", "githubUsername": "JasonHK"}, {"url": "https://github.com/bradzacher", "name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>"}, {"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bmish", "name": "<PERSON>", "githubUsername": "bmish"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "dist": {"shasum": "1657c814ffeba4d2f84c0d4ba0f44ca7ea1ca53a", "tarball": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.12.tgz", "fileCount": 17, "integrity": "sha512-03<PERSON>ubjWyOHlmljCVoxSuNDdmfZDzsrrz0P2LeJsOXr+ZwFQ+0yQIwNCwt/GYhV7Z31fgtXJTAEs+FYlEL851g==", "signatures": [{"sig": "MEQCIF2XtG89EaMHa78lBcWZKQbKpFenUu+RFlaqGTua6ggUAiAWFTYgfIaGKy0zy72yG6rY27daPdbm4B0SxIln34q/LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192753, "size": 27557}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json", "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint"}, "description": "TypeScript definitions for eslint", "directories": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/eslint_8.56.12_1724656100504_0.989462084482817", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "804eabaa45a9d9fa17f60c85a8d5139ef8826929e60e1de0ada0c3dbf17f6e2b", "_cnpmcore_publish_time": "2024-08-26T07:08:20.669Z", "publish_time": 1724656100669, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/pmdartus", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/j-f1", "name": "<PERSON>", "githubUsername": "j-f1"}, {"url": "https://github.com/saadq", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JasonHK", "name": "<PERSON>", "githubUsername": "JasonHK"}, {"url": "https://github.com/bradzacher", "name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>"}, {"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bmish", "name": "<PERSON>", "githubUsername": "bmish"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/eslint"}, "_source_registry_name": "default"}