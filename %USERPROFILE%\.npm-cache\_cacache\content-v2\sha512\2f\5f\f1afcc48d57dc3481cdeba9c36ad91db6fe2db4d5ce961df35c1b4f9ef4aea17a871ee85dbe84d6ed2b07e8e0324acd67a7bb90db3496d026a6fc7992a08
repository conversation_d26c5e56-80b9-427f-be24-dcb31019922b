{"_id": "@isaacs/brace-expansion", "_rev": "5206511-684bde3e67627409cfc2d8ee", "dist-tags": {"latest": "5.0.0"}, "name": "@isaacs/brace-expansion", "time": {"created": "2025-06-13T08:15:58.805Z", "modified": "2025-06-13T08:15:59.491Z", "5.0.0": "2025-06-12T20:06:57.789Z"}, "versions": {"5.0.0": {"name": "@isaacs/brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "5.0.0", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "type": "module", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.2", "@types/node": "^24.0.0", "mkdirp": "^3.0.1", "prettier": "^3.3.2", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.5"}, "dependencies": {"@isaacs/balanced-match": "^4.0.1"}, "license": "MIT", "engines": {"node": "20 || >=22"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "_id": "@isaacs/brace-expansion@5.0.0", "gitHead": "098d08f021620152df88dabd218755c22704eed5", "_nodeVersion": "22.14.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA==", "shasum": "4b3dabab7d8e75a429414a96bd67bf4c1d13e0f3", "tarball": "https://registry.npmmirror.com/@isaacs/brace-expansion/-/brace-expansion-5.0.0.tgz", "fileCount": 13, "unpackedSize": 45347, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHcxouL8GWTCb+F/NzgyiE3NedfRHh+gRcpD8ucWbNhDAiEA44ssKl5FWiq68EbX8UZWqigQ6kntQj5nE9NaI5nlJYM="}], "size": 7503}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/brace-expansion_5.0.0_1749758817612_0.04222675813374144"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-12T20:06:57.789Z", "publish_time": 1749758817789, "_source_registry_name": "default"}}, "description": "Brace expansion as known from sh/bash", "license": "MIT", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# @isaacs/brace-expansion\n\nA hybrid CJS/ESM TypeScript fork of\n[brace-expansion](http://npm.im/brace-expansion).\n\n[Brace expansion](https://www.gnu.org/software/bash/manual/html_node/Brace-Expansion.html),\nas known from sh/bash, in JavaScript.\n\n[![CI](https://github.com/juliangruber/brace-expansion/actions/workflows/ci.yml/badge.svg)](https://github.com/juliangruber/brace-expansion/actions/workflows/ci.yml)\n[![downloads](https://img.shields.io/npm/dm/brace-expansion.svg)](https://www.npmjs.org/package/brace-expansion)\n\n## Example\n\n```js\nimport { expand } from '@isaacs/brace-expansion'\n\nexpand('file-{a,b,c}.jpg')\n// => ['file-a.jpg', 'file-b.jpg', 'file-c.jpg']\n\nexpand('-v{,,}')\n// => ['-v', '-v', '-v']\n\nexpand('file{0..2}.jpg')\n// => ['file0.jpg', 'file1.jpg', 'file2.jpg']\n\nexpand('file-{a..c}.jpg')\n// => ['file-a.jpg', 'file-b.jpg', 'file-c.jpg']\n\nexpand('file{2..0}.jpg')\n// => ['file2.jpg', 'file1.jpg', 'file0.jpg']\n\nexpand('file{0..4..2}.jpg')\n// => ['file0.jpg', 'file2.jpg', 'file4.jpg']\n\nexpand('file-{a..e..2}.jpg')\n// => ['file-a.jpg', 'file-c.jpg', 'file-e.jpg']\n\nexpand('file{00..10..5}.jpg')\n// => ['file00.jpg', 'file05.jpg', 'file10.jpg']\n\nexpand('{{A..C},{a..c}}')\n// => ['A', 'B', 'C', 'a', 'b', 'c']\n\nexpand('ppp{,config,oe{,conf}}')\n// => ['ppp', 'pppconfig', 'pppoe', 'pppoeconf']\n```\n\n## API\n\n```js\nimport { expand } from '@isaacs/brace-expansion'\n```\n\n### const expanded = expand(str)\n\nReturn an array of all possible and valid expansions of `str`. If none are\nfound, `[str]` is returned.\n\nValid expansions are:\n\n```js\n/^(.*,)+(.+)?$/\n// {a,b,...}\n```\n\nA comma separated list of options, like `{a,b}` or `{a,{b,c}}` or `{,a,}`.\n\n```js\n/^-?\\d+\\.\\.-?\\d+(\\.\\.-?\\d+)?$/\n// {x..y[..incr]}\n```\n\nA numeric sequence from `x` to `y` inclusive, with optional increment.\nIf `x` or `y` start with a leading `0`, all the numbers will be padded\nto have equal length. Negative numbers and backwards iteration work too.\n\n```js\n/^-?\\d+\\.\\.-?\\d+(\\.\\.-?\\d+)?$/\n// {x..y[..incr]}\n```\n\nAn alphabetic sequence from `x` to `y` inclusive, with optional increment.\n`x` and `y` must be exactly one character, and if given, `incr` must be a\nnumber.\n\nFor compatibility reasons, the string `${` is not eligible for brace expansion.\n", "_source_registry_name": "default"}