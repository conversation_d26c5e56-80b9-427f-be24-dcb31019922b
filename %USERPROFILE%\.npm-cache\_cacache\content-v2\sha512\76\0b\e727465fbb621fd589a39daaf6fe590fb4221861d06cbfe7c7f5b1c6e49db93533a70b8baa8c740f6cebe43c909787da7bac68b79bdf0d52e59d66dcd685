{"_attachments": {}, "_id": "image-size", "_rev": "62007-61f1832f0053da989101cded", "author": {"url": "http://netroy.in/", "name": "netroy", "email": "<EMAIL>"}, "description": "get dimensions of any image file", "dist-tags": {"latest": "2.0.2", "legacy": "1.2.1"}, "license": "MIT", "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "name": "image-size", "readme": "# image-size\n\n[![Build Status](https://circleci.com/gh/image-size/image-size.svg?style=shield)](https://circleci.com/gh/image-size/image-size)\n[![Package Version](https://img.shields.io/npm/v/image-size.svg)](https://www.npmjs.com/package/image-size)\n[![Downloads](https://img.shields.io/npm/dm/image-size.svg)](http://npm-stat.com/charts.html?package=image-size&author=netroy&from=&to=)\n\nFast, lightweight NodeJS package to get dimensions of any image file or buffer.\n\n## Key Features\n- Zero dependencies\n- Supports all major image formats\n- Works with both files and buffers\n- Minimal memory footprint - reads only image headers\n- ESM and CommonJS support\n- TypeScript types included\n\n## Supported formats\n\n- BMP\n- CUR\n- DDS\n- GIF\n- HEIC (HEIF, AVCI, AVIF)\n- ICNS\n- ICO\n- J2C\n- JPEG-2000 (JP2)\n- JPEG\n- JPEG-XL\n- KTX (1 and 2)\n- PNG\n- PNM (PAM, PBM, PFM, PGM, PPM)\n- PSD\n- SVG\n- TGA\n- TIFF\n- WebP\n\n## Installation\n\n```shell\nnpm install image-size\n# or\nyarn add image-size\n# or\npnpm add image-size\n```\n\n## Usage\n\n### Passing in a Buffer/Uint8Array\nBest for streams, network requests, or when you already have the image data in memory.\n\n```javascript\nimport { imageSize } from 'image-size'\n// or\nconst { imageSize } = require('image-size')\n\nconst dimensions = imageSize(buffer)\nconsole.log(dimensions.width, dimensions.height)\n```\n\n### Reading from a file\nBest for local files. Returns a promise.\n\n```javascript\nimport { imageSizeFromFile } from 'image-size/fromFile'\n// or\nconst { imageSizeFromFile } = require('image-size/fromFile')\n\nconst dimensions = await imageSizeFromFile('photos/image.jpg')\nconsole.log(dimensions.width, dimensions.height)\n```\n\nNote: Reading from files has a default concurrency limit of **100**\nTo change this limit, you can call the `setConcurrency` function like this:\n\n```javascript\nimport { setConcurrency } from 'image-size/fromFile'\n// or\nconst { setConcurrency } = require('image-size/fromFile')\nsetConcurrency(123456)\n```\n\n### Reading from a file Syncronously (not recommended) ⚠️\nv1.x of this library had a sync API, that internally used sync file reads.  \n\nThis isn't recommended because this blocks the node.js main thread, which reduces the performance, and prevents this library from being used concurrently.  \n\nHowever if you still need to use this package syncronously, you can read the file syncronously into a buffer, and then pass the buffer to this library.  \n\n```javascript\nimport { readFileSync } from 'node:fs'\nimport { imageSize } from 'image-size'\n\nconst buffer = readFileSync('photos/image.jpg')\nconst dimensions = imageSize(buffer)\nconsole.log(dimensions.width, dimensions.height)\n```\n\n### 3. Command Line\nUseful for quick checks.\n\n```shell\nnpx image-size image1.jpg image2.png\n```\n\n### Multi-size\n\nIf the target file/buffer is an HEIF, an ICO, or a CUR file, the `width` and `height` will be the ones of the largest image in the set.\n\nAn additional `images` array is available and returns the dimensions of all the available images\n\n```javascript\nimport { imageSizeFromFile } from 'image-size/fromFile'\n// or\nconst { imageSizeFromFile } = require('image-size/fromFile')\n\nconst { images } = await imageSizeFromFile('images/multi-size.ico')\nfor (const dimensions of images) {\n  console.log(dimensions.width, dimensions.height)\n}\n```\n\n### Using a URL\n\n```javascript\nimport url from 'node:url'\nimport http from 'node:http'\nimport { imageSize } from 'image-size'\n\nconst imgUrl = 'http://my-amazing-website.com/image.jpeg'\nconst options = url.parse(imgUrl)\n\nhttp.get(options, function (response) {\n  const chunks = []\n  response\n    .on('data', function (chunk) {\n      chunks.push(chunk)\n    })\n    .on('end', function () {\n      const buffer = Buffer.concat(chunks)\n      console.log(imageSize(buffer))\n    })\n})\n```\n\n### Disabling certain image types\n\n```javascript\nimport { disableTypes } from 'image-size'\n// or\nconst { disableTypes } = require('image-size')\n\ndisableTypes(['tiff', 'ico'])\n```\n\n### JPEG image orientation\n\nIf the orientation is present in the JPEG EXIF metadata, it will be returned by the function. The orientation value is a [number between 1 and 8](https://exiftool.org/TagNames/EXIF.html#:~:text=0x0112,8%20=%20Rotate%20270%20CW) representing a type of orientation.\n\n```javascript\nimport { imageSizeFromFile } from 'image-size/fromFile'\n// or\nconst { imageSizeFromFile } = require('image-size/fromFile')\n\nconst { width, height, orientation } = await imageSizeFromFile('images/photo.jpeg')\nconsole.log(width, height, orientation)\n```\n\n# Limitations\n\n1. **Partial File Reading**\n   - Only reads image headers, not full files\n   - Some corrupted images might still report dimensions\n\n2. **SVG Limitations**\n   - Only supports pixel dimensions and viewBox\n   - Percentage values not supported\n\n3. **File Access**\n   - Reading from files has a default concurrency limit of 100\n   - Can be adjusted using `setConcurrency()`\n\n4. **Buffer Requirements**\n   - Some formats (like TIFF) require the full header in buffer\n   - Streaming partial buffers may not work for all formats\n\n## License\n\nMIT\n\n## Credits\n\nnot a direct port, but an attempt to have something like\n[dabble's imagesize](https://github.com/dabble/imagesize/blob/master/lib/image_size.rb) as a node module.\n\n## [Contributors](Contributors.md)\n", "time": {"created": "2022-01-26T17:21:51.654Z", "modified": "2025-04-02T14:31:54.043Z", "1.0.0": "2021-04-14T17:14:34.355Z", "0.9.7": "2021-03-15T12:22:49.971Z", "0.9.6": "2021-03-15T12:14:31.441Z", "0.9.5": "2021-03-11T12:23:11.583Z", "0.9.4": "2021-02-24T13:40:01.129Z", "0.9.3": "2020-10-26T16:46:46.836Z", "0.9.2": "2020-10-19T12:30:09.797Z", "0.9.1": "2020-09-03T09:55:44.857Z", "0.8.3": "2019-09-30T10:49:39.303Z", "0.8.2": "2019-09-23T15:17:23.892Z", "0.8.1": "2019-09-17T14:49:50.600Z", "0.8.0": "2019-09-17T11:40:41.209Z", "0.7.5": "2019-09-14T23:49:45.081Z", "0.7.4": "2019-04-23T12:22:23.102Z", "0.7.3": "2019-04-02T11:54:23.496Z", "0.7.2": "2019-02-15T15:12:06.715Z", "0.7.1": "2019-01-08T12:03:32.145Z", "0.7.0": "2019-01-08T11:01:17.100Z", "0.6.3": "2018-06-13T10:34:54.588Z", "0.6.2": "2017-12-16T11:35:21.737Z", "0.6.1": "2017-07-19T12:52:25.880Z", "0.6.0": "2017-06-26T11:40:59.233Z", "0.5.5": "2017-06-12T08:19:15.148Z", "0.5.4": "2017-05-15T10:36:38.574Z", "0.5.3": "2017-05-11T14:23:09.468Z", "0.5.2": "2017-05-09T11:06:18.090Z", "0.5.1": "2016-12-25T22:26:29.032Z", "0.5.0": "2016-03-22T21:51:24.205Z", "0.4.0": "2015-11-23T15:00:56.392Z", "0.3.5": "2014-10-16T21:27:39.057Z", "0.3.3": "2014-09-09T15:00:44.084Z", "0.3.2": "2014-07-15T13:51:37.505Z", "0.3.1": "2014-07-11T13:31:55.975Z", "0.3.0": "2014-06-09T14:12:27.971Z", "0.2.5": "2014-05-14T21:19:25.494Z", "0.2.4": "2014-05-09T09:33:10.914Z", "0.2.3": "2014-03-26T17:21:31.466Z", "0.2.2": "2014-03-19T21:28:17.178Z", "0.2.1": "2014-01-22T23:11:37.331Z", "0.1.20": "2014-01-22T23:11:06.362Z", "0.1.17": "2014-01-22T22:46:59.022Z", "0.1.16": "2013-12-03T16:34:35.855Z", "0.0.16": "2013-11-20T11:44:23.748Z", "0.1.15": "2013-11-17T23:07:42.764Z", "0.1.10": "2013-10-11T07:23:25.311Z", "0.1.7": "2013-10-11T06:34:56.411Z", "0.1.6": "2013-10-11T05:09:56.602Z", "0.1.5": "2013-07-05T11:48:58.390Z", "0.1.4": "2013-07-05T11:17:01.871Z", "0.1.3": "2013-07-04T22:07:34.579Z", "0.1.2": "2013-07-04T11:32:37.516Z", "0.1.1": "2013-07-03T23:27:26.355Z", "0.1.0": "2013-07-03T22:39:35.454Z", "0.0.5": "2013-07-03T21:18:12.940Z", "0.0.3": "2013-07-02T01:52:16.830Z", "0.0.2": "2013-07-02T01:38:50.596Z", "1.0.1": "2022-01-10T19:12:20.981Z", "1.0.2": "2022-07-12T14:40:59.467Z", "2.0.0-alpha.1": "2023-03-21T15:11:05.888Z", "2.0.0-alpha.2": "2023-11-13T16:18:50.217Z", "2.0.0-beta": "2023-11-13T21:42:16.231Z", "2.0.0-beta.1": "2023-11-13T23:10:48.079Z", "1.1.0": "2023-12-28T12:03:33.915Z", "1.1.1": "2024-01-02T15:16:15.106Z", "2.0.0-beta.2": "2024-01-12T13:02:14.712Z", "1.2.0": "2024-12-22T04:00:49.642Z", "2.0.0-beta.4": "2024-12-25T20:00:53.615Z", "2.0.0": "2025-02-25T11:47:11.040Z", "2.0.1": "2025-03-13T11:03:59.427Z", "2.0.2": "2025-04-02T14:22:29.940Z", "1.2.1": "2025-04-02T14:30:29.710Z"}, "versions": {"1.0.0": {"name": "image-size", "version": "1.0.0", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.16", "@types/glob": "7.1.3", "@types/mocha": "8.2.2", "@types/node": "14.14.37", "@types/sinon": "10.0.0", "@typescript-eslint/eslint-plugin": "4.22.0", "@typescript-eslint/parser": "4.22.0", "chai": "4.3.4", "eslint": "7.24.0", "glob": "7.1.6", "mocha": "8.3.2", "nyc": "15.1.0", "sinon": "10.0.0", "ts-node": "9.1.1", "typedoc": "0.20.35", "typescript": "4.2.4"}, "dependencies": {"queue": "6.0.2"}, "gitHead": "77b618c6c6ffdc762f07e4853e8b0164312af8f0", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@1.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.12", "dist": {"shasum": "58b31fe4743b1cec0a0ac26f5c914d3c5b2f0750", "size": 13730, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.0.0.tgz", "integrity": "sha512-JLJ6OwBfO1KcA+TvJT+v8gbE6iWbj24LyDNFgFEN0lzegn6cC6a/p3NIDaepMsJjQjlUWqIC7wJv8lBFxPNjcw=="}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_1.0.0_1618420474167_0.6966282369810668"}, "_hasShrinkwrap": false, "publish_time": 1618420474355, "_cnpm_publish_time": 1618420474355, "_cnpmcore_publish_time": "2021-12-13T14:25:56.583Z"}, "0.9.7": {"name": "image-size", "version": "0.9.7", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.15", "@types/glob": "7.1.3", "@types/mocha": "8.2.1", "@types/node": "14.14.34", "@types/sinon": "9.0.11", "@typescript-eslint/eslint-plugin": "4.17.0", "@typescript-eslint/parser": "4.17.0", "chai": "4.3.4", "eslint": "7.22.0", "glob": "7.1.6", "mocha": "8.3.2", "nyc": "15.1.0", "sinon": "9.2.4", "ts-node": "9.1.1", "typedoc": "0.20.32", "typescript": "4.2.3"}, "dependencies": {"queue": "6.0.2"}, "gitHead": "38206973ee59252ab40260c669fd8e984c68a980", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.7", "_nodeVersion": "14.16.0", "_npmVersion": "7.6.0", "dist": {"shasum": "43b4ead4b1310d5ae310a559d52935a347e47c09", "size": 13739, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.7.tgz", "integrity": "sha512-KRVgLNZkr00YGN0qn9MlIrmlxbRhsCcEb1Byq3WKGnIV4M48iD185cprRtaoK4t5iC+ym2Q5qlArxZ/V1yzDgA=="}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.7_1615810969773_0.8607474608906722"}, "_hasShrinkwrap": false, "publish_time": 1615810969971, "_cnpm_publish_time": 1615810969971, "_cnpmcore_publish_time": "2021-12-13T14:25:56.932Z"}, "0.9.6": {"name": "image-size", "version": "0.9.6", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.15", "@types/glob": "7.1.3", "@types/mocha": "8.2.1", "@types/node": "14.14.34", "@types/sinon": "9.0.11", "@typescript-eslint/eslint-plugin": "4.17.0", "@typescript-eslint/parser": "4.17.0", "chai": "4.3.4", "eslint": "7.22.0", "glob": "7.1.6", "mocha": "8.3.2", "nyc": "15.1.0", "sinon": "9.2.4", "ts-node": "9.1.1", "typedoc": "0.20.32", "typescript": "4.2.3"}, "dependencies": {"queue": "6.0.2"}, "gitHead": "196fe8b810f4bbf4680f38387ee0ee35e6652811", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.6", "_nodeVersion": "14.16.0", "_npmVersion": "7.6.0", "dist": {"shasum": "d9c3a1e758de8b6e880c941ff2ac2b2f6d0f63c5", "size": 13735, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.6.tgz", "integrity": "sha512-vaNukzyiIELaz/Dj8RubSaUthKN9rj/TgqKGDFjIbKZn6RgrZihcU0nZnvA4I0N5lXppxXt8AO8KKTWqrpRi9w=="}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.6_1615810471281_0.6561293609416852"}, "_hasShrinkwrap": false, "publish_time": 1615810471441, "_cnpm_publish_time": 1615810471441, "_cnpmcore_publish_time": "2021-12-13T14:25:57.305Z"}, "0.9.5": {"name": "image-size", "version": "0.9.5", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.15", "@types/glob": "7.1.3", "@types/mocha": "8.2.1", "@types/node": "14.14.31", "@types/sinon": "9.0.10", "@typescript-eslint/eslint-plugin": "4.15.2", "@typescript-eslint/parser": "4.15.2", "chai": "4.3.0", "eslint": "7.20.0", "glob": "7.1.6", "mocha": "8.3.0", "nyc": "15.1.0", "sinon": "9.2.4", "ts-node": "9.1.1", "typedoc": "0.20.28", "typescript": "4.2.2"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "ab25dc8a5ce976d9a7713f233aad87574cf60f0d", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.5", "_nodeVersion": "14.16.0", "_npmVersion": "7.6.0", "dist": {"shasum": "03a4224c5b643d2a0a7d21ee561cf7edf8985421", "size": 13692, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.5.tgz", "integrity": "sha512-HvnQBt6+u5PjmZvaFpJOB1VfrDdwxu8p456HAfYXPzEKl6GJKrNJZKDaN0A/0b1kJ7JpDUU6eBT7hmiREwzqWA=="}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.5_1615465391301_0.27126000730638156"}, "_hasShrinkwrap": false, "publish_time": 1615465391583, "_cnpm_publish_time": 1615465391583, "_cnpmcore_publish_time": "2021-12-13T14:25:57.689Z"}, "0.9.4": {"name": "image-size", "version": "0.9.4", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.15", "@types/glob": "7.1.3", "@types/mocha": "8.2.1", "@types/node": "14.14.31", "@types/sinon": "9.0.10", "@typescript-eslint/eslint-plugin": "4.15.2", "@typescript-eslint/parser": "4.15.2", "chai": "4.3.0", "eslint": "7.20.0", "glob": "7.1.6", "mocha": "8.3.0", "nyc": "15.1.0", "sinon": "9.2.4", "ts-node": "9.1.1", "typedoc": "0.20.28", "typescript": "4.2.2"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "7785eba909e885ce3b995e25690a5a7061a5cefa", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.4", "_nodeVersion": "14.16.0", "_npmVersion": "7.5.6", "dist": {"shasum": "45f6f764214b94152fd794c339cf37e10ca2e750", "size": 13690, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.4.tgz", "integrity": "sha512-4xNfbjG++A42OJdTPYlj2PquCxyWmanpBQflalWGlXEOfc3LOZA3aEZ+2iIZU5KorDUCcZ7iv2dhvHBbMN0G0w=="}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.4_1614174000969_0.7810693123232404"}, "_hasShrinkwrap": false, "publish_time": 1614174001129, "_cnpm_publish_time": 1614174001129, "_cnpmcore_publish_time": "2021-12-13T14:25:58.063Z"}, "0.9.3": {"name": "image-size", "version": "0.9.3", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "npm run clean && npm run build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.14", "@types/glob": "7.1.3", "@types/mocha": "8.0.3", "@types/node": "14.11.10", "@types/sinon": "9.0.8", "@typescript-eslint/eslint-plugin": "4.4.1", "@typescript-eslint/parser": "4.4.1", "chai": "4.2.0", "eslint": "7.11.0", "glob": "7.1.6", "mocha": "8.2.0", "nyc": "15.1.0", "sinon": "9.2.0", "ts-node": "9.0.0", "typedoc": "0.19.2", "typescript": "4.0.3"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "931b8a8af957f413f6f08ffaf9c1ad9ac9d99cec", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.3", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.7", "dist": {"shasum": "f7efce6b0a1649b44b9bc43b9d9a5acf272264b6", "size": 13369, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.3.tgz", "integrity": "sha512-5SakFa79uhUVSjKeQE30GVzzLJ0QNzB53+I+/VD1vIesD6GP6uatWIlgU0uisFNLt1u0d6kBydp7yfk+lLJhLQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.3_1603730806615_0.04536085728635508"}, "_hasShrinkwrap": false, "publish_time": 1603730806836, "_cnpm_publish_time": 1603730806836, "_cnpmcore_publish_time": "2021-12-13T14:25:58.450Z"}, "0.9.2": {"name": "image-size", "version": "0.9.2", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "npm run clean && npm run build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.14", "@types/glob": "7.1.3", "@types/mocha": "8.0.3", "@types/node": "14.11.10", "@types/sinon": "9.0.8", "@typescript-eslint/eslint-plugin": "4.4.1", "@typescript-eslint/parser": "4.4.1", "chai": "4.2.0", "eslint": "7.11.0", "glob": "7.1.6", "mocha": "8.2.0", "nyc": "15.1.0", "sinon": "9.2.0", "ts-node": "9.0.0", "typedoc": "0.19.2", "typescript": "4.0.3"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "d7e4f5a717d2c7a79bc4889fd8fa0f70c5805ce8", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.2", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.7", "dist": {"shasum": "04a227e1a60c290dc4b66b27bb811d8e9a7c4d80", "size": 13359, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.2.tgz", "integrity": "sha512-spzCX3sjQjIk19U+UqpGYNgoaJTOJ7yTQ/9XtvVjXbjNNzwH2fP8NeMKdGbh867rK+Ngy5IVYkcsij2udJuDLw=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.2_1603110609598_0.06021478073247888"}, "_hasShrinkwrap": false, "publish_time": 1603110609797, "_cnpm_publish_time": 1603110609797, "_cnpmcore_publish_time": "2021-12-13T14:25:58.843Z"}, "0.9.1": {"name": "image-size", "version": "0.9.1", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "npm run clean && npm run build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.2.12", "@types/glob": "7.1.3", "@types/mocha": "8.0.3", "@types/node": "14.6.3", "@types/sinon": "9.0.5", "@typescript-eslint/eslint-plugin": "4.0.1", "@typescript-eslint/parser": "4.0.1", "chai": "4.2.0", "coveralls": "3.1.0", "eslint": "7.8.1", "glob": "7.1.6", "mocha": "8.1.3", "nyc": "15.1.0", "sinon": "9.0.3", "ts-node": "9.0.0", "typedoc": "0.19.0", "typescript": "4.0.2"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "8fb34896a2012678fa2d54fcc063ddd442159856", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.9.1", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"shasum": "da39bc42b80cd33b143b229a10cf023968d0f1ba", "size": 13372, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.9.1.tgz", "integrity": "sha512-yBo6xGGjiWtApYroCGR9wTvaIgande5vmAfTYIld5ss5kN4tyDG5lrW1qGomOXgB05ss7GLXLpDYXEiFqSqkzg=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.9.1_1599126944642_0.9893379938446147"}, "_hasShrinkwrap": false, "publish_time": 1599126944857, "_cnpm_publish_time": 1599126944857, "_cnpmcore_publish_time": "2021-12-13T14:25:59.204Z"}, "0.8.3": {"name": "image-size", "version": "0.8.3", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "tslint -p . && tsc && eslint specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls", "clean": "rm -rf dist", "build": "tsc", "prepack": "npm run clean && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/node": "12.7.8", "chai": "4.2.0", "coveralls": "3.0.6", "eslint": "6.5.0", "glob": "7.1.4", "mocha": "6.2.1", "nyc": "14.1.1", "sinon": "7.5.0", "tslint": "5.20.0", "typescript": "3.6.3"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "f7668a6b8db3da735c860f35a490ed593526b88e", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.8.3", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"shasum": "f0b568857e034f29baffd37013587f2c0cad8b46", "size": 13397, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.8.3.tgz", "integrity": "sha512-SMtq1AJ+aqHB45c3FsB4ERK0UCiA2d3H1uq8s+8T0Pf8A3W4teyBQyaFaktH6xvZqh+npwlKU7i4fJo0r7TYTg=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.8.3_1569840579065_0.9921393303610448"}, "_hasShrinkwrap": false, "publish_time": 1569840579303, "_cnpm_publish_time": 1569840579303, "_cnpmcore_publish_time": "2021-12-13T14:25:59.616Z"}, "0.8.2": {"name": "image-size", "version": "0.8.2", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "tslint -p . && tsc && eslint specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls", "clean": "rm -rf dist", "build": "tsc", "prepack": "npm run clean && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/node": "12.7.5", "chai": "4.2.0", "coveralls": "3.0.6", "eslint": "6.4.0", "glob": "7.1.4", "mocha": "6.2.0", "nyc": "14.1.1", "sinon": "7.4.2", "tslint": "5.20.0", "typescript": "3.6.3"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "04e461ee474b5cab464b7abf138425cbb0ad3a87", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.8.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "9b5cef7a18a0991beba861b731fb4cfe7a45822d", "size": 12712, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.8.2.tgz", "integrity": "sha512-0AO8bEDtAcC+dScZmCDUvmxIYWlJ+0DQOl1BkTQYrrM3/oQORS03P0gDT7ZoElRozHlfoUxT+L2ErLFmbT5tdA=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.8.2_1569251843770_0.7795008098143899"}, "_hasShrinkwrap": false, "publish_time": 1569251843892, "_cnpm_publish_time": 1569251843892, "_cnpmcore_publish_time": "2021-12-13T14:26:00.002Z"}, "0.8.1": {"name": "image-size", "version": "0.8.1", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "tslint -p . && tsc && eslint specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls", "clean": "rm -rf dist", "build": "tsc", "prepack": "npm run clean && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/node": "12.7.5", "coveralls": "3.0.6", "eslint": "6.4.0", "expect.js": "0.3.1", "glob": "7.1.4", "mocha": "6.2.0", "nyc": "14.1.1", "sinon": "7.4.2", "tslint": "5.20.0", "typescript": "3.6.3"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "54de8487a88d7eef1ae6fd4b569634c5f4b21f6c", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.8.1", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"shasum": "d1dd4167a43196b1cb5fe58f2169340cdc21f862", "size": 11669, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.8.1.tgz", "integrity": "sha512-X2HcWljsuWJPMTb1CGAN2+xuAAjQK5kdSzhqQXZlJBf0MpdXqfIeQCMJKYKS2OEVDwReS2zO5i7MlikWPBTotQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.8.1_1568731790384_0.7029847651337651"}, "_hasShrinkwrap": false, "publish_time": 1568731790600, "_cnpm_publish_time": 1568731790600, "_cnpmcore_publish_time": "2021-12-13T14:26:00.378Z"}, "0.8.0": {"name": "image-size", "version": "0.8.0", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "tslint -p . && tsc && eslint specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls", "clean": "rm -rf dist", "build": "tsc", "prepack": "npm run clean && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/node": "12.7.5", "coveralls": "3.0.6", "eslint": "6.4.0", "expect.js": "0.3.1", "glob": "7.1.4", "mocha": "6.2.0", "nyc": "14.1.1", "sinon": "7.4.2", "tslint": "5.20.0", "typescript": "3.6.3"}, "dependencies": {"queue": "6.0.1"}, "gitHead": "17c84ae6ca62c611ef425063ec3f815fec337d0c", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.8.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"shasum": "878128f26af8afe00a8568f05cf0ea943bac4e74", "size": 11531, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.8.0.tgz", "integrity": "sha512-bgNV4nHicdaShqdJJYCfMnBvOV+nxNkRHMAu06AbiXkqakcWS6+2DqJl7wLiPVZAIAdgGs+4y3Y+UX6DyLtYMQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.8.0_1568720441054_0.9981572546281785"}, "_hasShrinkwrap": false, "publish_time": 1568720441209, "_cnpm_publish_time": 1568720441209, "_cnpmcore_publish_time": "2021-12-13T14:26:00.858Z"}, "0.7.5": {"name": "image-size", "version": "0.7.5", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "3.0.6", "eslint": "6.4.0", "expect.js": "0.3.1", "glob": "7.1.4", "mocha": "6.2.0", "nyc": "14.1.1", "sinon": "7.4.2"}, "gitHead": "02f1f3563ae46ab8277ff784ec4fa9c2e03d058f", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.7.5", "_nodeVersion": "12.9.0", "_npmVersion": "6.10.2", "dist": {"shasum": "269f357cf5797cb44683dfa99790e54c705ead04", "size": 10104, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.7.5.tgz", "integrity": "sha512-Hiyv+mXHfFEP7LzUL/llg9RwFxxY+o9N3JVLIeG5E7iFIFAalxvRU9UZthBdYDEVnzHMgjnKJPPpay5BWf1g9g=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.7.5_1568504984957_0.3434077392565009"}, "_hasShrinkwrap": false, "publish_time": 1568504985081, "_cnpm_publish_time": 1568504985081, "_cnpmcore_publish_time": "2021-12-13T14:26:01.281Z"}, "0.7.4": {"name": "image-size", "version": "0.7.4", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "3.0.3", "eslint": "5.16.0", "expect.js": "0.3.1", "glob": "7.1.3", "mocha": "6.1.4", "nyc": "14.0.0", "sinon": "7.3.2"}, "gitHead": "b2dd91a7a13777c3cb0777bb62d4f9af7c90f65c", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.7.4", "_nodeVersion": "11.10.1", "_npmVersion": "6.7.0", "dist": {"shasum": "092c1e541a93511917bfc957a1fc7add21c72e87", "size": 10037, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.7.4.tgz", "integrity": "sha512-GqPgxs+VkOr12aWwjSkyRzf5atzObWpFtiRuDgxCl2I/SDpZOKZFRD3iIAeAN6/usmn8SeLWRt7a8JRYK0Whbw=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.7.4_1556022142872_0.031256059799466884"}, "_hasShrinkwrap": false, "publish_time": 1556022143102, "_cnpm_publish_time": 1556022143102, "_cnpmcore_publish_time": "2021-12-13T14:26:01.759Z"}, "0.7.3": {"name": "image-size", "version": "0.7.3", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "3.0.3", "eslint": "5.16.0", "expect.js": "0.3.1", "glob": "7.1.3", "mocha": "6.0.2", "nyc": "13.3.0", "sinon": "7.3.1"}, "gitHead": "9b9b9cf7c02fd0ad7b637194f70cf214b6dd249f", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.7.3", "_nodeVersion": "11.10.1", "_npmVersion": "6.7.0", "dist": {"shasum": "dd73b4a06171c7898bff6cef27cd3fe1cc4fe117", "size": 9968, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.7.3.tgz", "integrity": "sha512-CgCZhKUtnwgCV/wh28LGDWdIWhbqq64DAL0c6kjtAtqrKbMr/EZ0yAUXQXdPBsGtQxWQdK6P3itDVfDJ7tjlvA=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.7.3_1554206063321_0.5285987218623995"}, "_hasShrinkwrap": false, "publish_time": 1554206063496, "_cnpm_publish_time": 1554206063496, "_cnpmcore_publish_time": "2021-12-13T14:26:02.220Z"}, "0.7.2": {"name": "image-size", "version": "0.7.2", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "3.0.2", "eslint": "5.13.0", "expect.js": "0.3.1", "glob": "7.1.3", "mocha": "5.2.0", "nyc": "13.3.0", "sinon": "7.2.3"}, "gitHead": "50b4fac0a4e131faba8dc9ebc57db63aebd16214", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.7.2", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "5014c2c43003020d265801867b8bcf97c4080cf1", "size": 9966, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.7.2.tgz", "integrity": "sha512-CBmVIFHyDyiWi1U24eNHl8SH0Iir2IgmEv1RwdRVZxWsEbSCvV5b/eXaYP8epOFv2dbw5uNBOrn1Nc5P5KvsUA=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.7.2_1550243526547_0.9822578547143948"}, "_hasShrinkwrap": false, "publish_time": 1550243526715, "_cnpm_publish_time": 1550243526715, "_cnpmcore_publish_time": "2021-12-13T14:26:02.695Z"}, "0.7.1": {"name": "image-size", "version": "0.7.1", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "3.0.2", "eslint": "5.12.0", "expect.js": "0.3.1", "glob": "7.1.3", "mocha": "5.2.0", "nyc": "13.1.0", "sinon": "7.2.2"}, "gitHead": "5c444a887cfc1079ad5d614ff0bfac3d1fc014b0", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.7.1", "_npmVersion": "6.5.0-next.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "961f7dfb1fc6bc7b8cb25e3986e1dc206b195eab", "size": 9806, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.7.1.tgz", "integrity": "sha512-Q9hk+wP7+CjudriUaGhP9BizAXBe1O7STPbGU/FesYSjqcupHTX70iAZ2vZoQ4mKWB9qlcEIv6h5sKUrRehlNQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.7.1_1546949012001_0.1072380319555315"}, "_hasShrinkwrap": false, "publish_time": 1546949012145, "_cnpm_publish_time": 1546949012145, "_cnpmcore_publish_time": "2021-12-13T14:26:03.154Z"}, "0.7.0": {"name": "image-size", "version": "0.7.0", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=4.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "3.0.2", "eslint": "5.12.0", "expect.js": "0.3.1", "glob": "7.1.3", "mocha": "5.2.0", "nyc": "13.1.0", "sinon": "7.2.2"}, "gitHead": "22005333ba007058250acb3d6003b8f038363422", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.7.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "1ac3de4eaaee0386f19ba6b19d479e02fc060de7", "size": 9097, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.7.0.tgz", "integrity": "sha512-oWhJUMBxKQhmdSUPw+bcU+JC9pUMBbLnDPIZCFxZWGajV2HCRA5n+fHCCT3qDPGbGVEwP2cYTP2efsSk8j0YKA=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.7.0_1546945276858_0.9008845371436056"}, "_hasShrinkwrap": false, "publish_time": 1546945277100, "_cnpm_publish_time": 1546945277100, "_cnpmcore_publish_time": "2021-12-13T14:26:03.640Z"}, "0.6.3": {"name": "image-size", "version": "0.6.3", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=4.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "^3.0.1", "eslint": "^4.19.1", "expect.js": "^0.3.1", "glob": "^7.1.1", "mocha": "^5.2.0", "nyc": "^11.9.0", "sinon": "^5.1.1"}, "gitHead": "2290e6a8ebb58d364c7c96799fbfa211d8ebbd3d", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.6.3", "_npmVersion": "6.0.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "e7e5c65bb534bd7cdcedd6cb5166272a85f75fb2", "size": 9082, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.6.3.tgz", "integrity": "sha512-47xSUiQioGaB96nqtp5/q55m0aBQSQdyIloMOc/x+QVTDZLNmXE892IIDrJ0hM1A5vcNUDD5tDffkSP5lCaIIA=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_0.6.3_1528886094460_0.35941359355853897"}, "_hasShrinkwrap": false, "publish_time": 1528886094588, "_cnpm_publish_time": 1528886094588, "_cnpmcore_publish_time": "2021-12-13T14:26:04.153Z"}, "0.6.2": {"name": "image-size", "version": "0.6.2", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=4.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"coveralls": "^3.0.0", "eslint": "^4.13.1", "expect.js": "^0.3.1", "glob": "^7.1.1", "mocha": "^4.0.1", "nyc": "^11.2.1", "sinon": "^4.0.2"}, "gitHead": "bbf1e36505ba2d13cce175f64fba2492c84da9fd", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.6.2", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "8ee316d4298b028b965091b673d5f1537adee5b4", "size": 8336, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.6.2.tgz", "integrity": "sha512-pH3vDzpczdsKHdZ9xxR3O46unSjisgVx0IImay7Zz2EdhRVbCkj+nthx9OuuWEhakx9FAO+fNVGrF0rZ2oMOvw=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size-0.6.2.tgz_1513424120851_0.08952897996641695"}, "directories": {}, "publish_time": 1513424121737, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513424121737, "_cnpmcore_publish_time": "2021-12-13T14:26:04.743Z"}, "0.6.1": {"name": "image-size", "version": "0.6.1", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=4.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^1.1.0-alpha.1", "jshint": "^2.9.4", "mocha": "^3.4.1", "sinon": "^2.2.0"}, "gitHead": "4c527ba608d742fbb29f6d9b3c77b831b069cbb2", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.6.1", "_npmVersion": "5.2.0", "_nodeVersion": "8.1.3", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "98122a562d59dcc097ef1b2c8191866eb8f5d663", "size": 8116, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.6.1.tgz", "integrity": "sha512-lHMlI2MykfeHAQdtydQh4fTcBQVf4zLTA91w1euBe9rbmAfJ/iyzMh8H3KD9u1RldlHaMS3tmMV5TEe9BkmW9g=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size-0.6.1.tgz_1500468744981_0.947695990325883"}, "directories": {}, "publish_time": 1500468745880, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500468745880, "_cnpmcore_publish_time": "2021-12-13T14:26:05.350Z"}, "0.6.0": {"name": "image-size", "version": "0.6.0", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=4.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "ico", "cur"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^1.1.0-alpha.1", "jshint": "^2.9.4", "mocha": "^3.4.1", "sinon": "^2.2.0"}, "gitHead": "b1fb2931ba70aa9e28f913178d3dc9dad96f0da7", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.6.0", "_shasum": "abd59fe0877f29a248ef2dd90f783bf57f4c61a7", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "abd59fe0877f29a248ef2dd90f783bf57f4c61a7", "size": 7993, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.6.0.tgz", "integrity": "sha512-vH5MAq4uo8TVcnLjdVjdnvxdYZ5IExwSMb6s6aaJh2RCxSSfRBufWHeYxolxUOXqcegf4J5DQsaCpeLnduQI1A=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size-0.6.0.tgz_1498477258221_0.03308123117312789"}, "directories": {}, "publish_time": 1498477259233, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498477259233, "_cnpmcore_publish_time": "2021-12-13T14:26:05.926Z"}, "0.5.5": {"name": "image-size", "version": "0.5.5", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^1.1.0-alpha.1", "jshint": "^2.9.4", "mocha": "^3.4.1", "sinon": "^2.2.0"}, "gitHead": "77ff58653f18c2ea3c9e176a6dee663beacb4889", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.5.5", "_shasum": "09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c", "size": 6977, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz", "integrity": "sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size-0.5.5.tgz_1497255554208_0.9632799690589309"}, "directories": {}, "publish_time": 1497255555148, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497255555148, "_cnpmcore_publish_time": "2021-12-13T14:26:06.425Z"}, "0.5.4": {"name": "image-size", "version": "0.5.4", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^1.1.0-alpha.1", "jshint": "^2.9.4", "mocha": "^3.4.1", "sinon": "^2.2.0"}, "gitHead": "ac7f47b31daee61e256ac7b41a01443d9165ba8c", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.5.4", "_shasum": "94e07beec0659386f1aefb84b2222e88405485cd", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "94e07beec0659386f1aefb84b2222e88405485cd", "size": 6963, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.5.4.tgz", "integrity": "sha512-pZ6OFlgYPCzEwnTboIif0rzImqCGRqdyEXHkjnxXi0W7E0iQlL9xDF+RgIpLyIDWPxazxxjZ0dA++s+h/YchdQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/image-size-0.5.4.tgz_1494844597753_0.4035931939724833"}, "directories": {}, "publish_time": 1494844598574, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494844598574, "_cnpmcore_publish_time": "2021-12-13T14:26:07.058Z"}, "0.5.3": {"name": "image-size", "version": "0.5.3", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "sinon": "^1.17.2"}, "gitHead": "424f459a3d81df322cb96dd0bdc26ff4c0e2d584", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.5.3", "_shasum": "5cbe9fafc8436386ceb7e9e3a9d90c5b71b70ad9", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "5cbe9fafc8436386ceb7e9e3a9d90c5b71b70ad9", "size": 6841, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.5.3.tgz", "integrity": "sha512-efiuXV9RK74NgSZhr8JmyXUiTzRRoi0nv7B8H/MajD4IbdEGUfoc68cSEutlo+jkdiae3bMhbZ6RD/sQ9JmHtA=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/image-size-0.5.3.tgz_1494512587495_0.8084348989650607"}, "directories": {}, "publish_time": 1494512589468, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494512589468, "_cnpmcore_publish_time": "2021-12-13T14:26:07.695Z"}, "0.5.2": {"name": "image-size", "version": "0.5.2", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "sinon": "^1.17.2"}, "gitHead": "75ce99c16d4aca5a6da4464c71df6e9b24bb9d68", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.5.2", "_shasum": "8e97cd0eb69ce04a2831a11a6095983100f8bda4", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "8e97cd0eb69ce04a2831a11a6095983100f8bda4", "size": 6821, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.5.2.tgz", "integrity": "sha512-b6tPB3u8wLUF5DXzP7b4t6GIvDVtWgn5QLyb+tCp8U5WyncEpjXQ2zI5A1Y07iPOgd/hgYAPubu62qtYZfcqkg=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/image-size-0.5.2.tgz_1494327976057_0.9268872726242989"}, "directories": {}, "publish_time": 1494327978090, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494327978090, "_cnpmcore_publish_time": "2021-12-13T14:26:08.243Z"}, "0.5.1": {"name": "image-size", "version": "0.5.1", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "sinon": "^1.17.2"}, "gitHead": "05fd80685ea70b2b7a27e76b16bc614f0949b4f4", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.5.1", "_shasum": "28eea8548a4b1443480ddddc1e083ae54652439f", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "dist": {"shasum": "28eea8548a4b1443480ddddc1e083ae54652439f", "size": 6633, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.5.1.tgz", "integrity": "sha512-mFIGpGG9OtwbyPivj+E97lKOC9IICkXa6UZZ+2eD/wDineTOPrarpUnYiMdpOv5cGzSxkuFMPZG8ANO2CpXCaw=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/image-size-0.5.1.tgz_1482704788414_0.32357512530870736"}, "directories": {}, "publish_time": 1482704789032, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482704789032, "_cnpmcore_publish_time": "2021-12-13T14:26:08.961Z"}, "0.5.0": {"name": "image-size", "version": "0.5.0", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^6.0.1", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.4", "sinon": "^1.17.2"}, "gitHead": "1420c0bc4b6a316d48edc0b3bdc0e4da6d7ddc63", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.5.0", "_shasum": "be7aed1c37b5ac3d9ba1d66a24b4c47ff8397651", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.8.0", "_npmUser": {"name": "zeke", "email": "<EMAIL>"}, "dist": {"shasum": "be7aed1c37b5ac3d9ba1d66a24b4c47ff8397651", "size": 6504, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.5.0.tgz", "integrity": "sha512-9UROgOopAMsApTnhO54eEaJIDc1909CzvPOcLy1EC+2Jo2/cC6qt6768IbOeJj7BL4Y6ZKrltQ6bcok3BiZ6cQ=="}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/image-size-0.5.0.tgz_1458683483601_0.9503235800657421"}, "directories": {}, "publish_time": 1458683484205, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458683484205, "_cnpmcore_publish_time": "2021-12-13T14:26:09.604Z"}, "0.4.0": {"name": "image-size", "version": "0.4.0", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "j<PERSON>t", "test": "mocha specs", "coverage": "istanbul cover _mocha specs"}, "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^6.0.1", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.4", "sinon": "^1.17.2"}, "gitHead": "0d6723b3345ae81d41f2c27e50c89cca522c377d", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@0.4.0", "_shasum": "d4b4e1f61952e4cbc1cea9a6b0c915fecb707510", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "shinnn", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "dist": {"shasum": "d4b4e1f61952e4cbc1cea9a6b0c915fecb707510", "size": 6480, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.4.0.tgz", "integrity": "sha512-TO4QD8fhVIGb0Yo5O7cCD3cizFYOMQ/tzQMsU63tvvuwl/Cry23FSuiia0as+6AmitO5+RAa/74Z/3SdpWWqSg=="}, "directories": {}, "publish_time": 1448290856392, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448290856392, "_cnpmcore_publish_time": "2021-12-13T14:26:10.311Z"}, "0.3.5": {"name": "image-size", "version": "0.3.5", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.10.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.4", "expect.js": "~0.3.1", "glob": "~4.0.6", "jshint": "~2.5.6", "mocha": "~1.21.5", "sinon": "~1.10.3"}, "gitHead": "ccef69913f1cac2cb88c5f6309d70a37cf097658", "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.3.5", "_shasum": "83240eab2fb5b00b04aab8c74b0471e9cba7ad8c", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "dist": {"shasum": "83240eab2fb5b00b04aab8c74b0471e9cba7ad8c", "size": 7268, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.3.5.tgz", "integrity": "sha512-naH1FVkLBoWjY1ljAzPbITSiZC4mk4+nNonkMncF1kulHn9wLxNOMJ54LG9M86oqbSRNW60IARHR/E/lZBBC9g=="}, "directories": {}, "publish_time": 1413494859057, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413494859057, "_cnpmcore_publish_time": "2021-12-13T14:26:11.052Z"}, "0.3.3": {"name": "image-size", "version": "0.3.3", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "gitHead": "43fc9add2e9085d329877038d07a03547735270d", "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.3.3", "_shasum": "7ac36a8cf501a40cd3ddb22859424960d9b11d89", "_from": ".", "_npmVersion": "2.0.0-beta.3", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "dist": {"shasum": "7ac36a8cf501a40cd3ddb22859424960d9b11d89", "size": 6714, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.3.3.tgz", "integrity": "sha512-lrZOnihSqt4gih8giauWh+0sJJqqA4P0MJyKyyUtc9xv4LxtEoUGVeq3BXWwsjCersc7Qgo/UYuKKUVlsGNKvg=="}, "directories": {}, "publish_time": 1410274844084, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410274844084, "_cnpmcore_publish_time": "2021-12-13T14:26:11.762Z"}, "0.3.2": {"name": "image-size", "version": "0.3.2", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.3.2", "_shasum": "cab539016c48e325a8d33c08e1a5cc673d725a86", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "dist": {"shasum": "cab539016c48e325a8d33c08e1a5cc673d725a86", "size": 6707, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.3.2.tgz", "integrity": "sha512-TsH9uYWkOMOBFhjJtGlRjx1trSz79gEdkt7+pKpAKQTtXR06GnXkOSNeq30BFZP2kXsukLU86M+6aTxwcBfgIg=="}, "directories": {}, "publish_time": 1405432297505, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405432297505, "_cnpmcore_publish_time": "2021-12-13T14:26:12.454Z"}, "0.3.1": {"name": "image-size", "version": "0.3.1", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "gitHead": "7441fdddc49f281f3b430a9db912293be02de095", "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.3.1", "_shasum": "8ee649889ed9a803e7372d72be3ab87debd86902", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "dist": {"shasum": "8ee649889ed9a803e7372d72be3ab87debd86902", "size": 6709, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.3.1.tgz", "integrity": "sha512-+VTA2+6ynLJtJdnhVvZflk/DMfN4nvmrsuLSBC9yWbtIvqkmrZQc3c8lzXXXhccJgubIVc91hK/Rjc3MamNO/g=="}, "directories": {}, "publish_time": 1405085515975, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405085515975, "_cnpmcore_publish_time": "2021-12-13T14:26:13.095Z"}, "0.3.0": {"name": "image-size", "version": "0.3.0", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.3.0", "_shasum": "716dfca14106f195d38ddfe0ec94d6eaeb9b0396", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "dist": {"shasum": "716dfca14106f195d38ddfe0ec94d6eaeb9b0396", "size": 6552, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.3.0.tgz", "integrity": "sha512-4GWvzVjYDa4yHEshS7342YbE76B0ea9fvAWjkGFtDNwMjsSAGj+9WlLFOpDSN6+RO3Qbllx9PbEipZRU/TahtA=="}, "directories": {}, "publish_time": 1402323147971, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402323147971, "_cnpmcore_publish_time": "2021-12-13T14:26:13.829Z"}, "0.2.5": {"name": "image-size", "version": "0.2.5", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.2.5", "dist": {"shasum": "7cea13f49c191319722fadd961b9f30363eec88f", "size": 6556, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.2.5.tgz", "integrity": "sha512-mHHboA4XPJoPLTYocpZF9q+2sDHMpMFoxWStAvtvD0iBNwabftMcd/kNLG6n4staesDMVxHSG8h4R5Lhubiw8w=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1400102365494, "_hasShrinkwrap": false, "_cnpm_publish_time": 1400102365494, "_cnpmcore_publish_time": "2021-12-13T14:26:14.694Z"}, "0.2.4": {"name": "image-size", "version": "0.2.4", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.2.4", "dist": {"shasum": "6018b8156d6b047a61d01e3eac1081a6cf21e144", "size": 249520, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.2.4.tgz", "integrity": "sha512-//33aVqBDmBK/28kHo2w7aMtvlcEM8+onhHsvJDyhtlZNc6pnRKFJafAtMVKttx7YH3ebRqWI6cJ8wOMjg2Pug=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399627990914, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399627990914, "_cnpmcore_publish_time": "2021-12-13T14:26:15.796Z"}, "0.2.3": {"name": "image-size", "version": "0.2.3", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.2.3", "dist": {"shasum": "d5c83746f892c4dcb12b5d30a9fd092c6e3e74bd", "size": 6931, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.2.3.tgz", "integrity": "sha512-DCU9O9039gAxO8zYp86YPuTHaCj91RwaSA3SJW/DuSbjh9lvUktW9si+Cj1JgtA853IpqgQkq4PswlE7DTHW7g=="}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395854491466, "_hasShrinkwrap": false, "_cnpm_publish_time": 1395854491466, "_cnpmcore_publish_time": "2021-12-13T14:26:16.562Z"}, "0.2.2": {"name": "image-size", "version": "0.2.2", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.2.2", "dist": {"shasum": "479f49d983bc61e16c39c9083510267c2b34038b", "size": 6896, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.2.2.tgz", "integrity": "sha512-zwje1UpfFuKYcuwKELkGTExXRi/1pUOcthO8MlnSpW+0e9wb8PgQnbxdoanazA/ZopgM6Ff6y4Oe82/bTbXQTw=="}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395264497178, "_hasShrinkwrap": false, "_cnpm_publish_time": 1395264497178, "_cnpmcore_publish_time": "2021-12-13T14:26:17.361Z"}, "0.2.1": {"name": "image-size", "version": "0.2.1", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "_id": "image-size@0.2.1", "dist": {"shasum": "06aa3ada70b5d30e5f001ddaf03b72d42111cad8", "size": 7045, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.2.1.tgz", "integrity": "sha512-35jbrJNe+buXuW/WbZ0aMvn2VyL3X6s1urYoHXzfUo15jlWk1M5QTiPdMI5pEUyuWpghHur9w71PiOclXZx8zQ=="}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390432297331, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390432297331, "_cnpmcore_publish_time": "2021-12-13T14:26:18.104Z"}, "0.1.20": {"name": "image-size", "version": "0.1.20", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "_id": "image-size@0.1.20", "dist": {"shasum": "4e0c50e394a78640c706907089022becbeb2e2cf", "size": 7045, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.20.tgz", "integrity": "sha512-llF87e/0mDmNYjs82LVwyoJYYAYGZj/2SuRQPnn1QF9NLWL5mXKb2PKpKsfAzXn0nTkW4I+g8MEE2h7EOdBY1g=="}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390432266362, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390432266362, "_cnpmcore_publish_time": "2021-12-13T14:26:18.864Z"}, "0.1.17": {"name": "image-size", "version": "0.1.17", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "_id": "image-size@0.1.17", "dist": {"shasum": "e34b79f33abc72d9ff6307ea3afc9ca04ba8daa9", "size": 6641, "noattachment": false, "tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.17.tgz", "integrity": "sha512-+mroxDr0PnorwtVz9EM6gprQxT40/NbWVRp81JVyr17khJRLq1az9FYAttdhs9V6oZ4VrPydHykJgRXOESuYQg=="}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390430819022, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390430819022, "_cnpmcore_publish_time": "2021-12-13T14:26:19.556Z"}, "0.1.16": {"name": "image-size", "version": "0.1.16", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "readmeFilename": "Readme.md", "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.1.16", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.16.tgz", "shasum": "3a18c7028fe2241a1dbe13d2cfc67b4a077729bc", "size": 6623, "noattachment": false, "integrity": "sha512-HlfSeXyUgGKsV4ZrwDHOyQFVWIon/sWc9kJ2P2St+gfwsSrOAHwkRUS/ULhfcGLBCyYOcSdJucp1PikY+Qb2ag=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386088475855, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386088475855, "_cnpmcore_publish_time": "2021-12-13T14:26:20.387Z"}, "0.0.16": {"name": "image-size", "version": "0.0.16", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"escomplex-js": "~0.1.0", "expect.js": "~0.2.0", "glob": "~3.2.7", "jshint": "~2.3.0", "mocha": "~1.14.0", "sinon": "~1.7.3"}, "readmeFilename": "Readme.md", "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.0.16", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.0.16.tgz", "shasum": "a156f99ca6603665937cf9ff81f85dc2cc7587d1", "size": 6622, "noattachment": false, "integrity": "sha512-Say/1b2IekIE0UJOPSLX3UiD73kan+070Fw9OPA1Ur9XJ+0I5/BYs8tk5GQpqPfR2dr8210E8hVu0oMgOldM3A=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384947863748, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384947863748, "_cnpmcore_publish_time": "2021-12-13T14:26:21.157Z"}, "0.1.15": {"name": "image-size", "version": "0.1.15", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.13.0", "expect.js": "~0.2.0", "jshint": "~2.1.11", "glob": "~3.2.6"}, "readmeFilename": "Readme.md", "homepage": "https://github.com/netroy/image-size", "_id": "image-size@0.1.15", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.15.tgz", "shasum": "e56af962ba10f8e52f4af5f16afd7b7c1e906ffb", "size": 6527, "noattachment": false, "integrity": "sha512-V/mD0zlaSGkLNiReKpMX8bBwpLMiVnWv8Oyqz+68BzbI0Dlwx1F3vYvtNdOor7iP52B7lmBNAf3RmKGbdVsHAA=="}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384729662764, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384729662764, "_cnpmcore_publish_time": "2021-12-13T14:26:22.070Z"}, "0.1.10": {"name": "image-size", "version": "0.1.10", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.13.0", "expect.js": "~0.2.0", "jshint": "~2.1.11", "glob": "~3.2.6"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.10", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.10.tgz", "shasum": "89e61aab60c21dbf41d9227645cb8f6f50bf41df", "size": 5080, "noattachment": false, "integrity": "sha512-dVZI2oJCpMdZQlnV12ghtJbHj2eObNodBg9kj/xmVq2vwPiKwP8/HLqh7zLJxPw7FQIIZeYOP4agSiHU9ZzsDA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381476205311, "_hasShrinkwrap": false, "_cnpm_publish_time": 1381476205311, "_cnpmcore_publish_time": "2021-12-13T14:26:22.940Z"}, "0.1.7": {"name": "image-size", "version": "0.1.7", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.13.0", "expect.js": "~0.2.0", "jshint": "~2.1.11", "glob": "~3.2.6"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.7", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.7.tgz", "shasum": "83c4fa746b67d788e42558ce01bd5cd695889f40", "size": 25350, "noattachment": false, "integrity": "sha512-xtaslOSefCh33KUqjj7f6wCz4puVmvezxcyN20NZ3yZxkmAVKSCHo+MdoPeJti8s8o/gZzfiqNBr+KVr7ekh9Q=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381473296411, "_hasShrinkwrap": false, "_cnpm_publish_time": 1381473296411, "_cnpmcore_publish_time": "2021-12-13T14:26:23.863Z"}, "0.1.6": {"name": "image-size", "version": "0.1.6", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.13.0", "expect.js": "~0.2.0", "jshint": "~2.1.11", "glob": "~3.2.6"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.6", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.6.tgz", "shasum": "eccfc8967959bbf13576d1ee9d4809783859d7db", "size": 25155, "noattachment": false, "integrity": "sha512-74XBrUqTXy+zAfDBuUy7QPwsgMD7fnWXeV22RsYO4Lxt9Ayiu9pxa6uk+thD5tl45/GGtimD+/RWVJA9KzvDqA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381468196602, "_hasShrinkwrap": false, "_cnpm_publish_time": 1381468196602, "_cnpmcore_publish_time": "2021-12-13T14:26:24.916Z"}, "0.1.5": {"name": "image-size", "version": "0.1.5", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0", "jshint": "~2.1.4"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.5", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.5.tgz", "shasum": "24d7654b692502dd9ba84da7bae1630873dab140", "size": 3261, "noattachment": false, "integrity": "sha512-muwTgElBfvsuIaDwjfPP4Du3VXg1/3+CFCFIeED24YTWfTRzWuj5ZsiOdklkQ9bSAzwuYfVfnLF8DPE8dqOjkg=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373024938390, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373024938390, "_cnpmcore_publish_time": "2021-12-13T14:26:25.920Z"}, "0.1.4": {"name": "image-size", "version": "0.1.4", "description": "get dimensions of any image file", "main": "lib/index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0", "jshint": "~2.1.4"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.4", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.4.tgz", "shasum": "e253cd0d3fc992fde945d4806ff40a4653137c46", "size": 3258, "noattachment": false, "integrity": "sha512-hBN9m+2WDJTHgfGUpdi2qW/5y/3jFFtP6SmDontnUipyC/Iodpb8cKzyHQDjjCzYOI/IVc3XtV+D/3hlPrREdw=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373023021871, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373023021871, "_cnpmcore_publish_time": "2021-12-13T14:26:26.822Z"}, "0.1.3": {"name": "image-size", "version": "0.1.3", "description": "get dimensions of any image file", "main": "index.js", "engines": {"node": ">=0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0", "jshint": "~2.1.4"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.3", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.3.tgz", "shasum": "6fab197ce26881d59bd1e56e70c29ed6d53e0707", "size": 3193, "noattachment": false, "integrity": "sha512-VIXm8vX5A17c8hMuuydBGun6BZKC6LSIy1jNaB4jh4HYOMjzVo3hAAIpXydqzQCHR36Fwr0aR2hVywFUm8LyZQ=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372975654579, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372975654579, "_cnpmcore_publish_time": "2021-12-13T14:26:27.724Z"}, "0.1.2": {"name": "image-size", "version": "0.1.2", "description": "get dimensions of any image file", "main": "index.js", "engines": {"node": "~0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0", "jshint": "~2.1.4"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.2", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.2.tgz", "shasum": "53b2138ae235b76033885277c2b0ab44c7c8e990", "size": 3170, "noattachment": false, "integrity": "sha512-K09LCp+YKwXPOOKMxKWDPwhvpTmZ76zbPs/bEj3Odp8QfnLEne92XmFkuHB4e2A4Uoos6BCS9PGhSgPNp0vYag=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372937557516, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372937557516, "_cnpmcore_publish_time": "2021-12-13T14:26:28.819Z"}, "0.1.1": {"name": "image-size", "version": "0.1.1", "description": "get dimensions of any image file", "main": "index.js", "engines": {"node": "~0.8.0"}, "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0", "jshint": "~2.1.4"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.1", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.1.tgz", "shasum": "23e1e78e24c20fda823aea5ae248e8c6290157c1", "size": 2793, "noattachment": false, "integrity": "sha512-oMJ/fq276htWHP1V8+6M4MqhjV5aLxRb2fFC6tVxNbiUV4o943RR2aYMnrsRf2Tir6hglZf2fY0D2Rcq17CCxQ=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372894046355, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372894046355, "_cnpmcore_publish_time": "2021-12-13T14:26:29.938Z"}, "0.1.0": {"name": "image-size", "version": "0.1.0", "description": "get dimensions of any image file", "main": "index.js", "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0", "jshint": "~2.1.4"}, "readmeFilename": "Readme.md", "_id": "image-size@0.1.0", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.1.0.tgz", "shasum": "ca99212a134ed9f45abf826d77e88ee2d88c516a", "size": 2811, "noattachment": false, "integrity": "sha512-bzRnPjjLONoTM1p2q2Gj4iblgoyB5sbkWVin7MW+tzSNs5fZITKbQezIXH2qRDlPA+1qjiQ0T3ptzwIpA9prug=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372891175454, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372891175454, "_cnpmcore_publish_time": "2021-12-13T14:26:30.922Z"}, "0.0.5": {"name": "image-size", "version": "0.0.5", "description": "get dimensions of any image file", "main": "index.js", "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "make"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0", "expect.js": "~0.2.0"}, "readmeFilename": "Readme.md", "_id": "image-size@0.0.5", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.0.5.tgz", "shasum": "3d6b8683aca0477990181825f726cb4ead148430", "size": 2269, "noattachment": false, "integrity": "sha512-GNvzf7q5vpDLC+SYI0fq3eS2r1+h9q5vtlNDVFQcm6TOTbsKaWoMLseJzBWWTG+M+FPgHysNpg7vcqnNBhdD4g=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372886292940, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372886292940, "_cnpmcore_publish_time": "2021-12-13T14:26:31.840Z"}, "0.0.3": {"name": "image-size", "version": "0.0.3", "description": "get dimensions of any image file", "main": "index.js", "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "node_modules/.bin/mocha --ui bdd --reporter spec specs/*.spec.js"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "Readme.md", "_id": "image-size@0.0.3", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.0.3.tgz", "shasum": "cda31d03fb993c9144b8033de6b4ee548bcbda0e", "size": 369416, "noattachment": false, "integrity": "sha512-bCKWaooL2OA/giyqxhrk32yGw0gpa11grgSaDzuiz3eFVhHpqRT8aUu3EsMmnBF7ssdh0p8d80aCtFqfoBhBpA=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372729936830, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372729936830, "_cnpmcore_publish_time": "2021-12-13T14:26:32.921Z"}, "0.0.2": {"name": "image-size", "version": "0.0.2", "description": "get dimensions of any image file", "main": "index.js", "bin": {"image-size": "./bin/image-size"}, "scripts": {"test": "node_modules/.bin/mocha --ui bdd --reporter spec specs/*.spec.js"}, "repository": {"type": "git", "url": "**************:netroy/image-size.git"}, "keywords": ["image", "dimensions"], "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "bugs": {"url": "https://github.com/netroy/image-size/issues"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "Readme.md", "_id": "image-size@0.0.2", "dist": {"tarball": "https://registry.npmmirror.com/image-size/-/image-size-0.0.2.tgz", "shasum": "1fff36087de2af7faaef0a0a169bf641cb1eecb7", "size": 369025, "noattachment": false, "integrity": "sha512-2RfilnRxx5OmS/UAVYJKxbM8Ds9vMwGRIpCe0ln2aDRHI4vUGzCXjNwfvkzl+546VpcXgojz9SCfmLQgkzKEww=="}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372729130596, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372729130596, "_cnpmcore_publish_time": "2021-12-13T14:26:34.071Z"}, "1.0.1": {"name": "image-size", "version": "1.0.1", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.0", "@types/glob": "7.2.0", "@types/mocha": "9.0.0", "@types/node": "17.0.8", "@types/sinon": "10.0.6", "@typescript-eslint/eslint-plugin": "5.9.1", "@typescript-eslint/parser": "5.9.1", "chai": "4.3.4", "eslint": "8.6.0", "glob": "7.2.0", "mocha": "9.1.3", "nyc": "15.1.0", "sinon": "12.0.1", "ts-node": "10.4.0", "typedoc": "0.22.10", "typescript": "4.5.4"}, "dependencies": {"queue": "6.0.2"}, "packageManager": "yarn@3.1.1", "gitHead": "834cf326ef89929a2109672ccb6e8066611da4e2", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@1.0.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-VAwkvNSNGClRw9mDHhc5Efax8PLlsOGcUTh0T/LIriC8vPA3U5PdqXWqkz406MoYHMKW8Uf9gWr05T/rYB44kQ==", "shasum": "86d6cfc2b1d19eab5d2b368d4b9194d9e48541c5", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.0.1.tgz", "fileCount": 46, "unpackedSize": 45406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3IUUCRA9TVsSAnZWagAAaKgP/2OkTTytrGCKovWawhE1\nXqOn6Niwsj+t8/L901bklDUZ+epN/iKIVFiR8dv5aMGHmufKBFicOtrxuma1\ntEGU6QWKfcWDJuX2LDcipNFdaRbn+IsNMpfo7K7BEdo7+qsT9Olff+OsJUsS\nSgsZmGsF/0W/t/BGuxF95B+GN73fTzsNj8eoDjeClmfUnL4GtjF7YGWGONgf\nglD5KDxQuL2RP96RipZEYi+FQxsbmO4ZjYXiQP2rRLyIRwfenS0H8ceN776U\nv6F/5sA8IgDKR9dxG7R+b816Q4sWZHeLpAU0XOsCK65xA8pevcaRfi+io7Zq\noHotpAOWhikBATzo6u14qiOTDKuYknrmi2D32Xkhr9T6rq+lLoyU8fhFKPEy\nSuujFm6+XwKwpisxbvIKCisNglUnSFM0tNVYccG/F3LvTnaIqretAQEMb2wr\nK/PpA1EAwnM8fLyv7L+/nvTcEbfPKlcp/+Oog6ZO0hILb6eZLa4DEXQ7hGNl\nflojhKz+QXBqvA95GHMUKiCEM4op/c+BByco96QTc7BObc3RIcKX7w7u6Nw+\nIfJ7Zub3I3XLde7bufvkaKarHlFU75yzLDW1kIQJvaV7zsOPM+p4SeKkMwMh\n9LKCo/V2e2ttvEuCPiR6GrnvgNnJLFQwK+txFDyekdowh08ZqqB+ed/vuwWt\nMr4R\r\n=69rj\r\n-----END PGP SIGNATURE-----\r\n", "size": 13932}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_1.0.1_1641841940805_0.3981089740590298"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-10T19:19:30.074Z"}, "1.0.2": {"name": "image-size", "version": "1.0.2", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tga", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.1", "@types/glob": "7.2.0", "@types/mocha": "9.1.1", "@types/node": "18.0.3", "@types/sinon": "10.0.12", "@typescript-eslint/eslint-plugin": "5.30.6", "@typescript-eslint/parser": "5.30.6", "chai": "4.3.6", "eslint": "8.19.0", "glob": "8.0.3", "mocha": "10.0.0", "nyc": "15.1.0", "sinon": "14.0.0", "ts-node": "10.8.2", "typedoc": "0.23.7", "typescript": "4.7.4"}, "dependencies": {"queue": "6.0.2"}, "packageManager": "yarn@3.2.0", "gitHead": "a71793d1a8d86c92b0ed95c11fe26e06a82003cd", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@1.0.2", "_nodeVersion": "16.15.1", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-xfOoWjceHntRb3qFCrh5ZFORYH8XCdYpASltMhZ/Q0KZiOwjdE/Yl2QCiWdwD+lygV5bMCvauzgu5PxBX/Yerg==", "shasum": "d778b6d0ab75b2737c1556dd631652eb963bc486", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.0.2.tgz", "fileCount": 48, "unpackedSize": 45475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmcxNoyo8wCz7e5js9ZfnpuDqo84vDw1+mXaYIviRBqQIgNXj7coTrUmc3j0l/PPTw9ypA5WJvVlS0HoZSdWHfXgM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizYf7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQAA/6A00LFJ3H2LQc/rLErxSOGIZsfghtpuQ5LrQBxvECFYQpaqNq\r\nwD1RZSVa7CAVuXcSMAOsKOid3hHqBglutl9r2p44RhhiOYuLRA8NhTv93Mi2\r\nfiHNMkti4SGhDVdqcF8VKpu8jrm5nkMikOCZY6zPBpjOWTs95vXW9LFJh1R+\r\njf/lefbzMmekekIXPXmlux92joKk+2eHEkpJC8+kRYMklZD7rvpE00uQNRMY\r\n16IcC+L/yGiiZ6XQw1cCAMBdg2K877KiuFMr7r61VpiBA3Bhx7i5WbxQVwVC\r\n30wMTy8p3z78dFaeSOpLEKJ54KT6Rz/NIjhrnNYDocinwivzEnqq0HhXn8Ai\r\nFlfZmTMsqiHNiTbuyhhS9BCTKHGmsZxzabIOBZt0coT7R8NOlPJ2LCwcatuU\r\noyjOrh1rJN62N0uiTXhm5/nK3k+Qqa2xP72wx2/fLi3rV0ylPwuUi3xwGvdj\r\nqufP+gF0C6jr3LTpyUV6YQPoYrhKy9KvzUJvU4ukcHCToXIZuq2T8jeE5KQ5\r\nic1vGGbNxzBNwfLvBd9/BRf8GNTioqMWUb+bxdo3tN2lfyR7r6I0HE0yn63W\r\nyrQDkqH/GCiQfhc62LY6fvwaG+wGBBMJOFDLA+ppd+4iEtFGx7mTElWKrXaL\r\nuI7AWtT+4tDpt0bz6ac74J1ZF7RvbBIF7pY=\r\n=jt4U\r\n-----END PGP SIGNATURE-----\r\n", "size": 13577}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_1.0.2_1657636859229_0.3705165448532588"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-12T15:47:09.469Z"}, "2.0.0-alpha.1": {"name": "image-size", "version": "2.0.0-alpha.1", "description": "get dimensions of any image file", "type": "module", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/dts/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "engines": {"node": ">=16.18.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint --ext .ts,.js bin lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc && tsc -p tsconfig.esm.json", "prepack": "yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tga", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.1", "@types/glob": "7.2.0", "@types/mocha": "9.1.1", "@types/node": "18.0.3", "@types/sinon": "10.0.12", "@typescript-eslint/eslint-plugin": "5.30.6", "@typescript-eslint/parser": "5.30.6", "chai": "4.3.6", "eslint": "8.19.0", "glob": "8.0.3", "mocha": "10.0.0", "nyc": "15.1.0", "sinon": "14.0.0", "ts-node": "10.8.2", "typedoc": "0.23.7", "typescript": "4.7.4"}, "dependencies": {"queue": "6.0.2"}, "packageManager": "yarn@3.2.0", "readmeFilename": "Readme.md", "gitHead": "b1558df8dce43d9d79035d4b6677aed3bc9896ae", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_id": "image-size@2.0.0-alpha.1", "_nodeVersion": "16.19.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-uEJ51hJKZJ/0e6WhIuX1vsyJKBvDR2JRXN8onDQqQTs1+VTI2oLeSyv81wNWou9CJjzCegjhWygphb4ML3psMA==", "shasum": "96d519b31a61ba6aa778b71b6b7491f1fa029a64", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0-alpha.1.tgz", "fileCount": 69, "unpackedSize": 81475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBkj3E4NTmj97DRJkev0Zn/MjfifqqbFE7ORJwjMjB/kAiEA1po+gYZZVkLiNxM9xoeDiqXiEN1sbHX7whzSReFJPqc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGckJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrq+w/9FtP3NLjWRl0eLB5Jc6ug9djvTq+lQKy0q65rc7Cg6fI4wD9Z\r\n1hERTVH4a+1y4vNd3bvrtu9OMt+2wZ1/6IjFrl5QG6pTozj0YujX1HOoCklE\r\nqwoFNk9Yl89Cs/WtKNoIBoryMtFceiIWcD2w0fNTAfWAVSPhX7+9c0a+Yprm\r\npN6GnMubWzl6fYRxKs1Vg3JX7R0r/VtsEtoTXumOBRnD4P09+MWz59Qk05Id\r\n02CDaFAVwk/4+Q4c59GZRB1Jhpne+z9ADP3RvhOwp5qFc/lv5Wddff0vTZRt\r\nuuscopysIs+7j9RvOq+PZaoFEvMkV2IWXGbuhPstJv9iz8ulBTeCfv02OtDS\r\nRyZylInDETMvDYsX1Cxub2XbaEEA49Y60tkvJek1/7T8meZWcVGDNgljmYGD\r\noIPE8K8DTDS5E4p3KvwIjuu+kIxOpI2jla6DhjDCZLMd5C+TVFswT5CMEaVQ\r\nru8/Lzxq2MD85XYxae3FW0hfIZofu4ROQY3IrHyTMQQBmi7zDp2gNfm8XiuR\r\n2KNjIvhrSpkUOZT4vndmp+zXLJyr6So6a++az2ZfCBiwhktFHq9MkJKBuFpg\r\ncTy+/7vA0xildrfj+hbTpNrRRWNPpIIFkWW1h6amN4jJu/nafjjhUS3MrTc+\r\n+TA5APOG2LJA5+xaT95SX9AA43T2fIPxJGM=\r\n=/xqW\r\n-----END PGP SIGNATURE-----\r\n", "size": 15872}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_2.0.0-alpha.1_1679411465709_0.5827646697833817"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-21T15:11:05.888Z", "publish_time": 1679411465888, "deprecated": "Use latest please"}, "2.0.0-alpha.2": {"name": "image-size", "version": "2.0.0-alpha.2", "description": "get dimensions of any image file", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts", "exports": {".": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts"}, "./*": {"require": "./dist/cjs/*.js", "import": "./dist/esm/*.mjs", "types": "./dist/dts/*.d.ts"}, "./types/*": {"require": "./dist/cjs/types/*.js", "import": "./dist/esm/types/*.mjs", "types": "./dist/dts/types/*.d.ts"}}, "engines": {"node": ">=18.18.0"}, "packageManager": "yarn@4.0.1", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.cjs.json nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc && tsc -p tsconfig.cjs.json", "prepack": "yarn clean && yarn build && node scripts/fix-package.js"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tga", "tiff", "webp", "svg", "icns", "ico", "cur"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.10", "@types/chai-as-promised": "7.1.8", "@types/glob": "8.1.0", "@types/mocha": "10.0.4", "@types/node": "18.18.9", "@types/sinon": "17.0.1", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "chai": "4.3.10", "chai-as-promised": "7.1.1", "eslint": "8.53.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-prettier": "5.0.1", "glob": "10.3.10", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.1.0", "sinon": "17.0.1", "ts-node": "10.9.1", "typedoc": "0.25.3", "typescript": "5.2.2"}, "_id": "image-size@2.0.0-alpha.2", "readmeFilename": "Readme.md", "gitHead": "0d81314abbff8bf8f884d04e5ea730df30b6a173", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.9.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-VTVy+UjEuX4cH5ROJMgs9Qmd37Bke9A/rKBDioZDL26AipRxwwhOqipyMUVMtMGcZCf8FZ9X8SPTRQZShxAqhQ==", "shasum": "f66ca8f6180078400734740c7036ef43ea151af3", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0-alpha.2.tgz", "fileCount": 78, "unpackedSize": 83356, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7poUaP+D3OqQ0XUtBVwYqnLMQzYejatPbOwql+infGwIgH+Lv+ybTVLB2+YZDNkxsPi7PZk6S/f7h/5NUbd3/c5U="}], "size": 22794}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_2.0.0-alpha.2_1699892330062_0.31472770778070225"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-13T16:18:50.217Z", "publish_time": 1699892330217, "_source_registry_name": "default", "deprecated": "Use latest please"}, "2.0.0-beta": {"name": "image-size", "version": "2.0.0-beta", "description": "get dimensions of any image file", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts", "exports": {".": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts"}, "./*": {"require": "./dist/cjs/*.js", "import": "./dist/esm/*.mjs", "types": "./dist/dts/*.d.ts"}, "./types/*": {"require": "./dist/cjs/types/*.js", "import": "./dist/esm/types/*.mjs", "types": "./dist/dts/types/*.d.ts"}}, "engines": {"node": ">=18.18.0"}, "packageManager": "yarn@4.0.1", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.cjs.json nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc && tsc -p tsconfig.cjs.json", "prepack": "yarn clean && yarn build && node scripts/fix-package.js"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.10", "@types/chai-as-promised": "7.1.8", "@types/glob": "8.1.0", "@types/mocha": "10.0.4", "@types/node": "18.18.9", "@types/sinon": "17.0.1", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "chai": "4.3.10", "chai-as-promised": "7.1.1", "eslint": "8.53.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-prettier": "5.0.1", "glob": "10.3.10", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.1.0", "sinon": "17.0.1", "ts-node": "10.9.1", "typedoc": "0.25.3", "typescript": "5.2.2"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "_id": "image-size@2.0.0-beta", "readmeFilename": "Readme.md", "gitHead": "de859be768b7070f03c0f4800180d533597067b2", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.9.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-gUuAuu6OLQpzPY1jamw6P4g1IlZWBf5jbGyisbylz9AdEcXX5NPXOWWS8vN7y21az6Prc5Iexm/UMxzL6L+RXg==", "shasum": "91331f8d24eca17c2e2ed7859bb6f3cc361e4230", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0-beta.tgz", "fileCount": 81, "unpackedSize": 84588, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFuVtkhbs6Xd5yha7KtH5WW8C4N095nyilVdEGVtVfWbAiBo268uKQzpMYsqxB6O+ZZbaiw8eVxRTx0S0suzEETMnQ=="}], "size": 23214}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_2.0.0-beta_1699911735980_0.9556870810650271"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-13T21:42:16.231Z", "publish_time": 1699911736231, "_source_registry_name": "default", "deprecated": "Use latest please"}, "2.0.0-beta.1": {"name": "image-size", "version": "2.0.0-beta.1", "description": "get dimensions of any image file", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts", "exports": {".": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts"}, "./*": {"require": "./dist/cjs/*.js", "import": "./dist/esm/*.mjs", "types": "./dist/dts/*.d.ts"}, "./types/*": {"require": "./dist/cjs/types/*.js", "import": "./dist/esm/types/*.mjs", "types": "./dist/dts/types/*.d.ts"}}, "engines": {"node": ">=18.18.0"}, "packageManager": "yarn@4.0.1", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.cjs.json nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc && tsc -p tsconfig.cjs.json", "prepack": "yarn clean && yarn build && node scripts/fix-package.js"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.10", "@types/chai-as-promised": "7.1.8", "@types/glob": "8.1.0", "@types/mocha": "10.0.4", "@types/node": "18.18.9", "@types/sinon": "17.0.1", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "chai": "4.3.10", "chai-as-promised": "7.1.1", "eslint": "8.53.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-prettier": "5.0.1", "glob": "10.3.10", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.1.0", "sinon": "17.0.1", "ts-node": "10.9.1", "typedoc": "0.25.3", "typescript": "5.2.2"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "_id": "image-size@2.0.0-beta.1", "readmeFilename": "Readme.md", "gitHead": "ce5917d4365330b639da2d4448d51cd5dc1445b1", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.9.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-80dwE/q/d+08KeC70Wepoer7pH5bB9hGZ3ldzs1nRJcxM9RRQNgAUab9gPZIZoQRRVjDQfi15oCCrgCVhUK6XA==", "shasum": "28060488b948f6b23721f63abfa84f4789a44217", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0-beta.1.tgz", "fileCount": 81, "unpackedSize": 85092, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDf40KBIiDYjHlwkZhlv7mRL5S6eRXBAlxwdEFLxepX0gIgKbmxLTMs2FT/yRWSi4pI3+Zhz4ziPasbNBCrhN5gvUQ="}], "size": 23365}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_2.0.0-beta.1_1699917047894_0.4627948485608828"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-13T23:10:48.079Z", "publish_time": 1699917048079, "_source_registry_name": "default", "deprecated": "Use latest please"}, "1.1.0": {"name": "image-size", "version": "1.1.0", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@3.5.1", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.5", "@types/glob": "8.1.0", "@types/mocha": "10.0.1", "@types/node": "18.16.16", "@types/sinon": "10.0.15", "@typescript-eslint/eslint-plugin": "5.59.8", "@typescript-eslint/parser": "5.59.8", "chai": "4.3.7", "eslint": "8.41.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "glob": "10.2.6", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "2.8.8", "sinon": "15.1.0", "ts-node": "10.9.1", "typedoc": "0.24.7", "typescript": "5.0.4"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "dependencies": {"queue": "6.0.2"}, "_id": "image-size@1.1.0", "gitHead": "0c822974cee05ccf26f8ee9c4612797e2d6bb77c", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-asnTHw2K8OlqT5kVnQwX+AGKQqpvLo95LbNzQ/C0ln3yzentZmAdd0ygoD004VC4Kkd4PV7J2iaPQkqwp9yuTw==", "shasum": "e0458a7957b1230ec3916ae2cac7273345a93a86", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.1.0.tgz", "fileCount": 50, "unpackedSize": 49905, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAlrlzpETKAzXqmAmvPUU6Y5HT4Nq0HFKNmGkLavzDO1AiBs2g+ptOq4V28q/44ZHk01U0J46WGgO0exgsKHUEogjA=="}], "size": 14409}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_1.1.0_1703765013720_0.6685161484609983"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-28T12:03:33.915Z", "publish_time": 1703765013915, "_source_registry_name": "default"}, "1.1.1": {"name": "image-size", "version": "1.1.1", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.11", "@types/glob": "8.1.0", "@types/mocha": "10.0.6", "@types/node": "18.19.3", "@types/sinon": "17.0.2", "@typescript-eslint/eslint-plugin": "6.16.0", "@typescript-eslint/parser": "6.16.0", "chai": "4.3.10", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.2", "glob": "10.3.10", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.1.1", "sinon": "17.0.1", "ts-node": "10.9.2", "typedoc": "0.25.4", "typescript": "5.3.3"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "dependencies": {"queue": "6.0.2"}, "_id": "image-size@1.1.1", "gitHead": "9120bfb0b1dc0dab5456f22c94133b6fc3f5f0a4", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-541xKlUw6jr/6gGuk92F+mYM5zaFAc5ahphvkqvNe2bQ6gVBkd6bfrmVJ2t4KDAfikAYZyIqTnktX3i6/aQDrQ==", "shasum": "ddd67d4dc340e52ac29ce5f546a09f4e29e840ac", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.1.1.tgz", "fileCount": 50, "unpackedSize": 49955, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGf/IAij6eI435kNdJ6caHzhB7juXAf/JSZFtdDJWPEAiA8by3sMyN/n+VRAI7oCZDYsoSmyX5MJWv5nsr2dNaLAQ=="}], "size": 14421}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_1.1.1_1704208574966_0.7856415195888076"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-02T15:16:15.106Z", "publish_time": 1704208575106, "_source_registry_name": "default"}, "2.0.0-beta.2": {"name": "image-size", "version": "2.0.0-beta.2", "description": "get dimensions of any image file", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts", "exports": {".": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.mjs", "types": "./dist/dts/index.d.ts"}, "./*": {"require": "./dist/cjs/*.js", "import": "./dist/esm/*.mjs", "types": "./dist/dts/*.d.ts"}, "./types/*": {"require": "./dist/cjs/types/*.js", "import": "./dist/esm/types/*.mjs", "types": "./dist/dts/types/*.d.ts"}}, "engines": {"node": ">=18.18.0"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.cjs.json nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc && tsc -p tsconfig.cjs.json", "prepack": "yarn clean && yarn build && node scripts/fix-package.js"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@types/chai": "4.3.11", "@types/chai-as-promised": "7.1.8", "@types/glob": "8.1.0", "@types/mocha": "10.0.6", "@types/node": "18.19.3", "@types/sinon": "17.0.2", "@typescript-eslint/eslint-plugin": "6.16.0", "@typescript-eslint/parser": "6.16.0", "chai": "4.3.10", "chai-as-promised": "7.1.1", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.2", "glob": "10.3.10", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.1.1", "sinon": "17.0.1", "ts-node": "10.9.2", "typedoc": "0.25.4", "typescript": "5.3.3"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "_id": "image-size@2.0.0-beta.2", "readmeFilename": "Readme.md", "gitHead": "8b4e5bd51a912decc7c01042d91094e176f2ccb2", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-1nDNnVxJixMWBynFgQ1q8+aVqK60TiNHpMyFAXt9xpzGZV+2lHI1IXjgdcAjBxPc4nx2ed1NdYs2I+Zfq+Zn7w==", "shasum": "d898c962ca5f3337aae715e9b359a3adb6180b4f", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0-beta.2.tgz", "fileCount": 81, "unpackedSize": 79509, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOuLe02MfqhireuNx5itoIcaIsa/MNuDU0cD7TbqFUlQIhAKJxD70brYs99LHG12/3ESL9Mo5JwvdvHJ0ylzq+Cem0"}], "size": 22856}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/image-size_2.0.0-beta.2_1705064534575_0.011668298158721946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-12T13:02:14.712Z", "publish_time": 1705064534712, "_source_registry_name": "default", "deprecated": "Use latest please"}, "1.2.0": {"name": "image-size", "version": "1.2.0", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint bin lib specs", "format": "prettier --write lib specs eslint.config.mjs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@eslint/js": "9.5.0", "@types/chai": "4.3.16", "@types/eslint__js": "8.42.3", "@types/glob": "8.1.0", "@types/mocha": "10.0.7", "@types/node": "18.19.39", "@types/sinon": "17.0.3", "chai": "4.4.1", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "glob": "10.4.2", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.3.2", "sinon": "17.0.1", "ts-node": "10.9.2", "typedoc": "0.25.13", "typescript": "5.4.5", "typescript-eslint": "7.13.1"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "dependencies": {"queue": "6.0.2"}, "_id": "image-size@1.2.0", "gitHead": "9d41448d7843405d1ff2c59352ec17a9bca3f358", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.18.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-4S8fwbO6w3GeCVN6OPtA9I5IGKkcDMPcKndtUlpJuCwu7JLjtj7JZpwqLuyY2nrmQT3AWsCJLSKPsc2mPBSl3w==", "shasum": "312af27a2ff4ff58595ad00b9344dd684c910df6", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.2.0.tgz", "fileCount": 56, "unpackedSize": 56607, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb+gAfdcXoqjfJmXhIzr5vnXjCZuRVoSBCSLjvwQ4pdgIhAKGE07oMhy/uDqxEhzG1lrCBAUztkfuMGZIvLO+4Ghdl"}], "size": 15965}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/image-size_1.2.0_1734840049419_0.73590180606312"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-22T04:00:49.642Z", "publish_time": 1734840049642, "_source_registry_name": "default"}, "2.0.0-beta.4": {"name": "image-size", "version": "2.0.0-beta.4", "description": "get dimensions of any image file", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}, "./fromFile": {"import": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.mjs"}, "require": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.cjs"}}, "./types/*": {"import": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.mjs"}, "require": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.cjs"}}}, "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "biome check lib specs", "format": "biome format --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.test.json c8 --reporter=text --reporter=lcov node --require ts-node/register --test --test-reporter=dot specs/*.spec.ts", "test:watch": "TS_NODE_PROJECT=tsconfig.test.json node --require ts-node/register --test --watch specs/*.spec.ts", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsup", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.4", "@types/glob": "8.1.0", "@types/node": "18.19.39", "c8": "10.1.3", "glob": "10.4.2", "ts-node": "10.9.2", "tsup": "8.3.5", "typedoc": "0.25.13", "typescript": "5.4.5"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "_id": "image-size@2.0.0-beta.4", "readmeFilename": "Readme.md", "gitHead": "636ebd0a5caea593174e2ff48ba374baf3239757", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.18.1", "_npmVersion": "11.0.0", "dist": {"integrity": "sha512-e2VQ0AUKKECajkd6U7mpA1wQaCnA1BE7DT527SUbHHDg9pzo4RjidrmC+x/Y9q7xTsdOLp+j+4ObNXLYhjiR+A==", "shasum": "495b6d04435785193a4819df64bc46a6187ec2a6", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0-beta.4.tgz", "fileCount": 116, "unpackedSize": 377939, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDL64E7O1z4CWf2Srg2KOMfRoutdfjEangEiagxVeAGqgIhAPDatjB6SWYdydpXETLMWOkjT12Hcph4CVzCbaOQeFgV"}], "size": 46230}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/image-size_2.0.0-beta.4_1735156853422_0.5766850990622465"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-25T20:00:53.615Z", "publish_time": 1735156853615, "_source_registry_name": "default", "deprecated": "Use latest please"}, "2.0.0": {"name": "image-size", "version": "2.0.0", "description": "get dimensions of any image file", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}, "./fromFile": {"import": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.mjs"}, "require": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.cjs"}}, "./types/*": {"import": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.mjs"}, "require": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.cjs"}}}, "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "biome check lib specs", "format": "biome format --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.test.json c8 --reporter=text --reporter=lcov node --require ts-node/register --test --test-reporter=dot specs/*.spec.ts", "test:watch": "TS_NODE_PROJECT=tsconfig.test.json node --require ts-node/register --test --watch specs/*.spec.ts", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsup", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.4", "@types/glob": "8.1.0", "@types/node": "18.19.39", "c8": "10.1.3", "glob": "10.4.2", "ts-node": "10.9.2", "tsup": "8.3.5", "typedoc": "0.25.13", "typescript": "5.4.5"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "_id": "image-size@2.0.0", "gitHead": "1fb8f411593acbdb413eaf97817a8eb0b7e8af83", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.18.2", "_npmVersion": "11.1.0", "dist": {"integrity": "sha512-HP07n1SpdIXGUL4VotUIOQz66MQOq8g7VN+Yj02YTVowqZScQ5i/JYU0+lkNr2pwt5j4hOpk94/UBV1ZCbS2fA==", "shasum": "5b03dd619e73a6e7e55c323f9934a0a5d4461e8b", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.0.tgz", "fileCount": 116, "unpackedSize": 377932, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCFeNHub90FpC1ey8FO7A0NywNdSDrqd77KiZ0NPBlBWgIhAO3RsiWQp0UVjg6SnVvAiA5Mj0BWDfE/G/66W994uQ+4"}], "size": 46227}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/image-size_2.0.0_1740484030826_0.18326701758094854"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-25T11:47:11.040Z", "publish_time": 1740484031040, "_source_registry_name": "default"}, "2.0.1": {"name": "image-size", "version": "2.0.1", "description": "get dimensions of any image file", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}, "./fromFile": {"import": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.mjs"}, "require": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.cjs"}}, "./types/*": {"import": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.mjs"}, "require": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.cjs"}}}, "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "biome check lib specs", "format": "biome format --write lib specs", "test": "TS_NODE_PROJECT=tsconfig.test.json c8 --reporter=text --reporter=lcov node --require ts-node/register --test --test-reporter=dot specs/*.spec.ts", "test:watch": "TS_NODE_PROJECT=tsconfig.test.json node --require ts-node/register --test --watch specs/*.spec.ts", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsup", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.4", "@types/glob": "8.1.0", "@types/node": "18.19.39", "c8": "10.1.3", "glob": "10.4.2", "ts-node": "10.9.2", "tsup": "8.3.5", "typedoc": "0.25.13", "typescript": "5.4.5"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "_id": "image-size@2.0.1", "gitHead": "a5750c51dc1d81756521521f5821da732038d8c4", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.18.3", "_npmVersion": "11.2.0", "dist": {"integrity": "sha512-NI6NK/2zchlZopsQrcVIS7jxA0/rtIy74B+/rx5s7rKQyFebmQjZVhzxXgRZJROk+WhhOq+S6sUaODxp0L5hfg==", "shasum": "80f06046b8facf8d63d78e1d350873fea375f0eb", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.1.tgz", "fileCount": 116, "unpackedSize": 378609, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGMwspqExnyf58EG234nMaa9jLB2SKubb2UNuwUd0Hm9AiEAt2Pol5jAkJgrlek8/FNitNJC4Jeyf5rCHY301sqiG/E="}], "size": 46468}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/image-size_2.0.1_1741863839236_0.3522309124353449"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-13T11:03:59.427Z", "publish_time": 1741863839427, "_source_registry_name": "default"}, "2.0.2": {"name": "image-size", "version": "2.0.2", "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "author": {"url": "http://netroy.in/", "name": "netroy", "email": "<EMAIL>"}, "license": "MIT", "_id": "image-size@2.0.2", "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "homepage": "https://github.com/image-size/image-size#readme", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "bin": {"image-size": "bin/image-size.js"}, "nyc": {"exclude": "specs/*.spec.ts", "include": "lib"}, "dist": {"shasum": "84a7b43704db5736f364bf0d1b029821299b4bdc", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-2.0.2.tgz", "fileCount": 116, "integrity": "sha512-IRqXKlaXwgSMAMtpNzZa1ZAe8m+Sa1770Dhk8VkSsP9LS+iHD62Zd8FQKs8fbPiagBE7BzoFX23cxFnwshpV6w==", "signatures": [{"sig": "MEUCIQCW5f/yDGUbZaY1C3ZRQP/gvZ+9RhHCqAg4RFSx8d10hQIgZznudNW/sjBWbyeJjLXgu18uVddwXSF4Oag2Lkg9J4M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 378406, "size": 46414}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">=16.x"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}, "./types/*": {"import": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.mjs"}, "require": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.cjs"}}, "./fromFile": {"import": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.mjs"}, "require": {"types": "./dist/fromFile.d.ts", "default": "./dist/fromFile.cjs"}}}, "gitHead": "032c3347b86f09a2e16449e17537cf5e1009520c", "scripts": {"lint": "biome check lib specs", "test": "TS_NODE_PROJECT=tsconfig.test.json c8 --reporter=text --reporter=lcov node --require ts-node/register --test --test-reporter=dot specs/*.spec.ts", "build": "tsup", "clean": "rm -rf dist docs", "format": "biome format --write lib specs", "prepack": "yarn clean && yarn build", "test:watch": "TS_NODE_PROJECT=tsconfig.test.json node --require ts-node/register --test --watch specs/*.spec.ts", "generate-docs": "typedoc"}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/image-size/image-size.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "get dimensions of any image file", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"c8": "10.1.3", "glob": "10.4.2", "tsup": "8.3.5", "ts-node": "10.9.2", "typedoc": "0.25.13", "typescript": "5.4.5", "@types/glob": "8.1.0", "@types/node": "18.19.39", "@biomejs/biome": "1.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/image-size_2.0.2_1743603749751_0.8190430845454806", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-04-02T14:22:29.940Z", "publish_time": 1743603749940, "_source_registry_name": "default"}, "1.2.1": {"name": "image-size", "version": "1.2.1", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": {"image-size": "bin/image-size.js"}, "scripts": {"lint": "eslint bin lib specs", "format": "prettier --write lib specs eslint.config.mjs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"type": "git", "url": "git://github.com/image-size/image-size.git"}, "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "license": "MIT", "devDependencies": {"@eslint/js": "9.5.0", "@types/chai": "4.3.16", "@types/eslint__js": "8.42.3", "@types/glob": "8.1.0", "@types/mocha": "10.0.7", "@types/node": "18.19.39", "@types/sinon": "17.0.3", "chai": "4.4.1", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "glob": "10.4.2", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.3.2", "sinon": "17.0.1", "ts-node": "10.9.2", "typedoc": "0.25.13", "typescript": "5.4.5", "typescript-eslint": "7.13.1"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "dependencies": {"queue": "6.0.2"}, "_id": "image-size@1.2.1", "readmeFilename": "Readme.md", "gitHead": "a4178fbb334ddb22d94cb4228ed597c24fd02e10", "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "_nodeVersion": "20.19.0", "_npmVersion": "11.2.0", "dist": {"integrity": "sha512-rH+46sQJ2dlwfjfhCyNx5thzrv+dtmBIhPHk0zgRUukHzZ/kRueTJXoYYsclBaKcSMBWuGbOFXtioLpzTb5euw==", "shasum": "ee118aedfe666db1a6ee12bed5821cde3740276d", "tarball": "https://registry.npmmirror.com/image-size/-/image-size-1.2.1.tgz", "fileCount": 56, "unpackedSize": 56781, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHmcp3sQmBJUNRIG5ZTZnJYmXSs/WxdnnaKdXAorXHeCAiBkOFb2SOqev2fmp61koXMrkIWc/t25gJ5zcpZXYzq2UQ=="}], "size": 16060}, "_npmUser": {"name": "netroy", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "netroy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/image-size_1.2.1_1743604229536_0.8134209879959355"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T14:30:29.710Z", "publish_time": 1743604229710, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "homepage": "https://github.com/image-size/image-size#readme", "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "jxl", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": {"url": "git://github.com/image-size/image-size.git", "type": "git"}, "_source_registry_name": "default"}