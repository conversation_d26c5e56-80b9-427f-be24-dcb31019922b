{"_attachments": {}, "_id": "schema-utils", "_rev": "2752-61f14a07830fd08f52a2c519", "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "description": "webpack Validation Utils", "dist-tags": {"latest": "4.3.2", "version-3": "3.3.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}], "name": "schema-utils", "readme": "<div align=\"center\">\n  <a href=\"http://json-schema.org\">\n    <img width=\"160\" height=\"160\"\n      src=\"https://raw.githubusercontent.com/webpack-contrib/schema-utils/master/.github/assets/logo.png\">\n  </a>\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\"\n      src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n</div>\n\n[![npm][npm]][npm-url]\n[![node][node]][node-url]\n[![tests][tests]][tests-url]\n[![coverage][cover]][cover-url]\n[![GitHub Discussions][discussion]][discussion-url]\n[![size][size]][size-url]\n\n# schema-utils\n\nPackage for validate options in loaders and plugins.\n\n## Getting Started\n\nTo begin, you'll need to install `schema-utils`:\n\n```console\nnpm install schema-utils\n```\n\n## API\n\n**schema.json**\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"option\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { option: true };\nconst configuration = { name: \"Loader Name/Plugin Name/Name\" };\n\nvalidate(schema, options, configuration);\n```\n\n### `schema`\n\nType: `String`\n\nJSON schema.\n\nSimple example of schema:\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"description\": \"This is description of option.\",\n      \"type\": \"string\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n### `options`\n\nType: `Object`\n\nObject with options.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, { name: 123 }, { name: \"MyPlugin\" });\n```\n\n### `configuration`\n\nAllow to configure validator.\n\nThere is an alternative method to configure the `name` and`baseDataPath` options via the `title` property in the schema.\nFor example:\n\n```json\n{\n  \"title\": \"My Loader options\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"description\": \"This is description of option.\",\n      \"type\": \"string\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\nThe last word used for the `baseDataPath` option, other words used for the `name` option.\nBased on the example above the `name` option equals `My Loader`, the `baseDataPath` option equals `options`.\n\n#### `name`\n\nType: `Object`\nDefault: `\"Object\"`\n\nAllow to setup name in validation errors.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, options, { name: \"MyPlugin\" });\n```\n\n```shell\nInvalid configuration object. MyPlugin has been initialised using a configuration object that does not match the API schema.\n - configuration.optionName should be a integer.\n```\n\n#### `baseDataPath`\n\nType: `String`\nDefault: `\"configuration\"`\n\nAllow to setup base data path in validation errors.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, options, { name: \"MyPlugin\", baseDataPath: \"options\" });\n```\n\n```shell\nInvalid options object. MyPlugin has been initialised using an options object that does not match the API schema.\n - options.optionName should be a integer.\n```\n\n#### `postFormatter`\n\nType: `Function`\nDefault: `undefined`\n\nAllow to reformat errors.\n\n```js\nimport schema from \"./path/to/schema.json\";\nimport { validate } from \"schema-utils\";\n\nconst options = { foo: \"bar\" };\n\nvalidate(schema, options, {\n  name: \"MyPlugin\",\n  postFormatter: (formattedError, error) => {\n    if (error.keyword === \"type\") {\n      return `${formattedError}\\nAdditional Information.`;\n    }\n\n    return formattedError;\n  },\n});\n```\n\n```shell\nInvalid options object. MyPlugin has been initialized using an options object that does not match the API schema.\n - options.optionName should be a integer.\n   Additional Information.\n```\n\n## Examples\n\n**schema.json**\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"type\": \"string\"\n    },\n    \"test\": {\n      \"anyOf\": [\n        { \"type\": \"array\" },\n        { \"type\": \"string\" },\n        { \"instanceof\": \"RegExp\" }\n      ]\n    },\n    \"transform\": {\n      \"instanceof\": \"Function\"\n    },\n    \"sourceMap\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n### `Loader`\n\n```js\nimport { getOptions } from \"loader-utils\";\nimport { validate } from \"schema-utils\";\n\nimport schema from \"path/to/schema.json\";\n\nfunction loader(src, map) {\n  const options = getOptions(this);\n\n  validate(schema, options, {\n    name: \"Loader Name\",\n    baseDataPath: \"options\",\n  });\n\n  // Code...\n}\n\nexport default loader;\n```\n\n### `Plugin`\n\n```js\nimport { validate } from \"schema-utils\";\n\nimport schema from \"path/to/schema.json\";\n\nclass Plugin {\n  constructor(options) {\n    validate(schema, options, {\n      name: \"Plugin Name\",\n      baseDataPath: \"options\",\n    });\n\n    this.options = options;\n  }\n\n  apply(compiler) {\n    // Code...\n  }\n}\n\nexport default Plugin;\n```\n\n### Allow to disable and enable validation (the `validate` function do nothing)\n\nThis can be useful when you don't want to do validation for `production` builds.\n\n```js\nimport { disableValidation, enableValidation, validate } from \"schema-utils\";\n\n// Disable validation\ndisableValidation();\n// Do nothing\nvalidate(schema, options);\n\n// Enable validation\nenableValidation();\n// Will throw an error if schema is not valid\nvalidate(schema, options);\n\n// Allow to undestand do you need validation or not\nconst need = needValidate();\n\nconsole.log(need);\n```\n\nAlso you can enable/disable validation using the `process.env.SKIP_VALIDATION` env variable.\n\nSupported values (case insensitive):\n\n- `yes`/`y`/`true`/`1`/`on`\n- `no`/`n`/`false`/`0`/`off`\n\n## Contributing\n\nPlease take a moment to read our contributing guidelines if you haven't yet done so.\n\n[CONTRIBUTING](./.github/CONTRIBUTING.md)\n\n## License\n\n[MIT](./LICENSE)\n\n[npm]: https://img.shields.io/npm/v/schema-utils.svg\n[npm-url]: https://npmjs.com/package/schema-utils\n[node]: https://img.shields.io/node/v/schema-utils.svg\n[node-url]: https://nodejs.org\n[tests]: https://github.com/webpack/schema-utils/workflows/schema-utils/badge.svg\n[tests-url]: https://github.com/webpack/schema-utils/actions\n[cover]: https://codecov.io/gh/webpack/schema-utils/branch/master/graph/badge.svg\n[cover-url]: https://codecov.io/gh/webpack/schema-utils\n[discussion]: https://img.shields.io/github/discussions/webpack/webpack\n[discussion-url]: https://github.com/webpack/webpack/discussions\n[size]: https://packagephobia.com/badge?p=schema-utils\n[size-url]: https://packagephobia.com/result?p=schema-utils\n", "time": {"created": "2022-01-26T13:17:59.840Z", "modified": "2025-04-22T17:48:03.782Z", "4.0.0": "2021-11-16T15:15:38.469Z", "3.1.1": "2021-07-19T11:38:40.814Z", "3.1.0": "2021-07-05T11:33:38.926Z", "3.0.0": "2020-10-05T18:21:37.231Z", "2.7.1": "2020-08-31T11:00:30.300Z", "2.7.0": "2020-05-29T14:10:00.858Z", "2.6.6": "2020-04-17T15:41:37.894Z", "2.6.5": "2020-03-11T12:05:26.330Z", "2.6.4": "2020-01-17T11:47:46.281Z", "2.6.3": "2020-01-17T10:30:14.977Z", "2.6.2": "2020-01-14T13:19:13.823Z", "2.6.1": "2019-11-28T13:12:15.734Z", "2.6.0": "2019-11-27T12:23:28.917Z", "2.5.0": "2019-10-15T12:06:37.995Z", "2.4.1": "2019-09-27T11:25:05.323Z", "2.4.0": "2019-09-26T19:03:42.469Z", "2.3.0": "2019-09-26T13:33:07.161Z", "2.2.0": "2019-09-02T10:57:34.379Z", "2.1.0": "2019-08-07T15:02:52.138Z", "2.0.1": "2019-07-18T16:12:42.173Z", "2.0.0": "2019-07-17T14:16:49.234Z", "1.0.0": "2018-08-07T16:31:12.254Z", "0.4.7": "2018-08-07T12:16:50.127Z", "0.4.6": "2018-08-06T17:06:08.539Z", "0.4.5": "2018-02-13T13:47:48.167Z", "0.4.4": "2018-02-13T13:33:41.966Z", "0.4.3": "2017-12-14T02:49:01.330Z", "0.4.2": "2017-11-09T20:16:27.548Z", "0.4.1": "2017-11-03T09:51:33.117Z", "0.4.0": "2017-10-30T18:21:50.029Z", "0.3.0": "2017-04-29T18:20:47.722Z", "0.2.1": "2017-03-13T07:04:56.561Z", "0.1.0": "2017-03-07T06:01:28.207Z", "4.0.1": "2023-04-15T14:55:14.105Z", "3.1.2": "2023-04-15T15:21:23.762Z", "4.1.0": "2023-06-07T22:22:21.141Z", "3.2.0": "2023-06-07T22:25:11.393Z", "4.2.0": "2023-06-14T14:25:16.297Z", "3.3.0": "2023-06-14T14:52:43.727Z", "4.3.0": "2024-12-11T18:13:26.041Z", "4.3.1": "2025-04-22T13:03:41.729Z", "4.3.2": "2025-04-22T17:47:45.257Z"}, "versions": {"4.0.0": {"name": "schema-utils", "version": "4.0.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-keywords": "^5.0.0", "ajv-formats": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.0", "@commitlint/cli": "^14.1.0", "@commitlint/config-conventional": "^14.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.3.1", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.3", "husky": "^7.0.4", "jest": "^27.3.1", "lint-staged": "^12.0.2", "npm-run-all": "^4.1.5", "prettier": "^2.4.1", "standard-version": "^9.3.2", "typescript": "^4.3.5", "webpack": "^5.64.1"}, "keywords": ["webpack"], "gitHead": "746a1e827653181dd620b09d0d61584ea4038bb7", "_id": "schema-utils@4.0.0", "_nodeVersion": "12.22.7", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==", "shasum": "60331e9e3ae78ec5d16353c467c34b3a0a1d3df7", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.0.0.tgz", "fileCount": 15, "unpackedSize": 66292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk8saCRA9TVsSAnZWagAARN8P/AycwzikWZ2sWpocm7hc\nMeHLwhGlX16qUqwImhnrGepd4yfRHVpcRnj49/n9wj2fnMhyo/pLaLGXXfpK\npuIgy85tZkttWNJGlKErr3yXGWD6UjU6BcU+VO3kffbbKjWEl5qBcXqz9GsZ\n/ERn7/yBDKTRP/zZ8nt4AWFBJ08BFtuht2xdXeDLlUO9VKxwLpw8p7TbfdH3\nEGWQZumXgZyPgzbsiaFupR5Iu0VT/4ugxA/HTEv9o6nH1cA2yUi9wM4S8NUU\nr6vIQxckIIeBDw0cZ9EUK6ZagVTyfxGDpjdKnEgNW8u4oXgS6I0iSOBehleW\nK60YaWVzux3awmMlsXSZWlaaJRNtGq8LeAtt0K+EaaajfyGW9yxAZ7k0iett\nunL1ggpdSQTYBZhaByxWifeuzDpRyH0L60BgcPMx3jrzwf1thzfzSYzc3Ua8\nhmP4BSQl35Ttu5aa2JSW2kunxwhqzjgHzFVWmkXdb98IlfbLschsm55KrP51\nIfyJmwWZQsXu+BG46CeoTel0DbJuQEPAaqE2V5xF2Nzz6lqaCjJzUsvpr7hx\nsjhgs7Lc6BXMULr4TwCclTSCk6Id/aLnwNlVjka//VOCJQf/mdZrBvyPsDer\nAt75Lxcbir0gTgEUkJfxCa/lIV9FuwIF9Udy74se+pWcCX1pjMm2qdGpKb4a\ngaNi\r\n=7vkI\r\n-----END PGP SIGNATURE-----\r\n", "size": 14338, "noattachment": false}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_4.0.0_1637075738265_0.43114987792105897"}, "_hasShrinkwrap": false, "publish_time": 1637075738469, "_cnpm_publish_time": 1637075738469, "_cnpmcore_publish_time": "2021-12-15T10:39:49.770Z"}, "3.1.1": {"name": "schema-utils", "version": "3.1.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.31.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "typescript": "^4.3.5", "webpack": "^5.45.1"}, "keywords": ["webpack"], "gitHead": "faadaf950afd79352d7f9d0c3e0601b7ee45c5f2", "_id": "schema-utils@3.1.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.16.0", "dist": {"shasum": "bc74c4b6b6995c1d88f76a8b77bea7219e0c8281", "size": 14170, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz", "integrity": "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_3.1.1_1626694720644_0.4370288263243398"}, "_hasShrinkwrap": false, "publish_time": 1626694720814, "_cnpm_publish_time": 1626694720814, "_cnpmcore_publish_time": "2021-12-15T10:39:49.958Z"}, "3.1.0": {"name": "schema-utils", "version": "3.1.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.7", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.29.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.6", "lint-staged": "^11.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.0", "typescript": "^4.3.4", "webpack": "^5.41.1"}, "keywords": ["webpack"], "gitHead": "3915bae9a2ce3206dff5f7f3dff717af2ae1d527", "_id": "schema-utils@3.1.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.16.0", "dist": {"shasum": "95986eb604f66daadeed56e379bfe7a7f963cdb9", "size": 14168, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.0.tgz", "integrity": "sha512-tTEaeYkyIhEZ9uWgAjDerWov3T9MgX8dhhy2r0IGeeX4W8ngtGl1++dUve/RUqzuaASSh7shwCDJjEzthxki8w=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_3.1.0_1625484818786_0.06525563801121104"}, "_hasShrinkwrap": false, "publish_time": 1625484818926, "_cnpm_publish_time": 1625484818926, "_cnpmcore_publish_time": "2021-12-15T10:39:50.159Z"}, "3.0.0": {"name": "schema-utils", "version": "3.0.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.12.5", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.6"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.5.0", "cross-env": "^7.0.2", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "eslint-plugin-import": "^2.22.1", "husky": "^4.3.0", "jest": "^26.5.0", "lint-staged": "^10.4.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.2", "standard-version": "^9.0.0", "typescript": "^4.0.3"}, "keywords": ["webpack"], "gitHead": "cdbf8b714a90e4a6919efff10d7633e413bca758", "_id": "schema-utils@3.0.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.8", "dist": {"shasum": "67502f6aa2b66a2d4032b4279a2944978a0913ef", "size": 17025, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.0.0.tgz", "integrity": "sha512-6D82/xSzO094ajanoOSbe4YvXWMfn2A//8Y1+MUqFAJul5Bs+yn36xbK9OtNDcRVSBJ9jjeoXftM6CfztsjOAA=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "d3viant0ne", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_3.0.0_1601922097119_0.04618294561022296"}, "_hasShrinkwrap": false, "publish_time": 1601922097231, "_cnpm_publish_time": 1601922097231, "_cnpmcore_publish_time": "2021-12-15T10:39:50.335Z"}, "2.7.1": {"name": "schema-utils", "version": "2.7.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.12.4", "ajv-keywords": "^3.5.2", "@types/json-schema": "^7.0.5"}, "devDependencies": {"@babel/cli": "^7.10.5", "@babel/core": "^7.11.4", "@babel/preset-env": "^7.11.0", "@commitlint/cli": "^10.0.0", "@commitlint/config-conventional": "^10.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.5.1", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.7.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "husky": "^4.2.5", "jest": "^25.5.4", "lint-staged": "^10.2.13", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^9.0.0", "typescript": "^4.0.2"}, "keywords": ["webpack"], "gitHead": "102d170506640346b8269d7e38a042b6b5b3a444", "_id": "schema-utils@2.7.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.8", "dist": {"shasum": "1ca4f32d1b24c590c203b8e7a50bf0ea4cd394d7", "size": 16977, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz", "integrity": "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.7.1_1598871630144_0.02020404742410964"}, "_hasShrinkwrap": false, "publish_time": 1598871630300, "_cnpm_publish_time": 1598871630300, "_cnpmcore_publish_time": "2021-12-15T10:39:50.496Z"}, "2.7.0": {"name": "schema-utils", "version": "2.7.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.12.2", "ajv-keywords": "^3.4.1", "@types/json-schema": "^7.0.4"}, "devDependencies": {"@babel/cli": "^7.10.1", "@babel/core": "^7.10.1", "@babel/preset-env": "^7.10.1", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.5.1", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "husky": "^4.2.5", "jest": "^25.5.4", "lint-staged": "^10.2.7", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^8.0.0", "typescript": "^3.9.3"}, "keywords": ["webpack"], "gitHead": "132fee2af33a2087568bd7482687576fbd121905", "_id": "schema-utils@2.7.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.5", "dist": {"shasum": "17151f76d8eae67fbbf77960c33c676ad9f4efc7", "size": 16840, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.0.tgz", "integrity": "sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.7.0_1590761400741_0.32278211142016877"}, "_hasShrinkwrap": false, "publish_time": 1590761400858, "_cnpm_publish_time": 1590761400858, "_cnpmcore_publish_time": "2021-12-15T10:39:50.675Z"}, "2.6.6": {"name": "schema-utils", "version": "2.6.6", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.3.0", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "husky": "^4.2.5", "jest": "^25.3.0", "lint-staged": "^10.0.8", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.1.0", "typescript": "^3.8.3"}, "keywords": ["webpack"], "gitHead": "d4bfe210d6087a51716cdb086aa67a9f3292e984", "_id": "schema-utils@2.6.6", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.4", "dist": {"shasum": "299fe6bd4a3365dc23d99fd446caff8f1d6c330c", "size": 15940, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.6.tgz", "integrity": "sha512-wHutF/WPSbIi9x6ctjGGk2Hvl0VOz5l3EKEuKbjPlB30mKZUzb9A5k9yEXRX3pwyqVLPvpfZZEllaFq/M718hA=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.6_1587138097694_0.21095751536081075"}, "_hasShrinkwrap": false, "publish_time": 1587138097894, "_cnpm_publish_time": 1587138097894, "_cnpmcore_publish_time": "2021-12-15T10:39:50.865Z"}, "2.6.5": {"name": "schema-utils", "version": "2.6.5", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/preset-env": "^7.8.7", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.1.0", "commitlint-azure-pipelines-cli": "^1.0.3", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "husky": "^4.2.3", "jest": "^25.1.0", "jest-junit": "^10.0.0", "lint-staged": "^10.0.8", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.1.0", "typescript": "^3.8.3"}, "keywords": ["webpack"], "gitHead": "91003752ae6c7f0c38036cb3c4532d88015ce554", "_id": "schema-utils@2.6.5", "_nodeVersion": "10.15.2", "_npmVersion": "6.14.2", "dist": {"shasum": "c758f0a7e624263073d396e29cd40aa101152d8a", "size": 15961, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.5.tgz", "integrity": "sha512-5KXuwKziQrTVHh8j/Uxz+QUbxkaLW9X/86NBlx/gnKgtsZA2GIVMUn17qWhRFwF8jdYb3Dig5hRO/W5mZqy6SQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.5_1583928326140_0.5492828509958352"}, "_hasShrinkwrap": false, "publish_time": 1583928326330, "_cnpm_publish_time": 1583928326330, "_cnpmcore_publish_time": "2021-12-15T10:39:51.187Z"}, "2.6.4": {"name": "schema-utils", "version": "2.6.4", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.8.3", "@babel/core": "^7.8.3", "@babel/preset-env": "^7.8.3", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.3", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-import": "^2.20.0", "husky": "^4.0.10", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.5"}, "keywords": ["webpack"], "gitHead": "72387e3417cf490bc2f7d22ec0d4066227b0696e", "_id": "schema-utils@2.6.4", "_nodeVersion": "10.15.2", "_npmVersion": "6.13.6", "dist": {"shasum": "a27efbf6e4e78689d91872ee3ccfa57d7bdd0f53", "size": 15629, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.4.tgz", "integrity": "sha512-VNjcaUxVnEeun6B2fiiUDjXXBtD4ZSH7pdbfIu1pOFwgptDPLMo/z9jr4sUfsjFVPqDCEin/F7IYlq7/E6yDbQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.4_1579261666107_0.8532225834628222"}, "_hasShrinkwrap": false, "publish_time": 1579261666281, "_cnpm_publish_time": 1579261666281, "_cnpmcore_publish_time": "2021-12-15T10:39:51.372Z"}, "2.6.3": {"name": "schema-utils", "version": "2.6.3", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.8.3", "@babel/core": "^7.8.3", "@babel/preset-env": "^7.8.3", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.3", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-import": "^2.20.0", "husky": "^4.0.10", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.5"}, "keywords": ["webpack"], "gitHead": "669ef56bc654b215e3eb8ce02ec531253fbd3054", "_id": "schema-utils@2.6.3", "_nodeVersion": "10.15.2", "_npmVersion": "6.13.6", "dist": {"shasum": "f889690172941febdf56b7e6785428d717d84786", "size": 15561, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.3.tgz", "integrity": "sha512-4vpF5nc+MYrHjsAnA43R75OtvjYUOFDmuwAdhzW7g0/zlFsepaMX/NLhOThg0L+5mqxIw7IK1oR9M3MsrklMsA=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.3_1579257014810_0.07111573525729709"}, "_hasShrinkwrap": false, "publish_time": 1579257014977, "_cnpm_publish_time": 1579257014977, "_cnpmcore_publish_time": "2021-12-15T10:39:51.575Z"}, "2.6.2": {"name": "schema-utils", "version": "2.6.2", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.8.3", "@babel/core": "^7.8.3", "@babel/preset-env": "^7.8.3", "@commitlint/cli": "^8.3.4", "@commitlint/config-conventional": "^8.3.4", "@types/json-schema": "^7.0.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.3", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-import": "^2.20.0", "husky": "^4.0.6", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.4"}, "keywords": ["webpack"], "gitHead": "94e517b041f9f051e25092ae7162f5b61834a9af", "_id": "schema-utils@2.6.2", "_nodeVersion": "10.15.2", "_npmVersion": "6.13.6", "dist": {"shasum": "9205ec5978709b0d9edbccb9a316faf11617a017", "size": 15472, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.2.tgz", "integrity": "sha512-sazKNMBX/jwrXRkOI7N6dtiTVYqzSckzol8SGuHt0lE/v3xSW6cUkOqzu6Bq2tW+dlUzq3CWIqHU3ZKauliqdg=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.2_1579007953704_0.15739376554868922"}, "_hasShrinkwrap": false, "publish_time": 1579007953823, "_cnpm_publish_time": 1579007953823, "_cnpmcore_publish_time": "2021-12-15T10:39:51.764Z"}, "2.6.1": {"name": "schema-utils", "version": "2.6.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.7.4", "@babel/core": "^7.7.4", "@babel/preset-env": "^7.7.4", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@types/json-schema": "^7.0.3", "@webpack-contrib/defaults": "^6.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.7.1", "eslint-config-prettier": "^6.7.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.1.0", "jest": "^24.9.0", "jest-junit": "^9.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.2"}, "keywords": ["webpack"], "gitHead": "6caaa59cf40af948ea3d9658f4c32bd79b6c37a5", "_id": "schema-utils@2.6.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.13.1", "dist": {"shasum": "eb78f0b945c7bcfa2082b3565e8db3548011dc4f", "size": 15338, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.1.tgz", "integrity": "sha512-0WXHDs1VDJyo+Zqs9TKLKyD/h7yDpHUhEFsM2CzkICFdoX1av+GBq/J2xRTFfsQO5kBfhZzANf2VcIm84jqDbg=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.1_1574946735632_0.10563936035910837"}, "_hasShrinkwrap": false, "publish_time": 1574946735734, "_cnpm_publish_time": 1574946735734, "_cnpmcore_publish_time": "2021-12-15T10:39:51.936Z"}, "2.6.0": {"name": "schema-utils", "version": "2.6.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.7.4", "@babel/core": "^7.7.4", "@babel/preset-env": "^7.7.4", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@types/json-schema": "^7.0.3", "@webpack-contrib/defaults": "^6.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.7.1", "eslint-config-prettier": "^6.7.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.1.0", "jest": "^24.9.0", "jest-junit": "^9.0.0", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "typescript": "^3.7.2"}, "keywords": ["webpack"], "gitHead": "6c6973b9decc1179c064971a770d4027da86e0b2", "_id": "schema-utils@2.6.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.13.1", "dist": {"shasum": "68a259aabbef9d08d1252c2e63c398e476308e80", "size": 15229, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.6.0.tgz", "integrity": "sha512-UlPB1ME4i/71cih/Rv92gK8043CrJTc2mjkyxDp4pdJ7ZfzY0g0hdGjjDB23jX3X+NXSneCdQbScGhn6K2tbpQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.6.0_1574857408783_0.42345536104907167"}, "_hasShrinkwrap": false, "publish_time": 1574857408917, "_cnpm_publish_time": 1574857408917, "_cnpmcore_publish_time": "2021-12-15T10:39:52.160Z"}, "2.5.0": {"name": "schema-utils", "version": "2.5.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "main": "dist/index.js", "types": "index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@types/json-schema": "^7.0.3", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^6.0.0", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.4.0", "eslint-config-prettier": "^6.3.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0"}, "keywords": ["webpack"], "gitHead": "b98e06cda3cd207ba9f35ca972c601c017700d2d", "_id": "schema-utils@2.5.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.12.0", "dist": {"shasum": "8f254f618d402cc80257486213c8970edfd7c22f", "size": 12360, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.5.0.tgz", "integrity": "sha512-32ISrwW2scPXHUSusP8qMg5dLUawKkyV+/qIEV9JdXKx+rsM6mi8vZY8khg2M69Qom16rtroWXD3Ybtiws38gQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.5.0_1571141197865_0.07724758074500793"}, "_hasShrinkwrap": false, "publish_time": 1571141197995, "_cnpm_publish_time": 1571141197995, "_cnpmcore_publish_time": "2021-12-15T10:39:52.363Z"}, "2.4.1": {"name": "schema-utils", "version": "2.4.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "main": "dist/index.js", "types": "index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@types/json-schema": "^7.0.3", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^6.0.0", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.4.0", "eslint-config-prettier": "^6.3.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0"}, "keywords": ["webpack"], "gitHead": "338db8b732aeb247db012b4bfae2fddf17d3e57e", "_id": "schema-utils@2.4.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.11.3", "dist": {"shasum": "e89ade5d056dc8bcaca377574bb4a9c4e1b8be56", "size": 11037, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.4.1.tgz", "integrity": "sha512-RqYLpkPZX5Oc3fw/kHHHyP56fg5Y+XBpIpV8nCg0znIALfq3OH+Ea9Hfeac9BAMwG5IICltiZ0vxFvJQONfA5w=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.4.1_1569583505137_0.7742291363655027"}, "_hasShrinkwrap": false, "publish_time": 1569583505323, "_cnpm_publish_time": 1569583505323, "_cnpmcore_publish_time": "2021-12-15T10:39:52.549Z"}, "2.4.0": {"name": "schema-utils", "version": "2.4.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "main": "dist/index.js", "types": "index.d.ts", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@types/json-schema": "^7.0.3", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^6.0.0", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.4.0", "eslint-config-prettier": "^6.3.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0"}, "keywords": ["webpack"], "gitHead": "fe4be5dfa85e24606df87ad4087b25c30baa745d", "_id": "schema-utils@2.4.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.11.3", "dist": {"shasum": "44947c4da4d798a3080ec2343249f9d8e4231fd6", "size": 10685, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.4.0.tgz", "integrity": "sha512-VJEKOvjynJweKWFgxaikuP22zl9JwvmylH/cW1dIKZyp3DS1adBGaYPtZ6CdBSxtfP0LwQY1gNA4rIMJsnammQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sokra"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.4.0_1569524622275_0.30116992902704154"}, "_hasShrinkwrap": false, "publish_time": 1569524622469, "_cnpm_publish_time": 1569524622469, "_cnpmcore_publish_time": "2021-12-15T10:39:52.725Z"}, "2.3.0": {"name": "schema-utils", "version": "2.3.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^6.0.0", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.4.0", "eslint-config-prettier": "^6.3.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0"}, "keywords": ["webpack"], "gitHead": "ba01fe51c617e0a8b2a36298cf8f71fd8f5b6fef", "_id": "schema-utils@2.3.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.11.3", "dist": {"shasum": "592c836507db364139a6dea7f46563d66b688a03", "size": 10411, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.3.0.tgz", "integrity": "sha512-0NWOS/em29583dLzou07qWp5xoKCnvm5NiDQ+IkI/Q8xP6rm1SMvQrmHseH0RSbBe4tM+LJ7IeZTqb/dF9dTRQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.3.0_1569504786725_0.5259988689114976"}, "_hasShrinkwrap": false, "publish_time": 1569504787161, "_cnpm_publish_time": 1569504787161, "_cnpmcore_publish_time": "2021-12-15T10:39:52.925Z"}, "2.2.0": {"name": "schema-utils", "version": "2.2.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.10.2", "ajv-keywords": "^3.4.1"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.1", "del": "^5.1.0", "del-cli": "^2.0.0", "eslint": "^6.3.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "lint-staged": "^9.2.5", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0"}, "keywords": ["webpack"], "gitHead": "c9556505e2a73e41e1213e74fc258a27ed1473b7", "_id": "schema-utils@2.2.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.11.2", "dist": {"shasum": "48a065ce219e0cacf4631473159037b2c1ae82da", "size": 10308, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.2.0.tgz", "integrity": "sha512-5EwsCNhfFTZvUreQhx/4vVQpJ/lnCAkgoIHLhSpp4ZirE+4hzFvdJi0FMub6hxbFVBJYSpeVVmon+2e7uEGRrA=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.2.0_1567421854221_0.7354612062500321"}, "_hasShrinkwrap": false, "publish_time": 1567421854379, "_cnpm_publish_time": 1567421854379, "_cnpmcore_publish_time": "2021-12-15T10:39:53.184Z"}, "2.1.0": {"name": "schema-utils", "version": "2.1.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/defaults": "^5.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^5.0.0", "del-cli": "^2.0.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.0.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^9.2.0", "npm-run-all": "^4.1.5", "prettier": "^1.0.0", "standard-version": "^6.0.1"}, "keywords": ["webpack"], "gitHead": "08d8147f2008bdc6403acd45927ae5f1c65e3636", "_id": "schema-utils@2.1.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.10.2", "dist": {"shasum": "940363b6b1ec407800a22951bdcc23363c039393", "size": 9716, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.1.0.tgz", "integrity": "sha512-g6SViEZAfGNrToD82ZPUjq52KUPDYc+fN5+g6Euo5mLokl/9Yx14z0Cu4RR1m55HtBXejO0sBt+qw79axN+Fiw=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.1.0_1565190172002_0.9844907865540982"}, "_hasShrinkwrap": false, "publish_time": 1565190172138, "_cnpm_publish_time": 1565190172138, "_cnpmcore_publish_time": "2021-12-15T10:39:53.405Z"}, "2.0.1": {"name": "schema-utils", "version": "2.0.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/defaults": "^5.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^5.0.0", "del-cli": "^2.0.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.0.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^9.2.0", "npm-run-all": "^4.1.5", "prettier": "^1.0.0", "standard-version": "^6.0.1"}, "keywords": ["webpack"], "gitHead": "ebbbe2c6ab023083e726c3ba7f1bc49a6d642d8b", "_id": "schema-utils@2.0.1", "_nodeVersion": "10.15.2", "_npmVersion": "6.10.1", "dist": {"shasum": "1eec2e059556af841b7f3a83b61af13d7a3f9196", "size": 9606, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.0.1.tgz", "integrity": "sha512-HJFKJ4JixDpRur06QHwi8uu2kZbng318ahWEKgBjc0ZklcE4FDvmm2wghb448q0IRaABxIESt8vqPFvwgMB80A=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.0.1_1563466362051_0.013276362947875153"}, "_hasShrinkwrap": false, "publish_time": 1563466362173, "_cnpm_publish_time": 1563466362173, "_cnpmcore_publish_time": "2021-12-15T10:39:53.682Z"}, "2.0.0": {"name": "schema-utils", "version": "2.0.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "main": "dist/index.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@webpack-contrib/defaults": "^5.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^5.0.0", "del-cli": "^2.0.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.0.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^6.4.0", "lint-staged": "^9.2.0", "npm-run-all": "^4.1.5", "prettier": "^1.0.0", "standard-version": "^6.0.1"}, "keywords": ["webpack"], "gitHead": "692b4376aeff71b09997497060c2b967b6318ce6", "_id": "schema-utils@2.0.0", "_nodeVersion": "10.15.2", "_npmVersion": "6.10.1", "dist": {"shasum": "e5bc78a5bff1e771e566b52f6a523b345352a448", "size": 9547, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.0.0.tgz", "integrity": "sha512-4JfkJmuT78xkJAZrYivuu6RNfX57ul5u+jsfxwRAdWw5eE1qIY/i4go1A3zAdJlTwYXLbvWHWXVvoYu3PjGf9A=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_2.0.0_1563373009093_0.465262828175129"}, "_hasShrinkwrap": false, "publish_time": 1563373009234, "_cnpm_publish_time": 1563373009234, "_cnpmcore_publish_time": "2021-12-15T10:39:53.917Z"}, "1.0.0": {"name": "schema-utils", "version": "1.0.0", "description": "webpack Validation Utils", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0", "del-cli": "^1.0.0", "eslint": "^5.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "jest": "^21.0.0", "prettier": "^1.0.0", "standard-version": "^4.0.0"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "license": "MIT", "gitHead": "6ca2322d4c8c5818c4a8f71328ac88e3507ec4e8", "_id": "schema-utils@1.0.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "0b79a93204d7b600d4b2850d1f66c2a34951c770", "size": 3854, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_1.0.0_1533659472153_0.8504713655306841"}, "_hasShrinkwrap": false, "publish_time": 1533659472254, "_cnpm_publish_time": 1533659472254, "_cnpmcore_publish_time": "2021-12-15T10:39:54.064Z"}, "0.4.7": {"name": "schema-utils", "version": "0.4.7", "description": "webpack Validation Utils", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0", "del-cli": "^1.0.0", "eslint": "^5.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "jest": "^21.0.0", "prettier": "^1.0.0", "standard-version": "^4.0.0"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "license": "MIT", "gitHead": "ebc09b7784df233874a771b03b35b95613bcbad2", "_id": "schema-utils@0.4.7", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "ba74f597d2be2ea880131746ee17d0a093c68187", "size": 3661, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.7.tgz", "integrity": "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_0.4.7_1533644210040_0.27989860459207727"}, "_hasShrinkwrap": false, "publish_time": 1533644210127, "_cnpm_publish_time": 1533644210127, "_cnpmcore_publish_time": "2021-12-15T10:39:54.260Z"}, "0.4.6": {"name": "schema-utils", "version": "0.4.6", "description": "webpack Validation Utils", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0", "del-cli": "^1.0.0", "eslint": "^5.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "jest": "^22.0.0", "prettier": "^1.0.0", "standard-version": "^4.0.0"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "license": "MIT", "gitHead": "ce6626b794fd0bf33b28c84b5d8a7354fb9cb524", "_id": "schema-utils@0.4.6", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "dab4516a656310a964ca772bd3771819ba2b5cec", "size": 3584, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.6.tgz", "integrity": "sha512-+8HiCq9YzNM3S8vF7w1qsXK30H8dCc8EVduWGJvF0YzMhw7ehi/mLckZR9WfsPjfSnT6btpOL3jljExw8S4K5Q=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_0.4.6_1533575168399_0.9556367856367722"}, "_hasShrinkwrap": false, "publish_time": 1533575168539, "_cnpm_publish_time": 1533575168539, "_cnpmcore_publish_time": "2021-12-15T10:39:54.508Z"}, "0.4.5": {"name": "schema-utils", "version": "0.4.5", "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "description": "Webpack Schema Validation Utilities", "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint --cache src test", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "release:ci": "conventional-github-releaser -p angular", "release:validate": "commitlint --from=$(git describe --tags --abbrev=0) --to=$(git rev-parse HEAD)", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "ci:coverage": "npm run test:coverage -- --runInBand", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^5.2.8", "@commitlint/config-angular": "^5.1.1", "@webpack-contrib/eslint-config-webpack": "^2.0.2", "babel-cli": "^6.26.0", "babel-jest": "^22.2.2", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "conventional-github-releaser": "^2.0.0", "cross-env": "^5.1.3", "del": "^3.0.0", "del-cli": "^1.1.0", "eslint": "^4.17.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.6.0", "husky": "^0.14.3", "jest": "^22.2.2", "lint-staged": "^6.1.0", "memory-fs": "^0.4.1", "nsp": "^3.1.0", "pre-commit": "^1.2.2", "prettier": "^1.10.2", "standard-version": "^4.3.0", "webpack": "^3.11.0", "webpack-defaults": "^2.0.0-rc.4"}, "engines": {"node": ">= 4.8.0 || >= 6.9.0 || >= 8.9.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "1afd5807c328014616bbaadb5a91a785f24cbdfc", "_id": "schema-utils@0.4.5", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "21836f0608aac17b78f9e3e24daff14a5ca13a3e", "size": 4786, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.5.tgz", "integrity": "sha512-yYrjb9TX2k/J1Y5UNy3KYdZq10xhYcF8nMpAW6o3hy6Q8WSIEf9lJHG/ePnOBfziPM3fvQwfOwa13U/Fh8qTfA=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_0.4.5_1518529667219_0.7169207153910973"}, "_hasShrinkwrap": false, "publish_time": 1518529668167, "_cnpm_publish_time": 1518529668167, "_cnpmcore_publish_time": "2021-12-15T10:39:54.701Z"}, "0.4.4": {"name": "schema-utils", "version": "0.4.4", "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "description": "Webpack Schema Validation Utilities", "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint --cache src test", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "release:ci": "conventional-github-releaser -p angular", "release:validate": "commitlint --from=$(git describe --tags --abbrev=0) --to=$(git rev-parse HEAD)", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "ci:coverage": "npm run test:coverage -- --runInBand", "defaults": "webpack-defaults"}, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^5.2.8", "@commitlint/config-angular": "^5.1.1", "@webpack-contrib/eslint-config-webpack": "^2.0.2", "babel-cli": "^6.26.0", "babel-jest": "^22.2.2", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "conventional-github-releaser": "^2.0.0", "cross-env": "^5.1.3", "del": "^3.0.0", "del-cli": "^1.1.0", "eslint": "^4.17.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.6.0", "husky": "^0.14.3", "jest": "^22.2.2", "lint-staged": "^6.1.0", "memory-fs": "^0.4.1", "nsp": "^3.1.0", "pre-commit": "^1.2.2", "prettier": "^1.10.2", "standard-version": "^4.3.0", "webpack": "^3.11.0", "webpack-defaults": "^2.0.0-rc.4"}, "engines": {"node": ">= 4.8.0 || >= 6.9.0 || >= 8.9.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "homepage": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/schema", "repository": {"type": "git", "url": "git+https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/schema.git"}, "bugs": {"url": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/schema/issues"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "65b591017dab9335484dcd976b612759101bceec", "_id": "schema-utils@0.4.4", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "6a280d39dfefb1ef84fc2e0f6ac91c249beac7b0", "size": 4741, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.4.tgz", "integrity": "sha512-RtoiBCRDIEJxld0Fl3qUm3HEF8NM35nnBqhTmxzO/9bfWa6t2+zWcQdSWRC1UMW+dfPxAtpChl4JfUIUgy9M1A=="}, "maintainers": [{"email": "<EMAIL>", "name": "bebraw"}, {"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "micha<PERSON>.c<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_0.4.4_1518528821343_0.9739770599349351"}, "_hasShrinkwrap": false, "publish_time": 1518528821966, "_cnpm_publish_time": 1518528821966, "_cnpmcore_publish_time": "2021-12-15T10:39:54.904Z"}, "0.4.3": {"name": "schema-utils", "version": "0.4.3", "description": "Webpack Schema Validation Utilities", "license": "MIT", "main": "dist/cjs.js", "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "files": ["dist"], "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "security": "nsp check", "test": "cross-env jest", "test:watch": "cross-env jest --watch", "test:coverage": "cross-env jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "travis:coverage": "npm run test:coverage -- --runInBand", "appveyor:test": "npm run test", "prepublish": "npm run build", "release": "standard-version", "webpack-defaults": "webpack-defaults"}, "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^4.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "57f2820227dfce3ce91651fbe4d8cdfa3c4ae483", "_id": "schema-utils@0.4.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "e2a594d3395834d5e15da22b48be13517859458e", "size": 4547, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.3.tgz", "integrity": "sha512-sgv/iF/T4/SewJkaVpldKC4WjSkz0JsOh2eKtxCPpCO1oR05+7MOF+H476HVRbLArkgA7j5TRJJ4p2jdFkUGQQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "bebraw"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils-0.4.3.tgz_1513219740440_0.3938244723249227"}, "directories": {}, "publish_time": 1513219741330, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513219741330, "_cnpmcore_publish_time": "2021-12-15T10:39:55.105Z"}, "0.4.2": {"name": "schema-utils", "version": "0.4.2", "description": "Webpack Schema Validation Utilities", "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "security": "nsp check", "test": "cross-env JEST=true jest", "test:watch": "cross-env JEST=true jest --watch", "test:coverage": "cross-env JEST=true jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "travis:coverage": "npm run test:coverage -- --runInBand", "appveyor:test": "npm run test", "prepublish": "npm run build", "release": "standard-version", "webpack-defaults": "webpack-defaults"}, "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0", "chalk": "^2.3.0"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^4.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.8.1", "webpack-defaults": "^1.6.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "gitHead": "9a36941560cf33e5c4f0ddd202b8097974f55b30", "_id": "schema-utils@0.4.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "86d92aedf4dfc51c9b321ef16bd33a9417b122d0", "size": 4576, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.2.tgz", "integrity": "sha512-LCuuUj7L43TbSIqeERp+/Z2FH/NxSA48mqcWlGTSYUUKsevGafj2SpyaFVTxyWWFLkIAS3p7jDTLpNsrU7PXoA=="}, "maintainers": [{"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "bebraw"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils-0.4.2.tgz_1510258586488_0.7539700423367321"}, "directories": {}, "publish_time": 1510258587548, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510258587548, "_cnpmcore_publish_time": "2021-12-15T10:39:55.326Z"}, "0.4.1": {"name": "schema-utils", "version": "0.4.1", "description": "Webpack Schema Validation Utilities", "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "travis:coverage": "npm run test:coverage -- --runInBand", "appveyor:test": "npm run test", "prepublish": "npm run build", "release": "standard-version", "webpack-defaults": "webpack-defaults"}, "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^4.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack-defaults": "^1.6.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "license": "MIT", "gitHead": "8e242c827bcb4d9f76a722e555cf7d20a6bbaa16", "_id": "schema-utils@0.4.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "db935de4227f61700aca2d89682ada32b1b243e3", "size": 4362, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.1.tgz", "integrity": "sha512-VT7bSXSENu+y/a+U8zgIfYSmR8dd/GlnBGoSO5v1kOpLGEgmqJGbf/l2V0PMklVUcZEoGQ/gtKs/LxMnRxFW4A=="}, "maintainers": [{"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "bebraw"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils-0.4.1.tgz_1509702693032_0.869106677128002"}, "directories": {}, "publish_time": 1509702693117, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509702693117, "_cnpmcore_publish_time": "2021-12-15T10:39:55.528Z"}, "0.4.0": {"name": "schema-utils", "version": "0.4.0", "description": "Webpack Schema Validation Utilities", "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "travis:coverage": "npm run test:coverage -- --runInBand", "appveyor:test": "npm run test", "prepublish": "npm run build", "release": "standard-version", "webpack-defaults": "webpack-defaults"}, "dependencies": {"ajv": "^5.0.0", "ajv-keywords": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "cross-env": "^5.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^4.0.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack-defaults": "^1.6.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "keywords": ["webpack", "webpack-plugin", "schema-utils", "loader"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "license": "MIT", "gitHead": "0cbab06711c55b16279fe874871a4ceb553a2320", "_id": "schema-utils@0.4.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "cf98d870a5f74e7526211f6d38caf0d250366688", "size": 4266, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.4.0.tgz", "integrity": "sha512-2QYsHbnLL40gWxhbAfOWa4hZKc9UcyV18/c/RIX5w3pa3BU5//SU4jnOQ6cPsCrwJl+NGZoaCvMXmXgUMYX0fw=="}, "maintainers": [{"email": "<EMAIL>", "name": "d3viant0ne"}, {"email": "<EMAIL>", "name": "bebraw"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils-0.4.0.tgz_1509387709032_0.2484780631493777"}, "directories": {}, "publish_time": 1509387710029, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509387710029, "_cnpmcore_publish_time": "2021-12-15T10:39:55.696Z"}, "0.3.0": {"name": "schema-utils", "version": "0.3.0", "description": "Webpack Schema Validation Utilities", "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "scripts": {"start": "yarn run build -- -w", "prebuild": "yarn run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test", "webpack-defaults": "webpack-defaults", "prepublish": "yarn run build", "release": "yarn run standard-version"}, "dependencies": {"ajv": "^5.0.0"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-jest": "^19.0.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.4.0", "babel-preset-webpack": "^1.0.0", "codecov": "^2.0.1", "cross-env": "^4.0.0", "del-cli": "^0.2.1", "eslint": "^3.19.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "jest": "^19.0.2", "lint-staged": "^3.4.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack-defaults": "^0.4.5"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "keywords": ["webpack", "plugin", "es2015"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}, {"name": "<PERSON>", "email": "@micha<PERSON>-c<PERSON><PERSON><PERSON>"}], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "license": "MIT", "gitHead": "96525dd9fd5c33056cbafe5680c5cdd10a994686", "_id": "schema-utils@0.3.0", "_shasum": "f5877222ce3e931edae039f17eb3716e7137f8cf", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "f5877222ce3e931edae039f17eb3716e7137f8cf", "size": 4065, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.3.0.tgz", "integrity": "sha512-QaVYBaD9U8scJw2EBWnCBY+LJ0AD+/2edTaigDs0XLDLBfJmSUK9KGqktg1rb32U3z4j/XwvFwHHH1YfbYFd7Q=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/schema-utils-0.3.0.tgz_1493490045779_0.2797495676204562"}, "directories": {}, "publish_time": 1493490047722, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493490047722, "_cnpmcore_publish_time": "2021-12-15T10:39:55.881Z"}, "0.2.1": {"name": "schema-utils", "version": "0.2.1", "description": "Webpack Schema Validation Utilities", "main": "dist/index.js", "files": ["dist", "lib", "src", ".babelrc"], "scripts": {"prebuild": "yarn run clean:dist", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "start": "yarn run serve:dev src", "serve:dev": "nodemon $2 --exec babel-node", "lint": "eslint --cache src test", "security": "nsp check", "test": "jest", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:test": "yarn run test", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "webpack-defaults": "webpack-defaults", "lint-staged": "lint-staged", "postinstall": "node lib/post_install.js", "prepublish": "yarn run build", "release": "yarn run standard-version"}, "dependencies": {"ajv": "^4.11.2"}, "devDependencies": {"babel-cli": "^6.22.2", "babel-jest": "^18.0.0", "babel-polyfill": "^6.22.0", "babel-preset-env": "^1.2.1", "babel-preset-webpack": "^1.0.0", "chai": "^3.5.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "del-cli": "^0.2.1", "eslint": "^3.14.1", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.2.0", "greenkeeper-postpublish": "^1.0.1", "jest": "^18.1.0", "lint-staged": "^3.3.1", "nodemon": "^1.11.0", "nsp": "^2.6.2", "pre-commit": "^1.2.2", "shx": "^0.2.2", "standard-version": "^4.0.0", "webpack-defaults": "^0.2.1"}, "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "keywords": ["webpack", "plugin", "es2015"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}], "license": "MIT", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "7152276042d2c5a106cdf9b5877357f910ac4c00", "_id": "schema-utils@0.2.1", "_shasum": "f49634960899ea5315e80d9a540c2c19dfb2cff8", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.1", "_npmUser": {"name": "bebraw", "email": "<EMAIL>"}, "dist": {"shasum": "f49634960899ea5315e80d9a540c2c19dfb2cff8", "size": 5115, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.2.1.tgz", "integrity": "sha512-3Lru850BgT44fM7gWF2dc+EfLzOHIPkQusHRsTu42r9ccP8gJc6bNr8pDB2O7vwV5J24mfA5MfRlmm0SRZS/nA=="}, "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "d3viant0ne", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/schema-utils-0.2.1.tgz_1489388695917_0.07002403354272246"}, "directories": {}, "publish_time": 1489388696561, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489388696561, "_cnpmcore_publish_time": "2021-12-15T10:39:56.132Z", "hasInstallScript": true}, "0.1.0": {"name": "schema-utils", "version": "0.1.0", "description": "Webpack Schema Validation Utilities", "main": "dist/index.js", "files": ["dist"], "scripts": {"prebuild": "yarn run clean:dist", "build": "cross-env NODE_ENV=production babel -s true src -d dist --ignore 'src/**/*.test.js'", "clean:dist": "del-cli dist", "start": "yarn run serve:dev src", "serve:dev": "nodemon $2 --exec babel-node", "lint": "eslint --cache src test", "security": "nsp check", "test": "jest", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:test": "yarn run test", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "webpack-defaults": "webpack-defaults", "lint-staged": "lint-staged", "prepublish": "yarn run build", "release": "yarn run standard-version"}, "dependencies": {"ajv": "^4.11.2"}, "devDependencies": {"babel-cli": "^6.22.2", "babel-jest": "^18.0.0", "babel-polyfill": "^6.22.0", "babel-preset-env": "^1.2.1", "babel-preset-webpack": "^1.0.0", "chai": "^3.5.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "del-cli": "^0.2.1", "eslint": "^3.14.1", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.2.0", "greenkeeper-postpublish": "^1.0.1", "jest": "^18.1.0", "lint-staged": "^3.3.1", "nodemon": "^1.11.0", "nsp": "^2.6.2", "pre-commit": "^1.2.2", "shx": "^0.2.2", "standard-version": "^4.0.0", "webpack-defaults": "^0.2.1"}, "engines": {"node": ">=4.3 <5.0.0 || >=5.10"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "keywords": ["webpack", "plugin", "es2015"], "author": {"name": "Webpack Contrib", "url": "https://github.com/webpack-contrib"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "@bebraw"}, {"name": "<PERSON>", "email": "@d3viant0ne"}], "license": "MIT", "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "gitHead": "26f041e0b114cb91382e26c569b7badfe6edc262", "_id": "schema-utils@0.1.0", "_shasum": "886490266fafde65598a77eef1519ab993196e47", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "d3viant0ne", "email": "<EMAIL>"}, "dist": {"shasum": "886490266fafde65598a77eef1519ab993196e47", "size": 3448, "noattachment": false, "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.1.0.tgz", "integrity": "sha512-s6d3k7zTUtx/FqbdInFYVWqsM2r/qc4qz0oxKdPyP5Tu9OTkeraqsAcPujJ8yN3wo1kPz4CNWKRLrqwfaatbIw=="}, "maintainers": [{"name": "d3viant0ne", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/schema-utils-0.1.0.tgz_1488866487592_0.1796807344071567"}, "directories": {}, "publish_time": 1488866488207, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488866488207, "_cnpmcore_publish_time": "2021-12-15T10:39:56.348Z"}, "4.0.1": {"name": "schema-utils", "version": "4.0.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^16.1.0", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^12.3.3", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.5.5", "webpack": "^5.68.0"}, "keywords": ["webpack"], "gitHead": "490d60a198ce595f7846fe87c3beec399d0aae5b", "_id": "schema-utils@4.0.1", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-lELhBAAly9NowEsX0yZBlw9ahZG+sK/1RJ21EpzdYHKEs13Vku3LJ+MIPhh4sMs0oCCeufZQEQbMekiA4vuVIQ==", "shasum": "eb2d042df8b01f4b5c276a2dfd41ba0faab72e8d", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.0.1.tgz", "fileCount": 15, "unpackedSize": 67278, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCebib9i2NX3pRNkHarEweuIu4bRoMTz4KvX+9rCXs3zwIgPDuBo+5qSCncU5PnXVnh2gwgN0Eizib3xY7XNmOjhMw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOrrSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHRg/9GTiMVvg0hRG1yzgvURQDhUeTNGCMDn761tH76vRThhjug6CH\r\nJSHDtLfFyt1yjAx13MoZ80pRdfwyt5nKgUhBnFVP572HsQisCVJQY/K28Hyg\r\nMwYVcBr8y8Psg/uwc3fk4VbLm1c3Iv21jC1vlE58GnYI67c5e9kPml6oer+6\r\nRob/anlMntBRm9x6ibRXBCP9AT54QQ7ost3voEsJnrLITauU61neox1S26c7\r\nJzO3OAqSNon28drzlLLluzZ8RHz6sP4X5HvFMEpGnFD/54zlXJFlyZ7jSvIf\r\n+H8TAEkRSrEgUU8j7wQBA2EZ+Ui+FswwZH5uvjijuXW7VI6OLmG89cKdUzDV\r\ne15lP9JmUhgfNRYMJAOqAhxgz+swDKxj3OgUPnWWlOq+pKUsIv2ONCz55AUk\r\nhjcpgAh+bzrhxSO0VIv+xqJ6gh0ogM/wkVVvH3OGcHPLGfJdsdN+t+87K1Xy\r\n5jz1i99JEtWlg/KhKZu2tZCrqvlOAgvRaV8ttnafiSsJDX6RxCLL2j7Agvps\r\nZCi8NnMDYdMcZZTyeGHBfKDBB9uij82Eq4FznLfaygMmUyTu6Le3DQ9HUfJ+\r\naFTmFQSMSr8Dry6ghjTylZ3SjwQxTIIuE0N1OAIel1jr/4SEBb0ie/QmlNSk\r\nHGneDZzfKjl5F4vPXRXDi3YK2hij35kA8No=\r\n=AjRJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 14556}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_4.0.1_1681570513924_0.7604472921397365"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-15T14:55:14.105Z", "publish_time": 1681570514105}, "3.1.2": {"name": "schema-utils", "version": "3.1.2", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.31.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "typescript": "^4.3.5", "webpack": "^5.45.1"}, "keywords": ["webpack"], "readmeFilename": "README.md", "gitHead": "d350b63e328b416f88ba1f31e3d9aff2550deea4", "_id": "schema-utils@3.1.2", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-pvjEHOgWc9OWA/f/DE3ohBWTD6EleVLf7iFUkoSwAxttdBhB9QUebQgxER2kWueOvRJXPHNnyrvvh9eZINB8Eg==", "shasum": "36c10abca6f7577aeae136c804b0c741edeadc99", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.2.tgz", "fileCount": 16, "unpackedSize": 80291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrLMMgL/dQkda27Q+OrZk+Qk+8fVUUkOr2FysWTzveGAIgIt5pXD8Ze3GirKeEeOn9KU203KRLxahGs6W4dUWoVKg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOsDzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXqQ/+NB/Ts53e8lvnpXVs1g1V00Btb4B7DRg0gZM4bylSqdZum5CV\r\nz3eUK189Bcf2KVpjv0QL7t5HGRHH5kvd3vDHPL2tKDE8/2fxLX0sg73lgpjW\r\nLOegni7I4ZBWvU4oLuB9w6hfEx1+XG+Ia4kpC1AS5V7tNoZC7UjiumxuS0lm\r\nE+erDGqm0VHB3xr+GdjYvfuEf7MxOQtpsTQ0/7gg1OXOC5FQLvBfaeBBx4iL\r\nq96E/cIQEKKkBzgzmtdCMaNNP0xWplkZ9amk2y79c0gP8eWrxfgMnnNOLZ6g\r\n7T2FtnHnAaUyY7bTIAdD8VYEy9mhuPH+ccj2/Lezb2zTr6vvDKRI3aKFO9tz\r\nKH0l36RAFI0auJwkjNbA75jTl+hDuHEywwmoYe4dAb9mlYX2mrxVtjoVCq4Z\r\nMLDPDzCTOV+jlrgqVhacBAXj/CwbbLQmrWQJgC8g8n1kGh6fctr947ZXCjYq\r\ny7HRO8EskM/NLMuPqjv1op8K5aYiYq/HCJFOxVp/fHfuejoY2qfRBih8PrOV\r\nMERLhkzomjDj5Bfy/XUgnFLL0GCI1MShzvnkFjLRaBtgBCfnJk9MXWvx3V5r\r\n53nuu7WLoTowe7mP7leVM4SgZwUdjX/BOlLrSXY733ytGJQM37cxTRgRbjkh\r\nJ7zBYHoogLU9WAl8GrjfG7L9t3MAy4mX2UA=\r\n=Ltuv\r\n-----END PGP SIGNATURE-----\r\n", "size": 17617}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_3.1.2_1681572083530_0.7132215357860934"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-15T15:21:23.762Z", "publish_time": 1681572083762}, "4.1.0": {"name": "schema-utils", "version": "4.1.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.9.5", "webpack": "^5.68.0"}, "keywords": ["webpack"], "gitHead": "3731a59e36211cae23c2dbf6cf78642d6e8a28f9", "_id": "schema-utils@4.1.0", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-Jw+GZVbP5IggB2WAn6UHI02LBwGmsIeYN/lNbSMZyDziQ7jmtAUrqKqDja+W89YHVs+KL/3IkIMltAklqB1vAw==", "shasum": "4cff1e434c12ed39502378b9a3e24787b37df41d", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.1.0.tgz", "fileCount": 18, "unpackedSize": 82717, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB0phsc7FOdQFobn89w8SjxbVMZ3H410gveWAYgflfC4AiEAmqRfHZDiu7povSx3XWsryazYnPR+m+xZT0dPnTOZTA8="}], "size": 18222}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_4.1.0_1686176540865_0.20236625049838053"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-07T22:22:21.141Z", "publish_time": 1686176541141, "_source_registry_name": "default"}, "3.2.0": {"name": "schema-utils", "version": "3.2.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.31.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "typescript": "^4.3.5", "webpack": "^5.45.1"}, "keywords": ["webpack"], "readmeFilename": "README.md", "gitHead": "dd3bc4ff9b92537308c16ad4e14219b0d8027b18", "_id": "schema-utils@3.2.0", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-0zTyLGyDJYd/MBxG1AhJkKa6fpEBds4OQO2ut0w7OYG+ZGhGea09lijvzsqegYSik88zc7cUtIlnnO+/BvD6gQ==", "shasum": "7dff4881064a4f22c09f0c6a1457feb820fd0636", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.2.0.tgz", "fileCount": 18, "unpackedSize": 83407, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUS02poQSFqNk71EF4f2ta/nLWc0JSaUkGuiTFuwtj8QIhANaYg+Ai3Gdgj+X3l5t4hX3V5CZi9+/p8J6yMlCwIvgc"}], "size": 18352}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_3.2.0_1686176711175_0.5898371053748572"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-07T22:25:11.393Z", "publish_time": 1686176711393, "_source_registry_name": "default"}, "4.2.0": {"name": "schema-utils", "version": "4.2.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.9.5", "webpack": "^5.68.0"}, "keywords": ["webpack"], "gitHead": "dcd6df12c191ac1b6cf5eda39ba5736c35f01ccb", "_id": "schema-utils@4.2.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==", "shasum": "70d7c93e153a273a805801882ebd3bff20d89c8b", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.2.0.tgz", "fileCount": 19, "unpackedSize": 71643, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC98uU2itlzCCiOAt59PwWUBWeNBOTL8AEFIQBUsU9vwQIhAN2DslC/mDy79rmHL39A14THr52LfnOQ35zXC2nVcl0/"}], "size": 15713}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_4.2.0_1686752716052_0.04255495863153946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-14T14:25:16.297Z", "publish_time": 1686752716297, "_source_registry_name": "default"}, "3.3.0": {"name": "schema-utils", "version": "3.3.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.6", "@babel/preset-env": "^7.14.7", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.0.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.31.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^6.0.0", "jest": "^27.0.6", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "standard-version": "^9.3.1", "typescript": "^4.3.5", "webpack": "^5.45.1"}, "keywords": ["webpack"], "readmeFilename": "README.md", "gitHead": "4afb750ccb668397499f4b01f8bd4dcb4cdba803", "_id": "schema-utils@3.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==", "shasum": "f50a88877c3c01652a15b622ae9e9795df7a60fe", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz", "fileCount": 18, "unpackedSize": 84831, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAkxtODDDEpirEdsp3gHLnB3SflMwz0bXAGGrEeodXoqAiEA3Xp6jH1p5ErOkSsRNPBeOi47Yoh5JLoz6K34aHDORKQ="}], "size": 18361}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/schema-utils_3.3.0_1686754363552_0.9904057589760691"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-14T14:52:43.727Z", "publish_time": 1686754363727, "_source_registry_name": "default"}, "4.3.0": {"name": "schema-utils", "version": "4.3.0", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.9.5", "webpack": "^5.97.1"}, "keywords": ["webpack"], "gitHead": "2fd7538e54d84ec5c0b01ce5827c00fc8344e5f7", "_id": "schema-utils@4.3.0", "_nodeVersion": "22.11.0", "_npmVersion": "9.6.0", "dist": {"integrity": "sha512-Gf9qqc58SpCA/xdziiHz35F4GNIWYWZrEshUc/G/r5BnLph6xpKuLeoJoQuj5WfBIx/eQLf+hmVPYHaxJu7V2g==", "shasum": "3b669f04f71ff2dfb5aba7ce2d5a9d79b35622c0", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.3.0.tgz", "fileCount": 21, "unpackedSize": 76517, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPp9xlIdSNi418jvR6QS5uBqStr6+PBSj9tIx4KH7e3QIhAISpHDQ1S0a3a+jSkwhRPvDYJvzBS1cuHj6LEW1Vv2Iu"}], "size": 17058}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/schema-utils_4.3.0_1733940805823_0.5933017449186149"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-11T18:13:26.041Z", "publish_time": 1733940806041, "_source_registry_name": "default"}, "4.3.1": {"name": "schema-utils", "version": "4.3.1", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.9.5", "webpack": "^5.97.1"}, "keywords": ["webpack"], "_id": "schema-utils@4.3.1", "gitHead": "1478499e3f2d404355b155690f46ab9b33daa0e6", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-jjlZ7UknkyQxGnHF1w8wDgWfdtnW0hBX7tmDp04zBwDBZ/6tPJI1+RWfBHGMA4+0nAjGptp+eDpIYP6mldJbqg==", "shasum": "0fe982d6bfaf404e77f02ed433d429d0f0ad0b6c", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.3.1.tgz", "fileCount": 21, "unpackedSize": 77157, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIB5kJE85FGJ1rpA1+QGwPWBpB4BLGbryuJ2VraigppwfAiEAno5c11QRx8Cy+PEALiYO/e6RHy+0qz344jC5FjFEIys="}], "size": 17108}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/schema-utils_4.3.1_1745327021523_0.8234706197376027"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-22T13:03:41.729Z", "publish_time": 1745327021729, "_source_registry_name": "default"}, "4.3.2": {"name": "schema-utils", "version": "4.3.2", "description": "webpack Validation Utils", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "homepage": "https://github.com/webpack/schema-utils", "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "declarations/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist declarations", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all lint:js lint:types fmt:check", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build && husky install", "release": "standard-version"}, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^16.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.4.6", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^27.4.7", "lint-staged": "^13.2.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "standard-version": "^9.3.2", "typescript": "^4.9.5", "webpack": "^5.97.1"}, "keywords": ["webpack"], "_id": "schema-utils@4.3.2", "gitHead": "2a92a6a229a5a63f6c9eecf5cb9d78add52d59a9", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==", "shasum": "0c10878bf4a73fd2b1dfd14b9462b26788c806ae", "tarball": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.3.2.tgz", "fileCount": 21, "unpackedSize": 77279, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEId7pZd6rWhJTJmvB+ttZUgdBrp7NNPk6cQkear3oQYAiEAtMPKyOCO/iTzT8oQSlmdW2La0Mex1xGgm7Km2GVLFkw="}], "size": 17129}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/schema-utils_4.3.2_1745344065064_0.9964303888570767"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-22T17:47:45.257Z", "publish_time": 1745344065257, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "homepage": "https://github.com/webpack/schema-utils", "keywords": ["webpack"], "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "_source_registry_name": "default"}