{"_attachments": {}, "_id": "normalize-wheel-es", "_rev": "1013290-61f298c145d527a190d04794", "author": {"name": "<PERSON><PERSON>"}, "description": "Mouse wheel normalization across multiple multiple browsers.", "dist-tags": {"latest": "1.2.0"}, "license": "BSD-3-<PERSON><PERSON>", "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "name": "normalize-wheel-es", "readme": "# Normalize Wheel\nMouse wheel normalization across multiple multiple browsers.\n\n## Original source\nThis code is extracted and from Facebook's [Fixed Data Table](https://github.com/facebook/fixed-data-table). Apart from import fixes, the code is unmodified.\n\n## Usage\nJust add it as an dependency in npm.\n\nYou can use it as follows:\n\n```js\nimport normalizeWheel from 'normalize-wheel-es';\n\ndocument.addEventListener('mousewheel', function (event) {\n    const normalized = normalizeWheel(event);\n\n    console.log(normalized.pixelX, normalized.pixelY);\n});\n```\n\n## License\nSee the `LICENSE` file.\n", "time": {"created": "2022-01-27T13:06:09.949Z", "modified": "2023-07-28T04:00:55.355Z", "1.1.1": "2021-10-31T10:50:13.330Z", "1.1.0": "2021-09-26T18:39:14.457Z", "1.0.2": "2021-09-26T18:32:59.290Z", "1.0.1": "2021-09-12T19:49:23.262Z", "1.0.0": "2021-09-12T19:43:42.071Z", "1.1.2": "2022-03-25T06:01:30.101Z", "1.1.3": "2022-07-19T01:17:04.477Z", "1.2.0": "2022-07-19T01:42:36.252Z"}, "versions": {"1.1.1": {"name": "normalize-wheel-es", "version": "1.1.1", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "scripts": {"build": "tsup", "release": "standard-version"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "author": {"name": "<PERSON><PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "devDependencies": {"standard-version": "^9.3.1", "tsup": "^5.2.1"}, "gitHead": "998ff25021aee5b7627a31803d8faa32fd0ebca8", "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "_id": "normalize-wheel-es@1.1.1", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.0", "dist": {"shasum": "a8096db6a56f94332d884fd8ebeda88f2fc79569", "size": 37276, "noattachment": false, "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.1.1.tgz", "integrity": "sha512-157VNH4CngrcsvF8xOVOe22cwniIR3nxSltdctvQeHZj8JttEeOXffK28jucWfWBXs0QNetAumjc1GiInnwX4w=="}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.1.1_1635677413153_0.8574163373600248"}, "_hasShrinkwrap": false, "publish_time": 1635677413330, "_cnpm_publish_time": 1635677413330, "_cnpmcore_publish_time": "2021-12-15T16:53:31.463Z"}, "1.1.0": {"name": "normalize-wheel-es", "version": "1.1.0", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/esm/index.js", "types": "index.d.ts", "scripts": {"build": "tsup", "release": "standard-version"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "author": {"name": "<PERSON><PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"standard-version": "^9.3.1", "tsup": "^5.2.1"}, "gitHead": "865920e55feffa867c1f7c9ed83551763bda8529", "_id": "normalize-wheel-es@1.1.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.20.1", "dist": {"shasum": "db017af1dd5d4c6222c07ae38bc224049d25861e", "size": 36133, "noattachment": false, "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.1.0.tgz", "integrity": "sha512-gkcE5xzp8WkSGgu2HItXePGyh3qDOetgPYg0RnjclOIaWTCMB75NTrk0t6KVlbm6ShSikV3ykBFZMiR9GDkvkA=="}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.1.0_1632681554299_0.18328568257496358"}, "_hasShrinkwrap": false, "publish_time": 1632681554457, "_cnpm_publish_time": 1632681554457, "_cnpmcore_publish_time": "2021-12-15T16:53:31.821Z"}, "1.0.2": {"name": "normalize-wheel-es", "version": "1.0.2", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "scripts": {"build": "tsup", "release": "standard-version"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "author": {"name": "<PERSON><PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"standard-version": "^9.3.1", "tsup": "^5.2.1"}, "gitHead": "61fe61417bdcfb056896f4d82ce3d0fbab4ddf38", "_id": "normalize-wheel-es@1.0.2", "_nodeVersion": "16.9.1", "_npmVersion": "7.20.1", "dist": {"shasum": "a3f5c286043fe68efa72ca929846eb3256a23bad", "size": 37096, "noattachment": false, "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.0.2.tgz", "integrity": "sha512-w+izQA0++4ATzax041MbebsE5xlkDbW7dLPSYWOw75+SOK1AXdMVxda7KDyA2R/54EeAjvH9SPMtRAhB0i6teQ=="}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.0.2_1632681179066_0.14504481581606776"}, "_hasShrinkwrap": false, "publish_time": 1632681179290, "_cnpm_publish_time": 1632681179290, "_cnpmcore_publish_time": "2021-12-15T16:53:32.226Z"}, "1.0.1": {"name": "normalize-wheel-es", "version": "1.0.1", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "index.js", "scripts": {}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "author": {"name": "<PERSON><PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "71cf93067100b0bd0875a3e60510bdbbb8718002", "_id": "normalize-wheel-es@1.0.1", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"shasum": "677952bf7720ede5c755f675bdaa007ab7e422f9", "size": 7547, "noattachment": false, "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.0.1.tgz", "integrity": "sha512-7gLH4NrMSmPbiKxFOeyguqevyUo96L6poAR/0NBS7nDUewsnDtt2aUzV5a42xPIpNPHh0boEfsJNPJ2KmeQPWQ=="}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.0.1_1631476163121_0.31944192194198884"}, "_hasShrinkwrap": false, "publish_time": 1631476163262, "_cnpm_publish_time": 1631476163262, "_cnpmcore_publish_time": "2021-12-15T16:53:32.436Z"}, "1.0.0": {"name": "normalize-wheel-es", "version": "1.0.0", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "index.js", "scripts": {}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "author": {"name": "<PERSON><PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "b7b4c0e82b96900fd91a8bb2c4b2f74dd8d4c187", "_id": "normalize-wheel-es@1.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"shasum": "eb1894b77f7d24db91c3b3c3701b639f71e65f42", "size": 7548, "noattachment": false, "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.0.0.tgz", "integrity": "sha512-ScTzb4NNqyGDn/On7E9UB8MZ9KgoEgL48HEHgVUG3pZI1A/4WG5bnLY2dKAY2IBH6f1DrrAuRuadMWvIxLJCIA=="}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.0.0_1631475821922_0.01618625576688615"}, "_hasShrinkwrap": false, "publish_time": 1631475822071, "_cnpm_publish_time": 1631475822071, "_cnpmcore_publish_time": "2021-12-15T16:53:32.653Z"}, "1.1.2": {"name": "normalize-wheel-es", "version": "1.1.2", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "author": {"name": "<PERSON><PERSON>"}, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "scripts": {"build": "tsup", "release": "standard-version"}, "devDependencies": {"@swc/core": "^1.2.160", "standard-version": "9.3.2", "tsup": "5.12.1"}, "gitHead": "482794f85d21f2ecf3741e9c370a0db8c0393644", "_id": "normalize-wheel-es@1.1.2", "_nodeVersion": "17.7.2", "_npmVersion": "8.5.2", "dist": {"integrity": "sha512-scX83plWJXYH1J4+BhAuIHadROzxX0UBF3+HuZNY2Ks8BciE7tSTQ+5JhTsvzjaO0/EJdm4JBGrfObKxFf3Png==", "shasum": "285e43676a62d687bf145e33452ea6be435162d0", "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.1.2.tgz", "fileCount": 9, "unpackedSize": 81042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPVq6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMeQ//ZzAsGu1P4HbK15VjsOAbyofw0BAzLQu3UaEFCpOPTSvAyNh1\r\nDxh/wSdKXd9Sts0dOV/vJAuRh6Si8gIjKhSh9jWHgOE/9EtjVuHys3GzNbNv\r\n5boveOQUXCPY0IRr4tnr5bWmPw7wYLzFoupjv9f8E8LfhsKN8Ft5eY8INm+x\r\nQmIb0gpXbB7HGmk3fFmEDCE26r/EbcXG9vTozzNoba+PDF24cPYx9vl1tN5W\r\nsa12uTWASmxKvdPuZYozNPpQPhNxqjfwR3gUfiT+UcVzbVAV2axN3laaXvQ8\r\noPiflYuZ12Boi49ubXFz37ISiovHMFGf8hjLXeRHJiut0fTa7s4gSsIyyMmA\r\nqPJDWT4mRt/X6CjP/xyRfC+ZNqwKBSWXOLFeN2ZYcKCzIIW+oiVjsR1FMh11\r\niW2VRFMjtZqmfKsoEhHOYzFB/btJNIA78AsY78CRRyUYbrUPuowEStzezD/J\r\nNMYE23nx2KkVCLezMvkI+BIzRb/g6k5lZoO+EweNNwD0oNGbBROxQ1rpqKo3\r\nrU0Fs7CapuxBrmcjVkWvRSoZj0ASjOoNCt9Lv4bt2YujYL697udZ9ByWNyQO\r\nkfZ74JQf+UvBsPLcsDtRz8NOxFUMIm2NKR3SLBOEcpURhuPKyoELvZFktkWw\r\nTEDfMSiXtmwplO+LL2Q89oDCMzRJEJiXeRI=\r\n=zCnu\r\n-----END PGP SIGNATURE-----\r\n", "size": 13778}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.1.2_1648188089926_0.12980399920823937"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-25T06:01:34.238Z"}, "1.1.3": {"name": "normalize-wheel-es", "version": "1.1.3", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "author": {"name": "<PERSON><PERSON>"}, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "devDependencies": {"@swc/core": "^1.2.218", "bumpp": "^8.2.1", "tsup": "^6.1.3"}, "scripts": {"build": "tsup", "release": "bumpp"}, "_id": "normalize-wheel-es@1.1.3", "_integrity": "sha512-PdSI8WY4eY8CU2CzCCe5KOaee8uEFdDOFbFYkO5W0Yuu4Qg8mEp2rpCBDYcju+Ywx+krjGvcYg3tAUvLUXtbsg==", "_resolved": "/private/var/folders/d9/9drlr87n4j904th57x6955l40000gn/T/fed462f4254983b56f1187d8efed1478/normalize-wheel-es-1.1.3.tgz", "_from": "file:normalize-wheel-es-1.1.3.tgz", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-PdSI8WY4eY8CU2CzCCe5KOaee8uEFdDOFbFYkO5W0Yuu4Qg8mEp2rpCBDYcju+Ywx+krjGvcYg3tAUvLUXtbsg==", "shasum": "6bd39a9f4e5fa46105fe5f2f57246d0a3dad1066", "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.1.3.tgz", "fileCount": 10, "unpackedSize": 78095, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHpcfrOp3h0H94OYhVUHsYbLDUYgmxyqZnWpNY8IfnnkAiB/0noNLM7uyH5BCc2giE/o78OlIT8EMzvCfqBG0NruRA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1gYQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqttg/7BdxEqHAy95UVE/wWMBEgGKh2LGOL81j6ZdGNN2xhGrdWxyMP\r\nHwXBfgqOmWgoMg7+MxWZHoJzuhezxZQrIKPbQO2dYDbmxJW4J8AkUceD2Y+P\r\nf3zphWDeyD9tDTRficHUbKtgpX9CofVrlDrZbhTE/54kCEYFfXmzPK6UUByP\r\nY9ZiRYuiZ817flIqWOWCkdcbrjHZHe3MLQMlxzIL/hmxvusqf11fQnvUDYEb\r\nOX4s3jHmc1hoY270jXQBLwPR/Tsxjx51Z1TnM1qkjI80OhR2HZ1/Z8XHmdoC\r\nzUx37w+IXlpbVq4+Yv0WfIAGX+dQDNqg6q3KxccgNl/y8hQyw50iv6Kr39FT\r\n3ri+/YYDdp0F3cFZ9QVlfkqmGCRyRK0tjMWD4UE3qqAVT/qmiC8VzjseePce\r\n+pivh9va92TT+q7ui+ub6PYalgHH8RzClO7D9xbFuMmkXNPkXCuySoSSsDRE\r\nx5+OcIPd5scdrwQE3EgIQZhWy6U8BRVJsSrjPsaQxzw0A/8eJ7EcMWZ6QhqT\r\n5HEYkYdgufIg9NaKtxyCTJUqgrkXHQN+pi6nga29ip+GzIcTo25j/BVr4yJY\r\n++g39AvZBqd+zDYgQ/v9GcFM5qKUvxm+OFpEw+fuG21tEDsRbuja8OhMylhy\r\nxMhValHAF/dbALl7277xSluFvxzMqIgOsY4=\r\n=0ob0\r\n-----END PGP SIGNATURE-----\r\n", "size": 13578}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.1.3_1658193424232_0.5907828176046346"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-19T01:17:19.083Z"}, "1.2.0": {"name": "normalize-wheel-es", "version": "1.2.0", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "author": {"name": "<PERSON><PERSON>"}, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "devDependencies": {"@swc/core": "^1.2.218", "bumpp": "^8.2.1", "tsup": "^6.1.3"}, "scripts": {"build": "tsup", "release": "bumpp"}, "_id": "normalize-wheel-es@1.2.0", "_integrity": "sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==", "_resolved": "/private/var/folders/d9/9drlr87n4j904th57x6955l40000gn/T/7713cb5b26bc8f7694a7078623801fbf/normalize-wheel-es-1.2.0.tgz", "_from": "file:normalize-wheel-es-1.2.0.tgz", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==", "shasum": "0fa2593d619f7245a541652619105ab076acf09e", "tarball": "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz", "fileCount": 10, "unpackedSize": 78114, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpC6cfJKKFJggwYkldxbVZ6lcK+6qgdLVGrlzhzUJQaAiA2cuM6XNAxVl30OpfeDV0hT3n6Gc28R8kEEXrtx8vLYw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1gwMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpewg/9FSSM6myJyRFPUYtoWcDXhfLflBQZtRBl7aS/o92CDJqOJllu\r\n8o4ok5NwAhYa5jU0UDSEb+MeaXHeum1V19JHwwfQcfkFRPPAzmdNLprIe1lO\r\nbr5pGnl6IPblqYwKhZgZwTLHgD6+UJTDffMYDsSietwWQcPsFFlPWV7fgdyo\r\nl5Wa/WY0PcHOqfQXOUw/1Cfgi4UPyP08J5zdsCPlYc5l7OkoutnMa46lIjuj\r\nDqpOli7J0qepaKJxF7e1YQM22+dr+Vb2sgL1sfEyhvDxe3zjzRnHgdsXUPzB\r\nwtPeodfv/OV8XXXamYTV4/F2ID87WmCjDkk75/FttP7zQ+S/ZyiLEkzZ4Jn0\r\neKDPx4G92jlOc+ndZT6nO+v1+QVH96OB531a2R/oe26xldN4W+yhWFWpW3cf\r\nKIALQJV4inmA0OaEHCaYucVEH1hqewYk9/zb12bvWXDs3ZLWj/f+P5E0q5xR\r\nSBMQhxW00hNirNt/aPYf2tZcnTHqVSgBctIB8Qs+VbelPFC0lIDRZ89VTwoA\r\nzM3xhMW1EcPLu3D68+3vS9T83TPYVrjS4MjKD49wfdrGDSBdVE/uAma0lr6p\r\nJELexTtiXJbNYYjngY0HTyAng1F221wMTNF5ApxrSOHdT6/E96N09ujbZ/UN\r\nJEUUqnloZ1JZ5O3f/3BK0Ug60m9brsxy/dI=\r\n=WuL9\r\n-----END PGP SIGNATURE-----\r\n", "size": 13580}, "_npmUser": {"name": "sxzz", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sxzz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/normalize-wheel-es_1.2.0_1658194956068_0.5607218428662839"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-19T01:43:05.758Z"}}, "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "keywords": ["mouse wheel", "normalization", "browser", "esm"], "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "_source_registry_name": "default"}