{"_attachments": {}, "_id": "vue-demi", "_rev": "309978-61f1de090053da9891151601", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "dist-tags": {"latest": "0.14.10", "next": "0.11.0-beta.1", "test": "0.6.0-alpha.1"}, "license": "MIT", "maintainers": [{"name": "posva", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "name": "vue-demi", "readme": "<p align=\"center\">\n<img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/>\n<br>\n<a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a>\n</p>\n\n<p align=\"center\">\n<b>Vue Demi</b> (<i>half</i> in French) is a developing utility<br> allows you to write <b>Universal Vue Libraries</b> for Vue 2 & 3<br>\n<i>See more details in <a href='https://antfu.me/posts/make-libraries-working-with-vue-2-and-3'>this blog post</a></i>\n</p>\n\n<br>\n\n<br>\n\n## Strategies\n\n- `<=2.6`: exports from `vue` + `@vue/composition-api` with plugin auto installing.\n- `2.7`: exports from `vue` (Composition API is built-in in Vue 2.7).\n- `>=3.0`: exports from `vue`, with polyfill of Vue 2's `set` and `del` API.\n\n## Usage\n\nInstall this as your plugin's dependency:\n\n```bash\nnpm i vue-demi\n# or\nyarn add vue-demi\n# or \npnpm i vue-demi\n```\n\nAdd `vue` and `@vue/composition-api` to your plugin's peer dependencies to specify what versions you support.\n\n```jsonc\n{\n  \"dependencies\": {\n    \"vue-demi\": \"latest\"\n  },\n  \"peerDependencies\": {\n    \"@vue/composition-api\": \"^1.0.0-rc.1\",\n    \"vue\": \"^2.0.0 || >=3.0.0\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@vue/composition-api\": {\n      \"optional\": true\n    }\n  },\n  \"devDependencies\": {\n    \"vue\": \"^3.0.0\" // or \"^2.6.0\" base on your preferred working environment\n  },\n}\n```\n\nImport everything related to Vue from it, it will redirect to `vue@2` + `@vue/composition-api` or `vue@3` based on users' environments.\n\n```ts\nimport { ref, reactive, defineComponent } from 'vue-demi'\n```\n\nPublish your plugin and all is done!\n\n> When using with [Vite](https://vitejs.dev), you will need to opt-out the pre-bundling to get `vue-demi` work properly by\n> ```js\n> // vite.config.js\n> export default defineConfig({\n>   optimizeDeps: {\n>     exclude: ['vue-demi']\n>  }\n> })\n> ```\n\n### Extra APIs\n\n`Vue Demi` provides extra APIs to help distinguish users' environments and to do some version-specific logic.\n\n### `isVue2` `isVue3`\n\n```ts\nimport { isVue2, isVue3 } from 'vue-demi'\n\nif (isVue2) {\n  // Vue 2 only\n} else {\n  // Vue 3 only\n}\n```\n\n### `Vue2`\n\nTo avoid bringing in all the tree-shakable modules, we provide a `Vue2` export to support access to Vue 2's global API. (See [#41](https://github.com/vueuse/vue-demi/issues/41).)\n\n```ts\nimport { Vue2 } from 'vue-demi'\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push('x-foo')\n}\n```\n\n### `install()`\n\nComposition API in Vue 2 is provided as a plugin and needs to be installed on the Vue instance before using. Normally, `vue-demi` will try to install it automatically. For some usages where you might need to ensure the plugin gets installed correctly, the `install()` API is exposed to as a safe version of `Vue.use(CompositionAPI)`. `install()` in the Vue 3 environment will be an empty function (no-op).\n\n```ts\nimport { install } from 'vue-demi'\n\ninstall()\n```\n\n## CLI\n\n### Manually Switch Versions\n\nTo explicitly switch the redirecting version, you can use these commands in your project's root.\n\n```bash\nnpx vue-demi-switch 2\n# or\nnpx vue-demi-switch 3\n```\n\n### Package Aliasing\n\nIf you would like to import `vue` under an alias, you can use the following command \n\n```bash\nnpx vue-demi-switch 2 vue2\n# or\nnpx vue-demi-switch 3 vue3\n```\n\nThen `vue-demi` will redirect APIs from the alias name you specified, for example:\n\n```ts\nimport * as Vue from 'vue3'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nexport * from 'vue3'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n}\n```\n\n### Auto Fix\n\nIf the `postinstall` hook doesn't get triggered or you have updated the Vue version, try to run the following command to resolve the redirecting.\n\n```bash\nnpx vue-demi-fix\n```\n\n### Isomorphic Testings\n\nYou can support testing for both versions by adding npm alias in your dev dependencies. For example:\n\n```json\n{\n  \"scripts\": {\n    \"test:2\": \"vue-demi-switch 2 vue2 && jest\",\n    \"test:3\": \"vue-demi-switch 3 && jest\",\n  },\n  \"devDependencies\": {\n    \"vue\": \"^3.0.0\",\n    \"vue2\": \"npm:vue@2\"\n  },\n}\n```\n\nor\n\n```json\n{\n  \"scripts\": {\n    \"test:2\": \"vue-demi-switch 2 && jest\",\n    \"test:3\": \"vue-demi-switch 3 vue3 && jest\",\n  },\n  \"devDependencies\": {\n    \"vue\": \"^2.6.0\",\n    \"vue3\": \"npm:vue@3\"\n  },\n}\n```\n\n## Examples\n\nSee [examples](./examples).\n\n## Who is using this?\n\n- [VueUse](https://github.com/vueuse/vueuse) - Collection of Composition API utils\n- [@vue/apollo-composable](https://github.com/vuejs/vue-apollo/tree/v4/packages/vue-apollo-composable) - Apollo GraphQL functions for Vue Composition API\n- [vuelidate](https://github.com/vuelidate/vuelidate) - Simple, lightweight model-based validation\n- [vue-composition-test-utils](https://github.com/ariesjia/vue-composition-test-utils) - Simple vue composition api unit test utilities\n- [vue-use-stripe](https://github.com/frandiox/vue-use-stripe) - Stripe Elements wrapper for Vue.js\n- [@opd/g2plot-vue](https://github.com/open-data-plan/g2plot-vue) - G2plot for vue\n- [vue-echarts](https://github.com/ecomfe/vue-echarts) - Vue.js component for Apache ECharts.\n- [fluent-vue](https://github.com/Demivan/fluent-vue) - Vue.js integration for [Fluent.js](https://github.com/projectfluent/fluent.js) - JavaScript implementation of [Project Fluent](https://projectfluent.org)  \n- [vue-datatable-url-sync](https://github.com/socotecio/vue-datatable-url-sync) - Synchronize datatable options and filters with the url to keep user preference even after refresh or navigation\n- [vue-insta-stories](https://github.com/UnevenSoftware/vue-insta-stories) - Instagram stories in your vue projects.\n- [vue-tiny-validate](https://github.com/FrontLabsOfficial/vue-tiny-validate) - Tiny Vue Validate Composition\n- [v-perfect-signature](https://github.com/wobsoriano/v-perfect-signature) - Pressure-sensitive signature drawing for Vue 2 and 3\n- [vue-winbox](https://github.com/wobsoriano/vue-winbox) - A wrapper component for WinBox.js that adds the ability to mount Vue components.\n- [vue-word-highlighter](https://github.com/kawamataryo/vue-word-highlighter) - The word highlighter library for Vue 2 and Vue 3\n- [vue-chart-3](https://github.com/victorgarciaesgi/vue-chart-3) - Vue.js component for Chart.js\n- [json-editor-vue](https://github.com/cloydlau/json-editor-vue) - JSON editor & viewer for Vue 2 and 3.\n- [kidar-echarts](https://github.com/kidarjs/kidar-echarts) - A simpler echarts component for Vue 2 and 3.\n- [vue3-sketch-ruler](https://github.com/kakajun/vue3-sketch-ruler) - The zoom operation used for page presentation for Vue 2 and 3( Replace render function with template )\n- [vue-rough-notation](https://github.com/Leecason/vue-rough-notation) - RoughNotation wrapper component for Vue 2 and 3.\n- [vue-request](https://github.com/AttoJS/vue-request) - Vue composition API for data fetching, supports SWR, polling, error retry, cache request, pagination, etc.\n- [vue3-lazyload](https://github.com/murongg/vue3-lazyload) - A vue3.x image lazyload plugin.\n- [vue-codemirror6](https://github.com/logue/vue-codemirror6) - CodeMirror6 component for Vue2 and 3.\n- [@tanstack/vue-query](https://github.com/TanStack/query) - TanStack Query for Vue.\n> open a PR to add your library ;)\n\n## Underhood\n\nSee [the blog post](https://antfu.me/posts/make-libraries-working-with-vue-2-and-3/#-introducing-vue-demi).\n\n## License\n\nMIT License © 2020 [Anthony Fu](https://github.com/antfu)\n", "time": {"created": "2022-01-26T23:49:29.828Z", "modified": "2024-07-25T16:49:40.325Z", "0.12.1": "2021-11-03T17:21:45.701Z", "0.12.0": "2021-10-27T09:50:11.822Z", "0.11.4": "2021-09-05T18:18:27.218Z", "0.11.3": "2021-08-14T07:50:46.236Z", "0.11.2": "2021-07-15T17:09:57.172Z", "0.11.1": "2021-07-15T16:23:17.150Z", "0.11.0": "2021-07-15T15:01:52.335Z", "0.11.0-beta.1": "2021-07-15T14:44:48.710Z", "0.10.1": "2021-07-09T16:54:12.030Z", "0.10.0": "2021-07-05T03:36:14.221Z", "0.9.1": "2021-05-27T07:36:51.421Z", "0.9.0": "2021-05-06T09:01:35.395Z", "0.8.1": "2021-05-05T23:37:44.920Z", "0.8.0": "2021-05-05T16:04:42.221Z", "0.7.5": "2021-04-18T02:23:12.975Z", "0.7.4": "2021-03-22T19:02:29.699Z", "0.7.3": "2021-03-13T19:42:47.209Z", "0.7.2": "2021-03-12T05:21:08.885Z", "0.7.1": "2021-03-01T09:48:31.659Z", "0.7.0": "2021-02-27T10:18:19.221Z", "0.6.2": "2021-02-26T15:18:36.602Z", "0.6.1": "2021-02-25T06:12:21.533Z", "0.6.0": "2021-01-08T18:28:12.572Z", "0.6.0-alpha.1": "2021-01-08T18:20:49.378Z", "0.5.4": "2021-01-05T19:16:40.086Z", "0.5.3": "2020-12-17T09:56:03.230Z", "0.5.2": "2020-12-17T09:50:15.953Z", "0.5.1": "2020-12-17T09:38:12.206Z", "0.5.0": "2020-12-16T09:42:59.950Z", "0.4.5": "2020-11-20T05:43:08.574Z", "0.4.4": "2020-11-19T08:57:57.516Z", "0.4.3": "2020-11-09T09:02:52.393Z", "0.4.2": "2020-11-09T08:58:21.130Z", "0.4.1": "2020-10-23T02:32:34.486Z", "0.4.0": "2020-10-02T16:12:20.128Z", "0.3.3": "2020-08-26T07:14:24.470Z", "0.3.2": "2020-08-24T17:45:44.852Z", "0.3.1": "2020-08-23T19:32:09.313Z", "0.3.0": "2020-08-23T12:55:25.809Z", "0.2.0": "2020-08-04T13:05:45.590Z", "0.2.0-alpha.2": "2020-08-04T13:02:45.262Z", "0.2.0-alpha.1": "2020-08-04T12:59:45.914Z", "0.1.11": "2020-08-04T12:52:23.039Z", "0.1.10": "2020-08-04T12:48:42.852Z", "0.1.9": "2020-08-04T12:46:24.002Z", "0.1.8": "2020-08-04T12:44:24.776Z", "0.1.7": "2020-08-04T12:41:30.332Z", "0.1.6": "2020-08-04T12:38:59.096Z", "0.1.5": "2020-08-04T12:35:52.055Z", "0.1.4": "2020-07-04T14:35:59.001Z", "0.1.3": "2020-07-03T07:45:33.460Z", "0.1.2": "2020-07-02T05:38:50.001Z", "0.1.1": "2020-07-01T16:49:09.971Z", "0.1.0": "2020-07-01T08:22:58.706Z", "0.0.7": "2020-07-01T06:20:05.337Z", "0.0.6": "2020-07-01T05:56:50.646Z", "0.0.5": "2020-07-01T05:54:03.264Z", "0.0.4": "2020-07-01T05:46:52.349Z", "0.0.3": "2020-07-01T05:29:48.267Z", "0.0.2": "2020-07-01T05:25:02.750Z", "0.0.1": "2020-07-01T05:18:42.643Z", "0.12.2": "2022-03-15T07:41:12.146Z", "0.12.3": "2022-03-15T16:26:18.815Z", "0.12.4": "2022-03-16T08:57:40.725Z", "0.12.5": "2022-03-30T16:35:22.816Z", "0.13.0": "2022-05-31T12:23:09.805Z", "0.13.1": "2022-06-01T08:12:13.984Z", "0.13.2": "2022-07-05T04:42:01.850Z", "0.13.3": "2022-07-12T04:34:43.004Z", "0.13.4": "2022-07-12T06:32:08.730Z", "0.13.5": "2022-07-16T06:41:40.877Z", "0.13.6": "2022-07-26T13:29:51.977Z", "0.13.7": "2022-08-10T15:35:40.423Z", "0.13.8": "2022-08-16T16:02:36.701Z", "0.13.9": "2022-08-23T13:22:24.503Z", "0.13.10": "2022-08-24T01:40:44.412Z", "0.13.11": "2022-08-25T16:04:35.478Z", "0.14.0": "2023-04-13T20:10:10.550Z", "0.14.1": "2023-05-09T09:32:13.213Z", "0.14.2": "2023-05-17T12:58:54.080Z", "0.14.3": "2023-05-17T13:55:04.838Z", "0.14.4": "2023-05-17T15:33:15.112Z", "0.14.5": "2023-05-18T10:15:54.928Z", "0.14.6": "2023-08-28T08:00:43.027Z", "0.14.7": "2024-02-01T11:32:36.980Z", "0.14.8": "2024-05-30T11:30:18.517Z", "0.14.9": "2024-07-23T12:10:12.955Z", "0.14.10": "2024-07-25T16:19:54.431Z"}, "versions": {"0.12.1": {"name": "vue-demi", "version": "0.12.1", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "c486adc2febc7c7b0e3eeb063046010fa96dd766", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.12.1", "_nodeVersion": "16.11.1", "_npmVersion": "8.0.0", "dist": {"shasum": "f7e18efbecffd11ab069d1472d7a06e319b4174c", "size": 6149, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.1.tgz", "integrity": "sha512-QL3ny+wX8c6Xm1/EZylbgzdoDolye+VpCXRhI2hug9dJTP3OUJ3lmiKN3CsVV3mOJKwFi0nsstbgob0vG7aoIw=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.12.1_1635960105503_0.18169671444913615"}, "_hasShrinkwrap": false, "publish_time": 1635960105701, "_cnpm_publish_time": 1635960105701, "_cnpmcore_publish_time": "2021-12-15T17:04:43.964Z", "hasInstallScript": true}, "0.12.0": {"name": "vue-demi", "version": "0.12.0", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "e468ab9159ec483b0c436650541ab7425b49118a", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.12.0", "_nodeVersion": "16.11.1", "_npmVersion": "8.0.0", "dist": {"shasum": "b202a01cdfc635443e41faea697d624caac0fa73", "size": 5985, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.0.tgz", "integrity": "sha512-eggsbQSQEJKlvQrtrJLx4J44MIVq5+Z7QetIEh1Na+ZWLgt5Fq0qskQ1QmckTTEoFcUdn36c4K23EjtXZhws7w=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.12.0_1635328211713_0.283744724218409"}, "_hasShrinkwrap": false, "publish_time": 1635328211822, "_cnpm_publish_time": 1635328211822, "_cnpmcore_publish_time": "2021-12-15T17:04:44.298Z", "hasInstallScript": true}, "0.11.4": {"name": "vue-demi", "version": "0.11.4", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "ec30133ebc6cab8f38aec2c6e684487092aa1171", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.11.4", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.3", "dist": {"shasum": "6101992fe4724cf5634018a16e953f3052e94e2a", "size": 5955, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.4.tgz", "integrity": "sha512-/3xFwzSykLW2HiiLie43a+FFgNOcokbBJ+fzvFXd0r2T8MYohqvphUyDQ8lbAwzQ3Dlcrb1c9ykifGkhSIAk6A=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.11.4_1630865907030_0.9170198842778654"}, "_hasShrinkwrap": false, "publish_time": 1630865907218, "_cnpm_publish_time": 1630865907218, "_cnpmcore_publish_time": "2021-12-15T17:04:44.581Z", "hasInstallScript": true}, "0.11.3": {"name": "vue-demi", "version": "0.11.3", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "node": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "9c3103e6828887f16c77e2a1d58e0d7ec57d52d2", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.11.3", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "dd7495b92b495ecfa35675bf024b1358a7add150", "size": 5925, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.3.tgz", "integrity": "sha512-DpM0TTMpclRZDV6AIacgg837zrim/C9Zn+2ztXBs9hsESJN9vC83ztjTe4KC4HgJuVle8YUjPp7HTwWtwOHfmg=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.11.3_1628927446088_0.7977477248167191"}, "_hasShrinkwrap": false, "publish_time": 1628927446236, "_cnpm_publish_time": 1628927446236, "_cnpmcore_publish_time": "2021-12-15T17:04:44.841Z", "hasInstallScript": true}, "0.11.2": {"name": "vue-demi", "version": "0.11.2", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "node": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "8815cc616f236e156d65433fd2caf8bfb0285a41", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.11.2", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "faa06da53887c493a695b997f4fcb4784a667990", "size": 5701, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.2.tgz", "integrity": "sha512-J+X8Au6BhQdcej6LY4O986634hZLu55L0ewU2j8my7WIKlu8cK0dqmdUxqVHHMd/cMrKKZ9SywB/id6aLhwCtA=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.11.2_1626368997049_0.44195505712219196"}, "_hasShrinkwrap": false, "publish_time": 1626368997172, "_cnpm_publish_time": 1626368997172, "_cnpmcore_publish_time": "2021-12-15T17:04:45.067Z", "hasInstallScript": true}, "0.11.1": {"name": "vue-demi", "version": "0.11.1", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "node": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "a0e99fbb0f032edcb4619adf05b6496a49f57561", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.11.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "5185747ca8df6ca2a7e55346714aa0055f42d5d5", "size": 5687, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.1.tgz", "integrity": "sha512-+wfNmITb4Do+eLRUGrG9aLZOtfkFP4OXABxRAmU8/OT5vXv0OPJw+cBhPrXmnrXJCM7nl6LnuOxG4BFozwoE1A=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.11.1_1626366196999_0.36447938444238015"}, "_hasShrinkwrap": false, "publish_time": 1626366197150, "_cnpm_publish_time": 1626366197150, "_cnpmcore_publish_time": "2021-12-15T17:04:45.276Z", "hasInstallScript": true}, "0.11.0": {"name": "vue-demi", "version": "0.11.0", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "node": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.6.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "de96ebc2d60d1b851715c17676378879496ac1b2", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.11.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "3fd2bd3004adc912051e6b2da3c3f5780f3dd9ef", "size": 5683, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.0.tgz", "integrity": "sha512-fjMHzLh3KFMxC4gvSn3KLM9klh868ccj+lKfpyt/Y6LODVfHjL1hGtDrszV7B7saaHtOPVqboGPmqXKwcYwwTQ=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.11.0_1626361312138_0.18073802350795654"}, "_hasShrinkwrap": false, "publish_time": 1626361312335, "_cnpm_publish_time": 1626361312335, "_cnpmcore_publish_time": "2021-12-15T17:04:45.522Z", "hasInstallScript": true}, "0.11.0-beta.1": {"name": "vue-demi", "version": "0.11.0-beta.1", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "node": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish --tag next"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.6.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "readmeFilename": "README.md", "gitHead": "e7353a994ddc2952600bd8a439419b2c2214481d", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.11.0-beta.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "3468cefc8d8283e93c0e23c0c72b4e7e1616e018", "size": 5692, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.11.0-beta.1.tgz", "integrity": "sha512-J6UPn4czgQXGvuV+PRwzP0dbcnvc/IfYoBwwacsCMq7hTxtRArN9zMypuGOW9Wlq1ltDGcUhR3C+XGpC5T+USQ=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.11.0-beta.1_1626360288571_0.33463707396261455"}, "_hasShrinkwrap": false, "publish_time": 1626360288710, "_cnpm_publish_time": 1626360288710, "_cnpmcore_publish_time": "2021-12-15T17:04:45.784Z", "hasInstallScript": true}, "0.10.1": {"name": "vue-demi", "version": "0.10.1", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs.js", "node": "./lib/index.cjs.js", "browser": "./lib/index.esm.js"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.6.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "6d1782d153dd0eca4c8dfc731a1f0be579c9c12f", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.10.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "229b81395510f02f4ee255344557a12cc0120930", "size": 5671, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.10.1.tgz", "integrity": "sha512-L6Oi+BvmMv6YXvqv5rJNCFHEKSVu7llpWWJczqmAQYOdmPPw5PNYoz1KKS//Fxhi+4QP64dsPjtmvnYGo1jemA=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.10.1_1625849651874_0.04213762951935762"}, "_hasShrinkwrap": false, "publish_time": 1625849652030, "_cnpm_publish_time": 1625849652030, "_cnpmcore_publish_time": "2021-12-15T17:04:45.991Z", "hasInstallScript": true}, "0.10.0": {"name": "vue-demi", "version": "0.10.0", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs.js", "node": "./lib/index.cjs.js", "browser": "./lib/index.esm.js"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "6112e2f6c811b121091a75c75707f807533ddb87", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.10.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "e21cad49e4aee210093de658684f0ba49ae404f1", "size": 5680, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.10.0.tgz", "integrity": "sha512-SlVtIkcCVwbSVAiWTk3ixcDF6bRjgPP5nKGjSx4HXmieEUDi2oVQJUQxoFmEOV21js7TKvclzEUhInDEkg0NDQ=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.10.0_1625456174068_0.8644586768222551"}, "_hasShrinkwrap": false, "publish_time": 1625456174221, "_cnpm_publish_time": 1625456174221, "_cnpmcore_publish_time": "2021-12-15T17:04:46.366Z", "hasInstallScript": true}, "0.9.1": {"name": "vue-demi", "version": "0.9.1", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.esm.js", "require": "./lib/index.cjs.js"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "fbac983fd789b910b467906e7477464f8465f62d", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.9.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "25d6e1ebd4d4010757ff3571e2bf6a1d7bf3de82", "size": 5668, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.9.1.tgz", "integrity": "sha512-7s1lufRD2l369eFWPjgLvhqCRk0XzGWJsQc7K4q+0mZtixyGIvsK1Cg88P4NcaRIEiBuuN4q1NN4SZKFKwQswA=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.9.1_1622101011213_0.0801837438435895"}, "_hasShrinkwrap": false, "publish_time": 1622101011421, "_cnpm_publish_time": 1622101011421, "_cnpmcore_publish_time": "2021-12-15T17:04:46.635Z", "hasInstallScript": true}, "0.9.0": {"name": "vue-demi", "version": "0.9.0", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.esm.js", "require": "./lib/index.cjs.js"}, "./": "./"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "cc73b7fd5f83000ad1a34043d3b9b642db5b7f94", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.9.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "b30da61450079d60a132d7aaf9e86d1949e8445e", "size": 5667, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.9.0.tgz", "integrity": "sha512-f8vVUpC726YXv99fF/3zHaw5CUYbP5H/DVWBN+pncXM8P2Uz88kkffwj9yD7MukuVzPICDHNrgS3VC2ursaP7g=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.9.0_1620291695289_0.2877112167741538"}, "_hasShrinkwrap": false, "publish_time": 1620291695395, "_cnpm_publish_time": 1620291695395, "_cnpmcore_publish_time": "2021-12-15T17:04:46.844Z", "hasInstallScript": true}, "0.8.1": {"name": "vue-demi", "version": "0.8.1", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.mjs", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.esm.mjs", "require": "./lib/index.cjs.js"}, "./": "./"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "8d13084344d2bbe172d0fa62cc2b997775533daa", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.8.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "8bbb003ff8b59a22af18fa1c74ea5ed3cf30010c", "size": 5666, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.8.1.tgz", "integrity": "sha512-j76<PERSON><PERSON>HalbBYK6XwR2xcARGi1uY58TKnmDCK2kES7qDdEiHh9OngjqPYBco9RnUON+1WHEFE4efuwH/vd1+T/Q=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.8.1_1620257864768_0.30800881932764756"}, "_hasShrinkwrap": false, "publish_time": 1620257864920, "_cnpm_publish_time": 1620257864920, "_cnpmcore_publish_time": "2021-12-15T17:04:47.109Z", "hasInstallScript": true}, "0.8.0": {"name": "vue-demi", "version": "0.8.0", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.mjs", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.esm.mjs", "require": "./lib/index.cjs.js"}, "./": "./"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "bf4ea99aa4ef6c1ed2b50d6e57143c7e3d40e1d7", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.8.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"shasum": "2eec8f3c97060f19d6f594d2355ee3a30f7184c7", "size": 5505, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.8.0.tgz", "integrity": "sha512-383zJByNjE9rrAgOrGkuihpgk15r7PlDQGTqMXxzDdyiB3ZsDAfe5BsiahJOJ/298Iw9zsZbGJrzCKOPh8IK1Q=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.8.0_1620230682087_0.658752564433331"}, "_hasShrinkwrap": false, "publish_time": 1620230682221, "_cnpm_publish_time": 1620230682221, "_cnpmcore_publish_time": "2021-12-15T17:04:47.433Z", "hasInstallScript": true}, "0.7.5": {"name": "vue-demi", "version": "0.7.5", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.esm.js", "require": "./lib/index.cjs.js"}, "./": "./"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "703433d8cfa2dc1903c6b541be5c3de840d69c72", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.7.5", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "88dee7492fc99a0f911ff03fc02c658fa2a79af8", "size": 5428, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.7.5.tgz", "integrity": "sha512-eFSQSvbQdY7C9ujOzvM6tn7XxwLjn0VQDXQsiYBLBwf28Na+2nTQR4BBBcomhmdP6mmHlBKAwarq6a0BPG87hQ=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.7.5_1618712592828_0.5577249752752627"}, "_hasShrinkwrap": false, "publish_time": 1618712592975, "_cnpm_publish_time": 1618712592975, "_cnpmcore_publish_time": "2021-12-15T17:04:47.715Z", "hasInstallScript": true}, "0.7.4": {"name": "vue-demi", "version": "0.7.4", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "typings": "lib/index.d.ts", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "d14f8fbe47bd1b297fee1214ae1c6befa6feb8c8", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.7.4", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "4c6be525788e1f6b3fd5d4f5f9f2148cf6645979", "size": 5317, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.7.4.tgz", "integrity": "sha512-PT0qzI9Rp8R8eUAsTPXADC+KAZdrMufmbZqEMMqvaiesWyjCpgyuuvS5Su8cvBjK9RevLT/YfFiKWxc8YB9/8g=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.7.4_1616439749593_0.9385488868101197"}, "_hasShrinkwrap": false, "publish_time": 1616439749699, "_cnpm_publish_time": 1616439749699, "_cnpmcore_publish_time": "2021-12-15T17:04:47.940Z", "hasInstallScript": true}, "0.7.3": {"name": "vue-demi", "version": "0.7.3", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "typings": "lib/index.d.ts", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "c154e9fa055188ffd446f6dd63032af8195fb3b6", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.7.3", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "463188044d31d33985e67da51f315b447a1fed85", "size": 5339, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.7.3.tgz", "integrity": "sha512-vrzM26H4CZCXBf/eu4T8nks6o7qgziYM52myk8bg+atw4qYqpeWJf5c82W8VdmgGfSIdh9ulOOe9+GeLc3Z/8A=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.7.3_1615664567047_0.4174384258440953"}, "_hasShrinkwrap": false, "publish_time": 1615664567209, "_cnpm_publish_time": 1615664567209, "_cnpmcore_publish_time": "2021-12-15T17:04:48.134Z", "hasInstallScript": true}, "0.7.2": {"name": "vue-demi", "version": "0.7.2", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "typings": "lib/index.d.ts", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "a05bc9d3ed482512e8477bc207d1b26856584042", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.7.2", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "bb450837d8f36b293d3f9fbaec60570bae3c6b0a", "size": 5344, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.7.2.tgz", "integrity": "sha512-cRaQymWYC3gcj3Y8CGDjPI7r2BIvHNMuoLSlfyo7CeqtFzV6eIlYK7xPnExPl4m3j6y5D1FOqdclEIWeim5EJg=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.7.2_1615526468707_0.701525422802072"}, "_hasShrinkwrap": false, "publish_time": 1615526468885, "_cnpm_publish_time": 1615526468885, "_cnpmcore_publish_time": "2021-12-15T17:04:48.343Z", "hasInstallScript": true}, "0.7.1": {"name": "vue-demi", "version": "0.7.1", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "typings": "lib/index.d.ts", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "817198aff40bc19d4eaed15b1a96c72da780a16c", "description": "<p align=\"center\"> <img src=\"./assets/banner.png\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.7.1", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "e7cf3e70e4c69bc192f4530c2665ce8b289f5dc4", "size": 5263, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.7.1.tgz", "integrity": "sha512-cJ3QnpcbIqdpeV3NnNgSDwneCJifvHct+6JNvfYn/49geK/uv5VJicZDvyg20guNhz+DZmUHBHyRQTJ32lSC2g=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.7.1_1614592111500_0.9006896457715396"}, "_hasShrinkwrap": false, "publish_time": 1614592111659, "_cnpm_publish_time": 1614592111659, "_cnpmcore_publish_time": "2021-12-15T17:04:48.573Z", "hasInstallScript": true}, "0.7.0": {"name": "vue-demi", "version": "0.7.0", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs.js", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.esm.js", "typings": "lib/index.d.ts", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "fb78fdb905d166ea98dbbea61a24c16118b19e38", "description": "<p align=\"center\"> <img src=\"./assets/banner.png\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.7.0", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "34aff70a9d9a45d983d68f5c33bb70f6d19500e1", "size": 5251, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.7.0.tgz", "integrity": "sha512-yOQ/BMfvTFbguwxQ1h6USEFkxvOI8ThhyZ1+57vlOl4jBZKC6ThmkzuQv4KOv4ItdxTj/foZBR6Lr5+tcr7nqg=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.7.0_1614421099061_0.5892608887268038"}, "_hasShrinkwrap": false, "publish_time": 1614421099221, "_cnpm_publish_time": 1614421099221, "_cnpmcore_publish_time": "2021-12-15T17:04:48.874Z", "hasInstallScript": true}, "0.6.2": {"name": "vue-demi", "version": "0.6.2", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "funding": "https://github.com/sponsors/antfu", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.25", "vue2": "^2.6.12", "vue": "^3.0.5"}, "gitHead": "e9639af31a1ce3584bd98ccb96f7d71e8ebd9384", "description": "<p align=\"center\"> <img src=\"./assets/banner.png\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.6.2", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "ed9633eaa69b7409cc6e8328e8a6c3d31ab39319", "size": 4833, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.6.2.tgz", "integrity": "sha512-4KfwxbqsZcDz7UryD11oHa6FLK9Gzx4MIwAEcP37IbUdXuF6DHg2w9L5Zv5t+FNqsuAjEQ2NnGVIZ3u71y435Q=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.6.2_1614352716292_0.7784896654350479"}, "_hasShrinkwrap": false, "publish_time": 1614352716602, "_cnpm_publish_time": 1614352716602, "_cnpmcore_publish_time": "2021-12-15T17:04:49.117Z", "hasInstallScript": true}, "0.6.1": {"name": "vue-demi", "version": "0.6.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "funding": "https://github.com/sponsors/antfu", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.25", "vue2": "^2.6.12", "vue": "^3.0.5"}, "gitHead": "db7cd2e27e253c8b20b1f8d6e4ec967e62c8d248", "description": "<p align=\"center\"> <img src=\"./assets/banner.png\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.6.1", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "6949c900a295d9c40e1fa5a74a2849b58cc57325", "size": 4633, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.6.1.tgz", "integrity": "sha512-2DI0owOzgEDnEBoXpp0nTBquAWLZqVf/+AOJpPUnviT17unMELEPNy5BdSRnQEz+DA0STtqaz+MZtwhzlDHm5Q=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.6.1_1614233541358_0.10050018055471299"}, "_hasShrinkwrap": false, "publish_time": 1614233541533, "_cnpm_publish_time": 1614233541533, "_cnpmcore_publish_time": "2021-12-15T17:04:49.344Z", "hasInstallScript": true}, "0.6.0": {"name": "vue-demi", "version": "0.6.0", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "funding": "https://github.com/sponsors/antfu", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && yarn publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.25", "vue2": "^2.6.12", "vue": "^3.0.5"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.6.0", "dist": {"shasum": "e314282f704cb449119b9fd002cbbc0e39f591fe", "size": 4790, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.6.0.tgz", "integrity": "sha512-8GEJa0mHJpYJeGeq5fD1pJct2kfdl30PHfmL1NaJ97mgKPyKojlIRt/3inGBK4Y0ylCI6T5vOo3chwpqDOq/Hw=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.6.0_1610130492424_0.959889644936444"}, "_hasShrinkwrap": false, "publish_time": 1610130492572, "_cnpm_publish_time": 1610130492572, "_cnpmcore_publish_time": "2021-12-15T17:04:49.571Z", "hasInstallScript": true}, "0.6.0-alpha.1": {"name": "vue-demi", "version": "0.6.0-alpha.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "funding": "https://github.com/sponsors/antfu", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && yarn publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.25", "vue2": "^2.6.12", "vue": "^3.0.5"}, "readmeFilename": "README.md", "gitHead": "59eb90d57189aeafc7c66132a11a03a5e1ac306b", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.6.0-alpha.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"shasum": "1b0224a5690c56c2d9ef3fb4c6912548351cf24f", "size": 4604, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.6.0-alpha.1.tgz", "integrity": "sha512-RU6SYOs/X5JY7lT+eiGhq+8yuVVRJ09eL/S2cbwv9Vg3+mHczHagUUGELz9S7XRl9mj37KZDg4gNDBPVx6iFWw=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.6.0-alpha.1_1610130049241_0.8962083323199084"}, "_hasShrinkwrap": false, "publish_time": 1610130049378, "_cnpm_publish_time": 1610130049378, "_cnpmcore_publish_time": "2021-12-15T17:04:49.819Z", "hasInstallScript": true}, "0.5.4": {"name": "vue-demi", "version": "0.5.4", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && yarn publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.5.4", "dist": {"shasum": "cedb887af819ff0126c545a5540170d8895b816d", "size": 4504, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.5.4.tgz", "integrity": "sha512-m/cm/Jo+USFx4JNGvhXVuAAbeB+Dejc0O8hzarph9MMuxxcMEDxf+0UBzg79g15YwYGIToX957XvzG0xSufG9A=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.5.4_1609874199945_0.15546384074915354"}, "_hasShrinkwrap": false, "publish_time": 1609874200086, "_cnpm_publish_time": 1609874200086, "_cnpmcore_publish_time": "2021-12-15T17:04:50.164Z", "hasInstallScript": true}, "0.5.3": {"name": "vue-demi", "version": "0.5.3", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && yarn publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.5.3", "dist": {"shasum": "ec0f10b2c56d5958f31363afaa2ef8b8b9c4e6ec", "size": 4484, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.5.3.tgz", "integrity": "sha512-1uW/aIJOEkRbyK3FBeReKbbMAZc/V5yUo4FQRprufm4GgMnAmJSSyk8mTJ9IgR4pbbCsQ7FddVHcuwz4IDEfGg=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.5.3_1608198963027_0.8803881520586252"}, "_hasShrinkwrap": false, "publish_time": 1608198963230, "_cnpm_publish_time": 1608198963230, "_cnpmcore_publish_time": "2021-12-15T17:04:50.380Z", "hasInstallScript": true}, "0.5.2": {"name": "vue-demi", "version": "0.5.2", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && yarn publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.5.2", "dist": {"shasum": "1c1a4c852f2b9f3f1dc05e9b23b0198ff4c4460b", "size": 4360, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.5.2.tgz", "integrity": "sha512-cO0bl8LCPFQdlZuGYhfoqBw4rqhA3SII9VqqkpPX0nDlFqzheDYlpqv1oM3Hkk+oyK1SNiMn6qSIvySnelL6GA=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.5.2_1608198615718_0.7782091550816661"}, "_hasShrinkwrap": false, "publish_time": 1608198615953, "_cnpm_publish_time": 1608198615953, "_cnpmcore_publish_time": "2021-12-15T17:04:50.630Z", "hasInstallScript": true}, "0.5.1": {"name": "vue-demi", "version": "0.5.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js", "vue-demi-switch": "scripts/switch-cli.js"}, "gitHead": "2e754e2ad1f98a7bc6c1f49507f49979921816a8", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.5.1", "_nodeVersion": "14.13.1", "_npmVersion": "7.0.9", "dist": {"shasum": "876ac940658b79e0d4d6342e3a6c10636521487e", "size": 4195, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.5.1.tgz", "integrity": "sha512-pqQ+vNz9ueI1AfFAe7gz4Ny0TbUSJp9vxVUDfwsXZkDvoNdJ8BtRRzesdJUotQN7PimZ4zD3cs1yOaMq2HMXEA=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.5.1_1608197891854_0.7461741321510211"}, "_hasShrinkwrap": false, "publish_time": 1608197892206, "_cnpm_publish_time": 1608197892206, "_cnpmcore_publish_time": "2021-12-15T17:04:50.961Z", "hasInstallScript": true}, "0.5.0": {"name": "vue-demi", "version": "0.5.0", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js", "vue-demi-switch": "scripts/switch-cli.js"}, "gitHead": "880d31cdce0a43a0e236760a7cc4a758b32c070a", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.5.0", "_nodeVersion": "14.13.1", "_npmVersion": "7.0.9", "dist": {"shasum": "40e5d6b08b2ee18f5dd73fae41aefb839b142855", "size": 3980, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.5.0.tgz", "integrity": "sha512-NIR8aKBz3ixC/2BhQJmpm8eGJ2mCACNah4ycb953HqGmz+zIl1F9mZIpl1yV6MIC0VIMNxhdFxBbrxM1wFq0oQ=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.5.0_1608111779786_0.7785450649096695"}, "_hasShrinkwrap": false, "publish_time": 1608111779950, "_cnpm_publish_time": 1608111779950, "_cnpmcore_publish_time": "2021-12-15T17:04:51.185Z", "hasInstallScript": true}, "0.4.5": {"name": "vue-demi", "version": "0.4.5", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "aa3170ab731bec645414196d14ab649a105d2ddb", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.4.5", "_nodeVersion": "14.13.1", "_npmVersion": "7.0.9", "dist": {"shasum": "ea422a4468cb6321a746826a368a770607f87791", "size": 3478, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.4.5.tgz", "integrity": "sha512-51xf1B6hV2PfjnzYHO/yUForFCRQ49KS8ngQb5T6l1HDEmfghTFtsxtRa5tbx4eqQsH76ll/0gIxuf1gei0ubw=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.4.5_1605850988408_0.7636370367284184"}, "_hasShrinkwrap": false, "publish_time": 1605850988574, "_cnpm_publish_time": 1605850988574, "_cnpmcore_publish_time": "2021-12-15T17:04:51.424Z", "hasInstallScript": true}, "0.4.4": {"name": "vue-demi", "version": "0.4.4", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "a77ccd93b0124b57326b0ad2177e7704e334f8d1", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.4.4", "_nodeVersion": "14.13.1", "_npmVersion": "7.0.9", "dist": {"shasum": "89c99655da21d530f53456c97cb2c5fbde6e4e17", "size": 3490, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.4.4.tgz", "integrity": "sha512-cv9FpE/lfvPMGTWiZbext/sewrCnYosiLK46KwNgUc6VIKq7D9LdtnInDStnLuMj3ybsQGEyHWt0JDlKQiA85Q=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.4.4_1605776277360_0.3704807762395981"}, "_hasShrinkwrap": false, "publish_time": 1605776277516, "_cnpm_publish_time": 1605776277516, "_cnpmcore_publish_time": "2021-12-15T17:04:51.670Z", "hasInstallScript": true}, "0.4.3": {"name": "vue-demi", "version": "0.4.3", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "1edad16ba97bb479714c4b223117aaeefeab5bc7", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.4.3", "_nodeVersion": "14.13.1", "_npmVersion": "7.0.9", "dist": {"shasum": "6aaa9b52f02c32b4f9d4d11f02a1ae71031453c3", "size": 3473, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.4.3.tgz", "integrity": "sha512-1DzLcZgHC9ZyFEYR4qZ83TdS1u9DglG8XVesBXqtbbmqFuO7sb8KG36kMfZCszieAweRDwAAVSAzjmEMG0+WwA=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.4.3_1604912572233_0.6452945964949055"}, "_hasShrinkwrap": false, "publish_time": 1604912572393, "_cnpm_publish_time": 1604912572393, "_cnpmcore_publish_time": "2021-12-15T17:04:52.001Z", "hasInstallScript": true}, "0.4.2": {"name": "vue-demi", "version": "0.4.2", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"vue": "^2.6.0 || >=3.0.0-rc.1"}, "optionalDependencies": {"@vue/composition-api": ">=1.0.0-beta.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "9f6217e7bca7f157b437adb5c20a5d94496ec991", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "dependencies": {"@vue/composition-api": ">=1.0.0-beta.1"}, "_id": "vue-demi@0.4.2", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "650ce14418dd5ebeea3fec37c86938679c11790b", "size": 3484, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.4.2.tgz", "integrity": "sha512-vyXHD63k0Rblk9yT1BU5zUv2DH5iMQSM4KEOQi23bAVH0Jj9+LGQcyQ4ZNepLTVn+emYsjB4sV8idau+1nmFSQ=="}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.4.2_1604912300938_0.6490452414282493"}, "_hasShrinkwrap": false, "publish_time": 1604912301130, "_cnpm_publish_time": 1604912301130, "_cnpmcore_publish_time": "2021-12-15T17:04:52.275Z", "hasInstallScript": true}, "0.4.1": {"name": "vue-demi", "version": "0.4.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": ">=1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "9f12cf2e434aab0e2c07fc07ee4aaf98674ffe53", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.4.1", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "2261b8afe57b99ef4a6ee82a7bd2e6fe471bdcde", "size": 3434, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.4.1.tgz", "integrity": "sha512-3xxnqY426eMKhmYdNygYwOvuaJlrgP+aSZLiD5GdCF7cBsMNmUZM7g3RL0NqIuY19WB5CCzuFbi/xW1Nd053xw=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.4.1_1603420354342_0.9223251175109675"}, "_hasShrinkwrap": false, "publish_time": 1603420354486, "_cnpm_publish_time": 1603420354486, "_cnpmcore_publish_time": "2021-12-15T17:04:52.475Z", "hasInstallScript": true}, "0.4.0": {"name": "vue-demi", "version": "0.4.0", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iife.js", "jsdelivr": "lib/index.iife.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "peerDependencies": {"@vue/composition-api": ">=1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "ccf50cdec4f5b10c31d636a1d38bc6e9dc9744cc", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.4.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"shasum": "074e61674b1ec6d3483d54ba9fd7acc1378a66a2", "size": 3363, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.4.0.tgz", "integrity": "sha512-7xaEKWpcyrbP5fVNFWSgF+gf0bWAfvlhH0Cb08Ec4XeTK6FCplaqXmaRLivzl3ypM0mkNcBAjSgctzMul0hgYg=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.4.0_1601655139994_0.021200477412440577"}, "_hasShrinkwrap": false, "publish_time": 1601655140128, "_cnpm_publish_time": 1601655140128, "_cnpmcore_publish_time": "2021-12-15T17:04:52.779Z", "hasInstallScript": true}, "0.3.3": {"name": "vue-demi", "version": "0.3.3", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "peerDependencies": {"@vue/composition-api": ">=1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.3.3", "dist": {"shasum": "27d7ae33e1d82752418ecfded60e625c5cc81866", "size": 3044, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.3.3.tgz", "integrity": "sha512-/CvXJUp7e41C/jS5ZgflMQgkY2qFX1ZkZq1kYwYH5RCC9gGy1vgvjsldtQlVORaAUt55+k/mQIUnUKNrLosoIw=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.3.3_1598426064342_0.6407739197629694"}, "_hasShrinkwrap": false, "publish_time": 1598426064470, "_cnpm_publish_time": 1598426064470, "_cnpmcore_publish_time": "2021-12-15T17:04:52.993Z", "hasInstallScript": true}, "0.3.2": {"name": "vue-demi", "version": "0.3.2", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "peerDependencies": {"@vue/composition-api": ">=1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.3.2", "dist": {"shasum": "a5aaeed4f76f5d9e5db83fbbf70ddec3e8ea40b2", "size": 2986, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.3.2.tgz", "integrity": "sha512-PJcowrO4HrjyscUianvYo0ywQ9zvX7dBLgO56d26ldVdtyFKRWUZ05xFN5i6FQhtogLonHinh0n82My2wAchHA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.3.2_1598291144568_0.08079729368889632"}, "_hasShrinkwrap": false, "publish_time": 1598291144852, "_cnpm_publish_time": 1598291144852, "_cnpmcore_publish_time": "2021-12-15T17:04:53.236Z", "hasInstallScript": true}, "0.3.1": {"name": "vue-demi", "version": "0.3.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "peerDependencies": {"@vue/composition-api": ">=1.0.0-beta.1", "vue": "^2.6.0 || >=3.0.0-rc.1"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.3.1", "dist": {"shasum": "2ab3dd1b744bf82c212fed445bd875c47ecb257d", "size": 2918, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.3.1.tgz", "integrity": "sha512-x8Kmcb19MdMlixNWE+eK06NsgranFQT1Vznng5pNY5wOqeMTPjHAkjmEqZiQzCetwYzU0gqB99suMEXpPri+yw=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.3.1_1598211129173_0.12632908305125046"}, "_hasShrinkwrap": false, "publish_time": 1598211129313, "_cnpm_publish_time": 1598211129313, "_cnpmcore_publish_time": "2021-12-15T17:04:53.507Z", "hasInstallScript": true}, "0.3.0": {"name": "vue-demi", "version": "0.3.0", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "peerDependencies": {"@vue/composition-api": ">=1.0.0-beta.1"}, "devDependencies": {"vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "gitHead": "6b952108b2b908b2eb3c7eea8b22a85552d7c8d4", "description": "<p align=\"center\"> <br> <img src=\"./assets/banner.png\" width=\"500\"/> <br> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.3.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"shasum": "9bd2f662726bf5be8c7d2b94d0c6ad5af396d328", "size": 2731, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.3.0.tgz", "integrity": "sha512-3wJWfUF6DKBR26APWNvbfKGLZyH/rSjDu3qhfce5uFfEsW3X+T4U7MdAP0HdFPHqmLcR5jrfzSpOvxwDMeuC6A=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.3.0_1598187325666_0.8479014142545966"}, "_hasShrinkwrap": false, "publish_time": 1598187325809, "_cnpm_publish_time": 1598187325809, "_cnpmcore_publish_time": "2021-12-15T17:04:53.726Z", "hasInstallScript": true}, "0.2.0": {"name": "vue-demi", "version": "0.2.0", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "dependencies": {"@vue/composition-api": ">=1.0.0-beta.1"}, "devDependencies": {"vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.2.0", "dist": {"shasum": "9b464f094c7c37547c1babe95e9bf2c61cf4a1d2", "size": 2901, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.2.0.tgz", "integrity": "sha512-EbB3UxYacKwORuey3VShOO0IJ+h8W2aq6v574m0XRMYrVOH2TkfO660gm7ZfNVHKHK4leNzVkAGGIfYDxrAFPg=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.2.0_1596546345478_0.49459938731336006"}, "_hasShrinkwrap": false, "publish_time": 1596546345590, "_cnpm_publish_time": 1596546345590, "_cnpmcore_publish_time": "2021-12-15T17:04:54.109Z", "hasInstallScript": true}, "0.2.0-alpha.2": {"name": "vue-demi", "version": "0.2.0-alpha.2", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "dependencies": {"@vue/composition-api": ">=1.0.0-beta.1"}, "devDependencies": {"vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.2.0-alpha.2", "dist": {"shasum": "e0ef37c8178225f08a4c4ca4efff224381585b02", "size": 2909, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.2.0-alpha.2.tgz", "integrity": "sha512-IJl9wQEDX86G8r6W2QiV7M1vp7Y/X1i+qfLXDgyktRWMcXJnvFC+wCbxkyZSYPMlZpNm3UlKLmn3rwKhfmQ7iQ=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.2.0-alpha.2_1596546165120_0.8372717579865661"}, "_hasShrinkwrap": false, "publish_time": 1596546165262, "_cnpm_publish_time": 1596546165262, "_cnpmcore_publish_time": "2021-12-15T17:04:54.323Z", "hasInstallScript": true}, "0.2.0-alpha.1": {"name": "vue-demi", "version": "0.2.0-alpha.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.2.0-alpha.1", "dist": {"shasum": "3cfdaa0b450d04473f7308d5c91788b2325d6da6", "size": 2906, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.2.0-alpha.1.tgz", "integrity": "sha512-Z2e4r0D4NO41uhkRdv91yv9Oag/W+CjKmahhW5Jabxx5XoBU38xWv0QuZcyCKllHWo1sYkg49rODxnYiaDpImA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.2.0-alpha.1_1596545985699_0.24808750453143524"}, "_hasShrinkwrap": false, "publish_time": 1596545985914, "_cnpm_publish_time": 1596545985914, "_cnpmcore_publish_time": "2021-12-15T17:04:54.587Z", "hasInstallScript": true}, "0.1.11": {"name": "vue-demi", "version": "0.1.11", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.11", "dist": {"shasum": "52ec60c1b0d6bbff0a8bfba3e431ad0458e33e5b", "size": 3168, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.11.tgz", "integrity": "sha512-0pgKQ0AJpau3CPlDOS9j3HXUdp2X7VpMJ1IXpab9qZe4mjaoTJ9MCzu0mKichGFcs1NgMLUdssUw51EbhNDiKA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.11_1596545542859_0.43214240425956985"}, "_hasShrinkwrap": false, "publish_time": 1596545543039, "_cnpm_publish_time": 1596545543039, "_cnpmcore_publish_time": "2021-12-15T17:04:54.805Z", "hasInstallScript": true}, "0.1.10": {"name": "vue-demi", "version": "0.1.10", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.10", "dist": {"shasum": "0950343442795e243796951e5fc5d5a49e48a118", "size": 3155, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.10.tgz", "integrity": "sha512-rn1Kcz4LjJp/M7MK9jMTDO7+/Bm/+VBclTjgsjo2rsU+rLy8sSTjO/ckJ2s1DVsQJP9Vnm/+PwGD7zvRDXpnDg=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.10_1596545322698_0.6604365298262955"}, "_hasShrinkwrap": false, "publish_time": 1596545322852, "_cnpm_publish_time": 1596545322852, "_cnpmcore_publish_time": "2021-12-15T17:04:55.033Z", "hasInstallScript": true}, "0.1.9": {"name": "vue-demi", "version": "0.1.9", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.9", "dist": {"shasum": "0bbf7b774158f1f8b2192cf309c9a61991c6ca8c", "size": 3159, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.9.tgz", "integrity": "sha512-76cqgHpFert+LxFMlgdH9TypGuin4jXVogE2Ge77Cc6uTR6gcZW9qg9kScnthdpQnJ1/VJ3XEkNLSPvmZjl+1A=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.9_1596545183898_0.3986488122168319"}, "_hasShrinkwrap": false, "publish_time": 1596545184002, "_cnpm_publish_time": 1596545184002, "_cnpmcore_publish_time": "2021-12-15T17:04:55.325Z", "hasInstallScript": true}, "0.1.8": {"name": "vue-demi", "version": "0.1.8", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.8", "dist": {"shasum": "b52fd44daed4e1a2283f97675781f1a02c27bb65", "size": 3157, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.8.tgz", "integrity": "sha512-gvp+tVNQBZ96tq+OEaDICEFFBZYhgR1TTxYmDMeqEuNK+QFcbZly/y12M52bP/cQCT15Ij3G+4fdEemJC2ad1A=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.8_1596545064681_0.27363365211348434"}, "_hasShrinkwrap": false, "publish_time": 1596545064776, "_cnpm_publish_time": 1596545064776, "_cnpmcore_publish_time": "2021-12-15T17:04:55.565Z", "hasInstallScript": true}, "0.1.7": {"name": "vue-demi", "version": "0.1.7", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.7", "dist": {"shasum": "41e47ae031165d444d6d3360c4549e467413a7b6", "size": 3177, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.7.tgz", "integrity": "sha512-BRslvK1lW4cN35etePUw+C7aNhW08GgklQ3r7cJ+MgUyKz66UF62bja/55Bus7Eh2wuxb37Tz4PNmh1jqVnCXA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.7_1596544890198_0.4794981835940697"}, "_hasShrinkwrap": false, "publish_time": 1596544890332, "_cnpm_publish_time": 1596544890332, "_cnpmcore_publish_time": "2021-12-15T17:04:55.801Z", "hasInstallScript": true}, "0.1.6": {"name": "vue-demi", "version": "0.1.6", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.6", "dist": {"shasum": "56caee8f69ebfd2dc91023ca1e8bcf2791d718aa", "size": 3188, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.6.tgz", "integrity": "sha512-2yF0bN9Qmxdqjy4RtET8pxMnWfGhD8zzHqu2k0g7/gyBH/PlT0KxdQTnH8HB0XKRl1mtTOBlo4Tqz3cFo41oAg=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.6_1596544738982_0.34010426557003504"}, "_hasShrinkwrap": false, "publish_time": 1596544739096, "_cnpm_publish_time": 1596544739096, "_cnpmcore_publish_time": "2021-12-15T17:04:55.994Z", "hasInstallScript": true}, "0.1.5": {"name": "vue-demi", "version": "0.1.5", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.5", "dist": {"shasum": "d98537e07d005ce0aa774b81eaa7300d55908447", "size": 3183, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.5.tgz", "integrity": "sha512-XCoaPEmRCUmNowuek9fM4CxImUVNUK2A7FHXqcCWEWiQZXglv7VOKH1KTIw5cAQ8bf0surCJphhspf1PLNMrAA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.5_1596544551949_0.49615786753493585"}, "_hasShrinkwrap": false, "publish_time": 1596544552055, "_cnpm_publish_time": 1596544552055, "_cnpmcore_publish_time": "2021-12-15T17:04:56.376Z", "hasInstallScript": true}, "0.1.4": {"name": "vue-demi", "version": "0.1.4", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "unpkg": "lib/index.iffe.js", "jsdelivr": "lib/index.iffe.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.4", "dist": {"shasum": "640207ebc8244cd994ddc06c86ad3bfc16b4b7f4", "size": 3156, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.4.tgz", "integrity": "sha512-8Hu4A9jLGK+SIIetMamf81eT2m9QUsiPaXSkiDTuPo9qEhSt56OCH/X+kmmg5Pg6kfzdTzKvvjlDBKWd41A7pg=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.4_1593873358862_0.9193989933831754"}, "_hasShrinkwrap": false, "publish_time": 1593873359001, "_cnpm_publish_time": 1593873359001, "_cnpmcore_publish_time": "2021-12-15T17:04:56.628Z", "hasInstallScript": true}, "0.1.3": {"name": "vue-demi", "version": "0.1.3", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.3", "dist": {"shasum": "e16321d20b025e95227fba01607f86e1fc9726e4", "size": 3019, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.3.tgz", "integrity": "sha512-0h9/PwtrJx8oZeOd4ftGNlpnWUxp9HoGy+Wengw4pvdUi0o1+PLMaCpoQZ9tNGnTom8cz90M1ydxJgJg3InCpA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.3_1593762333327_0.7010754269337511"}, "_hasShrinkwrap": false, "publish_time": 1593762333460, "_cnpm_publish_time": 1593762333460, "_cnpmcore_publish_time": "2021-12-15T17:04:57.054Z", "hasInstallScript": true}, "0.1.2": {"name": "vue-demi", "version": "0.1.2", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "bin": {"vue-demi-fix": "scripts/postinstall.js"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.2", "dist": {"shasum": "5f5a4b13a6eb4b04ea58183df72efcde387093f9", "size": 3013, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.2.tgz", "integrity": "sha512-igW769ogvhjSIMAq00Vzis3NroSGHdHM3Yloi9ekYFVZomFZjiv8emYmOQE/QXK7uHBERN+/5gNxRs+fRNqiMg=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.2_1593668329859_0.40539911168660225"}, "_hasShrinkwrap": false, "publish_time": 1593668330001, "_cnpm_publish_time": 1593668330001, "_cnpmcore_publish_time": "2021-12-15T17:04:57.322Z", "hasInstallScript": true}, "0.1.1": {"name": "vue-demi", "version": "0.1.1", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "Install this as your plugin's dependency:", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.1", "dist": {"shasum": "430da877e0535de01e402126237ec7eda2859c9b", "size": 2741, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.1.tgz", "integrity": "sha512-AdX+R9bWUikYFyyeaABwTudZ2cvrNt8BJIbwFp3nM4tfqMgHw3XifzeCXaMe/zowrjI3wYsYe4dx9PTCxNtAiQ=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.1_1593622149828_0.91556557245721"}, "_hasShrinkwrap": false, "publish_time": 1593622149971, "_cnpm_publish_time": 1593622149971, "_cnpmcore_publish_time": "2021-12-15T17:04:57.607Z", "hasInstallScript": true}, "0.1.0": {"name": "vue-demi", "version": "0.1.0", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> 🚧 Expiremental", "licenseText": "MIT License\n\nCopyright (c) 2020-present, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "vue-demi@0.1.0", "dist": {"shasum": "ad28ed6bfb21ed76f00929f62f7a52a6ef050752", "size": 2651, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.1.0.tgz", "integrity": "sha512-NUumQ9x9qEgg08U258kPO4ilG1sGUmw31nQgrTnmwocjIbRLQsCy7yjmKRSWRY06f9hC3W7qNZcb64qjOH5MDw=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.1.0_1593591778556_0.33068661214902684"}, "_hasShrinkwrap": false, "publish_time": 1593591778706, "_cnpm_publish_time": 1593591778706, "_cnpmcore_publish_time": "2021-12-15T17:04:57.832Z", "hasInstallScript": true}, "0.0.7": {"name": "vue-demi", "version": "0.0.7", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.7", "dist": {"shasum": "e758e19a58b169334956f3ec45b6ec76040ddbf1", "size": 1881, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.7.tgz", "integrity": "sha512-qTxAy0s9Wc5rn0qBi3AyiacjaMOGuFc5tU70bMgt2kHgJwGafaxKtt2Xr6Uo0CECaqi4MXlCZR2pssheBEWusA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.7_1593584405213_0.4898749129803812"}, "_hasShrinkwrap": false, "publish_time": 1593584405337, "_cnpm_publish_time": 1593584405337, "_cnpmcore_publish_time": "2021-12-15T17:04:58.135Z", "hasInstallScript": true}, "0.0.6": {"name": "vue-demi", "version": "0.0.6", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.6", "dist": {"shasum": "6998b1906cc790514568a7f07728ced5267966cf", "size": 1902, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.6.tgz", "integrity": "sha512-lqAAOvdfwMbUL2qpgF2dtVgvhTbWNmDf3bR7YbfndQUNGy1lRFu+HYO1cGZB8rGxaPxbftATbPaXbP3GeEGhBQ=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.6_1593583010483_0.5842762365570036"}, "_hasShrinkwrap": false, "publish_time": 1593583010646, "_cnpm_publish_time": 1593583010646, "_cnpmcore_publish_time": "2021-12-15T17:04:58.366Z", "hasInstallScript": true}, "0.0.5": {"name": "vue-demi", "version": "0.0.5", "main": "lib/index.cjs.js", "typings": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.5", "dist": {"shasum": "5c09a693da172b7a0bc857241664618590026535", "size": 1876, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.5.tgz", "integrity": "sha512-hxHdHmQ2QhWrdEhOGZqU3PQGXBMNdOH/wu0K3L6pi//4EguyHjRqzlfzJMMy6/7tkeQPnd+6WC7PdDLEao8ubQ=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.5_1593582842973_0.5755048594503815"}, "_hasShrinkwrap": false, "publish_time": 1593582843264, "_cnpm_publish_time": 1593582843264, "_cnpmcore_publish_time": "2021-12-15T17:04:58.625Z", "hasInstallScript": true}, "0.0.4": {"name": "vue-demi", "version": "0.0.4", "main": "lib/index.cjs.js", "typing": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.4", "dist": {"shasum": "b304c8d915e20a577c61801b8960677d2b9ba38f", "size": 1876, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.4.tgz", "integrity": "sha512-zRGS5P0BOL2gxOnkj7EB3tcpYt7EX8wAHaeo1M1jmxm0kXs5XIgQ2Sq5HZOOLp4M/lBvV65BkQbsj45zwJgheQ=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.4_1593582412165_0.014679643551655852"}, "_hasShrinkwrap": false, "publish_time": 1593582412349, "_cnpm_publish_time": 1593582412349, "_cnpmcore_publish_time": "2021-12-15T17:04:58.941Z", "hasInstallScript": true}, "0.0.3": {"name": "vue-demi", "version": "0.0.3", "main": "lib/index.cjs.js", "type": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.3", "dist": {"shasum": "8c6e8fe55b2df6a5f9c5f4c5e7972628f7ceff71", "size": 1865, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.3.tgz", "integrity": "sha512-cPOyf1yXePLG4J+VRX+Ifqa6rGlXjK7LZpUhQUNPt/s7Cqa8horv4se4POe+TJup3kIFJoFBhBFIwpKG/k3G7g=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.3_1593581388153_0.46389087557533903"}, "_hasShrinkwrap": false, "publish_time": 1593581388267, "_cnpm_publish_time": 1593581388267, "_cnpmcore_publish_time": "2021-12-15T17:04:59.150Z", "hasInstallScript": true}, "0.0.2": {"name": "vue-demi", "version": "0.0.2", "main": "lib/index.cjs.js", "type": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.2", "dist": {"shasum": "ec4e9f885742276c7eed4959b895049764f1a014", "size": 1860, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.2.tgz", "integrity": "sha512-jP+9xZiD/g8TTZ1/ZrHkWYEhSsDN2iObmkJI0Q8rvPz7aN1gCs89vq7oN+sO6Evk8cmFW0yEpiEKeJdf72KfTQ=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.2_1593581102627_0.5084755286812239"}, "_hasShrinkwrap": false, "publish_time": 1593581102750, "_cnpm_publish_time": 1593581102750, "_cnpmcore_publish_time": "2021-12-15T17:04:59.383Z", "hasInstallScript": true}, "0.0.1": {"name": "vue-demi", "version": "0.0.1", "main": "lib/index.cjs.js", "type": "lib/index.d.ts", "module": "lib/index.esm.js", "repository": {"type": "git", "url": "https://github.com/antfu/vue-demi.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/postinstall.js"}, "devDependencies": {"@vue/composition-api": "^1.0.0-beta.1", "vue": "^2.6.11"}, "description": "> WIP", "_id": "vue-demi@0.0.1", "dist": {"shasum": "9925fc15a1207a3289ad306cfbcae5c9817d608d", "size": 1867, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.0.1.tgz", "integrity": "sha512-klHkxyfc8DTk9bCTWJzK6B0tftNOYfBH2P9DVm7k+36iqTnvsEtrs13VWcb3bPkkixGk0Wk/TSVNSCeVT5QUCA=="}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.0.1_1593580722468_0.03874992951658918"}, "_hasShrinkwrap": false, "publish_time": 1593580722643, "_cnpm_publish_time": 1593580722643, "_cnpmcore_publish_time": "2021-12-15T17:04:59.741Z", "hasInstallScript": true}, "0.12.2": {"name": "vue-demi", "version": "0.12.2", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "773cb00eaa783fb95d4d5eed2ee81a5f559f89de", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.12.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-8RRLmYfxfiRoohEbZ44CSxpNAM9vcVBMinN7Hucdxxy9Pjr2PpuimPMLIW143+GzkrHsbY95fsFFzQOouFuetA==", "shasum": "7283bd94b207709e834ef77d3086508ee4fa921e", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.2.tgz", "fileCount": 18, "unpackedSize": 18667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMEMYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5Vg/+ONRii5U5tYNnUUttviERwGgysrQeGf6J/vSqKFz4CbLY2Cpz\r\nGUFbN2SD9CTKkCrpE/Z81DX5AXlyYVhiEBWL/hoGVzykLryf9CD+XyfwHTUq\r\nrs2th12W1sfo0VCUCWLEG6dDNiazzsUZBUxAgppQ5fEdBGzWP0sT+QoBgVsL\r\npvvHnyZuPk83FtrQcWaG3+ibvQ3PLmX9yj6KCzlOZpjFFXVRpS5UbViwWW6T\r\nrxZNsIeDn8ikk3elpFgS916YEy1H9VIAzanFMqz52UVSCfvhgfBv9Rs8NLxe\r\nu82n4WK7Ymn0foRu0VV9ESR89d5xFlTMYm8xdZXFkH3grO3NotgEDDzp6D8v\r\n/zz9DGkv4OIioKmij1JSWdmi3nMYz0YioytRNt7IYaRwwTjweltQG863d0H+\r\nHzSKKkf1EpZrUiriz7TcesEgHlTpi4wzTOg8FEFjj4rTputUlnisc0UFUKma\r\nw8I7BWoQKNWXKpU1i6lLXI5HSu3Sdyah11LPfdOEgCfkcKkTbc9YqnLqn9G/\r\nS1AaS62k7Sf1mr+XpNnbrkWZVhkYc2qs6742790hatpm6uY6S7lF/t1tw83S\r\nejDpeJZULNNd9BqFNT5Q/MZ6gDkYwyHA2Ap8bH0Xd1BtONQzoWtkZIgUIAG0\r\nqXx9SQZ7+imFEL9/1BHeSMMGTsgb/Ku9dA0=\r\n=29Vb\r\n-----END PGP SIGNATURE-----\r\n", "size": 6282}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.12.2_1647330072002_0.6247900491312282"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-15T07:41:16.652Z", "hasInstallScript": true}, "0.12.3": {"name": "vue-demi", "version": "0.12.3", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "16a6c543524bf530be5ff3066e63f8a930ee6db1", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.12.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-u1dV2Y7nxUCHJC0+KZG7YgqjOgDgd8GS0rHp2YTiXy6rBPaYvEraoXkndU3UrExMLoN8SOnCWkDB27yu0BxGtg==", "shasum": "d1b368e0a4955593b63678457a9a456ec21884e2", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.3.tgz", "fileCount": 18, "unpackedSize": 18628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiML4qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr6A//fgGXhFU0enSVmgI+mPplOLMAAnqbyaOuImdti9TBsMoYuqL9\r\nn8NtqoPlCumLNn7RO9ghla4Emqlnjn7DfnB4WQ/xDBGaaAp9aEGkeVld5Ryx\r\nyn50CTnFwOiilRY6TNM2bqL1889HmUrEI5geadLun1ZWZo/2KJmhQHS8wKi4\r\n+nBe3QBVIP7Zmip1fMp/l+X+9TD5YcHR1LL2MrBgU+X1yj0nvQDB2mXaQDBy\r\nGvzLx4dWFxNTTvmEY2dlZA+HHIn79iZ3cIL33Dqe5xVk58ZLTbq42ox4fVwE\r\nSnCkBnPH0OiA8fKI2QjpyXot5TeBk4yB3yfPCiIo+m+9nqZaf0VsstjynGHj\r\nooSshFAfEfMQmN0aarFBd/9nPFLmsZ/Cfy0WKZ3HUEOAGWWmPyNhyqWrQIie\r\nHlHVvPE/eDIJnQ4D0x7T5MNfCm2fCRWXgT0PpPVCMEJDXFup2oQyqhsuAtPC\r\nGVb6OLw610keaxX0/y/9Tf+yfEweKd0v9d4OJGB5SYJ4gYTOWbDVuHMSVc0o\r\n8QLHDCvzMqlWxu13mxHO9BD96u8AQ+6Z259PWVJm3nzWm+K65AYOlOOGk/2K\r\nKcm6J3Wd0guBWA88s8fFQWf9mHNjvybH1ULCMP5TEZazM2Oxi3CWmu1XCYiT\r\noMXZ1+MSBVmPQSVZ9NXXdusKIY1nbaDskGM=\r\n=IKDh\r\n-----END PGP SIGNATURE-----\r\n", "size": 6279}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.12.3_1647361578654_0.18073381040241854"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-15T16:28:56.474Z", "hasInstallScript": true}, "0.12.4": {"name": "vue-demi", "version": "0.12.4", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "8dec7058e78c8491ed36f762d28db416633c7206", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.12.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ztPDkFt0TSUdoq1ZI6oD730vgztBkiByhUW7L1cOTebiSBqSYfSQgnhYakYigBkyAybqCTH7h44yZuDJf2xILQ==", "shasum": "420dd17628f95f1bbce1102ad3c51074713a8049", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.4.tgz", "fileCount": 18, "unpackedSize": 18636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMaaEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpjnw//bCIOrkiJZFFNAG0ngjxDWD4pAIAipHYBdtZyYNEwpjeQLjCM\r\ncQe5BjUG8LPKREuHP1w1PtunlKosW49UYJtPwWNNbh1SxYmGKox5aTrd+ZAn\r\nPQklJr3l6pj30BFCUW5Qp+2cSa2w8VBPJFppwsnGK0R1nOExVRJyn93j+sC+\r\n4GK58/hRXhhm90+Qs5QtsYWtGYvlbu0kp9OyqgDSzuLNfKcN5twbo07SqZJM\r\niiWDaKkfGJocX8C4qWDYWon2bGXO7UeFaos2fpuF4iBwHQ/w6R9G9Uy2jSDO\r\nSnCRwxL4nTo2csJ4s7sjphLgOF8Cx0VzijGjOgZw0l4xkPbnO+CbPH2vqmGQ\r\n9zZeVnOhfPARBoHnpSJViZ4l0PuiF4eUHMLP9jZZSz3DZliiEcf1QiAbeHR8\r\nbrLJt06FosTE/xKfbihopJV5Nvz/G/lwVbWKeQu/4ItZt93AC8babMyBPGCU\r\ntp6q2az+3Ngux8XKmzkgBrhd/hiD0EEHj+rbCd3d309Q+sRGH8/AwOdHyMTD\r\n/ImBKjeKcv0xVZpPrJnNpHKI3VaxKerMQGeDv5KDU1u1j3ptsdrztossR8T0\r\nRMhNA/Sjn+R6meCxzUcm4lwZm6NvzJMe9iQ2JJ3onfCzUBybI999mI6Swbtr\r\nZHJ2rlTVlh4lGNECD5/npC5n/L9velsRIoA=\r\n=JVXp\r\n-----END PGP SIGNATURE-----\r\n", "size": 6279}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.12.4_1647421060585_0.6251666149597592"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-16T08:57:48.554Z", "hasInstallScript": true}, "0.12.5": {"name": "vue-demi", "version": "0.12.5", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "b55bc23119f11b63fd4dda6d277ecf3ea5e26952", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/master/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.12.5", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-BREuTgTYlUr0zw0EZn3hnhC3I6gPWv+Kwh4MCih6QcAeaTlaIX0DwOVN0wHej7hSvDPecz4jygy/idsgKfW58Q==", "shasum": "8eeed566a7d86eb090209a11723f887d28aeb2d1", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.5.tgz", "fileCount": 18, "unpackedSize": 18809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEeQjw+gUzK92UB79ikFmtUkkv1nkJp8Qk/okWV/LZFdAiAkZuTkdH861tlxEc65BaahLldiTGcHNxWa3Rpx8KgrVQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRIbKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvgA/8ClSwAkiKxBN7mXtmfiAonI1ajgJmTUmgf9TjthPiJa8quJK8\r\nxV2DutPAu34shcD2LAI6n1+yRGfDenNCwW9c1lyfcmT6ptu0YX0cEUgBCH8q\r\n7dBf5ugCVbsnm3nrsj1WNOjiwS/ohQ5KcSjDDK0B34TIpLVf5qOTv1L7Pt7u\r\nLniA/EZ/JAHvtx2Tv/uTxceJCSl4rhVKlKmb8DJUtqKmA0e4+rXlrRq8q8Lf\r\n6qI3Que/2tObD3zNpmH8k2cB//mklNMH95YuLrjtqAgHNU7F+ef+fvcrQHKf\r\n3GeazTxQAU2mCxhRcxWWndSeJwowSC8ilaUyuUwT/4TZAcLxrmaaHL2b4ZPW\r\nTL/UpzlwxgIrRbhZE9KxcV59j6wV8TMQDc1mXW1pgJbprMHbd/yS4XR/YOh7\r\nyPZiMNEJQcrrlO2P8BaWm8XtYF+WehXr2MdDqLdIhA5pqjt9smJbEY9gl2mp\r\nXyuauF/1/wUd9ESkdFwLJyawFLVs+8cw3xxvyb4nKhFjBfdWLB2dF49Ywq11\r\nyBdidooMUYCCn3LYcS89jXn7z/0JR71PDWOL2W6t3DEoELdrbfcn3qO3n8pn\r\n6Nuyub4sE0xN3Fb2t8x1aJ0nsGl25k8SSFdYCZCLmErWL8G7JS0EbDhYD+tH\r\nrVZhfCRVL0Zk43/qWh5UtP69Q3iCwxOKmZ0=\r\n=B2ES\r\n-----END PGP SIGNATURE-----\r\n", "size": 6314}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.12.5_1648658122637_0.5727245371492775"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-30T16:35:27.530Z", "hasInstallScript": true}, "0.13.0": {"name": "vue-demi", "version": "0.13.0", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "82bccb7e2d6e22b9786f1a2f49ad841eddbd888a", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-pu9R4Nydj+LMcKkIL1cnP1L2anJBTmVZzVWSdy5P1rkSRs9fxGCFrZmXdPTEsN37m5JtBVBSYKPk6xnORfjUsQ==", "shasum": "76c64a8987460fc242bf900afe8d3afc0c600d24", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.0.tgz", "fileCount": 21, "unpackedSize": 21784, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4aVcTMtNl1mV5MlVSmLALSEHJtH1Y2+lg0zXe4qCm1gIhAMvFKDMUtknYr10ki9xP5emnDoUPmgbFzj/IDj2c+6+/"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilgitACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq11g/+OH4vXDsf/0DbIH04Zz4QLh6BkyEn8GRD4Ag89OFDK3O2Gn1f\r\nZspRpIdYXrQERHZlTUmTOgzee+N4uPXrX3MeXstzwd3hffTG5tmJ2e8e8KgV\r\nbbh1k68qCo2taG36XXrX1N3VvqdKtIdG2FVGeA4EBDFNqTe9reRlF7ywMRrK\r\nvh4r/F3bSlqeRnMlEXaoe1gFsqRa1nCg7NZE/CaOLRaV8EJ0acjK7Y9reGvq\r\nEZW3L/w4LUmiMqzTzF+ZmbO5feRQ3kSDX5spdCHhORs7xr09nw9oSG6gGLVk\r\nLosBAd7H2vj6uFbE7fLXvEn3C3eS2RFFtrEkcX/aon5RhYRSOq3bXBlqJS4l\r\n0O74hUtQLfXnO+eoLDlxx9bVSrqY4fqgFS62lJAWrcsobhT7JFSIcbrKsg51\r\n5xPSdzdCtY/Av+AgB2PUYDwsxbKQbyL7T67j4FF2XE3n60GzKjF05jY2TiEf\r\n+gF4AX4W8lN+CmgT4qQbTyFjpHMj9EqZ9D10oweLc85a3lG/Ujon0uIhRpki\r\n4NfIwf7vjKc9BsZqwrC4UJBSrusegLuX8jwg6K0922Uaxx6tN+J4cvmoZZcm\r\naoWqduz8b7/dqckRq2QonZwK1cVgGonFIhPdD9gla0zg3ZnMKMu/8wXvYrya\r\nUWVr2GlAs2uJJlsz3hihA6vev09CXIvoPeg=\r\n=cq55\r\n-----END PGP SIGNATURE-----\r\n", "size": 6500}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.0_1653999789650_0.6698133495351541"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-31T12:58:06.538Z", "hasInstallScript": true}, "0.13.1": {"name": "vue-demi", "version": "0.13.1", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "0146526f17ee4b7f985572b1e306508526d543fc", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.1", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-xmkJ56koG3ptpLnpgmIzk9/4nFf4CqduSJbUM0OdPoU87NwRuZ6x49OLhjSa/fC15fV+5CbEnrxU4oyE022svg==", "shasum": "7604904c88be338418a10abbc94d5b8caa14cb8c", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.1.tgz", "fileCount": 21, "unpackedSize": 20490, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLHUNgu2IQejePyDSOVrIOCRek05/0rqdGSXwZFRlxOAIgWQA6m5+7uKMmwjNd/OxCLbKPw9F93XWHPnhU+fGuwEo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilx9eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwOhAAo5NRWSc6JlyB5RFF41BPPOxF85JD32LDpBSJy1Kiuakg4jO4\r\nvlke6SzTD7Fm27L9Zh/O7tu5ZeXacc0RT8U+aM9mn0vnwKVZ/ZQcm5Md4sbM\r\nVn21JruOGg6gykC4dS+7sgssR5tAqxwrACRYouvNkZVRVAsGw1boW1PFHFex\r\nd8Oy0WjWpOMruYTNi/74Zeauf0c1AZLDQvEKXS7cWQgUJUrqtosVaeWc3ztF\r\n3b6hrWG9JJKuM8UWKScIWFpmaz7xf+Za8L8hD3t2BFsalAUIqn3/i4Ikvmgs\r\nwiz9jr01V+rE5aS+nmCumch6vDQo6mwCWAg+K0odWbxkJopLdZNK7gfZa0LI\r\nU/B9toZ419Qks7g3ZdBpX4NsMZWUXNwi39mBr1Bg+TC9U2nowfQCwgmI8Yk0\r\nT/AbrZiQoQFSdmK2cj6okXYJE7O7rQy8ZkFEmYbUrNXjShnbM+U0Y0dF/NeW\r\nvM9ImMBf8ybzOJTpr5XfBs4CLIe8uWUR915k9Sz/SJWIfkpPzZBjzKg+I3ou\r\nJhQMdTVK0H13w84GzpAz86PtfqxphubeYoOAaiX7rH50GOZmynjZH+NsDnCC\r\nwWsIPSlDHa40HIs+7ln65qkHJDYjs7WW7wgQIjVSnnj/zFJ/VR7oYfQ7mdIt\r\nKdgNzD/H4bVzfts/qJWswHbaMVDcdg6MufQ=\r\n=IEX+\r\n-----END PGP SIGNATURE-----\r\n", "size": 6460}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.1_1654071133760_0.9180823183124782"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-01T09:46:06.698Z", "hasInstallScript": true}, "0.13.2": {"name": "vue-demi", "version": "0.13.2", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "fdc2a1b38a041319a91ef8086758abb93372cee5", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.2", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-41ukrclEbMddAyP7PvxMSYqnOSzPV6r7GNnyTSKSCNTaz19GehxmTiXyP9kwHSUv2+Dr6hHqiUiF7L1VAw2KdQ==", "shasum": "f6081c2feb5b698549729a81683ce029b8da289e", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.2.tgz", "fileCount": 21, "unpackedSize": 24063, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHyCawwp47KdrnOyvvQQObk7SO237NKmT/bCqs78JKXAIhAO6IJilFhAJAEVMtbTfH4jYwWFr0DgqyIAEOArIvKmDd"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiw8EZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK/RAAmmA1KznELSBPgOoydmZiOG8/OBxtzpbOsYEGf0D3/toTgH2J\r\nSTDZMBKXrc163hwDdkxl6JMVle06jjRZle1QSjUfxHEC5TAO2pugcBxf7V8T\r\nhH48ryi3sPuT/ljt5qN6OZZzZLRFslx2v9hE/r/ZY+Bnwfii+Df4p3Nj8h+o\r\nWLwBvRA+klbFMTFDnRrxIo9iCTWYXjPS644kVXHxY5B9A7M2YeXSaHpvWiKs\r\n8RMAmrS8qmkne35lUQWOSLXoq/E2WTUDx/S0wx8CmAshSBV8KEXxNL5Atl7y\r\nZao5k8stPXkKfzbvomDoYlwUHcYegLDDbOZ4K9RhNPe4Qf5nNKWp6pYHUITH\r\nd4Y9sSIdRYvNT4GyqwHzwlzADg+FbDeSrARRxy2cXf+9iLbWo8F44xYjGZe2\r\n022JbnYifrPtbpM3TbkvO7nYFMKKFVcYEMvyxtLhYntG9yYKGvtMD5uYW0JU\r\ndirK7OzAIqsXlYzOFspdNAFhz/AfrsD7z2dTJ/eNxOlmPAaqkMTEcqepXlOS\r\nCRu/kc6UUxe+1z+48nrj1OW3Ok+8doxTeoks9MWlxPDU3Bbaks2rVchcCAx4\r\nLsm4/GXnVdhrE3om4NMnp7VXrAHEmWyxRl+ukyG5fvR1TcHQjV5kYb15oGcH\r\n12V4t9skhPk5Kkw6NwMMpeA+hs9+qgrH9UM=\r\n=hcPM\r\n-----END PGP SIGNATURE-----\r\n", "size": 7324}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.2_1656996121607_0.25390543709232927"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-05T04:42:09.351Z", "hasInstallScript": true}, "0.13.3": {"name": "vue-demi", "version": "0.13.3", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "930e6d5053e7ed82f12258e2014cbd4e81bbb263", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.3", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-t0WemjxNSrrQPP1N5Aido5oLdpHdTh9Z4QS+iIHMeG7318N5FF6y1zb48umb5Wh0Tq68fCM0vH2RrYioA7l1eg==", "shasum": "6f761ecf72c6a6858407b22c0a8bfc24a26d2915", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.3.tgz", "fileCount": 21, "unpackedSize": 24037, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7zlKWsUgvfyl/q9t2cRVPXt4RfFLSEDYTYkLH7nHrWwIgUKxQW1WiKIPRrqxNlaNeVE/dJFFF7ngAaz6QQhpY2e8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizPnjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOPw/+K4p8iJyff3vllFwKyU7iP/3oBnvAW9XQKREHOZ4VRthy+YLH\r\n8umKJo7xViuJ7lcWGVzgCH2JSFYvAnHPktWKJb6aL31SHPS5CsBeot5kfPHK\r\nyPFhBTsnDOj27nQRcrtOzPWdeT+gJsyP1pmlYOiuo+DAxEMIpL17ONyQy2ye\r\niIvxjSJ7v9W5/iqgeXgWcYPYi/wMksl4xAfoUkXemB5KZsBGTx02qjp94r3u\r\npXdCeCndYH3uJIg61ksGaNYiLqf0wuZHJ+PoNcSRwmkk/imcCfXcvIwk9/TI\r\nxVZzbLzlBuCGh3bhhGgr63U3zdVZk68uQ3lt+aITRU/8FJelQkOHP/ofyZMp\r\nud/1T2hsdNSO6CIt0hsHx0O82K68Sp4pTebdIVb0D5V9hqJYT8i/iET/llcA\r\nEF6tN87PTJakGTU4KKJ556Ebv/QnB0vDoIF5Oh9lfzmsHCJ3OnLpHgyqNxSY\r\nWdr+y9iYc0/5UxWlWLWGJffwnmMpR4G4/5Acms+FRLg7lTVTrBafgj6qgLx6\r\n4AsO2YWkhHTVlnKj6QgKAcoV1BasFMaV6PgJXwmCdQsTuiHtxg0j6iKNiGYu\r\nYoRaxbbwOzgdzUQywWdDUh6BGKUr+lc4yVafcLAAqyGYThxx7Mn+w5rNG2Y6\r\nNn/+bRC7KcuQMZbM1fz0YkcF6KBdwOwrlFM=\r\n=XmMl\r\n-----END PGP SIGNATURE-----\r\n", "size": 7325}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.3_1657600482829_0.3215770351223286"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-12T04:34:55.947Z", "hasInstallScript": true}, "0.13.4": {"name": "vue-demi", "version": "0.13.4", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "dbe21e47cf3a695db145ef0af4d02db6eb533498", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.4", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-KP4lq9uSz0KZbaqCllRhnxMV3mYRsRWJfdsAhZyt5bV5O1RTpoeDptBRV9NOa/JgOpfaA9ane88VF7OjWNK/DA==", "shasum": "fcb320892d78d3a7ec227024776f77d9a5c4831d", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.4.tgz", "fileCount": 21, "unpackedSize": 24072, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAlAz+jM9bJyngzM8dd3KkBit0HM5NXN4GVMwdVx2nk3AiB9sOhgga7BwWVdLRowkNBogpt9Qd3mSNP6PD5zzF/h+A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizRVoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUfA/7BQrPLI3PvIyT7X7rCKTX8Addh3vmPdGmWiTZRRIIo7fV7bGV\r\nLyfz0LpC6E4qJhQ7F8yW1dBLR9gdz6krn93gcN3Ehtebud77q8tsgffeqpJl\r\ndhVj77Rt5zy/bJIoeKb0aRstiBCoYiLCK8cJSfhHtC4PulF1mroGh82BXyDh\r\nksl3gnYcmvehUldP7+4fEm2qEe0VC0cPmuUWxnk/dn5mcIUWbnoU0uzSzrgf\r\nZ7cKB9mCpaZSIxL9Mbv+/Em1DtOUWRH7qvC/6jjKCC1LZrGxjLuis+O4al8n\r\na6qXwtYvbjQbXkyK0BkFFrwmgzNN6QKXSn6K6mPizDi8ljBLxFtM9ckWhr8+\r\nhWD6+GG0Lh+QhF8PEHQ8jioLK5mNE+M763QVycKqNGso7LWrXH3w6CxYQgiF\r\nXrAkm1MreruYf8waURiEg09WDn/+cWNdwUHp+t8N6HXIaxycDfXmtzIvbW4G\r\nUUcJhO+kbjLin0eR1sc7Ftcnpj72s6ogXwceUKy0Rv0UsmWI4SABkxMLV1/C\r\nyrSk2Iy99UfvrxrdQV3VgwNHBJpZ7PJ7ZGRqxKqfq1P4qky+fZ20S/Uptcky\r\naQEZUZxqLB/mC4T2BUk7fX/NRj9EMsauMvKhNIcq/6ioy7oGjJhPm6BbE8Ft\r\nH3PO2yIJqOlhqz8+asS8DO95VOJIPpJw/7k=\r\n=D81E\r\n-----END PGP SIGNATURE-----\r\n", "size": 7331}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.4_1657607528513_0.7038330682127636"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-12T06:41:26.965Z", "hasInstallScript": true}, "0.13.5": {"name": "vue-demi", "version": "0.13.5", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "6c857d44cffaf064515b8f7494c403b6bb5c5f25", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.5", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-tO3K2bML3AwiHmVHeKCq6HLef2st4zBXIV5aEkoJl6HZ+gJWxWv2O8wLH8qrA3SX3lDoTDHNghLX1xZg83MXvw==", "shasum": "d5eddbc9eaefb89ce5995269d1fa6b0486312092", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.5.tgz", "fileCount": 21, "unpackedSize": 25063, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJa7fy04hh8QiezhfO+sXv+0who19RxpaYHXh2eycAEwIgaZuNHUTtBDGuFZTMpkYq4SS4M7/6CE8u4DM/wOlDWoY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0l2kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprwQ//a4c1Iq7l5VFOm72VrP1G+ssKgzk9xGItyGlZ78yaMAe56+1l\r\nLXGO4MNpU63oOVd+gYxOFsdScqPzwCPFpidSX/lxsVPyXfxCtWU4CGsgltrH\r\nTuOARs/W5dr8mTZGZ9ULzcZdMY0AMo9ZrgKVe2A6IGyx/Y4aajUsq1Q1kGM3\r\n5b/H9vt1u44T2RO8YQFxqGB1wvhRqCEjS6v9okKUNp/4eNeKUCxmr2OhmDmN\r\nUSBJho0sxb9bZ4yQt0T3cazbNbzzjuLc1NEkTqLkQ4+2+sYliPmaiYfqzbtW\r\nYlmBBHi+aQDEDeH/DK0thsy6YpUVoZU3MEJ6xuJOj8ie3Acprsl1N3Y+7FRQ\r\nUgPp4GHMWeVd9nuVwBTjb8/DvSjs2Cmd6EJ6phKxlgIj15p8MZ/c7iFcSejQ\r\nbLFaW0h+XLt4evB7OHIT3vjZYAcjAlgNscYXtcywAhuOef9NSxhlf7Rz+0pe\r\ncAkUfy5PTnym8QA825q3I4hlGidf2sHgYb6CwIxBqydp+e7P2U9vd4/llQ4G\r\n6EV5zeVC1ptQ02blJCKIewti6ra4qWUo4D7tfgDa0ooVntrJRtfiawLVF4kF\r\nAd9heo3TqNhXgC1hyDTs0nFr+j8nRcFVMW/FkspRxKO+pgF5MNde5rvhGc0n\r\n2TSVnt47L2EU0NoVx2LtJqAoiGr7rMEwqJ0=\r\n=fiyn\r\n-----END PGP SIGNATURE-----\r\n", "size": 7457}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.5_1657953700664_0.4598660467682887"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-16T06:42:37.567Z", "hasInstallScript": true}, "0.13.6": {"name": "vue-demi", "version": "0.13.6", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "48cf6ac872c79184a9b5abc8023eeb2edd9cdb21", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.6", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-02NYpxgyGE2kKGegRPYlNQSL1UWfA/+JqvzhGCOYjhfbLWXU5QQX0+9pAm/R2sCOPKr5NBxVIab7fvFU0B1RxQ==", "shasum": "f9433cbd75e68a970dec066647f4ba6c08ced48f", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.6.tgz", "fileCount": 21, "unpackedSize": 25106, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdgOUdPUo4NZ6zJ2IcikWAfQJCLRnaDHg7eJzYJ3y09QIhALFXMCinh36gU/hs+CzWVt3A4s57De+0EyiWzgGh8Usp"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3+xQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTnQ/+NPVhZ6MATDAcxclNnkVKOERwfTLv5EdjoHSBzQNXsHKLS0IW\r\nJ1IPsEVjt1wbBhNGPwtCCFV7DrSS1skO0LbqAFuHPtGnJPQ2VDsgebfzlZx1\r\nWrKOHZghbskWQVlQ3Par6LV60WjaoPmHoDA1Q5x9ZEoAZsll2NqDNYESWDAj\r\nmO6n1tzGojIxbBfUJTqHQlwQHcWDNhWx9g7VrIAb03NcQHhf3WSwp5vHf4pX\r\nSoXovzZX/o77f4YQ+LzrcWbd9/dTf7iuXnLpqCdzwQ+EfI7S4YRgYsd5I19W\r\nlnbjQekG8KBUmC6ZnUeHjDOKZSxQWxsItnRKZ55x6lfRV2xmmtL4WPlqHTIA\r\nEZRDNhKgogOHXyxbFezu2NPmeborioX1jNFXzzjHgqwBoF63OGTuIC9tI1dS\r\nM+fyUkMRjLU1NLRRpXP+vBoaKEUhpPhxgd1CJsZw992Ae068sB/Zy+SVRFTA\r\nFUV78FFi/5ICI+c3b6AvsugAUr34WYi+JRdgl5Zvr8ih22k5j1Sb89WLPDT7\r\nckb+4mMfOy37M4lbCFD50E/euzDqWyRRVtTfwHS6E3tHXIf9xyC2nrs0khvt\r\nwUnNwxzGDMiAW20HPnEiTDRiznj6ZTdnmZsFGx1emfPKn0M4rgqeFIbfFuMj\r\n4STsOg7Fw4b84pdSbEvtGGPK+O8N5ojMBI4=\r\n=Q4eU\r\n-----END PGP SIGNATURE-----\r\n", "size": 7457}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.6_1658842191814_0.9708313634525747"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-26T13:42:32.430Z", "hasInstallScript": true}, "0.13.7": {"name": "vue-demi", "version": "0.13.7", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "d7d81533fc86cb49b430c425c5a34170b959227f", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.7", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-hbhlvpx1gFW3TB5HxJ0mNxyA9Jh5iQt409taOs6zkhpvfJ7YzLs1rsLufJmDsjH5PI1cOyfikY1fE/meyHfU5A==", "shasum": "5ae380b15c13be556ac4a0da0a48450c98a01d4b", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.7.tgz", "fileCount": 21, "unpackedSize": 25116, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvc4ic/6iVGOeGq1y+BQcqzTqcQO4KWgP7AWixCuvCcgIgZalQ41mhZ3qEyKEl92b4IEuT4LkheX8IbLT3V7ro2o8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi89BMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolfBAAkjXUJFc2GVC2DeU4vXrQu0H0IvoXmibJtvBEsJCYbKJj68sd\r\neF3BaBr1lquKzZ+sxn5dOyVBITZD0xXc1rFG4Th1PBHoMoJ4HT/lt8w0K30O\r\nm6SQtGtH4RJWIei7paPaVd6bn2B/Ymvqg8qGSAIlxQ3K/gJSyem29HX3eFO1\r\nGzRYptzdov4EXjgNJzEyTVsCfbEitgvu430KCbAT1CEHhmD5fV33ehyozFbs\r\n2w+nWvUfAR5gj2eIm77YXYoADEjCzqxBQofTAiEitSs6waxxistf0t/Ya9AM\r\nwac89mzDRCtCuMaiC3/aHbWxszBEkuE+3jruC6nHCHAU1Z7Cnt+iag+2XIgl\r\nQzj+0ayiAqKvwTIZ+oB9kxFTS8uvt102OnquAah0pOKgeGu/PA/ygbYChehh\r\ng3gDXTBrgAgZsQLtZ2ceZMUtQF1eniVT5Ks6JLFvFW0x5uSC3rQEypCWh8mY\r\nExsn9jg/pMMDmHWg2USvtIpiJMJaN4EeQiEpn8B1hG50gAvUXWYac7HhdvSF\r\n2DOxStjt5H7qIerAgMC7WNE8uzuHrRSEQ4yATGAUu908o6Ot9hSoArkMUEEi\r\nlhAdtREUPqq3KBcS5MRaY5yV48aizEvJKZI7XOfdDcDIdO0t8u5eXJDIDHc6\r\nV5sIvia5k+YUSvO9aTTnXuiGKfiy6O/2EIQ=\r\n=fC4b\r\n-----END PGP SIGNATURE-----\r\n", "size": 7464}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.7_1660145740238_0.6558093127591524"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-10T15:43:16.938Z", "hasInstallScript": true}, "0.13.8": {"name": "vue-demi", "version": "0.13.8", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "b85b53661d33b17cdcc47aaccb8c2856647e9aed", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.8", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-Vy1zbZhCOdsmvGR6tJhAvO5vhP7eiS8xkbYQSoVa7o6KlIy3W8Rc53ED4qI4qpeRDjv3mLfXSEpYU6Yq4pgXRg==", "shasum": "5c568fb3b4d8f848acc658dfccd3d875035b5653", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.8.tgz", "fileCount": 21, "unpackedSize": 24540, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBUUZxCL2Nc+SEWbWRxFmpD1F3DDI5uNOKVX22T8D2mxAiEAljamkNqyrCuG/MQYIlFQLUKqU+nwDfvJ9/rF3pxYkO0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+7+cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmZQ/+IXnuBTXX7g78L8ln1cf5EUVnTx76MEFwXEZ+++0H/fC9r7wT\r\n3q5jS7pDQjEPiOjBFCkBZo8SIPHYo/ZzIQ6mFqRafhV6G3K5kOdb0m7vULzG\r\nLC2o9CLRMizpV/z2Sk0hVLzmoEQbl9YOv0UBLgbbX/vcie0dFl2VQoosd1Pq\r\nfT5xFORJrLbh0yct+XOclrxhY7EaYUQkaaqAl4hRjWjCdkgcTFlinVtDyiUl\r\n9LTMC0Mzd/H7IUSxZ6VgmtlLmFAPfz0MQ12kWk5LmO8Vuig7xNXmK3GS9eAC\r\niXzQKLk/UrKB81MVq3oClLOobnW+0Qn2PQ7pWIXKmxXOJdub8i+GGHr03O6r\r\n18DN9rHclAFfwMcaXr/9Ov5woF3TN5WaopkfsMWEFQvpHnJ4L/2kEF1Ranc8\r\njaXxmt7RDjmXA3XujTw06gFxO/3Tcxz/dldDrrRfWhocXZh9b294JPDRfvPv\r\nXqoVRUvzM8ca08UVQObeANSeR4cjYXZMaZAqJxh4Io/4sPWCfrrweQJ9V3DV\r\n/4hQRXBPtkYHh8anvvr+/spTLJ1Fit6Ch89jJwAbkT/j1jG5FUZwoijOyyat\r\nWQvH++KW8ZVzlbckBmJjMp0406JWIPWksAYUWi8A5Nyvt89L3v52iZGvh/A/\r\n2SLBGeGnkv4isBfJcfuAZcdCUbwmxMnfjiE=\r\n=zMGi\r\n-----END PGP SIGNATURE-----\r\n", "size": 7339}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.8_1660665756482_0.44683132049310537"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-16T16:03:29.788Z", "hasInstallScript": true}, "0.13.9": {"name": "vue-demi", "version": "0.13.9", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "f6e4258630e614ad569965aa97726a3e7de721ac", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.9", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-+4hVlCaBMsX5O3cbYA8xyLAK6jFJVWhmHHmyYTFRkwPhGvyxJbBUfizZiuXCdmDnrGjj7k2n1mhHJ2JvT4JTtA==", "shasum": "95034b5f9df28ea141e58cb9e9e386db663df03b", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.9.tgz", "fileCount": 21, "unpackedSize": 24682, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6AMZV8UPcsMZvRU/+f30oTCihUo6ATFwok+aMMOlw/wIhAJVXtsKm+1zzVUNKNdQkVH5IeGVaYvzqpTPlWX7ojSTY"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBNSQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj8Q/+LUkyM7KlAMuwuCgcUQwRiG2RNk8mE4Pqqir3g8fxATdlox6N\r\nGlusc4m/QyxnGK0iPBMGNBJAZkMqWOnYCVk5r0xWfbOs1Olmv5FnZ7XCQin4\r\nhJauoDPUmHN1/GYzskzbpFTUUQBUfM80R/9WkIdOgcL5mpUbNHtcoFE32mLi\r\nTix+N5nTP1EHewz8poJeTb020Nyw/fdmC9EKLRYotK8X+SnIozMsqOGGdfBf\r\nJ/U/32iWOlvmfvd5ohmWCigbScsoqRdKxax6BaYci9jdTWF7xiyw18A+Ty7H\r\nRzH3IYlM8OMb7LJTSsGBnUdh4yDAEsR/qeFHe8sKnKdhHBTSUrOuPDXcmewp\r\nwWM4MpqJUGYWDMb4ljK2mfAg7A71Y48wh6SfzGOs+CDGcMdcR+JeL7/Y+FSr\r\nPH2bclO60yUPadqnENRXAQ80cW/Hglq3GOdIL3RAsOjlvOz28k0YjUhOP17/\r\n9WGR2OIdOO4nl0KZE4ooibJnxoc0ZQYmvEP0QGWzmcFHVflcrC5FEhL8z3Zv\r\nP/iZKd3Psjl3Vobr3hWAaIRVK8uT/1GGKkH1W6mLaZQXneTIzSDWWAwAd3Aq\r\nwgM2miTDLd+z9Y4uiJIsP2E6QI53UXVoTFQAhTdx8ByxXRS9pWBBJi8xQIMZ\r\nkAKeJlUeMnXd5jLCOvvfgevWr56yR0gsjhE=\r\n=a5ZJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 7387}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.9_1661260944317_0.958381159509786"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-23T13:36:33.632Z", "hasInstallScript": true}, "0.13.10": {"name": "vue-demi", "version": "0.13.10", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "36d27efb4ae8bfdaba068d7d990961972c1307f5", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.10", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-/R4QhdqGyGqSysOfhkxmYHKwdETZq2z6HAf/fjeGErdJX9cJifX5ijHJS+VjNblGIhjXz/yQTwe/t7Cip+/aJw==", "shasum": "e01bccb9d515ea0ed4a50c1387cbce3e60e93256", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.10.tgz", "fileCount": 21, "unpackedSize": 24716, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDk9vA6L/2T/PdLRZWEhNHXCC9SJSpYIRHZcTG20YD0QIhAJ01eDQo2ODVLvhG1ULHfZ9S1+BxOB9qXg/3W4sa3a/s"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBYGcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaYRAAgRKyIf918nluFpjj3INutU+CTDIus1XZMUNgY92NoXm74ouU\r\nXBxzxLeQDMW3pJeBUmNWReNQiprHwv30yllJu831czu2gBjwDuAonQ3XBIP4\r\nWeJMn+pA2VyetyoS0sjPmmyvypLVy20MrwdcTUiX3g4qo6CwZWwkQeTkOvEI\r\nHRgsd2OCXheqC0lPBBB5S7YvyFIlJieLYUa6uwiJBtpPetPz3PtIs2+yphbG\r\nwcckiWPnXS4DmCg6A0DyKyy8kGgN1t76qC3Bh7Zg9vndU1nOpZWvpV4PcPTB\r\nBCv8I5N0t1oiE0G0Qnnr1ZeQUqSCvtqBi/50wsCvEZDIX3POUMTMVPmYIOax\r\nHRoOT78apBH4tah4Y8TezMb/5z5f5bBac0WQW+VLo2hyWYAoySEXQNtezRHs\r\nj7x77xarSjbHTVfNZ9Z+DS246lqAvBm6KuLmyHFwMBjZnwjw5N0/VKGjEVX5\r\nhj0Jt8oHsTgu2+fm55jW5I+4+FPjs6EV9j1t8GEwReDG48C/sssAOvGNgCVZ\r\nfhf1IKhHWHNYHzvXTnV1uJn2op/50rA0VCyPKp9P3l2jndbapICQnKYZ5PAT\r\nzV5TOgZX5TftUl/4+2mfCop49ewBa/EnfTEdnk7OhkCPkMjW/51p+k3FAQtT\r\nN48EMNAeuQ/SQ5dlOt4oFHWuIo+oHV5LYEA=\r\n=fSSq\r\n-----END PGP SIGNATURE-----\r\n", "size": 7392}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.10_1661305244204_0.7744970063281849"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-24T01:40:58.962Z", "hasInstallScript": true}, "0.13.11": {"name": "vue-demi", "version": "0.13.11", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "b5e79de517daf752bb4a5ca458a4eed9ac43801e", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.13.11", "_nodeVersion": "16.14.2", "_npmVersion": "8.8.0", "dist": {"integrity": "sha512-IR8HoEEGM65YY3ZJYAjMlKygDQn25D5ajNFNoKh9RSDMQtlzCxtfQjdQgv9jjK+m3377SsJXY8ysq8kLCZL25A==", "shasum": "7d90369bdae8974d87b1973564ad390182410d99", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.11.tgz", "fileCount": 21, "unpackedSize": 24841, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDoSqNo+yrdqLiT1KVNRUO0x1+yqOOyOF69EKNfB2NF2AIgaqkJqqT9+CSEBMtL2i3NxhaZckCqvoA77H50Hv6sQ/E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB52TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokzxAAgz2+eTC8sEQYa29Yl7fivMatscGPX4lBiC1oCBFPwz8Eh6im\r\nmxTCDXGvbNagDqckcTqfgaiEFPtXhmZ0t0yvvpOB6VkJKnfE21Tr9XfNNtrK\r\n7mk8Fxb3hNMlYGIBQcYxpId17rgn5QklOxy/kFoPP8ZWU2+0CDTSbEdJQ5rw\r\nVM4KuxkZRnEB2xQibax3UpfReHtanyAD5nU9TZTXDRJnSxgpQUykWiUQR8gV\r\nfzua0i/C+B+A8dM4A6id69nTk4RCCxxagCGRtc7c8NEgTG55rYJBCabvTGpc\r\n6kWlUZktqPRPlEW8n9BFel4oCAdx7uwjjo/BSBDUlUCNqP/1uoni+1FuDeqp\r\njw+0cHDEb9AXHu61138IgyJ/pogQNEL/+7GmGiSq9Wp7qCMLJOnHEuAN1M/p\r\nfuYQEP3l7+B8lZ8Wa3Oj3f7D/d6pgEGGkYE8Ikqe9WFVBIET0IpxN3oNLXT7\r\nKI2ADP2Q43HVfXbY4IKVMydurVSXJ6TyzmoTSkiNvMp4gVS930cOEk3oFEQs\r\n5VzQYAKZwC/oP3v3iSw+qWLzVaF1YjbFX5WKMZg61vPtgpKW4RqhaL7QbRwA\r\nNcTG1o+ngIlaRQF6fuqmN5QD0kIoIs7DcO7RIQcwpx0MRNCD5qYMk0pZ9E5X\r\nXC5jnJThB/2xpe0RcEUwWx0tJGlIV0jSDn4=\r\n=qBCG\r\n-----END PGP SIGNATURE-----\r\n", "size": 7424}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.13.11_1661443475284_0.4002912017148186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-25T16:12:44.182Z", "hasInstallScript": true}, "0.14.0": {"name": "vue-demi", "version": "0.14.0", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "1e027f1bd28f4bd2b59312b20cb98ec1ddd88403", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.0", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-gt58r2ogsNQeVoQ3EhoUAvUsH9xviydl0dWJj7dabBC/2L4uBId7ujtCwDRD0JhkGsV1i0CtfLAeyYKBht9oWg==", "shasum": "dcfd9a9cf9bb62ada1582ec9042372cf67ca6190", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.0.tgz", "fileCount": 21, "unpackedSize": 26144, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0HdhOm0SmXwfPxV40vSGq2QFpehE067bgxQ85/o/q4AIhALW2vk3X6ZgFpL8EYr7apZ8PqNvWfjVCtryv1hEwX0bz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOGGiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuLQ/9HCv4tAwqTcaWtS58i9p1KSGpLx8UATYMYdV6lxfOXuqiDDTr\r\n0LDxbQzWgjjZh6MxIvsJFIUX2MogbLr7XvvrRM6jtxepiVQySjVZ6+h9aySD\r\nw+/Kui625Rs9jWtPW632IK1E6HEBZrIwmuJH7YJ73ON3Qwj+Yih8fYixrOq3\r\ntXzsChyo8JW/CDeMM7PTx+M5sDPd7X9e+UqS6EnwL5woykc41G0cEVdgefF0\r\nQoiu/UZXPF0dWAHf4ST4TmQ76jHY3PZxgiPb1ohSz+dhnu9OhXZ7AOH6JYnS\r\n13bbvo8KOYQwNQAcWP46NPyOVZ+cOv+2XrDQ8aR5AGWx/zfmP4u9D46aVSk1\r\nLqDBZrC2ASG93KEFrHDFv3kqdh98n/C+k68B9MamkXiGmCpAsGfPoF27mr3Y\r\n4izFm8qHTcokY86aEDOZVleBF3eD1W/YwbF1g0QDMx9TS0IOQl8VRWdNYkj/\r\n+d5FVRMUUXhGzOhFWji0VAGxR2bzW0RKWz3nax7Oq2rIsSK3192o25jUW8i5\r\nnphG2yXLl9HogDCu0n6Xn6X/pjP7Hcn85wxApkvxFlO8qLpEwvZTJwg9/MU3\r\n/nXuH7V0MIyRc4l+mzAHcGrIuZ4UM+5LSpHgP9bGVoLm1jr96fhAy4P2kxx/\r\nsipRZJy9I2wK9svjiN+u9i1g5byBeaiXqLE=\r\n=UEA1\r\n-----END PGP SIGNATURE-----\r\n", "size": 7601}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.0_1681416610380_0.8439897790370985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-13T20:10:10.550Z", "publish_time": 1681416610550, "hasInstallScript": true}, "0.14.1": {"name": "vue-demi", "version": "0.14.1", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "d3f1e6ba66197d338db63f44cc3ad6eef3408875", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.1", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-rt+yuCtXvscYot9SQQj3WKZJVSriPNqVkpVBNEHPzSgBv7QIYzsS410VqVgvx8f9AAPgjg+XPKvmV3vOqqkJQQ==", "shasum": "1ed9af03a27642762bfed83d8750805302d0398d", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.1.tgz", "fileCount": 21, "unpackedSize": 26866, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDaUTOwS9xt4ggHGTmWErjytrBogfjJm2HYewghEcIoEAiEAqnv3c6Hz2ykj2e1GZWFT3ig52t42V3nriYUpsVjKa4w="}], "size": 7692}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.1_1683624733058_0.36735328632703235"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-09T09:32:13.213Z", "publish_time": 1683624733213, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.2": {"name": "vue-demi", "version": "0.14.2", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "9fdbf5be880f0e32f2d64e95cc91c444693b4cda", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.2", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-KZLwJIXhq5damlmP6AAgsUA6Zkk6pGW7+fuGV4KYdilOEEv1bPlolr6Fw3pts83cfxHWyYU/s2Z+orNaCDqZ4A==", "shasum": "beafa208d71827f84cff8291ef1d3fee2173700f", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.2.tgz", "fileCount": 21, "unpackedSize": 27010, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB0VzV6OTj2z0cRy+2pFf/aXt/Jdr+tsOHNTPSjKFvHEAiBNnSMiSqHnMj+FdfztubRdj6AVOA2Vjn7bRzTNccHPsg=="}], "size": 7703}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.2_1684328333895_0.6223093096235035"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-17T12:58:54.080Z", "publish_time": 1684328334080, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.3": {"name": "vue-demi", "version": "0.14.3", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "5c274bfcc1ab7686e3179b1bcc40b028507ffa12", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.3", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-aknytzARm7U20nMhvOdfa5IRiS+oyATtd55s3fICsT7wEtN/qoOiOINsNsNJjeZCOsPNOGS4p1yDOwH9cTxgjg==", "shasum": "5c7375527ad32580a7d8d28ee322bc059dbc1b99", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.3.tgz", "fileCount": 21, "unpackedSize": 27010, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICk+WtNRmwoENHrNDCuYUXbivH8V/mhTPO2+AmfZq6pZAiAlsCc89jFxvsv8Pil/6z92ZX3isHhYZEzI41x7wB5BVg=="}], "size": 7701}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.3_1684331704694_0.8154936932706642"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-17T13:55:04.838Z", "publish_time": 1684331704838, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.4": {"name": "vue-demi", "version": "0.14.4", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "3f82ddda9a7367dea2da2955b447d3d2b776ee56", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.4", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-YR9bCmtIYgsqNVRG3MLLWlhbZ9tTNMuWHPd7yx0pHS3NDX17MeVNHgKTOClYE8pBjsfNe4CMaReP7zQtHDIbiA==", "shasum": "e3e634eece46ad39e391523cde5b395d22f6cf33", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.4.tgz", "fileCount": 21, "unpackedSize": 27025, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdcxA8E/5vQVCY7xgLNNNF7Kg9yHaIiAZKffM5C8nkkwIgDP4Zch/jbepYDpREflLsPjzbBpZlZ2CtovHf8op/7Rg="}], "size": 7725}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.4_1684337594956_0.21707208403916223"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-17T15:33:15.112Z", "publish_time": 1684337595112, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.5": {"name": "vue-demi", "version": "0.14.5", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "e849e2a71ff21f2762f2e9c932c8a1a4da40aa1f", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.5", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==", "shasum": "676d0463d1a1266d5ab5cba932e043d8f5f2fbd9", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.5.tgz", "fileCount": 21, "unpackedSize": 27031, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID14Iz7eKgwIfn0G3fXg0XP8OM4Pqi8wiRXXeGHi6GRAAiAv7qhu9D2/U8i/jGToNr643xfGC7UefBogaIC8y+hk5Q=="}], "size": 7717}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.5_1684404954727_0.42624620560365023"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T10:15:54.928Z", "publish_time": 1684404954928, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.6": {"name": "vue-demi", "version": "0.14.6", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "80b8e939134c4d8e9457bd97235a46712ea0988a", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.6", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-8QA7wrYSHKaYgUxDA5ZC24w+eHm3sYCbp0EzcDwKqN3p6HqtTCGR/GVsPyZW92unff4UlcSh++lmqDWN3ZIq4w==", "shasum": "dc706582851dc1cdc17a0054f4fec2eb6df74c92", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.6.tgz", "fileCount": 21, "unpackedSize": 27143, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiB6EGRpDLe1Resr93zr1xWeoeoNmfIeLOJRR+C0jpjwIhAO/wabEW2CZ5RprYHrRSGOxcvirq2QOOpzbpXyrVJ/0w"}], "size": 7728}, "_npmUser": {"name": "posva", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "posva", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.6_1693209642778_0.16842326804375785"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-28T08:00:43.027Z", "publish_time": 1693209643027, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.7": {"name": "vue-demi", "version": "0.14.7", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node -e \"try{require('./scripts/postinstall.js')}catch(e){}\"", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "gitHead": "e42b5d594f72a8380da1eae91eacad645f161d06", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_id": "vue-demi@0.14.7", "_nodeVersion": "20.4.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-EOG8KXDQNwkJILkx/gPcoL/7vH+hORoBaKgGe+6W7VFMvCYJfmF2dGbvgDroVnI8LU7/kTu8mbjRZGBU1z9NTA==", "shasum": "8317536b3ef74c5b09f268f7782e70194567d8f2", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.7.tgz", "fileCount": 21, "unpackedSize": 27268, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6XZFCPS7jZGzb0zCNfoFONma0T2ZEEDFCdixiIp/aNAIhAP77dJBsByIUvtA246q08WgjU0/y/tnqF46fMIBgy6ex"}], "size": 7764}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "posva", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.7_1706787156810_0.025416464500105374"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-01T11:32:36.980Z", "publish_time": 1706787156980, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.8": {"name": "vue-demi", "version": "0.14.8", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node -e \"try{require('./scripts/postinstall.js')}catch(e){}\"", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "_id": "vue-demi@0.14.8", "gitHead": "6fe0e6ae168de10ae92e27f983c0bc9ed71efb79", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Uuqnk9YE9SsWeReYqK2alDI5YzciATE0r2SkA6iMAtuXvNTMNACJLJEXNXaEy94ECuBe4Sk6RzRU80kjdbIo1Q==", "shasum": "00335e9317b45e4a68d3528aaf58e0cec3d5640a", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.8.tgz", "fileCount": 21, "unpackedSize": 27416, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7kPKBgMUsk88FnnqTtCDpTElhFwv65MuR95BR6Nb6EAIgdf/nwg5lk9s423+kksAPebA280mDgK/QbCtFE+DgAbE="}], "size": 7841}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "posva", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.8_1717068618371_0.6870685666564675"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-30T11:30:18.517Z", "publish_time": 1717068618517, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.9": {"name": "vue-demi", "version": "0.14.9", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node -e \"try{require('./scripts/postinstall.js')}catch(e){}\"", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "_id": "vue-demi@0.14.9", "gitHead": "bf46f2d09bd20ae9a4e0a0ed08742c5eb371c61a", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-dC1TJMODGM8lxhP6wLToncaDPPNB3biVxxRDuNCYpuXwi70ou7NsGd97KVTJ2omepGId429JZt8oaZKeXbqxwg==", "shasum": "db2be43705e2bc8501f01ca6163e34ada2f2eb21", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.9.tgz", "fileCount": 21, "unpackedSize": 27490, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBFm7w4dc4ba8YX+Yseg+0l1cxDrrIm3L+1rdM5SnK04AiEAtDrw6eHJC5KFOVBkZlKWzxFH6+XTNdo13DX5k49jVfY="}], "size": 7876}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "posva", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.9_1721736612774_0.5830806628252865"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-23T12:10:12.955Z", "publish_time": 1721736612955, "_source_registry_name": "default", "hasInstallScript": true}, "0.14.10": {"name": "vue-demi", "version": "0.14.10", "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "funding": "https://github.com/sponsors/antfu", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "lib/index.cjs", "jsdelivr": "lib/index.iife.js", "unpkg": "lib/index.iife.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "scripts": {"postinstall": "node -e \"try{require('./scripts/postinstall.js')}catch(e){}\"", "release": "npx bumpp --tag --commit --push && npm publish"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "_id": "vue-demi@0.14.10", "gitHead": "4513510385f1c6a221c12cae28e0d3f33443e170", "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==", "shasum": "afc78de3d6f9e11bf78c55e8510ee12814522f04", "tarball": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.10.tgz", "fileCount": 21, "unpackedSize": 27571, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHwZ/WsI2bIl/hShI4BLDrDR/heI7Fnui20Lxi1abrywIhAJXZQ1Ew+Tu0OW92aYCFkT62xyPM+esH8iZ7XjGqKhU4"}], "size": 7893}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "posva", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-demi_0.14.10_1721924394221_0.9767572505664595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-25T16:19:54.431Z", "publish_time": 1721924394431, "_source_registry_name": "default", "hasInstallScript": true}}, "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "homepage": "https://github.com/antfu/vue-demi#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "_source_registry_name": "default"}