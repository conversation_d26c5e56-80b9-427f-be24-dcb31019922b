{"_attachments": {}, "_id": "@ctrl/tinycolor", "_rev": "3738-61f14d9f4ce7cf8f5826e760", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Fast, small color manipulation and conversion for JavaScript", "dist-tags": {"latest": "4.1.0"}, "license": "MIT", "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "name": "@ctrl/tinycolor", "readme": "# tinycolor \n\n[![npm](https://badgen.net/npm/v/@ctrl/tinycolor)](https://www.npmjs.com/package/@ctrl/tinycolor)\n[![coverage](https://badgen.net/codecov/c/github/scttcper/tinycolor)](https://codecov.io/gh/scttcper/tinycolor)\n[![bundlesize](https://badgen.net/bundlephobia/min/@ctrl/tinycolor)](https://bundlephobia.com/result?p=@ctrl/tinycolor)\n\n> TinyColor is a small library for color manipulation and conversion\n\nA fork of [tinycolor2](https://github.com/bgrins/TinyColor) by [<PERSON>](https://github.com/bgrins)\n\n__DEMO__: https://tinycolor.vercel.app  \n\n### Changes from tinycolor2\n\n* reformatted into TypeScript / es2015 and requires node >= 8\n  * tree shakeable \"module\" export and no package `sideEffects`\n* `tinycolor` is now exported as a class called `TinyColor`\n* default export removed, use `import { TinyColor } from '@ctrl/tinycolor'`\n* new `random`, an implementation of [randomColor](https://github.com/davidmerfield/randomColor/) by <PERSON> that returns a TinyColor object\n* several functions moved out of the tinycolor class and are no longer `TinyColor.<function>`\n  * `readability`, `fromRatio` moved out\n  * `random` moved out and renamed to `legacyRandom`\n  * `toFilter` has been moved out and renamed to `toMsFilter`\n* `mix`, `equals` use the current TinyColor object as the first parameter\n* added polyad colors tinycolor PR [126](https://github.com/bgrins/TinyColor/pull/126)\n* color wheel values (360) are allowed to over or under-spin and still return valid colors tinycolor PR [108](https://github.com/bgrins/TinyColor/pull/108)\n* added `tint()` and `shade()` tinycolor PR [159](https://github.com/bgrins/TinyColor/pull/159)\n* `isValid`, `format` are now propertys instead of a function\n\n## Install\n\n```sh\nnpm install @ctrl/tinycolor\n```\n\n## Use\n\n```ts\nimport { TinyColor } from '@ctrl/tinycolor';\nconst color = new TinyColor('red').toHexString(); // '#ff0000'\n```\n\n## Accepted String Input\n\nThe string parsing is very permissive. It is meant to make typing a color as input as easy as possible. All commas, percentages, parenthesis are optional, and most input allow either 0-1, 0%-100%, or 0-n (where n is either 100, 255, or 360 depending on the value).\n\nHSL and HSV both require either 0%-100% or 0-1 for the `S`/`L`/`V` properties. The `H` (hue) can have values between 0%-100% or 0-360.\n\nRGB input requires either 0-255 or 0%-100%.\n\nIf you call `tinycolor.fromRatio`, RGB and Hue input can also accept 0-1.\n\nHere are some examples of string input:\n\n### Hex, 8-digit (RGBA) Hex\n\n```ts\nnew TinyColor('#000');\nnew TinyColor('000');\nnew TinyColor('#369C');\nnew TinyColor('369C');\nnew TinyColor('#f0f0f6');\nnew TinyColor('f0f0f6');\nnew TinyColor('#f0f0f688');\nnew TinyColor('f0f0f688');\n```\n\n### RGB, RGBA\n\n```ts\nnew TinyColor('rgb (255, 0, 0)');\nnew TinyColor('rgb 255 0 0');\nnew TinyColor('rgba (255, 0, 0, .5)');\nnew TinyColor({ r: 255, g: 0, b: 0 });\n\nimport { fromRatio } from '@ctrl/tinycolor';\nfromRatio({ r: 1, g: 0, b: 0 });\nfromRatio({ r: 0.5, g: 0.5, b: 0.5 });\n```\n\n### HSL, HSLA\n\n```ts\nnew TinyColor('hsl(0, 100%, 50%)');\nnew TinyColor('hsla(0, 100%, 50%, .5)');\nnew TinyColor('hsl(0, 100%, 50%)');\nnew TinyColor('hsl 0 1.0 0.5');\nnew TinyColor({ h: 0, s: 1, l: 0.5 });\n```\n\n### HSV, HSVA\n\n```ts\nnew TinyColor('hsv(0, 100%, 100%)');\nnew TinyColor('hsva(0, 100%, 100%, .5)');\nnew TinyColor('hsv (0 100% 100%)');\nnew TinyColor('hsv 0 1 1');\nnew TinyColor({ h: 0, s: 100, v: 100 });\n```\n\n### CMYK\n\n```ts\nnew TinyColor('cmyk(0, 25, 20, 0)');\nnew TinyColor('cmyk(0, 100, 100, 0)');\nnew TinyColor('cmyk 100 0 100 0)');\nnew TinyColor({c: 0, m: 25, y: 25, k: 0});\n```\n\n### Named\n\n```ts\nnew TinyColor('RED');\nnew TinyColor('blanchedalmond');\nnew TinyColor('darkblue');\n```\n\n### Number\n```ts\nnew TinyColor(0x0);\nnew TinyColor(0xaabbcc);\n```\n\n### Accepted Object Input\n\nIf you are calling this from code, you may want to use object input. Here are some examples of the different types of accepted object inputs:\n\n```ts\n{ r: 255, g: 0, b: 0 }\n{ r: 255, g: 0, b: 0, a: .5 }\n{ h: 0, s: 100, l: 50 }\n{ h: 0, s: 100, v: 100 }\n```\n\n## Properties\n\n### originalInput\n\nThe original input passed into the constructer used to create the tinycolor instance\n\n```ts\nconst color = new TinyColor('red');\ncolor.originalInput; // \"red\"\ncolor = new TinyColor({ r: 255, g: 255, b: 255 });\ncolor.originalInput; // \"{r: 255, g: 255, b: 255}\"\n```\n\n### format\n\nReturns the format used to create the tinycolor instance\n\n```ts\nconst color = new TinyColor('red');\ncolor.format; // \"name\"\ncolor = new TinyColor({ r: 255, g: 255, b: 255 });\ncolor.format; // \"rgb\"\n```\n\n### isValid\n\nA boolean indicating whether the color was successfully parsed. Note: if the color is not valid then it will act like `black` when being used with other methods.\n\n```ts\nconst color1 = new TinyColor('red');\ncolor1.isValid; // true\ncolor1.toHexString(); // \"#ff0000\"\n\nconst color2 = new TinyColor('not a color');\ncolor2.isValid; // false\ncolor2.toString(); // \"#000000\"\n```\n\n## Methods\n\n### getBrightness\n\nReturns the perceived brightness of a color, from `0-255`, as defined by [Web Content Accessibility Guidelines (Version 1.0)](http://www.w3.org/TR/AERT#color-contrast).\n\n```ts\nconst color1 = new TinyColor('#fff');\ncolor1.getBrightness(); // 255\n\nconst color2 = new TinyColor('#000');\ncolor2.getBrightness(); // 0\n```\n\n### isLight\n\nReturn a boolean indicating whether the color's perceived brightness is light.\n\n```ts\nconst color1 = new TinyColor('#fff');\ncolor1.isLight(); // true\n\nconst color2 = new TinyColor('#000');\ncolor2.isLight(); // false\n```\n\n### isDark\n\nReturn a boolean indicating whether the color's perceived brightness is dark.\n\n```ts\nconst color1 = new TinyColor('#fff');\ncolor1.isDark(); // false\n\nconst color2 = new TinyColor('#000');\ncolor2.isDark(); // true\n```\n\n### getLuminance\n\nReturns the perceived luminance of a color, from `0-1` as defined by [Web Content Accessibility Guidelines (Version 2.0).](http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef)\n\n```ts\nconst color1 = new TinyColor('#fff');\ncolor1.getLuminance(); // 1\n\nconst color2 = new TinyColor('#000');\ncolor2.getLuminance(); // 0\n```\n\n### getAlpha\n\nReturns the alpha value of a color, from `0-1`.\n\n```ts\nconst color1 = new TinyColor('rgba(255, 0, 0, .5)');\ncolor1.getAlpha(); // 0.5\n\nconst color2 = new TinyColor('rgb(255, 0, 0)');\ncolor2.getAlpha(); // 1\n\nconst color3 = new TinyColor('transparent');\ncolor3.getAlpha(); // 0\n```\n\n### setAlpha\n\nSets the alpha value on a current color. Accepted range is in between `0-1`.\n\n```ts\nconst color = new TinyColor('red');\ncolor.getAlpha(); // 1\ncolor.setAlpha(0.5);\ncolor.getAlpha(); // .5\ncolor.toRgbString(); // \"rgba(255, 0, 0, .5)\"\n```\n\n### onBackground\n\nCompute how the color would appear on a background. When the color is fully transparent (i.e. `getAlpha() == 0`), the result will be the background color. When the color is not transparent at all (i.e. `getAlpha() == 1`), the result will be the color itself. Otherwise you will get a computed result.\n\n```ts\nconst color = new TinyColor('rgba(255, 0, 0, .5)');\nconst computedColor = color.onBackground('rgb(0, 0, 255)');\ncomputedColor.toRgbString(); // \"rgb(128, 0, 128)\"\n```\n\n### String Representations\n\nThe following methods will return a property for the `alpha` value, which can be ignored: `toHsv`, `toHsl`, `toRgb`\n\n### toHsv\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHsv(); // { h: 0, s: 1, v: 1, a: 1 }\n```\n\n### toHsvString\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHsvString(); // \"hsv(0, 100%, 100%)\"\ncolor.setAlpha(0.5);\ncolor.toHsvString(); // \"hsva(0, 100%, 100%, 0.5)\"\n```\n\n### toHsl\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHsl(); // { h: 0, s: 1, l: 0.5, a: 1 }\n```\n\n### toHslString\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHslString(); // \"hsl(0, 100%, 50%)\"\ncolor.setAlpha(0.5);\ncolor.toHslString(); // \"hsla(0, 100%, 50%, 0.5)\"\n```\n\n### toCmykString\n\n```ts\nconst color = new TinyColor('red');\ncolor.toCmykString(); // \"cmyk(0, 100, 100, 0)\"\n```\n\n### toNumber\n```ts\nnew TinyColor('#aabbcc').toNumber() === 0xaabbcc // true\nnew TinyColor('rgb(1, 1, 1)').toNumber() === (1 << 16) + (1 << 8) + 1 // true\n```\n\n### toHex\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHex(); // \"ff0000\"\n```\n\n### toHexString\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHexString(); // \"#ff0000\"\n```\n\n### toHex8\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHex8(); // \"ff0000ff\"\n```\n\n### toHex8String\n\n```ts\nconst color = new TinyColor('red');\ncolor.toHex8String(); // \"#ff0000ff\"\n```\n\n### toHexShortString\n\n```ts\nconst color1 = new TinyColor('#ff000000');\ncolor1.toHexShortString(); // \"#ff000000\"\ncolor1.toHexShortString(true); // \"#f000\"\n\nconst color2 = new TinyColor('#ff0000ff');\ncolor2.toHexShortString(); // \"#ff0000\"\ncolor2.toHexShortString(true); // \"#f00\"\n```\n\n### toRgb\n\n```ts\nconst color = new TinyColor('red');\ncolor.toRgb(); // { r: 255, g: 0, b: 0, a: 1 }\n```\n\n### toRgbString\n\n```ts\nconst color = new TinyColor('red');\ncolor.toRgbString(); // \"rgb(255, 0, 0)\"\ncolor.setAlpha(0.5);\ncolor.toRgbString(); // \"rgba(255, 0, 0, 0.5)\"\n```\n\n### toPercentageRgb\n\n```ts\nconst color = new TinyColor('red');\ncolor.toPercentageRgb(); // { r: \"100%\", g: \"0%\", b: \"0%\", a: 1 }\n```\n\n### toPercentageRgbString\n\n```ts\nconst color = new TinyColor('red');\ncolor.toPercentageRgbString(); // \"rgb(100%, 0%, 0%)\"\ncolor.setAlpha(0.5);\ncolor.toPercentageRgbString(); // \"rgba(100%, 0%, 0%, 0.5)\"\n```\n\n### toName\n\n```ts\nconst color = new TinyColor('red');\ncolor.toName(); // \"red\"\n```\n\n### toFilter\n\n```ts\nimport { toMsFilter } from '@ctrl/tinycolor';\ntoMsFilter('red', 'blue'); // 'progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffff0000,endColorstr=#ff0000ff)'\n```\n\n### toString\n\nPrint to a string, depending on the input format. You can also override this by passing one of `\"rgb\", \"prgb\", \"hex6\", \"hex3\", \"hex8\", \"name\", \"hsl\", \"hsv\"` into the function.\n\n```ts\nconst color1 = new TinyColor('red');\ncolor1.toString(); // \"red\"\ncolor1.toString('hsv'); // \"hsv(0, 100%, 100%)\"\n\nconst color2 = new TinyColor('rgb(255, 0, 0)');\ncolor2.toString(); // \"rgb(255, 0, 0)\"\ncolor2.setAlpha(0.5);\ncolor2.toString(); // \"rgba(255, 0, 0, 0.5)\"\n```\n\n### Color Modification\n\nThese methods manipulate the current color, and return it for chaining. For instance:\n\n```ts\nnew TinyColor('red')\n  .lighten()\n  .desaturate()\n  .toHexString(); // '#f53d3d'\n```\n\n### lighten\n\n`lighten: function(amount = 10) -> TinyColor`. Lighten the color a given amount, from 0 to 100. Providing 100 will always return white.\n\n```ts\nnew TinyColor('#f00').lighten().toString(); // '#ff3333'\nnew TinyColor('#f00').lighten(100).toString(); // '#ffffff'\n```\n\n### brighten\n\n`brighten: function(amount = 10) -> TinyColor`. Brighten the color a given amount, from 0 to 100.\n\n```ts\nnew TinyColor('#f00').brighten().toString(); // '#ff1919'\n```\n\n### darken\n\n`darken: function(amount = 10) -> TinyColor`. Darken the color a given amount, from 0 to 100. Providing 100 will always return black.\n\n```ts\nnew TinyColor('#f00').darken().toString(); // '#cc0000'\nnew TinyColor('#f00').darken(100).toString(); // '#000000'\n```\n\n### tint\n\nMix the color with pure white, from 0 to 100. Providing 0 will do nothing, providing 100 will always return white.\n\n```ts\nnew TinyColor('#f00').tint().toString(); // \"#ff1a1a\"\nnew TinyColor('#f00').tint(100).toString(); // \"#ffffff\"\n```\n\n### shade\n\nMix the color with pure black, from 0 to 100. Providing 0 will do nothing, providing 100 will always return black.\n\n```ts\nnew TinyColor('#f00').shade().toString(); // \"#e60000\"\nnew TinyColor('#f00').shade(100).toString(); // \"#000000\"\n```\n\n### desaturate\n\n`desaturate: function(amount = 10) -> TinyColor`. Desaturate the color a given amount, from 0 to 100. Providing 100 will is the same as calling `greyscale`.\n\n```ts\nnew TinyColor('#f00').desaturate().toString(); // \"#f20d0d\"\nnew TinyColor('#f00').desaturate(100).toString(); // \"#808080\"\n```\n\n### saturate\n\n`saturate: function(amount = 10) -> TinyColor`. Saturate the color a given amount, from 0 to 100.\n\n```ts\nnew TinyColor('hsl(0, 10%, 50%)').saturate().toString(); // \"hsl(0, 20%, 50%)\"\n```\n\n### greyscale\n\n`greyscale: function() -> TinyColor`. Completely desaturates a color into greyscale. Same as calling `desaturate(100)`.\n\n```ts\nnew TinyColor('#f00').greyscale().toString(); // \"#808080\"\n```\n\n### spin\n\n`spin: function(amount = 0) -> TinyColor`. Spin the hue a given amount, from -360 to 360. Calling with 0, 360, or -360 will do nothing (since it sets the hue back to what it was before).\n\n```ts\nnew TinyColor('#f00').spin(180).toString(); // \"#00ffff\"\nnew TinyColor('#f00').spin(-90).toString(); // \"#7f00ff\"\nnew TinyColor('#f00').spin(90).toString(); // \"#80ff00\"\n\n// spin(0) and spin(360) do nothing\nnew TinyColor('#f00').spin(0).toString(); // \"#ff0000\"\nnew TinyColor('#f00').spin(360).toString(); // \"#ff0000\"\n```\n\n### mix\n\n`mix: function(amount = 50) => TinyColor`. Mix the current color a given amount with another color, from 0 to 100. 0 means no mixing (return current color).\n\n```ts\nlet color1 = new TinyColor('#f0f');\nlet color2 = new TinyColor('#0f0');\n\ncolor1.mix(color2).toHexString(); // #808080\n```\n\n### Color Combinations\n\nCombination functions return an array of TinyColor objects unless otherwise noted.\n\n### analogous\n\n`analogous: function(results = 6, slices = 30) -> array<TinyColor>`.\n\n```ts\nconst colors = new TinyColor('#f00').analogous();\ncolors.map(t => t.toHexString()); // [ \"#ff0000\", \"#ff0066\", \"#ff0033\", \"#ff0000\", \"#ff3300\", \"#ff6600\" ]\n```\n\n### monochromatic\n\n`monochromatic: function(, results = 6) -> array<TinyColor>`.\n\n```ts\nconst colors = new TinyColor('#f00').monochromatic();\ncolors.map(t => t.toHexString()); // [ \"#ff0000\", \"#2a0000\", \"#550000\", \"#800000\", \"#aa0000\", \"#d40000\" ]\n```\n\n### splitcomplement\n\n`splitcomplement: function() -> array<TinyColor>`.\n\n```ts\nconst colors = new TinyColor('#f00').splitcomplement();\ncolors.map(t => t.toHexString()); // [ \"#ff0000\", \"#ccff00\", \"#0066ff\" ]\n```\n\n### triad\n\n`triad: function() -> array<TinyColor>`. Alias for `polyad(3)`.\n\n```ts\nconst colors = new TinyColor('#f00').triad();\ncolors.map(t => t.toHexString()); // [ \"#ff0000\", \"#00ff00\", \"#0000ff\" ]\n```\n\n### tetrad\n\n`tetrad: function() -> array<TinyColor>`. Alias for `polyad(4)`.\n\n```ts\nconst colors = new TinyColor('#f00').tetrad();\ncolors.map(t => t.toHexString()); // [ \"#ff0000\", \"#80ff00\", \"#00ffff\", \"#7f00ff\" ]\n```\n\n### polyad\n\n`polyad: function(number) -> array<TinyColor>`.\n\n```ts\nconst colors = new TinyColor('#f00').polyad(4);\ncolors.map(t => t.toHexString()); // [ \"#ff0000\", \"#80ff00\", \"#00ffff\", \"#7f00ff\" ]\n```\n\n### complement\n\n`complement: function() -> TinyColor`.\n\n```ts\nnew TinyColor('#f00').complement().toHexString(); // \"#00ffff\"\n```\n\n## Color Utilities\n\n### equals\n\n```ts\nlet color1 = new TinyColor('red');\nlet color2 = new TinyColor('#f00');\n\ncolor1.equals(color2); // true\n```\n\n### random\n\nReturns a random TinyColor object. This is an implementation of [randomColor](https://github.com/davidmerfield/randomColor/) by David Merfield.\nThe difference input parsing and output formatting are handled by TinyColor.\n\nYou can pass an options object to influence the type of color it produces. The options object accepts the following properties:\n\n* `hue` – Controls the hue of the generated color. You can pass a string representing a color name: `red`, `orange`, `yellow`, `green`, `blue`, `purple`, `pink` and `monochrome` are currently supported. If you pass a hexidecimal color string such as #00FFFF, its hue value will be extracted and used to generate colors.\n* `luminosity` – Controls the luminosity of the generated color. You can specify a string containing bright, light or dark.\n* `count` – An integer which specifies the number of colors to generate.\n* `seed` – An integer which when passed will cause randomColor to return the same color each time.\n* `alpha` – A decimal between 0 and 1. Only relevant when using a format with an alpha channel (rgba and hsla). Defaults to a random value.\n\n```ts\nimport { random } from '@ctrl/tinycolor';\n// Returns a TinyColor for an attractive color\nrandom();\n\n// Returns an array of ten green colors\nrandom({\n  count: 10,\n  hue: 'green',\n});\n\n// Returns a TinyColor object in a light blue\nrandom({\n  luminosity: 'light',\n  hue: 'blue',\n});\n\n// Returns a TinyColor object in a 'truly random' color\nrandom({\n  luminosity: 'random',\n  hue: 'random',\n});\n\n// Returns a dark RGB color with specified alpha\nrandom({\n  luminosity: 'dark',\n  alpha: 0.5,\n});\n```\n\n### Readability\n\nTinyColor assesses readability based on the [Web Content Accessibility Guidelines (Version 2.0)](http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef).\n\n#### readability\n\n`readability: function(TinyColor, TinyColor) -> number`.\nReturns the contrast ratio between two colors.\n\n```ts\nimport { readability } from '@ctrl/tinycolor';\nreadability('#000', '#000'); // 1\nreadability('#000', '#111'); // 1.1121078324840545\nreadability('#000', '#fff'); // 21\n```\n\nUse the values in your own calculations, or use one of the convenience functions below.\n\n#### isReadable\n\n`isReadable: function(TinyColor, TinyColor, Object) -> Boolean`. Ensure that foreground and background color combinations meet WCAG guidelines. `Object` is optional, defaulting to `{level: \"AA\",size: \"small\"}`. `level` can be `\"AA\"` or \"AAA\" and `size` can be `\"small\"` or `\"large\"`.\n\nHere are links to read more about the [AA](http://www.w3.org/TR/UNDERSTANDING-WCAG20/visual-audio-contrast-contrast.html) and [AAA](http://www.w3.org/TR/UNDERSTANDING-WCAG20/visual-audio-contrast7.html) requirements.\n\n```ts\nimport { isReadable } from '@ctrl/tinycolor';\nisReadable(\"#000\", \"#111\"); // false\nisReadable(\"#ff0088\", \"#5c1a72\", { level: \"AA\", size: \"small\" }); // false\nisReadable(\"#ff0088\", \"#5c1a72\", { level: \"AA\", size: \"large\" }), // true\n```\n\n#### mostReadable\n\n`mostReadable: function(TinyColor, [TinyColor, TinyColor ...], Object) -> Boolean`.\nGiven a base color and a list of possible foreground or background colors for that base, returns the most readable color.\nIf none of the colors in the list is readable, `mostReadable` will return the better of black or white if `includeFallbackColors:true`.\n\n```ts\nimport { mostReadable } from '@ctrl/tinycolor';\nmostReadable('#000', ['#f00', '#0f0', '#00f']).toHexString(); // \"#00ff00\"\nmostReadable('#123', ['#124', '#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\nmostReadable('#123', ['#124', '#125'], { includeFallbackColors: true }).toHexString(); // \"#ffffff\"\nmostReadable('#ff0088', ['#2e0c3a'], {\n  includeFallbackColors: true,\n  level: 'AAA',\n  size: 'large',\n}).toHexString(); // \"#2e0c3a\",\nmostReadable('#ff0088', ['#2e0c3a'], {\n  includeFallbackColors: true,\n  level: 'AAA',\n  size: 'small',\n}).toHexString(); // \"#000000\",\n```\n\nSee [index.html](https://github.com/bgrins/TinyColor/blob/master/index.html) in the project for a demo.\n\n## Common operations\n\n### clone\n\n`clone: function() -> TinyColor`.\nInstantiate a new TinyColor object with the same color. Any changes to the new one won't affect the old one.\n\n```ts\nconst color1 = new TinyColor('#F00');\nconst color2 = color1.clone();\ncolor2.setAlpha(0.5);\n\ncolor1.toString(); // \"#ff0000\"\ncolor2.toString(); // \"rgba(255, 0, 0, 0.5)\"\n```\n", "time": {"created": "2022-01-26T13:33:19.910Z", "modified": "2024-04-14T04:08:48.507Z", "3.4.0": "2021-02-09T18:37:36.510Z", "3.3.4": "2021-02-01T22:14:48.339Z", "3.3.3": "2021-01-22T20:11:34.743Z", "3.3.2": "2021-01-13T19:43:31.778Z", "3.3.1": "2020-12-13T17:34:57.361Z", "3.3.0": "2020-12-13T03:58:06.848Z", "3.2.1": "2020-12-09T19:22:30.474Z", "3.2.0": "2020-11-26T00:41:55.542Z", "3.1.7": "2020-11-13T17:31:40.102Z", "3.1.6": "2020-10-16T18:11:21.958Z", "3.1.5": "2020-09-28T05:54:20.026Z", "3.1.4": "2020-08-10T17:55:34.473Z", "3.1.3": "2020-07-07T06:40:16.172Z", "3.1.2": "2020-05-27T04:41:52.335Z", "3.1.1": "2020-05-10T06:30:59.525Z", "3.1.0": "2020-04-28T06:38:23.842Z", "3.0.2": "2020-04-22T17:47:09.563Z", "3.0.1": "2020-04-22T17:31:55.957Z", "3.0.0": "2020-04-22T17:18:35.275Z", "2.6.1": "2020-03-02T00:47:41.035Z", "2.6.0": "2019-12-19T07:17:42.535Z", "2.5.4": "2019-10-17T06:06:11.628Z", "2.5.3": "2019-07-17T06:39:25.147Z", "2.5.2": "2019-05-28T05:43:00.055Z", "2.5.1": "2019-05-20T14:44:08.768Z", "2.5.0": "2019-05-20T07:38:19.508Z", "2.4.0": "2019-03-05T17:45:47.395Z", "2.3.0": "2019-03-03T01:03:07.136Z", "2.2.1": "2018-09-26T06:16:55.858Z", "2.2.0": "2018-08-10T19:12:00.176Z", "2.1.0": "2018-08-02T18:07:11.995Z", "2.0.1": "2018-08-01T04:54:26.142Z", "2.0.0": "2018-05-09T07:58:55.675Z", "1.2.0": "2018-05-07T06:46:02.819Z", "1.1.1": "2018-05-02T16:36:18.342Z", "1.1.0": "2018-05-01T18:09:21.913Z", "1.0.0": "2018-04-30T06:54:02.773Z", "3.4.1": "2022-04-10T20:26:05.433Z", "3.5.0": "2022-12-05T21:06:45.717Z", "3.5.1": "2023-02-13T18:02:20.017Z", "3.6.0": "2023-02-14T17:39:50.610Z", "3.6.1": "2023-08-23T21:26:34.052Z", "4.0.0": "2023-08-23T22:16:43.990Z", "4.0.1": "2023-08-24T20:45:59.468Z", "4.0.2": "2023-08-30T16:12:53.229Z", "4.0.3": "2024-01-17T18:10:42.168Z", "4.0.4": "2024-04-08T17:42:24.450Z", "4.1.0": "2024-04-13T19:04:45.788Z"}, "versions": {"3.4.0": {"name": "@ctrl/tinycolor", "version": "3.4.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.11", "@jest/globals": "26.6.2", "@types/node": "14.14.22", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.38.0", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.20.16", "typescript": "4.1.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "e0a400a35d202d25e017bbb83e61cbc100703774", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.4.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "c3c5ae543c897caa9c2a68630bed355be5f9990f", "size": 53007, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.4.0.tgz", "integrity": "sha512-JZButFdZ1+/xAfpguQHoabIXkcqRRKpMrWKBkpEZZyxfY9C1DpADFB8PEqGSTeFr135SaTRfKqGKx5xSCLI7ZQ=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.4.0_1612895856372_0.9388424660415269"}, "_hasShrinkwrap": false, "publish_time": 1612895856510, "_cnpm_publish_time": 1612895856510, "_cnpmcore_publish_time": "2021-12-16T14:46:46.267Z"}, "3.3.4": {"name": "@ctrl/tinycolor", "version": "3.3.4", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.11", "@jest/globals": "26.6.2", "@types/node": "14.14.22", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.38.0", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.20.16", "typescript": "4.1.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "bc2de086cad0dbf94a79c126c027ed37586de20a", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.3.4", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "59691edd031eedc431bda1bdf601257c06289a40", "size": 52996, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.3.4.tgz", "integrity": "sha512-8vmPV/nIULFDWsnJalQJDqFLC2uTPx6A/ASA2t27QGp+7oXnbWWXCe0uV8xasIH2rGbI/XoB2vmkdP/94WvMrw=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.3.4_1612217688148_0.3911039819129978"}, "_hasShrinkwrap": false, "publish_time": 1612217688339, "_cnpm_publish_time": 1612217688339, "_cnpmcore_publish_time": "2021-12-16T14:46:46.536Z"}, "3.3.3": {"name": "@ctrl/tinycolor", "version": "3.3.3", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.11", "@jest/globals": "26.6.2", "@types/node": "14.14.22", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.38.0", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.20.16", "typescript": "4.1.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "6b2868cada30ee80284ccee6e4f3a7aa95c8eedd", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.3.3", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "980487763bc7c9238d6d88d1ac0dee2d4df3df68", "size": 53055, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.3.3.tgz", "integrity": "sha512-v75yutF4BDMv9weDQVM+K5XEfjiODhugSV729pnoxtBDO61ij2CsDnQa4N4E9xGaH3/FX5ASZjnajljT2F71tA=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.3.3_1611346294527_0.4649788846322316"}, "_hasShrinkwrap": false, "publish_time": 1611346294743, "_cnpm_publish_time": 1611346294743, "_cnpmcore_publish_time": "2021-12-16T14:46:46.853Z"}, "3.3.2": {"name": "@ctrl/tinycolor", "version": "3.3.2", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.8", "@jest/globals": "26.6.2", "@types/node": "14.14.16", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.35.1", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.20.4", "typescript": "4.1.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "ce3b3df873f644b529987882adf7a3ab648ee690", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.3.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "902c4c7e6b48553e4f83c0c1c565a6b071b02520", "size": 53037, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.3.2.tgz", "integrity": "sha512-28A2j9DISNtHRcfc+FSCkxIAMnQoORQYz+UQLjc0+KlsGl80MqfbG3EzqXxzVmCMatlgmHfcX7/DBoMg+ik/Fg=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.3.2_1610567011655_0.26516632072348134"}, "_hasShrinkwrap": false, "publish_time": 1610567011778, "_cnpm_publish_time": 1610567011778, "_cnpmcore_publish_time": "2021-12-16T14:46:47.158Z"}, "3.3.1": {"name": "@ctrl/tinycolor", "version": "3.3.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.8", "@jest/globals": "26.6.2", "@types/node": "14.14.13", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.34.2", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.19.2", "typescript": "4.1.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "edcddb7d8a30098f3df1d732576f420e9515b837", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.3.1", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"shasum": "fa0efcf813daa43f8a6aef3ddaa80f7e66f1278e", "size": 52916, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.3.1.tgz", "integrity": "sha512-jUJrjU62MUgHDSu5JfONfgRM2V7GfN5KknsygfIbxwRZXGeayIzxk4O9GiYgEAr9DG5HJThTF5+a5x3wtrOKzQ=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.3.1_1607880897127_0.1529615376158706"}, "_hasShrinkwrap": false, "publish_time": 1607880897361, "_cnpm_publish_time": 1607880897361, "_cnpmcore_publish_time": "2021-12-16T14:46:47.411Z"}, "3.3.0": {"name": "@ctrl/tinycolor", "version": "3.3.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "exports": "./dist/modern/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && tsc -p tsconfig.modern.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.8", "@jest/globals": "26.6.2", "@types/node": "14.14.13", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.34.2", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.19.2", "typescript": "4.1.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "c54b11901ef5433af92b7ecabcbcd275d7136bf8", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.3.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"shasum": "306be071761d1726dee364c4c1f7887bac646317", "size": 56305, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.3.0.tgz", "integrity": "sha512-JKiePztb/QBsucupn4waszYT76DAwiuf+39tE7mlDxL2m2XT2N7z06BVUmPPPyCXvJ8+IFLUkAOqUyYh049E/A=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.3.0_1607831886652_0.5479395416157034"}, "_hasShrinkwrap": false, "publish_time": 1607831886848, "_cnpm_publish_time": 1607831886848, "_cnpmcore_publish_time": "2021-12-16T14:46:47.622Z"}, "3.2.1": {"name": "@ctrl/tinycolor", "version": "3.2.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.7", "@ctrl/eslint-config": "1.2.7", "@jest/globals": "26.6.2", "@types/node": "14.14.11", "del-cli": "3.0.1", "jest": "26.6.3", "jest-junit": "12.0.0", "rollup": "2.34.2", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.29.0", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.1.1", "typedoc": "0.19.2", "typescript": "4.1.2"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "349909b370b57aa19577d7d8556121d98fa6b22e", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.2.1", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"shasum": "29a5a86bcfaa41555c8483a287294e520cc28cd6", "size": 54298, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.2.1.tgz", "integrity": "sha512-WmvsSfVKQx62vLbHXJvdh4PDjSK9YU6VW9ppXTlbjgDKCYtpy2sMWbK4i9OBdxY6RRwMMVctZhWo6Y5jfMRyTg=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.2.1_1607541750328_0.7311890944531461"}, "_hasShrinkwrap": false, "publish_time": 1607541750474, "_cnpm_publish_time": 1607541750474, "_cnpmcore_publish_time": "2021-12-16T14:46:47.874Z"}, "3.2.0": {"name": "@ctrl/tinycolor", "version": "3.2.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.1", "@ctrl/eslint-config": "1.2.4", "@jest/globals": "26.5.3", "@types/node": "14.11.9", "del-cli": "3.0.1", "jest": "26.5.3", "jest-junit": "12.0.0", "rollup": "2.31.0", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.0.4", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.27.3", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.0.0", "typedoc": "0.19.2", "typescript": "4.0.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "0dd54dd277956cdbcd0d65f13ec756e31c51c29c", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.2.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"shasum": "77a8a33edb2fdc02318c828be78f6fb3d6c65eb2", "size": 54272, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.2.0.tgz", "integrity": "sha512-cP1tbXA1qJp/er2CJaO+Pbe38p7RlhV9WytUxUe79xj++Q6s/jKVvzJ9U2dF9f1/lZAdG+j94A38CsNR+uW4gw=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.2.0_1606351315378_0.2515894487642911"}, "_hasShrinkwrap": false, "publish_time": 1606351315542, "_cnpm_publish_time": 1606351315542, "_cnpmcore_publish_time": "2021-12-16T14:46:48.157Z"}, "3.1.7": {"name": "@ctrl/tinycolor", "version": "3.1.7", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.1", "@ctrl/eslint-config": "1.2.4", "@jest/globals": "26.5.3", "@types/node": "14.11.9", "del-cli": "3.0.1", "jest": "26.5.3", "jest-junit": "12.0.0", "rollup": "2.31.0", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.0.4", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.27.3", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.0.0", "typedoc": "0.19.2", "typescript": "4.0.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "f10efcdaa4d972cc9da5248fa7f62f0bec4edb6d", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.1.7", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "1585f67629882002a9f8e15a2941c9a4321bf80c", "size": 53863, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.7.tgz", "integrity": "sha512-/0C6fjXbCwu22k8mMsKRSAo9zgu61d2p75Or9IuIC0Vu5CWN88t2QHK93LhNnxnqHWf5SFwFU28w9cKfTmnfvg=="}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.7_1605288699911_0.3135526037968994"}, "_hasShrinkwrap": false, "publish_time": 1605288700102, "_cnpm_publish_time": 1605288700102, "_cnpmcore_publish_time": "2021-12-16T14:46:48.392Z"}, "3.1.6": {"name": "@ctrl/tinycolor", "version": "3.1.6", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/preset-typescript": "7.12.1", "@ctrl/eslint-config": "1.2.4", "@jest/globals": "26.5.3", "@types/node": "14.11.9", "del-cli": "3.0.1", "jest": "26.5.3", "jest-junit": "12.0.0", "rollup": "2.31.0", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.0.4", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.27.3", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.0.0", "typedoc": "0.19.2", "typescript": "4.0.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "b060c34ee9c43e0607cceec0ca0da7883797d9d4", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.1.6", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "2d0ea7d433a34b1682e2e312e8a04812210fcc60", "size": 53866, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.6.tgz", "integrity": "sha512-9RUTT3omv+5mSYFVsX143R7cTDQmT1FibCzoUVmO294mRIT0Sc8dk5srN27BTH0JKzQDWKkNCKh6q/+EkNfpkA=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.6_1602871881787_0.07069157972416917"}, "_hasShrinkwrap": false, "publish_time": 1602871881958, "_cnpm_publish_time": 1602871881958, "_cnpmcore_publish_time": "2021-12-16T14:46:49.157Z"}, "3.1.5": {"name": "@ctrl/tinycolor", "version": "3.1.5", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.10.4", "@babel/preset-typescript": "7.10.4", "@ctrl/eslint-config": "1.2.1", "@jest/globals": "26.4.2", "@types/node": "14.11.2", "del-cli": "3.0.1", "jest": "26.4.2", "jest-junit": "11.1.0", "rollup": "2.28.2", "rollup-plugin-livereload": "2.0.0", "rollup-plugin-serve": "1.0.4", "rollup-plugin-sourcemaps": "0.6.2", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.27.3", "rollup-plugin-uglify": "6.0.4", "ts-node": "9.0.0", "typedoc": "0.19.2", "typescript": "4.0.3"}, "jest": {"testEnvironment": "node"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "0e8021b696899cee3b26efa17d1655c91176a653", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.1.5", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"shasum": "dd9174fb70392ccbbb84406df5c67d27e589d959", "size": 65652, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.5.tgz", "integrity": "sha512-EhlSHlgazQ4ZemE+RWg12wfIWvLY4L2rdKAr7weYGwYTq4ZgxKN8avhKGFAfHNO6e6JBCAP6XkD4dn0bqiHg6Q=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.5_1601272459872_0.22048031403904478"}, "_hasShrinkwrap": false, "publish_time": 1601272460026, "_cnpm_publish_time": 1601272460026, "_cnpmcore_publish_time": "2021-12-16T14:46:49.390Z"}, "3.1.4": {"name": "@ctrl/tinycolor", "version": "3.1.4", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@ctrl/eslint-config": "1.0.5", "@jest/globals": "26.3.0", "@types/node": "14.0.27", "del-cli": "3.0.1", "jest": "26.3.0", "jest-junit": "11.1.0", "rollup": "2.23.1", "rollup-plugin-livereload": "1.3.0", "rollup-plugin-serve": "1.0.3", "rollup-plugin-sourcemaps": "0.6.2", "rollup-plugin-terser": "7.0.0", "rollup-plugin-typescript2": "0.27.2", "rollup-plugin-uglify": "6.0.4", "ts-node": "8.10.2", "ts-jest": "26.1.4", "typedoc": "0.18.0", "typescript": "3.9.7"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "96abd27a70192f3c5659119c66be1c1a3a8bf2fe", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.1.4", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"shasum": "e27c158836e6523fd41a647bd86ec73a74aa4151", "size": 65688, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.4.tgz", "integrity": "sha512-WHn6ClDzGS3oACt4F/k0B9QwhQCeXXRguYE6UFe6OD6wLdESU8RoMs7Y1+FEr4Tj2VZd9bfb1aEhnB9KoVrLEA=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.4_1597082134223_0.7020196422203384"}, "_hasShrinkwrap": false, "publish_time": 1597082134473, "_cnpm_publish_time": 1597082134473, "_cnpmcore_publish_time": "2021-12-16T14:46:49.614Z"}, "3.1.3": {"name": "@ctrl/tinycolor", "version": "3.1.3", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p ./tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@ctrl/eslint-config": "1.0.3", "@jest/globals": "26.1.0", "@types/node": "14.0.18", "del-cli": "3.0.1", "jest": "26.1.0", "jest-junit": "11.0.1", "rollup": "2.20.0", "rollup-plugin-livereload": "1.3.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.6.2", "rollup-plugin-terser": "6.1.0", "rollup-plugin-typescript2": "0.27.1", "rollup-plugin-uglify": "6.0.4", "ts-node": "8.10.2", "ts-jest": "26.1.1", "typedoc": "0.17.8", "typescript": "3.9.6"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "e43b2664eeb9fdd8a4b3d0642f8583b2659087a4", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.1.3", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "3406f90fd04a394a83fe874b763b7d89edffd4c1", "size": 65672, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.3.tgz", "integrity": "sha512-dHamDINLG/gF4Q6m3Ndnn4nGfLUCmvhY6Oweudqr2hMAMTIB2NvFrNW5HyjtdbqjNz9Z/unvA+/GI9QqBRgX0g=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.3_1594104016005_0.9965300003551834"}, "_hasShrinkwrap": false, "publish_time": 1594104016172, "_cnpm_publish_time": 1594104016172, "_cnpmcore_publish_time": "2021-12-16T14:46:49.845Z"}, "3.1.2": {"name": "@ctrl/tinycolor", "version": "3.1.2", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/module/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p ./tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@jest/globals": "26.0.1", "@types/node": "14.0.5", "@typescript-eslint/eslint-plugin": "3.0.1", "@typescript-eslint/parser": "3.0.1", "del-cli": "3.0.1", "eslint": "7.1.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.29.0", "eslint-plugin-import": "2.20.2", "jest": "26.0.1", "jest-junit": "10.0.0", "rollup": "2.10.9", "rollup-plugin-livereload": "1.3.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.6.2", "rollup-plugin-terser": "6.1.0", "rollup-plugin-typescript2": "0.27.1", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.8", "ts-jest": "26.0.0", "ts-node": "8.10.1", "typedoc": "0.17.7", "typescript": "3.9.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "11fd8908a4e48ee1825caff452f18e959755d985", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@3.1.2", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.5", "dist": {"shasum": "f9dfa17147a229de32b75f4cef20f81e0dda0b8e", "size": 65718, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.2.tgz", "integrity": "sha512-Sw/L9CrwSrEpWoxuNFM+ibaDbbVALq3U+ZsOfOf0bmAEyMsza//S4TtRLsuXPXe1ekEbWfITxr00PRjtoIFI6w=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.2_1590554512236_0.9337208243920194"}, "_hasShrinkwrap": false, "publish_time": 1590554512335, "_cnpm_publish_time": 1590554512335, "_cnpmcore_publish_time": "2021-12-16T14:46:50.120Z"}, "3.1.1": {"name": "@ctrl/tinycolor", "version": "3.1.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/module/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p ./tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@jest/globals": "26.0.1", "@types/node": "13.13.5", "@typescript-eslint/eslint-plugin": "2.31.0", "@typescript-eslint/parser": "2.31.0", "del-cli": "3.0.0", "eslint": "6.8.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.28.0", "eslint-plugin-import": "2.20.2", "jest": "26.0.1", "jest-junit": "10.0.0", "rollup": "2.8.2", "rollup-plugin-livereload": "1.3.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.6.2", "rollup-plugin-terser": "5.3.0", "rollup-plugin-typescript2": "0.27.0", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.7", "ts-jest": "25.5.1", "ts-node": "8.10.1", "typedoc": "0.17.6", "typescript": "3.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "cb6b11fa90e4e982225acd02fc858e7917b9a114", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@3.1.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "3fc58d91b8da4cbc8195ce5efae60383234373ac", "size": 62208, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.1.tgz", "integrity": "sha512-XR7/YQzCX3gqkwZhpg/zy8O4sQ8Tk3uhg2nsYMw5gHtEb0I7Hob4ssiQxKkm/bVBN/NH8uKhN9PJc7RPkcpY4Q=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.1_1589092259366_0.7863382158936894"}, "_hasShrinkwrap": false, "publish_time": 1589092259525, "_cnpm_publish_time": 1589092259525, "_cnpmcore_publish_time": "2021-12-16T14:46:50.353Z"}, "3.1.0": {"name": "@ctrl/tinycolor", "version": "3.1.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/module/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p ./tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --target ES6 --mode file src", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@types/jest": "25.2.1", "@types/node": "13.13.2", "@typescript-eslint/eslint-plugin": "2.29.0", "@typescript-eslint/parser": "2.29.0", "del-cli": "^3.0.0", "eslint": "6.8.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.28.0", "eslint-plugin-import": "2.20.2", "jest": "25.4.0", "jest-junit": "10.0.0", "rollup": "2.7.2", "rollup-plugin-livereload": "1.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.5.0", "rollup-plugin-terser": "5.3.0", "rollup-plugin-typescript2": "0.27.0", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.7", "ts-jest": "25.4.0", "ts-node": "8.9.0", "typedoc": "0.17.4", "typescript": "3.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "94bd1c05a859d4e797a84f72eaef2dca08e343d4", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@3.1.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"shasum": "aeeb39271e0a797196a40e988558842610d2e1a5", "size": 62175, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.1.0.tgz", "integrity": "sha512-J92yG122XoF+UiuQSzBgV451vSm9B0xMb5oGs7vgZijYZtNc80hNep/KbgUeBbj5f+YHehrmbAHlg/yBGX3/+w=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.1.0_1588055903747_0.6497986268702558"}, "_hasShrinkwrap": false, "publish_time": 1588055903842, "_cnpm_publish_time": 1588055903842, "_cnpmcore_publish_time": "2021-12-16T14:46:50.627Z"}, "3.0.2": {"name": "@ctrl/tinycolor", "version": "3.0.2", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/module/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p ./tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@types/jest": "25.2.1", "@types/node": "13.13.2", "@typescript-eslint/eslint-plugin": "2.29.0", "@typescript-eslint/parser": "2.29.0", "del-cli": "^3.0.0", "eslint": "6.8.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.27.0", "eslint-plugin-import": "2.20.2", "jest": "25.4.0", "jest-junit": "10.0.0", "rollup": "2.7.1", "rollup-plugin-livereload": "1.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.5.0", "rollup-plugin-terser": "5.3.0", "rollup-plugin-typescript2": "0.27.0", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.7", "ts-jest": "25.4.0", "ts-node": "8.9.0", "typedoc": "0.17.4", "typescript": "3.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "d7017a1757a71b1ac951d3bc8eff271227126a2a", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@3.0.2", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"shasum": "35279ce126588539443fd2fda695177438b6f430", "size": 61526, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.0.2.tgz", "integrity": "sha512-Gh4rBwCqLlRowVqmNqbW3xRvQYbPlbnu6iudlZhsTcP/Ifo7q2rSQ2D8BSNUm7MNOozzn7t3buJNBF6RXvPq+g=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.0.2_1587577629448_0.4873301288010661"}, "_hasShrinkwrap": false, "publish_time": 1587577629563, "_cnpm_publish_time": 1587577629563, "_cnpmcore_publish_time": "2021-12-16T14:46:50.880Z"}, "3.0.1": {"name": "@ctrl/tinycolor", "version": "3.0.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@types/jest": "25.2.1", "@types/node": "13.13.2", "@typescript-eslint/eslint-plugin": "2.29.0", "@typescript-eslint/parser": "2.29.0", "eslint": "6.8.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.27.0", "eslint-plugin-import": "2.20.2", "jest": "25.4.0", "jest-junit": "10.0.0", "rollup": "2.7.1", "rollup-plugin-livereload": "1.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.5.0", "rollup-plugin-terser": "5.3.0", "rollup-plugin-typescript2": "0.27.0", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.7", "ts-jest": "25.4.0", "ts-node": "8.9.0", "typedoc": "0.17.4", "typescript": "3.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "d36a9575f1d71065e9c15fed4d2a9964ffad646a", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@3.0.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"shasum": "c1fdc3b9416059deb29a9937aa321cdd22d8b2b5", "size": 61534, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.0.1.tgz", "integrity": "sha512-PZYlajvmnf5JmdgXqNPYzH5XCy/F13KL5W7xgW78GtWnXDlahFnj8O8uQn5HPiUHiWOkb/UJoTjSW2QC9jNR8w=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.0.1_1587576715731_0.5437548982331784"}, "_hasShrinkwrap": false, "publish_time": 1587576715957, "_cnpm_publish_time": 1587576715957, "_cnpmcore_publish_time": "2021-12-16T14:46:51.124Z"}, "3.0.0": {"name": "@ctrl/tinycolor", "version": "3.0.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@types/jest": "25.2.1", "@types/node": "13.13.2", "@typescript-eslint/eslint-plugin": "2.29.0", "@typescript-eslint/parser": "2.29.0", "eslint": "6.8.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.27.0", "eslint-plugin-import": "2.20.2", "jest": "25.4.0", "jest-junit": "10.0.0", "rollup": "2.7.1", "rollup-plugin-livereload": "1.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.5.0", "rollup-plugin-terser": "5.3.0", "rollup-plugin-typescript2": "0.27.0", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.7", "ts-jest": "25.4.0", "ts-node": "8.9.0", "typedoc": "0.17.4", "typescript": "3.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=10.19.0"}, "gitHead": "41cfb8de77c39009a78fa2ba866555ae7aeb2332", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@3.0.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"shasum": "2bed59dce126fc4cd46fc1967d76007c0d004ba7", "size": 41548, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.0.0.tgz", "integrity": "sha512-kUwIv93RtPqb3STcxe88/ho6sWfw3rfCiZYNXaYXEIBeR5pW2taLtczZnORxdj0sYKkVjvDagKC5ZF4ZaUYDBw=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.0.0_1587575915071_0.1601858541424117"}, "_hasShrinkwrap": false, "publish_time": 1587575915275, "_cnpm_publish_time": 1587575915275, "_cnpmcore_publish_time": "2021-12-16T14:46:51.344Z"}, "2.6.1": {"name": "@ctrl/tinycolor", "version": "2.6.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "dependencies": {}, "devDependencies": {"@types/jest": "25.1.3", "@types/node": "13.7.7", "@typescript-eslint/eslint-plugin": "2.21.0", "@typescript-eslint/parser": "2.21.0", "eslint": "6.8.0", "eslint-config-xo-space": "0.24.0", "eslint-config-xo-typescript": "0.26.0", "eslint-plugin-import": "2.20.1", "jest": "25.1.0", "jest-junit": "10.0.0", "rollup": "1.32.0", "rollup-plugin-livereload": "1.0.4", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.5.0", "rollup-plugin-terser": "5.2.0", "rollup-plugin-typescript2": "0.26.0", "rollup-plugin-uglify": "6.0.4", "semantic-release": "17.0.4", "ts-jest": "25.2.1", "ts-node": "8.6.2", "typedoc": "0.16.11", "typescript": "3.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "92d871d9be890aa0acfffa6bcd3b7bffe543e04e", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.6.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.1", "dist": {"shasum": "0e78cc836a1fd997a9a22fa1c26c555411882160", "size": 52167, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.6.1.tgz", "integrity": "sha512-xKN3lAA+xbNhP+JX3sLhzA65IGiKqun/Yf9jBlZX7Og0SxlCrrqKxqTd6ccn20fv3Cgcvq6KnjPhnmS+v7uAwQ=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.6.1_1583110060903_0.760319933419151"}, "_hasShrinkwrap": false, "publish_time": 1583110061035, "_cnpm_publish_time": 1583110061035, "_cnpmcore_publish_time": "2021-12-16T14:46:51.599Z"}, "2.6.0": {"name": "@ctrl/tinycolor", "version": "2.6.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "devDependencies": {"@types/jest": "24.0.24", "@types/node": "12.12.21", "@typescript-eslint/eslint-plugin": "2.12.0", "@typescript-eslint/parser": "2.12.0", "eslint": "6.7.2", "eslint-config-xo-space": "0.22.0", "eslint-config-xo-typescript": "0.23.0", "eslint-plugin-import": "2.19.1", "jest": "24.9.0", "jest-junit": "10.0.0", "rollup": "1.27.13", "rollup-plugin-livereload": "1.0.4", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "5.1.3", "rollup-plugin-typescript2": "0.25.3", "rollup-plugin-uglify": "6.0.4", "semantic-release": "15.13.31", "ts-jest": "24.2.0", "ts-node": "8.5.4", "typedoc": "0.15.5", "typescript": "3.7.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "22b0277020f8e23b7260ebdb8ccc46d3d3c98d7b", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.6.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c269def5ed1f871913f299a475c01bdb39119eee", "size": 51734, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.6.0.tgz", "integrity": "sha512-bvkszNAcbmR2zrjjkaHbTVbEj07Id44HsBWf57mugPcvJNIPaWLqxWV/GUJVJuXXayqFP2X09cZRqKrCy/v10Q=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.6.0_1576739862359_0.867616918527023"}, "_hasShrinkwrap": false, "publish_time": 1576739862535, "_cnpm_publish_time": 1576739862535, "_cnpmcore_publish_time": "2021-12-16T14:46:51.892Z"}, "2.5.4": {"name": "@ctrl/tinycolor", "version": "2.5.4", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "devDependencies": {"@types/jest": "24.0.19", "@types/node": "12.11.1", "@typescript-eslint/eslint-plugin": "2.4.0", "@typescript-eslint/parser": "2.4.0", "eslint": "6.5.1", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.19.0", "eslint-plugin-import": "2.18.2", "jest": "24.9.0", "jest-junit": "8.0.0", "rollup": "1.24.0", "rollup-plugin-livereload": "1.0.4", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "5.1.2", "rollup-plugin-typescript2": "0.24.3", "rollup-plugin-uglify": "6.0.3", "semantic-release": "15.13.24", "ts-jest": "24.1.0", "ts-node": "8.4.1", "typedoc": "0.15.0", "typescript": "3.6.4"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "45e7b5764ced29f00bb1a3d614f78b6f2acc9846", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.5.4", "_nodeVersion": "10.16.3", "_npmVersion": "6.12.0", "dist": {"shasum": "fa20f45eaae74696070328342553ed5de224bb4d", "size": 51205, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.5.4.tgz", "integrity": "sha512-PXqAcLD6R8GL8dFcls728HrYzDdYN60U8RXvc39wmOs1pJIcyc3g+dleWu+fGgLnEGG4SwblEUu+EF4/dH/2ew=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.5.4_1571292371462_0.7078839075308576"}, "_hasShrinkwrap": false, "publish_time": 1571292371628, "_cnpm_publish_time": 1571292371628, "_cnpmcore_publish_time": "2021-12-16T14:46:52.161Z"}, "2.5.3": {"name": "@ctrl/tinycolor", "version": "2.5.3", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts test/**/*.ts", "prepare": "npm run build", "build": "del dist && npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "devDependencies": {"@types/jest": "24.0.15", "@types/node": "12.6.6", "@typescript-eslint/eslint-plugin": "1.12.0", "@typescript-eslint/parser": "1.12.0", "del-cli": "2.0.0", "eslint": "6.0.1", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.15.0", "eslint-plugin-import": "2.18.0", "jest": "24.8.0", "jest-junit": "6.4.0", "rollup": "1.17.0", "rollup-plugin-livereload": "1.0.1", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "5.1.1", "rollup-plugin-typescript2": "0.22.0", "rollup-plugin-uglify": "6.0.2", "semantic-release": "15.13.18", "ts-jest": "24.0.2", "ts-node": "8.3.0", "typedoc": "0.15.0-0", "typescript": "3.5.3"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "7343301b7554f9e52cb605e3330f565a32217b54", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.5.3", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.1", "dist": {"shasum": "65dbb773f3a7e9a37b88ea0a5b68a392f8a447b0", "size": 41317, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.5.3.tgz", "integrity": "sha512-tu0Xqhz0AI5LoU2yyz4asVAev4hzWt30dutbY6Nu7pKvLX0JP5p6NJ5uomwC/blhLMk88hVgXRODFlTB+c03Nw=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.5.3_1563345565011_0.901651089020135"}, "_hasShrinkwrap": false, "publish_time": 1563345565147, "_cnpm_publish_time": 1563345565147, "_cnpmcore_publish_time": "2021-12-16T14:46:52.369Z"}, "2.5.2": {"name": "@ctrl/tinycolor", "version": "2.5.2", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts test/**/*.ts", "prepare": "npm run build", "build": "del dist && npm run build-es6 && npm run build-cmjs && ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "devDependencies": {"@types/jest": "24.0.13", "@types/node": "12.0.2", "@typescript-eslint/eslint-plugin": "1.9.0", "@typescript-eslint/parser": "1.9.0", "del-cli": "1.1.0", "eslint": "5.16.0", "eslint-config-prettier": "4.3.0", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.12.0", "eslint-plugin-import": "2.17.3", "jest": "24.8.0", "jest-junit": "6.4.0", "rollup": "1.12.4", "rollup-plugin-livereload": "1.0.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "5.0.0", "rollup-plugin-typescript2": "0.21.1", "rollup-plugin-uglify": "6.0.2", "semantic-release": "15.13.12", "ts-jest": "24.0.2", "ts-node": "8.2.0", "typedoc": "0.15.0-0", "typescript": "3.4.5"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "ffdbd741eff7fafa0a13027c2af25ffb03f3ded2", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.5.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "a2f591d6f385d8751a3e9d295203ffb2171a7040", "size": 45878, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.5.2.tgz", "integrity": "sha512-0uQs1pXZtZByI5pLlWEWRnNmXmD/IWv7rgwDkuh5jGkBMC3gCVGYKbxlUglct8EBj3U6Ada1iTTSrNeep/rzlA=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.5.2_1559022179891_0.5722110318799571"}, "_hasShrinkwrap": false, "publish_time": 1559022180055, "_cnpm_publish_time": 1559022180055, "_cnpmcore_publish_time": "2021-12-16T14:46:52.599Z"}, "2.5.1": {"name": "@ctrl/tinycolor", "version": "2.5.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts test/**/*.ts", "prepare": "npm run build", "build": "del dist && npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "devDependencies": {"@types/fs-extra": "7.0.0", "@types/jest": "24.0.13", "@types/node": "12.0.2", "@typescript-eslint/eslint-plugin": "1.9.0", "@typescript-eslint/parser": "1.9.0", "del-cli": "^1.1.0", "eslint": "5.16.0", "eslint-config-prettier": "4.3.0", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.12.0", "eslint-plugin-import": "2.17.2", "fs-extra": "8.0.1", "jest": "24.8.0", "jest-junit": "6.4.0", "npm-run-all": "4.1.5", "rollup": "1.12.3", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "4.0.4", "rollup-plugin-typescript2": "0.21.1", "rollup-plugin-uglify": "6.0.2", "semantic-release": "15.13.12", "ts-jest": "24.0.2", "ts-node": "8.1.0", "typedoc": "0.14.2", "typescript": "3.4.5"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "dbaae13290d62b9314aea3c2d95c1fc2429ae821", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.5.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "8c28e97f1a37b5fc1dee00b5226d5e8947f7f8b0", "size": 45904, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.5.1.tgz", "integrity": "sha512-I5SD9ii2gh/4/LNyg+dXG6S5B41bZyQyXCldSZxNHMlNnFtDgo1h7Tn/PiCZ8qlRkAFmv+kx1ay/gtTyCVrGcw=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.5.1_1558363448579_0.6424353756533527"}, "_hasShrinkwrap": false, "publish_time": 1558363448768, "_cnpm_publish_time": 1558363448768, "_cnpmcore_publish_time": "2021-12-16T14:46:52.795Z"}, "2.5.0": {"name": "@ctrl/tinycolor", "version": "2.5.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./dist/public_api.js", "module": "./dist/es/public_api.js", "typings": "./dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts test/**/*.ts", "prepare": "npm run build", "build": "del dist && npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "semantic-release"}, "devDependencies": {"@types/fs-extra": "7.0.0", "@types/jest": "24.0.13", "@types/node": "12.0.2", "@typescript-eslint/eslint-plugin": "1.9.0", "@typescript-eslint/parser": "1.9.0", "del-cli": "^1.1.0", "eslint": "5.16.0", "eslint-config-prettier": "4.3.0", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.12.0", "eslint-plugin-import": "2.17.2", "fs-extra": "8.0.1", "jest": "24.8.0", "jest-junit": "6.4.0", "npm-run-all": "4.1.5", "rollup": "1.12.3", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "4.0.4", "rollup-plugin-typescript2": "0.21.1", "rollup-plugin-uglify": "6.0.2", "semantic-release": "15.13.12", "ts-jest": "24.0.2", "ts-node": "8.1.0", "typedoc": "0.14.2", "typescript": "3.4.5"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "15338083836a1c2f29d688e928b9fe72fa0a915a", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.5.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "d79bf9ebd1b94ad80c00ad313164569178858695", "size": 45900, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.5.0.tgz", "integrity": "sha512-+KOnyYjn+fHDChxYH1D+9L5RrmEq5wJm4nB7HvnZSLMlypPtCGhGivxUe2MxxyVtABTKdoOhULraCxlcY8aa0A=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.5.0_1558337899333_0.5787804984191625"}, "_hasShrinkwrap": false, "publish_time": 1558337899508, "_cnpm_publish_time": 1558337899508, "_cnpmcore_publish_time": "2021-12-16T14:46:53.053Z"}, "2.4.0": {"name": "@ctrl/tinycolor", "version": "2.4.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./es/public_api.js", "typings": "./public_api.d.ts", "sideEffects": false, "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts test/**/*.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "cd dist && semantic-release"}, "devDependencies": {"@types/fs-extra": "5.0.5", "@types/jest": "24.0.9", "@types/node": "11.10.4", "@typescript-eslint/eslint-plugin": "1.4.2", "@typescript-eslint/parser": "1.4.2", "eslint": "5.15.0", "eslint-config-prettier": "4.1.0", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.8.0", "eslint-plugin-import": "2.16.0", "fs-extra": "7.0.1", "jest": "24.1.0", "jest-junit": "6.3.0", "npm-run-all": "4.1.5", "rimraf": "2.6.3", "rollup": "1.4.0", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "4.0.4", "rollup-plugin-typescript2": "0.19.3", "rollup-plugin-uglify": "6.0.2", "semantic-release": "15.13.3", "ts-jest": "24.0.0", "ts-node": "8.0.2", "typedoc": "0.14.2", "typescript": "3.3.3333"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "a2a714f211da262cf615f705b555c2ac99dc91b3", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.4.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.2", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "0b806ffd71c9c4b28044fa1f6064e8c033a42b9d", "size": 47852, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.4.0.tgz", "integrity": "sha512-ZLjdsst8/ENM6spDmh3qPQSM+iDSerTGjA05KhAXog7o/aOa9tn7qzWMeOtJzGaDMmsBLxh4IuZ5GH7nfda9gg=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.4.0_1551807947184_0.8374149832843298"}, "_hasShrinkwrap": false, "publish_time": 1551807947395, "_cnpm_publish_time": 1551807947395, "_cnpmcore_publish_time": "2021-12-16T14:46:53.761Z"}, "2.3.0": {"name": "@ctrl/tinycolor", "version": "2.3.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./es/public_api.js", "typings": "./public_api.d.ts", "sideEffects": false, "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint --fix src/**/*.ts test/**/*.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "semantic-release": "cd dist && semantic-release"}, "devDependencies": {"@types/fs-extra": "5.0.5", "@types/jest": "24.0.9", "@types/node": "11.10.4", "@typescript-eslint/eslint-plugin": "1.4.2", "@typescript-eslint/parser": "1.4.2", "eslint": "5.15.0", "eslint-config-prettier": "4.1.0", "eslint-config-xo-space": "0.21.0", "eslint-config-xo-typescript": "0.8.0", "eslint-plugin-import": "2.16.0", "fs-extra": "7.0.1", "jest": "24.1.0", "jest-junit": "6.3.0", "npm-run-all": "4.1.5", "rimraf": "2.6.3", "rollup": "1.4.0", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "4.0.4", "rollup-plugin-typescript2": "0.19.3", "rollup-plugin-uglify": "6.0.2", "semantic-release": "15.13.3", "ts-jest": "24.0.0", "ts-node": "8.0.2", "typedoc": "0.14.2", "typescript": "3.3.3333"}, "release": {"branch": "master"}, "engines": {"node": ">=8.0.0"}, "gitHead": "75f48f456b2276faceb1def9f43eb05f7c3919e9", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.3.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.2", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "3af67cdc8e938433d405c58ff9b556c986117720", "size": 47730, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.3.0.tgz", "integrity": "sha512-N7f17eBWGCb9K5LHV9W0NSQwXZZ4ZHFtuqlVKwPffmF0fwd+nl5AOGaLC+qFhW2LveiZB0ZYHh2TKGW5Qixe3Q=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.3.0_1551574987005_0.9574150091760807"}, "_hasShrinkwrap": false, "publish_time": 1551574987136, "_cnpm_publish_time": 1551574987136, "_cnpmcore_publish_time": "2021-12-16T14:46:54.024Z"}, "2.2.1": {"name": "@ctrl/tinycolor", "version": "2.2.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./es/public_api.js", "typings": "./public_api.d.ts", "sideEffects": false, "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist && semantic-release"}, "devDependencies": {"@types/fs-extra": "5.0.4", "@types/jest": "23.3.2", "@types/node": "10.11.0", "commitizen": "2.10.1", "cz-conventional-changelog": "2.1.0", "fs-extra": "7.0.0", "jest": "23.6.0", "npm-run-all": "4.1.3", "rimraf": "2.6.2", "rollup": "0.66.2", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "3.0.0", "rollup-plugin-typescript2": "0.17.0", "rollup-plugin-uglify": "6.0.0", "semantic-release": "15.9.16", "travis-deploy-once": "5.0.8", "ts-jest": "23.10.1", "ts-node": "7.0.1", "tslint": "5.11.0", "tslint-config-prettier": "1.15.0", "tslint-config-standard": "8.0.1", "typedoc": "0.12.0", "typescript": "3.0.3"}, "release": {"branch": "master"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"testURL": "http://localhost", "transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "8d578a73fe3b2eafc1ccf3398e443a8126fbf1b7", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "4aae678391bb65ce12c39c0fed5159273709dd81", "size": 47583, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.2.1.tgz", "integrity": "sha512-HFYW2zo5H9L61it865F/6fMOphYRVxdSztcB3DHn9AoaNOO9G+uIBuNXwkt+BwT7CbjQoJ6N5L6VjF5Mumztkw=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.2.1_1537942615572_0.7243185666491485"}, "_hasShrinkwrap": false, "publish_time": 1537942615858, "_cnpm_publish_time": 1537942615858, "_cnpmcore_publish_time": "2021-12-16T14:46:54.254Z"}, "2.2.0": {"name": "@ctrl/tinycolor", "version": "2.2.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./es/public_api.js", "typings": "./public_api.d.ts", "sideEffects": false, "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist && semantic-release"}, "devDependencies": {"@types/fs-extra": "5.0.4", "@types/jest": "23.3.1", "@types/node": "10.5.7", "commitizen": "2.10.1", "cz-conventional-changelog": "2.1.0", "fs-extra": "7.0.0", "jest": "23.5.0", "npm-run-all": "4.1.3", "rimraf": "2.6.2", "rollup": "0.64.1", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "1.0.1", "rollup-plugin-typescript2": "0.16.1", "rollup-plugin-uglify": "4.0.0", "semantic-release": "15.9.7", "travis-deploy-once": "5.0.2", "ts-jest": "23.1.3", "ts-node": "7.0.0", "tslint": "5.11.0", "tslint-config-prettier": "1.14.0", "tslint-config-standard": "7.1.0", "typedoc": "0.11.1", "typescript": "3.0.1"}, "release": {"branch": "master"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"testURL": "http://localhost", "transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "59d37d8f6060b3504d14be602818e107b5945772", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.2.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "1608c8cffa3c9e79a86167d331fa687e2ff847f5", "size": 47588, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.2.0.tgz", "integrity": "sha512-EyIBWqpn6DzCQQjFKn1imrvk0riGxzTyfeFLRqH/6MLPR7enShJpZojaZpqK/BsyAQOtWBwvzG1DegtquOEd2Q=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.2.0_1533928320069_0.4139993576057279"}, "_hasShrinkwrap": false, "publish_time": 1533928320176, "_cnpm_publish_time": 1533928320176, "_cnpmcore_publish_time": "2021-12-16T14:46:54.505Z"}, "2.1.0": {"name": "@ctrl/tinycolor", "version": "2.1.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "sideEffects": false, "typings": "./public_api.d.ts", "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build-es6; npm run build-cmjs; ts-node build", "build-es6": "tsc -P ./tsconfig-build.json --outDir 'dist/es' --module es2015 -d false --moduleResolution node", "build-cmjs": "tsc -P ./tsconfig-build.json", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist && semantic-release"}, "devDependencies": {"@types/fs-extra": "5.0.4", "@types/jest": "23.3.1", "@types/node": "10.5.5", "commitizen": "2.10.1", "cz-conventional-changelog": "2.1.0", "fs-extra": "7.0.0", "jest": "23.4.2", "npm-run-all": "4.1.3", "rimraf": "2.6.2", "rollup": "0.63.5", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-terser": "1.0.1", "rollup-plugin-typescript2": "0.16.1", "rollup-plugin-uglify": "4.0.0", "semantic-release": "15.9.3", "travis-deploy-once": "5.0.1", "ts-jest": "23.0.1", "ts-node": "7.0.0", "tslint": "5.11.0", "tslint-config-prettier": "1.14.0", "tslint-config-standard": "7.1.0", "typedoc": "0.11.1", "typescript": "3.0.1"}, "release": {"branch": "master"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"testURL": "http://localhost", "transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "82e23a66746bf9f95557dd076217eeca9253050f", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.1.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "06b356495df354938553a5fbeda58a2ceea228d4", "size": 46615, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.1.0.tgz", "integrity": "sha512-JcPiFxUZqUFah3Hz0MCWUQzefQdzkORJQ+evMsWlRrMZA5DsbD7dRCyR2E1FbadKJIxO2Ddnb8fzRKO1Vp0EPQ=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.1.0_1533233231672_0.33195365832304846"}, "_hasShrinkwrap": false, "publish_time": 1533233231995, "_cnpm_publish_time": 1533233231995, "_cnpmcore_publish_time": "2021-12-16T14:46:54.761Z"}, "2.0.1": {"name": "@ctrl/tinycolor", "version": "2.0.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "sideEffects": false, "typings": "./public_api.d.ts", "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json && ts-node ./build.ts", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist/package-dist && semantic-release"}, "dependencies": {}, "devDependencies": {"@types/fs-extra": "5.0.4", "@types/jest": "23.3.1", "@types/node": "10.5.5", "commitizen": "2.10.1", "cz-conventional-changelog": "2.1.0", "fs-extra": "7.0.0", "jest": "23.4.2", "npm-run-all": "4.1.3", "rimraf": "2.6.2", "rollup": "0.63.4", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-typescript2": "0.16.1", "rollup-plugin-uglify": "4.0.0", "semantic-release": "15.9.3", "travis-deploy-once": "5.0.1", "ts-jest": "23.0.1", "ts-node": "7.0.0", "tslint": "5.11.0", "tslint-config-prettier": "1.14.0", "tslint-config-standard": "7.1.0", "typedoc": "0.11.1", "typescript": "3.0.1"}, "release": {"branch": "master"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"testURL": "http://localhost", "transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "2893b16de4acc5c0c248b6b9ecdbf5a6e1834a02", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "bac9d546ed25762ffd99dd991f45146bf6333095", "size": 55418, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.0.1.tgz", "integrity": "sha512-bf3El2FfcWQzU4h3SvTY1lg5n+ePBjWG3whjyPkX72HrpU46RDr3dPByLVHlj0G0fSecBXTOk9RQp/yOOqV97Q=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.0.1_1533099265992_0.9281828100734812"}, "_hasShrinkwrap": false, "publish_time": 1533099266142, "_cnpm_publish_time": 1533099266142, "_cnpmcore_publish_time": "2021-12-16T14:46:55.095Z"}, "2.0.0": {"name": "@ctrl/tinycolor", "version": "2.0.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "sideEffects": false, "typings": "./public_api.d.ts", "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json && ts-node ./build.ts", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist/package-dist && semantic-release"}, "dependencies": {}, "devDependencies": {"@types/fs-extra": "5.0.2", "@types/jest": "22.2.3", "@types/node": "10.0.6", "commitizen": "2.9.6", "cz-conventional-changelog": "2.1.0", "fs-extra": "6.0.0", "jest": "22.4.3", "npm-run-all": "4.1.3", "rimraf": "2.6.2", "rollup": "0.58.2", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-typescript2": "0.13.0", "rollup-plugin-uglify": "3.0.0", "semantic-release": "15.4.0", "travis-deploy-once": "5.0.0", "ts-jest": "22.4.5", "ts-node": "6.0.3", "tslint": "5.10.0", "tslint-config-prettier": "1.12.0", "tslint-config-standard": "7.0.0", "typedoc": "0.11.1", "typescript": "2.8.3"}, "release": {"branch": "master"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "4a21e8004a3e3f6979bd198eb693932d463edde0", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.1.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "d7fdd44eccf3555b86c56b7d5616c840248a72aa", "size": 81179, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-2.0.0.tgz", "integrity": "sha512-gs7iBtI1ANFGlKU7kG1p6JM9QyZPbZS8x7jr5NmM7yEFjaPj/L6TiZJ1Xg1hjluNYDF+z0voRusmlIrVtdYZtA=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_2.0.0_1525852735575_0.3445414784866523"}, "_hasShrinkwrap": false, "publish_time": 1525852735675, "_cnpm_publish_time": 1525852735675, "_cnpmcore_publish_time": "2021-12-16T14:46:55.374Z"}, "1.2.0": {"name": "@ctrl/tinycolor", "version": "1.2.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "sideEffects": false, "typings": "./public_api.d.ts", "scripts": {"demo": "npm-run-all --parallel start:demo watch:demo", "build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "start:demo": "serve demo/public", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json && ts-node ./build.ts", "build:docs": "typedoc src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist/package-dist && semantic-release"}, "dependencies": {}, "devDependencies": {"@types/fs-extra": "5.0.2", "@types/jest": "22.2.3", "@types/node": "10.0.4", "commitizen": "2.9.6", "cz-conventional-changelog": "2.1.0", "fs-extra": "6.0.0", "jest": "22.4.3", "npm-run-all": "4.1.3", "prettier": "1.12.1", "rimraf": "2.6.2", "rollup": "0.58.2", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-typescript2": "0.13.0", "rollup-plugin-uglify": "3.0.0", "semantic-release": "15.3.1", "travis-deploy-once": "5.0.0", "ts-jest": "22.4.5", "ts-node": "6.0.3", "tslint": "5.10.0", "tslint-config-prettier": "1.12.0", "tslint-config-standard": "7.0.0", "typedoc": "0.11.1", "typescript": "2.8.3"}, "release": {"branch": "master"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "327f615320e4272a31111e0b45687c4d48b28090", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "203f76cea8e4d2609a76f0e4198234ef006f6b5e", "size": 81389, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-1.2.0.tgz", "integrity": "sha512-3oZ<PERSON>+kakO4l0u3+qo3oZ+kX0Lb2xB9FctNgVOlrf0ZY0BBQWoETwOBDKEBI9LM0CQET979o7Sh78hnxX7MBxuQ=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_1.2.0_1525675562683_0.0012266599050576588"}, "_hasShrinkwrap": false, "publish_time": 1525675562819, "_cnpm_publish_time": 1525675562819, "_cnpmcore_publish_time": "2021-12-16T14:46:55.674Z"}, "1.1.1": {"name": "@ctrl/tinycolor", "version": "1.1.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "sideEffects": false, "typings": "./public_api.d.ts", "scripts": {"lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json && ts-node ./build.ts", "build:docs": "typedoc --out docs --target es6 --theme minimal --mode file src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "commitlint": "commitlint -e $GIT_PARAMS", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist/package-dist && semantic-release"}, "dependencies": {}, "devDependencies": {"@commitlint/cli": "6.2.0", "@commitlint/config-conventional": "6.1.3", "@types/fs-extra": "5.0.2", "@types/jest": "22.2.3", "@types/node": "10.0.2", "commitizen": "2.9.6", "cz-conventional-changelog": "2.1.0", "fs-extra": "6.0.0", "husky": "1.0.0-rc.2", "jest": "22.4.3", "prettier": "1.12.1", "rimraf": "2.6.2", "rollup": "0.58.2", "rollup-plugin-sourcemaps": "0.4.2", "semantic-release": "15.2.0", "travis-deploy-once": "5.0.0", "ts-jest": "22.4.4", "ts-node": "6.0.2", "tslint": "5.9.1", "tslint-config-prettier": "1.12.0", "tslint-config-standard": "7.0.0", "typedoc": "0.11.1", "typescript": "2.8.3"}, "release": {"branch": "master"}, "husky": {"hooks": {"commit-msg": "npm run commitlint"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "3480dc5e3b7c60fdd92ae10fb1d9a984e7935d41", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@1.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "f1b6b781adf1782ed48ff693bd395aa00eb6de0b", "size": 55470, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-1.1.1.tgz", "integrity": "sha512-XHG3hTNl62fHlrX8M8nJ/AwwJMU/yFH4LHGsKfE+67jKDjYHw/8AsHL1e0Z7Uzk2nXwP9IpLAVySCXNBV2IbZQ=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_1.1.1_1525278978231_0.8929806658481503"}, "_hasShrinkwrap": false, "publish_time": 1525278978342, "_cnpm_publish_time": 1525278978342, "_cnpmcore_publish_time": "2021-12-16T14:46:55.929Z"}, "1.1.0": {"name": "@ctrl/tinycolor", "version": "1.1.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "sideEffects": false, "typings": "./public_api.d.ts", "scripts": {"lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json && ts-node ./build.ts", "build:docs": "typedoc --out docs --target es6 --theme minimal --mode file src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "commitlint": "commitlint -e $GIT_PARAMS", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist/package-dist && semantic-release"}, "dependencies": {}, "devDependencies": {"@commitlint/cli": "6.1.3", "@commitlint/config-conventional": "6.1.3", "@types/fs-extra": "5.0.2", "@types/jest": "22.2.3", "@types/node": "10.0.1", "commitizen": "2.9.6", "cz-conventional-changelog": "2.1.0", "fs-extra": "6.0.0", "husky": "1.0.0-rc.2", "jest": "22.4.3", "prettier": "1.12.1", "rimraf": "2.6.2", "rollup": "0.58.2", "rollup-plugin-sourcemaps": "0.4.2", "semantic-release": "15.1.8", "travis-deploy-once": "5.0.0", "ts-jest": "22.4.4", "ts-node": "6.0.2", "tslint": "5.9.1", "tslint-config-prettier": "1.12.0", "tslint-config-standard": "7.0.0", "typedoc": "0.11.1", "typescript": "2.8.3"}, "release": {"branch": "master"}, "husky": {"hooks": {"commit-msg": "npm run commitlint"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "144e262b4533741b6c38f5092bc194ba5f495fdf", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "d4989005e0dc8c19ee257dcb74631000912d844a", "size": 55390, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-1.1.0.tgz", "integrity": "sha512-Tsd2Q5+zY1m+h2j3KRLR9YXwpAlWl5wm2jsmnIlLd+hMcBARU/s9pZpmSB4zVXenlGGavEuo2UozfEHs5JMXPQ=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_1.1.0_1525198161764_0.27906737334745824"}, "_hasShrinkwrap": false, "publish_time": 1525198161913, "_cnpm_publish_time": 1525198161913, "_cnpmcore_publish_time": "2021-12-16T14:46:56.217Z"}, "1.0.0": {"name": "@ctrl/tinycolor", "version": "1.0.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typectrl/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "./public_api.js", "module": "./bundles/tinycolor.es2015.js", "typings": "./public_api.d.ts", "scripts": {"lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'", "lint:fix": "tslint -p tsconfig.json -t stylish --fix 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json && ts-node ./build.ts", "build:docs": "typedoc --out docs --target es6 --theme minimal --mode file src && touch docs/.nojekyll", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run test -- --coverage --no-cache", "commit": "git-cz", "commitlint": "commitlint -e $GIT_PARAMS", "travis-deploy-once": "travis-deploy-once", "semantic-release": "cd dist/package-dist && semantic-release"}, "dependencies": {}, "devDependencies": {"@commitlint/cli": "^6.1.3", "@commitlint/config-conventional": "^6.1.3", "@types/fs-extra": "^5.0.2", "@types/jest": "^22.2.3", "@types/node": "^10.0.0", "commitizen": "^2.9.6", "cz-conventional-changelog": "^2.1.0", "fs-extra": "^5.0.0", "husky": "^1.0.0-rc.2", "jest": "^22.4.3", "prettier": "^1.12.1", "rimraf": "^2.6.2", "rollup": "^0.58.2", "rollup-plugin-sourcemaps": "^0.4.2", "semantic-release": "^15.1.7", "travis-deploy-once": "^5.0.0", "ts-jest": "^22.4.4", "ts-node": "^6.0.2", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "tslint-config-standard": "^7.0.0", "typedoc": "^0.11.1", "typescript": "^2.8.3"}, "release": {"branch": "master"}, "husky": {"hooks": {"commit-msg": "npm run commitlint"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"transform": {".ts": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/test/"], "collectCoverage": true}, "engines": {"node": ">=8.0.0"}, "gitHead": "2733da4cd3402e2f41b4c5efe0cc5cff9a39077b", "bugs": {"url": "https://github.com/typectrl/tinycolor/issues"}, "homepage": "https://github.com/typectrl/tinycolor#readme", "_id": "@ctrl/tinycolor@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "dist": {"shasum": "faf5cc00ce2d6e96cc6679876f82f508bfce6eed", "size": 41295, "noattachment": false, "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-1.0.0.tgz", "integrity": "sha512-eQYPYnnp77hfaIRqNC58QI3cgkhiKbS1nbnFt7zEYVYjC693A1JibHoJs9xca6pBYE0Acr0q7EPVbkRiZKQfPg=="}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_1.0.0_1525071242678_0.44458066670507246"}, "_hasShrinkwrap": false, "publish_time": 1525071242773, "_cnpm_publish_time": 1525071242773, "_cnpmcore_publish_time": "2021-12-16T14:46:56.518Z"}, "3.4.1": {"name": "@ctrl/tinycolor", "version": "3.4.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.17.9", "@babel/preset-typescript": "7.16.7", "@ctrl/eslint-config": "3.4.1", "@jest/globals": "27.5.1", "@types/node": "17.0.23", "del-cli": "4.0.1", "jest": "27.5.1", "jest-junit": "13.1.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.31.2", "ts-node": "10.7.0", "typedoc": "0.22.14", "typescript": "4.6.3"}, "jest": {"testEnvironment": "node", "coverageProvider": "v8"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "e20448498ea4571d19265407962c7d1cfaf51f05", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.4.1", "_nodeVersion": "17.9.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-ej5oVy6lykXsvieQtqZxCOaLT+xD4+QNarq78cIYISHmZXshCvROLudpQN3lfL8G0NL7plMSSK+zlyvCaIJ4Iw==", "shasum": "75b4c27948c81e88ccd3a8902047bcd797f38d32", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.4.1.tgz", "fileCount": 41, "unpackedSize": 243577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5zZGYRZE4vZre0BP0KS/IYsVPj4Vt07ZPV5yC0EgYZAiEA9MCKvh/GVyLl3BMvH9PueSvDZ4KUzwZo9p/Tj2Ko218="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUz1dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Xg/+L2bkWyhngUyf0ubBqWPs6g3oDGuQmsjknX3TJ4Yl/3h11Qvh\r\n022bSkJvsHLC2FHCC2M03amcTkUX7DWCeezvKVi7V8DEMEnjdzTmyIYYXDRo\r\nBQxmLjS/wNiiRdt+gfUmggqqOejQgDN+wUQYI9Kf6aCtRq+k1Mx0BmPCE0DT\r\nc0+Nck/qjd4qWKFamGFRIkWcBrKtDjKCQ8gsHnayokotYOHaj9CFM+MII9NG\r\nsOzRtcl4hRYfE1rTw9cGfRxSUQUl8XhEquVRkQVTJxXfyWwn4zfeC6QP9a7D\r\ne4xdrtMKnnL/zUs0W+H5V6kpj07R46OHHtgbMwtI+Mvvi7phmd9WJc1pkNQ/\r\ngfGv5mYbUX2b9vvysaeGHxuWidxaGo8FOG7HkxIohMs34L3IaVRiV14hEFIn\r\nZssa74D76Vi032Vl7Z8xxML9rHMfkSIyseNYPh0PdoWJ7SmQDzswSzUy+3fH\r\naTJFEUVoYe6BcduTooFtoR0obV55T1X9zY6assVCk6RVa4d8DXoK2ffx9wlh\r\n00Q+p0ZIkFumyioj84dXrax1nCYi/0g0BpIvx9m6+k37bH3GoGXkReDga690\r\nQ0tb5LRvx6TDjxzdXXgZzWvIGNpUfw37IUEVqw0NdZBljUX3JciO3mIFCBmb\r\nASzhL7Lb6phQKGsQh2qZMjvYcUlC3ci2R7o=\r\n=7SI/\r\n-----END PGP SIGNATURE-----\r\n", "size": 53014}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.4.1_1649622365267_0.7490588746462188"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-10T20:26:09.336Z"}, "3.5.0": {"name": "@ctrl/tinycolor", "version": "3.5.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.19.6", "@babel/preset-typescript": "7.18.6", "@ctrl/eslint-config": "3.5.6", "@jest/globals": "29.3.1", "@types/node": "18.11.11", "del-cli": "5.0.0", "jest": "29.3.1", "jest-junit": "15.0.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.34.1", "ts-node": "10.9.1", "typedoc": "0.23.21", "typescript": "4.9.3"}, "jest": {"testEnvironment": "node", "coverageProvider": "v8"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "f659dce8f9ca4597b3ecfe7862cb4da237d82a10", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.5.0", "_nodeVersion": "19.1.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-tlJpwF40DEQcfR/QF+wNMVyGMaO9FQp6Z1Wahj4Gk3CJQYHwA2xVG7iKDFdW6zuxZY9XWOpGcfNCTsX4McOsOg==", "shasum": "6e52b3d1c38d13130101771821e09cdd414a16bc", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.5.0.tgz", "fileCount": 41, "unpackedSize": 245253, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4CiFjGapuQNwy9AhVc8SkADFrHAc41da46IF9LpgEiAIhAKLXgtbF24hw1iF+bXRE5cOb4gy4oG2Q2/JL0YgMbvQe"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjl1lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfjQ/+LiUYyBBnjTsUgL2ruoJoKuVUEGnyQabDDVCnVhGoh/cA4DMP\r\nwzh2Eo1k7rQ1NfnadSkhCbZKniI/2f/j2c50gDZRRWSdssKMG2Cbf3dHG4fR\r\nLHgj5bzx2SeN3owQUeyfYlrg6kGAO9QJZNWoB8Y/WPlqHjCBrIJEWXNOP2o4\r\nQC+CFZIqGE/aPwx6t97L5oIkpxo0anksq0EcyIlGPO05omC/vbO+ZSysh78G\r\nKWtq2L+mSTiND/dJ5eHg1ESi/EOiT5WutHWGbMd2H5hL1MUORdVXyfGEKJB6\r\n3NdFMqRQ7Zaq9ZAUEkXHH95JbqCWo0hFOfJufPnBtZ8u7q2CALGP0VScmIOH\r\n3RXo21f2W3xCNPCALAHLP4klCkNjgRrlRrYx0lhqKfGMzb8hGvjqtKSXcZIl\r\nB94olVp9/PLNaySsYEgEthiZq1ARRSz+ccc/rb1/mv3iF05yCpiy32PfXnpw\r\nBe/rgivvhC3XaZ9stoB7im4KPxsbpuhXpEnSzu4Xo7ZIcqi5oikMMoUcVKjd\r\nSO3KfLLbnk4gO6cGuHxYVnapH4x1H9G3jmbQ6yGtlrL/6c2gFNb9fPpI6eDK\r\nOIJigZWczMj3byZNcOYyxnqOfqBTN1qhLDtS6EalOw73JBm9BXlcjsDUC+Jt\r\nzmPfrxpG0eA+7RWlUiSh8ot4YSDsYAzHkjY=\r\n=aGaE\r\n-----END PGP SIGNATURE-----\r\n", "size": 53390}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.5.0_1670274405514_0.5667345368929264"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-05T21:07:24.914Z"}, "3.5.1": {"name": "@ctrl/tinycolor", "version": "3.5.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.19.6", "@babel/preset-typescript": "7.18.6", "@ctrl/eslint-config": "3.5.6", "@jest/globals": "29.3.1", "@types/node": "18.11.11", "del-cli": "5.0.0", "jest": "29.3.1", "jest-junit": "15.0.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.34.1", "ts-node": "10.9.1", "typedoc": "0.23.21", "typescript": "4.9.3"}, "jest": {"testEnvironment": "node", "coverageProvider": "v8"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "365fdd2edff314a4888d06b032dd85ea8e8ba2a9", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.5.1", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-Bp8VF1lm91/vxFSBdVrrSe+P4KpjRCSAJ6qPSeLFnVprT/ERXHDvV2OJSJbRwl1r/KcySshTUnVbAzrSbn93fg==", "shasum": "e3b2a1473e802a945ee872dbb98bcf9a6a53755b", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.5.1.tgz", "fileCount": 41, "unpackedSize": 245849, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDJSvmALOCFMV0IuL0YasvX+AIzjApA7Tm2p+c4FWtT0AiBqMESm6xJoEBDLncHvFzJyMa7ZWzQOIE8aib/z/yvbbQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6nssACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6Hw//ft9ZcTup4xlksB1l+BEi0Koy4DQObkGrDGXqGFaJEINCiwv4\r\nz1ksoVOl1Xc5SvH9M3iEzBBNTZisjndxEjtdxBRXQQCi74aLPQgKXdt32KjC\r\nQl/RTKOZT8At+OgSrswDBvNSz5oq/WMymkhpxK+rW2SlKXhrpsDa7+hvrzAi\r\nB0fxIgJ04vOh9HH894wOxKa6lawlMqzLka1cy53gh0lFgh+v8FWzqUl69wEn\r\nUe8FlUba+P64vP4zrGEf/1ZslIkQU6V6DUuefD8EmGEL4Aa/uADHG8bBBK0H\r\nOkuCfXZ5lAHQb/y90JJWd/Fboll5bFTgGE2zGRXv/3AkeDgdYWF76ocBavQc\r\nCDuHGRHpaIKyziH5OURp8E5cBlKpKpYMfnxKvDrU+seaR4o2XGlseeuRuJ2x\r\ns9NQUUDTNzHXdh1Kdyu47WkwB/H7xmrsKbf0akhX0NPf3a5J+Oo4U43W5xFo\r\nq9R6l/HnNw8GKe0Mf/l5enVuaoli8Y9wrFrwVeuvV2s7w7t5s+kHQuSJwbZA\r\n5soei8e9CmqOuFXPoBq7PzzK0ti2mqGBIFgLdiPy/lhYcXLELtebIY7zoBVR\r\n6DmSF0oe647JnX1s44S9rZom7RDL0eB9ll9XPt2IXLM0UhI+uatR2VDwdHDM\r\nVz+lna3e2sd9IvX53LNqGyxb4nbF/RkPBIc=\r\n=QgUF\r\n-----END PGP SIGNATURE-----\r\n", "size": 53496}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.5.1_1676311339856_0.2583021412295041"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-13T18:02:20.017Z", "publish_time": 1676311340017}, "3.6.0": {"name": "@ctrl/tinycolor", "version": "3.6.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.19.6", "@babel/preset-typescript": "7.18.6", "@ctrl/eslint-config": "3.5.6", "@jest/globals": "29.3.1", "@types/node": "18.11.11", "del-cli": "5.0.0", "jest": "29.3.1", "jest-junit": "15.0.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.34.1", "ts-node": "10.9.1", "typedoc": "0.23.21", "typescript": "4.9.3"}, "jest": {"testEnvironment": "node", "coverageProvider": "v8"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "gitHead": "9dccfb6abf63fedcf27448f5d62b53100e6091f0", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_id": "@ctrl/tinycolor@3.6.0", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-/Z3l6pXthq0JvMYdUFyX9j0MaCltlIn6mfh9jLyQwg5aPKxkyNa0PTHtU1AlFXLNk55ZuAeJRcpvq+tmLfKmaQ==", "shasum": "53fa5fe9c34faee89469e48f91d51a3766108bc8", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.0.tgz", "fileCount": 41, "unpackedSize": 247974, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1tmCO+aD7JoRijg3Wz/O3nv2WPDS3I6vC7RVfrSNAQAiEA/R576+t92LZ15zO7ltXys6PMnwlALWdMtdun2gbIEtU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj68dmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9YQ//cfY1RWny28k091Q8A9hm7XuIAlnPs1XwLWzA9v+klHc6X4LB\r\n5fT06IZJj7JD8gdHZeAEkTV2vD/Rxfgurecsm07D3BAnIKR0jG+EV3I8qDKP\r\nuu+ejVXAbzLFsmbATWoOSWfPW8vs9CdheYiGM0lNsyWwGZvQRow4eP2jDWrN\r\nHCSCTgc+5T5kMkB5hukMr/zXI+AZFzQGOayF854jPCRux/YhNr05ifd35gSp\r\nq8Eax2SyDJPuXzumvN+cLFWXH5XUJhfnTstQx+Pja5ZNF9YKhwRdRlNa7V6i\r\nOEHbIkAoW7F86F5xMp84k8Tiez04lYLODomghndggH59XstGNN7iHsLuNGYn\r\ny6qo6TRxg1ChNn9Ync/BkIc5xVZ/AIqYFzY7LspsWtm4x4BSVr/uDsjx+UdO\r\nLb1iF6rhWnWTimhkm7tr0ZIcQplTwIDy/vfKj7RWTD38buV97aQcpcAGAtTG\r\n/WFQTL+9hDkbNSsucaPnhnLjQ2xq2hUFoBMIHNCEUfLCoNDtclsGK8DQECQF\r\nb8TXRJFozllxjzyYPXMw55PvI5bljqrj605Wz77/L5o08WQEoeDZUCJPBkz1\r\nQvGQpUCpcm3unfieX8uxgVQ4hpJxIe9AG9DZeqUQipxdka+mvzA3+IzlPENc\r\nk8HI36nwpwsKsE/bKEqy72zdgykikwELA3U=\r\n=v/vZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 53784}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.6.0_1676396390444_0.43262589830531684"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-14T17:39:50.610Z", "publish_time": 1676396390610}, "3.6.1": {"name": "@ctrl/tinycolor", "version": "3.6.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.19.6", "@babel/preset-typescript": "7.18.6", "@ctrl/eslint-config": "3.5.6", "@jest/globals": "29.3.1", "@types/node": "18.11.11", "del-cli": "5.0.0", "jest": "29.3.1", "jest-junit": "15.0.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.34.1", "ts-node": "10.9.1", "typedoc": "0.23.21", "typescript": "4.9.3"}, "jest": {"testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"(.+)\\.js": "$1"}}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}, "_id": "@ctrl/tinycolor@3.6.1", "gitHead": "ee70e0014e069cad3f4e177ee0cb819fdf4794ea", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==", "shasum": "b6c75a56a1947cc916ea058772d666a2c8932f31", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "fileCount": 41, "unpackedSize": 248687, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxkmcupAhr/csEf8YBvMRre5tU0hh29nPjnnX6hIrVcgIgAdWQpj6cSJIVJV3mP4WhZueoccL2bIjuUezhdGgCMYw="}], "size": 53845}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_3.6.1_1692825993835_0.5098933375068373"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T21:26:34.052Z", "publish_time": 1692825994052, "_source_registry_name": "default"}, "4.0.0": {"name": "@ctrl/tinycolor", "version": "4.0.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo:build": "npm run build --workspace=demo", "demo:watch": "npm run dev --workspace=demo", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "docs:build": "typedoc --out demo/dist/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "vitest run", "test:watch": "vitest", "test:ci": "vitest run --coverage --reporter=default --reporter=junit --outputFile=./junit.xml"}, "devDependencies": {"@ctrl/eslint-config": "4.0.3", "@rollup/plugin-terser": "0.4.3", "@types/node": "20.5.4", "@vitest/coverage-v8": "0.34.2", "del-cli": "5.0.0", "rollup": "3.28.1", "ts-node": "10.9.1", "typedoc": "0.24.8", "typescript": "5.1.6", "vitest": "0.34.2"}, "workspaces": ["demo"], "release": {"branch": "master"}, "engines": {"node": ">=14"}, "_id": "@ctrl/tinycolor@4.0.0", "gitHead": "1943691aa3c7b02efa285ab57638cbc63406ba05", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-c0CEvs6FsBU1QI/nVBY5QQvTnkkTjAfL6t1YwNtI6sGGf2CI+C7znHLxS5EqBCnIDPuLFkgJNMTba7n2eyTz8w==", "shasum": "f8185ed1639d3904ec473ece0e91e3d677b98c96", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-4.0.0.tgz", "fileCount": 41, "unpackedSize": 232583, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbNYPTX4gRrxl0riOtISBlGR77QwXc9UVdwSCAnjsDmgIgZDp9phnVwysB+7RzOliW1ngO8GJjJzmQ1TF+Z9x1CUI="}], "size": 51678}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_4.0.0_1692829003734_0.5631100437278618"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T22:16:43.990Z", "publish_time": 1692829003990, "_source_registry_name": "default"}, "4.0.1": {"name": "@ctrl/tinycolor", "version": "4.0.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo:build": "npm run build --workspace=demo", "demo:watch": "npm run dev --workspace=demo", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "docs:build": "typedoc --out demo/dist/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "vitest run", "test:watch": "vitest", "test:ci": "vitest run --coverage --reporter=default --reporter=junit --outputFile=./junit.xml"}, "devDependencies": {"@ctrl/eslint-config": "4.0.3", "@rollup/plugin-terser": "0.4.3", "@types/node": "20.5.4", "@vitest/coverage-v8": "0.34.2", "del-cli": "5.0.0", "rollup": "3.28.1", "ts-node": "10.9.1", "typedoc": "0.24.8", "typescript": "5.1.6", "vitest": "0.34.2"}, "workspaces": ["demo"], "release": {"branch": "master"}, "engines": {"node": ">=14"}, "_id": "@ctrl/tinycolor@4.0.1", "gitHead": "cd64cbd8c1e50fd356018187641cb0bc777c19f7", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-dfimuE1mfaqL8P8jyQzdk9yFeFUWCyhjK5VyydXgDtQO0fezr6aWaGauHnlI07BZBIF45gahb0oxJjkUcylDwQ==", "shasum": "982651217246bf241850dd979a04ff41a06b0847", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-4.0.1.tgz", "fileCount": 41, "unpackedSize": 232598, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIClA1QMmSNNP8Nh5c5iL+LlM9XKozXLjov/K7z8a9pa8AiEA2joF2xG2mbNgskQ4GHfdFL4o/26sn2t+YcbOCk82jhc="}], "size": 51688}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_4.0.1_1692909959280_0.5517477384029343"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-24T20:45:59.468Z", "publish_time": 1692909959468, "_source_registry_name": "default"}, "4.0.2": {"name": "@ctrl/tinycolor", "version": "4.0.2", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo:build": "npm run build --workspace=demo", "demo:watch": "npm run dev --workspace=demo", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "docs:build": "typedoc --out demo/dist/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "vitest run", "test:watch": "vitest", "test:ci": "vitest run --coverage --reporter=default --reporter=junit --outputFile=./junit.xml"}, "devDependencies": {"@ctrl/eslint-config": "4.0.3", "@rollup/plugin-terser": "0.4.3", "@types/node": "20.5.4", "@vitest/coverage-v8": "0.34.2", "del-cli": "5.0.0", "rollup": "3.28.1", "ts-node": "10.9.1", "typedoc": "0.24.8", "typescript": "5.1.6", "vitest": "0.34.2"}, "release": {"branch": "master"}, "engines": {"node": ">=14"}, "_id": "@ctrl/tinycolor@4.0.2", "gitHead": "31a98e09ac0f9499ccf6971acd2a006703527d27", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-fKQinXE9pJ83J1n+C3rDl2xNLJwfoYNvXLRy5cYZA9hBJJw2q+sbb/AOSNKmLxnTWyNTmy4994dueSwP4opi5g==", "shasum": "7665b09c0163722ffc20ee885eb9a8ff80d9ebde", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-4.0.2.tgz", "fileCount": 41, "unpackedSize": 233059, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCi3roJVlA9F0rrK6+Xpfh45XU4quPXfhNTenygcbV6OwIhALQ1+TFlY+o7Zeyf3sy9tawNYZuOBT6XdTzG9xWRNFeA"}], "size": 51692}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_4.0.2_1693411972947_0.6831962691730462"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-30T16:12:53.229Z", "publish_time": 1693411973229, "_source_registry_name": "default"}, "4.0.3": {"name": "@ctrl/tinycolor", "version": "4.0.3", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo:build": "npm run build --workspace=demo", "demo:watch": "npm run dev --workspace=demo", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "docs:build": "typedoc --out demo/dist/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "vitest run", "test:watch": "vitest", "test:ci": "vitest run --coverage --reporter=default --reporter=junit --outputFile=./junit.xml"}, "devDependencies": {"@ctrl/eslint-config": "4.0.14", "@rollup/plugin-terser": "0.4.4", "@types/node": "20.11.5", "@vitest/coverage-v8": "1.2.1", "del-cli": "5.1.0", "rollup": "4.9.5", "ts-node": "10.9.2", "typedoc": "0.25.7", "typescript": "5.3.3", "vitest": "1.2.1"}, "release": {"branches": ["master"]}, "engines": {"node": ">=14"}, "_id": "@ctrl/tinycolor@4.0.3", "gitHead": "64270d80c1287355c6c9f719baa6777d5eac3ee5", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-e9nEVehVJwkymQpkGhdSNzLT2Lr9UTTby+JePq4Z2SxBbOQjY7pLgSouAaXvfaGQVSAaY0U4eJdwfSDmCbItcw==", "shasum": "c56d96ef0d7be598cf68d1ab53f990849a79f5b4", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-4.0.3.tgz", "fileCount": 41, "unpackedSize": 233529, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9heH/7kWvLtRIQqWJTNRfaKG5pgwVmNYfJU/yMh1PxQIhAKuQ2ixcMoorF1Oyr1jIa4RqkMtKiEzIDWGmFaug7AIL"}], "size": 52026}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_4.0.3_1705515041997_0.787903950393215"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-17T18:10:42.168Z", "publish_time": 1705515042168, "_source_registry_name": "default"}, "4.0.4": {"name": "@ctrl/tinycolor", "version": "4.0.4", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo:build": "npm run build --workspace=demo", "demo:watch": "npm run dev --workspace=demo", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "docs:build": "typedoc --out demo/dist/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "vitest run", "test:watch": "vitest", "test:ci": "vitest run --coverage --reporter=default --reporter=junit --outputFile=./junit.xml"}, "devDependencies": {"@ctrl/eslint-config": "4.0.14", "@rollup/plugin-terser": "0.4.4", "@types/node": "20.11.5", "@vitest/coverage-v8": "1.2.1", "del-cli": "5.1.0", "rollup": "4.9.5", "ts-node": "10.9.2", "typedoc": "0.25.7", "typescript": "5.3.3", "vitest": "1.2.1"}, "release": {"branches": ["master"]}, "engines": {"node": ">=14"}, "_id": "@ctrl/tinycolor@4.0.4", "gitHead": "8dd8b3a6906bd24debb115e2b3a03c54fafccf39", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.12.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-DIjWxEgyP+YGh7Znx9PjsunmpVsAySYRofMUjZCRWUZ1JVS8GHurry6OebnQD2dPviRUTH9VmiupFivS7Ik9ew==", "shasum": "25dd4161b6f64678b5639cf070f15dcb8caf72f9", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-4.0.4.tgz", "fileCount": 41, "unpackedSize": 233765, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh7Vbf7hL5Dabeliu89rAYRyN/r1CorLyqiLPxau6L6gIgRcaeqBvs3fwvO1Az9GDD+NWEleCF68Q9UV9i71i4/i0="}], "size": 52068}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_4.0.4_1712598144280_0.9076680780990929"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-08T17:42:24.450Z", "publish_time": 1712598144450, "_source_registry_name": "default"}, "4.1.0": {"name": "@ctrl/tinycolor", "version": "4.1.0", "description": "Fast, small color manipulation and conversion for JavaScript", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "sideEffects": false, "scripts": {"demo:build": "npm run build --workspace=demo", "demo:watch": "npm run dev --workspace=demo", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "docs:build": "typedoc --out demo/dist/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "vitest run", "test:watch": "vitest", "test:ci": "vitest run --coverage --reporter=default --reporter=junit --outputFile=./junit.xml"}, "devDependencies": {"@ctrl/eslint-config": "4.0.14", "@rollup/plugin-terser": "0.4.4", "@types/node": "20.11.5", "@vitest/coverage-v8": "1.2.1", "del-cli": "5.1.0", "rollup": "4.9.5", "ts-node": "10.9.2", "typedoc": "0.25.7", "typescript": "5.3.3", "vitest": "1.2.1"}, "release": {"branches": ["master"]}, "engines": {"node": ">=14"}, "_id": "@ctrl/tinycolor@4.1.0", "gitHead": "2927a9d2aa03e037486a79a295542a7848621691", "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "_nodeVersion": "20.12.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-WyOx8cJQ+FQus4Mm4uPIZA64gbk3Wxh0so5Lcii0aJifqwoVOlfFtorjLE0Hen4OYyHZMXDWqMmaQemBhgxFRQ==", "shasum": "91a8f8120ffc9da2feb2a38f7862b300d5e9691a", "tarball": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-4.1.0.tgz", "fileCount": 41, "unpackedSize": 244065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXa04u9S2tyyxvx3pKQv5jOa2Lq9MtFqycMeoq6QyeNAiEA+SMjp9uAB5eLlyVxJCBQqZ0TEHS17ZcZl4KmttPlHvY="}], "size": 54251}, "_npmUser": {"name": "scttcper", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "scttcper", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tinycolor_4.1.0_1713035085606_0.23610525543602412"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-13T19:04:45.788Z", "publish_time": 1713035085788, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "homepage": "https://tinycolor.vercel.app", "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "_source_registry_name": "default"}