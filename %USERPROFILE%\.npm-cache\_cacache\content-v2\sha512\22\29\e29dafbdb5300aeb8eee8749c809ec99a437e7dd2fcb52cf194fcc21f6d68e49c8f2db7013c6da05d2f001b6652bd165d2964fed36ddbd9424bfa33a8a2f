{"_id": "@rollup/rollup-linux-s390x-gnu", "_rev": "3951654-660424c99965e7b1c102a029", "dist-tags": {"beta": "4.33.0-0", "latest": "4.45.0"}, "name": "@rollup/rollup-linux-s390x-gnu", "time": {"created": "2024-03-27T13:53:13.951Z", "modified": "2025-07-12T05:54:55.320Z", "4.13.1": "2024-03-27T10:28:00.099Z", "4.13.2": "2024-03-28T14:13:57.245Z", "4.14.0": "2024-04-03T05:23:17.078Z", "4.14.1": "2024-04-07T07:35:56.941Z", "4.14.2": "2024-04-12T06:23:57.849Z", "4.14.3": "2024-04-15T07:18:50.922Z", "4.15.0": "2024-04-20T05:37:35.823Z", "4.16.0": "2024-04-21T04:42:45.178Z", "4.16.1": "2024-04-21T18:30:24.220Z", "4.16.2": "2024-04-22T15:19:42.503Z", "4.16.3": "2024-04-23T05:12:53.333Z", "4.16.4": "2024-04-23T13:15:26.333Z", "4.17.0": "2024-04-27T11:30:11.736Z", "4.17.1": "2024-04-29T04:58:13.346Z", "4.17.2": "2024-04-30T05:01:10.259Z", "4.18.0": "2024-05-22T05:04:05.792Z", "4.18.1": "2024-07-08T15:25:33.914Z", "4.19.0": "2024-07-20T05:46:35.641Z", "4.19.1": "2024-07-27T04:54:20.982Z", "4.19.2": "2024-08-01T08:33:14.636Z", "4.20.0": "2024-08-03T04:49:12.110Z", "4.21.0": "2024-08-18T05:55:53.379Z", "4.21.1": "2024-08-26T15:54:35.260Z", "4.21.2": "2024-08-30T07:04:46.665Z", "4.21.3": "2024-09-12T07:06:11.738Z", "4.22.0": "2024-09-19T04:55:52.362Z", "4.22.1": "2024-09-20T08:22:13.802Z", "4.22.2": "2024-09-20T09:34:06.181Z", "4.22.3-0": "2024-09-20T14:48:20.500Z", "4.22.3": "2024-09-21T05:03:31.578Z", "4.22.4": "2024-09-21T06:11:41.817Z", "4.22.5": "2024-09-27T11:48:39.515Z", "4.23.0": "2024-10-01T07:10:39.209Z", "4.24.0": "2024-10-02T09:37:43.215Z", "4.24.1": "2024-10-27T06:43:27.443Z", "4.24.2": "2024-10-27T15:40:34.749Z", "4.25.0-0": "2024-10-29T06:15:37.263Z", "4.24.3": "2024-10-29T14:14:35.566Z", "4.24.4": "2024-11-04T08:47:34.636Z", "4.25.0": "2024-11-09T08:37:48.632Z", "4.26.0": "2024-11-13T06:45:25.065Z", "4.27.0-0": "2024-11-13T07:03:39.022Z", "4.27.0-1": "2024-11-14T06:33:36.495Z", "4.27.0": "2024-11-15T10:41:01.127Z", "4.27.1-0": "2024-11-15T13:28:35.373Z", "4.27.1-1": "2024-11-15T15:38:29.852Z", "4.27.1": "2024-11-15T16:08:08.266Z", "4.27.2": "2024-11-15T17:20:31.224Z", "4.27.3": "2024-11-18T16:40:02.971Z", "4.27.4": "2024-11-23T07:00:45.443Z", "4.28.0": "2024-11-30T13:16:13.476Z", "4.28.1": "2024-12-06T11:45:24.708Z", "4.29.0-0": "2024-12-16T06:40:22.936Z", "4.29.0-1": "2024-12-19T06:37:57.740Z", "4.29.0-2": "2024-12-20T06:56:31.986Z", "4.29.0": "2024-12-20T18:37:53.338Z", "4.29.1": "2024-12-21T07:16:31.881Z", "4.30.0-0": "2024-12-21T07:17:43.099Z", "4.30.0-1": "2024-12-30T06:52:46.647Z", "4.29.2": "2025-01-05T12:08:13.170Z", "4.30.0": "2025-01-06T06:37:07.057Z", "4.30.1": "2025-01-07T10:36:20.809Z", "4.31.0-0": "2025-01-14T05:58:10.783Z", "4.31.0": "2025-01-19T12:57:15.700Z", "4.32.0": "2025-01-24T08:28:03.502Z", "4.33.0-0": "2025-01-28T08:30:36.825Z", "4.32.1": "2025-01-28T08:33:43.965Z", "4.33.0": "2025-02-01T07:12:29.510Z", "4.34.0": "2025-02-01T08:40:50.335Z", "4.34.1": "2025-02-03T06:58:41.117Z", "4.34.2": "2025-02-04T08:10:32.059Z", "4.34.3": "2025-02-05T09:22:34.104Z", "4.34.4": "2025-02-05T21:31:40.451Z", "4.34.5": "2025-02-07T08:53:32.762Z", "4.34.6": "2025-02-07T16:32:36.647Z", "4.34.7": "2025-02-14T09:54:27.088Z", "4.34.8": "2025-02-17T06:26:53.146Z", "4.34.9": "2025-03-01T07:33:06.367Z", "4.35.0": "2025-03-08T06:25:17.972Z", "4.36.0": "2025-03-17T08:36:14.740Z", "4.37.0": "2025-03-23T14:57:35.544Z", "4.38.0": "2025-03-29T06:29:40.513Z", "4.39.0": "2025-04-02T04:50:05.673Z", "4.40.0": "2025-04-12T08:40:07.688Z", "4.40.1": "2025-04-28T04:35:55.930Z", "4.40.2": "2025-05-06T07:27:26.997Z", "4.41.0": "2025-05-18T05:34:00.415Z", "4.41.1": "2025-05-24T06:15:10.659Z", "4.41.2": "2025-06-06T11:41:05.805Z", "4.42.0": "2025-06-06T14:48:45.815Z", "4.43.0": "2025-06-11T05:23:15.061Z", "4.44.0": "2025-06-19T06:23:32.202Z", "4.44.1": "2025-06-26T04:34:50.213Z", "4.44.2": "2025-07-04T12:56:41.304Z", "4.45.0": "2025-07-12T05:54:37.766Z"}, "versions": {"4.13.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.13.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.13.1", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-U564BrhEfaNChdATQaEODtquCC7Ez+8Hxz1h5MAdMYj0AqD0GA9rHCpElajb/sQcaFL6NXmHc5O+7FXpWMa73Q==", "shasum": "c10a1f1522f0c9191ee45f677bd08763ddfdc039", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.13.1.tgz", "fileCount": 3, "unpackedSize": 4263433, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0UDgAL7J3wClvdteSJkkiEp9q75ivmeW5iM0iZMxeQQIgDJG0K/K58n2udh6cZukMK+ep3kUJ5z4IKSkKlce1DrE="}], "size": 1645438}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.13.1_1711535279926_0.05888404888185983"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-27T10:28:00.099Z", "publish_time": 1711535280099, "_source_registry_name": "default"}, "4.13.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.13.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.13.2", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-l4U0KDFwzD36j7HdfJ5/TveEQ1fUTjFFQP5qIt9gBqBgu1G8/kCaq5Ok05kd5TG9F8Lltf3MoYsUMw3rNlJ0Yg==", "shasum": "8e14a1b3c3b9a4440c70a9c1ba12d32aa21f9712", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.13.2.tgz", "fileCount": 3, "unpackedSize": 4263433, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyObdPDf0zkSKc/jC0CFu3UO0rku3RVd9J5Cey7wdhNAiBepgtrZVWAW/ZVw1jfkCwpia7AZFw3PTejzfxvEy0EKw=="}], "size": 1645437}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.13.2_1711635237001_0.9694457880301859"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-28T14:13:57.245Z", "publish_time": 1711635237245, "_source_registry_name": "default"}, "4.14.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.14.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.14.0", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-RxB/qez8zIDshNJDufYlTT0ZTVut5eCpAZ3bdXDU9yTxBzui3KhbGjROK2OYTTor7alM7XBhssgoO3CZ0XD3qA==", "shasum": "95f0c133b324da3e7e5c7d12855e0eb71d21a946", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.14.0.tgz", "fileCount": 3, "unpackedSize": 4271625, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCz7zmCmRzAeigww2anjFjKdNsXj4fWpRvL0+cI5AHQcgIhAPBeRE3Pnja0EwLXa30SMW1RaQf1Sk5DmwifMF12siE6"}], "size": 1651421}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.14.0_1712121796794_0.48320037517217207"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-03T05:23:17.078Z", "publish_time": 1712121797078, "_source_registry_name": "default"}, "4.14.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.14.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.14.1", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-VMgaGQ5zRX6ZqV/fas65/sUGc9cPmsntq2FiGmayW9KMNfWVG/j0BAqImvU4KTeOOgYSf1F+k6at1UfNONuNjA==", "shasum": "f7b4e2b0ca49be4e34f9ef0b548c926d94edee87", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.14.1.tgz", "fileCount": 3, "unpackedSize": 4206089, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDubgMeJrQtW146AV8vPXcDM2ziD0PIEwALtVl/uGW03gIgOhuouCLW/pJiOtcimQ+XM3+RbPAkoc9j921W/gbLLYs="}], "size": 1644098}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.14.1_1712475356729_0.147867311347462"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-07T07:35:56.941Z", "publish_time": 1712475356941, "_source_registry_name": "default"}, "4.14.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.14.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.14.2", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-xPV4y73IBEXToNPa3h5lbgXOi/v0NcvKxU0xejiFw6DtIYQqOTMhZ2DN18/HrrP0PmiL3rGtRG9gz1QE8vFKXQ==", "shasum": "65ad6f82729ef9d8634847189214e3205892f42f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.14.2.tgz", "fileCount": 3, "unpackedSize": 4185609, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH4OMTxExBt7Eafnaz2NhT0m4tgCXT4Vo6/finnUbm6XAiBnuluNhpQwLPr27qWX23arYd7KTp0FrUrhwhZtQIcC3Q=="}], "size": 1620209}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.14.2_1712903037629_0.7253009713203045"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T06:23:57.849Z", "publish_time": 1712903037849, "_source_registry_name": "default"}, "4.14.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.14.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.14.3", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-jOO/PEaDitOmY9TgkxF/TQIjXySQe5KVYB57H/8LRP/ux0ZoO8cSHCX17asMSv3ruwslXW/TLBcxyaUzGRHcqg==", "shasum": "09c9e5ec57a0f6ec3551272c860bb9a04b96d70f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.14.3.tgz", "fileCount": 3, "unpackedSize": 4218377, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGEySHMAxp4VyKD/1EnqA31BS0+FLzd/lCfE9rQlWHXQIhAN3+2hs4RzwBNZtEAlbBSoRrcim6FKlRFEzC8MNVPRGL"}], "size": 1634052}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.14.3_1713165530658_0.6418789304413364"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-15T07:18:50.922Z", "publish_time": 1713165530922, "_source_registry_name": "default"}, "4.15.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.15.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.15.0", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-hYPbhg9ow6/mXIkojc8LOeiip2sCTuw1taWyoOXTOWk9vawIXz8x7B4KkgWUAtvAElssxhSyEXr2EZycH/FGzQ==", "shasum": "cb43301e10f17f0a642416c5d3d82a26cf430fa8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.15.0.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGVUcEbAzUa9IHS8MiCay/X8neK/cTHipqDIQ75onA/WAiBE1JeS5mHx3zs+IXQZkFUHsG5tlADQliuo/8vwOTZIxA=="}], "size": 1674238}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.15.0_1713591455663_0.07814845227547096"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-20T05:37:35.823Z", "publish_time": 1713591455823, "_source_registry_name": "default"}, "4.16.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.16.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.16.0", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-j/9yBgWFlNFBfG/S1M2zkBNLeLkNVG59T5c4tlmlrxU+XITWJ3aMVWdpcZ/+mu7auGZftAXueAgAE9mb4lAlag==", "shasum": "693bac438527beefe3b061da847e3bfd6f6b571d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.16.0.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEGSuaDbkjilsMn+CQ/KQ5to8vIcKKllETwWVwH8w8gfAiAbMsNXLEMXGMZmisn0snejFzdJCIlSv84hU4Mp0Vy57Q=="}], "size": 1674238}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.16.0_1713674564973_0.3424622183691477"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-21T04:42:45.178Z", "publish_time": 1713674565178, "_source_registry_name": "default"}, "4.16.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.16.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.16.1", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-lsjLtDgtcGFEuBP6yrXwkRN5/wKlvUZtfbKZZu0yaoNpiBL4epgnO21osAALIspVRnl4qZgyLFd8xjCYYWgwfw==", "shasum": "246ac211ed0d78f7a9bc5c1d0653bde4c6cd9f63", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.16.1.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID0b14gvoBiYAyB3hx05hF2OVrdtTtL0CHN8yzuz4RdmAiAOIQQ2nMlELbiVKuYS2qXJ/a9GZWdGf3glCpQL5Z+Maw=="}], "size": 1674238}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.16.1_1713724223988_0.38310972591876724"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-21T18:30:24.220Z", "publish_time": 1713724224220, "_source_registry_name": "default"}, "4.16.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.16.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.16.2", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-jm2lvLc+/gqXfndlpDw05jKvsl/HKYxUEAt1h5UXcMFVpO4vGpoWmJVUfKDtTqSaHcCNw1his1XjkgR9aort3w==", "shasum": "39b87bd355dfafbc062ca856d3d6bc5aa1905d89", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.16.2.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXBUJZRK81LufdaIOzPTnFIg6RXX49jVlxrKX2PboLZAiAgBwqOAkNRl54NMs73iH5815GCX1Ol8ZXmBUj+LIEV6g=="}], "size": 1674239}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.16.2_1713799182303_0.7285356145777095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T15:19:42.503Z", "publish_time": 1713799182503, "_source_registry_name": "default"}, "4.16.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.16.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.16.3", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-vu4f3Y8iwjtRfSZdmtP8nC1jmRx1IrRVo2cLQlQfpFZ0e2AE9YbPgfIzpuK+i3C4zFETaLLNGezbBns2NuS/uA==", "shasum": "e237882ec01c1436598436184c9b72061c0f7dc3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.16.3.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTgN51r20bh8OUXoZxDoAiKqGtbKDGG/OUBrVmVxSDGwIgfgRlPCljUWv8bXqZkfGF+VbgY1AiCiz8yKbSd+X4z0U="}], "size": 1674238}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.16.3_1713849173108_0.48639519513914165"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T05:12:53.333Z", "publish_time": 1713849173333, "_source_registry_name": "default"}, "4.16.4": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.16.4", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.16.4", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-1xwwn9ZCQYuqGmulGsTZoKrrn0z2fAur2ujE60QgyDpHmBbXbxLaQiEvzJWDrscRq43c8DnuHx3QorhMTZgisQ==", "shasum": "d46097246a187d99fc9451fe8393b7155b47c5ec", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.16.4.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGM9WAgLsxPzv5jD7PWbx9qJ1dFXfthvz0FMuaSYh+OWAiEAo0gyDvem4TFamJ58J95dMGlJTJ7z6+/g+/jxUV9BH4E="}], "size": 1674239}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.16.4_1713878126175_0.8597571280848506"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T13:15:26.333Z", "publish_time": 1713878126333, "_source_registry_name": "default"}, "4.17.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.17.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.17.0", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-FtOgui6qMJ4jbSXTxElsy/60LEe/3U0rXkkz2G5CJ9rbHPAvjMvI+3qF0A0fwLQ5hW+/ZC6PbnS2KfRW9JkgDQ==", "shasum": "89cd27358ceb2351ae92b263df2ed0523f90b926", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.17.0.tgz", "fileCount": 3, "unpackedSize": 4296201, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGwPHo9ZVLj4Ozm/xxe1LY2ooomeiTQBKbp/BGC6kKm0AiAdrlaH7umQ997mjBlYpNixUhpNfnKPrwqDuAgQfUMCbQ=="}], "size": 1666442}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.17.0_1714217411511_0.5405636268533254"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-27T11:30:11.736Z", "publish_time": 1714217411736, "_source_registry_name": "default"}, "4.17.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.17.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.17.1", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-xeae5pMAxHFp6yX5vajInG2toST5lsCTrckSRUFwNgzYqnUjNBcQyqk1bXUxX5yhjWFl2Mnz3F8vQjl+2FRIcw==", "shasum": "004f361e29c5b6cd6fb35192583ec2adf541c366", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.17.1.tgz", "fileCount": 3, "unpackedSize": 4296201, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1a4/l4AzBpnYGdDSLuVtvPObyjaflO2ny+EGvpYMaCgIgTn8/fee4qfYeeK7hOHxhQHBJTLtF1P91E7NGxu/pa9Q="}], "size": 1666442}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.17.1_1714366693139_0.2900529918928787"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-29T04:58:13.346Z", "publish_time": 1714366693346, "_source_registry_name": "default"}, "4.17.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.17.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.17.2", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-W0UP/x7bnn3xN2eYMql2T/+wpASLE5SjObXILTMPUBDB/Fg/FxC+gX4nvCfPBCbNhz51C+HcqQp2qQ4u25ok6g==", "shasum": "f15775841c3232fca9b78cd25a7a0512c694b354", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.17.2.tgz", "fileCount": 3, "unpackedSize": 4296201, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChzbz1vBZZsbf7PQ0W0YKRv13EbqccqWVSthRKgNtPBgIhAP+legC+OBqYbCbbljcjYaLGmytGLE76E18BlGDI7wFf"}], "size": 1666442}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.17.2_1714453270016_0.4326132141668846"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-30T05:01:10.259Z", "publish_time": 1714453270259, "_source_registry_name": "default"}, "4.18.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.18.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.18.0", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-0UyyRHyDN42QL+NbqevXIIUnKA47A+45WyasO+y2bGJ1mhQrfrtXUpTxCOrfxCR4esV3/RLYyucGVPiUsO8xjg==", "shasum": "938138d3c8e0c96f022252a28441dcfb17afd7ec", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.18.0.tgz", "fileCount": 3, "unpackedSize": 4333065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtsyJ0WdxCXiSLDTC4+hxl5OymWgeel4sf74Sx+hYdFAIhAKyOCpQdRnJizDcgnk+XTUVJVj+nkkC9XVOhCkg/uwBa"}], "size": 1668137}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.18.0_1716354245484_0.11391120896821527"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-22T05:04:05.792Z", "publish_time": 1716354245792, "_source_registry_name": "default"}, "4.18.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.18.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.18.1", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.3", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-qb1hMMT3Fr/Qz1OKovCuUM11MUNLUuHeBC2DPPAWUYYUAOFWaxInaTwTQmc7Fl5La7DShTEpmYwgdt2hG+4TEg==", "shasum": "f1043d9f4026bf6995863cb3f8dd4732606e4baa", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.18.1.tgz", "fileCount": 3, "unpackedSize": 4009513, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqxl8EQTKT6EtZNg2fYIaMpbXpEq71rW19R4GaCEFTigIgAkLnqvhmMuLpo22b47aRIndYRPmBIOGprFGvAT3H550="}], "size": 1596935}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.18.1_1720452333553_0.47130946111269423"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-08T15:25:33.914Z", "publish_time": 1720452333914, "_source_registry_name": "default"}, "4.19.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.19.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.19.0", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-D6pkaF7OpE7lzlTOFCB2m3Ngzu2ykw40Nka9WmKGUOTS3xcIieHe82slQlNq69sVB04ch73thKYIWz/Ian8DUA==", "shasum": "579ca5f271421a961d3c73d221202c79e02ff03a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.19.0.tgz", "fileCount": 3, "unpackedSize": 4050473, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCY3Vw6TY/DlXB5qeNj/bouMAIblbmdhGiPb+ucTfxP0AIhAO7k3iBBmSG8x1xqasvIMV4722gsPMQEq73kdkdPxHOp"}], "size": 1607876}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.19.0_1721454395413_0.051284303884488125"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-20T05:46:35.641Z", "publish_time": 1721454395641, "_source_registry_name": "default"}, "4.19.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.19.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.19.1", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-0cYP5rGkQWRZKy9/HtsWVStLXzCF3cCBTRI+qRL8Z+wkYlqN7zrSYm6FuY5Kd5ysS5aH0q5lVgb/WbG4jqXN1Q==", "shasum": "173722cd745779d730d4b24d21386185e0e12de8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.19.1.tgz", "fileCount": 3, "unpackedSize": 3972649, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDArlH3OqemJ96YLiM6l0+g8hNsgItk8Ysbt6GenoC5CAIhAMEmRchLoFqqSv7p7zV72VnVmVrLCTtOpLNGVco41Qxa"}], "size": 1587782}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.19.1_1722056060745_0.7786843163868076"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-27T04:54:20.982Z", "publish_time": 1722056060982, "_source_registry_name": "default"}, "4.19.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.19.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.19.2", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-IJSUX1xb8k/zN9j2I7B5Re6B0NNJDJ1+soezjNojhT8DEVeDNptq2jgycCOpRhyGj0+xBn7Cq+PK7Q+nd2hxLA==", "shasum": "ef62e9bc5cc3b84fcfe96ec0a42d1989691217b3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.19.2.tgz", "fileCount": 3, "unpackedSize": 3972649, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqcnGXIjTgAGDvniNtWHwMQesI+p8fTLw3y3bKEZty9gIhAO/ecHRDhA2Ia29yfpQZfd0PngXfJMEtdki4j8kkeuum"}], "size": 1587782}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.19.2_1722501194417_0.6911971390708425"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-01T08:33:14.636Z", "publish_time": 1722501194636, "_source_registry_name": "default"}, "4.20.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.20.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.20.0", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-I0BtGXddHSHjV1mqTNkgUZLnS3WtsqebAXv11D5BZE/gfw5KoyXSAXVqyJximQXNvNzUo4GKlCK/dIwXlz+jlg==", "shasum": "5841e5390d4c82dd5cdf7b2c95a830e3c2f47dd3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.20.0.tgz", "fileCount": 3, "unpackedSize": 3984937, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2v0vgz+sDOiHvPgRHQXcATsdzp+gWoXIZe1O/OZVIuAIgZi5oFATPH1P7C7bT5ChSC5161PyUd3ExYmGVOZsYn28="}], "size": 1592165}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.20.0_1722660551859_0.9529561677731697"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-03T04:49:12.110Z", "publish_time": 1722660552110, "_source_registry_name": "default"}, "4.21.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.21.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.21.0", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-zJ4hA+3b5tu8u7L58CCSI0A9N1vkfwPhWd/puGXwtZlsB5bTkwDNW/+JCU84+3QYmKpLi+XvHdmrlwUwDA6kqw==", "shasum": "0bc49a79db4345d78d757bb1b05e73a1b42fa5c3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.21.0.tgz", "fileCount": 3, "unpackedSize": 3841577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvPEiIuZMC5SngxPStnRudS0cdDejw/NTLWViF4sTGkwIhAMFm174IR9ejSa899wBkv3LpEz2gPipAFuLpfjZXTx7H"}], "size": 1548167}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.21.0_1723960553185_0.06173837430288942"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-18T05:55:53.379Z", "publish_time": 1723960553379, "_source_registry_name": "default"}, "4.21.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.21.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.21.1", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-z/9rtlGd/OMv+gb1mNSjElasMf9yXusAxnRDrBaYB+eS1shFm6/4/xDH1SAISO5729fFKUkJ88TkGPRUh8WSAA==", "shasum": "daa2b62a6e6f737ebef6700a12a93c9764e18583", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.21.1.tgz", "fileCount": 3, "unpackedSize": 3837481, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBwt223Zn1vckwr+gYdf1L4U/G44IBcwJrDl/PPSzXLxAiEAse8V1Hza87zYoyrqIMFh4UVPa9gr3dRjo8jz6Oh0n08="}], "size": 1542395}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.21.1_1724687675051_0.12612950648827925"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-26T15:54:35.260Z", "publish_time": 1724687675260, "_source_registry_name": "default"}, "4.21.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.21.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.21.2", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-PMxkrWS9z38bCr3rWvDFVGD6sFeZJw4iQlhrup7ReGmfn7Oukrr/zweLhYX6v2/8J6Cep9IEA/SmjXjCmSbrMQ==", "shasum": "a3bfb8bc5f1e802f8c76cff4a4be2e9f9ac36a18", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.21.2.tgz", "fileCount": 3, "unpackedSize": 3857961, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEktor/TvwskaXzPdgdMKwbwixbEM2ED0Lfriv6NlWylAiEAo03Y8TvroyyxRNCpRBXv/KEeQ9GR+1+ut/UWKd2qABw="}], "size": 1552469}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.21.2_1725001486406_0.7482958806287803"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-30T07:04:46.665Z", "publish_time": 1725001486665, "_source_registry_name": "default"}, "4.21.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.21.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.21.3", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-R8cwY9wcnApN/KDYWTH4gV/ypvy9yZUHlbJvfaiXSB48JO3KpwSpjOGqO4jnGkLDSk1hgjYkTbTt6Q7uvPf8eg==", "shasum": "2b28fb89ca084efaf8086f435025d96b4a966957", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.21.3.tgz", "fileCount": 3, "unpackedSize": 3808809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH9UJCHPzcSa23u2VKOq532jqfkPy5TdMY9WexrvqXzNAiEAtEibAykG9M7KdIbj204O0XFMpQHJNkVVf9DZ4edSi/4="}], "size": 1521043}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.21.3_1726124771424_0.048782724919502085"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-12T07:06:11.738Z", "publish_time": 1726124771738, "_source_registry_name": "default"}, "4.22.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.0", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-EHmPnPWvyYqncObwqrosb/CpH3GOjE76vWVs0g4hWsDRUVhg61hBmlVg5TPXqF+g+PvIbqkC7i3h8wbn4Gp2Fg==", "shasum": "46bb2f1135aeec646b720d6032d7c86915f8b2ec", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.0.tgz", "fileCount": 3, "unpackedSize": 3784233, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARJnqEdVHTJYzA4+ymibNOR9F0EmFG20xdLmbzL5GmPAiBGMCRwaZIds9hSEwuOMSxPu6JRZkmViiv73ELPkiAY1A=="}], "size": 1513947}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.0_1726721752143_0.6022830497328444"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-19T04:55:52.362Z", "publish_time": 1726721752362, "_source_registry_name": "default"}, "4.22.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.1", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-Hz5iwqYv08PpEC75z0GAgLlOY+cLAb0PVx578mLW0naugNfG0WQqoDzQoJWiivmtTdgmwoH5YXDnjZJb7MDlhA==", "shasum": "e121262f56986a299db69f32f59177a60b92d595", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.1.tgz", "fileCount": 3, "unpackedSize": 3796521, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGUoTqaF5dkeE9OqBRvqjeL/gJxKirty6sO83Hbd7bFyAiEAhb0r90lm0HOJEle2gE1KyXjvCIxlUfZOGnQnizGA3DI="}], "size": 1516517}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.1_1726820533524_0.3962926518626626"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T08:22:13.802Z", "publish_time": 1726820533802, "_source_registry_name": "default"}, "4.22.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.2", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-qgYbOEbrPfEkH/OnUJd1/q4s89FvNJQIUldx8X2F/UM5sEbtkqZpf2s0yly2jSCKr1zUUOY1hnTP2J1WOzMAdA==", "shasum": "c8fca373bec6df8550b31b3dbb56e2b241bc8718", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.2.tgz", "fileCount": 3, "unpackedSize": 3796521, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMe3FGU6ebC36FBrdrOhQYuzEhGkyEqbhFQP7wQEXKTAIgbqDzdQFAXBz+Dx7YtF2q2ACDJ+8/uGtZODtsE3czr84="}], "size": 1516517}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.2_1726824845924_0.24226525093720452"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T09:34:06.181Z", "publish_time": 1726824846181, "_source_registry_name": "default"}, "4.22.3-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.3-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.3-0", "readmeFilename": "README.md", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-74J71X0WBLU7kpRnZX4PkO2KQp6eSydpsFh2/pZ1KGXvClAH2kzNnNNrgZRxJD6dHEmJyXVvGte4LFZh2aAFCQ==", "shasum": "7627b7cbb6112124b1c88ea6d0d7ecec7fd43432", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.3-0.tgz", "fileCount": 3, "unpackedSize": 3796523, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCv0sL2aMYi9/0i98fQh/E8/MOC02Hb6FUbCr/C97dN3QIgW6t/K6cDUwn80ATvstxjo+y/YgScmtA77eAlWX/PbGw="}], "size": 1516522}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.3-0_1726843700217_0.33024825060083796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T14:48:20.500Z", "publish_time": 1726843700500, "_source_registry_name": "default"}, "4.22.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.3", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-pesLBj+872ZyL43ALKua0syvFLYY/Z5cVsB5xdBGkYaRYjxwIDze1QN9Cxi8tlAhJGblz41D4507DEraITDvkw==", "shasum": "32f44416c9cdb1e177e0f3fb935653b5abb1855c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.3.tgz", "fileCount": 3, "unpackedSize": 3796521, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLqFDSuMgUfQJnuGA08e1a1eOvzM25FpR4OajOC1picAIhAJ2388ufYuXYxrl0yKNyyOJXp/hb1j/VUdQsdjUiYJ2B"}], "size": 1516517}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.3_1726895011316_0.9465014511871477"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-21T05:03:31.578Z", "publish_time": 1726895011578, "_source_registry_name": "default"}, "4.22.4": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.4", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.4", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-GqFJ9wLlbB9daxhVlrTe61vJtEY99/xB3C8e4ULVsVfflcpmR6c8UZXjtkMA6FhNONhj2eA5Tk9uAVw5orEs4Q==", "shasum": "9f883a7440f51a22ed7f99e1d070bd84ea5005fc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.4.tgz", "fileCount": 3, "unpackedSize": 3796521, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHheZTlpMd/osDlJotOElt5YAZHLzmPXApIEVcAexZOkAiBNa62SS5xqbvKHQWQRxMl6CMc2mW1gL77Vlvw1EG7DoA=="}], "size": 1516517}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.4_1726899101491_0.5089686726908866"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-21T06:11:41.817Z", "publish_time": 1726899101817, "_source_registry_name": "default"}, "4.22.5": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.22.5", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.22.5", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-bR5nCojtpuMss6TDEmf/jnBnzlo+6n1UhgwqUvRoe4VIotC7FG1IKkyJbwsT7JDsF2jxR+NTnuOwiGv0hLyDoQ==", "shasum": "0708889674dccecccd28e2befccf791e0767fcb7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.5.tgz", "fileCount": 3, "unpackedSize": 3808809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICLCoECTj2+NuNfYa90ETYQ2iHs/rWwZutKbPiNg4PjTAiAazUFJF2UO18pSVHv1PHy2aRlpH+USg6TL7/8TMzz00g=="}], "size": 1525425}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.22.5_1727437719249_0.6403815562171589"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-27T11:48:39.515Z", "publish_time": 1727437719515, "_source_registry_name": "default"}, "4.23.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.23.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.23.0", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-+bxqx+V/D4FGrpXzPGKp/SEZIZ8cIW3K7wOtcJAoCrmXvzRtmdUhYNbgd+RztLzfDEfA2WtKj5F4tcbNPuqgeg==", "shasum": "dac114e4eda8d6c5d6b46abd7f1638c6e5846f75", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.23.0.tgz", "fileCount": 3, "unpackedSize": 3808809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAZQYOCvifCWoXiLJkQn3qY01FpM5wffLFtb2yuzDHhjAiEA6z7hN8VjCxJTHqFS6hbgD4jWXErm0wGLjVZU5TRAZTE="}], "size": 1525425}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.23.0_1727766638946_0.1755183051219067"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-01T07:10:39.209Z", "publish_time": 1727766639209, "_source_registry_name": "default"}, "4.24.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.24.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.24.0", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-mjBaoo4ocxJppTorZVKWFpy1bfFj9FeCMJqzlMQGjpNPY9JwQi7OuS1axzNIk0nMX6jSgy6ZURDZ2w0QW6D56g==", "shasum": "e0b4f9a966872cb7d3e21b9e412a4b7efd7f0b58", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.24.0.tgz", "fileCount": 3, "unpackedSize": 3837481, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDs9Nw4pjMxwBuIwGQIFM9gXnZ4mf71thih47Ms3WmvNAIhAIYKsmlY6WcCmyHGJ2pblxX8S5xAEFJaiJ3nGmn5oz+a"}], "size": 1536349}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.24.0_1727861863019_0.8711066037477568"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-02T09:37:43.215Z", "publish_time": 1727861863215, "_source_registry_name": "default"}, "4.24.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.24.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.24.1", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-mkl3uWq/ix18gAfzBUIecSwioPyJkbR6QXVaNuOGM7Qbs7f1EfDLP4XtLSJx4GL6mO8GrKhB3cmhUc3zjUrQSg==", "shasum": "f29c9444ef777ca403e7e52131c6d7eb3c4b9df3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.24.1.tgz", "fileCount": 3, "unpackedSize": 3849769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCH3rW5wRh3PtCBNVrxDN088IrUwk8yMUHcmuOTUD5zeMCIQCz43XX2wiChNEZ/v1mANGoR08NfG5FbncFqfNp/3i4Tg=="}], "size": 1542065}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.24.1_1730011407198_0.38207653682051834"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T06:43:27.443Z", "publish_time": 1730011407443, "_source_registry_name": "default"}, "4.24.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.24.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.24.2", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-gc97UebApwdsSNT3q79glOSPdfwgwj5ELuiyuiMY3pEWMxeVqLGKfpDFoum4ujivzxn6veUPzkGuSYoh5deQ2Q==", "shasum": "daab36c9b5c8ac4bfe5a9c4c39ad711464b7dfee", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.24.2.tgz", "fileCount": 3, "unpackedSize": 3849769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmrryuiG4uGY9MGWfvTVbw1QQp8m5HLBG6al0bG5CxkAiEA/DEr5NOwHLx/p8VTxALYy0xCrHmQJYOvYBXhAIthLu0="}], "size": 1542065}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.24.2_1730043634531_0.2440391379324962"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T15:40:34.749Z", "publish_time": 1730043634749, "_source_registry_name": "default"}, "4.25.0-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.25.0-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.25.0-0", "readmeFilename": "README.md", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-6gNxCN4ccVn4mrtNwRl252NP57lJJ+D1WsvIjxStN7dODebc6kmGan72//P5xiK0rGmwP5x0hK6s8UrYLgrPcg==", "shasum": "401902749b7b6636d2cc46688d967b6d12442b7c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.25.0-0.tgz", "fileCount": 3, "unpackedSize": 3849771, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFnF9WF/X5+AnlStNbgT3uAdf3UxFOJU4ZZ8GlpWxMRLAiA3ayZbpzmry6YeQJP/npiMhLgkdqpTtEGGdaCsc6jSdw=="}], "size": 1542070}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.25.0-0_1730182537021_0.18717617055025726"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T06:15:37.263Z", "publish_time": 1730182537263, "_source_registry_name": "default"}, "4.24.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.24.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.24.3", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-HoojGXTC2CgCcq0Woc/dn12wQUlkNyfH0I1ABK4Ni9YXyFQa86Fkt2Q0nqgLfbhkyfQ6003i3qQk9pLh/SpAYw==", "shasum": "264f8a4c206173945bdab2a676d638b7945106a9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.24.3.tgz", "fileCount": 3, "unpackedSize": 3849769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHQpMX6A3Hl8pBHs9tqDlQfAb0LXwrKGliyCWX/toy1wIhAPvfakEnX6lkNT6a5tPE0fsFECN8Tf6fZn2COv23lXK2"}], "size": 1542065}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.24.3_1730211275283_0.5005641819314546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T14:14:35.566Z", "publish_time": 1730211275566, "_source_registry_name": "default"}, "4.24.4": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.24.4", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.24.4", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-P8MPErVO/y8ohWSP9JY7lLQ8+YMHfTI4bAdtCi3pC2hTeqFJco2jYspzOzTUB8hwUWIIu1xwOrJE11nP+0JFAQ==", "shasum": "1651fdf8144ae89326c01da5d52c60be63e71a82", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.24.4.tgz", "fileCount": 3, "unpackedSize": 3812905, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDJQnfB7Y9gQN8035l5aAEvuQP/1QrnQASKLPh8pwMUnAiEArPd9faSiiMU3jNuAb1fY1Kki320bKK8yk4Ibi+vPZYc="}], "size": 1531518}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.24.4_1730710054196_0.9167645058996998"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-04T08:47:34.636Z", "publish_time": 1730710054636, "_source_registry_name": "default"}, "4.25.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.25.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.25.0", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-9T/w0kQ+upxdkFL9zPVB6zy9vWW1deA3g8IauJxojN4bnz5FwSsUAD034KpXIVX5j5p/rn6XqumBMxfRkcHapQ==", "shasum": "4b8d50a205eac7b46cdcb9c50d4a6ae5994c02e0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.25.0.tgz", "fileCount": 3, "unpackedSize": 3841577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB0/0FCuH5GpjzdcAhqRaMF4UcJWBrIRWdXNWZUlmHBXAiAu58i3NSGHKLOBlW+09HuSf+b2u6qTyuBYBiFy9My5Lw=="}], "size": 1540722}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.25.0_1731141468322_0.7459221778450382"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-09T08:37:48.632Z", "publish_time": 1731141468632, "_source_registry_name": "default"}, "4.26.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.26.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.26.0", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-YYcg8MkbN17fMbRMZuxwmxWqsmQufh3ZJFxFGoHjrE7bv0X+T6l3glcdzd7IKLiwhT+PZOJCblpnNlz1/C3kGQ==", "shasum": "80289a528dd333b0e277efd93bfa8e2cdd27e5eb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.26.0.tgz", "fileCount": 3, "unpackedSize": 3841577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAvITBVXFX3cfpSin+0NqKGo+CZwHvTAmzLoqfTuOuTAAiAscjXt2lIsx8uH+uVI7blf1/b25qxQeIcnFktAiVesfA=="}], "size": 1540723}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.26.0_1731480324766_0.4241638054902648"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-13T06:45:25.065Z", "publish_time": 1731480325065, "_source_registry_name": "default"}, "4.27.0-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.0-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.0-0", "readmeFilename": "README.md", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ctbWMs3t5v58oR1PBb1Jnfn6DhkdcbzY3/euISTBWwWKclqHvULHl7hKeqZAYzxjyUVNGRu1jf2eUC/YkPfkHQ==", "shasum": "de4ae95a929fe6e7089c38ddb431789344fe6a1d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.0-0.tgz", "fileCount": 3, "unpackedSize": 3841579, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDADXkBX6Naf7QG5TH3D2t5qPWg0GaVRwnlMwecOv0bXAiAOQdJvKwDBsRlf+jkUply1zCTU2Rbp99tWKYOJLDTqRw=="}], "size": 1540725}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.0-0_1731481418835_0.813412532471717"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-13T07:03:39.022Z", "publish_time": 1731481419022, "_source_registry_name": "default"}, "4.27.0-1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.0-1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.0-1", "readmeFilename": "README.md", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-MoPWQcaaABgwmj41AWVn+tqoxkbs/1dL6ktyMUv8vavmAT9ebTguUC/O1A5iuwSqneM6z/lTlClJ2e81E0UVZQ==", "shasum": "f1d00d7797dbf621a3547f5722716dace216cb24", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.0-1.tgz", "fileCount": 3, "unpackedSize": 3841579, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDA7Wzv68p+E72b1foyOGgj+CYZDPsVnHNFz5aWLCF9OgIgJ6Udpf37VHLtrjf3MA/PV+lRyH5gMil57BldnYUy5W4="}], "size": 1540725}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.0-1_1731566016311_0.6403478887083405"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-14T06:33:36.495Z", "publish_time": 1731566016495, "_source_registry_name": "default"}, "4.27.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.0", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-2Q9qQnk/eWdvXzzHl22y7tpDHREppFUh7N6cCs70HZEbQSgB7wd/2S/B05SSiyAiIn5BL+fYiASLds5bz0IQFw==", "shasum": "c909cfa66fca58c0725c9c198da5eca77f22897b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.0.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHiHQRhL0SQQ4hXqyN3WN/npIrpTjfMBqSV/F8o1dAwwIhAOQ4Gb6wpC1AODqmTZwaiBo+i5Fl8QTx7OQMqfK5f24E"}], "size": 1530686}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.0_1731667260868_0.24489537605936107"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T10:41:01.127Z", "publish_time": 1731667261127, "_source_registry_name": "default"}, "4.27.1-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.1-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.1-0", "readmeFilename": "README.md", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-khoqePR1iOiMa8gawV1pthFx5Rx9m6+ivYyCB5jvwL1z3xkJ2xkv2tHyWyaL8OLl5cpcsRfaOFSwTa+7qXd5OQ==", "shasum": "ecd0f399de8245cd816fcde329c5ad16bb500f2a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.1-0.tgz", "fileCount": 3, "unpackedSize": 3833387, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGSf9jUW/4MdDmrJh6FHFD9uqgvTap97EXPapqF21wwBAiEAxocydujewT6WjhReq1pyG3YjsVzliwdKOADRfBcuOMM="}], "size": 1530692}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.1-0_1731677315091_0.89789702108012"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T13:28:35.373Z", "publish_time": 1731677315373, "_source_registry_name": "default"}, "4.27.1-1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "cd58d4b285e4c6a1f809dfcfe7dca0c9179fb135", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-i5euYlPGyfk8Hm4FcfK4Pv0xyH+ZNNpEkVHeD75lp8Rr+R51cjJH5K8hJCASsA9INGJKiGqjgTGXFSk47pxt4A==", "signatures": [{"sig": "MEUCIQDCUA2oPoqmiEMdEWbnAu9qeCDIYnQgh8zln5P1c6zkEwIgPGAfqVeQlxHOoc5dfemR1Xq7qWGRWCNQdMrGmClHsNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3833387, "size": 1530692}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-s390x-gnu_4.27.1-1_1731685109567_0.3322091377130383", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-11-15T15:38:29.852Z", "publish_time": 1731685109852, "_source_registry_name": "default"}, "4.27.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.1", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-eVs55EQmW92BDzlwzp1Yg5dGEP8UxXb611qe0DS2xM4WxmFPjjTyb7JSsrxRbdl91A5ZNcW4O8cQuDJ7ELYdeg==", "shasum": "1684a927129476608f990100e1d457c8fa841920", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.1.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDV0LFoIJ9Dz0YRj/CSs29z5qK9xQOkS2gE0xPSujFYpgIhAMFA9HBa2vaaMkjtW0FcHcRuu0L75c9FQYRO5Ac/y41Z"}], "size": 1530687}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.1_1731686887968_0.2804081837714887"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T16:08:08.266Z", "publish_time": 1731686888266, "_source_registry_name": "default"}, "4.27.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.2", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NHGo5i6IE/PtEPh5m0yw5OmPMpesFnzMIS/lzvN5vknnC1sXM5Z/id5VgcNPgpD+wHmIcuYYgW+Q53v+9s96lQ==", "shasum": "4941df3416caeecd265e718aa9e0a20efcb187bf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.2.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDl2pkN/W4YVF9Ev2LtimLfwNRL8XUNx7Cr/0hIjrctxAiAjKQhFY2c83plXjLWLVbpZy4bC5Eg8fD2JnMV0GJ2p1A=="}], "size": 1530687}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.2_1731691230930_0.33098733516805545"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T17:20:31.224Z", "publish_time": 1731691231224, "_source_registry_name": "default"}, "4.27.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.3", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-d4wVu6SXij/jyiwPvI6C4KxdGzuZOvJ6y9VfrcleHTwo68fl8vZC5ZYHsCVPUi4tndCfMlFniWgwonQ5CUpQcA==", "shasum": "6b9c04d84593836f942ceb4dd90644633d5fe871", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.3.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwTLU/NyL0Ve6v22Mg/vECijSo1cb8VLEc22EaPeMcIwIgaFJWTGH3TPKj2Lz1+PW29eZ7VS74mDgzjA/IyOcfJwA="}], "size": 1530686}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.3_1731948002700_0.8561372443889175"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-18T16:40:02.971Z", "publish_time": 1731948002971, "_source_registry_name": "default"}, "4.27.4": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.27.4", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.27.4", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PFz+y2kb6tbh7m3A7nA9++eInGcDVZUACulf/KzDtovvdTizHpZaJty7Gp0lFwSQcrnebHOqxF1MaKZd7psVRg==", "shasum": "efaddf22df27b87a267a731fbeb9539e92cd4527", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.27.4.tgz", "fileCount": 3, "unpackedSize": 3841577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChV+sNY3lvB3aUD/zMeoAK5HinXSlps/pm2zMKJnKVzAIhAILmEIAP/5W/AzHceX0c08/V6nGOHo++4iIETZXOwODi"}], "size": 1541606}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.27.4_1732345245219_0.8117600517771504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-23T07:00:45.443Z", "publish_time": 1732345245443, "_source_registry_name": "default"}, "4.28.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.28.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.28.0", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-LQlP5t2hcDJh8HV8RELD9/xlYtEzJkm/aWGsauvdO2ulfl3QYRjqrKW+mGAIWP5kdNCBheqqqYIGElSRCaXfpw==", "shasum": "eb2e3f3a06acf448115045c11a5a96868c95a556", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.28.0.tgz", "fileCount": 3, "unpackedSize": 3812905, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCl/V7n0c2n/3ArQ8ZQob+KAYp5zMsPK213VuQ2+4S1SgIgMESIa9qH5twkEdfGTh3BeNOGzIJ5jt9DP64z2YZaATI="}], "size": 1531289}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.28.0_1732972573204_0.7982492655693736"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-30T13:16:13.476Z", "publish_time": 1732972573476, "_source_registry_name": "default"}, "4.28.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.28.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.28.1", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/yqC2Y53oZjb0yz8PVuGOQQNOTwxcizudunl/tFs1aLvObTclTwZ0JhXF2XcPT/zuaymemCDSuuUPXJJyqeDOg==", "shasum": "015c52293afb3ff2a293cf0936b1d43975c1e9cd", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.28.1.tgz", "fileCount": 3, "unpackedSize": 3800617, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2b2xOqN+iFclYTy/uHUJSZQnc9QeK2uXsw+GAuGkRrAiBtbEoZLCgJkSopBF0cS2K3CKtQTKlIaNMTquR8CIKO4Q=="}], "size": 1529366}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-linux-s390x-gnu_4.28.1_1733485524510_0.8033267726814186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T11:45:24.708Z", "publish_time": 1733485524708, "_source_registry_name": "default"}, "4.29.0-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.29.0-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.29.0-0", "readmeFilename": "README.md", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-pGX5gH4jk85V9i1nAtQFQp0219tB7MEvtNsghtAwJ5KIRFSfWiz8P0CO7dgUMqioRELpFuGJULIEFBmBP4dObA==", "shasum": "6c57674d0adda0c27f60969945855f04bf097684", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.29.0-0.tgz", "fileCount": 3, "unpackedSize": 3829291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4yvic901nlZOjUUI5Y2DdgDT3tpi8opjhPOIboRh4qgIhANN8qU84zbmZT+n2+NckgsrsYxlufdNWzt+yAnfd4/5D"}], "size": 1534930}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.29.0-0_1734331222733_0.18471968339602896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-16T06:40:22.936Z", "publish_time": 1734331222936, "_source_registry_name": "default"}, "4.29.0-1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.29.0-1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.29.0-1", "readmeFilename": "README.md", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/nNNSyNS12TbY1WWR6EbpffoooADHrOSenXA2GwkPODHuG+HYIKFVaKUzpJleWOPeOBxrIl5afJBnIEAsYdV/Q==", "shasum": "54396f039d418797bf1755024c906366c0e4088a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.29.0-1.tgz", "fileCount": 3, "unpackedSize": 3829291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFNwPY2X+nOxmaVJgB1OMzCtFFHxnae+Aj3+2kX7WB62AiBTe3nR9PKEBzSBJY1R+frRVXj5pYqbFJ5uKnYOTODfFA=="}], "size": 1534931}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.29.0-1_1734590277459_0.48743650339972944"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-19T06:37:57.740Z", "publish_time": 1734590277740, "_source_registry_name": "default"}, "4.29.0-2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.29.0-2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.29.0-2", "readmeFilename": "README.md", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-f9mkg95JfrCKmZXFLGxFUuPcCHRv0PKrFLmx8Rern5/m2a7VlZNIivXAHfbZZ6zz8iKkFQozjo2B+697GQ+CBg==", "shasum": "2324d8fac9d5e726f91a02588b93bb2db6e4d0b1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.29.0-2.tgz", "fileCount": 3, "unpackedSize": 3829291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAOCwYWgnca7mpFeFvB+bKRBtsZj1MuCM/QGIMqDZ368AiEAjLRiyvoOEwBEuKl99pHfj6OYpFAab0vVjuFtYohdnDs="}], "size": 1534931}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.29.0-2_1734677791744_0.22810450524922588"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T06:56:31.986Z", "publish_time": 1734677791986, "_source_registry_name": "default"}, "4.29.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.29.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.29.0", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lSQtvrYIONme7a4gbf4O9d3zbZat3/5covIeoqk27ZIkTgBeL/67x+wq2bZfpLjkqQQp5SjBPQ/n0sg8iArzTg==", "shasum": "d998c75636cfd419d87ad13bbd775a80a6059f5f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.29.0.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN/T0lNjwDnB9NZ3PCUdp8RGOWhj5/WU5VsIOIEUqwzwIgTDOht6oXiUTEtUjevQMAmh74LXEhh0JAWXGDy9bmOug="}], "size": 1541061}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.29.0_1734719873094_0.642344497000642"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T18:37:53.338Z", "publish_time": 1734719873338, "_source_registry_name": "default"}, "4.29.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.29.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.29.1", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WM9lIkNdkhVwiArmLxFXpWndFGuOka4oJOZh8EP3Vb8q5lzdSCBuhjavJsw68Q9AKDGeOOIHYzYm4ZFvmWez5g==", "shasum": "31f51e1e05c6264552d03875d9e2e673f0fd86e3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.29.1.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEyvR3G92VkBRe9pLkTwQAnrrnfOYEshBhFjOQdiR+ofAiBKy4YUzcMhVf7fm4C0KX1ZyOOR/+T1lPZXTs/MGdjpNw=="}], "size": 1541061}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.29.1_1734765391666_0.613223697928643"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:16:31.881Z", "publish_time": 1734765391881, "_source_registry_name": "default"}, "4.30.0-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.30.0-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.30.0-0", "readmeFilename": "README.md", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-82KpfPE1jfW0tB8tD3n56IkWBAS5Jgcy0e/NVloWXygLqT23FsAgHCIAy8ZR1TAgEJDjIb7fzzOmOpzRnoP5rQ==", "shasum": "9e3f69dedc4b7c38a80cf0d44d42823c6fc8358d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.30.0-0.tgz", "fileCount": 3, "unpackedSize": 3833387, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD05sJ2cDal292lP+Na+EsT4H/2U560TA7F+dboa+qUrAIgAIcSWbXQ75sBcvMu2s8YMiZ4Gg41YzB6hB9KG5tB9ko="}], "size": 1541061}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.30.0-0_1734765462872_0.6329160713734541"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:17:43.099Z", "publish_time": 1734765463099, "_source_registry_name": "default"}, "4.30.0-1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.30.0-1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.30.0-1", "readmeFilename": "README.md", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dZo9ZLmS0SNJHFjOMBKgYjI3JZN2i6YQttkiYbHzyVhjAWoyZGkVv6Hzpr1jpB5lQatf0Tz1sHgYcuvNbJ1lfQ==", "shasum": "984e123bf66cfddde92bc66102fe03faa7547333", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.30.0-1.tgz", "fileCount": 3, "unpackedSize": 3833387, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDeVhKoIhXilKP3dB4drE4fTkmbKa26AuLt6cH0scGakAIhANS7dAE5fqIFssufdcAMk1XvRCNexFLNlXI5W665+/26"}], "size": 1541380}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.30.0-1_1735541566454_0.4059857751888518"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-30T06:52:46.647Z", "publish_time": 1735541566647, "_source_registry_name": "default"}, "4.29.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.29.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.29.2", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-9ouIR2vFWCyL0Z50dfnon5nOrpDdkTG9lNDs7MRaienQKlTyHcDxplmk3IbhFlutpifBSBr2H4rVILwmMLcaMA==", "shasum": "0ad4aaae2fd89c3607b743c63514c4561905672b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.29.2.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDR+zZmFtkVEtrZ5PeJxEFgBrF8wM9tyICTwFCiDXlZNAIgJQ4HIpdqzkDAcu5vH1LX1ru4erNcx7INACttVWoY84s="}], "size": 1541383}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.29.2_1736078892902_0.5698492968679894"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-05T12:08:13.170Z", "publish_time": 1736078893170, "_source_registry_name": "default"}, "4.30.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.30.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.30.0", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VU/P/IODrNPasgZDLIFJmMiLGez+BN11DQWfTVlViJVabyF3JaeaJkP6teI8760f18BMGCQOW9gOmuzFaI1pUw==", "shasum": "7054b237152d9e36c51194532a6b70ca1a62a487", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.30.0.tgz", "fileCount": 3, "unpackedSize": 3833385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHWWiiojEgIGv0ck0/20y8j8YvGJSAIcTZHnhM68SLkWAiBrsd+8h5Ig9kHGp+trALt/xYVkptR15e6EUL8PrLWVEw=="}], "size": 1541382}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.30.0_1736145426845_0.02931857091693102"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-06T06:37:07.057Z", "publish_time": 1736145427057, "_source_registry_name": "default"}, "4.30.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.30.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.30.1", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-GWFs97Ruxo5Bt+cvVTQkOJ6TIx0xJDD/bMAOXWJg8TCSTEK8RnFeOeiFTxKniTc4vMIaWvCplMAFBt9miGxgkA==", "shasum": "c2dcd8a4b08b2f2778eceb7a5a5dfde6240ebdea", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.30.1.tgz", "fileCount": 3, "unpackedSize": 3833361, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF9FWoaP3sJbyArkY6aAtO4rHcc4LmS9Fmdbi5M24910AiAXAqkL+Kn/bQoHvDWR0A/3OtJUAwQBexeAFowpC9t/qA=="}], "size": 1541160}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.30.1_1736246180552_0.18268225350674205"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-07T10:36:20.809Z", "publish_time": 1736246180809, "_source_registry_name": "default"}, "4.31.0-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.31.0-0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.31.0-0", "readmeFilename": "README.md", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-IK04nE3xNjucyyNptv0Vb3CcJkD9lFjXBlxOsbenXIS+CL0IKpLL6cCQLbsBjxojT1S5trMUnTKHei/N+/2rdw==", "shasum": "7fa2997eb8828e15d65e2d35e2223941d52c0819", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.31.0-0.tgz", "fileCount": 3, "unpackedSize": 3882467, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBNHaQux0divlDbKGvdEHCedDnZEOL8XrzIidn34vU9PAiBYl1aavmh8No1uxo37A2zANCgB3jkWI0VeZDQZGBbI5Q=="}], "size": 1558081}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.31.0-0_1736834290563_0.995238556056776"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-14T05:58:10.783Z", "publish_time": 1736834290783, "_source_registry_name": "default"}, "4.31.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.31.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.31.0", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-O1o5EUI0+RRMkK9wiTVpk2tyzXdXefHtRTIjBbmFREmNMy7pFeYXCFGbhKFwISA3UOExlo5GGUuuj3oMKdK6JQ==", "shasum": "cfe8052345c55864d83ae343362cf1912480170e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.31.0.tgz", "fileCount": 3, "unpackedSize": 3911137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH82eZlbFtrIIWtR6e5tiGLZAc7fvWHGe5XlKtrggq6PAiBIbMJt7WTRddJTtkqh/LpUtyxckFeGKItcy4Qz1xpvWw=="}], "size": 1558650}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.31.0_1737291435459_0.8092926712564286"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-19T12:57:15.700Z", "publish_time": 1737291435700, "_source_registry_name": "default"}, "4.32.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.32.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.32.0", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-8ZGN7ExnV0qjXa155Rsfi6H8M4iBBwNLBM9lcVS+4NcSzOFaNqmt7djlox8pN1lWrRPMRRQ8NeDlozIGx3Omsw==", "shasum": "fd99b335bb65c59beb7d15ae82be0aafa9883c19", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.32.0.tgz", "fileCount": 3, "unpackedSize": 3911137, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDQiLAmFqUMo6+5GoLgWQdPHSn3uyyqUSWX5HvIgx8dJAIhAMImEz5jVn5XjK5nyQzb6lfTyfkPsQr/DwGuYjcGFLJc"}], "size": 1561605}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.32.0_1737707283214_0.9499669529238213"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-24T08:28:03.502Z", "publish_time": 1737707283502, "_source_registry_name": "default"}, "4.33.0-0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-s390x-gnu@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "847ac69ccca5b1efe3eaf1993c4b63117fff6154", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-HbjJnfPS7rUO9N0LWoSAjrOBfISKeIpydoMBiGzthr1UiACRTk1n8Mx0bXw3CkTK6lv+Vq5txQM6gJY9+z4njw==", "signatures": [{"sig": "MEUCIQCDVDe2vk/95lrWbsh5VnxlOzgHrSAomyQnR7W/tgo/igIgR+r62GKvcRk9iH/AZon6EUpPiJGrnIne02LJ6njOdK0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3911139, "size": 1561613}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-s390x-gnu_4.33.0-0_1738053036557_0.8647038601072281", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-01-28T08:30:36.825Z", "publish_time": 1738053036825, "_source_registry_name": "default"}, "4.32.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.32.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.32.1", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-RKt8NI9tebzmEthMnfVgG3i/XeECkMPS+ibVZjZ6mNekpbbUmkNWuIN2yHsb/mBPyZke4nlI4YqIdFPgKuoyQQ==", "shasum": "cc98c32733ca472635759c78a79b5f8d887b2a6a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.32.1.tgz", "fileCount": 3, "unpackedSize": 3911137, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH78NZp2f6dG9mu819Oc5T8Z+Mj754outRbAAYYN48F1AiEA4fIVj8znbTdm66voMEcWYeTu9te6yAGT6AM728q1FdA="}], "size": 1561605}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.32.1_1738053223729_0.035294177775534896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-28T08:33:43.965Z", "publish_time": 1738053223965, "_source_registry_name": "default"}, "4.33.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-s390x-gnu@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["s390x"], "dist": {"shasum": "aa3269b4844c4c6000dd04def00f525e2b49c24f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-j/8mBAf5RQCJgm0lPkFOHu2qJsXxKJKBadwEwjHxB3K9ZIul+BxMfWYXUX/RUKGji3r7AaxOgsbD0XkGV7YaQg==", "signatures": [{"sig": "MEUCIC1UmzEs9LIYHoyrXM1yYMWsDrJeRdsOSuTRtt9FyMyHAiEAjsBCLTg/WnE2YUWb/VOj5eXxZ58OCeOIVdefCr524/s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3896785, "size": 1559329}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-s390x-gnu_4.33.0_1738393949288_0.4299087151013128", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-02-01T07:12:29.510Z", "publish_time": 1738393949510, "_source_registry_name": "default"}, "4.34.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.0", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UfUCo0h/uj48Jq2lnhX0AOhZPSTAq3Eostas+XZ+GGk22pI+Op1Y6cxQ1JkUuKYu2iU+mXj1QjPrZm9nNWV9rg==", "shasum": "e227deb812ccf6c077cef1fa4dc3fe39c9489049", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.0.tgz", "fileCount": 3, "unpackedSize": 3896785, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCTJSK3L52h7sFtHs64v8Ylz38oLBSaBcmA2bZaPThBvgIhAPKvkmIaO9OKM4a8r0954faSJ8+IZOXrc0F2aVZooGRl"}], "size": 1559329}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.0_1738399250129_0.12812123019712307"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-01T08:40:50.335Z", "publish_time": 1738399250335, "_source_registry_name": "default"}, "4.34.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.1", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-QlIo8ndocWBEnfmkYqj8vVtIUpIqJjfqKggjy7IdUncnt8BGixte1wDON7NJEvLg3Kzvqxtbo8tk+U1acYEBlw==", "shasum": "0c224baa3e1823f2b7b0e45dfaf43a99a18e8248", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.1.tgz", "fileCount": 3, "unpackedSize": 3896785, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDWtW68eyC5AJt/zt4ycFrwfKXhBnv2vkVuH/xroiZMPAiB9pxj8udjfTIbEZ3KTSexuG8X9qm7SQolkP9RdV8Nn1Q=="}], "size": 1559329}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.1_1738565920920_0.7001298115606314"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-03T06:58:41.117Z", "publish_time": 1738565921117, "_source_registry_name": "default"}, "4.34.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.2", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-49TtdeVAsdRuiUHXPrFVucaP4SivazetGUVH8CIxVsNsaPHV4PFkpLmH9LeqU/R4Nbgky9lzX5Xe1NrzLyraVA==", "shasum": "844807d9bf486b319b75a0dcecb355093af25edf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.2.tgz", "fileCount": 3, "unpackedSize": 3896785, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD5JnjGTPjbSqzgpXc/OAKDvy1ILi62IGh2ZfYenJlQVAIhAMErCV+EN9CrAOrj5rwclyvr7fIFQSq56Fn8ItoZXL5O"}], "size": 1559330}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.2_1738656631792_0.84184493415063"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-04T08:10:32.059Z", "publish_time": 1738656632059, "_source_registry_name": "default"}, "4.34.3": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.3", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.3", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dIOoOz8altjp6UjAi3U9EW99s8nta4gzi52FeI45GlPyrUH4QixUoBMH9VsVjt+9A2RiZBWyjYNHlJ/HmJOBCQ==", "shasum": "e6ac0788471a9f7400b358eb5f91292efcc900c4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.3.tgz", "fileCount": 3, "unpackedSize": 3896785, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCnxH0qI8q8m7jsgUiIihjVpug2VEzVya62TblGrO85RQIgN+Xk2SYhE5J5Am3TU42gsC/00fDrWulQ49Oijr0Y8b4="}], "size": 1559330}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.3_1738747353833_0.004562025244290835"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T09:22:34.104Z", "publish_time": 1738747354104, "_source_registry_name": "default"}, "4.34.4": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.4", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.4", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-nLbfQp2lbJYU8obhRQusXKbuiqm4jSJteLwfjnunDT5ugBKdxqw1X9KWwk8xp1OMC6P5d0WbzxzhWoznuVK6XA==", "shasum": "a6d303c937fb1a49b7aa44ab32be6d61bb571f59", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.4.tgz", "fileCount": 3, "unpackedSize": 3896785, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEUHW63P1lIwsIVM6aqwUndu8eVUOoXNAeFiVEDODieiAiEAz6S/5NgHkGlMklcrxMdUtnkRUFofMyJv2kgXrHo15Pk="}], "size": 1559329}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.4_1738791100181_0.16680289125463843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T21:31:40.451Z", "publish_time": 1738791100451, "_source_registry_name": "default"}, "4.34.5": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.5", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.5", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-V3+BvgyHb21aF7lw0sc78Tv0+xLp4lm2OM7CKFVrBuppsMvtl/9O5y2OX4tdDT0EhIsDP/ObJPqDuEg1ZoTwSQ==", "shasum": "e3a00ac613313a30ab2a26afa0e2728d3c40b01a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.5.tgz", "fileCount": 3, "unpackedSize": 3896785, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIExJbMwoYlMM5JvxQyCPjmhFaZ7wZNnK2KJDIzFYNFbOAiEAmcyieFcW3KCegrb3AI7vubsQSRCfPvCh8gTlo7ukUFQ="}], "size": 1561883}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.5_1738918412533_0.6759145037901049"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T08:53:32.762Z", "publish_time": 1738918412762, "_source_registry_name": "default"}, "4.34.6": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.6", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.6", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WoKLVrY9ogmaYPXwTH326+ErlCIgMmsoRSx6bO+l68YgJnlOXhygDYSZe/qbUJCSiCiZAQ+tKm88NcWuUXqOzw==", "shasum": "f9113498d22962baacdda008b5587d568b05aa34", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.6.tgz", "fileCount": 3, "unpackedSize": 3884481, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIADHoD9VxvOXzarptEAGM0VgsVF+BgOQ3gN0p9UCBu6uAiEAzTPauDOWd2Mf548EW+tKzLS1ku4ai1oOI/dQRZoRS1I="}], "size": 1557853}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.6_1738945956242_0.8539596140053689"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T16:32:36.647Z", "publish_time": 1738945956647, "_source_registry_name": "default"}, "4.34.7": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.7", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.7", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-nNXNjo4As6dNqRn7OrsnHzwTgtypfRA3u3AKr0B3sOOo+HkedIbn8ZtFnB+4XyKJojIfqDKmbIzO1QydQ8c+Pw==", "shasum": "73df374c57d036856e33dbd2715138922e91e452", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.7.tgz", "fileCount": 3, "unpackedSize": 3892673, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG3v9f38c6T3y5fTPQu/PfQrUKubQoaVmae5kSXjdpSlAiAp/35xeBCsxIII3TmXI+PuNxuQpV8IJ14bfLe/ImWWSQ=="}], "size": 1559362}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.7_1739526866861_0.4057067270650392"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T09:54:27.088Z", "publish_time": 1739526867088, "_source_registry_name": "default"}, "4.34.8": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.8", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.8", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-DdePVk1NDEuc3fOe3dPPTb+rjMtuFw89gw6gVWxQFAuEqqSdDKnrwzZHrUYdac7A7dXl9Q2Vflxpme15gUWQFA==", "shasum": "7dbc3ccbcbcfb3e65be74538dfb6e8dd16178fde", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.8.tgz", "fileCount": 3, "unpackedSize": 3892673, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGChCK5q+iDD7AfgQb2wY73hjHrxCvbmaje2afG3MBttAiByLmfXpaOW3ZyOFfrVs1B3QYXhSEUIEuKXjrjlrC/Oug=="}], "size": 1559362}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.8_1739773612770_0.8893368862364919"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-17T06:26:53.146Z", "publish_time": 1739773613146, "_source_registry_name": "default"}, "4.34.9": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.34.9", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.34.9", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-U+5SwTMoeYXoDzJX5dhDTxRltSrIax8KWwfaaYcynuJw8mT33W7oOgz0a+AaXtGuvhzTr2tVKh5UO8GVANTxyQ==", "shasum": "196347d2fa20593ab09d0b7e2589fb69bdf742c6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.9.tgz", "fileCount": 3, "unpackedSize": 3970497, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDAmiI9ooKOFOrW7IbkygykjppMfCbHwZvgK0VLUlTB5gIgNYczgugDRl24Z91lwzGxMV1kvt6FUAdnxa3R6UwglJg="}], "size": 1583704}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.34.9_1740814386112_0.2483098181950809"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-01T07:33:06.367Z", "publish_time": 1740814386367, "_source_registry_name": "default"}, "4.35.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.35.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.35.0", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hQRkPQPLYJZYGP+Hj4fR9dDBMIM7zrzJDWFEMPdTnTy95Ljnv0/4w/ixFw3pTBMEuuEuoqtBINYND4M7ujcuQw==", "shasum": "2e34835020f9e03dfb411473a5c2a0e8a9c5037b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.35.0.tgz", "fileCount": 3, "unpackedSize": 4052417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDqR7UJCgq4/uAribmQHnk/JVumXQGDi45Yvkx2VPD4nQIgfK7SjgLWp4s+VX4iOrkLNGyvoDX3Htphn0Q144SsMCs="}], "size": 1614582}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.35.0_1741415117750_0.36944743207692987"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-08T06:25:17.972Z", "publish_time": 1741415117972, "_source_registry_name": "default"}, "4.36.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.36.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.36.0", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4a5gf2jpS0AIe7uBjxDeUMNcFmaRTbNv7NxI5xOCs4lhzsVyGR/0qBXduPnoWf6dGC365saTiwag8hP1imTgag==", "shasum": "6fa895f181fa6804bc6ca27c0e9a6823355436dd", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.36.0.tgz", "fileCount": 3, "unpackedSize": 4048321, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBWMTQsQ99E5mXNq5wuYHVfU7tjpEEVwrf7nuQksFwlkAiAvYvxshLVJ8nmSD5NoRRjCN1mCVsrbaQgDa2pRY7gYhg=="}], "size": 1605573}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.36.0_1742200574487_0.7178864119826585"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-17T08:36:14.740Z", "publish_time": 1742200574740, "_source_registry_name": "default"}, "4.37.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.37.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.37.0", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hZDDU5fgWvDdHFuExN1gBOhCuzo/8TMpidfOR+1cPZJflcEzXdCy1LjnklQdW8/Et9sryOPJAKAQRw8Jq7Tg+A==", "shasum": "6b0b7df33eb32b0ee7423898b183acc1b5fee33e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.37.0.tgz", "fileCount": 3, "unpackedSize": 4011457, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIElYyxNP52DCPI93NBD8Nsg5iPRkoCwKoGhixsnrSrRIAiAm9f8nsITy0/DAf5yehr99OSL14crYU0LXnWF6tvbFXw=="}], "size": 1599505}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.37.0_1742741855312_0.9611442496986102"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-23T14:57:35.544Z", "publish_time": 1742741855544, "_source_registry_name": "default"}, "4.38.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.38.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.38.0", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Mpp6+Z5VhB9VDk7RwZXoG2qMdERm3Jw07RNlXHE0bOnEeX+l7Fy4bg+NxfyN15ruuY3/7Vrbpm75J9QHFqj5+Q==", "shasum": "1fe4a88b97e36d64dbf1f01cfa7842d269a094cf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.38.0.tgz", "fileCount": 3, "unpackedSize": 4052417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEMCHw2ZR2K0ij1q2A7wA1Rj+DZmaEhax6a4bgjSrPlJzHQCICUg1aBUmsDkh1baadbXickBk3rpUfX0+wRMOsAm1cE+"}], "size": 1608918}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.38.0_1743229780207_0.6566558065553836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-29T06:29:40.513Z", "publish_time": 1743229780513, "_source_registry_name": "default"}, "4.39.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.39.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.39.0", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-iRDJd2ebMunnk2rsSBYlsptCyuINvxUfGwOUldjv5M4tpa93K8tFMeYGpNk2+Nxl+OBJnBzy2/JCscGeO507kA==", "shasum": "7181c329395ed53340a0c59678ad304a99627f6d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.39.0.tgz", "fileCount": 3, "unpackedSize": 4052417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB4AA1B67yW+g3rltzGXpf5dP8gkUJxuzAg3TsRTC+yUAiAwlxvC/vkUDO8LLlWfPSLglXjrDt9grY2vIjViGoTDAw=="}], "size": 1608918}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.39.0_1743569405395_0.6538716774937625"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T04:50:05.673Z", "publish_time": 1743569405673, "_source_registry_name": "default"}, "4.40.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.40.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.40.0", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-SpnYlAfKPOoVsQqmTFJ0usx0z84bzGOS9anAC0AZ3rdSo3snecihbhFTlJZ8XMwzqAcodjFU4+/SM311dqE5Sw==", "shasum": "9bad59e907ba5bfcf3e9dbd0247dfe583112f70b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.40.0.tgz", "fileCount": 3, "unpackedSize": 4113401, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC1GOOkfhJxOqO07LY9Dc5gBQWA+8/IkKPVgcXE7KGG8wIhALvtu62DCFEK8gNrN/Dr+8mpPu0uzAYIjnazxeJ26W54"}], "size": 1633695}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.40.0_1744447207442_0.8473564310712027"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-12T08:40:07.688Z", "publish_time": 1744447207688, "_source_registry_name": "default"}, "4.40.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.40.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.40.1", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-h8d28xzYb98fMQKUz0w2fMc1XuGzLLjdyxVIbhbil4ELfk5/orZlSTpF/xdI9C8K0I8lCkq+1En2RJsawZekkg==", "shasum": "e9f09b802f1291839247399028beaef9ce034c81", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.40.1.tgz", "fileCount": 3, "unpackedSize": 4113425, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCusZLoqIrzev7pCDxwUOP/RsI5TcTUIBNkcBg2WaPbXgIhAM9CEWmB7ByyI/zihTujknBbwhseEYyKOLF/QXzzeoOC"}], "size": 1626231}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.40.1_1745814955695_0.3133009442049619"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-28T04:35:55.930Z", "publish_time": 1745814955930, "_source_registry_name": "default"}, "4.40.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.40.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.40.2", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-B7LKIz+0+p348JoAL4X/YxGx9zOx3sR+o6Hj15Y3aaApNfAshK8+mWZEf759DXfRLeL2vg5LYJBB7DdcleYCoQ==", "shasum": "dda3265bbbfe16a5d0089168fd07f5ebb2a866fe", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.40.2.tgz", "fileCount": 3, "unpackedSize": 4109329, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICml2UkQwOa3ER6STAJGMJPqULH/yjVcnHaasLj7Eq6uAiEAu9zLOR5fbij7cRsw2OLr6qjvb4jWXw5hRBRvd8ia5NQ="}], "size": 1619527}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.40.2_1746516446743_0.33177601637277787"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T07:27:26.997Z", "publish_time": 1746516446997, "_source_registry_name": "default"}, "4.41.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.41.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.41.0", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/L3pW48SxrWAlVsKCN0dGLB2bi8Nv8pr5S5ocSM+S0XCn5RCVCXqi8GVtHFsOBBCSeR+u9brV2zno5+mg3S4Aw==", "shasum": "5619303cc51994e3df404a497f42c79dc5efd6eb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.0.tgz", "fileCount": 3, "unpackedSize": 2925585, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDWTZ7TRSJf3ERKfnfBclO2UtYRdBO96ya6aTBl99pnLwIgJdSjlhhKRwrNHXapVCHQqwyXtrzFIvSValm4AMY4V5E="}], "size": 1188184}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.41.0_1747546440129_0.058044512997095454"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-18T05:34:00.415Z", "publish_time": 1747546440415, "_source_registry_name": "default"}, "4.41.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.41.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.41.1", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-oIE6M8WC9ma6xYqjvPhzZYk6NbobIURvP/lEbh7FWplcMO6gn7MM2yHKA1eC/GvYwzNKK/1LYgqzdkZ8YFxR8g==", "shasum": "98896eca8012547c7f04bd07eaa6896825f9e1a5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.1.tgz", "fileCount": 3, "unpackedSize": 3060969, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC+Q11jcnE2LpQrrWJz+nESR5OLvDWjvBOH9YfiXrM42AIhAO5Z+RDgMtQYY9L3Ht8kCtY6YUTWtDsPMK2cPd5VRk2Y"}], "size": 1230881}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.41.1_1748067310427_0.11418836156080636"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-24T06:15:10.659Z", "publish_time": 1748067310659, "_source_registry_name": "default"}, "4.41.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.41.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.41.2", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-j5oHqcj58pDqN3+CeJdTB3NfHipxIxVIH24VkRlVVhs9jJXks1ovtO4Tf6YxlBzf44ZTx9D2uhO9DZttC/wgUA==", "shasum": "e1f6e30eb4ea2e4dd768b5e8cf144d1ffb16b21e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.2.tgz", "fileCount": 3, "unpackedSize": 3065065, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCExCINiQk/CZVhF7au/f9VIABWiFJDwkrN+ukI5IP1SQIgRSwldOOgq98hsgb8VPZElo3lh/yr239oOjO9YB1VLOQ="}], "size": 1236070}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.41.2_1749210065585_0.11520086457872769"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T11:41:05.805Z", "publish_time": 1749210065805, "_source_registry_name": "default"}, "4.42.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.42.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.42.0", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-I2Y1ZUgTgU2RLddUHXTIgyrdOwljjkmcZ/VilvaEumtS3Fkuhbw4p4hgHc39Ypwvo2o7sBFNl2MquNvGCa55Iw==", "shasum": "4261c714cd750e3fb685a330dfca7bb8f5711469", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.42.0.tgz", "fileCount": 3, "unpackedSize": 3065065, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCafyZMp8YQHtC8eAka3eZcMVO2FotinJE697TggHGi1AIhANIGBZuBg4yY2rVFbbiENPL9QV8wYA5ZSQg/l0Q9VXQI"}], "size": 1236070}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.42.0_1749221325587_0.5666712488577836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T14:48:45.815Z", "publish_time": 1749221325815, "_source_registry_name": "default"}, "4.43.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.43.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.43.0", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-QmNIAqDiEMEvFV15rsSnjoSmO0+eJLoKRD9EAa9rrYNwO/XRCtOGM3A5A0X+wmG+XRrw9Fxdsw+LnyYiZWWcVw==", "shasum": "0852608843d05852af3f447bf43bb63d80d62b6a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.43.0.tgz", "fileCount": 3, "unpackedSize": 3065065, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDnEZ4Na7qqIQEJdf327lPnuDmKn1lzmG7az/Lac64QbAIgP45joKoHJTruOuyjSWjv8arWYYI3jgT2PV3Msk9dt5M="}], "size": 1236070}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.43.0_1749619394834_0.9368997242047754"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T05:23:15.061Z", "publish_time": 1749619395061, "_source_registry_name": "default"}, "4.44.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.44.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-8541GEyktXaw4lvnGp9m84KENcxInhAt6vPWJ9RodsB/iGjHoMB2Pp5MVBCiKIRxrxzJhGCxmNzdu+oDQ7kwRA==", "shasum": "59b4ebb2129d34b7807ed8c462ff0baaefca9ad4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.0.tgz", "fileCount": 3, "unpackedSize": 3052777, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAvehcufYMqbLdZiv7IPWMHut4XF8PDDUPQYFdSCOM3mAiAvTB7Ccia04p/qAbb5pr/Bv8WdejKBRU+CwUE6VZ2IMw=="}], "size": 1231371}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.44.0_1750314211937_0.7143568192447782"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-19T06:23:32.202Z", "publish_time": 1750314212202, "_source_registry_name": "default"}, "4.44.1": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.44.1", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.44.1", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Hu6hEdix0oxtUma99jSP7xbvjkUM/ycke/AQQ4EC5g7jNRLLIwjcNwaUy95ZKBJJwg1ZowsclNnjYqzN4zwkAw==", "shasum": "a3dec8281d8f2aef1703e48ebc65d29fe847933c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.1.tgz", "fileCount": 3, "unpackedSize": 3052777, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCEw0WimsgiHuB7D1ZZPDl/xw+Rs/r2XJgqpV5gxXq9rQIhAN0hd9Z8W3lsTc/m/N+A/iv4VRgWFKAO9W3pqLC68AXg"}], "size": 1231372}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.44.1_1750912489894_0.6871697982853147"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-26T04:34:50.213Z", "publish_time": 1750912490213, "_source_registry_name": "default"}, "4.44.2": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.44.2", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-evFOtkmVdY3udE+0QKrV5wBx7bKI0iHz5yEVx5WqDJkxp9YQefy4Mpx3RajIVcM6o7jxTvVd/qpC1IXUhGc1Mw==", "shasum": "dc43fb467bff9547f5b9937f38668da07fa8fa9f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.2.tgz", "fileCount": 3, "unpackedSize": 3024105, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCg26xiokeB7XRzqNpGOhiJtQa4AtpdW2rT3Nq3GllLJAIhAKHi1A6/BS1711g7FEF5poZA2XBmuShBmTEdIUUbrqHJ"}], "size": 1220332}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.44.2_1751633801026_0.27411884274145737"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-04T12:56:41.304Z", "publish_time": 1751633801304, "_source_registry_name": "default"}, "4.45.0": {"name": "@rollup/rollup-linux-s390x-gnu", "version": "4.45.0", "os": ["linux"], "cpu": ["s390x"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-s390x-gnu.node", "_id": "@rollup/rollup-linux-s390x-gnu@4.45.0", "gitHead": "b7c7c1159f70ebe8ad6f94c942ebab2fa59c7982", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-2l9sA7d7QdikL0xQwNMO3xURBUNEWyHVHfAsHsUdq+E/pgLTUcCE+gih5PCdmyHmfTDeXUWVhqL0WZzg0nua3g==", "shasum": "55b0790f499fb7adc14eb074c4e46aef92915813", "tarball": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.45.0.tgz", "fileCount": 3, "unpackedSize": 3024105, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCJHuhx3cyjeFzggwcWPzffQqjnHWHSqeVgNnfwEc+3NwIhAMoOkBRMofIsROIcwZxwyZvHOsNhIwUfrt8g87yR3XBB"}], "size": 1220332}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-s390x-gnu_4.45.0_1752299677536_0.6456197917731656"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-12T05:54:37.766Z", "publish_time": 1752299677766, "_source_registry_name": "default"}}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "description": "Native bindings for Rollup", "homepage": "https://rollupjs.org/", "license": "MIT", "maintainers": [{"name": "lukastaegert", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "shellscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "readme": "# `@rollup/rollup-linux-s390x-gnu`\n\nThis is the **s390x-unknown-linux-gnu** binary for `rollup`\n", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "_source_registry_name": "default"}