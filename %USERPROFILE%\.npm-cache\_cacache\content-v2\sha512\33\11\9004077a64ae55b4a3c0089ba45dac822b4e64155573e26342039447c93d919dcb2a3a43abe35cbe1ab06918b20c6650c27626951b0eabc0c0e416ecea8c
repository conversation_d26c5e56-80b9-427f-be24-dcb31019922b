{"_attachments": {}, "_id": "vue-draggable-next", "_rev": "1496185-61f32d694e4a47a13a88f0c9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Build Draggable component using vue 3", "dist-tags": {"beta": "2.0.4", "latest": "2.2.1"}, "license": "MIT", "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "name": "vue-draggable-next", "readme": "# vue-draggable-next\n\nVue 3 drag-and-drop component based on Sortable.js\n\n[Demo](https://vue-draggable-next.vercel.app/).\n\n## Installation\n\n```\nnpm install vue-draggable-next\n//or\nyarn add vue-draggable-next\n```\n\n### Typical use:\n\n```html\n<template>\n  <div class=\"flex m-10\">\n    <draggable class=\"dragArea list-group w-full\" :list=\"list\" @change=\"log\">\n      <div\n        class=\"list-group-item bg-gray-300 m-1 p-3 rounded-md text-center\"\n        v-for=\"element in list\"\n        :key=\"element.name\"\n      >\n        {{ element.name }}\n      </div>\n    </draggable>\n  </div>\n</template>\n<script>\n  import { defineComponent } from 'vue'\n  import { VueDraggableNext } from 'vue-draggable-next'\n  export default defineComponent({\n    components: {\n      draggable: VueDraggableNext,\n    },\n    data() {\n      return {\n        enabled: true,\n        list: [\n          { name: '<PERSON>', id: 1 },\n          { name: '<PERSON><PERSON>', id: 2 },\n          { name: '<PERSON>', id: 3 },\n          { name: '<PERSON>', id: 4 },\n        ],\n        dragging: false,\n      }\n    },\n    methods: {\n      log(event) {\n        console.log(event)\n      },\n    },\n  })\n</script>\n```\n\n### With `transition-group`:\n\n```html\n<draggable v-model=\"myArray\">\n  <transition-group>\n    <div v-for=\"element in myArray\" :key=\"element.id\">{{element.name}}</div>\n  </transition-group>\n</draggable>\n```\n\n### With Vuex:\n\n```html\n<draggable v-model=\"myList\"></draggable>\n```\n\n```javascript\ncomputed: {\n    myList: {\n        get() {\n            return this.$store.state.elements\n        },\n        set(value) {\n           this.$store.dispatch('updateElements', value)\n        }\n    }\n}\n```\n\n### Props\n\n#### value\n\nType: `Array`<br>\nRequired: `false`<br>\nDefault: `null`\n\nInput array to draggable component. Typically same array as referenced by inner element v-for directive.<br>\nThis is the preferred way to use Vue.draggable as it is compatible with Vuex.<br>\nIt should not be used directly but only though the `v-model` directive:\n\n```html\n<draggable v-model=\"myArray\"></draggable>\n```\n\n#### list\n\nType: `Array`<br>\nRequired: `false`<br>\nDefault: `null`\n\nAlternative to the `value` prop, list is an array to be synchronized with drag-and-drop.<br>\nThe main difference is that `list` prop is updated by draggable component using splice method, whereas `value` is immutable.<br>\n**Do not use in conjunction with value prop.**\n\n#### All sortable options\n\nNew in version 2.19\n\nSortable options can be set directly as vue.draggable props since version 2.19.\n\nThis means that all [sortable option](https://github.com/RubaXa/Sortable#options) are valid sortable props with the notable exception of all the method starting by \"on\" as draggable component expose the same API via events.\n\nkebab-case propery are supported: for example `ghost-class` props will be converted to `ghostClass` sortable option.\n\nExample setting handle, sortable and a group option:\n\n```HTML\n<draggable\n        v-model=\"list\"\n        handle=\".handle\"\n        :group=\"{ name: 'people', pull: 'clone', put: false }\"\n        ghost-class=\"ghost\"\n        :sort=\"false\"\n        @change=\"log\"\n      >\n      <!-- -->\n</draggable>\n```\n\n#### tag\n\nType: `String`<br>\nDefault: `'div'`\n\nHTML node type of the element that draggable component create as outer element.\n\n#### component\n\nType: `String`<br>\nDefault: `'null'`\n\nIt is also possible to pass the name of vue component as element. In this case, draggable attribute will be passed to the create component.\n\n#### componentData\n\nType: `Function`<br>\nRequired: `false`<br>\nDefault: `null`<br>\n\nif you need to set props or attrs to the created component.\n\n#### clone\n\nType: `Function`<br>\nRequired: `false`<br>\nDefault: `(original) => { return original;}`<br>\n\nFunction called on the source component to clone element when clone option is true. The unique argument is the viewModel element to be cloned and the returned value is its cloned version.<br>\nBy default vue-draggable-next reuses the viewModel element, so you have to use this hook if you want to clone or deep clone it.\n\n#### move\n\nType: `Function`<br>\nRequired: `false`<br>\nDefault: `null`<br>\n\nIf not null this function will be called in a similar way as [Sortable onMove callback](https://github.com/RubaXa/Sortable#move-event-object).\nReturning false will cancel the drag operation.\n\n```javascript\nfunction onMoveCallback(evt, originalEvent){\n   ...\n    // return false; — for cancel\n}\n```\n\nevt object has same property as [Sortable onMove event](https://github.com/RubaXa/Sortable#move-event-object), and 3 additional properties:\n\n- `draggedContext`: context linked to dragged element\n  - `index`: dragged element index\n  - `element`: dragged element underlying view model element\n  - `futureIndex`: potential index of the dragged element if the drop operation is accepted\n- `relatedContext`: context linked to current drag operation\n  - `index`: target element index\n  - `element`: target element view model element\n  - `list`: target list\n  - `component`: target VueComponent\n\nHTML:\n\n```HTML\n<draggable :list=\"list\" :move=\"checkMove\">\n```\n\njavascript:\n\n```javascript\ncheckMove: function(evt){\n    return (evt.draggedContext.element.name!=='apple');\n}\n```\n\n### Events\n\n- Support for Sortable events:\n\n  `start`, `add`, `remove`, `update`, `end`, `choose`, `unchoose`, `sort`, `filter`, `clone`<br>\n  Events are called whenever onStart, onAdd, onRemove, onUpdate, onEnd, onChoose, onUnchoose, onSort, onClone are fired by Sortable.js with the same argument.<br>\n  [See here for reference](https://github.com/RubaXa/Sortable#event-object-demo)\n\nHTML:\n\n```HTML\n<draggable :list=\"list\" @end=\"onEnd\">\n```\n\n- change event\n\n  `change` event is triggered when list prop is not null and the corresponding array is altered due to drag-and-drop operation.<br>\n  This event is called with one argument containing one of the following properties:\n\n  - `added`: contains information of an element added to the array\n    - `newIndex`: the index of the added element\n    - `element`: the added element\n  - `removed`: contains information of an element removed from to the array\n    - `oldIndex`: the index of the element before remove\n    - `element`: the removed element\n  - `moved`: contains information of an element moved within the array\n    - `newIndex`: the current index of the moved element\n    - `oldIndex`: the old index of the moved element\n    - `element`: the moved element\n\n## 🌸 Thanks\n\nThis project is heavily inspired by the following awesome vue 2 projects.\n\n- [SortableJS/Vue.Draggable](https://github.com/SortableJS/Vue.Draggable)\n\nThanks!\n", "time": {"created": "2022-01-27T23:40:25.653Z", "modified": "2023-07-28T00:26:36.996Z", "1.0.0": "2020-08-14T10:15:27.396Z", "1.0.1": "2020-08-14T10:20:21.791Z", "1.0.2": "2020-08-14T10:21:06.282Z", "1.0.3": "2020-08-29T08:11:37.427Z", "1.0.4": "2020-08-29T09:46:15.438Z", "1.0.5": "2020-08-30T14:37:49.961Z", "1.0.6": "2020-09-19T13:44:05.634Z", "1.0.7": "2020-09-19T14:06:49.360Z", "1.0.8": "2020-09-20T09:27:27.833Z", "2.0.0": "2020-11-07T06:05:31.914Z", "2.0.1": "2021-01-09T10:25:10.459Z", "2.0.2": "2021-08-16T17:43:34.781Z", "2.0.3": "2021-09-05T11:16:48.852Z", "2.0.4": "2021-09-12T07:14:27.831Z", "2.1.0": "2021-10-10T07:43:59.234Z", "2.1.1": "2021-10-19T16:27:44.642Z", "2.2.0": "2023-06-03T08:13:37.075Z", "2.2.1": "2023-06-20T09:10:40.255Z"}, "versions": {"1.0.0": {"name": "vue-draggable-next", "version": "1.0.0", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@types/sortablejs": "^1.10.5", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.6.2", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0-rc.5"}, "gitHead": "e9a15c9ca306333ea50be8e206616d1be52f8145", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-efNBPdlWsdfd72sOXok2wMIzB8yetcu0mzhmDCYDIniBor2sq9qOanHm2+c67A4ywlTKb7aLgZ74xBzOXaeNBA==", "shasum": "19a6109db631db5c514243742f13adb9cb16f66c", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.0.tgz", "fileCount": 36, "unpackedSize": 709647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNmQ/CRA9TVsSAnZWagAA1NAQAJ1DKQyYn1bGYzAF13YG\ntsR5fEI5q8F5v8bqSV7r+pCBSnCuotENDiruQRnT6MnZUzEd4WhNw1ne4Kp9\nZOM+dIGNTnzF5tEakFV0Ga/Aa6t64VvlBc9GwjhEs3BOYhhrrb2z9x0/0YpP\nssAqaOEQEAjUhruIfPvSD7u46j+kmL+6Q+n/UAsMjZ7oNRZZXTPXGsbe/CnD\nHg7dG0aUa2zVa9WgImD42b5cSffv1e2gAb6sdEri1HbFRxc0mCPiyI6o1HOX\n7FMe7gzwL5FGIMcBH1I9nyAHoZC4aF/gjSpo5RVGejGTZpZ++Lg5AcMG3WSG\noEy7CbSjfg47ihG42FZg/5Oc3dVT03FRYw5ROkvWXb/U9oGcjlmjuhmGbU1X\nja0BVo2YmzOQHH+nxFlv0JffKR1TonjVuXVYBWefcBtMlcIGeeqDc0e8uzm7\nLR6JLJYKyohy/kIvBY+quW1BbGdoY588nKOfFVgaKaPtPNDDr4an5MV8W/dx\nOb3M1osAjhIkEc7lv7xCWRsPjCqsi3sU2V85AFg4yYZRYlC+bIXU+6jUHbm4\n0XWEkPv8Ecky4zlRwhV6q+EJxcvi66C7Q3rGuQeMd1HbK6/IWBX/ukL50c0Q\n9ja/2m+qGvhacrYh33hDScg7+gFCJSWni1cc+BV6jnPWiVqBRUG6dbq3N389\npO6F\r\n=aWgR\r\n-----END PGP SIGNATURE-----\r\n", "size": 193165}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.0_1597400127251_0.565252342652105"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:32.254Z"}, "1.0.1": {"name": "vue-draggable-next", "version": "1.0.1", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@types/sortablejs": "^1.10.5", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.6.2", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0-rc.5"}, "gitHead": "2888dc599304351c95897d488bdcccb4a33c42d2", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-pJMANGD3Ri2DonlOWV5Awl1htKGAKHTKdnCiS5hF3GJl04HkAwnMtfXEhH4XEZuU9U/T6eNeaNDIlOxA/ofKOg==", "shasum": "cecc8369739f3d287848f622934b8b6e25e3524e", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.1.tgz", "fileCount": 36, "unpackedSize": 709880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNmVmCRA9TVsSAnZWagAAFFcP/05e+dWsIcQwv1LwkpUx\n5NOquIB/24yCD3ly5nosu3IuDu3cCiaip5TneBG0hjkInC29YNl5gOq5y3aL\n4LYHHF29fovDRN2mZ1kX0glpWrzXnlNlMlAMfXPLnMSOZ1As6dmKJM9cEmKO\nRgRAFsokBDqLI9aLzl6usxrLNebPptMeOcExw9tBDAk+KtjfveiKMqKRitF4\nQXlte5KFSjEetjEkBLZ9uMtM2+9wRWN1r+12CaCQPopoO/y+tEMlDnWcJtcK\nUtHkc42fh/0S9HbtaF3oOsKZ4rANrPmTg+Y8Uqc8vku4mv6CWCYEEpFOO2CA\njfq2amW9h+8r3CsMKzTodlFsD0t2UZVBLgqA0nglJRPHFrsyoGSQx7cpx/Si\nywEeTcrtJ6l6Oyfj1X+wRKXUQA6E9E/UjwYimnXfRcb8dEI0KakH+gNyT6Wk\nhgQZSIUTxV1xA1O5Y2CwBmVZHOviAk0FA7Qg89+Oa2+7lvyTN1zllb8jxsT0\nRcdxkOHVJxRVNLsrQZv7Y7q1UFm7kAFtS53zQ8zFbGdVjJBLzibYc4Y2mNfX\nUDdChEY2isUmNaJis83xu6JK6SdyU02EHcvTH7P9NDyyiKbICyFFfPzQHigX\ngoGKOu1M/4oby/aubqFox0jbmdJsVxVKAXnj0oCvWcD3oJwLAICKaMNpgFYi\n8viK\r\n=iCFW\r\n-----END PGP SIGNATURE-----\r\n", "size": 193218}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.1_1597400421431_0.5348560035623713"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:33.748Z"}, "1.0.2": {"name": "vue-draggable-next", "version": "1.0.2", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@types/sortablejs": "^1.10.5", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.6.2", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0-rc.5"}, "gitHead": "2888dc599304351c95897d488bdcccb4a33c42d2", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.2", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-Ddh2JDAKoXr1RZFYvbkSaZvP+/bSJNCoBwwOOc3oYyaJmmpC/zx+bBxfxvLKdKOLePVEweZTTfZ7THlYTfKtOw==", "shasum": "b4dd7435155b00bfc092bbd63acbf9bf25ec1d05", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.2.tgz", "fileCount": 36, "unpackedSize": 709880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNmWSCRA9TVsSAnZWagAAC7sP/0Vi9d63NWnlHtSRSHKQ\ng9SZK2UK3BJxghGGXpi8v2SgexAUSZ5hU8w8EBbLFkWLRErzhY1sF2Jw0wY7\n+jzkQ3YD3+Ykx7Atn+eOLpkIPSjLy9DS+67DdWk4VIEjQBBA8CVuCTWayqaM\n00kZ0WiQCJ+Br/DSPTwb/UCTnFMI3FUfodJaXcK8V98gDFOq2Qfknmr47yZk\n+WVHWu36PLLM2EaXEB1NX5XTEGW+sO5IDjgJtyKl9C+fz9Z+0MLXMWe5Lq3M\nq1aFypfrqaaImORmDcT4842gZgdciq8lNDSkBj57O+ngMqAYH5tyPRH72MSY\nDCywRt9iG9YUV8OrlNCG3GwbiXIXBDUbmdm52LvNLa08vQfgOZ68SQANrBLa\nVkFyEJw65QibJRRjw4Exrihli7uiFTPTlpjD8QcXVBf+Vzqe9CW3LhecAmVy\nOLeIil5LAm5qsXYki9ENScedSAeaE1Cil2rQ3jQDgbdTtm7zuBbvvJ53E83i\nrQ3Cb1i8K7F5owQNULjcG2o/Gfr893f2HPAouUybWfrCUPjv6tzwoAADPAAV\npk548tnyC2sjYyw8KwurLtV71msO8sBsVr7q8paNvEIRapUn5/hn7NkIP8SC\n1Dgc7fkb+wENQLVDpLPJusfOrH1jAcNzacS5KsgOs8oXhF0oSfJspj+rp2xh\nlNzr\r\n=dP9u\r\n-----END PGP SIGNATURE-----\r\n", "size": 193219}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.2_1597400466056_0.15717907882344528"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:35.264Z"}, "1.0.3": {"name": "vue-draggable-next", "version": "1.0.3", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "cross-env NODE_ENV=production vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@tailwindcss/ui": "^0.5.0", "@types/sortablejs": "^1.10.5", "@vue/compiler-sfc": "^3.0.0-rc.9", "cross-env": "^7.0.2", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.7.5", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0-rc.9"}, "gitHead": "e978b6f492ba67c0c83e9de016e41883db8202a7", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.3", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-1RPlFs+bEz4jm8GEc156iL4ZSctMiVhW1LWs+K6AGYV7TB57bLohoG9UBEdXGBKn4ixA0XuDuUQSxrVaHirGAQ==", "shasum": "987d3ab8bf134929c6074d71c551966de1d6efd8", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.3.tgz", "fileCount": 39, "unpackedSize": 738672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSg25CRA9TVsSAnZWagAAwJkP/Rh3aSJxo1jFMXFvrq/R\nHTsq5Bb96/bH6JmE7+caj/HOacvlRdT/jrN93Uwbnr0U/i+ycIsQ5IkCDGjb\neo78+Syijum//OyeuJ7VnkLQvcPWt/h6SDyU3FvhuBpFK0toJM61iCG3oaYJ\noSTb+iHnc4jjU2/ZNp8KXfaJ4t8w/NCfgFJM6alla7Pa2NFnJByyCdzZeAwZ\n4vj+lrffnK3FirUSSw8XckkfnDI8icq1NxkyNSNLiFMaskhkgqHblghzVMVV\nIUCPQzs+yUwgSxga/9XgLa0KXhxkY8vxc+3NxiQO7PMzgHeZg5dxEofVdpuO\nlkZfHybwlO395qAL1JZJzW35BARnA/uTYi43u6EJuA3A/PIi4FPNpLWJBKQD\nKuNvUf3coUfYtYOcxXEkge1C2K5K/lhGRFTKj1BuCBZ284VIGU43tLi2eIaO\nmS913s6kqlyyO+1HDxQFl5efFtbvm7DqVex0W/ftvarcAXcHh5MR2e9OTIuD\nytsnTtAIGdGod05yWbvR2NqgBTlrugdwM9DlA0CPxKpANYoa9pwmnrp3GsW0\nhymWViWgKO3OnKvkW3IxUZguwCWJDkL2gbtknFFqHrdLuNUf31mbyXhxlX5A\nt7N0hLtCC6PAXOW9xELkRHnTWrpLPGbGk2JEFCTLOGJaWh75Rg61M/i5su6K\n4mLY\r\n=6DT2\r\n-----END PGP SIGNATURE-----\r\n", "size": 198141}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.3_1598688697192_0.17470747138102127"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:36.736Z"}, "1.0.4": {"name": "vue-draggable-next", "version": "1.0.4", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "cross-env NODE_ENV=production vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@tailwindcss/ui": "^0.5.0", "@types/sortablejs": "^1.10.5", "@vue/compiler-sfc": "^3.0.0-rc.9", "cross-env": "^7.0.2", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.7.5", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0-rc.9", "vuex": "^4.0.0-beta.4"}, "gitHead": "611bc37bb3b3038ea90cf15889e20ea0a6be4142", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.4", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-cmGHLNX/6r/DeJQTKaIpToT63e7lSPw7HhLqwzgPKUXuwLkzJ93Iznkfpcg0JpMbjfh4L1T7ifzYGnaS+OdMwA==", "shasum": "05b735a17bb44d8855ebcbd22280ec18af5109ad", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.4.tgz", "fileCount": 43, "unpackedSize": 756969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSiPnCRA9TVsSAnZWagAAJ7kP/3OTmsw3faHg95UZgKjk\nUs+LK/ESDXgd+0l7d+2IMTyg9QmfZK5aEE+RsDezWhvfeT6HkTBhoxIC0wDV\nS0gcTwb6gAzXyPJ/VgA0hq/gS3eYhyZrppyhuy0B4qstgDG/8U58Zxo/YwdJ\nCGyTdbTPmXn96g5xOT9GEbqSHivm/9TySAszqOKxONVV/xZOvXQRLAwfPb7S\nSgdxlR1Hz3yU5CWMxmSnpNMfej54Z87FLoJfNsKOjEMB0ElGoZNhqSn0bPTT\nrvggmj5mQRK9qdR9yl0oqulP9zoDGRAItvKgiuLo6fibp4R6LOD7GdmTKBD1\njlzrRnySfylrLhj8fM3LUt3+KxnjlbkopWKK5gaJmKLJNNNJo28nVb5o4XVO\nUFM9JQbD00rl5w9FGlMcyl3LGXLN39dABxBiGp0goJkyCIcCe53FdGsHseY1\njidVdRaaK/9zZ8qGcb8HsaohonQ4X5fyL2svoQVLxaWpXxyzsaa+P8SoQzer\nDwDbuEoiJ6cfi+KiKce8JKYUk5J5xv8YMUWbQHvV07O3jdEl8c2qWXZyu7Sq\n3dToIzPPIEEStmUoMHJMQaeWuqO/raCZYlDtFZNSHF6PMmkSE3OEzV5A17P0\nBSG8Vkk7WHmMqyER8L8XTW0Pwk1kiv4wcKEswehvrBVar5t0Sfr2qeunrX7Q\nmyYT\r\n=caGn\r\n-----END PGP SIGNATURE-----\r\n", "size": 203275}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.4_1598694375295_0.21340765998433997"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:38.260Z"}, "1.0.5": {"name": "vue-draggable-next", "version": "1.0.5", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "cross-env NODE_ENV=production vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@tailwindcss/ui": "^0.5.0", "@types/sortablejs": "^1.10.5", "@vue/compiler-sfc": "^3.0.0-rc.9", "cross-env": "^7.0.2", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.7.5", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0-rc.9", "vuex": "^4.0.0-beta.4"}, "gitHead": "6d9cb5308e87e4810ecef06e00ff21a6f2465d8c", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.5", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-YeR0INdg7Kgp13qCUrFsF+q7Cy+E6I7dyFhfAtRkCGb1gaSh6WdQwEa3k33HR/kjB0rAXWmoxcE7T1d+N3eGcg==", "shasum": "ec872539c6d5ef383a5b17e5fe153eb5741be40f", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.5.tgz", "fileCount": 47, "unpackedSize": 763972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfS7m+CRA9TVsSAnZWagAAqXYP/0ld748Vwbw/Hb+9Sfao\nau7nvK5/Jua+gsbw7sKqknMPGffPj9lOtG1IDHENEE597x+k86nfRu8erTuv\nZffmwHL0Lse9OmSvU53zvMbYPb2S+6nD5M2UFgLuzic3QeQpDAMTdDOaP4G0\ngCoRgA025Qt8ivhCZGrMGpleSN190RaRuIAeuDvlAJ+XrsTeFRNVTJZoUld1\nvza3KDqF0ugqfAi4MxOfxBmi9254c/f1q5Nedc9XpG1He3DHDEr7zGOXVoUc\nupVmB4mHeDy7lOUGNbSIZos1Hfzp4XTOFOPglf7ruc0k4hh8SeDv0mk2RDad\nwkW7DIS1DrREqQEZMOtD3W8Xk71dvK3dr7X/KG9nZRnqr9pgQPfdexqv5qAf\nRw5klKbzyPUoJ6l4+VZVkz7MYFM+AGV8zHX10cPSLKXp0XvNi9bh0TzqAMkW\nfG8beRXdK6ex69/vYJvfwH24U5l8+3PKEpn8KlubRZ18TO+nqyU68UVOTVd0\n42sXY6KxzpjEs/ZoQHpXWY9iQnVqf/stko/TlP0HmV21UibObODeulpWosu+\nyh3jrzppadB6CJryHAcqQgW3tdaqLjUt9eiptgdGjIWCGP9XnoCHh/4rKl/R\n+XTyvGs8NRv/4dneFOF+PyeBg5LSuB66/N8HRW5/q1KJGk/afBNmEckeGBY7\nN2dJ\r\n=TZY7\r\n-----END PGP SIGNATURE-----\r\n", "size": 204621}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.5_1598798269773_0.1515483600766041"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:39.734Z"}, "1.0.6": {"name": "vue-draggable-next", "version": "1.0.6", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "type": "dist/vue-draggable-next.d.ts", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "cross-env NODE_ENV=production vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@tailwindcss/ui": "^0.5.0", "@types/sortablejs": "^1.10.5", "@vue/compiler-sfc": "^3.0.0-rc.9", "cross-env": "^7.0.2", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.7.5", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0", "vuex": "^4.0.0-beta.4"}, "gitHead": "d1e7c99f2b08253f90ef477fdf876adfd9abf998", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.6", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-/CZuIjJqcfn1jnWY8BhMuHSzpYtG8+70BhdKT63++EUOLISkcRpt8AHjVwKVmKqtd6jWU3LYwhrBEJXlW5CQ9Q==", "shasum": "838a80b6a08f2b0c5377cc510b9e4d3620bff3d6", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.6.tgz", "fileCount": 45, "unpackedSize": 731088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZgsmCRA9TVsSAnZWagAAN3UP/0ttDBhMoizJSs0V0eoa\nuXGvdFWutyBHafEADFxsJ37pI71kj6YipwkWCkMCoNkxfjo7kbPNMHnimQJZ\nM8g1JkFHm6doABURuRNd+sF8/QJJ0eOsxgblz7CNS4U7pvC3Vhhm/+df8WYz\nj3DisAek1LbH8h9PHjfO587No/D74YaV5f4C0EF264RuI4SDEC4R/bbqBHrJ\nul1geE44GVJpBbKzwxL4VO6+UUzOw0J8ivGgb6Or0NIRehCIttiB2FUtAdaw\nOYKqmj5rSuK0i/7Pe9gdAyY5nl93T5P28qdqsLlRl2LNhSd7/s1S1m5Mvf+x\nKdqg20uy1WeFg0yciZZju2L4t1dHVEpIpdQ3aKHYoCkjbBWo4Ftx7WIOLgdp\nHu9A5UvvB9iEIN/Lm1VqSf2XblQIk9FJD21y/u7oZXq+M0HWN33mxEkX6Miu\nH/EDGZo33h2tDXn0Zd8+TkvzQBJn2nboG8nsszoouhOe76sg90Nnj1L8yWON\nuNLitvOr7LnzonJ04C5rCXb6Cs0p6gIqiVwr7UjqAZXa1mvGB4NACIXsv0nm\nbxlZr++7oPdSjq3DhDmaSv4pC4IxNWirkLhU0C9ScNrnL4fmNa4304Fp9TeF\nRifjUrBEArKMiyBrFK4tBbDzBQ427FvzHPy5fMj6DoaXJGQr6QqcL2c6LXth\n8yHi\r\n=TTS1\r\n-----END PGP SIGNATURE-----\r\n", "size": 201954}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.6_1600523045458_0.16410758889963017"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:41.200Z"}, "1.0.7": {"name": "vue-draggable-next", "version": "1.0.7", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "cross-env NODE_ENV=production vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@tailwindcss/ui": "^0.5.0", "@types/sortablejs": "^1.10.5", "@vue/compiler-sfc": "^3.0.0", "cross-env": "^7.0.2", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.7.5", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0", "vuex": "^4.0.0-beta.4"}, "peerDependencies": {"sortablejs": "^1.10.2", "vue": "^3.0.0"}, "gitHead": "45601ffb26b0e0fb738df43685036de80062c595", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.7", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-1TU53jOaNs6PVCg+Va2WsaZ7AW5ifGr3Q+4WzOnBy443bYCznF2PN9KUgWSi4ibJ+wxAoNfVhQ9yTuyHg2hNBQ==", "shasum": "5dfad9385a5f6a8f7dacc3b41bb9958c408a888c", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.7.tgz", "fileCount": 46, "unpackedSize": 736799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZhB5CRA9TVsSAnZWagAA0eAP/0OBABqyRziedxyyzHAB\nB2saqqznZ4gjUrx2HBKtMFypKhxMyZyWXCm7U57sJduehrF0vxmnuUQnfesP\nxD9UdJ003liqYzJWIkb77aZnlJMpFirmiPgmNcfhVKJdYR1FjP4+DijvOeNG\nchykBswcOEMzein7AjQ8ycY1thBDEUVa+ucxZvf8UIIKzvlnlZ1lly47LJXh\njkiaEwkIz8ftDgYmQSRaMHBNztRHq6bzkUpEndaQfTtr0SLkRNUs/Rb8sxsP\nT0oWTaaRQwbGVoxufSrKVaPVRb7N0Hxw5Rc7OvawKG+oRB84TmXz58khEoLR\n4aIq1wqZlY9aXZW22mdYSgcqszS7Rn2P66NvdawwmxodBBylY2Nva7HHVqUF\nvlm+C1aQwkfGO1RCIY5WWZgxNWKmjtOs3W1UdxtbaiJ0TO3jQ8ueVzNZGIGs\n19nnlOFCv1s24NFmK7pIKYlyZT+7s9axr/y0htw68VLcLp8YmdSs8eLbPsam\nrHn2oFkhrxbyipmLBaT8lYnCWnqunujRQlRftCHxmGj028J4CgT6ziGgRllE\nZXE9DCXjfH1uZcOXhen6TDco8yXqx7VxiVINbrBb8YSm/vPB69uhWS2LHAmE\n19TAKemJZm+v9TnrBavOy7ktdbgmXZWfA091N/XT2X6YVfjgCurtrhKnLAjN\nVjuG\r\n=B/xG\r\n-----END PGP SIGNATURE-----\r\n", "size": 202126}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.7_1600524409158_0.88034939434055"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:42.664Z"}, "1.0.8": {"name": "vue-draggable-next", "version": "1.0.8", "description": "Vue 3 drag-and-drop component based on Sortable.js", "main": "dist/vue-draggable-next.cjs.js", "browser": "dist/vue-draggable-next.esm.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "scripts": {"build": "rollup -c rollup.config.js", "dev": "vite serve", "start": "vite serve --mode production", "demo:build": "cross-env NODE_ENV=production vite build", "release": "bash scripts/release.sh", "build:dts": "api-extractor run --local --verbose", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=10.16.0"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "dependencies": {"sortablejs": "^1.10.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@tailwindcss/ui": "^0.5.0", "@types/sortablejs": "^1.10.5", "@vue/compiler-sfc": "^3.0.0", "cross-env": "^7.0.2", "pascalcase": "^1.0.0", "prettier": "^2.0.5", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "tailwindcss": "^1.7.5", "ts-node": "^8.10.2", "typescript": "^3.9.7", "vite": "^1.0.0-rc.4", "vue": "^3.0.0", "vuex": "^4.0.0-beta.4"}, "peerDependencies": {"sortablejs": "^1.10.2", "vue": "^3.0.0"}, "gitHead": "89177dc0574f18977bda6c059d1153627c232a12", "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "_id": "vue-draggable-next@1.0.8", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-c15YO8HC2Lo2+rWXNoLDwiAFzf4pY5L4ESJsWp526qWeLAf6WjIXYSchmsO6AD0ozFIc+sp6B9zKafpFotxNIQ==", "shasum": "89a8b347422d20f694f977a125b9af2f67e85b99", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-1.0.8.tgz", "fileCount": 46, "unpackedSize": 737204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZyCACRA9TVsSAnZWagAAcO8P/0ClOwCk6MBZj9XZsLEn\nIQtnps2PwamT9vLYiC/E7KhGbkGAX+u5S4y85ZFigdoadSsseNunJFxboKHM\n/0cHLDv87b7cuUqbO0tRYgZiJ8gxqwkLiesBvfvDNYTDUnMe76ZwgVYvcyaS\ncSMMxSr5DoXZbSHBcivJnwqzPfHZHBR2pY3ZkIsisZ6Lk8eTEApSO17BqdxR\ntAILFXPSLpThr9ChNu7G2cBplvIiQ5I7jYd32gNoEMkRMwIArlw21+BXZX6r\nOlTW+it/b8RUKf6pysdYPIwEANlHOA6iLAgV4ICNG1u7aJ/iQu5SOw4aX2ut\nVTWrJy0swR1+uWi0gOMy58IRneDTDrzlK1QSpFvvdNbpljtPNMN44+T7y8Fb\nFPt9AkJM8VASjcX3X/5a5FKnB+EiUKHdytZvE/R3Fyl5oSyhbQx53EvNJqPw\nLmt48FgoEi0sIlFiuSSqpLOd3ai5Zf8W0Gb+hodpmCPzZe4LCqg3FQtng6M4\n21Fg3QWYCqBmAKWMZOGOWxorybhx1hEZRP2DMQXWBlCU2cz/0qUGK5vgNGmL\niuzffHGyoYPMNFUPZAgCIrGWkYhwGjORHI+3vqo+raySjPddKng7G//RlHF2\nRfKzT06PKQ7dltlOYqcyurOBZ6pd6IuJNrHodQxoB+4gbScMS481+YZq2eOl\nOssW\r\n=DAHd\r\n-----END PGP SIGNATURE-----\r\n", "size": 202224}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_1.0.8_1600594047639_0.5850910733928183"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:44.114Z"}, "2.0.0": {"name": "vue-draggable-next", "version": "2.0.0", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.8.1", "@rollup/plugin-alias": "^3.1.1", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-node-resolve": "^10.0.0", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.6", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.33.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.12.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.0.5", "vite": "^1.0.0-rc.9", "vue": "^3.0.2", "vuex": "^4.0.0-rc.1", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.12.0", "vue": "^3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "gitHead": "d3bf082a2a85b36a3963dffab8227bb91f1f8605", "_id": "vue-draggable-next@2.0.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-HLIjg0Kmeq8DD3LIAe3Ftuwd2ng4OUJ9PLpQX9ckAhyebNpNrKPkbsL7ge0Kpxn7qFsNID+5Cyx5aX9ayf4JfA==", "shasum": "cd56253567af8b9a7b07122e1d5955dd0f1e9fcf", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.0.0.tgz", "fileCount": 9, "unpackedSize": 333635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpjksCRA9TVsSAnZWagAAJ60P+wbhMgtS2kzmbzGPSvIl\nj5xWEaKARhlL/ZOPu+i87jBmEIl/sNeTDQgox+Un1sdtNqXr0DdxXroNd7Mq\nf+y5HLXWJyJHe9vz4L3r160hJ5hrC2+67+NDjW5XMJltSgSfJ3RqutzRjUgm\n+MoWGxf5897fkzF3Y8r9lX7Zn8knS0CcEhsgfR67gLt5cFf6+fOH7gtP7ZGE\njFlhoMBE6Z0FzZgsnyhXJ/mihY98tjszMLyhDPwLNSxCMFu+MTHbrRNDuF7q\n/xVutCqzECG2HXdiguJ8rqAtFrdAUIT2eGe3IfNqqZunUnVsyPXw+D6PrRRc\nrQSWrsw946oU0UOiJZKZBdU08wDqRUzCEe8rqm4BI857Z2TEeNKGrl1PK6eQ\nr8bjMwfykss21ROe+wJslBe/qdrMdz2b5eCpPi6mVicB5mxdyG9FYgs1X9+u\n9G3VDDVI3JLYSyDP/VC6/x8uEH33mkEHptKPeTAlE/mHpfPWR1QL3z/B62Zz\nGka8A2OvnEGggpeOmoed1T+vojpN2Lde3bjYD6+uwkV9xc964uAKIDQnBNMi\nFrs67WpOAO3jcT8jC3TQB+hBNW5Q5ot4x29h/GA/x6IjCSPWgZI79al/gJv4\nBMaBxFfqfpd1ebg8+8GrWCd0naz1WhOtOIV+yN6dyUlsl2BFq7RsIXXpzaJa\nbGOv\r\n=TqUQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 101672}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.0.0_1604729131786_0.367241760500884"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:45.407Z"}, "2.0.1": {"name": "vue-draggable-next", "version": "2.0.1", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.8.1", "@rollup/plugin-alias": "^3.1.1", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-node-resolve": "^10.0.0", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.6", "@vitejs/plugin-vue": "^1.0.4", "@vue/compiler-sfc": "^3.0.5", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.33.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.13.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.0.5", "vite": "^2.0.0-beta.15", "vue": "^3.0.5", "vuex": "^4.0.0-rc.2", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.13.0", "vue": "^3.0.5"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "gitHead": "47209608acf7ff7a93ae0ee21ee65f9126b64c73", "_id": "vue-draggable-next@2.0.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-8+7gqNL0Vb4GpXBTtGQx8yAWhvn8y61aoDgKmZwDu8b5dFB2wlj1r5SWtbqV26rwg6R8FRHVdlo55lwaOA51wA==", "shasum": "bb64efb302c7b8e759fd3f21427871789b624c67", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.0.1.tgz", "fileCount": 9, "unpackedSize": 576052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+YSGCRA9TVsSAnZWagAA5OMP/ipj+D7wESIr1T9Hlpbi\nEHpykCISaayQ4BWt3cncsglPN5U15PxcEUWvm43jiTQOQgek/VQ4lvPtrsjU\n0la6DGJkPpygSUyk12ANeWo6qmjWRF93L/qAB1B3YBDOYm40WYeYynB2jPxp\n1yQIqkP4GzK4u/LdU+CJCIYkl0UQEGbcUTLny3Yc+oN57NL5aXC3SNHSKlZf\nSGUwWlMpYz4EcsKrFMOH6g1kN+xe67ZriM7Wu52acCE9UQzq2uACeXkGRJ0S\ndDBnLrIhKO3Wsm++3F2rd5eRYbUi+NyOk15HbFnenBR5P6HS951UrR1qR+tx\nMygrMFwMyk3aNTth4oYXiFw9vsSl/AwRjYoYySxeK63+Gf24L6yaZ+9Qatkq\nGNcV6QmKnJZopqB37WNSnor529rgCZsVjY3qNgjIG9FxuDNhl3SLIYA+h5e9\nCIlkhGCKq2f+QIP9t8VGfLMXSy5WbFI5wE7F0+Xl2UpjEhAXWkPn84d+BtNE\nEIwOnmZBuv3NsSF3j3IyRiyEsIdDT1MdnG4Bomzr5wd1vvLzAt/nV1XAFgby\nB5aVmpQgMUwzQYgnmBQV7tZdL7W8TVyz4WSEvNShHnyumTMLQ/pqEgm/2Jp1\nLVHN2wc2ZSWG+ImQL2Khfn4y7cvg/ihqICSV1VGiNcYmpQPsG7rajdSr649y\n7OsU\r\n=IDbd\r\n-----END PGP SIGNATURE-----\r\n", "size": 142487}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.0.1_1610187910279_0.5103245307887083"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:45.602Z"}, "2.0.2": {"name": "vue-draggable-next", "version": "2.0.2", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "dependencies": {"vue-router": "4"}, "readmeFilename": "README.md", "gitHead": "57f73369907673e949b1a7cd5a3b352901ff0683", "_id": "vue-draggable-next@2.0.2", "_nodeVersion": "14.15.0", "_npmVersion": "7.19.0", "dist": {"integrity": "sha512-wCi/i/8Hlww9ASaGH6kp6QmSCzzqRP0ykTfDUMJ21L4idV/le2SP/hLEFc1QXdmn3mTwPwS5Z1GQo14nEH+a5A==", "shasum": "e3e7f0bc70afae3955ccceafe06550acfb082008", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.0.2.tgz", "fileCount": 9, "unpackedSize": 584662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhGqPGCRA9TVsSAnZWagAAMKwQAJ9e2ot5wQTcvnb+Iixi\n4alZt7w9mHDUW1inMTviHSvcIJTW4B0OU0axOYzldg1WKTYLyLOouZAcmfFC\nU2B7yIWGuB4vukchXzbpURllWyh6rrUz5q0KHnyPagbHRTDdB7S11ykx1edR\nxQVfJVszW8E5IJmXJJ8ZY1nddQEbBBvLyBdi8q3uMQCSWAXVyDH7naSg7lE1\nMsKDKAPscHksUap0CcSMdK0UOP0MXyD7wEgOw6FstWeun/S4DqMwqFVAUPLU\nrQnjm6TUtoQktze9Gu1qdbFnm31Sz8B3wAz2UqkYBWmkScft5VuYtvtmmxun\n+R5PoVNuecdcku55BkRpnGOtaZPpHlkAHUEhbFOz+2dqQhMlnJ5sUzbPsb49\nXuYfdjxyn33HRuzqxf6inoBk57AHzoCTwfzV+Jhq15yei2xXRBpJJSGDAeo8\nQu8PLw0AS+v5RP3MG7jKOjbF0BH0cjVnKoGDomPcxFkQvPI3rHyYzf3iqdK6\ncIBZr9MPkz2+TgcuS35aZJ+vv7uhXXpzDs5yTo5g/YxhHWNj+cNyMEjS2nz2\nWsRyiDT/zjfDwpjNX2gRmsxUMbyaK4FkejF2WW0HJNmWfKBR/SBXfkoJRAWh\nOphJXIK5Tjq852zQetnhQL9X1sSX9Q6LkwVhhpy2S9MrzLP8UY9Yg5cjy4Qm\nZYiX\r\n=lkP5\r\n-----END PGP SIGNATURE-----\r\n", "size": 143537}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.0.2_1629135814568_0.9995569528942259"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:47.092Z"}, "2.0.3": {"name": "vue-draggable-next", "version": "2.0.3", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "dependencies": {"vue-router": "4"}, "readmeFilename": "README.md", "gitHead": "57f73369907673e949b1a7cd5a3b352901ff0683", "_id": "vue-draggable-next@2.0.3", "_nodeVersion": "14.15.0", "_npmVersion": "7.19.0", "dist": {"integrity": "sha512-364S58rcCuxfn+4kSEd+m4ghv7lTxJ/sYI8y+gYWeVTuBQQXBRTBLmqcIJjYq9Xqj6gEy3Y5EunC+b7yV08Vkg==", "shasum": "75df6f51235c8fca37eef692ab3e5968f19161d2", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.0.3.tgz", "fileCount": 9, "unpackedSize": 584581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNKcgCRA9TVsSAnZWagAAnwIP/21w6J1l9wnVRBPbRDG6\nvNVfZnvps6sn/9O/nqXbGWPWC+sznChBbhgXeV+uvZZwoLYqV5iVJxdpiEmt\nIJVi39ZQIxGbuj1IIzuh16Txrf3TmRUTajY+AQeTyj00vjIGEk25X7zhIETa\n9ybV41xGYsR+n1G3K1na4WI2SSs53wjp0+6NyoOhRd/B716Nf/i9ZWNIsB0P\nbbzGvdCL05WozrjchV7QKeGJ5E0WWOiwT6HSeO2O+in2FTA3p+DBeiF0zrHG\nmurdFPp+7EBpQwaNOrCaOdU/gK7LsfcvvYlyFAFDZvVl68PxNNlXRpmERfda\nNKS4W4q4O4E6sMgDHt+mpduv5OTDi0ESXfJzOiLBvFjZSDlSC4dVAwQdrOYD\n1JdZcXYBCHNBhzywj0Lr6s7JMstpd78tYc+GarIGMm5/atKw1542xEa7KI1g\nMjw0twPVWLCRKo0cthFOAvUWb5frC4HM9WxeNO7TNu+S+w7hZzWi0E4wvLOk\n2FIpB+3UnBeccVdxs+uFrMQ7/PH+QuQof2cVGfVqwmhcAAPexyOFjol7tztK\nyUGJCza3yZgnIKRPF3Jbv9jEm1WKeMmnHOsVZNbM12B6T06b+idb5yn3g/yJ\nJGQG+MTpclNf/7/fxiE4cHZGpvAqmjI++ceq9FRvs1LYKpHdIynqjTablwOO\nCJFk\r\n=5NGv\r\n-----END PGP SIGNATURE-----\r\n", "size": 143527}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.0.3_1630840608685_0.7192510968213925"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:48.547Z"}, "2.0.4": {"name": "vue-draggable-next", "version": "2.0.4", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "dependencies": {"vue-router": "4"}, "readmeFilename": "README.md", "gitHead": "4c5175d7cd4e73bc5ab2d647bc827252bd5faf37", "_id": "vue-draggable-next@2.0.4", "_nodeVersion": "14.15.0", "_npmVersion": "7.19.0", "dist": {"integrity": "sha512-fMOLO7wzZx7rXHG+xXmqzt40Gz62XFcvlCzinGSubIkfutRBpc3ZeQ9mAfLg7170fTGQZFi5UOSPvTpPDf1gvw==", "shasum": "206ed2f4ad316f7a2b5b24ce4ef1914a32d3df31", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.0.4.tgz", "fileCount": 9, "unpackedSize": 583803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPajTCRA9TVsSAnZWagAAfM4P/0Ri7mp/tEdvsh5iC4+Z\nzvKWYlKyW3VVgYqZP9nSHCKxwbbLh7nrWxLlMLn0qJaH+HkJkzeHfNKhEz0j\nh4/nfpwPrdvq+DJKansVG0Z8yrU8o2wiqDjOusAEgEGqZM+kvKxElwCOGjGK\nn6EFLBBaEs7St4WIClLw+N2P1sc3LwYjK8MHrynWycE69SRm+RDdJ3qrXS8s\nku6drn0N2gpnfRq1SnDWa91ROY6sv+DjsdJ1m84t50zh4ZgrIklxI3D4jg6X\nn3Ayc9/CzAUc3ubodZl3uMosRyfq+ihzJLDh514hRe2laGOkKqIjVfVbQbFZ\ndtrlIvt68/XS6xz/DQ8p4ThciyZMirGlZvAY2MN9Mo2wEL3sw/cQxC1uulFK\nLpqWTx2RkPS6VTFbZzBGfsKaCe8oGILy6f3DBbO4ByJkQkEyu2ZsXZ0aboUp\nZaS/L/+PM2jAdfQEkeeKnjPITyhbyn29eJdkcZ9Ho3F67rg7a7MdunQtP4PC\nDnXffIM8ig2f01zrV1S4p63BNyzpA2ycZzJS3dE14azc2GoD5k2Sn48Dju/F\nuUnDuTrAdZqhSFDPSISzCzl9pk37j6WMEaO25NnUSp5Umaac1/27gpDoOJRN\nx5vfoFK6C0tEZbWovft7XCMRfWpk87IoplYlLhQsiCX1iBc8a/fZ/4qkEZCd\ne08S\r\n=QSOx\r\n-----END PGP SIGNATURE-----\r\n", "size": 143837}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.0.4_1631430867404_0.20006015845714442"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:49.988Z"}, "2.1.0": {"name": "vue-draggable-next", "version": "2.1.0", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "dependencies": {"vue-router": "4"}, "gitHead": "5e5b79197c20eb40f01063689d629fcf4ad938c9", "_id": "vue-draggable-next@2.1.0", "_nodeVersion": "14.15.0", "_npmVersion": "7.19.0", "dist": {"integrity": "sha512-xRMtOUxX/hYtY1C0irGJI1Opx9WRfQPwAqDR0qU3Tusa+iSxvTtiC3IQioU4THYmyior6V27rl0V8T3M19i/PA==", "shasum": "c5c0c7deef2f092278fc9e97bd3f668c43800896", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.1.0.tgz", "fileCount": 9, "unpackedSize": 583802, "size": 143843}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.1.0_1633851839073_0.8087817485464752"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:51.442Z"}, "2.1.1": {"name": "vue-draggable-next", "version": "2.1.1", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0", "vue-router": "4"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "gitHead": "c55378175f098835143aee2f3c81f76d90328ec7", "_id": "vue-draggable-next@2.1.1", "_nodeVersion": "14.15.0", "_npmVersion": "7.19.0", "dist": {"integrity": "sha512-f5lmA7t6LMaL4viR7dU30zzvqJzaKQs0ymL0Jy9UDT9uiZ2tXF3MzPzEvpTH2UODXZJkT+SnjeV1fXHMsgXLYA==", "shasum": "49886da82f116d11b3e4df7674320fdacf5d7e04", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.1.1.tgz", "fileCount": 9, "unpackedSize": 583778, "size": 143830}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.1.1_1634660864422_0.20787743807014025"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-21T23:26:51.546Z"}, "2.2.0": {"name": "vue-draggable-next", "version": "2.2.0", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0", "vue-router": "4"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "gitHead": "57a24df6481be04844b264e45efa15fbdab2654f", "_id": "vue-draggable-next@2.2.0", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-JQ7Ac4knnpsA47/acUhRR7negDHNZDLdpbXRR+n89f516rJDt+eHh48tfqTe80q2UfnLymQ46zi81gCMKFU4DQ==", "shasum": "cdefc345f950d64afb013436bf3111c7d87239f3", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.2.0.tgz", "fileCount": 9, "unpackedSize": 582196, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAtsE4X3CXIlbb36foDyrTU2z9v7/9cZyKzgzCdJyjTVAiACFE34VfSfAYZhqPDOIS4ACx9ING20uJ0ShCvXyTV/7w=="}], "size": 143911}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.2.0_1685780016817_0.04005412465740643"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-03T08:13:37.075Z", "publish_time": 1685780017075, "_source_registry_name": "default"}, "2.2.1": {"name": "vue-draggable-next", "version": "2.2.1", "description": "Build Draggable component using vue 3", "main": "dist/vue-draggable-next.cjs.js", "unpkg": "dist/vue-draggable-next.global.js", "jsdelivr": "dist/vue-draggable-next.global.js", "module": "dist/vue-draggable-next.esm-bundler.js", "types": "dist/vue-draggable-next.d.ts", "sideEffects": false, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose", "release": "bash scripts/release.sh", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "size": "size-limit", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts", "playground:dev": "vite serve", "playground:start": "vite serve --mode production", "playground:build": "cross-env NODE_ENV=production vite build"}, "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.18.4", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^2.3.4", "@size-limit/preset-small-lib": "^4.7.0", "@tailwindcss/ui": "^0.7.2", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/sortablejs": "^1.10.7", "@vitejs/plugin-vue": "^1.4.0", "@vue/compiler-sfc": "^3.2.2", "@vue/test-utils": "^2.0.0-beta.8", "codecov": "^3.8.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.2", "jest": "^26.5.3", "lint-staged": "^10.5.1", "pascalcase": "^1.0.0", "prettier": "^2.1.2", "rollup": "^2.56.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "size-limit": "^4.7.0", "sortablejs": "^1.14.0", "tailwindcss": "^1.9.6", "ts-jest": "^26.4.1", "typescript": "^4.3.5", "vite": "^2.4.1", "vue": "^3.2.2", "vuex": "^4.0.2", "yorkie": "^2.0.0", "vue-router": "4"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "size-limit": [{"path": "size-checks/basic.js"}], "peerDependencies": {"sortablejs": "^1.14.0", "vue": "^3.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "gitHead": "01d1408d803489b150706c3f2ac350497bcab449", "_id": "vue-draggable-next@2.2.1", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-EAMS1IRHF0kZO0o5PMOinsQsXIqsrKT1hKmbICxG3UEtn7zLFkLxlAtajcCcUTisNvQ6TtCB5COjD9a1raNADw==", "shasum": "adbe98c74610cca8f4eb63f92042681f96920451", "tarball": "https://registry.npmmirror.com/vue-draggable-next/-/vue-draggable-next-2.2.1.tgz", "fileCount": 10, "unpackedSize": 585065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBdtaSsxIQAe6T33/bim717rCPQ5mkKSVa8Njt8Lx44wIhAK9nR/IICnvSMRM6EMYlLymGBfyPkYeKq0eTJZPvrPhj"}], "size": 144598}, "_npmUser": {"name": "anish2690", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "anish2690", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-draggable-next_2.2.1_1687252240009_0.586645513939134"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-20T09:10:40.255Z", "publish_time": 1687252240255, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/anish2690/vue-draggable-next/issues"}, "homepage": "https://github.com/anish2690/vue-draggable-next#readme", "keywords": ["typescript", "javascript", "vue", "vue-next", "vue3", "draggable", "vue-draggable", "vue-draggable-next"], "repository": {"type": "git", "url": "git+https://github.com/anish2690/vue-draggable-next.git"}, "_source_registry_name": "default"}