{"_id": "@rollup/rollup-android-arm64", "_rev": "3616652-64c80974b742655469aff39d", "dist-tags": {"beta": "4.33.0-0", "latest": "4.45.0"}, "name": "@rollup/rollup-android-arm64", "time": {"created": "2023-07-31T19:20:20.265Z", "modified": "2025-07-12T05:54:09.839Z", "4.0.0-0": "2023-07-31T19:17:52.470Z", "4.0.0-1": "2023-08-01T04:48:56.022Z", "4.0.0-2": "2023-08-01T11:16:28.883Z", "4.0.0-3": "2023-08-04T08:16:50.501Z", "4.0.0-4": "2023-08-04T11:36:31.566Z", "4.0.0-5": "2023-08-20T06:56:45.168Z", "4.0.0-6": "2023-08-20T07:51:34.585Z", "4.0.0-7": "2023-08-20T10:33:19.280Z", "4.0.0-8": "2023-08-20T11:22:10.236Z", "4.0.0-9": "2023-08-20T14:28:56.248Z", "4.0.0-10": "2023-08-21T15:29:54.797Z", "4.0.0-11": "2023-08-23T10:15:43.497Z", "4.0.0-12": "2023-08-23T14:40:19.335Z", "4.0.0-13": "2023-08-24T15:48:30.214Z", "4.0.0-14": "2023-09-15T12:34:22.442Z", "4.0.0-15": "2023-09-15T13:06:49.426Z", "4.0.0-16": "2023-09-15T14:17:11.532Z", "4.0.0-17": "2023-09-15T14:59:03.049Z", "4.0.0-18": "2023-09-15T16:09:54.453Z", "4.0.0-19": "2023-09-15T18:50:53.917Z", "4.0.0-20": "2023-09-24T06:10:34.535Z", "4.0.0-21": "2023-09-24T17:22:20.510Z", "4.0.0-22": "2023-09-26T16:17:08.976Z", "4.0.0-23": "2023-09-26T20:14:19.487Z", "4.0.0-24": "2023-10-03T05:12:41.943Z", "4.0.0-25": "2023-10-05T14:12:40.344Z", "4.0.0": "2023-10-05T15:14:27.030Z", "4.0.1": "2023-10-06T12:36:35.428Z", "4.0.2": "2023-10-06T14:18:36.993Z", "4.1.0": "2023-10-14T05:52:09.981Z", "4.1.1": "2023-10-15T06:31:40.936Z", "4.1.3": "2023-10-15T17:48:22.508Z", "4.1.4": "2023-10-16T04:34:05.384Z", "4.1.5": "2023-10-28T09:23:27.017Z", "4.1.6": "2023-10-31T05:45:10.065Z", "4.2.0": "2023-10-31T08:10:35.897Z", "4.3.0": "2023-11-03T20:12:58.460Z", "4.3.1": "2023-11-11T07:57:51.113Z", "4.4.0": "2023-11-12T07:49:50.981Z", "4.4.1": "2023-11-14T05:25:02.282Z", "4.5.0": "2023-11-18T05:52:08.912Z", "4.5.1": "2023-11-21T20:13:05.887Z", "4.5.2": "2023-11-24T06:29:44.544Z", "4.6.0": "2023-11-26T13:39:10.888Z", "4.6.1": "2023-11-30T05:23:03.659Z", "4.7.0": "2023-12-08T07:57:57.077Z", "4.8.0": "2023-12-11T06:24:50.172Z", "4.9.0": "2023-12-13T09:24:13.670Z", "4.9.1": "2023-12-17T06:26:08.035Z", "4.9.2": "2023-12-30T06:23:24.418Z", "4.9.3": "2024-01-05T06:20:43.690Z", "4.9.4": "2024-01-06T06:38:56.789Z", "4.9.5": "2024-01-12T06:16:10.573Z", "4.9.6": "2024-01-21T05:52:15.928Z", "4.10.0": "2024-02-10T05:58:38.335Z", "4.11.0": "2024-02-15T06:09:33.560Z", "4.12.0": "2024-02-16T13:32:11.273Z", "4.12.1": "2024-03-06T06:03:29.534Z", "4.13.0": "2024-03-12T05:28:27.651Z", "4.13.1-1": "2024-03-24T07:39:16.201Z", "4.13.1": "2024-03-27T10:27:35.480Z", "4.13.2": "2024-03-28T14:13:31.454Z", "4.14.0": "2024-04-03T05:22:44.455Z", "4.14.1": "2024-04-07T07:35:35.817Z", "4.14.2": "2024-04-12T06:23:35.787Z", "4.14.3": "2024-04-15T07:18:28.202Z", "4.15.0": "2024-04-20T05:37:08.751Z", "4.16.0": "2024-04-21T04:41:45.780Z", "4.16.1": "2024-04-21T18:29:53.913Z", "4.16.2": "2024-04-22T15:19:10.364Z", "4.16.3": "2024-04-23T05:12:31.025Z", "4.16.4": "2024-04-23T13:15:03.555Z", "4.17.0": "2024-04-27T11:29:48.552Z", "4.17.1": "2024-04-29T04:57:49.585Z", "4.17.2": "2024-04-30T05:00:40.875Z", "4.18.0": "2024-05-22T05:03:39.309Z", "4.18.1": "2024-07-08T15:25:08.356Z", "4.19.0": "2024-07-20T05:46:11.466Z", "4.19.1": "2024-07-27T04:53:58.045Z", "4.19.2": "2024-08-01T08:32:50.418Z", "4.20.0": "2024-08-03T04:48:47.872Z", "4.21.0": "2024-08-18T05:55:32.026Z", "4.21.1": "2024-08-26T15:54:10.447Z", "4.21.2": "2024-08-30T07:04:23.266Z", "4.21.3": "2024-09-12T07:05:46.689Z", "4.22.0": "2024-09-19T04:55:28.775Z", "4.22.1": "2024-09-20T08:21:50.819Z", "4.22.2": "2024-09-20T09:33:42.450Z", "4.22.3-0": "2024-09-20T14:47:54.707Z", "4.22.3": "2024-09-21T05:03:05.893Z", "4.22.4": "2024-09-21T06:11:19.677Z", "4.22.5": "2024-09-27T11:48:12.946Z", "4.23.0": "2024-10-01T07:10:03.524Z", "4.24.0": "2024-10-02T09:37:17.130Z", "4.24.1": "2024-10-27T06:42:59.567Z", "4.24.2": "2024-10-27T15:40:05.148Z", "4.25.0-0": "2024-10-29T06:15:07.356Z", "4.24.3": "2024-10-29T14:14:04.822Z", "4.24.4": "2024-11-04T08:47:06.454Z", "4.25.0": "2024-11-09T08:37:19.686Z", "4.26.0": "2024-11-13T06:44:56.953Z", "4.27.0-0": "2024-11-13T07:03:11.766Z", "4.27.0-1": "2024-11-14T06:33:08.575Z", "4.27.0": "2024-11-15T10:40:33.908Z", "4.27.1-0": "2024-11-15T13:28:05.232Z", "4.27.1-1": "2024-11-15T15:37:59.937Z", "4.27.1": "2024-11-15T16:07:39.333Z", "4.27.2": "2024-11-15T17:20:00.550Z", "4.27.3": "2024-11-18T16:39:33.928Z", "4.27.4": "2024-11-23T07:00:18.996Z", "4.28.0": "2024-11-30T13:15:45.262Z", "4.28.1": "2024-12-06T11:44:54.527Z", "4.29.0-0": "2024-12-16T06:39:51.189Z", "4.29.0-1": "2024-12-19T06:37:29.883Z", "4.29.0-2": "2024-12-20T06:56:00.500Z", "4.29.0": "2024-12-20T18:37:22.548Z", "4.29.1": "2024-12-21T07:16:00.320Z", "4.30.0-0": "2024-12-21T07:17:12.727Z", "4.30.0-1": "2024-12-30T06:52:14.865Z", "4.29.2": "2025-01-05T12:07:40.719Z", "4.30.0": "2025-01-06T06:36:39.426Z", "4.30.1": "2025-01-07T10:35:50.707Z", "4.31.0-0": "2025-01-14T05:57:40.004Z", "4.31.0": "2025-01-19T12:56:45.414Z", "4.32.0": "2025-01-24T08:27:33.424Z", "4.33.0-0": "2025-01-28T08:30:06.955Z", "4.32.1": "2025-01-28T08:33:15.767Z", "4.33.0": "2025-02-01T07:11:57.263Z", "4.34.0": "2025-02-01T08:40:21.710Z", "4.34.1": "2025-02-03T06:58:11.917Z", "4.34.2": "2025-02-04T08:09:59.360Z", "4.34.3": "2025-02-05T09:22:02.403Z", "4.34.4": "2025-02-05T21:31:09.109Z", "4.34.5": "2025-02-07T08:52:59.830Z", "4.34.6": "2025-02-07T16:32:05.785Z", "4.34.7": "2025-02-14T09:53:57.160Z", "4.34.8": "2025-02-17T06:26:23.632Z", "4.34.9": "2025-03-01T07:32:34.711Z", "4.35.0": "2025-03-08T06:24:41.956Z", "4.36.0": "2025-03-17T08:35:41.346Z", "4.37.0": "2025-03-23T14:57:05.116Z", "4.38.0": "2025-03-29T06:29:03.079Z", "4.39.0": "2025-04-02T04:49:29.571Z", "4.40.0": "2025-04-12T08:39:32.922Z", "4.40.1": "2025-04-28T04:35:21.421Z", "4.40.2": "2025-05-06T07:26:50.833Z", "4.41.0": "2025-05-18T05:33:31.032Z", "4.41.1": "2025-05-24T06:14:28.754Z", "4.41.2": "2025-06-06T11:40:29.739Z", "4.42.0": "2025-06-06T14:48:10.134Z", "4.43.0": "2025-06-11T05:22:35.190Z", "4.44.0": "2025-06-19T06:22:54.172Z", "4.44.1": "2025-06-26T04:34:13.441Z", "4.44.2": "2025-07-04T12:56:06.058Z", "4.45.0": "2025-07-12T05:54:00.661Z"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-0", "os": ["android"], "cpu": ["arm64"], "main": "native/rollup.android-arm64.node", "description": "Next-generation ES module bundler", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_id": "@rollup/rollup-android-arm64@4.0.0-0", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-mURXKi640KON7A1TkCoSHfrnBCpkgOrZgKvohGxfpvJNRvvD73t2Fy12zvyHaB6s+gkgvuMJhuPky7/Y/4JVcg==", "shasum": "a74f9361f86f10d0689139b74140f5bc230cccf9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-0.tgz", "fileCount": 2, "unpackedSize": 723, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHXM9I7iKtuF6EjP1SzoKi9nM9iAD/RGrahlSR6d5pujAiByPD3/6PDgdmFo/Y3KK108dcJ/59axqSjHJZ8pZxOQqw=="}], "size": 491}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-0_1690831072201_0.034100970479768566"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-31T19:17:52.470Z", "publish_time": 1690831072470, "_source_registry_name": "default"}, "4.0.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-1", "os": ["android"], "cpu": ["arm64"], "main": "rollup.android-arm64.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "readmeFilename": "README.md", "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-1", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-yRGZinDmoLJ3ieBpnmMpljZLVsmfZm2T6xzJ+CbtBBGotkJl2spJUrVVZgyV78B1+n1C76EXAu4b4Sq6jhI8fg==", "shasum": "8f20da81456c5121817d6cd981af56cdca2f7667", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-1.tgz", "fileCount": 3, "unpackedSize": 2314132, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCxvkkzDa5TKJgd2N2sPxbJye8sDXOXa2IDBEv5/y/1AIgU4ya2eL+U4pfYOGgJCCe/WjeaAtK7O68zbZN7aTLiuM="}], "size": 960014}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-1_1690865335837_0.8933983604513083"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-01T04:48:56.022Z", "publish_time": 1690865336022, "_source_registry_name": "default"}, "4.0.0-2": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-2", "os": ["android"], "cpu": ["arm64"], "main": "rollup.android-arm64.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "readmeFilename": "README.md", "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-2", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-d6OTBmepSLv2AfpfsziT0DsOFiyztXwJgV68YqeyGl6TyR+B1RCdxRZEY09D/nhgyWgWhUaGh5k0bikravu55Q==", "shasum": "0adbf672ea61612a7af72b1579ffb6e770d8059b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-2.tgz", "fileCount": 3, "unpackedSize": 2314132, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfdttQnHO8PTjQnp3IDTcmc6HoOVgmtNynLx4TF9PjDwIgYld3lLz/dwFPylbOg0sm9g84q2HADoIdaShBx4M/DWg="}], "size": 960014}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-2_1690888588641_0.036158396925765945"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-01T11:16:28.883Z", "publish_time": 1690888588883, "_source_registry_name": "default"}, "4.0.0-3": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-3", "os": ["android"], "cpu": ["arm64"], "main": "rollup.android-arm64.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "readmeFilename": "README.md", "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-3", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-EGfEXBQE22zs063LZ399WpRd9Hq6pOFIJbKTHHnHAc9/mm7lcwD9OSpV0NY96s/9vEfGcydHJCpo7KPHPziOOA==", "shasum": "18d47c6698514d064ceaa1e11082dbc9cb4d8199", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-3.tgz", "fileCount": 3, "unpackedSize": 2300476, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjaf9XSQgnTgGABGnDXlHWs9hh40b4bsnKYg5kkpsPkQIhAMl/pBR1SnF1KAAtq0znJclblUKzruhhgB5wxwbl2FNi"}], "size": 959761}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-3_1691137010269_0.01701957573024404"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-04T08:16:50.501Z", "publish_time": 1691137010501, "_source_registry_name": "default"}, "4.0.0-4": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-4", "os": ["android"], "cpu": ["arm64"], "main": "rollup.android-arm64.node", "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "readmeFilename": "README.md", "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-4", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-7EkIHu6Rp/7btHX0kkKF2ys8YLa1U6XW3BH+rpjmPd0Bf13XTVJbv21o1uF+94MZXOYbbNqcX5y4xB3kM2tCuQ==", "shasum": "2d24d359a3b53685ed5c508daf3df1e48923d289", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-4.tgz", "fileCount": 3, "unpackedSize": 2300532, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQj7Tw8C1s8LNFw3G8nhbCp7f2XCvIPrUNb2x+4tVcYAIhANDwyVGHsMKnCcGmRynIQiooskbif8YvIKOybbWbIgto"}], "size": 959882}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-4_1691148991420_0.41066123365102647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-04T11:36:31.566Z", "publish_time": 1691148991566, "_source_registry_name": "default"}, "4.0.0-5": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-5", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.android-arm64.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-5", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-X5CU7mTClPbSSdQSLefd3/WZm2UnYLYvLyVOb3algqLYb+fs/S+PPtUzM2wvRqvHDDO9CAK6XNDD8T4vBAEfKA==", "shasum": "720e3fb501e40318455350e2c96d0c5c4e4043dd", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-5.tgz", "fileCount": 3, "unpackedSize": 2654991, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAOmTlIMm1QROtqGRL7AOQ7HqQ8DkwT8xM15H9J7GN4cAiEAmrmYyMO8ta2HP/rZhyNl2TW4SOP0LOhRgwZ75Acdd/I="}], "size": 1098651}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-5_1692514604941_0.08277866690301616"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T06:56:45.168Z", "publish_time": 1692514605168, "_source_registry_name": "default"}, "4.0.0-6": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-6", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.android-arm64.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-6", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-NjVp0pSrWjc9yrToU/4jbBS9D5hNCdDZQOITzMdEsXGm6oC+WixLwqx/jR29CKe+lK9zT0G8ySMnyatKjIuzjw==", "shasum": "d91e1993b1de03dce19bc81cf4518789b077f9ec", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-6.tgz", "fileCount": 3, "unpackedSize": 2654991, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFrspRNCkuSnOtGdiGKMQhiJtmztDsl/YUMHlFWL75djAiEArasWyJu1Nsfc3Qj5rS0m4TNzIb4Hu1pQrK5Tm2E4utg="}], "size": 1098651}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-6_1692517894331_0.39389146265137254"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T07:51:34.585Z", "publish_time": 1692517894585, "_source_registry_name": "default"}, "4.0.0-7": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-7", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.android-arm64.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-7", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-v1z4b6W7DhKgGYsIiQTsgJVbBVZ/swCdcyiKSekU8S4r9Q4Yc9kJwD2yKS++zgJtiK3loFJR6B9iqrxh/mxOxA==", "shasum": "123c22ee9cdc782cdd106303616938c4585be20b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-7.tgz", "fileCount": 7, "unpackedSize": 8109913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo8cmfYTOV8iP82D2cMJTVDQY4YZ7jTaFqcUykYsg0mwIgI4H2mP2ddVKNHuVxepsMbSpkwAysgjv2GEoYfwjPPFk="}], "size": 2359360}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-7_1692527599032_0.21919912887885729"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T10:33:19.280Z", "publish_time": 1692527599280, "_source_registry_name": "default"}, "4.0.0-8": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-8", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.android-arm64.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-8", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-rGWtzoYMl66MjKRxcp5FDiWe084HbcdP1ZI9U3YpWoyjRStQxU+Puk/90XkjNlRy06xodDy7FDkJmjw2tc2wEw==", "shasum": "27d3a68ea2110b5ea8d176e6a968cd2fd96795b9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-8.tgz", "fileCount": 7, "unpackedSize": 8109913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLzkf0N7iVMDLoqxVnR1gQ6j/c5udSJlNB/w+JFotL4QIhAPhhRi8X2fNfZFJhw0De1RWzwYfq7Lc/zH6zvaaduJ6I"}], "size": 2359151}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-8_1692530529821_0.38621169240486863"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T11:22:10.236Z", "publish_time": 1692530530236, "_source_registry_name": "default"}, "4.0.0-9": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-9", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.android-arm64.node", "default": "./wasm-node/bindings_wasm.js"}}, "readmeFilename": "README.md", "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-9", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-9QjWDfjW3u4/9cKuAEkdC8zUrvdG3xUvhnHwv4cm4r5/fF1Ur5lg8cj2v0qza2AFBZJQSCdRjCbvjhKW4zuFcg==", "shasum": "bc5f63934c376ee780dbf0f710a4a7ac6576c8cc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-9.tgz", "fileCount": 7, "unpackedSize": 8109913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBrStSZOSbo4CSZwOHij4nhnUJBzN8ms9Z3+SuTTiS7qAiBTcEJmWCko2Xerp45yn63TwMK7bdyLIrUyr/xIQOWsyg=="}], "size": 2359300}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-9_1692541736045_0.37084965041559537"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T14:28:56.248Z", "publish_time": 1692541736248, "_source_registry_name": "default"}, "4.0.0-10": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-10", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "exports": {".": {"node-addons": "./rollup.android-arm64.node", "default": "./wasm-node/bindings_wasm.js"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-10", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-uo4dXd3qFR3MuEl7T1H9GPvhAPdmvUC3Mvs4ClsvwfSzUZfPqd5emNt1O1aw6H5qZpEqj+z+PK6X+rkEX2CF6w==", "shasum": "ac7cba7c74f6d8d48babb2841a523cdc132da244", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-10.tgz", "fileCount": 7, "unpackedSize": 8127029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6Fvq4UDFRdxSlS0Iez6Teij3xqyJinyilpyvyIDy8WAiBYJs8/CsiuJiMjQEMUzLfqM5xEOlsU++LTxatGg2T1Dw=="}], "size": 2368995}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-10_1692631794503_0.5091205920229815"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-21T15:29:54.797Z", "publish_time": 1692631794797, "_source_registry_name": "default"}, "4.0.0-11": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-11", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-11", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-9zYlx8A9a31v77EnRc3zow4xT+nCNZDhk7NkkmrWYf+t8hndTEU7V6MHxqrBFWbX9Q9Y0qwDvBAM7kZPLc+7Nw==", "shasum": "4a7747bf16f0685e6b81a1fb868165f2d9f9f7a2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-11.tgz", "fileCount": 3, "unpackedSize": 2656976, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICMpZRsyT/lmwX0g7gJnsNI04On0tlDDOeaEbXKyIvvnAiBiQloYMgxD2J+GS+EEDeiLi02hM9ZsHoqhXCYJrZk1KQ=="}], "size": 1097061}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-11_1692785743253_0.17716589475155375"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T10:15:43.497Z", "publish_time": 1692785743497, "_source_registry_name": "default"}, "4.0.0-12": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-12", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-12", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-svi94XFLjcVGSGmcRFS7Nu0K2SekmeWJhBRKogPMIaFrz3cL3kpa7BlmhpuMzPSfsQ6FgvkbukjuO7x9m0X8OA==", "shasum": "380c03a11ee5bfd54a3b615cf2b67026f2d1f37d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-12.tgz", "fileCount": 3, "unpackedSize": 2656848, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDIOkvMFwFTntO7BNvKztr8jmpCEoZ1vNDykJNqJxid5AiEAkTucF8CVki8m9LSPrtirlqiKG2uXS9Gg71cEBax9J/4="}], "size": 1097086}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-12_1692801619009_0.4963091362754042"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T14:40:19.335Z", "publish_time": 1692801619335, "_source_registry_name": "default"}, "4.0.0-13": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-13", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-13", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-AfiMuhixnxHEPzVpYaszo/P/46cUSP9wNRHJMLUac+nWpZ0OnXj1x9+lICAnwTrPhzFg3w69PiCBToZCe8mFHA==", "shasum": "f90be0af2cfb4fc5c38c2ac9aa19af504076ec53", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-13.tgz", "fileCount": 3, "unpackedSize": 2675144, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHID2c9DZTFXPF38BQLL7gtXE4xjve7L9nrMggnahMTRAiEA9K8StebH0+naXuQ+SwQEryMTcUkzHRtongAv/k5McMI="}], "size": 1105500}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-13_1692892109980_0.6930204268665374"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-24T15:48:30.214Z", "publish_time": 1692892110214, "_source_registry_name": "default"}, "4.0.0-14": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-14", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-14", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-xLUxekk14EwSmoXLeb9L+a0T6wlz1rC1O78e977P8Ng9GDRxlAihou4WP1lQYjmUhRcYmdaOEeaGRMSmHKMH9Q==", "shasum": "62f50cc14baed1b031bf53de850be797c8e0854d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-14.tgz", "fileCount": 3, "unpackedSize": 2659640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoRnSodzW5ag/zhKsVT/2i0Tlq+6D3FLhm3bxlLEoLEwIgJXrAn97dPeMNgq9ptPr2x75w5hgqDgosQMtk8p/coyY="}], "size": 1091376}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-14_1694781262195_0.5383268467672917"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T12:34:22.442Z", "publish_time": 1694781262442, "_source_registry_name": "default"}, "4.0.0-15": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-15", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-15", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-muK/oG8GwAs1ONuS/b+F+YnPEPDOhkdGzvmAnYRq6+tOnCWMMebPckqQWP3a/vqerFIieHtmTxi5pTOv6VK0Kg==", "shasum": "9cda392102e051e5b15099157f49810f751cb2b9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-15.tgz", "fileCount": 3, "unpackedSize": 2659640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHzuyFNR3Pdjm1x3zDRUKBPFTl303EfTFjFWEemc/No7AiA++Tg+wG+Gmp53OalWc6gJpZgTQfqIBXAKgNb7124omQ=="}], "size": 1091380}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-15_1694783209157_0.985196525345112"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T13:06:49.426Z", "publish_time": 1694783209426, "_source_registry_name": "default"}, "4.0.0-16": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-16", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-16", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-t7oa+GIv9LGxSPU7oHE7U0NNpAB4C6CBGC/6ep/ouUvorNhPbwM0tOGwwl+KObh0M2fQd701uNmsn84bH3oQ8w==", "shasum": "c3b84e9700398f565d429278f608e9b3cc3dc980", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-16.tgz", "fileCount": 3, "unpackedSize": 2659640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICwuQK5wVw0ESrE/F9VBetYkjdMUTtcNboPJXCBCpXqxAiEAhh611TNDRxgn29QLQ1mieNbcuTQNwNwyS9JMMM49b3Q="}], "size": 1091380}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-16_1694787431295_0.030133060309461834"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T14:17:11.532Z", "publish_time": 1694787431532, "_source_registry_name": "default"}, "4.0.0-17": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-17", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-17", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-YEfkhv9N4EX72QTND3RXjU5WprAghELAKlxqQJU+jHT66uwUvby3a4uBhoYbu2A06qv22fyH56yHNAeUbmXLng==", "shasum": "7581f7eb816a6f632f38f34638ab6b89a78406b8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-17.tgz", "fileCount": 3, "unpackedSize": 2659640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDH0/EzshAXWVNruBvYkYVpFWsXGXd8kiHWILgEC1XKXQIgTBlufYEg5eH8jAiic0yQo+jwaUIcw2b0FKkxxi3wVVM="}], "size": 1091380}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-17_1694789942763_0.31036550903528637"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T14:59:03.049Z", "publish_time": 1694789943049, "_source_registry_name": "default"}, "4.0.0-18": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-18", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-18", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-/VveJNGF95NkBDfMgqXHW0ytj56qS/ckslXXGN/kmgVzhVtuI9uFrK+c9lpXBeBjIs4TYYPflunwEUPbWtwO8w==", "shasum": "1d89796fbe90dae5081bc3f4eadc4467a7334a48", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-18.tgz", "fileCount": 3, "unpackedSize": 2659640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGVlOfT0eMwty37O2KTtijfMGLMOJRQ3l/OcjRmvibW7AiEA8Gp2SRfGTtu19gWXzvt0XpXVV7cFQ9SoE1LYI+4W8DU="}], "size": 1091380}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-18_1694794194292_0.4931165194214926"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T16:09:54.453Z", "publish_time": 1694794194453, "_source_registry_name": "default"}, "4.0.0-19": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-19", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-19", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-+0EaQ8aorkIQSC2t9db+scn9w+uPUT4xZIAI9CQK+anXo3ux8blo8u62m5O+YZSVV4IOAaD3uZ9HuPLuA0/e3w==", "shasum": "ee7e01ee61ab037bab9657e945546bd7af10f131", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-19.tgz", "fileCount": 3, "unpackedSize": 2659640, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDitUZe4HQK81ycFP6JU9+ZutvKD4MB4v4jkoK+vtvIngIhAIL0qbH04bOqqJaUSMerUYClKZEF2b0yW0AfB/yNp+7i"}], "size": 1091380}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-19_1694803853706_0.3672838785569663"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-15T18:50:53.917Z", "publish_time": 1694803853917, "_source_registry_name": "default"}, "4.0.0-20": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-20", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-20", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-A672sDApM24bfRJWDVQagYZcUGZHZj4x9DcV0iUtmBar/3aktCW+foaelnlp4Yql3edu70yil1TDQMrvDIdH8A==", "shasum": "4c74961f98305bd7bed869a76fa3a8c2d02c5ffc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-20.tgz", "fileCount": 3, "unpackedSize": 2649838, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIADALqk8fERnEdch/fWMgF5Yvaat2tQJbM/JarWmyZtHAiEA/Ru7eAz66We1sXf2UkYNUIl/133czJ8VlcD0vk0afwQ="}], "size": 1089820}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-20_1695535834137_0.7801875640959186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-24T06:10:34.535Z", "publish_time": 1695535834535, "_source_registry_name": "default"}, "4.0.0-21": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-21", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-21", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-Wi5DJ2XU+yPqn/xLdfvxo296uQqmlgZWC9KC1mAjNtyCIkKPyQcqXsrr4EXgskBOe0rS0E8MfUeQtbloCRFNuw==", "shasum": "4ca5b120077fecef920e4973a3aa5f551b1617bb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-21.tgz", "fileCount": 3, "unpackedSize": 2649838, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDG73kRPL0lcR1vSI6quaLnfhzR3Q7MI/bmvlsjIyyYPwIgBn2qxaCKp5QbIbjeAIJQ9UM5MnrR7Q9rmworlVDwhU4="}], "size": 1089820}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-21_1695576140269_0.8979117941120722"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-24T17:22:20.510Z", "publish_time": 1695576140510, "_source_registry_name": "default"}, "4.0.0-22": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-22", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.0.0-22", "readmeFilename": "README.md", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-49x5mp/GVgrLlDwMpObBTQ/KbM4Wsa2kLkAvjhkW/T2d+Wpss6PL04uKkNcQecO467YH32AhOM543UJ+YtDKkw==", "shasum": "e1969533a8073e18edf3ae0f99606a415c3a7c49", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-22.tgz", "fileCount": 3, "unpackedSize": 2652886, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDANd5F+fOrzz0yC3oEcjtK1wxOel+3MgUlw8BkpKGBaAIgArwqj5GfAEh/eOg1FQpGfz8aS1Qj3egv2/1aPGtXCn8="}], "size": 1090429}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-22_1695745028737_0.6572723450762554"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T16:17:08.976Z", "publish_time": 1695745028976, "_source_registry_name": "default"}, "4.0.0-23": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-23", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "readmeFilename": "README.md", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_id": "@rollup/rollup-android-arm64@4.0.0-23", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-iyygT9P3wLXqVnKFE7OpteL32ijdZOnDbJhQ4vsOMfyZyE7ce0fEDMM/VsLirUMHqCH8sQO6lxFq/qBkj3sPLQ==", "shasum": "1efb480309e8d5c9fb2ca48a82a024cd5bcefcb1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-23.tgz", "fileCount": 3, "unpackedSize": 2652846, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvU+b16Pw9u4bgW/JF4bm1PbLmNLpKPcHdr4fOujCB8gIhAJhZfxi97rEMNkjJrkW9Co7R+hzVJCM26AThLBKSvCVH"}], "size": 1090524}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-23_1695759259230_0.1374495028865006"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-26T20:14:19.487Z", "publish_time": 1695759259487, "_source_registry_name": "default"}, "4.0.0-24": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-24", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.0.0-24", "readmeFilename": "README.md", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-ftTp5ByyyozDsHfmYGeErrQmBi4ZEVZItC4Siilwretkf+cMv9z0s0Ru8ncd28OZpaO0cr9b7Afm+DIRDyE8Kw==", "shasum": "6d350887468c0c97ba876c9b63d39de480502f35", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-24.tgz", "fileCount": 3, "unpackedSize": 2648662, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB920KFjee+kzlXmOaYN/OReEZQVrTI20QTwAPvLqFnKAiEAuXEjOyPC7eaHDIQnmV/lIvGV1aQOvI2FZAsbFmbd71M="}], "size": 1097090}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-24_1696309961658_0.8063248483721277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-03T05:12:41.943Z", "publish_time": 1696309961943, "_source_registry_name": "default"}, "4.0.0-25": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-25", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.0.0-25", "readmeFilename": "README.md", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-DfeB9B27MywgKmI3feFbXUmiaYE5lzOyE74dXebEJYKGXSkLG6ieU0qfjNU4uAA5SecorvhbX+xQz0i7UQHXng==", "shasum": "6bc0a418c1e3e49586d01b1ea88025ec60a41a17", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-25.tgz", "fileCount": 3, "unpackedSize": 2660134, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHcjwIgY+EhAgSwl8uLU/HAp/aI+8zlbwxHS+vDj+E62AiEAi1AE8koE5RYQojjaidx+6iluJEe9Ir9d7k2DcTp+mdw="}], "size": 1101930}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0-25_1696515160160_0.7257780263534896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-05T14:12:40.344Z", "publish_time": 1696515160344, "_source_registry_name": "default"}, "4.0.0": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.0.0", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-dcdg6Zp2bqIS/+2FHhdSS+lbcySufP2fYYoXkDa4W6uHE22L15psftdQZtFhxvvqRWPD1HsK0xIj5f07zuujkg==", "shasum": "e4fc4213a7d600f7a32ccfe0affcc2c7a799e52e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0.tgz", "fileCount": 3, "unpackedSize": 2660131, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGFZ4uA2I0pxQle+oNNQpEdSgrg1aaIVy7AeNXNWHWgKAiBBV/E5oASdgP+iElZxQDXBcAbgMuIB4n7FOWlNMZXxnQ=="}], "size": 1101925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.0_1696518866632_0.27005371550349744"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-05T15:14:27.030Z", "publish_time": 1696518867030, "_source_registry_name": "default"}, "4.0.1": {"name": "@rollup/rollup-android-arm64", "version": "4.0.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.0.1", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-cV7bMb68ipnlgxNjV2fvJRLeIHaEnJr6MrYo6+Re4rp5+B3iJMeOvd/ICjneWcutKzEiFtiyK55FSyu4A1juMg==", "shasum": "9cd6c359ecaa04c85a4439b241a10a3f78bbc020", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.1.tgz", "fileCount": 3, "unpackedSize": 2640587, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIELXjwiKLvvGjasxWqDsJprQBoby4k4So4mY3xeGEerIAiBXsa3qnRSwuG+TCZ/Pd3ru6K37ZuD8veZ9BKl2UrKwjw=="}], "size": 1091715}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.1_1696595795096_0.6013637148781834"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T12:36:35.428Z", "publish_time": 1696595795428, "_source_registry_name": "default"}, "4.0.2": {"name": "@rollup/rollup-android-arm64", "version": "4.0.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.0.2", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-lqCglytY3E6raze27DD9VQJWohbwCxzqs9aSHcj5X/8hJpzZfNdbsr4Ja9Hqp6iPyF53+5PtPx0pKRlkSvlHZg==", "shasum": "46327be24d75c7c53b7b065af29a62688eae3f1b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.2.tgz", "fileCount": 3, "unpackedSize": 2640667, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeKM161lELa4Z63hnzn1VKBnxyHEGKOVloQ7bumUjwVgIhAInNOc2CVBRIffu7UJQNK21m0Uh+CVzJvXTxurjLfe66"}], "size": 1091603}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.0.2_1696601916712_0.28923938845214003"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-06T14:18:36.993Z", "publish_time": 1696601916993, "_source_registry_name": "default"}, "4.1.0": {"name": "@rollup/rollup-android-arm64", "version": "4.1.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.1.0", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-pIi4Awf/YFwdc3H0VNYZMTS7FA0J00rS8AKoSfyB61GDVo+r7eOjSofoUPhDFXU7pfuTBiZ/4VAGa/qXGm5wcA==", "shasum": "bbd1f4a35e289f0781b914e3876916063e0e844f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.0.tgz", "fileCount": 3, "unpackedSize": 2701459, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAoG5GMAv4xM28ZT9ORf35iyn2Sd9/Gd2xUbiyMfFhdFAiBB8TEZPLlVQXHgF82gWrl1UCqnl/oTvWD7f4bW6tJdSw=="}], "size": 1115369}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.1.0_1697262729639_0.9475568042080948"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-14T05:52:09.981Z", "publish_time": 1697262729981, "_source_registry_name": "default"}, "4.1.1": {"name": "@rollup/rollup-android-arm64", "version": "4.1.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.1.1", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-bvGQbel/3YsTznK4FLPb+ZVIoYuM1SHntnJYJjlP2QI9AFqwYQqGRPvW27ZefTpDvFfy05Fdg+EhVMxU6uDvtw==", "shasum": "8e72cf1d1eb5ddb9fd858dcb6af0c703625be7f5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.1.tgz", "fileCount": 3, "unpackedSize": 2828507, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEazsIoNrl0ujfkBZkWUMXVU+INR0ERw99V8gUDHIoAeAiEA+AsA3TVzR7etrdAvvPeGNEnjbrj9i2nsxuC0QhtdcQc="}], "size": 1170477}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.1.1_1697351500750_0.3895312534862032"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-15T06:31:40.936Z", "publish_time": 1697351500936, "_source_registry_name": "default"}, "4.1.3": {"name": "@rollup/rollup-android-arm64", "version": "4.1.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.1.3", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-gO5j0qqT6ETdSf10gxTBeBmsKPC6yK80StTxHr4pvTYpPDfI7/mzSHy/3ez3OQyjxcBXs3i8tSF8aknwCkzv6Q==", "shasum": "b72d0384e080ca99f8a9d5941b31254eb36559cf", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.3.tgz", "fileCount": 3, "unpackedSize": 2828507, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCemgluNyKr90Sp1BJc1tHQEB+VMMD0gVQxDGIGYt+mGgIhAMs2mxwGPixnx4ERuGIeRCAXSSVDVXBD9Y5viHsVArgE"}], "size": 1170477}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.1.3_1697392102320_0.34343097978450365"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-15T17:48:22.508Z", "publish_time": 1697392102508, "_source_registry_name": "default"}, "4.1.4": {"name": "@rollup/rollup-android-arm64", "version": "4.1.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.1.4", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-D1e+ABe56T9Pq2fD+R3ybe1ylCDzu3tY4Qm2Mj24R9wXNCq35+JbFbOpc2yrroO2/tGhTobmEl2Bm5xfE/n8RA==", "shasum": "50c4e7668cb00a63d9a6810d0a607496ad4f0d09", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.4.tgz", "fileCount": 3, "unpackedSize": 2815707, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDo3j1Cfh8PPD3nzW37jPAOBke/RCkLYQuheeAyadv46gIgQMvv7vNXQWm4MPFcnlxXFNEl/UHPrZU0AWj70Ndbd34="}], "size": 1168300}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.1.4_1697430845114_0.7665059092663946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-16T04:34:05.384Z", "publish_time": 1697430845384, "_source_registry_name": "default"}, "4.1.5": {"name": "@rollup/rollup-android-arm64", "version": "4.1.5", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.1.5", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-tmXh7dyEt+JEz/NgDJlB1UeL/1gFV0v8qYzUAU42WZH4lmUJ5rp6/HkR2qUNC5jCgYEwd8/EfbHKtGIEfS4CUg==", "shasum": "b17d7262f00654296e8167b8b7a6708b79a29dc1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.5.tgz", "fileCount": 3, "unpackedSize": 2816283, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEZU81PcesYhCWzUlnLM92uXm0ls6fmHLbdS6HeVNsK9AiBZ0MzECaw4UMc1v56dcaCHBiXIxe0UD8Zx9HGmCGvF5A=="}], "size": 1169253}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.1.5_1698485006784_0.5017892251941491"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-28T09:23:27.017Z", "publish_time": 1698485007017, "_source_registry_name": "default"}, "4.1.6": {"name": "@rollup/rollup-android-arm64", "version": "4.1.6", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.1.6", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-A5eGcv5Jb99lt93z2yeW9Kb/nvnYCvb0e1w5b+oKjDno4k5rTppWuQSvgZP2LZAdhRMoBBzZ7bOVrRdHb1Qczw==", "shasum": "05e121d2c5c6bd33b0d661f0841f5749daa000d7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.6.tgz", "fileCount": 3, "unpackedSize": 2816283, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDToHuuKCAZyfbzm7IzbWtJCPrBVOzsk7rh4XJL3DZnDQIgUbQWx5eQN39IFe2rAGKon6H1jkue+mKVZOcRJjNqjJw="}], "size": 1169253}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.1.6_1698731109747_0.3855005301067196"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-31T05:45:10.065Z", "publish_time": 1698731110065, "_source_registry_name": "default"}, "4.2.0": {"name": "@rollup/rollup-android-arm64", "version": "4.2.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.2.0", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-+71T85hbMFrJI+zKQULNmSYBeIhru55PYoF/u75MyeN2FcxE4HSPw20319b+FcZ4lWx2Nx/Ql9tN+hoaD3GH/A==", "shasum": "416abdc076810cde6f84f05b0fc9decd2d7c319f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.2.0.tgz", "fileCount": 3, "unpackedSize": 2832691, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEZwZ0OtTcJbeOcoMSNyswSAnqbCcDYQvwLULir8edkoAiEAz0FrRxZa+hc7NYvWtGopRQNiLuPlpfWbQiLPdZnQoUw="}], "size": 1177128}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.2.0_1698739835668_0.037016601172050745"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-31T08:10:35.897Z", "publish_time": 1698739835897, "_source_registry_name": "default"}, "4.3.0": {"name": "@rollup/rollup-android-arm64", "version": "4.3.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.3.0", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-nLO/JsL9idr416vzi3lHm3Xm+QZh4qHij8k3Er13kZr5YhL7/+kBAx84kDmPc7HMexLmwisjDCeDIKNFp8mDlQ==", "shasum": "17b0f412034d14668c8acc8b7cbd8b1c76279599", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.3.0.tgz", "fileCount": 3, "unpackedSize": 2834483, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9/Id7q2xkmt07YAFq3vEo3O/VI/mCtVAc5qHHZkT8qgIhAKPbHR3nnj8gwvUyxOUG/O0iwI1OI2qqmHDeUmbQ+fvW"}], "size": 1178951}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.3.0_1699042378148_0.8970610266824155"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-03T20:12:58.460Z", "publish_time": 1699042378460, "_source_registry_name": "default"}, "4.3.1": {"name": "@rollup/rollup-android-arm64", "version": "4.3.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.3.1", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-3UbtU+7ocBMxYoMCDymHnFYB8tALVaEOjTe5pzAB65AJwXfDFAxADYGCJnBzDXD9u/G+7ktoYnMGYhitYphFkg==", "shasum": "d94f0d47a5d321c43958bd289b16e48aa6dd9311", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.3.1.tgz", "fileCount": 3, "unpackedSize": 2774755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDORDEryhrQE2kAAGoXVNYcqFuvehomgX1DDQsfKdCm5QIhAJ4Y2xb1ZmQnn+NZFKOARh25R4isqNNg7s2qLeRmiY33"}], "size": 1139752}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.3.1_1699689470919_0.3558364707804822"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-11T07:57:51.113Z", "publish_time": 1699689471113, "_source_registry_name": "default"}, "4.4.0": {"name": "@rollup/rollup-android-arm64", "version": "4.4.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.4.0", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-PlqvhzFxy5FRTB3wLSsGgPhiakv9jrgfu8tjSojLJFP0CdhfZSRDOFvQ2emWLUEBOSCnjpL63XSuFVMwg59ZtA==", "shasum": "38a90aa6be6ee7a3b78cca8dd919bbca8c426570", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.4.0.tgz", "fileCount": 3, "unpackedSize": 2406467, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGiOxwTn/kXQvdhAha35S77qH8XD6F+dmBsrLhXMgXZuAiAaU/G6m4rRidcbGGZm4b3bFjjfbtVQDYzwDCUuFTqWhg=="}], "size": 998879}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.4.0_1699775390742_0.5268285311834449"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-12T07:49:50.981Z", "publish_time": 1699775390981, "_source_registry_name": "default"}, "4.4.1": {"name": "@rollup/rollup-android-arm64", "version": "4.4.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.4.1", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-sRSkGTvGsARwWd7TzC8LKRf8FiPn7257vd/edzmvG4RIr9x68KBN0/Ek48CkuUJ5Pj/Dp9vKWv6PEupjKWjTYA==", "shasum": "f0492f00d18e1067785f8e820e137c00528c5e62", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.4.1.tgz", "fileCount": 3, "unpackedSize": 2406467, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZpvACVniWyQ2BvzXAv9aIN9D7pRbAXseJFWB75l9f4AiEA6wGkRq5F9W77PgsfeTinIE3Ul/XNEjG19eaJZZme9vM="}], "size": 998880}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.4.1_1699939501966_0.4206806913137209"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-14T05:25:02.282Z", "publish_time": 1699939502282, "_source_registry_name": "default"}, "4.5.0": {"name": "@rollup/rollup-android-arm64", "version": "4.5.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.5.0", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-UdMf1pOQc4ZmUA/NTmKhgJTBimbSKnhPS2zJqucqFyBRFPnPDtwA8MzrGNTjDeQbIAWfpJVAlxejw+/lQyBK/w==", "shasum": "8456a8c623cca4042ae4bf2ce03d875a02433191", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.5.0.tgz", "fileCount": 3, "unpackedSize": 2407875, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgvkPD1A9v0bua1XlQFOtX+yE0gjiZPxvMKbivcWQE9AIgW4wTch97KtoQhQIEe5E2ZDRsS6tZu9t9ybw1Qy1pLHU="}], "size": 999885}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.5.0_1700286728665_0.975230705044638"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-18T05:52:08.912Z", "publish_time": 1700286728912, "_source_registry_name": "default"}, "4.5.1": {"name": "@rollup/rollup-android-arm64", "version": "4.5.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.5.1", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-n1bX+LCGlQVuPlCofO0zOKe1b2XkFozAVRoczT+yxWZPGnkEAKTTYVOGZz8N4sKuBnKMxDbfhUsB1uwYdup/sw==", "shasum": "cae505492204c018d1c6335f3b845319b15dc669", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.5.1.tgz", "fileCount": 3, "unpackedSize": 2407875, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD588D+f1CiZJ7JpD0dU+Y9tLNeqvpLjuRffl+Jc+4PhwIgHsc4HuH/uPJ2UCluwodsQ1Z/2moWgxO7M3qhId0oDLE="}], "size": 999885}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.5.1_1700597585621_0.8474942881031158"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-21T20:13:05.887Z", "publish_time": 1700597585887, "_source_registry_name": "default"}, "4.5.2": {"name": "@rollup/rollup-android-arm64", "version": "4.5.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.5.2", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-xOuhj9HHtn8128ir8veoQsBbAUBasDbHIBniYTEx02pAmu9EXL+ZjJqngnNEy6ZgZ4h1JwL33GMNu3yJL5Mzow==", "shasum": "961089fe117ceca642b6432fadd093048da93ae8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.5.2.tgz", "fileCount": 3, "unpackedSize": 2421187, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEZ895/pbR+Wv+9/UO3lFB8mWMyKxZ19GifDaHPeOzwVAiEAsGq/42Q9M+BlufCAhszsHDe9ySCxm9iwDdkD5Do63vU="}], "size": 1007244}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.5.2_1700807384128_0.5232253443661334"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-24T06:29:44.544Z", "publish_time": 1700807384544, "_source_registry_name": "default"}, "4.6.0": {"name": "@rollup/rollup-android-arm64", "version": "4.6.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.6.0", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-y3Kt+34smKQNWilicPbBz/MXEY7QwDzMFNgwEWeYiOhUt9MTWKjHqe3EVkXwT2fR7izOvHpDWZ0o2IyD9SWX7A==", "shasum": "e0cf96960405947c1a09a389467e6aa10ae1a226", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.6.0.tgz", "fileCount": 3, "unpackedSize": 2421187, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDwVJsQMn7QdpU1TeGpPDvcLE6d7sbFR6mGviReyhKrwIhANUdnDpz997NBDykCgA3nRD0Yy/3M7IlUwP2SYkucuY3"}], "size": 1007244}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.6.0_1701005950580_0.7675892325409788"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-26T13:39:10.888Z", "publish_time": 1701005950888, "_source_registry_name": "default"}, "4.6.1": {"name": "@rollup/rollup-android-arm64", "version": "4.6.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.6.1", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-1TKm25Rn20vr5aTGGZqo6E4mzPicCUD79k17EgTLAsXc1zysyi4xXKACfUbwyANEPAEIxkzwue6JZ+stYzWUTA==", "shasum": "27c8c67fc5de574874085a1b480ac65b3e18378e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.6.1.tgz", "fileCount": 3, "unpackedSize": 2421187, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEI5FHkSPUK5e0MaqPXYHXAhsm/vqpVsOKtMriks9qdjAiEA6o8pflWrubzrK4nAxET9lOoK29y3B1qmHGECXjSeL38="}], "size": 1007244}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.6.1_1701321783423_0.5027343363871843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-30T05:23:03.659Z", "publish_time": 1701321783659, "_source_registry_name": "default"}, "4.7.0": {"name": "@rollup/rollup-android-arm64", "version": "4.7.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.7.0", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-/EBw0cuJ/KVHiU2qyVYUhogXz7W2vXxBzeE9xtVIMC+RyitlY2vvaoysMUqASpkUtoNIHlnKTu/l7mXOPgnKOA==", "shasum": "4e05031399a9c795612c9694827ec4ba55771bec", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.7.0.tgz", "fileCount": 3, "unpackedSize": 2431427, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpkdndpllvssRK0NTf+VWzOQKVPv70rUk27dqQoT4wpwIgZuQUf9XTm21ae0UfCKlvj+MaLsMxF//XV9eNUwd7B3Y="}], "size": 1008479}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.7.0_1702022276757_0.6871719061746648"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-08T07:57:57.077Z", "publish_time": 1702022277077, "_source_registry_name": "default"}, "4.8.0": {"name": "@rollup/rollup-android-arm64", "version": "4.8.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.8.0", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-aiItwP48BiGpMFS9Znjo/xCNQVwTQVcRKkFKsO81m8exrGjHkCBDvm9PHay2kpa8RPnZzzKcD1iQ9KaLY4fPQQ==", "shasum": "6c9fe8f9eb0cd9029be93b822b1a1c2d6b31c275", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.8.0.tgz", "fileCount": 3, "unpackedSize": 2431427, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdwjxu8kTvcZrWnV86hOCW2GxW/hmEf8xT1F0I9pMKuwIgbomNWHMf9LSub/NV/q10wal2o/RdI8oT3m2676kJxmY="}], "size": 1042412}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.8.0_1702275889865_0.9555021371572683"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-11T06:24:50.172Z", "publish_time": 1702275890172, "_source_registry_name": "default"}, "4.9.0": {"name": "@rollup/rollup-android-arm64", "version": "4.9.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.0", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-im6hUEyQ7ZfoZdNvtwgEJvBWZYauC9KVKq1w58LG2Zfz6zMd8gRrbN+xCVoqA2hv/v6fm9lp5LFGJ3za8EQH3A==", "shasum": "d4c14ef9e45d5c46b8d1f611ab8124a611d5be5b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.0.tgz", "fileCount": 3, "unpackedSize": 2431427, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGqlDLEbxWSl4qc4p8+TNGd4Hi6PnxQRA2STDo1B2IRAIgZd3EwVng+WaSV77O/ZiMDINePiTqWwLmiv6BtzoZh8k="}], "size": 1042412}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.0_1702459453410_0.16508658241946428"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-13T09:24:13.670Z", "publish_time": 1702459453670, "_source_registry_name": "default"}, "4.9.1": {"name": "@rollup/rollup-android-arm64", "version": "4.9.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.1", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-Jto9Fl3YQ9OLsTDWtLFPtaIMSL2kwGyGoVCmPC8Gxvym9TCZm4Sie+cVeblPO66YZsYH8MhBKDMGZ2NDxuk/XQ==", "shasum": "6f76cfa759c2d0fdb92122ffe28217181a1664eb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.1.tgz", "fileCount": 3, "unpackedSize": 2414211, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE3VvPjoC2n2kkXYnISRzhFTdqm5UT6pZesgwRm0PhM7AiAn8rfp/7XkqbJUIMgm1sm8RtM/gIl+MppiFa2aiL4W+w=="}], "size": 1036020}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.1_1702794367786_0.12688934093418647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-17T06:26:08.035Z", "publish_time": 1702794368035, "_source_registry_name": "default"}, "4.9.2": {"name": "@rollup/rollup-android-arm64", "version": "4.9.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.2", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-yZ+MUbnwf3SHNWQKJyWh88ii2HbuHCFQnAYTeeO1Nb8SyEiWASEi5dQUygt3ClHWtA9My9RQAYkjvrsZ0WK8Xg==", "shasum": "21bd0fbafdf442c6a17645b840f6a94556b0e9bb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.2.tgz", "fileCount": 3, "unpackedSize": 2426307, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsGEsTjG1iJAQwU9BOTAyhGlvxW/4w6L7S/OEZvN9XJgIhAOVeH8UhsPkVQV+QSRZ6Dw9RPf7/5cFRpdQTInK9S0rW"}], "size": 1035926}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.2_1703917404180_0.5909585794672465"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-30T06:23:24.418Z", "publish_time": 1703917404418, "_source_registry_name": "default"}, "4.9.3": {"name": "@rollup/rollup-android-arm64", "version": "4.9.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.3", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-kffYCJ2RhDL1DlshLzYPyJtVeusHlA8Q1j6k6s4AEVKLq/3HfGa2ADDycLsmPo3OW83r4XtOPqRMbcFzFsEIzQ==", "shasum": "5bde956d84961bba95ba3c98ffb8664946617d91", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.3.tgz", "fileCount": 3, "unpackedSize": 2422275, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxWf+rfqUxX/6cR4CXOsD8OmgRkhZAWkfnJa7U77Z0jgIhAItydlJRjKOS6h2vS4wRyPkowdjmV6Zi8fsyRBMZW8zO"}], "size": 1038922}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.3_1704435643447_0.11923906290030106"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-05T06:20:43.690Z", "publish_time": 1704435643690, "_source_registry_name": "default"}, "4.9.4": {"name": "@rollup/rollup-android-arm64", "version": "4.9.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.4", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-ehcBrOR5XTl0W0t2WxfTyHCR/3Cq2jfb+I4W+Ch8Y9b5G+vbAecVv0Fx/J1QKktOrgUYsIKxWAKgIpvw56IFNA==", "shasum": "96eb86fb549e05b187f2ad06f51d191a23cb385a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.4.tgz", "fileCount": 3, "unpackedSize": 2422339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDATO42O1z+IU8Y9QmJO6eEeY16rWc31swLs9jilND3tAiBCehWJhK6yHV+3Y2blYVyUuweT5SrxmdiQIrMwd+QEVg=="}], "size": 1038935}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.4_1704523136549_0.09208659028105481"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-06T06:38:56.789Z", "publish_time": 1704523136789, "_source_registry_name": "default"}, "4.9.5": {"name": "@rollup/rollup-android-arm64", "version": "4.9.5", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.5", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-f14d7uhAMtsCGjAYwZGv6TwuS3IFaM4ZnGMUn3aCBgkcHAYErhV1Ad97WzBvS2o0aaDv4mVz+syiN0ElMyfBPg==", "shasum": "33757c3a448b9ef77b6f6292d8b0ec45c87e9c1a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.5.tgz", "fileCount": 3, "unpackedSize": 2422339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNA+5sRNQjiWfQZQKzRjxVZKV0c+43Hx8zNzWzMt6OhAiEA1xYgWpa8lAqsyEU2iZdzgM79Ff2QbBNgO14W2Ry/kSg="}], "size": 1040201}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.5_1705040170399_0.060630503881570874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-12T06:16:10.573Z", "publish_time": 1705040170573, "_source_registry_name": "default"}, "4.9.6": {"name": "@rollup/rollup-android-arm64", "version": "4.9.6", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.9.6", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-T14aNLpqJ5wzKNf5jEDpv5zgyIqcpn1MlwCrUXLrwoADr2RkWA0vOWP4XxbO9aiO3dvMCQICZdKeDrFl7UMClw==", "shasum": "46327d5b86420d2307946bec1535fdf00356e47d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.6.tgz", "fileCount": 3, "unpackedSize": 2424771, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEWhpk6Dyv32SoNCDqz4ffDOVcsMpSlHs/WJ7zkq9MUpAiA9ENIANXg1WauPpHWkDJO+8SDZ8GOx9bfj0rbgwOQtOA=="}], "size": 1040604}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.9.6_1705816335703_0.9375921104036722"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-21T05:52:15.928Z", "publish_time": 1705816335928, "_source_registry_name": "default"}, "4.10.0": {"name": "@rollup/rollup-android-arm64", "version": "4.10.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.10.0", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-lvu0jK97mZDJdpZKDnZI93I0Om8lSDaiPx3OiCk0RXn3E8CMPJNS/wxjAvSJJzhhZpfjXsjLWL8LnS6qET4VNQ==", "shasum": "0114a042fd6396f4f3233e6171fd5b61a36ed539", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.10.0.tgz", "fileCount": 3, "unpackedSize": 2495124, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFB821AjM4AauK2NciF3vRZfWPsnesp3zwhvTvkgq2r/AiEAqeNWOTgvOYBuzIRao23zA9hliDLPTwCqiyQBJr230yA="}], "size": 1068005}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.10.0_1707544718148_0.27566604576602427"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-10T05:58:38.335Z", "publish_time": 1707544718335, "_source_registry_name": "default"}, "4.11.0": {"name": "@rollup/rollup-android-arm64", "version": "4.11.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.11.0", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-0ij3iw7sT5jbcdXofWO2NqDNjSVVsf6itcAkV2I6Xsq4+6wjW1A8rViVB67TfBEan7PV2kbLzT8rhOVWLI2YXw==", "shasum": "772cb1856f720863d982c17f25d3fbb930c946d3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.11.0.tgz", "fileCount": 3, "unpackedSize": 2495124, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBzHP6IyGuXY2OF6bu8lgpxxdWGUaj5fh5CpsXugY79QIhANIloM/b3mQEXi5DosmtdX8ll771JC54gBmryLGri5yg"}], "size": 1068005}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.11.0_1707977373386_0.15976047303997443"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-15T06:09:33.560Z", "publish_time": 1707977373560, "_source_registry_name": "default"}, "4.12.0": {"name": "@rollup/rollup-android-arm64", "version": "4.12.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.12.0", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-OBqcX2BMe6nvjQ0Nyp7cC90cnumt8PXmO7Dp3gfAju/6YwG0Tj74z1vKrfRz7qAv23nBcYM8BCbhrsWqO7PzQQ==", "shasum": "3822e929f415627609e53b11cec9a4be806de0e2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.12.0.tgz", "fileCount": 3, "unpackedSize": 2491604, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZf2KVw0djbsyGVoTUFgGtOpnWqN+Jn5A2E3tGSyGLKQIgfFQ9KLHVDiRiOnT5R6drEvPqhTnDYbi0X9N/O5MmxTY="}], "size": 1066258}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.12.0_1708090331066_0.657819741210919"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-16T13:32:11.273Z", "publish_time": 1708090331273, "_source_registry_name": "default"}, "4.12.1": {"name": "@rollup/rollup-android-arm64", "version": "4.12.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.12.1", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-wlzcWiH2Ir7rdMELxFE5vuM7D6TsOcJ2Yw0c3vaBR3VOsJFVTx9xvwnAvhgU5Ii8Gd6+I11qNHwndDscIm0HXg==", "shasum": "b1e606fb4b46b38dc32bf010d513449462d669e9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.12.1.tgz", "fileCount": 3, "unpackedSize": 2489684, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpS1xV7kfSXSn3P5+HR7hOr6M17E9sE2K7FYzVL2qqhQIgJsdWhAL8lbiHM4j1mCVhRGqhj1AzSz+JP1FRV3xlAk8="}], "size": 1064363}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.12.1_1709705009296_0.2924995945384046"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-06T06:03:29.534Z", "publish_time": 1709705009534, "_source_registry_name": "default"}, "4.13.0": {"name": "@rollup/rollup-android-arm64", "version": "4.13.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.13.0", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-BSbaCmn8ZadK3UAQdlauSvtaJjhlDEjS5hEVVIN3A4bbl3X+otyf/kOJV08bYiRxfejP3DXFzO2jz3G20107+Q==", "shasum": "8833679af11172b1bf1ab7cb3bad84df4caf0c9e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.0.tgz", "fileCount": 3, "unpackedSize": 2488276, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZqk6MxZgR4Qg9D4ISqt4cipMMCPoUa9ZvJb8uAyHCXAiBy2Xxg5a/z207DaJy6UGrkqB3T1kXi0krCttmjn/rxPg=="}], "size": 1061727}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.13.0_1710221307406_0.47086175270516195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-12T05:28:27.651Z", "publish_time": 1710221307651, "_source_registry_name": "default"}, "4.13.1-1": {"name": "@rollup/rollup-android-arm64", "version": "4.13.1-1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.13.1-1", "readmeFilename": "README.md", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-LwFHuPj6EN8DoflWz/J7DKncbV8VrPhJeweqDIJabavZ9tgDd+LONX0IT4CxxbgYWHbRl/IlGJnW6cWaYaH7VQ==", "shasum": "af4967c97656eac6a6a2ead3a3fb9aaa12629dff", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.1-1.tgz", "fileCount": 3, "unpackedSize": 2485398, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExoviAdkTnatNudB48EFXK5tLXWYoQkAxFUPBe0aTbHAiAi92827b+c0AcmExpzVPN3+tfCftfnPfPXH9M0G944EQ=="}], "size": 1063533}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.13.1-1_1711265956004_0.6806146010212806"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-24T07:39:16.201Z", "publish_time": 1711265956201, "_source_registry_name": "default"}, "4.13.1": {"name": "@rollup/rollup-android-arm64", "version": "4.13.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.13.1", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-TrTaFJ9pXgfXEiJKQ3yQRelpQFqgRzVR9it8DbeRzG0RX7mKUy0bqhCFsgevwXLJepQKTnLl95TnPGf9T9AMOA==", "shasum": "c89a55670e1179ed7ba3db06cee0d7da7b3d35ce", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.1.tgz", "fileCount": 3, "unpackedSize": 2485396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBKy8r2Vez2jx6bqSKr0FqmZKrJ57qloo4S3lf9i97LDAiEAnyFxbX23P2h+5gT6icQ6rpkqlzXqCrv9lwNyHP1iqes="}], "size": 1063532}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.13.1_1711535255253_0.9636238099093142"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-27T10:27:35.480Z", "publish_time": 1711535255480, "_source_registry_name": "default"}, "4.13.2": {"name": "@rollup/rollup-android-arm64", "version": "4.13.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.13.2", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-GdxxXbAuM7Y/YQM9/TwwP+L0omeE/lJAR1J+olu36c3LqqZEBdsIWeQ91KBe6nxwOnb06Xh7JS2U5ooWU5/LgQ==", "shasum": "0d2448251040fce19a98eee505dff5b3c8ec9b98", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.2.tgz", "fileCount": 3, "unpackedSize": 2485396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdaJre5UYgepvgi7wybPqSJV4s+k+o4lKML7cwu2qAgAiEAn2+DpYDgrP8u00rP2/SLAPSnsBdPgKZxUkWS4+53v7s="}], "size": 1063532}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.13.2_1711635211224_0.4620369965442084"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-28T14:13:31.454Z", "publish_time": 1711635211454, "_source_registry_name": "default"}, "4.14.0": {"name": "@rollup/rollup-android-arm64", "version": "4.14.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.14.0", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-fI9nduZhCccjzlsA/OuAwtFGWocxA4gqXGTLvOyiF8d+8o0fZUeSztixkYjcGq1fGZY3Tkq4yRvHPFxU+jdZ9Q==", "shasum": "81bba83b37382a2d0e30ceced06c8d3d85138054", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.0.tgz", "fileCount": 3, "unpackedSize": 2494996, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtQfE+LYuxQGS9cCII6DWmzbrWoxS9RwlpKaihW+VGnwIhAKwgfodSR4RliYDvj5nCAHVCY/B/IjhwYMtY87bWp4fj"}], "size": 1066807}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.14.0_1712121764191_0.2276901440583412"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-03T05:22:44.455Z", "publish_time": 1712121764455, "_source_registry_name": "default"}, "4.14.1": {"name": "@rollup/rollup-android-arm64", "version": "4.14.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.14.1", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Y/9OHLjzkunF+KGEoJr3heiD5X9OLa8sbT1lm0NYeKyaM3oMhhQFvPB0bNZYJwlq93j8Z6wSxh9+cyKQaxS7PQ==", "shasum": "154ca7e4f815d2e442ffc62ee7f64aee8b2547b0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.1.tgz", "fileCount": 3, "unpackedSize": 2508308, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRfTE9dfx4Ytc4pvca6j0BATzRiz8zbGikTCTm/cSLxAiEAsOgHRw/PtlB2GBcvh3pAy51GsB5ERW4Qu/sAg4IMmTk="}], "size": 1071873}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.14.1_1712475335512_0.3266433139314504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-07T07:35:35.817Z", "publish_time": 1712475335817, "_source_registry_name": "default"}, "4.14.2": {"name": "@rollup/rollup-android-arm64", "version": "4.14.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.14.2", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-lAarIdxZWbFSHFSDao9+I/F5jDaKyCqAPMq5HqnfpBw8dKDiCaaqM0lq5h1pQTLeIqueeay4PieGR5jGZMWprw==", "shasum": "08a2d2705193ebb3054941994e152808beb5254e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.2.tgz", "fileCount": 3, "unpackedSize": 2500180, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvn0RYZJi9s+rPTZA8WQfNgTRKzL+fwcVnoDWFwQ05YAiB9W5BmUjQ3jfxILgx4CYbgLo6NZghjpLTZwROykXOlYg=="}], "size": 1068990}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.14.2_1712903015546_0.05296136677033547"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T06:23:35.787Z", "publish_time": 1712903015787, "_source_registry_name": "default"}, "4.14.3": {"name": "@rollup/rollup-android-arm64", "version": "4.14.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.14.3", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-eQK5JIi+POhFpzk+LnjKIy4Ks+pwJ+NXmPxOCSvOKSNRPONzKuUvWE+P9JxGZVxrtzm6BAYMaL50FFuPe0oWMQ==", "shasum": "b26bd09de58704c0a45e3375b76796f6eda825e4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.3.tgz", "fileCount": 3, "unpackedSize": 2505412, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEA+T72gC2QGoXLguzvbFqNNudnklIhwv30NHBaeBrzwIhAOtpxhrxO0Q0f+dxDmOyHJajoxRPqv6um+LSmykclaCa"}], "size": 1070550}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.14.3_1713165507986_0.0294291404299305"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-15T07:18:28.202Z", "publish_time": 1713165508202, "_source_registry_name": "default"}, "4.15.0": {"name": "@rollup/rollup-android-arm64", "version": "4.15.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.15.0", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-5UywPdmC9jiVOShjQx4uuIcnTQOf85iA4jgg8bkFoH5NYWFfAfrJpv5eeokmTdSmYwUTT5IrcrBCJNkowhrZDA==", "shasum": "a2bdafdb753ece571956289a5ba8c37af748bd0c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.15.0.tgz", "fileCount": 3, "unpackedSize": 2557444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICtVIHADr3I7lhVWFw5EzG/IEJTPqe0Y2eL1KvMT6oX4AiEA9/CMu1LGpJUkgVaxgCXaiBQqJD4PAl5jCa80X9Ra9pw="}], "size": 1089003}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.15.0_1713591428530_0.0359154142677105"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-20T05:37:08.751Z", "publish_time": 1713591428751, "_source_registry_name": "default"}, "4.16.0": {"name": "@rollup/rollup-android-arm64", "version": "4.16.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.16.0", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-JltUBgsKgN108NO4/hj0B/dJYNrqqmdRCtUet5tFDi/w+0tvQP0FToyWBV4HKBcSX4cvFChrCyt5Rh4FX6M6QQ==", "shasum": "e81fa0fd632ae1c9e6837565b929c95e7bfa0c5a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.0.tgz", "fileCount": 3, "unpackedSize": 2557444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzeDy+dzYatQY9HoePa5g8B7ZXQq21aS4Yv0D8VSaOLQIhAKMKum2FoMweWnbQ2+HwCItv0FobZ4uL45pk1s9Ktbe3"}], "size": 1089003}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.16.0_1713674505522_0.6978039254064465"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-21T04:41:45.780Z", "publish_time": 1713674505780, "_source_registry_name": "default"}, "4.16.1": {"name": "@rollup/rollup-android-arm64", "version": "4.16.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.16.1", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-ttWB6ZCfRLuDIUiE0yiu5gcqOsYjA5F7kEV1ggHMj20FwLZ8A1FMeahZJFl/pnOmcnD2QL0z4AcDuo27utGU8A==", "shasum": "e7bd4f2b8ec5e049f98edbc68d72cb05356f81d8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.1.tgz", "fileCount": 3, "unpackedSize": 2557444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFQpii3QSYiqCW36dM5sG6BJ58rBM2GnxFlLXglYnKhDAiEAsLqWNR57YFZesRqidVCsyD9TdklPdu13A4bfMkO7dYE="}], "size": 1089003}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.16.1_1713724193705_0.8268136367830183"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-21T18:29:53.913Z", "publish_time": 1713724193913, "_source_registry_name": "default"}, "4.16.2": {"name": "@rollup/rollup-android-arm64", "version": "4.16.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.16.2", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-5/W1xyIdc7jw6c/f1KEtg1vYDBWnWCsLiipK41NiaWGLG93eH2edgE6EgQJ3AGiPERhiOLUqlDSfjRK08C9xFg==", "shasum": "f50f65d0c3b8b30d070d8616b2dfc0978dd588bd", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.2.tgz", "fileCount": 3, "unpackedSize": 2557444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTGNscyZgowuNDVrq2/0UfyQieRygh6t2mMrppciRs0QIgeDxxw86qkREWZZ2xoSXVW9xTcVU0L9/Fbjat8axghPc="}], "size": 1089003}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.16.2_1713799150064_0.06421275672431226"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-22T15:19:10.364Z", "publish_time": 1713799150364, "_source_registry_name": "default"}, "4.16.3": {"name": "@rollup/rollup-android-arm64", "version": "4.16.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.16.3", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-vGl+Bny8cawCM7ExugzqEB8ke3t7Pm9/mo+ciA9kJh6pMuNyM+31qhewMwHwseDZ/LtdW0SCocW1CsMxcq1Lsg==", "shasum": "c0a15028fc76573503b83e257fcf30748df7ded2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.3.tgz", "fileCount": 3, "unpackedSize": 2557444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCO/apDPi1+NK0YKZPqj21Pc81sezn61Kuy5L0v6l4lPQIhAOvbjPYPNYrt9tifR0sYhvvWu4wI6eboFyNKMi52ASwJ"}], "size": 1089003}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.16.3_1713849150803_0.3465061688189326"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T05:12:31.025Z", "publish_time": 1713849151025, "_source_registry_name": "default"}, "4.16.4": {"name": "@rollup/rollup-android-arm64", "version": "4.16.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.16.4", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-Bvm6D+NPbGMQOcxvS1zUl8H7DWlywSXsphAeOnVeiZLQ+0J6Is8T7SrjGTH29KtYkiY9vld8ZnpV3G2EPbom+w==", "shasum": "ffb84f1359c04ec8a022a97110e18a5600f5f638", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.4.tgz", "fileCount": 3, "unpackedSize": 2557444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFodGNoQwJp0n9DVLBOLe7N3rI6iIoezj5++ADyswnzdAiAKgKdmr5mWzBly+aNj9muZXGmeyFJsdKn9LcKBbYaUhg=="}], "size": 1089001}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.16.4_1713878103277_0.2837691934496718"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-23T13:15:03.555Z", "publish_time": 1713878103555, "_source_registry_name": "default"}, "4.17.0": {"name": "@rollup/rollup-android-arm64", "version": "4.17.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.17.0", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+kjt6dvxnyTIAo7oHeYseYhDyZ7xRKTNl/FoQI96PHkJVxoChldJnne/LzYqpqidoK1/0kX0/q+5rrYqjpth6w==", "shasum": "be0cac9af51c9c9b4b064f335fb9886b95b1df8a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.17.0.tgz", "fileCount": 3, "unpackedSize": 2570116, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVzkhGbc0lSSJHtBSuyAbfGBs4LscywekEKic9FbU/aAiEA3wXETvZ9sqdiU5TREZOZPYV43mODUrQYf96pTbnCCFY="}], "size": 1093530}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.17.0_1714217388407_0.2644193044775507"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-27T11:29:48.552Z", "publish_time": 1714217388552, "_source_registry_name": "default"}, "4.17.1": {"name": "@rollup/rollup-android-arm64", "version": "4.17.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.17.1", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-piwZDjuW2WiHr05djVdUkrG5JbjnGbtx8BXQchYCMfib/nhjzWoiScelZ+s5IJI7lecrwSxHCzW026MWBL+oJQ==", "shasum": "e32d5e6511a49ddd64c22f8b3668049f63b7b04c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.17.1.tgz", "fileCount": 3, "unpackedSize": 2570116, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGIxJnIJbn6/YyWyDr1EgxqAMzjCIGGDYSIlgFUJZnfQAiA3KH4fnWZJz3pmK6+GPqLM/GV7H8FdG+NLhpsLelNk5g=="}], "size": 1093531}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.17.1_1714366669327_0.5079199678716195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-29T04:57:49.585Z", "publish_time": 1714366669585, "_source_registry_name": "default"}, "4.17.2": {"name": "@rollup/rollup-android-arm64", "version": "4.17.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.17.2", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-yeX/Usk7daNIVwkq2uGoq2BYJKZY1JfyLTaHO/jaiSwi/lsf8fTFoQW/n6IdAsx5tx+iotu2zCJwz8MxI6D/Bw==", "shasum": "5aeef206d65ff4db423f3a93f71af91b28662c5b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.17.2.tgz", "fileCount": 3, "unpackedSize": 2570116, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/YTpNvQWSUV2b7Ym/WW8AA2EPv54XZVp6P0v1hpBT+QIgBD4A7a6PeW4i/ePwp3hddHc3pgxZRnUtJmQhy9pSAeM="}], "size": 1093531}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.17.2_1714453240631_0.7055604571419383"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-30T05:00:40.875Z", "publish_time": 1714453240875, "_source_registry_name": "default"}, "4.18.0": {"name": "@rollup/rollup-android-arm64", "version": "4.18.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.18.0", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-avCea0RAP03lTsDhEyfy+hpfr85KfyTctMADqHVhLAF3MlIkq83CP8UfAHUssgXTYd+6er6PaAhx/QGv4L1EiA==", "shasum": "97255ef6384c5f73f4800c0de91f5f6518e21203", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.18.0.tgz", "fileCount": 3, "unpackedSize": 2565708, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn/XW8kjAGFtiRfEyCQ4EkSpMhz7wTnQ0kBQT+D8uhsgIhAMh9lgJ7R6tW/HpH+prmkO4u7/JG2FtV9ExQc74IkBjY"}], "size": 1092497}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.18.0_1716354219087_0.921191547440773"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-22T05:03:39.309Z", "publish_time": 1716354219309, "_source_registry_name": "default"}, "4.18.1": {"name": "@rollup/rollup-android-arm64", "version": "4.18.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.18.1", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.3", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-F/tkdw0WSs4ojqz5Ovrw5r9odqzFjb5LIgHdHZG65dFI1lWTWRVy32KDJLKRISHgJvqUeUhdIvy43fX41znyDg==", "shasum": "82ab3c575f4235fb647abea5e08eec6cf325964e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.18.1.tgz", "fileCount": 3, "unpackedSize": 2271412, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJqN0Y1QfuX7VVLzqpNkVmHBSMTqp3eEd+0kzj+GjFNgIgaQHFB6wCgZU0cdQxbfczy4BSKEod0UgDMJmVFTpC5Ok="}], "size": 964584}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.18.1_1720452308160_0.2744288818474847"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-08T15:25:08.356Z", "publish_time": 1720452308356, "_source_registry_name": "default"}, "4.19.0": {"name": "@rollup/rollup-android-arm64", "version": "4.19.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.19.0", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-RDxUSY8D1tWYfn00DDi5myxKgOk6RvWPxhmWexcICt/MEC6yEMr4HNCu1sXXYLw8iAsg0D44NuU+qNq7zVWCrw==", "shasum": "e1a6d4bca2eb08c84fd996a4bf896ce4b6f4014c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.0.tgz", "fileCount": 3, "unpackedSize": 2241076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcW7DjvNahWy4CR/95me+eTnRa0peoks/JPbvYemgPXAiEAn+GgBabwclcoinim9WrVx0feFG/WtMEqii3JavaC5fE="}], "size": 944103}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.19.0_1721454371243_0.5059348381430184"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-20T05:46:11.466Z", "publish_time": 1721454371466, "_source_registry_name": "default"}, "4.19.1": {"name": "@rollup/rollup-android-arm64", "version": "4.19.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.19.1", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-thFUbkHteM20BGShD6P08aungq4irbIZKUNbG70LN8RkO7YztcGPiKTTGZS7Kw+x5h8hOXs0i4OaHwFxlpQN6A==", "shasum": "93de4d867709d3313794723b5afd91e1e174f906", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.1.tgz", "fileCount": 3, "unpackedSize": 2155060, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZxP/McWtOmnoPQuXg6ljXwqzi0rsELc8Vi8osW9OEugIhANwc39v2mzgGk9L2WJ7OKKPrn6MV8kRMqr9ABBnRGiW2"}], "size": 925878}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.19.1_1722056037834_0.3483589711179551"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-27T04:53:58.045Z", "publish_time": 1722056038045, "_source_registry_name": "default"}, "4.19.2": {"name": "@rollup/rollup-android-arm64", "version": "4.19.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.19.2", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-k0OC/b14rNzMLDOE6QMBCjDRm3fQOHAL8Ldc9bxEWvMo4Ty9RY6rWmGetNTWhPo+/+FNd1lsQYRd0/1OSix36A==", "shasum": "5d3c8c2f9742d62ba258cc378bd2d4720f0c431c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.2.tgz", "fileCount": 3, "unpackedSize": 2155060, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCa1d8AgRLWoy5v5I3Tx3GTy+vPKJc6w8OD3z8Y8/MnawIgfgvsuvG2GsXE1oeG9kjL2itsJCFqOwM8nI5xoVj2i7E="}], "size": 925879}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.19.2_1722501170201_0.12586707424354215"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-01T08:32:50.418Z", "publish_time": 1722501170418, "_source_registry_name": "default"}, "4.20.0": {"name": "@rollup/rollup-android-arm64", "version": "4.20.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.20.0", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-u00Ro/nok7oGzVuh/FMYfNoGqxU5CPWz1mxV85S2w9LxHR8OoMQBuSk+3BKVIDYgkpeOET5yXkx90OYFc+ytpQ==", "shasum": "64161f0b67050023a3859e723570af54a82cff5c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.20.0.tgz", "fileCount": 3, "unpackedSize": 2152708, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPnn39Ok5uY8eziIvTkafNfcjHFElHHrxLgSd1W0lE4AiBfxoHl9JdHH9RkBixe6DEGxR+xAOt71hg+29GoKc5TCQ=="}], "size": 924001}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.20.0_1722660527667_0.1766018396669995"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-03T04:48:47.872Z", "publish_time": 1722660527872, "_source_registry_name": "default"}, "4.21.0": {"name": "@rollup/rollup-android-arm64", "version": "4.21.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.21.0", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-a1sR2zSK1B4eYkiZu17ZUZhmUQcKjk2/j9Me2IDjk1GHW7LB5Z35LEzj9iJch6gtUfsnvZs1ZNyDW2oZSThrkA==", "shasum": "7e7157c8543215245ceffc445134d9e843ba51c0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.0.tgz", "fileCount": 3, "unpackedSize": 2106628, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiO6sJh+iULwmSGkD6sZa8VXSgmu7PhAqacz+l/g7VBQIgJDbMdwF9dwLkPeWini5QL9v1IoKsSICYrM1izVPJ+bA="}], "size": 909099}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.21.0_1723960531765_0.7701309954639484"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-18T05:55:32.026Z", "publish_time": 1723960532026, "_source_registry_name": "default"}, "4.21.1": {"name": "@rollup/rollup-android-arm64", "version": "4.21.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.21.1", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-t1lLYn4V9WgnIFHXy1d2Di/7gyzBWS8G5pQSXdZqfrdCGTwi1VasRMSS81DTYb+avDs/Zz4A6dzERki5oRYz1g==", "shasum": "fa3693e4674027702c42fcbbb86bbd0c635fd3b9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.1.tgz", "fileCount": 3, "unpackedSize": 2100228, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCE+9W5La6ZgK2z96xuDfVaiTrWxrwZc51YqKNcuVaazwIhAKXakjGOK3UF+RrYW6fxjkW/3kO+ZPAGH6J+0JLboBVM"}], "size": 908269}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.21.1_1724687650268_0.36515582423633797"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-26T15:54:10.447Z", "publish_time": 1724687650447, "_source_registry_name": "default"}, "4.21.2": {"name": "@rollup/rollup-android-arm64", "version": "4.21.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.21.2", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-xGU5ZQmPlsjQS6tzTTGwMsnKUtu0WVbl0hYpTPauvbRAnmIvpInhJtgjj3mcuJpEiuUw4v1s4BimkdfDWlh7gA==", "shasum": "baf1a014b13654f3b9e835388df9caf8c35389cb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.2.tgz", "fileCount": 3, "unpackedSize": 2097988, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiWUq9i22MeaiiNxiwlxu/J1+NrfCuMksu5yYFvGuNdwIgVbO/5e/BzvYexvxeVUwNwOLMoHmjW630VMc3H3ubU4Q="}], "size": 906576}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.21.2_1725001462953_0.47319863400771256"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-30T07:04:23.266Z", "publish_time": 1725001463266, "_source_registry_name": "default"}, "4.21.3": {"name": "@rollup/rollup-android-arm64", "version": "4.21.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.21.3", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-zrt8ecH07PE3sB4jPOggweBjJMzI1JG5xI2DIsUbkA+7K+Gkjys6eV7i9pOenNSDJH3eOr/jLb/PzqtmdwDq5g==", "shasum": "b94b6fa002bd94a9cbd8f9e47e23b25e5bd113ba", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.3.tgz", "fileCount": 3, "unpackedSize": 2050852, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwzPw1V5TIrFgKfdpeHqWcmSDtMJrRXDzKLcY4cdjGfwIhAID7z0HsdvkNbnmFMEPu8yzFk8vje9VYOuoCRhzNnGfW"}], "size": 888615}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.21.3_1726124746502_0.539882672756643"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-12T07:05:46.689Z", "publish_time": 1726124746689, "_source_registry_name": "default"}, "4.22.0": {"name": "@rollup/rollup-android-arm64", "version": "4.22.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.0", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ETHi4bxrYnvOtXeM7d4V4kZWixib2jddFacJjsOjwbgYSRsyXYtZHC4ht134OsslPIcnkqT+TKV4eU8rNBKyyQ==", "shasum": "7a44160a14017fa744912d7037c7d81d6f8a46e7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.0.tgz", "fileCount": 3, "unpackedSize": 2050988, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBD3sf50esIH0p6sqOovPWrzl9+JfREjntUi2qg7+7keAiBU5KzY2GKeISl03ffrX0S6DYd84ALnKr5+8C2DwBY7Fw=="}], "size": 887050}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.0_1726721728535_0.7595177155062254"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-19T04:55:28.775Z", "publish_time": 1726721728775, "_source_registry_name": "default"}, "4.22.1": {"name": "@rollup/rollup-android-arm64", "version": "4.22.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.1", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-Cr/dpKRc4tjK13SCZJrSDXSaKjL/fekn04BWMCJ+Pj4vPCp8rixvtArrnWUYycOdRNi7kx3MSClcvEP7C2nvCw==", "shasum": "80bcf4a0d65c7c837a6c8196eee3c10052617e1b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.1.tgz", "fileCount": 3, "unpackedSize": 2051820, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG3sptHkBAwarE9utFcETkC+FqR0qai7tfMKA5/NbDyaAiEAjzUywmuA/DT+evRW+P74kK9xZhH5w14wZS7TEkUpVZc="}], "size": 888852}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.1_1726820510477_0.9508128985874043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T08:21:50.819Z", "publish_time": 1726820510819, "_source_registry_name": "default"}, "4.22.2": {"name": "@rollup/rollup-android-arm64", "version": "4.22.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.2", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-I+B1v0a4iqdS9DvYt1RJZ3W+Oh9EVWjbY6gp79aAYipIbxSLEoQtFQlZEnUuwhDXCqMxJ3hluxKAdPD+GiluFQ==", "shasum": "d97ed02a950061adc2056d6d2d6df8f05d877ae9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.2.tgz", "fileCount": 3, "unpackedSize": 2051820, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCB5ykC2BDvG4oKTViwr6wxofcErUmo1Dz740xsFSOgtQIhAOFZiIEoG3bsnKkW5/iX5+JrhlBldEcOTV9XW590PSSq"}], "size": 888852}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.2_1726824822221_0.8892680988826835"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T09:33:42.450Z", "publish_time": 1726824822450, "_source_registry_name": "default"}, "4.22.3-0": {"name": "@rollup/rollup-android-arm64", "version": "4.22.3-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.3-0", "readmeFilename": "README.md", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-/VeeTIa8AvGO2yTqKG1Yrg3EW5EmJMgLC9gd2c2mlqaJQF0U+v5f8V0ZTK5rba8VzXvAQ96tV7aZhhckOciHqA==", "shasum": "c5d07d73abf36a963d20961115d672e02093550a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.3-0.tgz", "fileCount": 3, "unpackedSize": 2051822, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7dvhIBSao6hj+RDQEW9Hbxa1F+ziY4FlPq62cFoJNSAIgcfbYto4H+//6PMH87bl3bGHwTqCmtr8Fe743kK4Wo80="}], "size": 888854}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.3-0_1726843674425_0.4075610255137203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T14:47:54.707Z", "publish_time": 1726843674707, "_source_registry_name": "default"}, "4.22.3": {"name": "@rollup/rollup-android-arm64", "version": "4.22.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.3", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-52cRgrMaz2JxX0a/wfnnrkToHGzMWrI18X5n+e73Hz/BFPEV4QhglW5z8cOs6kyYHB9tQPo7kjMjFhxwj72SXg==", "shasum": "d1e3f51a2b17bbae91d5bc2c3cb5901a6075fb48", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.3.tgz", "fileCount": 3, "unpackedSize": 2051820, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKMdztY9TKuhlKGV6NB3cHywrCCigI2whep0JwRyRhtAIhAMEr5z2C1wpN7r/qJ8D7qOLvUaOWVvV/0avSdEyiU6V4"}], "size": 888852}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.3_1726894985631_0.7422389324522587"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-21T05:03:05.893Z", "publish_time": 1726894985893, "_source_registry_name": "default"}, "4.22.4": {"name": "@rollup/rollup-android-arm64", "version": "4.22.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.4", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-VXoK5UMrgECLYaMuGuVTOx5kcuap1Jm8g/M83RnCHBKOqvPPmROFJGQaZhGccnsFtfXQ3XYa4/jMCJvZnbJBdA==", "shasum": "654ca1049189132ff602bfcf8df14c18da1f15fb", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.4.tgz", "fileCount": 3, "unpackedSize": 2051820, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCXT3ljW+3Qm/o4YxdL3yojvL7dVqbGYeHYsLA5VjkvAIhAPqoSHb7FRPV3/4co+sBWngWCCHQai6yNXYXxt+GBm5I"}], "size": 888851}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.4_1726899079462_0.14405384188084724"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-21T06:11:19.677Z", "publish_time": 1726899079677, "_source_registry_name": "default"}, "4.22.5": {"name": "@rollup/rollup-android-arm64", "version": "4.22.5", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.22.5", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-S4pit5BP6E5R5C8S6tgU/drvgjtYW76FBuG6+ibG3tMvlD1h9LHVF9KmlmaUBQ8Obou7hEyS+0w+IR/VtxwNMQ==", "shasum": "08270faef6747e2716d3e978a8bbf479f75fb19a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.5.tgz", "fileCount": 3, "unpackedSize": 2054124, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPDJeyYAVTJE/BIFVa6cgswo/arRVlazN3p085HaexbAiEAztwBdzyYpjbSFAAA2zbWTGUHyKtWSDFet9VM16OlyXw="}], "size": 890633}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.22.5_1727437692678_0.15506188216866268"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-27T11:48:12.946Z", "publish_time": 1727437692946, "_source_registry_name": "default"}, "4.23.0": {"name": "@rollup/rollup-android-arm64", "version": "4.23.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.23.0", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-rEFtX1nP8gqmLmPZsXRMoLVNB5JBwOzIAk/XAcEPuKrPa2nPJ+DuGGpfQUR0XjRm8KjHfTZLpWbKXkA5BoFL3w==", "shasum": "0594aab393e7b13c4cd7f21bb72d953c128cdae4", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.23.0.tgz", "fileCount": 3, "unpackedSize": 2054124, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQ3pf6YKeZciu+NVjX8xN4Xcn7iSCNUB68qrQefXwsagIhANYAm/LQ7TG4SsnbOZKtJfAfBKTXzzyLWd+6qM1oLJ09"}], "size": 890633}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.23.0_1727766603278_0.3014750637066952"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-01T07:10:03.524Z", "publish_time": 1727766603524, "_source_registry_name": "default"}, "4.24.0": {"name": "@rollup/rollup-android-arm64", "version": "4.24.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.24.0", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ijLnS1qFId8xhKjT81uBHuuJp2lU4x2yxa4ctFPtG+MqEE6+C5f/+X/bStmxapgmwLwiL3ih122xv8kVARNAZA==", "shasum": "2ffaa91f1b55a0082b8a722525741aadcbd3971e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.0.tgz", "fileCount": 3, "unpackedSize": 2062636, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFCNMjKztJP/7lhBi8etsOmaQA3HABDX2wgvJBbIu6MAiEA4KzUDt3Whj0I1H6jrAueYPO+tNybesdTqx3pr+e6VSA="}], "size": 894375}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.24.0_1727861836919_0.636535351552231"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-02T09:37:17.130Z", "publish_time": 1727861837130, "_source_registry_name": "default"}, "4.24.1": {"name": "@rollup/rollup-android-arm64", "version": "4.24.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.24.1", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-y65R3hM9sJVAXV3qh/dJ5o2OCVzwy6d994qmi+rGw1i1onYY5AoV9dREDYoizaZvc9esEqOs07CyFgPzz4DBqg==", "shasum": "6f8fee7fa7dc4840c95cf2a270e37faeb0512a30", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.1.tgz", "fileCount": 3, "unpackedSize": 2233788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjRktxEVd0UM3aFdFw4nK+KuNt9mJ6AuL4jBnoTvgrRgIgMBzMuBsBoGH1kRTiiqW9bvUqfTSNtDyZ3zP9AF8Eq9o="}], "size": 993999}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.24.1_1730011379282_0.8749667129664866"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T06:42:59.567Z", "publish_time": 1730011379567, "_source_registry_name": "default"}, "4.24.2": {"name": "@rollup/rollup-android-arm64", "version": "4.24.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.24.2", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-iZoYCiJz3Uek4NI0J06/ZxUgwAfNzqltK0MptPDO4OR0a88R4h0DSELMsflS6ibMCJ4PnLvq8f7O1d7WexUvIA==", "shasum": "160975402adf85ecd58a0721ad60ae1779a68147", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.2.tgz", "fileCount": 3, "unpackedSize": 2233788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvaoNjwWNeKt2BUhJ9AQ6BpigPK7V1nPvIoVUHnHmqIgIgcnEXfCbdwcRYwiqrpyCYCoE2WdO4SO/ZQRT5L/yqKKk="}], "size": 993999}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.24.2_1730043604906_0.3989607417404504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T15:40:05.148Z", "publish_time": 1730043605148, "_source_registry_name": "default"}, "4.25.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.25.0-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.25.0-0", "readmeFilename": "README.md", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-00AtPMShFaBiWyXAZzaWEzc+P0cAFVtl+I41njsOCOkVLl7Y4qVmUVkvlYdQ8qSBXAaSRD8GsWpBNU4lvgDgkw==", "shasum": "fb4978a05cae9926793f6ff0b74404b36277fc7c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.25.0-0.tgz", "fileCount": 3, "unpackedSize": 2233790, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1glAzKpKEnIWVcB6nrRgcgduGw0qw80F8ujgrhTUeDQIgRPBnIa+yUn0603NsLIIb5p3SIVB2afodcloXFFyF7HQ="}], "size": 994002}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.25.0-0_1730182507050_0.6589198460705343"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T06:15:07.356Z", "publish_time": 1730182507356, "_source_registry_name": "default"}, "4.24.3": {"name": "@rollup/rollup-android-arm64", "version": "4.24.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.24.3", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-iAHpft/eQk9vkWIV5t22V77d90CRofgR2006UiCjHcHJFVI1E0oBkQIAbz+pLtthFw3hWEmVB4ilxGyBf48i2Q==", "shasum": "197e3bc01c228d3c23591e0fcedca91f8f398ec1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.3.tgz", "fileCount": 3, "unpackedSize": 2233788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDb6ak13gB5fgAhu/RDybIx16+0H4dfZfDsiJ64/V7/ygIhAPvNBGC2JYyvr6gZ4GZri3aqWRFVIZBjQu/A+KxJiiqE"}], "size": 993999}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.24.3_1730211244516_0.9338505513389832"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-29T14:14:04.822Z", "publish_time": 1730211244822, "_source_registry_name": "default"}, "4.24.4": {"name": "@rollup/rollup-android-arm64", "version": "4.24.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.24.4", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-j4nrEO6nHU1nZUuCfRKoCcvh7PIywQPUCBa2UsootTHvTHIoIu2BzueInGJhhvQO/2FTRdNYpf63xsgEqH9IhA==", "shasum": "96e01f3a04675d8d5973ab8d3fd6bc3be21fa5e1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.4.tgz", "fileCount": 3, "unpackedSize": 2224124, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHM2YD2FHb8b8uQJdXH1DjYEUh9deAPo+g6ZLER9Mt+zAiBnvfTntDUBTioUOLn+P0POM+P8/oyOVhw/BItsHVwpkQ=="}], "size": 986522}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.24.4_1730710026238_0.21897442428066882"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-04T08:47:06.454Z", "publish_time": 1730710026454, "_source_registry_name": "default"}, "4.25.0": {"name": "@rollup/rollup-android-arm64", "version": "4.25.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.25.0", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-/Y76tmLGUJqVBXXCfVS8Q8FJqYGhgH4wl4qTA24E9v/IJM0XvJCGQVSW1QZ4J+VURO9h8YCa28sTFacZXwK7Rg==", "shasum": "04f679231acf7284f1f8a1f7250d0e0944865ba8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.25.0.tgz", "fileCount": 3, "unpackedSize": 2228732, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkpJdrxiKw00S5d/zf5Na/OtHo+GUmqVJiy9Sw7bP25AiAUKEEIdSQvEBFvhukHH0In5hZu6hTdeUn8wuaCY50aAw=="}], "size": 989583}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.25.0_1731141439417_0.2754948626835616"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-09T08:37:19.686Z", "publish_time": 1731141439686, "_source_registry_name": "default"}, "4.26.0": {"name": "@rollup/rollup-android-arm64", "version": "4.26.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.26.0", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-YJa5Gy8mEZgz5JquFruhJODMq3lTHWLm1fOy+HIANquLzfIOzE9RA5ie3JjCdVb9r46qfAQY/l947V0zfGJ0OQ==", "shasum": "196a2379d81011422fe1128e512a8811605ede16", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.26.0.tgz", "fileCount": 3, "unpackedSize": 2228732, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5S+nuK1kihEBdC2NJ4IuDaBZxtwoWC6Cgc08OaJSzkwIgEoLFCtdLxCdzxNc710RQp/d8f+Cq92n2WNR+ErpwUks="}], "size": 989584}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.26.0_1731480296725_0.12758932743096207"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-13T06:44:56.953Z", "publish_time": 1731480296953, "_source_registry_name": "default"}, "4.27.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.27.0-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.0-0", "readmeFilename": "README.md", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-0VIktojlbmJL/+AWJj8Qz3Dd6L3uR7rSzJ292XLb0bUB9mumsd9mWhNSOLCHpqhfaM3cgbi2wg1kJEKmwHTsbA==", "shasum": "b4d5ef574b5583177ec2ed70a943fa5abe727833", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.0-0.tgz", "fileCount": 3, "unpackedSize": 2228734, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAtO5ujveHSYflDlP+YHGtjXXCLg2A9yJqYVg54X/qLlAiAltZzYGVZiVUiwLcNUUDVAFmgNIsKa/os4bkCI2jQS/w=="}], "size": 989587}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.0-0_1731481391587_0.08564498625549177"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-13T07:03:11.766Z", "publish_time": 1731481391766, "_source_registry_name": "default"}, "4.27.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.27.0-1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.0-1", "readmeFilename": "README.md", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-cx2VlRa6Q7IA0ma0E9Ufh8jGaaoyrR5tFIg+E4vJPmi2UnxxqkXGyrxTAn62DQD/zz8aegPfl2MJz1CFtJZ0Pw==", "shasum": "0c5534d443f440589a84fe424951f5a24553b997", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.0-1.tgz", "fileCount": 3, "unpackedSize": 2228734, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsMyhQTm1ueKmMfy41w3R1HbtBecpXFqsi+wYFlnVPEAiEA+vyRbv+glg+z8cax7fRm6GlI7ticmVNG5OVPJhPqB0I="}], "size": 989587}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.0-1_1731565988326_0.5662679670426896"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-14T06:33:08.575Z", "publish_time": 1731565988575, "_source_registry_name": "default"}, "4.27.0": {"name": "@rollup/rollup-android-arm64", "version": "4.27.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.0", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-cBUOny8GNXP++gN00Bo5L04I2oqUEFAU0OSDb+4hqp4/R/pZL/zlGzp7lJkhtPX52Rj+PIe0S8aOqhK4hztxHQ==", "shasum": "298172fd28b17bc745efad8128a4a1365e80b747", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.0.tgz", "fileCount": 3, "unpackedSize": 2231740, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1LSFA9FIS2JTXjWV8PU3M4F7Pq+WnbeabxTAy8PMGnAiEAizxR+vfZzxYCJ+EMcJIqTn3qUTKx3KboiG1CUzhzsUg="}], "size": 989149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.0_1731667233601_0.5257661846832868"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T10:40:33.908Z", "publish_time": 1731667233908, "_source_registry_name": "default"}, "4.27.1-0": {"name": "@rollup/rollup-android-arm64", "version": "4.27.1-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.1-0", "readmeFilename": "README.md", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Bytc1IdUMhbNK6CayAW2+W8/AzwrA1p4cCvw4SwVLKoqulfm81IbvPv15N0uYVRaq8HTwfvI/5XcwxRMtdRRwg==", "shasum": "32e941a4d6cd0d09ecf3a4b435b7292d3f67bef3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.1-0.tgz", "fileCount": 3, "unpackedSize": 2231742, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbes1pnbsgbf/xaWcmJloG8wYAkxFeI02vQZ6CzFzLJAiAP85asTeTSun5jNqeBmDG+Rnmk+Pc8lO1V1bidnvsLjQ=="}], "size": 989150}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.1-0_1731677284968_0.42149830637322516"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T13:28:05.232Z", "publish_time": 1731677285232, "_source_registry_name": "default"}, "4.27.1-1": {"name": "@rollup/rollup-android-arm64", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "fe61b1a6c1203c201fb7b5a91811c951095ea993", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-c3lA8NyEAq4ceVIJgXtjdVCxYR/Xp4y6sxAfCKZhM9Ve4ME98yG2/RvFdJOImO3eKtxqRAJRX1JZCZCnlcxcpQ==", "signatures": [{"sig": "MEQCIFrgqbp6cQi2mR9IuDsBodhUTsRqPENmV+8PkqJ/mI9eAiBeT9vGKKDDgwHxPHGvlvwIHTRQxsnu2DNGps4vECM1Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231742, "size": 989150}, "main": "./rollup.android-arm64.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.1-1_1731685079682_0.796434418202028", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-11-15T15:37:59.937Z", "publish_time": 1731685079937, "_source_registry_name": "default"}, "4.27.1": {"name": "@rollup/rollup-android-arm64", "version": "4.27.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.1", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WXrtqF2zOOTGjE6pNDF5oYPBlwpopSGaQPIZULbMKvchT7OyYzmUnEim0ICNAlz4qHYs4vxJOn1S4aLd930EKA==", "shasum": "b4486a7cc1378ff89547e51a7021f6dd4b6eec59", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.1.tgz", "fileCount": 3, "unpackedSize": 2231740, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHp8JN0Pz1J4FdvhV2CBEHOArFaAA2CYUZGB/VqhZ1P9AiEA7B8q+GoY/BKyeSDiMA2TcZWIJx8iIjtgx5Qs4H6XCJc="}], "size": 989149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.1_1731686859112_0.2080017475563345"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T16:07:39.333Z", "publish_time": 1731686859333, "_source_registry_name": "default"}, "4.27.2": {"name": "@rollup/rollup-android-arm64", "version": "4.27.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.2", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-xsPeJgh2ThBpUqlLgRfiVYBEf/P1nWlWvReG+aBWfNv3XEBpa6ZCmxSVnxJgLgkNz4IbxpLy64h2gCmAAQLneQ==", "shasum": "272fcb6416c60b2225192379fa2c5e63b48f19dc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.2.tgz", "fileCount": 3, "unpackedSize": 2231740, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAIOnXKklP4RCP3y5TpNpAOz7j/d6NN0zQSYOZ3J5YcQIhAOalXIeA6QUyGGlFhZEfDnknnq1Tjc7kJ3DspkFGE2pt"}], "size": 989149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.2_1731691200310_0.943570590161529"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-15T17:20:00.550Z", "publish_time": 1731691200550, "_source_registry_name": "default"}, "4.27.3": {"name": "@rollup/rollup-android-arm64", "version": "4.27.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.3", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-LJc5pDf1wjlt9o/Giaw9Ofl+k/vLUaYsE2zeQGH85giX2F+wn/Cg8b3c5CDP3qmVmeO5NzwVUzQQxwZvC2eQKw==", "shasum": "de840660ab65cf73bd6d4bc62d38acd9fc94cd6c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.3.tgz", "fileCount": 3, "unpackedSize": 2231740, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXy076pzAccqT2vEkr3rcwlGhgbIDKfxJ4ebwIAoKNFgIhAI+sLbn+00MeXwNB0sA2cTkRnnlMRmKf8KzbOjsqSSgd"}], "size": 989149}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.3_1731947973656_0.5157224851403006"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-18T16:39:33.928Z", "publish_time": 1731947973928, "_source_registry_name": "default"}, "4.27.4": {"name": "@rollup/rollup-android-arm64", "version": "4.27.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.27.4", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-wzKRQXISyi9UdCVRqEd0H4cMpzvHYt1f/C3CoIjES6cG++RHKhrBj2+29nPF0IB5kpy9MS71vs07fvrNGAl/iA==", "shasum": "0474250fcb5871aca952e249a0c3270fc4310b55", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.4.tgz", "fileCount": 3, "unpackedSize": 2232892, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEgighi9+m8MWpyOtz+5gW1oceZcUogTByTFgQeya7igAiBPmCwx/BUDV03GbCHhPiNSTGAW1dANAjrUN9PBDz/YMw=="}], "size": 992739}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.27.4_1732345218748_0.09955926445444274"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-23T07:00:18.996Z", "publish_time": 1732345218996, "_source_registry_name": "default"}, "4.28.0": {"name": "@rollup/rollup-android-arm64", "version": "4.28.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.28.0", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-eiNkznlo0dLmVG/6wf+Ifi/v78G4d4QxRhuUl+s8EWZpDewgk7PX3ZyECUXU0Zq/Ca+8nU8cQpNC4Xgn2gFNDA==", "shasum": "78a2b8a8a55f71a295eb860a654ae90a2b168f40", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.28.0.tgz", "fileCount": 3, "unpackedSize": 2235492, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCP7OQ0BXc/3Hrp6L40jVQxkAcbyBRkas48tAVz2Mi0LQIhAJAUbtwFgJPvf6VAzx9HlDR+Dv9R98EOuUVhsMC0EIrD"}], "size": 992120}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.28.0_1732972545029_0.1506303589077458"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-30T13:15:45.262Z", "publish_time": 1732972545262, "_source_registry_name": "default"}, "4.28.1": {"name": "@rollup/rollup-android-arm64", "version": "4.28.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.28.1", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-EbkK285O+1YMrg57xVA+Dp0tDBRB93/BZKph9XhMjezf6F4TpYjaUSuPt5J0fZXlSag0LmZAsTmdGGqPp4pQFA==", "shasum": "17ea71695fb1518c2c324badbe431a0bd1879f2d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.28.1.tgz", "fileCount": 3, "unpackedSize": 2231332, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC34a/ZDbvA3VjnVTvQuD1pAm8i4WKPUheTkU3+y+61gQIhAI2SUgIoD3M//02E3A3MP6Ynk76nVF2Eq7+GzEj84blT"}], "size": 989620}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rollup-android-arm64_4.28.1_1733485494269_0.871678849031668"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T11:44:54.527Z", "publish_time": 1733485494527, "_source_registry_name": "default"}, "4.29.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.29.0-0", "readmeFilename": "README.md", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-eMjbeyy3kIiS2WWeHkboMdCTMYlIBU2ocZHhUU4UEpxcAe+vW8j7u8vOSIqXEHchLShjIFqBvUFi9frbSCVMZg==", "shasum": "5d2a9574e0721f67108c4456de2e8b92c1074fb6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0-0.tgz", "fileCount": 3, "unpackedSize": 2223270, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwl4ab1r1X5uQW13ZWHlFW6D/FsXxbNvsVfNg3UwqT5AIhALvsWkihGS/9yXJ+Fr2bFWQPNI7e4nzdM1uyUqnttM10"}], "size": 987887}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.29.0-0_1734331190994_0.014896512416635366"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-16T06:39:51.189Z", "publish_time": 1734331191189, "_source_registry_name": "default"}, "4.29.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0-1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.29.0-1", "readmeFilename": "README.md", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-6fxkd9JXTBHnOiq2JjcuOm6sPOtLOAc2NTUc++qAxfY20sLslOdZ/cxzNRMd6iu3Ra9ZV0vntz7I8KRZFSgEhA==", "shasum": "21b06d6f1705de53b8d433282ea69b6ea277902a", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0-1.tgz", "fileCount": 3, "unpackedSize": 2223270, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcfNxdhUmh61kKRAzOgF+dCxetd0tkr/BguCOgfxnvHwIhAP9Y98kDcRVTIqN3BzM2BvOKNUe0xEyTIjkHRp7Elnqk"}], "size": 987887}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.29.0-1_1734590249554_0.6901523073032452"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-19T06:37:29.883Z", "publish_time": 1734590249883, "_source_registry_name": "default"}, "4.29.0-2": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0-2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.29.0-2", "readmeFilename": "README.md", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bp60JcErQYVcm5vIznWqWdJ/JvsGiOMPpn4q6UNLsZ3+nuf5Ognm+SKZD5bsT3/ye8ZiGynMiodU48pF8qnH/Q==", "shasum": "0649cf001102808c241366b568b09c75fb8468cc", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0-2.tgz", "fileCount": 3, "unpackedSize": 2223270, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlkqLjBNTuGoGc2L8n5mlN5ZyV3S7lxYd5PriQSI6ejAiEAuaZ7LkUv9yQeJTpExXJvsFS8tB/6E1F12WDwKs5C2eE="}], "size": 987887}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.29.0-2_1734677760249_0.7436522444853646"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T06:56:00.500Z", "publish_time": 1734677760500, "_source_registry_name": "default"}, "4.29.0": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.29.0", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-L/7oX07eY6ACt2NXDrku1JIPdf9VGV/DI92EjAd8FRDzMMub5hXFpT1OegBqimJh9xy9Vv+nToaVtZp4Ku9SEA==", "shasum": "039ab290f7a11a49ec6d5e7cb3031554f3e78372", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0.tgz", "fileCount": 3, "unpackedSize": 2232420, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDUdv51DX5uSQNc38wywaFtZ3NUQG6lGXtJ9DqRk/BDAIhANY1sNigacf0kHIyQ2xvMj7eLBSi1qXcQQhsULjdmzoh"}], "size": 988666}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.29.0_1734719842317_0.9690313048824604"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-20T18:37:22.548Z", "publish_time": 1734719842548, "_source_registry_name": "default"}, "4.29.1": {"name": "@rollup/rollup-android-arm64", "version": "4.29.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.29.1", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-CaRfrV0cd+NIIcVVN/jx+hVLN+VRqnuzLRmfmlzpOzB87ajixsN/+9L5xNmkaUUvEbI5BmIKS+XTwXsHEb65Ew==", "shasum": "bd1a98390e15b76eeef907175a37c5f0f9e4d214", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.1.tgz", "fileCount": 3, "unpackedSize": 2232420, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGM/X8LULpI4tuYEybButjYgZmnvtD7H02qOYPKOyuxoAiEAoFLLfbexbcu7tzri4fkHluo2fbuMs5inUDKw72sZyqQ="}], "size": 988666}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.29.1_1734765360089_0.016137248045506647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:16:00.320Z", "publish_time": 1734765360320, "_source_registry_name": "default"}, "4.30.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.30.0-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.30.0-0", "readmeFilename": "README.md", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-T5ZlI7GED9SmYrmz3JBVTwpE71gvs9f6upodLjRwOwbEs6Jlpj9uRpOLVJq/8sZJi0k5K0dyKjW3unobPDR8Kw==", "shasum": "74ad77bc039e9e1d3ab48a5ec24fdc984f6b7730", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.0-0.tgz", "fileCount": 3, "unpackedSize": 2232422, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUVpov8dd3/fgdaPnroN/l7Ak6jVVBGn4e0ZDOOWx8fAIhAPvyXmU975RsYMfBo5ZrRdh5KCOGbyofLt6dAybXSr/z"}], "size": 988665}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.30.0-0_1734765432497_0.6682178305957569"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-21T07:17:12.727Z", "publish_time": 1734765432727, "_source_registry_name": "default"}, "4.30.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.30.0-1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.30.0-1", "readmeFilename": "README.md", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/i98j75INBv+uz6Q31rqf+CSFIxDBghCH7xJ9ZjJIdpzt/5Y+WFA8tZzO44ylhOlZ3EMLcTZbhluogASuDjxKw==", "shasum": "76091ed17f622e0928c2ac2800340bc9e27a1d5f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.0-1.tgz", "fileCount": 3, "unpackedSize": 2230438, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHE16wCINJSNng0mmui5Xyj4NGLgZdtx9JNAAIbLbYnvAiACN8kFzhj7oMKdhzNlrei4318ueOqSCDdWCOLq3XSDAg=="}], "size": 989646}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.30.0-1_1735541534643_0.18495486156156837"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-30T06:52:14.865Z", "publish_time": 1735541534865, "_source_registry_name": "default"}, "4.29.2": {"name": "@rollup/rollup-android-arm64", "version": "4.29.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.29.2", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-mKRlVj1KsKWyEOwR6nwpmzakq6SgZXW4NUHNWlYSiyncJpuXk7wdLzuKdWsRoR1WLbWsZBKvsUCdCTIAqRn9cA==", "shasum": "cbc7e636a7aab984161fc045039bf3c6abb50083", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.2.tgz", "fileCount": 3, "unpackedSize": 2230436, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEFOYMJ8jHJluQAdg6EfQGabWF4jyY7zb/WTT2plOVAyAiEAv+X1BoREZCe6H/81ivi3MS6b25TAVC6lRl43FRNz5fc="}], "size": 989645}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.29.2_1736078860483_0.6088588582290888"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-05T12:07:40.719Z", "publish_time": 1736078860719, "_source_registry_name": "default"}, "4.30.0": {"name": "@rollup/rollup-android-arm64", "version": "4.30.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.30.0", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vqrQdusvVl7dthqNjWCL043qelBK+gv9v3ZiqdxgaJvmZyIAAXMjeGVSqZynKq69T7062T5VrVTuikKSAAVP6A==", "shasum": "7e5764268d3049b7341c60f1c650f1d71760a5b2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.0.tgz", "fileCount": 3, "unpackedSize": 2230436, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSTYxddmPW8OkAMzIxUiEGlbuZ0KwKKvNfdToeu++NGAIgI9cGgNtois3qAp4Z2Rwi1NxLoQvwmzwzgXHGoCLQiS4="}], "size": 989645}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.30.0_1736145399213_0.5558140592790926"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-06T06:36:39.426Z", "publish_time": 1736145399426, "_source_registry_name": "default"}, "4.30.1": {"name": "@rollup/rollup-android-arm64", "version": "4.30.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.30.1", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/NA2qXxE3D/BRjOJM8wQblmArQq1YoBVJjrjoTSBS09jgUisq7bqxNHJ8kjCHeV21W/9WDGwJEWSN0KQ2mtD/w==", "shasum": "9d81ea54fc5650eb4ebbc0a7d84cee331bfa30ad", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.1.tgz", "fileCount": 3, "unpackedSize": 2230436, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLhXzWhnYHwYwOKDZrcBUMli8s0A0KiiORGylyC10CMgIgaEJaxeRBB9wXCyYS0RI8Xi5qV7ipqyTxgLyVeKmw/b4="}], "size": 989645}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.30.1_1736246150467_0.2660670908749456"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-07T10:35:50.707Z", "publish_time": 1736246150707, "_source_registry_name": "default"}, "4.31.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.31.0-0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.31.0-0", "readmeFilename": "README.md", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dXA8prsULJy0lJfNUzKI47pGVJcwlnutl7wIBV4zkW7+GtY9c2KygEO2jzrXZwjVP0x3FxVB6d8mzgR9x97LXw==", "shasum": "cd6bcba2a52134298a0d73c2d6027c982acd4340", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.31.0-0.tgz", "fileCount": 3, "unpackedSize": 2249126, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4gFE0LoaMS7XDc8WUdrioqqtYsH1o4Dkzi7ovA+WlIwIhAJXEEF34mSVvZUwN6niKqoz8Ry3ZiVWcfqtpSkTYmlLv"}], "size": 993954}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.31.0-0_1736834259784_0.05172144638639997"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-14T05:57:40.004Z", "publish_time": 1736834260004, "_source_registry_name": "default"}, "4.31.0": {"name": "@rollup/rollup-android-arm64", "version": "4.31.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.31.0", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-iBbODqT86YBFHajxxF8ebj2hwKm1k8PTBQSojSt3d1FFt1gN+xf4CowE47iN0vOSdnd+5ierMHBbu/rHc7nq5g==", "shasum": "25c4d33259a7a2ccd2f52a5ffcc0bb3ab3f0729d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.31.0.tgz", "fileCount": 3, "unpackedSize": 2271332, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5jgLdiwqM4hOkt3xZBbxy5kUDf5Yorz5ufTEWDIHWFwIgFhus/PHsQct6K2gTjZXZ/RX2rFlglM8p8k7Mm9QHNyE="}], "size": 996649}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.31.0_1737291405176_0.8412498667586892"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-19T12:56:45.414Z", "publish_time": 1737291405414, "_source_registry_name": "default"}, "4.32.0": {"name": "@rollup/rollup-android-arm64", "version": "4.32.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.32.0", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qhFwQ+ljoymC+j5lXRv8DlaJYY/+8vyvYmVx074zrLsu5ZGWYsJNLjPPVJJjhZQpyAKUGPydOq9hRLLNvh1s3A==", "shasum": "846a73eef25b18ff94bac1e52acab6a7c7ac22fa", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.32.0.tgz", "fileCount": 3, "unpackedSize": 2272484, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIC2+hHLhg498ZS7ggowBiijBZu0RSCuWFLGR0i3x/KgJAiEAm8kBHNGMR96TBLmTrA5ztiVABvnSXTDioERW/td/pD4="}], "size": 997053}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.32.0_1737707253204_0.5742157236869534"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-24T08:27:33.424Z", "publish_time": 1737707253424, "_source_registry_name": "default"}, "4.33.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "069959281ef5a952a263d7b5f9b1d3ea4d4aa93d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-F0n3/jvk9zRQXUgoUKjt6K0oKwc8g0EFh12vfgNWmOCQ7n+Ogp/AXcyLbNYQ+y/4vxrGCu8POfJIlKgmuw5AoQ==", "signatures": [{"sig": "MEUCIFf/RnDxVsw8H2ofU0va1kmQ2XUO0PkLSEJx8+OklOluAiEAzQYD1b4aUCXHxYI+BvXh7Cs7401vY7KcWIytYwED2sM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2272486, "size": 997058}, "main": "./rollup.android-arm64.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.33.0-0_1738053006737_0.7739420191816417", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-01-28T08:30:06.955Z", "publish_time": 1738053006955, "_source_registry_name": "default"}, "4.32.1": {"name": "@rollup/rollup-android-arm64", "version": "4.32.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.32.1", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-If3PDskT77q7zgqVqYuj7WG3WC08G1kwXGVFi9Jr8nY6eHucREHkfpX79c0ACAjLj3QIWKPJR7w4i+f5EdLH5Q==", "shasum": "b5c00344b80f20889b72bfe65d3c209cef247362", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.32.1.tgz", "fileCount": 3, "unpackedSize": 2272484, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDBmMTuwfab8+2PqbDhBcqHWMZWenyFejnxDJ2CDakBNAiBO1RZEGO37Ow/+RSCOFpZNo9hCIoaaawni9uqDEL4m1A=="}], "size": 997054}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.32.1_1738053195575_0.33088579939890694"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-28T08:33:15.767Z", "publish_time": 1738053195767, "_source_registry_name": "default"}, "4.33.0": {"name": "@rollup/rollup-android-arm64", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "530049f68ff2454435fecd23b2be356bb50994b5", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-CALerXF20lsoIVAEb/FNjnMIvF7v79PUq9NDL2y2sv2cPFC8AFJzE23BbaOvS0CPqsawaAcc+vwlju5v+mw2Pg==", "signatures": [{"sig": "MEUCIH0YfRsTyweV1xvY2Bb0rTXE8hFTuyRcWAE4aEowKY1KAiEAhRzAeqsRgW2Z5S/8VHh8fGA73fOihCNmbNBDMYlacUk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236, "size": 994925}, "main": "./rollup.android-arm64.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.33.0_1738393917041_0.47014732848551044", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-02-01T07:11:57.263Z", "publish_time": 1738393917263, "_source_registry_name": "default"}, "4.34.0": {"name": "@rollup/rollup-android-arm64", "version": "4.34.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.0", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-yVh0Kf1f0Fq4tWNf6mWcbQBCLDpDrDEl88lzPgKhrgTcDrTtlmun92ywEF9dCjmYO3EFiSuJeeo9cYRxl2FswA==", "shasum": "667165775809f35ca1eaa872b07ec4b3ab92ea90", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.0.tgz", "fileCount": 3, "unpackedSize": 2252236, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCMkkC3hSoKrWcUcdEynM+/Y4P48GOBp4qeWO+105yO8gIhAOJNCqoLWJ+GRyBEZxaOj8WUaPpRGPTQP4d9jzE8MDdH"}], "size": 994925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.0_1738399221487_0.061529830871402735"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-01T08:40:21.710Z", "publish_time": 1738399221710, "_source_registry_name": "default"}, "4.34.1": {"name": "@rollup/rollup-android-arm64", "version": "4.34.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.1", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4H5ZtZitBPlbPsTv6HBB8zh1g5d0T8TzCmpndQdqq20Ugle/nroOyDMf9p7f88Gsu8vBLU78/cuh8FYHZqdXxw==", "shasum": "cbb3cad15748794b8dfec7f1e427e633f8f06d61", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.1.tgz", "fileCount": 3, "unpackedSize": 2252236, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDVFrDgt88HsDB1RYy5NcwiPTGA6h0NWFbyRCs3k0ZiOQIhANorPr6kghBy9FGlfE8k35OR6WqBgV2VnZ/l5msTkm0D"}], "size": 994925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.1_1738565891688_0.7098209722001447"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-03T06:58:11.917Z", "publish_time": 1738565891917, "_source_registry_name": "default"}, "4.34.2": {"name": "@rollup/rollup-android-arm64", "version": "4.34.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.2", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-K5GfWe+vtQ3kyEbihrimM38UgX57UqHp+oME7X/EX9Im6suwZfa7Hsr8AtzbJvukTpwMGs+4s29YMSO3rwWtsw==", "shasum": "17f9a9a9ee57e47839a697275d9149c065f8b7d7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.2.tgz", "fileCount": 3, "unpackedSize": 2252236, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC+pp0Z1aNvib1oNED0N6+ukiRSSzSAT3Z/MM8qqDtsTQIhAOHzMIiMc0/RKmNnyaEjrAyxvfMGO/yAtaMrh1RAsD+N"}], "size": 994925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.2_1738656598984_0.12078921721912383"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-04T08:09:59.360Z", "publish_time": 1738656599360, "_source_registry_name": "default"}, "4.34.3": {"name": "@rollup/rollup-android-arm64", "version": "4.34.3", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.3", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1PqMHiuRochQ6++SDI7SaRDWJKr/NgAlezBi5nOne6Da6IWJo3hK0TdECBDwd92IUDPG4j/bZmWuwOnomNT8wA==", "shasum": "850f0962a7a98a698dfc4b7530a3932b486d84c0", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.3.tgz", "fileCount": 3, "unpackedSize": 2252236, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGEuWFYHTPb0B76JUK54L5fwhwGGh+tGVjzCKISOCkiKAiBK4ztPnOzz4hTiWs4ktEVzy5o8CFROAlwoMjyBRwk1bg=="}], "size": 994925}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.3_1738747322133_0.9553003838859675"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T09:22:02.403Z", "publish_time": 1738747322403, "_source_registry_name": "default"}, "4.34.4": {"name": "@rollup/rollup-android-arm64", "version": "4.34.4", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.4", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1aRlh1gqtF7vNPMnlf1vJKk72Yshw5zknR/ZAVh7zycRAGF2XBMVDAHmFQz/Zws5k++nux3LOq/Ejj1WrDR6xg==", "shasum": "88d8b13c7a42231f22ac26d0abb1ad4dd8d88535", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.4.tgz", "fileCount": 3, "unpackedSize": 2252236, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCd+qHQWD0cZRqW5QRyaT5+CLTAg2d+vuT+ljClGbCoUgIgZzEaQA4HYsNm3rLaPQ8llliS5EbsFcu3rLLy3V6MtXE="}], "size": 994927}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.4_1738791068852_0.31373599439482414"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-05T21:31:09.109Z", "publish_time": 1738791069109, "_source_registry_name": "default"}, "4.34.5": {"name": "@rollup/rollup-android-arm64", "version": "4.34.5", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.5", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-9/A8/ZBOprUjkrJoP9BBEq2vdSud6BPd3LChw09bJQiEZH5oN4kWIkHu90cA0Cj0cSF5cIaD76+0lA+d5KHmpQ==", "shasum": "500406f6ad1d8cf39cbdb4af9decd47d8b6e46c6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.5.tgz", "fileCount": 3, "unpackedSize": 2245004, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBo07/71R5uiysWQLnSA/sUPuvg6I2X3MN5lIucH+IJsAiEAldCqjosByhsjgGPY4p/SFyRLbPvEIdRqRyjywvb0Mss="}], "size": 990052}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.5_1738918379472_0.6488829732282451"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T08:52:59.830Z", "publish_time": 1738918379830, "_source_registry_name": "default"}, "4.34.6": {"name": "@rollup/rollup-android-arm64", "version": "4.34.6", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.6", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-E8+2qCIjciYUnCa1AiVF1BkRgqIGW9KzJeesQqVfyRITGQN+dFuoivO0hnro1DjT74wXLRZ7QF8MIbz+luGaJA==", "shasum": "88326ff46168a47851077ca0bf0c442689ec088f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.6.tgz", "fileCount": 3, "unpackedSize": 2242804, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFYDS9PEokrsqSB1PSbtO1eg83VM5jNEr7AiJPjKULNuAiEAj3fExmqZLx5q/5d+qyOYrGOr9yGVaVfjC6B5JlFKCRI="}], "size": 988659}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.6_1738945925518_0.6710103698281769"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-07T16:32:05.785Z", "publish_time": 1738945925785, "_source_registry_name": "default"}, "4.34.7": {"name": "@rollup/rollup-android-arm64", "version": "4.34.7", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.7", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-<PERSON><PERSON>JpFUueUnSp53zhAa293QBYqwm94TgYTIfXyOTtidhm5V0LbLCJQRGkQClYiX3FXDQGSvPxOTD/6rPStMMDg==", "shasum": "b1ee64bb413b2feba39803b0a1bebf2a9f3d70e1", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.7.tgz", "fileCount": 3, "unpackedSize": 2242996, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCwc7xJ7GEDQwnJFYvK9e9NAGeWjd2n4xd3ntnG2xbpxAIhAJutG9Xr1ZmVsFrs0vFKmWdaAqrXjImYvfiqj17hjUhy"}], "size": 989070}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.7_1739526836918_0.32431437277091013"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-14T09:53:57.160Z", "publish_time": 1739526837160, "_source_registry_name": "default"}, "4.34.8": {"name": "@rollup/rollup-android-arm64", "version": "4.34.8", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.8", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Gigjz7mNWaOL9wCggvoK3jEIUUbGul656opstjaUSGC3eT0BM7PofdAJaBfPFWWkXNVAXbaQtC99OCg4sJv70Q==", "shasum": "4bea6db78e1f6927405df7fe0faf2f5095e01343", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.8.tgz", "fileCount": 3, "unpackedSize": 2242996, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAQ+LGJVPzGeHV31W5lMLo9QsE+hZz8r6E/m5Pv7rbMHAiEA9VpFbXI/QA+KsgEFfgdc3i0zatMG2hPqjiIVBAsMG/w="}], "size": 989069}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.8_1739773583344_0.09990261959900582"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-17T06:26:23.632Z", "publish_time": 1739773583632, "_source_registry_name": "default"}, "4.34.9": {"name": "@rollup/rollup-android-arm64", "version": "4.34.9", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.34.9", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4KW7P53h6HtJf5Y608T1ISKvNIYLWRKMvfnG0c44M6In4DQVU58HZFEVhWINDZKp7FZps98G3gxwC1sb0wXUUg==", "shasum": "128fe8dd510d880cf98b4cb6c7add326815a0c4b", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz", "fileCount": 3, "unpackedSize": 2349420, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF9fQG87PJyUNNL45KMfncgdpEwnC4JFTa8NRYLbsV5nAiB9jFw07swE1xzww04hMQxDBUYUb0WYgcfiKLSG+fA4Cg=="}], "size": 1041516}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.34.9_1740814354464_0.2771228933979908"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-01T07:32:34.711Z", "publish_time": 1740814354711, "_source_registry_name": "default"}, "4.35.0": {"name": "@rollup/rollup-android-arm64", "version": "4.35.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.35.0", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FtKddj9XZudurLhdJnBl9fl6BwCJ3ky8riCXjEw3/UIbjmIY58ppWwPEvU3fNu+W7FUsAsB1CdH+7EQE6CXAPA==", "shasum": "fa6cdfb1fc9e2c8e227a7f35d524d8f7f90cf4db", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.35.0.tgz", "fileCount": 3, "unpackedSize": 2357484, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAaGmNG1zeT5mUjr3hMhUic7QgjVvSivzGncRH0NVmD3AiEA0vXkLRYE+HTGgF9vN7Fzp5cDo+myGR7GAPq3oMpcZ3Q="}], "size": 1041682}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.35.0_1741415081687_0.7419028646465202"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-08T06:24:41.956Z", "publish_time": 1741415081956, "_source_registry_name": "default"}, "4.36.0": {"name": "@rollup/rollup-android-arm64", "version": "4.36.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.36.0", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NyfuLvdPdNUfUNeYKUwPwKsE5SXa2J6bCt2LdB/N+AxShnkpiczi3tcLJrm5mA+eqpy0HmaIY9F6XCa32N5yzg==", "shasum": "d38163692d0729bd64a026c13749ecac06f847e8", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.36.0.tgz", "fileCount": 3, "unpackedSize": 2364332, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCitYeemzW/FUtiJuuaW0E66xbXAAWK/fSxWYKX23IPmgIhAPi6UtZhzZWgyfuaqbVdmOIaFMmfH1foiZ/jPAowId4L"}], "size": 1050147}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.36.0_1742200541053_0.45643593022680395"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-17T08:35:41.346Z", "publish_time": 1742200541346, "_source_registry_name": "default"}, "4.37.0": {"name": "@rollup/rollup-android-arm64", "version": "4.37.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.37.0", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-6U3SlVyMxezt8Y+/iEBcbp945uZjJwjZimu76xoG7tO1av9VO691z8PkhzQ85ith2I8R2RddEPeSfcbyPfD4hA==", "shasum": "6edc6ffc8af8773e4bc28c72894dd5e846b8ee6c", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.37.0.tgz", "fileCount": 3, "unpackedSize": 2356844, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCTty8DZ37xV+Qi4Zw+7TKjD+CRwYPtGwtifmOxg9z6VgIgA5lesqTqP4raBiyX5yp/MTVWm+Tq8IspU0pk5HJPp1I="}], "size": 1043596}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.37.0_1742741824856_0.9588454549122201"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-23T14:57:05.116Z", "publish_time": 1742741825116, "_source_registry_name": "default"}, "4.38.0": {"name": "@rollup/rollup-android-arm64", "version": "4.38.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.38.0", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VUsgcy4GhhT7rokwzYQP+aV9XnSLkkhlEJ0St8pbasuWO/vwphhZQxYEKUP3ayeCYLhk6gEtacRpYP/cj3GjyQ==", "shasum": "c8806f88fd6727d3cf144c4ffb00f40d451b6618", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.38.0.tgz", "fileCount": 3, "unpackedSize": 2364780, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIH1Up5u/DXqByeaJ7YhDgEdpnnUFUvEpMouZjzK0VgHHAiBi9tAVjdRLlmPK4UaFfeW3RZ+NMqYMwXgjMbSvAL5wQg=="}], "size": 1048025}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.38.0_1743229742791_0.42439130612761233"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-29T06:29:03.079Z", "publish_time": 1743229743079, "_source_registry_name": "default"}, "4.39.0": {"name": "@rollup/rollup-android-arm64", "version": "4.39.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.39.0", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-It9+M1zE31KWfqh/0cJLrrsCPiF72PoJjIChLX+rEcujVRCb4NLQ5QzFkzIZW8Kn8FTbvGQBY5TkKBau3S8cCQ==", "shasum": "9c136034d3d9ed29d0b138c74dd63c5744507fca", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.39.0.tgz", "fileCount": 3, "unpackedSize": 2364780, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDuSMQfaYBMBAaJapefyIH94QRnO7T5lAUWHowl8ygkqgIgOj0UArxM32KeAmVvHkNospOrA977/5gr8TNDXOitMKo="}], "size": 1048025}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.39.0_1743569369302_0.9031476670306502"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T04:49:29.571Z", "publish_time": 1743569369571, "_source_registry_name": "default"}, "4.40.0": {"name": "@rollup/rollup-android-arm64", "version": "4.40.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.40.0", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PPA6aEEsTPRz+/4xxAmaoWDqh67N7wFbgFUJGMnanCFs0TV99M0M8QhhaSCks+n6EbQoFvLQgYOGXxlMGQe/6w==", "shasum": "9b5e130ecc32a5fc1e96c09ff371743ee71a62d3", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.0.tgz", "fileCount": 3, "unpackedSize": 2401260, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEsE15DGAqReOWKD3aHVvmoSAjvlKZYUjCw/E9TjUSjYAiEA3yjzXmazhLEAjgU29m7yjkSWxMeMHl8Bh587lLnTkWM="}], "size": 1059837}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.40.0_1744447172677_0.2465755126853333"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-12T08:39:32.922Z", "publish_time": 1744447172922, "_source_registry_name": "default"}, "4.40.1": {"name": "@rollup/rollup-android-arm64", "version": "4.40.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.40.1", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PPkxTOisoNC6TpnDKatjKkjRMsdaWIhyuMkA4UsBXT9WEZY4uHezBTjs6Vl4PbqQQeu6oION1w2voYZv9yquCw==", "shasum": "37ba63940211673e15dcc5f469a78e34276dbca7", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.1.tgz", "fileCount": 3, "unpackedSize": 2429252, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA29Ir9N0eZTjFUyihUo7FWal2DEGQmVXSMfrBqwqH+0AiEA9wBm/1lKVWz4pUdIbvpy+IgejilPZzk66pJknKEUv08="}], "size": 1061899}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.40.1_1745814921192_0.4181250317610661"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-28T04:35:21.421Z", "publish_time": 1745814921421, "_source_registry_name": "default"}, "4.40.2": {"name": "@rollup/rollup-android-arm64", "version": "4.40.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.40.2", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-13unNoZ8NzUmnndhPTkWPWbX3vtHodYmy+I9kuLxN+F+l+x3LdVF7UCu8TWVMt1POHLh6oDHhnOA04n8oJZhBw==", "shasum": "e2b38d0c912169fd55d7e38d723aada208d37256", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.2.tgz", "fileCount": 3, "unpackedSize": 2435716, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCYK6gclSs/j7b5jWFhoZPRCkptQL7bVIsP+xBW2rHJdgIhAKah5dM0xRqy8C1aQVYfHFI+H6w4AwvSAPoIg/maQBQF"}], "size": 1061505}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.40.2_1746516410571_0.01686806478982672"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-06T07:26:50.833Z", "publish_time": 1746516410833, "_source_registry_name": "default"}, "4.41.0": {"name": "@rollup/rollup-android-arm64", "version": "4.41.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.41.0", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-yDvqx3lWlcugozax3DItKJI5j05B0d4Kvnjx+5mwiUpWramVvmAByYigMplaoAQ3pvdprGCTCE03eduqE/8mPQ==", "shasum": "d73d641c59e9d7827e5ce0af9dfbc168b95cce0f", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.0.tgz", "fileCount": 3, "unpackedSize": 2544636, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCKYZ9TvnQ+QwcZZKvcf/MCQr7xcbROgQzXKIuFe5hCRwIhAMK2dxpMnCG9HQoHyDxJc77SYXkXXaT4QfpYCNec6Am9"}], "size": 1106914}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.41.0_1747546410774_0.02227937287065629"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-18T05:33:31.032Z", "publish_time": 1747546411032, "_source_registry_name": "default"}, "4.41.1": {"name": "@rollup/rollup-android-arm64", "version": "4.41.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.41.1", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-DXdQe1BJ6TK47ukAoZLehRHhfKnKg9BjnQYUu9gzhI8Mwa1d2fzxA1aw2JixHVl403bwp1+/o/NhhHtxWJBgEA==", "shasum": "d19af7e23760717f1d879d4ca3d2cd247742dff2", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.1.tgz", "fileCount": 3, "unpackedSize": 2646292, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDUoNYVj3Locpo4lKfuhNjH2VVSzcSY+sU7zxEoykZVgAiEA3YwKdQJBT04SQTvufm2HNg4KfYnD403l6eOTE0gpBgo="}], "size": 1149665}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.41.1_1748067268501_0.06835730897384651"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-24T06:14:28.754Z", "publish_time": 1748067268754, "_source_registry_name": "default"}, "4.41.2": {"name": "@rollup/rollup-android-arm64", "version": "4.41.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.41.2", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-8AVWhLnN9FteevGP+9pj/Y79vqE9TdziZTe5XkN5Z9+9QY7TEBbr4iz2te8/vXbLSLEdmaQx+o2GWXrLXDKGPg==", "shasum": "e083aed199dd18263e3510e83ff73ab74c27414d", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.2.tgz", "fileCount": 3, "unpackedSize": 2644564, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHmcRTAfwmpsEgBac1n5DfSHlh7rNUp6BzvslavL06wUAiBijs2J9ib+pi1Tsc5daCl/9LwsIBJv3D073bGaAqeVbQ=="}], "size": 1147353}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.41.2_1749210029532_0.7366986451695043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T11:40:29.739Z", "publish_time": 1749210029739, "_source_registry_name": "default"}, "4.42.0": {"name": "@rollup/rollup-android-arm64", "version": "4.42.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.42.0", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bpRipfTgmGFdCZDFLRvIkSNO1/3RGS74aWkJJTFJBH7h3MRV4UijkaEUeOMbi9wxtxYmtAbVcnMtHTPBhLEkaw==", "shasum": "6798394241d1b26f8b44d2bbd8de9c12eb9dd6e6", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.42.0.tgz", "fileCount": 3, "unpackedSize": 2644564, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCz8djcpUzLr6JIHOV9/dJABd9czRNh9ni2UcQ1mlb3DwIhALYtQGBfwCJU335T8VFkh6POUiv9Ldv/ju7NgDa+l+F6"}], "size": 1147352}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.42.0_1749221289906_0.6082750440644089"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T14:48:10.134Z", "publish_time": 1749221290134, "_source_registry_name": "default"}, "4.43.0": {"name": "@rollup/rollup-android-arm64", "version": "4.43.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.43.0", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==", "shasum": "f70ee53ba991fdd65c277b0716c559736d490a58", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz", "fileCount": 3, "unpackedSize": 2644564, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCOypNijEDL67Xc+fs8CW+x7mS2p9DEoOjV9WzljSJ2wQIgB0LsQxVI7ECdRC8R6z9wH6tof5Y3BLgp1xBHyLo4lqk="}], "size": 1147352}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.43.0_1749619354949_0.44978924870140546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T05:22:35.190Z", "publish_time": 1749619355190, "_source_registry_name": "default"}, "4.44.0": {"name": "@rollup/rollup-android-arm64", "version": "4.44.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-uNSk/TgvMbskcHxXYHzqwiyBlJ/lGcv8DaUfcnNwict8ba9GTTNxfn3/FAoFZYgkaXXAdrAA+SLyKplyi349Jw==", "shasum": "63566b0e76c62d4f96d44448f38a290562280200", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.0.tgz", "fileCount": 3, "unpackedSize": 2638036, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCGIfGlJftkg0Pyx3EY5BHPcwO+aOyePQpijlodrliEqAIhAO9QCwWO/nl0MjsvuyS/0J/sw+qIZrYyhGlPVPC8kQiO"}], "size": 1143651}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.44.0_1750314173968_0.3308945921719251"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-19T06:22:54.172Z", "publish_time": 1750314174172, "_source_registry_name": "default"}, "4.44.1": {"name": "@rollup/rollup-android-arm64", "version": "4.44.1", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.44.1", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-RurZetXqTu4p+G0ChbnkwBuAtwAbIwJkycw1n6GvlGlBuS4u5qlr5opix8cBAYFJgaY05TWtM+LaoFggUmbZEQ==", "shasum": "40379fd5501cfdfd7d8f86dfa1d3ce8d3a609493", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.1.tgz", "fileCount": 3, "unpackedSize": 2638036, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIANHEhpwCd/Z8Gppx12A//pueekgEyH7KW0W9vWLfLkgAiB56FPDOpamf3gKxrzecwBvnBIy+8aQ1zD4pqSUrDjLLg=="}], "size": 1143651}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.44.1_1750912453221_0.903399512306386"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-26T04:34:13.441Z", "publish_time": 1750912453441, "_source_registry_name": "default"}, "4.44.2": {"name": "@rollup/rollup-android-arm64", "version": "4.44.2", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Yt5MKrOosSbSaAK5Y4J+vSiID57sOvpBNBR6K7xAaQvk3MkcNVV0f9fE20T+41WYN8hDn6SGFlFrKudtx4EoxA==", "shasum": "7bd5591af68c64a75be1779e2b20f187878daba9", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2630676, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDBdztfBkT2xzFPudCou6dDa89iO1gXs16VwX+7bSOUTAiEAu9UnqF59vS4PDlFvb2aoK4on0UECAjPvuIIyYF9QtUc="}], "size": 1139711}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.44.2_1751633765792_0.705582389736116"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-04T12:56:06.058Z", "publish_time": 1751633766058, "_source_registry_name": "default"}, "4.45.0": {"name": "@rollup/rollup-android-arm64", "version": "4.45.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.45.0", "gitHead": "b7c7c1159f70ebe8ad6f94c942ebab2fa59c7982", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PSZ0SvMOjEAxwZeTx32eI/j5xSYtDCRxGu5k9zvzoY77xUNssZM+WV6HYBLROpY5CkXsbQjvz40fBb7WPwDqtQ==", "shasum": "00a51d1d4380cc677da80ac9da1a19e7806bf57e", "tarball": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.45.0.tgz", "fileCount": 3, "unpackedSize": 2630676, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDsBn9iM5IFisGAXNSGOx+mqCTVQLyT7TX2Y90ljL+RdAiBqxW48WE143EiO/EKJ8mcoH6egL99Ew5Xg4KbJ1SepKw=="}], "size": 1139711}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.45.0_1752299640396_0.9992518840544313"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-12T05:54:00.661Z", "publish_time": 1752299640661, "_source_registry_name": "default"}}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "description": "Native bindings for Rollup", "homepage": "https://rollupjs.org/", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "license": "MIT", "maintainers": [{"name": "lukastaegert", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "shellscape", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "readme": "# `@rollup/rollup-android-arm64`\n\nThis is the **aarch64-linux-android** binary for `rollup`\n", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "_source_registry_name": "default"}