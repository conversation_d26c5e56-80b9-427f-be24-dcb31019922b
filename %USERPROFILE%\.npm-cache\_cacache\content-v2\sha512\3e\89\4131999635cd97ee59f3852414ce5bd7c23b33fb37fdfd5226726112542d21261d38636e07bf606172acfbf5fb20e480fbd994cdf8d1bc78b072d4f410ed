{"_attachments": {}, "_id": "copy-anything", "_rev": "441-61f14490b677e08f51138ac4", "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "description": "An optimised way to copy'ing an object. A small and simple integration", "dist-tags": {"latest": "4.0.5"}, "license": "MIT", "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "copy-anything", "readme": "# Copy anything 🎭\n\n<a href=\"https://www.npmjs.com/package/copy-anything\"><img src=\"https://img.shields.io/npm/v/copy-anything.svg\" alt=\"Total Downloads\"></a>\n<a href=\"https://www.npmjs.com/package/copy-anything\"><img src=\"https://img.shields.io/npm/dw/copy-anything.svg\" alt=\"Latest Stable Version\"></a>\n\n```\nnpm i copy-anything\n```\n\nAn optimised way to copy'ing (cloning) an object or array. A small and simple integration.\n\n## Motivation\n\nI created this package because I tried a lot of similar packages that do copy'ing/cloning. But all had its quirks, and _all of them break things they are not supposed to break_... 😞\n\nI was looking for:\n\n- a simple copy/clone function\n- has to be fast!\n- props must lose any reference to original object\n- works with arrays and objects in arrays!\n- supports symbols\n- can copy non-enumerable props as well\n- **does not break special class instances**　‼️\n\nThis last one is crucial! So many libraries use custom classes that create objects with special prototypes, and such objects all break when trying to copy them improperly. So we gotta be careful!\n\ncopy-anything will copy objects and nested properties, but only as long as they're \"plain objects\". As soon as a sub-prop is not a \"plain object\" and has a special prototype, it will copy that instance over \"as is\". ♻️\n\n## Meet the family (more tiny utils with TS support)\n\n- [is-what 🙉](https://github.com/mesqueeb/is-what)\n- [is-where 🙈](https://github.com/mesqueeb/is-where)\n- [merge-anything 🥡](https://github.com/mesqueeb/merge-anything)\n- [check-anything 👁](https://github.com/mesqueeb/check-anything)\n- [remove-anything ✂️](https://github.com/mesqueeb/remove-anything)\n- [getorset-anything 🐊](https://github.com/mesqueeb/getorset-anything)\n- [map-anything 🗺](https://github.com/mesqueeb/map-anything)\n- [filter-anything ⚔️](https://github.com/mesqueeb/filter-anything)\n- [copy-anything 🎭](https://github.com/mesqueeb/copy-anything)\n- [case-anything 🐫](https://github.com/mesqueeb/case-anything)\n- [flatten-anything 🏏](https://github.com/mesqueeb/flatten-anything)\n- [nestify-anything 🧅](https://github.com/mesqueeb/nestify-anything)\n\n## Usage\n\n<!-- prettier-ignore-start -->\n```js\nimport { copy } from 'copy-anything'\n\nconst original = { name: 'Ditto', type: { water: true } }\nconst copy = copy(original)\n\n// now if we change a nested prop like the type\ncopy.type.water = false\n// or add a new nested prop\ncopy.type.fire = true\n\n// then the original object will still be the same:\n(original.type.water === true) // true\n(original.type.fire === undefined) // true\n```\n\n> Please note, by default copy-anything does not copy non-enumerable props. If you need to copy those, see the instructions further down below.\n\n## Works with arrays\n\nIt will also clone arrays, **as well as objects inside arrays!** 😉\n\n```js\nconst original = [{ name: 'Squirtle' }]\nconst copy = copy(original)\n\n// now if we change a prop in the array\ncopy[0].name = 'Wartortle'\n// or add a new item to the array\ncopy.push({ name: 'Charmander' })\n\n// then the original array will still be the same:\n(original[0].name === 'Squirtle') // true\n(original[1] === undefined) // true\n```\n\n## Non-enumerable\n\nBy default, copy-anything only copies enumerable properties. If you also want to copy non-enumerable properties you can do so by passing that as an option.\n\n```js\nconst original = { name: 'Bulbasaur' }\n// bulbasaur's ID is non-enumerable\nObject.defineProperty(original, 'id', {\n  value: '001',\n  writable: true,\n  enumerable: false,\n  configurable: true,\n})\nconst copy1 = copy(original)\n(copy1.id === undefined) // true\n\nconst copy2 = copy(original, { nonenumerable: true })\n(copy2.id === '001') // true\n```\n\n## Limit to specific props\n\nYou can limit to specific props.\n\n```js\nconst original = { name: 'Flareon', type: ['fire'], id: '136' }\nconst copy = copy(original, { props: ['name'] })\n\n(copy) // will look like: `{ name: 'Flareon' }`\n```\n\n> Please note, if the props you have specified are non-enumerable, you will also need to pass `{nonenumerable: true}`.\n\n<!-- prettier-ignore-end -->\n\n## Source code\n\nThe source code is literally just these lines. Most of the magic comes from the isPlainObject function from the [is-what library](https://github.com/mesqueeb/is-what).\n\n```JavaScript\nimport { isPlainObject } from 'is-what'\n\nexport function copy (target) {\n  if (isArray(target)) return target.map(i => copy(i))\n  if (!isPlainObject(target)) return target\n  return Object.keys(target)\n    .reduce((carry, key) => {\n      const val = target[key]\n      carry[key] = copy(val)\n      return carry\n    }, {})\n}\n```\n", "time": {"created": "2022-01-26T12:54:40.352Z", "modified": "2025-03-24T18:20:53.627Z", "3.0.2": "2022-01-26T12:59:34.751Z", "2.0.3": "2021-02-04T13:57:57.366Z", "2.0.1": "2020-03-15T05:47:59.289Z", "2.0.0": "2020-03-14T23:53:09.547Z", "1.6.0": "2020-02-12T07:08:22.245Z", "1.5.4": "2020-01-20T02:19:13.381Z", "1.5.3": "2020-01-20T02:08:04.692Z", "1.5.2": "2020-01-20T02:05:50.269Z", "1.5.1": "2019-11-21T22:51:34.530Z", "1.5.0": "2019-08-17T03:13:31.328Z", "1.4.0": "2019-08-16T02:24:19.840Z", "1.3.0": "2019-07-13T06:04:10.132Z", "1.2.4": "2019-07-13T03:43:18.288Z", "1.2.3": "2019-06-07T09:31:28.485Z", "1.2.2": "2019-05-07T09:06:09.897Z", "1.2.1": "2019-02-25T01:32:09.341Z", "1.1.1": "2019-02-22T06:01:43.072Z", "1.1.0": "2019-01-13T03:10:38.144Z", "1.0.0": "2018-12-15T16:29:22.602Z", "2.0.4": "2022-01-25T04:01:13.933Z", "2.0.5": "2022-01-25T09:08:28.097Z", "3.0.0": "2022-01-25T09:18:45.685Z", "2.0.6": "2022-01-25T10:52:27.915Z", "3.0.1": "2022-01-26T12:53:08.190Z", "3.0.3": "2022-12-01T04:39:17.840Z", "3.0.4": "2023-05-07T07:17:02.221Z", "3.0.5": "2023-05-23T02:23:05.454Z", "4.0.0": "2024-06-01T07:15:14.474Z", "4.0.1": "2024-06-05T01:25:51.679Z", "4.0.2": "2024-06-05T02:04:59.412Z", "4.0.3": "2025-02-19T18:48:47.047Z", "4.0.4": "2025-03-24T17:57:35.183Z", "4.0.5": "2025-03-24T18:18:23.270Z"}, "versions": {"3.0.2": {"name": "copy-anything", "sideEffects": false, "type": "module", "version": "3.0.2", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "engines": {"node": ">=12.13"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.6"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.1", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.3"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "868d6b208bcbd7be5c0e46287855fbbdcd5942cc", "_id": "copy-anything@3.0.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-CzATjGXzUQ0EvuvgOCI6A4BGOo2bcVx8B+eC2nF862iv9fopnPQwlrbACakNCHRIJbCSBj+J/9JeDf60k64MkA==", "shasum": "7189171ff5e1893b2287e8bf574b8cd448ed50b1", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.2.tgz", "fileCount": 6, "unpackedSize": 11966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8UW2CRA9TVsSAnZWagAAuYkP/2mHBDLmJDKIrXdUDBvd\n6LYOCmtViD2qDXGbvo1KLS5TXSQPiYlv9oZDdooS/55oLfVlSiWu5AaqrtZA\nEwCcswrp+0awN+nIo2StU4oYrqj75CEullFeer4u+dHMJUzdx3qkUFX6FN20\nhmt+g7aZ8YzEk1CUscR6+gUcR+3nssGxtSpeHeWu7jq4/MAHRM8YUrbDL0nI\n5ZWS9Y4hARvoSvFBkSFujkOVZmv6IPRp98a2yP3tjNnGbN8pFOASDkGLpHnm\nLlAgw6hul9M2UXEX+V4SMGCf5ZIKNXvgD13BxG78/sxQBPNupE++mPPqRr9W\nMSwkC1VV0cevSV/QrtRdrXZSmO9H6KZLN3xG04UyxKQ77DExco2pxbgHRrtZ\nXpHTKj8+XeOmZSN1MiRlw/9yCSXydOBvnnZKbLlScxh2d36Ck5ZhX1/IFGfu\nVJkiAR9MG0+9dGFdcOl4L8UbGi9Vr5uVen4nJVZRd1+wDdeDL/3daKjwGWQz\nfJhKJ5AgHoxrAC7VWKtauc6joP5Dy0DFXkOLSRxXY+pEGjP+iSf3sffU5Jx6\nO8TEjXPGt3I47Qppdwi60f3Mfg6x88//6xS1RmvdLd5CpkiP+QdxQpIi/AkS\n58/AsT5A7e9WWLvWWvfl3OXLlHpVtogUrxolDyaY3AshiI60qdsRCg52A9vc\njZhS\r\n=6lno\r\n-----END PGP SIGNATURE-----\r\n", "size": 4160}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_3.0.2_1643201974567_0.9051471022546638"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-26T12:59:40.595Z"}, "2.0.3": {"name": "copy-anything", "sideEffects": false, "version": "2.0.3", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run test && npm run rollup"}, "dependencies": {"is-what": "^3.12.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.14.2", "@typescript-eslint/parser": "^4.14.2", "ava": "^3.15.0", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^2.38.4", "rollup-plugin-typescript2": "^0.29.0", "ts-node": "^9.1.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.3"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "ava": {"extensions": ["ts"], "require": ["tsconfig-paths/register", "ts-node/register"]}, "gitHead": "af81ea7fae6a791700645be74abad092104920b5", "_id": "copy-anything@2.0.3", "_nodeVersion": "15.5.1", "_npmVersion": "7.3.0", "dist": {"shasum": "842407ba02466b0df844819bbe3baebbe5d45d87", "size": 7061, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.3.tgz", "integrity": "sha512-GK6QUtisv4fNS+XcI7shX0Gx9ORg7QqIznyfho79JTnX1XhLiyZHfftvGiziqzRiEi/Bjhgpi+D2o7HxJFPnDQ=="}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_2.0.3_1612447077176_0.4493955624879349"}, "_hasShrinkwrap": false, "publish_time": 1612447077366, "_cnpm_publish_time": 1612447077366, "_cnpmcore_publish_time": "2021-12-13T14:25:40.045Z"}, "2.0.1": {"name": "copy-anything", "sideEffects": false, "version": "2.0.1", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run test && npm run rollup"}, "dependencies": {"is-what": "^3.7.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.23.0", "@typescript-eslint/parser": "^2.23.0", "ava": "^3.5.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^1.32.1", "rollup-plugin-typescript2": "^0.25.3", "ts-node": "^8.6.2", "typescript": "^3.8.3"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "ava": {"extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "f2d7787b9d8ddf5dea547b077fadb69e1fc5d229", "_id": "copy-anything@2.0.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "2afbce6da684bdfcbec93752fa762819cb480d9a", "size": 7015, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.1.tgz", "integrity": "sha512-lA57e7viQHOdPQcrytv5jFeudZZOXuyk47lZym279FiDQ8jeZomXiGuVf6ffMKkJ+3TIai3J1J3yi6M+/4U35g=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_2.0.1_1584251279158_0.3001266580889872"}, "_hasShrinkwrap": false, "publish_time": 1584251279289, "_cnpm_publish_time": 1584251279289, "_cnpmcore_publish_time": "2021-12-13T14:25:40.379Z"}, "2.0.0": {"name": "copy-anything", "sideEffects": false, "version": "2.0.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run test && npm run rollup"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "dependencies": {"is-what": "^3.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.19.2", "@typescript-eslint/parser": "^2.19.2", "ava": "^3.3.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^1.31.0", "rollup-plugin-typescript2": "^0.25.3", "ts-node": "^8.6.2", "typescript": "^3.7.5"}, "ava": {"extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "70acdf711b0011872ce391e08b29f675af222834", "_id": "copy-anything@2.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "2783c8bb5462f136bae6ec023a588461833daaad", "size": 7010, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.0.tgz", "integrity": "sha512-EcQkta4OqR0KPnhHMMl81+DOyxrD8gqiIMYYmCuLeZmYCFlNNuN1IXexhQ55iDMzf/AqOXWSHI7AN0ZI3HyVtw=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_2.0.0_1584229989444_0.6563619977864859"}, "_hasShrinkwrap": false, "publish_time": 1584229989547, "_cnpm_publish_time": 1584229989547, "_cnpmcore_publish_time": "2021-12-13T14:25:40.731Z"}, "1.6.0": {"name": "copy-anything", "sideEffects": false, "version": "1.6.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run test && npm run rollup"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "dependencies": {"is-what": "^3.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.19.2", "@typescript-eslint/parser": "^2.19.2", "ava": "^3.3.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^1.31.0", "rollup-plugin-typescript2": "^0.25.3", "ts-node": "^8.6.2", "typescript": "^3.7.5"}, "ava": {"extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "70acdf711b0011872ce391e08b29f675af222834", "_id": "copy-anything@1.6.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "005a70d4b64126880ee4e2d07edc0b15bedf4c56", "size": 6973, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.6.0.tgz", "integrity": "sha512-ZEEDP2ciuSqBME5PfiiStiv51wdwTMC57c9+a+HWPuvJqR6VjhpaFccNbdzXtQdAfSDIS+ydQe6MTg1gbfDPOA=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.6.0_1581491302156_0.05242883167665369"}, "_hasShrinkwrap": false, "publish_time": 1581491302245, "_cnpm_publish_time": 1581491302245, "_cnpmcore_publish_time": "2021-12-13T14:25:41.066Z"}, "1.5.4": {"name": "copy-anything", "sideEffects": false, "version": "1.5.4", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.16.0", "@typescript-eslint/parser": "^2.16.0", "ava": "^2.4.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^1.29.0", "rollup-plugin-typescript2": "^0.25.3", "typescript": "^3.7.5"}, "dependencies": {"is-what": "^3.5.1"}, "gitHead": "ad625d56a73014046839d792e08d7f75f35e6fe2", "_id": "copy-anything@1.5.4", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "3d1b9b7e7cac1779b2af80eff0d35edc4049084d", "size": 6822, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.5.4.tgz", "integrity": "sha512-cOU0xjJRM8ylFJ9F2jUJ7hIbq2WGDu/S693B4QlI8x14pjy0o9kxr1FdYSjVqouk4TKIohtz0c5KkC/XdDVnIQ=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.5.4_1579486753259_0.44210515681112716"}, "_hasShrinkwrap": false, "publish_time": 1579486753381, "_cnpm_publish_time": 1579486753381, "_cnpmcore_publish_time": "2021-12-13T14:25:41.421Z"}, "1.5.3": {"name": "copy-anything", "sideEffects": false, "version": "1.5.3", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.16.0", "@typescript-eslint/parser": "^2.16.0", "ava": "^2.4.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^1.29.0", "rollup-plugin-typescript2": "^0.25.3", "typescript": "^3.7.5"}, "dependencies": {"is-what": "^3.5.0"}, "gitHead": "ea7bac09ccf23684580ac6bef0df262a07389efe", "_id": "copy-anything@1.5.3", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "1291d03bc844af2c9d29491ec1ea6eb3b1648349", "size": 6822, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.5.3.tgz", "integrity": "sha512-Frh6PxusNfnjpjKN5tggOVUQo6izGyFabqBWDEiqFhQsDRjBbyYojyI1zChEAQHQdw/Jv610iPGRpMzkawVpEg=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.5.3_1579486084581_0.48513798375577344"}, "_hasShrinkwrap": false, "publish_time": 1579486084692, "_cnpm_publish_time": 1579486084692, "_cnpmcore_publish_time": "2021-12-13T14:25:41.810Z"}, "1.5.2": {"name": "copy-anything", "version": "1.5.2", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c build/rollup.js", "build": "npm run lint && npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.16.0", "@typescript-eslint/parser": "^2.16.0", "ava": "^2.4.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "eslint-plugin-tree-shaking": "^1.8.0", "rollup": "^1.29.0", "rollup-plugin-typescript2": "^0.25.3", "typescript": "^3.7.5"}, "dependencies": {"is-what": "^3.5.0"}, "gitHead": "a2d69c2ca4aeb0a46837659aa7957f1d488baa24", "_id": "copy-anything@1.5.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "56d199f614f73ac213d8bad9800205fd8bb6b0a9", "size": 6813, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.5.2.tgz", "integrity": "sha512-KOQw9KxdvdrVjDLRLiVZE9FWqIb4tMyzKHZfs4gXbxlM+ftkkEQdF+oTHxkhebkBq2rpHrqj/C45m3IxQT36RA=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.5.2_1579485950108_0.998599104939107"}, "_hasShrinkwrap": false, "publish_time": 1579485950269, "_cnpm_publish_time": 1579485950269, "_cnpmcore_publish_time": "2021-12-13T14:25:42.289Z"}, "1.5.1": {"name": "copy-anything", "version": "1.5.1", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^2.4.0", "rollup": "^1.27.3", "rollup-plugin-typescript2": "^0.25.2", "typescript": "^3.7.2"}, "dependencies": {"is-what": "^3.3.1"}, "gitHead": "de20ad0b92fbbcae70a2e717c204ff42666f803a", "_id": "copy-anything@1.5.1", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.1", "dist": {"shasum": "8698acde652d0785e0c829692c1fff0d17d314fe", "size": 6009, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.5.1.tgz", "integrity": "sha512-CGc6y1tkkmRPg4CJUCe/lTUpGe4NTv8R9Ka+YwhjQFI7ohFYLDifS5OiHiPfW9gPmUVhO0XnagXP7RZjZFPy1w=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.5.1_1574376694366_0.10550876960141231"}, "_hasShrinkwrap": false, "publish_time": 1574376694530, "_cnpm_publish_time": 1574376694530, "_cnpmcore_publish_time": "2021-12-13T14:25:42.677Z"}, "1.5.0": {"name": "copy-anything", "version": "1.5.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.4.1", "rollup-plugin-typescript2": "^0.21.2", "typescript": "^3.5.3"}, "dependencies": {"is-what": "^3.2.4"}, "gitHead": "665b683555133bb1ac3add3bae6aa41609e35ed0", "_id": "copy-anything@1.5.0", "_nodeVersion": "12.8.0", "_npmVersion": "6.10.3", "dist": {"shasum": "26bbbd25c711688194253a64df988e720a992910", "size": 5574, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.5.0.tgz", "integrity": "sha512-veu74zguz1JFaRbGNEoB+/qBsgQ7h6y9dxEfGgf1xdOdX+13BYREGa6jn490rzeTiIAKYQHgrFwk2nuIF4YZXA=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.5.0_1566011611190_0.10685411057201066"}, "_hasShrinkwrap": false, "publish_time": 1566011611328, "_cnpm_publish_time": 1566011611328, "_cnpmcore_publish_time": "2021-12-13T14:25:43.099Z"}, "1.4.0": {"name": "copy-anything", "version": "1.4.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.4.1", "rollup-plugin-typescript2": "^0.21.2", "typescript": "^3.5.3"}, "dependencies": {"is-what": "^3.2.4"}, "gitHead": "3e207d2d726d7b190c83199b011d2adb1544d780", "_id": "copy-anything@1.4.0", "_nodeVersion": "12.8.0", "_npmVersion": "6.10.3", "dist": {"shasum": "3a5d895b1d40c02282360be776d7bf3f15aec49d", "size": 4299, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.4.0.tgz", "integrity": "sha512-LEtdb64fyxdz9v3o8DzenXBgTWxmvkR5Y3aofi6Ha9uRdNpKqepcdz07TIBbmOJznVub6eRb122Y88wZain70g=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.4.0_1565922259702_0.42127316582408336"}, "_hasShrinkwrap": false, "publish_time": 1565922259840, "_cnpm_publish_time": 1565922259840, "_cnpmcore_publish_time": "2021-12-13T14:25:43.458Z"}, "1.3.0": {"name": "copy-anything", "version": "1.3.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.4.1", "rollup-plugin-typescript2": "^0.21.2", "typescript": "^3.5.3"}, "dependencies": {"is-what": "^3.2.4"}, "gitHead": "481a2099e8c6aad570efdcf46028a5e7495cc085", "_id": "copy-anything@1.3.0", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"shasum": "fcf6052618aa293b52c254df13203c5e2112a1b9", "size": 4867, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.3.0.tgz", "integrity": "sha512-ulQWlPnLYyCrBcLgMg6NseM6bCYmrcC8gyUy157NNPunIGksz/o4dQidv4jQ5fM5T4RL13jyiqQi2y5b/cQcnw=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.3.0_1562997849996_0.9458290493450978"}, "_hasShrinkwrap": false, "publish_time": 1562997850132, "_cnpm_publish_time": 1562997850132, "_cnpmcore_publish_time": "2021-12-13T14:25:43.873Z"}, "1.2.4": {"name": "copy-anything", "version": "1.2.4", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.4.1", "rollup-plugin-typescript2": "^0.21.2", "typescript": "^3.5.3"}, "dependencies": {"is-what": "^3.2.4"}, "gitHead": "3e207d2d726d7b190c83199b011d2adb1544d780", "_id": "copy-anything@1.2.4", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"shasum": "eb873b94937a85b7640a720f7e708e6ac4e61778", "size": 4298, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.2.4.tgz", "integrity": "sha512-xxKK92dLKyXH1OvH/siFGj3keR0MfPpMujCEyIAM1Io/OV4tUUrbTcdlCI/yqcqScdPvyWf5q+7bwomlb6HoYQ=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.2.4_1562989397900_0.7727656348567158"}, "_hasShrinkwrap": false, "publish_time": 1562989398288, "_cnpm_publish_time": 1562989398288, "_cnpmcore_publish_time": "2021-12-13T14:25:44.384Z"}, "1.2.3": {"name": "copy-anything", "version": "1.2.3", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.4.1", "rollup-plugin-typescript2": "^0.21.1", "typescript": "^3.5.1"}, "dependencies": {"is-what": "^3.2.3"}, "gitHead": "db75ed9567dc225c6e5af4f85ff2eb82bc90ff63", "_id": "copy-anything@1.2.3", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"shasum": "693b482b8e9ddb2abd8c10e8ca5a4b8682600ec7", "size": 4297, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.2.3.tgz", "integrity": "sha512-1VTRfSRg1yFJkM9gOD1DhKft6pjeyjRk2t0TZTVIYpu5mQwPic1wT/Iy5j1Ifw0p5SNlWzeWI+nhMXfPhv7VjQ=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.2.3_1559899888347_0.1421440603035804"}, "_hasShrinkwrap": false, "publish_time": 1559899888485, "_cnpm_publish_time": 1559899888485, "_cnpmcore_publish_time": "2021-12-13T14:25:44.946Z"}, "1.2.2": {"name": "copy-anything", "version": "1.2.2", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.4.1", "rollup-plugin-typescript2": "^0.21.0", "typescript": "^3.4.5"}, "dependencies": {"is-what": "^3.1.2"}, "gitHead": "78fad8ec35e3ae41a3f842d892a8a6a5de5b513f", "_id": "copy-anything@1.2.2", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"shasum": "f72b5995784ebdff47e83a60f88dd9202f119993", "size": 4240, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.2.2.tgz", "integrity": "sha512-ZB47aJKwpZyepDT5JVaTS7mbm6gMOdICHLqgRnKFq3jxr7ZIw7D42qQF93eRA2Z8EAiA5fdtyK5g1ekMeGvROw=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.2.2_1557219969796_0.25940893305775337"}, "_hasShrinkwrap": false, "publish_time": 1557219969897, "_cnpm_publish_time": 1557219969897, "_cnpmcore_publish_time": "2021-12-13T14:25:45.443Z"}, "1.2.1": {"name": "copy-anything", "version": "1.2.1", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.0.1", "rollup-plugin-typescript2": "^0.19.2", "typescript": "^3.2.2"}, "dependencies": {"is-what": "^3.1.2"}, "gitHead": "f40f0d1bd9bae8f09d54037f0012c71f54af3b1b", "_id": "copy-anything@1.2.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f32d079bd300e1b6b95170593b0a22f9536f1ddf", "size": 4227, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.2.1.tgz", "integrity": "sha512-FXRDHYtCp4zhlrl5aOkPJSaECaDP81/YfJ72ju7ARtI6eCwt1K2nXx8Iz9v8a3d34VC6eaPm2WAipySAJqdRBA=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.2.1_1551058329197_0.**********569145"}, "_hasShrinkwrap": false, "publish_time": 1551058329341, "_cnpm_publish_time": 1551058329341, "_cnpmcore_publish_time": "2021-12-13T14:25:45.965Z"}, "1.1.1": {"name": "copy-anything", "version": "1.1.1", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.0.1", "rollup-plugin-typescript2": "^0.19.2", "typescript": "^3.2.2"}, "dependencies": {"is-what": "^3.1.2"}, "gitHead": "feae96b4fba9ae3d719aa297f81edd7645f20a9f", "_id": "copy-anything@1.1.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "70b17e45152719416faf8731eabe9a71d6aead2c", "size": 3749, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.1.1.tgz", "integrity": "sha512-2wVZrGDcYMkwlcxbIqaRfc8LAO5ni+KLLInZ+1FwUBK3S2qHcEYSzhPjdwY7hgjF5W1dVuuSb4rnmy1ZzW1SoQ=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.1.1_1550815302853_0.**********228415"}, "_hasShrinkwrap": false, "publish_time": 1550815303072, "_cnpm_publish_time": 1550815303072, "_cnpmcore_publish_time": "2021-12-13T14:25:46.480Z"}, "1.1.0": {"name": "copy-anything", "version": "1.1.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "rollup": "rollup -c build/rollup.js", "build": "npm run rollup && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "devDependencies": {"ava": "^1.0.1", "rollup-plugin-typescript2": "^0.18.1", "typescript": "^3.2.2"}, "dependencies": {"is-what": "^3.1.2"}, "gitHead": "0e9c4a824e1f520a998a906050ac8f02e81470a5", "_id": "copy-anything@1.1.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d243475ede080b48d33821fac9a84c9c984bef2b", "size": 57870, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.1.0.tgz", "integrity": "sha512-fA8D988xB27MB7Lz1h5KUDOGZRmoTsx1OYctPJCEqlD8jO+hqL6XSJiRPmvLmaxAi8zipgaQljiSmmYXTrZsIw=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.1.0_1547349038003_0.4072287156667662"}, "_hasShrinkwrap": false, "publish_time": 1547349038144, "_cnpm_publish_time": 1547349038144, "_cnpmcore_publish_time": "2021-12-13T14:25:47.043Z"}, "1.0.0": {"name": "copy-anything", "version": "1.0.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "main": "dist/index.cjs", "scripts": {"test": "ava test"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "keywords": ["copy", "json-stringify", "stringify-parse", "object"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "gitHead": "0e9c4a824e1f520a998a906050ac8f02e81470a5", "_id": "copy-anything@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "faebea7092696e74ff9fb5dd434ccf2b846c2f39", "size": 1102, "noattachment": false, "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-1.0.0.tgz", "integrity": "sha512-7ezGKrfbmdUUJleu2eagxu79hySt12i+V3K4gNxKAPqTYbtyHNv4b5qrBtG04OHMpBC+VYe1csnde7H96dIVyA=="}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_1.0.0_1544891362409_0.8777398564666705"}, "_hasShrinkwrap": false, "publish_time": 1544891362602, "_cnpm_publish_time": 1544891362602, "_cnpmcore_publish_time": "2021-12-13T14:25:47.551Z"}, "2.0.4": {"name": "copy-anything", "sideEffects": false, "type": "module", "version": "2.0.4", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "engines": {"node": ">=12.13", "npm": ">=7"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.0", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.1"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "4ca5a6bbef522d55929fd2134eb13379e605ff69", "_id": "copy-anything@2.0.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-ZRQMQ6oHTUAHDvGb1UNRL7LAIKhdy+FahwetyNKEAIKK0H+AFU9Lrt/7EkHq1EjgPyG0c/Dd8UTT2VDKKYj1TQ==", "shasum": "3b62865d0ced54d64bfee63b7f95f269f4a1b44a", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.4.tgz", "fileCount": 6, "unpackedSize": 11984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh73YJCRA9TVsSAnZWagAAeZ0P/Ao4vjQjw3CI//rZxsnZ\nyQ6rUkpwu7/nAEMJ8BVSD2CCjpLgp3aXMLztfvA8x4rgiZFzBCQRcj2onNiT\n49rQ/9EqEKB+ulVvCZ/JBvA5drxCG+Ps+UOqZ9TVl1yRyKjx1SEJYZlGWv17\nJu7H9yUiau+XCA0twAbLVtMm13nR8XWUNornD5+DhUy0/PdBMV/ZwoM9kATY\nCvM9cbvFb9l9vA3HNVl4LDKGm1SVM5sTIO+gE6b4o8fAVHQemjXW72GewRNW\nCYMUl0oCvs/Vru0gHru87X3Ke15I5vi/VVG3HBiWT6zulIzPDEp28/2fI1DX\n9CWtA5K9hbpKtylhAeFKc0BWNfWCUry56ARm/6BiXtwABmKC7RzA/aCCYl36\njvUDtqjFuMJv3DISlX3GdZCKXH/h4sOmApG0vHwam1mLw+KBOgngx8TLAtMt\nK0YyTfOpr0Nx6W2k2DCy7JDA5Ybx2jNkrHdwVQZKRFbMVLjb15LFLSHmbivb\nSEBnU3TbiR9Y6ajfBpdhBBtkNyK2A5pY1wkj5B0a1S2vqWPGuyjuMpuL0Bub\nj4x3KQpJfyyc/M9BMRZO8NLmGBTIu01uhOp8hjBGDIoinvoU1pqX9j8Gwa4U\nZyDB1LPxcWHwyNNMbg2tgUUVWGrRDlf0N1PKkxS3DtN33SKGVnEtcpn95rrY\nGx3Z\r\n=JV5P\r\n-----END PGP SIGNATURE-----\r\n", "size": 4167}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_2.0.4_1643083273778_0.70712906261056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T04:11:22.950Z"}, "2.0.5": {"name": "copy-anything", "sideEffects": false, "version": "2.0.5", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.0", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.1"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "f44ea8f6208d7bca7fc8a208a0895c9923243aeb", "_id": "copy-anything@2.0.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-x/dCMeZunCy2MjGX/+YyMyiXVVNRg8OPUJOPWFYCf0ex7BmcNPKe25A6NTo2XAfcUwud7yZ++SF5IMKaxU6h2Q==", "shasum": "1cd3d5fadd55790d2841d401e3ffb2e64798fec5", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.5.tgz", "fileCount": 6, "unpackedSize": 11904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh774MCRA9TVsSAnZWagAArDQP/1Cox4m9YhSD6RBWUgBh\nesoBrJolVjW7UbPJFUOS9ihUchNHQxAWP0jUF+yEzqM0JsethJFlIFggG0VT\nmlUi8jaNJP9HxbMQFxRea913gOfFA5DT64wcdOefi+kAmvQNsTifWCTmE2fK\nRR/0H9C9hD+O4Y9m9YqxcsRYWQdo/DyI44Jl9Ai2iN9st1qcV6qEV0ULJtjX\nA9cBdl22OEoMaE0nd+UJy99o1XbSBXcBqcdqNknm14GqCx2TJx6Getrpvfm/\n2G3PUQ0LISyBXtCyqhXqPGI0EPeNYxGB8wDiyQ9CJfxTxaLB/rfmH+7G036k\nP/JkEI2wMkDJLXvDg3bDLO5IH2dyRrlqunGJNi24h4Wkr1Eq7rXQZjBLgis9\nynAFhmjzfiZIWmA0s5T1rzSv0qzGDEJRfuFITflM8exzi2FWMuJBkBQhGa+T\nZRxdB1JqpQkIL0hgsrVy/e3b9vBx8sYi7qwZ1IBxnG0UzIimyvv4xhocy/7n\npcMWfI9wWhYqLbvvc3FzXngbHeLymIkI8ID6fZh5Ez7dXkgLJti/6sI1kkDP\nelvX3uK6EKUr2/A/lUvpcp1tq1jWJljYAm3kIZbjpzfq04B/FN0UNQc0Y6gT\niQwQHdyANGq9HPyZmzJmAsTIxligSvnkf/C8mMalfFSM/V/WVaUSznq286ee\nWjeE\r\n=ZJUC\r\n-----END PGP SIGNATURE-----\r\n", "size": 4134}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_2.0.5_1643101707970_0.7505504209295768"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T09:08:41.991Z"}, "3.0.0": {"name": "copy-anything", "sideEffects": false, "type": "module", "version": "3.0.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "engines": {"node": ">=12.13", "npm": ">=7"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.0", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.1"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "c97856a8ce9e04b6ed422142e1a5d7305385a500", "_id": "copy-anything@3.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-h+RGo5Xz4Oj86tdtJ017+gwcypskdlvrbivZhO7BfwujazryQccmKxkBcXYREs63Lgb3jJ0zvJ9ljvSYcOh7bg==", "shasum": "d736c5a2cff9e32cc0b80dba9ae5fbd927f73e82", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.0.tgz", "fileCount": 6, "unpackedSize": 11984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh78B1CRA9TVsSAnZWagAAL24P/1CUxPjGD9vUIpCQW2mF\nsOGAW3Gb8XqsAZLt2GINTh41oetSA0rDS4+tTR2XvPFMLVvxdZrK7kINwP7k\nyG9EqEvaj0aba7vN/BaYN5LHDyMSkSNPWRPY3342WivWkbCrDA1h1EZAgHMV\nzShWJO4bKBp44FNjzcg3H6kVBVhz0D94+1H6eDxLPAMfT/rxvVmajDhCtf+8\noLtcSvrAjiPADY7mBYtZIhjK5l9xkl0guRRpOJOym9GxYVlonFeV+dzZawy4\nEq5G/RZkZc58gtoP7y6iHeQkOniyjOHGkIGShareSqdnw4vvYnwj8SUB+PUk\nnXjjT8WtfQKbrwa0iWPdIrKbMruuxhCvIZcAGrQi7YDWK0B8WVI40aHL6DNx\nqO5LR3CeQp1xFqpx1iXG3fRilv4PsfCJYRfnhqIuGFfLsP7nDwNomzxYCec8\n5z4X4nGV0CU03hKiJNHGLbpFVNwMTNF61TxMXw1F1/U5VP3qOg2wQn+uGhBp\n38RWZC++/M0FXna1gEVAVJhemUQ/lCzvJMo8zFq6fb1DZs0T8l2wH0I2Eb3m\nUa4kWNpqEX1huD+ptQsq1GDqghU/mjvjtm+LVqKABbfvSO5V1VNLcTGjmOLF\nqNXIJevqAnK3pVRCoTMe44ZVpBQx9HYsWqGTWdAl7nFCZLqVKwHtuzR9pYbx\nr9tJ\r\n=LVmm\r\n-----END PGP SIGNATURE-----\r\n", "size": 4167}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_3.0.0_1643102325546_0.06604457936388375"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T09:24:16.072Z"}, "2.0.6": {"name": "copy-anything", "sideEffects": false, "version": "2.0.6", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^3.14.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.0", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.1"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "legacy"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "cf033add7e10336d04c2a755594f6b52f18dfe65", "_id": "copy-anything@2.0.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==", "shasum": "092454ea9584a7b7ad5573062b2a87f5900fc480", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.6.tgz", "fileCount": 6, "unpackedSize": 11901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh79ZrCRA9TVsSAnZWagAA2q4P/0+IeGIpz86xFmwtdi06\neqqBympUVUKLbVwbPQMDA+GNx86rsWTDZVOJOHg8dVZctiJsFmSeTecbsVjF\nO7/W2EofGrsMC5VCwC0b77a/aT3J7lfDtJ0DaqUj62NKFFCzwJBmnEPi8AJ/\nmRoj/sl7wePN2L3ufrRfvTDg4otPzI66M2Uz1JMdh5MR5nPEEZ4Nx9eapia9\nwIifzw12JaIJnQcdJS891rBOExEwDrRB/Md1e9ZkBxskAJKSj38UwbFh/8oF\nIJbGdCMOZ2M2TI0p6Amk4Auh5Ri6lHJrlIZ4Zv5a/GxD9Ux0U9VPiVFffYmW\nUb+Z763fXVG5b67O/WvrHKOPH4b/dH1PZEgpAKWlKUNhMUjvaiL+Yg1d4z2R\nYZ917YlO4LjKdpjAcFxv1GsfhtNexWf6ME9kfaqzsX3ZGElYP7d5cx1LxCqO\nGya6v/t86j1ka2NidJoPA8OSYfB1NOl56G2+VRT0QfWGExuN0/cjQch2pVAK\nhxLA0CIWDmpLUejtDYbwefACCfEB7Mgihy5fISnWXyA/yGG6tZGOBpd8/9qp\n5r7RO8o+siLkJuuRyyx7na2+0qP1Lf1SQC6WEGQXCOyJGJYHG3UBKsE0AKTg\neYxGL/cmwIpVcMC7KqysjeOudC2O8gLLcT7njn9KN7FNkRQsSlcXeLK0aXbg\nGtjY\r\n=ZDhL\r\n-----END PGP SIGNATURE-----\r\n", "size": 4134}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_2.0.6_1643107947750_0.6243219621582765"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-25T10:52:35.890Z"}, "3.0.1": {"name": "copy-anything", "sideEffects": false, "type": "module", "version": "3.0.1", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "engines": {"node": ">=12.13"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.0", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.1"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "6cc1dddd7ef4cfb3997070398c61b4f99a32f1a6", "_id": "copy-anything@3.0.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-ey/dE0CytGfyZMrhM3/OdemoQYUvOqVzP0idbx7rXt8ye77hDCy8oa5iN3F4cdGsA3P1GMQnfv1vRrlzSe84Dw==", "shasum": "142586e165e4cfdb215999728f6d44b03f1b489f", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.1.tgz", "fileCount": 6, "unpackedSize": 11966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8UQ0CRA9TVsSAnZWagAAvOUP/0p/1RBo5lw8yimu5zIK\nlaRNoYU9wcDvqoN5nmwwXkNrCtCiSYMudzOehjH9JPpM0M/Jz7Xt1g7eVzd1\ndiG1rG5fi+Vnq+Heat7k9ho60+bWWWSUkwtPq2lkC2WgZGNUBhB+bOfDH7mF\nzcCt/tLpDLQWvmyMD1do44ncDdUwdXJBSRyl6LaB1aPiTCaUF6s1E5FPGalh\nNl78ztMYxFNf3cnZYI+q5KdHh6z408nmqPFZ2mY2jU0DEzIfY/nT8NB5YEgU\nULro/wRqLQJL9gXfH3wIioQ4ly4XxPpKr06+YDKBStKM4X0DeuH9PxghVdxz\nUi4v8OSVAmlVLzFKT0mqDi/IbNsNWy+rDjWxp9tiSq8iHWd/3PfqMAO40WAE\n33AZZw+XyRN/83LlQ6JgHbMsCYJpwrBE0PcA42BgtXgGZ7q5bukaNfgHGaVy\nRFusmFluS6rrqkKeaeEVobAUNENISfLL+6OO/L7piDJ5m4MennuszV2UgkuZ\nRcCPczDZUmJLT2noAokQOW/3TEuCPHz+Bv/Q718CMyzUzpbhgPP4Q84T0LIG\n2w9DDihG5QLBewqdNDT4jGccP/KIXpi4urnsACEQUAxDFGOruIjeT+wZ9I3f\neJiDmx1kTR1+6HzHJ15RHdJ200VO/0xjLuDs9Q0d+HOBCKrK2dm2Bi2gwfpV\n1/Kp\r\n=3P10\r\n-----END PGP SIGNATURE-----\r\n", "size": 4157}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_3.0.1_1643201588032_0.28308379403834927"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-26T12:54:32.633Z"}, "3.0.3": {"name": "copy-anything", "sideEffects": false, "type": "module", "version": "3.0.3", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "engines": {"node": ">=12.13"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup --bundleConfigAsCjs -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.8"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "del-cli": "^5.0.0", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.2", "prettier": "^2.8.0", "rollup": "^3.5.0", "rollup-plugin-typescript2": "^0.34.1", "typescript": "^4.9.3", "vitest": "^0.25.3"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "1f7a7eb52a33eecea18240830142bf0bb93bc99f", "_id": "copy-anything@3.0.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-fpW2W/BqEzqPp29QS+MwwfisHCQZtiduTe/m8idFo0xbti9fIZ2WVhAsCv4ggFVH3AgCkVdpoOCtQC6gBrdhjw==", "shasum": "206767156f08da0e02efd392f71abcdf79643559", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.3.tgz", "fileCount": 6, "unpackedSize": 12084, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgtPWJxmySwbZ9JkIViSEyZPyncH8PUSi979WR4XlGdAIhAIlBDfPn4TUhqXKuXrYmQInUONOZWXztwJ3z8oDIcD+h"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiC/1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc3Q/+IwSCdBgC2nTw0xI7NwgzuBVZ8qocAly9NxxwKqdt9SreqXNP\r\nDgjeY0krmvOqbsBAL8FaBkBcHi89Lul+wFBb9Bi5eBJv7wddBO5WDjL+OiOb\r\nMTnfnKTXALw/5Xn+Rz1p4XTmyfbhaci2B4fooqZpLd0EpqxfcphpbyT0RIgR\r\nHU5eZPhjCjHRoypGq5dJHaeSzW7M/BapWdqKQWHUSg6olLl2q2DqbBVfqJD2\r\nvWZsVBnaKDB48nrhlEHTzWBH0e2qudpRV6zn1qg0vqvRl8wgKQlnl82qukJs\r\nIVF0E2eIDpfb8Knso1uoYfxTKJEj3HuNlU8UPGScXx+4cjeoCz6lBthzASRk\r\nhSGKWCACyjH805A/4f2YsMThfeNCkw0K2uVk3a1L88337eLMqCrjIZ8yYRYU\r\nMp6fzrjBtgPg2MDC9VErmc65Ft7xMDBWULAvoUCIhv+yMMTwjpmsWlIwGUBH\r\nsbkpCAjnSn7lnLk/kFQ1D67hc7GdPf1/cP3NrWnIuo6I7KBCs5wfogIXKh6S\r\npEipLbQkVPiTk2WtBmLAgOcNMFcKBTXHgIUv7YBuyEC56Hv1NJ9+jv46fOvq\r\nSPefnHyqwoIm8p0OexNJRbtWLHSee+pHajYiAw9iTZLrxe3dBlWWHrM/oX7m\r\nA/vX2P2NbG4LVy3e7+B/ywiJRQcfKHe8VBs=\r\n=hmC1\r\n-----END PGP SIGNATURE-----\r\n", "size": 4227}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_3.0.3_1669869557669_0.8415755562631355"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-01T04:39:22.948Z"}, "3.0.4": {"name": "copy-anything", "sideEffects": false, "type": "module", "version": "3.0.4", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs"}}, "engines": {"node": ">=12.13"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup --bundleConfigAsCjs -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.8"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "del-cli": "^5.0.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.7.0", "prettier": "^2.8.8", "rollup": "^3.21.5", "rollup-plugin-typescript2": "^0.34.1", "typescript": "^4.9.5", "vitest": "^0.31.0"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "b715900e62605ae79fc6a8049eaa74e852b7c5a0", "_id": "copy-anything@3.0.4", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-MaQ9FwzlZ/KLeVCLhzI3rZw0EhrIryfZa3AyT4agVybR0DjlkDHA8898lamLD6kfkf9MMn8D+zDAUR4+GxaymQ==", "shasum": "a9675339d260f7cec4a58e401565e302c15d9a17", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.4.tgz", "fileCount": 6, "unpackedSize": 12085, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH2ROKbrdxJmnPf/zP8WAHyA8mJPg59DNis/K3DvzWjMAiEAxOJ9tGeC6taXf/98nAbxWelQ5LadaPEt3w6t6xmzN/Y="}], "size": 4228}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_3.0.4_1683443822070_0.6703083035172621"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-07T07:17:02.221Z", "publish_time": 1683443822221, "_source_registry_name": "default"}, "3.0.5": {"name": "copy-anything", "version": "3.0.5", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "types": "./dist/index.d.ts", "module": "./dist/index.js", "main": "./dist/index.js", "exports": {".": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "engines": {"node": ">=12.13"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./rollup.config.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.8"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "del-cli": "^5.0.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.7.0", "prettier": "^2.8.8", "rollup": "^3.23.0", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-esbuild": "^5.0.0", "typescript": "^4.9.5", "vitest": "^0.31.0"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}, "gitHead": "751442a49ef7f405ab6590409610bf1df846b68f", "_id": "copy-anything@3.0.5", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-yCEafptTtb4bk7GLEQoM8KVJpxAfdBJYaXyzQEgQQQgYrZiDp8SJmGKlYza6CYjEDNstAdNdKA3UuoULlEbS6w==", "shasum": "2d92dce8c498f790fa7ad16b01a1ae5a45b020a0", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.5.tgz", "fileCount": 7, "unpackedSize": 11696, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcyOGoGUxtZi7yUvMM2FqXjwo149akqq4Ou8WJX9x/qAiEAm4s2w4U6Jdc35En2NX0sOLw4NBq1M5RMJZ2zTAoiaaA="}], "size": 4231}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_3.0.5_1684808585248_0.48231505306713607"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-23T02:23:05.454Z", "publish_time": 1684808585454, "_source_registry_name": "default"}, "4.0.0": {"name": "copy-anything", "version": "4.0.0", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "exports": {".": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "engines": {"node": ">=18"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src", "build": "del-cli dist && tsc", "release": "npm run lint && npm run build && np"}, "dependencies": {"is-what": "^4.1.8"}, "devDependencies": {"del-cli": "^5.1.0", "np": "^10.0.5", "vitest": "^1.6.0", "@cycraft/eslint": "^0.3.0", "@cycraft/tsconfig": "^0.1.2"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "_id": "copy-anything@4.0.0", "gitHead": "a6867b92b08c16538b19f468c575a49667edae89", "_nodeVersion": "20.12.2", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-8ggtOOhouP5A2Xsy7PG1wSHAZPabQDqmNJclGLYwzaINP0Scav9LzyRLdPQHE1nXlgS6oDUnFHWSgOikc1ua0w==", "shasum": "0aed762265c7dd156bac969eb544836dafde5e10", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-4.0.0.tgz", "fileCount": 5, "unpackedSize": 9285, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpE8HeIdq6nHxW/EgBEoj9fmtnxtL5EeJ3JnRgJw/4lwIgC7SV6m3WOuflM5P71vFlGiwsMV6Xc1ieSzpNiyEURz0="}], "size": 3788}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_4.0.0_1717226114279_0.20341982863720265"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-01T07:15:14.474Z", "publish_time": 1717226114474, "_source_registry_name": "default"}, "4.0.1": {"name": "copy-anything", "version": "4.0.1", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "exports": {".": "./dist/index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src", "build": "del-cli dist && tsc", "release": "npm run lint && npm run build && np"}, "dependencies": {"is-what": "^4.1.8"}, "devDependencies": {"del-cli": "^5.1.0", "np": "^10.0.5", "vitest": "^1.6.0", "@cycraft/eslint": "^0.3.0", "@cycraft/tsconfig": "^0.1.2"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "_id": "copy-anything@4.0.1", "gitHead": "61a27365b677e90eae4c8248b490e8f0f2b01ecc", "_nodeVersion": "20.12.2", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-nsi6UW0i5ZkxobDjgX6vn0rUjR3GRDLlWkM+MjL39WLvB9E1QlzftmMyBVDNd4QxDWLjqfT3zdNxEmUkfbqycw==", "shasum": "9704aa38271b1ed1ce9cbea3fb47718a6875c287", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-4.0.1.tgz", "fileCount": 5, "unpackedSize": 9081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMuzABk9niPFZd73aBaj+P4049sc0kFNmyW68HdMmk7AIhAPB2fL36wvboZFMDBTCoh2vR2VTGmxGnQHMxKkqij2UK"}], "size": 3737}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_4.0.1_1717550751538_0.1744682991941393"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T01:25:51.679Z", "publish_time": 1717550751679, "_source_registry_name": "default"}, "4.0.2": {"name": "copy-anything", "version": "4.0.2", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "exports": {".": "./dist/index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src", "build": "del-cli dist && tsc", "release": "npm run lint && npm run build && np"}, "dependencies": {"is-what": "^5.0.1"}, "devDependencies": {"del-cli": "^5.1.0", "np": "^10.0.5", "vitest": "^1.6.0", "@cycraft/eslint": "^0.3.0", "@cycraft/tsconfig": "^0.1.2"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "_id": "copy-anything@4.0.2", "gitHead": "57c4f183a8037f99a54af5a950b53cca3d65ea4d", "_nodeVersion": "20.12.2", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-c87DOYWkRP4gZybb8d4vedb8i8Yu13ukeZdbJArCqW8MiRig0b4Brq2fp5VPeva8l1AmOiXrRYFiQJO3mfolDg==", "shasum": "82cf89d5d819b0b784ad954c24083893f87bb37d", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-4.0.2.tgz", "fileCount": 5, "unpackedSize": 9081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVfvDVchUXjyMzgka6aQbWEcXbtZRsItvBvibJRUlznAiBYaRdEHPPVAZAzrGXux7Iu99X9od2HK/7chzgHk3vAeg=="}], "size": 3735}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/copy-anything_4.0.2_1717553099253_0.9361375340142593"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-05T02:04:59.412Z", "publish_time": 1717553099412, "_source_registry_name": "default"}, "4.0.3": {"name": "copy-anything", "version": "4.0.3", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "exports": {".": "./dist/index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src", "build": "del-cli dist && tsc", "release": "npm run lint && npm run build && np"}, "dependencies": {"is-what": "^5.2.0"}, "devDependencies": {"@cycraft/eslint": "^0.4.3", "@cycraft/tsconfig": "^0.1.2", "del-cli": "^6.0.0", "np": "^10.2.0", "vitest": "^3.0.6"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "_id": "copy-anything@4.0.3", "gitHead": "e5276772768819e749463610aac435b9eff4dd71", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-cOfM59OYltWfD8RzLwWUbyuW9mPX4gQlWPIWsa5cNXE7qkaVQO6tX+l2Yo8edZNGt3zVnWFAv8hhiOZIQ2XSwQ==", "shasum": "00d53d22c01002ad808a73636a0020e4779542b7", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-4.0.3.tgz", "fileCount": 5, "unpackedSize": 9081, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEjWSugvfhHxVl0KN+Ds61QiXXynubiMsDBgqMl84STiAiA9xY4/Iv/Fwb8nXP8Ts0Rlmv5euN22BZXD+5KOPyexzg=="}], "size": 3739}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/copy-anything_4.0.3_1739990926874_0.20293519420563988"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-19T18:48:47.047Z", "publish_time": 1739990927047, "_source_registry_name": "default"}, "4.0.4": {"name": "copy-anything", "version": "4.0.4", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "exports": {".": "./dist/index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src", "build": "del-cli dist && tsc", "release": "npm run lint && npm run build && np"}, "dependencies": {"is-what": "^5.2.0"}, "devDependencies": {"@cycraft/eslint": "^0.4.4", "@cycraft/tsconfig": "^0.1.2", "del-cli": "^6.0.0", "np": "^10.2.0", "vitest": "^3.0.9"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "_id": "copy-anything@4.0.4", "gitHead": "fe760210066f0c331322825963beb4c5a735a16c", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-1n4wXJ1/kVjOVADnP++yEh46uvvdvxDFRHPRlc66ya2jh27OwwNMcj6Fz4lf5Mnu8mc8aAqnfk49LYD2612AOA==", "shasum": "7f4dcbc5aacb27276806fc1c6700d8e3ef3b2e4a", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-4.0.4.tgz", "fileCount": 5, "unpackedSize": 9244, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEc8KUGLvAL9VzODcqTYmawiSXMmhLEv1dvG4USIPuDIAiEA2v8GoSezitP3MbBqvBs4rwA33Dq2Nx4Z8ApLaCUXzb0="}], "size": 3807}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/copy-anything_4.0.4_1742839055030_0.5830144321003452"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-24T17:57:35.183Z", "publish_time": 1742839055183, "_source_registry_name": "default"}, "4.0.5": {"name": "copy-anything", "version": "4.0.5", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "exports": {".": "./dist/index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src", "build": "del-cli dist && tsc", "release": "npm run lint && npm run build && np"}, "dependencies": {"is-what": "^5.2.0"}, "devDependencies": {"@cycraft/eslint": "^0.4.4", "@cycraft/tsconfig": "^0.1.2", "del-cli": "^6.0.0", "np": "^10.2.0", "vitest": "^3.0.9"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": {"name": "<PERSON>", "url": "https://cycraft.co"}, "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "_id": "copy-anything@4.0.5", "gitHead": "91d437f79baf76098cb58c18e0d070e76197343a", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-7Vv6asjS4gMOuILabD3l739tsaxFQmC+a7pLZm02zyvs8p977bL3zEgq3yDk5rn9B0PbYgIv++jmHcuUab4RhA==", "shasum": "16cabafd1ea4bb327a540b750f2b4df522825aea", "tarball": "https://registry.npmmirror.com/copy-anything/-/copy-anything-4.0.5.tgz", "fileCount": 5, "unpackedSize": 9998, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHBOds+ivNpP25DEjWR29iDOpL1eEDot4g0ehhCXpuSqAiEAm11dgFghXUpngAIJT+V4C0nJsNQPI+C2Hu7Bx5Fem4s="}], "size": 3977}, "_npmUser": {"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "me<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/copy-anything_4.0.5_1742840303092_0.3428922219615076"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-24T18:18:23.270Z", "publish_time": 1742840303270, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "_source_registry_name": "default"}