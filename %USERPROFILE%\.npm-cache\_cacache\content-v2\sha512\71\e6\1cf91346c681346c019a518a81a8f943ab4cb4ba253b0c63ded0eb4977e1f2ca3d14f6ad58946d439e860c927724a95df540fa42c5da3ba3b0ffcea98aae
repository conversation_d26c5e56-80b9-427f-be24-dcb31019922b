{"_attachments": {}, "_id": "wmf", "_rev": "1110874-61f2ad952b493a99b0be4045", "author": {"name": "sheetjs"}, "description": "GTA:MP Waffle's Multipackage Framework", "dist-tags": {"latest": "1.0.2"}, "license": "Apache-2.0", "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "name": "wmf", "readme": "# js-wmf\n\nProcessor for Windows MetaFile (WMF) files in JS (for the browser and nodejs).\n\n\n## Installation\n\nWith [npm](https://www.npmjs.org/package/wmf):\n\n```bash\n$ npm install wmf\n```\n\nIn the browser:\n\n```html\n<script src=\"wmf.js\"></script>\n```\n\nThe browser exposes a variable `WMF`.\n\n\n## Usage\n\nThe `data` argument is expected to be an `ArrayBuffer`, `Uint8Array` or `Buffer`\n\n- `WMF.image_size(data)` extracts the image offset and extents, returns an Array\n  `[width, height]` where both metrics are measured in pixels.\n\n- `WMF.draw_canvas(data, canvas)` parses the WMF and draws to a `Canvas`.\n\n### Notes\n\n- The library assumes the global `ImageData` is available.  For nodejs-powered\n  canvas implementations, a shim must be exposed as a global. Using the `canvas`\n  npm package:\n\n```js\nconst { createImageData } = require(\"canvas\");\nglobal.ImageData = createImageData;\n```\n\n- `OffscreenCanvas` in Chrome and some other Canvas implementations require\n  the dimensions in the constructor:\n\n```js\nconst size = WMF.image_size(data);\nconst canvas = new OffscreenCanvas(size[0], size[1]);\n```\n\n\n## Examples\n\n<details>\n  <summary><b>Browser Fetch into canvas</b> (click to show)</summary>\n\n```js\n// assume `canvas` is a DOM element\n(async() => {\n  const res = await fetch(\"url/for/image.wmf\");\n  const ab = await res.arrayBuffer();\n  WMF.draw_canvas(ab, document.getElementById(\"canvas\"));\n})();\n```\n\n</details>\n\n<details>\n  <summary><b>NodeJS (using `canvas` npm module)</b> (click to show)</summary>\n\n```js\nconst { createCanvas, createImageData } = require(\"canvas\");\nglobal.ImageData = createImageData;\n\nconst size = WMF.image_size(data);\nconst canvas = createCanvas(size[0], size[1]);\nWMF.draw_canvas(data, canvas);\n```\n\n</details>\n\n\n## License\n\nPlease consult the attached LICENSE file for details.  All rights not explicitly\ngranted by the Apache 2.0 License are reserved by the Original Author.\n\n\n## References\n\n - `MS-WMF`: Windows Metafile Format\n\n", "time": {"created": "2022-01-27T14:35:01.924Z", "modified": "2022-01-27T14:35:01.924Z", "1.0.2": "2020-03-16T16:32:17.663Z", "1.0.1": "2020-03-09T21:19:36.311Z", "1.0.0": "2020-03-01T04:52:50.770Z", "0.5.0": "2015-11-02T17:46:07.168Z", "0.4.2": "2015-11-01T16:35:09.443Z", "0.4.1": "2015-11-01T16:34:18.692Z", "0.4.0": "2015-10-31T18:36:00.487Z", "0.3.0": "2015-10-29T10:46:13.900Z", "0.2.2": "2015-10-27T09:06:35.560Z", "0.2.1": "2015-10-27T08:09:19.110Z", "0.2.0": "2015-10-26T22:44:37.711Z", "0.1.2": "2015-10-26T22:31:38.520Z", "0.1.1": "2015-10-26T22:28:20.378Z", "0.1.0": "2015-10-26T22:25:54.393Z"}, "versions": {"1.0.2": {"name": "wmf", "version": "1.0.2", "author": {"name": "sheetjs"}, "description": "Windows MetaFile (WMF) parser", "keywords": ["wmf", "image", "office", "word"], "main": "./dist/wmf.node.js", "unpkg": "./dist/wmf.js", "jsdelivr": "./dist/wmf.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {}, "devDependencies": {"source-map-loader": "^0.2.4", "uglifyjs-webpack-plugin": "^2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-wmf.git"}, "scripts": {}, "config": {"blanket": {"pattern": "wmf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-wmf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "gitHead": "3bf5459c6fdb65c3e0d2fb2477dde0c2b0ed4d08", "_id": "wmf@1.0.2", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "7d19d621071a08c2bdc6b7e688a9c435298cc2da", "size": 71391, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz", "integrity": "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wmf_1.0.2_1584376337439_0.8837145237790327"}, "_hasShrinkwrap": false, "publish_time": 1584376337663, "_cnpm_publish_time": 1584376337663, "_cnpmcore_publish_time": "2021-12-16T10:35:58.454Z"}, "1.0.1": {"name": "wmf", "version": "1.0.1", "author": {"name": "sheetjs"}, "description": "Windows MetaFile (WMF) parser", "keywords": ["wmf", "image", "office", "word"], "main": "./dist/wmf.node.js", "unpkg": "./dist/wmf.js", "jsdelivr": "./dist/wmf.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {}, "devDependencies": {"source-map-loader": "^0.2.4", "uglifyjs-webpack-plugin": "^2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-wmf.git"}, "scripts": {}, "config": {"blanket": {"pattern": "wmf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-wmf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=12.0"}, "gitHead": "10e0364e321504225b07c1b6573ba8407154a536", "_id": "wmf@1.0.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "f8690f185651bf88d39f0a21ae3e51bb1ec9fae9", "size": 71391, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-1.0.1.tgz", "integrity": "sha512-Mgopbef6qEsZvGss8ke/hMLg2XCCkt6emB/bZlCez9Zve9hrOj0lsrh0ncrN6Tnv6h/UCNn5nOd1UjjssezrtA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wmf_1.0.1_1583788776193_0.956075477335002"}, "_hasShrinkwrap": false, "publish_time": 1583788776311, "_cnpm_publish_time": 1583788776311, "_cnpmcore_publish_time": "2021-12-16T10:35:59.279Z"}, "1.0.0": {"name": "wmf", "version": "1.0.0", "author": {"name": "sheetjs"}, "description": "Windows MetaFile (WMF) parser", "keywords": ["wmf", "image"], "main": "./dist/wmf.node.js", "browser": "./js/index.js", "dependencies": {}, "devDependencies": {"source-map-loader": "^0.2.4", "uglifyjs-webpack-plugin": "^2.2.0"}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-wmf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=12.0"}, "gitHead": "369214009e2eb722a33ed932c78f62c1071f42b5", "_id": "wmf@1.0.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "c568bbd5ae29fb18830d1c68170c87fc0c5e787a", "size": 86994, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-1.0.0.tgz", "integrity": "sha512-r2RSMqjhEyxdHv7LvoNxQilFdUKASp09csSu7mudCViizeI0ADjrfkV37uW7T936Y+6RAn+w3M2R9cP1y3fsRA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "_npmUser": {"name": "sheetjs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wmf_1.0.0_1583038370591_0.02302964568764221"}, "_hasShrinkwrap": false, "publish_time": 1583038370770, "_cnpm_publish_time": 1583038370770, "_cnpmcore_publish_time": "2021-12-16T10:35:59.726Z"}, "0.5.0": {"name": "wmf", "version": "0.5.0", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "50728aee49f221dd01951df9f164f3b7710e2a78", "_id": "wmf@0.5.0", "_shasum": "51d067f4ece4dc855f990b1dabdcf8cdd7d8aff3", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "51d067f4ece4dc855f990b1dabdcf8cdd7d8aff3", "size": 11991, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.5.0.tgz", "integrity": "sha512-DfoELxSvGgiiHoboBzqKU/9hYRKbOiBr58r/0iUzh5ESYKrQl0h40eHmWn/93rY3mumEa9obqNoDfOrtggSiww=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1446486367168, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446486367168, "_cnpmcore_publish_time": "2021-12-16T10:35:59.923Z"}, "0.4.2": {"name": "wmf", "version": "0.4.2", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "521ed0c72f799807daf8f483b930ee7f537b1626", "_id": "wmf@0.4.2", "_shasum": "92191e11445454b976b870f355c41b13c0833fec", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "92191e11445454b976b870f355c41b13c0833fec", "size": 11757, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.4.2.tgz", "integrity": "sha512-63rPFtCNq5mw34Om+b1M6ku5jmd4k9wmN3jSKqDcdJKPBnfHmReToix7wMpDsRmDhpValUnw0USmuKc/fuVX+Q=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1446395709443, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446395709443, "_cnpmcore_publish_time": "2021-12-16T10:36:00.108Z"}, "0.4.1": {"name": "wmf", "version": "0.4.1", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "7a6a6ce100359ffa4d849389ddcd57824d6b4513", "_id": "wmf@0.4.1", "_shasum": "78024c9ca5838cc057f24cb49cacf51916abf7d9", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "78024c9ca5838cc057f24cb49cacf51916abf7d9", "size": 11751, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.4.1.tgz", "integrity": "sha512-FhPziu+Q4tkgEaE3WfRy6Oiv/QgvqQvuIVKN1DYYHD3RJix3HuhisHiMsI2pUs8edum7TaGM9eGhGpxLQC7YoQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1446395658692, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446395658692, "_cnpmcore_publish_time": "2021-12-16T10:36:00.276Z"}, "0.4.0": {"name": "wmf", "version": "0.4.0", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "1d04f495b0192251968d034b32cdd44e8565deef", "_id": "wmf@0.4.0", "_shasum": "bea72bdda11a13fbd2161feb5cb33b668283ec75", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "bea72bdda11a13fbd2161feb5cb33b668283ec75", "size": 11748, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.4.0.tgz", "integrity": "sha512-WmXAJ0xf0WvNwJI64yNCPoSyauNw19UB8dFIJrXk9EA0B5xFMNp3m1LkcKvRdnBU16wIWWIJ7OiliiscUy6ypg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1446316560487, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446316560487, "_cnpmcore_publish_time": "2021-12-16T10:36:00.473Z"}, "0.3.0": {"name": "wmf", "version": "0.3.0", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "0494ad423cceefbfa9a418fb3541b661550fe6fb", "_id": "wmf@0.3.0", "_shasum": "51606cd620678c3a453e11c8300dcb816c1e073f", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "51606cd620678c3a453e11c8300dcb816c1e073f", "size": 9497, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.3.0.tgz", "integrity": "sha512-pKZ+jpQ3kmnb6vmRB2CGMJjSD/ZUcCOHzt7g44P9kw8sUFKzv5sCoHAroqqhLeWP21O1RIoKO8SI+Sn1G/0FLg=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1446115573900, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446115573900, "_cnpmcore_publish_time": "2021-12-16T10:36:00.684Z"}, "0.2.2": {"name": "wmf", "version": "0.2.2", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "e2ac7e00393d14983d6f2186a82c126663f131d0", "_id": "wmf@0.2.2", "_shasum": "a2a0dd65967dddbf32d7075a8ec76721bb8d8bae", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "a2a0dd65967dddbf32d7075a8ec76721bb8d8bae", "size": 5746, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.2.2.tgz", "integrity": "sha512-fHYhr+iek8FygKlq3d4oNJnR6MZVmnMQo25r2cBry2lyoLeWQqmp2ok0/pLy2HhSErswP/pChlc4XKbx5feDIA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445936795560, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445936795560, "_cnpmcore_publish_time": "2021-12-16T10:36:00.909Z"}, "0.2.1": {"name": "wmf", "version": "0.2.1", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "d9d4608af3196178e1a8cf4694c9782a1d55a961", "_id": "wmf@0.2.1", "_shasum": "acd5c8d430a6a89c1125af32907f541727543bef", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "acd5c8d430a6a89c1125af32907f541727543bef", "size": 5688, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.2.1.tgz", "integrity": "sha512-6j7M/BA6FS6jVMBivWzt8k4CqDXXM0svsr2400dIw69fjGFIuiL+dnVRIlL4nTqJacJ2mHC06nu+mtOOM0aY1Q=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445933359110, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445933359110, "_cnpmcore_publish_time": "2021-12-16T10:36:01.138Z"}, "0.2.0": {"name": "wmf", "version": "0.2.0", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "4b8012804ea8d9cbb90e08ce4d338912afbf8f12", "_id": "wmf@0.2.0", "_shasum": "e3642399784a60e8d61e4fe1db88e34fd84db276", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "e3642399784a60e8d61e4fe1db88e34fd84db276", "size": 5078, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.2.0.tgz", "integrity": "sha512-6BSj7YDaQnIC2U7RFopNimtIKpSMhuXt+FbDwxr3vDKCCHSCQQX1cyXGMu07KyzJqSQv8tkXvuGNBI/vhZBBaQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445899477711, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445899477711, "_cnpmcore_publish_time": "2021-12-16T10:36:01.334Z"}, "0.1.2": {"name": "wmf", "version": "0.1.2", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://development.crix-dev.com/wmf/wmf"}, "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "de209024817e741be8f1ce3e2cbc37cbd6e1c427", "_id": "wmf@0.1.2", "_shasum": "fb3cbc1c38850c8f4a87f801200577ecada47de5", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "fb3cbc1c38850c8f4a87f801200577ecada47de5", "size": 5094, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.1.2.tgz", "integrity": "sha512-+6GTTvlpYAOMJiiiEx+YvRiD1LcfW0TbVMBnDun5YNHmXHfw/sCFALerAQA/anMLzbqU4Dg2qpMXvnqTeoEGMQ=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445898698520, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445898698520, "_cnpmcore_publish_time": "2021-12-16T10:36:01.540Z"}, "0.1.1": {"name": "wmf", "version": "0.1.1", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "0a7ed3cfef3dab8a6f8a26949e28c76bde31660c", "_id": "wmf@0.1.1", "_shasum": "a2ce4768f589dc791987ab2a334088823fbdf3ec", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "a2ce4768f589dc791987ab2a334088823fbdf3ec", "size": 4265, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.1.1.tgz", "integrity": "sha512-MTEfGN9t6p+YhroEIus8HwSRmvyUuTNU0oXTjHVtfc9zBVLl3rJy0K3Z5V4jGB+0NoasUlxaFUDfhUScNjgrrA=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445898500378, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445898500378, "_cnpmcore_publish_time": "2021-12-16T10:36:01.807Z"}, "0.1.0": {"name": "wmf", "version": "0.1.0", "description": "GTA:MP Waffle's Multipackage Framework", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"colors": "^1.1.2", "custom-logger": "^0.3.1"}, "devDependencies": {"babel": "^5.8.23", "babel-eslint": "^4.1.3", "eslint": "^1.6.0", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-react": "^3.5.1"}, "gitHead": "4775c524573898887e680ce65dfb8da966faf5d7", "_id": "wmf@0.1.0", "_shasum": "d1eee1c6bf35828838542837a65d5116442f4cfd", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "rukenshia", "email": "<EMAIL>"}, "dist": {"shasum": "d1eee1c6bf35828838542837a65d5116442f4cfd", "size": 4234, "noattachment": false, "tarball": "https://registry.npmmirror.com/wmf/-/wmf-0.1.0.tgz", "integrity": "sha512-j4AvPD6edcZeVIdtKsUMNArtRfxd9VZI6eew2WRtrFpWGy80f05i/A9x+KlBJKUF4BNrVaX573jVu2OfQT8adw=="}, "maintainers": [{"name": "sheetjs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1445898354393, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445898354393, "_cnpmcore_publish_time": "2021-12-16T10:36:02.008Z"}}, "_source_registry_name": "default"}