{"_attachments": {}, "_id": "semver", "_rev": "839-61f1455923990e8a812e8550", "description": "The semantic version parser used by npm.", "dist-tags": {"latest": "7.7.2", "latest-6": "6.3.1", "legacy": "5.7.2"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "semver", "readme": "semver(1) -- The semantic versioner for npm\n===========================================\n\n## Install\n\n```bash\nnpm install semver\n````\n\n## Usage\n\nAs a node module:\n\n```js\nconst semver = require('semver')\n\nsemver.valid('1.2.3') // '1.2.3'\nsemver.valid('a.b.c') // null\nsemver.clean('  =v1.2.3   ') // '1.2.3'\nsemver.satisfies('1.2.3', '1.x || >=2.5.0 || 5.0.0 - 7.2.3') // true\nsemver.gt('1.2.3', '9.8.7') // false\nsemver.lt('1.2.3', '9.8.7') // true\nsemver.minVersion('>=1.0.0') // '1.0.0'\nsemver.valid(semver.coerce('v2')) // '2.0.0'\nsemver.valid(semver.coerce('42.6.7.9.3-alpha')) // '42.6.7'\n```\n\nYou can also just load the module for the function that you care about if\nyou'd like to minimize your footprint.\n\n```js\n// load the whole API at once in a single object\nconst semver = require('semver')\n\n// or just load the bits you need\n// all of them listed here, just pick and choose what you want\n\n// classes\nconst SemVer = require('semver/classes/semver')\nconst Comparator = require('semver/classes/comparator')\nconst Range = require('semver/classes/range')\n\n// functions for working with versions\nconst semverParse = require('semver/functions/parse')\nconst semverValid = require('semver/functions/valid')\nconst semverClean = require('semver/functions/clean')\nconst semverInc = require('semver/functions/inc')\nconst semverDiff = require('semver/functions/diff')\nconst semverMajor = require('semver/functions/major')\nconst semverMinor = require('semver/functions/minor')\nconst semverPatch = require('semver/functions/patch')\nconst semverPrerelease = require('semver/functions/prerelease')\nconst semverCompare = require('semver/functions/compare')\nconst semverRcompare = require('semver/functions/rcompare')\nconst semverCompareLoose = require('semver/functions/compare-loose')\nconst semverCompareBuild = require('semver/functions/compare-build')\nconst semverSort = require('semver/functions/sort')\nconst semverRsort = require('semver/functions/rsort')\n\n// low-level comparators between versions\nconst semverGt = require('semver/functions/gt')\nconst semverLt = require('semver/functions/lt')\nconst semverEq = require('semver/functions/eq')\nconst semverNeq = require('semver/functions/neq')\nconst semverGte = require('semver/functions/gte')\nconst semverLte = require('semver/functions/lte')\nconst semverCmp = require('semver/functions/cmp')\nconst semverCoerce = require('semver/functions/coerce')\n\n// working with ranges\nconst semverSatisfies = require('semver/functions/satisfies')\nconst semverMaxSatisfying = require('semver/ranges/max-satisfying')\nconst semverMinSatisfying = require('semver/ranges/min-satisfying')\nconst semverToComparators = require('semver/ranges/to-comparators')\nconst semverMinVersion = require('semver/ranges/min-version')\nconst semverValidRange = require('semver/ranges/valid')\nconst semverOutside = require('semver/ranges/outside')\nconst semverGtr = require('semver/ranges/gtr')\nconst semverLtr = require('semver/ranges/ltr')\nconst semverIntersects = require('semver/ranges/intersects')\nconst semverSimplifyRange = require('semver/ranges/simplify')\nconst semverRangeSubset = require('semver/ranges/subset')\n```\n\nAs a command-line utility:\n\n```\n$ semver -h\n\nA JavaScript implementation of the https://semver.org/ specification\nCopyright Isaac Z. Schlueter\n\nUsage: semver [options] <version> [<version> [...]]\nPrints valid versions sorted by SemVer precedence\n\nOptions:\n-r --range <range>\n        Print versions that match the specified range.\n\n-i --increment [<level>]\n        Increment a version by the specified level.  Level can\n        be one of: major, minor, patch, premajor, preminor,\n        prepatch, prerelease, or release.  Default level is 'patch'.\n        Only one version may be specified.\n\n--preid <identifier>\n        Identifier to be used to prefix premajor, preminor,\n        prepatch or prerelease version increments.\n\n-l --loose\n        Interpret versions and ranges loosely\n\n-n <0|1>\n        This is the base to be used for the prerelease identifier.\n\n-p --include-prerelease\n        Always include prerelease versions in range matching\n\n-c --coerce\n        Coerce a string into SemVer if possible\n        (does not imply --loose)\n\n--rtl\n        Coerce version strings right to left\n\n--ltr\n        Coerce version strings left to right (default)\n\nProgram exits successfully if any valid version satisfies\nall supplied ranges, and prints all satisfying versions.\n\nIf no satisfying versions are found, then exits failure.\n\nVersions are printed in ascending order, so supplying\nmultiple versions to the utility will just sort them.\n```\n\n## Versions\n\nA \"version\" is described by the `v2.0.0` specification found at\n<https://semver.org/>.\n\nA leading `\"=\"` or `\"v\"` character is stripped off and ignored.\nSupport for stripping a leading \"v\" is kept for compatibility with `v1.0.0` of the SemVer\nspecification but should not be used anymore.\n\n## Ranges\n\nA `version range` is a set of `comparators` that specify versions\nthat satisfy the range.\n\nA `comparator` is composed of an `operator` and a `version`.  The set\nof primitive `operators` is:\n\n* `<` Less than\n* `<=` Less than or equal to\n* `>` Greater than\n* `>=` Greater than or equal to\n* `=` Equal.  If no operator is specified, then equality is assumed,\n  so this operator is optional but MAY be included.\n\nFor example, the comparator `>=1.2.7` would match the versions\n`1.2.7`, `1.2.8`, `2.5.3`, and `1.3.9`, but not the versions `1.2.6`\nor `1.1.0`. The comparator `>1` is equivalent to `>=2.0.0` and\nwould match the versions `2.0.0` and `3.1.0`, but not the versions\n`1.0.1` or `1.1.0`.\n\nComparators can be joined by whitespace to form a `comparator set`,\nwhich is satisfied by the **intersection** of all of the comparators\nit includes.\n\nA range is composed of one or more comparator sets, joined by `||`.  A\nversion matches a range if and only if every comparator in at least\none of the `||`-separated comparator sets is satisfied by the version.\n\nFor example, the range `>=1.2.7 <1.3.0` would match the versions\n`1.2.7`, `1.2.8`, and `1.2.99`, but not the versions `1.2.6`, `1.3.0`,\nor `1.1.0`.\n\nThe range `1.2.7 || >=1.2.9 <2.0.0` would match the versions `1.2.7`,\n`1.2.9`, and `1.4.6`, but not the versions `1.2.8` or `2.0.0`.\n\n### Prerelease Tags\n\nIf a version has a prerelease tag (for example, `1.2.3-alpha.3`) then\nit will only be allowed to satisfy comparator sets if at least one\ncomparator with the same `[major, minor, patch]` tuple also has a\nprerelease tag.\n\nFor example, the range `>1.2.3-alpha.3` would be allowed to match the\nversion `1.2.3-alpha.7`, but it would *not* be satisfied by\n`3.4.5-alpha.9`, even though `3.4.5-alpha.9` is technically \"greater\nthan\" `1.2.3-alpha.3` according to the SemVer sort rules.  The version\nrange only accepts prerelease tags on the `1.2.3` version.\nVersion `3.4.5` *would* satisfy the range because it does not have a\nprerelease flag, and `3.4.5` is greater than `1.2.3-alpha.7`.\n\nThe purpose of this behavior is twofold.  First, prerelease versions\nfrequently are updated very quickly, and contain many breaking changes\nthat are (by the author's design) not yet fit for public consumption.\nTherefore, by default, they are excluded from range-matching\nsemantics.\n\nSecond, a user who has opted into using a prerelease version has\nindicated the intent to use *that specific* set of\nalpha/beta/rc versions.  By including a prerelease tag in the range,\nthe user is indicating that they are aware of the risk.  However, it\nis still not appropriate to assume that they have opted into taking a\nsimilar risk on the *next* set of prerelease versions.\n\nNote that this behavior can be suppressed (treating all prerelease\nversions as if they were normal versions, for range-matching)\nby setting the `includePrerelease` flag on the options\nobject to any\n[functions](https://github.com/npm/node-semver#functions) that do\nrange matching.\n\n#### Prerelease Identifiers\n\nThe method `.inc` takes an additional `identifier` string argument that\nwill append the value of the string as a prerelease identifier:\n\n```javascript\nsemver.inc('1.2.3', 'prerelease', 'beta')\n// '1.2.4-beta.0'\n```\n\ncommand-line example:\n\n```bash\n$ semver 1.2.3 -i prerelease --preid beta\n1.2.4-beta.0\n```\n\nWhich then can be used to increment further:\n\n```bash\n$ semver 1.2.4-beta.0 -i prerelease\n1.2.4-beta.1\n```\n\nTo get out of the prerelease phase, use the `release` option:\n\n```bash\n$ semver 1.2.4-beta.1 -i release\n1.2.4\n```\n\n#### Prerelease Identifier Base\n\nThe method `.inc` takes an optional parameter 'identifierBase' string\nthat will let you let your prerelease number as zero-based or one-based.\nSet to `false` to omit the prerelease number altogether.\nIf you do not specify this parameter, it will default to zero-based.\n\n```javascript\nsemver.inc('1.2.3', 'prerelease', 'beta', '1')\n// '1.2.4-beta.1'\n```\n\n```javascript\nsemver.inc('1.2.3', 'prerelease', 'beta', false)\n// '1.2.4-beta'\n```\n\ncommand-line example:\n\n```bash\n$ semver 1.2.3 -i prerelease --preid beta -n 1\n1.2.4-beta.1\n```\n\n```bash\n$ semver 1.2.3 -i prerelease --preid beta -n false\n1.2.4-beta\n```\n\n### Advanced Range Syntax\n\nAdvanced range syntax desugars to primitive comparators in\ndeterministic ways.\n\nAdvanced ranges may be combined in the same way as primitive\ncomparators using white space or `||`.\n\n#### Hyphen Ranges `X.Y.Z - A.B.C`\n\nSpecifies an inclusive set.\n\n* `1.2.3 - 2.3.4` := `>=1.2.3 <=2.3.4`\n\nIf a partial version is provided as the first version in the inclusive\nrange, then the missing pieces are replaced with zeroes.\n\n* `1.2 - 2.3.4` := `>=1.2.0 <=2.3.4`\n\nIf a partial version is provided as the second version in the\ninclusive range, then all versions that start with the supplied parts\nof the tuple are accepted, but nothing that would be greater than the\nprovided tuple parts.\n\n* `1.2.3 - 2.3` := `>=1.2.3 <2.4.0-0`\n* `1.2.3 - 2` := `>=1.2.3 <3.0.0-0`\n\n#### X-Ranges `1.2.x` `1.X` `1.2.*` `*`\n\nAny of `X`, `x`, or `*` may be used to \"stand in\" for one of the\nnumeric values in the `[major, minor, patch]` tuple.\n\n* `*` := `>=0.0.0` (Any non-prerelease version satisfies, unless\n  `includePrerelease` is specified, in which case any version at all\n  satisfies)\n* `1.x` := `>=1.0.0 <2.0.0-0` (Matching major version)\n* `1.2.x` := `>=1.2.0 <1.3.0-0` (Matching major and minor versions)\n\nA partial version range is treated as an X-Range, so the special\ncharacter is in fact optional.\n\n* `\"\"` (empty string) := `*` := `>=0.0.0`\n* `1` := `1.x.x` := `>=1.0.0 <2.0.0-0`\n* `1.2` := `1.2.x` := `>=1.2.0 <1.3.0-0`\n\n#### Tilde Ranges `~1.2.3` `~1.2` `~1`\n\nAllows patch-level changes if a minor version is specified on the\ncomparator.  Allows minor-level changes if not.\n\n* `~1.2.3` := `>=1.2.3 <1.(2+1).0` := `>=1.2.3 <1.3.0-0`\n* `~1.2` := `>=1.2.0 <1.(2+1).0` := `>=1.2.0 <1.3.0-0` (Same as `1.2.x`)\n* `~1` := `>=1.0.0 <(1+1).0.0` := `>=1.0.0 <2.0.0-0` (Same as `1.x`)\n* `~0.2.3` := `>=0.2.3 <0.(2+1).0` := `>=0.2.3 <0.3.0-0`\n* `~0.2` := `>=0.2.0 <0.(2+1).0` := `>=0.2.0 <0.3.0-0` (Same as `0.2.x`)\n* `~0` := `>=0.0.0 <(0+1).0.0` := `>=0.0.0 <1.0.0-0` (Same as `0.x`)\n* `~1.2.3-beta.2` := `>=1.2.3-beta.2 <1.3.0-0` Note that prereleases in\n  the `1.2.3` version will be allowed, if they are greater than or\n  equal to `beta.2`.  So, `1.2.3-beta.4` would be allowed, but\n  `1.2.4-beta.2` would not, because it is a prerelease of a\n  different `[major, minor, patch]` tuple.\n\n#### Caret Ranges `^1.2.3` `^0.2.5` `^0.0.4`\n\nAllows changes that do not modify the left-most non-zero element in the\n`[major, minor, patch]` tuple.  In other words, this allows patch and\nminor updates for versions `1.0.0` and above, patch updates for\nversions `0.X >=0.1.0`, and *no* updates for versions `0.0.X`.\n\nMany authors treat a `0.x` version as if the `x` were the major\n\"breaking-change\" indicator.\n\nCaret ranges are ideal when an author may make breaking changes\nbetween `0.2.4` and `0.3.0` releases, which is a common practice.\nHowever, it presumes that there will *not* be breaking changes between\n`0.2.4` and `0.2.5`.  It allows for changes that are presumed to be\nadditive (but non-breaking), according to commonly observed practices.\n\n* `^1.2.3` := `>=1.2.3 <2.0.0-0`\n* `^0.2.3` := `>=0.2.3 <0.3.0-0`\n* `^0.0.3` := `>=0.0.3 <0.0.4-0`\n* `^1.2.3-beta.2` := `>=1.2.3-beta.2 <2.0.0-0` Note that prereleases in\n  the `1.2.3` version will be allowed, if they are greater than or\n  equal to `beta.2`.  So, `1.2.3-beta.4` would be allowed, but\n  `1.2.4-beta.2` would not, because it is a prerelease of a\n  different `[major, minor, patch]` tuple.\n* `^0.0.3-beta` := `>=0.0.3-beta <0.0.4-0`  Note that prereleases in the\n  `0.0.3` version *only* will be allowed, if they are greater than or\n  equal to `beta`.  So, `0.0.3-pr.2` would be allowed.\n\nWhen parsing caret ranges, a missing `patch` value desugars to the\nnumber `0`, but will allow flexibility within that value, even if the\nmajor and minor versions are both `0`.\n\n* `^1.2.x` := `>=1.2.0 <2.0.0-0`\n* `^0.0.x` := `>=0.0.0 <0.1.0-0`\n* `^0.0` := `>=0.0.0 <0.1.0-0`\n\nA missing `minor` and `patch` values will desugar to zero, but also\nallow flexibility within those values, even if the major version is\nzero.\n\n* `^1.x` := `>=1.0.0 <2.0.0-0`\n* `^0.x` := `>=0.0.0 <1.0.0-0`\n\n### Range Grammar\n\nPutting all this together, here is a Backus-Naur grammar for ranges,\nfor the benefit of parser authors:\n\n```bnf\nrange-set  ::= range ( logical-or range ) *\nlogical-or ::= ( ' ' ) * '||' ( ' ' ) *\nrange      ::= hyphen | simple ( ' ' simple ) * | ''\nhyphen     ::= partial ' - ' partial\nsimple     ::= primitive | partial | tilde | caret\nprimitive  ::= ( '<' | '>' | '>=' | '<=' | '=' ) partial\npartial    ::= xr ( '.' xr ( '.' xr qualifier ? )? )?\nxr         ::= 'x' | 'X' | '*' | nr\nnr         ::= '0' | ['1'-'9'] ( ['0'-'9'] ) *\ntilde      ::= '~' partial\ncaret      ::= '^' partial\nqualifier  ::= ( '-' pre )? ( '+' build )?\npre        ::= parts\nbuild      ::= parts\nparts      ::= part ( '.' part ) *\npart       ::= nr | [-0-9A-Za-z]+\n```\n\n## Functions\n\nAll methods and classes take a final `options` object argument.  All\noptions in this object are `false` by default.  The options supported\nare:\n\n- `loose`: Be more forgiving about not-quite-valid semver strings.\n  (Any resulting output will always be 100% strict compliant, of\n  course.)  For backwards compatibility reasons, if the `options`\n  argument is a boolean value instead of an object, it is interpreted\n  to be the `loose` param.\n- `includePrerelease`: Set to suppress the [default\n  behavior](https://github.com/npm/node-semver#prerelease-tags) of\n  excluding prerelease tagged versions from ranges unless they are\n  explicitly opted into.\n\nStrict-mode Comparators and Ranges will be strict about the SemVer\nstrings that they parse.\n\n* `valid(v)`: Return the parsed version, or null if it's not valid.\n* `inc(v, releaseType, options, identifier, identifierBase)`: \n  Return the version incremented by the release\n  type (`major`, `premajor`, `minor`, `preminor`, `patch`,\n  `prepatch`, `prerelease`, or `release`), or null if it's not valid\n  * `premajor` in one call will bump the version up to the next major\n    version and down to a prerelease of that major version.\n    `preminor`, and `prepatch` work the same way.\n  * If called from a non-prerelease version, `prerelease` will work the\n    same as `prepatch`. It increments the patch version and then makes a\n    prerelease. If the input version is already a prerelease it simply\n    increments it.\n  * `release` will remove any prerelease part of the version.\n  * `identifier` can be used to prefix `premajor`, `preminor`,\n    `prepatch`, or `prerelease` version increments. `identifierBase`\n    is the base to be used for the `prerelease` identifier.\n* `prerelease(v)`: Returns an array of prerelease components, or null\n  if none exist. Example: `prerelease('1.2.3-alpha.1') -> ['alpha', 1]`\n* `major(v)`: Return the major version number.\n* `minor(v)`: Return the minor version number.\n* `patch(v)`: Return the patch version number.\n* `intersects(r1, r2, loose)`: Return true if the two supplied ranges\n  or comparators intersect.\n* `parse(v)`: Attempt to parse a string as a semantic version, returning either\n  a `SemVer` object or `null`.\n\n### Comparison\n\n* `gt(v1, v2)`: `v1 > v2`\n* `gte(v1, v2)`: `v1 >= v2`\n* `lt(v1, v2)`: `v1 < v2`\n* `lte(v1, v2)`: `v1 <= v2`\n* `eq(v1, v2)`: `v1 == v2` This is true if they're logically equivalent,\n  even if they're not the same string.  You already know how to\n  compare strings.\n* `neq(v1, v2)`: `v1 != v2` The opposite of `eq`.\n* `cmp(v1, comparator, v2)`: Pass in a comparison string, and it'll call\n  the corresponding function above.  `\"===\"` and `\"!==\"` do simple\n  string comparison, but are included for completeness.  Throws if an\n  invalid comparison string is provided.\n* `compare(v1, v2)`: Return `0` if `v1 == v2`, or `1` if `v1` is greater, or `-1` if\n  `v2` is greater.  Sorts in ascending order if passed to `Array.sort()`.\n* `rcompare(v1, v2)`: The reverse of `compare`.  Sorts an array of versions\n  in descending order when passed to `Array.sort()`.\n* `compareBuild(v1, v2)`: The same as `compare` but considers `build` when two versions\n  are equal.  Sorts in ascending order if passed to `Array.sort()`.\n* `compareLoose(v1, v2)`: Short for `compare(v1, v2, { loose: true })`.\n* `diff(v1, v2)`: Returns the difference between two versions by the release type\n  (`major`, `premajor`, `minor`, `preminor`, `patch`, `prepatch`, or `prerelease`),\n  or null if the versions are the same.\n\n### Sorting\n\n* `sort(versions)`: Returns a sorted array of versions based on the `compareBuild` \n  function.\n* `rsort(versions)`: The reverse of `sort`. Returns an array of versions based on\n  the `compareBuild` function in descending order.\n\n### Comparators\n\n* `intersects(comparator)`: Return true if the comparators intersect\n\n### Ranges\n\n* `validRange(range)`: Return the valid range or null if it's not valid.\n* `satisfies(version, range)`: Return true if the version satisfies the\n  range.\n* `maxSatisfying(versions, range)`: Return the highest version in the list\n  that satisfies the range, or `null` if none of them do.\n* `minSatisfying(versions, range)`: Return the lowest version in the list\n  that satisfies the range, or `null` if none of them do.\n* `minVersion(range)`: Return the lowest version that can match\n  the given range.\n* `gtr(version, range)`: Return `true` if the version is greater than all the\n  versions possible in the range.\n* `ltr(version, range)`: Return `true` if the version is less than all the\n  versions possible in the range.\n* `outside(version, range, hilo)`: Return true if the version is outside\n  the bounds of the range in either the high or low direction.  The\n  `hilo` argument must be either the string `'>'` or `'<'`.  (This is\n  the function called by `gtr` and `ltr`.)\n* `intersects(range)`: Return true if any of the range comparators intersect.\n* `simplifyRange(versions, range)`: Return a \"simplified\" range that\n  matches the same items in the `versions` list as the range specified.  Note\n  that it does *not* guarantee that it would match the same versions in all\n  cases, only for the set of versions provided.  This is useful when\n  generating ranges by joining together multiple versions with `||`\n  programmatically, to provide the user with something a bit more\n  ergonomic.  If the provided range is shorter in string-length than the\n  generated range, then that is returned.\n* `subset(subRange, superRange)`: Return `true` if the `subRange` range is\n  entirely contained by the `superRange` range.\n\nNote that, since ranges may be non-contiguous, a version might not be\ngreater than a range, less than a range, *or* satisfy a range!  For\nexample, the range `1.2 <1.2.9 || >2.0.0` would have a hole from `1.2.9`\nuntil `2.0.0`, so version `1.2.10` would not be greater than the\nrange (because `2.0.1` satisfies, which is higher), nor less than the\nrange (since `1.2.8` satisfies, which is lower), and it also does not\nsatisfy the range.\n\nIf you want to know if a version satisfies or does not satisfy a\nrange, use the `satisfies(version, range)` function.\n\n### Coercion\n\n* `coerce(version, options)`: Coerces a string to semver if possible\n\nThis aims to provide a very forgiving translation of a non-semver string to\nsemver. It looks for the first digit in a string and consumes all\nremaining characters which satisfy at least a partial semver (e.g., `1`,\n`1.2`, `1.2.3`) up to the max permitted length (256 characters).  Longer\nversions are simply truncated (`4.6.3.9.2-alpha2` becomes `4.6.3`).  All\nsurrounding text is simply ignored (`v3.4 replaces v3.3.1` becomes\n`3.4.0`).  Only text which lacks digits will fail coercion (`version one`\nis not valid).  The maximum length for any semver component considered for\ncoercion is 16 characters; longer components will be ignored\n(`10000000000000000.4.7.4` becomes `4.7.4`).  The maximum value for any\nsemver component is `Number.MAX_SAFE_INTEGER || (2**53 - 1)`; higher value\ncomponents are invalid (`9999999999999999.4.7.4` is likely invalid).\n\nIf the `options.rtl` flag is set, then `coerce` will return the right-most\ncoercible tuple that does not share an ending index with a longer coercible\ntuple.  For example, `1.2.3.4` will return `2.3.4` in rtl mode, not\n`4.0.0`.  `1.2.3/4` will return `4.0.0`, because the `4` is not a part of\nany other overlapping SemVer tuple.\n\nIf the `options.includePrerelease` flag is set, then the `coerce` result will contain\nprerelease and build parts of a version.  For example, `1.2.3.4-rc.1+rev.2`\nwill preserve prerelease `rc.1` and build `rev.2` in the result.\n\n### Clean\n\n* `clean(version)`: Clean a string to be a valid semver if possible\n\nThis will return a cleaned and trimmed semver version. If the provided\nversion is not valid a null will be returned. This does not work for\nranges.\n\nex.\n* `s.clean(' = v 2.1.5foo')`: `null`\n* `s.clean(' = v 2.1.5foo', { loose: true })`: `'2.1.5-foo'`\n* `s.clean(' = v 2.1.5-foo')`: `null`\n* `s.clean(' = v 2.1.5-foo', { loose: true })`: `'2.1.5-foo'`\n* `s.clean('=v2.1.5')`: `'2.1.5'`\n* `s.clean('  =v2.1.5')`: `'2.1.5'`\n* `s.clean('      2.1.5   ')`: `'2.1.5'`\n* `s.clean('~1.0.0')`: `null`\n\n## Constants\n\nAs a convenience, helper constants are exported to provide information about what `node-semver` supports:\n\n### `RELEASE_TYPES`\n\n- major\n- premajor\n- minor\n- preminor\n- patch\n- prepatch\n- prerelease\n\n```\nconst semver = require('semver');\n\nif (semver.RELEASE_TYPES.includes(arbitraryUserInput)) {\n  console.log('This is a valid release type!');\n} else {\n  console.warn('This is NOT a valid release type!');\n}\n```\n\n### `SEMVER_SPEC_VERSION`\n\n2.0.0\n\n```\nconst semver = require('semver');\n\nconsole.log('We are currently using the semver specification version:', semver.SEMVER_SPEC_VERSION);\n```\n\n## Exported Modules\n\n<!--\nTODO: Make sure that all of these items are documented (classes aren't,\neg), and then pull the module name into the documentation for that specific\nthing.\n-->\n\nYou may pull in just the part of this semver utility that you need if you\nare sensitive to packing and tree-shaking concerns.  The main\n`require('semver')` export uses getter functions to lazily load the parts\nof the API that are used.\n\nThe following modules are available:\n\n* `require('semver')`\n* `require('semver/classes')`\n* `require('semver/classes/comparator')`\n* `require('semver/classes/range')`\n* `require('semver/classes/semver')`\n* `require('semver/functions/clean')`\n* `require('semver/functions/cmp')`\n* `require('semver/functions/coerce')`\n* `require('semver/functions/compare')`\n* `require('semver/functions/compare-build')`\n* `require('semver/functions/compare-loose')`\n* `require('semver/functions/diff')`\n* `require('semver/functions/eq')`\n* `require('semver/functions/gt')`\n* `require('semver/functions/gte')`\n* `require('semver/functions/inc')`\n* `require('semver/functions/lt')`\n* `require('semver/functions/lte')`\n* `require('semver/functions/major')`\n* `require('semver/functions/minor')`\n* `require('semver/functions/neq')`\n* `require('semver/functions/parse')`\n* `require('semver/functions/patch')`\n* `require('semver/functions/prerelease')`\n* `require('semver/functions/rcompare')`\n* `require('semver/functions/rsort')`\n* `require('semver/functions/satisfies')`\n* `require('semver/functions/sort')`\n* `require('semver/functions/valid')`\n* `require('semver/ranges/gtr')`\n* `require('semver/ranges/intersects')`\n* `require('semver/ranges/ltr')`\n* `require('semver/ranges/max-satisfying')`\n* `require('semver/ranges/min-satisfying')`\n* `require('semver/ranges/min-version')`\n* `require('semver/ranges/outside')`\n* `require('semver/ranges/simplify')`\n* `require('semver/ranges/subset')`\n* `require('semver/ranges/to-comparators')`\n* `require('semver/ranges/valid')`\n\n", "time": {"created": "2022-01-26T12:58:01.421Z", "modified": "2025-07-14T02:32:23.811Z", "7.3.5": "2021-03-23T01:37:52.803Z", "7.3.4": "2020-12-01T20:15:13.977Z", "7.3.3": "2020-12-01T19:30:05.865Z", "7.3.2": "2020-04-14T17:43:28.451Z", "7.3.1": "2020-04-14T16:56:08.021Z", "7.3.0": "2020-04-14T01:08:44.962Z", "7.2.3": "2020-04-13T18:31:33.110Z", "7.2.2": "2020-04-10T16:01:16.989Z", "7.2.1": "2020-04-06T23:37:44.278Z", "7.2.0": "2020-04-06T23:36:09.707Z", "7.1.3": "2020-02-11T21:54:05.273Z", "7.1.2": "2020-01-31T01:29:45.224Z", "7.1.1": "2019-12-17T16:56:29.010Z", "7.1.0": "2019-12-17T01:28:48.900Z", "7.0.0": "2019-12-14T19:36:54.748Z", "5.7.1": "2019-08-12T16:28:15.053Z", "6.3.0": "2019-07-23T19:25:26.568Z", "6.2.0": "2019-07-01T23:03:27.604Z", "6.1.3": "2019-07-01T05:51:08.761Z", "6.1.2": "2019-06-24T01:48:03.240Z", "6.1.1": "2019-05-28T17:15:08.376Z", "6.1.0": "2019-05-22T21:12:49.111Z", "6.0.0": "2019-03-26T23:30:05.580Z", "5.7.0": "2019-03-26T23:25:47.130Z", "5.6.0": "2018-10-10T23:52:25.375Z", "5.5.1": "2018-08-17T20:35:46.676Z", "5.5.0": "2018-01-16T19:27:59.818Z", "5.4.1": "2017-07-24T18:48:27.785Z", "5.4.0": "2017-07-24T16:39:33.594Z", "5.3.0": "2016-07-14T16:52:47.104Z", "5.2.0": "2016-06-28T18:00:41.679Z", "5.1.1": "2016-06-23T18:00:51.598Z", "5.1.0": "2015-11-18T23:18:02.918Z", "5.0.3": "2015-09-11T20:27:31.563Z", "5.0.2": "2015-09-11T17:09:40.057Z", "5.0.1": "2015-07-13T20:02:27.516Z", "5.0.0": "2015-07-11T17:29:40.652Z", "4.3.6": "2015-06-01T04:16:22.945Z", "4.3.5": "2015-05-29T22:25:40.918Z", "4.3.4": "2015-05-05T04:26:05.035Z", "4.3.3": "2015-03-27T16:56:24.729Z", "4.3.2": "2015-03-27T01:26:08.892Z", "4.3.1": "2015-02-24T19:49:50.416Z", "4.3.0": "2015-02-12T20:08:38.236Z", "4.2.2": "2015-02-10T06:46:44.370Z", "4.2.1": "2015-02-10T06:44:26.265Z", "4.2.0": "2014-12-23T09:42:46.263Z", "4.1.1": "2014-12-19T12:57:14.981Z", "4.1.0": "2014-10-16T00:55:35.923Z", "4.0.3": "2014-10-01T00:18:37.208Z", "4.0.2": "2014-09-30T23:55:26.916Z", "4.0.0": "2014-09-11T22:36:27.208Z", "3.0.1": "2014-07-24T17:24:36.175Z", "3.0.0": "2014-07-23T21:14:29.806Z", "2.3.2": "2014-07-22T19:24:50.090Z", "2.3.1": "2014-06-18T22:48:16.706Z", "2.3.0": "2014-05-07T01:15:02.092Z", "2.2.1": "2013-10-28T18:18:10.005Z", "2.2.0": "2013-10-25T20:02:44.049Z", "2.1.0": "2013-08-01T23:52:31.371Z", "2.0.11": "2013-07-24T03:23:19.907Z", "2.0.10": "2013-07-09T22:39:16.895Z", "2.0.9": "2013-07-06T03:45:40.578Z", "2.0.8": "2013-06-24T22:12:37.887Z", "2.0.7": "2013-06-20T18:57:03.281Z", "2.0.6": "2013-06-20T18:41:20.343Z", "2.0.5": "2013-06-20T15:42:04.599Z", "2.0.4": "2013-06-20T15:33:20.522Z", "2.0.3": "2013-06-20T15:15:33.034Z", "2.0.2": "2013-06-20T15:05:58.554Z", "2.0.1": "2013-06-20T04:42:51.354Z", "2.0.0-beta": "2013-06-18T00:01:26.679Z", "2.0.0-alpha": "2013-06-15T03:29:33.540Z", "1.1.4": "2013-03-01T18:14:56.811Z", "1.1.3": "2013-02-06T15:42:39.566Z", "1.1.2": "2013-01-06T16:25:56.424Z", "1.1.1": "2012-11-29T00:46:21.597Z", "1.1.0": "2012-10-02T17:02:34.309Z", "1.0.14": "2012-05-27T00:47:32.831Z", "1.0.13": "2011-12-21T17:07:14.144Z", "1.0.12": "2011-11-18T19:04:02.511Z", "1.0.11": "2011-11-15T16:40:04.239Z", "1.0.10": "2011-10-04T01:51:37.206Z", "1.0.9": "2011-07-20T21:38:13.081Z", "1.0.8": "2011-06-27T21:58:51.266Z", "1.0.7": "2011-06-17T16:26:07.324Z", "1.0.6": "2011-05-21T00:09:47.724Z", "1.0.5": "2011-05-03T23:11:54.939Z", "1.0.4": "2011-04-21T07:32:11.512Z", "1.0.3": "2011-04-19T23:29:13.670Z", "1.0.2": "2011-03-22T21:27:35.218Z", "1.0.1": "2011-02-18T17:15:49.775Z", "1.0.0": "2011-02-12T00:20:26.037Z", "7.3.6": "2022-04-06T16:35:25.625Z", "7.3.7": "2022-04-12T17:26:24.970Z", "7.3.8": "2022-10-04T19:40:47.960Z", "7.4.0": "2023-04-10T21:57:46.268Z", "7.5.0": "2023-04-17T17:22:56.540Z", "7.5.1": "2023-05-12T16:39:41.720Z", "7.5.2": "2023-06-15T20:26:11.975Z", "7.5.3": "2023-06-22T21:53:19.774Z", "7.5.4": "2023-07-07T21:10:32.589Z", "5.7.2": "2023-07-10T19:57:47.111Z", "6.3.1": "2023-07-10T22:38:41.428Z", "7.6.0": "2024-02-05T17:06:51.520Z", "7.6.1": "2024-05-07T16:02:28.840Z", "7.6.2": "2024-05-09T16:02:50.012Z", "7.6.3": "2024-07-16T22:27:19.119Z", "7.7.0": "2025-01-29T17:14:41.608Z", "7.7.1": "2025-02-03T21:46:12.515Z", "7.7.2": "2025-05-12T17:02:28.372Z"}, "versions": {"7.3.5": {"name": "semver", "version": "7.3.5", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "gitHead": "e79ac3a450e8bb504e78b8159e3efc70895699b8", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.5", "_nodeVersion": "15.3.0", "_npmVersion": "7.6.3", "dist": {"shasum": "0b621c879348d8998e4b0e4be94b3f12e6018ef7", "size": 26292, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz", "integrity": "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.5_1616463472660_0.6244304507667418"}, "_hasShrinkwrap": false, "publish_time": 1616463472803, "_cnpm_publish_time": 1616463472803, "_cnpmcore_publish_time": "2021-12-13T06:50:46.168Z"}, "7.3.4": {"name": "semver", "version": "7.3.4", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "gitHead": "093b40f8a7cb67946527b739fe8f8974c888e2a0", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.4", "_nodeVersion": "15.3.0", "_npmVersion": "7.0.12", "dist": {"shasum": "27aaa7d2e4ca76452f98d3add093a72c943edc97", "size": 25318, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.4.tgz", "integrity": "sha512-tCfb2WLjqFAtXn4KEdxIhalnRtoKFN7nAwj0B3ZXCbQloV2tq5eDbcTmT68JJD3nRJq24/XgxtQKFIpQdtvmVw=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.4_1606853713862_0.733630657601954"}, "_hasShrinkwrap": false, "publish_time": 1606853713977, "_cnpm_publish_time": 1606853713977, "_cnpmcore_publish_time": "2021-12-13T06:50:46.485Z"}, "7.3.3": {"name": "semver", "version": "7.3.3", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^4.1.5"}, "gitHead": "984a8d5f2d403f90ca95c201e9ba061ac96ca3fc", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.3", "_nodeVersion": "15.3.0", "_npmVersion": "7.0.12", "dist": {"shasum": "a6cef9257e40f4b8a78cd97e106d215f8a3ebdc9", "size": 25318, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.3.tgz", "integrity": "sha512-kBGrn+sE2tyi6f4c9aFrrYRSTTF5yNOEVRBCdpcgykFp3jt2ZGlBwzIwWER9J9HZnQa9IF1TrR8Xy2UU+eaUhQ=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.3_1606851005696_0.7698350137108039"}, "_hasShrinkwrap": false, "publish_time": 1606851005865, "_cnpm_publish_time": 1606851005865, "_cnpmcore_publish_time": "2021-12-13T06:50:46.840Z"}, "7.3.2": {"name": "semver", "version": "7.3.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "ce978f9a58b71d22a7c303432c9a5135510e01be", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "604962b052b81ed0786aae84389ffba70ffd3938", "size": 24526, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.2.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>32TeeambH6UrhtShmF7CRDqhL6/5XpPNp2DuRH6+9QLw/orhp72j87v8Qa1ScDkvrrBNpZcDejAirJmfXQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.2_1586886208300_0.6137271223938625"}, "_hasShrinkwrap": false, "publish_time": 1586886208451, "_cnpm_publish_time": 1586886208451, "_cnpmcore_publish_time": "2021-12-13T06:50:47.171Z"}, "7.3.1": {"name": "semver", "version": "7.3.1", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "b97044b0de1a771bff151c40695fd7e340b3a09c", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.1", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "8fbf3298570e73df7733f1b89e7f08e653cf1b8f", "size": 24515, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.1.tgz", "integrity": "sha512-r7sBEClKDfDFhPQfIk2D+EF74SS1j4uyfto50/KWQRzQj8IeKD7wqLbwkqb33289rtqQZoKzID5WOavkJ63DGg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.1_1586883367892_0.4603661322871977"}, "_hasShrinkwrap": false, "publish_time": 1586883368021, "_cnpm_publish_time": 1586883368021, "_cnpmcore_publish_time": "2021-12-13T06:50:47.528Z"}, "7.3.0": {"name": "semver", "version": "7.3.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "92bccf1d0950c9bd136f58886036e8c1921cd9a1", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.0", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "91f7c70ec944a63e5dc7a74cde2da375d8e0853c", "size": 24509, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.0.tgz", "integrity": "sha512-uyvgU/igkrMgNHwLgXvlpD9jEADbJhB0+JXSywoO47JgJ6c16iau9F9cjtc/E5o0PoqRYTiTIAPRKaYe84z6eQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.0_1586826524726_0.19048944441720184"}, "_hasShrinkwrap": false, "publish_time": 1586826524962, "_cnpm_publish_time": 1586826524962, "_cnpmcore_publish_time": "2021-12-13T06:50:47.908Z"}, "7.2.3": {"name": "semver", "version": "7.2.3", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "45b14954eac049a1d2824fb5543753e53192216a", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.2.3", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "3641217233c6382173c76bf2c7ecd1e1c16b0d8a", "size": 22961, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.2.3.tgz", "integrity": "sha512-utbW9Z7ZxVvwiIWkdOMLOR9G/NFXh2aRucghkVrEMJWuC++r3lCkBC3LwqBinyHzGMAJxY5tn6VakZGHObq5ig=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.2.3_1586802692959_0.21135831995159804"}, "_hasShrinkwrap": false, "publish_time": 1586802693110, "_cnpm_publish_time": 1586802693110, "_cnpmcore_publish_time": "2021-12-13T06:50:48.265Z"}, "7.2.2": {"name": "semver", "version": "7.2.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "5d0dcdac5daeef368b73b9b67d1aa6f554315e2b", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.2.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "d01432d74ed3010a20ffaf909d63a691520521cd", "size": 22923, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.2.2.tgz", "integrity": "sha512-Zo84u6o2PebMSK3zjJ6Zp5wi8VnQZnEaCP13Ul/lt1ANsLACxnJxq4EEm1PY94/por1Hm9+7xpIswdS5AkieMA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.2.2_1586534476820_0.045995279100802255"}, "_hasShrinkwrap": false, "publish_time": 1586534476989, "_cnpm_publish_time": 1586534476989, "_cnpmcore_publish_time": "2021-12-13T06:50:48.608Z"}, "7.2.1": {"name": "semver", "version": "7.2.1", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "dfe658fd611ccbf6703b1c9315f9ad8cb29db1bb", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.2.1", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "d997aa36bdbb00b501ae4ac4c7d17e9f7a587ae5", "size": 22757, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.2.1.tgz", "integrity": "sha512-aHhm1pD02jXXkyIpq25qBZjr3CQgg8KST8uX0OWXch3xE6jw+1bfbWnCjzMwojsTquroUmKFHNzU6x26mEiRxw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.2.1_1586216264072_0.03363624901937112"}, "_hasShrinkwrap": false, "publish_time": 1586216264278, "_cnpm_publish_time": 1586216264278, "_cnpmcore_publish_time": "2021-12-13T06:50:48.973Z"}, "7.2.0": {"name": "semver", "version": "7.2.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "c6581a8b6bf6dac430a30eb6be60ed0e06c22f74", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.2.0", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "e4c8e6ca40b6b953175194ae89b628afdc0a20e1", "size": 24860, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.2.0.tgz", "integrity": "sha512-VOQxUCRgttu7mxuvAgselSlok1XoOXju6XSJdTBo8+8RsvnPwKXEZtZVKvzlNigT1mXH2fDupcT8oX8Vw1Vm2w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.2.0_1586216169599_0.0732386597934036"}, "_hasShrinkwrap": false, "publish_time": 1586216169707, "_cnpm_publish_time": 1586216169707, "_cnpmcore_publish_time": "2021-12-13T06:50:49.402Z"}, "7.1.3": {"name": "semver", "version": "7.1.3", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "6e7982f23a0f2a378dad80de6a9acb435154e652", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.1.3", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.7", "dist": {"shasum": "e4345ce73071c53f336445cfc19efb1c311df2a6", "size": 22142, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.1.3.tgz", "integrity": "sha512-ekM0zfiA9SCBlsKa2X1hxyxiI4L3B6EbVJkkdgQXnSEEaHlGdvyodMruTiulSRWMMB4NeIuYNMC9rTKTz97GxA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.1.3_1581458045178_0.7603358869317778"}, "_hasShrinkwrap": false, "publish_time": 1581458045273, "_cnpm_publish_time": 1581458045273, "_cnpmcore_publish_time": "2021-12-13T06:50:49.775Z"}, "7.1.2": {"name": "semver", "version": "7.1.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "8f4d96d7816c296d311eef101588a3809170ea2b", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.1.2", "_nodeVersion": "14.0.0-pre", "_npmVersion": "6.13.7", "dist": {"shasum": "847bae5bce68c5d08889824f02667199b70e3d87", "size": 22134, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.1.2.tgz", "integrity": "sha512-BJs9T/H8sEVHbeigqzIEo57Iu/3DG6c4QoqTfbQB3BPA4zgzAomh/Fk9E7QtjWQ8mx2dgA9YCfSF4y9k9bHNpQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.1.2_1580434184945_0.2853223384213779"}, "_hasShrinkwrap": false, "publish_time": 1580434185224, "_cnpm_publish_time": 1580434185224, "_cnpmcore_publish_time": "2021-12-13T06:50:50.499Z"}, "7.1.1": {"name": "semver", "version": "7.1.1", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "bb36c98d71d5760d730abba71c68bc324035dd36", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.1.1", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.4", "dist": {"shasum": "29104598a197d6cbe4733eeecbe968f7b43a9667", "size": 22107, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.1.1.tgz", "integrity": "sha512-WfuG+fl6eh3eZ2qAf6goB7nhiCd7NPXhmyFxigB/TOkQyeLP8w8GsVehvtGNtnNmyboz4TgeK40B1Kbql/8c5A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.1.1_1576601788850_0.008654434026552194"}, "_hasShrinkwrap": false, "publish_time": 1576601789010, "_cnpm_publish_time": 1576601789010, "_cnpmcore_publish_time": "2021-12-13T06:50:50.965Z"}, "7.1.0": {"name": "semver", "version": "7.1.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.2"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "e663d38c2d3f77bfe8c9cae9770c409aa434c713", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.1.0", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.4", "dist": {"shasum": "e5870a302d6929a86a967c346e699e19154e38e0", "size": 21600, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.1.0.tgz", "integrity": "sha512-4P8Vc43MxQL6UKqSiEnf0jZNYx545R9W1HwXP6p65paPp86AUJiafZ8XG81hAbcldKMCUIbeykUTVYG19LB7Cw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.1.0_1576546128722_0.7082040504653042"}, "_hasShrinkwrap": false, "publish_time": 1576546128900, "_cnpm_publish_time": 1576546128900, "_cnpmcore_publish_time": "2021-12-13T06:50:51.406Z"}, "7.0.0": {"name": "semver", "version": "7.0.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.1"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "gitHead": "f56505b1c08856a7e6139f6ee5d4580f5f2feed8", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.0.0", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.4", "dist": {"shasum": "5f3ca35761e47e05b206c6daff2cf814f0316b8e", "size": 21519, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-7.0.0.tgz", "integrity": "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.0.0_1576352214659_0.7765955148506742"}, "_hasShrinkwrap": false, "publish_time": 1576352214748, "_cnpm_publish_time": 1576352214748, "_cnpmcore_publish_time": "2021-12-13T06:50:51.826Z"}, "5.7.1": {"name": "semver", "version": "5.7.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"tap": "^13.0.0-rc.18"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "readmeFilename": "README.md", "gitHead": "c83c18cf84f9ccaea3431c929bb285fd168c01e4", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.7.1", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"shasum": "a954f931aeba508d307bbf069eff0c01c96116f7", "size": 17541, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_5.7.1_1565627294887_0.7867378282056998"}, "_hasShrinkwrap": false, "publish_time": 1565627295053, "_cnpm_publish_time": 1565627295053, "_cnpmcore_publish_time": "2021-12-13T06:50:52.262Z"}, "6.3.0": {"name": "semver", "version": "6.3.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.3.1"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver.js"}, "tap": {"check-coverage": true}, "gitHead": "0eeceecfba490d136eb3ccae3a8dc118a28565a0", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.3.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.2", "dist": {"shasum": "ee0a64c8af5e8ceea67687b133761e1becbd1d3d", "size": 18921, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.3.0_1563909926455_0.245174701596915"}, "_hasShrinkwrap": false, "publish_time": 1563909926568, "_cnpm_publish_time": 1563909926568, "_cnpmcore_publish_time": "2021-12-13T06:50:52.733Z"}, "6.2.0": {"name": "semver", "version": "6.2.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.3.1"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver.js"}, "tap": {"check-coverage": true}, "gitHead": "ce6190e2b681700dcc5d7309fe8eda99941f712d", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.2.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.0-next.0", "dist": {"shasum": "4d813d9590aaf8a9192693d6c85b9344de5901db", "size": 20811, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.2.0.tgz", "integrity": "sha512-jdFC1VdUGT/2Scgbimf7FSx9iJLXoqfglSF+gJeuNWVpiE37OIbc1jywR/GJyFdz3mnkz2/id0L0J/cr0izR5A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.2.0_1562022207205_0.5907925634442714"}, "_hasShrinkwrap": false, "publish_time": 1562022207604, "_cnpm_publish_time": 1562022207604, "_cnpmcore_publish_time": "2021-12-13T06:50:53.271Z"}, "6.1.3": {"name": "semver", "version": "6.1.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.1.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "gitHead": "3dc88f3b3d1563aa92bca3b60d739b214937ca27", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.1.3", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.2", "dist": {"shasum": "ef997a1a024f67dd48a7f155df88bb7b5c6c3fc7", "size": 18170, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.1.3.tgz", "integrity": "sha512-aymF+56WJJMyXQHcd4hlK4N75rwj5RQpfW8ePlQnJsTYOBLlLbcIErR/G1s9SkIvKBqOudR3KAx4wEqP+F1hNQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.1.3_1561960268574_0.9939105883443704"}, "_hasShrinkwrap": false, "publish_time": 1561960268761, "_cnpm_publish_time": 1561960268761, "_cnpmcore_publish_time": "2021-12-13T06:50:53.951Z"}, "6.1.2": {"name": "semver", "version": "6.1.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.1.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "gitHead": "7ba4563de94e473817c7b8606f564359e78fa8ea", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.1.2", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "dist": {"shasum": "079960381376a3db62eb2edc8a3bfb10c7cfe318", "size": 17768, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.1.2.tgz", "integrity": "sha512-z4PqiCpomGtWj8633oeAdXm1Kn1W++3T8epkZYnwiVgIYIJ0QHszhInYSJTYxebByQH7KVCEAn8R9duzZW2PhQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.1.2_1561340883124_0.7463086402417924"}, "_hasShrinkwrap": false, "publish_time": 1561340883240, "_cnpm_publish_time": 1561340883240, "_cnpmcore_publish_time": "2021-12-13T06:50:54.516Z"}, "6.1.1": {"name": "semver", "version": "6.1.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.1.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "gitHead": "0e3bcedfb19e2f7ef64b9eb0a0f1554ed7d94be0", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.1.1", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "53f53da9b30b2103cd4f15eab3a18ecbcb210c9b", "size": 17757, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.1.1.tgz", "integrity": "sha512-rWYq2e5iYW+fFe/oPPtYJxYgjBm8sC4rmoGdUOgBB7VnwKt6HrL793l2voH1UlsyYZpJ4g0wfjnTEO1s1NP2eQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.1.1_1559063708250_0.6141590731812676"}, "_hasShrinkwrap": false, "publish_time": 1559063708376, "_cnpm_publish_time": 1559063708376, "_cnpmcore_publish_time": "2021-12-13T06:50:55.086Z"}, "6.1.0": {"name": "semver", "version": "6.1.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.1.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "gitHead": "0aea9ca2d3c4bc879f5a0f582d16026ee51b4495", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.1.0", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "e95dc415d45ecf03f2f9f83b264a6b11f49c0cca", "size": 17592, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.1.0.tgz", "integrity": "sha512-kCqEOOHoBcFs/2Ccuk4Xarm/KiWRSLEX9CAZF8xkJ6ZPlIoTZ8V5f7J16vYLJqDbR7KrxTJpR2lqjIEm2Qx9cQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.1.0_1558559568989_0.41700709355019305"}, "_hasShrinkwrap": false, "publish_time": 1558559569111, "_cnpm_publish_time": 1558559569111, "_cnpmcore_publish_time": "2021-12-13T06:50:55.687Z"}, "6.0.0": {"name": "semver", "version": "6.0.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"tap": "^13.0.0-rc.18"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "gitHead": "5fb517b2906a0763518e1941a3f4a163956a81d3", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.0.0", "_nodeVersion": "11.11.0", "_npmVersion": "6.9.0", "dist": {"shasum": "05e359ee571e5ad7ed641a6eec1e547ba52dea65", "size": 17449, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-6.0.0.tgz", "integrity": "sha512-0UewU+9rFapKFnlbirLi3byoOuhrSsli/z/ihNnvM24vgF+8sNBiI1LZPBSH9wJKUwaUbw+s3hToDLCXkrghrQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.0.0_1553643005349_0.774072845857031"}, "_hasShrinkwrap": false, "publish_time": 1553643005580, "_cnpm_publish_time": 1553643005580, "_cnpmcore_publish_time": "2021-12-13T06:50:56.291Z"}, "5.7.0": {"name": "semver", "version": "5.7.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"tap": "^13.0.0-rc.18"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "gitHead": "8055dda0aee91372e3bfc47754a62f40e8a63b98", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.7.0", "_nodeVersion": "11.11.0", "_npmVersion": "6.9.0", "dist": {"shasum": "790a7cf6fea5459bac96110b29b60412dc8ff96b", "size": 17209, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.7.0.tgz", "integrity": "sha512-Ya52jSX2u7QKghxeoFGpLwCtGlt7j0oY9DYb5apt9nPlJ42ID+ulTXESnt/qAQcoSERyZ5sl3LDIOw0nAn/5DA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_5.7.0_1553642746999_0.7735603320650997"}, "_hasShrinkwrap": false, "publish_time": 1553642747130, "_cnpm_publish_time": 1553642747130, "_cnpmcore_publish_time": "2021-12-13T06:50:56.845Z"}, "5.6.0": {"name": "semver", "version": "5.6.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js --cov -J"}, "devDependencies": {"tap": "^12.0.1"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "46727afbe21b8d14641a0d1c4c7ee58bd053f922", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.6.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "7e74256fbaa49c75aa7c7a205cc22799cac80004", "size": 16548, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.6.0.tgz", "integrity": "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_5.6.0_1539215545199_0.6768223257800898"}, "_hasShrinkwrap": false, "publish_time": 1539215545375, "_cnpm_publish_time": 1539215545375, "_cnpmcore_publish_time": "2021-12-13T06:50:57.405Z"}, "5.5.1": {"name": "semver", "version": "5.5.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js --cov -J"}, "devDependencies": {"tap": "^12.0.1"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "89bfa00a24b93cb7d10b6a89486e1f927837952f", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.5.1", "_npmVersion": "6.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "7dfdd8814bdb7cabc7be0fb1d734cfb66c940477", "size": 16168, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.5.1.tgz", "integrity": "sha512-PqpAxfrEhlSUWge8dwIp4tZnQ25DIOthpiaHNIthsjEFQD6EvqUKUDM7L8O2rShkFccYo1VjJR0coWfNkCubRw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_5.5.1_1534538146584_0.02094243650440908"}, "_hasShrinkwrap": false, "publish_time": 1534538146676, "_cnpm_publish_time": 1534538146676, "_cnpmcore_publish_time": "2021-12-13T06:50:58.004Z"}, "5.5.0": {"name": "semver", "version": "5.5.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js --cov -J"}, "devDependencies": {"tap": "^10.7.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "44cbc8482ac4f0f8d2de0abb7f8808056d2d55f9", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab", "size": 16212, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.5.0.tgz", "integrity": "sha512-4SJ3dm0WAwWy/NVeioZh5AntkdJoWKxHxcmyP622fOkgHa4z3R0TdBJICINyaSDE6uNwVc8gZr+ZinwZAH4xIA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver-5.5.0.tgz_1516130879707_0.30317740654572845"}, "directories": {}, "publish_time": 1516130879818, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516130879818, "_cnpmcore_publish_time": "2021-12-13T06:50:58.542Z"}, "5.4.1": {"name": "semver", "version": "5.4.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js --cov -J"}, "devDependencies": {"tap": "^10.7.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "0877c942a6af00edcda5c16fdd934684e1b20a1c", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.4.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e059c09d8571f0540823733433505d3a2f00b18e", "size": 15723, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.4.1.tgz", "integrity": "sha512-WfG/X9+oATh81XtllIo/I8gOiY9EXRdv1cQdyykeXK17YcUW3EXUAi2To4pcH6nZtJPr7ZOpM5OMyWJZm+8Rsg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver-5.4.1.tgz_1500922107643_0.5125251261051744"}, "directories": {}, "publish_time": 1500922107785, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500922107785, "_cnpmcore_publish_time": "2021-12-13T06:50:59.260Z"}, "5.4.0": {"name": "semver", "version": "5.4.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js --cov -J"}, "devDependencies": {"tap": "^10.7.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "e1c49c8dea7e75f0f341b98260098731e7f12519", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.4.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "4b753f9bc8dc4c0b30cf460124ed17ae65444ae8", "size": 15715, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.4.0.tgz", "integrity": "sha512-TBZ1MavfXEY92Ohe3vwQbXSSIUy7HRHuSayvV84i9/+BHzHxYZxtnam2FEdIMvkri17UmUD2iz5KzWI4MQpEyQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver-5.4.0.tgz_1500914373430_0.7377612616401166"}, "directories": {}, "publish_time": 1500914373594, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500914373594, "_cnpmcore_publish_time": "2021-12-13T06:50:59.912Z"}, "5.3.0": {"name": "semver", "version": "5.3.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^2.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "d21444a0658224b152ce54965d02dbe0856afb84", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.3.0", "_shasum": "9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f", "size": 15094, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.3.0.tgz", "integrity": "sha512-mfmm3/H9+67MCVix1h+IXTpDwL6710LyHuk7+cWC9T1mE0qz4iHhh6r4hU2wrIT9iTsAAC2XQRvfblL028cpLw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/semver-5.3.0.tgz_1468515166602_0.9155273644719273"}, "directories": {}, "publish_time": 1468515167104, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468515167104, "_cnpmcore_publish_time": "2021-12-13T06:51:00.527Z"}, "5.2.0": {"name": "semver", "version": "5.2.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^2.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "f7fef36765c53ebe237bf415c3ea002f24aa5621", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.2.0", "_shasum": "281995b80c1448209415ddbc4cf50c269cef55c5", "_from": ".", "_npmVersion": "3.10.2", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "281995b80c1448209415ddbc4cf50c269cef55c5", "size": 15038, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.2.0.tgz", "integrity": "sha512-+vNx/U181x07/dDbDtughakdpvn2eINSlw2EL+lPQZnQUmDTesiWjzH/dp95mxld9qP9D1sD+x71YLO4WURAeg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/semver-5.2.0.tgz_1467136841238_0.2250258030835539"}, "directories": {}, "publish_time": 1467136841679, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467136841679, "_cnpmcore_publish_time": "2021-12-13T06:51:01.188Z"}, "5.1.1": {"name": "semver", "version": "5.1.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^2.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "ad1d3658a1b5749c38b9d21280c629f4fa2fee54", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.1.1", "_shasum": "a3292a373e6f3e0798da0b20641b9a9c5bc47e19", "_from": ".", "_npmVersion": "3.10.2", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a3292a373e6f3e0798da0b20641b9a9c5bc47e19", "size": 20751, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.1.1.tgz", "integrity": "sha512-bNx9Zdbi1OUN62PbKeG4IgGG8YILX/nkHJ0NQEBwg5FmX8qTJfqhYd3reqkm0DxHCC8nkazb6UjNiBSHCBWVtA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/semver-5.1.1.tgz_1466704850953_0.017174890032038093"}, "directories": {}, "publish_time": 1466704851598, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466704851598, "_cnpmcore_publish_time": "2021-12-13T06:51:02.103Z"}, "5.1.0": {"name": "semver", "version": "5.1.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^2.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "8e33a30e62e40e4983d1c5f55e794331b861aadc", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.1.0", "_shasum": "85f2cf8550465c4df000cf7d86f6b054106ab9e5", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "85f2cf8550465c4df000cf7d86f6b054106ab9e5", "size": 20618, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.1.0.tgz", "integrity": "sha512-sfKXKhcz5XVyfUZa2V4RbjK0xjOJCMLNF9H4p4v0UCo9wNHM/lH9RDuyDbGEtxWLMDlPBc8xI7AbbVLKXty+rQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1447888682918, "_hasShrinkwrap": false, "_cnpm_publish_time": 1447888682918, "_cnpmcore_publish_time": "2021-12-13T06:51:02.856Z"}, "5.0.3": {"name": "semver", "version": "5.0.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^1.3.4"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "5f89ecbe78145ad0b501cf6279f602a23c89738d", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.0.3", "_shasum": "77466de589cd5d3c95f138aa78bc569a3cb5d27a", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "77466de589cd5d3c95f138aa78bc569a3cb5d27a", "size": 20185, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.0.3.tgz", "integrity": "sha512-5OkOBiw69xqmxOFIXwXsiY1HlE+om8nNptg1ZIf95fzcnfgOv2fLm7pmmGbRJsjJIqPpW5Kwy4wpDBTz5wQlUw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1442003251563, "_hasShrinkwrap": false, "_cnpm_publish_time": 1442003251563, "_cnpmcore_publish_time": "2021-12-13T06:51:03.584Z"}, "5.0.2": {"name": "semver", "version": "5.0.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "df967e1ad6251d0433b0398a93756142a423a528", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.0.2", "_shasum": "19041bd286619344116c60bcc011a3a4cb4a14ef", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "19041bd286619344116c60bcc011a3a4cb4a14ef", "size": 20089, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.0.2.tgz", "integrity": "sha512-VAVUsO7VKPfjjzkxcn02KJA0FxiaAx3xUFcF88QXXdRKuHpBsdGJnY51o5cfML2QUZPcghgnKQBamjD1N9SMUQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441991380057, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441991380057, "_cnpmcore_publish_time": "2021-12-13T06:51:04.359Z"}, "5.0.1": {"name": "semver", "version": "5.0.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "3408896f115cdb241684fb81f85abb0d2ecc27e9", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.0.1", "_shasum": "9fb3f4004f900d83c47968fe42f7583e05832cc9", "_from": ".", "_npmVersion": "3.1.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "9fb3f4004f900d83c47968fe42f7583e05832cc9", "size": 20083, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.0.1.tgz", "integrity": "sha512-Ne6/HdGZvvpXBdjW3o8J0pvxC2jnmVNBK7MKkMgsOBfrsIdTXfA5x+H9DUbQ2xzyvnLv0A0v9x8R4B40xNZIRQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1436817747516, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436817747516, "_cnpmcore_publish_time": "2021-12-13T06:51:05.207Z"}, "5.0.0": {"name": "semver", "version": "5.0.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "01ac00c45efa423894b2da5b043ce6190c96ae96", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.0.0", "_shasum": "f96fd0f81ea71ec131aceac26725cef2a255dc01", "_from": ".", "_npmVersion": "3.1.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f96fd0f81ea71ec131aceac26725cef2a255dc01", "size": 43239, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-5.0.0.tgz", "integrity": "sha512-z/rlLJBd9rXGzbWqnPw1zBkcn2hqGPQ+I95tJIBbyqMKnX9E+J4DqPvIJxxQHldsIxEG/Z60TVRwwJcjl11IeQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1436635780652, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436635780652, "_cnpmcore_publish_time": "2021-12-13T06:51:06.005Z"}, "4.3.6": {"name": "semver", "version": "4.3.6", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}, "license": "ISC", "repository": {"type": "git", "url": "git://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "63c48296ca5da3ba6a88c743bb8c92effc789811", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@4.3.6", "_shasum": "300bc6e0e86374f7ba61068b5b1ecd57fc6532da", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "300bc6e0e86374f7ba61068b5b1ecd57fc6532da", "size": 45480, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.6.tgz", "integrity": "sha512-IrpJ+yoG4EOH8DFWuVg+8H1kW1Oaof0Wxe7cPcXW3x9BjkN/eVo54F15LyqemnDIUYskQWr9qvl/RihmSy6+xQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1433132182945, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433132182945, "_cnpmcore_publish_time": "2021-12-13T06:51:06.916Z"}, "4.3.5": {"name": "semver", "version": "4.3.5", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}, "license": "ISC", "repository": {"type": "git", "url": "git://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "75bb9e0692b562f296b0a353a7934e0510727566", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@4.3.5", "_shasum": "c20865a8bb8e1b6ac958a390c8e835538fa0c707", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c20865a8bb8e1b6ac958a390c8e835538fa0c707", "size": 273715, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.5.tgz", "integrity": "sha512-xnlAURpjiUoauD8XiuSz2hH1At8J/01YZkDCO3hDlXPugZoKU4XNhdng3nfpTHNafuJ4D5DvJeEONcANBYPx3Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432938340918, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432938340918, "_cnpmcore_publish_time": "2021-12-13T06:51:07.854Z"}, "4.3.4": {"name": "semver", "version": "4.3.4", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "ISC", "repository": {"type": "git", "url": "git://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "d7d791dc9d321cb5f3211e39ce8857f6476922f9", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@4.3.4", "_shasum": "bf43a1aae304de040e12a13f84200ca7aeab7589", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "2.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "bf43a1aae304de040e12a13f84200ca7aeab7589", "size": 43705, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.4.tgz", "integrity": "sha512-Rx2ytq2YiTsK/aNQH01L6Geg1RKOobu8F/5zs80kFeA0qFhSiQlavwve2gAQqEHKqiRV9GV/USNirxQqMtrTNg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1430799965035, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430799965035, "_cnpmcore_publish_time": "2021-12-13T06:51:08.715Z"}, "4.3.3": {"name": "semver", "version": "4.3.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "bb32a43bdfa7223e4c450d181e5a2184b00f24d4", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver", "_id": "semver@4.3.3", "_shasum": "15466b61220bc371cd8f0e666a9f785329ea8228", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "15466b61220bc371cd8f0e666a9f785329ea8228", "size": 44023, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.3.tgz", "integrity": "sha512-jXRt0450HFhUqjxdOchxCfajQsS9NjXEe+NxorJNyHXef2t9lmbuq2gDPFs5107LEYrU2RZDg9zGzcMjeng0Fw=="}, "directories": {}, "publish_time": 1427475384729, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427475384729, "_cnpmcore_publish_time": "2021-12-13T06:51:09.646Z"}, "4.3.2": {"name": "semver", "version": "4.3.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "22e583cc12d21b80bd7175b64ebe55890aa34e46", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver", "_id": "semver@4.3.2", "_shasum": "c7a07158a80bedd052355b770d82d6640f803be7", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "c7a07158a80bedd052355b770d82d6640f803be7", "size": 43777, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.2.tgz", "integrity": "sha512-VyFUffiBx8hABJ9HYSTXLRwyZtdDHMzMtFmID1aiNAD2BZppBmJm0Hqw3p2jkgxP9BNt1pQ9RnC49P0EcXf6cA=="}, "directories": {}, "publish_time": 1427419568892, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427419568892, "_cnpmcore_publish_time": "2021-12-13T06:51:10.592Z"}, "4.3.1": {"name": "semver", "version": "4.3.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/npm/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "fa9be2b231666f7485e832f84d2fe99afc033e22", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver", "_id": "semver@4.3.1", "_shasum": "beb0129575b95f76110b29af08d370fd9eeb34bf", "_from": ".", "_npmVersion": "2.6.0", "_nodeVersion": "1.1.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "beb0129575b95f76110b29af08d370fd9eeb34bf", "size": 38741, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.1.tgz", "integrity": "sha512-XCegw2pJ735ut1ZyhSQ2Mm8W0Z/qkci+JjD2pIXNNFZXUMRlO93qNhSw8LBorDyEYbtiVa5FyodGbZ2k0w851g=="}, "directories": {}, "publish_time": 1424807390416, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424807390416, "_cnpmcore_publish_time": "2021-12-13T06:51:11.425Z"}, "4.3.0": {"name": "semver", "version": "4.3.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "12c0304de19c3d01ae2524b70592e9c49a76ff9d", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.3.0", "_shasum": "3757ceed2b91afefe0ba2c3b6bda49c688b0257a", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "3757ceed2b91afefe0ba2c3b6bda49c688b0257a", "size": 38740, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.3.0.tgz", "integrity": "sha512-RH9n+cmtBU68yyLhsqAjrMVrF7uhmbe5+vWMVtVklTcO5k/dN5lne6tHdz2l8SjcoZKvZfb1fOyG48hp01fcDQ=="}, "directories": {}, "publish_time": 1423771718236, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423771718236, "_cnpmcore_publish_time": "2021-12-13T06:51:12.220Z"}, "4.2.2": {"name": "semver", "version": "4.2.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "d2806e62a28290c0bb4b552b741029baf9829226", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.2.2", "_shasum": "a6aa6ac6a63c0dc7aff7ea48d5455ae2b93a3062", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "a6aa6ac6a63c0dc7aff7ea48d5455ae2b93a3062", "size": 37025, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.2.2.tgz", "integrity": "sha512-xg+0bAChxDcgXAZ9yJJ/I4xQm1CZaFr0hQ7E2HdCMnQbT2hUBidtCzCiPB0oWmc53UNDIBgNwNBdiDZG2Sx67Q=="}, "directories": {}, "publish_time": 1423550804370, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423550804370, "_cnpmcore_publish_time": "2021-12-13T06:51:13.035Z"}, "4.2.1": {"name": "semver", "version": "4.2.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "bdfb19555ee0f2f46ca6694931ca476d8b8c35af", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.2.1", "_shasum": "70828f545f40f49ffab91fef09c3cd3257937142", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "70828f545f40f49ffab91fef09c3cd3257937142", "size": 37023, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.2.1.tgz", "integrity": "sha512-3+aGL3H9fBl8UJKDqxMnrsGI3SYD44/KDyCHXo5/sJKDsH4SsXmTo5K9B6LorKd7cxRTVVoDTAhvXtq2+/zGgw=="}, "directories": {}, "publish_time": 1423550666265, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423550666265, "_cnpmcore_publish_time": "2021-12-13T06:51:13.926Z"}, "4.2.0": {"name": "semver", "version": "4.2.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "f353d3337dd9bef990b6873e281342260b4e63ae", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.2.0", "_shasum": "a571fd4adbe974fe32bd9cb4c5e249606f498423", "_from": ".", "_npmVersion": "2.1.14", "_nodeVersion": "0.10.33", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "a571fd4adbe974fe32bd9cb4c5e249606f498423", "size": 37061, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.2.0.tgz", "integrity": "sha512-w3jtCMLFhroT8G0VFOyFKX/Xla6aYgvIH1vou5vpQlQH9i6X0UIA60lwhwtBls/dtlZKZjCBAIMQ8Y/ROcTz1w=="}, "directories": {}, "publish_time": 1419327766263, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419327766263, "_cnpmcore_publish_time": "2021-12-13T06:51:14.975Z"}, "4.1.1": {"name": "semver", "version": "4.1.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "f43cb35c96b05e33442e75b68c689cc026bf5ced", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.1.1", "_shasum": "8d63e2e90df847e626d48ae068cd65786b0ed3d3", "_from": ".", "_npmVersion": "2.1.14", "_nodeVersion": "0.10.34", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "8d63e2e90df847e626d48ae068cd65786b0ed3d3", "size": 29416, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.1.1.tgz", "integrity": "sha512-fGVhAtp6zIx5Q2AHR3xu/8tUyPx0osOuL7kvJvX/S5b4rE85KazmMZnt+Mfhq8p9AOlz0POVqiTg1URFT46xkQ=="}, "directories": {}, "publish_time": 1418993834981, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418993834981, "_cnpmcore_publish_time": "2021-12-13T06:51:15.869Z"}, "4.1.0": {"name": "semver", "version": "4.1.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "f8db569b9fd00788d14064aaf81854ed81e1337a", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.1.0", "_shasum": "bc80a9ff68532814362cc3cfda3c7b75ed9c321c", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "bc80a9ff68532814362cc3cfda3c7b75ed9c321c", "size": 35620, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.1.0.tgz", "integrity": "sha512-lLkkQcdd/nO1WKpCh2rljlJ17truE0Bs2x+Nor41/yKwnYeHtyOQJqA97NP4zkez3+gJ1Uh5rUqiEOYgOBMXbw=="}, "directories": {}, "publish_time": 1413420935923, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413420935923, "_cnpmcore_publish_time": "2021-12-13T06:51:16.711Z"}, "4.0.3": {"name": "semver", "version": "4.0.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "58c971461ade78bca6c1970109de4dc66cc2c13b", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.0.3", "_shasum": "f79c9ba670efccc029d98a5017def64b0ce1644e", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "f79c9ba670efccc029d98a5017def64b0ce1644e", "size": 35002, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.0.3.tgz", "integrity": "sha512-89AI+k269YvAjOOeeK23fk2JQX3Uoh2efNO7hye1Rn1E+/K3R4sP0IK6v0yylDRXGIzu2qaKAPl4ltOzYhwUkA=="}, "directories": {}, "publish_time": 1412122717208, "_hasShrinkwrap": false, "_cnpm_publish_time": 1412122717208, "_cnpmcore_publish_time": "2021-12-13T06:51:17.749Z"}, "4.0.2": {"name": "semver", "version": "4.0.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "078061b03e7e10202f9d03fe447b528202cd7a06", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.0.2", "_shasum": "13f7293ca7d42886123963ca0b947a03f83f60c3", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "13f7293ca7d42886123963ca0b947a03f83f60c3", "size": 35542, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.0.2.tgz", "integrity": "sha512-GqDeHKVae+gifcdysWn/oQTzhNdvkj/qYKON49ChzAzYBu3A6rKMJs5AS4AZW5xHbdWKWJ2oUj1yiaL2F+jg/Q=="}, "directories": {}, "publish_time": 1412121326916, "_hasShrinkwrap": false, "_cnpm_publish_time": 1412121326916, "_cnpmcore_publish_time": "2021-12-13T06:51:18.726Z"}, "4.0.0": {"name": "semver", "version": "4.0.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "f71a46b52f5d413aff1cb3afa7d2f940b23ab1a0", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@4.0.0", "_shasum": "7be868416a5e669923a8e3af8bafa5faf62a151a", "_from": ".", "_npmVersion": "2.0.0-beta.3", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "7be868416a5e669923a8e3af8bafa5faf62a151a", "size": 34753, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-4.0.0.tgz", "integrity": "sha512-P1vQUCJKfUsGNh9i8HW9Y/EPxYS4ccD8Lez1yp8/yOsO/NlPodHINwZsF68jLK0NU+xfWVFRe95Dfhj3H2ORzg=="}, "directories": {}, "publish_time": 1410474987208, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410474987208, "_cnpmcore_publish_time": "2021-12-13T06:51:19.666Z"}, "3.0.1": {"name": "semver", "version": "3.0.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "4b24aeb54dd23560f53b0df01e64e5f229e6172f", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@3.0.1", "_shasum": "720ac012515a252f91fb0dd2e99a56a70d6cf078", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "720ac012515a252f91fb0dd2e99a56a70d6cf078", "size": 33318, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-3.0.1.tgz", "integrity": "sha512-MrF9mHWFtD/0eV4t3IheoXnGWTdw17axm5xqzOWyPsOMVnTtRAZT6uwPwslQXH5SsiaBLiMuu8NX8DtXWZfDwg=="}, "directories": {}, "publish_time": 1406222676175, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406222676175, "_cnpmcore_publish_time": "2021-12-13T06:51:20.524Z"}, "3.0.0": {"name": "semver", "version": "3.0.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "e077f4e33e21b280a5cf6688d850dabf5f6e48e2", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@3.0.0", "_shasum": "994f634b9535a36b07fde690faa811a9ed35b4f3", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "994f634b9535a36b07fde690faa811a9ed35b4f3", "size": 33062, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-3.0.0.tgz", "integrity": "sha512-UXaRocpOaX/BQohXM5bIL4Xx0W4IIiuzesMItEx5oe7Xt5SZ7TXmjIwO1+0rRGhMGJ8o4HxjFD0+eXg/ox53sg=="}, "directories": {}, "publish_time": 1406150069806, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406150069806, "_cnpmcore_publish_time": "2021-12-13T06:51:21.758Z"}, "2.3.2": {"name": "semver", "version": "2.3.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "87bcf749b18fd0ce32b1808f60a98eacecd84689", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@2.3.2", "_shasum": "b9848f25d6cf36333073ec9ef8856d42f1233e52", "_from": ".", "_npmVersion": "1.5.0-alpha-4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "b9848f25d6cf36333073ec9ef8856d42f1233e52", "size": 33177, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-2.3.2.tgz", "integrity": "sha512-abLdIKCosKfpnmhS52NCTjO4RiLspDfsn37prjzGrp9im5DPJOgh82Os92vtwGh6XdQryKI/7SREZnV+aqiXrA=="}, "directories": {}, "publish_time": 1406057090090, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406057090090, "_cnpmcore_publish_time": "2021-12-13T06:51:22.914Z"}, "2.3.1": {"name": "semver", "version": "2.3.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "gitHead": "e5b259a784b79895853aff1c6d5e23b07bdd4664", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@2.3.1", "_shasum": "6f65ee7d1aed753cdf9dda70e5631a3fb42a5bee", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "6f65ee7d1aed753cdf9dda70e5631a3fb42a5bee", "size": 33118, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-2.3.1.tgz", "integrity": "sha512-o+rwSUCokxzPIMBWGG96NGBo2O3QuUuWagXMxTSRME1hovdWXpM4AVUcnqXt2I16bSBJZTuiUwZrSDb8VIz8Qw=="}, "directories": {}, "publish_time": 1403131696706, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403131696706, "_cnpmcore_publish_time": "2021-12-13T06:51:23.912Z"}, "2.3.0": {"name": "semver", "version": "2.3.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@2.3.0", "_shasum": "d31b2903ebe2a1806c05b8e763916a7183108a15", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "d31b2903ebe2a1806c05b8e763916a7183108a15", "size": 32933, "noattachment": false, "tarball": "https://registry.npmmirror.com/semver/-/semver-2.3.0.tgz", "integrity": "sha512-QD4DH+D12a+3WeHAT7TOpe24YjL11i+vkW4PdY52KkvfZkams42ncME5afs/UgAzYzqDXWS668ulm+KrrTo9+g=="}, "directories": {}, "publish_time": 1399425302092, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399425302092, "_cnpmcore_publish_time": "2021-12-13T06:51:25.103Z"}, "2.2.1": {"name": "semver", "version": "2.2.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "homepage": "https://github.com/isaacs/node-semver", "_id": "semver@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.2.1.tgz", "shasum": "7941182b3ffcc580bff1c17942acdf7951c0d213", "size": 32060, "noattachment": false, "integrity": "sha512-zM5SE887Z8Ixx9cGaFnu9Wd8xr0RFwixASZcvUh2QGnf/1uxYmyetDzhzkEdDKipmZPq/JTB0gLo1Sg59LXkQQ=="}, "_from": ".", "_npmVersion": "1.3.12", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1382984290005, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382984290005, "_cnpmcore_publish_time": "2021-12-13T06:51:26.231Z"}, "2.2.0": {"name": "semver", "version": "2.2.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.2.0.tgz", "shasum": "290ec979d731b3dc6c08a15dbcffdeae420f4473", "size": 32077, "noattachment": false, "integrity": "sha512-P487vwoC/P9XUUaAQBciHmNNXF0sHxXJL/zlV0HxrAax0nMqdBQAl4QHOr1aXCIUo0kAtDYDJ9qDbqjuoSLS3w=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1382731364049, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382731364049, "_cnpmcore_publish_time": "2021-12-13T06:51:27.443Z"}, "2.1.0": {"name": "semver", "version": "2.1.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.1.0.tgz", "shasum": "356294a90690b698774d62cf35d7c91f983e728a", "size": 29078, "noattachment": false, "integrity": "sha512-c72GsIRwIoxL+mBGH33w2dwcX2tqO0I/Snh8jjYpOpktGoobMnu0dYT+2wl6Uvo8zLcvWsB4gP6HHLRsrbZcng=="}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375401151371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375401151371, "_cnpmcore_publish_time": "2021-12-13T06:51:28.550Z"}, "2.0.11": {"name": "semver", "version": "2.0.11", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.11", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.11.tgz", "shasum": "f51f07d03fa5af79beb537fc067a7e141786cced", "size": 27860, "noattachment": false, "integrity": "sha512-LllH2+bpxApwHikoDHLcxJRZO1iYdtECXcvtReaV4UEf3EiR2HhoyiS24Xq+S38lDGvI0t4ZifK8h6pHVB3oXA=="}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1374636199907, "_hasShrinkwrap": false, "_cnpm_publish_time": 1374636199907, "_cnpmcore_publish_time": "2021-12-13T06:51:29.638Z"}, "2.0.10": {"name": "semver", "version": "2.0.10", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.10", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.10.tgz", "shasum": "e3199263b1e9f1913dbc91efb4af559e8e4d3d31", "size": 21810, "noattachment": false, "integrity": "sha512-b7SPVdVdJ4vAP/Vaz3A0IFujeGT3zG9ivfuJqeSCSE3o4j+UfoIbOpuTHlzBin+4hN9jGhyBCtpj8ILyXXPSDw=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373409556895, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373409556895, "_cnpmcore_publish_time": "2021-12-13T06:51:30.905Z"}, "2.0.9": {"name": "semver", "version": "2.0.9", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.9", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.9.tgz", "shasum": "3c76b0f216bd62a95f5f03e9ec298da548632403", "size": 21235, "noattachment": false, "integrity": "sha512-uQ8oVqP1MRM8HpwN9KrY1YqpnMPMNEGUWLwC8aXuZBDoXs7nvEfBUYDPVzaaJafoDXv0aNYjdbG057N53i9zUA=="}, "_from": ".", "_npmVersion": "1.3.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373082340578, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373082340578, "_cnpmcore_publish_time": "2021-12-13T06:51:32.127Z"}, "2.0.8": {"name": "semver", "version": "2.0.8", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.8", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.8.tgz", "shasum": "f5c28ba4a6d56bd1d9dbe34aed288d69366a73c6", "size": 27013, "noattachment": false, "integrity": "sha512-tdvBVFDfQJ9GoManbsTAqDs69WEfz/HYFqHCWDkWfeX2gxLS2lFIDBsAjiUWoyc1z8wYdItbgvDsYF779ise5Q=="}, "_from": ".", "_npmVersion": "1.3.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372111957887, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372111957887, "_cnpmcore_publish_time": "2021-12-13T06:51:33.373Z"}, "2.0.7": {"name": "semver", "version": "2.0.7", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.7", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.7.tgz", "shasum": "7815c1a7ef647604646ecdabc95b90dfeb39174f", "size": 26912, "noattachment": false, "integrity": "sha512-iqPKEvInsF/uu8BmoCWGzhdY5QctPNndIIqYyU3VSgXh5cwU+oSR7zzj/CPhLYsaA+6hRqjNQN2RSLtg/i+6EQ=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371754623281, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371754623281, "_cnpmcore_publish_time": "2021-12-13T06:51:34.445Z"}, "2.0.6": {"name": "semver", "version": "2.0.6", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.6", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.6.tgz", "shasum": "3febe96c89813fc299d8e67cbcd685101e07335a", "size": 26836, "noattachment": false, "integrity": "sha512-IS1QUisbrNNjOsF0RKuhQ+DUbOYjs+tFxDI3corPjGkWIXTJrbIB4zSVRnVyIiVZc2jpN1YHi8D+zCtfm8teqQ=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371753680343, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371753680343, "_cnpmcore_publish_time": "2021-12-13T06:51:35.623Z"}, "2.0.5": {"name": "semver", "version": "2.0.5", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.5", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.5.tgz", "shasum": "601aebc1dbeedac222bb7e7b8882454f0c2a24c7", "size": 26324, "noattachment": false, "integrity": "sha512-ryGDg2K1/aWIKCjs9dM8aojyvYYYGt4haET1Q0jKuqkLCBj7eDeLfhChH4XzJRi7cSTiMi49WGibqRsELPPeIw=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371742924599, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371742924599, "_cnpmcore_publish_time": "2021-12-13T06:51:36.927Z"}, "2.0.4": {"name": "semver", "version": "2.0.4", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.4", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.4.tgz", "shasum": "1bb8e25f9a445b55f2d2ea5bc06742790b6a5ba7", "size": 60479, "noattachment": false, "integrity": "sha512-Bk18XYmfDuxL2HsfqaeBPaD7RlgY4cvZEAJFEaFw5iIxN7fXkg20h3XFrgg1xpDgQc3ieUTm0MspjK+Uf52MUw=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371742400522, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371742400522, "_cnpmcore_publish_time": "2021-12-13T06:51:38.030Z"}, "2.0.3": {"name": "semver", "version": "2.0.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.3", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.3.tgz", "shasum": "1778ae3b7d5f5f457a48b164320bf6e29c8e8fe1", "size": 50901, "noattachment": false, "integrity": "sha512-wAekqPFJmElFS/TKrb616ViPmLd4E0BvTp5nKKqeqV7lzPVVTIgadTNhUtbhWoe3Nps8q3OcziJ1CLIgoGanwg=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371741333034, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371741333034, "_cnpmcore_publish_time": "2021-12-13T06:51:39.337Z"}, "2.0.2": {"name": "semver", "version": "2.0.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.2", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.2.tgz", "shasum": "6697b743dafc38a9ae24d4f0183eb53f460b662a", "size": 43266, "noattachment": false, "integrity": "sha512-LaEXhHQiAkH+cJc34ZfpejQ+/edqRjAgD2FFe3g+K6X0sDlhqnP2tZU0WZ8ZXJJcjzwRJsSVg9YHoqyRcvmrmw=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371740758554, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371740758554, "_cnpmcore_publish_time": "2021-12-13T06:51:40.583Z"}, "2.0.1": {"name": "semver", "version": "2.0.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.1", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.1.tgz", "shasum": "92741b2bd09d9c53695cf116cd6de32cae925976", "size": 43254, "noattachment": false, "integrity": "sha512-PtxXFO3N7+Hq3ZgjZ3kfX/d/rK7p+KzOXbRu1kHxiW7hmFKwOBkMAPy2v6iic9Dg1YcHrsoYFfwJfUgcm+hIcA=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371703371354, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371703371354, "_cnpmcore_publish_time": "2021-12-13T06:51:41.762Z"}, "2.0.0-beta": {"name": "semver", "version": "2.0.0-beta", "description": "The semantic version parser used by npm.", "main": "semver.js", "browser": "semver.browser.js", "min": "semver.min.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.0-beta", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.0-beta.tgz", "shasum": "5c3585c4eaa97879cc07de06aa6e75a44f5b249f", "size": 43172, "noattachment": false, "integrity": "sha512-/NYzYBm/4twt8UsOjfSUX0Lx1G5zm1LjpwAhJd2ChXGIZcQ2UqjqApBWsyR5QhVWc6HWFnRoN2jUT2nmVn8IUQ=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371513686679, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371513686679, "_cnpmcore_publish_time": "2021-12-13T06:51:42.896Z"}, "2.0.0-alpha": {"name": "semver", "version": "2.0.0-alpha", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": "BSD", "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "_id": "semver@2.0.0-alpha", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-2.0.0-alpha.tgz", "shasum": "979de555a1bae1c781cc6b2907b576b59067706e", "size": 10279, "noattachment": false, "integrity": "sha512-sgLPsnfuNgIRa+1GafPpm97jb6gvrMjl2XiQ5MVXPb0BkWN7H4J4+SNTrpIz4ksARMJ7wAGkIeICV77mcP1VYA=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371266973540, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371266973540, "_cnpmcore_publish_time": "2021-12-13T06:51:44.051Z"}, "1.1.4": {"name": "semver", "version": "1.1.4", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "_id": "semver@1.1.4", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.1.4.tgz", "shasum": "2e5a4e72bab03472cc97f72753b4508912ef5540", "size": 8275, "noattachment": false, "integrity": "sha512-9causpLEkYDrfTz7cprleLz9dnlb0oKsKRHl33K92wJmXLhVc2dGlrQGJT/sjtLOAyuoQZl+ClI77+lnvzPSKg=="}, "_from": ".", "_npmVersion": "1.2.12", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1362161696811, "_hasShrinkwrap": false, "_cnpm_publish_time": 1362161696811, "_cnpmcore_publish_time": "2021-12-13T06:51:45.528Z"}, "1.1.3": {"name": "semver", "version": "1.1.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "_id": "semver@1.1.3", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.1.3.tgz", "shasum": "a0f06f2fb23b64ef9c0ff714fa079082e0532633", "size": 8230, "noattachment": false, "integrity": "sha512-kqR5VYrim9OOJyEXjQAWfKsE974sr190VrFmkWA841O1dDENc+8OUus69XQaWk6xjaiyHu0L4HOqixBJvir8eQ=="}, "_from": ".", "_npmVersion": "1.2.8", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1360165359566, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360165359566, "_cnpmcore_publish_time": "2021-12-13T06:51:46.909Z"}, "1.1.2": {"name": "semver", "version": "1.1.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "_id": "semver@1.1.2", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.1.2.tgz", "shasum": "f6c851dcb2776b0aa7af2294dadaf6ce20de897e", "size": 8038, "noattachment": false, "integrity": "sha512-sP5HF+okbIPRQUJ3kvn99FWkZDapbRspyXfUh+zZT5VBi1GZnmSLa3LeXVphWyiLN3OPGycS8zW5p0Oq1RGfhg=="}, "_npmVersion": "1.1.70", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1357489556424, "_hasShrinkwrap": false, "_cnpm_publish_time": 1357489556424, "_cnpmcore_publish_time": "2021-12-13T06:51:48.225Z"}, "1.1.1": {"name": "semver", "version": "1.1.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "readmeFilename": "README.md", "_id": "semver@1.1.1", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.1.1.tgz", "shasum": "de7faba203f7cbb59ac16da64198df0c6cebca30", "size": 8040, "noattachment": false, "integrity": "sha512-vC25KyajeTKWZJM41EVAexRb3UBIn4rN2JrBHrvxaTdJ4pN8N9mzUruYzYVJ6gqIrAUg6BntTEKblYnIxl/QKA=="}, "_npmVersion": "1.1.66", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1354149981597, "_hasShrinkwrap": false, "_cnpm_publish_time": 1354149981597, "_cnpmcore_publish_time": "2021-12-13T06:51:49.693Z"}, "1.1.0": {"name": "semver", "version": "1.1.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_id": "semver@1.1.0", "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.1.0.tgz", "shasum": "da9b9c837e31550a7c928622bc2381de7dd7a53e", "size": 8038, "noattachment": false, "integrity": "sha512-dWhAIeZiiKLiSijlIE2ebLDc6TmKb1xccwAmObQQS3DozfWiH56YXxi/9gh5lH0xU83pXK+CZVrnovwCShfjWw=="}, "_npmVersion": "1.1.62", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1349197354309, "_hasShrinkwrap": false, "_cnpm_publish_time": 1349197354309, "_cnpmcore_publish_time": "2021-12-13T06:51:50.988Z"}, "1.0.14": {"name": "semver", "version": "1.0.14", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "semver@1.0.14", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.22", "_nodeVersion": "v0.7.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.14.tgz", "shasum": "cac5e2d55a6fbf958cb220ae844045071c78f676", "size": 8029, "noattachment": false, "integrity": "sha512-edb8Hl6pnVrKQauQHTqQkRlpZB5RZ/pEe2ir3C3Ztdst0qIayag31dSLsxexLRe80NiWkCffTF5MB7XrGydhSQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1338079652831, "_hasShrinkwrap": false, "_cnpm_publish_time": 1338079652831, "_cnpmcore_publish_time": "2021-12-13T06:51:52.209Z"}, "1.0.13": {"name": "semver", "version": "1.0.13", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "semver@1.0.13", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-7", "_nodeVersion": "v0.6.7-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.13.tgz", "shasum": "9512ce1392105e72a0b739b27f39e0242913d07e", "size": 7986, "noattachment": false, "integrity": "sha512-qDRTcyNWqdabnjccyHRgJKraxVBICKe6EDZo4gSHNjMJuG9znreLCQOetjpbr1YJxwTiAMR/lYxIBDN68c1brQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1324487234144, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324487234144, "_cnpmcore_publish_time": "2021-12-13T06:51:53.602Z"}, "1.0.12": {"name": "semver", "version": "1.0.12", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "semver@1.0.12", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.12.tgz", "shasum": "4686f056e5894a9cba708adeabc2c49dada90778", "size": 10240, "noattachment": false, "integrity": "sha512-j<PERSON><PERSON>bCSCJPFUNPP8L8JPyEWcLG5t391s39mrCnbFZI5EC1QX6wcBRICOy+AghzdjDT/AN1pxXYAsyoTx2OB3zJg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321643042511, "_hasShrinkwrap": false, "_cnpm_publish_time": 1321643042511, "_cnpmcore_publish_time": "2021-12-13T06:51:54.863Z"}, "1.0.11": {"name": "semver", "version": "1.0.11", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "semver@1.0.11", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.11.tgz", "shasum": "1bd01c550d477cbf9a839b02269c4011ce147992", "size": 10240, "noattachment": false, "integrity": "sha512-NG2tk8v8aDLiJ82lFMDAmAfNLOYlf+qEwrmpUCcf+413uDlh2DPR5Uo5Y41FPjbcFOoXOTCLGSSYF1szV1W3rQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321375204239, "_hasShrinkwrap": false, "_cnpm_publish_time": 1321375204239, "_cnpmcore_publish_time": "2021-12-13T06:51:56.342Z"}, "1.0.10": {"name": "semver", "version": "1.0.10", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap semver.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "semver@1.0.10", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.92", "_nodeVersion": "v0.5.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.10.tgz", "shasum": "cfa5a85d95888c75b4a9275bda7491568d8cfd20", "size": 10240, "noattachment": false, "integrity": "sha512-01AAWjDhmaOBfwtahjC41n6Yc2MPOsnSTMmbS+Y5YCtDtBud9hPeg+hqSw+PmfL5bSK/qMrGWBCXUlRiyuQrxA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1317693097206, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317693097206, "_cnpmcore_publish_time": "2021-12-13T06:51:57.790Z"}, "1.0.9": {"name": "semver", "version": "1.0.9", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap semver.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/semver/1.0.9/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "semver@1.0.9", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.9.tgz", "shasum": "d053046aea88e25b488ecbc0a8c9deda03db8a9c", "size": 7001, "noattachment": false, "integrity": "sha512-2fPgYNmGmaejvD2obMSb52JTSJQVxm2KPP/Ckdx+CHsFUbM0kRe554jWvJUyh+oLctIFv2gDTKHxhg13tFsKzQ=="}, "directories": {}, "publish_time": 1311197893081, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311197893081, "_cnpmcore_publish_time": "2021-12-13T06:51:59.176Z"}, "1.0.8": {"name": "semver", "version": "1.0.8", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap semver.js"}, "devDependencies": {"tap": "0.x >=0.0.4"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/semver/1.0.8/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "semver@1.0.8", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.8.tgz", "shasum": "8a78a9bfad863a0660683c33c91f08b6cd2cfa98", "size": 7000, "noattachment": false, "integrity": "sha512-EO1NC3/lkkaQt4P6THqDPMaOT67hyXdwtTSLZu7puHADe2cZY1e53ILdA/1iEkdu4T3cMVht962xsxm64zl1UQ=="}, "directories": {}, "publish_time": 1309211931266, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1309211931266, "_cnpmcore_publish_time": "2021-12-13T06:52:00.557Z"}, "1.0.7": {"name": "semver", "version": "1.0.7", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "devDependencies": {"tap": "0.x"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/semver/1.0.7/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "semver@1.0.7", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.13", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.7.tgz", "shasum": "668e127e81e81e0954d25a6d2c1cb20a1538b2e3", "size": 6997, "noattachment": false, "integrity": "sha512-t8LmYpE71rsrHgd7e9n3KBWJfF/IlzJpKBkVT7KN8kJj/muyb1YCd2QMw3/tIpkexzvC6yamodMYVP32X7+NdQ=="}, "directories": {}, "publish_time": 1308327967324, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1308327967324, "_cnpmcore_publish_time": "2021-12-13T06:52:02.120Z"}, "1.0.6": {"name": "semver", "version": "1.0.6", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "devDependencies": {"tap": "0.x"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/semver/raw/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "dependencies": {}, "_id": "semver@1.0.6", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.6.tgz", "shasum": "697b6bff4b3eca86f35dc037c9ab2f1eb7af1a9e", "size": 6987, "noattachment": false, "integrity": "sha512-UgpYQpAv0dXBvjIZtInaqKhpIGGL3d2ezKkBn/+Lefd5mDiQ7ibbrw5/KewvEgrWSIZYPssxzBnsB19OBni4KA=="}, "directories": {}, "publish_time": 1305936587724, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305936587724, "_cnpmcore_publish_time": "2021-12-13T06:52:03.686Z"}, "1.0.5": {"name": "semver", "version": "1.0.5", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "devDependencies": {"tap": "0.x"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "dependencies": {}, "_id": "semver@1.0.5", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.4", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.5.tgz", "shasum": "7abca9337d64408ec2d42ffd974858c04dca3bdb", "size": 5917, "noattachment": false, "integrity": "sha512-Z7h3RyOZVszoXynP9IU7j088RvIoi3NivBWfH7ZdjDQ3XPFShNU4udjLM0kxh0Afnm12P+RAGAqHNJXBpJOqpw=="}, "directories": {}, "publish_time": 1304464314939, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1304464314939, "_cnpmcore_publish_time": "2021-12-13T06:52:05.379Z"}, "1.0.4": {"name": "semver", "version": "1.0.4", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "devDependencies": {"tap": "0.x"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "dependencies": {}, "_id": "semver@1.0.4", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc9", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.4.tgz", "shasum": "41c0da40706d0defe763998281fc616a2a5d1e46", "size": 5852, "noattachment": false, "integrity": "sha512-2QU4IpdkKSBoWbWKU0w3aGvlWyY4CvzFPiDs1bBivfKnomaXzF5fx0+BceFFDmPr4p2fUCeip2PSnid2+utiIA=="}, "directories": {}, "publish_time": 1303371131512, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1303371131512, "_cnpmcore_publish_time": "2021-12-13T06:52:06.900Z"}, "1.0.3": {"name": "semver", "version": "1.0.3", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "dependencies": {}, "devDependencies": {}, "_id": "semver@1.0.3", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc9", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.3.tgz", "shasum": "453f40adadf8ce23ff4eb937972c6a007d52ef0d", "size": 4743, "noattachment": false, "integrity": "sha512-n3g1ulgVjIVSa5IJ/zjNsbqEaLkL1AwVqBBY1JDP/kar37uL7HEuWK4tG7qR/IjhcHbcWUaGkW2ioLH61jOffg=="}, "directories": {}, "publish_time": 1303255753670, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1303255753670, "_cnpmcore_publish_time": "2021-12-13T06:52:08.367Z"}, "1.0.2": {"name": "semver", "version": "1.0.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_id": "semver@1.0.2", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.0rc3", "_nodeVersion": "v0.5.0-pre", "directories": {"bin": "./bin"}, "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.2.tgz", "shasum": "57e4e1460c0f1abc2c2c6273457abc04e309706c", "size": 4587, "noattachment": false, "integrity": "sha512-pR1479ERuU15GZJ9uWDvfkE7GgBpgS1RJVjJxgidz4ExvhFUMFYKspUYCrsqnN5JyZvLHHM4DaIMqE8Z21gIyg=="}, "publish_time": 1300829255218, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1300829255218, "_cnpmcore_publish_time": "2021-12-13T06:52:09.833Z"}, "1.0.1": {"name": "semver", "version": "1.0.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "node semver.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-semver.git"}, "bin": {"semver": "./bin/semver"}, "_id": "semver@1.0.1", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.0-8", "_nodeVersion": "v0.5.0-pre", "directories": {"bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.1.tgz", "shasum": "93b90b9a3e00c7a143f2e49f6e2b32fd72237cdb", "size": 4587, "noattachment": false, "integrity": "sha512-I1MWjDW0QWmfI0ctueHeQuBDh2I3joLhSnOkYf20UBV1esvoxdNV1WgUNsPW0LpZ+Zq5O9nbbfACWsXs8kZQmg=="}, "publish_time": 1298049349775, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298049349775, "_cnpmcore_publish_time": "2021-12-13T06:52:11.211Z"}, "1.0.0": {"name": "semver", "version": "1.0.0", "description": "The semantic version parser used by npm.", "main": "semver", "scripts": {"test": "node semver.js"}, "bin": {"semver": "./bin/semver"}, "_id": "semver@1.0.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.0-beta", "_nodeVersion": "v0.4.0", "directories": {"bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/semver/-/semver-1.0.0.tgz", "shasum": "11f18a0c08ed21c988fc2b0257f1951969816615", "size": 3946, "noattachment": false, "integrity": "sha512-3l/lKKm8i8qr6QUjadwmuzJa56Jzo4qfKxBcDi38YZdrHdDl0dmcJnHM9tozk45jTGN3wLWvTE570ywWiKTyTw=="}, "publish_time": 1297470026037, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297470026037, "_cnpmcore_publish_time": "2021-12-13T06:52:12.736Z"}, "7.3.6": {"name": "semver", "version": "7.3.6", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": "^10.0.0 || ^12.0.0 || ^14.0.0 || >=16.0.0"}, "dependencies": {"lru-cache": "^7.4.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x"], "distPaths": ["bin/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"]}, "gitHead": "1ea0fe261851fed16e507410c08b40a2b91a1d1e", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.6", "_nodeVersion": "16.14.2", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-HZWqcgwLsjaX1HBD31msI/rXktuIhS+lWvdE4kN9z+8IVT4Itc7vqU2WvYsyD6/sjYCt4dEKH/m1M3dwI9CC5w==", "shasum": "5d73886fb9c0c6602e79440b97165c29581cbb2b", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.6.tgz", "fileCount": 51, "unpackedSize": 87319, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0mR8aBDXEQrqMXJH4NgCScNwpLCzGX/rNgmqCdkAPaQIhAPmriqoVLJxT75raIgPjQJPeJKr4CUaw/8apB7slN30t"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTcFNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqASBAAi5vCBGxg2XXI1DTgenEXg7yCvhft4Q5rBo+hND1ZkAcZGksQ\r\nlejC6/ndxYxjDNQbc/D3f/xUonG0WpyLTTPPNCe9+jP1svbx8YT76Z8lybbi\r\nFICSyZfiF2m16xJ1YdCrUTHyomM9mQtepcOwc+xpT2MOg+zm3fhpTG5cBNbR\r\n2K4pyrSxbx2o+zCtPvhznUIlrY4o+bZj62RC87wd2wqH8AVjP1HE6o+7excO\r\njnyWNbR01uH2lj5hJMyvzXKdOq61RnTlieYgaMiDErRTJPduf4BfiWgMR+tb\r\n6IpIOV8A2JFb5fYY9hwc71Fu/vT5Y7hJ6Pr30kmcy5i7KMT7nWa+sHy9D4Ei\r\nIkA4guz430p2EP3LTQ4rInoPn9ZujFmYfPQ5ZSGsYMK78U/J9MONp6u2fdBw\r\nNoL02nX/4TwJRkZhdtqZG92Xu6wAQu2rSMrlPUxNmCggjFH3GSzAHEIWEtMf\r\nSK7nOuVznf5g5q+19BF5JS1dd7fiy/SvLXsUzm8T3z2WHdedSOTntBTM/VsZ\r\n0uqsNqNIgwaCmDhRqOi2lHykMQBeYNu/OnBnAJ9m8l+8Q9n3h/zOwbDG2HBl\r\nnqsTjC59QCqcseSLWM/+rmoeqqyeDMBNpspVmRA4HmeZPe7DRiqRQprppofr\r\nCgI4xBKoPT52KbkdAaL1P1aLFiqHsS+7qSM=\r\n=f01D\r\n-----END PGP SIGNATURE-----\r\n", "size": 24984}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.6_1649262925411_0.9717734085396461"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-06T17:59:16.634Z"}, "7.3.7": {"name": "semver", "version": "7.3.7", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.3.2", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.3.2", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x"], "distPaths": ["bin/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"]}, "gitHead": "7a2d69c294571bb71c53ccf8104edb3937bc28b2", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.7", "_nodeVersion": "16.14.2", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==", "shasum": "12c5b649afdbf9049707796e22a4028814ce523f", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.7.tgz", "fileCount": 51, "unpackedSize": 87418, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHK6xa/t9j6mwcOiHg8i6GpUlmBq/dtzLAcOUroqDrJUAiAtwDesc7AH66h7RJNH9HDYjRkSlTsVGEKiab5bnN66Ew=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVbZBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1yQ//eGzrJTVQAT1CMLLJGLDs5Y/T83535qrINHJ5JW8PiKq9WPHM\r\nbe8Ti8L45Onw8CdAyElaKJB3bufCebGKy+qFm8vU5VGKiwe4UNSuhVK2QZFz\r\nFxb1a7NT/nvXxCtRtAeXm2BSkIhdawMLbATGp6Ljc1TTWTomqrYndyCVspSg\r\nVqTBgHvU0szXZlNTmtekPO1X+i/dRqOAblLBxKmbGsF77B5bxRpPfTgt/fj6\r\n7ol0+r1C0NCw5NOkkKGlcEVUFhO+Z3Hv4KhIYqfRuDarPYxDJi9XMZ2hvqmB\r\nNb2N3WtaAF2WK7rUxp+er0tUM0kfvNL4EuZDb2zLA7tBKj5CLdpUeb/yF3JJ\r\n+qikPaXrj2lROE4dqqXEvcEYDe0JDZ6NyL4c6W6FHSzelzsRhr3xA8SkrLqA\r\nYQs+YYDQr6/upSg6swHT+8RG/AfkmmhkP+MrSj3LDtwlb+h2tOQxzUHVKKLg\r\n2d/arVjZzOThKLyaLUZk+JPNb/jUR9NiQ/tbJZaRS7T/VkBNPYXQ42Pql5eU\r\njxfskGPv5ngu2VPWZuFCQ2FsMlAgxMTXptKKk0qU4/cjGiMlJR+dIffvvsF0\r\n6HRZOKE3ist+/Kr7E1kDOj15pmE6vPHMVX3p85Hm3FNuk8P/GM2wkfGzwMf1\r\nyB9kCZ+JWBGgLbbksXucyAkiz/hiLkftfWQ=\r\n=wgB4\r\n-----END PGP SIGNATURE-----\r\n", "size": 25007}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.7_1649784384799_0.18837178049597436"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-12T17:41:29.297Z"}, "7.3.8": {"name": "semver", "version": "7.3.8", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.4.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.4.4", "engines": ">=10", "content": "./scripts", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"]}, "gitHead": "dc0fe202faaf19a545ce5eeab3480be84180a082", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.3.8", "_nodeVersion": "18.6.0", "_npmVersion": "9.0.0-pre.3", "dist": {"integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "shasum": "07a78feafb3f7b32347d725e33de7e2a2df67798", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.3.8.tgz", "fileCount": 51, "unpackedSize": 88204, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfMbIqJguiHPwmmbY/zHraBPuMAOlCG7rCwn8kvfM1vgIhAOAbX3CN/MUAGw9zRjP1q8r26neb8J2gYkklnnC4jnqi"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPIw/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr99Q//bEVkJFE4JZ+XsxdxrFiT1xnwBV0+vIMlWX5757NRSf5bM2b8\r\nRrElOYrivTuPRdRVU/ic7yDvzuFIGfqA6sN/x6coWcBrSaltBsDBVTvTlPYu\r\nvjhsSUFVdqumaCEhbVD/87dQPz4qW80RVsrBVAXRo3Gf4ZYMzrZSyKfymhxG\r\nHnyubfSZTbdntSHoVvNNIy2xBQpsvgLhqEXHGsHFjC9NDsVAzmpo1a+iRSMi\r\nmqtnE6YsjFn6BszTeS5K3ZCBOQwDaYUdrAAU3WfO3kruJRkz7lqJ/Dj8UCEn\r\n9J3+t8dAAC0DPFNeEvohoGtBLJSO8kogNY5XFokT3ds2hSd97kQEDHm56xHS\r\nWaSlO66DNqLzfDuel+OEadc1JNAmyQeaLRwaCvJhhF8BdVRG3EVmlojAe+fQ\r\nTDnA9mkUXN8F2kM9Ixnf4Wg0eflUcg+SsIqgyie05hBm9GaJ9fd4r9AJ3S/s\r\nM3vZ8yTrxwcatcnF8+Zl6FaRe35r89Gn80c76FtL+bPytMlidTimLZLqbeEm\r\nbcDHf2bUi0sRWD2BXYxGUAFGDr/FipjPC/jG7lw04TnISWBlzYJTiJrm4jvV\r\nfv1KgtyB9P9W1XMElI8MKqJQnNIm9vCR5/R0o8Z93QXmVe+SIGsDfIAulM/W\r\neORiAoO5f/qVcnGmC/bF9kDGHSqtvNxfLMs=\r\n=/c9Q\r\n-----END PGP SIGNATURE-----\r\n", "size": 25191}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.3.8_1664912447716_0.647142154438872"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-04T19:40:55.947Z"}, "7.4.0": {"name": "semver", "version": "7.4.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.13.0", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.13.0", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "npmSpec": "8", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "gitHead": "82aa7f62617d445f578584543565329a62343aab", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.4.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-RgOxM8Mw+7Zus0+zcLEUn8+JfoLpj/huFTItQy2hsM4khuC1HYRDp0cU482Ewn/Fcy6bCjufD8vAj7voC66KQw==", "shasum": "8481c92feffc531ab1e012a8ffc15bdd3a0f4318", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.4.0.tgz", "fileCount": 51, "unpackedSize": 90078, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+AHRIawycC+CL7jn6IeG8qI0YTzXhH/DGZ17RN919WAiEArBUyfYL/BbnT0yTtL0lY3MMt1i/8BKCVmvK+sD8fepQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNIZaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYLA//V71EN9qQnvlXt4hyb5Hr7WghnBJV//K9elHIYf6pRBfvhJH+\r\ntQamY/yjdJuOjVAv98aUYkbab8PzH0N1ed5GjIlstPi4y6jMybKNW5EYYJEi\r\nWiFnxlyo0iJdTSnJu6Ud2xjNGzz12NMNTWqEsZ1Zl+VBO1fzDDv7ccInmsyH\r\nP7tr04fywpgwOGbMDgwpdEMCxDyC+ra3x2v7ZBQl8UgyfMktoYOJMKpHCMQG\r\n7Od9DW4Z/+PliXe9umzswKHHJxZzk0iyFBoBAkQeEo6gHe2q1YDCU4IOMRMr\r\n9wGD7D4hiif+QkgDuGbRQptyLSl6GXnw6PheUjao8thR7hg40CyTq96NRAW/\r\nuNLb0I4SkQmmMH+V9iIbieHdXIHSCiWDdMRfPdYtTqlXJYJmTuNdxAUnwR9t\r\nfEZ1wM8xxK6z97ZPgiCNOKHPsL4AHtIglCSPltXw+AfSUvFu3SfoB3cb00PH\r\n5S+WnRgSEuRO0FShqFEG0WHrX4X9rZgtd08bpHGRBhymkYl+r5U7nS32AyDy\r\nxZ4QsoNblkZDCkhWxHdL5lg5fMvIsnwtPYnehLOxOZE5gLhnZHzl6+GSDhjN\r\npV5JSy9YdnykoJypDGG0OyE2ro4a3SjMellh2UKxXmT1sjUGpjcPJH2l4ChY\r\niCrqkalZScXZtjhMT12b1m+snN1n7HwJq2w=\r\n=y35w\r\n-----END PGP SIGNATURE-----\r\n", "size": 25670}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.4.0_1681163866102_0.5153635697004932"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-10T21:57:46.268Z", "publish_time": 1681163866268}, "7.5.0": {"name": "semver", "version": "7.5.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.13.0", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.13.0", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "npmSpec": "8", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "gitHead": "5b02ad7163a3ddcbcadf499e4f6195d6f2226dce", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.5.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-+XC0AD/R7Q2mPSRuy2Id0+CGTZ98+8f+KvwirxOKIEyid+XSx6HbC63p+O4IndTHuX5Z+JxQ0TghCkO5Cg/2HA==", "shasum": "ed8c5dc8efb6c629c88b23d41dc9bf40c1d96cd0", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.5.0.tgz", "fileCount": 51, "unpackedSize": 91367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDt07E5UJuRwXPOEi2hbEuVDLVZG3Pf9OMnpX48BNkovAIhAI9nWUYPRzvhBwQjGhrkaVWjLEDlnUGLInCY6j9khlNu"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPYBwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSbxAAkuGiIuyA9EhYLW+KXPEY13ovIYhhJPKzhiL4VU22qt4OV+dm\r\nMwfWUoO4D3uQdpnbBnS5RHFI9Y3414A4AUNMUjlnsORbJJycIeHm82/3WJtT\r\nD3K5QtKtGhdMVOIHy9VlahtEj0hJvxiirv6Ly7xnlFP6cw1Xoo8uivD9wXPt\r\n30qSQyYUxAJNG/199y6Homa7oAFJW0ctu7iTZfHpGtrh5cwOBYTxSDDKtkZl\r\nP8W+Ip+1OPkHn1HdBGlSaMJSzuJtYWUbYG415KJf6n/QIOWp8zTnfg1RTeTJ\r\nrctDzU8WGdoC3cvLkuv8e50NvmbPNI8KNHgDqV5UhcEOy1t4LCnWcBntLXXJ\r\nGBhIk85AA4pG31euM6dyyoVFuQBoq6nbhdy859+YE40DypSiT8v1V4yq38Y0\r\n6syo+j4/yPCKrVxDfiePupltJIe5g9nwSoKWU7AWMQ1DREq9KqI2EaYUygYF\r\ndL+7uHTr8ALEaBZfZ4LJ3EwTAhhcrrUSrmImNVVicnHjQWAv3VtLFV8MisN6\r\neqMTJwpPhtWi8C6s2kdM3HHsNf6eEt5IRs1HYr6t41kAIUF+bjtVTG9GnUB0\r\nHK+y98OTS461JUEQlPULbSxbYRwmv9Ka7NIdYPg9Zmw4efZyRW5Ize5cYfv5\r\nIXRWQBbGo/8ovhjW5NSsM2WnaeVT1Et2R+4=\r\n=c4yY\r\n-----END PGP SIGNATURE-----\r\n", "size": 26092}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.5.0_1681752176355_0.39230078049183925"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-17T17:22:56.540Z", "publish_time": 1681752176540}, "7.5.1": {"name": "semver", "version": "7.5.1", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.14.1", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.14.1", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "npmSpec": "8", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "gitHead": "aa016a67162c195938f7873ea29a73dac47ff9ba", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.5.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-Wvss5ivl8TMRZXXESstBA4uR5iXgEN/VC5/sOcuXdVLzcdkz4HWetIoRfG5gb5X+ij/G9rw9YoGn3QoQ8OCSpw==", "shasum": "c90c4d631cf74720e46b21c1d37ea07edfab91ec", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.5.1.tgz", "fileCount": 51, "unpackedSize": 91379, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDg9wLVr9b1ltyYFc61qa+HZlOkWATcmpBZwf324Wp9xwIhAMntmIqywp/Cuz/Gmf2LCC5STJSAWiGOgUWxu3trPi+Z"}], "size": 26100}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.5.1_1683909581518_0.26769160533187386"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-12T16:39:41.720Z", "publish_time": 1683909581720, "_source_registry_name": "default"}, "7.5.2": {"name": "semver", "version": "7.5.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.15.1", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.15.1", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "npmSpec": "8", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "gitHead": "e7b78de06eb14a7fa2075cedf9f167040d8d31af", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.5.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.1", "dist": {"integrity": "sha512-SoftuTROv/cRjCze/scjGyiDtcUyxw1rgYQSZY7XTmtR5hX+dm76iDbTH8TkLPHCQmlbQVSSbNZCPM2hb0knnQ==", "shasum": "5b851e66d1be07c1cdaf37dfc856f543325a2beb", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.5.2.tgz", "fileCount": 51, "unpackedSize": 92605, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmFfzANuN7yDnbcJkGbVoNdXIriQXp3gdOfpbBYCmXPQIhAKwIaEvIBBPoUBP7ubl66810YwU3KZGZja0PxNUIOpy0"}], "size": 26617}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.5.2_1686860771824_0.57606870088476"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T20:26:11.975Z", "publish_time": 1686860771975, "_source_registry_name": "default"}, "7.5.3": {"name": "semver", "version": "7.5.3", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.15.1", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.15.1", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "npmSpec": "8", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "gitHead": "7fdf1ef223826b428d7f8aaf906e9eeefa9469f9", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@7.5.3", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-QBlUtyVk/5EeHbi7X0fw6liDZc7BBmEaSYn01fMU1OUYbf6GPsbTtd8WmnqbI20SeycoHSeiybkE/q1Q+qlThQ==", "shasum": "161ce8c2c6b4b3bdca6caadc9fa3317a4c4fe88e", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.5.3.tgz", "fileCount": 51, "unpackedSize": 93390, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0fBOo0eTChhzJ+ngR/HOI/HIn/FBMJ1cSleP7UUHpGgIhAM2H6NINrINo+1jE5UweaX31/tPunAlyaczNsd7rrbq6"}], "size": 26872}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.5.3_1687470799532_0.2805096124485813"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-22T21:53:19.774Z", "publish_time": 1687470799774, "_source_registry_name": "default"}, "7.5.4": {"name": "semver", "version": "7.5.4", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.17.0", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.17.0", "engines": ">=10", "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "npmSpec": "8", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "_id": "semver@7.5.4", "gitHead": "36cd334708ec1f85a71445622fb1864bceee0f4e", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "18.16.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==", "shasum": "483986ec4ed38e1c6c48c34894a9182dbff68a6e", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz", "fileCount": 51, "unpackedSize": 93401, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICledieqn36Ququ16KUtspwRpndZ1cor5Bn8AL0istatAiA6nTSWUF4M/o1UcAnaSEaoEwNxbIEvOfE94WXVBFla7A=="}], "size": 26879}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.5.4_1688764232427_0.41544901656519095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-07T21:10:32.589Z", "publish_time": 1688764232589, "_source_registry_name": "default"}, "5.7.2": {"name": "semver", "version": "5.7.2", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/ --100 --timeout=30", "lint": "echo linting disabled", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap test/ --100 --timeout=30", "posttest": "npm run lint"}, "devDependencies": {"@npmcli/template-oss": "4.17.0", "tap": "^12.7.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "content": "./scripts/template-oss", "version": "4.17.0"}, "readmeFilename": "README.md", "gitHead": "63169c1d87a1f36eb35022a3c6fcaf7ba6954055", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@5.7.2", "_nodeVersion": "20.2.0", "_npmVersion": "9.7.1", "dist": {"integrity": "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==", "shasum": "48d55db737c3287cd4835e17fa13feace1c41ef8", "tarball": "https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz", "fileCount": 6, "unpackedSize": 63315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA1FkZlK+BP8dzoMUahZunoDStleso00k4b8Mnt/73xDAiEA9Fa9ZlmGHmvqmXmPvnAkueEb8/gl8D27TKon4US4m+Y="}], "size": 17872}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_5.7.2_1689019066913_0.7461531805384485"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-10T19:57:47.111Z", "publish_time": 1689019067111, "_source_registry_name": "default"}, "6.3.1": {"name": "semver", "version": "6.3.1", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/ --100 --timeout=30", "lint": "echo linting disabled", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap test/ --100 --timeout=30", "posttest": "npm run lint"}, "devDependencies": {"@npmcli/template-oss": "4.17.0", "tap": "^12.7.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "content": "./scripts/template-oss", "version": "4.17.0"}, "readmeFilename": "README.md", "gitHead": "b717044e57bd132c7e5aa50e9af9a03f10d4655a", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_id": "semver@6.3.1", "_nodeVersion": "20.2.0", "_npmVersion": "9.7.1", "dist": {"integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "shasum": "556d2ef8689146e46dcea4bfdd095f3434dffcb4", "tarball": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "fileCount": 6, "unpackedSize": 68343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPu+5SJS1ygK7jDAPKWWXoKPfkubt2xDbbpmCZnRoCHwIgAm7TDcdNEL196wlziooSvSxaEIQmW42yauM55KOgKwQ="}], "size": 19093}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_6.3.1_1689028721173_0.39493960745374723"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-10T22:38:41.428Z", "publish_time": 1689028721428, "_source_registry_name": "default"}, "7.6.0": {"name": "semver", "version": "7.6.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "dependencies": {"lru-cache": "^6.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "publish": "true"}, "_id": "semver@7.6.0", "gitHead": "377f709718053a477ed717089c4403c4fec332a1", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.4.0", "dist": {"integrity": "sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==", "shasum": "1a46a4db4bffcccd97b743b5005c8325f23d4e2d", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.6.0.tgz", "fileCount": 51, "unpackedSize": 94244, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNwnW9kHxzw5D4hq/5k8jay1Xp6PrsP+zdldgIrenJrAIgINOiu9gTWDQrcXN/KUnnkLYuBWuSSHZXZsItjsV3czs="}], "size": 27204}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.6.0_1707152811382_0.682335914387501"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-05T17:06:51.520Z", "publish_time": 1707152811520, "_source_registry_name": "default"}, "7.6.1": {"name": "semver", "version": "7.6.1", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "_id": "semver@7.6.1", "gitHead": "d777418116aeaecca9842b7621dd0ac1a92100bc", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "22.1.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-f/vbBsu+fOiYt+lmwZV0rVwJScl46HppnOA1ZvIuBWKOTlllpyJ3bfVax76/OrhCH38dyxoDIA8K7uB963IYgA==", "shasum": "60bfe090bf907a25aa8119a72b9f90ef7ca281b2", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.6.1.tgz", "fileCount": 52, "unpackedSize": 95504, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDJkY8/BXAHjS+R851ucquRGdIm0QC+GqiwwsvGENwj1AiByhonw2VM5P0ud5qqlmv8tD+K5HTGZ7ihm/zD1ZCSGgA=="}], "size": 27588}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.6.1_1715097748632_0.42046359595446403"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-07T16:02:28.840Z", "publish_time": 1715097748840, "_source_registry_name": "default"}, "7.6.2": {"name": "semver", "version": "7.6.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "_id": "semver@7.6.2", "gitHead": "eb1380b1ecd74f6572831294d55ef4537dfe1a2a", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "22.1.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==", "shasum": "1e3b34759f896e8f14d6134732ce798aeb0c6e13", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.6.2.tgz", "fileCount": 52, "unpackedSize": 95424, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6/pwdiEu5Ip6DyQ8rwsJ13wLppdOMIDtJOClcLbK+nwIhAMgA4McQIu/+mVtitzpO97NIKFlLAt+8ABL6dnMipilm"}], "size": 27580}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.6.2_1715270569842_0.9942888461775998"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-09T16:02:50.012Z", "publish_time": 1715270570012, "_source_registry_name": "default"}, "7.6.3": {"name": "semver", "version": "7.6.3", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "_id": "semver@7.6.3", "gitHead": "0a12d6c7debb1dc82d8645c770e77c47bac5e1ea", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "22.4.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==", "shasum": "980f7b5550bc175fb4dc09403085627f9eb33143", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.6.3.tgz", "fileCount": 52, "unpackedSize": 95824, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAqB4ybgf5pFTkoWqIEDsWUkavZuelVqZrDd52CZ13OoAiEA+SBgG8aqumWI14kRLo9B6g3jiWzJt1jDtPpgfn0p/iw="}], "size": 27678}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/semver_7.6.3_1721168838877_0.9241625569831082"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-16T22:27:19.119Z", "publish_time": 1721168839119, "_source_registry_name": "default"}, "7.7.0": {"name": "semver", "version": "7.7.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "_id": "semver@7.7.0", "gitHead": "2cfcbb5021059d0b6642a77400efb4b51133bd75", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "22.13.1", "_npmVersion": "11.0.0", "dist": {"integrity": "sha512-DrfFnPzblFmNrIZzg5RzHegbiRWg7KMR7btwi2yjHwx06zsUbO5g613sVwEV7FTwmzJu+Io0lJe2GJ3LxqpvBQ==", "shasum": "9c6fe61d0c6f9fa9e26575162ee5a9180361b09c", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.7.0.tgz", "fileCount": 52, "unpackedSize": 96558, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIA1RRcJDXsTva4cdPL5DN5VXGfalYYYzXMkC+256FkEkAiAGUxt+/oj9B4GtMJT/VfdKSGhIJThbb7gNwi4sb107cg=="}], "size": 28331}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/semver_7.7.0_1738170881440_0.41305976524258514"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-29T17:14:41.608Z", "publish_time": 1738170881608, "_source_registry_name": "default"}, "7.7.1": {"name": "semver", "version": "7.7.1", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "_id": "semver@7.7.1", "gitHead": "30c438bb46c74f319aa8783f96d233ebf5f4a90d", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "22.13.1", "_npmVersion": "11.1.0", "dist": {"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==", "shasum": "abd5098d82b18c6c81f6074ff2647fd3e7220c9f", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.7.1.tgz", "fileCount": 52, "unpackedSize": 96674, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.7.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDI9SVHcoBbYQUqWYqedRBye+SM2pOj6BV9jBPpUOBszQIgUMGFgxbiwWvdfdCfce17m7VbWiRVZe+heIdtM0GanmI="}], "size": 28361}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/semver_7.7.1_1738619172328_0.9405645367047419"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-03T21:46:12.515Z", "publish_time": 1738619172515, "_source_registry_name": "default"}, "7.7.2": {"name": "semver", "version": "7.7.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.3", "benchmark": "^2.1.4", "tap": "^16.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "bin": {"semver": "bin/semver.js"}, "tap": {"timeout": 30, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": ">=10"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.3", "engines": ">=10", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "publish": "true"}, "_id": "semver@7.7.2", "gitHead": "281055e7716ef0415a8826972471331989ede58c", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "_nodeVersion": "22.15.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "shasum": "67d99fdcd35cec21e6f8b87a7fd515a33f982b58", "tarball": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "fileCount": 52, "unpackedSize": 97420, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.7.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBWgpqrE5Kqf9YKjIum5n0B6fPDkhrQ2Uf5DKmopfaS5AiBcu38UXAKlgwXKCiOnWMjRjjo4YSVB/6f2YaHE284k7g=="}], "size": 28388}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/semver_7.7.2_1747069348186_0.6289037087827105"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-12T17:02:28.372Z", "publish_time": 1747069348372, "_source_registry_name": "default"}}, "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "homepage": "https://github.com/npm/node-semver#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "_source_registry_name": "default"}