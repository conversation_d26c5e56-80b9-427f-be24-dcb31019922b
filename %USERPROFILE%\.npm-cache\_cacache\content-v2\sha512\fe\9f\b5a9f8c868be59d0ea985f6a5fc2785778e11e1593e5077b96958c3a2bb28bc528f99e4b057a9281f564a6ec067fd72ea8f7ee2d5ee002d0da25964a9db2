{"_attachments": {}, "_id": "eslint-plugin-prettier", "_rev": "196-61f14423b77ea98a748f278d", "author": {"name": "<PERSON>"}, "description": "Runs prettier as an eslint rule", "dist-tags": {"alpha": "5.0.0-alpha.2", "latest": "5.5.1"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}], "name": "eslint-plugin-prettier", "readme": "# eslint-plugin-prettier [![Build Status](https://github.com/prettier/eslint-plugin-prettier/actions/workflows/ci.yml/badge.svg?branch=main)](https://github.com/prettier/eslint-plugin-prettier/actions?query=workflow%3ACI+branch%3Amain)\n\nRuns [Prettier](https://github.com/prettier/prettier) as an [ESLint](https://eslint.org) rule and reports differences as individual ESLint issues.\n\nIf your desired formatting does not match Prettier’s output, you should use a different tool such as [prettier-eslint](https://github.com/prettier/prettier-eslint) instead.\n\nPlease read [Integrating with linters](https://prettier.io/docs/en/integrating-with-linters.html) before installing.\n\n## TOC <!-- omit in toc -->\n\n- [Sample](#sample)\n- [Installation](#installation)\n- [Configuration (legacy: `.eslintrc*`)](#configuration-legacy-eslintrc)\n- [Configuration (new: `eslint.config.js`)](#configuration-new-eslintconfigjs)\n- [`Svelte` support](#svelte-support)\n- [`arrow-body-style` and `prefer-arrow-callback` issue](#arrow-body-style-and-prefer-arrow-callback-issue)\n- [Options](#options)\n- [Sponsors](#sponsors)\n- [Backers](#backers)\n- [Contributing](#contributing)\n- [Changelog](#changelog)\n- [License](#license)\n\n## Sample\n\n```js\nerror: Insert `,` (prettier/prettier) at pkg/commons-atom/ActiveEditorRegistry.js:22:25:\n  20 | import {\n  21 |   observeActiveEditorsDebounced,\n> 22 |   editorChangesDebounced\n     |                         ^\n  23 | } from './debounced';;\n  24 |\n  25 | import {observableFromSubscribeFunction} from '../commons-node/event';\n\n\nerror: Delete `;` (prettier/prettier) at pkg/commons-atom/ActiveEditorRegistry.js:23:21:\n  21 |   observeActiveEditorsDebounced,\n  22 |   editorChangesDebounced\n> 23 | } from './debounced';;\n     |                     ^\n  24 |\n  25 | import {observableFromSubscribeFunction} from '../commons-node/event';\n  26 | import {cacheWhileSubscribed} from '../commons-node/observable';\n\n\n2 errors found.\n```\n\n> `./node_modules/.bin/eslint --format codeframe pkg/commons-atom/ActiveEditorRegistry.js` (code from [nuclide](https://github.com/facebook/nuclide)).\n\n## Installation\n\n```sh\nnpm install --save-dev eslint-plugin-prettier eslint-config-prettier\nnpm install --save-dev --save-exact prettier\n```\n\n**_`eslint-plugin-prettier` does not install Prettier or ESLint for you._** _You must install these yourself._\n\nThis plugin works best if you disable all other ESLint rules relating to code formatting, and only enable rules that detect potential bugs. If another active ESLint rule disagrees with `prettier` about how code should be formatted, it will be impossible to avoid lint errors. Our recommended configuration automatically enables [`eslint-config-prettier`](https://github.com/prettier/eslint-config-prettier) to disable all formatting-related ESLint rules.\n\n## Configuration (legacy: `.eslintrc*`)\n\nFor [legacy configuration](https://eslint.org/docs/latest/use/configure/configuration-files-deprecated), this plugin ships with a `plugin:prettier/recommended` config that sets up both `eslint-plugin-prettier` and [`eslint-config-prettier`](https://github.com/prettier/eslint-config-prettier) in one go.\n\nAdd `plugin:prettier/recommended` as the _last_ item in the extends array in your `.eslintrc*` config file, so that `eslint-config-prettier` has the opportunity to override other configs:\n\n```json\n{\n  \"extends\": [\"plugin:prettier/recommended\"]\n}\n```\n\nThis will:\n\n- Enable the `prettier/prettier` rule.\n- Disable the `arrow-body-style` and `prefer-arrow-callback` rules which are problematic with this plugin - see the below for why.\n- Enable the `eslint-config-prettier` config which will turn off ESLint rules that conflict with Prettier.\n\n## Configuration (new: `eslint.config.js`)\n\nFor [flat configuration](https://eslint.org/docs/latest/use/configure/configuration-files-new), this plugin ships with an `eslint-plugin-prettier/recommended` config that sets up both `eslint-plugin-prettier` and [`eslint-config-prettier`](https://github.com/prettier/eslint-config-prettier) in one go.\n\nImport `eslint-plugin-prettier/recommended` and add it as the _last_ item in the configuration array in your `eslint.config.js` file so that `eslint-config-prettier` has the opportunity to override other configs:\n\n```js\nconst eslintPluginPrettierRecommended = require('eslint-plugin-prettier/recommended');\n\nmodule.exports = [\n  // Any other config imports go at the top\n  eslintPluginPrettierRecommended,\n];\n```\n\nThis will:\n\n- Enable the `prettier/prettier` rule.\n- Disable the `arrow-body-style` and `prefer-arrow-callback` rules which are problematic with this plugin - see the below for why.\n- Enable the `eslint-config-prettier` config which will turn off ESLint rules that conflict with Prettier.\n\n## `Svelte` support\n\nWe recommend to use [`eslint-plugin-svelte`](https://github.com/ota-meshi/eslint-plugin-svelte) instead of [`eslint-plugin-svelte3`](https://github.com/sveltejs/eslint-plugin-svelte3) because `eslint-plugin-svelte` has a correct [`eslint-svelte-parser`](https://github.com/ota-meshi/svelte-eslint-parser) instead of hacking.\n\nWhen use with `eslint-plugin-svelte3`, `eslint-plugin-prettier` will just ignore the text passed by `eslint-plugin-svelte3`, because the text has been modified.\n\nIf you still decide to use `eslint-plugin-svelte3`, you'll need to run `prettier --write *.svelte` manually.\n\n## `arrow-body-style` and `prefer-arrow-callback` issue\n\nIf you use [arrow-body-style](https://eslint.org/docs/rules/arrow-body-style) or [prefer-arrow-callback](https://eslint.org/docs/rules/prefer-arrow-callback) together with the `prettier/prettier` rule from this plugin, you can in some cases end up with invalid code due to a bug in ESLint’s autofix – see [issue #65](https://github.com/prettier/eslint-plugin-prettier/issues/65).\n\nFor this reason, it’s recommended to turn off these rules. The `plugin:prettier/recommended` config does that for you.\n\nYou _can_ still use these rules together with this plugin if you want, because the bug does not occur _all the time._ But if you do, you need to keep in mind that you might end up with invalid code, where you manually have to insert a missing closing parenthesis to get going again.\n\nIf you’re fixing large of amounts of previously unformatted code, consider temporarily disabling the `prettier/prettier` rule and running `eslint --fix` and `prettier --write` separately.\n\n## Options\n\n> Note: While it is possible to pass options to Prettier via your ESLint configuration file, it is not recommended because editor extensions such as `prettier-atom` and `prettier-vscode` **will** read [`.prettierrc`](https://prettier.io/docs/en/configuration.html), but **won't** read settings from ESLint, which can lead to an inconsistent experience.\n\n- The first option:\n  - An object representing [options](https://prettier.io/docs/en/options.html) that will be passed into prettier. Example:\n\n    ```json\n    {\n      \"prettier/prettier\": [\n        \"error\",\n        {\n          \"singleQuote\": true,\n          \"parser\": \"flow\"\n        }\n      ]\n    }\n    ```\n\n    NB: This option will merge and override any config set with `.prettierrc` files\n\n- The second option:\n  - An object with the following options\n    - `usePrettierrc`: Enables loading of the Prettier configuration file, (default: `true`). May be useful if you are using multiple tools that conflict with each other, or do not wish to mix your ESLint settings with your Prettier configuration. And also, it is possible to run prettier without loading the prettierrc config file [via the CLI's --no-config option](https://prettier.io/docs/en/cli.html#--no-config) or through the API by [calling prettier.format() without passing through the options generated by calling resolveConfig](https://prettier.io/docs/en/api.html#prettierresolveconfigfilepath--options).\n\n      ```json\n      {\n        \"prettier/prettier\": [\n          \"error\",\n          {},\n          {\n            \"usePrettierrc\": false\n          }\n        ]\n      }\n      ```\n\n    - `fileInfoOptions`: Options that are passed to [prettier.getFileInfo](https://prettier.io/docs/en/api.html#prettiergetfileinfofilepath--options) to decide whether a file needs to be formatted. Can be used for example to opt-out from ignoring files located in `node_modules` directories.\n\n      ```json\n      {\n        \"prettier/prettier\": [\n          \"error\",\n          {},\n          {\n            \"fileInfoOptions\": {\n              \"withNodeModules\": true\n            }\n          }\n        ]\n      }\n      ```\n\n- The rule is auto fixable -- if you run `eslint` with the `--fix` flag, your code will be formatted according to `prettier` style.\n\n---\n\n## Sponsors\n\n| @prettier/plugin-eslint                                                                                                                                                        | eslint-config-prettier                                                                                                                                                       | eslint-plugin-prettier                                                                                                                                     | prettier-eslint                                                                                                                                          | prettier-eslint-cli                                                                                                                                                 |\n| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n| [![@prettier/plugin-eslint Open Collective sponsors](https://opencollective.com/prettier-plugin-eslint/tiers/sponsors.svg)](https://opencollective.com/prettier-plugin-eslint) | [![eslint-config-prettier Open Collective backers](https://opencollective.com/eslint-config-prettier/tiers/sponsors.svg)](https://opencollective.com/eslint-config-prettier) | [![eslint-plugin-prettier Open Collective backers](https://opencollective.com/eslint-plugin-prettier/tiers/sponsors.svg)](https://opencollective.com/rxts) | [![prettier-eslint Open Collective sponsors](https://opencollective.com/prettier-eslint/tiers/sponsors.svg)](https://opencollective.com/prettier-eslint) | [![prettier-eslint-cli Open Collective backers](https://opencollective.com/prettier-eslint-cli/tiers/sponsors.svg)](https://opencollective.com/prettier-eslint-cli) |\n\n## Backers\n\n| @prettier/plugin-eslint                                                                                                                                                      | eslint-config-prettier                                                                                                                                                      | eslint-plugin-prettier                                                                                                                                    | prettier-eslint                                                                                                                                        | prettier-eslint-cli                                                                                                                                                |\n| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |\n| [![@prettier/plugin-eslint Open Collective backers](https://opencollective.com/prettier-plugin-eslint/tiers/backers.svg)](https://opencollective.com/prettier-plugin-eslint) | [![eslint-config-prettier Open Collective backers](https://opencollective.com/eslint-config-prettier/tiers/backers.svg)](https://opencollective.com/eslint-config-prettier) | [![eslint-plugin-prettier Open Collective backers](https://opencollective.com/eslint-plugin-prettier/tiers/backers.svg)](https://opencollective.com/rxts) | [![prettier-eslint Open Collective backers](https://opencollective.com/prettier-eslint/tiers/backers.svg)](https://opencollective.com/prettier-eslint) | [![prettier-eslint-cli Open Collective backers](https://opencollective.com/prettier-eslint-cli/tiers/backers.svg)](https://opencollective.com/prettier-eslint-cli) |\n\n## Contributing\n\nSee [CONTRIBUTING.md](https://github.com/prettier/eslint-plugin-prettier/blob/main/CONTRIBUTING.md)\n\n## Changelog\n\nDetailed changes for each release are documented in [CHANGELOG.md](./CHANGELOG.md).\n\n## License\n\n[MIT](http://opensource.org/licenses/MIT)\n", "time": {"created": "2022-01-26T12:52:51.481Z", "modified": "2025-06-25T11:27:04.731Z", "4.0.0": "2021-08-30T19:52:29.922Z", "3.4.1": "2021-08-20T21:52:38.668Z", "3.4.0": "2021-04-15T01:01:36.467Z", "3.3.1": "2021-01-04T18:48:14.880Z", "3.3.0": "2020-12-13T22:50:41.004Z", "3.2.0": "2020-12-03T06:51:41.579Z", "3.1.4": "2020-06-14T23:23:38.075Z", "3.1.3": "2020-04-13T01:16:27.528Z", "3.1.2": "2019-12-15T03:57:26.924Z", "3.1.1": "2019-09-18T06:30:22.289Z", "3.1.0": "2019-05-11T14:55:45.018Z", "3.0.1": "2018-12-28T07:19:12.896Z", "3.0.0": "2018-10-01T06:12:59.827Z", "2.7.0": "2018-09-26T21:26:10.336Z", "2.6.2": "2018-07-06T01:26:15.381Z", "2.6.1": "2018-06-23T02:19:33.894Z", "2.6.0": "2018-02-02T00:31:27.407Z", "2.5.0": "2018-01-16T02:02:41.148Z", "2.4.0": "2017-12-17T02:37:58.032Z", "2.3.1": "2017-09-18T16:08:27.999Z", "2.3.0": "2017-09-18T05:34:29.844Z", "2.2.0": "2017-08-16T03:39:43.252Z", "2.1.2": "2017-06-14T09:50:12.927Z", "2.1.1": "2017-05-19T11:17:29.750Z", "2.1.0": "2017-05-17T14:07:06.887Z", "2.0.1": "2017-02-26T05:49:09.422Z", "2.0.0": "2017-01-28T20:14:49.070Z", "1.0.0": "2017-01-26T04:15:55.798Z", "4.1.0": "2022-06-27T12:49:42.915Z", "4.2.0": "2022-06-30T02:14:26.325Z", "4.2.1": "2022-06-30T03:33:54.584Z", "5.0.0-alpha.0": "2022-09-02T14:57:31.346Z", "5.0.0-alpha.1": "2023-03-10T09:52:48.675Z", "5.0.0-alpha.2": "2023-07-06T01:56:49.446Z", "5.0.0": "2023-07-11T07:45:44.838Z", "5.0.1": "2023-10-11T02:35:57.043Z", "5.1.0": "2023-12-19T21:12:11.477Z", "5.1.1": "2023-12-21T16:41:39.701Z", "5.1.2": "2023-12-24T05:25:47.611Z", "5.1.3": "2024-01-10T03:34:34.437Z", "5.2.1": "2024-07-17T11:31:10.718Z", "5.2.2": "2025-01-15T05:15:11.778Z", "5.2.3": "2025-01-19T00:07:28.777Z", "5.2.4": "2025-03-24T01:50:30.779Z", "5.2.5": "2025-03-25T10:32:57.666Z", "5.2.6": "2025-04-02T16:15:13.067Z", "5.3.0": "2025-05-04T07:12:25.323Z", "5.3.1": "2025-05-04T08:05:37.207Z", "5.4.0": "2025-05-05T10:54:58.169Z", "5.4.1": "2025-05-30T03:35:35.994Z", "5.5.0": "2025-06-17T08:41:37.332Z", "5.5.1": "2025-06-25T10:56:46.097Z"}, "versions": {"4.0.0": {"name": "eslint-plugin-prettier", "version": "4.0.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "devDependencies": {"@graphql-eslint/eslint-plugin": "^2.0.1", "@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.28.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "graphql": "^15.5.1", "mocha": "^6.0.0", "prettier": "^2.3.0", "vue-eslint-parser": "^6.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "licenseText": "# The MIT License (MIT)\n\nCopyright © 2017 <PERSON><PERSON> and <PERSON>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the “Software”), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n", "_id": "eslint-plugin-prettier@4.0.0", "dist": {"shasum": "8b99d1e4b8b24a762472b4567992023619cb98e0", "size": 15726, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.0.0.tgz", "integrity": "sha512-98MqmCJ7vJodoQK359bqQWaxOE0CS8paAz/GgjaZLyex4TTk3g9HugoO89EqWCrFiOqn9EVvcoo7gZzONCWVwQ=="}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_4.0.0_1630353149738_0.634201211940923"}, "_hasShrinkwrap": false, "publish_time": 1630353149922, "_cnpm_publish_time": 1630353149922, "_cnpmcore_publish_time": "2021-12-13T15:14:27.967Z"}, "3.4.1": {"name": "eslint-plugin-prettier", "version": "3.4.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=5.0.0", "prettier": ">=1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "e53f23b890c9c6e73ba6e58db0d05eb91d962b71", "_id": "eslint-plugin-prettier@3.4.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"shasum": "e9ddb200efb6f3d05ffe83b1665a716af4a387e5", "size": 15993, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.4.1.tgz", "integrity": "sha512-htg25EUYUeIhKHXjOinK4BgCcDwtLHjqaxCDsMy5nbnUMkKFvIhMVCp+5GFUXQ4Nr8lBsPqtGAqBenbpFqAA2g=="}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.4.1_1629496358487_0.9998273944812643"}, "_hasShrinkwrap": false, "publish_time": 1629496358668, "_cnpm_publish_time": 1629496358668, "_cnpmcore_publish_time": "2021-12-13T15:14:28.262Z"}, "3.4.0": {"name": "eslint-plugin-prettier", "version": "3.4.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=5.0.0", "prettier": ">=1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "0813a83512ab52194b4e8da7193daf880a8cbef5", "_id": "eslint-plugin-prettier@3.4.0", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"shasum": "cdbad3bf1dbd2b177e9825737fe63b476a08f0c7", "size": 15598, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.4.0.tgz", "integrity": "sha512-UDK6rJT6INSfcOo545jiaOwB701uAIt2/dR7WnFQoGCVl1/EMqdANBmwUaqqQ45aXprsTGzSa39LI1PyuRBxxw=="}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.4.0_1618448496323_0.34469101917753253"}, "_hasShrinkwrap": false, "publish_time": 1618448496467, "_cnpm_publish_time": 1618448496467, "_cnpmcore_publish_time": "2021-12-13T15:14:28.655Z"}, "3.3.1": {"name": "eslint-plugin-prettier", "version": "3.3.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=5.0.0", "prettier": ">=1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "b0ddb107cc782e1d9e0b9b9c508e53e28545f422", "_id": "eslint-plugin-prettier@3.3.1", "_nodeVersion": "10.22.0", "_npmVersion": "6.14.6", "dist": {"shasum": "7079cfa2497078905011e6f82e8dd8453d1371b7", "size": 15180, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.3.1.tgz", "integrity": "sha512-Rq3jkcFY8RYeQLgk2cCwuc0P7SEFwDravPhsJZOQ5N4YI4DSg50NyqJ/9gdZHzQlHf8MvafSesbNJCcP/FF6pQ=="}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.3.1_1609786094719_0.6678779784675799"}, "_hasShrinkwrap": false, "publish_time": 1609786094880, "_cnpm_publish_time": 1609786094880, "_cnpmcore_publish_time": "2021-12-13T15:14:29.034Z"}, "3.3.0": {"name": "eslint-plugin-prettier", "version": "3.3.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=5.0.0", "prettier": ">=1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "peerDependenciesMeta": {"eslint-plugin-prettier": {"optional": true}}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "0ecf183ec223ea951077897f9b02244861ece52b", "_id": "eslint-plugin-prettier@3.3.0", "_nodeVersion": "10.22.0", "_npmVersion": "6.14.6", "dist": {"shasum": "61e295349a65688ffac0b7808ef0a8244bdd8d40", "size": 15032, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.3.0.tgz", "integrity": "sha512-tMTwO8iUWlSRZIwS9k7/E4vrTsfvsrcM5p1eftyuqWH25nKsz/o6/54I7jwQ/3zobISyC7wMy9ZsFwgTxOcOpQ=="}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.3.0_1607899840895_0.4186756915462224"}, "_hasShrinkwrap": false, "publish_time": 1607899841004, "_cnpm_publish_time": 1607899841004, "_cnpmcore_publish_time": "2021-12-13T15:14:29.410Z"}, "3.2.0": {"name": "eslint-plugin-prettier", "version": "3.2.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=5.0.0", "prettier": ">=1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "bd0ac3bcbf91f83d01b022fd0b99aed71052de15", "_id": "eslint-plugin-prettier@3.2.0", "_nodeVersion": "10.22.0", "_npmVersion": "6.14.6", "dist": {"shasum": "af391b2226fa0e15c96f36c733f6e9035dbd952c", "size": 14968, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.2.0.tgz", "integrity": "sha512-kOUSJnFjAUFKwVxuzy6sA5yyMx6+o9ino4gCdShzBNx4eyFRudWRYKCFolKjoM40PEiuU6Cn7wBLfq3WsGg7qg=="}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.2.0_1606978301436_0.040357193324182195"}, "_hasShrinkwrap": false, "publish_time": 1606978301579, "_cnpm_publish_time": 1606978301579, "_cnpmcore_publish_time": "2021-12-13T15:14:29.753Z"}, "3.1.4": {"name": "eslint-plugin-prettier", "version": "3.1.4", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=5.0.0", "prettier": ">=1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^7.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "f0394c4e7002780cc810111d8e22d9b28ddba82e", "_id": "eslint-plugin-prettier@3.1.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "168ab43154e2ea57db992a2cd097c828171f75c2", "size": 13824, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.4.tgz", "integrity": "sha512-jZDa8z76klRqo+TdGDTFJSavwbnWK2ZpqGKNZ+VvweMW516pDUMmQ2koXvxEE4JhzNvTv+radye/bWGBmA6jmg=="}, "maintainers": [{"email": "<EMAIL>", "name": "b<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "zertosh"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.1.4_1592177017952_0.35508387874391767"}, "_hasShrinkwrap": false, "publish_time": 1592177018075, "_cnpm_publish_time": 1592177018075, "_cnpmcore_publish_time": "2021-12-13T15:14:30.135Z"}, "3.1.3": {"name": "eslint-plugin-prettier", "version": "3.1.3", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^6.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "9e3fde0847da262e0ff2574a4741c339a50c4462", "_id": "eslint-plugin-prettier@3.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ae116a0fc0e598fdae48743a4430903de5b4e6ca", "size": 13326, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.3.tgz", "integrity": "sha512-+HG5jmu/dN3ZV3T6eCD7a4BlAySdN7mLIbJYo0z1cFQuI+r2DiTJEFeF68ots93PsnrMxbzIZ2S/ieX+mkrBeQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "b<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "zertosh"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.1.3_1586740587392_0.12360819020789626"}, "_hasShrinkwrap": false, "publish_time": 1586740587528, "_cnpm_publish_time": 1586740587528, "_cnpmcore_publish_time": "2021-12-13T15:14:30.560Z"}, "3.1.2": {"name": "eslint-plugin-prettier", "version": "3.1.2", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^6.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "licenseText": "# The MIT License (MIT)\n\nCopyright © 2017 <PERSON><PERSON> and <PERSON>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the “Software”), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n", "_id": "eslint-plugin-prettier@3.1.2", "dist": {"shasum": "432e5a667666ab84ce72f945c72f77d996a5c9ba", "size": 12946, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.2.tgz", "integrity": "sha512-GlolCC9y3XZfv3RQfwGew7NnuFDKsfI4lbvRK+PIIo23SFH+LemGs4cKwzAaRa+Mdb+lQO/STaIayno8T5sJJA=="}, "maintainers": [{"email": "<EMAIL>", "name": "b<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "zertosh"}], "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.1.2_1576382246822_0.5756106882615699"}, "_hasShrinkwrap": false, "publish_time": 1576382246924, "_cnpm_publish_time": 1576382246924, "_cnpmcore_publish_time": "2021-12-13T15:14:30.968Z"}, "3.1.1": {"name": "eslint-plugin-prettier", "version": "3.1.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^6.0.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "cb2d4b34b086b81f7b45e4c8c7aac561c73e85cb", "_id": "eslint-plugin-prettier@3.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "507b8562410d02a03f0ddc949c616f877852f2ba", "size": 12219, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.1.tgz", "integrity": "sha512-A+TZuHZ0KU0cnn56/9mfR7/KjUJ9QNVXUhwvRFSR7PGPe0zQR6PTkmyqg1AtUUEOzTqeRsUwyKFh0oVZKVCrtA=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.1.1_1568788222185_0.7287231960041012"}, "_hasShrinkwrap": false, "publish_time": 1568788222289, "_cnpm_publish_time": 1568788222289, "_cnpmcore_publish_time": "2021-12-13T15:14:31.401Z"}, "3.1.0": {"name": "eslint-plugin-prettier", "version": "3.1.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^5.6.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^6.0.0", "prettier": "^1.15.3", "vue-eslint-parser": "^6.0.0"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "bb521d0487cf29ac6eefb9c8bbd83b1150af1530", "_id": "eslint-plugin-prettier@3.1.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "dist": {"shasum": "8695188f95daa93b0dc54b249347ca3b79c4686d", "size": 11384, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.0.tgz", "integrity": "sha512-XWX2yVuwVNLOUhQijAkXz+rMPPoCr7WFiAl8ig6I7Xn+pPVhDhzg4DxHpmbeb0iqjO9UronEA3Tb09ChnFVHHA=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.1.0_1557586544865_0.8591264872936486"}, "_hasShrinkwrap": false, "publish_time": 1557586545018, "_cnpm_publish_time": 1557586545018, "_cnpmcore_publish_time": "2021-12-13T15:14:31.901Z"}, "3.0.1": {"name": "eslint-plugin-prettier", "version": "3.0.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^5.6.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^3.1.0", "eslint-plugin-eslint-plugin": "^2.0.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-self": "^1.1.0", "mocha": "^5.2.0", "prettier": "^1.15.3", "vue-eslint-parser": "^4.0.2"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "d4231c2ecf22dfc30ed09407b682063645e903dc", "_id": "eslint-plugin-prettier@3.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.4.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "19d521e3981f69dd6d14f64aec8c6a6ac6eb0b0d", "size": 9605, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.0.1.tgz", "integrity": "sha512-/PMttrarPAY78PLvV3xfWibMOdMDl57hmlQ2XqFeA37wd+CJ7WSxV7txqjVPHi/AAFKd2lX0ZqfsOc/i5yFCSQ=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.0.1_1545981552695_0.7279532203291668"}, "_hasShrinkwrap": false, "publish_time": 1545981552896, "_cnpm_publish_time": 1545981552896, "_cnpmcore_publish_time": "2021-12-13T15:14:32.448Z"}, "3.0.0": {"name": "eslint-plugin-prettier", "version": "3.0.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^5.6.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^3.1.0", "eslint-plugin-eslint-plugin": "^1.4.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-self": "^1.1.0", "mocha": "^5.2.0", "prettier": "^1.13.0", "vue-eslint-parser": "^3.2.2"}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "68271f8a8fc0e219716b08d1a9ba759fc068839a", "_id": "eslint-plugin-prettier@3.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f6b823e065f8c36529918cdb766d7a0e975ec30c", "size": 8449, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.0.0.tgz", "integrity": "sha512-4g11opzhqq/8+AMmo5Vc2Gn7z9alZ4JqrbZ+D4i8KlSyxeQhZHlmIrY8U9Akf514MoEhogPa87Jgkq87aZ2Ohw=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_3.0.0_1538374379599_0.506245238150622"}, "_hasShrinkwrap": false, "publish_time": 1538374379827, "_cnpm_publish_time": 1538374379827, "_cnpmcore_publish_time": "2021-12-13T15:14:32.898Z"}, "2.7.0": {"name": "eslint-plugin-prettier", "version": "2.7.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"@not-an-aardvark/node-release-script": "^0.1.0", "eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.13.0", "semver": "^5.3.0", "vue-eslint-parser": "^2.0.2"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "869f56d5946d3d55a960bee2ab282e69cb15b9d6", "_id": "eslint-plugin-prettier@2.7.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b4312dcf2c1d965379d7f9d5b5f8aaadc6a45904", "size": 10353, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.7.0.tgz", "integrity": "sha512-CStQYJgALoQBw3FsBzH0VOVDRnJ/ZimUlpLm226U8qgqYJfPOY/CPK6wyRInMxh73HSKg5wyRwdS4BVYYHwokA=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_2.7.0_1537997170196_0.756855699436153"}, "_hasShrinkwrap": false, "publish_time": 1537997170336, "_cnpm_publish_time": 1537997170336, "_cnpmcore_publish_time": "2021-12-13T15:14:33.343Z"}, "2.6.2": {"name": "eslint-plugin-prettier", "version": "2.6.2", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.10.2", "semver": "^5.3.0", "vue-eslint-parser": "^2.0.2"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "bc337809caf860130aea457fb5d4a298eb61aa39", "_id": "eslint-plugin-prettier@2.6.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "71998c60aedfa2141f7bfcbf9d1c459bf98b4fad", "size": 9587, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.6.2.tgz", "integrity": "sha512-tGek5clmW5swrAx1mdPYM8oThrBE83ePh7LeseZHBWfHVGrHPhKn7Y5zgRMbU/9D5Td9K4CEmUPjGxA7iw98Og=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_2.6.2_1530840375289_0.5655292665140659"}, "_hasShrinkwrap": false, "publish_time": 1530840375381, "_cnpm_publish_time": 1530840375381, "_cnpmcore_publish_time": "2021-12-13T15:14:33.784Z"}, "2.6.1": {"name": "eslint-plugin-prettier", "version": "2.6.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.10.2", "semver": "^5.3.0", "vue-eslint-parser": "^2.0.2"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "1c41c41436646ea444c749287651eac8e9b677e5", "_id": "eslint-plugin-prettier@2.6.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "de902b4a66b7bca24296429a59a1cc04020ccbbd", "size": 9230, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.6.1.tgz", "integrity": "sha512-wNZ2z0oVCWnf+3BSI7roS+z4gGu2AwcPKUek+SlLZMZg+X0KbZLsB2knul7fd0K3iuIp402HIYzm4f2+OyfXxA=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_2.6.1_1529720373793_0.8582188493799761"}, "_hasShrinkwrap": false, "publish_time": 1529720373894, "_cnpm_publish_time": 1529720373894, "_cnpmcore_publish_time": "2021-12-13T15:14:34.247Z"}, "2.6.0": {"name": "eslint-plugin-prettier", "version": "2.6.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.10.2", "semver": "^5.3.0", "vue-eslint-parser": "^2.0.2"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "d772dfaf5b0c810565653d45194061eb11ae2b09", "_id": "eslint-plugin-prettier@2.6.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "33e4e228bdb06142d03c560ce04ec23f6c767dd7", "size": 9174, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.6.0.tgz", "integrity": "sha512-floiaI4F7hRkTrFe8V2ItOK97QYrX75DjmdzmVITZoAP6Cn06oEDPQRsO6MlHEP/u2SxI3xQ52Kpjw6j5WGfeQ=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.6.0.tgz_1517531486471_0.21083621098659933"}, "directories": {}, "publish_time": 1517531487407, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517531487407, "_cnpmcore_publish_time": "2021-12-13T15:14:34.735Z"}, "2.5.0": {"name": "eslint-plugin-prettier", "version": "2.5.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.10.2", "semver": "^5.3.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "eba622e1597267c230f8a95213ebb6f2d87383a7", "_id": "eslint-plugin-prettier@2.5.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "39a91dd7528eaf19cd42c0ee3f2c1f684606a05f", "size": 8658, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.5.0.tgz", "integrity": "sha512-L06bewYpt2Wb8Uk7os8f/0cL5DjddL38t1M/nOpjw5MqVFBn1RIIBBE6tfr37lHUH7AvAubZsvu/bDmNl4RBKQ=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.5.0.tgz_1516068160279_0.049084554659202695"}, "directories": {}, "publish_time": 1516068161148, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516068161148, "_cnpmcore_publish_time": "2021-12-13T15:14:35.250Z"}, "2.4.0": {"name": "eslint-plugin-prettier", "version": "2.4.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.6.1", "semver": "^5.3.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "a7288684377348e8c5de35e77f8776729363f81c", "_id": "eslint-plugin-prettier@2.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.2.1", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "85cab0775c6d5e3344ef01e78d960f166fb93aae", "size": 8482, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.4.0.tgz", "integrity": "sha512-P0EohHM1MwL36GX5l1TOEYyt/5d7hcxrX3CqCjibTN3dH7VCAy2kjsC/WB6dUHnpB4mFkZq1Ndfh2DYQ2QMEGQ=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.4.0.tgz_1513478277115_0.3524991925805807"}, "directories": {}, "publish_time": 1513478278032, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513478278032, "_cnpmcore_publish_time": "2021-12-13T15:14:35.745Z"}, "2.3.1": {"name": "eslint-plugin-prettier", "version": "2.3.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.6.1", "semver": "^5.3.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "62085cc01731847d66456e089a7529d25dcb1254", "_id": "eslint-plugin-prettier@2.3.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e7a746c67e716f335274b88295a9ead9f544e44d", "size": 7907, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.3.1.tgz", "integrity": "sha512-AV8shBlGN9tRZffj5v/f4uiQWlP3qiQ+lh+BhTqRLuKSyczx+HRWVkVZaf7dOmguxghAH1wftnou/JUEEChhGg=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.3.1.tgz_1505750907024_0.5389670932199806"}, "directories": {}, "publish_time": 1505750907999, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505750907999, "_cnpmcore_publish_time": "2021-12-13T15:14:36.332Z"}, "2.3.0": {"name": "eslint-plugin-prettier", "version": "2.3.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^21.0.0"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "eslint-plugin-self": "^1.0.1", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.6.1", "semver": "^5.3.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "cdd1a83e69ed0e50eb9c591bc356a8ed13793a11", "_id": "eslint-plugin-prettier@2.3.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "520a6e57c178c608ed04b75c79a4e7960a91bd2f", "size": 7817, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.3.0.tgz", "integrity": "sha512-RMJbh/z6LJBPWybXUXfHNzMljkje1oY37jhJ+Z+nYLQgnmpF4dHlRHl5RbuAaY1P5iXBRFHtQMGnH6CmzfkXHg=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.3.0.tgz_1505712868925_0.7421463127247989"}, "directories": {}, "publish_time": 1505712869844, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505712869844, "_cnpmcore_publish_time": "2021-12-13T15:14:36.840Z"}, "2.2.0": {"name": "eslint-plugin-prettier", "version": "2.2.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^20.0.1"}, "peerDependencies": {"prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.3.1", "semver": "^5.3.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "540425438bb4134a2623d28b2af84e9ff6eac7b0", "_id": "eslint-plugin-prettier@2.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f2837ad063903d73c621e7188fb3d41486434088", "size": 7340, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.2.0.tgz", "integrity": "sha512-LmIOP99A+BMeMYZoDeU196cV7FNIHJEjjWLARdz6JSTYmGK+HTEAVbJukPr/ZVOKPs5Hf7b10aXwSbty6Gqqsw=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.2.0.tgz_1502854783178_0.4994309451431036"}, "directories": {}, "publish_time": 1502854783252, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502854783252, "_cnpmcore_publish_time": "2021-12-13T15:14:37.360Z"}, "2.1.2": {"name": "eslint-plugin-prettier", "version": "2.1.2", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^20.0.1"}, "peerDependencies": {"eslint": ">=3.14.1", "prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "mocha": "^3.1.2", "moment": "^2.18.1", "prettier": "^1.3.1", "semver": "^5.3.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "6c351c32e664f5b5811cef45cd13b3227ff282f3", "_id": "eslint-plugin-prettier@2.1.2", "_shasum": "4b90f4ee7f92bfbe2e926017e1ca40eb628965ea", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4b90f4ee7f92bfbe2e926017e1ca40eb628965ea", "size": 7032, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.1.2.tgz", "integrity": "sha512-KB7sYJAffWWNuY2CX/QclyzrFv2U3UTRQI6HPoXym1qKSqT4mtLHVmBQkS1Mb5mWHblG+EqE5RVsbwhkRiMdmw=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier-2.1.2.tgz_1497433812835_0.9184185413178056"}, "directories": {}, "publish_time": 1497433812927, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497433812927, "_cnpmcore_publish_time": "2021-12-13T15:14:37.914Z"}, "2.1.1": {"name": "eslint-plugin-prettier", "version": "2.1.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^20.0.1"}, "peerDependencies": {"eslint": "^3.14.1", "prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "mocha": "^3.1.2", "prettier": "^1.3.1"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "56873bf2f943307fa17e8269785ecd1dbaf7e1ef", "_id": "eslint-plugin-prettier@2.1.1", "_shasum": "2fb7e2ab961f2b61d2c8cf91bc17716ca8c53868", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "dist": {"shasum": "2fb7e2ab961f2b61d2c8cf91bc17716ca8c53868", "size": 7797, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.1.1.tgz", "integrity": "sha512-LKY3tbrIgFYYILhn8+BnuovHCwwWcR5TDNDJIH8HnrFf4HiJaKQd4WNhujR4ugaCzisBU+mSLbRj2e5tTr56iA=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/eslint-plugin-prettier-2.1.1.tgz_1495192649056_0.5792952182237059"}, "directories": {}, "publish_time": 1495192649750, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495192649750, "_cnpmcore_publish_time": "2021-12-13T15:14:38.470Z"}, "2.1.0": {"name": "eslint-plugin-prettier", "version": "2.1.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "files": ["eslint-plugin-prettier.js"], "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"fast-diff": "^1.1.1", "jest-docblock": "^20.0.1"}, "peerDependencies": {"eslint": "^3.14.1", "prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.7.1", "eslint-plugin-node": "^4.2.2", "mocha": "^3.1.2", "prettier": "^1.3.1"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "466268215adeb475a09f1a0fa1879c8bfbdfffd8", "_id": "eslint-plugin-prettier@2.1.0", "_shasum": "bab2aaeba26eadedb1a92beef30cac4b4755fea4", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "dist": {"shasum": "bab2aaeba26eadedb1a92beef30cac4b4755fea4", "size": 7426, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.1.0.tgz", "integrity": "sha512-+IlqRKwr2cjP0iEXGz8KhKC2QBelY2loJ7cwPel1BOVs120lBwhis5uU6oK5ZlhxeIMHkJO/F1Kj178Gtafsug=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/eslint-plugin-prettier-2.1.0.tgz_1495030025770_0.4487081023398787"}, "directories": {}, "publish_time": 1495030026887, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495030026887, "_cnpmcore_publish_time": "2021-12-13T15:14:39.022Z"}, "2.0.1": {"name": "eslint-plugin-prettier", "version": "2.0.1", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "main": "lib/index.js", "scripts": {"lint": "eslint .eslintrc.js lib/ tests/ --ignore-pattern !.eslintrc.js", "test": "npm run lint && mocha tests --recursive"}, "repository": {"type": "git", "url": "git+https://github.com/not-an-aardvark/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/not-an-aardvark/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/not-an-aardvark/eslint-plugin-prettier#readme", "dependencies": {"requireindex": "~1.1.0"}, "peerDependencies": {"eslint": "^3.14.1", "prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-config-prettier": "^1.3.0", "eslint-plugin-eslint-plugin": "^0.2.1", "eslint-plugin-node": "^3.0.5", "mocha": "^3.1.2", "prettier": "^0.18.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "3fefbf3fc1f7b4507c3bb050a24eeda9c6231c4b", "_id": "eslint-plugin-prettier@2.0.1", "_shasum": "2ae1216cf053dd728360ca8560bf1aabc8af3fa9", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2ae1216cf053dd728360ca8560bf1aabc8af3fa9", "size": 4651, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.0.1.tgz", "integrity": "sha512-xW7ooqEUS4xyS0Wq9Gdqn1DFiHBkfH8hwYXaGNYAXAunhmDfOALi5bQ+JA3eR1v9MfSXXaIiTLxeFU3zqS8+MA=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/eslint-plugin-prettier-2.0.1.tgz_1488088147269_0.7652223000768572"}, "directories": {}, "publish_time": 1488088149422, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488088149422, "_cnpmcore_publish_time": "2021-12-13T15:14:39.520Z"}, "2.0.0": {"name": "eslint-plugin-prettier", "version": "2.0.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "main": "lib/index.js", "scripts": {"lint": "eslint .eslintrc.js lib/ tests/ --ignore-pattern !.eslintrc.js", "test": "npm run lint && mocha tests --recursive"}, "repository": {"type": "git", "url": "git+https://github.com/not-an-aardvark/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/not-an-aardvark/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/not-an-aardvark/eslint-plugin-prettier#readme", "dependencies": {"requireindex": "~1.1.0"}, "peerDependencies": {"eslint": "^3.14.1", "prettier": ">= 0.11.0"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-plugin-eslint-plugin": "^0.2.1", "eslint-plugin-node": "^3.0.5", "mocha": "^3.1.2", "prettier": ">= 0.11.0"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "090e1c5bd318faec627bfebdf3cceee5308c40f8", "_id": "eslint-plugin-prettier@2.0.0", "_shasum": "8d117dcbe1e9fdafaf593166480c1ad79b8d3125", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8d117dcbe1e9fdafaf593166480c1ad79b8d3125", "size": 4096, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-2.0.0.tgz", "integrity": "sha512-X0dvQSyZyRnRCb3giUh4ldif9Gkje8tkUMAWEArnlcdoZmIWAwmwAP23Mj2APXPgxP/P9gWMrxMHxeQ3y6EkIg=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/eslint-plugin-prettier-2.0.0.tgz_1485634488413_0.14870760357007384"}, "directories": {}, "publish_time": 1485634489070, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485634489070, "_cnpmcore_publish_time": "2021-12-13T15:14:40.114Z"}, "1.0.0": {"name": "eslint-plugin-prettier", "version": "1.0.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "main": "lib/index.js", "scripts": {"lint": "eslint .eslintrc.js lib/ tests/ --ignore-pattern !.eslintrc.js", "test": "npm run lint && mocha tests --recursive"}, "dependencies": {"prettier": "^0.11.0", "requireindex": "~1.1.0"}, "peerDependencies": {"eslint": "^3.14.1"}, "devDependencies": {"eslint": "^3.14.1", "eslint-config-not-an-aardvark": "^2.0.0", "eslint-plugin-eslint-plugin": "^0.2.1", "eslint-plugin-node": "^3.0.5", "mocha": "^3.1.2"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "gitHead": "b68bf1233b677f72c57b056e235f719b29372bec", "_id": "eslint-plugin-prettier@1.0.0", "_shasum": "6d549c6156ddf0eff7f7fc875bebe7a15587414e", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6d549c6156ddf0eff7f7fc875bebe7a15587414e", "size": 3295, "noattachment": false, "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-1.0.0.tgz", "integrity": "sha512-gxA+VkC8CGF5q5yhVJqoqCkOCmOk7+KD4L2qUbf4vILMgoROxUY6TafVJzsu2nuGjwcDBEM1PpGkYj4BNHF/Jw=="}, "maintainers": [{"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/eslint-plugin-prettier-1.0.0.tgz_1485404153796_0.6774270725436509"}, "directories": {}, "publish_time": 1485404155798, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485404155798, "_cnpmcore_publish_time": "2021-12-13T15:14:40.803Z"}, "4.1.0": {"name": "eslint-plugin-prettier", "version": "4.1.0", "description": "Runs prettier as an eslint rule", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "author": {"name": "<PERSON>"}, "main": "eslint-plugin-prettier.js", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "format": "yarn run prettier '**/*.{js,json,md,yml}' --write && yarn run lint --fix", "generate-release": "node-release-script"}, "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "devDependencies": {"@graphql-eslint/eslint-plugin": "^2.5.0", "@not-an-aardvark/node-release-script": "^0.1.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-not-an-aardvark": "^2.1.0", "eslint-config-prettier": "^8.5.0", "eslint-mdx": "^1.17.0", "eslint-plugin-eslint-plugin": "^4.3.0", "eslint-plugin-mdx": "^1.17.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "link:.", "eslint-plugin-self": "^1.2.1", "graphql": "^16.5.0", "mocha": "^9.2.2", "prettier": "^2.7.1", "vue-eslint-parser": "^8.3.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "resolutions": {"@babel/traverse": "^7.18.5"}, "engines": {"node": ">=12.0.0"}, "license": "MIT", "gitHead": "551dd0e29840f189df71bfc9bd3fd5aa74f2b8f1", "_id": "eslint-plugin-prettier@4.1.0", "_nodeVersion": "16.15.1", "_npmVersion": "8.13.1", "dist": {"integrity": "sha512-A3AXIEfTnq3D5qDFjWJdQ9c4BLhw/TqhSR+6+SVaoPJBAWciFEuJiNQh275OnjRrAi7yssZzuWBRw66VG2g6UA==", "shasum": "1cd4b3fadf3b3cdb30b1874b55e7f93f85eb43ad", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.1.0.tgz", "fileCount": 4, "unpackedSize": 20162, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF7v2LmzOnobCOBY+aZue6qqSPoeDI6KrcpdNToOPDTqAiEA1m107dURfg3JVh7Y7ZNNAgk2RVQ2B3+gyU/j8+WikiU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuadmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmre6A//WkKTfvpII27EoymT4jDwEKB4Nrss1PEKh7QPwMcCnFq/2WH2\r\nw9WT5y/0Nd7YrPpW9ylzG9lekacluZ7BUM+vM9RsZjhGXnDGLyLAJJ8PkOG0\r\njukIzE3mYdJFwzOjqe0lfrHUFk24Rf4ameNFuQ/13sLTCGsoVG7JiTk93HNJ\r\nHqWcwfikpBVwIlQCxRrXnjEBmXUOXWSkpklRUuQZvV9dWkGWs2+AVielhAb4\r\nyzvA/KXsg50nOSOx0+wbQcYLNxl19P7ZrX48mKVTHsVZuHJBhG7i4kyplr2g\r\naWs95n49nQa7Xrgs7HtGE/8WsgkQv9CGeyjBYBcso0mxAvJDQbUPjxc63NE6\r\nDb/v1bBjAidy08OOst7TitI4EFTGblfEHTjXKDg0m180gFxyS8kL/gRuFNaF\r\nZzqZuBRpUpM0ZupJxw+pDGoryYvOufpQqKH6q4HasL9tXljq8pnjx8jWRSYp\r\n6LzP2f+/Z+qb6f35SmP9/4DPRul6yiwzflhWHffnPduVYDNk2Bu/iiOuj0NC\r\n6aiBMpxlsme4L/E04zAYbLNLAPBaeMuFguYR6xUicqjI7W3BakwU132AogDt\r\nz4Al/09VI4eIVGgPHB04un4MX7XSp15r3mzgIjs852V6UqCkgJMCO5hRQDNY\r\ncDzEAcOb16OdF5e80plwVO1by246Tdjy0e4=\r\n=iXPy\r\n-----END PGP SIGNATURE-----\r\n", "size": 6842}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_4.1.0_1656334182777_0.2336349711889898"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-27T13:15:45.034Z"}, "4.2.0": {"name": "eslint-plugin-prettier", "version": "4.2.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "license": "MIT", "engines": {"node": ">=12.0.0"}, "main": "eslint-plugin-prettier.js", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "scripts": {"format": "yarn prettier '**/*.{js,json,md,yml}' --write && yarn lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prepare": "patch-package && simple-git-hooks && yarn-deduplicate --strategy fewer || exit 0", "prerelease": "yarn format && yarn test", "release": "changeset publish", "test": "yarn lint && mocha"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "devDependencies": {"@1stg/common-config": "~3.0.0", "@1stg/eslint-config": "~3.0.0", "@changesets/changelog-github": "^0.4.5", "@changesets/cli": "^2.23.0", "@graphql-eslint/eslint-plugin": "^2.5.0", "@ota-meshi/eslint-plugin-svelte": "^0.34.1", "@typescript-eslint/parser": "^5.29.0", "eslint-config-prettier": "^8.5.0", "eslint-mdx": "^1.17.0", "eslint-plugin-eslint-plugin": "^4.3.0", "eslint-plugin-mdx": "^1.17.0", "eslint-plugin-self": "^1.2.1", "eslint-plugin-utils": "^0.1.0", "graphql": "^16.5.0", "mocha": "^9.2.2", "patch-package": "^6.4.7", "prettier": "^2.7.1", "svelte": "^3.48.0", "vue-eslint-parser": "^8.3.0"}, "resolutions": {"@babel/traverse": "^7.18.5", "eslint-plugin-prettier": "link:."}, "packageManager": "yarn@1.22.19", "gitHead": "1430fd1af7c14d32af6d6b4e606dbc3e59f421c3", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@4.2.0", "_nodeVersion": "16.15.1", "_npmVersion": "8.13.1", "dist": {"integrity": "sha512-w+EYWwMltYqlsirUG2LXROmC8cBIUSEpR4s/Oft8wX8Xm99FTfU+M+ehv47ZQbYPqD9ZAuCtZI6IuWw3Q004dw==", "shasum": "25b178fecca21c120612d241d1abb11cfe405675", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.0.tgz", "fileCount": 4, "unpackedSize": 22021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBM/yWHXR4bsbcxegwKAH2oUEChErlFYSBHa7oprIV9KAiALCDMx2XT/j3Idu+YJARtVVHIdF1kmmcsR5TClyPHw2Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivQcCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolJA/9GLcdw146Fs5jVJQ2afnn56onEnM8mi4AFu4eksocBq7k9Ajv\r\nfFB0vQDH8fpXNwgsf6vcXIdyBT9L3Hw5qVxYLFozuW0UY7JzRIxVXqIKaP3w\r\npQErPlJzYm71Y11hUWcoG6XTl95VHxF+I4y1r801o+iypF8cb0WTm5gnt9B3\r\nDQJzkGKDTISnHhFfF7ltntvow7cteiXTlLRrZAsjc69SCZWe+6FRme+tz1Io\r\ny5HgN+hWoZYq/RTfT+kdVy/vH/gdPrYf7Z6UPVYKbhpoXGAYQhUEU8hf4IES\r\nYJaa6VA+RLwXvxoLTaAN0hf2+DM0DY8jrqADhBMC03nPETbbhLqmEFKQtfq+\r\nu8P5g/KmYdzp/lpgmpNEhrd6RmoGZukMZGWu7ljY1T0DqETSDS+j/uaaBD6P\r\n46Jh6a2XF5Wb6HXb3edr+mUnF1TmD8movNwbgpakhvS1Rft81RB1x3qbHGlM\r\n3bwC7mbl89jRVZNwB3Cr1FPhDgYugPerznOZ+3GR//dU7giWcbL+iu5Sczqx\r\nJHXN5cB05HeXaqbuf0+dfvHodLFQmIvs8f6SmKVtkSMeFAFDg0XZkhN+vlij\r\nk1HN69KpiVdc99ORILUpE3qMoaqKmCJi2Z2xOwVPaOVzpHmqspWUFZcUzuhU\r\nJbPy6010cbqCzaBF1zj4b9SPV9FTLnF0FmI=\r\n=q5uI\r\n-----END PGP SIGNATURE-----\r\n", "size": 7412}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_4.2.0_1656555266179_0.305063564698292"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-30T02:14:31.906Z"}, "4.2.1": {"name": "eslint-plugin-prettier", "version": "4.2.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "license": "MIT", "engines": {"node": ">=12.0.0"}, "main": "eslint-plugin-prettier.js", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "scripts": {"format": "yarn prettier '**/*.{js,json,md,yml}' --write && yarn lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prepare": "patch-package && simple-git-hooks && yarn-deduplicate --strategy fewer || exit 0", "prerelease": "yarn format && yarn test", "release": "changeset publish", "test": "yarn lint && mocha"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "devDependencies": {"@1stg/common-config": "~3.0.0", "@1stg/eslint-config": "~3.0.0", "@changesets/changelog-github": "^0.4.5", "@changesets/cli": "^2.23.0", "@graphql-eslint/eslint-plugin": "^2.5.0", "@ota-meshi/eslint-plugin-svelte": "^0.34.1", "@typescript-eslint/parser": "^5.29.0", "eslint-config-prettier": "^8.5.0", "eslint-mdx": "^1.17.0", "eslint-plugin-eslint-plugin": "^4.3.0", "eslint-plugin-mdx": "^1.17.0", "eslint-plugin-self": "^1.2.1", "eslint-plugin-utils": "^0.1.0", "graphql": "^16.5.0", "mocha": "^9.2.2", "patch-package": "^6.4.7", "svelte": "^3.48.0", "vue-eslint-parser": "^8.3.0"}, "resolutions": {"@babel/traverse": "^7.18.5", "eslint-plugin-prettier": "link:.", "prettier": "^2.7.1"}, "packageManager": "yarn@1.22.19", "gitHead": "19efb8a7f20e57c468d0fcc91f5396e4f850750e", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@4.2.1", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==", "shasum": "651cbb88b1dab98bfd42f017a12fa6b2d993f94b", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz", "fileCount": 5, "unpackedSize": 58284, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtrlL1OY1oQzVXOd3L1GG2eGV1Q7MLMXv7vYba0/2HXAIhALXqI4+zvGOqAit+BXwWqCOqvHbHOhkgPJM0QBaMcG65"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivRmiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQCQ/+JTeF0aRwbbsgCyj6UGHFNkem5ON5NAouN1BzdK+XvTi27g9e\r\nZES/8tgPp++0xi6hkoxfCUec/W480aDh6DZlRTITV+ll2Z1Pvh1+pq11ejLO\r\n39f6kxumBotDgmQa/23vfTI0I/OODz0uGFbfDkThDrNyzffeIYlHJvEAT9N+\r\nxFXMnn3UKqy88/gq5pRofF4N5IoQxx3MDJwmf210KfnTMnnwQ6cP4pBuupmy\r\n6oxmj2vPvSGHLV7IkP5sAl7kKm5CQD0lOtNsw6SbWggCUayvSvMcYVBBZfju\r\n060QdMpkSFkYIFGFA4El2R66SKHyK1WpBFHicKn3iR1Hb9HIrBZidDHu68fh\r\niNiDZySh+uFLTQhyuhOWy6+PdwvfUk3SFJ4EMz1hct4L20JDB98chsDxz9oL\r\nXB5q5LYKi0Fc30pGatNz3Itxevl4zAWRKw1veDR13jImsXASUyJS2idNNUQt\r\n2m8B906Xc5DsYIwRqKdhbe6NyEQKTa7plwnsga1VCSSO2tFmpMdEoLZ3XU31\r\nL9YNKInhfhhlc6Ys4xZOZuADJwFkVqNEl6nkr4G55tOqBB8xoTtQkqxSXSlD\r\nHQisy+GYm1EMhQOjzKogzJaZ3hflZF3COrzrqYf4RYtvmeCCR7hgJDYuEnfc\r\nPqm59tpR6qn1fDkzKxRPKWzLef3qwJn6msc=\r\n=Kamx\r\n-----END PGP SIGNATURE-----\r\n", "size": 17450}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_4.2.1_1656560034385_0.5545004389734378"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-30T03:34:11.647Z"}, "5.0.0-alpha.0": {"name": "eslint-plugin-prettier", "version": "5.0.0-alpha.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "yarn@1.22.19", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "scripts": {"format": "yarn prettier '**/*.{js,json,md,yml}' --write && yarn lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prepare": "simple-git-hooks && yarn-deduplicate --strategy fewer || exit 0", "prerelease": "yarn format && yarn test", "release": "changeset publish", "test": "yarn lint && mocha"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "devDependencies": {"@1stg/common-config": "^7.1.1", "@changesets/changelog-github": "^0.4.6", "@changesets/cli": "^2.24.4", "@graphql-eslint/eslint-plugin": "^3.10.7", "@types/eslint": "^8.4.6", "@types/prettier": "^2.7.0", "@types/prettier-linter-helpers": "^1.0.1", "@typescript-eslint/parser": "^5.36.1", "eslint-config-prettier": "^8.5.0", "eslint-mdx": "^2.0.2", "eslint-plugin-eslint-plugin": "^5.0.6", "eslint-plugin-mdx": "^2.0.2", "eslint-plugin-self": "^1.2.1", "eslint-plugin-svelte": "^2.7.0", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.6.0", "mocha": "^10.0.0", "svelte": "^3.49.0", "vue-eslint-parser": "^9.0.3", "yarn-deduplicate": "^6.0.0"}, "resolutions": {"eslint-plugin-prettier": "link:.", "prettier": "^3.0.0-alpha.0"}, "readmeFilename": "README.md", "gitHead": "ba490d76f6e41001cc3ed5d6afc3a02908ea3fbf", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.0.0-alpha.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-ZFQYCs/SGj4eVRtBGdG+ZNcV0N8QJstm03SQL8e0I/A/5VGJfH0kX88shUv5cHWFN6xTuISGvAG6XVCYUT1YVw==", "shasum": "8a4b4d511db39dc7830cc8ee83967f8ba3cf8577", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 24480, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICE7BjHnpIIWtI6sbgMzjRFhlcx71Y+MS99DlmxxsUowAiBa90TGPxZtGd2vhnXZkggD7Rkyx8jAzbDK6R47Hlrv2w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEhnbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKSw/+KjwqeQad6OWgG+eipLSQxVVevQKYhyFFHsfm7QhGUefvrYeZ\r\nUt18qRX8oKsJF9allWI6wYzco16T/I93JwnY2yv+TUjR3BQ/ZYrriuCytrvJ\r\n/huf2lknfw7IBbIyHhN0u7KLfBM+UZu4+UTchw9JC1qOptkLXPIuAIAykgiD\r\nLVJC79VFGJALZWCz8qbdT8EgvBeMmZnF3Tif96JFmrWmw04CNU2OO6xKA2gX\r\niQr/T+3B1kpjJLJ/1h3c5fAId6v9X7SEx6gudRD5/q0DgbZ1LKlwsfMKEBNR\r\nM2cLgzYXwcn6yckdJKRVB0fZHeBT8SWL8eZX7KzJthso/OYzQQphI2pW3hFP\r\nf1hDAk9tZoEPSBu4wHCcQbbdoDlzYTI6KsmHhxCjJZm3I7oBPs+Tb/OS0biN\r\nczB/hClawITeGz3V623CsxuEgN5Bs/IvW7K8Pi+l/yhSGTwD3/vc3hKcwMpV\r\nyPmRO7WqjHY0/JyDnkw7eEULreC2KV5WnL1mu3Zlr0FfqoUxdl0wKXHAiXCm\r\nJf/f67gGgLA3DeFzp8JFJaCzv1TGwS8H4e6fhOtvVIgGlr4WAg6LXDuSG2Vj\r\nd7N5ihmknHWaqlcz6vpFe9HgWpOa0XezQTk5Vaf1G2JhCv1nDKlSotWabNYn\r\nG9y6T8wuLnQ/KPQJU9H8aWzr+R1aVxP3604=\r\n=RgRs\r\n-----END PGP SIGNATURE-----\r\n", "size": 8213}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.0.0-alpha.0_1662130651145_0.42679767923096223"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-02T15:01:12.401Z"}, "5.0.0-alpha.1": {"name": "eslint-plugin-prettier", "version": "5.0.0-alpha.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "yarn@1.22.19", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "scripts": {"format": "yarn prettier '**/*.{js,json,md,yml}' --write && yarn lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prepare": "simple-git-hooks && yarn-deduplicate --strategy fewer || exit 0", "prerelease": "yarn format && yarn test", "release": "changeset publish", "test": "yarn lint && mocha"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "devDependencies": {"@1stg/common-config": "^7.1.1", "@changesets/changelog-github": "^0.4.6", "@changesets/cli": "^2.24.4", "@graphql-eslint/eslint-plugin": "^3.10.7", "@types/eslint": "^8.4.6", "@types/prettier": "^2.7.0", "@types/prettier-linter-helpers": "^1.0.1", "@typescript-eslint/parser": "^5.36.1", "eslint-config-prettier": "^8.5.0", "eslint-mdx": "^2.0.2", "eslint-plugin-eslint-plugin": "^5.0.6", "eslint-plugin-mdx": "^2.0.2", "eslint-plugin-self": "^1.2.1", "eslint-plugin-svelte": "^2.7.0", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.6.0", "mocha": "^10.0.0", "svelte": "^3.49.0", "vue-eslint-parser": "^9.0.3", "yarn-deduplicate": "^6.0.0"}, "resolutions": {"eslint-plugin-prettier": "link:.", "prettier": "^3.0.0-alpha.0"}, "readmeFilename": "README.md", "gitHead": "00449dfd152dbdd5a0a52426bb538dddcf702328", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.0.0-alpha.1", "_nodeVersion": "18.12.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-VB20LzLKsHC19yRtxyr73OFJexGaIm8dkODwtUB5qwFrqRhswr/J624dMxCSBe6Dyx3993dbm2jr+rwJ9yoPhA==", "shasum": "fc81f9041497b2ee79b777a3ae52006622624097", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 24505, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCphCs00NdcWRVH/vKnY9/39yrXn1BQcN656ESM7ROOqAIhAMSLSY/1t8zPpplFWmaGskG0BQjUOuq70Xd0dRRZ6+cz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCv3wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTBg//RN5IE9/BLu7KHSC5ZZLHB6aRhJjE2k3zhY7019INELQsZQPl\r\n+RnnSMRHucpVgF/QB5PIe0FoVGKAejfBxsQFMMqeXIigrmmPiCYdQGHtw5lB\r\ndn6bn2+q8lx+hLXvdyky2IwWn23NYfVXN/7n7MIxgQqX7YYSnYGE76S6zxAc\r\n6ECZSMl4JDsNFEXZ5sQb7NnWfptN8+bwZR9vV4nK6d3YPqmMaRJOv6rs4sbw\r\n+SPedjq0kztIFO0AvEDYjs2qZ0O3BGU/59U8Hb0uz9auQr6h443cgCRQKww7\r\nb3nZSLKl4MDu+8lBR+VxUnsIqOrYQx5lfD3gwxv0A/Hk6ZHIUmUCW4yc8r9y\r\n6pyaw/tKYnf/wjr6wNgw5wiIB7Ks62fWIg6F5GvDhDULJ0fU3mJ5f26jKRRo\r\nNu2hjy4lTnhr90dJUeGJIOlgMxx5m9EE6MzmklE/tr+cb1E7P6hSwAjAO/ah\r\n/wW+LABEc+/62Ads4xqQxlCbI9Cg3wJbGHVRBOcVf/gf2StYXqU43c3ou2XM\r\nSCzZk9B5CbHbw2ZxW7Q7bfYpq0cPt876i6kb4URKvX5J9Rrw7CCwhU7FsSnK\r\nNFuPYO0psCvKz2SmwQzDo6VQewkmVP57IBHSkn74ngHK77Nbu/GCh5xX7zE1\r\nE5qnZ2AWxh4tyAdT9QCgR9+huR3Celdf0r0=\r\n=T7Uu\r\n-----END PGP SIGNATURE-----\r\n", "size": 8222}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.0.0-alpha.1_1678441968524_0.7894934256288753"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-10T09:52:48.675Z", "publish_time": 1678441968675}, "5.0.0-alpha.2": {"name": "eslint-plugin-prettier", "version": "5.0.0-alpha.2", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "yarn@1.22.19", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "scripts": {"format": "yarn prettier '**/*.{js,json,md,yml}' --write && yarn lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prepare": "simple-git-hooks && yarn-deduplicate --strategy fewer || exit 0", "prerelease": "yarn format && yarn test", "release": "changeset publish", "test": "yarn lint && mocha"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "devDependencies": {"@1stg/common-config": "^7.1.1", "@changesets/changelog-github": "^0.4.6", "@changesets/cli": "^2.24.4", "@graphql-eslint/eslint-plugin": "^3.10.7", "@types/eslint": "^8.4.6", "@types/prettier": "^2.7.0", "@types/prettier-linter-helpers": "^1.0.1", "@typescript-eslint/parser": "^5.36.1", "eslint-config-prettier": "^8.5.0", "eslint-mdx": "^2.0.2", "eslint-plugin-eslint-plugin": "^5.0.6", "eslint-plugin-mdx": "^2.0.2", "eslint-plugin-self": "^1.2.1", "eslint-plugin-svelte": "^2.7.0", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.6.0", "mocha": "^10.0.0", "svelte": "^3.49.0", "vue-eslint-parser": "^9.0.3", "yarn-deduplicate": "^6.0.0"}, "resolutions": {"eslint-plugin-prettier": "link:.", "prettier": "^3.0.0-alpha.0"}, "readmeFilename": "README.md", "gitHead": "6878434497d4b57fdcfa91f026c45eee5ffb24be", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.0.0-alpha.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-F6YBCbrRzvZwcINw3crm1+/uX/i+rJYaFErPtwCfUoPLywRfY7pwBtI3yMe5OpIotuaiws8cd29oM80ca6NQSQ==", "shasum": "4dd7edf88ff9bc13a3bbd91c059aae267dac4576", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.0-alpha.2.tgz", "fileCount": 6, "unpackedSize": 24510, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbkb9g5VtQs9vPc/sySsYZj8+wMGiVHLtxvaZktPcnFwIgXMb/7m6FaqjDAIije3vxxlk4VtY8/h90bK990AGhHaw="}], "size": 8224}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.0.0-alpha.2_1688608609087_0.09727809595246595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-06T01:56:49.446Z", "publish_time": 1688608609446, "_source_registry_name": "default"}, "5.0.0": {"name": "eslint-plugin-prettier", "version": "5.0.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "pnpm@7.33.3", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.4.8", "@changesets/cli": "^2.26.2", "@commitlint/config-conventional": "^17.6.6", "@eslint-community/eslint-plugin-eslint-comments": "^3.2.1", "@graphql-eslint/eslint-plugin": "^3.20.0", "@types/eslint": "^8.44.0", "@types/prettier-linter-helpers": "^1.0.1", "commitlint": "^17.6.6", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.1.0", "eslint-plugin-eslint-plugin": "^5.1.0", "eslint-plugin-mdx": "^2.1.0", "eslint-plugin-n": "^16.0.1", "eslint-plugin-prettier": "link:.", "eslint-plugin-svelte": "^2.32.2", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.7.1", "lint-staged": "^13.2.3", "mocha": "^10.2.0", "prettier": "^3.0.0", "prettier-plugin-pkg": "^0.18.0", "simple-git-hooks": "^2.8.1", "svelte": "^4.0.5", "vue-eslint-parser": "^9.3.1"}, "scripts": {"format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prerelease": "pnpm format && pnpm test", "release": "changeset publish", "test": "pnpm lint && mocha"}, "_resolved": "", "_integrity": "", "_from": "file:eslint-plugin-prettier-5.0.0.tgz", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.0.0", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-AgaZCVuYDXHUGxj/ZGu1u8H8CYgDY3iG6w5kUFw4AzMVXzB7VvbKgYR4nATIN+OvUrghMbiDLeimVjVY5ilq3w==", "shasum": "6887780ed95f7708340ec79acfdf60c35b9be57a", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.0.tgz", "fileCount": 6, "unpackedSize": 24501, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+7oDSwLwPRr6RduMfwlM8eQlOIx8CrX76cpjtvG2RXAiBUgGQXYvQwj+4gIxECd/b7yMdRGkfczJvLTb/NX2ZspA=="}], "size": 8174}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.0.0_1689061544573_0.9507524979965378"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-11T07:45:44.838Z", "publish_time": 1689061544838, "_source_registry_name": "default"}, "5.0.1": {"name": "eslint-plugin-prettier", "version": "5.0.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "pnpm@7.33.3", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "scripts": {"format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prepare": "simple-git-hooks", "prerelease": "pnpm format && pnpm test", "release": "changeset publish", "test": "pnpm lint && mocha"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.4.8", "@changesets/cli": "^2.26.2", "@commitlint/config-conventional": "^17.6.6", "@eslint-community/eslint-plugin-eslint-comments": "^3.2.1", "@graphql-eslint/eslint-plugin": "^3.20.0", "@types/eslint": "^8.44.0", "@types/prettier-linter-helpers": "^1.0.1", "commitlint": "^17.6.6", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.1.0", "eslint-plugin-eslint-plugin": "^5.1.0", "eslint-plugin-mdx": "^2.1.0", "eslint-plugin-n": "^16.0.1", "eslint-plugin-prettier": "link:.", "eslint-plugin-svelte": "^2.32.2", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.7.1", "lint-staged": "^13.2.3", "mocha": "^10.2.0", "prettier": "^3.0.0", "prettier-plugin-pkg": "^0.18.0", "simple-git-hooks": "^2.8.1", "svelte": "^4.0.5", "vue-eslint-parser": "^9.3.1"}, "pnpm": {"patchedDependencies": {"@graphql-eslint/eslint-plugin@3.20.0": "patches/@<EMAIL>"}}, "gitHead": "aa81435000a5e163df07400fafb875aa8e3ea83b", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.0.1", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-m3u5RnR56asrwV/lDC4GHorlW75DsFfmUcjfCYylTUs85dBRnB7VM6xG8eCMJdeDRnppzmxZVf1GEPJvl1JmNg==", "shasum": "a3b399f04378f79f066379f544e42d6b73f11515", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.1.tgz", "fileCount": 7, "unpackedSize": 62913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6pwSMPr3UAdPOQuIe58AU2FlwqJxVAUsCJat+9GpdQAiA455qQdJQBGVxqjsbUGhr0gLbgg+i2YphyXMBhl6gVMQ=="}], "size": 18764}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.0.1_1696991756807_0.8356405714562323"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-11T02:35:57.043Z", "publish_time": 1696991757043, "_source_registry_name": "default"}, "5.1.0": {"name": "eslint-plugin-prettier", "version": "5.1.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "pnpm@7.33.3", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.26.2", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.1.0", "@eslint/js": "^8.55.0", "@graphql-eslint/eslint-plugin": "^3.20.0", "@types/eslint": "^8.44.7", "@types/prettier-linter-helpers": "^1.0.1", "commitlint": "^18.4.3", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.1.0", "eslint-plugin-eslint-plugin": "^5.1.0", "eslint-plugin-mdx": "^2.2.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-prettier": "link:.", "eslint-plugin-svelte": "^2.32.2", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.7.1", "lint-staged": "^15.1.0", "mocha": "^10.2.0", "prettier": "^3.0.0", "prettier-plugin-pkg": "^0.18.0", "simple-git-hooks": "^2.8.1", "svelte": "^4.0.5", "vue-eslint-parser": "^9.3.1"}, "scripts": {"format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prerelease": "pnpm format && pnpm test", "release": "changeset publish", "test": "pnpm lint && mocha"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.1.0", "_integrity": "sha512-hQc+2zbnMeXcIkg+pKZtVa+3Yqx4WY7SMkn1PLZ4VbBEU7jJIpVn9347P8BBhTbz6ne85aXvQf30kvexcqBeWw==", "_resolved": "/private/var/folders/wj/tsvx8lhd63x91_k9mg6dpk880000gn/T/07994dbbfabe99bab268dd43a84f24b4/eslint-plugin-prettier-5.1.0.tgz", "_from": "file:eslint-plugin-prettier-5.1.0.tgz", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-hQc+2zbnMeXcIkg+pKZtVa+3Yqx4WY7SMkn1PLZ4VbBEU7jJIpVn9347P8BBhTbz6ne85aXvQf30kvexcqBeWw==", "shasum": "f14bb2b18756ad54f1ad3dc4c989cb73dfa326a3", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.1.0.tgz", "fileCount": 8, "unpackedSize": 26805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWYOeZkKK4M3AQRGVDU7roxUTB694QiXc0gQiOux/z1QIgCCqH8f4ndZIk0aMwgZG8MRuMmJvIOJgkuRxQ6h5BH6A="}], "size": 8537}, "_npmUser": {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.1.0_1703020331194_0.4367749340348632"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-19T21:12:11.477Z", "publish_time": 1703020331477, "_source_registry_name": "default"}, "5.1.1": {"name": "eslint-plugin-prettier", "version": "5.1.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/prettier", "license": "MIT", "packageManager": "pnpm@7.33.3", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.26.2", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.1.0", "@eslint/js": "^8.55.0", "@graphql-eslint/eslint-plugin": "^3.20.0", "@prettier/plugin-pug": "^3.0.0", "@types/eslint": "^8.44.7", "@types/prettier-linter-helpers": "^1.0.1", "commitlint": "^18.4.3", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.1.0", "eslint-plugin-eslint-plugin": "^5.1.0", "eslint-plugin-mdx": "^2.2.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-prettier": "link:.", "eslint-plugin-pug": "^1.2.5", "eslint-plugin-svelte": "^2.32.2", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.7.1", "lint-staged": "^15.1.0", "mocha": "^10.2.0", "prettier": "^3.0.0", "prettier-plugin-pkg": "^0.18.0", "simple-git-hooks": "^2.8.1", "svelte": "^4.0.5", "vue-eslint-parser": "^9.3.1"}, "scripts": {"format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prerelease": "pnpm format && pnpm test", "release": "changeset publish", "test": "pnpm lint && mocha"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.1.1", "_integrity": "sha512-WQpV3mSmIobb77s4qiCZu3dBrZZ0rj8ckSfBtRrgNK9Wnh2s3eiaxNTWloz1LJ1WtvqZES/PAI7PLvsrGt/CEA==", "_resolved": "/Users/<USER>/.tmp/8856c3dc11b218918bfd4a8efe8e6c69/eslint-plugin-prettier-5.1.1.tgz", "_from": "file:eslint-plugin-prettier-5.1.1.tgz", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-WQpV3mSmIobb77s4qiCZu3dBrZZ0rj8ckSfBtRrgNK9Wnh2s3eiaxNTWloz1LJ1WtvqZES/PAI7PLvsrGt/CEA==", "shasum": "ab7d9823788b557ff7ccdd50a5849d7760cb8bef", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.1.1.tgz", "fileCount": 8, "unpackedSize": 26893, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfCfsBeQ/FQZ0X9cox481CdSRCYdLSNPkMvxQH9m+wCwIhAPuJj7rxPLNxiUqucPZ2o2wO7Xc7W1k8AR4duGxJy44J"}], "size": 8611}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.1.1_1703176899537_0.20105796094424333"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-21T16:41:39.701Z", "publish_time": 1703176899701, "_source_registry_name": "default"}, "5.1.2": {"name": "eslint-plugin-prettier", "version": "5.1.2", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "packageManager": "pnpm@7.33.5", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.6"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.1.0", "@eslint/js": "^8.56.0", "@graphql-eslint/eslint-plugin": "^3.20.1", "@prettier/plugin-pug": "^3.0.0", "@types/eslint": "^8.56.0", "@types/prettier-linter-helpers": "^1.0.4", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.2.1", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-mdx": "^2.2.1", "eslint-plugin-n": "^16.5.0", "eslint-plugin-prettier": "link:.", "eslint-plugin-pug": "^1.2.5", "eslint-plugin-svelte": "^2.35.1", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.8.1", "lint-staged": "^15.2.0", "mocha": "^10.2.0", "prettier": "^3.1.1", "prettier-plugin-pkg": "^0.18.0", "simple-git-hooks": "^2.9.0", "svelte": "^4.2.8", "vue-eslint-parser": "^9.3.2"}, "scripts": {"format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "prerelease": "pnpm format && pnpm test", "release": "changeset publish", "test": "pnpm lint && mocha"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.1.2", "_integrity": "sha512-dhlpWc9vOwohcWmClFcA+HjlvUpuyynYs0Rf+L/P6/0iQE6vlHW9l5bkfzN62/Stm9fbq8ku46qzde76T1xlSg==", "_resolved": "/Users/<USER>/.tmp/bab170871280473974f09bffadec451a/eslint-plugin-prettier-5.1.2.tgz", "_from": "file:eslint-plugin-prettier-5.1.2.tgz", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-dhlpWc9vOwohcWmClFcA+HjlvUpuyynYs0Rf+L/P6/0iQE6vlHW9l5bkfzN62/Stm9fbq8ku46qzde76T1xlSg==", "shasum": "584c94d4bf31329b2d4cbeb10fd600d17d6de742", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.1.2.tgz", "fileCount": 8, "unpackedSize": 33788, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAm4pOOujXLbzbbygqB+Cj1Jd1md9MLhEEVk+H0IfdRYAiAGqEd4AK1eL1v4fiyaFt+p9rY9w0xz1tSNooj4XehyMQ=="}], "size": 9309}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.1.2_1703395547426_0.13124070397128396"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-24T05:25:47.611Z", "publish_time": 1703395547611, "_source_registry_name": "default"}, "5.1.3": {"name": "eslint-plugin-prettier", "version": "5.1.3", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "packageManager": "pnpm@7.33.5", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.6"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.1.0", "@eslint/js": "^8.56.0", "@graphql-eslint/eslint-plugin": "^3.20.1", "@prettier/plugin-pug": "^3.0.0", "@types/eslint": "^8.56.0", "@types/prettier-linter-helpers": "^1.0.4", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.3.0", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-mdx": "^2.3.0", "eslint-plugin-n": "^16.5.0", "eslint-plugin-prettier": "link:.", "eslint-plugin-pug": "^1.2.5", "eslint-plugin-svelte": "^2.35.1", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.8.1", "lint-staged": "^15.2.0", "mocha": "^10.2.0", "prettier": "^3.1.1", "prettier-plugin-pkg": "^0.18.0", "prettier-plugin-svelte": "^3.1.2", "simple-git-hooks": "^2.9.0", "svelte": "^4.2.8", "vue-eslint-parser": "^9.3.2"}, "scripts": {"check": "prettier --check . && pnpm lint", "format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "release": "pnpm check && pnpm test && changeset publish", "test": "pnpm lint && mocha"}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_id": "eslint-plugin-prettier@5.1.3", "_integrity": "sha512-C9GCVAs4Eq7ZC/XFQHITLiHJxQngdtraXaM+LoUFoFp/lHNl2Zn8f3WQbe9HvTBBQ9YnKFB0/2Ajdqwo5D1EAw==", "_resolved": "/tmp/854c67e66d175f31220ffe36d1baec78/eslint-plugin-prettier-5.1.3.tgz", "_from": "file:eslint-plugin-prettier-5.1.3.tgz", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-C9GCVAs4Eq7ZC/XFQHITLiHJxQngdtraXaM+LoUFoFp/lHNl2Zn8f3WQbe9HvTBBQ9YnKFB0/2Ajdqwo5D1EAw==", "shasum": "17cfade9e732cef32b5f5be53bd4e07afd8e67e1", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.1.3.tgz", "fileCount": 8, "unpackedSize": 33897, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGrwwwa/aSuPmLnUXJK472gyGGq/aZj148zpmw/GBkjWAiEA5/4Pl5yWszhZ2u/Hy9oFJev56h6s1wB6TgPnQ67G1wk="}], "size": 9330}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.1.3_1704857674265_0.20665195312643592"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-10T03:34:34.437Z", "publish_time": 1704857674437, "_source_registry_name": "default"}, "5.2.1": {"name": "eslint-plugin-prettier", "version": "5.2.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "packageManager": "pnpm@7.33.5", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.9.1"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.1.0", "@eslint/js": "^8.56.0", "@graphql-eslint/eslint-plugin": "^3.20.1", "@html-eslint/parser": "^0.24.1", "@prettier/plugin-pug": "^3.0.0", "@types/eslint": "^8.56.0", "@types/prettier-linter-helpers": "^1.0.4", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.3.0", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-mdx": "^2.3.0", "eslint-plugin-n": "^16.5.0", "eslint-plugin-prettier": "link:.", "eslint-plugin-pug": "^1.2.5", "eslint-plugin-svelte": "^2.35.1", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.8.1", "lint-staged": "^15.2.0", "mocha": "^10.2.0", "prettier": "^3.1.1", "prettier-plugin-pkg": "^0.18.0", "prettier-plugin-svelte": "^3.1.2", "simple-git-hooks": "^2.9.0", "svelte": "^4.2.8", "vue-eslint-parser": "^9.3.2"}, "scripts": {"check": "prettier --check . && pnpm lint", "format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "release": "pnpm check && pnpm test && changeset publish", "test": "pnpm lint && mocha"}, "_id": "eslint-plugin-prettier@5.2.1", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-gH3iR3g4JfF+yYPaJYkN7jEl9QbweL/YfkoRlNnuIEHEz1vHVlCmWOS+eGGiRuzHQXdJFCOTxRgvju9b8VUmrw==", "_resolved": "/tmp/857cf15cee08a8e0c99ebb21250c15a0/eslint-plugin-prettier-5.2.1.tgz", "_from": "file:eslint-plugin-prettier-5.2.1.tgz", "_nodeVersion": "20.15.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-gH3iR3g4JfF+yYPaJYkN7jEl9QbweL/YfkoRlNnuIEHEz1vHVlCmWOS+eGGiRuzHQXdJFCOTxRgvju9b8VUmrw==", "shasum": "d1c8f972d8f60e414c25465c163d16f209411f95", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.1.tgz", "fileCount": 8, "unpackedSize": 34308, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYAKb+udtHdc7HSc7BUeOY0YdsaupZgO/x1aVO7SjrcgIgYde32NGxjIi45y+Pu+S2HZNUIVnweWYeQruXr4rDd/o="}], "size": 9361}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/eslint-plugin-prettier_5.2.1_1721215870553_0.4006208339515469"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-17T11:31:10.718Z", "publish_time": 1721215870718, "_source_registry_name": "default"}, "5.2.2": {"name": "eslint-plugin-prettier", "version": "5.2.2", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "packageManager": "pnpm@7.33.5", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.9.1"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.1.0", "@eslint/js": "^8.56.0", "@graphql-eslint/eslint-plugin": "^3.20.1", "@html-eslint/parser": "^0.24.1", "@prettier/plugin-pug": "^3.0.0", "@types/eslint": "^8.56.0", "@types/prettier-linter-helpers": "^1.0.4", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.3.0", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-mdx": "^2.3.0", "eslint-plugin-n": "^16.5.0", "eslint-plugin-prettier": "link:.", "eslint-plugin-pug": "^1.2.5", "eslint-plugin-svelte": "^2.35.1", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.8.1", "lint-staged": "^15.2.0", "mocha": "^10.2.0", "prettier": "^3.1.1", "prettier-plugin-pkg": "^0.18.0", "prettier-plugin-svelte": "^3.1.2", "simple-git-hooks": "^2.9.0", "svelte": "^4.2.8", "vue-eslint-parser": "^9.3.2"}, "scripts": {"check": "prettier --check . && pnpm lint", "format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "release": "pnpm check && pnpm test && changeset publish", "test": "pnpm lint && mocha"}, "_id": "eslint-plugin-prettier@5.2.2", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-1yI3/hf35wmlq66C8yOyrujQnel+v5l1Vop5Cl2I6ylyNTT1JbuUUnV3/41PzwTzcyDp/oF0jWE3HXvcH5AQOQ==", "_resolved": "/tmp/b7ecee35d20290b8d9d3f1e8ce397277/eslint-plugin-prettier-5.2.2.tgz", "_from": "file:eslint-plugin-prettier-5.2.2.tgz", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-1yI3/hf35wmlq66C8yOyrujQnel+v5l1Vop5Cl2I6ylyNTT1JbuUUnV3/41PzwTzcyDp/oF0jWE3HXvcH5AQOQ==", "shasum": "d1f068f65dc8490f102eda21d1f4cd150c205211", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.2.tgz", "fileCount": 8, "unpackedSize": 34474, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyb7vuRDSK9dBkr/ylZ7yMemNhKBVCdRSUSHoYwZ8BEQIgJt0ZcXKEeLGNHqwzoTVI/xVj2EJHs/zCzeMqjgoow48="}], "size": 9395}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.2.2_1736918111566_0.5775307120370186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-15T05:15:11.778Z", "publish_time": 1736918111778, "_source_registry_name": "default"}, "5.2.3": {"name": "eslint-plugin-prettier", "version": "5.2.3", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "packageManager": "pnpm@7.33.5", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.9.1"}, "devDependencies": {"@1stg/remark-preset": "^2.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@commitlint/config-conventional": "^18.4.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.4.1", "@eslint/js": "^8.56.0", "@graphql-eslint/eslint-plugin": "^3.20.1", "@html-eslint/parser": "^0.24.1", "@prettier/plugin-pug": "^3.0.0", "@types/eslint": "^8.56.0", "@types/prettier-linter-helpers": "^1.0.4", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-formatter-friendly": "^7.0.0", "eslint-mdx": "^2.3.0", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-mdx": "^2.3.0", "eslint-plugin-n": "^16.5.0", "eslint-plugin-prettier": "link:.", "eslint-plugin-pug": "^1.2.5", "eslint-plugin-svelte": "^2.35.1", "eslint-plugin-svelte3": "^4.0.0", "graphql": "^16.8.1", "lint-staged": "^15.2.0", "mocha": "^10.2.0", "prettier": "^3.1.1", "prettier-plugin-pkg": "^0.18.0", "prettier-plugin-svelte": "^3.1.2", "simple-git-hooks": "^2.9.0", "svelte": "^4.2.8", "vue-eslint-parser": "^9.3.2"}, "scripts": {"check": "prettier --check . && pnpm lint", "format": "prettier --write . && pnpm lint --fix", "lint": "eslint . --cache -f friendly --max-warnings 10", "release": "pnpm check && pnpm test && changeset publish", "test": "pnpm lint && mocha"}, "_id": "eslint-plugin-prettier@5.2.3", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-qJ+y0FfCp/mQYQ/vWQ3s7eUlFEL4PyKfAJxsnYTJ4YT73nsJBWqmEpFryxV9OeUiqmsTsYJ5Y+KDNaeP31wrRw==", "_resolved": "/tmp/1baeaa91fa4a2692cff829ec1a576336/eslint-plugin-prettier-5.2.3.tgz", "_from": "file:eslint-plugin-prettier-5.2.3.tgz", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-qJ+y0FfCp/mQYQ/vWQ3s7eUlFEL4PyKfAJxsnYTJ4YT73nsJBWqmEpFryxV9OeUiqmsTsYJ5Y+KDNaeP31wrRw==", "shasum": "c4af01691a6fa9905207f0fbba0d7bea0902cce5", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.3.tgz", "fileCount": 8, "unpackedSize": 34521, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+A5ac+ClIvAdOyX/mLf/bm1YMirt1brfBsi15CeYOcAiBrjED0XWbAg9loNU5VsDkP736axSsC6qL3BUszTi8jiA=="}], "size": 9410}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.2.3_1737245248577_0.7023219327346113"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-19T00:07:28.777Z", "publish_time": 1737245248777, "_source_registry_name": "default"}, "5.2.4": {"name": "eslint-plugin-prettier", "version": "5.2.4", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": "*", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.10.2"}, "_id": "eslint-plugin-prettier@5.2.4", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-SFtuYmnhwYCtuCDTKPoK+CEzCnEgKTU2qTLwoCxvrC0MFBTIXo1i6hDYOI4cwHaE5GZtlWmTN3YfucYi7KJwPw==", "_resolved": "/tmp/6285273a358293c5ecc50bb3d25e9ff8/eslint-plugin-prettier-5.2.4.tgz", "_from": "file:eslint-plugin-prettier-5.2.4.tgz", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-SFtuYmnhwYCtuCDTKPoK+CEzCnEgKTU2qTLwoCxvrC0MFBTIXo1i6hDYOI4cwHaE5GZtlWmTN3YfucYi7KJwPw==", "shasum": "6daa54a11da8c48971475d7c0e239d0b6c6dbc60", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.4.tgz", "fileCount": 8, "unpackedSize": 33045, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEJ24ugi8fBbAG4CYA5Z0ktjNHEHLFY8T4G9bU/qyzZKAiBJWRF5USpaJDZBbHwyhyy0v5rb6MtlCBMheSt1PtwuDw=="}], "size": 8995}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.2.4_1742781030546_0.5568400351990059"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-24T01:50:30.779Z", "publish_time": 1742781030779, "_source_registry_name": "default"}, "5.2.5": {"name": "eslint-plugin-prettier", "version": "5.2.5", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.10.2"}, "_id": "eslint-plugin-prettier@5.2.5", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-IKKP8R87pJyMl7WWamLgPkloB16dagPIdd2FjBDbyRYPKo93wS/NbCOPh6gH+ieNLC+XZrhJt/kWj0PS/DFdmg==", "_resolved": "/tmp/79ddf140be6670da4106aaa81581cdfd/eslint-plugin-prettier-5.2.5.tgz", "_from": "file:eslint-plugin-prettier-5.2.5.tgz", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-IKKP8R87pJyMl7WWamLgPkloB16dagPIdd2FjBDbyRYPKo93wS/NbCOPh6gH+ieNLC+XZrhJt/kWj0PS/DFdmg==", "shasum": "0ff00b16f4c80ccdafd6a24a263effba1700087e", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.5.tgz", "fileCount": 8, "unpackedSize": 33072, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAqOqyCFwYQ2JxMnr8VYXouUk/5ICDAPHMEvk3RPzN9cAiBV2nJQoVTg5umAxZpvpBpv7QsdYEeSRAuOqISzP8xhkA=="}], "size": 9006}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.2.5_1742898777376_0.135867047155388"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-25T10:32:57.666Z", "publish_time": 1742898777666, "_source_registry_name": "default"}, "5.2.6": {"name": "eslint-plugin-prettier", "version": "5.2.6", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "types": "eslint-plugin-prettier.d.ts", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.0"}, "_id": "eslint-plugin-prettier@5.2.6", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-mUcf7QG2Tjk7H055Jk0lGBjbgDnfrvqjhXh9t2xLMSCjZVcw9Rb1V6sVNXO0th3jgeO7zllWPTNRil3JW94TnQ==", "_resolved": "/tmp/b623fac81d3f202e37d9d48881f4f96b/eslint-plugin-prettier-5.2.6.tgz", "_from": "file:eslint-plugin-prettier-5.2.6.tgz", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-mUcf7QG2Tjk7H055Jk0lGBjbgDnfrvqjhXh9t2xLMSCjZVcw9Rb1V6sVNXO0th3jgeO7zllWPTNRil3JW94TnQ==", "shasum": "be39e3bb23bb3eeb7e7df0927cdb46e4d7945096", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.6.tgz", "fileCount": 8, "unpackedSize": 33072, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDImd3mv4K+XpBu0aW5ECZt8HXNnJP8diSsrexnDR5dcgIhAMwjWk1ii4cNjf+kZOfZA0oaryvRR90KrFH12t16JEuA"}], "size": 9005}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.2.6_1743610512866_0.6219192173288264"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T16:15:13.067Z", "publish_time": 1743610513067, "_source_registry_name": "default"}, "5.3.0": {"name": "eslint-plugin-prettier", "version": "5.3.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.0"}, "_id": "eslint-plugin-prettier@5.3.0", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-IiIlXr77W7cksZ6Rws/bJ9S3aPNPriY3Gc1+ZPtmcaA16Q9MqjesSUzjBrMKcYPgDwBzgeKGvzhWu2GtXpKJQA==", "_resolved": "/tmp/e935f7c829a88158fa16c4ef1b83daff/eslint-plugin-prettier-5.3.0.tgz", "_from": "file:eslint-plugin-prettier-5.3.0.tgz", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-IiIlXr77W7cksZ6Rws/bJ9S3aPNPriY3Gc1+ZPtmcaA16Q9MqjesSUzjBrMKcYPgDwBzgeKGvzhWu2GtXpKJQA==", "shasum": "752aca7b2635c619e545318d01ab0a4e6204b8ef", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.3.0.tgz", "fileCount": 8, "unpackedSize": 33273, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF9SFIyxe9yivo6DVn52yEmgEF+z6/b+3vUawe9xt7lgAiEA2S+gq1DROnnmDg4tN8FZYviyHYfJ0epIOuWbh5hKAtQ="}], "size": 9155}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.3.0_1746342745102_0.10640187381580635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-04T07:12:25.323Z", "publish_time": 1746342745323, "_source_registry_name": "default"}, "5.3.1": {"name": "eslint-plugin-prettier", "version": "5.3.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.0"}, "_id": "eslint-plugin-prettier@5.3.1", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-vad9VWgEm9xaVXRNmb4aeOt0PWDc61IAdzghkbYQ2wavgax148iKoX1rNJcgkBGCipzLzOnHYVgL7xudM9yccQ==", "_resolved": "/tmp/c2467ae437ac19062594bf038304ed2c/eslint-plugin-prettier-5.3.1.tgz", "_from": "file:eslint-plugin-prettier-5.3.1.tgz", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-vad9VWgEm9xaVXRNmb4aeOt0PWDc61IAdzghkbYQ2wavgax148iKoX1rNJcgkBGCipzLzOnHYVgL7xudM9yccQ==", "shasum": "9a16effb2d9d1c6ff44d182f48becfc54d5cff5a", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.3.1.tgz", "fileCount": 8, "unpackedSize": 33272, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-prettier@5.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDZ3JnXoThyNwvNUCr1rK0OMYLG1oP56HMBRjPEA3IacQIgKIm4HwgxYtBPloXZSswhtN8vWfeJGu5EBD8yxqMef+c="}], "size": 9149}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.3.1_1746345937009_0.736979849093097"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-04T08:05:37.207Z", "publish_time": 1746345937207, "_source_registry_name": "default"}, "5.4.0": {"name": "eslint-plugin-prettier", "version": "5.4.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.0"}, "_id": "eslint-plugin-prettier@5.4.0", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-BvQOvUhkVQM1i63iMETK9Hjud9QhqBnbtT1Zc642p9ynzBuCe5pybkOnvqZIBypXmMlsGcnU4HZ8sCTPfpAexA==", "_resolved": "/tmp/889dab0a202cfcfb02fa98afad7d72b5/eslint-plugin-prettier-5.4.0.tgz", "_from": "file:eslint-plugin-prettier-5.4.0.tgz", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-BvQOvUhkVQM1i63iMETK9Hjud9QhqBnbtT1Zc642p9ynzBuCe5pybkOnvqZIBypXmMlsGcnU4HZ8sCTPfpAexA==", "shasum": "54d4748904e58eaf1ffe26c4bffa4986ca7f952b", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.4.0.tgz", "fileCount": 8, "unpackedSize": 33143, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-prettier@5.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIE6GUQwke/u/jPLXKivfh+K1c5f5Wuab2DQUMmGm3pI8AiBH1b7Sau9HbKGFVuc5AkjzXQnlX5inZQehFUffmt1F+w=="}], "size": 9144}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.4.0_1746442497976_0.22100322411612217"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-05T10:54:58.169Z", "publish_time": 1746442498169, "_source_registry_name": "default"}, "5.4.1": {"name": "eslint-plugin-prettier", "version": "5.4.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "maintainers": [{"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "_id": "eslint-plugin-prettier@5.4.1", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-9dF+KuU/Ilkq27A8idRP7N2DH8iUR6qXcjF3FR2wETY21PZdBrIjwCau8oboyGj9b7etWmTGEeM8e7oOed6ZWg==", "_resolved": "/tmp/fa4d54bd4ccd18cecbf941972aafb583/eslint-plugin-prettier-5.4.1.tgz", "_from": "file:eslint-plugin-prettier-5.4.1.tgz", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-9dF+KuU/Ilkq27A8idRP7N2DH8iUR6qXcjF3FR2wETY21PZdBrIjwCau8oboyGj9b7etWmTGEeM8e7oOed6ZWg==", "shasum": "99b55d7dd70047886b2222fdd853665f180b36af", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.4.1.tgz", "fileCount": 8, "unpackedSize": 33143, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-prettier@5.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIC5B8WwImj+shJdT5L8mhQVaql3Qt9dzxBcaEf5YfeeJAiEAxqonUQxDvYHNTRL0OTdoVfmPclcQocMQs834KKuMfVE="}], "size": 9145}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.4.1_1748576135824_0.406545865232824"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-30T03:35:35.994Z", "publish_time": 1748576135994, "_source_registry_name": "default"}, "5.5.0": {"name": "eslint-plugin-prettier", "version": "5.5.0", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "maintainers": [{"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "_id": "eslint-plugin-prettier@5.5.0", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-8qsOYwkkGrahrgoUv76NZi23koqXOGiiEzXMrT8Q7VcYaUISR+5MorIUxfWqYXN0fN/31WbSrxCxFkVQ43wwrA==", "_resolved": "/tmp/7d2ff9cb292b9c6f2ec1c106f68949c1/eslint-plugin-prettier-5.5.0.tgz", "_from": "file:eslint-plugin-prettier-5.5.0.tgz", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-8qsOYwkkGrahrgoUv76NZi23koqXOGiiEzXMrT8Q7VcYaUISR+5MorIUxfWqYXN0fN/31WbSrxCxFkVQ43wwrA==", "shasum": "cf763962f90bad035db03ca008ffb0c9b359fb16", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.0.tgz", "fileCount": 8, "unpackedSize": 34212, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-prettier@5.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHVyNiFZYb6wR2LeCupJpRDphvtqdzWRlQq6vxHo/nOBAiBqDODqJtFmbTAze+m2dXaovU+HxoGqSuphB1+dCds4Fw=="}], "size": 9501}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>", "actor": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.5.0_1750149697152_0.24486686463023033"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-17T08:41:37.332Z", "publish_time": 1750149697332, "_source_registry_name": "default"}, "5.5.1": {"name": "eslint-plugin-prettier", "version": "5.5.1", "description": "Runs prettier as an eslint rule", "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "author": {"name": "<PERSON>"}, "maintainers": [{"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://opencollective.com/eslint-plugin-prettier", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "eslint-plugin-prettier.js", "types": "eslint-plugin-prettier.d.ts", "exports": {".": {"types": "./eslint-plugin-prettier.d.ts", "default": "./eslint-plugin-prettier.js"}, "./recommended": {"types": "./recommended.d.ts", "default": "./recommended.js"}, "./package.json": "./package.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}, "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "_id": "eslint-plugin-prettier@5.5.1", "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "_integrity": "sha512-dobTkHT6XaEVOo8IO90Q4DOSxnm3Y151QxPJlM/vKC0bVy+d6cVWQZLlFiuZPP0wS6vZwSKeJgKkcS+KfMBlRw==", "_resolved": "/tmp/5ed3b1f569db0c8c1812a4f9b0698f2d/eslint-plugin-prettier-5.5.1.tgz", "_from": "file:eslint-plugin-prettier-5.5.1.tgz", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-dobTkHT6XaEVOo8IO90Q4DOSxnm3Y151QxPJlM/vKC0bVy+d6cVWQZLlFiuZPP0wS6vZwSKeJgKkcS+KfMBlRw==", "shasum": "470820964de9aedb37e9ce62c3266d2d26d08d15", "tarball": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.1.tgz", "fileCount": 8, "unpackedSize": 34105, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-prettier@5.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFlN711S/gazQYvpCAsDvyXeyWfAUKDqnyCfbuCzw+FOAiEAvotpzMHC7735TAVRK/d5LJnI9ImJzySog7MJ13XERcg="}], "size": 9476}, "_npmUser": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>", "actor": {"name": "jou<PERSON><PERSON>n", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-plugin-prettier_5.5.1_1750849005827_0.17221321095331987"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-25T10:56:46.097Z", "publish_time": 1750849006097, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/prettier/eslint-plugin-prettier/issues"}, "homepage": "https://github.com/prettier/eslint-plugin-prettier#readme", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "prettier"], "repository": {"type": "git", "url": "git+https://github.com/prettier/eslint-plugin-prettier.git"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JounQin"}], "_source_registry_name": "default"}