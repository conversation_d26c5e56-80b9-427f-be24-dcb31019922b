<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <span class="close-btn" title="关闭登录窗口" @click="$emit('close')">
        <svg class="close-icon" viewBox="0 0 20 20" width="22" height="22">
          <line
            x1="5"
            y1="5"
            x2="15"
            y2="15"
            stroke="#00ffff"
            stroke-width="2"
            stroke-linecap="round"
          />
          <line
            x1="15"
            y1="5"
            x2="5"
            y2="15"
            stroke="#00ffff"
            stroke-width="2"
            stroke-linecap="round"
          />
        </svg>
        <span class="close-tooltip">退出</span>
      </span>
      <img src="@/assets/logo.png" alt="logo" class="login-logo" />
      <h3>用户登录</h3>
      <p class="login-subtitle">欢迎使用项目管理系统</p>
      <form @submit.prevent="handleLogin">
        <div class="input-group">
          <label for="username">用户名</label>
          <input
            id="username"
            v-model="loginForm.username"
            type="text"
            required
            autocomplete="username"
          />
        </div>
        <div class="input-group" style="position: relative">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="loginForm.password"
            :type="showPassword ? 'text' : 'password'"
            required
            autocomplete="current-password"
          />
          <span class="password-toggle" @click="togglePasswordVisibility">
            {{ showPassword ? "隐藏" : "显示" }}
          </span>
        </div>
        <div class="button-group">
          <button type="submit" :disabled="loading">
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? "登录中..." : "登录" }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineEmits } from "vue";
import { useStore } from "vuex";
import { login } from "@/utils/api";
import { showToast } from "@/utils/toast";

const emit = defineEmits(["close", "login-success"]);

const store = useStore();
const loading = ref(false);
const showPassword = ref(false);

const loginForm = reactive({
  username: "",
  password: "",
});

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    showToast("请输入用户名和密码", "error");
    return;
  }

  loading.value = true;
  const minLoading = new Promise((resolve) => setTimeout(resolve, 500)); // 至少loading 500ms
  try {
    const response = await login(loginForm.username, loginForm.password);
    await minLoading;
    if (response.success) {
      // 更新 Vuex store
      store.commit("setToken", response.data.token);
      store.commit("setUser", response.data.userInfo);
      // 发出登录成功事件
      emit("login-success");
      // 清空表单
      loginForm.username = "";
      loginForm.password = "";
    } else {
      showToast(response.message || "登录失败", "error");
    }
  } catch (error) {
    await minLoading;
    console.error("登录失败:", error);
    showToast(
      error.response?.data?.message || "登录失败，请检查用户名和密码",
      "error"
    );
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: linear-gradient(135deg, #23234b 0%, #1a1a40 100%);
  padding: 2.5rem 2rem 2rem 2rem;
  border-radius: 22px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.55);
  width: 100%;
  max-width: 420px;
  animation: fadeIn 0.3s ease-in-out;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-logo {
  width: 60px;
  height: 60px;
  margin-bottom: 0.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px #00ffff44;
}
.login-subtitle {
  color: #7ee7ff;
  text-align: center;
  margin: -1rem 0 1.5rem 0;
  font-size: 1rem;
  letter-spacing: 1px;
}
.input-group {
  margin-bottom: 1rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #cccccc;
}

.input-group input {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(90deg, #2a2a60 60%, #23234b 100%);
  color: #00ffff;
  font-size: 1rem;
  box-shadow: 0 2px 8px #00ffff22;
  transition: box-shadow 0.2s;
}
.input-group input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #00ffff99;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 2.8rem;
  cursor: pointer;
  color: #00ffff;
  font-size: 0.9rem;
}

.button-group {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
}

.button-group button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button-group button[type="submit"] {
  background: linear-gradient(90deg, #00ffff 60%, #00bfff 100%);
  color: #0f0f23;
  font-weight: bold;
  box-shadow: 0 2px 8px #00ffff44;
  position: relative;
  overflow: hidden;
}

.button-group button[type="submit"]:hover {
  background: linear-gradient(90deg, #00dddd 60%, #00aaff 100%);
  transform: scale(1.04);
}

.button-group button[type="submit"]:disabled {
  background: #00aaaa;
  color: #eee;
}

.button-group button[type="button"] {
  background-color: #666;
  color: white;
}

.button-group button[type="button"]:hover {
  background-color: #777;
  transform: scale(1.02);
}

.button-group button:disabled {
  background-color: #00aaaa;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid #fff;
  border-top: 2px solid #00bfff;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.close-btn {
  position: absolute;
  top: 18px;
  right: 18px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
}
.close-icon {
  transition: transform 0.2s;
}
.close-btn:hover .close-icon {
  transform: rotate(90deg) scale(1.15);
}
.close-tooltip {
  display: none;
  background: #222e;
  color: #00ffff;
  font-size: 0.95rem;
  border-radius: 6px;
  padding: 2px 10px;
  margin-left: 8px;
  white-space: nowrap;
  box-shadow: 0 2px 8px #00ffff33;
  position: relative;
  top: 1px;
}
.close-btn:hover .close-tooltip {
  display: inline-block;
}
</style>
