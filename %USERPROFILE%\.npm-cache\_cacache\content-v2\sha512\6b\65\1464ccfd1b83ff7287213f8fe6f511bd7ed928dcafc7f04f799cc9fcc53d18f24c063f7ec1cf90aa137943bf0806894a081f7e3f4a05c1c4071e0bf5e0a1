{"_attachments": {}, "_id": "brace-expansion", "_rev": "888-61f14569b677e08f5113c211", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "description": "Brace expansion as known from sh/bash", "dist-tags": {"1.x": "1.1.12", "2.x": "2.0.2", "3.x": "3.0.1", "latest": "4.0.1"}, "license": "MIT", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "brace-expansion", "readme": "# brace-expansion\n\n[Brace expansion](https://www.gnu.org/software/bash/manual/html_node/Brace-Expansion.html),\nas known from sh/bash, in JavaScript.\n\n[![CI](https://github.com/juliangruber/brace-expansion/actions/workflows/ci.yml/badge.svg)](https://github.com/juliangruber/brace-expansion/actions/workflows/ci.yml)\n[![downloads](https://img.shields.io/npm/dm/brace-expansion.svg)](https://www.npmjs.org/package/brace-expansion)\n\n## Example\n\n```js\nimport expand from 'brace-expansion'\n\nexpand('file-{a,b,c}.jpg')\n// => ['file-a.jpg', 'file-b.jpg', 'file-c.jpg']\n\nexpand('-v{,,}')\n// => ['-v', '-v', '-v']\n\nexpand('file{0..2}.jpg')\n// => ['file0.jpg', 'file1.jpg', 'file2.jpg']\n\nexpand('file-{a..c}.jpg')\n// => ['file-a.jpg', 'file-b.jpg', 'file-c.jpg']\n\nexpand('file{2..0}.jpg')\n// => ['file2.jpg', 'file1.jpg', 'file0.jpg']\n\nexpand('file{0..4..2}.jpg')\n// => ['file0.jpg', 'file2.jpg', 'file4.jpg']\n\nexpand('file-{a..e..2}.jpg')\n// => ['file-a.jpg', 'file-c.jpg', 'file-e.jpg']\n\nexpand('file{00..10..5}.jpg')\n// => ['file00.jpg', 'file05.jpg', 'file10.jpg']\n\nexpand('{{A..C},{a..c}}')\n// => ['A', 'B', 'C', 'a', 'b', 'c']\n\nexpand('ppp{,config,oe{,conf}}')\n// => ['ppp', 'pppconfig', 'pppoe', 'pppoeconf']\n```\n\n## API\n\n```js\nimport expand from 'brace-expansion'\n```\n\n### const expanded = expand(str)\n\nReturn an array of all possible and valid expansions of `str`. If none are\nfound, `[str]` is returned.\n\nValid expansions are:\n\n```js\n/^(.*,)+(.+)?$/\n// {a,b,...}\n```\n\nA comma separated list of options, like `{a,b}` or `{a,{b,c}}` or `{,a,}`.\n\n```js\n/^-?\\d+\\.\\.-?\\d+(\\.\\.-?\\d+)?$/\n// {x..y[..incr]}\n```\n\nA numeric sequence from `x` to `y` inclusive, with optional increment.\nIf `x` or `y` start with a leading `0`, all the numbers will be padded\nto have equal length. Negative numbers and backwards iteration work too.\n\n```js\n/^-?\\d+\\.\\.-?\\d+(\\.\\.-?\\d+)?$/\n// {x..y[..incr]}\n```\n\nAn alphabetic sequence from `x` to `y` inclusive, with optional increment.\n`x` and `y` must be exactly one character, and if given, `incr` must be a\nnumber.\n\nFor compatibility reasons, the string `${` is not eligible for brace expansion.\n\n## Installation\n\nWith [npm](https://npmjs.org) do:\n\n```bash\nnpm install brace-expansion\n```\n\n## Contributors\n\n- [Julian Gruber](https://github.com/juliangruber)\n- [Isaac Z. Schlueter](https://github.com/isaacs)\n- [Haelwenn Monnier](https://github.com/lanodan)\n\n## Sponsors\n\nThis module is proudly supported by my [Sponsors](https://github.com/juliangruber/sponsors)!\n\nDo you want to support modules like this to improve their quality, stability and weigh in on new features? Then please consider donating to my [Patreon](https://www.patreon.com/juliangruber). Not sure how much of my modules you're using? Try [feross/thanks](https://github.com/feross/thanks)!\n\n## Security contact information\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## License\n\n(MIT)\n\nCopyright (c) 2013 Julian Gruber &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\nof the Software, and to permit persons to whom the Software is furnished to do\nso, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "time": {"created": "2022-01-26T12:58:17.266Z", "modified": "2025-06-11T08:54:46.221Z", "2.0.1": "2021-02-22T16:18:13.617Z", "2.0.0": "2020-10-05T11:41:11.973Z", "1.1.11": "2018-02-10T07:42:22.313Z", "1.1.10": "2018-02-09T21:13:29.675Z", "1.1.9": "2018-02-09T09:53:36.709Z", "1.1.8": "2017-06-12T07:19:41.589Z", "1.1.7": "2017-04-07T08:13:51.907Z", "1.1.6": "2016-07-20T20:48:37.117Z", "1.1.5": "2016-06-15T11:21:03.644Z", "1.1.4": "2016-05-01T19:14:21.252Z", "1.1.3": "2016-02-11T18:51:31.874Z", "1.1.2": "2015-11-28T12:58:57.647Z", "1.1.1": "2015-09-27T21:58:47.098Z", "1.1.0": "2014-12-16T18:58:15.116Z", "1.0.1": "2014-12-03T07:58:39.708Z", "1.0.0": "2014-11-30T09:58:55.317Z", "0.0.0": "2013-10-13T12:58:50.153Z", "3.0.0": "2023-10-07T13:31:03.177Z", "4.0.0": "2024-02-27T11:56:43.001Z", "4.0.1": "2025-06-11T07:04:23.771Z", "3.0.1": "2025-06-11T08:44:12.277Z", "2.0.2": "2025-06-11T08:48:36.535Z", "1.1.12": "2025-06-11T08:52:58.148Z"}, "versions": {"2.0.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "2.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "b9c0e57027317a8d0a56a7ccee28fc478d847da2", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@2.0.1", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"shasum": "1edc459e0f0c548486ecf9fc99f2221364b9a0ae", "size": 4345, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_2.0.1_1614010693500_0.3082242768639887"}, "_hasShrinkwrap": false, "publish_time": 1614010693617, "_cnpm_publish_time": 1614010693617, "_cnpmcore_publish_time": "2021-12-16T11:57:26.478Z"}, "2.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "0b6a022491103b806770bc037654744bef3e63be", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@2.0.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.14.4", "dist": {"shasum": "3b53b490c803c23a6a5d6c9c8b309879c37c7f98", "size": 4315, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.0.tgz", "integrity": "sha512-A4GHY1GpcTnp+Elcwp1CbKHY6ZQwwVR7QdjZk4fPetEh7oNBfICu+eLvvVvTEMHgC+SGn+XiLAgGo0MnPPBGOg=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_2.0.0_1601898071832_0.5293279392460339"}, "_hasShrinkwrap": false, "publish_time": 1601898071973, "_cnpm_publish_time": 1601898071973, "_cnpmcore_publish_time": "2021-12-16T11:57:26.707Z"}, "1.1.11": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.11", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "01a21de7441549d26ac0c0a9ff91385d16e5c21c", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.11", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3c7fcbf529d87226f3d2f52b966ff5271eb441dd", "size": 4239, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_1.1.11_1518248541320_0.33962849281003904"}, "_hasShrinkwrap": false, "publish_time": 1518248542313, "_cnpm_publish_time": 1518248542313, "_cnpmcore_publish_time": "2021-12-16T11:57:26.862Z"}, "1.1.10": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.10", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "54a6176731eb223cd3dede1473190d885d6b3648", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.10", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5205cdf64c9798c180dc74b7bfc670c3974e6300", "size": 4209, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.10.tgz", "integrity": "sha512-u0KjSZq9NOEh36yRmKT/pIYOu0rpGAyUTeUmJgNd1K2tpAaUomh092TZ0fqbBGQc4hz85BVngAiB2mqekvQvIw=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_1.1.10_1518210808996_0.14734749523785462"}, "_hasShrinkwrap": false, "publish_time": 1518210809675, "_cnpm_publish_time": 1518210809675, "_cnpmcore_publish_time": "2021-12-16T11:57:27.099Z"}, "1.1.9": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.9", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "0f82dab6708f7c451e4a865b817057bc5a6b3c8e", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.9", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "acdc7dde0e939fb3b32fe933336573e2a7dc2b7c", "size": 4096, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.9.tgz", "integrity": "sha512-/+o3o6OV1cm3WKrO7U4wykU+ZICE6HiMEuravc2d03NIuM/VaRn5iMcoQ7NyxFXjvpmRICP2EER0YOnh4yIapA=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_1.1.9_1518170016033_0.0827503901708313"}, "_hasShrinkwrap": false, "publish_time": 1518170016709, "_cnpm_publish_time": 1518170016709, "_cnpmcore_publish_time": "2021-12-16T11:57:27.274Z"}, "1.1.8": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.8", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "8f59e68bd5c915a0d624e8e39354e1ccf672edf6", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.8", "_shasum": "c07b211c7c952ec1f8efd51a77ef0d1d3990a292", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c07b211c7c952ec1f8efd51a77ef0d1d3990a292", "size": 3939, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.8.tgz", "integrity": "sha512-Dnfc9ROAPrkkeLIUweEbh7LFT9Mc53tO/bbM044rKjhgAEyIGKvKXg97PM/kRizZIfUHaROZIoeEaWao+Unzfw=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion-1.1.8.tgz_1497251980593_0.6575565172825009"}, "directories": {}, "publish_time": 1497251981589, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497251981589, "_cnpmcore_publish_time": "2021-12-16T11:57:27.509Z"}, "1.1.7": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.7", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "892512024872ca7680554be90f6e8ce065053372", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.7", "_shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "size": 3936, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.7.tgz", "integrity": "sha512-ebXXDR1wKKxJNfTM872trAU5hpKduCkTN37ipoxsh5yibWq8FfxiobiHuVlPFkspSSNhrxbPHbM4kGyDGdJ5mg=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.7.tgz_1491552830231_0.7213963181711733"}, "directories": {}, "publish_time": 1491552831907, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491552831907, "_cnpmcore_publish_time": "2021-12-16T11:57:27.725Z"}, "1.1.6": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.6", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "791262fa06625e9c5594cde529a21d82086af5f2", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.6", "_shasum": "7197d7eaa9b87e648390ea61fc66c84427420df9", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7197d7eaa9b87e648390ea61fc66c84427420df9", "size": 3876, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.6.tgz", "integrity": "sha512-do+EUHPJZmz1wYWxOspwBMwgEqs0T5xSClPfYRwug3giEKZoiuMN9Ans1hjT8yZZ1Dkx1oaU4yRe540HKKHA0A=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.6.tgz_1469047715600_0.9362958471756428"}, "directories": {}, "publish_time": 1469047717117, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469047717117, "_cnpmcore_publish_time": "2021-12-16T11:57:27.918Z"}, "1.1.5": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.5", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.5.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "ff31acab078f1bb696ac4c55ca56ea24e6495fb6", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.5", "_shasum": "f5b4ad574e2cb7ccc1eb83e6fe79b8ecadf7a526", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f5b4ad574e2cb7ccc1eb83e6fe79b8ecadf7a526", "size": 3653, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.5.tgz", "integrity": "sha512-FtnR1B5L0wpwEeryoTeqAmxrybW2/7BI8lqG9WSk6FxHoPCg5O474xPgWWQkoS7wAilt97IWvz3hDOWtgqMNzg=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.5.tgz_1465989660138_0.34528115345165133"}, "directories": {}, "publish_time": 1465989663644, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465989663644, "_cnpmcore_publish_time": "2021-12-16T11:57:28.169Z"}, "1.1.4": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.4", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.5.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "1660b75d0bf03b022e7888b576cd5a4080692c1d", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.4", "_shasum": "464a204c77f482c085c2a36c456bbfbafb67a127", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "464a204c77f482c085c2a36c456bbfbafb67a127", "size": 3915, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.4.tgz", "integrity": "sha512-wpJYpqGrDNnMWoi1GX8s8C4/SkHCuuLV0Sxlkvc4+rEBTNkUI2xLiUU3McR0b5dVw71Yw50l+sBGhusHNnjFnw=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.4.tgz_1462130058897_0.14984136167913675"}, "directories": {}, "publish_time": 1462130061252, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462130061252, "_cnpmcore_publish_time": "2021-12-16T11:57:28.399Z"}, "1.1.3": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.3", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.3.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.4.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "f0da1bb668e655f67b6b2d660c6e1c19e2a6f231", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.3", "_shasum": "46bff50115d47fc9ab89854abb87d98078a10991", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "46bff50115d47fc9ab89854abb87d98078a10991", "size": 3910, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.3.tgz", "integrity": "sha512-JzSkuJYnfzmR0jZiCE/Nbw1I9/NL2Z2diIfhffu5Aq3nihHtfO8CNYcwxmAyTKYKWyte1b1vYBHMVhMbe+WZdw=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.3.tgz_1455216688668_0.948847763473168"}, "directories": {}, "publish_time": 1455216691874, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455216691874, "_cnpmcore_publish_time": "2021-12-16T11:57:28.620Z"}, "1.1.2": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.2", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.3.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.2.2"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "b03773a30fa516b1374945b68e9acb6253d595fa", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.2", "_shasum": "f21445d0488b658e2771efd870eff51df29f04ef", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f21445d0488b658e2771efd870eff51df29f04ef", "size": 3908, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.2.tgz", "integrity": "sha512-QY1LGlHZzEwE7NbolI6UYCtLE2zp0I49Cx7anmMGHjwPcb5E/fN/mk5i6oERkhhx78K/UPNEwLjLhHM3tZwjcw=="}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1448715537647, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448715537647, "_cnpmcore_publish_time": "2021-12-16T11:57:28.896Z"}, "1.1.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "^3.0.3"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "f50da498166d76ea570cf3b30179f01f0f119612", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.1", "_shasum": "da5fb78aef4c44c9e4acf525064fb3208ebab045", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "0.10.36", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "da5fb78aef4c44c9e4acf525064fb3208ebab045", "size": 3923, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.1.tgz", "integrity": "sha512-8sehXzl+5+hVq+azy8bdvi/vdY1DA0eKIM+k+wK4XqBAy3e0khAcxN+CMIf6QObpDLR4LXBBH8eRRR500WDidg=="}, "directories": {}, "publish_time": 1443391127098, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443391127098, "_cnpmcore_publish_time": "2021-12-16T11:57:29.088Z"}, "1.1.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "^3.0.3"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "b5fa3b1c74e5e2dba2d0efa19b28335641bc1164", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.0", "_shasum": "c9b7d03c03f37bc704be100e522b40db8f6cfcd9", "_from": ".", "_npmVersion": "2.1.10", "_nodeVersion": "0.10.32", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c9b7d03c03f37bc704be100e522b40db8f6cfcd9", "size": 8868, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.0.tgz", "integrity": "sha512-jW1t9kL3kiXzovHnEgYNuYMnF+hHB1TlyK2wox32dPrWRvwNEJlXz3NdB5mdjFK1Pom22qVVvpGXN2hICWmvGw=="}, "directories": {}, "publish_time": 1418756295116, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418756295116, "_cnpmcore_publish_time": "2021-12-16T11:57:29.340Z"}, "1.0.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "ceba9627f19c590feb7df404e1d6c41f8c01b93a", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.0.1", "_shasum": "817708d72ab27a8c312d25efababaea963439ed5", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.16", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "817708d72ab27a8c312d25efababaea963439ed5", "size": 9022, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.0.1.tgz", "integrity": "sha512-agencL/m7vghsxEHLqdfg0cz3hHCEo46p+VCthmo2ldRTsmW7DANziRJnYCzGPT2Rc6OaYoNmiC9Fq/6laK8Lg=="}, "directories": {}, "publish_time": 1417593519708, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417593519708, "_cnpmcore_publish_time": "2021-12-16T11:57:29.549Z"}, "1.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "55329dcf69a61c2ea76320c5e87a56de48682c80", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.0.0", "_shasum": "a01656d12ebbbd067c8e935903f194ea5efee4ee", "_from": ".", "_npmVersion": "2.1.8", "_nodeVersion": "0.10.32", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a01656d12ebbbd067c8e935903f194ea5efee4ee", "size": 7083, "noattachment": false, "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.0.0.tgz", "integrity": "sha512-lpqC6FxtM5XVWHdevRkMRPWSpsoLOWqurCALDPKm0VnLHf3DQ2rqFO8WBc6ierDnXeiMnCzwtDl6PgZrPY7xxA=="}, "directories": {}, "publish_time": 1417341535317, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417341535317, "_cnpmcore_publish_time": "2021-12-16T11:57:29.761Z"}, "0.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "dependencies": {"concat-map": "0.0.0", "balanced-match": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@0.0.0", "dist": {"tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-0.0.0.tgz", "shasum": "b2142015e8ee12d4cdae2a23908d28d44c2baa9f", "size": 3946, "noattachment": false, "integrity": "sha512-<PERSON><PERSON><PERSON>tiom0CcPQjWOvuqQsl/jP/GbJYO9oRJwJiZcB0f2e4PM3EAwoxAzTJBOcUJ0SSlKShb0wB5bkpzoH4YgbYg=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1381669130153, "_hasShrinkwrap": false, "_cnpm_publish_time": 1381669130153, "_cnpmcore_publish_time": "2021-12-16T11:57:29.947Z"}, "3.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}, "gitHead": "b01a637b0578a7c59acc7d8386f11f8d0710b512", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@3.0.0", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-P+6OwxY7i0tsp0Xdei2CjvVOQke51REB4c2d2wCckcMn6NBElNqLuzr6PsxFCdJ3i/cpGEkZ/Nng5I7ZkLo0CA==", "shasum": "2ba8d16a84bb3b440107587dae0fa59cf8672452", "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-3.0.0.tgz", "fileCount": 7, "unpackedSize": 12214, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEC9uzfKooJ89Q8QLlD+tzLeFwFe/78sLtWLDO3ReypKAiBnVIsCDBixEed2GXe6+kCRV/O2pdWrYiYU+YT9S1FG4A=="}], "size": 4716}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_3.0.0_1696685462916_0.3750340778742729"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-07T13:31:03.177Z", "publish_time": 1696685463177, "_source_registry_name": "default"}, "4.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "4.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}, "gitHead": "6a39bdddcf944374b475d99b0e8292d3727c7ebe", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@4.0.0", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-l/mOwLWs7BQIgOKrL46dIAbyCKvPV7YJPDspkuc88rHsZRlg3hptUGdU7Trv0VFP4d3xnSGBQrKu5ZvGB7UeIw==", "shasum": "bb24b89bf4d4b37d742acac89b65d1a32b379a81", "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-4.0.0.tgz", "fileCount": 8, "unpackedSize": 12770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrhNEy/hwZjHIlsHCYab0+IHgrz7kDfa1w6u/e+kx1EAIgOp8c3E2/Sn13tVi4fV1P31KM5fQX1SqJtVtpcVNa8gI="}], "size": 4938}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_4.0.0_1709035002841_0.7308632197804894"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-27T11:56:43.001Z", "publish_time": 1709035003001, "_source_registry_name": "default"}, "4.0.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}, "_id": "brace-expansion@4.0.1", "gitHead": "c85b8ad3f53d1eb65f4996a495cae61949855f7c", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-YClrbvTCXGe70pU2JiEiPLYXO9gQkyxYeKpJIQHVS/gOs6EWMQP2RYBwjFLNT322Ji8TOC3IMPfsYCedNpzKfA==", "shasum": "3387e13eaa2992025d05ea47308f77e4a8dedd1e", "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-4.0.1.tgz", "fileCount": 8, "unpackedSize": 12775, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDM35BPIuCbJPcYEBCh7CiAu49GUmmIYkH5k35GV1gmCQIhAKwPJK1YnN3VtTML0OB+JNUt9BSXLQMdK/TXeVbrLslu"}], "size": 4978}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/brace-expansion_4.0.1_1749625463581_0.5120348729969268"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T07:04:23.771Z", "publish_time": 1749625463771, "_source_registry_name": "default"}, "3.0.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "3.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}, "publishConfig": {"tag": "3.x"}, "_id": "brace-expansion@3.0.1", "readmeFilename": "README.md", "gitHead": "a057bebec555a951f378148351ce25c54000d4d2", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-d+NXbXQ463UPpTMIHWzVzp8x9lG5ALx1cR+0MUo9IztykL5+U5bN07D5rlT+yQGcr1mz7ydxFaw5FdaFYW247w==", "shasum": "ca8272e391c052107d076e60da89c4b8b06bdf3c", "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-3.0.1.tgz", "fileCount": 7, "unpackedSize": 12262, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEhfp8EcrJDnKMI+oXQmK2sWQEVXPhBm/CMyG28znj4wAiB6YiEk1m4Ka4QJFvNPVxa/Qm6fd+nlNUZKH/A4a6zVjg=="}], "size": 4763}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/brace-expansion_3.0.1_1749631452111_0.9477068221541256"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T08:44:12.277Z", "publish_time": 1749631452277, "_source_registry_name": "default"}, "2.0.2": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "2.0.2", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "publishConfig": {"tag": "2.x"}, "_id": "brace-expansion@2.0.2", "readmeFilename": "README.md", "gitHead": "a3efcee659ef0fb381e2b50d759c720900580a15", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "shasum": "54fc53237a613d854c7bd37463aad17df87214e7", "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.2.tgz", "fileCount": 5, "unpackedSize": 11534, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHowR6pWiAws0hC5C7DsbWOVB2Ob/pQlQIgpExp7SNSeAiBnxaRTaBM/EMpAPQicV/a54Zg9vvJzXmLkfYtCCGVOxw=="}], "size": 4372}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/brace-expansion_2.0.2_1749631716389_0.6531654539004599"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T08:48:36.535Z", "publish_time": 1749631716535, "_source_registry_name": "default"}, "1.1.12": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.12", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "publishConfig": {"tag": "1.x"}, "_id": "brace-expansion@1.1.12", "readmeFilename": "README.md", "gitHead": "44f33b47c5c6a965d507421af43e86cf5971d711", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "shasum": "ab9b454466e5a8cc3a187beaad580412a9c5b843", "tarball": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.12.tgz", "fileCount": 4, "unpackedSize": 11107, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCefRVlAiPWPz42vj2hoqvreB3Job7AZ0kiUGVtB1yl1gIge5j1MzW2qkqi81uRNwZK1QPOMLfb8Xo8f2zlR2sYDV0="}], "size": 4246}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/brace-expansion_1.1.12_1749631977985_0.4250458404269102"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-11T08:52:58.148Z", "publish_time": 1749631978148, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "homepage": "https://github.com/juliangruber/brace-expansion", "keywords": [], "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "_source_registry_name": "default"}