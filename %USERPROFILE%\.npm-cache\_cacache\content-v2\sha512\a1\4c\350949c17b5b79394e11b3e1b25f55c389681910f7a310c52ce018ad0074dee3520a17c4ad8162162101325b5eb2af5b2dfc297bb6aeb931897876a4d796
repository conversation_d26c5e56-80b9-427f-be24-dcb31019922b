{"_id": "@typescript-eslint/project-service", "_rev": "5186320-6835c47e74fa36c3a82bee49", "dist-tags": {"canary": "8.36.1-alpha.2", "latest": "8.36.0"}, "name": "@typescript-eslint/project-service", "time": {"created": "2025-05-27T13:56:14.743Z", "modified": "2025-07-09T13:59:57.390Z", "8.32.2-alpha.12": "2025-05-27T13:55:32.590Z", "8.33.0": "2025-05-27T17:21:37.262Z", "8.33.1-alpha.0": "2025-05-29T13:16:29.149Z", "8.33.1-alpha.1": "2025-05-29T13:29:37.541Z", "8.33.1-alpha.2": "2025-05-29T15:14:31.226Z", "8.33.1-alpha.3": "2025-05-30T07:17:12.111Z", "8.33.1-alpha.4": "2025-05-31T08:23:19.551Z", "8.33.1-alpha.5": "2025-06-01T11:27:50.578Z", "8.33.1-alpha.6": "2025-06-02T14:13:08.223Z", "8.33.1": "2025-06-02T17:19:33.217Z", "8.33.2-alpha.0": "2025-06-02T17:32:06.262Z", "8.33.2-alpha.1": "2025-06-05T16:29:56.832Z", "8.33.2-alpha.2": "2025-06-06T12:17:57.877Z", "8.33.2-alpha.3": "2025-06-07T03:00:15.772Z", "8.33.2-alpha.4": "2025-06-09T07:11:25.062Z", "8.33.2-alpha.5": "2025-06-09T07:25:01.041Z", "8.33.2-alpha.6": "2025-06-09T07:37:34.143Z", "8.33.2-alpha.7": "2025-06-09T07:44:23.504Z", "8.33.2-alpha.8": "2025-06-09T11:29:53.261Z", "8.33.2-alpha.9": "2025-06-09T11:36:37.693Z", "8.34.0": "2025-06-09T17:18:45.685Z", "8.34.1-alpha.0": "2025-06-09T17:31:16.689Z", "8.34.1-alpha.1": "2025-06-10T23:26:11.046Z", "8.34.1-alpha.2": "2025-06-13T23:57:45.965Z", "8.34.1-alpha.3": "2025-06-14T21:56:05.815Z", "8.34.1-alpha.4": "2025-06-16T11:34:16.327Z", "8.34.1-alpha.5": "2025-06-16T13:40:39.728Z", "8.34.1-alpha.6": "2025-06-16T13:53:32.408Z", "8.34.1-alpha.7": "2025-06-16T14:06:59.650Z", "8.34.1-alpha.8": "2025-06-16T14:18:38.717Z", "8.34.1": "2025-06-16T17:19:27.231Z", "8.34.2-alpha.0": "2025-06-16T17:32:15.255Z", "8.34.2-alpha.1": "2025-06-18T02:06:16.193Z", "8.34.2-alpha.2": "2025-06-23T16:42:19.754Z", "8.34.2-alpha.3": "2025-06-23T17:13:07.089Z", "8.35.0": "2025-06-23T17:19:33.437Z", "8.35.1-alpha.0": "2025-06-23T17:26:12.263Z", "8.35.1-alpha.1": "2025-06-23T17:39:23.667Z", "8.35.1-alpha.2": "2025-06-24T22:06:51.122Z", "8.35.1-alpha.3": "2025-06-24T22:15:42.981Z", "8.35.1-alpha.4": "2025-06-25T12:15:27.543Z", "8.35.1-alpha.5": "2025-06-26T13:07:00.695Z", "8.35.1-alpha.6": "2025-06-26T13:45:30.142Z", "8.35.1": "2025-06-30T17:18:54.266Z", "8.35.2-alpha.0": "2025-07-01T09:26:41.376Z", "8.35.2-alpha.1": "2025-07-01T11:08:01.271Z", "8.35.2-alpha.2": "2025-07-01T11:32:18.772Z", "8.35.2-alpha.3": "2025-07-01T11:39:37.099Z", "8.35.2-alpha.4": "2025-07-01T12:08:57.265Z", "8.35.2-alpha.5": "2025-07-01T13:58:07.107Z", "8.35.2-alpha.6": "2025-07-05T17:16:37.671Z", "8.35.2-alpha.7": "2025-07-06T22:29:00.091Z", "8.35.2-alpha.8": "2025-07-06T22:43:32.563Z", "8.35.2-alpha.9": "2025-07-06T23:38:53.649Z", "8.35.2-alpha.10": "2025-07-07T12:34:56.833Z", "8.35.2-alpha.11": "2025-07-07T12:48:55.210Z", "8.36.0": "2025-07-07T17:19:35.494Z", "8.36.1-alpha.0": "2025-07-07T17:32:17.190Z", "8.36.1-alpha.1": "2025-07-08T09:50:04.792Z", "8.36.1-alpha.2": "2025-07-09T13:59:38.047Z"}, "versions": {"8.32.2-alpha.12": {"name": "@typescript-eslint/project-service", "version": "8.32.2-alpha.12", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.32.2-alpha.12", "@typescript-eslint/types": "^8.32.2-alpha.12", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.32.2-alpha.12", "gitHead": "d2ffec7988976a07495bde7270630db1a0fe1d32", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-nl7+raox8zEn4iSPK6uBkU5XkGJuOtom22vZNflOHE9nnTqp5sjI1YBV0q5fS4vXcbbXXJQt8oZmSkGT6DvvFw==", "shasum": "4affcea3d3d0e2f3a1b6c90b27e7705f075767ba", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.32.2-alpha.12.tgz", "fileCount": 12, "unpackedSize": 14991, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.32.2-alpha.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIQCEgdGAcSrGHn90yC/aBQTi7+4oMAmhNZkw72/Hwj3x6AIfUT20cppuljZax5xSIUvSpBH1NFjSdNSeB7L2oYJedw=="}], "size": 5413}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.32.2-alpha.12_1748354132416_0.6180458173114209"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-27T13:55:32.590Z", "publish_time": 1748354132590, "_source_registry_name": "default"}, "8.33.0": {"name": "@typescript-eslint/project-service", "version": "8.33.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.0", "@typescript-eslint/types": "^8.33.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.0", "gitHead": "d2ffec7988976a07495bde7270630db1a0fe1d32", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-d1hz0u9l6N+u/gcrk6s6gYdl7/+pp8yHheRTqP6X5hVDKALEaTn8WfGiit7G511yueBEL3OpOEpD+3/MBdoN+A==", "shasum": "71f37ef9010de47bf20963914743c5cbef851e08", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.0.tgz", "fileCount": 12, "unpackedSize": 14964, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF3ApMep1e/1mjsC2VyaLNjOHvU0p8vg7B/S465C/RiEAiEAoFN5XNiv0oMaZKlWqMB1OR/qM1tUGGXlDVlqQEIgbPM="}], "size": 5406}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.0_1748366497082_0.1615844794149115"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-27T17:21:37.262Z", "publish_time": 1748366497262, "_source_registry_name": "default"}, "8.33.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.0", "@typescript-eslint/types": "^8.33.1-alpha.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.0", "readmeFilename": "README.md", "gitHead": "02ebbe10f6d9fc6a415b33a76bd76eb5402083e5", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UI5aMGOkL4FaicHIbD/HTteQ3svXtL4RtXYmznXF5hh+ogNLpc2z5RIyHs8JoXEyHPlRPkimgOEStl/w0S1JQQ==", "shasum": "4ee9e028c05c3f4c0a8798c3c385b5fd2e09d826", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.0.tgz", "fileCount": 12, "unpackedSize": 14988, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCEiZR5/YoKE7hOcq06eSABWUkMzQ31679TpHaUvb/jjwIhAN6IW1y0BtmJf42gctTBlQZLmQCkUnsIV2tWwv/uUCMH"}], "size": 5410}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.0_1748524588954_0.354976823621294"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-29T13:16:29.149Z", "publish_time": 1748524589149, "_source_registry_name": "default"}, "8.33.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.1", "@typescript-eslint/types": "^8.33.1-alpha.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.1", "readmeFilename": "README.md", "gitHead": "294fb238ced1d96d379336ffbb27122a66dd1bb4", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-BO9hGSnv5n2pO36QdbjdF9TYdy3XEX++gBu7zoxANV9dk7ZrpwWrIHpnWKLwPBhtQ7/0n1jJQaYcDZA9vQ1Z3A==", "shasum": "20443a622ba21340a6da435a3355491abc27f917", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.1.tgz", "fileCount": 12, "unpackedSize": 14988, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCm7UbuR8b+LDR4W4hFf9rWEt2SOkatP6kQ0aXsB/s50QIhAMY0PrND4eNkCjjHF48WhooKXoDxXY3Q+jIY0S8Bjx1v"}], "size": 5410}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.1_1748525377374_0.8814667522230781"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-29T13:29:37.541Z", "publish_time": 1748525377541, "_source_registry_name": "default"}, "8.33.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.2", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.2", "@typescript-eslint/types": "^8.33.1-alpha.2", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.2", "readmeFilename": "README.md", "gitHead": "702cea862db34b980e580f46314141c24024ee47", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-stHyIQrbiXGB9VJ3f6NFKu8SmYQJYrpu63rapNCNzvIDqGBqnd567affgbq5atM2uSRvwGSO3Oc3uegCy/stsg==", "shasum": "9e530205f91fc225ec72843727c887dec2a4a04f", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.2.tgz", "fileCount": 12, "unpackedSize": 14988, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCgepLIkFrHEKc3w9st+xvqm512JG387GvW5iuvxIokIgIhAN/XhfS2W3aNZPIRgBH12LGt2lxabcDs/ed2nv/WiCqz"}], "size": 5410}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.2_1748531671039_0.07273057588912257"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-29T15:14:31.226Z", "publish_time": 1748531671226, "_source_registry_name": "default"}, "8.33.1-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.3", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.3", "@typescript-eslint/types": "^8.33.1-alpha.3", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.3", "readmeFilename": "README.md", "gitHead": "4bc72143fd73614d12ea7bf38a9e223f60066da3", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-2af9OUb2KDOI26TE2o9mgDEOoreCti2RFA1k1QT5Df3zz0KlnSrmhzlLVFFk8JqNFbQy8v3XfryQN7A+PYBKEQ==", "shasum": "5f7c102ac2437a521ea9ccbe99534dae886e05fd", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.3.tgz", "fileCount": 12, "unpackedSize": 14988, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFA7+wrPa4vidN+J9xjYrVwvOVriKMzFn84UxtCfGbk7AiB0qAdopoaf02NkOfUU/rPdg3V1gBI+9xUvGwPgzZiMsQ=="}], "size": 5410}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.3_1748589431884_0.8297070840032168"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-30T07:17:12.111Z", "publish_time": 1748589432111, "_source_registry_name": "default"}, "8.33.1-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.4", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.4", "@typescript-eslint/types": "^8.33.1-alpha.4", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.4", "readmeFilename": "README.md", "gitHead": "e4933170f75b3a8a0c9bf3985fb4d2ddb6e4b4c6", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-<PERSON><PERSON>+RrBxkuir5EwJ8NIISq+HXyx9QkwWvLn/EZu8eEN87K0l3bReuvNV46wIIF2s9NJxyJciPpzowThbGckTbg==", "shasum": "3ee8823f67853785a3dbcb95c9006f9575937921", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.4.tgz", "fileCount": 12, "unpackedSize": 14988, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD2xwqsrb04GCYYrhIM9rgBCsKd4eh+6QNCN09IUiXvkwIgTrEAYr9RoT3Ma41aBEK4X7ulqjpmnIMro/Ifba+Yl2I="}], "size": 5410}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.4_1748679799382_0.6293363497260356"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-31T08:23:19.551Z", "publish_time": 1748679799551, "_source_registry_name": "default"}, "8.33.1-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.5", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.5", "@typescript-eslint/types": "^8.33.1-alpha.5", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.5", "readmeFilename": "README.md", "gitHead": "9d46857e1377980bf4878fb273d5ef3848075bb5", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/tVU1WUzP3+VDdtm3aoGBlCuYn7yLmJAOmQMRV3TymPe/UElT5G/UO6kS7ptF1agk3855cxYCjU1/Gt9hq4F5w==", "shasum": "15354f4f127d47c3f22f9751a0bc34c90e60d550", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.5.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF+GeuYX7IcBl40lM69z8CW0uiRQ6Ecu7W3C6WjDDUI6AiEAvjnwUQ1IOLRkWT+AS66yCwiiI+BZD595FXCGuksBimw="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.5_1748777270368_0.4560502713453487"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-01T11:27:50.578Z", "publish_time": 1748777270578, "_source_registry_name": "default"}, "8.33.1-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.33.1-alpha.6", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1-alpha.6", "@typescript-eslint/types": "^8.33.1-alpha.6", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1-alpha.6", "readmeFilename": "README.md", "gitHead": "c14bcac24268636dddc8c75f85f66b42e8dbbf76", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-T2xJ6lqQ7yGG3k2Ulqn2w7/SGqMSxYE3qJOjRWTvrHmamj+J7sgLpaF9rC1wtPkt7Rvtjt/dvspT9rpYo1TkMQ==", "shasum": "3f1750837fa4548e3328dda49e4e07561ea00570", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1-alpha.6.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG7KfpQSOHLM1n6BooDMdgEwc32gnXT5bOKJ5cr2d/w3AiAbqlFrXMeOQzJc4OpKf3TOMR467gST2H1fCDXxWpKOcw=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1-alpha.6_1748873588052_0.011852546697939559"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-02T14:13:08.223Z", "publish_time": 1748873588223, "_source_registry_name": "default"}, "8.33.1": {"name": "@typescript-eslint/project-service", "version": "8.33.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.1", "@typescript-eslint/types": "^8.33.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.1", "gitHead": "936f35022c1e1357da82c4b958b7bff2563e2075", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-DZR0efeNklDIHHGRpMpR5gJITQpu6tLr9lDJnKdONTC7vvzOlLAG/wcfxcdxEWrbiZApcoBCzXqU/Z458Za5Iw==", "shasum": "c85e7d9a44d6a11fe64e73ac1ed47de55dc2bf9f", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.1.tgz", "fileCount": 12, "unpackedSize": 15028, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD5c2KrOBVE1ZKTgErX2YArm0PgVWeJY9WZMsjMLfrM3AIgFBBlXTyU0ITGtVGdwcRFlG0Zb5C0PZPyye0iMozo2xs="}], "size": 5428}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.1_1748884773037_0.03527614410829916"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-02T17:19:33.217Z", "publish_time": 1748884773217, "_source_registry_name": "default"}, "8.33.2-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.0", "@typescript-eslint/types": "^8.33.2-alpha.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.0", "readmeFilename": "README.md", "gitHead": "936f35022c1e1357da82c4b958b7bff2563e2075", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hZpU6jb7mhZyRAqx+na8proawgexU4KM10dRLiy2xxsVZMvNbiurtCMWb9r0C9SrlFP9txsnZxjNVUdCdF8mUg==", "shasum": "70f0178f6879da0419710a55d65a091d7804455d", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.0.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGX+JsUBux+WdujWvIrd50a3RLWXfemlsNOeFkU0wzMuAiA3An/T0bw8rtUD6j3lQGxQkRrKFtEg9EPKf07XU8Ly6w=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.0_1748885526057_0.08258797253733063"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-02T17:32:06.262Z", "publish_time": 1748885526262, "_source_registry_name": "default"}, "8.33.2-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.1", "@typescript-eslint/types": "^8.33.2-alpha.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.1", "readmeFilename": "README.md", "gitHead": "0f5c59c1b116ced6aaff7b2c632f924b2ca49596", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NN8mKbgqX25O04sPn3kgrcB5rORIX5daviHzhNKl8PjZh+F2y4ME8kFF1Cf8VviOZjKu8oaqQInolSzhJ3QPtw==", "shasum": "a249476ca18d4c081d712ea870b811c7eae1b797", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.1.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHn5ZcQCfs8qnQYaPCj/akNsqeHFZhDERsmWMhjmRcyhAiEAxnubuaLsdBKacHmdJT8kQdnTYPRhvLlctQFjdf7w+vY="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.1_1749140996652_0.9668931088580712"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-05T16:29:56.832Z", "publish_time": 1749140996832, "_source_registry_name": "default"}, "8.33.2-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.2", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.2", "@typescript-eslint/types": "^8.33.2-alpha.2", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.2", "readmeFilename": "README.md", "gitHead": "f5eb171473a248fd076c5a223c41643f144d4787", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/z9g8/sYPBKXLJjm+iWixWCJeJDpOEhchuItNKEJUOAwSID30Fp5tK+Nyye7WtT3+hTC5Duj98aBfiR7BmjcdQ==", "shasum": "383ee4b61fbacc372c74c1333ca2fc774a30dc56", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.2.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC1hztnnF6jRsE3H8rQQR50+pK/k5pYTBw1yQc9R2gv2wIhAKJbQmuSEkr9lSKLzEIALmr++ZzBsExVW0wLxYYYDOq4"}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.2_1749212277718_0.41355156081018807"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T12:17:57.877Z", "publish_time": 1749212277877, "_source_registry_name": "default"}, "8.33.2-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.3", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.3", "@typescript-eslint/types": "^8.33.2-alpha.3", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.3", "readmeFilename": "README.md", "gitHead": "1e0ba6253cdecbbb69b37537599aad9a21ed310e", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-D9+kVhlS+NErd6puTBgBPkjhLC0p+c6X0jz14XUNgXX7IlRWYJig58Ybknpl906bSslFl6vonrhzIunZm/WPTg==", "shasum": "321a1429bde072924ff930808366aaedc21321ef", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.3.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCYHAUW/KLKBgra+IgYW1C9rHypSAZyErIz5yokMLjbFAIgZ4MfOCxCyhGhZu5R14MLkZJKRQhaO0a59MwXeuPj9zI="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.3_1749265215564_0.07342672413466578"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-07T03:00:15.772Z", "publish_time": 1749265215772, "_source_registry_name": "default"}, "8.33.2-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.4", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.4", "@typescript-eslint/types": "^8.33.2-alpha.4", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.4", "readmeFilename": "README.md", "gitHead": "2df6f99883b8ed2e762c602f80a05c854aef87dc", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-kd04UFwBKQCEeVCo/wmomvq+RObVAKwENWBNB8Q9XSFEQh817zhCFoySsd+Gf9rQ/RByNGSDm2hoS6ZLBQJyNg==", "shasum": "a0629fabf3373a7f0de0bff2ef33dbc90a26a9cc", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.4.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEMBpQuH/f3dwh7MoOMhNDUc2nbGaABYaJ/+QKoE07whAiBtlFhcJwIJbjuWJCXmjP0WbyZkFIJbo6rsu+ADjNMTbA=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.4_1749453084895_0.6905814905834604"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T07:11:25.062Z", "publish_time": 1749453085062, "_source_registry_name": "default"}, "8.33.2-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.5", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.5", "@typescript-eslint/types": "^8.33.2-alpha.5", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.5", "readmeFilename": "README.md", "gitHead": "d159a211d3970978f7311e5227462f30a53a060c", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YbUBxXmq1zi6pbOCnfU9wksKf84RWaKhm62zP1WfeVCQI+dMBQZ0A8/34nzc5NMBTPhKG2vfKUnpVPjveS/lDQ==", "shasum": "6124c22255896c42c5d6ee0b8d54acd8b5de8c63", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.5.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCMmruoII6OMTEO0qeKfXmpk0X+qLRkyFUBPsTg0wSLdAIgLL93m7qfcCQwLvCGzkYXCDgb+/siCTaOMUff1+q+/QU="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.5_1749453900872_0.7808495997193807"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T07:25:01.041Z", "publish_time": 1749453901041, "_source_registry_name": "default"}, "8.33.2-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.6", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.6", "@typescript-eslint/types": "^8.33.2-alpha.6", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.6", "readmeFilename": "README.md", "gitHead": "685e530478362c9e5a43db01aadc200a361cbc6f", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3<PERSON><PERSON>Vs4k5avpQlaMk2pglz10PljZZK9G841J1yMY58KvVMcSy1f68z8d723Jkfv3ozqL2wSCfw4NfhVU15mefQ==", "shasum": "e0977ef9b0417eff955360a13a42c662ed59e7b7", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.6.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCFDvRD+baMz1n2aVgDt+v13d2jHyfUgmXBGipVVCVguQIgEEKHP21ydhg+oSjWiPO4ZbL7Nn6c39a1BH/mQaBKB2E="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.6_1749454653981_0.42583328855962277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T07:37:34.143Z", "publish_time": 1749454654143, "_source_registry_name": "default"}, "8.33.2-alpha.7": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.7", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.7", "@typescript-eslint/types": "^8.33.2-alpha.7", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.7", "readmeFilename": "README.md", "gitHead": "b2eded83f68d8e1685d1ea5e826bbfa891086b7f", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FwKwepqSQX7KREPik7iD7XWdxAB0zs7s9lXXEjsBdz+vkV3ZBkSxNE/vKfrZh7qYRPecDJ6mmWYT+9b69mSAXQ==", "shasum": "9d9e4a064d5b15372a3c1a4a3965081e5faa5e41", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.7.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG7goP1NpYj/UMKVixSvjXZ2i93kdMqIF8EH3fZDBrI4AiEAlKNPWId3+FiFbKVutTsrUffhiP6h22yqPbARIdk0ZC8="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.7_1749455063339_0.3394721494024946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T07:44:23.504Z", "publish_time": 1749455063504, "_source_registry_name": "default"}, "8.33.2-alpha.8": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.8", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.8", "@typescript-eslint/types": "^8.33.2-alpha.8", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.8", "readmeFilename": "README.md", "gitHead": "8208974140a1e658e5234435836476642e9a56e1", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-D1idHFoXHr3sZO9gSnltq1BD5K19+gONH6r3hwcMMOLPOccG1d6lLAQh9uu2dbHwJ4iZlbtDeEF55fxNyN8RRA==", "shasum": "7824882f82ef5b1243ae187c7fbef47ef94697c7", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.8.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDq6iLBG7+lBYq+KNq+noGUHiFuSlJKuWZ+x9JeMSJrRwIhAM0bESXNByNBc2xvxyCbkCzgmQYKI/6kkz1++bK6Cgwm"}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.8_1749468593041_0.7045012867511631"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T11:29:53.261Z", "publish_time": 1749468593261, "_source_registry_name": "default"}, "8.33.2-alpha.9": {"name": "@typescript-eslint/project-service", "version": "8.33.2-alpha.9", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.2-alpha.9", "@typescript-eslint/types": "^8.33.2-alpha.9", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.33.2-alpha.9", "readmeFilename": "README.md", "gitHead": "9b598778b4033e2cd5bdf3fa6e1af32c84a34a0b", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ZraJH61EAapNVft3cTzfyJ2tgnX//B0VKW/prp+UX2iiDuQCikAWB1DdrkJ5DRzZGD/fTxrK2Md7xSc1xHZnhA==", "shasum": "05cca6e5c46b4a29c3b08686eca199c760fe1f3d", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.2-alpha.9.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.33.2-alpha.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDS38MD1A/Ok3eGJ8R0vXS0eQOZvvg8pmlCbXBCN65u0QIgXMzp4WVe0LBMYsPfZjLnp+Tkc+KAH9x2Ie/glW14B2I="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.33.2-alpha.9_1749468997516_0.21813173326277946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T11:36:37.693Z", "publish_time": 1749468997693, "_source_registry_name": "default"}, "8.34.0": {"name": "@typescript-eslint/project-service", "version": "8.34.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.0", "@typescript-eslint/types": "^8.34.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.0", "gitHead": "8915a477608892596fc6ed2bc45dbbac7f41a361", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-iEgDALRf970/B2YExmtPMPF54NenZUf4xpL3wsCRx/lgjz6ul/l13R81ozP/ZNuXfnLCS+oPmG7JIxfdNYKELw==", "shasum": "449119b72fe9fae185013a6bdbaf1ffbfee6bcaf", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.0.tgz", "fileCount": 12, "unpackedSize": 15028, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAE1a/0hVWkLJlUtinzOWZ50ObmvnYKmtdrVwX6Ngvh5AiBuwqWSCilTVuTCQ95CZV+hh2lotZj4H8Jz/2smWEqUXw=="}], "size": 5428}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.0_1749489525522_0.3132994898594925"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T17:18:45.685Z", "publish_time": 1749489525685, "_source_registry_name": "default"}, "8.34.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.0", "@typescript-eslint/types": "^8.34.1-alpha.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.1-alpha.0", "readmeFilename": "README.md", "gitHead": "8915a477608892596fc6ed2bc45dbbac7f41a361", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-oKEGzC5KRwOxQlPsRH0TE83QF9hBM7Nd9f+Z0NkGUuA4Scp1MPOxYpzQedGcjpTLtpozH0i7q06qVCY22AlF9A==", "shasum": "55450653bc5026cf2f0b0a2cb2adbb0045dc698e", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.0.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDY8UPyYaAsrOSCSCHemP4/+U4z3sql7EeZkBNmmBaHHAiBChWvWFS5e4gu1rkKW+29YupqrgTsmbgSJ90624fEe4w=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.1-alpha.0_1749490276507_0.7849202643405693"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T17:31:16.689Z", "publish_time": 1749490276689, "_source_registry_name": "default"}, "8.34.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.1", "@typescript-eslint/types": "^8.34.1-alpha.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.1-alpha.1", "readmeFilename": "README.md", "gitHead": "445514aa1c9a2927051d73a7c0c4a1d004a7f855", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-AhxkPRivRTTiUw4GAWODVwJy17tjm3IEOtQFLMWsPGnF9VaHxUr2Cld2aC9IGMEMHr2GCl7ebesdgEeMp6mi4w==", "shasum": "77377d778e8c8f6a2e6a7b362119eb7818116a1b", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.1.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCLIlYfTQ/rZCAUfuTHyUpJ0r9/3hDLC1ljubwQqvqRGwIgU3eFWNX0tbTDEIUpTd8sFV5VcjON7nXcapOuXnalW9M="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.1-alpha.1_1749597970826_0.14491991145322602"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-10T23:26:11.046Z", "publish_time": 1749597971046, "_source_registry_name": "default"}, "8.34.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.2", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "d38b6653bb4145fab1db19e98b168216f26378c5", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-v3+RBVmfoieGv6RR2+CA+2DuFNkM7/Et2B/N1k/5WgINj9Nth3C4HRhCKIlWvIZvYa2ACj3GFUV9iWNULL+CFA==", "signatures": [{"sig": "MEQCIHedNScc2cz7R9ludr6ZKFnmCY/oR7kwE8eLGnw3JhrAAiBJv8e0To3twByT1MCBlMtQp1Dk1LCVFZL3RcjzCtv1RA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "de8943e98e188d83801ec1044ffc69451db1aa63", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.2", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.2_1749859065758_0.5253278537166235", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-13T23:57:45.965Z", "publish_time": 1749859065965, "_source_registry_name": "default"}, "8.34.1-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.3", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "e028542aef72047f7f9aae2c7c1eef6b22736316", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.3.tgz", "fileCount": 12, "integrity": "sha512-OhA0WukQ5TJoCPyUuy4KgVLWxeXvIBsbrlo9i79NHIZkX+0nqhpDXQy64lDsz/y3S4aox8RFkMzoPq/Li3kC1g==", "signatures": [{"sig": "MEYCIQC5b621PqaKfvsF/jy/5W3FkUSOpf4y4wCWr2uOCWO6iwIhAIV2G+inhjw6BmGmQxUv72hYheU7Oo/3calB8dJbA8g+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "af94f163a1d6447a84c5571fff5e38e4c700edb9", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.3", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.3_1749938165624_0.17442314310233376", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-14T21:56:05.815Z", "publish_time": 1749938165815, "_source_registry_name": "default"}, "8.34.1-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.4", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "6454412e0a7aa03d579dd0a5f6c75ae9821f7676", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.4.tgz", "fileCount": 12, "integrity": "sha512-Cz5zBr6TXNSBzNFGj2viP1my+53Mh7iU1HU+HvSHEK17ZcIjpn1FU2A8cvClNK0CoSTXRwF3UDv57KsE02x4Lw==", "signatures": [{"sig": "MEQCIHNmI3ag0m640pfTOZmtxwAaUtL+thCFFvRYf6K6tGbbAiAVs20BPl149aO/k8XPULax7iaFlq11uUbg47bWWEA8cg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "c971881e7af3e8e91f45391e6653a2815f725c3a", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.4", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.4_1750073656145_0.8955916284625582", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T11:34:16.327Z", "publish_time": 1750073656327, "_source_registry_name": "default"}, "8.34.1-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.5", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "35e14aaacff9831314f7d47ec9bb5ed7f9fcf59b", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.5.tgz", "fileCount": 12, "integrity": "sha512-QgPU3XpcN+XUUL7lUbtbMzT0r1i5kFIeo7exRxNeeAme5y3b8zilyETggPWJUBbiQkHY3L4s0+I8yFG9qiBc1Q==", "signatures": [{"sig": "MEUCIQCpVHN6Ixz5/Ru4CR7vHJtkRIUCuA2ss2FFjG55asqWegIgH1ld+608KMo6ZHJ9oa+hP74iRRu+03IIeNVGkbe+o2Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "f9d4d10c2330b6d646eff148b7648a84b7387a1e", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.5", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.5_1750081239565_0.8904283800137323", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T13:40:39.728Z", "publish_time": 1750081239728, "_source_registry_name": "default"}, "8.34.1-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.6", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "52f36c3edfa66352bb461d8e3b04096c02e0ef55", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.6.tgz", "fileCount": 12, "integrity": "sha512-mpvdRf53iVxQSjkbJsnGtZALBTSAkFeShYSo+kFWZXpwyzOtIsS1ISRTgwudyT5Gbc4D0+e6GJ4rI4IA5QKhhg==", "signatures": [{"sig": "MEUCIF2TfhhhRIGZgyQIMebDs0HPKW2axEtfrbgh59gEPF2JAiEA2Fz0Sn+BRMdLYD6mg4hh69aU2ucBlAw3ipf3BBbua/g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "8a69a33ab1e22c7e4b3727aa004fb58b98fd4a3b", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.6", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.6_1750082012237_0.19115493812732964", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T13:53:32.408Z", "publish_time": 1750082012408, "_source_registry_name": "default"}, "8.34.1-alpha.7": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.7", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "8f16e32039649cd5f36e93597d5050ef34ce800b", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.7.tgz", "fileCount": 12, "integrity": "sha512-7<PERSON><PERSON>+HDPli2QMVWWmFJ8r35oRx6r2g5j7W+LoIoJZ09r9wDMVaHrbcQZigTAQKowjklOTF7/oxQvZOKH70D2g==", "signatures": [{"sig": "MEYCIQCFUQJ1laoPReDBACz6kiR5Xt4D/vj94OFqefM0iEpChAIhANCmxEkXm6av39tb9dqwtkEKDV7l3KwizV737EE+ZSLo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "2fbae4863fc39279f61ba77bfe01e080a5796072", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.7", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.7_1750082819412_0.13132084178359738", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T14:06:59.650Z", "publish_time": 1750082819650, "_source_registry_name": "default"}, "8.34.1-alpha.8": {"name": "@typescript-eslint/project-service", "version": "8.34.1-alpha.8", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "a1479e69348e311a98428e33a36b0fbbd94b9dcb", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1-alpha.8.tgz", "fileCount": 12, "integrity": "sha512-0syHs/kXb8Y/9jjIUKRqX51tIIgmwOxnwFIL+y345gFxnOUuxT8Vcy09xhARLVH/rrmehEkGePK04w4j7G1r+w==", "signatures": [{"sig": "MEUCIQD4hjaOvaL+WBk4mbDfG0n2wEksf2gkzr7vdqvWS9TQ5AIgOISOxWRaX/iVp2/DSo6VxRdf8zD5FkYkK2ih8wwbmFI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.1-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15052, "size": 5437}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "3a8369d2c5798ef3187c8ff412d409e2d5e17726", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1-alpha.8", "@typescript-eslint/tsconfig-utils": "^8.34.1-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1-alpha.8_1750083518518_0.48338070023151714", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T14:18:38.717Z", "publish_time": 1750083518717, "_source_registry_name": "default"}, "8.34.1": {"name": "@typescript-eslint/project-service", "version": "8.34.1", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "_id": "@typescript-eslint/project-service@8.34.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://typescript-eslint.io", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "dist": {"shasum": "20501f8b87202c45f5e70a5b24dcdcb8fe12d460", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.1.tgz", "fileCount": 12, "integrity": "sha512-nuHlOmFZfuRwLJKDGQOVc0xnQrAmuq1Mj/ISou5044y1ajGNp2BNliIqp7F2LPQ5sForz8lempMFCovfeS1XoA==", "signatures": [{"sig": "MEUCIQDcFNAmQ1D73qLuTgjZjnCZE/ywGEpKyBUeLRAno2CZhQIgGqcpwi8/JKh1koOGrLs9SqzZfjLIska1X31ANLh2hms=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15028, "size": 5428}, "type": "commonjs", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/typescript-eslint", "type": "opencollective"}, "gitHead": "ccd07914d933c3f7a284c9a7df5b1d6d40495fc5", "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "typecheck": "yarn run -BT nx typecheck"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "type": "git", "directory": "packages/project-service"}, "_npmVersion": "10.8.2", "description": "Standalone TypeScript project service wrapper for linting.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"debug": "^4.3.4", "@typescript-eslint/types": "^8.34.1", "@typescript-eslint/tsconfig-utils": "^8.34.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/project-service_8.34.1_1750094367003_0.4597727850989952", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T17:19:27.231Z", "publish_time": 1750094367231, "_source_registry_name": "default"}, "8.34.2-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.0", "@typescript-eslint/types": "^8.34.2-alpha.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.2-alpha.0", "readmeFilename": "README.md", "gitHead": "ccd07914d933c3f7a284c9a7df5b1d6d40495fc5", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-zMhINy0qe0OONI4ja2UKj+n3aWwtivvu56cY2s39Xal4phf7Sg8sjFwJgq7IppSXmI30EwY6hqP82+UFQhjqbQ==", "shasum": "bb13fefc13d77da22e8817a4b54a22ec62523853", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.0.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD8+YOgREslVmvWBfg+QbuluBK1v227qreLt+BUeFeMEgIgVxmJEyLtaUWoSyWak2ngvwzlYBH+lW0vH+2rLBYyKRs="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.2-alpha.0_1750095135083_0.5049115101507413"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-16T17:32:15.255Z", "publish_time": 1750095135255, "_source_registry_name": "default"}, "8.34.2-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.1", "@typescript-eslint/types": "^8.34.2-alpha.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.2-alpha.1", "readmeFilename": "README.md", "gitHead": "76cc62cb37353711b64e3353bb62e2e0a688d629", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-N3zlET/lYZU5tzpBJGqmcUBZ0RQNB4J9mNj3jW6M9FHPPWAJnTXklSlaLnIutHCOwjOk5NdsJgepF3S9cxlsAA==", "shasum": "0e9d418478d5e929853ac77e01b931e7d6eddb6f", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.1.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEihkzCxenhmir8vnfRZckMpRB/pOnPaUh38fpZFSDl0AiEAyDkI5rbo4+StswbkkeTwr/ZnnYEUE8wQALMkFuxFJb8="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.2-alpha.1_1750212375989_0.2732291713459116"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-18T02:06:16.193Z", "publish_time": 1750212376193, "_source_registry_name": "default"}, "8.34.2-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.2", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.2", "@typescript-eslint/types": "^8.34.2-alpha.2", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.2-alpha.2", "readmeFilename": "README.md", "gitHead": "2e35e3a7bf03654730039ec432cbf445819057fd", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-X08M6x9wS5anWiGupyfV2sL2+upYRtoahZJxNke1sXb3ymrZBPXK6exHKtBseFLidFMZAoMisPvt2YfdCbP3Zw==", "shasum": "1283dc6f1dda97cb3835696e1e4943ce3c12af18", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.2.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDi/4NpVXD6jqFaJk8tU36OChqIgM4dNTC1g7qCrr56EAiEA98lX5aLGimiA2Mc/VmPHwXsy/IY+GUyWBHw79Ijnkt0="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.2-alpha.2_1750696939580_0.867848147981273"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T16:42:19.754Z", "publish_time": 1750696939754, "_source_registry_name": "default"}, "8.34.2-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.34.2-alpha.3", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.2-alpha.3", "@typescript-eslint/types": "^8.34.2-alpha.3", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.34.2-alpha.3", "readmeFilename": "README.md", "gitHead": "c273e038fbd525232a8896786db28e9705cf205a", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-O68Hz4Wma3ykURKOxz5fSnW2pCWeVT2bGdjvcl1Nm1lqjXWTXYaUPEMhTEg8m35wo1TkQa693UdYyvAxN0AEAg==", "shasum": "f16bd34e6a23cada317dd7271c5ba1fe573688a9", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.34.2-alpha.3.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.34.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC3aAatPcU/HBJIlRMIQmWJ0mbAsjd47uHqNpTgz34z9QIhALQUF6llYn6gXGw9GyADsNy46dMdJtBfgnaNaiBKdKEm"}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.34.2-alpha.3_1750698786853_0.4382617602214187"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T17:13:07.089Z", "publish_time": 1750698787089, "_source_registry_name": "default"}, "8.35.0": {"name": "@typescript-eslint/project-service", "version": "8.35.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.0", "gitHead": "d19c9f383a2e345656b601aa42ec250293609019", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-41xatqRwWZuhUMF/aZm2fcUsOFKNcG28xqRSS6ZVr9BVJtGExosLAm5A1OxTjRMagx8nJqva+P5zNIGt8RIgbQ==", "shasum": "00bd77e6845fbdb5684c6ab2d8a400a58dcfb07b", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.0.tgz", "fileCount": 12, "unpackedSize": 15028, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC+j0ULtezBwHOHtGFBhjsZLA+UrdI0GOyX5Rxm2/lEkgIhAKIUhzDrzAFrgeT+u/49jT0f7PEorWR0qrGuZvigSD9U"}], "size": 5428}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.0_1750699173264_0.40435574143264796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T17:19:33.437Z", "publish_time": 1750699173437, "_source_registry_name": "default"}, "8.35.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.0", "@typescript-eslint/types": "^8.35.1-alpha.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.0", "readmeFilename": "README.md", "gitHead": "f000a1f0c77c8275ffe7ea92e04a94275e73396d", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YcLl6msX0Zgv5sitcIM4tsbJmGn+CU/FUcG3cv0bBPlW5yw5G65DMlYG+OJ8S/GY4349Tk//ODhfWrj3iFKAzA==", "shasum": "d9ae1400f23f08ddad3e5989ff161a7e56eacc52", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.0.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHddPMTyv7MPmgxf7WlJcXwOQRwqxblzrzL/6wPe/eZFAiAAjdcE4CL2xUbnf2YN6b5UFJJOGJFVE7xDPRS+p342mQ=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.0_1750699572066_0.950852372518276"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T17:26:12.263Z", "publish_time": 1750699572263, "_source_registry_name": "default"}, "8.35.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.1", "@typescript-eslint/types": "^8.35.1-alpha.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.1", "readmeFilename": "README.md", "gitHead": "d19c9f383a2e345656b601aa42ec250293609019", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-BCQqOPBWdQh6imtg8cKiwmmE8uiQRkbc503xb12PrNpv64CmEuT+Y19iEWdr7tCIwNB8VNjT/p+WuPAzoa1mew==", "shasum": "1fe7d68785b54ec992a7ddde8130074f23ff47eb", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.1.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDdGgKwR91rDtDHBr+bQoot+ZS1vK+D6kobKY5b/WDIBAiArr80p26Nnhpt9C0g+F+Oz7hS2SiIe9wIsjmHeLYO1ZQ=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.1_1750700363469_0.9408405862829095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T17:39:23.667Z", "publish_time": 1750700363667, "_source_registry_name": "default"}, "8.35.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.2", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.2", "@typescript-eslint/types": "^8.35.1-alpha.2", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.2", "readmeFilename": "README.md", "gitHead": "3ecb96f2d2203b65ce0f2e2eb48c1ff5e875bdae", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-EVDlaPGpQH+HfGXf6lVkzcUYBGyuEUeM4QQ5WTW4yG/oo9N0cNRf3KJx6r9LWTFeMyvWyvoJFfo1To/mNmMmDg==", "shasum": "91a110147133483bcd5fab254415ba0fed227cc0", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.2.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFrv6lINF6pZ67QqkW7Of/oedRgc980Kopzd/Cn95zxOAiAS1YhozoESYCgSx0prMv4Rk5a4cadAn0GuNUPTEIH6vQ=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.2_1750802810943_0.3266072447958801"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-24T22:06:51.122Z", "publish_time": 1750802811122, "_source_registry_name": "default"}, "8.35.1-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.3", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.3", "@typescript-eslint/types": "^8.35.1-alpha.3", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.3", "readmeFilename": "README.md", "gitHead": "9dc91aaff7c55082a7a768788664d625f8283500", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-C+fjIh65GkCskykAXlWOLquKmjaIHPxp6xat67l9AYoxQETu9bpqPH5kvPa7JGZqqVQz3yq2Ozw2fB07A1leMQ==", "shasum": "309af16113503cc22804705138566ba5abf1501b", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.3.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD5fvD1zQXgmbEtQVOwGwhlxT5syQFgStBU4BAfYwcdtwIgNkJP7JTevzkz5bgQG2yxm+U9ap40KBTJpLhu7Co8AIA="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.3_1750803342810_0.12386886631379301"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-24T22:15:42.981Z", "publish_time": 1750803342981, "_source_registry_name": "default"}, "8.35.1-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.4", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.4", "@typescript-eslint/types": "^8.35.1-alpha.4", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.4", "readmeFilename": "README.md", "gitHead": "3e545207f0e34611f528128fc699b25091bc40b3", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-flPIXI2Fs2cynkTeegvI6kHtgUIbaGyEZw7ybw/yhuwhemKPDTzchjYv0NQtRy9w5Gh0EKwYnn5J9AvcZ4CMHw==", "shasum": "e528382534ab3d29efcc6ee90947cd1ec710d438", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.4.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHBbjaZ5iAj7LCkTQJ8oW53h+xfDzr5Caz6qKoYpEEWEAiEAgB63DaU9OTn6/Ohff4wLTG6mMR4bTFCEhGdqtxxfvAc="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.4_1750853727339_0.1848706057641485"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-25T12:15:27.543Z", "publish_time": 1750853727543, "_source_registry_name": "default"}, "8.35.1-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.5", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.5", "@typescript-eslint/types": "^8.35.1-alpha.5", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.5", "readmeFilename": "README.md", "gitHead": "20f8564b82b225c8d685f5f06284516f1f22b32a", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-gLkXjvyCznj+7i2Xl+AJdX5ezDDeguQWY5dly2ZVG6pHllS95JEPoV527I1eGB3seP60ZFd3Wbs3lbOChnauKw==", "shasum": "ad7639e5df56130fbf8b9ad09c3a9e90c6363bc1", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.5.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEvKZESNaEjOc9sikJzWtpNXLogNMBLMeqAl8EsdyIlDAiA7J4yy4gg6mbeb1P6JzNFlw1u4jKYNVy7q6ApVFZixXg=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.5_1750943220494_0.4843126427050546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-26T13:07:00.695Z", "publish_time": 1750943220695, "_source_registry_name": "default"}, "8.35.1-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.35.1-alpha.6", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1-alpha.6", "@typescript-eslint/types": "^8.35.1-alpha.6", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1-alpha.6", "readmeFilename": "README.md", "gitHead": "db32b8a82d58eddb29be207a5f4476644973abbf", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-gfjw147v8S70ZIoyZtN9mQPha+YMLmUjPHkMNjceeCgt/FUqKCu8Etzh52fwyKV95bfFkGX98NKC2+0nENlNRQ==", "shasum": "31dbc45f3ad24008e200e0cc5cc626ef248e250e", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1-alpha.6.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.1-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBIG2ry84YPhSVaK6FmGeM01lWaFHQcqEWGAfSsw/RA2AiEA/eIV0MHzQiBusmoUkMmcixtecpkUpWt+6Y4agCW9yKg="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1-alpha.6_1750945529973_0.7390106551451201"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-26T13:45:30.142Z", "publish_time": 1750945530142, "_source_registry_name": "default"}, "8.35.1": {"name": "@typescript-eslint/project-service", "version": "8.35.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.1", "@typescript-eslint/types": "^8.35.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.1", "gitHead": "f9bd7d86fc39eb2957de7eefdcd3ab9b6c9dc4a7", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VYxn/5LOpVxADAuP3NrnxxHYfzVtQzLKeldIhDhzC8UHaiQvYlXvKuVho1qLduFbJjjy5U5bkGwa3rUGUb1Q6Q==", "shasum": "815bb771f2f6c97780e44299434ece3c2e526127", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1.tgz", "fileCount": 12, "unpackedSize": 15028, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDSi3fj9ugpZNZrqcu7wN8AudAKAtdneEGgT9sLOHk2bAiAnWkubw/B7YA3NxhX6EFl61xkULf/uZxfZZIraLFxnKw=="}], "size": 5428}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.1_1751303934097_0.920468560443171"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-30T17:18:54.266Z", "publish_time": 1751303934266, "_source_registry_name": "default"}, "8.35.2-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.0", "@typescript-eslint/types": "^8.35.2-alpha.0", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.0", "readmeFilename": "README.md", "gitHead": "e2ecca60d9e8486bb13e98b3e1a65d529bedef03", "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ondZ0Kq69r7MGf7ZF00qzqry90FmOjgFkgFcV7DjZlPBOu/DQnFFyQ9gKQ0w/0mGxAfDfhT1mWPjkH82Urot0Q==", "shasum": "76dba5b2dd75c010240ccc1c8a4b21815f87e953", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.0.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD3watIVuIEbBrN1k9EmBNtWkT6KPcVu1uKFFX+VjHU8AIgDzKwbijSYdU8SBAstFeyyJXayycPxnUra8V6YIwjurM="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.0_1751362001199_0.3211689095385817"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T09:26:41.376Z", "publish_time": 1751362001376, "_source_registry_name": "default"}, "8.35.2-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.1", "@typescript-eslint/types": "^8.35.2-alpha.1", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.1", "readmeFilename": "README.md", "gitHead": "f391560ce3edeeecde056420284799124ef1d244", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YrLhM85DI18wxXNYSPqRWtZRHT7OFLJMVUuqANIVjqnGu59e9uurtgI3Z5Ivcv+XISgByppf+BMEj2dmURIMjQ==", "shasum": "4643c0c73135d64b9bf56273a18c99cebff29d27", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.1.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDuRJDoUjSHjcuRsBL+FGZ7FhUn6O51iMhdGHZ3ZJxm2wIgAJ8s/4bOYqSMqRWwlWi6eYnXsqc1TSOMk8vQJG7SWcs="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.1_1751368081109_0.7648994138741798"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T11:08:01.271Z", "publish_time": 1751368081271, "_source_registry_name": "default"}, "8.35.2-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.2", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.2", "@typescript-eslint/types": "^8.35.2-alpha.2", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.2", "readmeFilename": "README.md", "gitHead": "50a3ab6ea8ea44691ff8fbe16c1a4f46950fca34", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lH+PwHG4eFRBPSfZTiro3cEUDTzJkTRrA2SJ1j7UO9BNsaVsOdVVML10G4Ws2pVHUSddcc4Q/SdV05dqbsAcDQ==", "shasum": "0a1336e2f6af6e26e8d36cac4435ba92025e10c5", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.2.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCgRHhYG/f7Sr6sx3sb8Tpyn12ped+3i5VuylISqnb4UwIhAOjljH/GpT7iMLdvsOFAAKFvundACg4ZWbu4/32hon1a"}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.2_1751369538607_0.9674669661470692"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T11:32:18.772Z", "publish_time": 1751369538772, "_source_registry_name": "default"}, "8.35.2-alpha.3": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.3", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.3", "@typescript-eslint/types": "^8.35.2-alpha.3", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.3", "readmeFilename": "README.md", "gitHead": "cefd4a93d80f67a9b8a892529dee4ff96adafcfd", "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-f+/KcAshRfFCea7TMAupRGI0f4rcA7IYQHMz/7yoa2hQQwuXYxMeqt6rNI7SwQGPJtJw7cNXDTDkl+n8FfQb7g==", "shasum": "5613c10ecf87fab24ce01c9d669fd24fed259f9b", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.3.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCpyd5SMJFwep3aiXv8FgVRS6X7mtjHIDrD/K830ACliwIgGmvtDMaDtQOXS9xWM22nQ9bAWs4DZQuinHI187jEqhA="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.3_1751369976938_0.014403139251423669"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T11:39:37.099Z", "publish_time": 1751369977099, "_source_registry_name": "default"}, "8.35.2-alpha.4": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.4", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.4", "@typescript-eslint/types": "^8.35.2-alpha.4", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.4", "readmeFilename": "README.md", "gitHead": "c14c98e8f7487d7cd13551b34f039dc278a40092", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-9CfudOhoEYIS8GMLFFKXCQo7IlU8uyYF3pcW/0catx7zT0bCCYCxAXY/CQ1knQWvW07o9+O8txS2H9n9xAWo3g==", "shasum": "66fc86e245a2b4d74d0343ed90ef88f4c50a4caa", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.4.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHo20H6UTeusn1S7mSr1ntCZuCDLENt1qdRrgj7pyM0vAiEAhEEQXtBL91Nn+kF4AhdVeYjyXGU41FvYmXgd5/+WUJQ="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.4_1751371737109_0.02298643585724469"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T12:08:57.265Z", "publish_time": 1751371737265, "_source_registry_name": "default"}, "8.35.2-alpha.5": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.5", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.5", "@typescript-eslint/types": "^8.35.2-alpha.5", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.5", "readmeFilename": "README.md", "gitHead": "5b378e290e56eefe638f22444d55bc5c0744c28c", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ToYyZmSc0d6UEasmUksFcbEKCYhvctNfjVzJHbdu0/ru+ega0+6DkimnW4b6OinPsN/APzP+2h8WfDmC7q5FoA==", "shasum": "753a5e74403b6eabad71b95995bdb103c7685457", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.5.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCAghRHu1UFsyHuyzSVxhazg+Kpom03QbsqAeSFoPScWQIgD/wD4F0h51MOc5dcZ8IapON0IG+jHrGMSnBnrY+iz5Q="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.5_1751378286952_0.9586521696401367"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-01T13:58:07.107Z", "publish_time": 1751378287107, "_source_registry_name": "default"}, "8.35.2-alpha.6": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.6", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.6", "@typescript-eslint/types": "^8.35.2-alpha.6", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.6", "readmeFilename": "README.md", "gitHead": "7ec793193d7b9c6c9928191e462c54b10b177723", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bKpHb+bZgM3HMT5ugUT7I6WB5bMZ1xncNH98M7653X8JAs5WC9nVFwgXpqGXwIA3rPX76CJ0Zx3w5tQc64mVMA==", "shasum": "45f2d48a1d108aed40ec9f391e2c593b3e4aab7a", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.6.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIE7LNDUKuyNLSvH6UGqeF0oacNcsTiHlTL9C6oFR8/qIAiEAwzDG2zF/24oAf5c9WDclkBQGIup8taZTp4hMyFzkZVY="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.6_1751735797485_0.6872979301776461"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-05T17:16:37.671Z", "publish_time": 1751735797671, "_source_registry_name": "default"}, "8.35.2-alpha.7": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.7", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.7", "@typescript-eslint/types": "^8.35.2-alpha.7", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.7", "readmeFilename": "README.md", "gitHead": "1ca81c14ba904e52be6726eb20c79bbcab57243c", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-F1XJwvfm6iX1+M3BCj3m1JXNllmfvOZtBEGD0HaErycSiyCsEfgnZLi1yYyS4C6XS+GVJpiK+85zfb6ITADu1w==", "shasum": "364622ae7556cd3b45ff56d5b1c5b2599a266b52", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.7.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAyRFRaepFxt+EjEc8cRWrcFfnfbZ37ypHCMIoamy3sCAiAFRiZgWAdJLC655Kk/NorS0m0rV8tdtkkmPzGASUhpbA=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.7_1751840939914_0.18776185631172737"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-06T22:29:00.091Z", "publish_time": 1751840940091, "_source_registry_name": "default"}, "8.35.2-alpha.8": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.8", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.8", "@typescript-eslint/types": "^8.35.2-alpha.8", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.8", "readmeFilename": "README.md", "gitHead": "4a60d3e96cba7a4b8c2721c2d5fd15a68076bf4e", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-J7hQWRq/WiOKv5vwH2WUND+LOBArSMvfT57mLpwCZgtgtn1vNzOr7KMJaeky60AxnGEAlyMwX1ulIoJJJS3syw==", "shasum": "e8b912dc28f4a7cde7aadcf95e4112c2a0e6d6d8", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.8.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDVgpv+joyT1wmhc8bgyBvxmMN5yyI5kKI5Rm408Fm4agIgWFzm4IR/T5S1/18HCGCc7TJi6D98DvPsHV0ovhhs5NA="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.8_1751841812168_0.08101274445695594"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-06T22:43:32.563Z", "publish_time": 1751841812563, "_source_registry_name": "default"}, "8.35.2-alpha.9": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.9", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.9", "@typescript-eslint/types": "^8.35.2-alpha.9", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.9", "readmeFilename": "README.md", "gitHead": "c81ba15ab439b6880157dd2bd41fc830f903e8b5", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PAc92ym3JyteCErw6561pPuWX3Sp70d/fx8JxfLjsN2bU6aETFxJ/t/gRzHeU+kVrPpFTxuSihLyTK/CnzhP+Q==", "shasum": "3dab88ef30aa976ee6a7316e09f91ad03ef618e9", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.9.tgz", "fileCount": 12, "unpackedSize": 15052, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBW1fap8HSim0tkq/GGdHcoBiaI7BDdC0bwgavK6UP69AiEAhUMRVhdv06ImiASnxFmR2rbp5hm/4n0YPGclsFfOo9M="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.9_1751845133466_0.12465478901906679"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-06T23:38:53.649Z", "publish_time": 1751845133649, "_source_registry_name": "default"}, "8.35.2-alpha.10": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.10", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.10", "@typescript-eslint/types": "^8.35.2-alpha.10", "debug": "^4.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.10", "readmeFilename": "README.md", "gitHead": "25728c230968d09164483e29e0eddfc0e663055f", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ifyLJI5m1mHRdePLYXHCMh4hjFgDWvWQe0h5w/QtbVHuybaWSH6QrwgmVvwXH07WLlzQlphjS6g38DJUskYJEg==", "shasum": "c8e0423b91430e98da1be91f30025a6e1a66acfd", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.10.tgz", "fileCount": 12, "unpackedSize": 15055, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGgJ+s96ahXuhW4tKo6lbbWqNphADeYH56AEM1Lz7E4TAiA+YdsUDNFoQAZZCD/7tttMJ9caQmbPNKHTITsAXKZCUQ=="}], "size": 5437}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.10_1751891696663_0.8147729685667477"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T12:34:56.833Z", "publish_time": 1751891696833, "_source_registry_name": "default"}, "8.35.2-alpha.11": {"name": "@typescript-eslint/project-service", "version": "8.35.2-alpha.11", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.2-alpha.11", "@typescript-eslint/types": "^8.35.2-alpha.11", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.35.2-alpha.11", "readmeFilename": "README.md", "gitHead": "5e3288a94b4029f26aec983cc910b9146626dc28", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lbFigpfgbDto68nODx8Y35gjL/SskuYAIfMBWEJOh7hXZCmT2dsIWNXreQef6zZB3TXy5dyzz5Uh+WOD0GeX8w==", "shasum": "941bc9d88bcd31d211d8148b6040ed978a012244", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.2-alpha.11.tgz", "fileCount": 12, "unpackedSize": 15185, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.35.2-alpha.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBmrBdL0MNUIQeK3DyV8FJgFPh+yxNIQ/ELfGQQoZAF1AiEAo+Lf2v8p9VXM0Y4NpUapkwoFgl/S3w/y4WX7/plGnXM="}], "size": 5475}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.35.2-alpha.11_1751892535027_0.3020009679013782"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T12:48:55.210Z", "publish_time": 1751892535210, "_source_registry_name": "default"}, "8.36.0": {"name": "@typescript-eslint/project-service", "version": "8.36.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.36.0", "@typescript-eslint/types": "^8.36.0", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.36.0", "gitHead": "84b7a2e905c2a82fda3015f01683525d019c9c3c", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-JAhQFIABkWccQYeLMrHadu/fhpzmSQ1F1KXkpzqiVxA/iYI6UnRt2trqXHt1sYEcw1mxLnB9rKMsOxXPxowN/g==", "shasum": "0c4acdcbe56476a43cdabaac1f08819424a379fd", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.36.0.tgz", "fileCount": 12, "unpackedSize": 15158, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAYdqm4qOX0jkNBCQUYnVhrn9r2GYfiPlnKFXQHPmeOtAiEA6Gsl0YP0AKUPGMhtc92DPuWUE7gfMm3EP3R3wxwKmkw="}], "size": 5466}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.36.0_1751908775301_0.7751938696337828"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T17:19:35.494Z", "publish_time": 1751908775494, "_source_registry_name": "default"}, "8.36.1-alpha.0": {"name": "@typescript-eslint/project-service", "version": "8.36.1-alpha.0", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.36.1-alpha.0", "@typescript-eslint/types": "^8.36.1-alpha.0", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.36.1-alpha.0", "readmeFilename": "README.md", "gitHead": "84b7a2e905c2a82fda3015f01683525d019c9c3c", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-A/cVGEJo4JgDz/L5p2WwL7iQVEtr+9U3XYIX6OxvEiFgtoEwCpaJc288t4qbBQPdCV9tj4iBuxtn2VnrUMqmZw==", "shasum": "f11c33d10552cc7f6d90fff34d05f824420319a3", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.36.1-alpha.0.tgz", "fileCount": 12, "unpackedSize": 15182, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.36.1-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICMH8r96YGCWzCxDGmVxjuAtduvQ/C4e+UoxlM3Xr0c5AiAaIhYLFmDiNiX5Zx1wL0zhVcgoAHBli0MsaXQ+1MBsfA=="}], "size": 5475}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.36.1-alpha.0_1751909537015_0.7005160405423956"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-07T17:32:17.190Z", "publish_time": 1751909537190, "_source_registry_name": "default"}, "8.36.1-alpha.1": {"name": "@typescript-eslint/project-service", "version": "8.36.1-alpha.1", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.36.1-alpha.1", "@typescript-eslint/types": "^8.36.1-alpha.1", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.36.1-alpha.1", "readmeFilename": "README.md", "gitHead": "5c5e3d5c3853ab613e06be0d77a40e970017b3fc", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-R3XbGRrGx5pXmVbgcs9O0u7g6CRUwZvDbl8rbpAgx9f2xPkmxBp/y08OHmEAyviPPrEWn6OFRHGoTSkSmiEH4Q==", "shasum": "bf70cb084f8c0abf68a242f96edbec0b9cf7ae7a", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.36.1-alpha.1.tgz", "fileCount": 12, "unpackedSize": 15182, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.36.1-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCROI3OdLrFIwpY2YbIo4esTam2K3BelxfmZVh7EOOWTwIgT2nhreqt5Hq0skYVjbgEcfGfOM8UxNCGEvauVXDI1k0="}], "size": 5475}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.36.1-alpha.1_1751968204620_0.8667838345923207"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-08T09:50:04.792Z", "publish_time": 1751968204792, "_source_registry_name": "default"}, "8.36.1-alpha.2": {"name": "@typescript-eslint/project-service", "version": "8.36.1-alpha.2", "description": "Standalone TypeScript project service wrapper for linting.", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.36.1-alpha.2", "@typescript-eslint/types": "^8.36.1-alpha.2", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "publishConfig": {"access": "public"}, "nx": {"name": "project-service", "includedScripts": ["clean"]}, "_id": "@typescript-eslint/project-service@8.36.1-alpha.2", "readmeFilename": "README.md", "gitHead": "16c344ec7d274ea542157e0f19682dd1930ab838", "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-61i9g/2TQvJwvGmM2aJvmjodSRpVHgWvVPHzRoXc/Ex3hE2+/jYfym8Z8AADo13acgpsn1dI4Jadugiofiq/Jg==", "shasum": "95cc76c4f6e880e6b290fd61154d119403828ae5", "tarball": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.36.1-alpha.2.tgz", "fileCount": 12, "unpackedSize": 15182, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@typescript-eslint%2fproject-service@8.36.1-alpha.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDvXBfgcL4239JxxqVlggWaxoAAZPUduFSOgYZx8moieQIgROd/gvDoLtZTtUdM/Yc/f3AxlbQqMx41x0xCCoxjtb0="}], "size": 5475}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/project-service_8.36.1-alpha.2_1752069577857_0.13528640601437103"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-07-09T13:59:38.047Z", "publish_time": 1752069578047, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "description": "Standalone TypeScript project service wrapper for linting.", "homepage": "https://typescript-eslint.io", "keywords": ["eslint", "typescript", "estree"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "brad<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# `@typescript-eslint/project-service`\n\n> Standalone TypeScript project service wrapper for linting.\n\n[![NPM Version](https://img.shields.io/npm/v/@typescript-eslint/project-service.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/project-service)\n[![NPM Downloads](https://img.shields.io/npm/dm/@typescript-eslint/project-service.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/project-service)\n\nA standalone export of the \"Project Service\" that powers typed linting for typescript-eslint.\n\n> See https://typescript-eslint.io for general documentation on typescript-eslint, the tooling that allows you to run ESLint and Prettier on TypeScript code.\n\n<!-- Local path for docs: docs/packages/Project_Service.mdx -->\n", "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/project-service"}, "_source_registry_name": "default"}