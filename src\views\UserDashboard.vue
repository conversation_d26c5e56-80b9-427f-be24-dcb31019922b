<script lang="ts">
import { ref, defineComponent } from 'vue';

interface Task {
  id: number;
  title: string;
  description: string;
  priority: string;
  dueDate: string;
}

export default defineComponent({
  name: 'UserDashboard',
  setup() {
    const todoTasks = ref<Task[]>([
      {
        id: 1,
        title: '完成项目文档',
        description: '编写项目需求文档和技术方案',
        priority: '高',
        dueDate: '2025-07-20',
      },
    ]);

    const inProgressTasks = ref<Task[]>([
      {
        id: 2,
        title: '开发登录功能',
        description: '实现用户登录和认证功能',
        priority: '中',
        dueDate: '2025-07-18',
      },
    ]);

    const completedTasks = ref<Task[]>([
      {
        id: 3,
        title: '系统部署',
        description: '完成系统初始化部署',
        priority: '高',
        dueDate: '2025-07-13',
      },
    ]);

    return {
      todoTasks,
      inProgressTasks,
      completedTasks,
    };
  },
});
</script>
